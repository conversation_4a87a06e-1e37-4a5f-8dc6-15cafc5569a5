{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\changePwd.vue?vue&type=style&index=0&id=38f72e90&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\changePwd.vue", "mtime": 1748540171913}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changePwd.vue"], "names": [], "mappings": ";AAgOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "changePwd.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-lock\"></i>\r\n            修改密码\r\n          </h2>\r\n          <div class=\"page-subtitle\">为了您的账户安全，请定期更换密码</div>\r\n        </div>\r\n        <el-button \r\n          type=\"text\" \r\n          icon=\"el-icon-back\"\r\n          @click=\"goBack\"\r\n          class=\"back-btn\"\r\n        >\r\n          返回\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 修改密码表单 -->\r\n      <div class=\"form-section\">\r\n        <div class=\"form-card\">\r\n          <div class=\"security-tips\">\r\n            <div class=\"tips-header\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>密码安全提示</span>\r\n            </div>\r\n            <ul class=\"tips-list\">\r\n              <li>密码长度至少8位，包含字母、数字</li>\r\n              <li>不要使用过于简单的密码</li>\r\n              <li>建议定期更换密码</li>\r\n              <li>不要在多个平台使用相同密码</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <el-form \r\n            :model=\"passwordForm\" \r\n            :rules=\"rules\" \r\n            ref=\"passwordForm\" \r\n            label-width=\"120px\"\r\n            class=\"password-form\"\r\n          >\r\n            <el-form-item label=\"当前密码\" prop=\"oldPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.oldPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请输入当前密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.newPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请输入新密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"确认新密码\" prop=\"confirmPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.confirmPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 密码强度指示器 -->\r\n            <div class=\"password-strength\" v-if=\"passwordForm.newPassword\">\r\n              <div class=\"strength-label\">密码强度：</div>\r\n              <div class=\"strength-bar\">\r\n                <div \r\n                  class=\"strength-fill\" \r\n                  :class=\"passwordStrengthClass\"\r\n                  :style=\"{ width: passwordStrengthWidth }\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"strength-text\" :class=\"passwordStrengthClass\">\r\n                {{ passwordStrengthText }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作按钮 -->\r\n            <div class=\"action-buttons\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"changePassword\"\r\n                :loading=\"loading\"\r\n                size=\"medium\"\r\n              >\r\n                确认修改\r\n              </el-button>\r\n              <el-button \r\n                @click=\"resetForm\"\r\n                size=\"medium\"\r\n              >\r\n                重置\r\n              </el-button>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ChangePwd\",\r\n  data() {\r\n    // 确认密码验证\r\n    const validateConfirmPassword = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请再次输入新密码'));\r\n      } else if (value !== this.passwordForm.newPassword) {\r\n        callback(new Error('两次输入密码不一致'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      loading: false,\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      rules: {\r\n        oldPassword: [\r\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n          { min: 8, message: '密码长度至少8位', trigger: 'blur' },\r\n          { \r\n            pattern: /^(?=.*[a-zA-Z])(?=.*\\d).+$/, \r\n            message: '密码必须包含字母和数字', \r\n            trigger: 'blur' \r\n          }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: '请确认新密码', trigger: 'blur' },\r\n          { validator: validateConfirmPassword, trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 密码强度计算\r\n    passwordStrength() {\r\n      const password = this.passwordForm.newPassword;\r\n      if (!password) return 0;\r\n      \r\n      let strength = 0;\r\n      \r\n      // 长度检查\r\n      if (password.length >= 8) strength += 1;\r\n      if (password.length >= 12) strength += 1;\r\n      \r\n      // 字符类型检查\r\n      if (/[a-z]/.test(password)) strength += 1;\r\n      if (/[A-Z]/.test(password)) strength += 1;\r\n      if (/\\d/.test(password)) strength += 1;\r\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) strength += 1;\r\n      \r\n      return Math.min(strength, 4);\r\n    },\r\n    passwordStrengthWidth() {\r\n      return (this.passwordStrength / 4) * 100 + '%';\r\n    },\r\n    passwordStrengthClass() {\r\n      const classes = ['weak', 'fair', 'good', 'strong'];\r\n      return classes[Math.max(0, this.passwordStrength - 1)] || 'weak';\r\n    },\r\n    passwordStrengthText() {\r\n      const texts = ['弱', '一般', '良好', '强'];\r\n      return texts[Math.max(0, this.passwordStrength - 1)] || '弱';\r\n    }\r\n  },\r\n  methods: {\r\n    changePassword() {\r\n      this.$refs.passwordForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          \r\n          // 模拟密码修改过程\r\n          setTimeout(() => {\r\n            this.loading = false;\r\n            this.$message.success('密码修改成功！');\r\n            this.resetForm();\r\n            \r\n            // 可以选择跳转回个人信息页面或者首页\r\n            setTimeout(() => {\r\n              this.$router.push('/profile');\r\n            }, 1500);\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    resetForm() {\r\n      this.$refs.passwordForm.resetFields();\r\n      this.passwordForm = {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0 0 8px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #1890ff;\r\n  font-size: 22px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin: 0;\r\n}\r\n\r\n.back-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.back-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.form-card {\r\n  padding: 32px;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.tips-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 500;\r\n  color: #52c41a;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.tips-list {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #52c41a;\r\n}\r\n\r\n.tips-list li {\r\n  margin-bottom: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式 */\r\n.password-form {\r\n  max-width: 500px;\r\n}\r\n\r\n/* 密码强度指示器 */\r\n.password-strength {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 24px;\r\n  padding: 0 0 0 120px;\r\n}\r\n\r\n.strength-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  white-space: nowrap;\r\n}\r\n\r\n.strength-bar {\r\n  flex: 1;\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n}\r\n\r\n.strength-fill {\r\n  height: 100%;\r\n  transition: all 0.3s;\r\n  border-radius: 3px;\r\n}\r\n\r\n.strength-fill.weak {\r\n  background: #ff4d4f;\r\n}\r\n\r\n.strength-fill.fair {\r\n  background: #faad14;\r\n}\r\n\r\n.strength-fill.good {\r\n  background: #1890ff;\r\n}\r\n\r\n.strength-fill.strong {\r\n  background: #52c41a;\r\n}\r\n\r\n.strength-text {\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.strength-text.weak {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.strength-text.fair {\r\n  color: #faad14;\r\n}\r\n\r\n.strength-text.good {\r\n  color: #1890ff;\r\n}\r\n\r\n.strength-text.strong {\r\n  color: #52c41a;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  margin-top: 32px;\r\n  padding-top: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.password-form ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.password-form ::v-deep .el-input__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.password-form ::v-deep .el-input__inner:focus {\r\n  border-color: #1890ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .form-card {\r\n    padding: 24px 16px;\r\n  }\r\n  \r\n  .password-strength {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 0;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .strength-bar {\r\n    width: 100%;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>\r\n"]}]}