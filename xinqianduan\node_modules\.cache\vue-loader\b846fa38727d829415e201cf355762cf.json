{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=template&id=71a50989&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748540171916}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}