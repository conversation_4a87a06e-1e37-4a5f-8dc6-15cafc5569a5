{"version": 3, "names": ["_helperSplitExportDeclaration", "require", "t", "_helperEnvironmentVisitor", "_traverseNode", "_visitors", "renameVisitor", "ReferencedIdentifier", "node", "state", "name", "old<PERSON>ame", "newName", "<PERSON><PERSON>", "path", "scope", "bindingIdentifierEquals", "binding", "identifier", "skip", "isMethod", "requeueComputedKeyAndDecorators", "ObjectProperty", "key", "shorthand", "getBindingIdentifier", "_node$extra", "extra", "AssignmentExpression|Declaration|VariableDeclarator", "isVariableDeclaration", "ids", "getOuterBindingIdentifiers", "Renamer", "constructor", "maybeConvertFromExportDeclaration", "parentDeclar", "maybeExportDeclar", "parentPath", "isExportDeclaration", "isExportDefaultDeclaration", "declaration", "isDeclaration", "id", "isExportAllDeclaration", "splitExportDeclaration", "maybeConvertFromClassFunctionDeclaration", "maybeConvertFromClassFunctionExpression", "rename", "find", "isFunctionExpression", "isClassExpression", "bindingIds", "blockToTraverse", "arguments", "block", "traverseNode", "explode", "discriminant", "removeOwnBinding", "bindings", "exports", "default"], "sources": ["../../../src/scope/lib/renamer.ts"], "sourcesContent": ["import type Binding from \"../binding.ts\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as t from \"@babel/types\";\nimport type { NodePath, Visitor } from \"../../index.ts\";\nimport { requeueComputedKeyAndDecorators } from \"@babel/helper-environment-visitor\";\nimport { traverseNode } from \"../../traverse-node.ts\";\nimport { explode } from \"../../visitors.ts\";\nimport type { Identifier } from \"@babel/types\";\n\nconst renameVisitor: Visitor<Renamer> = {\n  ReferencedIdentifier({ node }, state) {\n    if (node.name === state.oldName) {\n      node.name = state.newName;\n    }\n  },\n\n  Scope(path, state) {\n    if (\n      !path.scope.bindingIdentifierEquals(\n        state.oldName,\n        state.binding.identifier,\n      )\n    ) {\n      path.skip();\n      if (path.isMethod()) {\n        requeueComputedKeyAndDecorators(path);\n      }\n    }\n  },\n\n  ObjectProperty({ node, scope }, state) {\n    const { name } = node.key as Identifier;\n    if (\n      node.shorthand &&\n      // In destructuring the identifier is already renamed by the\n      // AssignmentExpression|Declaration|VariableDeclarator visitor,\n      // while in object literals it's renamed later by the\n      // ReferencedIdentifier visitor.\n      (name === state.oldName || name === state.newName) &&\n      // Ignore shadowed bindings\n      scope.getBindingIdentifier(name) === state.binding.identifier\n    ) {\n      node.shorthand = false;\n      if (!process.env.BABEL_8_BREAKING) {\n        if (node.extra?.shorthand) node.extra.shorthand = false;\n      }\n    }\n  },\n\n  \"AssignmentExpression|Declaration|VariableDeclarator\"(\n    path: NodePath<t.AssignmentPattern | t.Declaration | t.VariableDeclarator>,\n    state,\n  ) {\n    if (path.isVariableDeclaration()) return;\n    const ids = path.getOuterBindingIdentifiers();\n\n    for (const name in ids) {\n      if (name === state.oldName) ids[name].name = state.newName;\n    }\n  },\n};\n\nexport default class Renamer {\n  constructor(binding: Binding, oldName: string, newName: string) {\n    this.newName = newName;\n    this.oldName = oldName;\n    this.binding = binding;\n  }\n\n  declare oldName: string;\n  declare newName: string;\n  declare binding: Binding;\n\n  maybeConvertFromExportDeclaration(parentDeclar: NodePath) {\n    const maybeExportDeclar = parentDeclar.parentPath;\n\n    if (!maybeExportDeclar.isExportDeclaration()) {\n      return;\n    }\n\n    if (maybeExportDeclar.isExportDefaultDeclaration()) {\n      const { declaration } = maybeExportDeclar.node;\n      if (t.isDeclaration(declaration) && !declaration.id) {\n        return;\n      }\n    }\n\n    if (maybeExportDeclar.isExportAllDeclaration()) {\n      return;\n    }\n\n    splitExportDeclaration(\n      maybeExportDeclar as NodePath<\n        Exclude<t.ExportDeclaration, t.ExportAllDeclaration>\n      >,\n    );\n  }\n\n  maybeConvertFromClassFunctionDeclaration(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function declaration\n\n    // if (!path.isFunctionDeclaration() && !path.isClassDeclaration()) return;\n    // if (this.binding.kind !== \"hoisted\") return;\n\n    // path.node.id = identifier(this.oldName);\n    // path.node._blockHoist = 3;\n\n    // path.replaceWith(\n    //   variableDeclaration(\"let\", [\n    //     variableDeclarator(identifier(this.newName), toExpression(path.node)),\n    //   ]),\n    // );\n  }\n\n  maybeConvertFromClassFunctionExpression(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function expression\n\n    // if (!path.isFunctionExpression() && !path.isClassExpression()) return;\n    // if (this.binding.kind !== \"local\") return;\n\n    // path.node.id = identifier(this.oldName);\n\n    // this.binding.scope.parent.push({\n    //   id: identifier(this.newName),\n    // });\n\n    // path.replaceWith(\n    //   assignmentExpression(\"=\", identifier(this.newName), path.node),\n    // );\n  }\n\n  rename(/* Babel 7 - block?: t.Pattern | t.Scopable */) {\n    const { binding, oldName, newName } = this;\n    const { scope, path } = binding;\n\n    const parentDeclar = path.find(\n      path =>\n        path.isDeclaration() ||\n        path.isFunctionExpression() ||\n        path.isClassExpression(),\n    );\n    if (parentDeclar) {\n      const bindingIds = parentDeclar.getOuterBindingIdentifiers();\n      if (bindingIds[oldName] === binding.identifier) {\n        // When we are renaming an exported identifier, we need to ensure that\n        // the exported binding keeps the old name.\n        this.maybeConvertFromExportDeclaration(parentDeclar);\n      }\n    }\n\n    const blockToTraverse = process.env.BABEL_8_BREAKING\n      ? scope.block\n      : (arguments[0] as t.Pattern | t.Scopable) || scope.block;\n    traverseNode(\n      blockToTraverse,\n      explode(renameVisitor),\n      scope,\n      this,\n      scope.path,\n      // When blockToTraverse is a SwitchStatement, the discriminant\n      // is not part of the current scope and thus should be skipped.\n      { discriminant: true },\n    );\n\n    if (process.env.BABEL_8_BREAKING) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    } else if (!arguments[0]) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    }\n\n    if (parentDeclar) {\n      this.maybeConvertFromClassFunctionDeclaration(path);\n      this.maybeConvertFromClassFunctionExpression(path);\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,6BAAA,GAAAC,OAAA;AACA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAGA,MAAMK,aAA+B,GAAG;EACtCC,oBAAoBA,CAAC;IAAEC;EAAK,CAAC,EAAEC,KAAK,EAAE;IACpC,IAAID,IAAI,CAACE,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAE;MAC/BH,IAAI,CAACE,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC3B;EACF,CAAC;EAEDC,KAAKA,CAACC,IAAI,EAAEL,KAAK,EAAE;IACjB,IACE,CAACK,IAAI,CAACC,KAAK,CAACC,uBAAuB,CACjCP,KAAK,CAACE,OAAO,EACbF,KAAK,CAACQ,OAAO,CAACC,UAChB,CAAC,EACD;MACAJ,IAAI,CAACK,IAAI,CAAC,CAAC;MACX,IAAIL,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE;QACnB,IAAAC,yDAA+B,EAACP,IAAI,CAAC;MACvC;IACF;EACF,CAAC;EAEDQ,cAAcA,CAAC;IAAEd,IAAI;IAAEO;EAAM,CAAC,EAAEN,KAAK,EAAE;IACrC,MAAM;MAAEC;IAAK,CAAC,GAAGF,IAAI,CAACe,GAAiB;IACvC,IACEf,IAAI,CAACgB,SAAS,KAKbd,IAAI,KAAKD,KAAK,CAACE,OAAO,IAAID,IAAI,KAAKD,KAAK,CAACG,OAAO,CAAC,IAElDG,KAAK,CAACU,oBAAoB,CAACf,IAAI,CAAC,KAAKD,KAAK,CAACQ,OAAO,CAACC,UAAU,EAC7D;MACAV,IAAI,CAACgB,SAAS,GAAG,KAAK;MACa;QAAA,IAAAE,WAAA;QACjC,KAAAA,WAAA,GAAIlB,IAAI,CAACmB,KAAK,aAAVD,WAAA,CAAYF,SAAS,EAAEhB,IAAI,CAACmB,KAAK,CAACH,SAAS,GAAG,KAAK;MACzD;IACF;EACF,CAAC;EAED,qDAAqDI,CACnDd,IAA0E,EAC1EL,KAAK,EACL;IACA,IAAIK,IAAI,CAACe,qBAAqB,CAAC,CAAC,EAAE;IAClC,MAAMC,GAAG,GAAGhB,IAAI,CAACiB,0BAA0B,CAAC,CAAC;IAE7C,KAAK,MAAMrB,IAAI,IAAIoB,GAAG,EAAE;MACtB,IAAIpB,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAEmB,GAAG,CAACpB,IAAI,CAAC,CAACA,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC5D;EACF;AACF,CAAC;AAEc,MAAMoB,OAAO,CAAC;EAC3BC,WAAWA,CAAChB,OAAgB,EAAEN,OAAe,EAAEC,OAAe,EAAE;IAC9D,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,OAAO,GAAGA,OAAO;EACxB;EAMAiB,iCAAiCA,CAACC,YAAsB,EAAE;IACxD,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,UAAU;IAEjD,IAAI,CAACD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC,EAAE;MAC5C;IACF;IAEA,IAAIF,iBAAiB,CAACG,0BAA0B,CAAC,CAAC,EAAE;MAClD,MAAM;QAAEC;MAAY,CAAC,GAAGJ,iBAAiB,CAAC5B,IAAI;MAC9C,IAAIN,CAAC,CAACuC,aAAa,CAACD,WAAW,CAAC,IAAI,CAACA,WAAW,CAACE,EAAE,EAAE;QACnD;MACF;IACF;IAEA,IAAIN,iBAAiB,CAACO,sBAAsB,CAAC,CAAC,EAAE;MAC9C;IACF;IAEA,IAAAC,qCAAsB,EACpBR,iBAGF,CAAC;EACH;EAEAS,wCAAwCA,CAAC/B,IAAc,EAAE;IACvD,OAAOA,IAAI;EAeb;EAEAgC,uCAAuCA,CAAChC,IAAc,EAAE;IACtD,OAAOA,IAAI;EAgBb;EAEAiC,MAAMA,CAAA,EAAiD;IACrD,MAAM;MAAE9B,OAAO;MAAEN,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC1C,MAAM;MAAEG,KAAK;MAAED;IAAK,CAAC,GAAGG,OAAO;IAE/B,MAAMkB,YAAY,GAAGrB,IAAI,CAACkC,IAAI,CAC5BlC,IAAI,IACFA,IAAI,CAAC2B,aAAa,CAAC,CAAC,IACpB3B,IAAI,CAACmC,oBAAoB,CAAC,CAAC,IAC3BnC,IAAI,CAACoC,iBAAiB,CAAC,CAC3B,CAAC;IACD,IAAIf,YAAY,EAAE;MAChB,MAAMgB,UAAU,GAAGhB,YAAY,CAACJ,0BAA0B,CAAC,CAAC;MAC5D,IAAIoB,UAAU,CAACxC,OAAO,CAAC,KAAKM,OAAO,CAACC,UAAU,EAAE;QAG9C,IAAI,CAACgB,iCAAiC,CAACC,YAAY,CAAC;MACtD;IACF;IAEA,MAAMiB,eAAe,GAEhBC,SAAS,CAAC,CAAC,CAAC,IAA+BtC,KAAK,CAACuC,KAAK;IAC3D,IAAAC,0BAAY,EACVH,eAAe,EACf,IAAAI,iBAAO,EAAClD,aAAa,CAAC,EACtBS,KAAK,EACL,IAAI,EACJA,KAAK,CAACD,IAAI,EAGV;MAAE2C,YAAY,EAAE;IAAK,CACvB,CAAC;IAMM,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAE;MACxBtC,KAAK,CAAC2C,gBAAgB,CAAC/C,OAAO,CAAC;MAC/BI,KAAK,CAAC4C,QAAQ,CAAC/C,OAAO,CAAC,GAAGK,OAAO;MACjC,IAAI,CAACA,OAAO,CAACC,UAAU,CAACR,IAAI,GAAGE,OAAO;IACxC;IAEA,IAAIuB,YAAY,EAAE;MAChB,IAAI,CAACU,wCAAwC,CAAC/B,IAAI,CAAC;MACnD,IAAI,CAACgC,uCAAuC,CAAChC,IAAI,CAAC;IACpD;EACF;AACF;AAAC8C,OAAA,CAAAC,OAAA,GAAA7B,OAAA", "ignoreList": []}