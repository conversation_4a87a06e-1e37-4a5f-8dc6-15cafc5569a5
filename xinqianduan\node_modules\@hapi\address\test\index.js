'use strict';

const Punycode = require('punycode');

const Code = require('@hapi/code');
const Address = require('..');
const Lab = require('@hapi/lab');


const internals = {};


const { describe, it } = exports.lab = Lab.script();
const expect = Code.expect;


describe('email', () => {

    it('available as direct require', () => {

        expect(require('../lib/email').isValid('<EMAIL>')).to.be.true();
    });

    describe('analyze()', () => {

        it('identifies error', () => {

            const tests = [
                ['', 'Address must be a non-empty string'],
                ['ê**************', 'Address contains forbidden Unicode characters', { allowUnicode: false }],
                ['test@test@test', 'Address cannot contain more than one @ character'],
                ['test', 'Address must contain one @ character'],
                ['@example.com', 'Address local part cannot be empty'],
                ['test@', 'Domain must be a non-empty string'],
                ['<EMAIL>', 'Address too long'],
                ['<EMAIL>', 'Address local part too long'],
                ['<EMAIL>', 'Address local part contains empty dot-separated segment'],
                ['x:<EMAIL>', 'Address local part contains invalid character'],
                ['ê:<EMAIL>', 'Address local part contains invalid character'],
                ['test@com', 'Domain lacks the minimum required number of segments'],
                ['<EMAIL>-such-tld', 'Domain uses forbidden TLD'],
                ['<EMAIL>', 'Domain contains empty dot-separated segment'],
                ['<EMAIL>', 'Domain contains dot-separated segment that is too long'],
                ['test@example+.com', 'Domain contains invalid character', { tlds: false }],
                ['test@example.com_', 'Domain contains invalid tld character', { tlds: false }]
            ];

            for (let i = 0; i < tests.length; ++i) {
                const email = tests[i];
                const output = Address.email.analyze(email[0], email[2]);
                const result = email[1];

                if (!output ||
                    output.error !== result) {

                    console.log(i, email[0]);
                }

                expect(output.error).to.equal(result);
            }
        });

        it('validates options', () => {

            const tests = [
                ['<EMAIL>', 'Invalid options: tlds must be a boolean or an object', { tlds: 1 }],
                ['<EMAIL>', 'Invalid options: tlds.allow must be a Set object or true', { tlds: { allow: ['test'] } }],
                ['<EMAIL>', 'Invalid options: tlds.deny must be a Set object', { tlds: { deny: ['test'] } }],
                ['<EMAIL>', 'Invalid options: cannot specify both tlds.allow and tlds.deny lists', { tlds: { allow: new Set(), deny: new Set() } }],
                [1, 'Invalid input: email must be a string']
            ];

            for (let i = 0; i < tests.length; ++i) {
                const email = tests[i];
                expect(() => Address.email.analyze(email[0], email[2])).to.throw(email[1]);
            }
        });

        describe('validated TLD', () => {

            it('applies built-in list', () => {

                expect(Address.email.analyze('<EMAIL>')).to.not.exist();
                expect(Address.email.analyze('<EMAIL>', { tlds: true })).to.not.exist();
                expect(Address.email.analyze('<EMAIL>', { tlds: { allow: true } })).to.not.exist();
            });

            it('ignores built-in list', () => {

                expect(Address.email.analyze('<EMAIL>-top', { tlds: false })).to.not.exist();
            });

            it('denies listed tls', () => {

                expect(Address.email.analyze('<EMAIL>', { tlds: { deny: new Set(['test']) } })).to.not.exist();
                expect(Address.email.analyze('<EMAIL>', { tlds: { deny: new Set(['com']) } })).to.equal({ error: 'Domain uses forbidden TLD' });
            });
        });
    });

    describe('isValid()', () => {

        it('validates email', () => {

            // Tests adapted from https://github.com/skeggse/isemail
            // Copyright (c) 2008-2019, Eli Skeggs, Dominic Sayers, GlobeSherpa

            const tests = [
                ['\r', false],
                ['test', false],
                ['@', false],
                ['test@', false],
                ['test@io', false],
                ['test@io', true, { minDomainSegments: 1 }],
                ['@io', false],
                ['@iana.org', false],
                ['<EMAIL>', true],
                ['<EMAIL>', true],
                ['<EMAIL>', true],
                ['<EMAIL>', true],
                ['ê**************', true],
                ['ñoñó*************', true],
                ['ñoñó******************', true],
                ['伊昭傑@郵件.商務', true, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['\ud801\udc37\ud852\<EMAIL>', true],
                ['<EMAIL>', true],
                ['.<EMAIL>', false],
                ['<EMAIL>', false],
                ['test..iana.org', false],
                ['test_exa-mple.com', false],
                ['!#$%&`*+/=?^`{|}~@iana.org', true],
                ['test\\@<EMAIL>', false],
                ['<EMAIL>', true],
                ['<EMAIL>', true],
                ['test@iana.123', false],
                ['test@***************', false],
                ['<EMAIL>', true],
                ['<EMAIL>', false],
                ['\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\<EMAIL>', false],
                ['test@abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghiklm', false],
                ['test@\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06.org', true],
                ['test@abcdefghijklmnopqrstuvwxyzabcdefghijklmno\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06.org', false],
                ['<EMAIL>', false],
                ['<EMAIL>', true],
                ['<EMAIL>', false],
                ['<EMAIL>', false],
                ['<EMAIL>', false],
                ['<EMAIL>.', false],
                ['<EMAIL>', false],
                ['<EMAIL>', false],
                ['<EMAIL>.\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06', false],
                ['<EMAIL>', false],
                ['<EMAIL>\ud83d\ude06', false],
                ['<EMAIL>\ud83d\ude06', false],
                ['<EMAIL>', false],
                ['<EMAIL>.\ud83d\ude06', false],
                ['\"\r', false],
                ['\"test\"@iana.org', false],
                ['\"\"@iana.org', false],
                ['\"\"\"@iana.org', false],
                ['\"\\a\"@iana.org', false],
                ['\"\\\"\"@iana.org', false],
                ['\"\\\"@iana.org', false],
                ['\"\\\\\"@iana.org', false],
                ['test\"@iana.org', false],
                ['\"<EMAIL>', false],
                ['\"test\"<EMAIL>', false],
                ['test\"text\"@iana.org', false],
                ['\"test\"\"test\"@iana.org', false],
                ['\"test\".\"test\"@iana.org', false],
                ['\"test\\ test\"@iana.org', false],
                ['\"test\".<EMAIL>', false],
                ['\"test\u0000\"@iana.org', false],
                ['\"test\\\u0000\"@iana.org', false],
                ['\"test\r\n test\"@iana.org', false],
                ['\"abcdefghijklmnopqrstuvwxyz abcdefghijklmnopqrstuvwxyz abcdefghj\"@iana.org', false],
                ['\"abcdefghijklmnopqrstuvwxyz abcdefghijklmnopqrstuvwxyz abcdefg\\h\"@iana.org', false],
                ['test@[***************]', false],
                ['test@a[***************]', false],
                ['test@[255.255.255]', false],
                ['test@[***************.255]', false],
                ['test@[255.255.255.256]', false],
                ['test@[1111:2222:3333:4444:5555:6666:7777:8888]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888:9999]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:888G]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666::8888]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555::8888]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666::7777:8888]', false],
                ['test@[IPv6::3333:4444:5555:6666:7777:8888]', false],
                ['test@[IPv6:::3333:4444:5555:6666:7777:8888]', false],
                ['test@[IPv6:1111::4444:5555::8888]', false],
                ['test@[IPv6:::]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:***************]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:***************]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:***************]', false],
                ['test@[IPv6:1111:2222:3333:4444::***************]', false],
                ['test@[IPv6:1111:2222:3333:4444:5555:6666::***************]', false],
                ['test@[IPv6:1111:2222:3333:4444:::***************]', false],
                ['test@[IPv6::***************]', false],
                ['test@[***************].local', false],
                ['test@local.[***************]', false],
                ['test@local.[***************].local', false],
                ['test@local.(comment)[***************].local', false],
                ['test@local. [***************].local', false],
                ['test@local.[***************](comment).local', false],
                ['test@local.[***************] .local', false],
                [' test @iana.org', false],
                ['test@ iana .com', false],
                ['test . <EMAIL>', false],
                ['\r\n <EMAIL>', false],
                ['\r\n \r\n <EMAIL>', false],
                ['(\r', false],
                ['(comment)<EMAIL>', false],
                ['((comment)<EMAIL>', false],
                ['(comment(comment))<EMAIL>', false],
                ['test@(comment)iana.org', false],
                ['test(comment)@iana.org', false],
                ['test(comment)<EMAIL>', false],
                ['test@(comment)[***************]', false],
                ['(comment)<EMAIL>', false],
                ['test@(comment)abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefg.com', false],
                ['(comment)<EMAIL>', false],
                ['<EMAIL>\n', false],
                ['<EMAIL>', true],
                ['<EMAIL>-', false],
                ['\"<EMAIL>', false],
                ['(<EMAIL>', false],
                ['test@(iana.org', false],
                ['test@[1.2.3.4', false],
                ['\"test\\\"@iana.org', false],
                ['(comment\\)<EMAIL>', false],
                ['<EMAIL>(comment\\)', false],
                ['<EMAIL>(comment\\', false],
                ['test@[RFC-5322-domain-literal]', false],
                ['test@[RFC-5322-郵件ñó-domain-literal]', false],
                ['test@[RFC-5322]-domain-literal]', false],
                ['test@[RFC-5322].domain-literal]', false],
                ['test@[RFC-5322-[domain-literal]', false],
                ['test@[', false],
                ['test@[\u0007]', false],
                ['test@[RFC-5322-\\\u0007-domain-literal]', false],
                ['test@[RFC-5322-\\\t-domain-literal]', false],
                ['test@[RFC-5322-\\]-domain-literal]', false],
                ['test@[RFC-5322-\\郵-no-domain-literal]', false],
                ['test@[RFC-5322--domain-literal]', false],
                ['test@[RFC-5322-domain-literal\\]', false],
                ['test@[RFC-5322-domain-literal\\', false],
                ['test@[RFC 5322 domain literal]', false],
                ['test@[RFC-5322-domain-literal] (comment)', false],
                ['@iana.org', false],
                ['test@.org', false],
                ['\"\"@iana.org', false],
                ['\"\"@iana.org', false],
                ['\"\\\"@iana.org', false],
                ['()<EMAIL>', false],
                ['()<EMAIL>', false],
                ['<EMAIL>\r', false],
                ['\<EMAIL>', false],
                ['\"\rtest\"@iana.org', false],
                ['(\r)<EMAIL>', false],
                ['<EMAIL>(\r)', false],
                ['test@<iana>.org', false],
                ['\<EMAIL>', false],
                ['\"\n\"@iana.org', false],
                ['\"\\\n\"@iana.org', false],
                ['(\n)<EMAIL>', false],
                ['\<EMAIL>', false],
                ['test@\u0007.org', false],
                ['\"\u0007\"@iana.org', false],
                ['\"\\\u0007\"@iana.org', false],
                ['(\u0007)<EMAIL>', false],
                ['\r\<EMAIL>', false],
                ['\r\n \r\<EMAIL>', false],
                [' \r\<EMAIL>', false],
                [' \r\n <EMAIL>', false],
                [' \r\n \r\<EMAIL>', false],
                [' \r\n\r\<EMAIL>', false],
                [' \r\n\r\n <EMAIL>', false],
                ['<EMAIL>\r\n ', false],
                ['<EMAIL>\r\n \r\n ', false],
                ['<EMAIL>\r\n', false],
                ['<EMAIL> \r', false],
                ['<EMAIL>\r\n \r\n', false],
                ['<EMAIL> \r\n', false],
                ['<EMAIL> \r\n ', false],
                ['<EMAIL> \r\n \r\n', false],
                ['<EMAIL> \r\n\r\n', false],
                ['<EMAIL> \r\n\r\n ', false],
                ['test@iana. org', false],
                ['test@[\r', false],
                ['test@[\r\n', false],
                [' <EMAIL>', false],
                ['<EMAIL> ', false],
                ['test@[IPv6:1::2:]', false],
                ['\"test\\\u0094\"@iana.org', false],
                ['test@iana/icann.org', false],
                ['test@iana!icann.org', false],
                ['test@iana?icann.org', false],
                ['test@iana^icann.org', false],
                ['test@iana{icann}.org', false],
                ['test.(comment)<EMAIL>', false],
                ['test@iana.(comment)org', false],
                ['test@iana(comment)iana.org', false],
                ['(comment\r\n comment)<EMAIL>', false],
                ['test@org', true, { minDomainSegments: 1 }],
                ['test\ud800@invalid', false],
                ['\"\ud800\"@invalid', false],
                ['\"\\\ud800\"@invalid', false],
                ['(\ud800)thing@invalid', false],
                ['\"\\\ud800\"@invalid', false],
                ['test@\ud800\udfffñoñó郵件ñoñó郵件.郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.noñó郵件ñoñó郵.商務', false, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['test@\ud800\udfffñoñó郵件ñoñó郵件.郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.oñó郵件ñoñó郵件ñoñó郵件.商務', false, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['test@ñoñoñó郵件\ud83d\ude06ñoñ.oñó郵件\uc138ñoñ.oñó郵件\u0644\u4eec\u010dñoñoñó郵件\u05dcño.ñoñó郵件\u092f\u672cñoñoñó郵件\uc138añoñ.oñó郵件\ud83d\ude06bc\uc138郵\ud83d\ude06ño.ñoñó郵件ñoñoñó郵件\ud83d\ude06ñoñoñó郵件\uc138ñoñ.oñó郵件\u0644\u4eecñoñoñó.郵件\ud83d\ude06ñoñoñó郵件郵\uc138ñoñoñó郵件\u0644\u4eecñoñoñó郵件.\ud83d\ude06ñoñoñó郵件郵\uc138\u0644\u4eec.郵件\ud83d\ude06ñoñoñó郵.件郵\uc138\u4eec\ud83d\ude06ñoñoñó件郵\uc138ñoñoñó郵件', false, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['test@ñoñó郵件ñoñó郵件ñoñó郵件ñoñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件.商務', false, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['\ud83d\ude06ñoñó郵件ñoñó郵件ñoñó\ud83d\ude06郵件ñoñoñó郵@\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.郵件ñoñó郵件ñoñó\ud83d\ude06.郵件ñoñó郵件ñoñó.郵件ñoñó郵件.ñoñó郵件ñoñó.郵件ñoñó郵件.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06商務.郵件ñoñó郵件ñoñó郵件.\ud83d\ude06商務.\ud83d\ude06商務.\ud83d\ude06商務', false, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['test@[\0', false],
                ['(\0)<EMAIL>', false],
                ['shouldbe@invalid', false],
                ['shouldbe@INVALID', false],
                ['<EMAIL>', true],
                ['<EMAIL>', true],
                ['<EMAIL>', false],
                ['shouldbe@XN--UNUP4Y', true, { minDomainSegments: 1 }],
                ['shouldbe@xn--unup4y', true, { minDomainSegments: 1 }],
                ['shouldbe@\u6e38\u620f', true, { minDomainSegments: 1 }],
                ['æøå', false],
                ['<EMAIL>', true, { ignoreLength: true }],
                ['<EMAIL>@example.com', false],
                ['<EMAIL>/path', false],
                ['<EMAIL>:123', false]
            ];

            for (let i = 0; i < tests.length; ++i) {
                const email = tests[i];
                const valid = Address.email.isValid(email[0], email[2]);
                const result = email[1];

                if (valid !== result) {
                    const outcome = Address.email.analyze(email[0], email[2]);
                    if (outcome) {
                        console.log(i, email[0], outcome.error);
                    }
                    else {
                        console.log(i, email[0]);
                    }
                }

                expect(valid).to.equal(result);
            }
        });
    });
});

describe('domain', () => {

    it('available as direct require', () => {

        expect(require('../lib/domain').isValid('example.com')).to.be.true();
    });

    describe('analyze()', () => {

        it('identifies error', () => {

            const tests = [
                ['', 'Domain must be a non-empty string'],
                ['êiana.org', 'Domain contains forbidden Unicode characters', { allowUnicode: false }],
                ['abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz.com', 'Domain too long'],
                ['com', 'Domain lacks the minimum required number of segments'],
                ['x.no-such-tld', 'Domain uses forbidden TLD'],
                ['example..com', 'Domain contains empty dot-separated segment'],
                ['1234567890123456789012345678901234567890123456789012345678901234567890.com', 'Domain contains dot-separated segment that is too long'],
                ['example+.com', 'Domain contains invalid character', { tlds: false }],
                ['example.com_', 'Domain contains invalid tld character', { tlds: false }]
            ];

            for (let i = 0; i < tests.length; ++i) {
                const domain = tests[i];
                const output = Address.domain.analyze(domain[0], domain[2]);
                const result = domain[1];

                if (!output ||
                    output.error !== result) {

                    console.log(i, domain[0]);
                }

                expect(output.error).to.equal(result);
            }
        });

        it('validates options', () => {

            const tests = [
                ['example.com', 'Invalid options: tlds must be a boolean or an object', { tlds: 1 }],
                ['example.com', 'Invalid options: tlds.allow must be a Set object or true', { tlds: { allow: ['test'] } }],
                ['example.com', 'Invalid options: tlds.deny must be a Set object', { tlds: { deny: ['test'] } }],
                ['example.com', 'Invalid options: cannot specify both tlds.allow and tlds.deny lists', { tlds: { allow: new Set(), deny: new Set() } }],
                [1, 'Invalid input: domain must be a string']
            ];

            for (let i = 0; i < tests.length; ++i) {
                const domain = tests[i];
                expect(() => Address.domain.analyze(domain[0], domain[2])).to.throw(domain[1]);
            }
        });
    });

    describe('isValid()', () => {

        it('validates domain', () => {

            const tests = [
                ['\r', false],
                ['test', false],
                ['@', false],
                ['iana.org', true],
                ['nominet.org.uk', true],
                ['about.museum', true],
                ['x.商務', true, { tlds: { allow: new Set([Punycode.toASCII('商務')]) } }],
                ['iana.123', false],
                ['***************', false],
                ['XN--UNUP4Y', true, { minDomainSegments: 1 }],
                ['<EMAIL>', false],
                ['test:example.com', false],
                ['example.com:123', false],
                ['example.com/path', false]
            ];

            for (let i = 0; i < tests.length; ++i) {
                const domain = tests[i];
                const valid = Address.domain.isValid(domain[0], domain[2]);
                const result = domain[1];

                if (valid !== result) {
                    const outcome = Address.domain.analyze(domain[0], domain[2]);
                    if (outcome) {
                        console.log(i, domain[0], outcome.error);
                    }
                    else {
                        console.log(i, domain[0]);
                    }
                }

                expect(valid).to.equal(result);
            }
        });
    });
});
