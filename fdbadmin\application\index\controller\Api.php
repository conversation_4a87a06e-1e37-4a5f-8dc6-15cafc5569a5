<?php
namespace app\index\controller;
use think\Request;
use untils\JsonService;
use models\{<PERSON>s,Configs,Zhuany<PERSON>
};
use think\Controller;
use think\facade\View;

class Api extends Controller{
	private $configs = [];
	public function __construct(Configs $model){
		$configs = $model->getAllData();
        $this->configs =$configs;  
	}
	public  function lawyerCateList(Zhuanyes $model){
		$list = $model->select();
		if(!empty($list)) return JsonService::successful("1",$list);
		else return JsonService::fail("2");
		
	}
}