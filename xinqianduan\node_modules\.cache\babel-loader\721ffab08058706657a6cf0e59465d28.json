{"remainingRequest": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\src\\main.js", "dependencies": [{"path": "H:\\fdbfront\\src\\main.js", "mtime": 1732626900071}, {"path": "H:\\fdbfront\\babel.config.js", "mtime": 1732626900032}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnLi9yb3V0ZXInOwppbXBvcnQgc3RvcmUgZnJvbSAnLi9zdG9yZSc7CmltcG9ydCBFbGVtZW50VUkgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCAnZWxlbWVudC11aS9saWIvdGhlbWUtY2hhbGsvaW5kZXguY3NzJzsKaW1wb3J0IHsgcG9zdFJlcXVlc3QgfSBmcm9tICIuL3V0aWxzL2FwaSI7CmltcG9ydCB7IHBvc3RLZXlWYWx1ZVJlcXVlc3QgfSBmcm9tICIuL3V0aWxzL2FwaSI7CmltcG9ydCB7IHB1dFJlcXVlc3QgfSBmcm9tICIuL3V0aWxzL2FwaSI7CmltcG9ydCB7IGRlbGV0ZVJlcXVlc3QgfSBmcm9tICIuL3V0aWxzL2FwaSI7CmltcG9ydCB7IGdldFJlcXVlc3QgfSBmcm9tICIuL3V0aWxzL2FwaSI7ClZ1ZS5wcm90b3R5cGUucG9zdFJlcXVlc3QgPSBwb3N0UmVxdWVzdDsKVnVlLnByb3RvdHlwZS5wb3N0S2V5VmFsdWVSZXF1ZXN0ID0gcG9zdEtleVZhbHVlUmVxdWVzdDsKVnVlLnByb3RvdHlwZS5wdXRSZXF1ZXN0ID0gcHV0UmVxdWVzdDsKVnVlLnByb3RvdHlwZS5kZWxldGVSZXF1ZXN0ID0gZGVsZXRlUmVxdWVzdDsKVnVlLnByb3RvdHlwZS5nZXRSZXF1ZXN0ID0gZ2V0UmVxdWVzdDsKVnVlLmNvbmZpZy5wcm9kdWN0aW9uVGlwID0gZmFsc2U7ClZ1ZS51c2UoRWxlbWVudFVJKTsKbmV3IFZ1ZSh7CiAgcm91dGVyLAogIHN0b3JlLAogIHJlbmRlcjogaCA9PiBoKEFwcCkKfSkuJG1vdW50KCcjYXBwJyk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "postRequest", "postKeyValueRequest", "putRequest", "deleteRequest", "getRequest", "prototype", "config", "productionTip", "use", "render", "h", "$mount"], "sources": ["H:/fdbfront/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport ElementUI from 'element-ui';\r\nimport 'element-ui/lib/theme-chalk/index.css';\r\nimport {postRequest} from \"./utils/api\";\r\nimport {postKeyValueRequest} from \"./utils/api\";\r\nimport {putRequest} from \"./utils/api\";\r\nimport {deleteRequest} from \"./utils/api\";\r\nimport {getRequest} from \"./utils/api\";\r\nVue.prototype.postRequest = postRequest;\r\nVue.prototype.postKeyValueRequest = postKeyValueRequest;\r\nVue.prototype.putRequest = putRequest;\r\nVue.prototype.deleteRequest = deleteRequest;\r\nVue.prototype.getRequest = getRequest;\r\nVue.config.productionTip = false\r\n\r\nVue.use(ElementUI);\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,SAAQC,WAAW,QAAO,aAAa;AACvC,SAAQC,mBAAmB,QAAO,aAAa;AAC/C,SAAQC,UAAU,QAAO,aAAa;AACtC,SAAQC,aAAa,QAAO,aAAa;AACzC,SAAQC,UAAU,QAAO,aAAa;AACtCT,GAAG,CAACU,SAAS,CAACL,WAAW,GAAGA,WAAW;AACvCL,GAAG,CAACU,SAAS,CAACJ,mBAAmB,GAAGA,mBAAmB;AACvDN,GAAG,CAACU,SAAS,CAACH,UAAU,GAAGA,UAAU;AACrCP,GAAG,CAACU,SAAS,CAACF,aAAa,GAAGA,aAAa;AAC3CR,GAAG,CAACU,SAAS,CAACD,UAAU,GAAGA,UAAU;AACrCT,GAAG,CAACW,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCZ,GAAG,CAACa,GAAG,CAACT,SAAS,CAAC;AAClB,IAAIJ,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLW,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACd,GAAG;AACpB,CAAC,CAAC,CAACe,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}