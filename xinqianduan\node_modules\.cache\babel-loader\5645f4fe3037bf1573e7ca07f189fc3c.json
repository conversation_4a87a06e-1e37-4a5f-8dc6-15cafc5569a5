{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue?vue&type=template&id=d4a8e664&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue", "mtime": 1748604247132}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "type", "on", "click", "handleUpload", "_v", "disabled", "selectedFiles", "length", "handleBatchArchive", "handleBatchDownload", "handleBatchDelete", "staticStyle", "width", "data", "fileList", "handleSelectionChange", "prop", "label", "scopedSlots", "_u", "key", "fn", "scope", "class", "getFileIcon", "row", "fileType", "_s", "fileName", "formatFileSize", "size", "$event", "handlePreview", "handleDownload", "handleDelete", "title", "visible", "uploadDialogVisible", "update:visible", "drag", "multiple", "action", "uploadUrl", "beforeUpload", "handleProgress", "handleUploadSuccess", "handleUploadError", "uploadFileList", "slot", "previewDialogVisible", "fullscreen", "isImage", "src", "previewUrl", "alt", "isPdf", "height", "isOffice", "staticRenderFns", "_withStripped"], "sources": ["H:/fdbfront/xinqianduan/src/views/pages/archive/File.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"archive-container\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"operation-bar\" },\n        [\n          _c(\n            \"el-button-group\",\n            [\n              _c(\n                \"el-button\",\n                { attrs: { type: \"primary\" }, on: { click: _vm.handleUpload } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-upload\" }),\n                  _vm._v(\" 上传文件 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"success\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchArchive },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-folder-add\" }),\n                  _vm._v(\" 批量归档 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"warning\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchDownload },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-download\" }),\n                  _vm._v(\" 批量下载 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"danger\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchDelete },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-delete\" }),\n                  _vm._v(\" 批量删除 \"),\n                ]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"file-list-container\" },\n        [\n          _c(\n            \"el-table\",\n            {\n              staticStyle: { width: \"100%\" },\n              attrs: { data: _vm.fileList },\n              on: { \"selection-change\": _vm.handleSelectionChange },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"fileName\",\n                  label: \"文件名\",\n                  \"min-width\": \"200\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"div\", { staticClass: \"file-name-cell\" }, [\n                          _c(\"i\", {\n                            class: _vm.getFileIcon(scope.row.fileType),\n                          }),\n                          _c(\"span\", [_vm._v(_vm._s(scope.row.fileName))]),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"fileType\", label: \"类型\", width: \"100\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"category\", label: \"分类\", width: \"120\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"size\", label: \"大小\", width: \"120\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \" \" + _vm._s(_vm.formatFileSize(scope.row.size)) + \" \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"uploadTime\", label: \"上传时间\", width: \"180\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"操作\", width: \"200\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { size: \"mini\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handlePreview(scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"预览\")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { size: \"mini\", type: \"success\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDownload(scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"下载\")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { size: \"mini\", type: \"danger\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"文件上传\",\n            visible: _vm.uploadDialogVisible,\n            width: \"500px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.uploadDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-upload\",\n            {\n              staticClass: \"upload-demo\",\n              attrs: {\n                drag: \"\",\n                multiple: \"\",\n                action: _vm.uploadUrl,\n                \"before-upload\": _vm.beforeUpload,\n                \"on-progress\": _vm.handleProgress,\n                \"on-success\": _vm.handleUploadSuccess,\n                \"on-error\": _vm.handleUploadError,\n                \"file-list\": _vm.uploadFileList,\n              },\n            },\n            [\n              _c(\"i\", { staticClass: \"el-icon-upload\" }),\n              _c(\"div\", { staticClass: \"el-upload__text\" }, [\n                _vm._v(\"将文件拖到此处，或\"),\n                _c(\"em\", [_vm._v(\"点击上传\")]),\n              ]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"el-upload__tip\",\n                  attrs: { slot: \"tip\" },\n                  slot: \"tip\",\n                },\n                [_vm._v(\" 支持任意格式文件，单个文件不超过500MB \")]\n              ),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"文件预览\",\n            visible: _vm.previewDialogVisible,\n            width: \"80%\",\n            fullscreen: true,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.previewDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\"div\", { staticClass: \"preview-container\" }, [\n            _vm.isImage\n              ? _c(\"div\", { staticClass: \"image-preview\" }, [\n                  _c(\"img\", {\n                    attrs: { src: _vm.previewUrl, alt: \"预览图片\" },\n                  }),\n                ])\n              : _vm.isPdf\n              ? _c(\"div\", { staticClass: \"pdf-preview\" }, [\n                  _c(\"iframe\", {\n                    attrs: {\n                      src: _vm.previewUrl,\n                      width: \"100%\",\n                      height: \"600px\",\n                    },\n                  }),\n                ])\n              : _vm.isOffice\n              ? _c(\"div\", { staticClass: \"office-preview\" }, [\n                  _c(\"iframe\", {\n                    attrs: {\n                      src: _vm.previewUrl,\n                      width: \"100%\",\n                      height: \"600px\",\n                    },\n                  }),\n                ])\n              : _c(\"div\", { staticClass: \"other-preview\" }, [\n                  _c(\"p\", [_vm._v(\"该文件类型暂不支持预览，请下载后查看\")]),\n                ]),\n          ]),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,iBAAiB,EACjB,CACEA,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACQ;IAAa;EAAE,CAAC,EAC/D,CACEP,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,SAAS;MACfK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACa;IAAmB;EACtC,CAAC,EACD,CACEZ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,SAAS;MACfK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACc;IAAoB;EACvC,CAAC,EACD,CACEb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,QAAQ;MACdK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACe;IAAkB;EACrC,CAAC,EACD,CACEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,UAAU,EACV;IACEe,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9Bb,KAAK,EAAE;MAAEc,IAAI,EAAElB,GAAG,CAACmB;IAAS,CAAC;IAC7Bb,EAAE,EAAE;MAAE,kBAAkB,EAAEN,GAAG,CAACoB;IAAsB;EACtD,CAAC,EACD,CACEnB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEC,IAAI,EAAE,WAAW;MAAEY,KAAK,EAAE;IAAK;EAC1C,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MACLiB,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,KAAK;MACZ,WAAW,EAAE;IACf,CAAC;IACDC,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1B,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,GAAG,EAAE;UACN2B,KAAK,EAAE5B,GAAG,CAAC6B,WAAW,CAACF,KAAK,CAACG,GAAG,CAACC,QAAQ;QAC3C,CAAC,CAAC,EACF9B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACS,EAAE,CAACT,GAAG,CAACgC,EAAE,CAACL,KAAK,CAACG,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFhC,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM;EACvD,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM;EACvD,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM,CAAC;IAClDM,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL3B,GAAG,CAACS,EAAE,CACJ,GAAG,GAAGT,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACkC,cAAc,CAACP,KAAK,CAACG,GAAG,CAACK,IAAI,CAAC,CAAC,GAAG,GACrD,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFlC,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAM;EAC3D,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEkB,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM,CAAC;IACpCM,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1B,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE;UAAO,CAAC;UACvB7B,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU6B,MAAM,EAAE;cACvB,OAAOpC,GAAG,CAACqC,aAAa,CAACV,KAAK,CAACG,GAAG,CAAC;YACrC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDR,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE,MAAM;YAAE9B,IAAI,EAAE;UAAU,CAAC;UACxCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU6B,MAAM,EAAE;cACvB,OAAOpC,GAAG,CAACsC,cAAc,CAACX,KAAK,CAACG,GAAG,CAAC;YACtC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDR,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE,MAAM;YAAE9B,IAAI,EAAE;UAAS,CAAC;UACvCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU6B,MAAM,EAAE;cACvB,OAAOpC,GAAG,CAACuC,YAAY,CAACZ,KAAK,CAACG,GAAG,CAAC;YACpC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLoC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAEzC,GAAG,CAAC0C,mBAAmB;MAChCzB,KAAK,EAAE;IACT,CAAC;IACDX,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAqC,CAAUP,MAAM,EAAE;QAClCpC,GAAG,CAAC0C,mBAAmB,GAAGN,MAAM;MAClC;IACF;EACF,CAAC,EACD,CACEnC,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MACLwC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE9C,GAAG,CAAC+C,SAAS;MACrB,eAAe,EAAE/C,GAAG,CAACgD,YAAY;MACjC,aAAa,EAAEhD,GAAG,CAACiD,cAAc;MACjC,YAAY,EAAEjD,GAAG,CAACkD,mBAAmB;MACrC,UAAU,EAAElD,GAAG,CAACmD,iBAAiB;MACjC,WAAW,EAAEnD,GAAG,CAACoD;IACnB;EACF,CAAC,EACD,CACEnD,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACS,EAAE,CAAC,WAAW,CAAC,EACnBR,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEiD,IAAI,EAAE;IAAM,CAAC;IACtBA,IAAI,EAAE;EACR,CAAC,EACD,CAACrD,GAAG,CAACS,EAAE,CAAC,yBAAyB,CAAC,CACpC,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLoC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAEzC,GAAG,CAACsD,oBAAoB;MACjCrC,KAAK,EAAE,KAAK;MACZsC,UAAU,EAAE;IACd,CAAC;IACDjD,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAqC,CAAUP,MAAM,EAAE;QAClCpC,GAAG,CAACsD,oBAAoB,GAAGlB,MAAM;MACnC;IACF;EACF,CAAC,EACD,CACEnC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACwD,OAAO,GACPvD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MAAEqD,GAAG,EAAEzD,GAAG,CAAC0D,UAAU;MAAEC,GAAG,EAAE;IAAO;EAC5C,CAAC,CAAC,CACH,CAAC,GACF3D,GAAG,CAAC4D,KAAK,GACT3D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,QAAQ,EAAE;IACXG,KAAK,EAAE;MACLqD,GAAG,EAAEzD,GAAG,CAAC0D,UAAU;MACnBzC,KAAK,EAAE,MAAM;MACb4C,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,CAAC,GACF7D,GAAG,CAAC8D,QAAQ,GACZ7D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,QAAQ,EAAE;IACXG,KAAK,EAAE;MACLqD,GAAG,EAAEzD,GAAG,CAAC0D,UAAU;MACnBzC,KAAK,EAAE,MAAM;MACb4C,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,CAAC,GACF5D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CACxC,CAAC,CACP,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIsD,eAAe,GAAG,EAAE;AACxBhE,MAAM,CAACiE,aAAa,GAAG,IAAI;AAE3B,SAASjE,MAAM,EAAEgE,eAAe", "ignoreList": []}]}