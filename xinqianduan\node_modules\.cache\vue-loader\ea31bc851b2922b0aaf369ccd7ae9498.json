{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748614871552}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["debts.vue"], "names": [], "mappings": ";AAinBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debts.vue", "sourceRoot": "src/views/pages/debt", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <div @click=\"viewUserData(scope.row.uid)\" class=\"clickable-text\">{{scope.row.users.nickname}}</div>\r\n            </template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\" class=\"clickable-text\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <!-- 债务人编辑抽屉 -->\r\n    <el-drawer\r\n      title=\"债务人管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleDrawerClose\">\r\n      <div class=\"drawer-content-wrapper\">\r\n        <!-- 左侧导航菜单 -->\r\n        <div class=\"drawer-sidebar\">\r\n          <el-menu\r\n            :default-active=\"activeDebtTab\"\r\n            class=\"drawer-menu\"\r\n            @select=\"handleDebtTabSelect\">\r\n            <el-menu-item index=\"details\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>债务人详情</span>\r\n            </el-menu-item>\r\n            <el-submenu index=\"evidence\">\r\n              <template slot=\"title\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据</span>\r\n              </template>\r\n              <el-menu-item index=\"evidence-all\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>全部</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-video\">\r\n                <i class=\"el-icon-video-camera\"></i>\r\n                <span>视频</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-image\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                <span>图片</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-audio\">\r\n                <i class=\"el-icon-microphone\"></i>\r\n                <span>语音</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-document\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span>文档</span>\r\n              </el-menu-item>\r\n            </el-submenu>\r\n          </el-menu>\r\n        </div>\r\n\r\n        <!-- 右侧内容区域 -->\r\n        <div class=\"drawer-content\">\r\n          <!-- 债务人详情标签页 -->\r\n          <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                债务人详情\r\n              </div>\r\n              \r\n              <div v-if=\"ruleForm.is_user == 1\">\r\n                <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n              </div>\r\n              \r\n              <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 20px;\">\r\n                <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" style=\"margin-top: 20px;\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择用户\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n                      <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">选择用户</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"用户信息\" v-if=\"ruleForm.utel\">\r\n                      {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人姓名\">\r\n                      <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人电话\">\r\n                      <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"身份证号码\">\r\n                      <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务金额\">\r\n                      <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"债务人地址\">\r\n                  <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"案由描述\">\r\n                  <el-input v-model=\"ruleForm.case_des\" autocomplete=\"off\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n              </el-form>\r\n\r\n              <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 30px;\">\r\n                <el-descriptions-item>\r\n                  <el-table :data=\"ruleForm.debttrans\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"></el-table-column>\r\n                    <el-table-column prop=\"status_name\" label=\"跟进状态\"></el-table-column>\r\n                    <el-table-column prop=\"type_name\" label=\"跟进类型\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额（元）\"></el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"></el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">移除</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"drawer-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确定</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 证据管理标签页 -->\r\n          <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                {{ getEvidenceTitle() }}\r\n                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                  <i class=\"el-icon-plus\"></i> 上传证据\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 证据列表 -->\r\n              <div class=\"evidence-container\">\r\n                <!-- 身份证照片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>身份证照片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('cards')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传身份证\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.cards && ruleForm.cards.length > 0\">\r\n                      <div v-for=\"(item7, index7) in ruleForm.cards\" :key=\"index7\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <img :src=\"item7\" @click=\"showImage(item7)\" class=\"evidence-image\" />\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item7, 'cards', index7)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据图片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据图片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('images')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传图片\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.images && ruleForm.images.length > 0\">\r\n                      <div v-for=\"(item5, index5) in ruleForm.images\" :key=\"index5\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <el-image style=\"width: 100%; height: 150px;\" :src=\"item5\" :preview-src-list=\"ruleForm.images\" fit=\"cover\"></el-image>\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item5, 'images', index5)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的图片 -->\r\n                    <div v-if=\"ruleForm.del_images && ruleForm.del_images.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的图片</h5>\r\n                      <div class=\"evidence-grid\">\r\n                        <div v-for=\"(item8, index8) in ruleForm.del_images\" :key=\"index8\" class=\"evidence-item\">\r\n                          <div class=\"evidence-preview\">\r\n                            <el-image style=\"width: 100%; height: 150px;\" :src=\"item8\" :preview-src-list=\"ruleForm.del_images\" fit=\"cover\"></el-image>\r\n                          </div>\r\n                          <div class=\"evidence-actions\">\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item8, 'del_images', index8)\">删除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据文件 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-document'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据文件</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('attach_path')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传文件\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"file-list\" v-if=\"ruleForm.attach_path && ruleForm.attach_path.length > 0\">\r\n                      <div v-for=\"(item6, index6) in ruleForm.attach_path\" :key=\"index6\" class=\"file-item\" v-if=\"item6\">\r\n                        <div class=\"file-icon\">\r\n                          <i class=\"el-icon-document file-type-icon\"></i>\r\n                        </div>\r\n                        <div class=\"file-info\">\r\n                          <div class=\"file-name\">文件{{ index6 + 1 }}</div>\r\n                        </div>\r\n                        <div class=\"file-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                          </el-button>\r\n                          <el-button type=\"success\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item6, 'attach_path', index6)\">移除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的文件 -->\r\n                    <div v-if=\"ruleForm.del_attach_path && ruleForm.del_attach_path.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的文件</h5>\r\n                      <div class=\"file-list\">\r\n                        <div v-for=\"(item9, index9) in ruleForm.del_attach_path\" :key=\"index9\" class=\"file-item\" v-if=\"item9\">\r\n                          <div class=\"file-icon\">\r\n                            <i class=\"el-icon-document file-type-icon\"></i>\r\n                          </div>\r\n                          <div class=\"file-info\">\r\n                            <div class=\"file-name\">文件{{ index9 + 1 }}</div>\r\n                          </div>\r\n                          <div class=\"file-actions\">\r\n                            <el-button type=\"primary\" size=\"mini\">\r\n                              <a :href=\"item9\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                            </el-button>\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item9, 'del_attach_path', index9)\">移除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空状态 -->\r\n                <div v-if=\"!hasEvidence()\" class=\"no-evidence\">\r\n                  <i class=\"el-icon-folder-opened\"></i>\r\n                  <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                  <br>\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                    <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n            title=\"用户列表\"\r\n            :visible.sync=\"dialogUserFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\">\r\n\r\n        <el-row style=\"width: 300px\">\r\n            <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"searchUserData()\"\r\n                ></el-button>\r\n            </el-input>\r\n        </el-row>\r\n\r\n        <el-table\r\n                :data=\"listUser\"\r\n                style=\"width: 100%; margin-top: 10px\"\r\n                size=\"mini\"\r\n                @current-change=\"selUserData\"\r\n        >\r\n            <el-table-column label=\"选择\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n            <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n            <el-table-column prop=\"\" label=\"头像\">\r\n                <template slot-scope=\"scope\">\r\n                    <div>\r\n\r\n                        <el-row v-if=\"scope.row.headimg==''\">\r\n                            <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                        </el-row>\r\n                        <el-row v-else>\r\n                            <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                        </el-row>\r\n\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n            <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n            <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n            <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n            <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        </el-table>\r\n\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n      <!-- 债务人详情抽屉 -->\r\n      <el-drawer\r\n        title=\"债务人详情\"\r\n        :visible.sync=\"drawerViewDebtDetail\"\r\n        direction=\"rtl\"\r\n        size=\"60%\"\r\n        :before-close=\"handleDebtDetailDrawerClose\">\r\n        <div class=\"drawer-content-wrapper\">\r\n          <div class=\"drawer-content\">\r\n            <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n\r\n\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!-- 用户详情抽屉 -->\r\n      <el-drawer\r\n        title=\"用户详情\"\r\n        :visible.sync=\"drawerViewUserDetail\"\r\n        direction=\"rtl\"\r\n        size=\"60%\"\r\n        :before-close=\"handleUserDetailDrawerClose\">\r\n        <div class=\"drawer-content-wrapper\">\r\n          <div class=\"drawer-content\">\r\n            <user-details :id=\"currentId\"></user-details>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails,DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [\r\n        {\r\n          id: 1,\r\n          uid: 1001,\r\n          name: \"张三\",\r\n          tel: \"13800138001\",\r\n          money: \"50000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"50000\",\r\n          ctime: \"2024-01-15 10:30:00\",\r\n          address: \"北京市朝阳区建国路88号\",\r\n          idcard_no: \"110101199001011234\",\r\n          case_des: \"借款纠纷，借款人未按约定时间还款\",\r\n          users: {\r\n            nickname: \"李四\"\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          uid: 1002,\r\n          name: \"王五\",\r\n          tel: \"13900139002\",\r\n          money: \"120000\",\r\n          status: \"调节中\",\r\n          back_money: \"30000\",\r\n          un_money: \"90000\",\r\n          ctime: \"2024-01-10 14:20:00\",\r\n          address: \"上海市浦东新区陆家嘴金融区\",\r\n          idcard_no: \"310101199205155678\",\r\n          case_des: \"合同纠纷，未按合同约定支付货款\",\r\n          users: {\r\n            nickname: \"赵六\"\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          uid: 1003,\r\n          name: \"陈七\",\r\n          tel: \"13700137003\",\r\n          money: \"80000\",\r\n          status: \"诉讼中\",\r\n          back_money: \"20000\",\r\n          un_money: \"60000\",\r\n          ctime: \"2024-01-05 09:15:00\",\r\n          address: \"广州市天河区珠江新城\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"服务费纠纷，拒绝支付约定的服务费用\",\r\n          users: {\r\n            nickname: \"孙八\"\r\n          }\r\n        },\r\n        {\r\n          id: 4,\r\n          uid: 1004,\r\n          name: \"刘九\",\r\n          tel: \"13600136004\",\r\n          money: \"200000\",\r\n          status: \"已结案\",\r\n          back_money: \"200000\",\r\n          un_money: \"0\",\r\n          ctime: \"2023-12-20 16:45:00\",\r\n          address: \"深圳市南山区科技园\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"投资纠纷，已通过调解达成一致\",\r\n          users: {\r\n            nickname: \"周十\"\r\n          }\r\n        },\r\n        {\r\n          id: 5,\r\n          uid: 1005,\r\n          name: \"吴十一\",\r\n          tel: \"13500135005\",\r\n          money: \"75000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"75000\",\r\n          ctime: \"2024-01-18 11:30:00\",\r\n          address: \"杭州市西湖区文三路\",\r\n          idcard_no: \"330101199406067890\",\r\n          case_des: \"租赁纠纷，拖欠房租及违约金\",\r\n          users: {\r\n            nickname: \"郑十二\"\r\n          }\r\n        },\r\n        {\r\n          id: 6,\r\n          uid: 1006,\r\n          name: \"马十三\",\r\n          tel: \"13400134006\",\r\n          money: \"150000\",\r\n          status: \"调节中\",\r\n          back_money: \"50000\",\r\n          un_money: \"100000\",\r\n          ctime: \"2024-01-12 13:20:00\",\r\n          address: \"成都市锦江区春熙路\",\r\n          idcard_no: \"510101199009091234\",\r\n          case_des: \"买卖合同纠纷，货物质量问题导致损失\",\r\n          users: {\r\n            nickname: \"冯十四\"\r\n          }\r\n        }\r\n      ],\r\n      total: 6,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      drawerViewUserDetail: false,\r\n      drawerViewDebtDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n      activeDebtTab: 'details',\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n      _this.activeDebtTab = 'details';\r\n      _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.drawerViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.drawerViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      \r\n      // 开发模式：使用示例数据，不发送HTTP请求\r\n      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';\r\n      \r\n      if (isDevelopment) {\r\n        // 模拟异步加载\r\n        setTimeout(() => {\r\n          // 这里的数据已经在data()中定义了，所以直接设置loading为false\r\n          _this.loading = false;\r\n        }, 500);\r\n        return;\r\n      }\r\n      \r\n      // 生产模式：发送HTTP请求获取真实数据\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n    handleDrawerClose() {\r\n      this.dialogFormVisible = false;\r\n    },\r\n    handleUserDetailDrawerClose() {\r\n      this.drawerViewUserDetail = false;\r\n    },\r\n    handleDebtDetailDrawerClose() {\r\n      this.drawerViewDebtDetail = false;\r\n    },\r\n    handleDebtTabSelect(index) {\r\n      this.activeDebtTab = index;\r\n    },\r\n    getEvidenceTitle() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部证据';\r\n        case 'evidence-video':\r\n          return '视频证据';\r\n        case 'evidence-image':\r\n          return '图片证据';\r\n        case 'evidence-audio':\r\n          return '语音证据';\r\n        case 'evidence-document':\r\n          return '文档证据';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    getEvidenceTypeText() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部';\r\n        case 'evidence-video':\r\n          return '视频';\r\n        case 'evidence-image':\r\n          return '图片';\r\n        case 'evidence-audio':\r\n          return '语音';\r\n        case 'evidence-document':\r\n          return '文档';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    hasEvidence() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return this.ruleForm.cards.length > 0 || this.ruleForm.images.length > 0 || this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-video':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-image':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-audio':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-document':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    uploadEvidence() {\r\n      // Implementation of uploadEvidence method\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.drawer-content-wrapper {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n  width: 200px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n}\r\n\r\n.drawer-menu {\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.drawer-menu .el-menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.drawer-menu .el-menu-item.is-active {\r\n  background-color: #409eff;\r\n  color: white;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item {\r\n  padding-left: 40px;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.evidence-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.evidence-section h4 {\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.evidence-section h5 {\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.evidence-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: #fff;\r\n}\r\n\r\n.evidence-item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n  width: 100%;\r\n  height: 150px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.evidence-actions {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #409eff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.file-type-icon {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.drawer-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.no-evidence {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n  border: 2px dashed #dcdfe6;\r\n}\r\n\r\n.no-evidence i {\r\n  font-size: 48px;\r\n  margin-bottom: 15px;\r\n  display: block;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .drawer-content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .drawer-sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n  \r\n  .evidence-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 10px;\r\n  }\r\n  \r\n  .file-item {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .file-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 可点击文本样式 */\r\n.clickable-text {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  text-decoration: none;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.clickable-text:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n</style>\r\n"]}]}