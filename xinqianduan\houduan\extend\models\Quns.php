<?php
namespace models;
use think\Model;
class Quns extends Base{
	public function getLvshiIdAttr($value){
		if(empty($value)) return [];
		else return unserialize($value);
	}
	public function getYuangongIdAttr($value){
		if(empty($value)) return [];
		else return unserialize($value);
	}
    public function getuidAttr($value){
        if(empty($value)) return [];
        else return unserialize($value);
    }
}