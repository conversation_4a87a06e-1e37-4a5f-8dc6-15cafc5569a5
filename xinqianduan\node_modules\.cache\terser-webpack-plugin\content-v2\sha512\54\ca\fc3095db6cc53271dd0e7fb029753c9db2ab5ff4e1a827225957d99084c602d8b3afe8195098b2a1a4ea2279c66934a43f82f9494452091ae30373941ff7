{"map": "{\"version\":3,\"sources\":[\"js/chunk-462ad2b9.cff16a8b.js\"],\"names\":[\"window\",\"push\",\"0091\",\"module\",\"__webpack_exports__\",\"__webpack_require__\",\"23df\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"_v\",\"_s\",\"$router\",\"currentRoute\",\"name\",\"attrs\",\"type\",\"on\",\"click\",\"refulsh\",\"placeholder\",\"clearable\",\"model\",\"value\",\"search\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"slot\",\"is_deal\",\"_l\",\"options1\",\"item\",\"key\",\"id\",\"label\",\"title\",\"icon\",\"$event\",\"getData\",\"clearData\",\"directives\",\"rawName\",\"loading\",\"data\",\"list\",\"stripe\",\"border\",\"prop\",\"width\",\"show-overflow-tooltip\",\"min-width\",\"scopedSlots\",\"_u\",\"fn\",\"scope\",\"row\",\"size\",\"underline\",\"viewUserData\",\"uid\",\"nickname\",\"phone\",\"fixed\",\"plain\",\"generateAIContract\",\"_e\",\"editData\",\"submitForReview\",\"delData\",\"$index\",\"page-sizes\",\"page-size\",\"layout\",\"total\",\"background\",\"size-change\",\"handleSizeChange\",\"current-change\",\"handleCurrentChange\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"ref\",\"ruleForm\",\"rules\",\"label-width\",\"formLabelWidth\",\"autocomplete\",\"readonly\",\"rows\",\"desc\",\"disabled\",\"file_path\",\"changeFile\",\"action\",\"show-file-list\",\"on-success\",\"handleSuccess\",\"delImage\",\"content\",\"saveData\",\"dialogVisible\",\"src\",\"show_image\",\"dialogAIGenerate\",\"currentOrder\",\"matchedContractType\",\"currentUserInfo\",\"id_card\",\"address\",\"template_file\",\"template_name\",\"description\",\"show-icon\",\"closable\",\"aiGenerating\",\"percentage\",\"aiProgress\",\"status\",\"stroke-width\",\"aiProgressText\",\"generatedContract\",\"startAIGeneration\",\"saveGeneratedContract\",\"dialogViewUserDetail\",\"direction\",\"close-on-press-escape\",\"modal-append-to-body\",\"currentId\",\"staticRenderFns\",\"UserDetail\",\"dingzhivue_type_script_lang_js\",\"components\",\"UserDetails\",\"[object Object]\",\"allSize\",\"page\",\"is_pay\",\"url\",\"info\",\"is_num\",\"contractTypes\",\"required\",\"message\",\"trigger\",\"options\",\"getContractTypes\",\"methods\",\"filed\",\"console\",\"log\",\"_this\",\"getInfo\",\"setTimeout\",\"find\",\"$message\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"then\",\"deleteRequest\",\"resp\",\"code\",\"msg\",\"catch\",\"index\",\"splice\",\"go\",\"allData\",\"order_sn\",\"create_time\",\"filteredData\",\"filter\",\"includes\",\"length\",\"$refs\",\"validate\",\"valid\",\"postRequest\",\"val\",\"res\",\"success\",\"error\",\"file\",\"isTypeTrue\",\"test\",\"fileName\",\"getRequest\",\"template_size\",\"orderData\",\"matchContractType\",\"getUserInfo\",\"orderTitle\",\"keywords\",\"劳动\",\"租赁\",\"买卖\",\"服务\",\"借款\",\"matchedType\",\"userInfoMap\",\"1\",\"2\",\"3\",\"4\",\"5\",\"simulateAIGeneration\",\"steps\",\"progress\",\"text\",\"currentStep\",\"updateProgress\",\"generateContractContent\",\"contractTemplate\",\"Date\",\"toLocaleString\",\"warning\",\"orderIndex\",\"findIndex\",\"review_status\",\"submit_time\",\"review_step\",\"wenshu_dingzhivue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\",\"5a84\",\"67a0\",\"exports\",\"aa42\",\"d522\",\"shadow\",\"gutter\",\"span\",\"company\",\"linkman\",\"linkphone\",\"yuangong_id\",\"start_time\",\"year\",\"headimg\",\"staticStyle\",\"cursor\",\"nativeOn\",\"showImage\",\"license\",\"height\",\"fit\",\"tiaojie_name\",\"fawu_name\",\"lian_name\",\"htsczy_name\",\"ls_name\",\"ywy_name\",\"debts\",\"header-cell-style\",\"color\",\"tel\",\"money\",\"viewDebtDetail\",\"UserDetailvue_type_script_lang_js\",\"props\",\"String\",\"Number\",\"watch\",\"immediate\",\"newId\",\"testUserData\",\"imageUrl\",\"debt\",\"components_UserDetailvue_type_script_lang_js\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAAqBC,GAE7C,aACmdA,EAAoB,SAOjeC,OACA,SAAUH,EAAQC,EAAqBC,GAE7C,aAEAA,EAAoBE,EAAEH,GAGtB,IAAII,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,6BACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGL,KAAKM,QAAQC,aAAaC,SAAUP,EAAG,YAAa,CACpEE,YAAa,cACbM,MAAO,CACLC,KAAQ,QAEVC,GAAI,CACFC,MAASb,EAAIc,UAEd,CAACZ,EAAG,IAAK,CACVE,YAAa,oBACXJ,EAAIK,GAAG,WAAY,GAAIH,EAAG,MAAO,CACnCE,YAAa,kBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,WAAYH,EAAG,WAAY,CACpCE,YAAa,eACbM,MAAO,CACLK,YAAe,gBACfC,UAAa,IAEfC,MAAO,CACLC,MAAOlB,EAAImB,OAAOC,QAClBC,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAImB,OAAQ,UAAWG,IAElCE,WAAY,mBAEb,CAACtB,EAAG,IAAK,CACVE,YAAa,gCACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,cACD,GAAIvB,EAAG,MAAO,CACnBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,YAAa,CACpCE,YAAa,gBACbM,MAAO,CACLK,YAAe,QACfC,UAAa,IAEfC,MAAO,CACLC,MAAOlB,EAAImB,OAAOO,QAClBL,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAImB,OAAQ,UAAWG,IAElCE,WAAY,mBAEbxB,EAAI2B,GAAG3B,EAAI4B,UAAU,SAAUC,GAChC,OAAO3B,EAAG,YAAa,CACrB4B,IAAKD,EAAKE,GACVrB,MAAO,CACLsB,MAASH,EAAKI,MACdf,MAASW,EAAKE,SAGhB,IAAK,GAAI7B,EAAG,MAAO,CACrBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBQ,MAAO,CACLC,KAAQ,UACRuB,KAAQ,kBAEVtB,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIoC,aAGd,CAACpC,EAAIK,GAAG,UAAWH,EAAG,YAAa,CACpCQ,MAAO,CACLwB,KAAQ,wBAEVtB,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIqC,eAGd,CAACrC,EAAIK,GAAG,WAAY,OAAQH,EAAG,MAAO,CACvCE,YAAa,iBACZ,CAACF,EAAG,WAAY,CACjBoC,WAAY,CAAC,CACX7B,KAAM,UACN8B,QAAS,YACTrB,MAAOlB,EAAIwC,QACXhB,WAAY,YAEdpB,YAAa,aACbM,MAAO,CACL+B,KAAQzC,EAAI0C,KACZC,OAAU,GACVC,OAAU,KAEX,CAAC1C,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,WACRb,MAAS,MACTc,MAAS,MACTC,wBAAyB,MAEzB7C,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,OACRb,MAAS,OACTc,MAAS,SAET5C,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,QACRb,MAAS,OACTgB,YAAa,MACbD,wBAAyB,MAEzB7C,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,OACRb,MAAS,OACTgB,YAAa,MACbD,wBAAyB,MAEzB7C,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,UACRb,MAAS,OACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,SAAU,CACnBQ,MAAO,CACLC,KAA6B,GAArByC,EAAMC,IAAI3B,QAAe,UAAiC,GAArB0B,EAAMC,IAAI3B,QAAe,UAAY,OAClF4B,KAAQ,UAET,CAACtD,EAAIK,GAAG,IAAML,EAAIM,GAAwB,GAArB8C,EAAMC,IAAI3B,QAAe,MAA6B,GAArB0B,EAAMC,IAAI3B,QAAe,MAAQ,OAAS,cAGrGxB,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,WACRb,MAAS,MACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,UAAW,CACpBQ,MAAO,CACLC,KAAQ,UACR4C,WAAa,GAEf3C,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIwD,aAAaJ,EAAMC,IAAII,QAGrC,CAACzD,EAAIK,GAAG,IAAML,EAAIM,GAAG8C,EAAMC,IAAIK,UAAY,cAGhDxD,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,QACRb,MAAS,OACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,UAAW,CACpBQ,MAAO,CACLC,KAAQ,UACR4C,WAAa,GAEf3C,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIwD,aAAaJ,EAAMC,IAAII,QAGrC,CAACzD,EAAIK,GAAG,IAAML,EAAIM,GAAG8C,EAAMC,IAAIM,OAAS,cAG7CzD,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,cACRb,MAAS,OACTc,MAAS,SAET5C,EAAG,kBAAmB,CACxBQ,MAAO,CACLkD,MAAS,QACT5B,MAAS,KACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAAoB,SAAnBgD,EAAMC,IAAI1C,KAAkBT,EAAG,YAAa,CAC9CE,YAAa,aACbM,MAAO,CACLC,KAAQ,UACR2C,KAAQ,OACRpB,KAAQ,sBACR2B,MAAS,IAEXjD,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAI8D,mBAAmBV,EAAMC,QAGvC,CAACrD,EAAIK,GAAG,YAAcL,EAAI+D,KAAM7D,EAAG,YAAa,CACjDE,YAAa,aACbM,MAAO,CACLC,KAAQ,UACR2C,KAAQ,OACRpB,KAAQ,gBACR2B,MAAS,IAEXjD,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIgE,SAASZ,EAAMC,IAAItB,OAGjC,CAAC/B,EAAIK,GAAG,YAAmC,IAAtB+C,EAAMC,IAAI3B,QAAgBxB,EAAG,YAAa,CAChEE,YAAa,aACbM,MAAO,CACLC,KAAQ,UACR2C,KAAQ,OACRpB,KAAQ,iBACR2B,MAAS,IAEXjD,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIiE,gBAAgBb,EAAMC,QAGpC,CAACrD,EAAIK,GAAG,YAAcL,EAAI+D,KAAM7D,EAAG,YAAa,CACjDE,YAAa,aACbM,MAAO,CACLC,KAAQ,SACR2C,KAAQ,OACRpB,KAAQ,gBACR2B,MAAS,IAEXjD,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIkE,QAAQd,EAAMe,OAAQf,EAAMC,IAAItB,OAG9C,CAAC/B,EAAIK,GAAG,WAAY,WAGxB,GAAIH,EAAG,MAAO,CACjBE,YAAa,sBACZ,CAACF,EAAG,gBAAiB,CACtBQ,MAAO,CACL0D,aAAc,CAAC,GAAI,GAAI,IAAK,KAC5BC,YAAarE,EAAIsD,KACjBgB,OAAU,0CACVC,MAASvE,EAAIuE,MACbC,WAAc,IAEhB5D,GAAI,CACF6D,cAAezE,EAAI0E,iBACnBC,iBAAkB3E,EAAI4E,wBAErB,IAAK,GAAI1E,EAAG,YAAa,CAC5BQ,MAAO,CACLuB,MAASjC,EAAIiC,MAAQ,KACrB4C,QAAW7E,EAAI8E,kBACfC,wBAAwB,EACxBjC,MAAS,OAEXlC,GAAI,CACFoE,iBAAkB,SAAU7C,GAC1BnC,EAAI8E,kBAAoB3C,KAG3B,CAACjC,EAAG,UAAW,CAChB+E,IAAK,WACLvE,MAAO,CACLO,MAASjB,EAAIkF,SACbC,MAASnF,EAAImF,QAEd,CAACjF,EAAG,eAAgB,CACrBQ,MAAO,CACLsB,MAAS,OACToD,cAAepF,EAAIqF,iBAEpB,CAACnF,EAAG,WAAY,CACjBQ,MAAO,CACL4E,aAAgB,MAChBC,SAAY,IAEdtE,MAAO,CACLC,MAAOlB,EAAIkF,SAASjD,MACpBZ,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,QAAS5D,IAElCE,WAAY,qBAEX,GAAItB,EAAG,eAAgB,CAC1BQ,MAAO,CACLsB,MAAS,OACToD,cAAepF,EAAIqF,iBAEpB,CAACnF,EAAG,WAAY,CACjBQ,MAAO,CACL4E,aAAgB,MAChBC,SAAY,GACZ5E,KAAQ,WACR6E,KAAQ,GAEVvE,MAAO,CACLC,MAAOlB,EAAIkF,SAASO,KACpBpE,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,OAAQ5D,IAEjCE,WAAY,oBAEX,GAAItB,EAAG,eAAgB,CAC1BQ,MAAO,CACLsB,MAAS,OACToD,cAAepF,EAAIqF,iBAEpB,CAACnF,EAAG,MAAO,CAACA,EAAG,WAAY,CAC5BQ,MAAO,CACLsB,MAAS,GAEXf,MAAO,CACLC,MAAOlB,EAAIkF,SAASxD,QACpBL,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,UAAW5D,IAEpCE,WAAY,qBAEb,CAACxB,EAAIK,GAAG,SAAUH,EAAG,WAAY,CAClCQ,MAAO,CACLsB,MAAS,GAEXf,MAAO,CACLC,MAAOlB,EAAIkF,SAASxD,QACpBL,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,UAAW5D,IAEpCE,WAAY,qBAEb,CAACxB,EAAIK,GAAG,UAAW,KAA8B,GAAxBL,EAAIkF,SAASxD,SAAqC,GAArB1B,EAAIkF,SAASvE,KAAYT,EAAG,eAAgB,CACnGQ,MAAO,CACLsB,MAAS,QACToD,cAAepF,EAAIqF,eACnBxC,KAAQ,cAET,CAAC3C,EAAG,WAAY,CACjBE,YAAa,WACbM,MAAO,CACLgF,UAAY,GAEdzE,MAAO,CACLC,MAAOlB,EAAIkF,SAASS,UACpBtE,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,YAAa5D,IAEtCE,WAAY,wBAEZtB,EAAG,kBAAmB,CAACA,EAAG,YAAa,CACzCU,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAI4F,WAAW,gBAGzB,CAAC1F,EAAG,YAAa,CAClBQ,MAAO,CACLmF,OAAU,2BACVC,kBAAkB,EAClBC,aAAc/F,EAAIgG,gBAEnB,CAAChG,EAAIK,GAAG,WAAY,GAAIL,EAAIkF,SAASS,UAAYzF,EAAG,YAAa,CAClEQ,MAAO,CACLC,KAAQ,UAEVC,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIiG,SAASjG,EAAIkF,SAASS,UAAW,gBAG/C,CAAC3F,EAAIK,GAAG,QAAUL,EAAI+D,MAAO,IAAK,GAAK/D,EAAI+D,KAA8B,GAAxB/D,EAAIkF,SAASxD,SAAqC,GAArB1B,EAAIkF,SAASvE,KAAYT,EAAG,eAAgB,CAC3HQ,MAAO,CACLsB,MAAS,OACToD,cAAepF,EAAIqF,iBAEpB,CAACnF,EAAG,WAAY,CACjBQ,MAAO,CACL4E,aAAgB,MAChB3E,KAAQ,WACR6E,KAAQ,GAEVvE,MAAO,CACLC,MAAOlB,EAAIkF,SAASgB,QACpB7E,SAAU,SAAUC,GAClBtB,EAAIuB,KAAKvB,EAAIkF,SAAU,UAAW5D,IAEpCE,WAAY,uBAEX,GAAKxB,EAAI+D,MAAO,GAAI7D,EAAG,MAAO,CACjCE,YAAa,gBACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,YAAa,CAClBU,GAAI,CACFC,MAAS,SAAUsB,GACjBnC,EAAI8E,mBAAoB,KAG3B,CAAC9E,EAAIK,GAAG,SAAUH,EAAG,YAAa,CACnCQ,MAAO,CACLC,KAAQ,WAEVC,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAImG,cAGd,CAACnG,EAAIK,GAAG,UAAW,IAAK,GAAIH,EAAG,YAAa,CAC7CQ,MAAO,CACLuB,MAAS,OACT4C,QAAW7E,EAAIoG,cACftD,MAAS,OAEXlC,GAAI,CACFoE,iBAAkB,SAAU7C,GAC1BnC,EAAIoG,cAAgBjE,KAGvB,CAACjC,EAAG,WAAY,CACjBQ,MAAO,CACL2F,IAAOrG,EAAIsG,eAEV,GAAIpG,EAAG,YAAa,CACvBE,YAAa,qBACbM,MAAO,CACLuB,MAAS,SACT4C,QAAW7E,EAAIuG,iBACfxB,wBAAwB,EACxBjC,MAAS,OAEXlC,GAAI,CACFoE,iBAAkB,SAAU7C,GAC1BnC,EAAIuG,iBAAmBpE,KAG1B,CAACjC,EAAG,MAAO,CACZE,YAAa,uBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,sBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIK,GAAG,YAAaH,EAAG,MAAO,CAChCE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIwG,aAAavE,YAAa/B,EAAG,MAAO,CACpGE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIwG,aAAaf,WAAYvF,EAAG,MAAO,CACnGE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyG,oBAAoBxE,OAAS,qBAAsB/B,EAAG,MAAO,CAC7HE,YAAa,qBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIK,GAAG,YAAaH,EAAG,MAAO,CAChCE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0G,gBAAgBhD,eAAgBxD,EAAG,MAAO,CAC1GE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0G,gBAAgB/C,YAAazD,EAAG,MAAO,CACvGE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,WAAYH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0G,gBAAgBC,SAAW,YAAazG,EAAG,MAAO,CAClHE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIK,GAAG,SAAUH,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0G,gBAAgBE,SAAW,gBAAiB1G,EAAG,MAAO,CACpHE,YAAa,yBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,0BACXJ,EAAIK,GAAG,YAAaL,EAAIyG,oBAAoBI,cAAgB3G,EAAG,MAAO,CACxEE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXF,EAAG,OAAQ,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyG,oBAAoBK,kBAAmB5G,EAAG,SAAU,CACpFQ,MAAO,CACLC,KAAQ,UACR2C,KAAQ,SAET,CAACtD,EAAIK,GAAG,YAAa,KAAOH,EAAG,MAAO,CACvCE,YAAa,eACZ,CAACF,EAAG,WAAY,CACjBQ,MAAO,CACLuB,MAAS,aACTtB,KAAQ,UACRoG,YAAe,uBACfC,YAAa,GACbC,UAAY,MAEX,KAAMjH,EAAIkH,aAAehH,EAAG,MAAO,CACtCE,YAAa,uBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,oBACXJ,EAAIK,GAAG,gBAAiBH,EAAG,cAAe,CAC5CQ,MAAO,CACLyG,WAAcnH,EAAIoH,WAClBC,OAA6B,MAAnBrH,EAAIoH,WAAqB,UAAY,KAC/CE,eAAgB,KAEhBpH,EAAG,IAAK,CACVE,YAAa,iBACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIuH,oBAAqB,GAAKvH,EAAI+D,KAAM/D,EAAIwH,kBAAoBtH,EAAG,MAAO,CAC1FE,YAAa,kBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,kBACXJ,EAAIK,GAAG,YAAaH,EAAG,MAAO,CAChCE,YAAa,oBACZ,CAACF,EAAG,WAAY,CACjBE,YAAa,mBACbM,MAAO,CACLC,KAAQ,WACR6E,KAAQ,GACRzE,YAAe,sBAEjBE,MAAO,CACLC,MAAOlB,EAAIwH,kBACXnG,SAAU,SAAUC,GAClBtB,EAAIwH,kBAAoBlG,GAE1BE,WAAY,wBAEX,KAAOxB,EAAI+D,OAAQ7D,EAAG,MAAO,CAChCE,YAAa,gBACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,YAAa,CAClBU,GAAI,CACFC,MAAS,SAAUsB,GACjBnC,EAAIuG,kBAAmB,KAG1B,CAACvG,EAAIK,GAAG,QAASH,EAAG,YAAa,CAClCQ,MAAO,CACLC,KAAQ,UACR6B,QAAWxC,EAAIkH,aACfxB,UAAa1F,EAAIyG,oBAAoBI,eAEvCjG,GAAI,CACFC,MAASb,EAAIyH,oBAEd,CAACzH,EAAIK,GAAG,IAAML,EAAIM,GAAGN,EAAIkH,aAAe,WAAa,UAAY,OAAQlH,EAAIwH,kBAAoBtH,EAAG,YAAa,CAClHQ,MAAO,CACLC,KAAQ,WAEVC,GAAI,CACFC,MAASb,EAAI0H,wBAEd,CAAC1H,EAAIK,GAAG,YAAcL,EAAI+D,MAAO,KAAM7D,EAAG,YAAa,CACxDE,YAAa,qBACbM,MAAO,CACLuB,MAAS,OACT4C,QAAW7E,EAAI2H,qBACfC,UAAa,MACbtE,KAAQ,MACRuE,yBAAyB,EACzBC,wBAAwB,GAE1BlH,GAAI,CACFoE,iBAAkB,SAAU7C,GAC1BnC,EAAI2H,qBAAuBxF,KAG9B,CAACjC,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,eAAgB,CACrBQ,MAAO,CACLqB,GAAM/B,EAAI+H,cAET,MAAO,IAEVC,EAAkB,GAKlBC,EAAarI,EAAoB,QAKJsI,EAAiC,CAChEzH,KAAM,OACN0H,WAAY,CACVC,YAAaH,EAAW,MAE1BI,OACE,MAAO,CACLC,QAAS,OACT5F,KAAM,GACN6B,MAAO,EACPwD,UAAW,EACXQ,KAAM,EACNjF,KAAM,GACNnC,OAAQ,CACNC,QAAS,GACToH,QAAS,EACT9G,SAAU,GAEZc,SAAS,EACTiG,IAAK,YACLxG,MAAO,OACPyG,KAAM,GACN5D,mBAAmB,EACnB6C,sBAAsB,EACtBpB,kBAAkB,EAClBD,WAAY,GACZF,eAAe,EACflB,SAAU,CACRjD,MAAO,GACP0G,OAAQ,GAGVnC,aAAc,GACdE,gBAAiB,GACjBD,oBAAqB,GACrBmC,cAAe,GAEf1B,cAAc,EACdE,WAAY,EACZG,eAAgB,GAChBC,kBAAmB,GACnBrC,MAAO,CACLlD,MAAO,CAAC,CACN4G,UAAU,EACVC,QAAS,QACTC,QAAS,SAEXpD,UAAW,CAAC,CACVkD,UAAU,EACVC,QAAS,QACTC,QAAS,UAGb1D,eAAgB,QAChB2D,QAAS,CAAC,CACRjH,IAAK,EACLE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OAETL,SAAU,CAAC,CACTG,IAAK,EACLE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,UAIboG,UACEpI,KAAKmC,UACLnC,KAAKgJ,oBAEPC,QAAS,CACPb,WAAWc,GACTlJ,KAAKkJ,MAAQA,EACbC,QAAQC,IAAIpJ,KAAKkJ,QAEnBd,YACEpI,KAAKkB,OAAS,CACZC,QAAS,GACToH,QAAS,EACT9G,SAAU,GAEZzB,KAAKsI,KAAO,EACZtI,KAAKmC,WAEPiG,aAAatG,GACX,IAAIuH,EAAQrJ,KACZmJ,QAAQC,IAAI,2BAA4BtH,GAC9B,GAANA,IACF9B,KAAK8H,UAAYhG,EACjBqH,QAAQC,IAAI,kBAAmBpJ,KAAK8H,YAEtCuB,EAAM3B,sBAAuB,EAC7ByB,QAAQC,IAAI,aAEdhB,SAAStG,GAEG,GAANA,EACF9B,KAAKsJ,QAAQxH,GAEb9B,KAAKiF,SAAW,CACdjD,MAAO,GACPwD,KAAM,KAIZ4C,QAAQtG,GACN,IAAIuH,EAAQrJ,KACZmJ,QAAQC,IAAI,kBAAmBtH,GAG/ByH,WAAW,KAET,MAAM3H,EAAOyH,EAAM5G,KAAK+G,KAAK5H,GAAQA,EAAKE,KAAOA,GAC7CF,GACFyH,EAAMpE,SAAW,CACfnD,GAAIF,EAAKE,GACTE,MAAOJ,EAAKI,MACZwD,KAAM5D,EAAK4D,KACX/D,QAASG,EAAKH,QACdf,KAAoB,SAAdkB,EAAKlB,KAAkB,EAAI,EACjCuF,QAAS,GACTP,UAAW,IAEbyD,QAAQC,IAAI,UAAWC,EAAMpE,UAC7BoE,EAAMxE,mBAAoB,GAE1BwE,EAAMI,SAAS,CACb/I,KAAM,QACNmI,QAAS,cAGZ,MAiBLT,QAAQtG,GACN9B,KAAK0J,SAAS,UAAW,KAAM,CAC7BC,kBAAmB,KACnBC,iBAAkB,KAClBlJ,KAAM,YACLmJ,KAAK,KACN7J,KAAK8J,cAAc9J,KAAKwI,IAAM,cAAgB1G,GAAI+H,KAAKE,IACpC,KAAbA,EAAKC,KACPhK,KAAKyJ,SAAS,CACZ/I,KAAM,UACNmI,QAASkB,EAAKE,MAGhBjK,KAAKyJ,SAAS,CACZ/I,KAAM,QACNmI,QAASkB,EAAKE,UAInBC,MAAM,KACPlK,KAAKyJ,SAAS,CACZ/I,KAAM,QACNmI,QAAS,aAIfT,QAAQ+B,EAAOrI,GACb9B,KAAK0J,SAAS,WAAY,KAAM,CAC9BC,kBAAmB,KACnBC,iBAAkB,KAClBlJ,KAAM,YACLmJ,KAAK,KACN7J,KAAK8J,cAAc9J,KAAKwI,IAAM,aAAe1G,GAAI+H,KAAKE,IACnC,KAAbA,EAAKC,OACPhK,KAAKyJ,SAAS,CACZ/I,KAAM,UACNmI,QAAS,UAEX7I,KAAKyC,KAAK2H,OAAOD,EAAO,QAG3BD,MAAM,KACPlK,KAAKyJ,SAAS,CACZ/I,KAAM,QACNmI,QAAS,aAIfT,UACEpI,KAAKM,QAAQ+J,GAAG,IAElBjC,aACEpI,KAAKsI,KAAO,EACZtI,KAAKqD,KAAO,GACZrD,KAAKmC,WAEPiG,UACE,IAAIiB,EAAQrJ,KACZqJ,EAAM9G,SAAU,EAGhBgH,WAAW,KACT,IAAIe,EAAU,CAAC,CACbxI,GAAI,EACJyI,SAAU,cACV7J,KAAM,OACNsB,MAAO,SACPwD,KAAM,oCACN/D,QAAS,EACTgC,SAAU,KACVC,MAAO,cACPF,IAAK,EACLgH,YAAa,uBACZ,CACD1I,GAAI,EACJyI,SAAU,cACV7J,KAAM,OACNsB,MAAO,SACPwD,KAAM,8BACN/D,QAAS,EACTgC,SAAU,KACVC,MAAO,cACPF,IAAK,EACLgH,YAAa,uBACZ,CACD1I,GAAI,EACJyI,SAAU,cACV7J,KAAM,OACNsB,MAAO,SACPwD,KAAM,+BACN/D,QAAS,EACTgC,SAAU,KACVC,MAAO,cACPF,IAAK,EACLgH,YAAa,uBACZ,CACD1I,GAAI,EACJyI,SAAU,cACV7J,KAAM,OACNsB,MAAO,SACPwD,KAAM,6BACN/D,QAAS,EACTgC,SAAU,KACVC,MAAO,cACPF,IAAK,EACLgH,YAAa,uBACZ,CACD1I,GAAI,EACJyI,SAAU,cACV7J,KAAM,OACNsB,MAAO,SACPwD,KAAM,+BACN/D,QAAS,EACTgC,SAAU,KACVC,MAAO,cACPF,IAAK,EACLgH,YAAa,wBAIXC,EAAeH,EACfjB,EAAMnI,OAAOC,UACfsJ,EAAeH,EAAQI,OAAO9I,GAAQA,EAAK2I,SAASI,SAAStB,EAAMnI,OAAOC,UAAYS,EAAKI,MAAM2I,SAAStB,EAAMnI,OAAOC,UAAYS,EAAK6B,SAASkH,SAAStB,EAAMnI,OAAOC,UAAYS,EAAK8B,MAAMiH,SAAStB,EAAMnI,OAAOC,YAExL,IAA1BkI,EAAMnI,OAAOO,SAA2C,KAAzB4H,EAAMnI,OAAOO,UAC9CgJ,EAAeA,EAAaC,OAAO9I,GAAQA,EAAKH,SAAW4H,EAAMnI,OAAOO,UAE1E4H,EAAM5G,KAAOgI,EACbpB,EAAM/E,MAAQmG,EAAaG,OAC3BvB,EAAM9G,SAAU,GACf,MAkBL6F,WACE,IAAIiB,EAAQrJ,KACZA,KAAK6K,MAAM,YAAYC,SAASC,IAC9B,IAAIA,EAiBF,OAAO,EAhBP/K,KAAKgL,YAAY3B,EAAMb,IAAM,OAAQxI,KAAKiF,UAAU4E,KAAKE,IACtC,KAAbA,EAAKC,MACPX,EAAMI,SAAS,CACb/I,KAAM,UACNmI,QAASkB,EAAKE,MAEhBjK,KAAKmC,UACLkH,EAAMxE,mBAAoB,GAE1BwE,EAAMI,SAAS,CACb/I,KAAM,QACNmI,QAASkB,EAAKE,WAS1B7B,iBAAiB6C,GACfjL,KAAKqD,KAAO4H,EACZjL,KAAKmC,WAEPiG,oBAAoB6C,GAClBjL,KAAKsI,KAAO2C,EACZjL,KAAKmC,WAEPiG,cAAc8C,GACI,KAAZA,EAAIlB,MACNhK,KAAKyJ,SAAS0B,QAAQ,QACtBnL,KAAKiF,SAASjF,KAAKkJ,OAASgC,EAAI1I,KAAKgG,KAErCxI,KAAKyJ,SAAS2B,MAAMF,EAAIjB,MAG5B7B,UAAUiD,GACRrL,KAAKqG,WAAagF,EAClBrL,KAAKmG,eAAgB,GAEvBiC,aAAaiD,GACX,MAAMC,EAAa,0BAA0BC,KAAKF,EAAK3K,MAClD4K,GACHtL,KAAKyJ,SAAS2B,MAAM,cAIxBhD,SAASiD,EAAMG,GACb,IAAInC,EAAQrJ,KACZqJ,EAAMoC,WAAW,6BAA+BJ,GAAMxB,KAAKE,IACxC,KAAbA,EAAKC,MACPX,EAAMpE,SAASuG,GAAY,GAC3BnC,EAAMI,SAAS0B,QAAQ,UAEvB9B,EAAMI,SAAS2B,MAAMrB,EAAKE,QAKhC7B,mBAEEmB,WAAW,KACTvJ,KAAK2I,cAAgB,CAAC,CACpB7G,GAAI,EACJE,MAAO,OACP4E,cAAe,kDACfC,cAAe,cACf6E,cAAe,QACd,CACD5J,GAAI,EACJE,MAAO,OACP4E,cAAe,iDACfC,cAAe,aACf6E,cAAe,OACd,CACD5J,GAAI,EACJE,MAAO,OACP4E,cAAe,GACfC,cAAe,GACf6E,cAAe,GACd,CACD5J,GAAI,EACJE,MAAO,OACP4E,cAAe,mDACfC,cAAe,aACf6E,cAAe,QACd,CACD5J,GAAI,EACJE,MAAO,OACP4E,cAAe,GACfC,cAAe,GACf6E,cAAe,KAEhB,MAGLtD,mBAAmBuD,GACjBxC,QAAQC,IAAI,iBAAkBuC,GAG9B3L,KAAKuG,aAAeoF,EAGpB3L,KAAK4L,kBAAkBD,EAAU3J,OAGjChC,KAAK6L,YAAYF,EAAUnI,KAG3BxD,KAAKiH,cAAe,EACpBjH,KAAKmH,WAAa,EAClBnH,KAAKsH,eAAiB,GACtBtH,KAAKuH,kBAAoB,GAGzBvH,KAAKsG,kBAAmB,GAG1B8B,kBAAkB0D,GAEhB,MAAMC,EAAW,CACfC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,QAER,IAAIC,EAAc,KAClB,IAAK,IAAIlL,KAAW4K,EAClB,GAAID,EAAWnB,SAASxJ,GAAU,CAChCkL,EAAcrM,KAAK2I,cAAca,KAAK9I,GAAQA,EAAKsB,QAAU+J,EAAS5K,IACtE,MAGJnB,KAAKwG,oBAAsB6F,GAAe,GAC1ClD,QAAQC,IAAI,YAAapJ,KAAKwG,sBAGhC4B,YAAY5E,GAEV,MAAM8I,EAAc,CAClBC,EAAG,CACD9I,SAAU,KACVC,MAAO,cACPgD,QAAS,qBACTC,QAAS,kBAEX6F,EAAG,CACD/I,SAAU,KACVC,MAAO,cACPgD,QAAS,qBACTC,QAAS,kBAEX8F,EAAG,CACDhJ,SAAU,KACVC,MAAO,cACPgD,QAAS,qBACTC,QAAS,kBAEX+F,EAAG,CACDjJ,SAAU,KACVC,MAAO,cACPgD,QAAS,qBACTC,QAAS,iBAEXgG,EAAG,CACDlJ,SAAU,KACVC,MAAO,cACPgD,QAAS,qBACTC,QAAS,kBAGb3G,KAAKyG,gBAAkB6F,EAAY9I,IAAQ,CACzCC,SAAU,OACVC,MAAO,GACPgD,QAAS,GACTC,QAAS,IAEXwC,QAAQC,IAAI,YAAapJ,KAAKyG,kBAGhC2B,oBACOpI,KAAKwG,oBAAoBI,eAI9B5G,KAAKiH,cAAe,EACpBjH,KAAKmH,WAAa,EAClBnH,KAAKuH,kBAAoB,GAGzBvH,KAAK4M,wBARH5M,KAAKyJ,SAAS2B,MAAM,wBAWxBhD,uBACE,MAAMyE,EAAQ,CAAC,CACbC,SAAU,GACVC,KAAM,eACL,CACDD,SAAU,GACVC,KAAM,eACL,CACDD,SAAU,GACVC,KAAM,eACL,CACDD,SAAU,GACVC,KAAM,eACL,CACDD,SAAU,IACVC,KAAM,YAER,IAAIC,EAAc,EAClB,MAAMC,EAAiB,KACjBD,EAAcH,EAAMjC,QACtB5K,KAAKmH,WAAa0F,EAAMG,GAAaF,SACrC9M,KAAKsH,eAAiBuF,EAAMG,GAAaD,KACzCC,IACAzD,WAAW0D,EAAgB,OAG3BjN,KAAKkN,0BACLlN,KAAKiH,cAAe,IAGxBgG,KAGF7E,0BACE,MAAM+E,EAAmB,GAAGnN,KAAKwG,oBAAoBxE,oBAEjDhC,KAAKyG,gBAAgBhD,kBACxBzD,KAAKyG,gBAAgBC,iBACrB1G,KAAKyG,gBAAgB/C,aACvB1D,KAAKyG,gBAAgBE,wGAOxB3G,KAAKuG,aAAaf,4cAgCb,IAAI4H,MAAOC,yBACZrN,KAAKuG,aAAagE,WAClBvK,KAAKuH,kBAAoB4F,EACzBnN,KAAKyJ,SAAS0B,QAAQ,cAGxB/C,wBACE,IAAKpI,KAAKuH,kBAER,YADAvH,KAAKyJ,SAAS6D,QAAQ,cAMxBtN,KAAKyJ,SAAS0B,QAAQ,WAGtB,MAAMoC,EAAavN,KAAKyC,KAAK+K,UAAU5L,GAAQA,EAAKE,KAAO9B,KAAKuG,aAAazE,KACzD,IAAhByL,IACFvN,KAAKyC,KAAK8K,GAAY9L,QAAU,GAIlCzB,KAAKsG,kBAAmB,GAG1B8B,gBAAgBhF,GACd+F,QAAQC,IAAI,QAAShG,GACrBpD,KAAK0J,SAAS,yBAA0B,KAAM,CAC5CC,kBAAmB,OACnBC,iBAAkB,KAClBlJ,KAAM,YACLmJ,KAAK,KAENN,WAAW,KAET,MAAMY,EAAQnK,KAAKyC,KAAK+K,UAAU5L,GAAQA,EAAKE,KAAOsB,EAAItB,KAC3C,IAAXqI,IAEFnK,KAAKyC,KAAK0H,GAAS,IACdnK,KAAKyC,KAAK0H,GACbsD,cAAe,YAEfC,aAAa,IAAIN,MAAOC,iBACxBM,YAAa,oBAGjB3N,KAAKyJ,SAAS0B,QAAQ,uBACrB,OACFjB,MAAM,KACPlK,KAAKyJ,SAAShB,KAAK,cAMOmF,EAAwC,EAKtEC,GAHsElO,EAAoB,QAGpEA,EAAoB,SAW1CmO,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACA9N,EACAiI,GACA,EACA,KACA,WACA,MAIyCrI,EAAoB,WAAcoO,EAAiB,SAIxFE,OACA,SAAUvO,EAAQC,EAAqBC,GAE7C,aACgdA,EAAoB,SAO9dsO,OACA,SAAUxO,EAAQyO,EAASvO,KAM3BwO,KACA,SAAU1O,EAAQyO,EAASvO,KAM3ByO,KACA,SAAU3O,EAAQC,EAAqBC,GAE7C,aAGA,IAAIG,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,yBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,YACbM,MAAO,CACL4N,OAAU,UAEX,CAACpO,EAAG,MAAO,CACZE,YAAa,cACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACJ,EAAIK,GAAG,cAAeH,EAAG,SAAU,CACrCQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAK+F,SAAW,cAAevO,EAAG,SAAU,CAChEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,SAAUH,EAAG,MAAO,CAC7BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAK/E,OAAS,cAAezD,EAAG,SAAU,CAC9DQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKhF,UAAY,eAAgB,GAAIxD,EAAG,SAAU,CACtEQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,SAAUH,EAAG,MAAO,CAC7BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKgG,SAAW,cAAexO,EAAG,SAAU,CAChEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKiG,WAAa,cAAezO,EAAG,SAAU,CAClEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKkG,aAAe,eAAgB,GAAI1O,EAAG,SAAU,CACzEQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKmG,YAAc,cAAe3O,EAAG,SAAU,CACnEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKoG,KAAO9O,EAAI0I,KAAKoG,KAAO,IAAM,cAAe5O,EAAG,SAAU,CAClFQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,QAASH,EAAG,MAAO,CAC5BE,YAAa,cACZ,CAACJ,EAAI0I,KAAKqG,SAAgC,KAArB/O,EAAI0I,KAAKqG,QAAiB7O,EAAG,YAAa,CAChE8O,YAAa,CACXC,OAAU,WAEZvO,MAAO,CACL2F,IAAOrG,EAAI0I,KAAKqG,QAChBzL,KAAQ,IAEV4L,SAAU,CACRrO,MAAS,SAAUsB,GACjB,OAAOnC,EAAImP,UAAUnP,EAAI0I,KAAKqG,aAG/B7O,EAAG,OAAQ,CACdE,YAAa,WACZ,CAACJ,EAAIK,GAAG,UAAW,QAAS,GAAIH,EAAG,SAAU,CAC9CQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,KAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,cACZ,CAACJ,EAAI0I,KAAK0G,SAAgC,KAArBpP,EAAI0I,KAAK0G,QAAiBlP,EAAG,WAAY,CAC/D8O,YAAa,CACXlM,MAAS,QACTuM,OAAU,QACVJ,OAAU,WAEZvO,MAAO,CACL2F,IAAOrG,EAAI0I,KAAK0G,QAChBE,IAAO,SAET1O,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAImP,UAAUnP,EAAI0I,KAAK0G,YAGjC,CAAClP,EAAG,MAAO,CACZE,YAAa,aACbM,MAAO,CACLe,KAAQ,SAEVA,KAAM,SACL,CAACvB,EAAG,IAAK,CACVE,YAAa,gCACNF,EAAG,OAAQ,CAClBE,YAAa,WACZ,CAACJ,EAAIK,GAAG,UAAW,QAAS,IAAK,GAAIH,EAAG,UAAW,CACpDE,YAAa,YACbM,MAAO,CACL4N,OAAU,UAEX,CAACpO,EAAG,MAAO,CACZE,YAAa,cACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,IAAK,CACVE,YAAa,qBACXF,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACJ,EAAIK,GAAG,YAAaH,EAAG,SAAU,CACnCQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,SAAUH,EAAG,MAAO,CAC7BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAK6G,cAAgB,cAAerP,EAAG,SAAU,CACrEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAK8G,WAAa,cAAetP,EAAG,SAAU,CAClEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAK+G,WAAa,eAAgB,GAAIvP,EAAG,SAAU,CACvEQ,MAAO,CACL6N,OAAU,KAEX,CAACrO,EAAG,SAAU,CACfQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,UAAWH,EAAG,MAAO,CAC9BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKgH,aAAe,cAAexP,EAAG,SAAU,CACpEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,QAASH,EAAG,MAAO,CAC5BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKiH,SAAW,cAAezP,EAAG,SAAU,CAChEQ,MAAO,CACL8N,KAAQ,IAET,CAACtO,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIK,GAAG,SAAUH,EAAG,MAAO,CAC7BE,YAAa,aACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI0I,KAAKkH,UAAY,eAAgB,IAAK,GAAI1P,EAAG,UAAW,CAC5EE,YAAa,YACbM,MAAO,CACL4N,OAAU,UAEX,CAACpO,EAAG,MAAO,CACZE,YAAa,cACbM,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,IAAK,CACVE,YAAa,kBACXF,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACJ,EAAIK,GAAG,aAAcH,EAAG,WAAY,CACtCoC,WAAY,CAAC,CACX7B,KAAM,UACN8B,QAAS,YACTrB,MAAOlB,EAAIwC,QACXhB,WAAY,YAEdwN,YAAa,CACXlM,MAAS,QAEXpC,MAAO,CACL+B,KAAQzC,EAAI0I,KAAKmH,MACjBvM,KAAQ,SACRX,OAAU,GACVmN,oBAAqB,CACnBtL,WAAY,UACZuL,MAAO,aAGV,CAAC7P,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,OACRb,MAAS,QACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,SAAU,CACnBQ,MAAO,CACLC,KAAQ,UACR2C,KAAQ,UAET,CAACtD,EAAIK,GAAGL,EAAIM,GAAG8C,EAAMC,IAAI5C,gBAG9BP,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,MACRb,MAAS,QACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,OAAQ,CACjBE,YAAa,gBACZ,CAACJ,EAAIK,GAAGL,EAAIM,GAAG8C,EAAMC,IAAI2M,eAG9B9P,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,QACRb,MAAS,OACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,OAAQ,CACjBE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,IAAML,EAAIM,GAAG8C,EAAMC,IAAI4M,iBAGpC/P,EAAG,kBAAmB,CACxBQ,MAAO,CACLmC,KAAQ,SACRb,MAAS,KACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,SAAU,CACnBQ,MAAO,CACLC,KAA6B,QAArByC,EAAMC,IAAIgE,OAAmB,UAAY,UACjD/D,KAAQ,UAET,CAACtD,EAAIK,GAAG,IAAML,EAAIM,GAAG8C,EAAMC,IAAIgE,QAAU,cAG9CnH,EAAG,kBAAmB,CACxBQ,MAAO,CACLsB,MAAS,KACTc,MAAS,OAEXG,YAAajD,EAAIkD,GAAG,CAAC,CACnBpB,IAAK,UACLqB,GAAI,SAAUC,GACZ,MAAO,CAAClD,EAAG,YAAa,CACtBQ,MAAO,CACLC,KAAQ,OACR2C,KAAQ,SAEV1C,GAAI,CACFC,MAAS,SAAUsB,GACjB,OAAOnC,EAAIkQ,eAAe9M,EAAMC,QAGnC,CAACnD,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIK,GAAG,kBAGZ,GAAKL,EAAI0I,KAAKmH,OAAmC,IAA1B7P,EAAI0I,KAAKmH,MAAMhF,OAIN7K,EAAI+D,KAJiB7D,EAAG,MAAO,CAClEE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXF,EAAG,IAAK,CAACF,EAAIK,GAAG,gBAA4B,GAAIH,EAAG,YAAa,CAClEQ,MAAO,CACLuB,MAAS,OACT4C,QAAW7E,EAAIoG,cACftD,MAAS,OAEXlC,GAAI,CACFoE,iBAAkB,SAAU7C,GAC1BnC,EAAIoG,cAAgBjE,KAGvB,CAACjC,EAAG,WAAY,CACjBQ,MAAO,CACL2F,IAAOrG,EAAIsG,eAEV,IAAK,IAER0B,EAAkB,GAKWmI,EAAoC,CACnE1P,KAAM,cACN2P,MAAO,CACLrO,GAAI,CACFpB,KAAM,CAAC0P,OAAQC,QACfzH,UAAU,IAGdR,OACE,MAAO,CACLK,KAAM,GAENlG,SAAS,EACT4D,eAAe,EACfE,WAAY,KAGhBiK,MAAO,CACLxO,GAAI,CACFyO,WAAW,EAEXnI,QAAQoI,GACFA,GAAkB,GAATA,IACXrH,QAAQC,IAAI,sBAAuBoH,GACnCxQ,KAAKsJ,QAAQkH,OAKrBvH,QAAS,CACPb,QAAQtG,GACN,IAAIuH,EAAQrJ,KACZmJ,QAAQC,IAAI,eAAgBtH,GAC5BuH,EAAM9G,SAAU,EAGhBgH,WAAW,KACT,MAAMkH,EAAe,CACnB3O,GAAIA,EACJ0M,QAAS,WACT9K,MAAO,cACPD,SAAU,KACVgL,QAAS,KACTK,QAAS,GACTH,YAAa,QACbD,UAAW,cACXY,aAAc,OACdC,UAAW,MACXC,UAAW,OACXC,YAAa,OACbC,QAAS,MACTC,SAAU,OACVR,QAAS,GACTP,WAAY,aACZC,KAAM,EACNe,MAAO,CAAC,CACNpP,KAAM,OACNuP,IAAK,cACLC,MAAO,QACP5I,OAAQ,OACP,CACD5G,KAAM,OACNuP,IAAK,cACLC,MAAO,QACP5I,OAAQ,SAGZiC,EAAMZ,KAAOgI,EACbpH,EAAM9G,SAAU,EAChB4G,QAAQC,IAAI,YAAaqH,IACxB,MAkBLrI,UAAUsI,GACR1Q,KAAKqG,WAAaqK,EAClB1Q,KAAKmG,eAAgB,GAEvBiC,eAAeuI,GACbxH,QAAQC,IAAI,WAAYuH,GAExB3Q,KAAKyJ,SAAShB,KAAK,iBAKSmI,EAA+C,EAK7E/C,GAHyElO,EAAoB,QAGvEA,EAAoB,SAW1CmO,EAAYC,OAAOF,EAAoB,KAA3BE,CACd6C,EACA9Q,EACAiI,GACA,EACA,KACA,WACA,MAI4CrI,EAAoB,KAAQoO,EAAiB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-462ad2b9\"],{\"0091\":function(e,t,s){\"use strict\";s(\"67a0\")},\"23df\":function(e,t,s){\"use strict\";s.r(t);var a=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"contract-custom-container\"},[t(\"div\",{staticClass:\"page-header\"},[t(\"h1\",{staticClass:\"page-title\"},[e._v(e._s(this.$router.currentRoute.name))]),t(\"el-button\",{staticClass:\"refresh-btn\",attrs:{type:\"text\"},on:{click:e.refulsh}},[t(\"i\",{staticClass:\"el-icon-refresh\"}),e._v(\" 刷新 \")])],1),t(\"div\",{staticClass:\"search-section\"},[t(\"div\",{staticClass:\"search-form\"},[t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"关键词搜索\")]),t(\"el-input\",{staticClass:\"search-input\",attrs:{placeholder:\"请输入订单号/购买人/套餐\",clearable:\"\"},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,\"keyword\",t)},expression:\"search.keyword\"}},[t(\"i\",{staticClass:\"el-input__icon el-icon-search\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"处理状态\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.search.is_deal,callback:function(t){e.$set(e.search,\"is_deal\",t)},expression:\"search.is_deal\"}},e._l(e.options1,(function(e){return t(\"el-option\",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t(\"div\",{staticClass:\"search-actions\"},[t(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\"},on:{click:function(t){return e.getData()}}},[e._v(\" 搜索 \")]),t(\"el-button\",{attrs:{icon:\"el-icon-refresh-left\"},on:{click:function(t){return e.clearData()}}},[e._v(\" 重置 \")])],1)])]),t(\"div\",{staticClass:\"table-section\"},[t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"data-table\",attrs:{data:e.list,stripe:\"\",border:\"\"}},[t(\"el-table-column\",{attrs:{prop:\"order_sn\",label:\"工单号\",width:\"120\",\"show-overflow-tooltip\":\"\"}}),t(\"el-table-column\",{attrs:{prop:\"type\",label:\"工单类型\",width:\"100\"}}),t(\"el-table-column\",{attrs:{prop:\"title\",label:\"工单标题\",\"min-width\":\"150\",\"show-overflow-tooltip\":\"\"}}),t(\"el-table-column\",{attrs:{prop:\"desc\",label:\"工单内容\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"}}),t(\"el-table-column\",{attrs:{prop:\"is_deal\",label:\"处理状态\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-tag\",{attrs:{type:2==s.row.is_deal?\"success\":1==s.row.is_deal?\"warning\":\"info\",size:\"small\"}},[e._v(\" \"+e._s(2==s.row.is_deal?\"已处理\":1==s.row.is_deal?\"处理中\":\"待处理\")+\" \")])]}}])}),t(\"el-table-column\",{attrs:{prop:\"nickname\",label:\"用户名\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-link\",{attrs:{type:\"primary\",underline:!1},on:{click:function(t){return e.viewUserData(s.row.uid)}}},[e._v(\" \"+e._s(s.row.nickname)+\" \")])]}}])}),t(\"el-table-column\",{attrs:{prop:\"phone\",label:\"用户手机\",width:\"130\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-link\",{attrs:{type:\"primary\",underline:!1},on:{click:function(t){return e.viewUserData(s.row.uid)}}},[e._v(\" \"+e._s(s.row.phone)+\" \")])]}}])}),t(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"发起时间\",width:\"160\"}}),t(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\",width:\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"action-buttons\"},[\"合同定制\"===s.row.type?t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-magic-stick\",plain:\"\"},on:{click:function(t){return e.generateAIContract(s.row)}}},[e._v(\" AI生成 \")]):e._e(),t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-check\",plain:\"\"},on:{click:function(t){return e.editData(s.row.id)}}},[e._v(\" 完成制作 \")]),2===s.row.is_deal?t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"warning\",size:\"mini\",icon:\"el-icon-upload\",plain:\"\"},on:{click:function(t){return e.submitForReview(s.row)}}},[e._v(\" 提交审核 \")]):e._e(),t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-close\",plain:\"\"},on:{click:function(t){return e.delData(s.$index,s.row.id)}}},[e._v(\" 取消 \")])],1)]}}])})],1),t(\"div\",{staticClass:\"pagination-wrapper\"},[t(\"el-pagination\",{attrs:{\"page-sizes\":[20,50,100,200],\"page-size\":e.size,layout:\"total, sizes, prev, pager, next, jumper\",total:e.total,background:\"\"},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handleCurrentChange}})],1)],1),t(\"el-dialog\",{attrs:{title:e.title+\"内容\",visible:e.dialogFormVisible,\"close-on-click-modal\":!1,width:\"70%\"},on:{\"update:visible\":function(t){e.dialogFormVisible=t}}},[t(\"el-form\",{ref:\"ruleForm\",attrs:{model:e.ruleForm,rules:e.rules}},[t(\"el-form-item\",{attrs:{label:\"合同标题\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\",readonly:\"\"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,\"title\",t)},expression:\"ruleForm.title\"}})],1),t(\"el-form-item\",{attrs:{label:\"合同要求\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\",readonly:\"\",type:\"textarea\",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,\"desc\",t)},expression:\"ruleForm.desc\"}})],1),t(\"el-form-item\",{attrs:{label:\"制作状态\",\"label-width\":e.formLabelWidth}},[t(\"div\",[t(\"el-radio\",{attrs:{label:2},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,\"is_deal\",t)},expression:\"ruleForm.is_deal\"}},[e._v(\"已完成\")]),t(\"el-radio\",{attrs:{label:1},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,\"is_deal\",t)},expression:\"ruleForm.is_deal\"}},[e._v(\"处理中\")])],1)]),2==e.ruleForm.is_deal&&2==e.ruleForm.type?t(\"el-form-item\",{attrs:{label:\"请上传文件\",\"label-width\":e.formLabelWidth,prop:\"file_path\"}},[t(\"el-input\",{staticClass:\"el_input\",attrs:{disabled:!0},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,\"file_path\",t)},expression:\"ruleForm.file_path\"}}),t(\"el-button-group\",[t(\"el-button\",{on:{click:function(t){return e.changeFile(\"file_path\")}}},[t(\"el-upload\",{attrs:{action:\"/admin/Upload/uploadFile\",\"show-file-list\":!1,\"on-success\":e.handleSuccess}},[e._v(\" 上传 \")])],1),e.ruleForm.file_path?t(\"el-button\",{attrs:{type:\"danger\"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,\"file_path\")}}},[e._v(\"删除\")]):e._e()],1)],1):e._e(),2==e.ruleForm.is_deal&&2!=e.ruleForm.type?t(\"el-form-item\",{attrs:{label:\"内容回复\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\",type:\"textarea\",rows:4},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,\"content\",t)},expression:\"ruleForm.content\"}})],1):e._e()],1),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(\"取 消\")]),t(\"el-button\",{attrs:{type:\"primary\"},on:{click:function(t){return e.saveData()}}},[e._v(\"确 定\")])],1)],1),t(\"el-dialog\",{attrs:{title:\"图片查看\",visible:e.dialogVisible,width:\"30%\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[t(\"el-image\",{attrs:{src:e.show_image}})],1),t(\"el-dialog\",{staticClass:\"ai-generate-dialog\",attrs:{title:\"AI生成合同\",visible:e.dialogAIGenerate,\"close-on-click-modal\":!1,width:\"80%\"},on:{\"update:visible\":function(t){e.dialogAIGenerate=t}}},[t(\"div\",{staticClass:\"ai-generate-content\"},[t(\"div\",{staticClass:\"order-info-section\"},[t(\"h3\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" 工单信息 \")]),t(\"div\",{staticClass:\"info-grid\"},[t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"工单标题：\")]),t(\"span\",[e._v(e._s(e.currentOrder.title))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"工单内容：\")]),t(\"span\",[e._v(e._s(e.currentOrder.desc))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"合同类型：\")]),t(\"span\",[e._v(e._s(e.matchedContractType.title||\"未匹配到合同类型\"))])])])]),t(\"div\",{staticClass:\"user-info-section\"},[t(\"h3\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-user\"}),e._v(\" 用户信息 \")]),t(\"div\",{staticClass:\"info-grid\"},[t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"用户姓名：\")]),t(\"span\",[e._v(e._s(e.currentUserInfo.nickname))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"联系电话：\")]),t(\"span\",[e._v(e._s(e.currentUserInfo.phone))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"身份证号：\")]),t(\"span\",[e._v(e._s(e.currentUserInfo.id_card||\"未填写\"))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"地址：\")]),t(\"span\",[e._v(e._s(e.currentUserInfo.address||\"未填写\"))])])])]),t(\"div\",{staticClass:\"template-info-section\"},[t(\"h3\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-document-copy\"}),e._v(\" 合同模板 \")]),e.matchedContractType.template_file?t(\"div\",{staticClass:\"template-info\"},[t(\"div\",{staticClass:\"template-file\"},[t(\"i\",{staticClass:\"el-icon-document\"}),t(\"span\",[e._v(e._s(e.matchedContractType.template_name))]),t(\"el-tag\",{attrs:{type:\"success\",size:\"mini\"}},[e._v(\"已找到模板\")])],1)]):t(\"div\",{staticClass:\"no-template\"},[t(\"el-alert\",{attrs:{title:\"未找到对应的合同模板\",type:\"warning\",description:\"请先在合同类型管理中为该类型上传模板文件\",\"show-icon\":\"\",closable:!1}})],1)]),e.aiGenerating?t(\"div\",{staticClass:\"ai-progress-section\"},[t(\"h3\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-loading\"}),e._v(\" AI生成中... \")]),t(\"el-progress\",{attrs:{percentage:e.aiProgress,status:100===e.aiProgress?\"success\":null,\"stroke-width\":8}}),t(\"p\",{staticClass:\"progress-text\"},[e._v(e._s(e.aiProgressText))])],1):e._e(),e.generatedContract?t(\"div\",{staticClass:\"result-section\"},[t(\"h3\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-check\"}),e._v(\" 生成结果 \")]),t(\"div\",{staticClass:\"contract-preview\"},[t(\"el-input\",{staticClass:\"contract-content\",attrs:{type:\"textarea\",rows:15,placeholder:\"AI生成的合同内容将显示在这里...\"},model:{value:e.generatedContract,callback:function(t){e.generatedContract=t},expression:\"generatedContract\"}})],1)]):e._e()]),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{on:{click:function(t){e.dialogAIGenerate=!1}}},[e._v(\"取消\")]),t(\"el-button\",{attrs:{type:\"primary\",loading:e.aiGenerating,disabled:!e.matchedContractType.template_file},on:{click:e.startAIGeneration}},[e._v(\" \"+e._s(e.aiGenerating?\"AI生成中...\":\"开始AI生成\")+\" \")]),e.generatedContract?t(\"el-button\",{attrs:{type:\"success\"},on:{click:e.saveGeneratedContract}},[e._v(\" 保存合同 \")]):e._e()],1)]),t(\"el-drawer\",{staticClass:\"user-detail-drawer\",attrs:{title:\"用户详情\",visible:e.dialogViewUserDetail,direction:\"rtl\",size:\"60%\",\"close-on-press-escape\":!0,\"modal-append-to-body\":!1},on:{\"update:visible\":function(t){e.dialogViewUserDetail=t}}},[t(\"div\",{staticClass:\"drawer-content\"},[t(\"user-details\",{attrs:{id:e.currentId}})],1)])],1)},i=[],l=s(\"d522\"),n={name:\"list\",components:{UserDetails:l[\"a\"]},data(){return{allSize:\"mini\",list:[],total:1,currentId:0,page:1,size:20,search:{keyword:\"\",is_pay:-1,is_deal:-1},loading:!0,url:\"/dingzhi/\",title:\"合同定制\",info:{},dialogFormVisible:!1,dialogViewUserDetail:!1,dialogAIGenerate:!1,show_image:\"\",dialogVisible:!1,ruleForm:{title:\"\",is_num:0},currentOrder:{},currentUserInfo:{},matchedContractType:{},contractTypes:[],aiGenerating:!1,aiProgress:0,aiProgressText:\"\",generatedContract:\"\",rules:{title:[{required:!0,message:\"请填写标题\",trigger:\"blur\"}],file_path:[{required:!0,message:\"请上传文件\",trigger:\"blur\"}]},formLabelWidth:\"120px\",options:[{id:-1,title:\"请选择\"},{id:1,title:\"未支付\"},{id:2,title:\"已支付\"},{id:3,title:\"退款\"}],options1:[{id:-1,title:\"请选择\"},{id:0,title:\"待处理\"},{id:1,title:\"处理中\"},{id:2,title:\"已处理\"}]}},mounted(){this.getData(),this.getContractTypes()},methods:{changeFile(e){this.filed=e,console.log(this.filed)},clearData(){this.search={keyword:\"\",is_pay:-1,is_deal:-1},this.page=1,this.getData()},viewUserData(e){let t=this;console.log(\"viewUserData 被调用，传入的 ID:\",e),0!=e&&(this.currentId=e,console.log(\"设置 currentId 为:\",this.currentId)),t.dialogViewUserDetail=!0,console.log(\"打开用户详情抽屉\")},editData(e){0!=e?this.getInfo(e):this.ruleForm={title:\"\",desc:\"\"}},getInfo(e){let t=this;console.log(\"getInfo 被调用，ID:\",e),setTimeout(()=>{const s=t.list.find(t=>t.id===e);s?(t.ruleForm={id:s.id,title:s.title,desc:s.desc,is_deal:s.is_deal,type:\"合同定制\"===s.type?1:2,content:\"\",file_path:\"\"},console.log(\"设置表单数据:\",t.ruleForm),t.dialogFormVisible=!0):t.$message({type:\"error\",message:\"未找到对应的数据\"})},300)},tuikuan(e){this.$confirm(\"是否申请退款?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"tuikuan?id=\"+e).then(e=>{200==e.code?this.$message({type:\"success\",message:e.msg}):this.$message({type:\"error\",message:e.msg})})}).catch(()=>{this.$message({type:\"error\",message:\"取消退款!\"})})},delData(e,t){this.$confirm(\"是否删除该信息?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"delete?id=\"+t).then(t=>{200==t.code&&(this.$message({type:\"success\",message:\"删除成功!\"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:\"error\",message:\"取消删除!\"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,setTimeout(()=>{let t=[{id:1,order_sn:\"WD202403001\",type:\"合同定制\",title:\"劳动合同定制\",desc:\"需要定制一份标准的劳动合同模板，包含薪资、工作时间、福利待遇等条款\",is_deal:0,nickname:\"张三\",phone:\"13800138001\",uid:1,create_time:\"2024-03-20 10:30:00\"},{id:2,order_sn:\"WD202403002\",type:\"合同审核\",title:\"租赁合同审核\",desc:\"请帮忙审核房屋租赁合同，检查条款是否合理，有无法律风险\",is_deal:1,nickname:\"李四\",phone:\"13800138002\",uid:2,create_time:\"2024-03-19 14:20:00\"},{id:3,order_sn:\"WD202403003\",type:\"合同定制\",title:\"买卖合同定制\",desc:\"需要定制商品买卖合同，涉及货物交付、付款方式、违约责任等\",is_deal:2,nickname:\"王五\",phone:\"13800138003\",uid:3,create_time:\"2024-03-18 09:15:00\"},{id:4,order_sn:\"WD202403004\",type:\"法律咨询\",title:\"服务合同咨询\",desc:\"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",is_deal:1,nickname:\"赵六\",phone:\"13800138004\",uid:4,create_time:\"2024-03-17 16:45:00\"},{id:5,order_sn:\"WD202403005\",type:\"合同定制\",title:\"借款合同定制\",desc:\"需要定制个人借款合同，明确借款金额、利率、还款方式等条款\",is_deal:0,nickname:\"孙七\",phone:\"13800138005\",uid:5,create_time:\"2024-03-16 11:20:00\"}],s=t;e.search.keyword&&(s=t.filter(t=>t.order_sn.includes(e.search.keyword)||t.title.includes(e.search.keyword)||t.nickname.includes(e.search.keyword)||t.phone.includes(e.search.keyword))),-1!==e.search.is_deal&&\"\"!==e.search.is_deal&&(s=s.filter(t=>t.is_deal==e.search.is_deal)),e.list=s,e.total=s.length,e.loading=!1},500)},saveData(){let e=this;this.$refs[\"ruleForm\"].validate(t=>{if(!t)return!1;this.postRequest(e.url+\"save\",this.ruleForm).then(t=>{200==t.code?(e.$message({type:\"success\",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:\"error\",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success(\"上传成功\"),this.ruleForm[this.filed]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error(\"上传图片格式不对!\")},delImage(e,t){let s=this;s.getRequest(\"/Upload/delImage?fileName=\"+e).then(e=>{200==e.code?(s.ruleForm[t]=\"\",s.$message.success(\"删除成功!\")):s.$message.error(e.msg)})},getContractTypes(){setTimeout(()=>{this.contractTypes=[{id:1,title:\"劳动合同\",template_file:\"/uploads/templates/labor_contract_template.docx\",template_name:\"劳动合同模板.docx\",template_size:245760},{id:2,title:\"租赁合同\",template_file:\"/uploads/templates/lease_contract_template.pdf\",template_name:\"租赁合同模板.pdf\",template_size:512e3},{id:3,title:\"买卖合同\",template_file:\"\",template_name:\"\",template_size:0},{id:4,title:\"服务合同\",template_file:\"/uploads/templates/service_contract_template.doc\",template_name:\"服务合同模板.doc\",template_size:327680},{id:5,title:\"借款合同\",template_file:\"\",template_name:\"\",template_size:0}]},100)},generateAIContract(e){console.log(\"开始AI生成合同，工单数据:\",e),this.currentOrder=e,this.matchContractType(e.title),this.getUserInfo(e.uid),this.aiGenerating=!1,this.aiProgress=0,this.aiProgressText=\"\",this.generatedContract=\"\",this.dialogAIGenerate=!0},matchContractType(e){const t={\"劳动\":\"劳动合同\",\"租赁\":\"租赁合同\",\"买卖\":\"买卖合同\",\"服务\":\"服务合同\",\"借款\":\"借款合同\"};let s=null;for(let a in t)if(e.includes(a)){s=this.contractTypes.find(e=>e.title===t[a]);break}this.matchedContractType=s||{},console.log(\"匹配到的合同类型:\",this.matchedContractType)},getUserInfo(e){const t={1:{nickname:\"张三\",phone:\"13800138001\",id_card:\"110101199001011234\",address:\"北京市朝阳区某某街道123号\"},2:{nickname:\"李四\",phone:\"13800138002\",id_card:\"110101199002022345\",address:\"上海市浦东新区某某路456号\"},3:{nickname:\"王五\",phone:\"13800138003\",id_card:\"110101199003033456\",address:\"广州市天河区某某大道789号\"},4:{nickname:\"赵六\",phone:\"13800138004\",id_card:\"110101199004044567\",address:\"深圳市南山区某某街101号\"},5:{nickname:\"孙七\",phone:\"13800138005\",id_card:\"110101199005055678\",address:\"杭州市西湖区某某路202号\"}};this.currentUserInfo=t[e]||{nickname:\"未知用户\",phone:\"\",id_card:\"\",address:\"\"},console.log(\"获取到的用户信息:\",this.currentUserInfo)},startAIGeneration(){this.matchedContractType.template_file?(this.aiGenerating=!0,this.aiProgress=0,this.generatedContract=\"\",this.simulateAIGeneration()):this.$message.error(\"未找到对应的合同模板，无法进行AI生成\")},simulateAIGeneration(){const e=[{progress:20,text:\"正在分析工单内容...\"},{progress:40,text:\"正在解析合同模板...\"},{progress:60,text:\"正在整合用户信息...\"},{progress:80,text:\"正在生成合同条款...\"},{progress:100,text:\"AI生成完成！\"}];let t=0;const s=()=>{t<e.length?(this.aiProgress=e[t].progress,this.aiProgressText=e[t].text,t++,setTimeout(s,1e3)):(this.generateContractContent(),this.aiGenerating=!1)};s()},generateContractContent(){const e=`${this.matchedContractType.title}\\n\\n甲方（委托方）：${this.currentUserInfo.nickname}\\n身份证号：${this.currentUserInfo.id_card}\\n联系电话：${this.currentUserInfo.phone}\\n地址：${this.currentUserInfo.address}\\n\\n乙方（受托方）：[待填写]\\n\\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就以下事项达成一致，签订本合同：\\n\\n一、合同内容\\n${this.currentOrder.desc}\\n\\n二、合同条款\\n[根据AI分析生成的具体条款内容]\\n\\n1. 权利义务\\n   甲方权利：[根据合同类型和用户需求生成]\\n   甲方义务：[根据合同类型和用户需求生成]\\n   乙方权利：[根据合同类型和用户需求生成]\\n   乙方义务：[根据合同类型和用户需求生成]\\n\\n2. 履行期限\\n   [根据工单内容分析生成具体期限]\\n\\n3. 违约责任\\n   [根据合同类型生成标准违约条款]\\n\\n4. 争议解决\\n   因履行本合同发生的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉。\\n\\n5. 其他约定\\n   [根据具体需求生成其他条款]\\n\\n三、合同生效\\n本合同自双方签字（盖章）之日起生效。\\n\\n甲方签字：_________________ 日期：_________________\\n\\n乙方签字：_________________ 日期：_________________\\n\\n---\\n本合同由AI智能生成，请仔细核对内容后使用。\\n生成时间：${(new Date).toLocaleString()}\\n工单号：${this.currentOrder.order_sn}`;this.generatedContract=e,this.$message.success(\"AI合同生成完成！\")},saveGeneratedContract(){if(!this.generatedContract)return void this.$message.warning(\"没有可保存的合同内容\");this.$message.success(\"合同保存成功！\");const e=this.list.findIndex(e=>e.id===this.currentOrder.id);-1!==e&&(this.list[e].is_deal=1),this.dialogAIGenerate=!1},submitForReview(e){console.log(\"提交审核:\",e),this.$confirm(\"确认将此合同提交审核？提交后将进入审核流程。\",\"提示\",{confirmButtonText:\"确定提交\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{setTimeout(()=>{const t=this.list.findIndex(t=>t.id===e.id);-1!==t&&(this.list[t]={...this.list[t],review_status:\"submitted\",submit_time:(new Date).toLocaleString(),review_step:\"mediator_review\"}),this.$message.success(\"合同已成功提交审核！将进入审核流程。\")},500)}).catch(()=>{this.$message.info(\"已取消提交\")})}}},r=n,o=(s(\"5a84\"),s(\"2877\")),c=Object(o[\"a\"])(r,a,i,!1,null,\"5ead154d\",null);t[\"default\"]=c.exports},\"5a84\":function(e,t,s){\"use strict\";s(\"aa42\")},\"67a0\":function(e,t,s){},aa42:function(e,t,s){},d522:function(e,t,s){\"use strict\";var a=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"user-detail-container\"},[t(\"el-card\",{staticClass:\"info-card\",attrs:{shadow:\"hover\"}},[t(\"div\",{staticClass:\"card-header\",attrs:{slot:\"header\"},slot:\"header\"},[t(\"i\",{staticClass:\"el-icon-user\"}),t(\"span\",{staticClass:\"card-title\"},[e._v(\"客户基本信息\")])]),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"公司名称\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.company||\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"手机号\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.phone||\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"客户姓名\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.nickname||\"未填写\"))])])])],1),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"联系人\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.linkman||\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"联系方式\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.linkphone||\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"用户来源\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.yuangong_id||\"未填写\"))])])])],1),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"开始时间\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.start_time||\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"会员年限\")]),t(\"div\",{staticClass:\"info-value\"},[e._v(e._s(e.info.year?e.info.year+\"年\":\"未填写\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"头像\")]),t(\"div\",{staticClass:\"info-value\"},[e.info.headimg&&\"\"!==e.info.headimg?t(\"el-avatar\",{staticStyle:{cursor:\"pointer\"},attrs:{src:e.info.headimg,size:50},nativeOn:{click:function(t){return e.showImage(e.info.headimg)}}}):t(\"span\",{staticClass:\"no-data\"},[e._v(\"未上传\")])],1)])])],1),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:24}},[t(\"div\",{staticClass:\"info-item\"},[t(\"div\",{staticClass:\"info-label\"},[e._v(\"营业执照\")]),t(\"div\",{staticClass:\"info-value\"},[e.info.license&&\"\"!==e.info.license?t(\"el-image\",{staticStyle:{width:\"100px\",height:\"100px\",cursor:\"pointer\"},attrs:{src:e.info.license,fit:\"cover\"},on:{click:function(t){return e.showImage(e.info.license)}}},[t(\"div\",{staticClass:\"image-slot\",attrs:{slot:\"error\"},slot:\"error\"},[t(\"i\",{staticClass:\"el-icon-picture-outline\"})])]):t(\"span\",{staticClass:\"no-data\"},[e._v(\"未上传\")])],1)])])],1)],1),t(\"el-card\",{staticClass:\"info-card\",attrs:{shadow:\"hover\"}},[t(\"div\",{staticClass:\"card-header\",attrs:{slot:\"header\"},slot:\"header\"},[t(\"i\",{staticClass:\"el-icon-s-custom\"}),t(\"span\",{staticClass:\"card-title\"},[e._v(\"服务团队\")])]),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"调解员\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.tiaojie_name||\"未分配\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"法务专员\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.fawu_name||\"未分配\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"立案专员\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.lian_name||\"未分配\"))])])])],1),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"合同专员\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.htsczy_name||\"未分配\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"律师\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.ls_name||\"未分配\"))])])]),t(\"el-col\",{attrs:{span:8}},[t(\"div\",{staticClass:\"team-item\"},[t(\"div\",{staticClass:\"team-role\"},[e._v(\"业务员\")]),t(\"div\",{staticClass:\"team-name\"},[e._v(e._s(e.info.ywy_name||\"未分配\"))])])])],1)],1),t(\"el-card\",{staticClass:\"info-card\",attrs:{shadow:\"hover\"}},[t(\"div\",{staticClass:\"card-header\",attrs:{slot:\"header\"},slot:\"header\"},[t(\"i\",{staticClass:\"el-icon-money\"}),t(\"span\",{staticClass:\"card-title\"},[e._v(\"债务人信息\")])]),t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{data:e.info.debts,size:\"medium\",stripe:\"\",\"header-cell-style\":{background:\"#f5f7fa\",color:\"#606266\"}}},[t(\"el-table-column\",{attrs:{prop:\"name\",label:\"债务人姓名\",width:\"150\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-tag\",{attrs:{type:\"primary\",size:\"small\"}},[e._v(e._s(s.row.name))])]}}])}),t(\"el-table-column\",{attrs:{prop:\"tel\",label:\"债务人电话\",width:\"150\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"span\",{staticClass:\"phone-number\"},[e._v(e._s(s.row.tel))])]}}])}),t(\"el-table-column\",{attrs:{prop:\"money\",label:\"债务金额\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"span\",{staticClass:\"money-amount\"},[e._v(\"¥\"+e._s(s.row.money))])]}}])}),t(\"el-table-column\",{attrs:{prop:\"status\",label:\"状态\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-tag\",{attrs:{type:\"已完成\"===s.row.status?\"success\":\"warning\",size:\"small\"}},[e._v(\" \"+e._s(s.row.status)+\" \")])]}}])}),t(\"el-table-column\",{attrs:{label:\"操作\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-button\",{attrs:{type:\"text\",size:\"small\"},on:{click:function(t){return e.viewDebtDetail(s.row)}}},[t(\"i\",{staticClass:\"el-icon-view\"}),e._v(\" 详情 \")])]}}])})],1),e.info.debts&&0!==e.info.debts.length?e._e():t(\"div\",{staticClass:\"empty-data\"},[t(\"i\",{staticClass:\"el-icon-document\"}),t(\"p\",[e._v(\"暂无债务人信息\")])])],1),t(\"el-dialog\",{attrs:{title:\"图片查看\",visible:e.dialogVisible,width:\"30%\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[t(\"el-image\",{attrs:{src:e.show_image}})],1)],1)},i=[],l={name:\"UserDetails\",props:{id:{type:[String,Number],required:!0}},data(){return{info:{},loading:!1,dialogVisible:!1,show_image:\"\"}},watch:{id:{immediate:!0,handler(e){e&&0!=e&&(console.log(\"UserDetails 接收到 ID:\",e),this.getInfo(e))}}},methods:{getInfo(e){let t=this;console.log(\"正在获取用户信息，ID:\",e),t.loading=!0,setTimeout(()=>{const s={id:e,company:\"测试公司有限公司\",phone:\"13800138001\",nickname:\"张三\",linkman:\"李四\",headimg:\"\",yuangong_id:\"微信小程序\",linkphone:\"13800138002\",tiaojie_name:\"王调解员\",fawu_name:\"赵法务\",lian_name:\"钱立案员\",htsczy_name:\"孙合同员\",ls_name:\"周律师\",ywy_name:\"吴业务员\",license:\"\",start_time:\"2024-01-01\",year:1,debts:[{name:\"债务人A\",tel:\"13900139001\",money:\"50000\",status:\"处理中\"},{name:\"债务人B\",tel:\"13900139002\",money:\"30000\",status:\"已完成\"}]};t.info=s,t.loading=!1,console.log(\"用户数据加载完成:\",s)},500)},showImage(e){this.show_image=e,this.dialogVisible=!0},viewDebtDetail(e){console.log(\"查看债务人详情:\",e),this.$message.info(\"债务人详情功能待开发\")}}},n=l,r=(s(\"0091\"),s(\"2877\")),o=Object(r[\"a\"])(n,a,i,!1,null,\"4468717a\",null);t[\"a\"]=o.exports}}]);", "extractedComments": []}