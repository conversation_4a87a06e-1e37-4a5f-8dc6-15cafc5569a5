{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue", "mtime": 1748425644039}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetails", "name", "components", "data", "allSize", "list", "total", "currentId", "page", "size", "search", "keyword", "is_pay", "is_deal", "loading", "url", "title", "info", "dialogFormVisible", "dialogViewUserDetail", "show_image", "dialogVisible", "ruleForm", "is_num", "images", "attach_path", "rules", "required", "message", "trigger", "file_path", "form<PERSON>abe<PERSON><PERSON>", "options", "id", "options1", "mounted", "getData", "methods", "changeFile", "filed", "console", "log", "clearData", "viewUserData", "_this", "editData", "getInfo", "desc", "getRequest", "then", "resp", "code", "$message", "type", "msg", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "delData", "index", "splice", "refulsh", "$router", "go", "searchData", "postRequest", "count", "saveData", "$refs", "validate", "valid", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName"], "sources": ["src/views/pages/wenshu/shenhe.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\"> </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\"> </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\"> </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\"> </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\"> </el-table-column>\r\n        <el-table-column prop=\"nickname\" label=\"用户名\">\r\n            <template slot-scope=\"scope\">\r\n              <div @click=\"viewUserData(scope.row.uid)\">{{scope.row.nickname}}</div>\r\n            </template> </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"用户手机\">\r\n          <template slot-scope=\"scope\">\r\n            <div @click=\"viewUserData(scope.row.uid)\">{{scope.row.phone}}</div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              取消\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同内容\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同图片\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.images[0]\">\r\n            <div style=\"width: 100%;display: table-cell;\">\r\n          <div style=\"float: left;margin-left:2px;\"\r\n            v-for=\"(item2, index2) in ruleForm.images\"\r\n            :key=\"index2\"\r\n            class=\"image-list\"\r\n          >\r\n            <img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />\r\n          </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同文件\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.attach_path[0]\">\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n          <div\r\n            v-for=\"(item3, index3) in ruleForm.attach_path\"\r\n            :key=\"index3\"\r\n          >\r\n            <div v-if=\"item3\">\r\n              <div >文件{{ index3 +1 }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\">下载</a></div><br />\r\n            </div>\r\n          </div>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"用户详情\"\r\n            :visible.sync=\"dialogViewUserDetail\"\r\n            :close-on-click-modal=\"false\"  width=\"80%\"\r\n    >\r\n      <user-details :id=\"currentId\"></user-details>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/shenhe/\",\r\n      title: \"合同审核\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        images:{},\r\n        attach_path:{}\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": "AA8MA;AACA,OAAAA,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAN,KAAA;QACAO,MAAA;QACAC,MAAA;QACAC,WAAA;MACA;MAEAC,KAAA;QACAV,KAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,SAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,cAAA;MACAC,OAAA,GACA;QACAC,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA,EACA;MACAkB,QAAA,GACA;QACAD,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA,GACA;QACAiB,EAAA;QACAjB,KAAA;MACA;IAEA;EACA;EACAmB,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;MACAC,OAAA,CAAAC,GAAA,MAAAF,KAAA;IACA;IACAG,UAAA;MACA,KAAAhC,MAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAAwB,OAAA;IACA;IACAO,aAAAV,EAAA;MACA,IAAAW,KAAA;MACA,IAAAX,EAAA;QACA,KAAA1B,SAAA,GAAA0B,EAAA;MACA;MAEAW,KAAA,CAAAzB,oBAAA;IACA;IACA0B,SAAAZ,EAAA;MACA,IAAAW,KAAA;MACA,IAAAX,EAAA;QACA,KAAAa,OAAA,CAAAb,EAAA;MACA;QACA,KAAAX,QAAA;UACAN,KAAA;UACA+B,IAAA;QACA;MACA;IACA;IACAD,QAAAb,EAAA;MACA,IAAAW,KAAA;MACAA,KAAA,CAAAI,UAAA,CAAAJ,KAAA,CAAA7B,GAAA,gBAAAkB,EAAA,EAAAgB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAtB,QAAA,GAAA4B,IAAA,CAAA/C,IAAA;UACAyC,KAAA,CAAA1B,iBAAA;QACA;UACA0B,KAAA,CAAAQ,QAAA;YACAC,IAAA;YACAzB,OAAA,EAAAsB,IAAA,CAAAI;UACA;QACA;MACA;IACA;IACAC,QAAAtB,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAL,IAAA;MACA,GACAJ,IAAA;QACA,KAAAU,aAAA,MAAA5C,GAAA,mBAAAkB,EAAA,EAAAgB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAC,IAAA;cACAzB,OAAA,EAAAsB,IAAA,CAAAI;YACA;UACA;YACA,KAAAF,QAAA;cACAC,IAAA;cACAzB,OAAA,EAAAsB,IAAA,CAAAI;YACA;UACA;QACA;MACA,GACAM,KAAA;QACA,KAAAR,QAAA;UACAC,IAAA;UACAzB,OAAA;QACA;MACA;IACA;IACAiC,QAAAC,KAAA,EAAA7B,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAL,IAAA;MACA,GACAJ,IAAA;QACA,KAAAU,aAAA,MAAA5C,GAAA,kBAAAkB,EAAA,EAAAgB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAC,IAAA;cACAzB,OAAA;YACA;YACA,KAAAvB,IAAA,CAAA0D,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAR,QAAA;UACAC,IAAA;UACAzB,OAAA;QACA;MACA;IACA;IACAoC,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA3D,IAAA;MACA,KAAAC,IAAA;MACA,KAAA2B,OAAA;IACA;IAEAA,QAAA;MACA,IAAAQ,KAAA;MAEAA,KAAA,CAAA9B,OAAA;MACA8B,KAAA,CACAwB,WAAA,CACAxB,KAAA,CAAA7B,GAAA,mBAAA6B,KAAA,CAAApC,IAAA,cAAAoC,KAAA,CAAAnC,IAAA,EACAmC,KAAA,CAAAlC,MACA,EACAuC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAvC,IAAA,GAAA6C,IAAA,CAAA/C,IAAA;UACAyC,KAAA,CAAAtC,KAAA,GAAA4C,IAAA,CAAAmB,KAAA;QACA;QACAzB,KAAA,CAAA9B,OAAA;MACA;IACA;IACAwD,SAAA;MACA,IAAA1B,KAAA;MACA,KAAA2B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAL,WAAA,CAAAxB,KAAA,CAAA7B,GAAA,gBAAAO,QAAA,EAAA2B,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA;gBACAC,IAAA;gBACAzB,OAAA,EAAAsB,IAAA,CAAAI;cACA;cACA,KAAAlB,OAAA;cACAQ,KAAA,CAAA1B,iBAAA;YACA;cACA0B,KAAA,CAAAQ,QAAA;gBACAC,IAAA;gBACAzB,OAAA,EAAAsB,IAAA,CAAAI;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAoB,iBAAAC,GAAA;MACA,KAAAlE,IAAA,GAAAkE,GAAA;MAEA,KAAAvC,OAAA;IACA;IACAwC,oBAAAD,GAAA;MACA,KAAAnE,IAAA,GAAAmE,GAAA;MACA,KAAAvC,OAAA;IACA;IACAyC,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAA3B,IAAA;QACA,KAAAC,QAAA,CAAA2B,OAAA;QACA,KAAAzD,QAAA,MAAAiB,KAAA,IAAAuC,GAAA,CAAA3E,IAAA,CAAAY,GAAA;MACA;QACA,KAAAqC,QAAA,CAAA4B,KAAA,CAAAF,GAAA,CAAAxB,GAAA;MACA;IACA;IAEA2B,UAAAC,IAAA;MACA,KAAA9D,UAAA,GAAA8D,IAAA;MACA,KAAA7D,aAAA;IACA;IACA8D,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA7B,IAAA;MACA,KAAA+B,UAAA;QACA,KAAAhC,QAAA,CAAA4B,KAAA;QACA;MACA;IACA;IACAM,SAAAJ,IAAA,EAAAK,QAAA;MACA,IAAA3C,KAAA;MACAA,KAAA,CAAAI,UAAA,gCAAAkC,IAAA,EAAAjC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAtB,QAAA,CAAAiE,QAAA;UAEA3C,KAAA,CAAAQ,QAAA,CAAA2B,OAAA;QACA;UACAnC,KAAA,CAAAQ,QAAA,CAAA4B,KAAA,CAAA9B,IAAA,CAAAI,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}