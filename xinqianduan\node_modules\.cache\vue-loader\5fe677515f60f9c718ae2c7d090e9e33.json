{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue?vue&type=style&index=0&id=335f2615&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue", "mtime": 1748540171924}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["shenhe.vue"], "names": [], "mappings": ";AAwr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file": "shenhe.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-review-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document-checked\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入工单号/用户名/合同标题\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">审核状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同审核数据\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"order-sn\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ scope.row.order_sn }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.type === '合同审核' ? 'warning' : 'info'\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"title\" label=\"合同标题\" min-width=\"150\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"contract-title\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"desc\" label=\"审核要求\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"review-desc\">\r\n              {{ scope.row.desc }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"review_status\" label=\"审核状态\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getStatusType(scope.row.review_status)\"\r\n              size=\"small\"\r\n            >\r\n              {{ getStatusText(scope.row.review_status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"current_reviewer\" label=\"当前审核人\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.current_reviewer\">{{ scope.row.current_reviewer }}</span>\r\n            <span v-else class=\"text-muted\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-user\"></i>\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-phone\"></i>\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"提交时间\" width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- 审核按钮 -->\r\n              <el-button\r\n                v-if=\"canReview(scope.row)\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"startReview(scope.row)\"\r\n                icon=\"el-icon-edit-outline\"\r\n                class=\"action-btn\"\r\n              >\r\n                审核\r\n              </el-button>\r\n\r\n              <!-- 查看审核进度 -->\r\n              <el-button\r\n                type=\"info\"\r\n                size=\"mini\"\r\n                @click=\"viewReviewProgress(scope.row)\"\r\n                icon=\"el-icon-view\"\r\n                class=\"action-btn\"\r\n              >\r\n                进度\r\n              </el-button>\r\n\r\n              <!-- 预览合同 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"previewContract(scope.row)\"\r\n                icon=\"el-icon-document\"\r\n                class=\"action-btn\"\r\n              >\r\n                预览\r\n              </el-button>\r\n\r\n              <!-- 立案按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.review_status === 'approved'\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitToFiling(scope.row)\"\r\n                icon=\"el-icon-folder-add\"\r\n                class=\"action-btn\"\r\n              >\r\n                立案\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同内容\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同图片\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.images[0]\">\r\n            <div style=\"width: 100%;display: table-cell;\">\r\n          <div style=\"float: left;margin-left:2px;\"\r\n            v-for=\"(item2, index2) in ruleForm.images\"\r\n            :key=\"index2\"\r\n            class=\"image-list\"\r\n          >\r\n            <img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />\r\n          </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同文件\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.attach_path[0]\">\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n          <div\r\n            v-for=\"(item3, index3) in ruleForm.attach_path\"\r\n            :key=\"index3\"\r\n          >\r\n            <div v-if=\"item3\">\r\n              <div >文件{{ index3 +1 }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\">下载</a></div><br />\r\n            </div>\r\n          </div>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 合同预览对话框 -->\r\n    <el-dialog\r\n      title=\"合同预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"contract-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ previewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ previewData.nickname }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-time\"></i>\r\n              提交时间：{{ previewData.create_time }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div class=\"section\">\r\n            <h4>审核要求</h4>\r\n            <p>{{ previewData.desc }}</p>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.images && previewData.images.length\">\r\n            <h4>合同图片</h4>\r\n            <div class=\"image-gallery\">\r\n              <div\r\n                v-for=\"(image, index) in previewData.images\"\r\n                :key=\"index\"\r\n                class=\"image-item\"\r\n                @click=\"showImage(image)\"\r\n              >\r\n                <img :src=\"image\" alt=\"合同图片\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.attach_path && previewData.attach_path.length\">\r\n            <h4>合同文件</h4>\r\n            <div class=\"file-list\">\r\n              <div\r\n                v-for=\"(file, index) in previewData.attach_path\"\r\n                :key=\"index\"\r\n                class=\"file-item\"\r\n                v-if=\"file\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>文件{{ index + 1 }}</span>\r\n                <div class=\"file-actions\">\r\n                  <el-button type=\"text\" @click=\"viewFile(file)\">查看</el-button>\r\n                  <el-button type=\"text\" @click=\"downloadFile(file)\">下载</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog\r\n      title=\"合同审核\"\r\n      :visible.sync=\"dialogReview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"review-dialog\"\r\n    >\r\n      <div class=\"review-content\">\r\n        <div class=\"review-header\">\r\n          <h3>{{ reviewData.title }}</h3>\r\n          <div class=\"review-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ reviewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ reviewData.nickname }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"review-form\">\r\n          <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewForm\" label-width=\"100px\">\r\n            <el-form-item label=\"审核结果\" prop=\"result\">\r\n              <el-radio-group v-model=\"reviewForm.result\">\r\n                <el-radio label=\"approved\">通过</el-radio>\r\n                <el-radio label=\"rejected\">不通过</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              v-if=\"reviewForm.result === 'rejected'\"\r\n              label=\"不通过理由\"\r\n              prop=\"reason\"\r\n            >\r\n              <el-select\r\n                v-model=\"reviewForm.reason\"\r\n                placeholder=\"请选择不通过理由\"\r\n                style=\"width: 100%\"\r\n                clearable\r\n              >\r\n                <el-option label=\"合同条款不完整\" value=\"incomplete_terms\"></el-option>\r\n                <el-option label=\"法律条款有误\" value=\"legal_error\"></el-option>\r\n                <el-option label=\"格式不规范\" value=\"format_error\"></el-option>\r\n                <el-option label=\"内容与需求不符\" value=\"content_mismatch\"></el-option>\r\n                <el-option label=\"缺少必要附件\" value=\"missing_attachments\"></el-option>\r\n                <el-option label=\"其他问题\" value=\"other\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              label=\"审核意见\"\r\n              prop=\"comment\"\r\n              :rules=\"reviewForm.result === 'rejected' ? [{ required: true, message: '请填写审核意见', trigger: 'blur' }] : []\"\r\n            >\r\n              <el-input\r\n                type=\"textarea\"\r\n                v-model=\"reviewForm.comment\"\r\n                :rows=\"4\"\r\n                placeholder=\"请填写详细的审核意见...\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogReview = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitReview\" :loading=\"reviewSubmitting\">\r\n          提交审核\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核进度抽屉 -->\r\n    <el-drawer\r\n      title=\"审核进度\"\r\n      :visible.sync=\"dialogProgress\"\r\n      direction=\"rtl\"\r\n      size=\"50%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"progress-drawer\"\r\n    >\r\n      <div class=\"progress-drawer-content\">\r\n        <div class=\"progress-header\">\r\n          <h3>{{ progressData.title }}</h3>\r\n          <div class=\"progress-meta\">\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>工单号：{{ progressData.order_sn }}</span>\r\n            </div>\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-info\"></i>\r\n              <span>当前状态：{{ getStatusText(progressData.review_status) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"progress-timeline\">\r\n          <el-timeline>\r\n            <el-timeline-item\r\n              v-for=\"(step, index) in reviewSteps\"\r\n              :key=\"index\"\r\n              :timestamp=\"step.time\"\r\n              :type=\"step.status === 'completed' ? 'success' : step.status === 'current' ? 'primary' : 'info'\"\r\n              :icon=\"step.status === 'completed' ? 'el-icon-check' : step.status === 'current' ? 'el-icon-loading' : 'el-icon-time'\"\r\n              placement=\"top\"\r\n            >\r\n              <div class=\"timeline-content\">\r\n                <h4>{{ step.title }}</h4>\r\n                <p>{{ step.reviewer }}</p>\r\n                <div v-if=\"step.comment\" class=\"step-comment\">\r\n                  <strong>审核意见：</strong>{{ step.comment }}\r\n                </div>\r\n                <div v-if=\"step.reason\" class=\"step-reason\">\r\n                  <strong>不通过理由：</strong>{{ step.reason }}\r\n                </div>\r\n              </div>\r\n            </el-timeline-item>\r\n          </el-timeline>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/shenhe/\",\r\n      title: \"合同审核\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogPreview: false,\r\n      dialogReview: false,\r\n      dialogProgress: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewData: {},\r\n      reviewData: {},\r\n      progressData: {},\r\n      reviewSteps: [],\r\n      reviewSubmitting: false,\r\n\r\n      // 审核表单\r\n      reviewForm: {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      },\r\n\r\n      // 审核表单验证规则\r\n      reviewRules: {\r\n        result: [\r\n          { required: true, message: '请选择审核结果', trigger: 'change' }\r\n        ]\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        images:{},\r\n        attach_path:{}\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"SH202403001\",\r\n            type: \"合同审核\",\r\n            title: \"劳动合同审核\",\r\n            desc: \"请帮忙审核劳动合同条款，检查是否符合劳动法规定，特别关注薪资、工作时间、福利待遇等条款的合法性\",\r\n            review_status: \"mediator_review\", // 调解员审核中\r\n            current_reviewer: \"调解员\",\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\",\r\n            images: [\r\n              \"/uploads/contracts/labor_contract_page1.jpg\",\r\n              \"/uploads/contracts/labor_contract_page2.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/labor_contract.pdf\",\r\n              \"/uploads/contracts/labor_contract_supplement.docx\"\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"SH202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"房屋租赁合同审核，需要检查租金条款、押金规定、违约责任等是否合理\",\r\n            review_status: \"business_review\", // 业务员审核中\r\n            current_reviewer: \"业务员\",\r\n            nickname: \"李四\",\r\n            phone: \"***********\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/lease_contract_page1.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/lease_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"SH202403003\",\r\n            type: \"合同审核\",\r\n            title: \"买卖合同审核\",\r\n            desc: \"商品买卖合同审核，重点关注货物交付、付款方式、质量保证等条款\",\r\n            review_status: \"approved\", // 全部审核通过\r\n            current_reviewer: \"\",\r\n            nickname: \"王五\",\r\n            phone: \"***********\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\",\r\n            images: [],\r\n            attach_path: [\r\n              \"/uploads/contracts/sale_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"SH202403004\",\r\n            type: \"合同审核\",\r\n            title: \"服务合同审核\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            review_status: \"lawyer_review\", // 律师审核中\r\n            current_reviewer: \"律师\",\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\",\r\n            images: [],\r\n            attach_path: []\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"SH202403005\",\r\n            type: \"合同审核\",\r\n            title: \"借款合同审核\",\r\n            desc: \"个人借款合同审核，需要确认利率、还款方式、担保条款等是否符合法律规定\",\r\n            review_status: \"rejected\", // 审核不通过\r\n            current_reviewer: \"\",\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/loan_contract_page1.jpg\",\r\n              \"/uploads/contracts/loan_contract_page2.jpg\",\r\n              \"/uploads/contracts/loan_contract_page3.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/loan_contract.pdf\"\r\n            ]\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 预览合同\r\n    previewContract(row) {\r\n      console.log('预览合同:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 查看文件\r\n    viewFile(fileUrl) {\r\n      window.open(fileUrl, '_blank');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        'submitted': 'info',\r\n        'mediator_review': 'warning',\r\n        'business_review': 'warning',\r\n        'lawyer_review': 'warning',\r\n        'final_review': 'warning',\r\n        'approved': 'success',\r\n        'rejected': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'submitted': '已提交',\r\n        'mediator_review': '调解员审核中',\r\n        'business_review': '业务员审核中',\r\n        'lawyer_review': '律师审核中',\r\n        'final_review': '最终审核中',\r\n        'approved': '审核通过',\r\n        'rejected': '审核不通过'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 判断是否可以审核\r\n    canReview(row) {\r\n      // 这里可以根据当前用户角色和审核状态判断\r\n      // 简化处理：只要不是已通过或已拒绝的都可以审核\r\n      return row.review_status !== 'approved' && row.review_status !== 'rejected';\r\n    },\r\n\r\n    // 开始审核\r\n    startReview(row) {\r\n      console.log('开始审核:', row);\r\n      this.reviewData = row;\r\n\r\n      // 重置审核表单\r\n      this.reviewForm = {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      };\r\n\r\n      this.dialogReview = true;\r\n    },\r\n\r\n    // 提交审核\r\n    submitReview() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewSubmitting = true;\r\n\r\n          // 模拟提交审核\r\n          setTimeout(() => {\r\n            const result = this.reviewForm.result;\r\n            const currentData = this.reviewData;\r\n\r\n            // 更新列表中的数据\r\n            const index = this.list.findIndex(item => item.id === currentData.id);\r\n            if (index !== -1) {\r\n              if (result === 'approved') {\r\n                // 通过审核，进入下一个审核环节\r\n                this.list[index] = this.getNextReviewStatus(this.list[index]);\r\n              } else {\r\n                // 审核不通过\r\n                this.list[index].review_status = 'rejected';\r\n                this.list[index].current_reviewer = '';\r\n              }\r\n            }\r\n\r\n            this.reviewSubmitting = false;\r\n            this.dialogReview = false;\r\n            this.$message.success('审核提交成功！');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取下一个审核状态\r\n    getNextReviewStatus(currentItem) {\r\n      const statusFlow = {\r\n        'submitted': 'mediator_review',\r\n        'mediator_review': 'business_review',\r\n        'business_review': 'lawyer_review',\r\n        'lawyer_review': 'final_review',\r\n        'final_review': 'approved'\r\n      };\r\n\r\n      const reviewerMap = {\r\n        'mediator_review': '调解员',\r\n        'business_review': '业务员',\r\n        'lawyer_review': '律师',\r\n        'final_review': '最终审核人',\r\n        'approved': ''\r\n      };\r\n\r\n      const nextStatus = statusFlow[currentItem.review_status];\r\n      return {\r\n        ...currentItem,\r\n        review_status: nextStatus,\r\n        current_reviewer: reviewerMap[nextStatus]\r\n      };\r\n    },\r\n\r\n    // 查看审核进度\r\n    viewReviewProgress(row) {\r\n      console.log('查看审核进度:', row);\r\n      this.progressData = row;\r\n\r\n      // 模拟审核步骤数据\r\n      this.reviewSteps = this.generateReviewSteps(row);\r\n      this.dialogProgress = true;\r\n    },\r\n\r\n    // 生成审核步骤\r\n    generateReviewSteps(row) {\r\n      const allSteps = [\r\n        {\r\n          title: '法务提交',\r\n          reviewer: '法务部门',\r\n          status: 'completed',\r\n          time: row.create_time,\r\n          comment: '合同已完成，提交审核'\r\n        },\r\n        {\r\n          title: '调解员审核',\r\n          reviewer: '调解员',\r\n          status: row.review_status === 'submitted' ? 'pending' :\r\n                  row.review_status === 'mediator_review' ? 'current' : 'completed',\r\n          time: row.review_status === 'submitted' ? '' : '2024-03-20 11:00:00',\r\n          comment: row.review_status === 'submitted' ? '' : '合同条款符合调解要求'\r\n        },\r\n        {\r\n          title: '业务员审核',\r\n          reviewer: '业务员',\r\n          status: ['submitted', 'mediator_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'business_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '2024-03-20 14:30:00',\r\n          comment: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '业务流程审核通过'\r\n        },\r\n        {\r\n          title: '律师审核',\r\n          reviewer: '律师',\r\n          status: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'lawyer_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '2024-03-20 16:00:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '法律条款审核通过'\r\n        },\r\n        {\r\n          title: '最终审核',\r\n          reviewer: '最终审核人',\r\n          status: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'final_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '2024-03-20 17:30:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '最终审核通过'\r\n        }\r\n      ];\r\n\r\n      // 如果审核被拒绝，添加拒绝信息\r\n      if (row.review_status === 'rejected') {\r\n        allSteps.push({\r\n          title: '审核不通过',\r\n          reviewer: '审核人员',\r\n          status: 'completed',\r\n          time: '2024-03-20 18:00:00',\r\n          comment: '',\r\n          reason: '合同条款不完整，需要重新修改'\r\n        });\r\n      }\r\n\r\n      return allSteps;\r\n    },\r\n\r\n    // 提交立案\r\n    submitToFiling(row) {\r\n      this.$confirm('确认将此合同提交到立案部门？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交立案\r\n        setTimeout(() => {\r\n          this.$message.success('已成功提交到立案部门！');\r\n\r\n          // 更新状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            this.list[index].review_status = 'filed';\r\n            this.list[index].current_reviewer = '立案部门';\r\n          }\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-review-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.order-sn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.order-sn i {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.contract-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.contract-title i {\r\n  color: #e6a23c;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-desc {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.user-link {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n}\r\n\r\n.user-link i {\r\n  font-size: 12px;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 合同预览对话框样式 */\r\n.contract-preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #409eff;\r\n}\r\n\r\n.preview-body .section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.preview-body .section h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-body .section p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.image-gallery {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.image-item {\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n}\r\n\r\n.file-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-item i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 审核对话框样式 */\r\n.review-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.review-content {\r\n  padding: 24px;\r\n}\r\n\r\n.review-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.review-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.review-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.review-form {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #f093fb;\r\n}\r\n\r\n/* 审核进度抽屉样式 */\r\n.progress-drawer >>> .el-drawer {\r\n  border-radius: 0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.progress-drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n.progress-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.progress-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 16px 0;\r\n}\r\n\r\n.progress-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.progress-meta .meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.progress-meta .meta-item i {\r\n  color: #4facfe;\r\n  font-size: 16px;\r\n}\r\n\r\n.progress-timeline {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  border-left: 4px solid #4facfe;\r\n}\r\n\r\n.timeline-content h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.timeline-content p {\r\n  color: #606266;\r\n  margin: 0 0 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.step-comment,\r\n.step-reason {\r\n  background: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.step-comment {\r\n  border-left: 3px solid #67c23a;\r\n}\r\n\r\n.step-reason {\r\n  border-left: 3px solid #f56c6c;\r\n}\r\n\r\n.step-comment strong,\r\n.step-reason strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-muted {\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-review-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .image-gallery {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}