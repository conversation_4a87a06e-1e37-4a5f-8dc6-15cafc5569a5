{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748464417190}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUGVybWlzc2lvbk1hbmFnZW1lbnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB2aWV3TW9kZTogJ3RyZWUnLCAvLyB0cmVlIHwgdGFibGUKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNlYXJjaDogewogICAgICAgIGtleXdvcmQ6ICIiLAogICAgICAgIHR5cGU6ICIiLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgdHJlZURhdGE6IFtdLAogICAgICBvcmlnaW5hbERhdGE6IFtdLCAvLyDkv53lrZjljp/lp4vmlbDmja4KICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dUaXRsZTogIuaWsOWinuadg+mZkCIsCiAgICAgIHBhcmVudE9wdGlvbnM6IFtdLAogICAgICBydWxlRm9ybTogewogICAgICAgIGlkOiBudWxsLAogICAgICAgIGxhYmVsOiAiIiwKICAgICAgICBjb2RlOiAiIiwKICAgICAgICB0eXBlOiAibWVudSIsCiAgICAgICAgcGFyZW50X2lkOiBudWxsLAogICAgICAgIHNvcnQ6IDAsCiAgICAgICAgc3RhdHVzOiAxLAogICAgICAgIGRlc2NyaXB0aW9uOiAiIiwKICAgICAgICBwYXRoOiAiIiwKICAgICAgICBpY29uOiAiIgogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIGxhYmVsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5p2D6ZmQ5ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGNvZGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXmnYPpmZDku6PnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeadg+mZkOexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRyZWVQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLAogICAgICAgIGxhYmVsOiAnbGFiZWwnCiAgICAgIH0sCiAgICAgIGNhc2NhZGVyUHJvcHM6IHsKICAgICAgICB2YWx1ZTogJ2lkJywKICAgICAgICBsYWJlbDogJ2xhYmVsJywKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICBjaGVja1N0cmljdGx5OiB0cnVlCiAgICAgIH0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLy8g6KGo5qC85pWw5o2uIC0g5bCG5qCR5b2i5pWw5o2u5omB5bmz5YyW5L2G5L+d5oyB5bGC57qn5YWz57O7CiAgICB0YWJsZURhdGEoKSB7CiAgICAgIHJldHVybiB0aGlzLmZsYXR0ZW5UcmVlRm9yVGFibGUodGhpcy50cmVlRGF0YSk7CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXREYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDojrflj5bmnYPpmZDmlbDmja4KICAgIGdldERhdGEoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIAogICAgICAvLyDkvb/nlKjmtYvor5XmlbDmja4KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgCiAgICAgICAgY29uc3QgbW9ja0RhdGEgPSBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAxLAogICAgICAgICAgICBsYWJlbDogIuezu+e7n+euoeeQhiIsCiAgICAgICAgICAgIGNvZGU6ICJzeXN0ZW0iLAogICAgICAgICAgICB0eXBlOiAibWVudSIsCiAgICAgICAgICAgIHBhcmVudF9pZDogbnVsbCwKICAgICAgICAgICAgc29ydDogMSwKICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICBkZXNjcmlwdGlvbjogIuezu+e7n+euoeeQhuaooeWdlyIsCiAgICAgICAgICAgIHBhdGg6ICIvc3lzdGVtIiwKICAgICAgICAgICAgaWNvbjogImVsLWljb24tc2V0dGluZyIsCiAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDExLAogICAgICAgICAgICAgICAgbGFiZWw6ICLnlKjmiLfnrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTp1c2VyIiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMSwKICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIueUqOaIt+euoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL3VzZXIiLAogICAgICAgICAgICAgICAgaWNvbjogImVsLWljb24tdXNlciIsCiAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiLAogICAgICAgICAgICAgICAgY2hpbGRyZW46IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAxMTEsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmn6XnnIvnlKjmiLciLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJzeXN0ZW06dXNlcjp2aWV3IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDExLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5p+l55yL55So5oi35YiX6KGoIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMTEyLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5paw5aKe55So5oi3IiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAic3lzdGVtOnVzZXI6YWRkIiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDExLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDIsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5paw5aKe55So5oi3IiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMTEzLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi57yW6L6R55So5oi3IiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAic3lzdGVtOnVzZXI6ZWRpdCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAzLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIue8lui+keeUqOaIt+S/oeaBryIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDExNCwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWIoOmZpOeUqOaItyIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTp1c2VyOmRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiA0LAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWIoOmZpOeUqOaItyIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBpZDogMTIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuiBjOS9jeeuoeeQhiIsCiAgICAgICAgICAgICAgICBjb2RlOiAic3lzdGVtOnBvc2l0aW9uIiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMSwKICAgICAgICAgICAgICAgIHNvcnQ6IDIsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuiBjOS9jeeuoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL3poaXdlaSIsCiAgICAgICAgICAgICAgICBpY29uOiAiZWwtaWNvbi1wb3N0Y2FyZCIsCiAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiLAogICAgICAgICAgICAgICAgY2hpbGRyZW46IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAxMjEsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmn6XnnIvogYzkvY0iLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJzeXN0ZW06cG9zaXRpb246dmlldyIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMiwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAxLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuafpeeci+iBjOS9jeWIl+ihqCIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDEyMiwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuaWsOWinuiBjOS9jSIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTpwb3NpdGlvbjphZGQiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMTIsCiAgICAgICAgICAgICAgICAgICAgc29ydDogMiwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLmlrDlop7ogYzkvY0iLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAxMjMsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLnvJbovpHogYzkvY0iLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJzeXN0ZW06cG9zaXRpb246ZWRpdCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMiwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAzLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIue8lui+keiBjOS9jeS/oeaBryIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDEyNCwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWIoOmZpOiBjOS9jSIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTpwb3NpdGlvbjpkZWxldGUiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMTIsCiAgICAgICAgICAgICAgICAgICAgc29ydDogNCwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLliKDpmaTogYzkvY0iLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDEzLAogICAgICAgICAgICAgICAgbGFiZWw6ICLmnYPpmZDnrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTpwZXJtaXNzaW9uIiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMSwKICAgICAgICAgICAgICAgIHNvcnQ6IDMsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuadg+mZkOeuoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL3F1YW54aWFuIiwKICAgICAgICAgICAgICAgIGljb246ICJlbC1pY29uLWtleSIsCiAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiLAogICAgICAgICAgICAgICAgY2hpbGRyZW46IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAxMzEsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmn6XnnIvmnYPpmZAiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJzeXN0ZW06cGVybWlzc2lvbjp2aWV3IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDEzLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5p+l55yL5p2D6ZmQ5YiX6KGoIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMTMyLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5paw5aKe5p2D6ZmQIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAic3lzdGVtOnBlcm1pc3Npb246YWRkIiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDEzLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDIsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5paw5aKe5p2D6ZmQIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMTMzLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi57yW6L6R5p2D6ZmQIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAic3lzdGVtOnBlcm1pc3Npb246ZWRpdCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMywKICAgICAgICAgICAgICAgICAgICBzb3J0OiAzLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIue8lui+keadg+mZkOS/oeaBryIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDEzNCwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWIoOmZpOadg+mZkCIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogInN5c3RlbTpwZXJtaXNzaW9uOmRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAxMywKICAgICAgICAgICAgICAgICAgICBzb3J0OiA0LAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWIoOmZpOadg+mZkCIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBpZDogMiwKICAgICAgICAgICAgbGFiZWw6ICLkuJrliqHnrqHnkIYiLAogICAgICAgICAgICBjb2RlOiAiYnVzaW5lc3MiLAogICAgICAgICAgICB0eXBlOiAibWVudSIsCiAgICAgICAgICAgIHBhcmVudF9pZDogbnVsbCwKICAgICAgICAgICAgc29ydDogMiwKICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICBkZXNjcmlwdGlvbjogIuS4muWKoeeuoeeQhuaooeWdlyIsCiAgICAgICAgICAgIHBhdGg6ICIvYnVzaW5lc3MiLAogICAgICAgICAgICBpY29uOiAiZWwtaWNvbi1zdWl0Y2FzZSIsCiAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDIxLAogICAgICAgICAgICAgICAgbGFiZWw6ICLorqLljZXnrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOm9yZGVyIiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMiwKICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuiuouWNleeuoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL2RpbmdkYW4iLAogICAgICAgICAgICAgICAgaWNvbjogImVsLWljb24tZG9jdW1lbnQiLAogICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIiwKICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMjExLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5p+l55yL6K6i5Y2VIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiYnVzaW5lc3M6b3JkZXI6dmlldyIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAyMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAxLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuafpeeci+iuouWNleWIl+ihqCIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDIxMiwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuaWsOWinuiuouWNlSIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOm9yZGVyOmFkZCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAyMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAyLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuaWsOWinuiuouWNlSIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDIxMywKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIue8lui+keiuouWNlSIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOm9yZGVyOmVkaXQiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMjEsCiAgICAgICAgICAgICAgICAgICAgc29ydDogMywKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLnvJbovpHorqLljZXkv6Hmga8iLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAyMTQsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLliKDpmaTorqLljZUiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJidXNpbmVzczpvcmRlcjpkZWxldGUiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMjEsCiAgICAgICAgICAgICAgICAgICAgc29ydDogNCwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLliKDpmaTorqLljZUiLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAyMTUsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLlr7zlh7rorqLljZUiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJidXNpbmVzczpvcmRlcjpleHBvcnQiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMjEsCiAgICAgICAgICAgICAgICAgICAgc29ydDogNSwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLlr7zlh7rorqLljZXmlbDmja4iLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDIyLAogICAgICAgICAgICAgICAgbGFiZWw6ICLlrqLmiLfnrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOmN1c3RvbWVyIiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMiwKICAgICAgICAgICAgICAgIHNvcnQ6IDIsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWuouaIt+euoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL2N1c3RvbWVyIiwKICAgICAgICAgICAgICAgIGljb246ICJlbC1pY29uLXVzZXItc29saWQiLAogICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIiwKICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMjIxLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5p+l55yL5a6i5oi3IiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiYnVzaW5lc3M6Y3VzdG9tZXI6dmlldyIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAyMiwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAxLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuafpeeci+WuouaIt+WIl+ihqCIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDIyMiwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuaWsOWinuWuouaItyIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOmN1c3RvbWVyOmFkZCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAyMiwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAyLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuaWsOWinuWuouaItyIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDIyMywKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIue8lui+keWuouaItyIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImJ1c2luZXNzOmN1c3RvbWVyOmVkaXQiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMjIsCiAgICAgICAgICAgICAgICAgICAgc29ydDogMywKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLnvJbovpHlrqLmiLfkv6Hmga8iLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAyMjQsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLliKDpmaTlrqLmiLciLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJidXNpbmVzczpjdXN0b21lcjpkZWxldGUiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMjIsCiAgICAgICAgICAgICAgICAgICAgc29ydDogNCwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLliKDpmaTlrqLmiLciLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgXQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6IDMsCiAgICAgICAgICAgIGxhYmVsOiAi5paH5Lmm566h55CGIiwKICAgICAgICAgICAgY29kZTogImRvY3VtZW50IiwKICAgICAgICAgICAgdHlwZTogIm1lbnUiLAogICAgICAgICAgICBwYXJlbnRfaWQ6IG51bGwsCiAgICAgICAgICAgIHNvcnQ6IDMsCiAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgZGVzY3JpcHRpb246ICLmlofkuabnrqHnkIbmqKHlnZciLAogICAgICAgICAgICBwYXRoOiAiL2RvY3VtZW50IiwKICAgICAgICAgICAgaWNvbjogImVsLWljb24tZG9jdW1lbnQtY29weSIsCiAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDMxLAogICAgICAgICAgICAgICAgbGFiZWw6ICLlkIjlkIznrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50OmNvbnRyYWN0IiwKICAgICAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMywKICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWQiOWQjOeuoeeQhuWKn+iDvSIsCiAgICAgICAgICAgICAgICBwYXRoOiAiL2hldG9uZyIsCiAgICAgICAgICAgICAgICBpY29uOiAiZWwtaWNvbi1kb2N1bWVudCIsCiAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiLAogICAgICAgICAgICAgICAgY2hpbGRyZW46IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAzMTEsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmn6XnnIvlkIjlkIwiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJkb2N1bWVudDpjb250cmFjdDp2aWV3IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMxLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5p+l55yL5ZCI5ZCM5YiX6KGoIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMzEyLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5paw5aKe5ZCI5ZCMIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiZG9jdW1lbnQ6Y29udHJhY3Q6YWRkIiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMxLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDIsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5paw5aKe5ZCI5ZCMIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMzEzLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi57yW6L6R5ZCI5ZCMIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiZG9jdW1lbnQ6Y29udHJhY3Q6ZWRpdCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAzLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIue8lui+keWQiOWQjOS/oeaBryIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDMxNCwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWIoOmZpOWQiOWQjCIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50OmNvbnRyYWN0OmRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzMSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiA0LAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWIoOmZpOWQiOWQjCIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDMxNSwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWuoeaguOWQiOWQjCIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50OmNvbnRyYWN0OmF1ZGl0IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMxLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDUsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5a6h5qC45ZCI5ZCMIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGlkOiAzMiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5b6L5biI5Ye9566h55CGIiwKICAgICAgICAgICAgICAgIGNvZGU6ICJkb2N1bWVudDpsYXd5ZXIiLAogICAgICAgICAgICAgICAgdHlwZTogIm1lbnUiLAogICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzLAogICAgICAgICAgICAgICAgc29ydDogMiwKICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5b6L5biI5Ye9566h55CG5Yqf6IO9IiwKICAgICAgICAgICAgICAgIHBhdGg6ICIvbGF3eWVyIiwKICAgICAgICAgICAgICAgIGljb246ICJlbC1pY29uLW1lc3NhZ2UiLAogICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIiwKICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMzIxLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5p+l55yL5b6L5biI5Ye9IiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiZG9jdW1lbnQ6bGF3eWVyOnZpZXciLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogMzIsCiAgICAgICAgICAgICAgICAgICAgc29ydDogMSwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLmn6XnnIvlvovluIjlh73liJfooagiLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiAzMjIsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLlj5HpgIHlvovluIjlh70iLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJkb2N1bWVudDpsYXd5ZXI6c2VuZCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzMiwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAyLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWPkemAgeW+i+W4iOWHvSIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDMyMywKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIue8lui+keW+i+W4iOWHvSIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50Omxhd3llcjplZGl0IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMyLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDMsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi57yW6L6R5b6L5biI5Ye9IiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGlkOiAzMywKICAgICAgICAgICAgICAgIGxhYmVsOiAi6K++56iL566h55CGIiwKICAgICAgICAgICAgICAgIGNvZGU6ICJkb2N1bWVudDpjb3Vyc2UiLAogICAgICAgICAgICAgICAgdHlwZTogIm1lbnUiLAogICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzLAogICAgICAgICAgICAgICAgc29ydDogMywKICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi6K++56iL566h55CG5Yqf6IO9IiwKICAgICAgICAgICAgICAgIHBhdGg6ICIva2VjaGVuZyIsCiAgICAgICAgICAgICAgICBpY29uOiAiZWwtaWNvbi12aWRlby1wbGF5IiwKICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIsCiAgICAgICAgICAgICAgICBjaGlsZHJlbjogWwogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDMzMSwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuafpeeci+ivvueoiyIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50OmNvdXJzZTp2aWV3IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMzLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDEsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5p+l55yL6K++56iL5YiX6KGoIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMzMyLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5paw5aKe6K++56iLIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiZG9jdW1lbnQ6Y291cnNlOmFkZCIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzMywKICAgICAgICAgICAgICAgICAgICBzb3J0OiAyLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuaWsOWinuivvueoiyIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDMzMywKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIue8lui+keivvueoiyIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImRvY3VtZW50OmNvdXJzZTplZGl0IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDMzLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDMsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi57yW6L6R6K++56iLIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBpZDogMzM0LAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5Yig6Zmk6K++56iLIiwKICAgICAgICAgICAgICAgICAgICBjb2RlOiAiZG9jdW1lbnQ6Y291cnNlOmRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiAzMywKICAgICAgICAgICAgICAgICAgICBzb3J0OiA0LAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuWIoOmZpOivvueoiyIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBpZDogNCwKICAgICAgICAgICAgbGFiZWw6ICLotKLliqHnrqHnkIYiLAogICAgICAgICAgICBjb2RlOiAiZmluYW5jZSIsCiAgICAgICAgICAgIHR5cGU6ICJtZW51IiwKICAgICAgICAgICAgcGFyZW50X2lkOiBudWxsLAogICAgICAgICAgICBzb3J0OiA0LAogICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi6LSi5Yqh566h55CG5qih5Z2XIiwKICAgICAgICAgICAgcGF0aDogIi9maW5hbmNlIiwKICAgICAgICAgICAgaWNvbjogImVsLWljb24tY29pbiIsCiAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgaWQ6IDQxLAogICAgICAgICAgICAgICAgbGFiZWw6ICLmlK/ku5jnrqHnkIYiLAogICAgICAgICAgICAgICAgY29kZTogImZpbmFuY2U6cGF5bWVudCIsCiAgICAgICAgICAgICAgICB0eXBlOiAibWVudSIsCiAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDQsCiAgICAgICAgICAgICAgICBzb3J0OiAxLAogICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLmlK/ku5jnrqHnkIblip/og70iLAogICAgICAgICAgICAgICAgcGF0aDogIi9vcmRlciIsCiAgICAgICAgICAgICAgICBpY29uOiAiZWwtaWNvbi1tb25leSIsCiAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiLAogICAgICAgICAgICAgICAgY2hpbGRyZW46IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiA0MTEsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmn6XnnIvmlK/ku5jorrDlvZUiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJmaW5hbmNlOnBheW1lbnQ6dmlldyIsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogImFjdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgcGFyZW50X2lkOiA0MSwKICAgICAgICAgICAgICAgICAgICBzb3J0OiAxLAogICAgICAgICAgICAgICAgICAgIHN0YXR1czogMSwKICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogIuafpeeci+aUr+S7mOiusOW9lSIsCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTAxIDEwOjAwOjAwIgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDQxMiwKICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWkhOeQhumAgOasviIsCiAgICAgICAgICAgICAgICAgICAgY29kZTogImZpbmFuY2U6cGF5bWVudDpyZWZ1bmQiLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgIHBhcmVudF9pZDogNDEsCiAgICAgICAgICAgICAgICAgICAgc29ydDogMiwKICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICLlpITnkIbpgIDmrL7nlLPor7ciLAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wMSAxMDowMDowMCIKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGlkOiA0MTMsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLlr7zlh7rotKLliqHmiqXooagiLAogICAgICAgICAgICAgICAgICAgIGNvZGU6ICJmaW5hbmNlOnBheW1lbnQ6ZXhwb3J0IiwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICBwYXJlbnRfaWQ6IDQxLAogICAgICAgICAgICAgICAgICAgIHNvcnQ6IDMsCiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAi5a+85Ye66LSi5Yqh5oql6KGoIiwKICAgICAgICAgICAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDEgMTA6MDA6MDAiCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0KICAgICAgICBdOwogICAgICAgIAogICAgICAgIHRoaXMudHJlZURhdGEgPSBtb2NrRGF0YTsKICAgICAgICB0aGlzLm9yaWdpbmFsRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobW9ja0RhdGEpKTsgLy8g5rex5ou36LSd5L+d5a2Y5Y6f5aeL5pWw5o2uCiAgICAgICAgdGhpcy51cGRhdGVQYXJlbnRPcHRpb25zKCk7CiAgICAgIH0sIDUwMCk7CiAgICB9LAoKICAgIC8vIOWwhuagkeW9ouaVsOaNruaJgeW5s+WMlueUqOS6juihqOagvOaYvuekuu+8iOS/neaMgeWxgue6p+e7k+aehO+8iQogICAgZmxhdHRlblRyZWVGb3JUYWJsZSh0cmVlLCBsZXZlbCA9IDAsIHJlc3VsdCA9IFtdKSB7CiAgICAgIHRyZWUuZm9yRWFjaChub2RlID0+IHsKICAgICAgICBjb25zdCBmbGF0Tm9kZSA9IHsKICAgICAgICAgIC4uLm5vZGUsCiAgICAgICAgICBsZXZlbDogbGV2ZWwsCiAgICAgICAgICBoYXNDaGlsZHJlbjogbm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDAKICAgICAgICB9OwogICAgICAgIC8vIOenu+mZpGNoaWxkcmVu5bGe5oCn6YG/5YWN6KGo5qC85riy5p+T6Zeu6aKYCiAgICAgICAgZGVsZXRlIGZsYXROb2RlLmNoaWxkcmVuOwogICAgICAgIHJlc3VsdC5wdXNoKGZsYXROb2RlKTsKCiAgICAgICAgLy8g6YCS5b2S5aSE55CG5a2Q6IqC54K5CiAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmZsYXR0ZW5UcmVlRm9yVGFibGUobm9kZS5jaGlsZHJlbiwgbGV2ZWwgKyAxLCByZXN1bHQpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiByZXN1bHQ7CiAgICB9LAoKICAgIC8vIOabtOaWsOeItue6p+adg+mZkOmAiemhuQogICAgdXBkYXRlUGFyZW50T3B0aW9ucygpIHsKICAgICAgdGhpcy5wYXJlbnRPcHRpb25zID0gdGhpcy5idWlsZENhc2NhZGVyT3B0aW9ucyh0aGlzLnRyZWVEYXRhKTsKICAgIH0sCgogICAgLy8g5p6E5bu657qn6IGU6YCJ5oup5Zmo6YCJ6aG5CiAgICBidWlsZENhc2NhZGVyT3B0aW9ucyh0cmVlKSB7CiAgICAgIHJldHVybiB0cmVlLm1hcChub2RlID0+ICh7CiAgICAgICAgaWQ6IG5vZGUuaWQsCiAgICAgICAgbGFiZWw6IG5vZGUubGFiZWwsCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4gPyB0aGlzLmJ1aWxkQ2FzY2FkZXJPcHRpb25zKG5vZGUuY2hpbGRyZW4pIDogW10KICAgICAgfSkpOwogICAgfSwKCiAgICAvLyDmkJzntKLmlbDmja4KICAgIHNlYXJjaERhdGEoKSB7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKCiAgICAvLyDmuIXnqbrmkJzntKIKICAgIGNsZWFyU2VhcmNoKCkgewogICAgICB0aGlzLnNlYXJjaCA9IHsKICAgICAgICBrZXl3b3JkOiAiIiwKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBzdGF0dXM6ICIiCiAgICAgIH07CiAgICAgIHRoaXMuc2VhcmNoRGF0YSgpOwogICAgfSwKCiAgICAvLyDliLfmlrDpobXpnaIKICAgIHJlZnVsc2goKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsKICAgIH0sCgogICAgLy8g57yW6L6R5p2D6ZmQCiAgICBlZGl0RGF0YShpZCkgewogICAgICBpZiAoaWQgPT09IDApIHsKICAgICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIuaWsOWinuadg+mZkCI7CiAgICAgICAgdGhpcy5ydWxlRm9ybSA9IHsKICAgICAgICAgIGlkOiBudWxsLAogICAgICAgICAgbGFiZWw6ICIiLAogICAgICAgICAgY29kZTogIiIsCiAgICAgICAgICB0eXBlOiAibWVudSIsCiAgICAgICAgICBwYXJlbnRfaWQ6IG51bGwsCiAgICAgICAgICBzb3J0OiAwLAogICAgICAgICAgc3RhdHVzOiAxLAogICAgICAgICAgZGVzY3JpcHRpb246ICIiLAogICAgICAgICAgcGF0aDogIiIsCiAgICAgICAgICBpY29uOiAiIgogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICLnvJbovpHmnYPpmZAiOwogICAgICAgIGNvbnN0IHBlcm1pc3Npb24gPSB0aGlzLmZpbmRQZXJtaXNzaW9uQnlJZChpZCk7CiAgICAgICAgaWYgKHBlcm1pc3Npb24pIHsKICAgICAgICAgIHRoaXMucnVsZUZvcm0gPSB7IC4uLnBlcm1pc3Npb24gfTsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAoKICAgIC8vIOa3u+WKoOWtkOadg+mZkAogICAgYWRkQ2hpbGQocGFyZW50RGF0YSkgewogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIuaWsOWinuWtkOadg+mZkCI7CiAgICAgIHRoaXMucnVsZUZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgbGFiZWw6ICIiLAogICAgICAgIGNvZGU6ICIiLAogICAgICAgIHR5cGU6ICJhY3Rpb24iLAogICAgICAgIHBhcmVudF9pZDogW3BhcmVudERhdGEuaWRdLAogICAgICAgIHNvcnQ6IDAsCiAgICAgICAgc3RhdHVzOiAxLAogICAgICAgIGRlc2NyaXB0aW9uOiAiIiwKICAgICAgICBwYXRoOiAiIiwKICAgICAgICBpY29uOiAiIgogICAgICB9OwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCgogICAgLy8g5qC55o2uSUTmn6Xmib7mnYPpmZDvvIjlnKjljp/lp4vmlbDmja7kuK3mn6Xmib7vvIkKICAgIGZpbmRQZXJtaXNzaW9uQnlJZChpZCwgdHJlZSA9IHRoaXMub3JpZ2luYWxEYXRhKSB7CiAgICAgIGZvciAobGV0IG5vZGUgb2YgdHJlZSkgewogICAgICAgIGlmIChub2RlLmlkID09PSBpZCkgewogICAgICAgICAgcmV0dXJuIG5vZGU7CiAgICAgICAgfQogICAgICAgIGlmIChub2RlLmNoaWxkcmVuKSB7CiAgICAgICAgICBjb25zdCBmb3VuZCA9IHRoaXMuZmluZFBlcm1pc3Npb25CeUlkKGlkLCBub2RlLmNoaWxkcmVuKTsKICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kOwogICAgICAgIH0KICAgICAgfQogICAgICByZXR1cm4gbnVsbDsKICAgIH0sCgogICAgLy8g5Yig6Zmk5p2D6ZmQCiAgICBkZWxEYXRhKGlkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuehruWumuimgeWIoOmZpOi/meS4quadg+mZkOWQl++8n+WIoOmZpOWQjuS4jeWPr+aBouWkje+8gSIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgLy8g5qih5ouf5Yig6ZmkCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLliKDpmaTmiJDlip/vvIEiKTsKICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi5bey5Y+W5raI5Yig6ZmkIik7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDkv53lrZjmnYPpmZAKICAgIHNhdmVEYXRhKCkgewogICAgICB0aGlzLiRyZWZzWyJydWxlRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgLy8g5qih5ouf5L+d5a2YCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3ModGhpcy5ydWxlRm9ybS5pZCA/ICLmm7TmlrDmiJDlip/vvIEiIDogIuaWsOWinuaIkOWKn++8gSIpOwogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy8g5pS55Y+Y54q25oCBCiAgICBjaGFuZ2VTdGF0dXMocm93KSB7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5p2D6ZmQ54q25oCB5beyJHtyb3cuc3RhdHVzID09PSAxID8gJ+WQr+eUqCcgOiAn56aB55SoJ31gKTsKICAgIH0sCgogICAgLy8g6I635Y+W6IqC54K55Zu+5qCHCiAgICBnZXROb2RlSWNvbih0eXBlKSB7CiAgICAgIGNvbnN0IGljb25NYXAgPSB7CiAgICAgICAgbWVudTogJ2VsLWljb24tbWVudScsCiAgICAgICAgYWN0aW9uOiAnZWwtaWNvbi1zZXR0aW5nJywKICAgICAgICBkYXRhOiAnZWwtaWNvbi1kb2N1bWVudCcKICAgICAgfTsKICAgICAgcmV0dXJuIGljb25NYXBbdHlwZV0gfHwgJ2VsLWljb24tbWVudSc7CiAgICB9LAoKICAgIC8vIOiOt+WPluexu+Wei+minOiJsgogICAgZ2V0VHlwZUNvbG9yKHR5cGUpIHsKICAgICAgY29uc3QgY29sb3JNYXAgPSB7CiAgICAgICAgbWVudTogJ3ByaW1hcnknLAogICAgICAgIGFjdGlvbjogJ3N1Y2Nlc3MnLAogICAgICAgIGRhdGE6ICd3YXJuaW5nJwogICAgICB9OwogICAgICByZXR1cm4gY29sb3JNYXBbdHlwZV0gfHwgJ3ByaW1hcnknOwogICAgfSwKCiAgICAvLyDojrflj5bnsbvlnovmoIfnrb4KICAgIGdldFR5cGVMYWJlbCh0eXBlKSB7CiAgICAgIGNvbnN0IGxhYmVsTWFwID0gewogICAgICAgIG1lbnU6ICfoj5zljZXmnYPpmZAnLAogICAgICAgIGFjdGlvbjogJ+aTjeS9nOadg+mZkCcsCiAgICAgICAgZGF0YTogJ+aVsOaNruadg+mZkCcKICAgICAgfTsKICAgICAgcmV0dXJuIGxhYmVsTWFwW3R5cGVdIHx8ICfoj5zljZXmnYPpmZAnOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["quanxian.vue"], "names": [], "mappings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file": "quanxian.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\n  <div class=\"permission-container\">\n    <!-- 页面标题区域 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-key\"></i>\n            权限管理\n          </h2>\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\n        </div>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"editData(0)\"\n            class=\"add-btn\"\n          >\n            新增权限\n          </el-button>\n          <el-button\n            icon=\"el-icon-refresh\"\n            @click=\"refulsh\"\n            class=\"refresh-btn\"\n          >\n            刷新\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <el-card shadow=\"never\" class=\"search-card\">\n        <div class=\"search-form\">\n          <div class=\"search-row\">\n            <div class=\"search-item\">\n              <label class=\"search-label\">权限搜索</label>\n              <el-input\n                v-model=\"search.keyword\"\n                placeholder=\"请输入权限名称或描述\"\n                class=\"search-input\"\n                clearable\n                @keyup.enter.native=\"searchData\"\n              >\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\n              </el-input>\n            </div>\n            \n            <div class=\"search-item\">\n              <label class=\"search-label\">权限类型</label>\n              <el-select\n                v-model=\"search.type\"\n                placeholder=\"请选择权限类型\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </div>\n\n            <div class=\"search-item\">\n              <label class=\"search-label\">状态</label>\n              <el-select\n                v-model=\"search.status\"\n                placeholder=\"请选择状态\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"启用\" :value=\"1\"></el-option>\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"search-actions\">\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\n              搜索\n            </el-button>\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\n              重置\n            </el-button>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 权限树形结构 -->\n    <div class=\"tree-section\">\n      <el-card shadow=\"never\" class=\"tree-card\">\n        <div class=\"tree-header\">\n          <div class=\"tree-title\">\n            <i class=\"el-icon-menu\"></i>\n            权限树形结构\n          </div>\n          <div class=\"tree-tools\">\n            <el-button-group>\n              <el-button \n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \n                icon=\"el-icon-s-grid\"\n                @click=\"viewMode = 'tree'\"\n                size=\"small\"\n              >\n                树形视图\n              </el-button>\n              <el-button \n                :type=\"viewMode === 'table' ? 'primary' : ''\" \n                icon=\"el-icon-menu\"\n                @click=\"viewMode = 'table'\"\n                size=\"small\"\n              >\n                列表视图\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 树形视图 -->\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\n          <el-tree\n            :data=\"treeData\"\n            :props=\"treeProps\"\n            :default-expand-all=\"true\"\n            node-key=\"id\"\n            class=\"permission-tree\"\n          >\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\n              <div class=\"node-content\">\n                <div class=\"node-info\">\n                  <i :class=\"getNodeIcon(data.type)\"></i>\n                  <span class=\"node-label\">{{ data.label }}</span>\n                  <el-tag \n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \n                    size=\"mini\"\n                    class=\"node-status\"\n                  >\n                    {{ data.status === 1 ? '启用' : '禁用' }}\n                  </el-tag>\n                </div>\n                <div class=\"node-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click=\"editData(data.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click=\"addChild(data)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click=\"delData(data.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </span>\n          </el-tree>\n        </div>\n\n        <!-- 表格视图 -->\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\n          <el-table\n            :data=\"tableData\"\n            v-loading=\"loading\"\n            class=\"permission-table\"\n            stripe\n            row-key=\"id\"\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n            :default-expand-all=\"false\"\n          >\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\n              <template slot-scope=\"scope\">\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\n            </el-table-column>\n\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-tag\n                  :type=\"getTypeColor(scope.row.type)\"\n                  size=\"small\"\n                >\n                  {{ getTypeLabel(scope.row.type) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-switch\n                  v-model=\"scope.row.status\"\n                  :active-value=\"1\"\n                  :inactive-value=\"0\"\n                  @change=\"changeStatus(scope.row)\"\n                >\n                </el-switch>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <div class=\"action-buttons\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click.stop=\"editData(scope.row.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click.stop=\"addChild(scope.row)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                    v-if=\"scope.row.type === 'menu'\"\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click.stop=\"delData(scope.row.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 编辑权限对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogFormVisible\"\n      :close-on-click-modal=\"false\"\n      width=\"60%\"\n    >\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限名称\" prop=\"label\">\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限代码\" prop=\"code\">\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限类型\" prop=\"type\">\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"父级权限\">\n              <el-cascader\n                v-model=\"ruleForm.parent_id\"\n                :options=\"parentOptions\"\n                :props=\"cascaderProps\"\n                placeholder=\"请选择父级权限\"\n                clearable\n                style=\"width: 100%\"\n              ></el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"排序\">\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-switch\n                v-model=\"ruleForm.status\"\n                :active-value=\"1\"\n                :inactive-value=\"0\"\n                active-text=\"启用\"\n                inactive-text=\"禁用\"\n              >\n              </el-switch>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"权限描述\">\n          <el-input\n            v-model=\"ruleForm.description\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入权限描述\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\n            <template slot=\"prepend\">\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"PermissionManagement\",\n  data() {\n    return {\n      viewMode: 'tree', // tree | table\n      loading: false,\n      search: {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      },\n      treeData: [],\n      originalData: [], // 保存原始数据\n      dialogFormVisible: false,\n      dialogTitle: \"新增权限\",\n      parentOptions: [],\n      ruleForm: {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"menu\",\n        parent_id: null,\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      },\n      rules: {\n        label: [\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\n        ],\n        code: [\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\n        ]\n      },\n      treeProps: {\n        children: 'children',\n        label: 'label'\n      },\n      cascaderProps: {\n        value: 'id',\n        label: 'label',\n        children: 'children',\n        checkStrictly: true\n      }\n    };\n  },\n  computed: {\n    // 表格数据 - 将树形数据扁平化但保持层级关系\n    tableData() {\n      return this.flattenTreeForTable(this.treeData);\n    }\n  },\n  mounted() {\n    this.getData();\n  },\n  methods: {\n    // 获取权限数据\n    getData() {\n      this.loading = true;\n      \n      // 使用测试数据\n      setTimeout(() => {\n        this.loading = false;\n        \n        const mockData = [\n          {\n            id: 1,\n            label: \"系统管理\",\n            code: \"system\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 1,\n            status: 1,\n            description: \"系统管理模块\",\n            path: \"/system\",\n            icon: \"el-icon-setting\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 11,\n                label: \"用户管理\",\n                code: \"system:user\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 1,\n                status: 1,\n                description: \"用户管理功能\",\n                path: \"/user\",\n                icon: \"el-icon-user\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 111,\n                    label: \"查看用户\",\n                    code: \"system:user:view\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看用户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 112,\n                    label: \"新增用户\",\n                    code: \"system:user:add\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 113,\n                    label: \"编辑用户\",\n                    code: \"system:user:edit\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑用户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 114,\n                    label: \"删除用户\",\n                    code: \"system:user:delete\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 12,\n                label: \"职位管理\",\n                code: \"system:position\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 2,\n                status: 1,\n                description: \"职位管理功能\",\n                path: \"/zhiwei\",\n                icon: \"el-icon-postcard\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 121,\n                    label: \"查看职位\",\n                    code: \"system:position:view\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看职位列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 122,\n                    label: \"新增职位\",\n                    code: \"system:position:add\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 123,\n                    label: \"编辑职位\",\n                    code: \"system:position:edit\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑职位信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 124,\n                    label: \"删除职位\",\n                    code: \"system:position:delete\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 13,\n                label: \"权限管理\",\n                code: \"system:permission\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 3,\n                status: 1,\n                description: \"权限管理功能\",\n                path: \"/quanxian\",\n                icon: \"el-icon-key\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 131,\n                    label: \"查看权限\",\n                    code: \"system:permission:view\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看权限列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 132,\n                    label: \"新增权限\",\n                    code: \"system:permission:add\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 133,\n                    label: \"编辑权限\",\n                    code: \"system:permission:edit\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑权限信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 134,\n                    label: \"删除权限\",\n                    code: \"system:permission:delete\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 2,\n            label: \"业务管理\",\n            code: \"business\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 2,\n            status: 1,\n            description: \"业务管理模块\",\n            path: \"/business\",\n            icon: \"el-icon-suitcase\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 21,\n                label: \"订单管理\",\n                code: \"business:order\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 1,\n                status: 1,\n                description: \"订单管理功能\",\n                path: \"/dingdan\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 211,\n                    label: \"查看订单\",\n                    code: \"business:order:view\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看订单列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 212,\n                    label: \"新增订单\",\n                    code: \"business:order:add\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 213,\n                    label: \"编辑订单\",\n                    code: \"business:order:edit\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑订单信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 214,\n                    label: \"删除订单\",\n                    code: \"business:order:delete\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 215,\n                    label: \"导出订单\",\n                    code: \"business:order:export\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 5,\n                    status: 1,\n                    description: \"导出订单数据\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 22,\n                label: \"客户管理\",\n                code: \"business:customer\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 2,\n                status: 1,\n                description: \"客户管理功能\",\n                path: \"/customer\",\n                icon: \"el-icon-user-solid\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 221,\n                    label: \"查看客户\",\n                    code: \"business:customer:view\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看客户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 222,\n                    label: \"新增客户\",\n                    code: \"business:customer:add\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 223,\n                    label: \"编辑客户\",\n                    code: \"business:customer:edit\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑客户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 224,\n                    label: \"删除客户\",\n                    code: \"business:customer:delete\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 3,\n            label: \"文书管理\",\n            code: \"document\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 3,\n            status: 1,\n            description: \"文书管理模块\",\n            path: \"/document\",\n            icon: \"el-icon-document-copy\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 31,\n                label: \"合同管理\",\n                code: \"document:contract\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 1,\n                status: 1,\n                description: \"合同管理功能\",\n                path: \"/hetong\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 311,\n                    label: \"查看合同\",\n                    code: \"document:contract:view\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看合同列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 312,\n                    label: \"新增合同\",\n                    code: \"document:contract:add\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 313,\n                    label: \"编辑合同\",\n                    code: \"document:contract:edit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑合同信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 314,\n                    label: \"删除合同\",\n                    code: \"document:contract:delete\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 315,\n                    label: \"审核合同\",\n                    code: \"document:contract:audit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 5,\n                    status: 1,\n                    description: \"审核合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 32,\n                label: \"律师函管理\",\n                code: \"document:lawyer\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 2,\n                status: 1,\n                description: \"律师函管理功能\",\n                path: \"/lawyer\",\n                icon: \"el-icon-message\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 321,\n                    label: \"查看律师函\",\n                    code: \"document:lawyer:view\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看律师函列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 322,\n                    label: \"发送律师函\",\n                    code: \"document:lawyer:send\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 2,\n                    status: 1,\n                    description: \"发送律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 323,\n                    label: \"编辑律师函\",\n                    code: \"document:lawyer:edit\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 33,\n                label: \"课程管理\",\n                code: \"document:course\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 3,\n                status: 1,\n                description: \"课程管理功能\",\n                path: \"/kecheng\",\n                icon: \"el-icon-video-play\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 331,\n                    label: \"查看课程\",\n                    code: \"document:course:view\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看课程列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 332,\n                    label: \"新增课程\",\n                    code: \"document:course:add\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 333,\n                    label: \"编辑课程\",\n                    code: \"document:course:edit\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 334,\n                    label: \"删除课程\",\n                    code: \"document:course:delete\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 4,\n            label: \"财务管理\",\n            code: \"finance\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 4,\n            status: 1,\n            description: \"财务管理模块\",\n            path: \"/finance\",\n            icon: \"el-icon-coin\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 41,\n                label: \"支付管理\",\n                code: \"finance:payment\",\n                type: \"menu\",\n                parent_id: 4,\n                sort: 1,\n                status: 1,\n                description: \"支付管理功能\",\n                path: \"/order\",\n                icon: \"el-icon-money\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 411,\n                    label: \"查看支付记录\",\n                    code: \"finance:payment:view\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看支付记录\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 412,\n                    label: \"处理退款\",\n                    code: \"finance:payment:refund\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 2,\n                    status: 1,\n                    description: \"处理退款申请\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 413,\n                    label: \"导出财务报表\",\n                    code: \"finance:payment:export\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 3,\n                    status: 1,\n                    description: \"导出财务报表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          }\n        ];\n        \n        this.treeData = mockData;\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\n        this.updateParentOptions();\n      }, 500);\n    },\n\n    // 将树形数据扁平化用于表格显示（保持层级结构）\n    flattenTreeForTable(tree, level = 0, result = []) {\n      tree.forEach(node => {\n        const flatNode = {\n          ...node,\n          level: level,\n          hasChildren: node.children && node.children.length > 0\n        };\n        // 移除children属性避免表格渲染问题\n        delete flatNode.children;\n        result.push(flatNode);\n\n        // 递归处理子节点\n        if (node.children && node.children.length > 0) {\n          this.flattenTreeForTable(node.children, level + 1, result);\n        }\n      });\n      return result;\n    },\n\n    // 更新父级权限选项\n    updateParentOptions() {\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\n    },\n\n    // 构建级联选择器选项\n    buildCascaderOptions(tree) {\n      return tree.map(node => ({\n        id: node.id,\n        label: node.label,\n        children: node.children ? this.buildCascaderOptions(node.children) : []\n      }));\n    },\n\n    // 搜索数据\n    searchData() {\n      this.getData();\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.search = {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      };\n      this.searchData();\n    },\n\n    // 刷新页面\n    refulsh() {\n      this.$router.go(0);\n    },\n\n    // 编辑权限\n    editData(id) {\n      if (id === 0) {\n        this.dialogTitle = \"新增权限\";\n        this.ruleForm = {\n          id: null,\n          label: \"\",\n          code: \"\",\n          type: \"menu\",\n          parent_id: null,\n          sort: 0,\n          status: 1,\n          description: \"\",\n          path: \"\",\n          icon: \"\"\n        };\n      } else {\n        this.dialogTitle = \"编辑权限\";\n        const permission = this.findPermissionById(id);\n        if (permission) {\n          this.ruleForm = { ...permission };\n        }\n      }\n      this.dialogFormVisible = true;\n    },\n\n    // 添加子权限\n    addChild(parentData) {\n      this.dialogTitle = \"新增子权限\";\n      this.ruleForm = {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"action\",\n        parent_id: [parentData.id],\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      };\n      this.dialogFormVisible = true;\n    },\n\n    // 根据ID查找权限（在原始数据中查找）\n    findPermissionById(id, tree = this.originalData) {\n      for (let node of tree) {\n        if (node.id === id) {\n          return node;\n        }\n        if (node.children) {\n          const found = this.findPermissionById(id, node.children);\n          if (found) return found;\n        }\n      }\n      return null;\n    },\n\n    // 删除权限\n    delData(id) {\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        // 模拟删除\n        this.$message.success(\"删除成功！\");\n        this.getData();\n      }).catch(() => {\n        this.$message.info(\"已取消删除\");\n      });\n    },\n\n    // 保存权限\n    saveData() {\n      this.$refs[\"ruleForm\"].validate((valid) => {\n        if (valid) {\n          // 模拟保存\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\n          this.dialogFormVisible = false;\n          this.getData();\n        }\n      });\n    },\n\n    // 改变状态\n    changeStatus(row) {\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\n    },\n\n    // 获取节点图标\n    getNodeIcon(type) {\n      const iconMap = {\n        menu: 'el-icon-menu',\n        action: 'el-icon-setting',\n        data: 'el-icon-document'\n      };\n      return iconMap[type] || 'el-icon-menu';\n    },\n\n    // 获取类型颜色\n    getTypeColor(type) {\n      const colorMap = {\n        menu: 'primary',\n        action: 'success',\n        data: 'warning'\n      };\n      return colorMap[type] || 'primary';\n    },\n\n    // 获取类型标签\n    getTypeLabel(type) {\n      const labelMap = {\n        menu: '菜单权限',\n        action: '操作权限',\n        data: '数据权限'\n      };\n      return labelMap[type] || '菜单权限';\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 权限管理容器 */\n.permission-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding: 24px;\n}\n\n/* 页面标题区域 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 24px 32px;\n  border-radius: 12px;\n  color: white;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.title-section h2.page-title {\n  margin: 0;\n  font-size: 28px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.title-section .page-subtitle {\n  margin: 8px 0 0 0;\n  opacity: 0.9;\n  font-size: 14px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.add-btn, .refresh-btn {\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.add-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.add-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.refresh-btn {\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 24px;\n}\n\n.search-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form {\n  padding: 8px;\n}\n\n.search-row {\n  display: flex;\n  gap: 24px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n}\n\n.search-item {\n  display: flex;\n  flex-direction: column;\n  min-width: 200px;\n}\n\n.search-label {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 8px;\n  font-weight: 500;\n}\n\n.search-input, .search-select {\n  width: 240px;\n}\n\n.search-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n/* 树形结构区域 */\n.tree-section {\n  margin-bottom: 24px;\n}\n\n.tree-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.tree-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.tree-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #262626;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.tree-tools {\n  display: flex;\n  gap: 12px;\n}\n\n/* 树形视图样式 */\n.tree-view {\n  padding: 24px;\n}\n\n.permission-tree {\n  background: transparent;\n}\n\n.tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.node-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.node-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.node-label {\n  font-weight: 500;\n  color: #262626;\n}\n\n.node-status {\n  margin-left: 8px;\n}\n\n.node-actions {\n  display: flex;\n  gap: 4px;\n}\n\n/* 表格视图样式 */\n.table-view {\n  padding: 0 24px 24px;\n}\n\n.permission-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.permission-name-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.permission-name {\n  font-weight: 500;\n  color: #262626;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.action-buttons .el-button {\n  margin: 2px;\n}\n\n/* 表格行层级样式 */\n.permission-table .el-table__row[data-level=\"0\"] {\n  background-color: #fafafa;\n  font-weight: 600;\n}\n\n.permission-table .el-table__row[data-level=\"1\"] {\n  background-color: #f5f5f5;\n}\n\n.permission-table .el-table__row[data-level=\"2\"] {\n  background-color: #ffffff;\n}\n\n/* 对话框样式 */\n.dialog-footer {\n  text-align: right;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .permission-container {\n    padding: 16px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .search-row {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .search-item {\n    min-width: auto;\n  }\n\n  .search-input, .search-select {\n    width: 100%;\n  }\n\n  .tree-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n\n  .node-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .node-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n}\n</style>\n"]}]}