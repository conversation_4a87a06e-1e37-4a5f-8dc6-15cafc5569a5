{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue", "mtime": 1748617950297}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "allSize", "tableData", "loading", "total", "page", "size", "viewMode", "saveLoading", "search", "keyword", "minPrice", "maxPrice", "ruleForm", "title", "price", "year", "desc", "sort", "good", "num", "rules", "required", "message", "trigger", "dialogFormVisible", "form<PERSON>abe<PERSON><PERSON>", "url", "types", "computed", "averagePrice", "Array", "isArray", "length", "reduce", "sum", "item", "parseFloat", "Math", "round", "toLocaleString", "premiumPackages", "filter", "averageYear", "parseInt", "dialogTitle", "id", "filteredPackages", "filtered", "toLowerCase", "includes", "mounted", "getData", "methods", "editData", "_this", "getInfo", "getTypes", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "postRequest", "count", "handleSizeChange", "val", "handleCurrentChange", "formatDate", "dateStr", "Date", "toLocaleDateString", "resetSearch", "saveData", "$refs", "validate", "valid", "checked", "map", "setTimeout", "success"], "sources": ["src/views/pages/taocan/taocan.vue"], "sourcesContent": ["<template>\r\n  <div class=\"package-management-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-box\"></i>\r\n          套餐类型管理\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务套餐产品和价格配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"getData\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-box\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">套餐总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon price-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ averagePrice }}</div>\r\n              <div class=\"stat-label\">平均价格</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon premium-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ premiumPackages }}</div>\r\n              <div class=\"stat-label\">高端套餐</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon duration-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ averageYear }}年</div>\r\n              <div class=\"stat-label\">平均年限</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 稳定\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新增套餐\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入套餐名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"价格范围\">\r\n            <el-input-number \r\n              v-model=\"search.minPrice\" \r\n              placeholder=\"最低价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n            <span style=\"margin: 0 8px\">-</span>\r\n            <el-input-number \r\n              v-model=\"search.maxPrice\" \r\n              placeholder=\"最高价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 套餐展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"package-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          套餐列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"package-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"pkg in filteredPackages\" \r\n          :key=\"pkg.id\"\r\n          class=\"package-item\"\r\n        >\r\n          <div class=\"package-header\">\r\n            <div class=\"package-title\">{{ pkg.title }}</div>\r\n            <div class=\"package-price\">¥{{ pkg.price }}</div>\r\n          </div>\r\n          \r\n          <div class=\"package-content\">\r\n            <div class=\"package-info\">\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ pkg.year }}年服务</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-sort\"></i>\r\n                <span>排序: {{ pkg.sort }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"package-desc\">\r\n              {{ pkg.desc || '暂无描述' }}\r\n            </div>\r\n            \r\n            <div class=\"package-features\" v-if=\"pkg.services && pkg.services.length > 0\">\r\n              <div class=\"feature-title\">包含服务:</div>\r\n              <div class=\"feature-list\">\r\n                <el-tag \r\n                  v-for=\"service in pkg.services.slice(0, 3)\" \r\n                  :key=\"service.id\"\r\n                  size=\"mini\"\r\n                  class=\"feature-tag\"\r\n                >\r\n                  {{ service.name }}\r\n                </el-tag>\r\n                <span v-if=\"pkg.services.length > 3\" class=\"more-services\">\r\n                  +{{ pkg.services.length - 3 }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"package-footer\">\r\n            <div class=\"package-meta\">\r\n              <span class=\"create-time\">{{ formatDate(pkg.create_time) }}</span>\r\n            </div>\r\n            <div class=\"package-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(pkg.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, pkg.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n          :data=\"filteredPackages\" \r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"套餐信息\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-package-info\">\r\n                <div class=\"table-package-title\">{{ scope.row.title }}</div>\r\n                <div class=\"table-package-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"价格\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"price-display\">¥{{ scope.row.price }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"year\" label=\"年限\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"info\" size=\"small\">{{ scope.row.year }}年</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" />\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐名称\" prop=\"title\">\r\n              <el-input \r\n                v-model=\"ruleForm.title\" \r\n                placeholder=\"请输入套餐名称\"\r\n                autocomplete=\"off\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐价格\" prop=\"price\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.price\"\r\n                :min=\"0\"\r\n                :max=\"999999\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入价格\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务年限\" prop=\"year\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.year\"\r\n                :min=\"1\"\r\n                :max=\"10\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入年限\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number\r\n                v-model=\"ruleForm.sort\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"数字越小排序越靠前\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-form-item label=\"套餐内容\" prop=\"good\">\r\n          <div class=\"service-selection\">\r\n            <div class=\"service-title\">选择包含的服务类型:</div>\r\n            <div class=\"service-list\">\r\n              <div \r\n              v-for=\"(item, index) in types\"\r\n              :key=\"index\"\r\n                class=\"service-item\"\r\n            >\r\n                <div class=\"service-checkbox\">\r\n                  <el-checkbox v-model=\"item.checked\" :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n                </div>\r\n                <div class=\"service-input\" v-if=\"item.is_num == 1 && item.checked\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                    size=\"small\"\r\n                    placeholder=\"次数\"\r\n                  />\r\n                  <span class=\"input-suffix\">次</span>\r\n                </div>\r\n                <div class=\"service-unlimited\" v-else-if=\"item.checked\">\r\n                  <el-tag size=\"small\" type=\"success\">不限次数</el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"套餐描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入套餐详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"PackageManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写套餐名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"120px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    averagePrice() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return '0';\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\r\n      return Math.round(total / this.tableData.length).toLocaleString();\r\n    },\r\n    premiumPackages() {\r\n      return Array.isArray(this.tableData) ? this.tableData.filter(item => parseFloat(item.price) > 10000).length : 0;\r\n    },\r\n    averageYear() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return 1;\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseInt(item.year) || 1), 0);\r\n      return Math.round(total / this.tableData.length);\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑套餐' : '新增套餐';\r\n    },\r\n    filteredPackages() {\r\n      if (!Array.isArray(this.tableData)) return [];\r\n      let filtered = this.tableData;\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filtered = filtered.filter(item =>\r\n          (item.title && item.title.toLowerCase().includes(keyword)) ||\r\n          (item.desc && item.desc.toLowerCase().includes(keyword))\r\n        );\r\n      }\r\n\r\n      // 价格范围筛选\r\n      if (this.search.minPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) >= this.search.minPrice);\r\n      }\r\n\r\n      if (this.search.maxPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) <= this.search.maxPrice);\r\n      }\r\n\r\n      return filtered;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n          // 处理服务类型选择\r\n          this.ruleForm.good = this.types\r\n            .filter(type => type.checked)\r\n            .map(type => type.id);\r\n          \r\n          // 模拟保存操作\r\n          setTimeout(() => {\r\n            this.saveLoading = false;\r\n            this.dialogFormVisible = false;\r\n            this.$message.success('保存成功');\r\n            this.getData();\r\n          }, 1000);\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.package-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.price-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.premium-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.duration-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .package-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 套餐网格视图 */\r\n.package-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.package-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.package-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.package-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.package-price {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #ffd04b;\r\n}\r\n\r\n.package-content {\r\n  padding: 20px;\r\n}\r\n\r\n.package-info {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.package-desc {\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  min-height: 40px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.package-features {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.feature-title {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.feature-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  align-items: center;\r\n}\r\n\r\n.feature-tag {\r\n  background: #f0f9ff;\r\n  color: #0369a1;\r\n  border: 1px solid #e0f2fe;\r\n}\r\n\r\n.more-services {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  font-style: italic;\r\n}\r\n\r\n.package-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.package-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.package-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-package-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-package-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.price-display {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 服务选择区域 */\r\n.service-selection {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background: #fafafa;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-item:hover {\r\n  border-color: #409EFF;\r\n  background: #f8f9ff;\r\n}\r\n\r\n.service-checkbox {\r\n  flex: 1;\r\n}\r\n\r\n.service-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.input-suffix {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.service-unlimited {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .package-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n  width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .package-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .package-header {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-input {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA+ZA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,OAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,GAAA;MACA;MACAA,GAAA;MACAC,KAAA;QACAP,KAAA,GACA;UACAQ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAT,KAAA,GACA;UACAO,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAR,IAAA,GACA;UACAM,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,iBAAA;MACAC,cAAA;MACAC,GAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,aAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,MAAA9B,SAAA,UAAAA,SAAA,CAAA+B,MAAA;MACA,MAAA7B,KAAA,QAAAF,SAAA,CAAAgC,MAAA,EAAAC,GAAA,EAAAC,IAAA,KAAAD,GAAA,IAAAE,UAAA,CAAAD,IAAA,CAAArB,KAAA;MACA,OAAAuB,IAAA,CAAAC,KAAA,CAAAnC,KAAA,QAAAF,SAAA,CAAA+B,MAAA,EAAAO,cAAA;IACA;IACAC,gBAAA;MACA,OAAAV,KAAA,CAAAC,OAAA,MAAA9B,SAAA,SAAAA,SAAA,CAAAwC,MAAA,CAAAN,IAAA,IAAAC,UAAA,CAAAD,IAAA,CAAArB,KAAA,WAAAkB,MAAA;IACA;IACAU,YAAA;MACA,KAAAZ,KAAA,CAAAC,OAAA,MAAA9B,SAAA,UAAAA,SAAA,CAAA+B,MAAA;MACA,MAAA7B,KAAA,QAAAF,SAAA,CAAAgC,MAAA,EAAAC,GAAA,EAAAC,IAAA,KAAAD,GAAA,IAAAS,QAAA,CAAAR,IAAA,CAAApB,IAAA;MACA,OAAAsB,IAAA,CAAAC,KAAA,CAAAnC,KAAA,QAAAF,SAAA,CAAA+B,MAAA;IACA;IACAY,YAAA;MACA,YAAAhC,QAAA,CAAAiC,EAAA;IACA;IACAC,iBAAA;MACA,KAAAhB,KAAA,CAAAC,OAAA,MAAA9B,SAAA;MACA,IAAA8C,QAAA,QAAA9C,SAAA;;MAEA;MACA,SAAAO,MAAA,CAAAC,OAAA;QACA,MAAAA,OAAA,QAAAD,MAAA,CAAAC,OAAA,CAAAuC,WAAA;QACAD,QAAA,GAAAA,QAAA,CAAAN,MAAA,CAAAN,IAAA,IACAA,IAAA,CAAAtB,KAAA,IAAAsB,IAAA,CAAAtB,KAAA,CAAAmC,WAAA,GAAAC,QAAA,CAAAxC,OAAA,KACA0B,IAAA,CAAAnB,IAAA,IAAAmB,IAAA,CAAAnB,IAAA,CAAAgC,WAAA,GAAAC,QAAA,CAAAxC,OAAA,CACA;MACA;;MAEA;MACA,SAAAD,MAAA,CAAAE,QAAA;QACAqC,QAAA,GAAAA,QAAA,CAAAN,MAAA,CAAAN,IAAA,IAAAC,UAAA,CAAAD,IAAA,CAAArB,KAAA,UAAAN,MAAA,CAAAE,QAAA;MACA;MAEA,SAAAF,MAAA,CAAAG,QAAA;QACAoC,QAAA,GAAAA,QAAA,CAAAN,MAAA,CAAAN,IAAA,IAAAC,UAAA,CAAAD,IAAA,CAAArB,KAAA,UAAAN,MAAA,CAAAG,QAAA;MACA;MAEA,OAAAoC,QAAA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAR,EAAA;MACA,IAAAS,KAAA;MACA,IAAAT,EAAA;QACA,KAAAU,OAAA,CAAAV,EAAA;MACA;QACA,KAAAjC,QAAA;UACAC,KAAA;UACAC,KAAA;UACAC,IAAA;UACAC,IAAA;UACAC,IAAA;UACAC,IAAA;UACAC,GAAA;QACA;QACAmC,KAAA,CAAAE,QAAA;MACA;MACAF,KAAA,CAAA9B,iBAAA;IACA;IACA+B,QAAAV,EAAA;MACA,IAAAS,KAAA;MACAA,KAAA,CAAAG,UAAA,CAAAH,KAAA,CAAA5B,GAAA,gBAAAmB,EAAA,EAAAa,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAL,KAAA,CAAA1C,QAAA,GAAA+C,IAAA,CAAA5D,IAAA;UACAuD,KAAA,CAAA3B,KAAA,GAAA2B,KAAA,CAAA1C,QAAA,CAAAO,GAAA;QACA;MACA;IACA;IACAyC,QAAAC,KAAA,EAAAhB,EAAA;MACA,KAAAiB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAxC,GAAA,kBAAAmB,EAAA,EAAAa,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACA3C,OAAA;YACA;YACA,KAAArB,SAAA,CAAAoE,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACA3C,OAAA;QACA;MACA;IACA;IACAkC,SAAA;MACA,KAAAe,WAAA,sBAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACA,KAAAxC,KAAA,GAAAgC,IAAA,CAAA5D,IAAA;QACA;MACA;IACA;IACAoD,QAAA;MACA,IAAAG,KAAA;MACAA,KAAA,CAAApD,OAAA;MACAoD,KAAA,CACAiB,WAAA,CACAjB,KAAA,CAAA5B,GAAA,mBAAA4B,KAAA,CAAAlD,IAAA,cAAAkD,KAAA,CAAAjD,IAAA,EACAiD,KAAA,CAAA9C,MACA,EACAkD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAArD,SAAA,GAAA0D,IAAA,CAAA5D,IAAA;UACAuD,KAAA,CAAAnD,KAAA,GAAAwD,IAAA,CAAAa,KAAA;QACA;QACAlB,KAAA,CAAApD,OAAA;MACA;IACA;IACAuE,iBAAAC,GAAA;MACA,KAAArE,IAAA,GAAAqE,GAAA;MACA,KAAAvB,OAAA;IACA;IACAwB,oBAAAD,GAAA;MACA,KAAAtE,IAAA,GAAAsE,GAAA;MACA,KAAAvB,OAAA;IACA;IACAyB,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,WAAAC,IAAA,CAAAD,OAAA,EAAAE,kBAAA;IACA;IACAC,YAAA;MACA,KAAAxE,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACA,KAAAP,IAAA;MACA,KAAA+C,OAAA;IACA;IACA8B,SAAA;MACA,KAAAC,KAAA,CAAAtE,QAAA,CAAAuE,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAA7E,WAAA;UACA;UACA,KAAAK,QAAA,CAAAM,IAAA,QAAAS,KAAA,CACAc,MAAA,CAAAwB,IAAA,IAAAA,IAAA,CAAAoB,OAAA,EACAC,GAAA,CAAArB,IAAA,IAAAA,IAAA,CAAApB,EAAA;;UAEA;UACA0C,UAAA;YACA,KAAAhF,WAAA;YACA,KAAAiB,iBAAA;YACA,KAAA4C,QAAA,CAAAoB,OAAA;YACA,KAAArC,OAAA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}