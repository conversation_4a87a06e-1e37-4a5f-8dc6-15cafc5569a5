{"name": "@nodelib/fs.stat", "version": "1.1.3", "description": "Get the status of a file with some features", "license": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat", "keywords": ["NodeLib", "fs", "FileSystem", "file system", "stat"], "engines": {"node": ">= 6"}, "main": "out/index.js", "typings": "out/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> out", "lint": "tslint \"src/**/*.ts\" -p . -t stylish", "compile": "tsc -b .", "compile:watch": "tsc -p . --watch --sourceMap", "test": "mocha \"out/**/*.spec.js\" -s 0", "build": "npm run clean && npm run lint && npm run compile && npm test", "watch": "npm run clean && npm run compile:watch"}}