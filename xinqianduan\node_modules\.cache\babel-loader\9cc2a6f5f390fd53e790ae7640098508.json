{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\File.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\File.vue", "mtime": 1748542462348}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "formatFileSize", "getFileType", "getRequest", "postRequest", "deleteRequest", "name", "data", "fileList", "selectedFiles", "uploadDialogVisible", "previewDialogVisible", "uploadFileList", "previewUrl", "currentFile", "uploadUrl", "fileTypeMap", "computed", "isImage", "image", "includes", "fileType", "isPdf", "pdf", "isOffice", "office", "created", "fetchFileList", "methods", "mockFiles", "id", "fileName", "category", "size", "uploadTime", "$message", "success", "error", "handleUpload", "beforeUpload", "file", "isLt500M", "handleProgress", "event", "console", "log", "percentage", "handleUploadSuccess", "response", "handleUploadError", "handlePreview", "generatePreviewUrl", "_response$data", "handleDownload", "link", "document", "createElement", "href", "btoa", "download", "click", "handleDelete", "$confirm", "type", "handleSelectionChange", "selection", "handleBatchArchive", "length", "fileIds", "map", "handleBatchDownload", "for<PERSON>ach", "handleBatchDelete", "getFileIcon", "iconMap"], "sources": ["src/views/pages/archive/File.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-container\">\r\n    <!-- 顶部操作栏 -->\r\n    <div class=\"operation-bar\">\r\n      <el-button-group>\r\n        <el-button type=\"primary\" @click=\"handleUpload\">\r\n          <i class=\"el-icon-upload\"></i> 上传文件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleBatchArchive\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-folder-add\"></i> 批量归档\r\n        </el-button>\r\n        <el-button type=\"warning\" @click=\"handleBatchDownload\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-download\"></i> 批量下载\r\n        </el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchDelete\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-delete\"></i> 批量删除\r\n        </el-button>\r\n      </el-button-group>\r\n    </div>\r\n\r\n    <!-- 文件列表区域 -->\r\n    <div class=\"file-list-container\">\r\n      <el-table\r\n        :data=\"fileList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\">\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"55\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileName\"\r\n          label=\"文件名\"\r\n          min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"file-name-cell\">\r\n              <i :class=\"getFileIcon(scope.row.fileType)\"></i>\r\n              <span>{{ scope.row.fileName }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileType\"\r\n          label=\"类型\"\r\n          width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"category\"\r\n          label=\"分类\"\r\n          width=\"120\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"size\"\r\n          label=\"大小\"\r\n          width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatFileSize(scope.row.size) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"uploadTime\"\r\n          label=\"上传时间\"\r\n          width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          width=\"240\"\r\n          align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handlePreview(scope.row)\">\r\n                预览\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleDownload(scope.row)\">\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\">\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 文件上传对话框 -->\r\n    <el-dialog\r\n      title=\"文件上传\"\r\n      :visible.sync=\"uploadDialogVisible\"\r\n      width=\"500px\">\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        multiple\r\n        :action=\"uploadUrl\"\r\n        :before-upload=\"beforeUpload\"\r\n        :on-progress=\"handleProgress\"\r\n        :on-success=\"handleUploadSuccess\"\r\n        :on-error=\"handleUploadError\"\r\n        :file-list=\"uploadFileList\">\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          支持任意格式文件，单个文件不超过500MB\r\n        </div>\r\n      </el-upload>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"80%\"\r\n      :fullscreen=\"true\">\r\n      <div class=\"preview-container\">\r\n        <!-- 图片预览 -->\r\n        <div v-if=\"isImage\" class=\"image-preview\">\r\n          <img :src=\"previewUrl\" alt=\"预览图片\">\r\n        </div>\r\n        <!-- PDF预览 -->\r\n        <div v-else-if=\"isPdf\" class=\"pdf-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- Office文档预览 -->\r\n        <div v-else-if=\"isOffice\" class=\"office-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- 其他文件类型 -->\r\n        <div v-else class=\"other-preview\">\r\n          <p>该文件类型暂不支持预览，请下载后查看</p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { formatFileSize, getFileType } from '@/utils/fileUtils'\r\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ArchiveFile',\r\n  data() {\r\n    return {\r\n      fileList: [],\r\n      selectedFiles: [],\r\n      uploadDialogVisible: false,\r\n      previewDialogVisible: false,\r\n      uploadFileList: [],\r\n      previewUrl: '',\r\n      currentFile: null,\r\n      uploadUrl: '/archive/upload',\r\n      // 文件类型映射\r\n      fileTypeMap: {\r\n        'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp'],\r\n        'pdf': ['pdf'],\r\n        'office': ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],\r\n        'text': ['txt', 'md'],\r\n        'archive': ['zip', 'rar', '7z']\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'userRole'\r\n    ]),\r\n    isImage() {\r\n      return this.currentFile && this.fileTypeMap.image.includes(this.currentFile.fileType)\r\n    },\r\n    isPdf() {\r\n      return this.currentFile && this.fileTypeMap.pdf.includes(this.currentFile.fileType)\r\n    },\r\n    isOffice() {\r\n      return this.currentFile && this.fileTypeMap.office.includes(this.currentFile.fileType)\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchFileList()\r\n  },\r\n  methods: {\r\n    // 获取文件列表\r\n    async fetchFileList() {\r\n      try {\r\n        // 模拟归档文件数据\r\n        const mockFiles = [\r\n          {\r\n            id: 1,\r\n            fileName: \"合同模板.pdf\",\r\n            fileType: \"pdf\",\r\n            category: \"合同文件\",\r\n            size: 1024000,\r\n            uploadTime: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            fileName: \"案件资料.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 512000,\r\n            uploadTime: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            fileName: \"咨询记录.txt\",\r\n            fileType: \"txt\",\r\n            category: \"咨询记录\",\r\n            size: 8192,\r\n            uploadTime: \"2024-01-13 16:45:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            fileName: \"证据材料.jpg\",\r\n            fileType: \"jpg\",\r\n            category: \"案件文书\",\r\n            size: 2048000,\r\n            uploadTime: \"2024-01-12 09:15:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            fileName: \"法律意见书.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 768000,\r\n            uploadTime: \"2024-01-11 16:30:00\"\r\n          }\r\n        ];\r\n        \r\n        this.fileList = mockFiles;\r\n        this.$message.success('文件列表加载成功');\r\n      } catch (error) {\r\n        this.$message.error('获取文件列表失败');\r\n      }\r\n    },\r\n    // 显示上传对话框\r\n    handleUpload() {\r\n      this.uploadDialogVisible = true\r\n    },\r\n    // 文件上传前检查\r\n    beforeUpload(file) {\r\n      const isLt500M = file.size / 1024 / 1024 < 500\r\n      if (!isLt500M) {\r\n        this.$message.error('文件大小不能超过 500MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    // 处理上传进度\r\n    handleProgress(event, file) {\r\n      console.log('上传进度：', file.percentage)\r\n    },\r\n    // 处理上传成功\r\n    handleUploadSuccess(response, file) {\r\n      this.$message.success('文件上传成功')\r\n      this.uploadDialogVisible = false\r\n      this.fetchFileList()\r\n    },\r\n    // 处理上传失败\r\n    handleUploadError() {\r\n      this.$message.error('文件上传失败')\r\n    },\r\n    // 处理文件预览\r\n    async handlePreview(file) {\r\n      this.currentFile = file\r\n      this.previewDialogVisible = true\r\n      // 根据文件类型生成预览URL\r\n      this.previewUrl = await this.generatePreviewUrl(file)\r\n    },\r\n    // 生成预览URL\r\n    async generatePreviewUrl(file) {\r\n      try {\r\n        const response = await getRequest(`/archive/preview/${file.id}`)\r\n        return response.data?.previewUrl || 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      } catch (error) {\r\n        return 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      }\r\n    },\r\n    // 处理文件下载\r\n    async handleDownload(file) {\r\n      try {\r\n        // 创建一个虚拟的下载链接\r\n        const link = document.createElement('a')\r\n        link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n        link.download = file.fileName\r\n        link.click()\r\n        this.$message.success('开始下载')\r\n      } catch (error) {\r\n        this.$message.error('文件下载失败')\r\n      }\r\n    },\r\n    // 处理文件删除\r\n    async handleDelete(file) {\r\n      try {\r\n        await this.$confirm('确认删除该文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        await deleteRequest(`/archive/files/${file.id}`)\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 处理批量操作\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n    },\r\n    // 批量归档\r\n    async handleBatchArchive() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认归档选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await postRequest('/archive/files/batch/archive', { fileIds })\r\n        this.$message.success('归档成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('归档失败')\r\n        }\r\n      }\r\n    },\r\n    // 批量下载\r\n    async handleBatchDownload() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        this.$message.success('开始批量下载')\r\n        // 模拟批量下载\r\n        this.selectedFiles.forEach(file => {\r\n          const link = document.createElement('a')\r\n          link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n          link.download = file.fileName\r\n          link.click()\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('下载失败')\r\n      }\r\n    },\r\n    // 批量删除\r\n    async handleBatchDelete() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认删除选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await deleteRequest('/archive/files/batch', { fileIds })\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'ppt': 'el-icon-document',\r\n        'pptx': 'el-icon-document',\r\n        'txt': 'el-icon-document',\r\n        'image': 'el-icon-picture',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n    // 格式化文件大小\r\n    formatFileSize(size) {\r\n      return formatFileSize(size)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-container {\r\n  padding: 20px;\r\n\r\n  .operation-bar {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .file-list-container {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n    \r\n    .el-table {\r\n      border: 1px solid #ebeef5;\r\n      border-radius: 4px;\r\n      \r\n      .el-table__header-wrapper {\r\n        .el-table__header {\r\n          th {\r\n            background-color: #fafafa;\r\n            color: #606266;\r\n            font-weight: 600;\r\n            border-bottom: 1px solid #ebeef5;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .el-table__body-wrapper {\r\n        .el-table__body {\r\n          tr {\r\n            &:hover {\r\n              background-color: #f5f7fa;\r\n            }\r\n            \r\n            td {\r\n              border-bottom: 1px solid #f0f0f0;\r\n              padding: 12px 0;\r\n              vertical-align: middle;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .file-name-cell {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    \r\n    .el-button {\r\n      margin: 0;\r\n      min-width: 60px;\r\n      height: 28px;\r\n      font-size: 12px;\r\n      padding: 5px 12px;\r\n      border-radius: 4px;\r\n      \r\n      &.el-button--mini {\r\n        padding: 5px 12px;\r\n      }\r\n    }\r\n    \r\n    .el-button + .el-button {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n\r\n  .preview-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 400px;\r\n\r\n    .image-preview {\r\n      img {\r\n        max-width: 100%;\r\n        max-height: 600px;\r\n      }\r\n    }\r\n\r\n    .pdf-preview, .office-preview {\r\n      width: 100%;\r\n      height: 600px;\r\n    }\r\n\r\n    .other-preview {\r\n      text-align: center;\r\n      color: #909399;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";AAkJA,SAAAA,UAAA;AACA,SAAAC,cAAA,EAAAC,WAAA;AACA,SAAAC,UAAA,EAAAC,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,UAAA;MACAC,WAAA;MACAC,SAAA;MACA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAjB,UAAA,EACA,WACA;IACAkB,QAAA;MACA,YAAAJ,WAAA,SAAAE,WAAA,CAAAG,KAAA,CAAAC,QAAA,MAAAN,WAAA,CAAAO,QAAA;IACA;IACAC,MAAA;MACA,YAAAR,WAAA,SAAAE,WAAA,CAAAO,GAAA,CAAAH,QAAA,MAAAN,WAAA,CAAAO,QAAA;IACA;IACAG,SAAA;MACA,YAAAV,WAAA,SAAAE,WAAA,CAAAS,MAAA,CAAAL,QAAA,MAAAN,WAAA,CAAAO,QAAA;IACA;EACA;EACAK,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,cAAA;MACA;QACA;QACA,MAAAE,SAAA,IACA;UACAC,EAAA;UACAC,QAAA;UACAV,QAAA;UACAW,QAAA;UACAC,IAAA;UACAC,UAAA;QACA,GACA;UACAJ,EAAA;UACAC,QAAA;UACAV,QAAA;UACAW,QAAA;UACAC,IAAA;UACAC,UAAA;QACA,GACA;UACAJ,EAAA;UACAC,QAAA;UACAV,QAAA;UACAW,QAAA;UACAC,IAAA;UACAC,UAAA;QACA,GACA;UACAJ,EAAA;UACAC,QAAA;UACAV,QAAA;UACAW,QAAA;UACAC,IAAA;UACAC,UAAA;QACA,GACA;UACAJ,EAAA;UACAC,QAAA;UACAV,QAAA;UACAW,QAAA;UACAC,IAAA;UACAC,UAAA;QACA,EACA;QAEA,KAAA1B,QAAA,GAAAqB,SAAA;QACA,KAAAM,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA;MACA;IACA;IACA;IACAC,aAAA;MACA,KAAA5B,mBAAA;IACA;IACA;IACA6B,aAAAC,IAAA;MACA,MAAAC,QAAA,GAAAD,IAAA,CAAAP,IAAA;MACA,KAAAQ,QAAA;QACA,KAAAN,QAAA,CAAAE,KAAA;QACA;MACA;MACA;IACA;IACA;IACAK,eAAAC,KAAA,EAAAH,IAAA;MACAI,OAAA,CAAAC,GAAA,UAAAL,IAAA,CAAAM,UAAA;IACA;IACA;IACAC,oBAAAC,QAAA,EAAAR,IAAA;MACA,KAAAL,QAAA,CAAAC,OAAA;MACA,KAAA1B,mBAAA;MACA,KAAAiB,aAAA;IACA;IACA;IACAsB,kBAAA;MACA,KAAAd,QAAA,CAAAE,KAAA;IACA;IACA;IACA,MAAAa,cAAAV,IAAA;MACA,KAAA1B,WAAA,GAAA0B,IAAA;MACA,KAAA7B,oBAAA;MACA;MACA,KAAAE,UAAA,cAAAsC,kBAAA,CAAAX,IAAA;IACA;IACA;IACA,MAAAW,mBAAAX,IAAA;MACA;QAAA,IAAAY,cAAA;QACA,MAAAJ,QAAA,SAAA7C,UAAA,qBAAAqC,IAAA,CAAAV,EAAA;QACA,SAAAsB,cAAA,GAAAJ,QAAA,CAAAzC,IAAA,cAAA6C,cAAA,uBAAAA,cAAA,CAAAvC,UAAA;MACA,SAAAwB,KAAA;QACA;MACA;IACA;IACA;IACA,MAAAgB,eAAAb,IAAA;MACA;QACA;QACA,MAAAc,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,2CAAAC,IAAA,CAAAlB,IAAA,CAAAT,QAAA;QACAuB,IAAA,CAAAK,QAAA,GAAAnB,IAAA,CAAAT,QAAA;QACAuB,IAAA,CAAAM,KAAA;QACA,KAAAzB,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA;MACA;IACA;IACA;IACA,MAAAwB,aAAArB,IAAA;MACA;QACA,WAAAsB,QAAA;UACAC,IAAA;QACA;QACA,MAAA1D,aAAA,mBAAAmC,IAAA,CAAAV,EAAA;QACA,KAAAK,QAAA,CAAAC,OAAA;QACA,KAAAT,aAAA;MACA,SAAAU,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,QAAA,CAAAE,KAAA;QACA;MACA;IACA;IACA;IACA2B,sBAAAC,SAAA;MACA,KAAAxD,aAAA,GAAAwD,SAAA;IACA;IACA;IACA,MAAAC,mBAAA;MACA,UAAAzD,aAAA,CAAA0D,MAAA;MACA;QACA,WAAAL,QAAA;UACAC,IAAA;QACA;QACA,MAAAK,OAAA,QAAA3D,aAAA,CAAA4D,GAAA,CAAA7B,IAAA,IAAAA,IAAA,CAAAV,EAAA;QACA,MAAA1B,WAAA;UAAAgE;QAAA;QACA,KAAAjC,QAAA,CAAAC,OAAA;QACA,KAAAT,aAAA;MACA,SAAAU,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,QAAA,CAAAE,KAAA;QACA;MACA;IACA;IACA;IACA,MAAAiC,oBAAA;MACA,UAAA7D,aAAA,CAAA0D,MAAA;MACA;QACA,KAAAhC,QAAA,CAAAC,OAAA;QACA;QACA,KAAA3B,aAAA,CAAA8D,OAAA,CAAA/B,IAAA;UACA,MAAAc,IAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,IAAA,CAAAG,IAAA,2CAAAC,IAAA,CAAAlB,IAAA,CAAAT,QAAA;UACAuB,IAAA,CAAAK,QAAA,GAAAnB,IAAA,CAAAT,QAAA;UACAuB,IAAA,CAAAM,KAAA;QACA;MACA,SAAAvB,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA;MACA;IACA;IACA;IACA,MAAAmC,kBAAA;MACA,UAAA/D,aAAA,CAAA0D,MAAA;MACA;QACA,WAAAL,QAAA;UACAC,IAAA;QACA;QACA,MAAAK,OAAA,QAAA3D,aAAA,CAAA4D,GAAA,CAAA7B,IAAA,IAAAA,IAAA,CAAAV,EAAA;QACA,MAAAzB,aAAA;UAAA+D;QAAA;QACA,KAAAjC,QAAA,CAAAC,OAAA;QACA,KAAAT,aAAA;MACA,SAAAU,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,QAAA,CAAAE,KAAA;QACA;MACA;IACA;IACA;IACAoC,YAAApD,QAAA;MACA,MAAAqD,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAArD,QAAA;IACA;IACA;IACApB,eAAAgC,IAAA;MACA,OAAAhC,cAAA,CAAAgC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}