(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77d38154"],{"13d5":function(t,e,s){"use strict";var a=s("23e7"),i=s("d58f").left,l=s("a640"),r=s("2d00"),o=s("605d"),c=!o&&r>79&&r<83,n=c||!l("reduce");a({target:"Array",proto:!0,forced:n},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"605d":function(t,e,s){"use strict";var a=s("da84"),i=s("c6b6");t.exports="process"===i(a.process)},"684d":function(t,e,s){},a640:function(t,e,s){"use strict";var a=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&a((function(){s.call(null,e||function(){return 1},1)}))}},d311:function(t,e,s){"use strict";s("684d")},d58f:function(t,e,s){"use strict";var a=s("59ed"),i=s("7b0b"),l=s("44ad"),r=s("07fa"),o=TypeError,c="Reduce of empty array with no initial value",n=function(t){return function(e,s,n,d){var u=i(e),p=l(u),m=r(u);if(a(s),0===m&&n<2)throw new o(c);var g=t?m-1:0,v=t?-1:1;if(n<2)while(1){if(g in p){d=p[g],g+=v;break}if(g+=v,t?g<0:m<=g)throw new o(c)}for(;t?g>=0:m>g;g+=v)g in p&&(d=s(d,p[g],g,u));return d}};t.exports={left:n(!1),right:n(!0)}},e32b:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"client-group-container"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-left"},[e("h2",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-s-custom"}),t._v(" "+t._s(this.$router.currentRoute.name)+" ")]),e("div",{staticClass:"page-subtitle"},[t._v("管理签约客户群组和工作协作")])]),e("div",{staticClass:"header-actions"},[e("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.refulsh}},[t._v(" 刷新数据 ")])],1)]),e("div",{staticClass:"stats-section"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon total-icon"},[e("i",{staticClass:"el-icon-s-custom"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.total))]),e("div",{staticClass:"stat-label"},[t._v("客户群组")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +6% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon active-icon"},[e("i",{staticClass:"el-icon-chat-line-round"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.activeGroups))]),e("div",{staticClass:"stat-label"},[t._v("活跃群组")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +10% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon member-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.totalMembers))]),e("div",{staticClass:"stat-label"},[t._v("总成员数")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +15% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon efficiency-icon"},[e("i",{staticClass:"el-icon-data-analysis"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v("92%")]),e("div",{staticClass:"stat-label"},[t._v("协作效率")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +3% ")])])])])],1)],1),e("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"card-title"},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索管理 ")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.editData(0)}}},[t._v(" 新建群组 ")])],1)]),e("div",{staticClass:"search-section"},[e("el-form",{staticClass:"search-form",attrs:{model:t.search,inline:!0}},[e("el-form-item",{attrs:{label:"关键词"}},[e("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入群组名称或描述",clearable:""},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.searchData()}},slot:"append"})],1)],1),e("el-form-item",[e("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetSearch}},[t._v(" 重置 ")])],1)],1)],1)]),e("el-card",{staticClass:"group-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"card-title"},[e("i",{staticClass:"el-icon-tickets"}),t._v(" 群组列表 ")]),e("div",{staticClass:"view-controls"},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.viewMode,callback:function(e){t.viewMode=e},expression:"viewMode"}},[e("el-radio-button",{attrs:{label:"grid"}},[t._v("卡片视图")]),e("el-radio-button",{attrs:{label:"table"}},[t._v("表格视图")])],1)],1)]),"grid"===t.viewMode?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"group-grid"},t._l(t.list,(function(s){return e("div",{key:s.id,staticClass:"group-item"},[e("div",{staticClass:"group-header"},[e("div",{staticClass:"group-avatar"},[s.pic_path?e("img",{attrs:{src:s.pic_path,alt:"群组头像"}}):e("i",{staticClass:"el-icon-s-custom default-avatar"})]),e("div",{staticClass:"group-info"},[e("div",{staticClass:"group-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"group-desc"},[t._v(t._s(s.desc||"暂无描述"))])])]),e("div",{staticClass:"group-content"},[e("div",{staticClass:"group-stats"},[e("div",{staticClass:"stat-item"},[e("i",{staticClass:"el-icon-user"}),e("span",[t._v(t._s(t.getGroupMemberCount(s))+"人")])]),e("div",{staticClass:"stat-item"},[e("i",{staticClass:"el-icon-time"}),e("span",[t._v(t._s(t.formatDate(s.create_time)))])])]),s.members&&s.members.length>0?e("div",{staticClass:"group-members"},[e("div",{staticClass:"member-avatars"},[t._l(s.members.slice(0,5),(function(t,s){return e("div",{key:s,staticClass:"member-avatar",attrs:{title:t.name}},[e("i",{staticClass:"el-icon-user"})])})),s.members.length>5?e("div",{staticClass:"more-members"},[t._v(" +"+t._s(s.members.length-5)+" ")]):t._e()],2)]):t._e()]),e("div",{staticClass:"group-footer"},[e("div",{staticClass:"group-status"},[e("el-tag",{attrs:{type:t.getGroupStatusType(s),size:"small"}},[t._v(" "+t._s(t.getGroupStatusText(s))+" ")])],1),e("div",{staticClass:"group-actions"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text"},on:{click:function(e){return t.editData(s.id)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text"},on:{click:function(e){return t.delData(-1,s.id)}}},[t._v(" 删除 ")])],1)])])})),0):t._e(),"table"===t.viewMode?e("div",[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:t.list}},[e("el-table-column",{attrs:{label:"群组信息","min-width":"250"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"table-group-info"},[e("div",{staticClass:"table-group-header"},[e("div",{staticClass:"table-group-avatar"},[s.row.pic_path?e("img",{attrs:{src:s.row.pic_path,alt:"群组头像"}}):e("i",{staticClass:"el-icon-s-custom"})]),e("div",{staticClass:"table-group-details"},[e("div",{staticClass:"table-group-title"},[t._v(t._s(s.row.title))]),e("div",{staticClass:"table-group-desc"},[t._v(t._s(s.row.desc||"暂无描述"))])])])])]}}],null,!1,2111639002)}),e("el-table-column",{attrs:{label:"成员",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"member-count"},[e("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(t.getGroupMemberCount(s.row))+"人 ")])]}}],null,!1,1893861867)}),e("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:t.getGroupStatusType(s.row),size:"small"}},[t._v(" "+t._s(t.getGroupStatusText(s.row))+" ")])]}}],null,!1,1533789601)}),e("el-table-column",{attrs:{label:"创建时间",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-info"},[t._v(" "+t._s(t.formatDate(s.row.create_time))+" ")])]}}],null,!1,2692560985)}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"action-buttons"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.editData(s.row.id)}}},[e("i",{staticClass:"el-icon-edit"}),t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.delData(s.$index,s.row.id)}}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 删除 ")])],1)]}}],null,!1,1323445013)})],1)],1):t._e(),e("div",{staticClass:"pagination-wrapper"},[e("el-pagination",{attrs:{"page-sizes":[12,20,50,100],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total,background:""},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{staticClass:"edit-dialog",attrs:{title:t.dialogTitle,visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"650px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"群组名称",prop:"title"}},[e("el-input",{attrs:{placeholder:"请输入群组名称",autocomplete:"off"},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1),e("el-form-item",{attrs:{label:"群组头像",prop:"pic_path"}},[e("div",{staticClass:"avatar-upload"},[t.ruleForm.pic_path?e("div",{staticClass:"avatar-preview"},[e("img",{attrs:{src:t.ruleForm.pic_path,alt:"群组头像"}}),e("div",{staticClass:"avatar-actions"},[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.showImage(t.ruleForm.pic_path)}}},[t._v("查看")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.delImage(t.ruleForm.pic_path,"pic_path")}}},[t._v("删除")])],1)]):e("div",{staticClass:"avatar-upload-area"},[e("el-upload",{staticClass:"avatar-uploader",attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":t.handleSuccess,"before-upload":t.beforeUpload}},[e("div",{staticClass:"upload-placeholder"},[e("i",{staticClass:"el-icon-plus"}),e("div",[t._v("上传头像")])])])],1),e("div",{staticClass:"upload-tip"},[t._v("建议尺寸: 96×96像素")])])]),e("el-form-item",{attrs:{label:"负责员工"}},[e("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.yuangongs,props:t.props,placeholder:"请选择负责员工",filterable:"",clearable:""},model:{value:t.ruleForm.yuangong_id,callback:function(e){t.$set(t.ruleForm,"yuangong_id",e)},expression:"ruleForm.yuangong_id"}})],1),e("el-form-item",{attrs:{label:"群组成员"}},[e("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.users,props:t.props,placeholder:"请选择群组成员",filterable:"",clearable:""},model:{value:t.ruleForm.uid,callback:function(e){t.$set(t.ruleForm,"uid",e)},expression:"ruleForm.uid"}})],1),e("el-form-item",{attrs:{label:"群组描述"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入群组详细描述...",autocomplete:"off"},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.saveLoading},on:{click:function(e){return t.saveData()}}},[t._v(" 保存 ")])],1)],1),e("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"image-viewer"},[e("el-image",{attrs:{src:t.show_image,fit:"contain"}})],1)])],1)},i=[],l=(s("13d5"),{name:"ClientGroupManagement",components:{},data(){return{props:{multiple:!0},allSize:"mini",list:[],total:1,page:1,size:12,viewMode:"grid",saveLoading:!1,search:{keyword:""},loading:!0,url:"/qun/",title:"工作群",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0,pic_path:"",desc:"",yuangong_id:[],uid:[]},uid:[],rules:{title:[{required:!0,message:"请填写群组名称",trigger:"blur"}]},formLabelWidth:"120px",users:[],lvshis:[],yuangongs:[]}},computed:{activeGroups(){return Array.isArray(this.list)?this.list.filter(t=>t.title&&""!==t.title.trim()).length:0},totalMembers(){return Array.isArray(this.list)?this.list.reduce((t,e)=>t+this.getGroupMemberCount(e),0):0},dialogTitle(){return this.ruleForm.id?"编辑群组":"新建群组"}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):this.ruleForm={title:"",desc:"",uid:"",pic_path:"",yuangong_id:"",lvshi_id:""},e.dialogFormVisible=!0,e.getLvshi(),e.getYuaong(),e.getUser()},getLvshi(){let t=this;t.getRequest(t.url+"getLvshi").then(e=>{200==e.code&&(t.lvshis=e.data)})},getYuaong(){let t=this;t.getRequest(t.url+"getYuangong").then(e=>{200==e.code&&(t.yuangongs=e.data)})},getUser(){let t=this;t.getRequest(t.url+"getKehu").then(e=>{if(200==e.code){let s=e.data;s.forEach((t,e)=>{t.label=t.nickname,t.value=t.id}),t.users=s}})},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{t&&(e.ruleForm=t.data)})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(t,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let t=this;t.loading=!0,t.postRequest(t.url+"index?page="+t.page+"&size="+t.size,t.search).then(e=>{200==e.code&&(t.list=e.data,t.total=e.count),t.loading=!1})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest(t.url+"save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},handleSuccess(t){this.ruleForm.pic_path=t.data.url},showImage(t){this.show_image=t,this.dialogVisible=!0},beforeUpload(t){const e=/^image\/(jpeg|png|jpg)$/.test(t.type);e||this.$message.error("上传图片格式不对!")},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})},formatDate(t){return t?new Date(t).toLocaleDateString("zh-CN"):"未设置"},resetSearch(){this.search={keyword:""},this.page=1,this.getData()},getGroupMemberCount(t){return t.uid&&Array.isArray(t.uid)?t.uid.length:t.members&&Array.isArray(t.members)?t.members.length:Math.floor(20*Math.random())+3},getGroupStatusType(t){return t.title&&""!==t.title.trim()?"success":"info"},getGroupStatusText(t){return t.title&&""!==t.title.trim()?"正常":"待完善"}}}),r=l,o=(s("d311"),s("2877")),c=Object(o["a"])(r,a,i,!1,null,"da698b76",null);e["default"]=c.exports}}]);
//# sourceMappingURL=chunk-77d38154.7ec4020f.js.map