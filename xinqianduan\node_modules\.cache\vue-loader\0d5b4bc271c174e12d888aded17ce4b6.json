{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue?vue&type=style&index=0&id=228d4396&prod&lang=scss&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue", "mtime": 1748617691744}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1748377681648}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["File.vue"], "names": [], "mappings": ";AAyYA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "File.vue", "sourceRoot": "src/views/pages/archive", "sourcesContent": ["<template>\r\n  <div class=\"archive-container\">\r\n    <!-- 顶部操作栏 -->\r\n    <div class=\"operation-bar\">\r\n      <el-button-group>\r\n        <el-button type=\"primary\" @click=\"handleUpload\">\r\n          <i class=\"el-icon-upload\"></i> 上传文件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleBatchArchive\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-folder-add\"></i> 批量归档\r\n        </el-button>\r\n        <el-button type=\"warning\" @click=\"handleBatchDownload\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-download\"></i> 批量下载\r\n        </el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchDelete\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-delete\"></i> 批量删除\r\n        </el-button>\r\n      </el-button-group>\r\n    </div>\r\n\r\n    <!-- 文件列表区域 -->\r\n    <div class=\"file-list-container\">\r\n      <el-table\r\n        :data=\"fileList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\">\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"55\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileName\"\r\n          label=\"文件名\"\r\n          min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"file-name-cell\">\r\n              <i :class=\"getFileIcon(scope.row.fileType)\"></i>\r\n              <span>{{ scope.row.fileName }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileType\"\r\n          label=\"类型\"\r\n          width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"category\"\r\n          label=\"分类\"\r\n          width=\"120\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"size\"\r\n          label=\"大小\"\r\n          width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatFileSize(scope.row.size) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"uploadTime\"\r\n          label=\"上传时间\"\r\n          width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          width=\"240\"\r\n          align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handlePreview(scope.row)\">\r\n                预览\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleDownload(scope.row)\">\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\">\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 文件上传对话框 -->\r\n    <el-dialog\r\n      title=\"文件上传\"\r\n      :visible.sync=\"uploadDialogVisible\"\r\n      width=\"500px\">\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        multiple\r\n        :action=\"uploadUrl\"\r\n        :before-upload=\"beforeUpload\"\r\n        :on-progress=\"handleProgress\"\r\n        :on-success=\"handleUploadSuccess\"\r\n        :on-error=\"handleUploadError\"\r\n        :file-list=\"uploadFileList\">\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          支持任意格式文件，单个文件不超过500MB\r\n        </div>\r\n      </el-upload>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"80%\"\r\n      :fullscreen=\"true\">\r\n      <div class=\"preview-container\">\r\n        <!-- 图片预览 -->\r\n        <div v-if=\"isImage\" class=\"image-preview\">\r\n          <img :src=\"previewUrl\" alt=\"预览图片\">\r\n        </div>\r\n        <!-- PDF预览 -->\r\n        <div v-else-if=\"isPdf\" class=\"pdf-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- Office文档预览 -->\r\n        <div v-else-if=\"isOffice\" class=\"office-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- 其他文件类型 -->\r\n        <div v-else class=\"other-preview\">\r\n          <p>该文件类型暂不支持预览，请下载后查看</p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { formatFileSize, getFileType } from '@/utils/fileUtils'\r\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ArchiveFile',\r\n  data() {\r\n    return {\r\n      fileList: [],\r\n      selectedFiles: [],\r\n      uploadDialogVisible: false,\r\n      previewDialogVisible: false,\r\n      uploadFileList: [],\r\n      previewUrl: '',\r\n      currentFile: null,\r\n      uploadUrl: '/archive/upload',\r\n      // 文件类型映射\r\n      fileTypeMap: {\r\n        'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp'],\r\n        'pdf': ['pdf'],\r\n        'office': ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],\r\n        'text': ['txt', 'md'],\r\n        'archive': ['zip', 'rar', '7z']\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'userRole'\r\n    ]),\r\n    isImage() {\r\n      return this.currentFile && this.fileTypeMap.image.includes(this.currentFile.fileType)\r\n    },\r\n    isPdf() {\r\n      return this.currentFile && this.fileTypeMap.pdf.includes(this.currentFile.fileType)\r\n    },\r\n    isOffice() {\r\n      return this.currentFile && this.fileTypeMap.office.includes(this.currentFile.fileType)\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchFileList()\r\n  },\r\n  methods: {\r\n    // 获取文件列表\r\n    async fetchFileList() {\r\n      try {\r\n        // 模拟归档文件数据\r\n        const mockFiles = [\r\n          {\r\n            id: 1,\r\n            fileName: \"合同模板.pdf\",\r\n            fileType: \"pdf\",\r\n            category: \"合同文件\",\r\n            size: 1024000,\r\n            uploadTime: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            fileName: \"案件资料.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 512000,\r\n            uploadTime: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            fileName: \"咨询记录.txt\",\r\n            fileType: \"txt\",\r\n            category: \"咨询记录\",\r\n            size: 8192,\r\n            uploadTime: \"2024-01-13 16:45:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            fileName: \"证据材料.jpg\",\r\n            fileType: \"jpg\",\r\n            category: \"案件文书\",\r\n            size: 2048000,\r\n            uploadTime: \"2024-01-12 09:15:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            fileName: \"法律意见书.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 768000,\r\n            uploadTime: \"2024-01-11 16:30:00\"\r\n          }\r\n        ];\r\n        \r\n        this.fileList = mockFiles;\r\n        this.$message.success('文件列表加载成功');\r\n      } catch (error) {\r\n        this.$message.error('获取文件列表失败');\r\n      }\r\n    },\r\n    // 显示上传对话框\r\n    handleUpload() {\r\n      this.uploadDialogVisible = true\r\n    },\r\n    // 文件上传前检查\r\n    beforeUpload(file) {\r\n      const isLt500M = file.size / 1024 / 1024 < 500\r\n      if (!isLt500M) {\r\n        this.$message.error('文件大小不能超过 500MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    // 处理上传进度\r\n    handleProgress(event, file) {\r\n      console.log('上传进度：', file.percentage)\r\n    },\r\n    // 处理上传成功\r\n    handleUploadSuccess(response, file) {\r\n      this.$message.success('文件上传成功')\r\n      this.uploadDialogVisible = false\r\n      this.fetchFileList()\r\n    },\r\n    // 处理上传失败\r\n    handleUploadError() {\r\n      this.$message.error('文件上传失败')\r\n    },\r\n    // 处理文件预览\r\n    async handlePreview(file) {\r\n      this.currentFile = file\r\n      this.previewDialogVisible = true\r\n      // 根据文件类型生成预览URL\r\n      this.previewUrl = await this.generatePreviewUrl(file)\r\n    },\r\n    // 生成预览URL\r\n    async generatePreviewUrl(file) {\r\n      try {\r\n        const response = await getRequest(`/archive/preview/${file.id}`)\r\n        return response.data?.previewUrl || 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      } catch (error) {\r\n        return 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      }\r\n    },\r\n    // 处理文件下载\r\n    async handleDownload(file) {\r\n      try {\r\n        // 创建一个虚拟的下载链接\r\n        const link = document.createElement('a')\r\n        link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n        link.download = file.fileName\r\n        link.click()\r\n        this.$message.success('开始下载')\r\n      } catch (error) {\r\n        this.$message.error('文件下载失败')\r\n      }\r\n    },\r\n    // 处理文件删除\r\n    async handleDelete(file) {\r\n      try {\r\n        await this.$confirm('确认删除该文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        await deleteRequest(`/archive/files/${file.id}`)\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 处理批量操作\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n    },\r\n    // 批量归档\r\n    async handleBatchArchive() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认归档选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await postRequest('/archive/files/batch/archive', { fileIds })\r\n        this.$message.success('归档成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('归档失败')\r\n        }\r\n      }\r\n    },\r\n    // 批量下载\r\n    async handleBatchDownload() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        this.$message.success('开始批量下载')\r\n        // 模拟批量下载\r\n        this.selectedFiles.forEach(file => {\r\n          const link = document.createElement('a')\r\n          link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n          link.download = file.fileName\r\n          link.click()\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('下载失败')\r\n      }\r\n    },\r\n    // 批量删除\r\n    async handleBatchDelete() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认删除选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await deleteRequest('/archive/files/batch', { fileIds })\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'ppt': 'el-icon-document',\r\n        'pptx': 'el-icon-document',\r\n        'txt': 'el-icon-document',\r\n        'image': 'el-icon-picture',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n    // 格式化文件大小\r\n    formatFileSize(size) {\r\n      return formatFileSize(size)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-container {\r\n  padding: 20px;\r\n\r\n  .operation-bar {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .file-list-container {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n    \r\n    .el-table {\r\n      border: 1px solid #ebeef5;\r\n      border-radius: 4px;\r\n      \r\n      .el-table__header-wrapper {\r\n        .el-table__header {\r\n          th {\r\n            background-color: #fafafa;\r\n            color: #606266;\r\n            font-weight: 600;\r\n            border-bottom: 1px solid #ebeef5;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .el-table__body-wrapper {\r\n        .el-table__body {\r\n          tr {\r\n            &:hover {\r\n              background-color: #f5f7fa;\r\n            }\r\n            \r\n            td {\r\n              border-bottom: 1px solid #f0f0f0;\r\n              padding: 12px 0;\r\n              vertical-align: middle;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .file-name-cell {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    \r\n    .el-button {\r\n      margin: 0;\r\n      min-width: 60px;\r\n      height: 28px;\r\n      font-size: 12px;\r\n      padding: 5px 12px;\r\n      border-radius: 4px;\r\n      \r\n      &.el-button--mini {\r\n        padding: 5px 12px;\r\n      }\r\n    }\r\n    \r\n    .el-button + .el-button {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n\r\n  .preview-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 400px;\r\n\r\n    .image-preview {\r\n      img {\r\n        max-width: 100%;\r\n        max-height: 600px;\r\n      }\r\n    }\r\n\r\n    .pdf-preview, .office-preview {\r\n      width: 100%;\r\n      height: 600px;\r\n    }\r\n\r\n    .other-preview {\r\n      text-align: center;\r\n      color: #909399;\r\n    }\r\n  }\r\n}\r\n</style> "]}]}