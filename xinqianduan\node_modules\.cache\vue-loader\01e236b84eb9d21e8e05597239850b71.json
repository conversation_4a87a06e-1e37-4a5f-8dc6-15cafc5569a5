{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\data\\configs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\data\\configs.vue", "mtime": 1748336508326}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["configs.vue"], "names": [], "mappings": ";AA6NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "configs.vue", "sourceRoot": "src/views/pages/data", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-title\">\r\n        基础设置\r\n      </div>\r\n\r\n      <!-- 标签页导航 -->\r\n      <div class=\"tab-container\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <div class=\"form-container\">\r\n              <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\">\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"网站名称\">\r\n                      <el-input v-model=\"ruleForm.site_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\">\r\n                      <el-input v-model=\"ruleForm.company_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系方式\">\r\n                      <el-input v-model=\"ruleForm.site_tel\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"邮箱\">\r\n                      <el-input v-model=\"ruleForm.email\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"ruleForm.site_address\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案号\">\r\n                      <el-input v-model=\"ruleForm.site_icp\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案链接\">\r\n                      <el-input v-model=\"ruleForm.site_icp_url\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-form-item label=\"网站Logo\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.site_logo\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传Logo图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('site_logo')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"showImage(ruleForm.site_logo)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"推广律师\">\r\n                  <el-select\r\n                    v-model=\"ruleForm.lvshi\"\r\n                    placeholder=\"请选择推广律师\"\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                      v-for=\"(item, index) in lvshi\"\r\n                      :key=\"index\"\r\n                      :label=\"item.title\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广标题\">\r\n                      <el-input v-model=\"ruleForm.my_title\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广语\">\r\n                      <el-input v-model=\"ruleForm.my_desc\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"推广图片\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.about_path\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传推广图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('about_path')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"showImage(ruleForm.about_path)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"隐私条款内容\">\r\n                <el-input\r\n                  v-model=\"ruleForm.yinsi\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"12\"\r\n                  placeholder=\"请输入隐私条款内容\"\r\n                ></el-input>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"关于我们内容\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_about_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"change\"\r\n                ></editor-bar>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"团队介绍内容\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_team_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"change\"\r\n                ></editor-bar>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n\r\n      <!-- 提交按钮 -->\r\n      <div class=\"submit-container\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"medium\"\r\n          @click=\"saveData\"\r\n          :loading=\"fullscreenLoading\"\r\n          >保存设置\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: \"这是隐私条款的演示内容...\",\r\n        index_about_content: \"<p>这是关于我们的演示内容...</p>\",\r\n        index_team_content: \"<p>这是团队介绍的演示内容...</p>\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tab-container {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.form-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 提交按钮容器 */\r\n.submit-container {\r\n  text-align: center;\r\n  padding: 24px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-input, .el-select, .el-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.el-textarea .el-textarea__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 标签页样式 */\r\n.el-tabs--card > .el-tabs__header .el-tabs__item {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\r\n  background-color: #fff;\r\n  border-bottom-color: #fff;\r\n}\r\n\r\n/* 上传按钮样式 */\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-wrapper {\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .upload-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .upload-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}