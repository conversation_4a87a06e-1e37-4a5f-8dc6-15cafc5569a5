{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue?vue&type=template&id=335f2615&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue", "mtime": 1748439533995}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}