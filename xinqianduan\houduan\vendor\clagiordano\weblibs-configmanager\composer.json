{"name": "clagiordano/weblibs-configmanager", "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "type": "library", "license": "LGPL-3.0-or-later", "keywords": ["clag<PERSON><PERSON><PERSON>", "weblibs", "configuration", "manager", "tool"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "require": {"php": ">=5.4", "symfony/yaml": "^2.8", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^4.8", "clagiordano/phpunit-result-printer": "^1", "php-coveralls/php-coveralls": "^1.1"}, "autoload-dev": {"psr-4": {"clagiordano\\weblibs\\configmanager\\tests\\": "tests/", "clagiordano\\weblibs\\configmanager\\testdata\\": "testdata/"}}, "scripts": {"test": ["Composer\\Config::disableProcessTimeout", "./vendor/bin/phpunit --no-coverage"], "coverage": ["Composer\\Config::disableProcessTimeout", "php -dzend_extension=xdebug.so ./vendor/bin/phpunit"], "debug": "php -dxdebug.remote_autostart=On -dzend_extension=xdebug.so ./vendor/bin/phpunit"}, "config": {"platform": {"php": "5.4"}}}