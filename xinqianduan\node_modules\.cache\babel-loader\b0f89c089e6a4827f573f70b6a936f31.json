{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\src\\store\\index.js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\store\\index.js", "mtime": 1748377686250}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICJ2dWUiOwppbXBvcnQgVnVleCBmcm9tICJ2dWV4IjsKVnVlLnVzZShWdWV4KTsKZXhwb3J0IGRlZmF1bHQgbmV3IFZ1ZXguU3RvcmUoewogIHN0YXRlOiB7CiAgICB0b2tlbjogd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInRva2VuIikgfHwgImRlbW8tdG9rZW4tMTIzNDU2IiwKICAgIHNwYnM6IHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJzcGJzIiksCiAgICB0aXRsZTogd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInRpdGxlIikgfHwgIuazleW+i+acjeWKoeeuoeeQhuezu+e7nyIsCiAgICBxdWFueGlhbjogd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInF1YW54aWFuIikgfHwgImFkbWluIgogIH0sCiAgZ2V0dGVyczogewogICAgR0VUX1RPS0VOOiBzdGF0ZSA9PiBzdGF0ZS50b2tlbiwKICAgIEdFVF9USVRMRTogc3RhdGUgPT4gc3RhdGUudGl0bGUsCiAgICBHRVRfU1BCUzogc3RhdGUgPT4gc3RhdGUuc3BicywKICAgIEdFVF9RVUFOWElBTjogc3RhdGUgPT4gc3RhdGUucXVhbnhpYW4KICB9LAogIG11dGF0aW9uczogewogICAgSU5JVF9UT0tFTihzdGF0ZSwgdG9rZW4pIHsKICAgICAgc3RhdGUudG9rZW4gPSB0b2tlbjsKICAgICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInRva2VuIiwgdG9rZW4pOwogICAgfSwKICAgIElOSVRfU1BCUyhzdGF0ZSwgc3BicykgewogICAgICBzdGF0ZS5zcGJzID0gc3BiczsKICAgICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInNwYnMiLCBzcGJzKTsKICAgIH0sCiAgICBJTklUX1FVQU5YSUFOKHN0YXRlLCBxdWFueGlhbikgewogICAgICBzdGF0ZS5xdWFueGlhbiA9IHF1YW54aWFuOwogICAgICB3aW5kb3cuc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgicXVhbnhpYW4iLCBxdWFueGlhbik7CiAgICB9LAogICAgSU5JVF9USVRMRShzdGF0ZSwgdGl0bGUpIHsKICAgICAgc3RhdGUudGl0bGUgPSB0aXRsZTsKICAgICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInRpdGxlIiwgdGl0bGUpOwogICAgfQogIH0sCiAgYWN0aW9uczoge30sCiAgbW9kdWxlczoge30KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "Store", "state", "token", "window", "sessionStorage", "getItem", "spbs", "title", "quanxian", "getters", "GET_TOKEN", "GET_TITLE", "GET_SPBS", "GET_QUANXIAN", "mutations", "INIT_TOKEN", "setItem", "INIT_SPBS", "INIT_QUANXIAN", "INIT_TITLE", "actions", "modules"], "sources": ["H:/fdbfront/xinqianduan/src/store/index.js"], "sourcesContent": ["import Vue from \"vue\";\r\nimport Vuex from \"vuex\";\r\n\r\nVue.use(Vuex);\r\n\r\nexport default new Vuex.Store({\r\n  state: {\r\n    token: window.sessionStorage.getItem(\"token\") || \"demo-token-123456\",\r\n\tspbs: window.sessionStorage.getItem(\"spbs\"),\r\n    title: window.sessionStorage.getItem(\"title\") || \"法律服务管理系统\",\r\n    quanxian: window.sessionStorage.getItem(\"quanxian\") || \"admin\"\r\n  },\r\n  getters: {\r\n    GET_TOKEN: (state) => state.token,\r\n    GET_TITLE: (state) => state.title,\r\n\tGET_SPBS: (state) => state.spbs,\r\n    GET_QUANXIAN: (state) => state.quanxian,\r\n  },\r\n  mutations: {\r\n    INIT_TOKEN(state, token) {\r\n      state.token = token;\r\n      window.sessionStorage.setItem(\"token\", token);\r\n    },\r\n\tINIT_SPBS(state, spbs) {\r\n\t  state.spbs = spbs;\r\n\t  window.sessionStorage.setItem(\"spbs\", spbs);\r\n\t},\r\n    INIT_QUANXIAN(state, quanxian) {\r\n      state.quanxian = quanxian;\r\n      window.sessionStorage.setItem(\"quanxian\", quanxian);\r\n    },\r\n    INIT_TITLE(state, title) {\r\n      state.title = title;\r\n      window.sessionStorage.setItem(\"title\", title);\r\n    },\r\n  },\r\n  actions: {},\r\n  modules: {},\r\n});\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACE,KAAK,CAAC;EAC5BC,KAAK,EAAE;IACLC,KAAK,EAAEC,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,mBAAmB;IACvEC,IAAI,EAAEH,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACxCE,KAAK,EAAEJ,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU;IAC3DG,QAAQ,EAAEL,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI;EACzD,CAAC;EACDI,OAAO,EAAE;IACPC,SAAS,EAAGT,KAAK,IAAKA,KAAK,CAACC,KAAK;IACjCS,SAAS,EAAGV,KAAK,IAAKA,KAAK,CAACM,KAAK;IACpCK,QAAQ,EAAGX,KAAK,IAAKA,KAAK,CAACK,IAAI;IAC5BO,YAAY,EAAGZ,KAAK,IAAKA,KAAK,CAACO;EACjC,CAAC;EACDM,SAAS,EAAE;IACTC,UAAUA,CAACd,KAAK,EAAEC,KAAK,EAAE;MACvBD,KAAK,CAACC,KAAK,GAAGA,KAAK;MACnBC,MAAM,CAACC,cAAc,CAACY,OAAO,CAAC,OAAO,EAAEd,KAAK,CAAC;IAC/C,CAAC;IACJe,SAASA,CAAChB,KAAK,EAAEK,IAAI,EAAE;MACrBL,KAAK,CAACK,IAAI,GAAGA,IAAI;MACjBH,MAAM,CAACC,cAAc,CAACY,OAAO,CAAC,MAAM,EAAEV,IAAI,CAAC;IAC7C,CAAC;IACEY,aAAaA,CAACjB,KAAK,EAAEO,QAAQ,EAAE;MAC7BP,KAAK,CAACO,QAAQ,GAAGA,QAAQ;MACzBL,MAAM,CAACC,cAAc,CAACY,OAAO,CAAC,UAAU,EAAER,QAAQ,CAAC;IACrD,CAAC;IACDW,UAAUA,CAAClB,KAAK,EAAEM,KAAK,EAAE;MACvBN,KAAK,CAACM,KAAK,GAAGA,KAAK;MACnBJ,MAAM,CAACC,cAAc,CAACY,OAAO,CAAC,OAAO,EAAET,KAAK,CAAC;IAC/C;EACF,CAAC;EACDa,OAAO,EAAE,CAAC,CAAC;EACXC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}]}