{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\UserDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\UserDetail.vue", "mtime": 1748615971050}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "id", "type", "String", "Number", "required", "data", "info", "loading", "dialogVisible", "show_image", "watch", "immediate", "handler", "newId", "console", "log", "getInfo", "methods", "_this", "setTimeout", "testUserData", "company", "phone", "nickname", "linkman", "headimg", "yuangong_id", "linkphone", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "license", "start_time", "year", "debts", "tel", "money", "status", "showImage", "imageUrl", "viewDebtDetail", "debt", "$message"], "sources": ["src/components/UserDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAuNA;EACAA,IAAA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,IAAA;MAAA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;IACA;EACA;EACAC,KAAA;IACAV,EAAA;MACAW,SAAA;MAAA;MACAC,QAAAC,KAAA;QACA,IAAAA,KAAA,IAAAA,KAAA;UACAC,OAAA,CAAAC,GAAA,wBAAAF,KAAA;UACA,KAAAG,OAAA,CAAAH,KAAA;QACA;MACA;IACA;EACA;EACAI,OAAA;IACAD,QAAAhB,EAAA;MACA,IAAAkB,KAAA;MACAJ,OAAA,CAAAC,GAAA,iBAAAf,EAAA;MACAkB,KAAA,CAAAX,OAAA;;MAEA;MACAY,UAAA;QACA,MAAAC,YAAA;UACApB,EAAA,EAAAA,EAAA;UACAqB,OAAA;UACAC,KAAA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,YAAA;UACAC,SAAA;UACAC,SAAA;UACAC,WAAA;UACAC,OAAA;UACAC,QAAA;UACAC,OAAA;UACAC,UAAA;UACAC,IAAA;UACAC,KAAA,GACA;YACAvC,IAAA;YACAwC,GAAA;YACAC,KAAA;YACAC,MAAA;UACA,GACA;YACA1C,IAAA;YACAwC,GAAA;YACAC,KAAA;YACAC,MAAA;UACA;QAEA;QAEAtB,KAAA,CAAAZ,IAAA,GAAAc,YAAA;QACAF,KAAA,CAAAX,OAAA;QACAO,OAAA,CAAAC,GAAA,cAAAK,YAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IAEAqB,UAAAC,QAAA;MACA,KAAAjC,UAAA,GAAAiC,QAAA;MACA,KAAAlC,aAAA;IACA;IAEAmC,eAAAC,IAAA;MACA9B,OAAA,CAAAC,GAAA,aAAA6B,IAAA;MACA;MACA,KAAAC,QAAA,CAAAvC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}