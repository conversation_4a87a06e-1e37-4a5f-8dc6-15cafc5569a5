{"version": 3, "sources": ["webpack:///./src/views/pages/yonghu/chat.vue", "webpack:///./src/views/pages/yonghu/emoji.js", "webpack:///./src/components/audioplay.vue", "webpack:///src/components/audioplay.vue", "webpack:///./src/components/audioplay.vue?56f3", "webpack:///./src/components/audioplay.vue?721f", "webpack:///src/views/pages/yonghu/chat.vue", "webpack:///./src/views/pages/yonghu/chat.vue?d959", "webpack:///./src/views/pages/yonghu/chat.vue?57f0", "webpack:///./src/views/pages/yonghu/chat.vue?6f66"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "$event", "isEmji", "directives", "name", "rawName", "value", "search", "expression", "attrs", "domProps", "changeKeyword", "target", "composing", "isShowSeach", "del", "_e", "showDaiban", "_v", "_l", "quns", "item", "index", "key", "class", "active", "quliaoIndex", "changeQun", "pic_path", "count", "_s", "title", "create_time", "desc", "users", "selectId", "redSession", "time", "content", "ref", "handleScroll", "list", "oneself", "yuangong_id", "yon_id", "avatar", "type", "openImg", "staticStyle", "datas", "openFile", "files", "size", "_m", "quanyuan", "la", "userss", "headimg", "nickname", "yuangongss", "zhiwei", "stopPropagation", "openEmji", "apply", "arguments", "emojiData", "<PERSON><PERSON><PERSON><PERSON>", "handleSuccess", "changeFile", "handleSuccess1", "beforeUpload", "daiban", "<PERSON><PERSON><PERSON>", "textContent", "send", "isShowPopup", "imgUlr", "table", "gridData", "scopedSlots", "_u", "fn", "scope", "editData", "row", "id", "dialogFormVisible", "ruleForm", "model", "type_title", "callback", "$$v", "$set", "is_deal", "file_path", "delImage", "slot", "saveData", "staticRenderFns", "isPlay", "style", "autoPlay", "props", "recordFile", "String", "data", "myAuto", "Audio", "methods", "play", "palyEnd", "pause", "addEventListener", "component", "_this", "components", "audioplay", "lvshiss", "activeName", "lists", "Names", "lv<PERSON><PERSON>", "timer", "quli<PERSON><PERSON>", "yuanshiquns", "yuanshiusers", "getInfo", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "postRequest", "lvshis", "yuangongs", "$refs", "validate", "valid", "getData", "uid", "is_daiban", "getList", "e", "_value", "filter", "toLowerCase", "includes", "console", "log", "field", "window", "open", "img", "file", "split", "showClose", "sendImg", "flie", "sendFile", "change", "scrollTop", "sendMessage", "length", "scrollHeight", "loading", "lvshi_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "orther_id", "direction", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "fileName", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted", "sessionStorage", "getItem", "setInterval"], "mappings": "uHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAIO,QAAS,KAAS,CAACL,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,QAAQ,CAACM,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOX,EAAIY,OAAQC,WAAW,WAAWT,YAAY,cAAcU,MAAM,CAAC,KAAO,OAAO,YAAc,MAAMC,SAAS,CAAC,MAASf,EAAIY,QAASP,GAAG,CAAC,OAASL,EAAIgB,cAAc,MAAQ,SAASV,GAAWA,EAAOW,OAAOC,YAAiBlB,EAAIY,OAAON,EAAOW,OAAON,WAAWX,EAAImB,YAAajB,EAAG,MAAM,CAACE,YAAY,qBAAqBC,GAAG,CAAC,MAAQL,EAAIoB,OAAOpB,EAAIqB,SAASnB,EAAG,SAAS,CAACG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIsB,WAAW,QAAQ,CAACtB,EAAIuB,GAAG,QAAQrB,EAAG,SAAS,CAACY,MAAM,CAAC,KAAO,WAAWT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIsB,WAAW,QAAQ,CAACtB,EAAIuB,GAAG,QAAQrB,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACJ,EAAIwB,GAAIxB,EAAIyB,MAAM,SAASC,EAAKC,GAAO,OAAOzB,EAAG,KAAK,CAAC0B,IAAI,MAAQD,EAAMvB,YAAY,cAAcyB,MAAM,CAAEC,OAAQH,IAAU3B,EAAI+B,aAAc1B,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIgC,UAAUL,MAAU,CAACzB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,SAASU,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,IAAMY,EAAKO,YAAaP,EAAKQ,MAAQ,EAAGhC,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKQ,UAAUlC,EAAIqB,OAAOnB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKU,UAAUlC,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKW,gBAAgBnC,EAAG,IAAI,CAACE,YAAY,WAAW,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKY,SAAUZ,EAAKQ,MAAQ,EAAGhC,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKQ,UAAUlC,EAAIqB,YAAWrB,EAAIwB,GAAIxB,EAAIuC,OAAO,SAASb,EAAKC,GAAO,OAAOzB,EAAG,KAAK,CAAC0B,IAAID,EAAMvB,YAAY,cAAcyB,MAAM,CAAEC,OAAQH,IAAU3B,EAAIwC,UAAWnC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIyC,WAAWd,MAAU,CAACzB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,SAASU,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,IAAMY,EAAKO,YAAY/B,EAAG,OAAO,CAACF,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKU,UAAUlC,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKgB,SAASxC,EAAG,IAAI,CAACE,YAAY,WAAW,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKiB,oBAAmB,IAAI,GAAGzC,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,SAAS,CAACE,YAAY,UAAU,CAAQF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,IAAIvB,EAAImC,GAAGnC,EAAIoC,OAAO,WAAoBlC,EAAG,MAAM,CAAC0C,IAAI,OAAOxC,YAAY,kBAAkBC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAON,EAAI6C,kBAAkB7C,EAAIwB,GAAIxB,EAAI8C,MAAM,SAASpB,EAAKC,GAAO,OAAOzB,EAAG,MAAM,CAAC0B,IAAID,EAAMvB,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAKW,kBAAkBnC,EAAG,MAAM,CAAC2B,MAAM,CAAC,cAAe,CAAEkB,QAASrB,EAAKsB,aAAehD,EAAIiD,UAAW,CAAC/C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIuB,GAAG,IAAIvB,EAAImC,GAAGT,EAAKU,OAAO,OAAOlC,EAAG,MAAM,CAAC2B,MAAM,CAAC,iBAAiB,CAAC3B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMY,EAAKwB,YAAYhD,EAAG,MAAM,CAACE,YAAY,aAAa,CAAe,SAAbsB,EAAKyB,KAAiBjD,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMY,EAAKiB,SAAStC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIoD,QAAQ1B,EAAKiB,eAAe3C,EAAIqB,KAAmB,QAAbK,EAAKyB,KAAgBjD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAImC,GAAGT,EAAKiB,SAAS,OAAO3C,EAAIqB,KAAmB,SAAbK,EAAKyB,KAAiBjD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACmD,YAAY,CAAC,QAAU,OAAO,cAAc,WAAW,CAACnD,EAAG,YAAY,CAACY,MAAM,CAAC,WAAaY,EAAKiB,WAAWzC,EAAG,MAAM,CAACF,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAK4B,WAAW,KAAKtD,EAAIqB,KAAmB,QAAbK,EAAKyB,KAAgBjD,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIuD,SAAS7B,EAAKiB,YAAY,CAACzC,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAK8B,MAAM/C,SAASP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGT,EAAK8B,MAAMC,WAAWzD,EAAI0D,GAAG,GAAE,KAAQ1D,EAAIqB,gBAAe,GAAGnB,EAAG,MAAM,CAACmD,YAAY,CAAC,SAAW,WAAW,IAAM,OAAO,MAAQ,OAAO,YAAY,OAAO,OAAS,WAAWhD,GAAG,CAAC,MAAQL,EAAI2D,WAAW,CAAC3D,EAAIuB,GAAG,SAAkB,GAARvB,EAAI4D,GAAU1D,EAAG,MAAM,CAACmD,YAAY,CAAC,SAAW,WAAW,aAAa,OAAO,MAAQ,QAAQ,WAAa,UAAU,OAAS,QAAQ,MAAQ,SAAS,IAAM,QAAQ,CAACnD,EAAG,MAAM,GAAG,CAACA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAIwB,GAAIxB,EAAI6D,QAAQ,SAASnC,EAAKC,GAAO,OAAOzB,EAAG,MAAM,CAAC0B,IAAID,EAAMvB,YAAY,aAAaJ,EAAIwB,GAAIE,EAAKoB,MAAM,SAASnC,EAAMiB,GAAK,OAAO1B,EAAG,MAAM,CAAC0B,IAAIA,GAAK,CAAC1B,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMH,EAAMmD,WAAW5D,EAAG,MAAM,CAACE,YAAY,MAAM,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGxB,EAAMoD,kBAAiB,MAAK,KAAK/D,EAAIwB,GAAIxB,EAAIgE,YAAY,SAAStC,EAAKC,GAAO,OAAOzB,EAAG,MAAM,CAAC0B,IAAID,EAAMvB,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAImC,GAAGT,EAAKuC,QAAQ,OAAO/D,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAIwB,GAAIE,EAAKoB,MAAM,SAASnC,EAAMiB,GAAK,OAAO1B,EAAG,MAAM,CAAC0B,IAAIA,EAAIxB,YAAY,aAAa,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMH,EAAMsB,YAAY/B,EAAG,MAAM,CAACE,YAAY,MAAM,CAACJ,EAAIuB,GAAGvB,EAAImC,GAAGxB,EAAMyB,eAAc,SAAQ,KAAKpC,EAAIqB,KAAKnB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,mBAAmB,IAAM,IAAIT,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAO4D,kBAAyBlE,EAAImE,SAASC,MAAM,KAAMC,eAAenE,EAAG,MAAM,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOX,EAAIO,OAAQM,WAAW,WAAWT,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYJ,EAAIwB,GAAIxB,EAAIsE,WAAW,SAAS5C,EAAKC,GAAO,OAAOzB,EAAG,MAAM,CAAC0B,IAAID,EAAMvB,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIuE,SAAS7C,MAAS,CAAC1B,EAAIuB,GAAG,IAAIvB,EAAImC,GAAGT,GAAM,UAAS,OAAOxB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAad,EAAIwE,gBAAgB,CAACtE,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,qBAAqB,IAAM,SAAS,GAAGZ,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIyE,WAAW,YAAY,CAACvE,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAad,EAAI0E,eAAe,gBAAgB1E,EAAI2E,eAAe,CAACzE,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,kBAAkB,IAAM,SAAS,GAAGZ,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,MAAQL,EAAI4E,SAAS,CAAC1E,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,iBAAiB,IAAM,QAAQZ,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,MAAQL,EAAI6E,cAAc,CAAC3E,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,kBAAkB,IAAM,UAAUZ,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAACM,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOX,EAAI8E,YAAajE,WAAW,gBAAgBC,MAAM,CAAC,YAAc,UAAUC,SAAS,CAAC,MAASf,EAAI8E,aAAczE,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOW,OAAOC,YAAiBlB,EAAI8E,YAAYxE,EAAOW,OAAON,aAAYT,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACG,GAAG,CAAC,MAAQL,EAAI+E,OAAO,CAAC/E,EAAIuB,GAAG,qBAAqBrB,EAAG,MAAM,CAACE,YAAY,YAAYyB,MAAM,CAAE,aAAc7B,EAAIgF,cAAe,CAAC9E,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMd,EAAIiF,OAAO,IAAM,QAAQ/E,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACG,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAIgF,aAAc,KAAS,CAAChF,EAAIuB,GAAG,aAAarB,EAAG,YAAY,CAACY,MAAM,CAAC,MAAQ,OAAO,QAAUd,EAAIkF,MAAM,UAAY,MAAM,KAAO,OAAO7E,GAAG,CAAC,iBAAiB,SAASC,GAAQN,EAAIkF,MAAM5E,KAAU,CAACJ,EAAG,WAAW,CAACY,MAAM,CAAC,KAAOd,EAAImF,WAAW,CAACjF,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,cAAc,MAAQ,OAAO,MAAQ,SAASZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,QAAQ,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,OAAO,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,aAAa,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,gBAAgB,MAAQ,QAAQZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAMsE,YAAYpF,EAAIqF,GAAG,CAAC,CAACzD,IAAI,UAAU0D,GAAG,SAASC,GAAO,MAAO,CAACrF,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,OAAO,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIwF,SAASD,EAAME,IAAIC,OAAO,CAAC1F,EAAIuB,GAAG,kBAAkB,IAAI,GAAGrB,EAAG,YAAY,CAACY,MAAM,CAAC,MAAQd,EAAIoC,MAAQ,KAAK,QAAUpC,EAAI2F,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOtF,GAAG,CAAC,iBAAiB,SAASC,GAAQN,EAAI2F,kBAAkBrF,KAAU,CAACJ,EAAG,UAAU,CAAC0C,IAAI,WAAW9B,MAAM,CAAC,MAAQd,EAAI4F,WAAW,CAAC1F,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,IAAI+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASE,WAAYC,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,aAAcI,IAAMnF,WAAW,0BAA0B,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,IAAI+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASxD,MAAO2D,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,QAASI,IAAMnF,WAAW,qBAAqB,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,GAAG,KAAO,WAAW,KAAO,GAAG+E,MAAM,CAAClF,MAAOX,EAAI4F,SAAStD,KAAMyD,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,OAAQI,IAAMnF,WAAW,oBAAoB,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,MAAM,CAACA,EAAG,WAAW,CAACY,MAAM,CAAC,MAAQ,GAAG+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASM,QAASH,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,UAAWI,IAAMnF,WAAW,qBAAqB,CAACb,EAAIuB,GAAG,SAASrB,EAAG,WAAW,CAACY,MAAM,CAAC,MAAQ,GAAG+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASM,QAASH,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,UAAWI,IAAMnF,WAAW,qBAAqB,CAACb,EAAIuB,GAAG,UAAU,KAA8B,GAAxBvB,EAAI4F,SAASM,SAAqC,GAArBlG,EAAI4F,SAASzC,KAAWjD,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,QAAQ,KAAO,cAAc,CAACZ,EAAG,WAAW,CAACE,YAAY,WAAWU,MAAM,CAAC,UAAW,GAAM+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASO,UAAWJ,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,YAAaI,IAAMnF,WAAW,wBAAwBX,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACA,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAad,EAAI0E,iBAAiB,CAAC1E,EAAIuB,GAAG,WAAW,GAAIvB,EAAI4F,SAASO,UAAWjG,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,UAAUT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIoG,SAASpG,EAAI4F,SAASO,UAAW,gBAAgB,CAACnG,EAAIuB,GAAG,QAAQvB,EAAIqB,MAAM,IAAI,GAAGrB,EAAIqB,KAA8B,GAAxBrB,EAAI4F,SAASM,SAAqC,GAArBlG,EAAI4F,SAASzC,KAAWjD,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAG+E,MAAM,CAAClF,MAAOX,EAAI4F,SAASjD,QAASoD,SAAS,SAAUC,GAAMhG,EAAIiG,KAAKjG,EAAI4F,SAAU,UAAWI,IAAMnF,WAAW,uBAAuB,GAAGb,EAAIqB,MAAM,GAAGnB,EAAG,MAAM,CAACE,YAAY,gBAAgBU,MAAM,CAAC,KAAO,UAAUuF,KAAK,UAAU,CAACnG,EAAG,YAAY,CAACG,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI2F,mBAAoB,KAAS,CAAC3F,EAAIuB,GAAG,SAASrB,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,WAAWT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIsG,cAAc,CAACtG,EAAIuB,GAAG,UAAU,IAAI,IAAI,IAE/8UgF,EAAkB,CAAC,WAAY,IAAIvG,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAM,yB,UCFlI,MAAMwD,EAAU,CACf,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MAMcA,QC1DXvE,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,IAAI,CAAC2B,MAAoB,GAAd7B,EAAIwG,OAAkB,qBAAuB,sBAAsBnD,YAAY,CAAC,OAAS,UAAU,eAAe,OAAO,aAAa,MAAM,YAAY,QAAQoD,MAAqB,GAAdzG,EAAIwG,OAAkB,GAAK,cAAe1F,MAAM,CAAC,KAAO,aAAaT,GAAG,CAAC,MAAQL,EAAI0G,UAAUL,KAAK,eAExVE,EAAkB,GCcP,GACfI,MAAA,CACAC,WAAA,CACAzD,KAAA0D,SAGApG,KAAA,YACAqG,OACA,OACAN,QAAA,EACAO,OAAA,IAAAC,MAAA,KAAAJ,cAGAK,QAAA,CACAP,WACA,KAAAF,QAAA,KAAAA,OACA,KAAAA,QACA,KAAAO,OAAAG,OACA,KAAAC,YAEA,KAAAJ,OAAAK,QACA,KAAAD,YAGAA,UACA,KAAAJ,OAAAM,iBAAA,aACA,KAAAb,QAAA,OC1CkV,I,YCO9Uc,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCyUf,IAAAC,EAGe,OACf9G,KAAA,OACA+G,WAAA,CAAAC,aACAX,OACA,OACAjD,OAAA,GACA6D,QAAA,GACA1D,WAAA,GACAkB,OAAA,EACAC,SAAA,GACAS,SAAA,GACAD,mBAAA,EACArB,YAEA9B,SAAA,EACAmF,WAAA,QACA/G,OAAA,GACAkB,QAAA,EACAmD,OAAA,GACAhC,OAAA,EACAyC,GAAA,EACAvE,aAAA,EACAgC,KAAA,GAEAyE,MAAA,GACAhE,IAAA,EACAiE,MAAA,GACA7C,aAAA,EACAF,YAAA,GACAtC,SAAA,EACAsF,QAAA,IACA7F,SAAA,GACAkE,UAAA,GACArD,KAAA,GACAiF,MAAA,GACAxF,MAAA,GACAd,KAAA,GACAM,YAAA,EACAiG,QAAA,GACAzH,QAAA,EACA6B,MAAA,GACA6F,YAAA,GACAC,aAAA,KAGAjB,QAAA,CACAzB,SAAAE,GAEA,GAAAA,EACA,KAAAyC,QAAAzC,GAEA,KAAAE,SAAA,CACAxD,MAAA,GACAE,KAAA,KAIA8F,eAAAC,GACA,KAAAA,EAAAC,MACA,KAAAC,SAAAC,QAAA,QACA,KAAA5C,SAAA,aAAAyC,EAAAvB,KAAA2B,KAEA,KAAAF,SAAAG,MAAAL,EAAAM,MAGAR,QAAAzC,GACA,IAAA6B,EAAA,KACA,KAAAqB,WAAA,oBAAAlD,GAAAmD,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAA3B,SAAAkD,EAAAhC,KACAS,EAAA5B,mBAAA,GAEA4B,EAAAgB,SAAA,CACApF,KAAA,QACA4F,QAAAD,EAAAH,SAKAhF,WACA4D,EAAA3D,IAAA2D,EAAA3D,GACA2D,EAAAyB,YAAA,wBAAAtD,GAAA6B,EAAA7B,KAAAmD,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAA1D,OAAAiF,EAAAhC,KAAAvE,MACAgF,EAAAG,QAAAoB,EAAAhC,KAAAmC,OACA1B,EAAAvD,WAAA8E,EAAAhC,KAAAoC,cAKA5C,WACA,IAAAiB,EAAA,KACA,KAAA4B,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAL,YAAA,qBAAApD,UAAAiD,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAA,CACApF,KAAA,UACA4F,QAAAD,EAAAH,MAEA,KAAAW,UACA/B,EAAA5B,mBAAA,GAEA4B,EAAAgB,SAAA,CACApF,KAAA,QACA4F,QAAAD,EAAAH,WASA9D,cACA,IAAA0E,EAAA,KAAA9H,KAAA,KAAAM,aAAA,OACAwF,EAAArC,OAAA,EACAqC,EAAAyB,YAAA,qBAAAO,QAAAV,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAApC,SAAA2D,EAAAhC,SAIAxF,WAAAkI,GAEAjC,EACAyB,YAAA,gBAAAQ,cACAX,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAA9F,KAAAqH,EAAAhC,KACAS,EAAAU,YAAAa,EAAAhC,KACAS,EAAA/E,UAAA,EACA+E,EAAAkC,cAIAzI,cAAA0I,GACA,IAAAjI,EAAA8F,EAAAU,YACA1F,EAAAgF,EAAAW,aACAtH,EAAA8I,EAAAzI,OAAA0I,OAEApC,EAAA9F,OAAAmI,OAAA9C,IAAA,GAAAA,EAAA1E,MAAAxB,WACA2G,EAAAhF,QAAAqH,OACA9C,IACAlG,GAAAkG,EAAA1E,MAAAyH,cAAAC,SAAAlJ,EAAAiJ,iBAGAjF,SACA,IAAAc,EAAA,KAAAjE,KAAA,KAAAM,aAAA,MACAyH,EAAA,QAAA/H,KAAA,KAAAM,aAAA,iBACAwF,EACAyB,YAAA,gBAAAtD,KAAA8D,cACAX,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAA9F,KAAA,KAAAM,aAAA,aAAAyH,EACAjC,EAAAgB,SAAAC,QAAAM,EAAAH,MAEApB,EAAAgB,SAAAG,MAAAI,EAAAH,QAIAxE,WACA,KAAA5D,QAAA,KAAAA,OACAwJ,QAAAC,IAAA,+BAEAvF,WAAAwF,GACA,KAAA9G,KAAA8G,GAEA1G,SAAAkF,GACAyB,OAAAC,KAAA1B,EAAA,WAGArF,QAAAgH,GACA,KAAAnF,OAAAmF,EACA,KAAApF,aAAA,EACA+E,QAAAC,IAAA,aAAAI,IAEAzF,aAAA0F,GACA,IAAAlH,EAAAkH,EAAAlH,KAEA,GADA4G,QAAAC,IAAA7G,EAAA,QAEA,QAAAkH,EAAAlH,KAAAmH,MAAA,SACA,SAAAD,EAAAlH,KAAAmH,MAAA,SACA,QAAAD,EAAAlH,KAAAmH,MAAA,SACA,QAAAD,EAAAlH,KAAAmH,MAAA,SACA,QAAAD,EAAAlH,KAAAmH,MAAA,SACA,SAAAD,EAAAlH,KAAAmH,MAAA,SACA,SAAAD,EAAAlH,KAAAmH,MAAA,QAOA,OALA,KAAA/B,SAAA,CACAgC,WAAA,EACAxB,QAAA,mDACA5F,KAAA,WAEA,GAGAqB,cAAA6D,GACA,IAAAd,EAAA,KACAwC,QAAAC,IAAA3B,GACA,KAAAA,EAAAC,KACAf,EAAAiD,QAAAnC,EAAAvB,KAAA2B,KAEAlB,EAAAgB,SAAAG,MAAAL,EAAAM,MAGAjE,eAAA2D,EAAAoC,GACA,KAAApC,EAAAC,KACAf,EAAAmD,SAAArC,EAAAvB,KAAA2B,IAAAgC,GAEA,KAAAlC,SAAA,CACAgC,WAAA,EACAxB,QAAA,wCACA5F,KAAA,WAIAV,WAAAd,GACA,KAAAa,SAAAb,EACA,KAAAI,aAAA,EACAwF,EAAA3D,IAAA,EACA2D,EAAAkC,WAMAzH,UAAAL,GACA,KAAAa,UAAA,EACA,KAAAT,YAAAJ,EACA4F,EAAA9F,KAAAE,GAAAO,MAAA,EACAqF,EAAA3D,IAAA,EACA2D,EAAAkC,WAMAlF,SAAA7C,GACA,KAAAoD,aAAApD,GAEAiJ,OAAAjB,GACA,KAAA9I,OAAA,KAAAO,aAAA,EACA,KAAAA,aAAA,GAEAC,MACA,KAAAR,OAAA,GACA,KAAAO,aAAA,GAEA0B,aAAA6G,GACA,QAAAP,MAAArG,KAAA8H,WACAb,QAAAC,IAAA,aAIAjF,OACAwC,EAAAsD,YAAAtD,EAAAzC,aACAyC,EAAAzC,YAAA,IAMA2E,UACA,OAAAlC,EAAA/E,SAAA,CACA,IAAAkD,EAAA6B,EAAAhF,MAAAgF,EAAA/E,UAAAkD,GACA6B,EAAAnF,MAAAmF,EAAAhF,MAAAgF,EAAA/E,UAAAJ,MAEAmF,EAAAyB,YAAA,kBAAAO,IAAA7D,IAAAmD,KAAAC,IACA,KAAAA,EAAAR,MACAQ,EAAAhC,KAAAgE,OAAA,IACAvD,EAAAzE,KAAAgG,EAAAhC,KAEAS,EAAA4B,MAAArG,KAAA8H,UAAArD,EAAA4B,MAAArG,KAAAiI,cAGAxD,EAAAyD,SAAA,QAEA,CACA,IAAAtF,EAAA6B,EAAA9F,KAAA8F,EAAAxF,aAAA2D,GACAxD,EACA,EAAAqF,EAAA9F,KAAA8F,EAAAxF,aAAAwH,IAAAuB,OACA,EAAAvD,EAAA9F,KAAA8F,EAAAxF,aAAAkJ,SAAAH,OACA,EAAAvD,EAAA9F,KAAA8F,EAAAxF,aAAAiB,YAAA8H,OACAvD,EAAA7B,KACAqE,QAAAC,IAAAzC,EAAA7B,IAEA6B,EAAAnF,MAAAmF,EAAA9F,KAAA8F,EAAAxF,aAAAK,MAAA,IAAAF,EAAA,IACAqF,EAAAyB,YAAA,qBAAAkC,OAAAxF,IAAAmD,KAAAC,IACA,KAAAA,EAAAR,OACAQ,EAAAhC,KAAAgE,OAAA,GACAvD,EAAAzE,KAAAgG,EAAAhC,KACAS,EAAA4B,MAAArG,KAAA8H,UAAArD,EAAA4B,MAAArG,KAAAiI,cAEAxD,EAAAzE,KAAA,GAGAqI,WACA,SAAAhC,MAAArG,KAAA8H,UAAA,KAAAzB,MAAArG,KAAAiI,aACA,IAGAxD,EAAAyD,SAAA,MAIAI,cACA,OAAA7D,EAAA/E,SAAA,CACA,IAAA+G,EAAAhC,EAAAhF,MAAAgF,EAAA/E,UAAAkD,GACA6B,EAAAnF,MAAAmF,EAAAhF,MAAAgF,EAAA/E,UAAAJ,MAEA,IAAAsD,EAAA,EACA6B,EAAAzE,KAAAgI,OAAA,IACApF,EAAA6B,EAAAzE,KAAAyE,EAAAzE,KAAAgI,OAAA,GAAApF,GACA6B,EACAyB,YAAA,wBAAAO,MAAA7D,OACAmD,KAAAC,IACAvB,EAAA8D,UACA,KAAAvC,EAAAR,MACAQ,EAAAhC,KAAAgE,OAAA,IACAvD,EAAAzE,KAAAwI,KAAAxC,EAAAhC,MAGAqE,WACA,IACA,KAAAhC,MAAArG,KAAA8H,UACA,KAAAzB,MAAArG,KAAAiI,aACA,MAIAxD,EAAAyD,SAAA,SAGA,CACA,IAAAE,EAAA3D,EAAA9F,KAAA8F,EAAAxF,aAAA2D,GACAxD,EACA,EAAAqF,EAAA9F,KAAA8F,EAAAxF,aAAAkJ,SAAAH,OACA,EAAAvD,EAAA9F,KAAA8F,EAAAxF,aAAAiB,YAAA8H,OACA,EAEAvD,EAAAnF,MAAAmF,EAAA9F,KAAA8F,EAAAxF,aAAAK,MAAA,IAAAF,EAAA,IACA,IAAAwD,EAAA,EACA6B,EAAAzE,KAAAgI,OAAA,GACApF,EAAA6B,EAAAzE,KAAAyE,EAAAzE,KAAAgI,OAAA,GAAApF,GACA6B,EACAyB,YAAA,wBAAAkC,SAAAxF,OACAmD,KAAAC,IACAvB,EAAA8D,UACA,KAAAvC,EAAAR,OACAf,EAAAzE,KAAAwI,KAAAxC,EAAAhC,MAEAqE,WACA,IACA5D,EAAA4B,MAAArG,KAAA8H,UACArD,EAAA4B,MAAArG,KAAAiI,aACA,MAGAxD,EAAAyD,SAAA,MAGAtF,EAAA,EACA6B,EACAyB,YAAA,wBAAAkC,SAAAxF,OACAmD,KAAAC,IACAvB,EAAA8D,UACA,KAAAvC,EAAAR,OACAf,EAAAzE,KAAAwI,KAAAxC,EAAAhC,MAEAqE,WACA,IACA5D,EAAA4B,MAAArG,KAAA8H,UACArD,EAAA4B,MAAArG,KAAAiI,aACA,MAGAxD,EAAAyD,SAAA,OAKAH,YAAAlI,GACA,OAAA4E,EAAA/E,SAAA,CACA,IAAAkD,EAAA6B,EAAAhF,MAAAgF,EAAA/E,UAAAkD,GACA6F,EAAA,EACAhE,EACAyB,YAAA,qBACAO,IAAA7D,EACA8F,UAAA,OACArI,KAAA,OACAR,UACA4I,cAEA1C,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,WAGA,CACApB,EAAA9F,KAAA8F,EAAAxF,aAAAwH,IAAA,IACA2B,EAAA3D,EAAA9F,KAAA8F,EAAAxF,aAAA2D,GACA6B,EACAyB,YAAA,wBAEAwC,UAAA,OACArI,KAAA,OACAR,UACAuI,WAEArC,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,SAKA+B,SAAA/H,EAAAa,GACA,OAAA+D,EAAA/E,SAAA,CAEA,IAAA+I,EAAA,EACAhE,EACAyB,YAAA,qBAEAwC,UAAA,OACArI,KAAA,OACAR,UACA4I,YACA/H,UAEAqF,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,WAGA,CAEA,IAAAuC,EAAA3D,EAAA9F,KAAA8F,EAAAxF,aAAA2D,GACA6B,EACAyB,YAAA,wBAEAwC,UAAA,OACArI,KAAA,OACAR,UACAuI,SACA1H,UAEAqF,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,SAKA6B,QAAA7H,GACA,OAAA4E,EAAA/E,SAAA,CACA,IAAAkD,EAAA6B,EAAAhF,MAAAgF,EAAA/E,UAAAkD,GACA6F,EAAA,EACAhE,EACAyB,YAAA,qBACAO,IAAA7D,EACA8F,UAAA,OACArI,KAAA,QACAR,UACA4I,cAEA1C,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,WAGA,CACA,IAAAY,EAAAhC,EAAA9F,KAAA8F,EAAAxF,aAAAwH,IACA2B,EAAA3D,EAAA9F,KAAA8F,EAAAxF,aAAA2D,GACA6B,EACAyB,YAAA,wBACAO,MACAiC,UAAA,OACArI,KAAA,QACAR,UACAuI,WAEArC,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAAgB,SAAAG,MAAAI,EAAAH,SAKA8C,cACAlE,EAAAyB,YAAA,qBAAAH,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAAhF,MAAAuG,EAAAhC,KACAS,EAAAW,aAAAY,EAAAhC,SAIA4E,SACAnE,EAAAyB,YAAA,gBAAAH,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAA9F,KAAAqH,EAAAhC,KACAS,EAAAU,YAAAa,EAAAhC,KACAS,EAAA/E,UAAA,EAEA2I,WAAA,KACA5D,EAAAkC,WACA,UAIA4B,UACA9D,EAAAyB,YAAA,gBAAAH,KAAAC,IACA,KAAAA,EAAAR,OACAf,EAAA9F,KAAAqH,EAAAhC,KACAS,EAAAU,YAAAa,EAAAhC,KACAS,EAAA/E,UAAA,MAIAmJ,cACA,IAAApE,EAAA,KAEAqE,SAAAC,UAAAnC,IACA,IAAAoC,EAAA5B,OAAA6B,MAAAC,QAGA,KAAAF,GACAvE,EAAAxC,SAIAqB,SAAAiE,EAAA4B,GACA,IAAA1E,EAAA,KACAA,EAAAqB,WAAA,6BAAAyB,GAAAxB,KAAAC,IACA,KAAAA,EAAAR,MACAf,EAAA3B,SAAAqG,GAAA,GAEA1E,EAAAgB,SAAAC,QAAA,UAEAjB,EAAAgB,SAAAG,MAAAI,EAAAH,SAKAuD,gBACAnC,QAAAC,IAAA,OACAmC,cAAA,KAAApE,QAEAqE,UACA7E,EAAA,KACAA,EAAAmE,SACAnE,EAAAkE,cACAlE,EAAAtE,OAAAiH,OAAAmC,eAAAC,QAAA,QACA/E,EAAAQ,MAAAwE,YAAA,KACAhF,EAAA6D,eACA,MACA7D,EAAAoE,gBC14B2W,ICQvW,G,UAAY,eACd,EACA5L,EACAwG,GACA,EACA,KACA,KACA,OAIa,e,kECnBf", "file": "js/chunk-1c0897a3.f77938e1.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"body-div\",on:{\"click\":function($event){_vm.isEmji = false}}},[_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"msglist\"},[_c('div',{staticClass:\"wrapper\"},[_c('div',{staticClass:\"search-wrapper\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.search),expression:\"search\"}],staticClass:\"searchInput\",attrs:{\"type\":\"text\",\"placeholder\":\"搜索\"},domProps:{\"value\":(_vm.search)},on:{\"change\":_vm.changeKeyword,\"input\":function($event){if($event.target.composing)return;_vm.search=$event.target.value}}}),(_vm.isShowSeach)?_c('div',{staticClass:\"searchInput-delete\",on:{\"click\":_vm.del}}):_vm._e()])]),_c('el-tag',{on:{\"click\":function($event){return _vm.showDaiban('2')}}},[_vm._v(\"群聊\")]),_c('el-tag',{attrs:{\"type\":\"success\"},on:{\"click\":function($event){return _vm.showDaiban('1')}}},[_vm._v(\"代办\")]),_c('ul',{staticClass:\"msg-left-box\"},[_vm._l((_vm.quns),function(item,index){return _c('li',{key:'qun' + index,staticClass:\"sessionlist\",class:{ active: index === _vm.quliaoIndex },on:{\"click\":function($event){return _vm.changeQun(index)}}},[_c('div',{staticClass:\"list-left\"},[_c('img',{staticClass:\"avatar\",attrs:{\"width\":\"38\",\"height\":\"38\",\"src\":item.pic_path}}),(item.count > 0)?_c('span',[_vm._v(_vm._s(item.count))]):_vm._e()]),_c('div',{staticClass:\"list-right\"},[_c('p',{staticClass:\"name\"},[_vm._v(_vm._s(item.title))]),_c('span',{staticClass:\"time\"},[_vm._v(_vm._s(item.create_time))]),_c('p',{staticClass:\"lastmsg\"},[_vm._v(_vm._s(item.desc))]),(item.count > 0)?_c('p',{staticClass:\"number-badge\"},[_vm._v(_vm._s(item.count))]):_vm._e()])])}),_vm._l((_vm.users),function(item,index){return _c('li',{key:index,staticClass:\"sessionlist\",class:{ active: index === _vm.selectId },on:{\"click\":function($event){return _vm.redSession(index)}}},[_c('div',{staticClass:\"list-left\"},[_c('img',{staticClass:\"avatar\",attrs:{\"width\":\"42\",\"height\":\"42\",\"src\":item.pic_path}}),_c('span',[_vm._v(\"99\")])]),_c('div',{staticClass:\"list-right\"},[_c('p',{staticClass:\"name\"},[_vm._v(_vm._s(item.title))]),_c('span',{staticClass:\"time\"},[_vm._v(_vm._s(item.time))]),_c('p',{staticClass:\"lastmsg\"},[_vm._v(_vm._s(item.content))])])])})],2)],1),_c('div',{staticClass:\"chatbox\"},[_c('div',{staticClass:\"message\"},[_c('header',{staticClass:\"header\"},[(true)?_c('div',{staticClass:\"friendname\"},[_vm._v(\" \"+_vm._s(_vm.title)+\" \")]):_vm._e()])]),_c('div',{ref:\"list\",staticClass:\"message-wrapper\",on:{\"scroll\":function($event){return _vm.handleScroll()}}},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"msg-box\"},[_c('div',{staticClass:\"msg-time\"},[_c('span',[_vm._v(_vm._s(item.create_time))])]),_c('div',{class:['chatMsg-box', { oneself: item.yuangong_id == _vm.yon_id }]},[_c('div',{staticClass:\"chat-name\"},[_vm._v(\" \"+_vm._s(item.title)+\" \")]),_c('div',{class:['chatMsg-flex']},[_c('div',{staticClass:\"flex-view\"},[_c('img',{attrs:{\"src\":item.avatar}})]),_c('div',{staticClass:\"flex-view\"},[(item.type == 'image')?_c('div',{staticClass:\"chatMsg-img\"},[_c('img',{attrs:{\"src\":item.content},on:{\"click\":function($event){return _vm.openImg(item.content)}}})]):_vm._e(),(item.type == 'text')?_c('div',{staticClass:\"chatMsg-content\"},[_vm._v(\" \"+_vm._s(item.content)+\" \")]):_vm._e(),(item.type == 'voice')?_c('div',{staticClass:\"chatMsg-content\"},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[_c('audioplay',{attrs:{\"recordFile\":item.content}}),_c('div',[_vm._v(_vm._s(item.datas))])],1)]):_vm._e(),(item.type == 'file')?_c('div',{staticClass:\"file-box\"},[_c('div',{staticClass:\"file-flex\",on:{\"click\":function($event){return _vm.openFile(item.content)}}},[_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(item.files.name))]),_c('div',{staticClass:\"file-size\"},[_vm._v(_vm._s(item.files.size))])]),_vm._m(0,true)]):_vm._e()])])])])}),0),_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"20px\",\"font-size\":\"32px\",\"cursor\":\"pointer\"},on:{\"click\":_vm.quanyuan}},[_vm._v(\"···\")]),(_vm.la==true)?_c('div',{staticStyle:{\"position\":\"absolute\",\"overflow-y\":\"auto\",\"width\":\"200px\",\"background\":\"#f2f2f2\",\"height\":\"690px\",\"right\":\"-200px\",\"top\":\"0px\"}},[_c('div',{},[_c('div',{staticClass:\"chat-list-box\"},[_c('div',{staticClass:\"chat-list-title\"},[_vm._v(\" 用户 \")]),_c('div',{staticClass:\"chat-list\"},_vm._l((_vm.userss),function(item,index){return _c('div',{key:index,staticClass:\"chat-flex\"},_vm._l((item.list),function(value,key){return _c('div',{key:key},[_c('img',{attrs:{\"src\":value.headimg}}),_c('div',{staticClass:\"sl\"},[_vm._v(_vm._s(value.nickname))])])}),0)}),0)]),_vm._l((_vm.yuangongss),function(item,index){return _c('div',{key:index,staticClass:\"chat-list-box\"},[_c('div',{staticClass:\"chat-list-title\"},[_vm._v(\" \"+_vm._s(item.zhiwei)+\" \")]),_c('div',{staticClass:\"chat-list\"},_vm._l((item.list),function(value,key){return _c('div',{key:key,staticClass:\"chat-flex\"},[_c('img',{attrs:{\"src\":value.pic_path}}),_c('div',{staticClass:\"sl\"},[_vm._v(_vm._s(value.title))])])}),0)])})],2)]):_vm._e(),_c('div',{staticClass:\"input-box\"},[_c('div',{staticClass:\"workbar-box\"},[_c('div',{staticClass:\"upload-emji\"},[_c('img',{attrs:{\"src\":\"img/biaoqing.png\",\"alt\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.openEmji.apply(null, arguments)}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isEmji),expression:\"isEmji\"}],staticClass:\"emji-box\"},[_c('div',{staticClass:\"biao-box\"},_vm._l((_vm.emojiData),function(item,index){return _c('div',{key:index,staticClass:\"biao-flex\",on:{\"click\":function($event){return _vm.getEmoji(item)}}},[_vm._v(\" \"+_vm._s(item)+\" \")])}),0)])]),_c('div',{staticClass:\"upload-file\"},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_c('img',{attrs:{\"src\":\"img/insert_img.png\",\"alt\":\"\"}})])],1),_c('div',{staticClass:\"upload-file\",on:{\"click\":function($event){return _vm.changeFile('image')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess1,\"before-upload\":_vm.beforeUpload}},[_c('img',{attrs:{\"src\":\"img/wenjian.png\",\"alt\":\"\"}})])],1),_c('div',{staticClass:\"upload-file\",on:{\"click\":_vm.daiban}},[_c('img',{attrs:{\"src\":\"img/daiban.png\",\"alt\":\"\"}})]),_c('div',{staticClass:\"upload-file\",on:{\"click\":_vm.showgongdan}},[_c('img',{attrs:{\"src\":\"img/gongdan.png\",\"alt\":\"\"}})])]),_c('div',{staticClass:\"input-text\"},[_c('textarea',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.textContent),expression:\"textContent\"}],attrs:{\"placeholder\":\"您想说什么？\"},domProps:{\"value\":(_vm.textContent)},on:{\"input\":function($event){if($event.target.composing)return;_vm.textContent=$event.target.value}}})]),_c('div',{staticClass:\"input-btn\"},[_c('span',{on:{\"click\":_vm.send}},[_vm._v(\"发送Enter\")])])])])]),_c('div',{staticClass:\"img-popup\",class:{ 'show-popup': _vm.isShowPopup }},[_c('div',[_c('div',{staticClass:\"img-div\"},[_c('img',{attrs:{\"src\":_vm.imgUlr,\"alt\":\"\"}})]),_c('div',{staticClass:\"close\"},[_c('span',{on:{\"click\":function($event){_vm.isShowPopup = false}}},[_vm._v(\"×\")])])])]),_c('el-drawer',{attrs:{\"title\":\"客户工单\",\"visible\":_vm.table,\"direction\":\"rtl\",\"size\":\"40%\"},on:{\"update:visible\":function($event){_vm.table=$event}}},[_c('el-table',{attrs:{\"data\":_vm.gridData}},[_c('el-table-column',{attrs:{\"property\":\"create_time\",\"label\":\"下单日期\",\"width\":\"150\"}}),_c('el-table-column',{attrs:{\"property\":\"title\",\"label\":\"需求标题\"}}),_c('el-table-column',{attrs:{\"property\":\"desc\",\"label\":\"需求描述\"}}),_c('el-table-column',{attrs:{\"property\":\"type_title\",\"label\":\"下单类型\"}}),_c('el-table-column',{attrs:{\"property\":\"is_deal_title\",\"label\":\"状态\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"完成制作\")])]}}])})],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm}},[_c('el-form-item',{attrs:{\"label\":\"工单类型\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.type_title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"type_title\", $$v)},expression:\"ruleForm.type_title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单标题\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单描述\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"制作状态\"}},[_c('div',[_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"已完成\")]),_c('el-radio',{attrs:{\"label\":1},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"处理中\")])],1)]),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type == 2)?_c('el-form-item',{attrs:{\"label\":\"请上传文件\",\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess1}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1):_vm._e(),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type != 2)?_c('el-form-item',{attrs:{\"label\":\"内容回复\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"file-flex\"},[_c('img',{attrs:{\"src\":\"img/wenjian.png\"}})])\n}]\n\nexport { render, staticRenderFns }", "const emojiData=[\r\n\t'😀',\r\n\t'😃',\r\n\t'😄',\r\n\t'😆',\r\n\t'😅',\r\n\t'🤣',\r\n\t'🙂',\r\n\t'😇',\r\n\t'🤩',\r\n\t'🤩',\r\n\t'😘',\r\n\t'😗',\r\n\t'😚',\r\n\t'😋',\r\n\t'😛',\r\n\t'🤪',\r\n\t'🙁',\r\n\t'😴',\r\n\t'😷',\r\n\t'🤮',\r\n\t'🥵',\r\n\t'🥵',\r\n\t'🤢',\r\n\t'🤢',\r\n\t'😦',\r\n\t'😰',\r\n\t'😥',\r\n\t'😱',\r\n\t'😈',\r\n\t'💀',\r\n\t'🤡',\r\n\t'👺',\r\n\t'👿',\r\n\t'😠',\r\n\t'😡',\r\n\t'😤',\r\n\t'🥱',\r\n\t'👻',\r\n\t'👽',\r\n\t'😺',\r\n\t'😸',\r\n\t'😹',\r\n\t'😻',\r\n\t'😼',\r\n\t'😽',\r\n\t'🙀',\r\n\t'😿',\r\n\t'🙈',\r\n\t'🙉',\r\n\t'🙊',\r\n\t'💋',\r\n\t'💌'\r\n\t\r\n\t\r\n\t\r\n\t\r\n]\r\nexport default emojiData", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('i',{class:_vm.isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause',staticStyle:{\"cursor\":\"pointer\",\"margin-right\":\"10px\",\"margin-top\":\"3px\",\"font-size\":\"25px\"},style:(_vm.isPlay == false ? '' : 'color: red;'),attrs:{\"slot\":\"reference\"},on:{\"click\":_vm.autoPlay},slot:\"reference\"})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <i\r\n    slot=\"reference\"\r\n    :style=\"isPlay == false ? '' : 'color: red;'\"\r\n    :class=\"isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause'\"\r\n    @click=\"autoPlay\"\r\n    style=\"\r\n      cursor: pointer;\r\n      margin-right: 10px;\r\n      margin-top: 3px;\r\n      font-size: 25px;\r\n    \"\r\n  ></i>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    recordFile: {\r\n      type: String,\r\n    },\r\n  },\r\n  name: \"audioplay\",\r\n  data() {\r\n    return {\r\n      isPlay: false,\r\n      myAuto: new Audio(this.recordFile),\r\n    };\r\n  },\r\n  methods: {\r\n    autoPlay() {\r\n      this.isPlay = !this.isPlay;\r\n      if (this.isPlay) {\r\n        this.myAuto.play();\r\n        this.palyEnd();\r\n      } else {\r\n        this.myAuto.pause();\r\n        this.palyEnd();\r\n      }\r\n    },\r\n    palyEnd() {\r\n      this.myAuto.addEventListener(\"ended\", () => {\r\n        this.isPlay = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped></style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./audioplay.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./audioplay.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./audioplay.vue?vue&type=template&id=51f04c58&scoped=true\"\nimport script from \"./audioplay.vue?vue&type=script&lang=js\"\nexport * from \"./audioplay.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51f04c58\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"body-div\" @click=\"isEmji = false\">\r\n    <div class=\"content\">\r\n      <div class=\"msglist\">\r\n        <!-- 搜索 -->\r\n        <div class=\"wrapper\">\r\n          <div class=\"search-wrapper\">\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"searchInput\"\r\n              placeholder=\"搜索\"\r\n              @change=\"changeKeyword\"\r\n            />\r\n            <div\r\n              v-if=\"isShowSeach\"\r\n              class=\"searchInput-delete\"\r\n              @click=\"del\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <el-tag @click=\"showDaiban('2')\">群聊</el-tag>\r\n        <el-tag type=\"success\" @click=\"showDaiban('1')\">代办</el-tag>\r\n\r\n        <!-- 好友列表 -->\r\n        <ul class=\"msg-left-box\">\r\n          <!--工作群-->\r\n          <li\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === quliaoIndex }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"38\" height=\"38\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.create_time }}</span>\r\n              <p class=\"lastmsg\">{{ item.desc }}</p>\r\n              <p class=\"number-badge\" v-if=\"item.count > 0\">{{ item.count }}</p>\r\n            </div>\r\n          </li>\r\n          <li\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === selectId }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"42\" height=\"42\" :src=\"item.pic_path\" />\r\n              <span>99</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.time }}</span>\r\n              <p class=\"lastmsg\">{{ item.content }}</p>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"chatbox\">\r\n        <!-- v-loading=\"listLoading\" -->\r\n        <div class=\"message\">\r\n          <header class=\"header\">\r\n            <div class=\"friendname\" v-if=\"true\">\r\n              <!-- {{ lists.user.name }} -->\r\n              {{ title }}\r\n            </div>\r\n          </header>\r\n        </div>\r\n        <!-- 聊天框 -->\r\n        <div ref=\"list\" class=\"message-wrapper\" @scroll=\"handleScroll()\">\r\n          <div class=\"msg-box\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <div class=\"msg-time\">\r\n              <span>{{ item.create_time }}</span>\r\n            </div>\r\n            <div\r\n              :class=\"['chatMsg-box', { oneself: item.yuangong_id == yon_id }]\"\r\n            >\r\n              <div class=\"chat-name\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div :class=\"['chatMsg-flex']\">\r\n                <div class=\"flex-view\">\r\n                  <img :src=\"item.avatar\" />\r\n                  <!-- <span>{{ item.title }}</span> -->\r\n                </div>\r\n                <div class=\"flex-view\">\r\n                  <!-- 图片 -->\r\n                  <div class=\"chatMsg-img\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'voice'\">\r\n\t\t\t\t\t  <div class=\"\" style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t  \t<audioplay :recordFile=\"item.content\"></audioplay>\r\n\t\t\t\t\t  \t<div>{{ item.datas }}</div>\r\n\t\t\t\t\t  </div>\r\n                   \r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <!-- 文件 -->\r\n                  <div class=\"file-box\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-flex\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-name\">{{ item.files.name }}</div>\r\n                      <div class=\"file-size\">{{ item.files.size }}</div>\r\n                    </div>\r\n                    <div class=\"file-flex\">\r\n                      <img src=\"img/wenjian.png\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t<div style=\"position: absolute; top: 14px;right: 20px; font-size: 32px;\r\n    cursor: pointer;\" @click=\"quanyuan\">···</div>\r\n\t\t<div class=\"\" style=\"position: absolute;      overflow-y: auto;  width: 200px;\r\n    background: #f2f2f2;\r\n    height: 690px;right:-200px;top: 0px;\" v-if=\"la==true\">\r\n\t\t<div class=\"\">\r\n\t\t\t<div class=\"chat-list-box\" >\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t用户\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\"> \r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(item, index) in userss\" :key=\"index\">\r\n\t\t\t\t\t\t<div v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<img :src=\"value.headimg\"/>\r\n\t\t\t\t\t\t\t<div class=\"sl\">{{value.nickname}}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\r\n\t\t\t<div class=\"chat-list-box\" v-for=\"(item,index) in yuangongss\" :key=\"index\">\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t{{item.zhiwei}}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\">\r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t<img :src=\"value.pic_path\" />\r\n\t\t\t\t\t\t<div class=\"sl\">{{value.title}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\r\n\t</div>\r\n        <!-- 输入框 -->\r\n        <div class=\"input-box\">\r\n          <div class=\"workbar-box\">\r\n            <div class=\"upload-emji\">\r\n              <img src=\"img/biaoqing.png\" alt=\"\" @click.stop=\"openEmji\" />\r\n              <div class=\"emji-box\" v-show=\"isEmji\">\r\n                <div class=\"biao-box\">\r\n                  <div\r\n                    class=\"biao-flex\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"upload-file\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                <img src=\"img/insert_img.png\" alt=\"\"\r\n              /></el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"changeFile('image')\">\r\n              <!-- <input\r\n                type=\"file\"\r\n                title=\"选择文件发送\"\r\n                autocomplete=\"off\"\r\n                accept=\"application/*\"\r\n              /> -->\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <img src=\"img/wenjian.png\" alt=\"\" />\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"daiban\">\r\n              <img src=\"img/daiban.png\" alt=\"\" />\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"showgongdan\">\r\n              <img src=\"img/gongdan.png\" alt=\"\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"input-text\">\r\n            <textarea\r\n              placeholder=\"您想说什么？\"\r\n              v-model=\"textContent\"\r\n            ></textarea>\r\n          </div>\r\n          <div class=\"input-btn\">\r\n            <span @click=\"send\">发送Enter</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"img-popup\" :class=\"{ 'show-popup': isShowPopup }\">\r\n      <div>\r\n        <div class=\"img-div\">\r\n          <img :src=\"imgUlr\" alt=\"\" />\r\n        </div>\r\n        <div class=\"close\">\r\n          <span @click=\"isShowPopup = false\">×</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n\t\tuserss:[],\r\n\t\tlvshiss:[],\r\n\t\tyuangongss:[],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      // 得知当前选择的是哪个对话\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n\t  id:0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      //聊天记录\r\n      lists: [],\r\n\t  la:false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\tquanyuan(){\r\n\t\t_this.la =!_this.la\r\n\t\t_this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n\t\t  if (resp.code == 200) {\r\n\t\t    _this.userss = resp.data.users\r\n\t\t    _this.lvshiss = resp.data.lvshis\r\n\t\t    _this.yuangongss = resp.data.yuangongs\r\n\t\t\t\r\n\t\t  }\r\n\t\t});\r\n\t},\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n\t\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target._value;\r\n\r\n      _this.quns = quns.filter((data) => data.title.search(search) != -1);\r\n      _this.users = users.filter(\r\n        (data) =>\r\n          !search || data.title.toLowerCase().includes(search.toLowerCase())\r\n      );\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    //查看图片\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n\t\t !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n\t_this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n\t  _this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    //发送\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n      //   _this.getList();\r\n      /* setTimeout(\r\n         () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n         0*/\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\t\t\r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n\t\t\t_this.id = id;\r\n\tconsole.log(_this.id)\r\n\t\t\t\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n\t\t\t\r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  //    _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n\t\t\tid = 1;\r\n\t\t\t_this\r\n\t\t\t  .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n\t\t\t  .then((resp) => {\r\n\t\t\t    _this.getQun1();\r\n\t\t\t    if (resp.code == 200) {\r\n\t\t\t      _this.list.push(resp.data);\r\n\t\t\t\r\n\t\t\t      setTimeout(\r\n\t\t\t        () =>\r\n\t\t\t          (_this.$refs.list.scrollTop =\r\n\t\t\t            _this.$refs.list.scrollHeight),\r\n\t\t\t        1000\r\n\t\t\t      );\r\n\t\t\t    }\r\n\t\t\t    _this.loading = false;\r\n\t\t\t  });\r\n\t\t}\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        //  let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            //  uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        //      let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n\t\t\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        //f7 118 f8 119 f10 120\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n\r\n    //默认获取列表第一个\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t\r\n\t.chat-list-box{\r\n\t\tmargin-bottom: 10px;\r\n\t\t.chat-list-title{\r\n\t\t\tbackground: #ededed;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tpadding: 10px 5px;\r\n\t\t}\r\n\t\t.chat-list{\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\twidth: 100%;\r\n\t\t\t\r\n\t\t\t.chat-flex{\r\n\t\t\t\tpadding: 0px 8px;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 30px;\r\n\t\t\t\timg{\r\n\t\t\t\t\twidth: 45px;\r\n\t\t\t\t\theight: 45px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t}\r\n\t\t\t\t.sl{\r\n\t\t\t\t\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tmax-width:100px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n.chat-name {\r\n  padding-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #333333;\r\n  text-align: left;\r\n}\r\n.oneself {\r\n  .chat-name {\r\n    text-align: right !important;\r\n  }\r\n}\r\nhtml,\r\nbody,\r\ndiv,\r\nspan,\r\napplet,\r\nobject,\r\niframe,\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\np,\r\nblockquote,\r\npre,\r\na,\r\nabbr,\r\nacronym,\r\naddress,\r\nbig,\r\ncite,\r\ncode,\r\ndel,\r\ndfn,\r\nem,\r\nimg,\r\nins,\r\nkbd,\r\nq,\r\ns,\r\nsamp,\r\nsmall,\r\nstrike,\r\nstrong,\r\nsub,\r\nsup,\r\ntt,\r\nvar,\r\nb,\r\nu,\r\ni,\r\ncenter,\r\ndl,\r\ndt,\r\ndd,\r\nol,\r\nul,\r\nli,\r\nfieldset,\r\nform,\r\nlabel,\r\nlegend,\r\ntable,\r\ncaption,\r\ntbody,\r\ntfoot,\r\nthead,\r\ntr,\r\nth,\r\ntd,\r\narticle,\r\naside,\r\ncanvas,\r\ndetails,\r\nembed,\r\nfigure,\r\nfigcaption,\r\nfooter,\r\nheader,\r\nmenu,\r\nnav,\r\noutput,\r\nruby,\r\nsection,\r\nsummary,\r\ntime,\r\nmark,\r\naudio,\r\nvideo,\r\ninput {\r\n  margin: 0;\r\n  padding: 0;\r\n  border: 0;\r\n  font-size: 100%;\r\n  font-weight: normal;\r\n  vertical-align: baseline;\r\n}\r\n\r\n.body-div {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 87vh;\r\n  background: #999999;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 6px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content {\r\n\tposition: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  width: 900px;\r\n  background: #f2f2f2;\r\n  height: 690px;\r\n}\r\n\r\n.content .msglist {\r\n  width: 350px;\r\n  background: #e6e6e6;\r\n  overflow-y: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box {\r\n  height: calc(100% - 60px);\r\n  overflow: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  padding: 12px;\r\n  -webkit-transition: background-color 0.1s;\r\n  transition: background-color 0.1s;\r\n  font-size: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist:hover {\r\n  background-color: gainsboro;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist.active {\r\n  background-color: #c4c4c4;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left {\r\n  position: relative;\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left span {\r\n  position: absolute;\r\n  width: 15px;\r\n  height: 15px;\r\n  text-align: center;\r\n  background: red;\r\n  color: #ffffff;\r\n  top: 0;\r\n  right: 0;\r\n  border-radius: 50%;\r\n  font-size: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-right {\r\n  position: relative;\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  margin-top: 4px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .name {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  font-size: 14px;\r\n  width: 190px;\r\n  // width: 100px;\r\n  //   overflow: hidden;\r\n  //   white-space: nowrap;\r\n  //   text-overflow: ellipsis;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .time {\r\n  float: right;\r\n  color: #999;\r\n  font-size: 10px;\r\n  vertical-align: top;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .lastmsg {\r\n  position: absolute;\r\n  font-size: 12px;\r\n  width: 130px;\r\n  height: 15px;\r\n  line-height: 15px;\r\n  color: #999;\r\n  bottom: 8px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n.content .msglist .msg-left-box .sessionlist .number-badge {\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: red;\r\n    color: white;\r\n    width: 25px;\r\n    height: 25px;\r\n    border-radius: 50%;\r\n    font-size: 13px;\r\n    position: absolute;\r\n    top: 24px;\r\n    left: 223px;\r\n}\r\n.content .msglist .wrapper {\r\n  padding: 22px 12px 12px 12px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper {\r\n  position: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  height: 26px;\r\n  width: 100%;\r\n  background-color: #e5e3e2;\r\n  border: 1px solid #d9d7d6;\r\n  border-radius: 2px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 6px;\r\n  background-color: #e5e3e2;\r\n  outline: none;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput:focus {\r\n  background-color: #f2efee;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .icon-search {\r\n  display: inline-block;\r\n  width: 24px;\r\n  height: 24px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput-delete {\r\n  display: block;\r\n  position: absolute;\r\n  outline: none;\r\n  top: 0;\r\n  right: 0;\r\n  width: 24px;\r\n  height: 100%;\r\n  background-image: url(\"/img/delete.png\");\r\n  background-size: 26px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .message {\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message .header {\r\n  height: 60px;\r\n  padding: 28px 30px 0;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  border-bottom: 1px solid #e7e7e7;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: justify;\r\n  -ms-flex-pack: justify;\r\n  justify-content: space-between;\r\n}\r\n\r\n.content .chatbox .message .header .friendname {\r\n  font-size: 18px;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message-wrapper {\r\n  height: calc(100% - 240px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box {\r\n  padding: 0 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:last-child {\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time {\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time span {\r\n  display: inline-block;\r\n  padding: 3px 6px;\r\n  border-radius: 3px;\r\n  background-color: #dcdcdc;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img {\r\n  width: 200px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img img {\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-flex {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: start;\r\n  -ms-flex-align: start;\r\n  align-items: flex-start;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1) {\r\n  width: 44px;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  img {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 6px;\r\n  display: block;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  span {\r\n  display: block;\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: left;\r\n  padding-top: 3px;\r\n  width: 50px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding: 0 15px;\r\n  padding-right: 30px;\r\n  text-align: left;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content {\r\n  padding: 10px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  text-align: justify;\r\n  display: inline-block;\r\n  position: relative;\r\n  max-width: 260px;\r\n  min-height: 20px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content::after {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 7px solid transparent;\r\n  left: -14px;\r\n  top: 10px;\r\n  border-right: 7px solid #ffffff;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-img\r\n  img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.content .chatbox .input-box {\r\n  height: 180px;\r\n  background: #fff;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box {\r\n  height: 40px;\r\n  padding: 0 10px;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: start;\r\n  -ms-flex-pack: start;\r\n  justify-content: flex-start;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file {\r\n  position: relative;\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 10px;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 20px;\r\n  opacity: 0;\r\n  z-index: 10;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input[type=\"file\"] {\r\n  color: transparent;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file img {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  -webkit-transform: translate(-50%, -50%);\r\n  transform: translate(-50%, -50%);\r\n  display: block;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.content .chatbox .input-box .input-text {\r\n  width: 100%;\r\n  height: 90px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-text textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  padding: 0 10px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  outline: none;\r\n  font-size: 16px;\r\n  resize: none;\r\n  border: none;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 5px 15px;\r\n  height: 50px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span {\r\n  padding: 5px 15px;\r\n  font-size: 16px;\r\n  background: #f2f2f2;\r\n  cursor: pointer;\r\n  border-radius: 2px;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span:hover {\r\n  background: #129611;\r\n  color: #ffffff;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.oneself .flex-view:nth-child(1) {\r\n  -webkit-box-ordinal-group: 3;\r\n  -ms-flex-order: 2;\r\n  order: 2;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-ordinal-group: 2;\r\n  -ms-flex-order: 1;\r\n  order: 1;\r\n  padding-left: 30px !important;\r\n  padding-right: 15px !important;\r\n  text-align: right !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content {\r\n  position: relative;\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::after {\r\n  border: none !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 8px solid transparent;\r\n  right: -16px;\r\n  top: 12px;\r\n  border-left: 8px solid #b2e281;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .file-box {\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.file-box {\r\n  width: 230px;\r\n  height: 60px;\r\n  -webkit-box-shadow: 0px 0px 5px #ededed;\r\n  box-shadow: 0px 0px 5px #ededed;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding-right: 10px;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-name {\r\n  width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-size {\r\n  font-size: 12px;\r\n  padding-top: 10px;\r\n  color: #999999;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) {\r\n  width: 34px;\r\n  height: 34px;\r\n  text-align: center;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.img-popup {\r\n  position: fixed;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  opacity: 0;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.img-popup .close {\r\n  text-align: center;\r\n}\r\n\r\n.img-popup .close span {\r\n  width: 100px;\r\n  height: 100px;\r\n  font-size: 60px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n\r\n.img-popup .img-div {\r\n  height: 500px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.img-popup .img-div img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.show-popup {\r\n  opacity: 1;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n  pointer-events: all;\r\n}\r\n\r\n.upload-emji {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  img {\r\n    width: 20px;\r\n    height: 20px;\r\n    cursor: pointer;\r\n  }\r\n  .emji-box {\r\n    position: absolute;\r\n    top: -250px;\r\n    width: 440px;\r\n    height: 250px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 0px 10px #ededed;\r\n    border-radius: 12px;\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\";\r\n      border: 7px solid transparent;\r\n      border-top: 7px solid #ffffff;\r\n      bottom: -14px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    .biao-box {\r\n      padding: 10px;\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      .biao-flex {\r\n        text-align: center;\r\n        width: 32px;\r\n        margin: 5px;\r\n        font-size: 20px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n/*# sourceMappingURL=all.css.map */\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./chat.vue?vue&type=template&id=5b813fe9\"\nimport script from \"./chat.vue?vue&type=script&lang=js\"\nexport * from \"./chat.vue?vue&type=script&lang=js\"\nimport style0 from \"./chat.vue?vue&type=style&index=0&id=5b813fe9&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=style&index=0&id=5b813fe9&prod&lang=scss\""], "sourceRoot": ""}