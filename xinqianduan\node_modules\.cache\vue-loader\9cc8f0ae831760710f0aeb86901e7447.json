{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\TodoList.vue?vue&type=style&index=0&id=355176df&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\TodoList.vue", "mtime": 1749570453173}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci50b2RvLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKfQoKLnBhZ2UtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5wYWdlLWhlYWRlciBoMiB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjMzAzMTMzOwp9CgouZmlsdGVyLWNhcmQgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5maWx0ZXItZm9ybSB7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLmxpc3QtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnRvZG8tdGl0bGUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDhweDsKfQoKLnRvZG8tdGl0bGUgLmNvbXBsZXRlZCB7CiAgdGV4dC1kZWNvcmF0aW9uOiBsaW5lLXRocm91Z2g7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi5vdmVyZHVlIHsKICBjb2xvcjogI2Y1NmM2YzsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLm5vLWR1ZS1kYXRlIHsKICBjb2xvcjogI2MwYzRjYzsKfQoKLnBhZ2luYXRpb24td3JhcHBlciB7CiAgbWFyZ2luLXRvcDogMjBweDsKICB0ZXh0LWFsaWduOiByaWdodDsKfQoKLmRpYWxvZy1mb290ZXIgewogIHRleHQtYWxpZ246IHJpZ2h0Owp9Cg=="}, {"version": 3, "sources": ["TodoList.vue"], "names": [], "mappings": ";AAuVA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "TodoList.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\n  <div class=\"todo-container\">\n    <div class=\"page-header\">\n      <h2>待办事项管理</h2>\n      <el-button type=\"primary\" @click=\"showAddDialog = true\">\n        <i class=\"el-icon-plus\"></i> 新增待办事项\n      </el-button>\n    </div>\n\n    <!-- 筛选条件 -->\n    <el-card class=\"filter-card\" shadow=\"never\">\n      <el-form :inline=\"true\" :model=\"filterForm\" class=\"filter-form\">\n        <el-form-item label=\"状态\">\n          <el-select v-model=\"filterForm.status\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未完成\" value=\"0\"></el-option>\n            <el-option label=\"已完成\" value=\"1\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"优先级\">\n          <el-select v-model=\"filterForm.priority\" placeholder=\"请选择优先级\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"高\" value=\"high\"></el-option>\n            <el-option label=\"中\" value=\"medium\"></el-option>\n            <el-option label=\"低\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"类型\">\n          <el-select v-model=\"filterForm.type\" placeholder=\"请选择类型\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"债务处理\" value=\"debt\"></el-option>\n            <el-option label=\"订单处理\" value=\"order\"></el-option>\n            <el-option label=\"用户管理\" value=\"user\"></el-option>\n            <el-option label=\"系统任务\" value=\"system\"></el-option>\n            <el-option label=\"一般任务\" value=\"general\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"loadTodos\">查询</el-button>\n          <el-button @click=\"resetFilter\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 待办事项列表 -->\n    <el-card class=\"list-card\">\n      <el-table :data=\"todoList\" v-loading=\"loading\" stripe>\n        <el-table-column prop=\"title\" label=\"标题\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"todo-title\">\n              <el-checkbox \n                v-model=\"scope.row.completed\" \n                @change=\"handleStatusChange(scope.row)\"\n                :disabled=\"scope.row.status === 2\"\n              ></el-checkbox>\n              <span :class=\"{ 'completed': scope.row.completed }\">{{ scope.row.title }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"description\" label=\"描述\" min-width=\"200\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"type_text\" label=\"类型\" width=\"100\"></el-table-column>\n        <el-table-column prop=\"priority_text\" label=\"优先级\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag \n              :type=\"getPriorityType(scope.row.priority)\" \n              size=\"small\"\n            >\n              {{ scope.row.priority_text }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"due_date\" label=\"截止时间\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.due_date\" :class=\"{ 'overdue': isOverdue(scope.row.due_date) }\">\n              {{ scope.row.due_date }}\n            </span>\n            <span v-else class=\"no-due-date\">无</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag \n              :type=\"scope.row.completed ? 'success' : 'info'\" \n              size=\"small\"\n            >\n              {{ scope.row.completed ? '已完成' : '未完成' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"editTodo(scope.row)\">编辑</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"deleteTodo(scope.row)\" style=\"color: #f56c6c;\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.page\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"pagination.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog \n      :title=\"editingTodo.id ? '编辑待办事项' : '新增待办事项'\" \n      :visible.sync=\"showAddDialog\"\n      width=\"600px\"\n    >\n      <el-form :model=\"editingTodo\" :rules=\"todoRules\" ref=\"todoForm\" label-width=\"100px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"editingTodo.title\" placeholder=\"请输入待办事项标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"editingTodo.description\" \n            placeholder=\"请输入详细描述\"\n            :rows=\"3\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"type\">\n          <el-select v-model=\"editingTodo.type\" placeholder=\"请选择类型\">\n            <el-option label=\"债务处理\" value=\"debt\"></el-option>\n            <el-option label=\"订单处理\" value=\"order\"></el-option>\n            <el-option label=\"用户管理\" value=\"user\"></el-option>\n            <el-option label=\"系统任务\" value=\"system\"></el-option>\n            <el-option label=\"一般任务\" value=\"general\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"优先级\" prop=\"priority\">\n          <el-select v-model=\"editingTodo.priority\" placeholder=\"请选择优先级\">\n            <el-option label=\"高\" value=\"high\"></el-option>\n            <el-option label=\"中\" value=\"medium\"></el-option>\n            <el-option label=\"低\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"截止时间\">\n          <el-date-picker\n            v-model=\"editingTodo.due_date\"\n            type=\"datetime\"\n            placeholder=\"选择截止时间\"\n            format=\"yyyy-MM-dd HH:mm\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\">\n          </el-date-picker>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showAddDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"saveTodo\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRequest, postRequest, putRequest, deleteRequest } from '@/utils/api'\n\nexport default {\n  name: 'TodoList',\n  mixins: [{ methods: { getRequest, postRequest, putRequest, deleteRequest } }],\n  data() {\n    return {\n      loading: false,\n      showAddDialog: false,\n      todoList: [],\n      filterForm: {\n        status: '',\n        priority: '',\n        type: ''\n      },\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      },\n      editingTodo: {\n        id: null,\n        title: '',\n        description: '',\n        type: 'general',\n        priority: 'medium',\n        due_date: null\n      },\n      todoRules: {\n        title: [\n          { required: true, message: '请输入标题', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择类型', trigger: 'change' }\n        ],\n        priority: [\n          { required: true, message: '请选择优先级', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadTodos()\n  },\n  methods: {\n    async loadTodos() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          size: this.pagination.size,\n          ...this.filterForm\n        }\n        const response = await this.getRequest('/admin/todo/list', params)\n        if (response.code === 200) {\n          this.todoList = response.data.list || []\n          this.pagination.total = response.data.total || 0\n        }\n      } catch (error) {\n        console.error('加载待办事项失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async handleStatusChange(todo) {\n      try {\n        const response = await this.postRequest('/dashboard/updateTodo', {\n          id: todo.id,\n          completed: todo.completed\n        })\n        if (response.code === 200) {\n          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\n        }\n      } catch (error) {\n        console.error('更新状态失败:', error)\n        todo.completed = !todo.completed // 回滚\n        this.$message.error('更新失败')\n      }\n    },\n\n    editTodo(todo) {\n      this.editingTodo = { ...todo }\n      this.showAddDialog = true\n    },\n\n    async deleteTodo(todo) {\n      try {\n        await this.$confirm('确定要删除这个待办事项吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.deleteRequest('/admin/todo/delete', { id: todo.id })\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          this.loadTodos()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async saveTodo() {\n      try {\n        await this.$refs.todoForm.validate()\n        \n        const isEdit = !!this.editingTodo.id\n        const url = isEdit ? '/admin/todo/update' : '/admin/todo/create'\n        const response = await this.postRequest(url, this.editingTodo)\n        \n        if (response.code === 200) {\n          this.$message.success(isEdit ? '更新成功' : '创建成功')\n          this.showAddDialog = false\n          this.resetForm()\n          this.loadTodos()\n        }\n      } catch (error) {\n        console.error('保存失败:', error)\n        this.$message.error('保存失败')\n      }\n    },\n\n    resetForm() {\n      this.editingTodo = {\n        id: null,\n        title: '',\n        description: '',\n        type: 'general',\n        priority: 'medium',\n        due_date: null\n      }\n      this.$refs.todoForm && this.$refs.todoForm.resetFields()\n    },\n\n    resetFilter() {\n      this.filterForm = {\n        status: '',\n        priority: '',\n        type: ''\n      }\n      this.pagination.page = 1\n      this.loadTodos()\n    },\n\n    handleSizeChange(size) {\n      this.pagination.size = size\n      this.pagination.page = 1\n      this.loadTodos()\n    },\n\n    handleCurrentChange(page) {\n      this.pagination.page = page\n      this.loadTodos()\n    },\n\n    getPriorityType(priority) {\n      const map = {\n        high: 'danger',\n        medium: 'warning',\n        low: 'info'\n      }\n      return map[priority] || 'info'\n    },\n\n    isOverdue(dueDate) {\n      if (!dueDate) return false\n      return new Date(dueDate) < new Date()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.todo-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form {\n  margin-bottom: 0;\n}\n\n.list-card {\n  margin-bottom: 20px;\n}\n\n.todo-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.todo-title .completed {\n  text-decoration: line-through;\n  color: #909399;\n}\n\n.overdue {\n  color: #f56c6c;\n  font-weight: bold;\n}\n\n.no-due-date {\n  color: #c0c4cc;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}