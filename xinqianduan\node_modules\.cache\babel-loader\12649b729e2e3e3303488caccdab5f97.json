{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\TodoList.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\TodoList.vue", "mtime": 1749572294856}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getRequest", "postRequest", "putRequest", "deleteRequest", "name", "mixins", "methods", "data", "loading", "showAddDialog", "todoList", "filterForm", "status", "priority", "type", "pagination", "page", "size", "total", "editingTodo", "id", "title", "description", "due_date", "todoRules", "required", "message", "trigger", "mounted", "loadTodos", "params", "response", "code", "list", "error", "console", "$message", "handleStatusChange", "todo", "completed", "success", "editTodo", "deleteTodo", "$confirm", "confirmButtonText", "cancelButtonText", "saveTodo", "$refs", "todoForm", "validate", "isEdit", "url", "resetForm", "resetFields", "resetFilter", "handleSizeChange", "handleCurrentChange", "getPriorityType", "map", "high", "medium", "low", "isOverdue", "dueDate", "Date"], "sources": ["src/views/pages/TodoList.vue"], "sourcesContent": ["<template>\n  <div class=\"todo-container\">\n    <div class=\"page-header\">\n      <h2>待办事项管理</h2>\n      <el-button type=\"primary\" @click=\"showAddDialog = true\">\n        <i class=\"el-icon-plus\"></i> 新增待办事项\n      </el-button>\n    </div>\n\n    <!-- 筛选条件 -->\n    <el-card class=\"filter-card\" shadow=\"never\">\n      <el-form :inline=\"true\" :model=\"filterForm\" class=\"filter-form\">\n        <el-form-item label=\"状态\">\n          <el-select v-model=\"filterForm.status\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未完成\" value=\"0\"></el-option>\n            <el-option label=\"已完成\" value=\"1\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"优先级\">\n          <el-select v-model=\"filterForm.priority\" placeholder=\"请选择优先级\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"高\" value=\"high\"></el-option>\n            <el-option label=\"中\" value=\"medium\"></el-option>\n            <el-option label=\"低\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"类型\">\n          <el-select v-model=\"filterForm.type\" placeholder=\"请选择类型\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"债务处理\" value=\"debt\"></el-option>\n            <el-option label=\"订单处理\" value=\"order\"></el-option>\n            <el-option label=\"用户管理\" value=\"user\"></el-option>\n            <el-option label=\"系统任务\" value=\"system\"></el-option>\n            <el-option label=\"一般任务\" value=\"general\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"loadTodos\">查询</el-button>\n          <el-button @click=\"resetFilter\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 待办事项列表 -->\n    <el-card class=\"list-card\">\n      <el-table :data=\"todoList\" v-loading=\"loading\" stripe>\n        <el-table-column prop=\"title\" label=\"标题\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"todo-title\">\n              <el-checkbox \n                v-model=\"scope.row.completed\" \n                @change=\"handleStatusChange(scope.row)\"\n                :disabled=\"scope.row.status === 2\"\n              ></el-checkbox>\n              <span :class=\"{ 'completed': scope.row.completed }\">{{ scope.row.title }}</span>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"description\" label=\"描述\" min-width=\"200\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"type_text\" label=\"类型\" width=\"100\"></el-table-column>\n        <el-table-column prop=\"priority_text\" label=\"优先级\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag \n              :type=\"getPriorityType(scope.row.priority)\" \n              size=\"small\"\n            >\n              {{ scope.row.priority_text }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"due_date\" label=\"截止时间\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.due_date\" :class=\"{ 'overdue': isOverdue(scope.row.due_date) }\">\n              {{ scope.row.due_date }}\n            </span>\n            <span v-else class=\"no-due-date\">无</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag \n              :type=\"scope.row.completed ? 'success' : 'info'\" \n              size=\"small\"\n            >\n              {{ scope.row.completed ? '已完成' : '未完成' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"editTodo(scope.row)\">编辑</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"deleteTodo(scope.row)\" style=\"color: #f56c6c;\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.page\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"pagination.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog \n      :title=\"editingTodo.id ? '编辑待办事项' : '新增待办事项'\" \n      :visible.sync=\"showAddDialog\"\n      width=\"600px\"\n    >\n      <el-form :model=\"editingTodo\" :rules=\"todoRules\" ref=\"todoForm\" label-width=\"100px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"editingTodo.title\" placeholder=\"请输入待办事项标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"editingTodo.description\" \n            placeholder=\"请输入详细描述\"\n            :rows=\"3\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"type\">\n          <el-select v-model=\"editingTodo.type\" placeholder=\"请选择类型\">\n            <el-option label=\"债务处理\" value=\"debt\"></el-option>\n            <el-option label=\"订单处理\" value=\"order\"></el-option>\n            <el-option label=\"用户管理\" value=\"user\"></el-option>\n            <el-option label=\"系统任务\" value=\"system\"></el-option>\n            <el-option label=\"一般任务\" value=\"general\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"优先级\" prop=\"priority\">\n          <el-select v-model=\"editingTodo.priority\" placeholder=\"请选择优先级\">\n            <el-option label=\"高\" value=\"high\"></el-option>\n            <el-option label=\"中\" value=\"medium\"></el-option>\n            <el-option label=\"低\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"截止时间\">\n          <el-date-picker\n            v-model=\"editingTodo.due_date\"\n            type=\"datetime\"\n            placeholder=\"选择截止时间\"\n            format=\"yyyy-MM-dd HH:mm\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\">\n          </el-date-picker>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showAddDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"saveTodo\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRequest, postRequest, putRequest, deleteRequest } from '@/utils/api'\n\nexport default {\n  name: 'TodoList',\n  mixins: [{ methods: { getRequest, postRequest, putRequest, deleteRequest } }],\n  data() {\n    return {\n      loading: false,\n      showAddDialog: false,\n      todoList: [],\n      filterForm: {\n        status: '',\n        priority: '',\n        type: ''\n      },\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      },\n      editingTodo: {\n        id: null,\n        title: '',\n        description: '',\n        type: 'general',\n        priority: 'medium',\n        due_date: null\n      },\n      todoRules: {\n        title: [\n          { required: true, message: '请输入标题', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择类型', trigger: 'change' }\n        ],\n        priority: [\n          { required: true, message: '请选择优先级', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadTodos()\n  },\n  methods: {\n    async loadTodos() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          size: this.pagination.size,\n          ...this.filterForm\n        }\n        const response = await this.getRequest('/todo/list', params)\n        if (response.code === 200) {\n          this.todoList = response.data.list || []\n          this.pagination.total = response.data.total || 0\n        }\n      } catch (error) {\n        console.error('加载待办事项失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async handleStatusChange(todo) {\n      try {\n        const response = await this.postRequest('/dashboard/updateTodo', {\n          id: todo.id,\n          completed: todo.completed\n        })\n        if (response.code === 200) {\n          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\n        }\n      } catch (error) {\n        console.error('更新状态失败:', error)\n        todo.completed = !todo.completed // 回滚\n        this.$message.error('更新失败')\n      }\n    },\n\n    editTodo(todo) {\n      this.editingTodo = { ...todo }\n      this.showAddDialog = true\n    },\n\n    async deleteTodo(todo) {\n      try {\n        await this.$confirm('确定要删除这个待办事项吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.deleteRequest('/todo/delete', { id: todo.id })\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          this.loadTodos()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async saveTodo() {\n      try {\n        await this.$refs.todoForm.validate()\n        \n        const isEdit = !!this.editingTodo.id\n        const url = isEdit ? '/todo/update' : '/todo/create'\n        const response = await this.postRequest(url, this.editingTodo)\n        \n        if (response.code === 200) {\n          this.$message.success(isEdit ? '更新成功' : '创建成功')\n          this.showAddDialog = false\n          this.resetForm()\n          this.loadTodos()\n        }\n      } catch (error) {\n        console.error('保存失败:', error)\n        this.$message.error('保存失败')\n      }\n    },\n\n    resetForm() {\n      this.editingTodo = {\n        id: null,\n        title: '',\n        description: '',\n        type: 'general',\n        priority: 'medium',\n        due_date: null\n      }\n      this.$refs.todoForm && this.$refs.todoForm.resetFields()\n    },\n\n    resetFilter() {\n      this.filterForm = {\n        status: '',\n        priority: '',\n        type: ''\n      }\n      this.pagination.page = 1\n      this.loadTodos()\n    },\n\n    handleSizeChange(size) {\n      this.pagination.size = size\n      this.pagination.page = 1\n      this.loadTodos()\n    },\n\n    handleCurrentChange(page) {\n      this.pagination.page = page\n      this.loadTodos()\n    },\n\n    getPriorityType(priority) {\n      const map = {\n        high: 'danger',\n        medium: 'warning',\n        low: 'info'\n      }\n      return map[priority] || 'info'\n    },\n\n    isOverdue(dueDate) {\n      if (!dueDate) return false\n      return new Date(dueDate) < new Date()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.todo-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form {\n  margin-bottom: 0;\n}\n\n.list-card {\n  margin-bottom: 20px;\n}\n\n.todo-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.todo-title .completed {\n  text-decoration: line-through;\n  color: #909399;\n}\n\n.overdue {\n  color: #f56c6c;\n  font-weight: bold;\n}\n\n.no-due-date {\n  color: #c0c4cc;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": "AAoKA,SAAAA,UAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,MAAA;IAAAC,OAAA;MAAAN,UAAA;MAAAC,WAAA;MAAAC,UAAA;MAAAC;IAAA;EAAA;EACAI,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,QAAA;MACAC,UAAA;QACAC,MAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,WAAA;QACAC,EAAA;QACAC,KAAA;QACAC,WAAA;QACAR,IAAA;QACAD,QAAA;QACAU,QAAA;MACA;MACAC,SAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,IAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,QAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;EACA;EACAvB,OAAA;IACA,MAAAuB,UAAA;MACA,KAAArB,OAAA;MACA;QACA,MAAAsB,MAAA;UACAd,IAAA,OAAAD,UAAA,CAAAC,IAAA;UACAC,IAAA,OAAAF,UAAA,CAAAE,IAAA;UACA,QAAAN;QACA;QACA,MAAAoB,QAAA,cAAA/B,UAAA,eAAA8B,MAAA;QACA,IAAAC,QAAA,CAAAC,IAAA;UACA,KAAAtB,QAAA,GAAAqB,QAAA,CAAAxB,IAAA,CAAA0B,IAAA;UACA,KAAAlB,UAAA,CAAAG,KAAA,GAAAa,QAAA,CAAAxB,IAAA,CAAAW,KAAA;QACA;MACA,SAAAgB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAA1B,OAAA;MACA;IACA;IAEA,MAAA6B,mBAAAC,IAAA;MACA;QACA,MAAAP,QAAA,cAAA9B,WAAA;UACAmB,EAAA,EAAAkB,IAAA,CAAAlB,EAAA;UACAmB,SAAA,EAAAD,IAAA,CAAAC;QACA;QACA,IAAAR,QAAA,CAAAC,IAAA;UACA,KAAAI,QAAA,CAAAI,OAAA,CAAAF,IAAA,CAAAC,SAAA;QACA;MACA,SAAAL,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACAI,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAAC,SAAA;QACA,KAAAH,QAAA,CAAAF,KAAA;MACA;IACA;IAEAO,SAAAH,IAAA;MACA,KAAAnB,WAAA;QAAA,GAAAmB;MAAA;MACA,KAAA7B,aAAA;IACA;IAEA,MAAAiC,WAAAJ,IAAA;MACA;QACA,WAAAK,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA/B,IAAA;QACA;QAEA,MAAAiB,QAAA,cAAA5B,aAAA;UAAAiB,EAAA,EAAAkB,IAAA,CAAAlB;QAAA;QACA,IAAAW,QAAA,CAAAC,IAAA;UACA,KAAAI,QAAA,CAAAI,OAAA;UACA,KAAAX,SAAA;QACA;MACA,SAAAK,KAAA;QACA,IAAAA,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA;QACA;MACA;IACA;IAEA,MAAAY,SAAA;MACA;QACA,WAAAC,KAAA,CAAAC,QAAA,CAAAC,QAAA;QAEA,MAAAC,MAAA,UAAA/B,WAAA,CAAAC,EAAA;QACA,MAAA+B,GAAA,GAAAD,MAAA;QACA,MAAAnB,QAAA,cAAA9B,WAAA,CAAAkD,GAAA,OAAAhC,WAAA;QAEA,IAAAY,QAAA,CAAAC,IAAA;UACA,KAAAI,QAAA,CAAAI,OAAA,CAAAU,MAAA;UACA,KAAAzC,aAAA;UACA,KAAA2C,SAAA;UACA,KAAAvB,SAAA;QACA;MACA,SAAAK,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEAkB,UAAA;MACA,KAAAjC,WAAA;QACAC,EAAA;QACAC,KAAA;QACAC,WAAA;QACAR,IAAA;QACAD,QAAA;QACAU,QAAA;MACA;MACA,KAAAwB,KAAA,CAAAC,QAAA,SAAAD,KAAA,CAAAC,QAAA,CAAAK,WAAA;IACA;IAEAC,YAAA;MACA,KAAA3C,UAAA;QACAC,MAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACA,KAAAC,UAAA,CAAAC,IAAA;MACA,KAAAa,SAAA;IACA;IAEA0B,iBAAAtC,IAAA;MACA,KAAAF,UAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAF,UAAA,CAAAC,IAAA;MACA,KAAAa,SAAA;IACA;IAEA2B,oBAAAxC,IAAA;MACA,KAAAD,UAAA,CAAAC,IAAA,GAAAA,IAAA;MACA,KAAAa,SAAA;IACA;IAEA4B,gBAAA5C,QAAA;MACA,MAAA6C,GAAA;QACAC,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACA,OAAAH,GAAA,CAAA7C,QAAA;IACA;IAEAiD,UAAAC,OAAA;MACA,KAAAA,OAAA;MACA,WAAAC,IAAA,CAAAD,OAAA,QAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}