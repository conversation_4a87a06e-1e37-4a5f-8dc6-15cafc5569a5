{"name": "@babel/plugin-transform-optional-chaining", "version": "7.24.8", "description": "Transform optional chaining operators into a series of nil checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.8", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.24.7", "@babel/traverse": "^7.24.8"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}