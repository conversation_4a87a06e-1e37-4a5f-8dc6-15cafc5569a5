<?php
namespace app\admin\controller;
use think\Request;
use untils\{JsonService,WechatApp};
use models\{<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Yuan<PERSON>,<PERSON>dans};

class Until extends Base
{
    
    public function __construct(){
        parent::__construct();
 
    }
    public function getOrcode(){
        $path = '/qrcode/yuaong_id='.$this->userid;
        $res = '';
        $res = WechatApp::_getqrcode("yuaong_id=".$this->userid);
        
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res);
    }
}