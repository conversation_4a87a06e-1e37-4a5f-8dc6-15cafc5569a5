{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue", "mtime": 1748336508341}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA+KA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"名称\"> </el-table-column>\r\n        <el-table-column prop=\"zhiwei_id\" label=\"职位\"> </el-table-column>\r\n        <el-table-column prop=\"pic_path\" label=\"头像\">\r\n          <template slot-scope=\"scope\">\r\n            <img\r\n              :src=\"scope.row.pic_path\"\r\n              style=\"width: 160px; height: 80px\"\r\n              @click=\"showImage(scope.row.pic_path)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"手机号码\"> </el-table-column>\r\n        <el-table-column prop=\"account\" label=\"账号\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button type=\"text\" size=\"small\" @click=\"chongzhi(scope.row.id)\"\r\n              >重置密码</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"职位类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhiwei_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.zhiwei_id\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n          >\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in zhiweis\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '名称'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '手机'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '账号'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"account\"\r\n        >\r\n          <el-input v-model=\"ruleForm.account\" autocomplete=\"off\">\r\n            <template slot=\"append\">默认密码888888</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"头像\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n          <div class=\"el-upload__tip\">330rpx*300rpx</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/Yuangong/\",\r\n      title: \"员工\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n      field: \"\",\r\n      zhiweis: [],\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传头像\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    chongzhi(id) {\r\n      this.$confirm(\"重置密码888888?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.postRequest(\"/yuangong/chongzhi\", { id: id }).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"重置成功!\",\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: \"重置失败!\",\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消重置!\",\r\n          });\r\n        });\r\n    },\r\n    getZhiwei() {\r\n      this.postRequest(\"/zhiwei/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.zhiweis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          pic_path: \"\",\r\n          account: \"\",\r\n          phone: \"\",\r\n          zhiwei_id: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getZhiwei();\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}