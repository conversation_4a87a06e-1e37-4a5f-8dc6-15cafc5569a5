(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7adf685e"],{"26b2":function(e,t,i){"use strict";var l=function(){var e=this,t=e._self._c;return t("el-row",[t("el-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-top"},on:{click:e.exports}},[e._v("导出跟进记录")]),t("el-descriptions",{attrs:{title:"债务信息"}},[t("el-descriptions-item",{attrs:{label:"用户姓名"}},[e._v(e._s(e.info.nickname))]),t("el-descriptions-item",{attrs:{label:"债务人姓名"}},[e._v(e._s(e.info.name))]),t("el-descriptions-item",{attrs:{label:"债务人电话"}},[e._v(e._s(e.info.tel))]),t("el-descriptions-item",{attrs:{label:"债务人地址"}},[e._v(e._s(e.info.address))]),t("el-descriptions-item",{attrs:{label:"债务金额"}},[e._v(e._s(e.info.money))]),t("el-descriptions-item",{attrs:{label:"合计回款"}},[e._v(e._s(e.info.back_money))]),t("el-descriptions-item",{attrs:{label:"未回款"}},[e._v(e._s(e.info.un_money))]),t("el-descriptions-item",{attrs:{label:"提交时间"}},[e._v(e._s(e.info.ctime))]),t("el-descriptions-item",{attrs:{label:"最后一次修改时间"}},[e._v(e._s(e.info.utime))])],1),t("el-descriptions",{attrs:{title:"债务人身份信息",colon:!1}},[t("el-descriptions-item",[e.info.cards[0]?t("div",{staticStyle:{width:"100%",display:"table-cell"}},e._l(e.info.cards,(function(i,l){return t("div",{key:l,staticClass:"image-list",staticStyle:{float:"left","margin-left":"2px"}},[t("img",{staticStyle:{width:"100px",height:"100px"},attrs:{src:i,mode:"aspectFit"},on:{click:function(t){return e.showImage(i)}}})])})),0):e._e()])],1),t("el-descriptions",{attrs:{title:"案由",colon:!1}},[t("el-descriptions-item",[e._v(e._s(e.info.case_des))])],1),t("el-descriptions",{attrs:{title:"证据图片",colon:!1}},[t("el-descriptions-item",[e.info.images[0]?t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary"},on:{click:function(t){return e.downloadFiles(e.info.images_download)}}},[e._v("全部下载")]):e._e(),e.info.images[0]?t("div",{staticStyle:{width:"100%",display:"table-cell"}},e._l(e.info.images,(function(i,l){return t("div",{key:l,staticClass:"image-list",staticStyle:{float:"left","margin-left":"2px"}},[t("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:i,"preview-src-list":e.info.images}}),t("a",{attrs:{href:i,target:"_blank",download:"evidence."+i.split(".")[1]}},[e._v("下载")])],1)})),0):e._e()],1)],1),e.info.attach_path[0]?t("el-descriptions",{attrs:{title:"证据文件",colon:!1}},[t("el-descriptions-item",[t("div",{staticStyle:{width:"100%",display:"table-cell","line-height":"20px"}},e._l(e.info.attach_path,(function(i,l){return t("div",{key:l},[i?t("div",[t("div",[e._v("文件"+e._s(l+1+"->"+i.split(".")[1])),t("a",{staticStyle:{"margin-left":"10px"},attrs:{href:i,target:"_blank"}},[e._v("查看")]),t("a",{staticStyle:{"margin-left":"10px"},attrs:{href:i,target:"_blank"}},[e._v("下载")])]),t("br")]):e._e()])})),0)])],1):e._e(),t("el-descriptions",{attrs:{title:"跟进记录",colon:!1}},[t("el-descriptions-item",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.info.debttrans,size:"mini"}},[t("el-table-column",{attrs:{prop:"day",label:"跟进日期"}}),t("el-table-column",{attrs:{prop:"ctime",label:"提交时间"}}),t("el-table-column",{attrs:{prop:"au_id",label:"操作人员"}}),t("el-table-column",{attrs:{prop:"type",label:"进度类型"}}),t("el-table-column",{attrs:{prop:"total_price",label:"费用金额/手续费"}}),t("el-table-column",{attrs:{prop:"content",label:"费用内容"}}),t("el-table-column",{attrs:{prop:"rate",label:"手续费比率"}}),t("el-table-column",{attrs:{prop:"back_money",label:"回款金额"}}),t("el-table-column",{attrs:{prop:"pay_type",label:"支付状态"}}),t("el-table-column",{attrs:{prop:"pay_time",label:"支付时间"}}),t("el-table-column",{attrs:{prop:"pay_order_type",label:"支付方式"}}),t("el-table-column",{attrs:{prop:"desc",label:"进度描述"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(i.$index,i.row.id)}}},[e._v(" 移除 ")])]}}])})],1)],1)],1)],1)},a=[],s={name:"DebtDetail",props:{id:{type:String,required:!0}},data(){return{info:[]}},watch:{id:{immediate:!0,handler(e){this.getInfo(e)}}},methods:{getInfo(e){let t=this;t.getRequest("/debt/view?id="+e).then(e=>{200==e.code?t.info=e.data:t.$message({type:"error",message:e.msg})})},downloadFiles(e){e.forEach(e=>{const t=document.createElement("a");t.href=e.path,t.download=e.name,t.click()})},exports:function(){let e=this;location.href="/admin/debt/view?token="+e.$store.getters.GET_TOKEN+"&export=1&id="+e.ruleForm.id}}},r=s,o=i("2877"),n=Object(o["a"])(r,l,a,!1,null,null,null);t["a"]=n.exports},"342b":function(e,t,i){},a745:function(e,t,i){"use strict";i("342b")},c3ba:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",[t("el-col",{attrs:{span:4}},[t("el-input",{attrs:{placeholder:"请输入订单号/套餐",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-col",{attrs:{span:3}},[t("el-select",{attrs:{placeholder:"支付状态",size:e.allSize},model:{value:e.search.is_pay,callback:function(t){e.$set(e.search,"is_pay",t)},expression:"search.is_pay"}},e._l(e.options,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("el-col",{attrs:{span:3}},[t("el-select",{attrs:{placeholder:"处理状态",size:e.allSize},model:{value:e.search.is_deal,callback:function(t){e.$set(e.search,"is_deal",t)},expression:"search.is_deal"}},e._l(e.options1,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("el-col",{attrs:{span:8}},[t("el-date-picker",{attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":"支付开始日期","end-placeholder":"支付结束日期",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},model:{value:e.search.pay_time,callback:function(t){e.$set(e.search,"pay_time",t)},expression:"search.pay_time"}})],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v("搜索")])],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.clearData()}}},[e._v("重置")])],1)],1),t("el-row",[t("el-col",{attrs:{span:5}},[t("el-view",{attrs:{label:"支付金额统计"}},[t("span",{staticClass:"el-pagination-count"},[e._v("支付金额统计:"+e._s(e.money)+"元")])])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"}},[t("el-table-column",{attrs:{prop:"order_sn",label:"订单号"}}),t("el-table-column",{attrs:{prop:"title",label:"套餐"}}),t("el-table-column",{attrs:{prop:"total_price",label:"支付金额",sortable:""}}),t("el-table-column",{attrs:{prop:"is_pay",label:"支付状态"}}),t("el-table-column",{attrs:{prop:"refund_time",label:"支付时间",sortable:""}}),t("el-table-column",{attrs:{prop:"is_deal",label:"处理状态",sortable:""}}),t("el-table-column",{attrs:{prop:"body",label:"购买类型"}}),t("el-table-column",{attrs:{prop:"phone",label:"用户号码",sortable:""},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{on:{click:function(t){return e.viewUserData(i.row.uid)}}},[e._v(e._s(i.row.phone))])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",sortable:""}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(i){return["未支付"==i.row.is_pay?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.free(i.row.id)}}},[e._v("免支付")]):e._e(),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewData(i.row.id)}}},[e._v("查看")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.tuikuan(i.row.id)}}},[e._v("退款")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(i.row.id)}}},[e._v("完成制作")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(i.$index,i.row.id)}}},[e._v(" 取消 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"制作状态","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:2},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,"is_deal",t)},expression:"ruleForm.is_deal"}},[e._v("已完成")]),t("el-radio",{attrs:{label:1},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,"is_deal",t)},expression:"ruleForm.is_deal"}},[e._v("处理中")])],1)]),2==e.ruleForm.is_deal?t("el-form-item",{attrs:{label:"请上传文件","label-width":e.formLabelWidth,prop:"file_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,"file_path",t)},expression:"ruleForm.file_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFile("file_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传 ")])],1),e.ruleForm.file_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,"file_path")}}},[e._v("删除")]):e._e()],1)],1):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-dialog",{attrs:{title:"订单查看",visible:e.viewFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.viewFormVisible=t}}},[t("el-descriptions",{attrs:{title:"订单信息"}},[t("el-descriptions-item",{attrs:{label:"订单号"}},[e._v(e._s(e.info.order_sn))]),t("el-descriptions-item",{attrs:{label:"购买类型"}},[e._v(e._s(e.info.body))]),t("el-descriptions-item",{attrs:{label:"支付金额"}},[e._v(e._s(e.info.total_price))]),t("el-descriptions-item",{attrs:{label:"支付状态"}},[e._v(e._s(e.info.is_pay_name))]),t("el-descriptions-item",{attrs:{label:"支付时间"}},[e._v(e._s(e.info.pay_time))]),t("el-descriptions-item",{attrs:{label:"支付方式"}},[e._v("微信支付")]),t("el-descriptions-item",{attrs:{label:"退款时间"}},[e._v(e._s(e.info.refund_time))]),t("el-descriptions-item",{attrs:{label:"免支付操作人"}},[e._v(e._s(e.info.free_operator))])],1),t("el-descriptions",{attrs:{title:"服务信息"}},[t("el-descriptions-item",{attrs:{label:"服务信息"}},[e._v(e._s(e.info.body))])],1),t("el-descriptions",{attrs:{title:"用户信息"}},[t("el-descriptions-item",{attrs:{label:"用户姓名"}},[t("div",{on:{click:function(t){return e.viewUserData(e.info.uid)}}},[e._v(e._s(e.info.linkman))])]),t("el-descriptions-item",{attrs:{label:"用户电话"}},[t("div",{on:{click:function(t){return e.viewUserData(e.info.uid)}}},[e._v(e._s(e.info.linkphone))])])],1),t("el-descriptions",{attrs:{title:"债务人信息"}},[t("el-descriptions-item",{attrs:{label:"债务人姓名"}},[t("div",{on:{click:function(t){return e.viewDebtData(e.info.dt_id)}}},[e._v(e._s(e.info.debts_name))])]),t("el-descriptions-item",{attrs:{label:"债务人电话"}},[t("div",{on:{click:function(t){return e.viewDebtData(e.info.dt_id)}}},[e._v(e._s(e.info.debts_tel))])])],1),t("el-descriptions",{attrs:{title:"制作信息"}},[t("el-descriptions-item",{attrs:{label:"制作状态"}},[e._v(e._s(e.info.is_deal_name))]),t("el-descriptions-item",{attrs:{label:"制作文件"}},[e._v("文件"),t("a",{attrs:{href:e.info.file_path,target:"_blank"}},[e._v("查看")]),t("a",{attrs:{href:e.info.file_path}},[e._v("下载")])])],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.viewFormVisible=!1}}},[e._v("取 消")])],1)],1),t("el-dialog",{attrs:{title:"用户详情",visible:e.dialogViewUserDetail,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogViewUserDetail=t}}},[t("user-details",{attrs:{id:e.currentId}})],1),t("el-dialog",{attrs:{title:"债务查看",visible:e.dialogViewDebtDetail,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogViewDebtDetail=t}}},[t("debt-detail",{attrs:{id:e.currentDebtId}}),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogViewDebtDetail=!1}}},[e._v("取 消")])],1)],1)],1)},a=[],s=i("d522"),r=i("26b2"),o={name:"list",components:{UserDetails:s["a"],DebtDetail:r["a"]},data(){return{allSize:"mini",list:[],total:1,money:0,currentId:0,currentDebtId:0,page:1,size:20,search:{keyword:"",is_pay:-1,is_deal:-1},loading:!0,url:"/order/",title:"订单",info:{},dialogFormVisible:!1,dialogViewUserDetail:!1,viewFormVisible:!1,dialogViewDebtDetail:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],file_path:[{required:!0,message:"请上传文件",trigger:"blur"}]},formLabelWidth:"120px",options:[{id:-1,title:"支付状态"},{id:1,title:"未支付"},{id:2,title:"已支付"},{id:3,title:"退款"}],options1:[{id:-1,title:"处理状态"},{id:1,title:"待处理"},{id:2,title:"已处理"}]}},mounted(){this.getData()},methods:{changeFile(e){this.filed=e,console.log(this.filed)},clearData(){this.search={keyword:"",is_pay:"",refund_time:""},this.getData()},viewUserData(e){let t=this;0!=e&&(this.currentId=e),t.dialogViewUserDetail=!0},viewDebtData(e){let t=this;0!=e&&(this.currentDebtId=e),t.dialogViewDebtDetail=!0},editData(e){0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""}},viewData(e){0!=e?this.getView(e):this.ruleForm={title:"",desc:""}},getView(e){let t=this;t.getRequest(t.url+"view?id="+e).then(e=>{200==e.code?(t.info=e.data,t.viewFormVisible=!0):t.$message({type:"error",message:e.msg})})},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{200==e.code?(t.ruleForm=e.data,t.dialogFormVisible=!0):t.$message({type:"error",message:e.msg})})},tuikuan(e){this.$confirm("是否申请退款?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"tuikuan?id="+e).then(e=>{200==e.code?this.$message({type:"success",message:e.msg}):this.$message({type:"error",message:e.msg})})}).catch(()=>{this.$message({type:"error",message:"取消退款!"})})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},free(e){var t=this;this.$confirm("是否设定此订单为免支付?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.postRequest("/dingdan/free?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"修改成功!"}),t.getData())})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count.count,e.money=t.count.money),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success("上传成功"),this.ruleForm[this.filed]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let i=this;i.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(i.ruleForm[t]="",i.$message.success("删除成功!")):i.$message.error(e.msg)})}}},n=o,c=(i("a745"),i("2877")),d=Object(c["a"])(n,l,a,!1,null,"76904e12",null);t["default"]=d.exports},d522:function(e,t,i){"use strict";var l=function(){var e=this,t=e._self._c;return t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company))]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone))]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname))]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman))]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.info.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id))]),t("el-descriptions-item",{attrs:{label:"联系方式"}},[e._v(e._s(e.info.linkphone))]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"营业执照"}},[""!=e.info.license&&null!=e.info.license?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.license},on:{click:function(t){return e.showImage(e.info.license)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(e._s(e.info.start_time))]),t("el-descriptions-item",{attrs:{label:"会员年限"}},[e._v(e._s(e.info.year)+"年")])],1),t("el-descriptions",{attrs:{title:"债务人信息",colon:!1}},[t("el-descriptions-item",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.info.debts,size:"mini"}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名"}}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话"}}),t("el-table-column",{attrs:{prop:"money",label:"债务金额（元）"}}),t("el-table-column",{attrs:{prop:"status",label:"状态"}})],1)],1)],1)],1)},a=[],s={name:"UserDetails",props:{id:{type:String,required:!0}},data(){return{info:[]}},watch:{id:{immediate:!0,handler(e){this.getInfo(e)}}},methods:{getInfo(e){let t=this;t.getRequest("/user/read?id="+e).then(e=>{e&&(t.info=e.data)})}}},r=s,o=i("2877"),n=Object(o["a"])(r,l,a,!1,null,null,null);t["a"]=n.exports}}]);
//# sourceMappingURL=chunk-7adf685e.cfc13bb6.js.map