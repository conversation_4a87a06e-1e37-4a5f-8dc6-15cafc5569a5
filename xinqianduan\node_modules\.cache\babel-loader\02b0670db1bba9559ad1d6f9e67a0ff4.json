{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748427648149}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSG9tZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBtb25leV9jb3VudDogMCwKICAgICAgdXNlcl9jb3VudDogMCwKICAgICAgdmlzaXRfY291bnQ6IDEyMzQsCiAgICAgIC8vIOa8lOekuuaVsOaNrgogICAgICBzZWFyY2hfY291bnQ6IDAsCiAgICAgIGV4cG9ydF9jb3VudDogMCwKICAgICAgb3JkZXJfY291bnQ6IDAsCiAgICAgIGdhb2RlX2NvdW50OiAwLAogICAgICB0ZW5neHVuX2NvdW50OiAwLAogICAgICBiYWlkdV9jb3VudDogMCwKICAgICAgc2h1bnFpd2FuZ19jb3VudDogMCwKICAgICAgc2hvd19pbWFnZTogIiIsCiAgICAgIG1lbnVzOiBbXSwKICAgICAgdXJsOiAiL1l1YW5nb25nLyIKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgbmFtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RJVExFOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOe6r+<PERSON><PERSON><PERSON><PERSON>+aooeW8jyAtIOebtOaOpeaPkOS+m+iPnOWNleaVsOaNrgogICAgdGhpcy5tZW51cyA9IFt7CiAgICAgIHBhdGg6ICIvamljaHUiLAogICAgICBuYW1lOiAi5Z+656GA566h55CGIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi9jb25maWciLAogICAgICAgIG5hbWU6ICLln7rnoYDorr7nva4iCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL2Jhbm5lciIsCiAgICAgICAgbmFtZTogIui9ruaSreWbviIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvbmF2IiwKICAgICAgICBuYW1lOiAi6aaW6aG15a+86IiqIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9nb25nZ2FvIiwKICAgICAgICBuYW1lOiAi5YWs5ZGKIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3hpYWRhbiIsCiAgICAgIG5hbWU6ICLorqLljZXnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3R5cGUiLAogICAgICAgIG5hbWU6ICLmnI3liqHnsbvlnosiCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL3Rhb2NhbiIsCiAgICAgICAgbmFtZTogIuWll+mkkOexu+WeiyIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvZGluZ2RhbiIsCiAgICAgICAgbmFtZTogIuetvue6pueUqOaIt+WIl+ihqCIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvcXVuIiwKICAgICAgICBuYW1lOiAi562+57qm5a6i5oi3576kIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3lvbmdodSIsCiAgICAgIG5hbWU6ICLnlKjmiLfnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3VzZXIiLAogICAgICAgIG5hbWU6ICLnlKjmiLfliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIvemhpZnUiLAogICAgICBuYW1lOiAi5pSv5LuY5YiX6KGoIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi9vcmRlciIsCiAgICAgICAgbmFtZTogIuaUr+S7mOWIl+ihqCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi9saWFvdGlhbiIsCiAgICAgIG5hbWU6ICLogYrlpKnliJfooagiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2NoYXQiLAogICAgICAgIG5hbWU6ICLogYrlpKnliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIvZGVidCIsCiAgICAgIG5hbWU6ICLlgLrmnYPnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2RlYnRzIiwKICAgICAgICBuYW1lOiAi5YC65Yqh5Lq65YiX6KGoIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3dlbnNodWd1YW5saSIsCiAgICAgIG5hbWU6ICLmlofkuabnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2Rpbmd6aGkiLAogICAgICAgIG5hbWU6ICLlkIjlkIzlrprliLYiCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL3NoZW5oZSIsCiAgICAgICAgbmFtZTogIuWQiOWQjOWuoeaguCIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvY2F0ZSIsCiAgICAgICAgbmFtZTogIuWQiOWQjOexu+WeiyIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvaGV0b25nIiwKICAgICAgICBuYW1lOiAi5ZCI5ZCM5YiX6KGoIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9sYXd5ZXIiLAogICAgICAgIG5hbWU6ICLlj5HlvovluIjlh70iCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL2tlY2hlbmciLAogICAgICAgIG5hbWU6ICLor77nqIvliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIveXVhbmdvbmciLAogICAgICBuYW1lOiAi5ZGY5bel566h55CGIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi96aGl3ZWkiLAogICAgICAgIG5hbWU6ICLogYzkvY0iCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL3l1YW5nb25nIiwKICAgICAgICBuYW1lOiAi5ZGY5belIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9sdnNoaSIsCiAgICAgICAgbmFtZTogIuW+i+W4iCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi9mdXd1IiwKICAgICAgbmFtZTogIuacjeWKoeeuoeeQhiIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvZnV3dSIsCiAgICAgICAgbmFtZTogIuacjeWKoeWIl+ihqCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi94aW53ZW4iLAogICAgICBuYW1lOiAi5qGI5L6L566h55CGIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi9hbmxpIiwKICAgICAgICBuYW1lOiAi5qGI5L6L5YiX6KGoIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL2x2c2hpZ3VhbmxpIiwKICAgICAgbmFtZTogIuS4k+S4mueuoeeQhiIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvemh1YW55ZSIsCiAgICAgICAgbmFtZTogIuS4k+S4muWIl+ihqCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi9hcmNoaXZlIiwKICAgICAgbmFtZTogIuW9kuaho+euoeeQhiIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvYXJjaGl2ZS9maWxlIiwKICAgICAgICBuYW1lOiAi5paH5Lu25b2S5qGjIgogICAgICB9XQogICAgfV07CiAgICBjb25zb2xlLmxvZygi6I+c5Y2V5pWw5o2u5bey5Yqg6L29OiIsIHRoaXMubWVudXMpOwogIH0sCiAgbWV0aG9kczogewogICAgc2hvd1FyY29kZSgpIHsKICAgICAgLy8g57qv5YmN56uv5qih5byPIC0g5pi+56S65ryU56S65LqM57u056CBCiAgICAgIHRoaXMuc2hvd19pbWFnZSA9ICJkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNakF3SWlCb1pXbG5hSFE5SWpJd01DSWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklqNDhjbVZqZENCM2FXUjBhRDBpTWpBd0lpQm9aV2xuYUhROUlqSXdNQ0lnWm1sc2JEMGlJMlptWmlJdlBqeDBaWGgwSUhnOUlqRXdNQ0lnZVQwaU1UQXdJaUIwWlhoMExXRnVZMmh2Y2owaWJXbGtaR3hsSWlCbWIyNTBMWE5wZW1VOUlqRTBJajdtajdMbnBiN2t1b3pudTdUbm9JRThMM1JsZUhRK1BDOXpkbWMrIjsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBtZW51Q2xpY2soaW5kZXgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goaW5kZXgpOwogICAgfSwKICAgIGdldFF1YW54aWFuKCkgewogICAgICAvLyDnuq/liY3nq6/mqKHlvI8gLSDkuI3pnIDopoHlkI7nq6/mnYPpmZDpqozor4EKICAgICAgY29uc29sZS5sb2coIue6r+WJjeerr+aooeW8j++8jOi3s+i/h+adg+mZkOmqjOivgSIpOwogICAgfSwKICAgIGdldENvdW50QWxsKCkgewogICAgICAvLyDnuq/liY3nq6/mqKHlvI8gLSDkvb/nlKjmvJTnpLrmlbDmja4KICAgICAgY29uc29sZS5sb2coIue6r+WJjeerr+aooeW8j++8jOS9v+eUqOa8lOekuuaVsOaNriIpOwogICAgfSwKICAgIGxvZ291dCgpIHsKICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJJTklUX1RPS0VOIiwgIiIpOwogICAgICB0aGlzLiRzdG9yZS5jb21taXQoIklOSVRfVElUTEUiLCAiIik7CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICBtZXNzYWdlOiAi6YCA5Ye65oiQ5YqfIgogICAgICB9KTsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMuJHJvdXRlci5wdXNoKCIvbG9naW4iKTsKICAgICAgfSwgMTUwMCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "dialogVisible", "money_count", "user_count", "visit_count", "search_count", "export_count", "order_count", "gaode_count", "tengxun_count", "baidu_count", "shunqiwang_count", "show_image", "menus", "url", "computed", "$store", "getters", "GET_TITLE", "mounted", "path", "children", "console", "log", "methods", "showQrcode", "menuClick", "index", "$router", "push", "getQuanxian", "getCountAll", "logout", "commit", "$message", "type", "message", "_this", "setTimeout"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <!-- 如果有子菜单，显示为下拉菜单 -->\r\n          <el-submenu\r\n            v-for=\"(item, index) in menus\"\r\n            v-if=\"item.children && item.children.length > 1\"\r\n            :key=\"'submenu-' + index\"\r\n            :index=\"item.path\"\r\n            popper-class=\"vertical-submenu\"\r\n          >\r\n            <template slot=\"title\">{{ item.name }}</template>\r\n            <el-menu-item\r\n              v-for=\"(child, indexj) in item.children\"\r\n              :key=\"indexj\"\r\n              :index=\"child.path\"\r\n            >\r\n              {{ child.name }}\r\n            </el-menu-item>\r\n          </el-submenu>\r\n          <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\r\n          <el-menu-item\r\n            v-for=\"(item, index) in menus\"\r\n            v-if=\"!item.children || item.children.length <= 1\"\r\n            :key=\"'menuitem-' + index\"\r\n            :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\r\n          >\r\n            {{ item.name }}\r\n          </el-menu-item>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">管理员</span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item\r\n              ><div @click=\"menuClick('/changePwd')\">\r\n                修改密码\r\n              </div></el-dropdown-item\r\n            >\r\n            <el-dropdown-item>\r\n              <div @click=\"logout()\">退出登录</div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{\r\n            this.$router.currentRoute.name\r\n          }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <el-row :gutter=\"12\" v-if=\"this.$router.currentRoute.path == '/'\">\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\"> 访问量 {{ visit_count }}</el-card>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\">\r\n              <span @click=\"showQrcode\">查看二维码</span></el-card\r\n            >\r\n          </el-col>\r\n        </el-row>\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/zhifu\",\r\n        name: \"支付列表\",\r\n        children: [\r\n          { path: \"/order\", name: \"支付列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/liaotian\",\r\n        name: \"聊天列表\",\r\n        children: [\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/debt\",\r\n        name: \"债权管理\",\r\n        children: [\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" },\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" },\r\n          { path: \"/lvshi\", name: \"律师\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"专业管理\",\r\n        children: [\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/archive\",\r\n        name: \"归档管理\",\r\n        children: [\r\n          { path: \"/archive/file\", name: \"文件归档\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-submenu__drop-down .el-menu .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* 强制下拉菜单容器为垂直布局 */\r\n.el-submenu__drop-down,\r\n.el-menu--horizontal .el-submenu__drop-down,\r\n.el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 确保子菜单内的ul也是垂直的 */\r\n.el-submenu__drop-down ul,\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  list-style: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制覆盖任何可能的inline样式 */\r\n.el-submenu__drop-down .el-menu-item[style] {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  position: relative !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 使用更高的CSS优先级 */\r\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对自定义popper-class的样式 - 美化版本 */\r\n.vertical-submenu {\r\n  display: block !important;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\r\n  overflow: hidden !important;\r\n  min-width: 180px !important;\r\n  padding: 8px 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 44px !important;\r\n  line-height: 44px !important;\r\n  padding: 0 20px !important;\r\n  margin: 2px 8px !important;\r\n  background-color: rgba(255, 255, 255, 0.95) !important;\r\n  color: #2c3e50 !important;\r\n  text-align: left !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  font-weight: 500 !important;\r\n  font-size: 14px !important;\r\n  transition: all 0.3s ease !important;\r\n  position: relative !important;\r\n  width: calc(100% - 16px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  color: #fff !important;\r\n  transform: translateX(4px) !important;\r\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:active {\r\n  transform: translateX(2px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:last-child {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n/* 添加一些动画效果 */\r\n.vertical-submenu .el-menu-item::before {\r\n  content: '' !important;\r\n  position: absolute !important;\r\n  left: 0 !important;\r\n  top: 50% !important;\r\n  transform: translateY(-50%) !important;\r\n  width: 3px !important;\r\n  height: 0 !important;\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border-radius: 0 2px 2px 0 !important;\r\n  transition: height 0.3s ease !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover::before {\r\n  height: 20px !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 150px;\r\n  min-width: 150px;\r\n  text-align: right;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n}\r\n\r\n.user-info:hover {\r\n  color: #ffd04b;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background-color: #f5f5f5;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-breadcrumb {\r\n  line-height: 50px;\r\n}\r\n\r\n/* 主内容区域样式 - 新UI风格 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f5f5f5;\r\n  padding: 16px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 页面内容容器 */\r\n.main-content .page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 标签页导航样式 */\r\n.tab-navigation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tab-navigation .el-tabs__header {\r\n  margin: 0;\r\n}\r\n\r\n.tab-navigation .el-tabs__nav-wrap::after {\r\n  height: 1px;\r\n  background-color: #e8e8e8;\r\n}\r\n\r\n/* 搜索筛选区域样式 */\r\n.search-section {\r\n  background: #fafafa;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-left: auto;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table {\r\n  margin-top: 16px;\r\n}\r\n\r\n.data-table .el-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n}\r\n\r\n.data-table .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.data-table .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding: 16px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .top-header {\r\n    flex-direction: column;\r\n    height: auto;\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-center {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .top-menu {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";AA8FA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MAAA;MACAC,YAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACAhB,KAAA;MACA,YAAAiB,MAAA,CAAAC,OAAA,CAAAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAN,KAAA,IACA;MACAO,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,EACA;IAEAuB,OAAA,CAAAC,GAAA,kBAAAV,KAAA;EACA;EACAW,OAAA;IACAC,WAAA;MACA;MACA,KAAAb,UAAA;MACA,KAAAX,aAAA;IACA;IACAyB,UAAAC,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,KAAA;IACA;IACAG,YAAA;MACA;MACAR,OAAA,CAAAC,GAAA;IACA;IACAQ,YAAA;MACA;MACAT,OAAA,CAAAC,GAAA;IACA;IACAS,OAAA;MACA,KAAAhB,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,IAAAC,KAAA;MACAC,UAAA;QACAD,KAAA,CAAAT,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}