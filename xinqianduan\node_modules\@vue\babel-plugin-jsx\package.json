{"name": "@vue/babel-plugin-jsx", "version": "1.2.2", "description": "Babel plugin for Vue 3 JSX", "author": "Amour1688 <<EMAIL>>", "homepage": "https://github.com/vuejs/babel-plugin-jsx/tree/dev/packages/babel-plugin-jsx#readme", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/vuejs/babel-plugin-jsx"}, "bugs": {"url": "https://github.com/vuejs/babel-plugin-jsx/issues"}, "files": ["dist"], "dependencies": {"@babel/helper-module-imports": "~7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/template": "^7.23.9", "@babel/traverse": "^7.23.9", "@babel/types": "^7.23.9", "camelcase": "^6.3.0", "html-tags": "^3.3.1", "svg-tags": "^1.0.0", "@vue/babel-helper-vue-transform-on": "1.2.2", "@vue/babel-plugin-resolve-type": "1.2.2"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.5", "@types/svg-tags": "^1.0.2", "@vue/runtime-dom": "^3.4.15", "@vue/test-utils": "^2.4.4", "regenerator-runtime": "^0.14.1", "vue": "^3.4.15"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}, "scripts": {"build": "tsup", "watch": "tsup --watch"}}