<?php
namespace app\admin\controller;
use think\Request;
use models\{Gongdans,Users,Yuangongs};
use untils\{JsonService,WechatApp};
class Dingzhi
{
    protected $model;
    public function __construct(Gongdans $model){
        //parent::__construct();
        $this->model=$model;

    }
    public function index(Request $request,$page=1,$size=20){
        $where=[];
        $search=$request->post();
        $where[]=['type','=',2];
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }

        if($search['is_deal']!=-1){
            $where[]=['is_deal','=',$search['is_deal']];
        }
        $res = $this->model::withAttr('phone',function($v,$d){
            return Users::where(['id'=>$d['uid']])->value('phone');
        })->append(['phone'])->withAttr('nickname',function($v,$d){
            return Users::where(['id'=>$d['uid']])->value('nickname');
        })->append(['nickname','phone'])
            ->withAttr('type',function($v,$d){
            switch ($v) {
                case '1':
                    return '合同审核';  // code...
                    break;
                case '2':
                    return '合同定制';  // code...
                    break;
                case '3':
                    return '发律师函';  // code...
                    break;
            }
        })->withAttr('is_deal',function($v,$d){
            switch ($v) {
                case '1':
                    return '处理中';
                    break;
                 case '2':
                    return '已完成';
                    break;
                     case '0':
                    return '待处理';
                    break;


            }
        })
        ->where($where)
        ->where(['is_delete'=>0])
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');

        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(empty($errorMsg) && $form['is_deal'] == 2){
            $openid = Users::where(['id'=>$form['uid']])->value('openid');
            if(!empty($openid)){
                //发送公众号消息
                $arr['thing4'] = ['value'=>"合同定制"];//订单名称
                $arr['phrase11'] = ['value'=>'审核完成'];//订单状态
                $arr['time3'] = ['value'=>date('Y-m-d H:i:s',time())];//受理日期
                $arr['thing6'] = ['value'=>'您定制的合同文件已上传，请下载'];//订单备注
                WechatApp::send_temp($openid,$arr);
            }
        }
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model::withAttr('type_title',function($v,$d){
            switch ($d['type']) {
                case '1':
                    return '合同审核';
                    break;

                case '2':
                    return '合同定制';
                    break;
                case '3':
                    return '发律师函';
                    break;
            }
        })->append(['type_title'])->find($id);
        $res->hidden(['create_time','update_time']);

        if(empty($res)) return JsonService::fail('获取数据失败');

        else return JsonService::successful('成功',$res);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->saveData(['id'=>$id,'is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function tuikuan($id){
        if(empty($id)) return JsonService::fail('数据不存在');
        $info = $this->model->find($id);
        if($info->is_pay!=2){
            return JsonService::fail('订单未支付/已退款,不能申请退款');
        }
        $res = $this->model->saveData(['id'=>$id,'is_pay'=>3,'refund_time'=>now()]);
        if(empty($res)) return JsonService::fail('退款成功');
        else return JsonService::successful('退款失败');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
}
