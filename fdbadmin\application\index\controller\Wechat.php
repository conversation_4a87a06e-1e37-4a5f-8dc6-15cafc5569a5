<?php
namespace app\index\controller;
use think\Request;
use untils\{JsonService,Jssdk,WxApi,WechatPay};
use models\{Banners,Users,Orders,Debttrans};
use think\Controller;
use think\facade\View;

class Wechat extends Controller{

	public function getSignPackage(Request $request)
    {
        $url = $request->param('url');
       // $jssdk = new Jssdk("wxb2fc940e0dfbc084", "c3a38952ec64e27552ce936b14afb10f");
        $jssdk = new Jssdk("wx82309797155b7b16", "8348621479a22b3c39dc929fcbe89548");
        $signPackage = $jssdk->GetSignPackage(urldecode($url));
        return JsonService::successful("ok",$signPackage);
    }

    public function autho(Request $request,WxApi $WxApi,Users $user){
    	//1.获取code
        $code = $request->post('code');

        //2、获取openid
        $str = $WxApi->_access_token($code);
        if(!empty($str['access_token'])) {
            //3、获取用户信息 headimgurl
            $userinfo = $WxApi->get_userinfo($str['access_token'], $str['openid']);
            $uid = '';
            //4、通过openid查询数据库
            $hasOne = $user->where(['openid' => $str['openid']])->find();
            //5、判断用户是否存在,生成token
            $data = [
                'nickname' => user_text_encode($userinfo['nickname']),
                'openid' => $userinfo['openid'],
                'headimgurl' => $userinfo['headimgurl']
            ];
            if (empty($hasOne)) {
                //新增用户
                $uid = $user->saveData($data);
            } else {
                //更新openid
                $uid = $hasOne['id'];
                $user->where(['id' => $hasOne['id']])->update($data);
            }
            $data['id'] = $uid;
            return json(array('code'=>200,'msg'=>'登陆成功','data'=>$data));
        }else{
            return json(array('code'=>400,'msg'=>'登陆失败','data'=>array()));
        }
    }
    
    public function pay(Request $request,Orders $model,Users $Users){
        
		$order_sn = $request->post('order_sn');	
    	//$notify_url = 'https://fdb.zhongfabang.cn/index/Wechat/notify';
        $notify_url = 'https://web.faduobang.com/index/Wechat/notify';
		$wechatPay = new WechatPay();
		$info  = $model->where(['order_sn'=>$order_sn])->find();
		
		$openid =$Users->where(['id'=>$info['uid']])->value('openid');
		$time = time();
		settype($time, "string");
		$order=[
		    'body'=>'测试支付',
		    'total_fee'=>$info['total_price']*100,
		    'out_trade_no'=>$order_sn,
		    'trade_type'=>'JSAPI',
		    'notify_url'=>$notify_url,
		    'openid'=>$openid
		];
		$result = $wechatPay->_unifiedOrder($order);

		$resdata = array(
            'appId'         => strval($result['appid']),
            'nonceStr'      => strval($result['nonce_str']),
            'package'       => "prepay_id=".$result['prepay_id'],
            'signType'      => 'MD5',
            'timeStamp'     => $time
        );
        
        $resdata['paySign']=$wechatPay->_makeSign($resdata);
    	if(empty($result)) return JsonService::fail('失败');
    	else return JsonService::successful('成功',$resdata);
    }
    
    public function notify(){

        $model = new WechatPay();
    	$result=$model->_notify();
        $now = time();
        if ($result) {
	        // 验证成功 修改数据库的订单状态等 $result['out_trade_no']为订单id
			try {

            file_put_contents('./notify.text', "支付开始", FILE_APPEND);
            file_put_contents('./notify.text', PHP_EOL, FILE_APPEND);

			$orderModel = new Orders();
			$info =$orderModel->where(['order_sn'=>$result['out_trade_no']])->find();

			file_put_contents('./notify.text', print_r($info), FILE_APPEND);
            $up_data = [
                "id" => $info["id"],
                "is_pay" => 2,
                "is_deal" => 1,
                "pay_time" => $now
            ];
            if($info["type"] == 5){
                $up_data["is_deal"] = 2;
            }
			$orderModel->saveData($up_data);

			$debttransModel = new Debttrans();
			$debt_info = $debttransModel
                ->where(['order_sn'=>$result['out_trade_no']])
                ->find();

			if($debt_info){
                $up_data = [
                    "id" => $debt_info["id"],
                    "pay_type" => 3,
                    "pay_order_type" => 2,
                    "pay_time" => $now
                ];
                $debttransModel->saveData($up_data);
            }

            file_put_contents('./notify.text', "支付结束", FILE_APPEND);
            file_put_contents('./notify.text', PHP_EOL, FILE_APPEND);
			} catch (Exception $e) {
				 file_put_contents('./notify.text', $e);
			}
	    }
    }
}