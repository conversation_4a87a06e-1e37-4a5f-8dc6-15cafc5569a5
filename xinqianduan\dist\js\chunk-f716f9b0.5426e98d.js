(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f716f9b0"],{"0091":function(t,a,s){"use strict";s("67a0")},"26b2":function(t,a,s){"use strict";var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"debt-detail-container"},[a("div",{staticClass:"action-bar"},[a("el-button",{attrs:{size:"medium",type:"primary",icon:"el-icon-download"},on:{click:t.exports}},[t._v(" 导出跟进记录 ")])],1),a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-money"}),a("span",{staticClass:"card-title"},[t._v("债务基本信息")]),a("div",{staticClass:"debt-status"},[a("el-tag",{attrs:{type:t.getDebtStatusType(),size:"medium"}},[t._v(" "+t._s(t.getDebtStatusText())+" ")])],1)]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("委托人")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.nickname||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("债务人姓名")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.name||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("债务人电话")]),a("div",{staticClass:"info-value phone-number"},[t._v(t._s(t.info.tel||"未填写"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("债务人地址")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.address||"未填写"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"amount-card debt-amount"},[a("div",{staticClass:"amount-label"},[t._v("债务总金额")]),a("div",{staticClass:"amount-value"},[t._v("¥"+t._s(t.formatMoney(t.info.money)))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"amount-card back-amount"},[a("div",{staticClass:"amount-label"},[t._v("已回款金额")]),a("div",{staticClass:"amount-value"},[t._v("¥"+t._s(t.formatMoney(t.info.back_money)))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"amount-card remaining-amount"},[a("div",{staticClass:"amount-label"},[t._v("未回款金额")]),a("div",{staticClass:"amount-value"},[t._v("¥"+t._s(t.formatMoney(t.info.un_money)))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("提交时间")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.ctime||"未填写"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("最后修改时间")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.utime||"未填写"))])])])],1)],1),t.info.cards&&t.info.cards.length>0?a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-postcard"}),a("span",{staticClass:"card-title"},[t._v("债务人身份信息")])]),a("div",{staticClass:"id-cards-grid"},t._l(t.info.cards,(function(s,e){return a("div",{key:e,staticClass:"id-card-item",on:{click:function(a){return t.showImage(s)}}},[a("el-image",{staticClass:"id-card-image",attrs:{src:s,fit:"cover"}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline"})])]),a("div",{staticClass:"id-card-label"},[t._v(" "+t._s(0===e?"身份证正面":"身份证反面")+" ")])],1)})),0)]):t._e(),a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-document"}),a("span",{staticClass:"card-title"},[t._v("案由描述")])]),a("div",{staticClass:"case-description"},[a("p",[t._v(t._s(t.info.case_des||"暂无案由描述"))])])]),t.info.images&&t.info.images.length>0?a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-picture"}),a("span",{staticClass:"card-title"},[t._v("证据图片")]),a("el-button",{staticClass:"header-action",attrs:{size:"small",type:"primary",icon:"el-icon-download"},on:{click:function(a){return t.downloadFiles(t.info.images_download)}}},[t._v(" 全部下载 ")])],1),a("div",{staticClass:"evidence-images-grid"},t._l(t.info.images,(function(s,e){return a("div",{key:e,staticClass:"evidence-image-item"},[a("el-image",{staticClass:"evidence-image",attrs:{src:s,"preview-src-list":t.info.images,fit:"cover"}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline"})])]),a("div",{staticClass:"evidence-actions"},[a("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-download"},on:{click:function(a){return t.downloadSingleFile(s,"evidence_"+(e+1))}}},[t._v(" 下载 ")])],1)],1)})),0)]):t._e(),t.info.attach_path&&t.info.attach_path.length>0?a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-folder"}),a("span",{staticClass:"card-title"},[t._v("证据文件")])]),a("div",{staticClass:"evidence-files-list"},t._l(t.info.attach_path,(function(s,e){return s?a("div",{key:e,staticClass:"file-item"},[a("div",{staticClass:"file-info"},[a("i",{staticClass:"file-icon",class:t.getFileIcon(s)}),a("div",{staticClass:"file-details"},[a("div",{staticClass:"file-name"},[t._v("文件"+t._s(e+1))]),a("div",{staticClass:"file-type"},[t._v(t._s(t.getFileExtension(s)))])])]),a("div",{staticClass:"file-actions"},[a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-view"},on:{click:function(a){return t.viewFile(s)}}},[t._v(" 查看 ")]),a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-download"},on:{click:function(a){return t.downloadSingleFile(s,"file_"+(e+1))}}},[t._v(" 下载 ")])],1)]):t._e()})),0)]):t._e(),a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-time"}),a("span",{staticClass:"card-title"},[t._v("跟进记录")]),a("div",{staticClass:"record-count"},[t._v(" 共 "+t._s(t.info.debttrans?t.info.debttrans.length:0)+" 条记录 ")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"follow-up-table",staticStyle:{width:"100%"},attrs:{data:t.info.debttrans,size:"medium",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[a("el-table-column",{attrs:{prop:"day",label:"跟进日期",width:"110"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("i",{staticClass:"el-icon-date"}),t._v(" "+t._s(s.row.day)+" ")]}}])}),a("el-table-column",{attrs:{prop:"ctime",label:"提交时间",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(s.row.ctime)+" ")]}}])}),a("el-table-column",{attrs:{prop:"au_id",label:"操作人员",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-tag",{attrs:{type:"info",size:"small"}},[t._v(t._s(s.row.au_id))])]}}])}),a("el-table-column",{attrs:{prop:"type",label:"进度类型",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-tag",{attrs:{type:t.getProgressType(s.row.type),size:"small"}},[t._v(" "+t._s(s.row.type)+" ")])]}}])}),a("el-table-column",{attrs:{prop:"total_price",label:"费用金额",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.total_price&&"0"!==s.row.total_price?a("span",{staticClass:"money-text"},[t._v(" ¥"+t._s(s.row.total_price)+" ")]):a("span",{staticClass:"no-data"},[t._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"content",label:"费用内容",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row.content||"-")+" ")]}}])}),a("el-table-column",{attrs:{prop:"back_money",label:"回款金额",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.back_money&&"0"!==s.row.back_money?a("span",{staticClass:"money-text success"},[t._v(" ¥"+t._s(s.row.back_money)+" ")]):a("span",{staticClass:"no-data"},[t._v("-")])]}}])}),a("el-table-column",{attrs:{prop:"pay_type",label:"支付状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-tag",{attrs:{type:"已支付"===s.row.pay_type?"success":"warning",size:"small"}},[t._v(" "+t._s(s.row.pay_type||"未支付")+" ")])]}}])}),a("el-table-column",{attrs:{prop:"pay_time",label:"支付时间",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row.pay_time||"-")+" ")]}}])}),a("el-table-column",{attrs:{prop:"pay_order_type",label:"支付方式",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row.pay_order_type||"-")+" ")]}}])}),a("el-table-column",{attrs:{prop:"desc",label:"进度描述","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("div",{staticClass:"desc-content"},[t._v(t._s(s.row.desc))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"80"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-button",{staticClass:"danger-btn",attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return a.preventDefault(),t.delData(s.$index,s.row.id)}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 移除 ")])]}}])})],1),t.info.debttrans&&0!==t.info.debttrans.length?t._e():a("div",{staticClass:"empty-data"},[a("i",{staticClass:"el-icon-document"}),a("p",[t._v("暂无跟进记录")])])],1),a("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(a){t.dialogVisible=a}}},[a("el-image",{staticStyle:{width:"100%"},attrs:{src:t.show_image}})],1)],1)},i=[],l={name:"DebtDetail",props:{id:{type:[String,Number],required:!0}},data(){return{info:{nickname:"",name:"",tel:"",address:"",money:"",back_money:"",un_money:"",ctime:"",utime:"",case_des:"",cards:[],images:[],images_download:[],attach_path:[],debttrans:[]},loading:!1,dialogVisible:!1,show_image:""}},watch:{id:{immediate:!0,handler(t){this.getInfo(t)}}},methods:{getInfo(t){let a=this;console.log("正在获取债务详情，ID:",t),a.loading=!0,setTimeout(()=>{const s={id:t,nickname:"张三",name:"债务人李四",tel:"13900139001",address:"北京市朝阳区测试街道123号",money:"50000",back_money:"10000",un_money:"40000",ctime:"2024-01-01 10:00:00",utime:"2024-01-15 15:30:00",case_des:"借款纠纷，借款人未按约定时间还款，现申请追讨欠款及利息。",cards:["/static/images/id_card_front.jpg","/static/images/id_card_back.jpg"],images:["/static/images/evidence1.jpg","/static/images/evidence2.jpg","/static/images/evidence3.jpg"],images_download:[{name:"证据1.jpg",path:"/static/images/evidence1.jpg"},{name:"证据2.jpg",path:"/static/images/evidence2.jpg"}],attach_path:["/static/files/contract.pdf","/static/files/bank_record.xlsx"],debttrans:[{id:1,day:"2024-01-15",ctime:"2024-01-15 10:30:00",au_id:"调解员王五",type:"电话联系",total_price:"0",content:"联系费用",rate:"0",back_money:"0",pay_type:"未支付",pay_time:"",pay_order_type:"",desc:"已与债务人取得联系，对方表示将在本月底前还款"},{id:2,day:"2024-01-10",ctime:"2024-01-10 14:20:00",au_id:"法务赵六",type:"发送催款函",total_price:"200",content:"律师函费用",rate:"0",back_money:"0",pay_type:"已支付",pay_time:"2024-01-10 14:25:00",pay_order_type:"微信支付",desc:"向债务人发送正式催款函，要求在15日内还款"}]};a.info=s,a.loading=!1,console.log("债务详情数据加载完成:",s)},500)},downloadFiles(t){t.forEach(t=>{const a=document.createElement("a");a.href=t.path,a.download=t.name,a.click()})},exports:function(){let t=this;location.href="/admin/debt/view?token="+t.$store.getters.GET_TOKEN+"&export=1&id="+t.ruleForm.id},showImage(t){this.show_image=t,this.dialogVisible=!0},downloadSingleFile(t,a){const s=document.createElement("a");s.href=t,s.download=a,s.click()},viewFile(t){window.open(t,"_blank")},getFileIcon(t){const a=this.getFileExtension(t).toLowerCase(),s={pdf:"el-icon-document",doc:"el-icon-document",docx:"el-icon-document",xls:"el-icon-s-grid",xlsx:"el-icon-s-grid",txt:"el-icon-document",zip:"el-icon-folder",rar:"el-icon-folder"};return s[a]||"el-icon-document"},getFileExtension(t){return t.split(".").pop()||""},formatMoney(t){return t&&"0"!==t?parseFloat(t).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00"},getDebtStatusType(){const t=parseFloat(this.info.un_money||0);return 0===t?"success":t>0?"warning":"info"},getDebtStatusText(){const t=parseFloat(this.info.un_money||0);return 0===t?"已结清":t>0?"未结清":"处理中"},getProgressType(t){const a={"电话联系":"primary","发送催款函":"warning","法院起诉":"danger","调解成功":"success","回款确认":"success"};return a[t]||"info"},delData(t,a){this.$confirm("确定要移除这条跟进记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.info.debttrans.splice(t,1),this.$message.success("删除成功")}).catch(()=>{this.$message.info("已取消删除")})}}},n=l,o=(s("474f"),s("2877")),c=Object(o["a"])(n,e,i,!1,null,"d8466e1a",null);a["a"]=c.exports},"474f":function(t,a,s){"use strict";s("5d57")},"5d57":function(t,a,s){},"67a0":function(t,a,s){},d522:function(t,a,s){"use strict";var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"user-detail-container"},[a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-user"}),a("span",{staticClass:"card-title"},[t._v("客户基本信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("公司名称")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.company||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("手机号")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.phone||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("客户姓名")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.nickname||"未填写"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("联系人")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.linkman||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("联系方式")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.linkphone||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("用户来源")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.yuangong_id||"未填写"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("开始时间")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.start_time||"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("会员年限")]),a("div",{staticClass:"info-value"},[t._v(t._s(t.info.year?t.info.year+"年":"未填写"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("头像")]),a("div",{staticClass:"info-value"},[t.info.headimg&&""!==t.info.headimg?a("el-avatar",{staticStyle:{cursor:"pointer"},attrs:{src:t.info.headimg,size:50},nativeOn:{click:function(a){return t.showImage(t.info.headimg)}}}):a("span",{staticClass:"no-data"},[t._v("未上传")])],1)])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"info-label"},[t._v("营业执照")]),a("div",{staticClass:"info-value"},[t.info.license&&""!==t.info.license?a("el-image",{staticStyle:{width:"100px",height:"100px",cursor:"pointer"},attrs:{src:t.info.license,fit:"cover"},on:{click:function(a){return t.showImage(t.info.license)}}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline"})])]):a("span",{staticClass:"no-data"},[t._v("未上传")])],1)])])],1)],1),a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-s-custom"}),a("span",{staticClass:"card-title"},[t._v("服务团队")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("调解员")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.tiaojie_name||"未分配"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("法务专员")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.fawu_name||"未分配"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("立案专员")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.lian_name||"未分配"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("合同专员")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.htsczy_name||"未分配"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("律师")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.ls_name||"未分配"))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"team-item"},[a("div",{staticClass:"team-role"},[t._v("业务员")]),a("div",{staticClass:"team-name"},[t._v(t._s(t.info.ywy_name||"未分配"))])])])],1)],1),a("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-money"}),a("span",{staticClass:"card-title"},[t._v("债务人信息")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.info.debts,size:"medium",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[a("el-table-column",{attrs:{prop:"name",label:"债务人姓名",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-tag",{attrs:{type:"primary",size:"small"}},[t._v(t._s(s.row.name))])]}}])}),a("el-table-column",{attrs:{prop:"tel",label:"债务人电话",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("span",{staticClass:"phone-number"},[t._v(t._s(s.row.tel))])]}}])}),a("el-table-column",{attrs:{prop:"money",label:"债务金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("span",{staticClass:"money-amount"},[t._v("¥"+t._s(s.row.money))])]}}])}),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-tag",{attrs:{type:"已完成"===s.row.status?"success":"warning",size:"small"}},[t._v(" "+t._s(s.row.status)+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.viewDebtDetail(s.row)}}},[a("i",{staticClass:"el-icon-view"}),t._v(" 详情 ")])]}}])})],1),t.info.debts&&0!==t.info.debts.length?t._e():a("div",{staticClass:"empty-data"},[a("i",{staticClass:"el-icon-document"}),a("p",[t._v("暂无债务人信息")])])],1),a("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(a){t.dialogVisible=a}}},[a("el-image",{attrs:{src:t.show_image}})],1)],1)},i=[],l={name:"UserDetails",props:{id:{type:[String,Number],required:!0}},data(){return{info:{},loading:!1,dialogVisible:!1,show_image:""}},watch:{id:{immediate:!0,handler(t){t&&0!=t&&(console.log("UserDetails 接收到 ID:",t),this.getInfo(t))}}},methods:{getInfo(t){let a=this;console.log("正在获取用户信息，ID:",t),a.loading=!0,setTimeout(()=>{const s={id:t,company:"测试公司有限公司",phone:"13800138001",nickname:"张三",linkman:"李四",headimg:"",yuangong_id:"微信小程序",linkphone:"13800138002",tiaojie_name:"王调解员",fawu_name:"赵法务",lian_name:"钱立案员",htsczy_name:"孙合同员",ls_name:"周律师",ywy_name:"吴业务员",license:"",start_time:"2024-01-01",year:1,debts:[{name:"债务人A",tel:"13900139001",money:"50000",status:"处理中"},{name:"债务人B",tel:"13900139002",money:"30000",status:"已完成"}]};a.info=s,a.loading=!1,console.log("用户数据加载完成:",s)},500)},showImage(t){this.show_image=t,this.dialogVisible=!0},viewDebtDetail(t){console.log("查看债务人详情:",t),this.$message.info("债务人详情功能待开发")}}},n=l,o=(s("0091"),s("2877")),c=Object(o["a"])(n,e,i,!1,null,"4468717a",null);a["a"]=c.exports}}]);
//# sourceMappingURL=chunk-f716f9b0.5426e98d.js.map