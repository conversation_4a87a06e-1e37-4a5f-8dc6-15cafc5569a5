{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748425644041}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_this", "emojiData", "audioplay", "name", "components", "data", "userss", "lvshiss", "yuangongss", "table", "gridData", "ruleForm", "dialogFormVisible", "selectId", "activeName", "search", "active", "imgUlr", "yon_id", "id", "isShowSeach", "type", "lists", "la", "Names", "isShowPopup", "textContent", "lv<PERSON><PERSON>", "pic_path", "file_path", "list", "timer", "users", "quns", "quliaoIndex", "quli<PERSON><PERSON>", "isEmji", "title", "yuanshiquns", "yuanshiusers", "methods", "editData", "getInfo", "desc", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "quanyuan", "postRequest", "lvshis", "yuangongs", "saveData", "$refs", "validate", "valid", "getData", "<PERSON><PERSON><PERSON>", "uid", "showDaiban", "is_daiban", "getList", "changeKeyword", "e", "target", "_value", "filter", "toLowerCase", "includes", "daiban", "openEmji", "console", "log", "changeFile", "field", "openFile", "window", "open", "openImg", "img", "beforeUpload", "file", "split", "showClose", "handleSuccess", "sendImg", "handleSuccess1", "flie", "sendFile", "redSession", "index", "changeQun", "count", "<PERSON><PERSON><PERSON><PERSON>", "item", "change", "del", "handleScroll", "scrollTop", "send", "sendMessage", "length", "scrollHeight", "loading", "lvshi_id", "yuangong_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "content", "orther_id", "direction", "files", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "delImage", "fileName", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted", "sessionStorage", "getItem", "setInterval"], "sources": ["src/views/pages/yonghu/chat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"body-div\" @click=\"isEmji = false\">\r\n    <div class=\"content\">\r\n      <div class=\"msglist\">\r\n        <!-- 搜索 -->\r\n        <div class=\"wrapper\">\r\n          <div class=\"search-wrapper\">\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"searchInput\"\r\n              placeholder=\"搜索\"\r\n              @change=\"changeKeyword\"\r\n            />\r\n            <div\r\n              v-if=\"isShowSeach\"\r\n              class=\"searchInput-delete\"\r\n              @click=\"del\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <el-tag @click=\"showDaiban('2')\">群聊</el-tag>\r\n        <el-tag type=\"success\" @click=\"showDaiban('1')\">代办</el-tag>\r\n\r\n        <!-- 好友列表 -->\r\n        <ul class=\"msg-left-box\">\r\n          <!--工作群-->\r\n          <li\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === quliaoIndex }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"38\" height=\"38\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.create_time }}</span>\r\n              <p class=\"lastmsg\">{{ item.desc }}</p>\r\n              <p class=\"number-badge\" v-if=\"item.count > 0\">{{ item.count }}</p>\r\n            </div>\r\n          </li>\r\n          <li\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === selectId }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"42\" height=\"42\" :src=\"item.pic_path\" />\r\n              <span>99</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.time }}</span>\r\n              <p class=\"lastmsg\">{{ item.content }}</p>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"chatbox\">\r\n        <!-- v-loading=\"listLoading\" -->\r\n        <div class=\"message\">\r\n          <header class=\"header\">\r\n            <div class=\"friendname\" v-if=\"true\">\r\n              <!-- {{ lists.user.name }} -->\r\n              {{ title }}\r\n            </div>\r\n          </header>\r\n        </div>\r\n        <!-- 聊天框 -->\r\n        <div ref=\"list\" class=\"message-wrapper\" @scroll=\"handleScroll()\">\r\n          <div class=\"msg-box\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <div class=\"msg-time\">\r\n              <span>{{ item.create_time }}</span>\r\n            </div>\r\n            <div\r\n              :class=\"['chatMsg-box', { oneself: item.yuangong_id == yon_id }]\"\r\n            >\r\n              <div class=\"chat-name\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div :class=\"['chatMsg-flex']\">\r\n                <div class=\"flex-view\">\r\n                  <img :src=\"item.avatar\" />\r\n                  <!-- <span>{{ item.title }}</span> -->\r\n                </div>\r\n                <div class=\"flex-view\">\r\n                  <!-- 图片 -->\r\n                  <div class=\"chatMsg-img\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'voice'\">\r\n\t\t\t\t\t  <div class=\"\" style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t  \t<audioplay :recordFile=\"item.content\"></audioplay>\r\n\t\t\t\t\t  \t<div>{{ item.datas }}</div>\r\n\t\t\t\t\t  </div>\r\n                   \r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <!-- 文件 -->\r\n                  <div class=\"file-box\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-flex\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-name\">{{ item.files.name }}</div>\r\n                      <div class=\"file-size\">{{ item.files.size }}</div>\r\n                    </div>\r\n                    <div class=\"file-flex\">\r\n                      <img src=\"img/wenjian.png\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t<div style=\"position: absolute; top: 14px;right: 20px; font-size: 32px;\r\n    cursor: pointer;\" @click=\"quanyuan\">···</div>\r\n\t\t<div class=\"\" style=\"position: absolute;      overflow-y: auto;  width: 200px;\r\n    background: #f2f2f2;\r\n    height: 690px;right:-200px;top: 0px;\" v-if=\"la==true\">\r\n\t\t<div class=\"\">\r\n\t\t\t<div class=\"chat-list-box\" >\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t用户\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\"> \r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(item, index) in userss\" :key=\"index\">\r\n\t\t\t\t\t\t<div v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<img :src=\"value.headimg\"/>\r\n\t\t\t\t\t\t\t<div class=\"sl\">{{value.nickname}}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\r\n\t\t\t<div class=\"chat-list-box\" v-for=\"(item,index) in yuangongss\" :key=\"index\">\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t{{item.zhiwei}}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\">\r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t<img :src=\"value.pic_path\" />\r\n\t\t\t\t\t\t<div class=\"sl\">{{value.title}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\r\n\t</div>\r\n        <!-- 输入框 -->\r\n        <div class=\"input-box\">\r\n          <div class=\"workbar-box\">\r\n            <div class=\"upload-emji\">\r\n              <img src=\"img/biaoqing.png\" alt=\"\" @click.stop=\"openEmji\" />\r\n              <div class=\"emji-box\" v-show=\"isEmji\">\r\n                <div class=\"biao-box\">\r\n                  <div\r\n                    class=\"biao-flex\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"upload-file\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                <img src=\"img/insert_img.png\" alt=\"\"\r\n              /></el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"changeFile('image')\">\r\n              <!-- <input\r\n                type=\"file\"\r\n                title=\"选择文件发送\"\r\n                autocomplete=\"off\"\r\n                accept=\"application/*\"\r\n              /> -->\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <img src=\"img/wenjian.png\" alt=\"\" />\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"daiban\">\r\n              <img src=\"img/daiban.png\" alt=\"\" />\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"showgongdan\">\r\n              <img src=\"img/gongdan.png\" alt=\"\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"input-text\">\r\n            <textarea\r\n              placeholder=\"您想说什么？\"\r\n              v-model=\"textContent\"\r\n            ></textarea>\r\n          </div>\r\n          <div class=\"input-btn\">\r\n            <span @click=\"send\">发送Enter</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"img-popup\" :class=\"{ 'show-popup': isShowPopup }\">\r\n      <div>\r\n        <div class=\"img-div\">\r\n          <img :src=\"imgUlr\" alt=\"\" />\r\n        </div>\r\n        <div class=\"close\">\r\n          <span @click=\"isShowPopup = false\">×</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n\t\tuserss:[],\r\n\t\tlvshiss:[],\r\n\t\tyuangongss:[],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      // 得知当前选择的是哪个对话\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n\t  id:0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      //聊天记录\r\n      lists: [],\r\n\t  la:false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\tquanyuan(){\r\n\t\t_this.la =!_this.la\r\n\t\t_this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n\t\t  if (resp.code == 200) {\r\n\t\t    _this.userss = resp.data.users\r\n\t\t    _this.lvshiss = resp.data.lvshis\r\n\t\t    _this.yuangongss = resp.data.yuangongs\r\n\t\t\t\r\n\t\t  }\r\n\t\t});\r\n\t},\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n\t\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target._value;\r\n\r\n      _this.quns = quns.filter((data) => data.title.search(search) != -1);\r\n      _this.users = users.filter(\r\n        (data) =>\r\n          !search || data.title.toLowerCase().includes(search.toLowerCase())\r\n      );\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    //查看图片\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n\t\t !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n\t_this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n\t  _this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    //发送\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n      //   _this.getList();\r\n      /* setTimeout(\r\n         () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n         0*/\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\t\t\r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n\t\t\t_this.id = id;\r\n\tconsole.log(_this.id)\r\n\t\t\t\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n\t\t\t\r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  //    _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n\t\t\tid = 1;\r\n\t\t\t_this\r\n\t\t\t  .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n\t\t\t  .then((resp) => {\r\n\t\t\t    _this.getQun1();\r\n\t\t\t    if (resp.code == 200) {\r\n\t\t\t      _this.list.push(resp.data);\r\n\t\t\t\r\n\t\t\t      setTimeout(\r\n\t\t\t        () =>\r\n\t\t\t          (_this.$refs.list.scrollTop =\r\n\t\t\t            _this.$refs.list.scrollHeight),\r\n\t\t\t        1000\r\n\t\t\t      );\r\n\t\t\t    }\r\n\t\t\t    _this.loading = false;\r\n\t\t\t  });\r\n\t\t}\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        //  let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            //  uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        //      let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n\t\t\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        //f7 118 f8 119 f10 120\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n\r\n    //默认获取列表第一个\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t\r\n\t.chat-list-box{\r\n\t\tmargin-bottom: 10px;\r\n\t\t.chat-list-title{\r\n\t\t\tbackground: #ededed;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tpadding: 10px 5px;\r\n\t\t}\r\n\t\t.chat-list{\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\twidth: 100%;\r\n\t\t\t\r\n\t\t\t.chat-flex{\r\n\t\t\t\tpadding: 0px 8px;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 30px;\r\n\t\t\t\timg{\r\n\t\t\t\t\twidth: 45px;\r\n\t\t\t\t\theight: 45px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t}\r\n\t\t\t\t.sl{\r\n\t\t\t\t\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tmax-width:100px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n.chat-name {\r\n  padding-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #333333;\r\n  text-align: left;\r\n}\r\n.oneself {\r\n  .chat-name {\r\n    text-align: right !important;\r\n  }\r\n}\r\nhtml,\r\nbody,\r\ndiv,\r\nspan,\r\napplet,\r\nobject,\r\niframe,\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\np,\r\nblockquote,\r\npre,\r\na,\r\nabbr,\r\nacronym,\r\naddress,\r\nbig,\r\ncite,\r\ncode,\r\ndel,\r\ndfn,\r\nem,\r\nimg,\r\nins,\r\nkbd,\r\nq,\r\ns,\r\nsamp,\r\nsmall,\r\nstrike,\r\nstrong,\r\nsub,\r\nsup,\r\ntt,\r\nvar,\r\nb,\r\nu,\r\ni,\r\ncenter,\r\ndl,\r\ndt,\r\ndd,\r\nol,\r\nul,\r\nli,\r\nfieldset,\r\nform,\r\nlabel,\r\nlegend,\r\ntable,\r\ncaption,\r\ntbody,\r\ntfoot,\r\nthead,\r\ntr,\r\nth,\r\ntd,\r\narticle,\r\naside,\r\ncanvas,\r\ndetails,\r\nembed,\r\nfigure,\r\nfigcaption,\r\nfooter,\r\nheader,\r\nmenu,\r\nnav,\r\noutput,\r\nruby,\r\nsection,\r\nsummary,\r\ntime,\r\nmark,\r\naudio,\r\nvideo,\r\ninput {\r\n  margin: 0;\r\n  padding: 0;\r\n  border: 0;\r\n  font-size: 100%;\r\n  font-weight: normal;\r\n  vertical-align: baseline;\r\n}\r\n\r\n.body-div {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 87vh;\r\n  background: #999999;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 6px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content {\r\n\tposition: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  width: 900px;\r\n  background: #f2f2f2;\r\n  height: 690px;\r\n}\r\n\r\n.content .msglist {\r\n  width: 350px;\r\n  background: #e6e6e6;\r\n  overflow-y: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box {\r\n  height: calc(100% - 60px);\r\n  overflow: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  padding: 12px;\r\n  -webkit-transition: background-color 0.1s;\r\n  transition: background-color 0.1s;\r\n  font-size: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist:hover {\r\n  background-color: gainsboro;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist.active {\r\n  background-color: #c4c4c4;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left {\r\n  position: relative;\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left span {\r\n  position: absolute;\r\n  width: 15px;\r\n  height: 15px;\r\n  text-align: center;\r\n  background: red;\r\n  color: #ffffff;\r\n  top: 0;\r\n  right: 0;\r\n  border-radius: 50%;\r\n  font-size: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-right {\r\n  position: relative;\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  margin-top: 4px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .name {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  font-size: 14px;\r\n  width: 190px;\r\n  // width: 100px;\r\n  //   overflow: hidden;\r\n  //   white-space: nowrap;\r\n  //   text-overflow: ellipsis;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .time {\r\n  float: right;\r\n  color: #999;\r\n  font-size: 10px;\r\n  vertical-align: top;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .lastmsg {\r\n  position: absolute;\r\n  font-size: 12px;\r\n  width: 130px;\r\n  height: 15px;\r\n  line-height: 15px;\r\n  color: #999;\r\n  bottom: 8px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n.content .msglist .msg-left-box .sessionlist .number-badge {\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: red;\r\n    color: white;\r\n    width: 25px;\r\n    height: 25px;\r\n    border-radius: 50%;\r\n    font-size: 13px;\r\n    position: absolute;\r\n    top: 24px;\r\n    left: 223px;\r\n}\r\n.content .msglist .wrapper {\r\n  padding: 22px 12px 12px 12px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper {\r\n  position: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  height: 26px;\r\n  width: 100%;\r\n  background-color: #e5e3e2;\r\n  border: 1px solid #d9d7d6;\r\n  border-radius: 2px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 6px;\r\n  background-color: #e5e3e2;\r\n  outline: none;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput:focus {\r\n  background-color: #f2efee;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .icon-search {\r\n  display: inline-block;\r\n  width: 24px;\r\n  height: 24px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput-delete {\r\n  display: block;\r\n  position: absolute;\r\n  outline: none;\r\n  top: 0;\r\n  right: 0;\r\n  width: 24px;\r\n  height: 100%;\r\n  background-image: url(\"/img/delete.png\");\r\n  background-size: 26px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .message {\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message .header {\r\n  height: 60px;\r\n  padding: 28px 30px 0;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  border-bottom: 1px solid #e7e7e7;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: justify;\r\n  -ms-flex-pack: justify;\r\n  justify-content: space-between;\r\n}\r\n\r\n.content .chatbox .message .header .friendname {\r\n  font-size: 18px;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message-wrapper {\r\n  height: calc(100% - 240px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box {\r\n  padding: 0 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:last-child {\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time {\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time span {\r\n  display: inline-block;\r\n  padding: 3px 6px;\r\n  border-radius: 3px;\r\n  background-color: #dcdcdc;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img {\r\n  width: 200px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img img {\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-flex {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: start;\r\n  -ms-flex-align: start;\r\n  align-items: flex-start;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1) {\r\n  width: 44px;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  img {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 6px;\r\n  display: block;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  span {\r\n  display: block;\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: left;\r\n  padding-top: 3px;\r\n  width: 50px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding: 0 15px;\r\n  padding-right: 30px;\r\n  text-align: left;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content {\r\n  padding: 10px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  text-align: justify;\r\n  display: inline-block;\r\n  position: relative;\r\n  max-width: 260px;\r\n  min-height: 20px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content::after {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 7px solid transparent;\r\n  left: -14px;\r\n  top: 10px;\r\n  border-right: 7px solid #ffffff;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-img\r\n  img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.content .chatbox .input-box {\r\n  height: 180px;\r\n  background: #fff;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box {\r\n  height: 40px;\r\n  padding: 0 10px;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: start;\r\n  -ms-flex-pack: start;\r\n  justify-content: flex-start;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file {\r\n  position: relative;\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 10px;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 20px;\r\n  opacity: 0;\r\n  z-index: 10;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input[type=\"file\"] {\r\n  color: transparent;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file img {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  -webkit-transform: translate(-50%, -50%);\r\n  transform: translate(-50%, -50%);\r\n  display: block;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.content .chatbox .input-box .input-text {\r\n  width: 100%;\r\n  height: 90px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-text textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  padding: 0 10px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  outline: none;\r\n  font-size: 16px;\r\n  resize: none;\r\n  border: none;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 5px 15px;\r\n  height: 50px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span {\r\n  padding: 5px 15px;\r\n  font-size: 16px;\r\n  background: #f2f2f2;\r\n  cursor: pointer;\r\n  border-radius: 2px;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span:hover {\r\n  background: #129611;\r\n  color: #ffffff;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.oneself .flex-view:nth-child(1) {\r\n  -webkit-box-ordinal-group: 3;\r\n  -ms-flex-order: 2;\r\n  order: 2;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-ordinal-group: 2;\r\n  -ms-flex-order: 1;\r\n  order: 1;\r\n  padding-left: 30px !important;\r\n  padding-right: 15px !important;\r\n  text-align: right !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content {\r\n  position: relative;\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::after {\r\n  border: none !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 8px solid transparent;\r\n  right: -16px;\r\n  top: 12px;\r\n  border-left: 8px solid #b2e281;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .file-box {\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.file-box {\r\n  width: 230px;\r\n  height: 60px;\r\n  -webkit-box-shadow: 0px 0px 5px #ededed;\r\n  box-shadow: 0px 0px 5px #ededed;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding-right: 10px;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-name {\r\n  width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-size {\r\n  font-size: 12px;\r\n  padding-top: 10px;\r\n  color: #999999;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) {\r\n  width: 34px;\r\n  height: 34px;\r\n  text-align: center;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.img-popup {\r\n  position: fixed;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  opacity: 0;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.img-popup .close {\r\n  text-align: center;\r\n}\r\n\r\n.img-popup .close span {\r\n  width: 100px;\r\n  height: 100px;\r\n  font-size: 60px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n\r\n.img-popup .img-div {\r\n  height: 500px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.img-popup .img-div img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.show-popup {\r\n  opacity: 1;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n  pointer-events: all;\r\n}\r\n\r\n.upload-emji {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  img {\r\n    width: 20px;\r\n    height: 20px;\r\n    cursor: pointer;\r\n  }\r\n  .emji-box {\r\n    position: absolute;\r\n    top: -250px;\r\n    width: 440px;\r\n    height: 250px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 0px 10px #ededed;\r\n    border-radius: 12px;\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\";\r\n      border: 7px solid transparent;\r\n      border-top: 7px solid #ffffff;\r\n      bottom: -14px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    .biao-box {\r\n      padding: 10px;\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      .biao-flex {\r\n        text-align: center;\r\n        width: 32px;\r\n        margin: 5px;\r\n        font-size: 20px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n/*# sourceMappingURL=all.css.map */\r\n</style>\r\n"], "mappings": ";AA2VA,IAAAA,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,iBAAA;MACAX,SAAA,EAAAA,SAAA;MACA;MACAY,QAAA;MACAC,UAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,EAAA;MACAC,WAAA;MACAC,IAAA;MACA;MACAC,KAAA;MACAC,EAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAb,QAAA;MACAc,OAAA;MACAC,QAAA;MACAC,SAAA;MACAC,IAAA;MACAC,KAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAAtB,EAAA;MACA,IAAAnB,KAAA;MACA,IAAAmB,EAAA;QACA,KAAAuB,OAAA,CAAAvB,EAAA;MACA;QACA,KAAAR,QAAA;UACA0B,KAAA;UACAM,IAAA;QACA;MACA;IACA;IACAC,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAArC,QAAA,gBAAAkC,GAAA,CAAAxC,IAAA,CAAA4C,GAAA;MACA;QACA,KAAAF,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACAT,QAAAvB,EAAA;MACA,IAAAnB,KAAA;MACA,KAAAoD,UAAA,uBAAAjC,EAAA,EAAAkC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAW,QAAA,GAAA2C,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAY,iBAAA;QACA;UACAZ,KAAA,CAAA+C,QAAA;YACA1B,IAAA;YACAkC,OAAA,EAAAD,IAAA,CAAAH;UACA;QACA;MACA;IACA;IACAK,SAAA;MACAxD,KAAA,CAAAuB,EAAA,IAAAvB,KAAA,CAAAuB,EAAA;MACAvB,KAAA,CAAAyD,WAAA;QAAAtC,EAAA,EAAAnB,KAAA,CAAAmB;MAAA,GAAAkC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAM,MAAA,GAAAgD,IAAA,CAAAjD,IAAA,CAAA2B,KAAA;UACAhC,KAAA,CAAAO,OAAA,GAAA+C,IAAA,CAAAjD,IAAA,CAAAqD,MAAA;UACA1D,KAAA,CAAAQ,UAAA,GAAA8C,IAAA,CAAAjD,IAAA,CAAAsD,SAAA;QAEA;MACA;IACA;IACAC,SAAA;MACA,IAAA5D,KAAA;MACA,KAAA6D,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAN,WAAA,uBAAA9C,QAAA,EAAA0C,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAR,IAAA;cACA9C,KAAA,CAAA+C,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;cACA,KAAAa,OAAA;cACAhE,KAAA,CAAAY,iBAAA;YACA;cACAZ,KAAA,CAAA+C,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAc,YAAA;MACA,IAAAC,GAAA,QAAAjC,IAAA,MAAAC,WAAA;MACAlC,KAAA,CAAAS,KAAA;MACAT,KAAA,CAAAyD,WAAA;QAAAS,GAAA,EAAAA;MAAA,GAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAU,QAAA,GAAA4C,IAAA,CAAAjD,IAAA;QACA;MACA;IACA;IACA8D,WAAAC,SAAA;MAEApE,KAAA,CACAyD,WAAA;QAAAW,SAAA,EAAAA;MAAA,GACAf,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAiC,IAAA,GAAAqB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAgB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAa,QAAA;UACAb,KAAA,CAAAqE,OAAA;QACA;MACA;IACA;IACAC,cAAAC,CAAA;MACA,IAAAtC,IAAA,GAAAjC,KAAA,CAAAsC,WAAA;MACA,IAAAN,KAAA,GAAAhC,KAAA,CAAAuC,YAAA;MACA,IAAAxB,MAAA,GAAAwD,CAAA,CAAAC,MAAA,CAAAC,MAAA;MAEAzE,KAAA,CAAAiC,IAAA,GAAAA,IAAA,CAAAyC,MAAA,CAAArE,IAAA,IAAAA,IAAA,CAAAgC,KAAA,CAAAtB,MAAA,CAAAA,MAAA;MACAf,KAAA,CAAAgC,KAAA,GAAAA,KAAA,CAAA0C,MAAA,CACArE,IAAA,IACA,CAAAU,MAAA,IAAAV,IAAA,CAAAgC,KAAA,CAAAsC,WAAA,GAAAC,QAAA,CAAA7D,MAAA,CAAA4D,WAAA,GACA;IACA;IACAE,OAAA;MACA,IAAA1D,EAAA,QAAAc,IAAA,MAAAC,WAAA;MACA,IAAAkC,SAAA,QAAAnC,IAAA,MAAAC,WAAA;MACAlC,KAAA,CACAyD,WAAA;QAAAtC,EAAA,EAAAA,EAAA;QAAAiD,SAAA,EAAAA;MAAA,GACAf,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAiC,IAAA,MAAAC,WAAA,iBAAAkC,SAAA;UACApE,KAAA,CAAA+C,QAAA,CAAAC,OAAA,CAAAM,IAAA,CAAAH,GAAA;QACA;UACAnD,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACA2B,SAAA;MACA,KAAA1C,MAAA,SAAAA,MAAA;MACA2C,OAAA,CAAAC,GAAA;IACA;IACAC,WAAAC,KAAA;MACA,KAAA7D,IAAA,GAAA6D,KAAA;IACA;IACAC,SAAAlC,GAAA;MACAmC,MAAA,CAAAC,IAAA,CAAApC,GAAA;IACA;IACA;IACAqC,QAAAC,GAAA;MACA,KAAAtE,MAAA,GAAAsE,GAAA;MACA,KAAA9D,WAAA;MACAsD,OAAA,CAAAC,GAAA,eAAAO,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,IAAApE,IAAA,GAAAoE,IAAA,CAAApE,IAAA;MACA0D,OAAA,CAAAC,GAAA,CAAA3D,IAAA;MACA,IACA,CAAAoE,IAAA,CAAApE,IAAA,CAAAqE,KAAA,qBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,sBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,qBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,qBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,qBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,sBACA,CAAAD,IAAA,CAAApE,IAAA,CAAAqE,KAAA,oBACA;QACA,KAAA3C,QAAA;UACA4C,SAAA;UACApC,OAAA;UACAlC,IAAA;QACA;QACA;MACA;IACA;IACAuE,cAAA/C,GAAA;MACA,IAAA7C,KAAA;MACA+E,OAAA,CAAAC,GAAA,CAAAnC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA9C,KAAA,CAAA6F,OAAA,CAAAhD,GAAA,CAAAxC,IAAA,CAAA4C,GAAA;MACA;QACAjD,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACA2C,eAAAjD,GAAA,EAAAkD,IAAA;MACA,IAAAlD,GAAA,CAAAC,IAAA;QACA9C,KAAA,CAAAgG,QAAA,CAAAnD,GAAA,CAAAxC,IAAA,CAAA4C,GAAA,EAAA8C,IAAA;MACA;QACA,KAAAhD,QAAA;UACA4C,SAAA;UACApC,OAAA;UACAlC,IAAA;QACA;MACA;IACA;IACA4E,WAAAC,KAAA;MACA,KAAArF,QAAA,GAAAqF,KAAA;MACA,KAAAhE,WAAA;MACAlC,KAAA,CAAAuB,EAAA;MACAvB,KAAA,CAAAqE,OAAA;MACA;MACA;MACA;MACA;IACA;IACA8B,UAAAD,KAAA;MACA,KAAArF,QAAA;MACA,KAAAqB,WAAA,GAAAgE,KAAA;MACAlG,KAAA,CAAAiC,IAAA,CAAAiE,KAAA,EAAAE,KAAA;MACApG,KAAA,CAAAuB,EAAA;MACAvB,KAAA,CAAAqE,OAAA;MACA;MACA;MACA;MACA;IACA;IACAgC,SAAAC,IAAA;MACA,KAAA5E,WAAA,IAAA4E,IAAA;IACA;IACAC,OAAAhC,CAAA;MACA,SAAAxD,MAAA,OAAAK,WAAA,aACA,KAAAA,WAAA;IACA;IACAoF,IAAA;MACA,KAAAzF,MAAA;MACA,KAAAK,WAAA;IACA;IACAqF,aAAAlC,CAAA;MACA,SAAAV,KAAA,CAAA/B,IAAA,CAAA4E,SAAA;QACA3B,OAAA,CAAAC,GAAA;MACA;IACA;IACA;IACA2B,KAAA;MACA3G,KAAA,CAAA4G,WAAA,CAAA5G,KAAA,CAAA0B,WAAA;MACA1B,KAAA,CAAA0B,WAAA;MACA;MACA;AACA;AACA;IACA;IACA2C,QAAA;MACA,IAAArE,KAAA,CAAAa,QAAA;QACA,IAAAM,EAAA,GAAAnB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAM,EAAA;QACAnB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAwB,KAAA;QAEArC,KAAA,CAAAyD,WAAA;UAAAS,GAAA,EAAA/C;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAjD,IAAA,CAAAwG,MAAA;cACA7G,KAAA,CAAA8B,IAAA,GAAAwB,IAAA,CAAAjD,IAAA;cAEAL,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,GAAA1G,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA;YACA;UACA;UACA9G,KAAA,CAAA+G,OAAA;QACA;MACA;QACA,IAAA5F,EAAA,GAAAnB,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAf,EAAA;QACA,IAAAiF,KAAA,GACApG,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAgC,GAAA,CAAA2C,MAAA,OACA7G,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8E,QAAA,CAAAH,MAAA,OACA7G,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA+E,WAAA,CAAAJ,MAAA;QACA7G,KAAA,CAAAmB,EAAA,GAAAA,EAAA;QACA4D,OAAA,CAAAC,GAAA,CAAAhF,KAAA,CAAAmB,EAAA;QAEAnB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAG,KAAA,SAAA+D,KAAA;QACApG,KAAA,CAAAyD,WAAA;UAAAyD,MAAA,EAAA/F;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAjD,IAAA,CAAAwG,MAAA;cACA7G,KAAA,CAAA8B,IAAA,GAAAwB,IAAA,CAAAjD,IAAA;cACAL,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,GAAA1G,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA;YACA;cACA9G,KAAA,CAAA8B,IAAA;YACA;YAEAqF,UAAA,CACA,WAAAtD,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,QAAA7C,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,CACA;UACA;UACA9G,KAAA,CAAA+G,OAAA;QACA;MACA;IACA;IACAK,YAAA;MACA,IAAApH,KAAA,CAAAa,QAAA;QACA,IAAAqD,GAAA,GAAAlE,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAM,EAAA;QACAnB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAwB,KAAA;QAEA,IAAAlB,EAAA;QACA,IAAAnB,KAAA,CAAA8B,IAAA,CAAA+E,MAAA;UACA1F,EAAA,GAAAnB,KAAA,CAAA8B,IAAA,CAAA9B,KAAA,CAAA8B,IAAA,CAAA+E,MAAA,MAAA1F,EAAA;UACAnB,KAAA,CACAyD,WAAA;YAAAS,GAAA,EAAAA,GAAA;YAAA/C,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAtD,KAAA,CAAAqH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA,IAAAQ,IAAA,CAAAjD,IAAA,CAAAwG,MAAA;gBACA7G,KAAA,CAAA8B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAjD,IAAA;gBACA;;gBAEA8G,UAAA,CACA,MACA,KAAAtD,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,GACA,KAAA7C,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;cACA;YACA;YACA9G,KAAA,CAAA+G,OAAA;UACA;QACA;MACA;QACA,IAAAG,MAAA,GAAAlH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAf,EAAA;QACA,IAAAiF,KAAA,GACApG,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8E,QAAA,CAAAH,MAAA,OACA7G,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA+E,WAAA,CAAAJ,MAAA,OACA;QAEA7G,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAG,KAAA,SAAA+D,KAAA;QACA,IAAAjF,EAAA;QACA,IAAAnB,KAAA,CAAA8B,IAAA,CAAA+E,MAAA;UACA1F,EAAA,GAAAnB,KAAA,CAAA8B,IAAA,CAAA9B,KAAA,CAAA8B,IAAA,CAAA+E,MAAA,MAAA1F,EAAA;UACAnB,KAAA,CACAyD,WAAA;YAAAyD,MAAA,EAAAA,MAAA;YAAA/F,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAtD,KAAA,CAAAqH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA9C,KAAA,CAAA8B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAjD,IAAA;cAEA8G,UAAA,CACA,MACAnH,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,GACA1G,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;YACA;YACA9G,KAAA,CAAA+G,OAAA;UACA;QACA;UACA5F,EAAA;UACAnB,KAAA,CACAyD,WAAA;YAAAyD,MAAA,EAAAA,MAAA;YAAA/F,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAtD,KAAA,CAAAqH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA9C,KAAA,CAAA8B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAjD,IAAA;cAEA8G,UAAA,CACA,MACAnH,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAA4E,SAAA,GACA1G,KAAA,CAAA6D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;YACA;YACA9G,KAAA,CAAA+G,OAAA;UACA;QACA;MACA;IACA;IACAH,YAAAW,OAAA;MACA,IAAAvH,KAAA,CAAAa,QAAA;QACA,IAAAM,EAAA,GAAAnB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAM,EAAA;QACA,IAAAqG,SAAA;QACAxH,KAAA,CACAyD,WAAA;UACAS,GAAA,EAAA/C,EAAA;UACAsG,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAnE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAe,GAAA,GAAAlE,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAgC,GAAA;QACA,IAAAgD,MAAA,GAAAlH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAf,EAAA;QACAnB,KAAA,CACAyD,WAAA;UACA;UACAgE,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA7D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA6C,SAAAuB,OAAA,EAAAG,KAAA;MACA,IAAA1H,KAAA,CAAAa,QAAA;QACA;QACA,IAAA2G,SAAA;QACAxH,KAAA,CACAyD,WAAA;UACA;UACAgE,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA,SAAA;UACAE,KAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA;QACA,IAAA+D,MAAA,GAAAlH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAf,EAAA;QACAnB,KAAA,CACAyD,WAAA;UACA;UACAgE,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA,MAAA;UACAQ,KAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA0C,QAAA0B,OAAA;MACA,IAAAvH,KAAA,CAAAa,QAAA;QACA,IAAAM,EAAA,GAAAnB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAa,QAAA,EAAAM,EAAA;QACA,IAAAqG,SAAA;QACAxH,KAAA,CACAyD,WAAA;UACAS,GAAA,EAAA/C,EAAA;UACAsG,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAnE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAe,GAAA,GAAAlE,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAgC,GAAA;QACA,IAAAgD,MAAA,GAAAlH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAf,EAAA;QACAnB,KAAA,CACAyD,WAAA;UACAS,GAAA,EAAAA,GAAA;UACAuD,SAAA;UAAA;UACApG,IAAA;UAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA7D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA9C,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACAwE,YAAA;MACA3H,KAAA,CAAAyD,WAAA,sBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAgC,KAAA,GAAAsB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAuC,YAAA,GAAAe,IAAA,CAAAjD,IAAA;QACA;MACA;IACA;IACAuH,OAAA;MACA5H,KAAA,CAAAyD,WAAA,iBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAiC,IAAA,GAAAqB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAgB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAa,QAAA;UAEAsG,UAAA;YACAnH,KAAA,CAAAqE,OAAA;UACA;QACA;MACA;IACA;IACAgD,QAAA;MACArH,KAAA,CAAAyD,WAAA,iBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAiC,IAAA,GAAAqB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAgB,IAAA,CAAAjD,IAAA;UACAL,KAAA,CAAAa,QAAA;QACA;MACA;IACA;IACAgH,YAAA;MACA,IAAA7H,KAAA;MAEA8H,QAAA,CAAAC,SAAA,GAAAxD,CAAA;QACA,IAAAyD,IAAA,GAAA5C,MAAA,CAAA6C,KAAA,CAAAC,OAAA;;QAEA;QACA,IAAAF,IAAA;UACAhI,KAAA,CAAA2G,IAAA;QACA;MACA;IACA;IACAwB,SAAA1C,IAAA,EAAA2C,QAAA;MACA,IAAApI,KAAA;MACAA,KAAA,CAAAoD,UAAA,gCAAAqC,IAAA,EAAApC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA9C,KAAA,CAAAW,QAAA,CAAAyH,QAAA;UAEApI,KAAA,CAAA+C,QAAA,CAAAC,OAAA;QACA;UACAhD,KAAA,CAAA+C,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;EACA;EACAkF,cAAA;IACAtD,OAAA,CAAAC,GAAA;IACAsD,aAAA,MAAAvG,KAAA;EACA;EACAwG,QAAA;IACAvI,KAAA;IACAA,KAAA,CAAA4H,MAAA;IACA5H,KAAA,CAAA2H,WAAA;IACA3H,KAAA,CAAAkB,MAAA,GAAAkE,MAAA,CAAAoD,cAAA,CAAAC,OAAA;IACAzI,KAAA,CAAA+B,KAAA,GAAA2G,WAAA;MACA1I,KAAA,CAAAoH,WAAA;IACA;IACApH,KAAA,CAAA6H,WAAA;;IAEA;EACA;AACA", "ignoreList": []}]}