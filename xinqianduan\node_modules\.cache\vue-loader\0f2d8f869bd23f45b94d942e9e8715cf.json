{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue", "mtime": 1748604247125}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SystemMonitor.vue"], "names": [], "mappings": ";AAkHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SystemMonitor.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div class=\"system-monitor\">\r\n    <el-card shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">系统状态监控</span>\r\n        <el-button type=\"text\" @click=\"refreshData\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新\r\n        </el-button>\r\n      </div>\r\n      \r\n      <div class=\"monitor-content\">\r\n        <!-- 系统状态指示器 -->\r\n        <div class=\"status-indicators\">\r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon online\">\r\n              <i class=\"el-icon-success\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">系统状态</div>\r\n              <div class=\"status-value\">正常运行</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">运行时间</div>\r\n              <div class=\"status-value\">{{ systemUptime }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">在线用户</div>\r\n              <div class=\"status-value\">{{ onlineUsers }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性能指标 -->\r\n        <div class=\"performance-metrics\">\r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">CPU使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.cpu }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.cpu\" \r\n              :color=\"getProgressColor(systemMetrics.cpu)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">内存使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.memory }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.memory\" \r\n              :color=\"getProgressColor(systemMetrics.memory)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">磁盘使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.disk }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.disk\" \r\n              :color=\"getProgressColor(systemMetrics.disk)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务状态 -->\r\n        <div class=\"service-status\">\r\n          <div class=\"service-title\">服务状态</div>\r\n          <div class=\"service-list\">\r\n            <div \r\n              v-for=\"service in services\" \r\n              :key=\"service.name\"\r\n              class=\"service-item\"\r\n            >\r\n              <div class=\"service-info\">\r\n                <span class=\"service-name\">{{ service.name }}</span>\r\n                <span \r\n                  class=\"service-status-badge\" \r\n                  :class=\"service.status\"\r\n                >\r\n                  {{ service.status === 'online' ? '正常' : '异常' }}\r\n                </span>\r\n              </div>\r\n              <div class=\"service-details\">\r\n                <span class=\"service-version\">{{ service.version }}</span>\r\n                <span class=\"service-uptime\">运行 {{ service.uptime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SystemMonitor',\r\n  data() {\r\n    return {\r\n      systemUptime: '7天 12小时 35分钟',\r\n      onlineUsers: 23,\r\n      systemMetrics: {\r\n        cpu: 45,\r\n        memory: 62,\r\n        disk: 38\r\n      },\r\n      services: [\r\n        {\r\n          name: 'Web服务器',\r\n          status: 'online',\r\n          version: 'v2.1.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '数据库',\r\n          status: 'online',\r\n          version: 'MySQL 8.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '文件服务',\r\n          status: 'online',\r\n          version: 'v1.5.2',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '邮件服务',\r\n          status: 'online',\r\n          version: 'v3.2.1',\r\n          uptime: '6天'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.startMonitoring()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.monitorTimer) {\r\n      clearInterval(this.monitorTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    startMonitoring() {\r\n      // 模拟实时数据更新\r\n      this.monitorTimer = setInterval(() => {\r\n        this.updateMetrics()\r\n      }, 5000)\r\n    },\r\n    \r\n    updateMetrics() {\r\n      // 模拟性能指标变化\r\n      this.systemMetrics.cpu = Math.floor(Math.random() * 30) + 30\r\n      this.systemMetrics.memory = Math.floor(Math.random() * 20) + 50\r\n      this.systemMetrics.disk = Math.floor(Math.random() * 15) + 30\r\n      \r\n      // 模拟在线用户数变化\r\n      this.onlineUsers = Math.floor(Math.random() * 10) + 20\r\n    },\r\n    \r\n    refreshData() {\r\n      this.updateMetrics()\r\n      this.$message.success('数据已刷新')\r\n    },\r\n    \r\n    getProgressColor(percentage) {\r\n      if (percentage < 50) {\r\n        return '#67C23A'\r\n      } else if (percentage < 80) {\r\n        return '#E6A23C'\r\n      } else {\r\n        return '#F56C6C'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.system-monitor {\r\n  height: 100%;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.monitor-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicators {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n.status-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #f0f0f0;\r\n  color: #666;\r\n  font-size: 18px;\r\n}\r\n\r\n.status-icon.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-info {\r\n  flex: 1;\r\n}\r\n\r\n.status-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 性能指标 */\r\n.performance-metrics {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.metric-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.metric-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 服务状态 */\r\n.service-status {\r\n  border-top: 1px solid #f0f0f0;\r\n  padding-top: 20px;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.service-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.service-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.service-status-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.service-status-badge.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.service-status-badge.offline {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.service-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.service-version,\r\n.service-uptime {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .status-indicators {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .status-item {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-details {\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style> "]}]}