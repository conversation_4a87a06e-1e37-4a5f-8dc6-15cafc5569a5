<?php

namespace app\admin\controller;

use models\Debts;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\Exception;
use think\exception\PDOException;
use think\Request;
use untils\JsonService;
use models\{Users, Yuangongs};

class User extends Base
{
    protected $model;

    public function __construct(Users $model)
    {
        parent::__construct();
        $this->model = $model;

    }

    public function index(Request $request, $page = 1, $size = 20)
    {
        $where = [];
        $userid = $this->userid;//登录者id
        $search = $request->post();
        if (!empty($search['keyword'])) {
            $where[] = ['phone|company|linkman', "like", "%" . $search['keyword'] . "%"];
        }
        $order = ['id' => 'desc'];
        if (!empty($search['prop']) && !empty($search['order'])) {
            $order = [$search['prop'] => $search['order'] == 'descending' ? 'desc' : 'asc'];
        }
        $res = $this->model
            ::withAttr('end_time', function ($v, $d) {
                if (!empty($d['year'])) return date('Y-m-d', $d['start_time'] * 1 + $d['year'] * 1 * 365 * 60 * 60 * 24);

            })->withAttr('yuangong_id', function ($v, $d) {
                if (empty($v)) return '小程序';
                else return Yuangongs::where('id', $v)->value('title');
            })->withAttr('tiaojie_name', function ($v, $d) {
                if (empty($d['tiaojie_id'])) return '';
                else return Yuangongs::where('id', $d['tiaojie_id'])->value('title');
            })->withAttr('fawu_name', function ($v, $d) {
                if (empty($d['fawu_id'])) return '';
                else return Yuangongs::where('id', $d['fawu_id'])->value('title');
            })->withAttr('lian_name', function ($v, $d) {
                if (empty($d['lian_id'])) return '';
                else return Yuangongs::where('id', $d['lian_id'])->value('title');
            })->withAttr('ywy_name', function ($v, $d) {
                if (empty($d['ywy_id'])) return '';
                else return Yuangongs::where('id', $d['ywy_id'])->value('title');
            })->withAttr('ls_name', function ($v, $d) {
                if (empty($d['ls_id'])) return '';
                else return Yuangongs::where('id', $d['ls_id'])->value('title');
            })->withAttr('htsczy_name', function ($v, $d) {
                if (empty($d['htsczy_id'])) return '';
                else return Yuangongs::where('id', $d['htsczy_id'])->value('title');
            })
            ->where($where)
            ->where("{$userid}=1 or tiaojie_id={$userid} or fawu_id={$userid} or lian_id={$userid} or ywy_id={$userid} or ls_id={$userid} or htsczy_id={$userid} or (tiaojie_id=0 and fawu_id=0 and lian_id=0 and htsczy_id=0 and ls_id=0 and ywy_id=0)")
            ->order($order)
            ->limit($size)
            ->page($page)
            ->append(['tiaojie_name','fawu_name','lian_name','ywy_name','ls_name','htsczy_name'])
            ->select();
        $count = $this->model->where($where)->count();
        if (empty($res)) return JsonService::fail('失败');
        else return JsonService::successful($this->userid == 1 ? '超级管理员' : '成功', $res, $count);
    }

    public function index1(Request $request, $page = 1, $size = 20)
    {
        $where = [];
        $search = $request->post();
        if (!empty($search['keyword'])) {
            $where[] = ['phone|company|linkman', "like", "%" . $search['keyword'] . "%"];
        }
        $res = $this->model::withAttr('end_time', function ($v, $d) {
            if (!empty($d['year'])) return date('Y-m-d', $d['start_time'] * 1 + $d['year'] * 1 * 365 * 60 * 60 * 24);

        })->withAttr('yuangong_id', function ($v, $d) {
            if (empty($v)) return '小程序';
            else return Yuangongs::where('id', $v)->value('title');
        })
            ->where($where)
            ->where(['yuangong_id' => $this->userid])
            ->order(['id' => 'desc'])
            ->limit($size)
            ->page($page)
            ->select();
        $count = $this->model
            ->where(['yuangong_id' => $this->userid])
            ->where($where)->count();
        if (empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功', $res, $count);
    }

    public function save(Request $request)
    {
        if (!$request->isPost()) return JsonService::fail('非法请求2');
        $form = $request->post();
        if (empty($form)) return JsonService::fail('未接收到参数');

        $form['start_time'] = strtotime($form['start_time']) * 1;
        if (!empty($form['year'])) {
            if (empty($form['start_time'])) return JsonService::fail('请选择开始时间');
            $form['end_time'] = $form['start_time'] + 365 * 24 * 60 * 60 * $form['year'] * 1;

            $form['vip_id'] = 1;
        }

        if (!empty($form['password'])) {
            $validate = new \app\index\validate\User;
            if (!$validate->check(['password' => $form['password']])) return JsonService::fail($validate->getError());
        } else {
            $form['password'] = $form['phone'];
        }


        unset($form['yuangong_id']);
        $res = $this->model->saveData($form);
        $errorMsg = $this->model::getErrorInfo();
        if (!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id = 0)
    {
        if (empty($id)) return JsonService::fail('未接收到参数');
        $res = $this->model::withAttr('start_time', function ($v, $d) {
            if (!empty($v)) return date('Y-m-d', $v);
            else return date('Y-m-d', time());
        })->withAttr('yuangong_id', function ($v, $d) {
            if (empty($v)) return '小程序';
            else return Yuangongs::where('id', $v)->value('title');
        })->withAttr('tiaojie_name', function ($v, $d) {
            if (empty($d['tiaojie_id'])) return '';
            else return Yuangongs::where('id', $d['tiaojie_id'])->value('title');
        })->withAttr('fawu_name', function ($v, $d) {
            if (empty($d['fawu_id'])) return '';
            else return Yuangongs::where('id', $d['fawu_id'])->value('title');
        })->withAttr('lian_name', function ($v, $d) {
            if (empty($d['lian_id'])) return '';
            else return Yuangongs::where('id', $d['lian_id'])->value('title');
        })->withAttr('ls_name', function ($v, $d) {
            if (empty($d['ls_id'])) return '';
            else return Yuangongs::where('id', $d['ls_id'])->value('title');
        })->withAttr('ywy_name', function ($v, $d) {
            if (empty($d['ywy_id'])) return '';
            else return Yuangongs::where('id', $d['ywy_id'])->value('title');
        })->withAttr('htsczy_name', function ($v, $d) {
            if (empty($d['htsczy_id'])) return '';
            else return Yuangongs::where('id', $d['htsczy_id'])->value('title');
        })
            ->append(['tiaojie_name','fawu_name','lian_name','ywy_name','ls_name','htsczy_name'])
            ->field('*')
            ->find($id);
        $res->hidden(['create_time', 'update_time']);
        if(!empty($id)){
            $where=[];
            $where[]=['uid','=',$id];
            $debts = Debts::where($where)->withAttr('status',function($v,$d){
                switch ($v) {
                    case 1:
                        return '待处理';
                        break;
                    case 2:
                        return '调节中';
                        break;
                    case 3:
                        return '诉讼中';
                        break;
                    case 4:
                        return '已结案';
                        break;
                }
            })
                ->field('name,tel,money,status')
                ->order(['ctime'=>'desc'])
                ->select();
            $res['debts'] = $debts;
        }
        if (empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功', $res);
    }

    public function delete($id = 0)
    {
        if ($this->userid != 1) {
            return JsonService::fail('只有超级管理员才可以删除');
        }
        if (empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->delData(['id' => $id]);
        if (empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }

    public function getList()
    {
        $res = $this->model->select()->toArray();
        if (empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok', $res);
    }

    public function export(Request $request)
    {
        $where = [];
        $search = $request->get();
        if (!empty($search['keyword'])) {
            $where[] = ['phone|company|linkman', "like", "%" . $search['keyword'] . "%"];
        }
        $res = $this->model::withAttr('end_time', function ($v, $d) {
            if (!empty($d['year'])) return date('Y-m-d', $d['start_time'] * 1 + $d['year'] * 1 * 365 * 60 * 60 * 24);

        })->withAttr('yuangong_id', function ($v, $d) {
            if (empty($v)) return '小程序';
            else return Yuangongs::where('id', $v)->value('title');
        })
            ->where($where)
            ->where(['yuangong_id' => $this->userid])
            ->order(['id' => 'desc'])
            ->select();

        $headerData = [
            '注册手机号码' => 'phone',
            '公司名称' => 'company',
            '联系人' => 'linkman',
            '联系号码' => 'linkphone',
            '用户来源' => 'yuangong_id',
            '到期时间' => 'end_time',
            '录入时间' => 'create_time'
        ];
        exportData($res, $headerData, '用户列表');
    }

    public function export2(Request $request)
    {
        $where = [];
        $search = $request->post();
        if (!empty($search['keyword'])) {
            $where[] = ['phone|company|linkman', "like", "%" . $search['keyword'] . "%"];
        }
        $order = ['id' => 'desc'];
        if (!empty($search['prop']) && !empty($search['order'])) {
            $order = [$search['prop'] => $search['order'] == 'descending' ? 'desc' : 'asc'];
        }
        $res = $this->model::withAttr('end_time', function ($v, $d) {
            if (!empty($d['year'])) return date('Y-m-d', $d['start_time'] * 1 + $d['year'] * 1 * 365 * 60 * 60 * 24);

        })->withAttr('yuangong_id', function ($v, $d) {
            if (empty($v)) return '小程序';
            else return Yuangongs::where('id', $v)->value('title');
        })
            ->where($where)
            ->order($order)
            ->select();

        $headerData = [
            '注册手机号码' => 'phone',
            '公司名称' => 'company',
            '联系人' => 'linkman',
            '联系号码' => 'linkphone',
            '用户来源' => 'yuangong_id',
            '到期时间' => 'end_time',
            '录入时间' => 'create_time'
        ];
        exportData($res, $headerData, '用户列表');
    }

    /**
     * 导入
     */
    public function import(Request $request)
    {
        $file = $request->file('file');
        if (!$file) {
            return JsonService::fail('没有数据!');
        }

        $filePath = ROOT_PATH . DS . 'public' . DS . 'uploads';
        $file = $file->move($filePath);
        //实例化reader
        $ext = $file->getExtension();
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            return JsonService::fail('未知的数据格式!');
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }

        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath . DS . $file->getSaveName())) {
                return JsonService::fail('未知的数据格式!');
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }

            //获取手机号列表
            $mobileArr = [];
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string)$currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : $val;
                }

                if (empty($values[0])) {
                    return JsonService::fail('手机号不能为空!');
                }

                if (in_array($values[0], $mobileArr)) {
                    return JsonService::fail($values[0] . '手机号重复!');
                }
                $mobileArr[] = $values[0];
            }
            $exist = $this->model->whereIn('phone', $mobileArr)->value('phone');
            if ($exist) return JsonService::fail($exist . '手机号已存在!');

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string)$currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : trim($val);
                }

                $password = empty($values[1]) ? $values[0] : $values[1];
                $insertData = [
                    'create_time' => time(),
                    'update_time' => time(),
                    'nickname' => $values[2],
                    'phone' => $values[0],
                    'company' => $values[3],
                    'linkman' => $values[4],
                    'linkphone' => $values[5],
                    'yuangong_id' => $this->userid,
                    'password' => $password
                ];
                $insert[] = $insertData;
            }
        } catch (Exception $exception) {
            @unlink($filePath . DS . $file->getSaveName());
            return JsonService::fail($exception->getMessage());
        }
        @unlink($filePath . DS . $file->getSaveName());
        if (!$insert) {
            return JsonService::fail('没有数据需要导入');
        }
        try {
            $this->model->saveAll($insert);
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            return JsonService::fail($msg);
        } catch (Exception $e) {
            return JsonService::fail($e->getMessage());
        }
        return JsonService::successful('导入成功');
    }

}
