{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue", "mtime": 1748454232524}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lawyer.vue"], "names": [], "mappings": ";AA2pBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "lawyer.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"lawyer-letter-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            发律师函管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和处理律师函制作工单</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">关键词搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入工单号/标题/用户手机号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">处理状态</label>\r\n              <el-select\r\n                v-model=\"search.is_deal\"\r\n                placeholder=\"请选择处理状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in options1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.title\"\r\n                  :value=\"item.id\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <h3>\r\n              <i class=\"el-icon-tickets\"></i>\r\n              律师函工单列表\r\n            </h3>\r\n            <span class=\"table-count\">共 {{ total }} 条记录</span>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"lawyer-table\"\r\n          stripe\r\n          border\r\n          empty-text=\"暂无律师函工单数据\"\r\n        >\r\n          <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"order-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"order-text\">{{ scope.row.order_sn }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"type\" label=\"工单类型\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ scope.row.type || '律师函' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <span class=\"title-text\" :title=\"scope.row.title\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"desc-cell\">\r\n                <span class=\"desc-text\" :title=\"scope.row.desc\">\r\n                  {{ scope.row.desc ? (scope.row.desc.length > 50 ? scope.row.desc.substring(0, 50) + '...' : scope.row.desc) : '-' }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getStatusType(scope.row.is_deal)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getStatusText(scope.row.is_deal) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"uid\" label=\"用户手机\" width=\"130\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"phone-cell\">\r\n                <i class=\"el-icon-phone\"></i>\r\n                <span>{{ scope.row.uid || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"dt_name\" label=\"债务人\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"debtor-cell clickable\" @click=\"showDebtorDetail(scope.row)\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"debtor-name\">{{ scope.row.dt_name || '-' }}</span>\r\n                <i class=\"el-icon-arrow-right arrow-icon\"></i>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-check\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                  :disabled=\"scope.row.is_deal === 2\"\r\n                >\r\n                  {{ scope.row.is_deal === 2 ? '已完成' : '完成制作' }}\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-close\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  取消\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <!-- 律师函处理对话框 -->\r\n    <el-dialog\r\n      title=\"律师函制作处理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"process-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <div class=\"dialog-content\">\r\n        <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-position=\"top\">\r\n          <!-- 工单基本信息 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-info\"></i>\r\n              工单基本信息\r\n            </h4>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单类型\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.type_title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-folder\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单标题\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"工单描述\">\r\n              <el-input\r\n                v-model=\"ruleForm.desc\"\r\n                readonly\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                class=\"readonly-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 处理状态设置 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              处理状态设置\r\n            </h4>\r\n            <el-form-item label=\"制作状态\">\r\n              <el-radio-group v-model=\"ruleForm.is_deal\" class=\"status-radio-group\">\r\n                <el-radio :label=\"1\" class=\"status-radio\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  处理中\r\n                </el-radio>\r\n                <el-radio :label=\"2\" class=\"status-radio\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  已完成\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- AI生成律师函 -->\r\n          <div class=\"form-section ai-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-cpu\"></i>\r\n              AI智能生成\r\n            </h4>\r\n            <div class=\"ai-generation-content\">\r\n              <div class=\"ai-description\">\r\n                <p>\r\n                  <i class=\"el-icon-info\"></i>\r\n                  AI将根据工单信息、债务人详情和合同模板自动生成律师函内容\r\n                </p>\r\n              </div>\r\n              <div class=\"ai-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-cpu\"\r\n                  @click=\"generateLawyerLetter\"\r\n                  :loading=\"aiGenerating\"\r\n                  class=\"ai-generate-btn\"\r\n                >\r\n                  {{ aiGenerating ? 'AI生成中...' : 'AI生成律师函' }}\r\n                </el-button>\r\n              </div>\r\n              <!-- AI生成结果展示 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-result\">\r\n                <h5 class=\"result-title\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  AI生成结果\r\n                </h5>\r\n                <div class=\"generated-content\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"8\"\r\n                    placeholder=\"AI生成的律师函内容将显示在这里...\"\r\n                    class=\"ai-content-textarea\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"ai-result-actions\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-check\"\r\n                    @click=\"useAiContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    使用此内容\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"regenerateContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    重新生成\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 完成处理区域 -->\r\n          <div v-if=\"ruleForm.is_deal == 2\" class=\"form-section completion-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-upload\"></i>\r\n              完成处理\r\n            </h4>\r\n\r\n            <!-- 处理方式选择 -->\r\n            <el-form-item label=\"处理方式\" class=\"process-method-item\">\r\n              <el-radio-group v-model=\"processMethod\" @change=\"onProcessMethodChange\" class=\"method-radio-group\">\r\n                <el-radio label=\"ai\" class=\"method-radio\">\r\n                  <i class=\"el-icon-cpu\"></i>\r\n                  AI智能生成\r\n                </el-radio>\r\n                <el-radio label=\"upload\" class=\"method-radio\">\r\n                  <i class=\"el-icon-upload\"></i>\r\n                  手动上传文件\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <!-- AI生成方式 -->\r\n            <div v-if=\"processMethod === 'ai'\" class=\"ai-process-section\">\r\n              <div class=\"ai-process-info\">\r\n                <el-alert\r\n                  title=\"AI生成模式\"\r\n                  description=\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\"\r\n                  type=\"info\"\r\n                  :closable=\"false\"\r\n                  show-icon\r\n                  class=\"ai-mode-alert\"\r\n                ></el-alert>\r\n              </div>\r\n\r\n              <!-- AI生成的内容预览 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-content-preview\">\r\n                <el-form-item label=\"生成内容预览\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"6\"\r\n                    placeholder=\"AI生成的律师函内容...\"\r\n                    class=\"ai-preview-textarea\"\r\n                    readonly\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <el-form-item label=\"文件生成\">\r\n                <div class=\"ai-file-generation\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"generateAiFile\"\r\n                    :loading=\"fileGenerating\"\r\n                    class=\"generate-file-btn\"\r\n                  >\r\n                    {{ fileGenerating ? '生成文件中...' : '生成律师函文件' }}\r\n                  </el-button>\r\n                  <div v-if=\"aiGeneratedFile\" class=\"generated-file-info\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <span>{{ aiGeneratedFile.name }}</span>\r\n                    <el-tag type=\"success\" size=\"mini\">已生成</el-tag>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <!-- 手动上传方式 -->\r\n            <div v-if=\"processMethod === 'upload'\" class=\"upload-process-section\">\r\n              <el-form-item\r\n                label=\"律师函文件\"\r\n                prop=\"file_path\"\r\n                class=\"file-upload-item\"\r\n              >\r\n                <div class=\"upload-area\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.file_path\"\r\n                    placeholder=\"请上传律师函文件\"\r\n                    readonly\r\n                    class=\"file-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                  <div class=\"upload-buttons\">\r\n                    <el-button @click=\"changeFile('file_path')\" type=\"primary\" icon=\"el-icon-upload\">\r\n                      <el-upload\r\n                        action=\"/admin/Upload/uploadFile\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleSuccess\"\r\n                        style=\"display: inline-block;\"\r\n                      >\r\n                        上传文件\r\n                      </el-upload>\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"ruleForm.file_path\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n                    >\r\n                      删除文件\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <el-form-item label=\"处理说明\">\r\n              <el-input\r\n                v-model=\"ruleForm.content\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入处理说明或备注信息...\"\r\n                class=\"content-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\" icon=\"el-icon-close\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" icon=\"el-icon-check\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 债务人详情右侧滑出面板 -->\r\n    <div class=\"debtor-detail-panel\" :class=\"{ 'panel-open': showDebtorPanel }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDebtorPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"header-info\">\r\n            <h3 class=\"panel-title\">\r\n              <i class=\"el-icon-user\"></i>\r\n              债务人详情\r\n            </h3>\r\n            <p class=\"panel-subtitle\">{{ currentDebtor.dt_name }}</p>\r\n          </div>\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"closeDebtorPanel\"\r\n            class=\"close-btn\"\r\n          ></el-button>\r\n        </div>\r\n\r\n        <!-- 左侧菜单 -->\r\n        <div class=\"panel-body\">\r\n          <div class=\"sidebar-menu\">\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'basic' }\"\r\n                 @click=\"activeTab = 'basic'\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n              <span>基本信息</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'user' }\"\r\n                 @click=\"activeTab = 'user'\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>关联用户</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'files' }\"\r\n                 @click=\"activeTab = 'files'\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              <span>相关文件</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'history' }\"\r\n                 @click=\"activeTab = 'history'\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>历史记录</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"content-area\">\r\n            <!-- 基本信息 -->\r\n            <div v-if=\"activeTab === 'basic'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-info\"></i>\r\n                  债务人基本信息\r\n                </h4>\r\n                <div class=\"info-grid\">\r\n                  <div class=\"info-item\">\r\n                    <label>姓名：</label>\r\n                    <span>{{ currentDebtor.dt_name }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentDebtor.id_card || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>联系电话：</label>\r\n                    <span>{{ currentDebtor.phone || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>地址：</label>\r\n                    <span>{{ currentDebtor.address || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务金额：</label>\r\n                    <span class=\"debt-amount\">¥{{ currentDebtor.debt_amount || '0.00' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务类型：</label>\r\n                    <span>{{ currentDebtor.debt_type || '未分类' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 关联用户信息 -->\r\n            <div v-if=\"activeTab === 'user'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-user\"></i>\r\n                  关联用户信息\r\n                </h4>\r\n                <div class=\"user-card\">\r\n                  <div class=\"user-avatar\">\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </div>\r\n                  <div class=\"user-info\">\r\n                    <h5>{{ currentDebtor.user_name }}</h5>\r\n                    <p>手机号：{{ currentDebtor.user_phone }}</p>\r\n                    <p>注册时间：{{ currentDebtor.user_register_time }}</p>\r\n                    <p>用户状态：\r\n                      <el-tag :type=\"currentDebtor.user_status === 'active' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ currentDebtor.user_status === 'active' ? '正常' : '异常' }}\r\n                      </el-tag>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 相关文件 -->\r\n            <div v-if=\"activeTab === 'files'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-folder\"></i>\r\n                  相关文件\r\n                </h4>\r\n                <div class=\"file-list\">\r\n                  <div v-for=\"file in currentDebtor.files\" :key=\"file.id\" class=\"file-item\">\r\n                    <div class=\"file-icon\">\r\n                      <i :class=\"getFileIcon(file.type)\"></i>\r\n                    </div>\r\n                    <div class=\"file-info\">\r\n                      <h6>{{ file.name }}</h6>\r\n                      <p>{{ file.upload_time }}</p>\r\n                      <p class=\"file-size\">{{ file.size }}</p>\r\n                    </div>\r\n                    <div class=\"file-actions\">\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"previewFile(file)\">\r\n                        <i class=\"el-icon-view\"></i>\r\n                        预览\r\n                      </el-button>\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"downloadFile(file)\">\r\n                        <i class=\"el-icon-download\"></i>\r\n                        下载\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.files || currentDebtor.files.length === 0\" class=\"empty-files\">\r\n                    <i class=\"el-icon-folder-opened\"></i>\r\n                    <p>暂无相关文件</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 历史记录 -->\r\n            <div v-if=\"activeTab === 'history'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  历史记录\r\n                </h4>\r\n                <div class=\"history-timeline\">\r\n                  <div v-for=\"record in currentDebtor.history\" :key=\"record.id\" class=\"timeline-item\">\r\n                    <div class=\"timeline-dot\"></div>\r\n                    <div class=\"timeline-content\">\r\n                      <h6>{{ record.action }}</h6>\r\n                      <p>{{ record.description }}</p>\r\n                      <span class=\"timeline-time\">{{ record.time }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.history || currentDebtor.history.length === 0\" class=\"empty-history\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    <p>暂无历史记录</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      // 债务人详情面板相关\r\n      showDebtorPanel: false,\r\n      activeTab: 'basic',\r\n      currentDebtor: {},\r\n      // AI生成相关\r\n      aiGenerating: false,\r\n      aiGeneratedContent: '',\r\n      contractTypes: [], // 合同类型列表\r\n      // 处理方式相关\r\n      processMethod: 'upload', // 默认为手动上传，可选值：'ai' | 'upload'\r\n      fileGenerating: false, // 文件生成状态\r\n      aiGeneratedFile: null, // AI生成的文件信息\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n  methods: {\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        0: 'warning',  // 待处理\r\n        1: 'primary',  // 处理中\r\n        2: 'success'   // 已处理\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待处理',\r\n        1: '处理中',\r\n        2: '已处理'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_deal: -1,\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        is_deal: 1,\r\n        type_title: \"\",\r\n        desc: \"\",\r\n        file_path: \"\",\r\n        content: \"\"\r\n      };\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      this.handleDialogClose();\r\n      this.dialogFormVisible = false;\r\n      // 重置AI生成内容\r\n      this.aiGeneratedContent = '';\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"债务催收\",\r\n            template_file: \"/uploads/templates/debt_collection_template.docx\",\r\n            template_name: \"债务催收律师函模板.docx\",\r\n            template_size: 245760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您欠付我方当事人的债务事宜，特致函如下：\r\n\r\n一、债务事实\r\n根据相关证据材料显示，您于{{debt_date}}向我方当事人借款人民币{{debt_amount}}元，约定还款期限为{{repay_date}}。但截至目前，您仍未履行还款义务，已构成违约。\r\n\r\n二、法律后果\r\n您的上述行为已构成违约，根据《中华人民共和国民法典》相关规定，您应当承担相应的法律责任。\r\n\r\n三、催告要求\r\n现特函告知，请您在收到本函后7日内，将所欠款项{{debt_amount}}元及相应利息一次性支付给我方当事人。\r\n\r\n四、法律警告\r\n如您在上述期限内仍不履行还款义务，我方将依法采取包括但不限于向人民法院提起诉讼等法律手段维护我方当事人的合法权益，由此产生的一切法律后果由您承担。\r\n\r\n特此函告！\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n联系电话：{{contact_phone}}\r\n地址：{{law_firm_address}}\r\n            `\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"合同违约\",\r\n            template_file: \"/uploads/templates/contract_breach_template.pdf\",\r\n            template_name: \"合同违约律师函模板.pdf\",\r\n            template_size: 512000,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您违反合同约定的事宜，特致函如下：\r\n\r\n一、合同事实\r\n您与我方当事人于{{contract_date}}签订了《{{contract_title}}》，约定了双方的权利义务。\r\n\r\n二、违约事实\r\n根据合同约定及相关证据，您存在以下违约行为：\r\n{{breach_details}}\r\n\r\n三、法律后果\r\n您的违约行为已给我方当事人造成了经济损失，根据合同约定及法律规定，您应当承担违约责任。\r\n\r\n四、要求\r\n请您在收到本函后7日内：\r\n1. 立即停止违约行为\r\n2. 履行合同义务\r\n3. 赔偿相应损失\r\n\r\n如您拒不履行，我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"知识产权侵权\",\r\n            template_file: \"/uploads/templates/ip_infringement_template.doc\",\r\n            template_name: \"知识产权侵权律师函模板.doc\",\r\n            template_size: 327680,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您侵犯我方当事人知识产权的事宜，特致函如下：\r\n\r\n一、权利基础\r\n我方当事人依法享有{{ip_type}}的专有权利，该权利受法律保护。\r\n\r\n二、侵权事实\r\n经调查发现，您未经我方当事人许可，擅自{{infringement_details}}，侵犯了我方当事人的合法权益。\r\n\r\n三、法律后果\r\n您的行为构成侵权，应当承担停止侵害、赔偿损失等法律责任。\r\n\r\n四、要求\r\n请您在收到本函后立即：\r\n1. 停止一切侵权行为\r\n2. 销毁侵权产品\r\n3. 赔偿经济损失\r\n\r\n否则我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"劳动争议\",\r\n            template_file: \"/uploads/templates/labor_dispute_template.docx\",\r\n            template_name: \"劳动争议律师函模板.docx\",\r\n            template_size: 298760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就劳动争议事宜，特致函如下：\r\n\r\n一、劳动关系\r\n我方当事人与您存在劳动关系，期间为{{employment_period}}。\r\n\r\n二、争议事实\r\n{{dispute_details}}\r\n\r\n三、法律依据\r\n根据《劳动法》、《劳动合同法》等相关法律法规，您应当履行相应义务。\r\n\r\n四、要求\r\n请您在收到本函后7日内妥善处理相关事宜，否则我方将通过法律途径解决。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"房屋租赁\",\r\n            template_file: \"/uploads/templates/lease_dispute_template.pdf\",\r\n            template_name: \"房屋租赁纠纷律师函模板.pdf\",\r\n            template_size: 445760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就房屋租赁纠纷事宜，特致函如下：\r\n\r\n一、租赁关系\r\n您与我方当事人签订了房屋租赁合同，租赁期限为{{lease_period}}。\r\n\r\n二、违约事实\r\n{{lease_breach_details}}\r\n\r\n三、要求\r\n请您在收到本函后立即：\r\n1. {{specific_requirements}}\r\n2. 支付相关费用\r\n3. 配合解决纠纷\r\n\r\n如不配合，我方将依法维权。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成律师函\r\n    async generateLawyerLetter() {\r\n      if (!this.ruleForm.title || !this.ruleForm.type_title) {\r\n        this.$message.warning('请先确保工单信息完整');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n\r\n      try {\r\n        // 1. 根据工单标题和类型找到对应的合同模板\r\n        const matchedTemplate = this.findMatchingTemplate();\r\n\r\n        if (!matchedTemplate) {\r\n          this.$message.warning('未找到匹配的律师函模板，请先在合同类型管理中上传相应模板');\r\n          this.aiGenerating = false;\r\n          return;\r\n        }\r\n\r\n        // 2. 获取债务人详情信息\r\n        const debtorInfo = await this.getDebtorInfo();\r\n\r\n        // 3. 获取关联用户信息\r\n        const userInfo = await this.getUserInfo();\r\n\r\n        // 4. 模拟AI生成过程\r\n        await this.simulateAiGeneration(matchedTemplate, debtorInfo, userInfo);\r\n\r\n        this.$message.success('AI律师函生成完成！');\r\n\r\n        // 如果是AI模式，自动生成文件\r\n        if (this.processMethod === 'ai') {\r\n          setTimeout(() => {\r\n            this.generateAiFile();\r\n          }, 500);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI生成失败:', error);\r\n        this.$message.error('AI生成失败，请重试');\r\n      } finally {\r\n        this.aiGenerating = false;\r\n      }\r\n    },\r\n\r\n    // 查找匹配的模板\r\n    findMatchingTemplate() {\r\n      // 根据工单标题和类型匹配模板\r\n      const title = this.ruleForm.title.toLowerCase();\r\n      const typeTitle = this.ruleForm.type_title.toLowerCase();\r\n\r\n      // 匹配规则\r\n      const matchRules = [\r\n        { keywords: ['债务', '催收', '欠款', '借款'], templateId: 1 },\r\n        { keywords: ['合同', '违约', '违反'], templateId: 2 },\r\n        { keywords: ['知识产权', '侵权', '商标', '专利'], templateId: 3 },\r\n        { keywords: ['劳动', '工资', '员工'], templateId: 4 },\r\n        { keywords: ['租赁', '房屋', '租金'], templateId: 5 }\r\n      ];\r\n\r\n      for (const rule of matchRules) {\r\n        if (rule.keywords.some(keyword =>\r\n          title.includes(keyword) || typeTitle.includes(keyword)\r\n        )) {\r\n          return this.contractTypes.find(type => type.id === rule.templateId);\r\n        }\r\n      }\r\n\r\n      // 默认返回债务催收模板\r\n      return this.contractTypes.find(type => type.id === 1);\r\n    },\r\n\r\n    // 获取债务人信息\r\n    async getDebtorInfo() {\r\n      // 模拟获取债务人详细信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: this.ruleForm.dt_name || '张三',\r\n            id_card: '110101199001011234',\r\n            phone: '13800138001',\r\n            address: '北京市朝阳区建国门外大街1号',\r\n            debt_amount: '100000.00',\r\n            debt_type: '借款纠纷',\r\n            debt_date: '2023-06-15',\r\n            repay_date: '2023-12-15'\r\n          });\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      // 模拟获取关联用户信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: '李明',\r\n            phone: this.ruleForm.uid || '13900139001',\r\n            register_time: '2023-01-15',\r\n            status: 'active'\r\n          });\r\n        }, 300);\r\n      });\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    async simulateAiGeneration(template, debtorInfo, userInfo) {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          // 替换模板中的变量\r\n          let content = template.template_content;\r\n\r\n          // 替换债务人信息\r\n          content = content.replace(/\\{\\{debtor_name\\}\\}/g, debtorInfo.name);\r\n          content = content.replace(/\\{\\{debt_amount\\}\\}/g, debtorInfo.debt_amount);\r\n          content = content.replace(/\\{\\{debt_date\\}\\}/g, debtorInfo.debt_date);\r\n          content = content.replace(/\\{\\{repay_date\\}\\}/g, debtorInfo.repay_date);\r\n\r\n          // 替换用户信息\r\n          content = content.replace(/\\{\\{user_name\\}\\}/g, userInfo.name);\r\n\r\n          // 替换其他信息\r\n          content = content.replace(/\\{\\{current_date\\}\\}/g, new Date().toLocaleDateString('zh-CN'));\r\n          content = content.replace(/\\{\\{law_firm_name\\}\\}/g, '北京市XX律师事务所');\r\n          content = content.replace(/\\{\\{contact_phone\\}\\}/g, '010-12345678');\r\n          content = content.replace(/\\{\\{law_firm_address\\}\\}/g, '北京市朝阳区XX大厦XX层');\r\n\r\n          // 根据工单描述添加具体内容\r\n          if (this.ruleForm.desc) {\r\n            content = content.replace(/\\{\\{breach_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{dispute_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{infringement_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{lease_breach_details\\}\\}/g, this.ruleForm.desc);\r\n          }\r\n\r\n          // 清理未替换的变量\r\n          content = content.replace(/\\{\\{[^}]+\\}\\}/g, '[待填写]');\r\n\r\n          this.aiGeneratedContent = content.trim();\r\n          resolve();\r\n        }, 2000); // 模拟2秒的AI生成时间\r\n      });\r\n    },\r\n\r\n    // 使用AI生成的内容\r\n    useAiContent() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('没有可用的AI生成内容');\r\n        return;\r\n      }\r\n\r\n      // 将AI生成的内容设置到处理说明中\r\n      this.ruleForm.content = this.aiGeneratedContent;\r\n\r\n      // 自动设置为已完成状态\r\n      this.ruleForm.is_deal = 2;\r\n\r\n      this.$message.success('已应用AI生成的律师函内容');\r\n    },\r\n\r\n    // 重新生成内容\r\n    regenerateContent() {\r\n      this.aiGeneratedContent = '';\r\n      this.generateLawyerLetter();\r\n    },\r\n\r\n    // 处理方式切换\r\n    onProcessMethodChange(method) {\r\n      // 清空之前的数据\r\n      if (method === 'ai') {\r\n        // 切换到AI模式，清空上传的文件\r\n        this.ruleForm.file_path = '';\r\n        this.aiGeneratedFile = null;\r\n      } else {\r\n        // 切换到上传模式，清空AI生成的内容\r\n        this.aiGeneratedContent = '';\r\n        this.aiGeneratedFile = null;\r\n      }\r\n    },\r\n\r\n    // AI生成文件\r\n    async generateAiFile() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('请先生成律师函内容');\r\n        return;\r\n      }\r\n\r\n      this.fileGenerating = true;\r\n      try {\r\n        // 模拟文件生成过程\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n        // 这里应该调用实际的文件生成接口\r\n        // 将AI生成的内容转换为文件\r\n        const fileName = `律师函_${this.ruleForm.dt_name || '债务人'}_${new Date().getTime()}.docx`;\r\n\r\n        this.aiGeneratedFile = {\r\n          name: fileName,\r\n          path: `/uploads/lawyer_letters/${fileName}`,\r\n          size: '25KB'\r\n        };\r\n\r\n        // 将生成的文件路径设置到表单中\r\n        this.ruleForm.file_path = this.aiGeneratedFile.path;\r\n\r\n        this.$message.success('文件生成成功');\r\n      } catch (error) {\r\n        console.error('文件生成失败:', error);\r\n        this.$message.error('文件生成失败，请重试');\r\n      } finally {\r\n        this.fileGenerating = false;\r\n      }\r\n    },\r\n\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n\r\n      // 使用模拟数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        }\r\n      ];\r\n\r\n      // 查找对应的数据\r\n      const foundData = testData.find(item => item.id === id);\r\n\r\n      if (foundData) {\r\n        _this.ruleForm = { ...foundData };\r\n        _this.dialogFormVisible = true;\r\n        console.log('加载律师函详情数据:', _this.ruleForm);\r\n      } else {\r\n        _this.$message({\r\n          type: \"error\",\r\n          message: \"未找到对应的律师函数据\",\r\n        });\r\n      }\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27) {\r\n        if (this.dialogFormVisible) {\r\n          this.cancelDialog();\r\n        }\r\n        if (this.showDebtorPanel) {\r\n          this.closeDebtorPanel();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 显示债务人详情\r\n    showDebtorDetail(row) {\r\n      console.log('显示债务人详情:', row);\r\n\r\n      // 模拟获取债务人详细信息\r\n      this.currentDebtor = {\r\n        ...row,\r\n        // 基本信息\r\n        id_card: this.generateIdCard(),\r\n        phone: this.generatePhone(),\r\n        address: this.generateAddress(),\r\n        debt_amount: this.generateDebtAmount(),\r\n        debt_type: this.generateDebtType(),\r\n\r\n        // 关联用户信息\r\n        user_name: this.generateUserName(row.uid),\r\n        user_phone: row.uid,\r\n        user_register_time: this.generateRegisterTime(),\r\n        user_status: 'active',\r\n\r\n        // 相关文件\r\n        files: this.generateFiles(row.dt_name),\r\n\r\n        // 历史记录\r\n        history: this.generateHistory(row.dt_name)\r\n      };\r\n\r\n      this.activeTab = 'basic';\r\n      this.showDebtorPanel = true;\r\n    },\r\n\r\n    // 关闭债务人详情面板\r\n    closeDebtorPanel() {\r\n      this.showDebtorPanel = false;\r\n      this.currentDebtor = {};\r\n    },\r\n\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'zip': 'el-icon-folder-opened',\r\n        'rar': 'el-icon-folder-opened'\r\n      };\r\n      return iconMap[fileType] || 'el-icon-document';\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      console.log('预览文件:', file);\r\n      this.$message.info('文件预览功能开发中...');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      console.log('下载文件:', file);\r\n      this.$message.success('开始下载文件: ' + file.name);\r\n    },\r\n\r\n    // 生成模拟数据的辅助方法\r\n    generateIdCard() {\r\n      const prefixes = ['110101', '310101', '440101', '500101'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const year = 1980 + Math.floor(Math.random() * 30);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      const suffix = String(Math.floor(Math.random() * 9999)).padStart(4, '0');\r\n      return `${prefix}${year}${month}${day}${suffix}`;\r\n    },\r\n\r\n    generatePhone() {\r\n      const prefixes = ['138', '139', '150', '151', '188', '189'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const suffix = String(Math.floor(Math.random() * 100000000)).padStart(8, '0');\r\n      return `${prefix}${suffix}`;\r\n    },\r\n\r\n    generateAddress() {\r\n      const addresses = [\r\n        '北京市朝阳区建国门外大街1号',\r\n        '上海市浦东新区陆家嘴环路1000号',\r\n        '广州市天河区珠江新城花城大道85号',\r\n        '深圳市南山区深南大道10000号',\r\n        '杭州市西湖区文三路90号'\r\n      ];\r\n      return addresses[Math.floor(Math.random() * addresses.length)];\r\n    },\r\n\r\n    generateDebtAmount() {\r\n      const amounts = ['50000.00', '100000.00', '200000.00', '500000.00', '1000000.00'];\r\n      return amounts[Math.floor(Math.random() * amounts.length)];\r\n    },\r\n\r\n    generateDebtType() {\r\n      const types = ['借款纠纷', '合同违约', '房屋租赁', '劳动争议', '知识产权'];\r\n      return types[Math.floor(Math.random() * types.length)];\r\n    },\r\n\r\n    generateUserName(phone) {\r\n      const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴'];\r\n      const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];\r\n      const surname = surnames[Math.floor(Math.random() * surnames.length)];\r\n      const name = names[Math.floor(Math.random() * names.length)];\r\n      return `${surname}${name}`;\r\n    },\r\n\r\n    generateRegisterTime() {\r\n      const year = 2020 + Math.floor(Math.random() * 4);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      return `${year}-${month}-${day} 10:30:00`;\r\n    },\r\n\r\n    generateFiles(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: `${debtorName}_身份证.jpg`,\r\n          type: 'jpg',\r\n          size: '2.5MB',\r\n          upload_time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: `${debtorName}_借款合同.pdf`,\r\n          type: 'pdf',\r\n          size: '1.2MB',\r\n          upload_time: '2024-01-16 09:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: `${debtorName}_银行流水.pdf`,\r\n          type: 'pdf',\r\n          size: '3.8MB',\r\n          upload_time: '2024-01-17 16:45:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    generateHistory(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          action: '创建债务人档案',\r\n          description: `创建了${debtorName}的债务人档案`,\r\n          time: '2024-01-15 09:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          action: '上传相关文件',\r\n          description: '上传了身份证、合同等相关文件',\r\n          time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          action: '发起律师函',\r\n          description: '针对债务纠纷发起律师函制作申请',\r\n          time: '2024-01-16 10:20:00'\r\n        },\r\n        {\r\n          id: 4,\r\n          action: '更新债务信息',\r\n          description: '更新了债务金额和联系方式',\r\n          time: '2024-01-17 15:10:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载律师函测试数据...');\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.order_sn.toLowerCase().includes(keyword) ||\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.uid.includes(keyword) ||\r\n              item.dt_name.includes(keyword)\r\n            );\r\n          }\r\n\r\n          // 状态筛选\r\n          if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n            filteredData = filteredData.filter(item => item.is_deal === _this.search.is_deal);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('律师函数据加载完成:', _this.list);\r\n          console.log('总数:', _this.total);\r\n        } catch (error) {\r\n          console.error('加载律师函测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 300);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存操作\r\n          console.log('保存律师函数据:', this.ruleForm);\r\n\r\n          // 模拟API延迟\r\n          setTimeout(() => {\r\n            _this.$message({\r\n              type: \"success\",\r\n              message: \"律师函处理状态更新成功！\",\r\n            });\r\n            this.getData();\r\n            _this.dialogFormVisible = false;\r\n          }, 500);\r\n\r\n          // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n          /*\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n          */\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.lawyer-letter-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section h2.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  align-items: flex-end;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input,\r\n.search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  border: none;\r\n  padding: 10px 24px;\r\n}\r\n\r\n.reset-btn {\r\n  padding: 10px 24px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.table-header {\r\n  padding: 20px 24px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.table-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.lawyer-table {\r\n  margin: 0 24px 20px;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.order-cell,\r\n.title-cell,\r\n.desc-cell,\r\n.phone-cell,\r\n.debtor-cell,\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.order-text,\r\n.title-text,\r\n.desc-text {\r\n  font-weight: 500;\r\n}\r\n\r\n.desc-text {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 债务人单元格可点击样式 */\r\n.debtor-cell.clickable {\r\n  cursor: pointer;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n}\r\n\r\n.debtor-cell.clickable:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.debtor-name {\r\n  font-weight: 500;\r\n  color: #409eff;\r\n}\r\n\r\n.arrow-icon {\r\n  font-size: 12px;\r\n  opacity: 0.6;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.debtor-cell.clickable:hover .arrow-icon {\r\n  opacity: 1;\r\n  transform: translateX(2px);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.process-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.dialog-content {\r\n  padding: 0 8px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 32px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.section-title {\r\n  margin: 0 0 20px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.readonly-input,\r\n.readonly-textarea {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.status-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.status-radio {\r\n  padding: 12px 20px;\r\n  border: 2px solid #dcdfe6;\r\n  border-radius: 8px;\r\n  background-color: white;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.status-radio:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.status-radio.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.completion-section {\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.file-input {\r\n  flex: 1;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-textarea {\r\n  margin-top: 16px;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  padding: 10px 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .lawyer-letter-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 债务人详情右侧滑出面板 */\r\n.debtor-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debtor-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 800px;\r\n  height: 100%;\r\n  background-color: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-width: 90vw;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n/* 面板头部 */\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.header-info h3.panel-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-subtitle {\r\n  margin: 4px 0 0 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.close-btn {\r\n  color: white !important;\r\n  font-size: 18px;\r\n}\r\n\r\n.close-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1) !important;\r\n}\r\n\r\n/* 面板主体 */\r\n.panel-body {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧菜单 */\r\n.sidebar-menu {\r\n  width: 200px;\r\n  background-color: #f8f9fa;\r\n  border-right: 1px solid #ebeef5;\r\n  padding: 16px 0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.menu-item.active {\r\n  background-color: #409eff;\r\n  color: white;\r\n  position: relative;\r\n}\r\n\r\n.menu-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 20px;\r\n  background-color: white;\r\n}\r\n\r\n.menu-item i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 信息区块 */\r\n.info-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.info-section .section-title {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  min-height: 60px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n  padding-top: 2px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n  line-height: 1.5;\r\n}\r\n\r\n.debt-amount {\r\n  color: #f56c6c !important;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.user-avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.user-info h5 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  color: #303133;\r\n}\r\n\r\n.user-info p {\r\n  margin: 4px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表 */\r\n.file-list {\r\n  space-y: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.file-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 6px;\r\n  background-color: #409eff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-info h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-info p {\r\n  margin: 2px 0;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.file-size {\r\n  color: #409eff !important;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.empty-files,\r\n.empty-history {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-files i,\r\n.empty-history i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n/* 历史时间线 */\r\n.history-timeline {\r\n  position: relative;\r\n  padding-left: 20px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.timeline-item:not(:last-child)::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -15px;\r\n  top: 20px;\r\n  width: 2px;\r\n  height: calc(100% - 10px);\r\n  background-color: #e4e7ed;\r\n}\r\n\r\n.timeline-dot {\r\n  position: absolute;\r\n  left: -20px;\r\n  top: 5px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #409eff;\r\n  border: 2px solid white;\r\n  box-shadow: 0 0 0 2px #409eff;\r\n}\r\n\r\n.timeline-content {\r\n  background-color: #fafbfc;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.timeline-content h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.timeline-content p {\r\n  margin: 0 0 8px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .panel-content {\r\n    width: 700px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n    max-width: 100vw;\r\n  }\r\n\r\n  .panel-body {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    width: 100%;\r\n    display: flex;\r\n    overflow-x: auto;\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .menu-item {\r\n    white-space: nowrap;\r\n    min-width: 120px;\r\n    justify-content: center;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n/* AI生成功能样式 */\r\n.ai-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.ai-section .section-title {\r\n  color: white;\r\n  border-bottom-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.ai-generation-content {\r\n  padding: 16px 0;\r\n}\r\n\r\n.ai-description {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.ai-description p {\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.ai-actions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-generate-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  font-weight: 600;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ai-generate-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.ai-result {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.result-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: white;\r\n}\r\n\r\n.generated-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ai-content-textarea {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 6px;\r\n}\r\n\r\n.ai-content-textarea .el-textarea__inner {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: #333;\r\n  font-family: 'Microsoft YaHei', sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.ai-result-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.ai-result-actions .el-button {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.ai-result-actions .el-button:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 处理方式选择样式 */\r\n.process-method-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.method-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.method-radio {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border: 2px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  background: #fff;\r\n}\r\n\r\n.method-radio:hover {\r\n  border-color: #409eff;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.method-radio.is-checked {\r\n  border-color: #409eff;\r\n  background: #e6f7ff;\r\n  color: #409eff;\r\n}\r\n\r\n.method-radio i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* AI处理区域样式 */\r\n.ai-process-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-mode-alert {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-content-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-preview-textarea .el-textarea__inner {\r\n  background: #fff;\r\n  border: 1px solid #dcdfe6;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.ai-file-generation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.generate-file-btn {\r\n  border-radius: 6px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.generated-file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  color: #409eff;\r\n}\r\n\r\n.generated-file-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 手动上传区域样式 */\r\n.upload-process-section {\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n</style>\r\n"]}]}