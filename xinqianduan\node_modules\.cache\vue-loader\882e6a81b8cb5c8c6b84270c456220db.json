{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue?vue&type=style&index=0&id=070cf012&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue", "mtime": 1748617691748}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dingdan.vue"], "names": [], "mappings": ";AA8gDA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "dingdan.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n\t<div class=\"order-management-container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<div class=\"page-header\">\r\n\t\t\t<div class=\"header-left\">\r\n\t\t\t\t<h2 class=\"page-title\">\r\n\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t{{ this.$router.currentRoute.name }}\r\n\t\t\t\t</h2>\r\n\t\t\t\t<div class=\"page-subtitle\">管理客户签约订单和合同信息</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t<el-button\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\ticon=\"el-icon-refresh\"\r\n\t\t\t\t\t\t@click=\"refulsh\"\r\n\t\t\t\t\t\tclass=\"refresh-btn\"\r\n\t\t\t\t>\r\n\t\t\t\t\t刷新数据\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 重要通知和提醒区域 -->\r\n\t\t<div class=\"notifications-section\" v-if=\"hasImportantNotifications\">\r\n\t\t\t<el-card shadow=\"hover\" class=\"notification-card\">\r\n\t\t\t\t<div class=\"notification-header\">\r\n\t\t\t\t\t<i class=\"el-icon-warning-outline notification-icon\"></i>\r\n\t\t\t\t\t<span class=\"notification-title\">重要提醒</span>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tsize=\"mini\" \r\n\t\t\t\t\t\t@click=\"dismissNotifications\"\r\n\t\t\t\t\t\tclass=\"dismiss-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"notification-content\">\r\n\t\t\t\t\t<div class=\"notification-list\">\r\n\t\t\t\t\t\t<!-- 待审核订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"pendingOrders > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item urgent\"\r\n\t\t\t\t\t\t\t@click=\"filterByStatus('1')\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ pendingOrders }}</strong> 个订单待审核，请及时处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"warning\">立即处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 即将到期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiringOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item warning\"\r\n\t\t\t\t\t\t\t@click=\"showExpiringOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiringOrders.length }}</strong> 个订单即将到期，请提醒客户续费\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\">查看详情</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 已过期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiredOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item error\"\r\n\t\t\t\t\t\t\t@click=\"showExpiredOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiredOrders.length }}</strong> 个订单已过期，需要联系客户处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\">紧急处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 高价值订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"highValueOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item info\"\r\n\t\t\t\t\t\t\t@click=\"showHighValueOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ highValueOrders.length }}</strong> 个高价值订单需要重点关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"success\">查看订单</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\r\n\t\t<!-- 统计卡片区域 -->\r\n\t\t<div class=\"stats-section\">\r\n\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon total-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ total }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总订单数</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +8%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('1')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon pending-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">待审核</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change warning\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i> 需关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('2')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon approved-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-circle-check\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">已通过</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +12%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"showRevenueChart\">\r\n\t\t\t\t\t\t<div class=\"stat-icon revenue-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-money\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">¥{{ totalRevenue }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总收入</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +15%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t</div>\r\n\r\n\t\t<!-- 搜索和筛选区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"search-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-search\"></i>\r\n\t\t\t\t\t\t搜索与筛选\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"card-subtitle\">快速查找和管理订单信息</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t\t<el-button-group class=\"action-group\">\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t\t导出\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n\t\t\t\t\t\t\t刷新\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</el-button-group>\r\n\t\t\t\t\t<el-button type=\"primary\" @click=\"batchAudit\" icon=\"el-icon-check\" class=\"primary-action\">\r\n\t\t\t\t\t\t批量审核\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"search-section\">\r\n\t\t\t\t<el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n\t\t\t\t\t<div class=\"search-row\">\r\n\t\t\t\t\t\t<el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tprefix-icon=\"el-icon-search\"\r\n\t\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t\t\********************=\"getData()\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"业务员\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.salesman\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tclass=\"search-select\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"审核状态\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-select v-model=\"search.status\" placeholder=\"选择状态\" clearable class=\"search-select\">\r\n\t\t\t\t\t\t\t\t<el-option label=\"全部状态\" value=\"\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未审核\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"已通过\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未通过\" value=\"3\" />\r\n\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item class=\"search-actions-item\">\r\n\t\t\t\t\t\t\t<div class=\"search-actions\">\r\n\t\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\" class=\"search-btn\">\r\n\t\t\t\t\t\t\t\t\t搜索\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n\t\t\t\t\t\t\t\t\t重置\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n\t\t\t\t\t\t\t\t\t<i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ showAdvanced ? '收起' : '高级筛选' }}\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 高级筛选区域 -->\r\n\t\t\t\t\t<transition name=\"slide-fade\">\r\n\t\t\t\t\t\t<div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n\t\t\t\t\t\t\t<el-divider content-position=\"left\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-setting\"></i>\r\n\t\t\t\t\t\t\t\t高级筛选选项\r\n\t\t\t\t\t\t\t</el-divider>\r\n\t\t\t\t\t\t\t<div class=\"advanced-content\">\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付时间\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"date-picker\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.payType\" placeholder=\"选择支付方式\" class=\"pay-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部方式\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全款支付\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"分期付款\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"金额范围\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"amount-range\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.minAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最小金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"range-separator\">-</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.maxAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最大金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"套餐类型\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.packageType\" placeholder=\"选择套餐\" clearable class=\"package-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部套餐\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"基础套餐\" value=\"basic\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"高级套餐\" value=\"advanced\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"专业套餐\" value=\"professional\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.sortBy\" placeholder=\"排序字段\" class=\"sort-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"创建时间\" value=\"create_time\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"支付金额\" value=\"pay_age\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"订单状态\" value=\"status\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"到期时间\" value=\"end_time\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item class=\"advanced-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n\t\t\t\t\t\t\t\t\t\t\t应用筛选\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n\t\t\t\t\t\t\t\t\t\t\t清空选项\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</transition>\r\n\t\t\t\t</el-form>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 数据表格区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"table-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-tickets\"></i>\r\n\t\t\t\t\t\t订单列表\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"selected-info\" v-if=\"selectedRows.length > 0\">\r\n\t\t\t\t\t\t已选择 {{ selectedRows.length }} 项\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"table-actions\">\r\n\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t导出数据\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchApprove\" \r\n\t\t\t\t\t\ticon=\"el-icon-check\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量通过\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchReject\" \r\n\t\t\t\t\t\ticon=\"el-icon-close\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"danger\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量拒绝\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<el-table \r\n\t\t\t\t\t:data=\"list\" \r\n\t\t\t\t\tv-loading=\"loading\" \r\n\t\t\t\t\tclass=\"modern-table\"\r\n\t\t\t\t\t:row-class-name=\"tableRowClassName\"\r\n\t\t\t\t\t@selection-change=\"handleSelectionChange\"\r\n\t\t\t>\r\n\t\t\t\t<el-table-column type=\"selection\" width=\"55\" />\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"客户信息\" min-width=\"200\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"client-info\" @click=\"viewUserData(scope.row.client?.id)\">\r\n\t\t\t\t\t\t\t<div class=\"client-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"client-avatar\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-office-building\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"client-details\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"company-name\">\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.company || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-info\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"contact-name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-phone\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-phone\"></i>\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"套餐内容\" min-width=\"180\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"package-info\">\r\n\t\t\t\t\t\t\t<div class=\"package-name\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.taocan?.title || '未选择套餐' }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-price\">\r\n\t\t\t\t\t\t\t\t<span class=\"price-label\">价格：</span>\r\n\t\t\t\t\t\t\t\t<span class=\"price-value\">¥{{ scope.row.taocan?.price || 0 }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-duration\">\r\n\t\t\t\t\t\t\t\t<el-tag size=\"small\" type=\"info\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.taocan?.year || 0 }}年服务\r\n\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"支付情况\" min-width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"payment-info\">\r\n\t\t\t\t\t\t\t<div class=\"payment-type\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-wallet\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.pay_type == 1 ? \"全款\" : `分期/${scope.row.qishu}期` }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-amount\">\r\n\t\t\t\t\t\t\t\t<span class=\"paid\">已付：¥{{ scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-amount\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<span class=\"remaining\">余款：¥{{ scope.row.total_price - scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-progress\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<el-progress \r\n\t\t\t\t\t\t\t\t\t:percentage=\"Math.round((scope.row.pay_age / scope.row.total_price) * 100)\" \r\n\t\t\t\t\t\t\t\t\t:stroke-width=\"6\"\r\n\t\t\t\t\t\t\t\t\t:show-text=\"false\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"审核状态\" width=\"120\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"status-info\" @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t:type=\"getStatusType(scope.row.status)\" \r\n\t\t\t\t\t\t\t\t\tclass=\"status-tag\"\r\n\t\t\t\t\t\t\t\t\t:effect=\"scope.row.status == 1 ? 'plain' : 'dark'\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{{ getStatusText(scope.row.status) }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t<div v-if=\"scope.row.status == 3\" class=\"status-reason\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.status_msg }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\" width=\"100\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"member-info\">\r\n\t\t\t\t\t\t\t<el-tag type=\"info\" size=\"small\">\r\n\t\t\t\t\t\t\t\t{{ scope.row.member?.title || '未分配' }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"时间信息\" min-width=\"140\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"time-info\">\r\n\t\t\t\t\t\t\t<div class=\"create-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t\t\t创建：{{ formatDate(scope.row.create_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"end-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-date\"></i>\r\n\t\t\t\t\t\t\t\t到期：{{ formatDate(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-days\" :class=\"getRemainingDaysClass(scope.row.end_time)\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t\t{{ getRemainingDays(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\" width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"editData(scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"view-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-view\"></i>\r\n\t\t\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickApprove(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"approve-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-check\"></i>\r\n\t\t\t\t\t\t\t\t\t通过\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickReject(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"reject-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t\t\t\t\t拒绝\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"delData(scope.$index, scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"delete-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-delete\"></i>\r\n\t\t\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t\r\n\t\t\t<!-- 分页 -->\r\n\t\t\t<div class=\"pagination-wrapper\">\r\n\t\t\t\t<el-pagination\r\n\t\t\t\t\t\t@size-change=\"handleSizeChange\"\r\n\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t\t:page-sizes=\"[20, 50, 100, 200]\"\r\n\t\t\t\t\t\t:page-size=\"size\"\r\n\t\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\"\r\n\t\t\t\t\t\t:total=\"total\"\r\n\t\t\t\t\t\tbackground\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 订单详情对话框 -->\r\n\t\t<el-dialog \r\n\t\t\t\ttitle=\"订单详情\" \r\n\t\t\t\t:visible.sync=\"dialogFormVisible\" \r\n\t\t\t\t:close-on-click-modal=\"false\" \r\n\t\t\t\twidth=\"85%\"\r\n\t\t\t\tclass=\"order-detail-dialog\"\r\n\t\t>\r\n\t\t\t\t<div v-if=\"is_info\" class=\"order-detail-content\">\r\n\t\t\t\t\t\t<!-- 客户信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t客户信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"公司名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"info\">{{ info.client?.company || '未填写' }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系人\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系方式\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"showImage(info.client?.pic_path)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client?.pic_path\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t查看执照\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag v-else type=\"info\">暂无</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"调解员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.tiaojie_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"法务专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.fawu_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"立案专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.lian_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"合同专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.htsczy_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"指定律师\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.ls_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 债务人信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.debts && info.debts.length > 0\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user-solid\"></i>\r\n\t\t\t\t\t\t\t\t\t\t债务人信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-table :data=\"info.debts\" size=\"medium\" class=\"debt-table\">\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"name\" label=\"债务人姓名\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"tel\" label=\"联系电话\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"money\" label=\"债务金额（元）\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"status\" label=\"状态\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag :type=\"getDebtStatusType(scope.row.status)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.status }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t</el-table>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 套餐信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.taocan\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t\t\t套餐信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"primary\">{{ info.taocan.title }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-highlight\">¥{{ info.taocan.price }}</span>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"服务年限\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"success\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t\t\t<el-button @click=\"dialogFormVisible = false\">关闭</el-button>\r\n\t\t\t\t\t\t<el-button type=\"primary\" @click=\"downloadOrder\">下载订单</el-button>\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 图片查看对话框 -->\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n\t\t\t\t<div class=\"image-viewer\">\r\n\t\t\t\t\t\t<el-image :src=\"show_image\" fit=\"contain\" />\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 收入统计图表对话框 -->\r\n\t\t<el-dialog title=\"收入统计分析\" :visible.sync=\"showRevenueDialog\" width=\"80%\" class=\"revenue-dialog\">\r\n\t\t\t<div class=\"revenue-stats\">\r\n\t\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>月度收入趋势</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-data-line chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>月度收入趋势图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"mock-chart-data\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 60%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 80%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 45%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 70%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 90%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 65%\"></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>支付方式分布</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-pie-chart chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>支付方式比例图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"payment-stats\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot full-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t全款支付: {{ fullPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot installment-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t分期付款: {{ installmentPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t\t<el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n\t\t\t\t\t<el-col :span=\"24\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>订单状态统计</h4>\r\n\t\t\t\t\t\t\t<div class=\"status-overview\">\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle pending-circle\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>待审核</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle approved-circle\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已通过</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle rejected-circle\">{{ rejectedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已拒绝</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle total-circle\">{{ total }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>总计</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 数据导出对话框 -->\r\n\t\t<el-dialog title=\"数据导出\" :visible.sync=\"showExportDialog\" width=\"600px\" class=\"export-dialog\">\r\n\t\t\t<el-form :model=\"exportForm\" label-width=\"120px\">\r\n\t\t\t\t<el-form-item label=\"导出格式\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.format\">\r\n\t\t\t\t\t\t<el-radio label=\"excel\">Excel (.xlsx)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"csv\">CSV (.csv)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"pdf\">PDF (.pdf)</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"导出内容\">\r\n\t\t\t\t\t<el-checkbox-group v-model=\"exportForm.fields\">\r\n\t\t\t\t\t\t<el-checkbox label=\"client\">客户信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"package\">套餐信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"payment\">支付情况</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"status\">审核状态</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"time\">时间信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"member\">业务员信息</el-checkbox>\r\n\t\t\t\t\t</el-checkbox-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"数据范围\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.range\">\r\n\t\t\t\t\t\t<el-radio label=\"all\">全部数据</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"current\">当前页面</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"selected\">选中项目</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"filtered\">筛选结果</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"时间范围\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tv-model=\"exportForm.dateRange\"\r\n\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tstyle=\"width: 100%\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"showExportDialog = false\">取消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"executeExport\" :loading=\"exportLoading\">\r\n\t\t\t\t\t<i class=\"el-icon-download\"></i>\r\n\t\t\t\t\t开始导出\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t\tallSize: \"mini\",\r\n\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\ttotal: 1,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tsize: 20,\r\n\t\t\t\t\t\tsearch: {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tloading: true,\r\n\t\t\t\t\t\turl: \"/dingdan/\",\r\n\t\t\t\t\t\ttitle: \"签约用户\",\r\n\t\t\t\t\t\tinfo: {},\r\n\t\t\t\t\t\tcurrentId:0,\r\n\t\t\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\t\t\tshow_image: \"\",\r\n\t\t\t\t\t\tdialogVisible: false,\r\n\t\t\t\t\t\tdialogViewUserDetail:false,\r\n\t\t\t\t\t\tis_info: false,\r\n\t\t\t\t\t\tupload_index: \"\",\r\n\t\t\t\t\t\tdialogStatus: false,\r\n\t\t\t\t\t\tdialogEndTime: false,\r\n\t\t\t\t\t\truleForm: {\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tstatus_msg: \"\",\r\n\t\t\t\t\t\t\t\tend_time: \"\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\trules: {\r\n\t\t\t\t\t\t\t\tstatus_msg: [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\t\t\tshowAdvanced: false,\r\n\t\t\t\t\t\tselectedRows: [],\r\n\t\t\t\t\t\tshowRevenueDialog: false,\r\n\t\t\t\t\t\tshowExportDialog: false,\r\n\t\t\t\t\t\texportForm: {\r\n\t\t\t\t\t\t\t\tformat: \"excel\",\r\n\t\t\t\t\t\t\t\tfields: [\"client\", \"package\", \"payment\", \"status\", \"time\", \"member\"],\r\n\t\t\t\t\t\t\t\trange: \"all\",\r\n\t\t\t\t\t\t\t\tdateRange: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\texportLoading: false,\r\n\t\t\t\t\t\tfullPaymentCount: 0,\r\n\t\t\t\t\t\tinstallmentPaymentCount: 0,\r\n\t\t\t\t\t\tshowNotifications: true\r\n\t\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t\t// 统计数据计算\r\n\t\t\t\tpendingOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tapprovedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 2).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\trejectedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 3).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\ttotalRevenue() {\r\n\t\t\t\t\t\treturn this.list.reduce((sum, item) => sum + (item.pay_age || 0), 0).toLocaleString();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tfullPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tinstallmentPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type !== 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t// 即将到期的订单（7天内）\r\n\t\t\t\texpiringOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\tconst sevenDaysLater = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate >= today && endDate <= sevenDaysLater;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 已过期的订单\r\n\t\t\t\texpiredOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate < today;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 高价值订单（金额超过5000）\r\n\t\t\t\thighValueOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => (item.pay_age || 0) > 5000);\r\n\t\t\t\t},\r\n\t\t\t\t// 是否有重要通知\r\n\t\t\t\thasImportantNotifications() {\r\n\t\t\t\t\t\treturn this.showNotifications && (\r\n\t\t\t\t\t\t\t\tthis.pendingOrders > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiringOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiredOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.highValueOrders.length > 0\r\n\t\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\teditData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tviewUserData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t\t},\r\n\t\t\t\tgetInfo(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tdelData(index, id) {\r\n\t\t\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\r\n\t\t\t\tupdateEndTIme() {\r\n\t\t\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\trefulsh() {\r\n\t\t\t\t\t\tthis.$router.go(0);\r\n\t\t\t\t},\r\n\t\t\t\tgetData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.loading = true;\r\n\t\t\t\t\t\t_this\r\n\t\t\t\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tsaveData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\thandleSizeChange(val) {\r\n\t\t\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleCurrentChange(val) {\r\n\t\t\t\t\t\tthis.page = val;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleSuccess(res) {\r\n\t\t\t\t\t\tlet _this = this\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t},\r\n\t\t\t\tbeforeUpload(file) {\r\n\t\t\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdelImage(file, fileName) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tshowImage(file) {\r\n\t\t\t\t\t\tthis.show_image = file;\r\n\t\t\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tchangePinzhen(index) {\r\n\t\t\t\t\t\tthis.index = index\r\n\t\t\t\t},\r\n\t\t\t\tshowStatus(row) {\r\n\t\t\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tshowEndTime(row) {\r\n\t\t\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tchangeEndTime() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusType(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: 'warning',\r\n\t\t\t\t\t\t\t\t2: 'success', \r\n\t\t\t\t\t\t\t\t3: 'danger'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || 'info';\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusText(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: '未审核',\r\n\t\t\t\t\t\t\t\t2: '已通过',\r\n\t\t\t\t\t\t\t\t3: '未通过'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || '未知';\r\n\t\t\t\t},\r\n\t\t\t\tgetDebtStatusType(status) {\r\n\t\t\t\t\t\t// 债务状态类型映射\r\n\t\t\t\t\t\tif (status === '已解决') return 'success';\r\n\t\t\t\t\t\tif (status === '处理中') return 'warning';\r\n\t\t\t\t\t\treturn 'info';\r\n\t\t\t\t},\r\n\t\t\t\tformatDate(dateStr) {\r\n\t\t\t\t\t\tif (!dateStr) return '未设置';\r\n\t\t\t\t\t\treturn new Date(dateStr).toLocaleDateString('zh-CN');\r\n\t\t\t\t},\r\n\t\t\t\ttableRowClassName({row, rowIndex}) {\r\n\t\t\t\t\t\tif (row.status === 1) return 'warning-row';\r\n\t\t\t\t\t\tif (row.status === 3) return 'danger-row';\r\n\t\t\t\t\t\treturn '';\r\n\t\t\t\t},\r\n\t\t\t\tresetSearch() {\r\n\t\t\t\t\t\tthis.search = {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\texportData() {\r\n\t\t\t\t\tthis.showExportDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\tdownloadOrder() {\r\n\t\t\t\t\t\tthis.$message.success('订单下载功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\ttoggleAdvanced() {\r\n\t\t\t\t\t\tthis.showAdvanced = !this.showAdvanced;\r\n\t\t\t\t},\r\n\t\t\t\tapplyAdvancedSearch() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tclearAdvancedSearch() {\r\n\t\t\t\t\t\tthis.resetSearch();\r\n\t\t\t\t},\r\n\t\t\t\trefreshData() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tbatchAudit() {\r\n\t\t\t\t\t\tthis.$message.success('批量审核功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\thandleSelectionChange(selection) {\r\n\t\t\t\t\t\tthis.selectedRows = selection;\r\n\t\t\t\t},\r\n\t\t\t\tfilterByStatus(status) {\r\n\t\t\t\t\tthis.search.status = status;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t\tthis.$message.success(`已筛选${this.getStatusText(status) || '全部'}订单`);\r\n\t\t\t\t},\r\n\t\t\t\tshowRevenueChart() {\r\n\t\t\t\t\tthis.showRevenueDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\texecuteExport() {\r\n\t\t\t\t\tthis.exportLoading = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟导出过程\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst formatText = {\r\n\t\t\t\t\t\t\t'excel': 'Excel',\r\n\t\t\t\t\t\t\t'csv': 'CSV', \r\n\t\t\t\t\t\t\t'pdf': 'PDF'\r\n\t\t\t\t\t\t}[this.exportForm.format];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst rangeText = {\r\n\t\t\t\t\t\t\t'all': '全部数据',\r\n\t\t\t\t\t\t\t'current': '当前页面',\r\n\t\t\t\t\t\t\t'selected': '选中项目',\r\n\t\t\t\t\t\t\t'filtered': '筛选结果'\r\n\t\t\t\t\t\t}[this.exportForm.range];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 这里可以集成真实的导出库，如 xlsx、jsPDF等\r\n\t\t\t\t\t\tconst blob = this.generateExportData();\r\n\t\t\t\t\t\tconst url = URL.createObjectURL(blob);\r\n\t\t\t\t\t\tconst link = document.createElement('a');\r\n\t\t\t\t\t\tlink.href = url;\r\n\t\t\t\t\t\tlink.download = `订单数据_${new Date().toISOString().split('T')[0]}.${this.exportForm.format}`;\r\n\t\t\t\t\t\tlink.click();\r\n\t\t\t\t\t\tURL.revokeObjectURL(url);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.exportLoading = false;\r\n\t\t\t\t\t\tthis.showExportDialog = false;\r\n\t\t\t\t\t\tthis.$message.success(`${formatText}格式的${rangeText}导出成功！`);\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t},\r\n\t\t\t\tgenerateExportData() {\r\n\t\t\t\t\t// 根据选择的范围获取数据\r\n\t\t\t\t\tlet exportList = [];\r\n\t\t\t\t\tswitch(this.exportForm.range) {\r\n\t\t\t\t\t\tcase 'all':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'current':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'selected':\r\n\t\t\t\t\t\t\texportList = this.selectedRows;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'filtered':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据选择的字段生成数据\r\n\t\t\t\t\tconst data = exportList.map(item => {\r\n\t\t\t\t\t\tconst row = {};\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('client')) {\r\n\t\t\t\t\t\t\trow['公司名称'] = item.client?.company || '';\r\n\t\t\t\t\t\t\trow['联系人'] = item.client?.linkman || '';\r\n\t\t\t\t\t\t\trow['联系电话'] = item.client?.phone || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('package')) {\r\n\t\t\t\t\t\t\trow['套餐名称'] = item.taocan?.title || '';\r\n\t\t\t\t\t\t\trow['套餐价格'] = item.taocan?.price || 0;\r\n\t\t\t\t\t\t\trow['服务年限'] = item.taocan?.year || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('payment')) {\r\n\t\t\t\t\t\t\trow['支付方式'] = item.pay_type == 1 ? '全款' : '分期';\r\n\t\t\t\t\t\t\trow['已付金额'] = item.pay_age || 0;\r\n\t\t\t\t\t\t\trow['总金额'] = item.total_price || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('status')) {\r\n\t\t\t\t\t\t\trow['审核状态'] = this.getStatusText(item.status);\r\n\t\t\t\t\t\t\trow['状态说明'] = item.status_msg || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('time')) {\r\n\t\t\t\t\t\t\trow['创建时间'] = item.create_time || '';\r\n\t\t\t\t\t\t\trow['到期时间'] = item.end_time || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('member')) {\r\n\t\t\t\t\t\t\trow['业务员'] = item.member?.title || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn row;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 生成CSV格式的Blob\r\n\t\t\t\t\tconst csvContent = this.convertToCSV(data);\r\n\t\t\t\t\treturn new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\t\t\t\t},\r\n\t\t\t\tconvertToCSV(data) {\r\n\t\t\t\t\tif (!data || data.length === 0) return '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst headers = Object.keys(data[0]);\r\n\t\t\t\t\tconst csvRows = [headers.join(',')];\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor (const row of data) {\r\n\t\t\t\t\t\tconst values = headers.map(header => {\r\n\t\t\t\t\t\t\tconst value = row[header];\r\n\t\t\t\t\t\t\treturn typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tcsvRows.push(values.join(','));\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn csvRows.join('\\n');\r\n\t\t\t\t},\r\n\t\t\t\tbatchApprove() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量通过的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$confirm(`确认批量通过选中的 ${pendingRows.length} 个待审核订单吗？`, '批量审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t\t'status_msg': '批量审核通过'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量审核完成，成功通过 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量审核过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量审核');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tbatchReject() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量拒绝的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$prompt('请输入批量拒绝理由', `批量拒绝 ${pendingRows.length} 个订单`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量拒绝完成，成功拒绝 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量拒绝过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量拒绝');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickApprove(row) {\r\n\t\t\t\t\tthis.$confirm(`确认通过 ${row.client?.company || '该客户'} 的订单吗？`, '快速审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\t// 调用API通过订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t'status_msg': '快速审核通过'\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('审核通过成功');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickReject(row) {\r\n\t\t\t\t\tthis.$prompt('请输入拒绝理由', `拒绝订单 - ${row.client?.company || '客户'}`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\t// 调用API拒绝订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('订单已拒绝');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDays(end_time) {\r\n\t\t\t\t\tif (!end_time) return '未设置';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return `已过期${Math.abs(remainingDays)}天`;\r\n\t\t\t\t\tif (remainingDays === 0) return '今天到期';\r\n\t\t\t\t\tif (remainingDays <= 7) return `${remainingDays}天后到期`;\r\n\t\t\t\t\tif (remainingDays <= 30) return `${remainingDays}天后到期`;\r\n\t\t\t\t\treturn `${remainingDays}天后到期`;\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDaysClass(end_time) {\r\n\t\t\t\t\tif (!end_time) return '';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return 'expired';\r\n\t\t\t\t\tif (remainingDays <= 3) return 'urgent';\r\n\t\t\t\t\tif (remainingDays <= 7) return 'warning';\r\n\t\t\t\t\treturn 'normal';\r\n\t\t\t\t},\r\n\t\t\t\t// 通知系统相关方法\r\n\t\t\t\tdismissNotifications() {\r\n\t\t\t\t\tthis.showNotifications = false;\r\n\t\t\t\t\tthis.$message.success('已暂时隐藏提醒通知');\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiringOrders() {\r\n\t\t\t\t\t// 显示即将到期的订单\r\n\t\t\t\t\tthis.$message.info(`查看${this.expiringOrders.length}个即将到期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiredOrders() {\r\n\t\t\t\t\t// 显示已过期的订单\r\n\t\t\t\t\tthis.$message.warning(`查看${this.expiredOrders.length}个已过期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowHighValueOrders() {\r\n\t\t\t\t\t// 显示高价值订单\r\n\t\t\t\t\tthis.$message.success(`查看${this.highValueOrders.length}个高价值订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n.order-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card:active {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.pending-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.revenue-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n.stat-change.warning {\r\n  color: #f39c12;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker,\r\n.pay-select,\r\n.package-select,\r\n.sort-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner,\r\n.pay-select .el-input__inner,\r\n.package-select .el-input__inner,\r\n.sort-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus,\r\n.pay-select .el-input__inner:focus,\r\n.package-select .el-input__inner:focus,\r\n.sort-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.amount-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.amount-range .el-input-number {\r\n  flex: 1;\r\n}\r\n\r\n.range-separator {\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n  padding: 0 4px;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 选择信息样式 */\r\n.selected-info {\r\n  font-size: 13px;\r\n  color: #667eea;\r\n  margin-top: 4px;\r\n  font-weight: 500;\r\n  background: rgba(102, 126, 234, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 表格行样式 */\r\n.modern-table .warning-row {\r\n  background-color: #fff7e6 !important;\r\n}\r\n\r\n.modern-table .danger-row {\r\n  background-color: #fff2f0 !important;\r\n}\r\n\r\n/* 客户信息样式 */\r\n.client-info {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.client-info:hover {\r\n  background-color: #f8f9ff;\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n  margin: -8px;\r\n}\r\n\r\n.client-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.client-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.client-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.company-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.contact-info, .contact-phone {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 2px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 套餐信息样式 */\r\n.package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.package-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.package-price {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.price-label {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.package-duration {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 支付信息样式 */\r\n.payment-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-type {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.payment-amount, .remaining-amount {\r\n  font-size: 13px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.paid {\r\n  color: #27ae60;\r\n  font-weight: 500;\r\n}\r\n\r\n.remaining {\r\n  color: #e74c3c;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-progress {\r\n  margin-top: 8px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__outer {\r\n  background-color: #f0f0f0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__inner {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 业务员信息样式 */\r\n.member-info {\r\n  text-align: center;\r\n}\r\n\r\n.member-info .el-tag {\r\n  font-size: 12px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 状态信息样式 */\r\n.status-info {\r\n  cursor: pointer;\r\n  text-align: center;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-reason {\r\n  font-size: 12px;\r\n  color: #e74c3c;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n}\r\n\r\n.create-time, .end-time {\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.remaining-days {\r\n  margin-top: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  text-align: center;\r\n}\r\n\r\n.remaining-days.normal {\r\n  background-color: #f0f9ff;\r\n  color: #1e40af;\r\n  border: 1px solid #dbeafe;\r\n}\r\n\r\n.remaining-days.warning {\r\n  background-color: #fef3c7;\r\n  color: #b45309;\r\n  border: 1px solid #fde68a;\r\n}\r\n\r\n.remaining-days.urgent {\r\n  background-color: #fee2e2;\r\n  color: #dc2626;\r\n  border: 1px solid #fecaca;\r\n}\r\n\r\n.remaining-days.expired {\r\n  background-color: #f3f4f6;\r\n  color: #6b7280;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.view-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.approve-btn {\r\n  color: #67C23A !important;\r\n}\r\n\r\n.reject-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.view-btn:hover, .approve-btn:hover, .reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n.approve-btn:hover {\r\n  background-color: rgba(103, 194, 58, 0.1) !important;\r\n}\r\n\r\n.reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(245, 108, 108, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-detail-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.order-detail-content {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border: none;\r\n}\r\n\r\n.detail-header {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.clickable-tag {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-tag:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-highlight {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.money-amount {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.debt-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .order-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-input-number {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .amount-range {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .client-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .selected-info {\r\n    margin-top: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n  \r\n  .remaining-days {\r\n    font-size: 11px;\r\n    padding: 2px 6px;\r\n  }\r\n  \r\n  .payment-progress {\r\n    margin-top: 6px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .action-buttons .el-button {\r\n    font-size: 12px;\r\n    padding: 4px 8px;\r\n  }\r\n  \r\n  .table-actions .el-button {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 收入统计图表样式 */\r\n.revenue-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.revenue-stats {\r\n  min-height: 400px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #f0f0f0;\r\n  height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-card h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 12px;\r\n  color: #667eea;\r\n}\r\n\r\n.chart-placeholder p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.mock-chart-data {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 8px;\r\n  height: 100px;\r\n  width: 200px;\r\n}\r\n\r\n.chart-bar {\r\n  flex: 1;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 4px 4px 0 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chart-bar:hover {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.payment-stats {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.payment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.payment-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.full-payment {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.installment-payment {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.status-overview {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.status-circle {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.status-circle:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.pending-circle {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-circle {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.rejected-circle {\r\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\r\n}\r\n\r\n.total-circle {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.status-item span {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据导出对话框样式 */\r\n.export-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.export-dialog .el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.export-dialog .el-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox-group {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox {\r\n  margin: 0;\r\n}\r\n\r\n.export-dialog .el-form-item__label {\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-dialog .el-radio {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.export-dialog .dialog-footer {\r\n  padding: 16px 0 0 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button {\r\n  margin-left: 12px;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 响应式图表样式 */\r\n@media (max-width: 768px) {\r\n  .revenue-stats .el-col {\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .chart-card {\r\n    height: auto;\r\n    min-height: 250px;\r\n  }\r\n  \r\n  .status-overview {\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .status-circle {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .export-dialog .el-checkbox-group {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 通知系统样式 */\r\n.notifications-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 12px;\r\n  border: 1px solid #ffd93d;\r\n  background: linear-gradient(135deg, #fff9c4 0%, #ffffff 100%);\r\n  box-shadow: 0 4px 20px rgba(255, 217, 61, 0.3);\r\n}\r\n\r\n.notification-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px 20px 12px 20px;\r\n  border-bottom: 1px solid rgba(255, 217, 61, 0.2);\r\n}\r\n\r\n.notification-icon {\r\n  font-size: 20px;\r\n  color: #f39c12;\r\n}\r\n\r\n.notification-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.dismiss-btn {\r\n  color: #7f8c8d !important;\r\n  padding: 4px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.dismiss-btn:hover {\r\n  color: #e74c3c !important;\r\n  background: rgba(231, 76, 60, 0.1) !important;\r\n}\r\n\r\n.notification-content {\r\n  padding: 16px 20px 20px 20px;\r\n}\r\n\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.notification-item:hover {\r\n  transform: translateX(4px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.notification-item.urgent {\r\n  background: linear-gradient(135deg, #fee2e2 0%, #fef7f7 100%);\r\n  border-color: rgba(239, 68, 68, 0.2);\r\n}\r\n\r\n.notification-item.urgent:hover {\r\n  background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%);\r\n}\r\n\r\n.notification-item.warning {\r\n  background: linear-gradient(135deg, #fef3c7 0%, #fefdf8 100%);\r\n  border-color: rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.notification-item.warning:hover {\r\n  background: linear-gradient(135deg, #fde68a 0%, #fef3c7 100%);\r\n}\r\n\r\n.notification-item.error {\r\n  background: linear-gradient(135deg, #ffebee 0%, #fafafa 100%);\r\n  border-color: rgba(244, 67, 54, 0.2);\r\n}\r\n\r\n.notification-item.error:hover {\r\n  background: linear-gradient(135deg, #ffcdd2 0%, #ffebee 100%);\r\n}\r\n\r\n.notification-item.info {\r\n  background: linear-gradient(135deg, #e3f2fd 0%, #fafafa 100%);\r\n  border-color: rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.notification-item.info:hover {\r\n  background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 100%);\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.notification-item.urgent .notification-dot {\r\n  background: #ef4444;\r\n}\r\n\r\n.notification-item.warning .notification-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.notification-item.error .notification-dot {\r\n  background: #f44336;\r\n}\r\n\r\n.notification-item.info .notification-dot {\r\n  background: #2196f3;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    opacity: 0.7;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.notification-text {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  line-height: 1.5;\r\n}\r\n\r\n.notification-text strong {\r\n  color: #e74c3c;\r\n  font-weight: 600;\r\n}\r\n\r\n.notification-action {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.notification-action .el-button {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.notification-action .el-button:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.export-dialog .el-checkbox-group {\r\n  grid-template-columns: 1fr;\r\n}\r\n\r\n.notifications-section {\r\n  margin: 0 -8px 16px -8px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 8px;\r\n  margin: 0 8px;\r\n}\r\n\r\n.notification-header {\r\n  padding: 12px 16px 8px 16px;\r\n}\r\n\r\n.notification-content {\r\n  padding: 12px 16px 16px 16px;\r\n}\r\n\r\n.notification-item {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.notification-item .notification-dot {\r\n  order: -1;\r\n  align-self: flex-start;\r\n}\r\n\r\n.notification-text {\r\n  order: 0;\r\n  width: 100%;\r\n}\r\n\r\n.notification-action {\r\n  order: 1;\r\n  align-self: flex-end;\r\n}\r\n</style>\r\n"]}]}