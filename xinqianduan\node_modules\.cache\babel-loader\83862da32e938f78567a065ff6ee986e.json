{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748606652788}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_this", "emojiData", "audioplay", "name", "components", "data", "userss", "lvshiss", "yuangongss", "table", "gridData", "ruleForm", "dialogFormVisible", "currentTab", "selectId", "activeName", "search", "active", "imgUlr", "yon_id", "id", "isShowSeach", "type", "lists", "la", "Names", "isShowPopup", "textContent", "lv<PERSON><PERSON>", "pic_path", "file_path", "list", "timer", "users", "quns", "quliaoIndex", "quli<PERSON><PERSON>", "isEmji", "title", "yuanshiquns", "yuanshiusers", "methods", "editData", "getInfo", "desc", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "quanyuan", "console", "log", "loadTestMembers", "closeMemberPanel", "handleChatMainClick", "saveData", "$refs", "validate", "valid", "postRequest", "getData", "<PERSON><PERSON><PERSON>", "uid", "showDaiban", "is_daiban", "getList", "changeKeyword", "e", "target", "value", "length", "filter", "toLowerCase", "includes", "daiban", "openEmji", "changeFile", "field", "openFile", "window", "open", "openImg", "img", "beforeUpload", "file", "split", "showClose", "handleSuccess", "sendImg", "handleSuccess1", "flie", "sendFile", "redSession", "index", "loadTestMessages", "changeQun", "count", "<PERSON><PERSON><PERSON><PERSON>", "item", "change", "del", "handleScroll", "scrollTop", "send", "sendMessage", "scrollHeight", "loading", "lvshi_id", "yuangong_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "content", "orther_id", "direction", "files", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "delImage", "fileName", "loadTestData", "create_time", "time", "loadPrivateMessages", "qun", "loadGroupMessages", "$nextTick", "groupMessages", "avatar", "privateMessages", "nickname", "headimg", "zhiwei", "type_title", "is_deal_title", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted"], "sources": ["src/views/pages/yonghu/chat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\" @click=\"handleChatMainClick\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button \r\n                type=\"text\" \r\n                icon=\"el-icon-more\" \r\n                @click=\"quanyuan\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 群成员侧边栏 -->\r\n        <div class=\"member-sidebar\" :class=\"{ 'show': la }\" @click.stop>\r\n          <div class=\"member-header\">\r\n            <h3>群成员</h3>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"closeMemberPanel\"\r\n              class=\"close-btn\"\r\n            ></el-button>\r\n          </div>\r\n          <div class=\"member-content\">\r\n            <!-- 用户列表 -->\r\n            <div class=\"member-section\" v-if=\"userss.length\">\r\n              <div class=\"section-title\">用户</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-item\" v-for=\"(item, index) in userss\" :key=\"'user-' + index\">\r\n                  <div v-for=\"(value, key) in item.list\" :key=\"'user-item-' + key\" class=\"member-card\">\r\n                    <img :src=\"value.headimg\" class=\"member-avatar\"/>\r\n                    <div class=\"member-name\">{{ value.nickname }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 员工列表 -->\r\n            <div class=\"member-section\" v-for=\"(item, index) in yuangongss\" :key=\"'staff-' + index\">\r\n              <div class=\"section-title\">{{ item.zhiwei }}</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-card\" v-for=\"(value, key) in item.list\" :key=\"'staff-item-' + key\">\r\n                  <img :src=\"value.pic_path\" class=\"member-avatar\"/>\r\n                  <div class=\"member-name\">{{ value.title }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n      la: false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    quanyuan() {\r\n      console.log('点击群成员按钮');\r\n      _this.la = !_this.la;\r\n      // 使用测试数据，不调用API\r\n      if (_this.la) {\r\n        _this.loadTestMembers();\r\n      }\r\n    },\r\n    // 关闭群成员面板\r\n    closeMemberPanel() {\r\n      _this.la = false;\r\n    },\r\n    // 点击聊天主区域时关闭群成员面板\r\n    handleChatMainClick() {\r\n      if (_this.la) {\r\n        _this.la = false;\r\n      }\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      _this.isEmji = !_this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      _this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      _this.imgUlr = img;\r\n      _this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      console.log('点击私聊:', index);\r\n      _this.selectId = index;\r\n      _this.quliaoIndex = -1;\r\n      _this.la = false;\r\n      _this.loadTestMessages();\r\n    },\r\n    changeQun(index) {\r\n      console.log('点击群聊:', index);\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.la = false;\r\n      _this.loadTestMessages();\r\n    },\r\n    getEmoji(item) {\r\n      _this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (_this.search) _this.isShowSeach = true;\r\n      else _this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      _this.search = \"\";\r\n      _this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (_this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 群聊测试数据\r\n      _this.quns = [\r\n        {\r\n          id: 1,\r\n          title: \"法务团队群\",\r\n          desc: \"最新消息：合同审核已完成\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          create_time: \"09:30\",\r\n          count: 3,\r\n          uid: \"1,2,3\",\r\n          lvshi_id: \"1,2\",\r\n          yuangong_id: \"1,2,3\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"客户服务群\",\r\n          desc: \"张三：请问合同什么时候能完成？\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          create_time: \"昨天\",\r\n          count: 1,\r\n          uid: \"4,5\",\r\n          lvshi_id: \"3\",\r\n          yuangong_id: \"4,5\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"紧急处理群\",\r\n          desc: \"李四：这个案件需要加急处理\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          create_time: \"15:20\",\r\n          count: 5,\r\n          uid: \"1,3,5\",\r\n          lvshi_id: \"1\",\r\n          yuangong_id: \"1,3\",\r\n          is_daiban: 1\r\n        }\r\n      ];\r\n\r\n      // 私聊用户测试数据\r\n      _this.users = [\r\n        {\r\n          id: 1,\r\n          title: \"张三（客户）\",\r\n          content: \"您好，请问我的合同审核进度如何？\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          time: \"10:30\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"李四（律师）\",\r\n          content: \"合同已经审核完毕，请查收\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          time: \"09:15\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"王五（调解员）\",\r\n          content: \"调解会议安排在明天下午2点\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          time: \"昨天\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"赵六（客户）\",\r\n          content: \"谢谢您的帮助！\",\r\n          pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n          time: \"16:45\"\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据用于搜索\r\n      _this.yuanshiquns = [..._this.quns];\r\n      _this.yuanshiusers = [..._this.users];\r\n\r\n      // 设置默认选中第一个群聊\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = 0;\r\n\r\n      // 加载第一个群聊的消息\r\n      setTimeout(() => {\r\n        _this.loadTestMessages();\r\n      }, 500);\r\n    },\r\n    // 加载测试消息数据\r\n    loadTestMessages() {\r\n      console.log('加载测试消息, selectId:', _this.selectId, 'quliaoIndex:', _this.quliaoIndex);\r\n      // 设置当前聊天标题\r\n      if (_this.selectId !== -1) {\r\n        _this.title = _this.users[_this.selectId].title;\r\n        // 加载私聊消息\r\n        _this.loadPrivateMessages();\r\n      } else {\r\n        const qun = _this.quns[_this.quliaoIndex];\r\n        const count = qun.uid.split(',').length + qun.lvshi_id.split(',').length + qun.yuangong_id.split(',').length;\r\n        _this.title = qun.title + \"(\" + count + \")\";\r\n        // 加载群聊消息\r\n        _this.loadGroupMessages();\r\n      }\r\n\r\n      // 滚动到底部\r\n      _this.$nextTick(() => {\r\n        if (_this.$refs.list) {\r\n          _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n        }\r\n      });\r\n    },\r\n    // 加载群聊消息\r\n    loadGroupMessages() {\r\n      const groupMessages = {\r\n        0: [ // 法务团队群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:00:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"大家好，今天我们讨论一下最新的合同审核流程\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:05:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张专员\",\r\n            type: \"text\",\r\n            content: \"好的，我这边已经准备好相关材料了\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 09:10:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"请大家查看一下这份合同模板\"\r\n          },\r\n          {\r\n            id: 4,\r\n            create_time: \"2024-01-22 09:12:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"image\",\r\n            content: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          },\r\n          {\r\n            id: 5,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"这个模板看起来不错，我们可以在此基础上进行修改\"\r\n          }\r\n        ],\r\n        1: [ // 客户服务群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:00:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"请问合同什么时候能完成？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，合同预计明天下午可以完成审核\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:10:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢！\"\r\n          }\r\n        ],\r\n        2: [ // 紧急处理群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 15:00:00\",\r\n            yuangong_id: 5,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李四\",\r\n            type: \"text\",\r\n            content: \"这个案件需要加急处理\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 15:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，我立即安排处理\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 15:10:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"我这边也会配合加急处理\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = groupMessages[_this.quliaoIndex] || [];\r\n      console.log('群聊消息加载完成:', _this.list.length);\r\n    },\r\n    // 加载私聊消息\r\n    loadPrivateMessages() {\r\n      const privateMessages = {\r\n        0: [ // 张三（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:30:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"您好，请问我的合同审核进度如何？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:35:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，您的合同正在审核中，预计今天下午可以完成\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:40:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢您！\"\r\n          }\r\n        ],\r\n        1: [ // 李四（律师）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"合同已经审核完毕，请查收\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:20:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，辛苦了！\"\r\n          }\r\n        ],\r\n        2: [ // 王五（调解员）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-21 16:00:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"王调解员\",\r\n            type: \"text\",\r\n            content: \"调解会议安排在明天下午2点\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-21 16:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"好的，我会准时参加\"\r\n          }\r\n        ],\r\n        3: [ // 赵六（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 16:45:00\",\r\n            yuangong_id: 6,\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n            title: \"赵六\",\r\n            type: \"text\",\r\n            content: \"谢谢您的帮助！\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 16:50:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"不客气，有问题随时联系我\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = privateMessages[_this.selectId] || [];\r\n      console.log('私聊消息加载完成:', _this.list.length);\r\n    },\r\n\r\n    // 加载测试群成员数据\r\n    loadTestMembers() {\r\n      // 用户列表\r\n      _this.userss = [\r\n        {\r\n          list: [\r\n            {\r\n              id: 1,\r\n              nickname: \"张三\",\r\n              headimg: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n            },\r\n            {\r\n              id: 2,\r\n              nickname: \"李四\",\r\n              headimg: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            },\r\n            {\r\n              id: 3,\r\n              nickname: \"王五\",\r\n              headimg: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 员工列表\r\n      _this.yuangongss = [\r\n        {\r\n          zhiwei: \"律师\",\r\n          list: [\r\n            {\r\n              id: 1,\r\n              title: \"李律师\",\r\n              pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            },\r\n            {\r\n              id: 2,\r\n              title: \"陈律师\",\r\n              pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          zhiwei: \"调解员\",\r\n          list: [\r\n            {\r\n              id: 3,\r\n              title: \"王调解员\",\r\n              pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          zhiwei: \"法务专员\",\r\n          list: [\r\n            {\r\n              id: 4,\r\n              title: \"张专员\",\r\n              pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n            },\r\n            {\r\n              id: 5,\r\n              title: \"赵专员\",\r\n              pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 工单测试数据\r\n      _this.gridData = [\r\n        {\r\n          id: 1,\r\n          create_time: \"2024-01-20\",\r\n          title: \"合同审核\",\r\n          desc: \"需要审核一份购销合同\",\r\n          type_title: \"法律咨询\",\r\n          is_deal_title: \"处理中\"\r\n        },\r\n        {\r\n          id: 2,\r\n          create_time: \"2024-01-21\",\r\n          title: \"债务追讨\",\r\n          desc: \"客户需要追讨欠款\",\r\n          type_title: \"债务处理\",\r\n          is_deal_title: \"已完成\"\r\n        },\r\n        {\r\n          id: 3,\r\n          create_time: \"2024-01-22\",\r\n          title: \"律师函起草\",\r\n          desc: \"起草催款律师函\",\r\n          type_title: \"文书制作\",\r\n          is_deal_title: \"待处理\"\r\n        }\r\n      ];\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    // 使用测试数据替代API调用\r\n    _this.loadTestData();\r\n    _this.yon_id = 1; // 设置当前用户ID\r\n    // 注释掉定时器，避免不必要的API调用\r\n    // _this.timer = setInterval(() => {\r\n    //   _this.getMoreList();\r\n    // }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 群成员侧边栏 */\r\n.member-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -300px;\r\n  width: 300px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.member-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n</style>\r\n"], "mappings": ";AAkbA,IAAAA,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,iBAAA;MACAX,SAAA,EAAAA,SAAA;MACAY,UAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,EAAA;MACAC,WAAA;MACAC,IAAA;MACAC,KAAA;MACAC,EAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAb,QAAA;MACAc,OAAA;MACAC,QAAA;MACAC,SAAA;MACAC,IAAA;MACAC,KAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAAtB,EAAA;MACA,IAAApB,KAAA;MACA,IAAAoB,EAAA;QACA,KAAAuB,OAAA,CAAAvB,EAAA;MACA;QACA,KAAAT,QAAA;UACA2B,KAAA;UACAM,IAAA;QACA;MACA;IACA;IACAC,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAAtC,QAAA,gBAAAmC,GAAA,CAAAzC,IAAA,CAAA6C,GAAA;MACA;QACA,KAAAF,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACAT,QAAAvB,EAAA;MACA,IAAApB,KAAA;MACA,KAAAqD,UAAA,uBAAAjC,EAAA,EAAAkC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAW,QAAA,GAAA4C,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAY,iBAAA;QACA;UACAZ,KAAA,CAAAgD,QAAA;YACA1B,IAAA;YACAkC,OAAA,EAAAD,IAAA,CAAAH;UACA;QACA;MACA;IACA;IACAK,SAAA;MACAC,OAAA,CAAAC,GAAA;MACA3D,KAAA,CAAAwB,EAAA,IAAAxB,KAAA,CAAAwB,EAAA;MACA;MACA,IAAAxB,KAAA,CAAAwB,EAAA;QACAxB,KAAA,CAAA4D,eAAA;MACA;IACA;IACA;IACAC,iBAAA;MACA7D,KAAA,CAAAwB,EAAA;IACA;IACA;IACAsC,oBAAA;MACA,IAAA9D,KAAA,CAAAwB,EAAA;QACAxB,KAAA,CAAAwB,EAAA;MACA;IACA;IACAuC,SAAA;MACA,IAAA/D,KAAA;MACA,KAAAgE,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,uBAAAxD,QAAA,EAAA2C,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAAgD,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;cACA,KAAAgB,OAAA;cACApE,KAAA,CAAAY,iBAAA;YACA;cACAZ,KAAA,CAAAgD,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAiB,YAAA;MACA,IAAAC,GAAA,QAAApC,IAAA,MAAAC,WAAA;MACAnC,KAAA,CAAAS,KAAA;MACAT,KAAA,CAAAmE,WAAA;QAAAG,GAAA,EAAAA;MAAA,GAAAhB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAU,QAAA,GAAA6C,IAAA,CAAAlD,IAAA;QACA;MACA;IACA;IACAkE,WAAAC,SAAA;MACA,KAAA3D,UAAA,GAAA2D,SAAA;MACAxE,KAAA,CACAmE,WAAA;QAAAK,SAAA,EAAAA;MAAA,GACAlB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAd,KAAA,CAAAyE,OAAA;QACA;MACA;IACA;IACAC,cAAAC,CAAA;MACA,IAAAzC,IAAA,GAAAlC,KAAA,CAAAuC,WAAA;MACA,IAAAN,KAAA,GAAAjC,KAAA,CAAAwC,YAAA;MACA,IAAAxB,MAAA,GAAA2D,CAAA,CAAAC,MAAA,CAAAC,KAAA;MAEA,KAAAxD,WAAA,GAAAL,MAAA,CAAA8D,MAAA;MAEA9E,KAAA,CAAAkC,IAAA,GAAAA,IAAA,CAAA6C,MAAA,CAAA1E,IAAA,IAAAA,IAAA,CAAAiC,KAAA,CAAA0C,WAAA,GAAAC,QAAA,CAAAjE,MAAA,CAAAgE,WAAA;MACAhF,KAAA,CAAAiC,KAAA,GAAAA,KAAA,CAAA8C,MAAA,CAAA1E,IAAA,IAAAA,IAAA,CAAAiC,KAAA,CAAA0C,WAAA,GAAAC,QAAA,CAAAjE,MAAA,CAAAgE,WAAA;IACA;IACAE,OAAA;MACA,IAAA9D,EAAA,QAAAc,IAAA,MAAAC,WAAA;MACA,IAAAqC,SAAA,QAAAtC,IAAA,MAAAC,WAAA;MACAnC,KAAA,CACAmE,WAAA;QAAA/C,EAAA,EAAAA,EAAA;QAAAoD,SAAA,EAAAA;MAAA,GACAlB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,MAAAC,WAAA,iBAAAqC,SAAA;UACAxE,KAAA,CAAAgD,QAAA,CAAAC,OAAA,CAAAM,IAAA,CAAAH,GAAA;QACA;UACApD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACA+B,SAAA;MACAnF,KAAA,CAAAqC,MAAA,IAAArC,KAAA,CAAAqC,MAAA;MACAqB,OAAA,CAAAC,GAAA;IACA;IACAyB,WAAAC,KAAA;MACArF,KAAA,CAAAsB,IAAA,GAAA+D,KAAA;IACA;IACAC,SAAApC,GAAA;MACAqC,MAAA,CAAAC,IAAA,CAAAtC,GAAA;IACA;IACAuC,QAAAC,GAAA;MACA1F,KAAA,CAAAkB,MAAA,GAAAwE,GAAA;MACA1F,KAAA,CAAA0B,WAAA;MACAgC,OAAA,CAAAC,GAAA,eAAA+B,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,IAAAtE,IAAA,GAAAsE,IAAA,CAAAtE,IAAA;MACAoC,OAAA,CAAAC,GAAA,CAAArC,IAAA;MACA,IACA,CAAAsE,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,qBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,sBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,qBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,qBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,qBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,sBACA,CAAAD,IAAA,CAAAtE,IAAA,CAAAuE,KAAA,oBACA;QACA7F,KAAA,CAAAgD,QAAA;UACA8C,SAAA;UACAtC,OAAA;UACAlC,IAAA;QACA;QACA;MACA;IACA;IACAyE,cAAAjD,GAAA;MACA,IAAA9C,KAAA;MACA0D,OAAA,CAAAC,GAAA,CAAAb,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA/C,KAAA,CAAAgG,OAAA,CAAAlD,GAAA,CAAAzC,IAAA,CAAA6C,GAAA;MACA;QACAlD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACA6C,eAAAnD,GAAA,EAAAoD,IAAA;MACA,IAAApD,GAAA,CAAAC,IAAA;QACA/C,KAAA,CAAAmG,QAAA,CAAArD,GAAA,CAAAzC,IAAA,CAAA6C,GAAA,EAAAgD,IAAA;MACA;QACAlG,KAAA,CAAAgD,QAAA;UACA8C,SAAA;UACAtC,OAAA;UACAlC,IAAA;QACA;MACA;IACA;IACA8E,WAAAC,KAAA;MACA3C,OAAA,CAAAC,GAAA,UAAA0C,KAAA;MACArG,KAAA,CAAAc,QAAA,GAAAuF,KAAA;MACArG,KAAA,CAAAmC,WAAA;MACAnC,KAAA,CAAAwB,EAAA;MACAxB,KAAA,CAAAsG,gBAAA;IACA;IACAC,UAAAF,KAAA;MACA3C,OAAA,CAAAC,GAAA,UAAA0C,KAAA;MACArG,KAAA,CAAAc,QAAA;MACAd,KAAA,CAAAmC,WAAA,GAAAkE,KAAA;MACArG,KAAA,CAAAkC,IAAA,CAAAmE,KAAA,EAAAG,KAAA;MACAxG,KAAA,CAAAwB,EAAA;MACAxB,KAAA,CAAAsG,gBAAA;IACA;IACAG,SAAAC,IAAA;MACA1G,KAAA,CAAA2B,WAAA,IAAA+E,IAAA;IACA;IACAC,OAAAhC,CAAA;MACA,IAAA3E,KAAA,CAAAgB,MAAA,EAAAhB,KAAA,CAAAqB,WAAA,aACArB,KAAA,CAAAqB,WAAA;IACA;IACAuF,IAAA;MACA5G,KAAA,CAAAgB,MAAA;MACAhB,KAAA,CAAAqB,WAAA;IACA;IACAwF,aAAAlC,CAAA;MACA,IAAA3E,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA;QACApD,OAAA,CAAAC,GAAA;MACA;IACA;IACAoD,KAAA;MACA/G,KAAA,CAAAgH,WAAA,CAAAhH,KAAA,CAAA2B,WAAA;MACA3B,KAAA,CAAA2B,WAAA;IACA;IACA8C,QAAA;MACA,IAAAzE,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAwB,KAAA;QAEAtC,KAAA,CAAAmE,WAAA;UAAAG,GAAA,EAAAlD;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAyE,MAAA;cACA9E,KAAA,CAAA+B,IAAA,GAAAwB,IAAA,CAAAlD,IAAA;cAEAL,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GAAA9G,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAAkF,YAAA;YACA;UACA;UACAjH,KAAA,CAAAkH,OAAA;QACA;MACA;QACA,IAAA9F,EAAA,GAAApB,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACA,IAAAoF,KAAA,GACAxG,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAmC,GAAA,CAAAQ,MAAA,OACA9E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAgF,QAAA,CAAArC,MAAA,OACA9E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAiF,WAAA,CAAAtC,MAAA;QACA9E,KAAA,CAAAoB,EAAA,GAAAA,EAAA;QACAsC,OAAA,CAAAC,GAAA,CAAA3D,KAAA,CAAAoB,EAAA;QAEApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAG,KAAA,SAAAkE,KAAA;QACAxG,KAAA,CAAAmE,WAAA;UAAAkD,MAAA,EAAAjG;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAyE,MAAA;cACA9E,KAAA,CAAA+B,IAAA,GAAAwB,IAAA,CAAAlD,IAAA;cACAL,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GAAA9G,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAAkF,YAAA;YACA;cACAjH,KAAA,CAAA+B,IAAA;YACA;YAEAuF,UAAA,CACA,WAAAtD,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,QAAA9C,KAAA,CAAAjC,IAAA,CAAAkF,YAAA,EACA,CACA;UACA;UACAjH,KAAA,CAAAkH,OAAA;QACA;MACA;IACA;IACAK,YAAA;MACA,IAAAvH,KAAA,CAAAc,QAAA;QACA,IAAAwD,GAAA,GAAAtE,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAwB,KAAA;QAEA,IAAAlB,EAAA;QACA,IAAApB,KAAA,CAAA+B,IAAA,CAAA+C,MAAA;UACA1D,EAAA,GAAApB,KAAA,CAAA+B,IAAA,CAAA/B,KAAA,CAAA+B,IAAA,CAAA+C,MAAA,MAAA1D,EAAA;UACApB,KAAA,CACAmE,WAAA;YAAAG,GAAA,EAAAA,GAAA;YAAAlD,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAwH,OAAA;YACA,IAAAjE,IAAA,CAAAR,IAAA;cACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAyE,MAAA;gBACA9E,KAAA,CAAA+B,IAAA,CAAA0F,IAAA,CAAAlE,IAAA,CAAAlD,IAAA;gBACAiH,UAAA,CACA,MACA,KAAAtD,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GACA,KAAA9C,KAAA,CAAAjC,IAAA,CAAAkF,YAAA,EACA,IACA;cACA;YACA;YACAjH,KAAA,CAAAkH,OAAA;UACA;QACA;MACA;QACA,IAAAG,MAAA,GAAArH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACA,IAAAoF,KAAA,GACAxG,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAgF,QAAA,CAAArC,MAAA,OACA9E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAiF,WAAA,CAAAtC,MAAA,OACA;QAEA9E,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAG,KAAA,SAAAkE,KAAA;QACA,IAAApF,EAAA;QACA,IAAApB,KAAA,CAAA+B,IAAA,CAAA+C,MAAA;UACA1D,EAAA,GAAApB,KAAA,CAAA+B,IAAA,CAAA/B,KAAA,CAAA+B,IAAA,CAAA+C,MAAA,MAAA1D,EAAA;UACApB,KAAA,CACAmE,WAAA;YAAAkD,MAAA,EAAAA,MAAA;YAAAjG,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAwH,OAAA;YACA,IAAAjE,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAA+B,IAAA,CAAA0F,IAAA,CAAAlE,IAAA,CAAAlD,IAAA;cAEAiH,UAAA,CACA,MACAtH,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GACA9G,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAAkF,YAAA,EACA,IACA;YACA;YACAjH,KAAA,CAAAkH,OAAA;UACA;QACA;UACA9F,EAAA;UACApB,KAAA,CACAmE,WAAA;YAAAkD,MAAA,EAAAA,MAAA;YAAAjG,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAwH,OAAA;YACA,IAAAjE,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAA+B,IAAA,CAAA0F,IAAA,CAAAlE,IAAA,CAAAlD,IAAA;cAEAiH,UAAA,CACA,MACAtH,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GACA9G,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAAkF,YAAA,EACA,IACA;YACA;YACAjH,KAAA,CAAAkH,OAAA;UACA;QACA;MACA;IACA;IACAF,YAAAU,OAAA;MACA,IAAA1H,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAuG,SAAA;QACA3H,KAAA,CACAmE,WAAA;UACAG,GAAA,EAAAlD,EAAA;UACAwG,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAkB,GAAA,GAAAtE,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAmC,GAAA;QACA,IAAA+C,MAAA,GAAArH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACAmE,WAAA;UACAyD,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA/D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA+C,SAAAuB,OAAA,EAAAG,KAAA;MACA,IAAA7H,KAAA,CAAAc,QAAA;QACA,IAAA6G,SAAA;QACA3H,KAAA,CACAmE,WAAA;UACAyD,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA,SAAA;UACAE,KAAA,EAAAA;QACA,GACAvE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAiE,MAAA,GAAArH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACAmE,WAAA;UACAyD,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA,MAAA;UACAQ,KAAA,EAAAA;QACA,GACAvE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA4C,QAAA0B,OAAA;MACA,IAAA1H,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAuG,SAAA;QACA3H,KAAA,CACAmE,WAAA;UACAG,GAAA,EAAAlD,EAAA;UACAwG,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAkB,GAAA,GAAAtE,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAmC,GAAA;QACA,IAAA+C,MAAA,GAAArH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACAmE,WAAA;UACAG,GAAA,EAAAA,GAAA;UACAsD,SAAA;UACAtG,IAAA;UACAoG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA/D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA0E,YAAA;MACA9H,KAAA,CAAAmE,WAAA,sBAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAiC,KAAA,GAAAsB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAwC,YAAA,GAAAe,IAAA,CAAAlD,IAAA;QACA;MACA;IACA;IACA0H,OAAA;MACA/H,KAAA,CAAAmE,WAAA,iBAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAwG,UAAA;YACAtH,KAAA,CAAAyE,OAAA;UACA;QACA;MACA;IACA;IACA+C,QAAA;MACAxH,KAAA,CAAAmE,WAAA,iBAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;QACA;MACA;IACA;IACAkH,YAAA;MACA,IAAAhI,KAAA;MAEAiI,QAAA,CAAAC,SAAA,GAAAvD,CAAA;QACA,IAAAwD,IAAA,GAAA5C,MAAA,CAAA6C,KAAA,CAAAC,OAAA;QAEA,IAAAF,IAAA;UACAnI,KAAA,CAAA+G,IAAA;QACA;MACA;IACA;IACAuB,SAAA1C,IAAA,EAAA2C,QAAA;MACA,IAAAvI,KAAA;MACAA,KAAA,CAAAqD,UAAA,gCAAAuC,IAAA,EAAAtC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAW,QAAA,CAAA4H,QAAA;UAEAvI,KAAA,CAAAgD,QAAA,CAAAC,OAAA;QACA;UACAjD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACA;IACAoF,aAAA;MACA;MACAxI,KAAA,CAAAkC,IAAA,IACA;QACAd,EAAA;QACAkB,KAAA;QACAM,IAAA;QACAf,QAAA;QACA4G,WAAA;QACAjC,KAAA;QACAlC,GAAA;QACA6C,QAAA;QACAC,WAAA;QACA5C,SAAA;MACA,GACA;QACApD,EAAA;QACAkB,KAAA;QACAM,IAAA;QACAf,QAAA;QACA4G,WAAA;QACAjC,KAAA;QACAlC,GAAA;QACA6C,QAAA;QACAC,WAAA;QACA5C,SAAA;MACA,GACA;QACApD,EAAA;QACAkB,KAAA;QACAM,IAAA;QACAf,QAAA;QACA4G,WAAA;QACAjC,KAAA;QACAlC,GAAA;QACA6C,QAAA;QACAC,WAAA;QACA5C,SAAA;MACA,EACA;;MAEA;MACAxE,KAAA,CAAAiC,KAAA,IACA;QACAb,EAAA;QACAkB,KAAA;QACAoF,OAAA;QACA7F,QAAA;QACA6G,IAAA;MACA,GACA;QACAtH,EAAA;QACAkB,KAAA;QACAoF,OAAA;QACA7F,QAAA;QACA6G,IAAA;MACA,GACA;QACAtH,EAAA;QACAkB,KAAA;QACAoF,OAAA;QACA7F,QAAA;QACA6G,IAAA;MACA,GACA;QACAtH,EAAA;QACAkB,KAAA;QACAoF,OAAA;QACA7F,QAAA;QACA6G,IAAA;MACA,EACA;;MAEA;MACA1I,KAAA,CAAAuC,WAAA,OAAAvC,KAAA,CAAAkC,IAAA;MACAlC,KAAA,CAAAwC,YAAA,OAAAxC,KAAA,CAAAiC,KAAA;;MAEA;MACAjC,KAAA,CAAAc,QAAA;MACAd,KAAA,CAAAmC,WAAA;;MAEA;MACAmF,UAAA;QACAtH,KAAA,CAAAsG,gBAAA;MACA;IACA;IACA;IACAA,iBAAA;MACA5C,OAAA,CAAAC,GAAA,sBAAA3D,KAAA,CAAAc,QAAA,kBAAAd,KAAA,CAAAmC,WAAA;MACA;MACA,IAAAnC,KAAA,CAAAc,QAAA;QACAd,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAwB,KAAA;QACA;QACAtC,KAAA,CAAA2I,mBAAA;MACA;QACA,MAAAC,GAAA,GAAA5I,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA;QACA,MAAAqE,KAAA,GAAAoC,GAAA,CAAAtE,GAAA,CAAAuB,KAAA,MAAAf,MAAA,GAAA8D,GAAA,CAAAzB,QAAA,CAAAtB,KAAA,MAAAf,MAAA,GAAA8D,GAAA,CAAAxB,WAAA,CAAAvB,KAAA,MAAAf,MAAA;QACA9E,KAAA,CAAAsC,KAAA,GAAAsG,GAAA,CAAAtG,KAAA,SAAAkE,KAAA;QACA;QACAxG,KAAA,CAAA6I,iBAAA;MACA;;MAEA;MACA7I,KAAA,CAAA8I,SAAA;QACA,IAAA9I,KAAA,CAAAgE,KAAA,CAAAjC,IAAA;UACA/B,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAA+E,SAAA,GAAA9G,KAAA,CAAAgE,KAAA,CAAAjC,IAAA,CAAAkF,YAAA;QACA;MACA;IACA;IACA;IACA4B,kBAAA;MACA,MAAAE,aAAA;QACA;QAAA;QACA;UACA3H,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA;MAEA;MAEA1H,KAAA,CAAA+B,IAAA,GAAAgH,aAAA,CAAA/I,KAAA,CAAAmC,WAAA;MACAuB,OAAA,CAAAC,GAAA,cAAA3D,KAAA,CAAA+B,IAAA,CAAA+C,MAAA;IACA;IACA;IACA6D,oBAAA;MACA,MAAAM,eAAA;QACA;QAAA;QACA;UACA7H,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA,GACA;UACAtG,EAAA;UACAqH,WAAA;UACArB,WAAA;UACA4B,MAAA;UACA1G,KAAA;UACAhB,IAAA;UACAoG,OAAA;QACA;MAEA;MAEA1H,KAAA,CAAA+B,IAAA,GAAAkH,eAAA,CAAAjJ,KAAA,CAAAc,QAAA;MACA4C,OAAA,CAAAC,GAAA,cAAA3D,KAAA,CAAA+B,IAAA,CAAA+C,MAAA;IACA;IAEA;IACAlB,gBAAA;MACA;MACA5D,KAAA,CAAAM,MAAA,IACA;QACAyB,IAAA,GACA;UACAX,EAAA;UACA8H,QAAA;UACAC,OAAA;QACA,GACA;UACA/H,EAAA;UACA8H,QAAA;UACAC,OAAA;QACA,GACA;UACA/H,EAAA;UACA8H,QAAA;UACAC,OAAA;QACA;MAEA,EACA;;MAEA;MACAnJ,KAAA,CAAAQ,UAAA,IACA;QACA4I,MAAA;QACArH,IAAA,GACA;UACAX,EAAA;UACAkB,KAAA;UACAT,QAAA;QACA,GACA;UACAT,EAAA;UACAkB,KAAA;UACAT,QAAA;QACA;MAEA,GACA;QACAuH,MAAA;QACArH,IAAA,GACA;UACAX,EAAA;UACAkB,KAAA;UACAT,QAAA;QACA;MAEA,GACA;QACAuH,MAAA;QACArH,IAAA,GACA;UACAX,EAAA;UACAkB,KAAA;UACAT,QAAA;QACA,GACA;UACAT,EAAA;UACAkB,KAAA;UACAT,QAAA;QACA;MAEA,EACA;;MAEA;MACA7B,KAAA,CAAAU,QAAA,IACA;QACAU,EAAA;QACAqH,WAAA;QACAnG,KAAA;QACAM,IAAA;QACAyG,UAAA;QACAC,aAAA;MACA,GACA;QACAlI,EAAA;QACAqH,WAAA;QACAnG,KAAA;QACAM,IAAA;QACAyG,UAAA;QACAC,aAAA;MACA,GACA;QACAlI,EAAA;QACAqH,WAAA;QACAnG,KAAA;QACAM,IAAA;QACAyG,UAAA;QACAC,aAAA;MACA,EACA;IACA;EACA;EACAC,cAAA;IACA7F,OAAA,CAAAC,GAAA;IACA6F,aAAA,MAAAxH,KAAA;EACA;EACAyH,QAAA;IACAzJ,KAAA;IACA;IACAA,KAAA,CAAAwI,YAAA;IACAxI,KAAA,CAAAmB,MAAA;IACA;IACA;IACA;IACA;IACAnB,KAAA,CAAAgI,WAAA;EACA;AACA", "ignoreList": []}]}