{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\Dashboard.vue?vue&type=template&id=2e5e4e3f&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\Dashboard.vue", "mtime": 1748429146923}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}