<template>
  <div class="notification-container">
    <div class="page-header">
      <h2>系统通知</h2>
      <div class="header-actions">
        <el-button @click="markAllAsRead" :disabled="unreadCount === 0">
          <i class="el-icon-check"></i> 全部标记为已读
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          <i class="el-icon-plus"></i> 发布通知
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ totalCount }}</div>
            <div class="stat-label">总通知数</div>
          </div>
          <i class="el-icon-bell stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number unread">{{ unreadCount }}</div>
            <div class="stat-label">未读通知</div>
          </div>
          <i class="el-icon-message stat-icon"></i>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="状态">
          <el-select v-model="filterForm.is_read" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="未读" value="0"></el-option>
            <el-option label="已读" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filterForm.type" placeholder="请选择类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="系统通知" value="system"></el-option>
            <el-option label="更新通知" value="update"></el-option>
            <el-option label="备份通知" value="backup"></el-option>
            <el-option label="警告通知" value="warning"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="级别">
          <el-select v-model="filterForm.level" placeholder="请选择级别" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="信息" value="info"></el-option>
            <el-option label="警告" value="warning"></el-option>
            <el-option label="错误" value="error"></el-option>
            <el-option label="成功" value="success"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadNotifications">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 通知列表 -->
    <el-card class="list-card">
      <div class="notification-list" v-loading="loading">
        <div 
          v-for="notification in notificationList" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
        >
          <div class="notification-header">
            <div class="notification-meta">
              <el-tag 
                :type="getLevelType(notification.level)" 
                size="small"
                class="level-tag"
              >
                {{ getLevelText(notification.level) }}
              </el-tag>
              <el-tag 
                :type="getTypeColor(notification.type)" 
                size="small"
                class="type-tag"
              >
                {{ getTypeText(notification.type) }}
              </el-tag>
              <span class="notification-time">{{ notification.time }}</span>
            </div>
            <div class="notification-actions">
              <el-button 
                v-if="!notification.read" 
                type="text" 
                size="small" 
                @click="markAsRead(notification)"
              >
                标记已读
              </el-button>
              <el-button type="text" size="small" @click="deleteNotification(notification)">
                删除
              </el-button>
            </div>
          </div>
          <div class="notification-content">
            <h4 class="notification-title">{{ notification.title }}</h4>
            <p class="notification-desc">{{ notification.content }}</p>
          </div>
        </div>

        <div v-if="notificationList.length === 0" class="empty-state">
          <i class="el-icon-bell"></i>
          <p>暂无通知</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 发布通知对话框 -->
    <el-dialog 
      title="发布通知" 
      :visible.sync="showAddDialog"
      width="600px"
    >
      <el-form :model="newNotification" :rules="notificationRules" ref="notificationForm" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="newNotification.title" placeholder="请输入通知标题"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input 
            type="textarea" 
            v-model="newNotification.content" 
            placeholder="请输入通知内容"
            :rows="4"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="newNotification.type" placeholder="请选择类型">
            <el-option label="系统通知" value="system"></el-option>
            <el-option label="更新通知" value="update"></el-option>
            <el-option label="备份通知" value="backup"></el-option>
            <el-option label="警告通知" value="warning"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-select v-model="newNotification.level" placeholder="请选择级别">
            <el-option label="信息" value="info"></el-option>
            <el-option label="警告" value="warning"></el-option>
            <el-option label="错误" value="error"></el-option>
            <el-option label="成功" value="success"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标用户">
          <el-select v-model="newNotification.target_type" placeholder="请选择目标用户">
            <el-option label="所有用户" value="all"></el-option>
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="普通用户" value="user"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="publishNotification">发布</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRequest, postRequest, deleteRequest } from '@/utils/api'

export default {
  name: 'NotificationList',
  mixins: [{ methods: { getRequest, postRequest, deleteRequest } }],
  data() {
    return {
      loading: false,
      showAddDialog: false,
      notificationList: [],
      totalCount: 0,
      unreadCount: 0,
      filterForm: {
        is_read: '',
        type: '',
        level: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      newNotification: {
        title: '',
        content: '',
        type: 'system',
        level: 'info',
        target_type: 'all'
      },
      notificationRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择级别', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.loadNotifications()
    this.loadStats()
  },
  methods: {
    async loadNotifications() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.filterForm
        }
        const response = await this.getRequest('/notification/list', params)
        if (response.code === 200) {
          this.notificationList = response.data.list || []
          this.pagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载通知失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    async loadStats() {
      try {
        const response = await this.getRequest('/notification/stats')
        if (response.code === 200) {
          this.totalCount = response.data.total || 0
          this.unreadCount = response.data.unread || 0
        }
      } catch (error) {
        console.error('加载统计失败:', error)
      }
    },

    async markAsRead(notification) {
      try {
        const response = await this.postRequest('/dashboard/markNotificationRead', {
          id: notification.id
        })
        if (response.code === 200) {
          notification.read = true
          this.unreadCount = Math.max(0, this.unreadCount - 1)
          this.$message.success('标记成功')
        }
      } catch (error) {
        console.error('标记失败:', error)
        this.$message.error('操作失败')
      }
    },

    async markAllAsRead() {
      try {
        await this.$confirm('确定要将所有未读通知标记为已读吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.postRequest('/notification/markAllRead')
        if (response.code === 200) {
          this.notificationList.forEach(item => item.read = true)
          this.unreadCount = 0
          this.$message.success('操作成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('操作失败:', error)
          this.$message.error('操作失败')
        }
      }
    },

    async deleteNotification(notification) {
      try {
        await this.$confirm('确定要删除这条通知吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.deleteRequest('/notification/delete', { id: notification.id })
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadNotifications()
          this.loadStats()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    async publishNotification() {
      try {
        await this.$refs.notificationForm.validate()
        
        const response = await this.postRequest('/notification/create', this.newNotification)
        if (response.code === 200) {
          this.$message.success('发布成功')
          this.showAddDialog = false
          this.resetForm()
          this.loadNotifications()
          this.loadStats()
        }
      } catch (error) {
        console.error('发布失败:', error)
        this.$message.error('发布失败')
      }
    },

    resetForm() {
      this.newNotification = {
        title: '',
        content: '',
        type: 'system',
        level: 'info',
        target_type: 'all'
      }
      this.$refs.notificationForm && this.$refs.notificationForm.resetFields()
    },

    resetFilter() {
      this.filterForm = {
        is_read: '',
        type: '',
        level: ''
      }
      this.pagination.page = 1
      this.loadNotifications()
    },

    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadNotifications()
    },

    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadNotifications()
    },

    getLevelType(level) {
      const map = {
        info: 'info',
        warning: 'warning',
        error: 'danger',
        success: 'success'
      }
      return map[level] || 'info'
    },

    getLevelText(level) {
      const map = {
        info: '信息',
        warning: '警告',
        error: '错误',
        success: '成功'
      }
      return map[level] || '信息'
    },

    getTypeColor(type) {
      const map = {
        system: '',
        update: 'success',
        backup: 'info',
        warning: 'warning'
      }
      return map[type] || ''
    },

    getTypeText(type) {
      const map = {
        system: '系统通知',
        update: '更新通知',
        backup: '备份通知',
        warning: '警告通知'
      }
      return map[type] || '系统通知'
    }
  }
}
</script>

<style scoped>
.notification-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-number.unread {
  color: #f56c6c;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-icon {
  font-size: 40px;
  color: #c0c4cc;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin-bottom: 0;
}

.list-card {
  margin-bottom: 20px;
}

.notification-list {
  min-height: 400px;
}

.notification-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.notification-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 4px solid #409eff;
  background-color: #f0f9ff;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-tag, .type-tag {
  margin-right: 8px;
}

.notification-time {
  color: #909399;
  font-size: 12px;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.notification-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-desc {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
