{"version": 3, "names": ["_t", "require", "isFor", "isForStatement", "isIfStatement", "isStatement", "WithStatement", "node", "word", "space", "token", "print", "object", "printBlock", "IfStatement", "test", "needsBlock", "alternate", "getLastStatement", "consequent", "newline", "indent", "printAndIndentOnComments", "dedent", "endsWith", "statement", "body", "ForStatement", "exit", "enterForStatementInit", "init", "update", "WhileStatement", "ForXStatement", "isForOf", "type", "await", "noIndentInnerCommentsHere", "left", "right", "ForInStatement", "exports", "ForOfStatement", "DoWhileStatement", "semicolon", "printStatementAfterKeyword", "printer", "parent", "isLabel", "printTerminatorless", "BreakStatement", "label", "ContinueStatement", "ReturnStatement", "argument", "ThrowStatement", "LabeledStatement", "TryStatement", "block", "handlers", "handler", "finalizer", "CatchClause", "param", "typeAnnotation", "SwitchStatement", "discriminant", "printSequence", "cases", "addNewlines", "leading", "cas", "length", "rightBrace", "SwitchCase", "DebuggerStatement", "VariableDeclaration", "declare", "kind", "hasInits", "declar", "declarations", "printList", "separator", "undefined", "VariableDeclarator", "id", "definite"], "sources": ["../../src/generators/statements.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isFor,\n  isForStatement,\n  isIfStatement,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nexport function WithStatement(this: Printer, node: t.WithStatement) {\n  this.word(\"with\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.object, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function IfStatement(this: Printer, node: t.IfStatement) {\n  this.word(\"if\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.space();\n\n  const needsBlock =\n    node.alternate && isIfStatement(getLastStatement(node.consequent));\n  if (needsBlock) {\n    this.token(\"{\");\n    this.newline();\n    this.indent();\n  }\n\n  this.printAndIndentOnComments(node.consequent, node);\n\n  if (needsBlock) {\n    this.dedent();\n    this.newline();\n    this.token(\"}\");\n  }\n\n  if (node.alternate) {\n    if (this.endsWith(charCodes.rightCurlyBrace)) this.space();\n    this.word(\"else\");\n    this.space();\n    this.printAndIndentOnComments(node.alternate, node);\n  }\n}\n\n// Recursively get the last statement.\nfunction getLastStatement(statement: t.Statement): t.Statement {\n  // @ts-expect-error: If statement.body is empty or not a Node, isStatement will return false\n  const { body } = statement;\n  if (isStatement(body) === false) {\n    return statement;\n  }\n\n  return getLastStatement(body);\n}\n\nexport function ForStatement(this: Printer, node: t.ForStatement) {\n  this.word(\"for\");\n  this.space();\n  this.token(\"(\");\n\n  {\n    const exit = this.enterForStatementInit(true);\n    this.print(node.init, node);\n    exit();\n  }\n\n  this.token(\";\");\n\n  if (node.test) {\n    this.space();\n    this.print(node.test, node);\n  }\n  this.token(\";\");\n\n  if (node.update) {\n    this.space();\n    this.print(node.update, node);\n  }\n\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function WhileStatement(this: Printer, node: t.WhileStatement) {\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nfunction ForXStatement(this: Printer, node: t.ForXStatement) {\n  this.word(\"for\");\n  this.space();\n  const isForOf = node.type === \"ForOfStatement\";\n  if (isForOf && node.await) {\n    this.word(\"await\");\n    this.space();\n  }\n  this.noIndentInnerCommentsHere();\n  this.token(\"(\");\n  {\n    const exit = isForOf ? null : this.enterForStatementInit(true);\n    this.print(node.left, node);\n    exit?.();\n  }\n  this.space();\n  this.word(isForOf ? \"of\" : \"in\");\n  this.space();\n  this.print(node.right, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport const ForInStatement = ForXStatement;\nexport const ForOfStatement = ForXStatement;\n\nexport function DoWhileStatement(this: Printer, node: t.DoWhileStatement) {\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n  this.space();\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.semicolon();\n}\n\nfunction printStatementAfterKeyword(\n  printer: Printer,\n  node: t.Node,\n  parent: t.Node,\n  isLabel: boolean,\n) {\n  if (node) {\n    printer.space();\n    printer.printTerminatorless(node, parent, isLabel);\n  }\n\n  printer.semicolon();\n}\n\nexport function BreakStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"break\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ContinueStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"continue\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ReturnStatement(this: Printer, node: t.ReturnStatement) {\n  this.word(\"return\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function ThrowStatement(this: Printer, node: t.ThrowStatement) {\n  this.word(\"throw\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function LabeledStatement(this: Printer, node: t.LabeledStatement) {\n  this.print(node.label, node);\n  this.token(\":\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function TryStatement(this: Printer, node: t.TryStatement) {\n  this.word(\"try\");\n  this.space();\n  this.print(node.block, node);\n  this.space();\n\n  // Esprima bug puts the catch clause in a `handlers` array.\n  // see https://code.google.com/p/esprima/issues/detail?id=433\n  // We run into this from regenerator generated ast.\n  // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n  if (node.handlers) {\n    // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n    this.print(node.handlers[0], node);\n  } else {\n    this.print(node.handler, node);\n  }\n\n  if (node.finalizer) {\n    this.space();\n    this.word(\"finally\");\n    this.space();\n    this.print(node.finalizer, node);\n  }\n}\n\nexport function CatchClause(this: Printer, node: t.CatchClause) {\n  this.word(\"catch\");\n  this.space();\n  if (node.param) {\n    this.token(\"(\");\n    this.print(node.param, node);\n    this.print(node.param.typeAnnotation, node);\n    this.token(\")\");\n    this.space();\n  }\n  this.print(node.body, node);\n}\n\nexport function SwitchStatement(this: Printer, node: t.SwitchStatement) {\n  this.word(\"switch\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.discriminant, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"{\");\n\n  this.printSequence(node.cases, node, {\n    indent: true,\n    addNewlines(leading, cas) {\n      if (!leading && node.cases[node.cases.length - 1] === cas) return -1;\n    },\n  });\n\n  this.rightBrace(node);\n}\n\nexport function SwitchCase(this: Printer, node: t.SwitchCase) {\n  if (node.test) {\n    this.word(\"case\");\n    this.space();\n    this.print(node.test, node);\n    this.token(\":\");\n  } else {\n    this.word(\"default\");\n    this.token(\":\");\n  }\n\n  if (node.consequent.length) {\n    this.newline();\n    this.printSequence(node.consequent, node, { indent: true });\n  }\n}\n\nexport function DebuggerStatement(this: Printer) {\n  this.word(\"debugger\");\n  this.semicolon();\n}\n\nexport function VariableDeclaration(\n  this: Printer,\n  node: t.VariableDeclaration,\n  parent: t.Node,\n) {\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  const { kind } = node;\n  if (kind === \"await using\") {\n    this.word(\"await\");\n    this.space();\n    this.word(\"using\", true);\n  } else {\n    this.word(kind, kind === \"using\");\n  }\n  this.space();\n\n  let hasInits = false;\n  // don't add whitespace to loop heads\n  if (!isFor(parent)) {\n    for (const declar of node.declarations) {\n      if (declar.init) {\n        // has an init so let's split it up over multiple lines\n        hasInits = true;\n      }\n    }\n  }\n\n  //\n  // use a pretty separator when we aren't in compact mode, have initializers and don't have retainLines on\n  // this will format declarations like:\n  //\n  //   let foo = \"bar\", bar = \"foo\";\n  //\n  // into\n  //\n  //   let foo = \"bar\",\n  //       bar = \"foo\";\n  //\n\n  this.printList(node.declarations, node, {\n    separator: hasInits\n      ? function (this: Printer) {\n          this.token(\",\");\n          this.newline();\n        }\n      : undefined,\n    indent: node.declarations.length > 1 ? true : false,\n  });\n\n  if (isFor(parent)) {\n    // don't give semicolons to these nodes since they'll be inserted in the parent generator\n    if (isForStatement(parent)) {\n      if (parent.init === node) return;\n    } else {\n      if (parent.left === node) return;\n    }\n  }\n\n  this.semicolon();\n}\n\nexport function VariableDeclarator(this: Printer, node: t.VariableDeclarator) {\n  this.print(node.id, node);\n  if (node.definite) this.token(\"!\"); // TS\n  // @ts-expect-error todo(flow-ts) Property 'typeAnnotation' does not exist on type 'MemberExpression'.\n  this.print(node.id.typeAnnotation, node);\n  if (node.init) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.init, node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAKsB;EAJpBC,KAAK;EACLC,cAAc;EACdC,aAAa;EACbC;AAAW,IAAAL,EAAA;AAQN,SAASM,aAAaA,CAAgBC,IAAqB,EAAE;EAClE,IAAI,CAACC,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,MAAM,EAAEL,IAAI,CAAC;EAC7B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAASO,WAAWA,CAAgBP,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EAEZ,MAAMO,UAAU,GACdT,IAAI,CAACU,SAAS,IAAIb,aAAa,CAACc,gBAAgB,CAACX,IAAI,CAACY,UAAU,CAAC,CAAC;EACpE,IAAIH,UAAU,EAAE;IACd,IAAI,CAACN,SAAK,IAAI,CAAC;IACf,IAAI,CAACU,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,MAAM,CAAC,CAAC;EACf;EAEA,IAAI,CAACC,wBAAwB,CAACf,IAAI,CAACY,UAAU,EAAEZ,IAAI,CAAC;EAEpD,IAAIS,UAAU,EAAE;IACd,IAAI,CAACO,MAAM,CAAC,CAAC;IACb,IAAI,CAACH,OAAO,CAAC,CAAC;IACd,IAAI,CAACV,SAAK,IAAI,CAAC;EACjB;EAEA,IAAIH,IAAI,CAACU,SAAS,EAAE;IAClB,IAAI,IAAI,CAACO,QAAQ,IAA0B,CAAC,EAAE,IAAI,CAACf,KAAK,CAAC,CAAC;IAC1D,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACa,wBAAwB,CAACf,IAAI,CAACU,SAAS,EAAEV,IAAI,CAAC;EACrD;AACF;AAGA,SAASW,gBAAgBA,CAACO,SAAsB,EAAe;EAE7D,MAAM;IAAEC;EAAK,CAAC,GAAGD,SAAS;EAC1B,IAAIpB,WAAW,CAACqB,IAAI,CAAC,KAAK,KAAK,EAAE;IAC/B,OAAOD,SAAS;EAClB;EAEA,OAAOP,gBAAgB,CAACQ,IAAI,CAAC;AAC/B;AAEO,SAASC,YAAYA,CAAgBpB,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EAEf;IACE,MAAMkB,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC;IAC7C,IAAI,CAAClB,KAAK,CAACJ,IAAI,CAACuB,IAAI,EAAEvB,IAAI,CAAC;IAC3BqB,IAAI,CAAC,CAAC;EACR;EAEA,IAAI,CAAClB,SAAK,GAAI,CAAC;EAEf,IAAIH,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACN,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC7B;EACA,IAAI,CAACG,SAAK,GAAI,CAAC;EAEf,IAAIH,IAAI,CAACwB,MAAM,EAAE;IACf,IAAI,CAACtB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACwB,MAAM,EAAExB,IAAI,CAAC;EAC/B;EAEA,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAASyB,cAAcA,CAAgBzB,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEA,SAAS0B,aAAaA,CAAgB1B,IAAqB,EAAE;EAC3D,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,MAAMyB,OAAO,GAAG3B,IAAI,CAAC4B,IAAI,KAAK,gBAAgB;EAC9C,IAAID,OAAO,IAAI3B,IAAI,CAAC6B,KAAK,EAAE;IACzB,IAAI,CAAC5B,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAAC4B,yBAAyB,CAAC,CAAC;EAChC,IAAI,CAAC3B,SAAK,GAAI,CAAC;EACf;IACE,MAAMkB,IAAI,GAAGM,OAAO,GAAG,IAAI,GAAG,IAAI,CAACL,qBAAqB,CAAC,IAAI,CAAC;IAC9D,IAAI,CAAClB,KAAK,CAACJ,IAAI,CAAC+B,IAAI,EAAE/B,IAAI,CAAC;IAC3BqB,IAAI,YAAJA,IAAI,CAAG,CAAC;EACV;EACA,IAAI,CAACnB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC0B,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;EAChC,IAAI,CAACzB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACgC,KAAK,EAAEhC,IAAI,CAAC;EAC5B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,MAAMiC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAGP,aAAa;AACpC,MAAMS,cAAc,GAAAD,OAAA,CAAAC,cAAA,GAAGT,aAAa;AAEpC,SAASU,gBAAgBA,CAAgBpC,IAAwB,EAAE;EACxE,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;EAC3B,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACkC,SAAS,CAAC,CAAC;AAClB;AAEA,SAASC,0BAA0BA,CACjCC,OAAgB,EAChBvC,IAAY,EACZwC,MAAc,EACdC,OAAgB,EAChB;EACA,IAAIzC,IAAI,EAAE;IACRuC,OAAO,CAACrC,KAAK,CAAC,CAAC;IACfqC,OAAO,CAACG,mBAAmB,CAAC1C,IAAI,EAAEwC,MAAM,EAAEC,OAAO,CAAC;EACpD;EAEAF,OAAO,CAACF,SAAS,CAAC,CAAC;AACrB;AAEO,SAASM,cAAcA,CAAgB3C,IAAyB,EAAE;EACvE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClBqC,0BAA0B,CAAC,IAAI,EAAEtC,IAAI,CAAC4C,KAAK,EAAE5C,IAAI,EAAE,IAAI,CAAC;AAC1D;AAEO,SAAS6C,iBAAiBA,CAAgB7C,IAAyB,EAAE;EAC1E,IAAI,CAACC,IAAI,CAAC,UAAU,CAAC;EACrBqC,0BAA0B,CAAC,IAAI,EAAEtC,IAAI,CAAC4C,KAAK,EAAE5C,IAAI,EAAE,IAAI,CAAC;AAC1D;AAEO,SAAS8C,eAAeA,CAAgB9C,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnBqC,0BAA0B,CAAC,IAAI,EAAEtC,IAAI,CAAC+C,QAAQ,EAAE/C,IAAI,EAAE,KAAK,CAAC;AAC9D;AAEO,SAASgD,cAAcA,CAAgBhD,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClBqC,0BAA0B,CAAC,IAAI,EAAEtC,IAAI,CAAC+C,QAAQ,EAAE/C,IAAI,EAAE,KAAK,CAAC;AAC9D;AAEO,SAASiD,gBAAgBA,CAAgBjD,IAAwB,EAAE;EACxE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC4C,KAAK,EAAE5C,IAAI,CAAC;EAC5B,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;AAC7B;AAEO,SAASkD,YAAYA,CAAgBlD,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmD,KAAK,EAAEnD,IAAI,CAAC;EAC5B,IAAI,CAACE,KAAK,CAAC,CAAC;EAMZ,IAAIF,IAAI,CAACoD,QAAQ,EAAE;IAEjB,IAAI,CAAChD,KAAK,CAACJ,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAAC,EAAEpD,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACqD,OAAO,EAAErD,IAAI,CAAC;EAChC;EAEA,IAAIA,IAAI,CAACsD,SAAS,EAAE;IAClB,IAAI,CAACpD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACsD,SAAS,EAAEtD,IAAI,CAAC;EAClC;AACF;AAEO,SAASuD,WAAWA,CAAgBvD,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIF,IAAI,CAACwD,KAAK,EAAE;IACd,IAAI,CAACrD,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACwD,KAAK,EAAExD,IAAI,CAAC;IAC5B,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACwD,KAAK,CAACC,cAAc,EAAEzD,IAAI,CAAC;IAC3C,IAAI,CAACG,SAAK,GAAI,CAAC;IACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;AAC7B;AAEO,SAAS0D,eAAeA,CAAgB1D,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC2D,YAAY,EAAE3D,IAAI,CAAC;EACnC,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,IAAI,CAAC;EAEf,IAAI,CAACyD,aAAa,CAAC5D,IAAI,CAAC6D,KAAK,EAAE7D,IAAI,EAAE;IACnCc,MAAM,EAAE,IAAI;IACZgD,WAAWA,CAACC,OAAO,EAAEC,GAAG,EAAE;MACxB,IAAI,CAACD,OAAO,IAAI/D,IAAI,CAAC6D,KAAK,CAAC7D,IAAI,CAAC6D,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC;IACtE;EACF,CAAC,CAAC;EAEF,IAAI,CAACE,UAAU,CAAClE,IAAI,CAAC;AACvB;AAEO,SAASmE,UAAUA,CAAgBnE,IAAkB,EAAE;EAC5D,IAAIA,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACP,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;IAC3B,IAAI,CAACG,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACE,SAAK,GAAI,CAAC;EACjB;EAEA,IAAIH,IAAI,CAACY,UAAU,CAACqD,MAAM,EAAE;IAC1B,IAAI,CAACpD,OAAO,CAAC,CAAC;IACd,IAAI,CAAC+C,aAAa,CAAC5D,IAAI,CAACY,UAAU,EAAEZ,IAAI,EAAE;MAAEc,MAAM,EAAE;IAAK,CAAC,CAAC;EAC7D;AACF;AAEO,SAASsD,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACnE,IAAI,CAAC,UAAU,CAAC;EACrB,IAAI,CAACoC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASgC,mBAAmBA,CAEjCrE,IAA2B,EAC3BwC,MAAc,EACd;EACA,IAAIxC,IAAI,CAACsE,OAAO,EAAE;IAEhB,IAAI,CAACrE,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,MAAM;IAAEqE;EAAK,CAAC,GAAGvE,IAAI;EACrB,IAAIuE,IAAI,KAAK,aAAa,EAAE;IAC1B,IAAI,CAACtE,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACA,IAAI,CAACsE,IAAI,EAAEA,IAAI,KAAK,OAAO,CAAC;EACnC;EACA,IAAI,CAACrE,KAAK,CAAC,CAAC;EAEZ,IAAIsE,QAAQ,GAAG,KAAK;EAEpB,IAAI,CAAC7E,KAAK,CAAC6C,MAAM,CAAC,EAAE;IAClB,KAAK,MAAMiC,MAAM,IAAIzE,IAAI,CAAC0E,YAAY,EAAE;MACtC,IAAID,MAAM,CAAClD,IAAI,EAAE;QAEfiD,QAAQ,GAAG,IAAI;MACjB;IACF;EACF;EAcA,IAAI,CAACG,SAAS,CAAC3E,IAAI,CAAC0E,YAAY,EAAE1E,IAAI,EAAE;IACtC4E,SAAS,EAAEJ,QAAQ,GACf,YAAyB;MACvB,IAAI,CAACrE,SAAK,GAAI,CAAC;MACf,IAAI,CAACU,OAAO,CAAC,CAAC;IAChB,CAAC,GACDgE,SAAS;IACb/D,MAAM,EAAEd,IAAI,CAAC0E,YAAY,CAACT,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG;EAChD,CAAC,CAAC;EAEF,IAAItE,KAAK,CAAC6C,MAAM,CAAC,EAAE;IAEjB,IAAI5C,cAAc,CAAC4C,MAAM,CAAC,EAAE;MAC1B,IAAIA,MAAM,CAACjB,IAAI,KAAKvB,IAAI,EAAE;IAC5B,CAAC,MAAM;MACL,IAAIwC,MAAM,CAACT,IAAI,KAAK/B,IAAI,EAAE;IAC5B;EACF;EAEA,IAAI,CAACqC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASyC,kBAAkBA,CAAgB9E,IAA0B,EAAE;EAC5E,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC+E,EAAE,EAAE/E,IAAI,CAAC;EACzB,IAAIA,IAAI,CAACgF,QAAQ,EAAE,IAAI,CAAC7E,SAAK,GAAI,CAAC;EAElC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC+E,EAAE,CAACtB,cAAc,EAAEzD,IAAI,CAAC;EACxC,IAAIA,IAAI,CAACuB,IAAI,EAAE;IACb,IAAI,CAACrB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAK,GAAI,CAAC;IACf,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACuB,IAAI,EAAEvB,IAAI,CAAC;EAC7B;AACF", "ignoreList": []}