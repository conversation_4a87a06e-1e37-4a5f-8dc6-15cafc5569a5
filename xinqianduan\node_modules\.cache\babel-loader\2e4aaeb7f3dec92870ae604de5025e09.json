{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748608591384}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_this", "emojiData", "audioplay", "name", "components", "data", "userss", "lvshiss", "yuangongss", "table", "gridData", "ruleForm", "dialogFormVisible", "currentTab", "selectId", "activeName", "search", "active", "imgUlr", "yon_id", "id", "isShowSeach", "type", "lists", "Names", "isShowPopup", "textContent", "lv<PERSON><PERSON>", "pic_path", "file_path", "list", "timer", "users", "quns", "quliaoIndex", "quli<PERSON><PERSON>", "isEmji", "title", "yuanshiquns", "yuanshiusers", "showMemberPanel", "memberData", "lawyers", "staff", "showUserDetail", "activeDetailTab", "currentUserDetail", "avatar", "phone", "idCard", "registerTime", "lastLogin", "status", "debtors", "documents", "orders", "payments", "methods", "editData", "getInfo", "desc", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "saveData", "$refs", "validate", "valid", "postRequest", "getData", "<PERSON><PERSON><PERSON>", "uid", "showDaiban", "is_daiban", "getList", "changeKeyword", "e", "target", "value", "length", "filter", "toLowerCase", "includes", "daiban", "openEmji", "console", "log", "changeFile", "field", "openFile", "window", "open", "openImg", "img", "beforeUpload", "file", "split", "showClose", "handleSuccess", "sendImg", "handleSuccess1", "flie", "sendFile", "redSession", "index", "loadTestMessages", "loadUserDetailData", "changeQun", "count", "<PERSON><PERSON><PERSON><PERSON>", "item", "change", "del", "handleScroll", "scrollTop", "send", "sendMessage", "scrollHeight", "loading", "lvshi_id", "yuangong_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "content", "orther_id", "direction", "files", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "delImage", "fileName", "loadTestData", "create_time", "time", "loadPrivateMessages", "qun", "loadGroupMessages", "$nextTick", "groupMessages", "privateMessages", "toggleMemberPanel", "loadMemberData", "current<PERSON><PERSON>", "replace", "amount", "statusText", "size", "uploadTime", "createTime", "description", "getDocIcon", "iconMap", "image", "pdf", "word", "excel", "default", "getOrderStatusType", "typeMap", "downloadDoc", "doc", "previewDoc", "info", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted"], "sources": ["src/views/pages/yonghu/chat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\" @click=\"showMemberPanel = false\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"用户详情\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-user\"\r\n                @click.stop=\"showUserDetail = !showUserDetail\"\r\n                class=\"user-detail-btn\"\r\n                :class=\"{ 'active': showUserDetail }\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-more\"\r\n                @click.stop=\"toggleMemberPanel\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户详情侧边栏 -->\r\n        <div class=\"user-detail-sidebar\" :class=\"{ 'show': showUserDetail }\" @click.stop>\r\n          <div class=\"detail-header\">\r\n            <h3>用户详情</h3>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"showUserDetail = false\"\r\n              class=\"close-btn\"\r\n            ></el-button>\r\n          </div>\r\n\r\n          <div class=\"detail-content\">\r\n            <!-- 左侧子菜单 -->\r\n            <div class=\"detail-menu\">\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'info' }\"\r\n                   @click=\"activeDetailTab = 'info'\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>基本信息</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'debtors' }\"\r\n                   @click=\"activeDetailTab = 'debtors'\">\r\n                <i class=\"el-icon-s-custom\"></i>\r\n                <span>关联债务人</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'documents' }\"\r\n                   @click=\"activeDetailTab = 'documents'\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>相关文档</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'orders' }\"\r\n                   @click=\"activeDetailTab = 'orders'\">\r\n                <i class=\"el-icon-tickets\"></i>\r\n                <span>工单记录</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'payments' }\"\r\n                   @click=\"activeDetailTab = 'payments'\">\r\n                <i class=\"el-icon-money\"></i>\r\n                <span>支付记录</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧内容区域 -->\r\n            <div class=\"detail-main\">\r\n              <!-- 基本信息 -->\r\n              <div v-if=\"activeDetailTab === 'info'\" class=\"tab-content\">\r\n                <div class=\"user-profile\">\r\n                  <div class=\"profile-avatar\">\r\n                    <img :src=\"currentUserDetail.avatar\" alt=\"用户头像\" />\r\n                  </div>\r\n                  <div class=\"profile-info\">\r\n                    <h4>{{ currentUserDetail.name }}</h4>\r\n                    <p class=\"user-type\">{{ currentUserDetail.type }}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"info-section\">\r\n                  <div class=\"info-item\">\r\n                    <label>手机号码：</label>\r\n                    <span>{{ currentUserDetail.phone }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentUserDetail.idCard }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>注册时间：</label>\r\n                    <span>{{ currentUserDetail.registerTime }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>最后登录：</label>\r\n                    <span>{{ currentUserDetail.lastLogin }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>用户状态：</label>\r\n                    <el-tag :type=\"currentUserDetail.status === '正常' ? 'success' : 'danger'\">\r\n                      {{ currentUserDetail.status }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 关联债务人 -->\r\n              <div v-if=\"activeDetailTab === 'debtors'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>关联债务人列表</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.debtors.length }}人</span>\r\n                </div>\r\n                <div class=\"debtors-list\">\r\n                  <div v-for=\"debtor in currentUserDetail.debtors\" :key=\"debtor.id\" class=\"debtor-card\">\r\n                    <div class=\"debtor-info\">\r\n                      <div class=\"debtor-name\">{{ debtor.name }}</div>\r\n                      <div class=\"debtor-details\">\r\n                        <span class=\"debt-amount\">欠款金额：¥{{ debtor.amount }}</span>\r\n                        <span class=\"debt-status\" :class=\"debtor.status\">{{ debtor.statusText }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"debtor-actions\">\r\n                      <el-button type=\"text\" size=\"small\">查看详情</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 相关文档 -->\r\n              <div v-if=\"activeDetailTab === 'documents'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>相关文档</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.documents.length }}个</span>\r\n                </div>\r\n                <div class=\"documents-list\">\r\n                  <div v-for=\"doc in currentUserDetail.documents\" :key=\"doc.id\" class=\"document-item\">\r\n                    <div class=\"doc-icon\">\r\n                      <i :class=\"getDocIcon(doc.type)\"></i>\r\n                    </div>\r\n                    <div class=\"doc-info\">\r\n                      <div class=\"doc-name\">{{ doc.name }}</div>\r\n                      <div class=\"doc-meta\">\r\n                        <span>{{ doc.size }}</span>\r\n                        <span>{{ doc.uploadTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"doc-actions\">\r\n                      <el-button type=\"text\" size=\"small\" @click=\"downloadDoc(doc)\">下载</el-button>\r\n                      <el-button type=\"text\" size=\"small\" @click=\"previewDoc(doc)\">预览</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 工单记录 -->\r\n              <div v-if=\"activeDetailTab === 'orders'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>工单记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.orders.length }}个</span>\r\n                </div>\r\n                <div class=\"orders-list\">\r\n                  <div v-for=\"order in currentUserDetail.orders\" :key=\"order.id\" class=\"order-item\">\r\n                    <div class=\"order-info\">\r\n                      <div class=\"order-title\">{{ order.title }}</div>\r\n                      <div class=\"order-meta\">\r\n                        <span class=\"order-type\">{{ order.type }}</span>\r\n                        <span class=\"order-time\">{{ order.createTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"order-status\">\r\n                      <el-tag :type=\"getOrderStatusType(order.status)\">{{ order.status }}</el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 支付记录 -->\r\n              <div v-if=\"activeDetailTab === 'payments'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>支付记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.payments.length }}笔</span>\r\n                </div>\r\n                <div class=\"payments-list\">\r\n                  <div v-for=\"payment in currentUserDetail.payments\" :key=\"payment.id\" class=\"payment-item\">\r\n                    <div class=\"payment-info\">\r\n                      <div class=\"payment-desc\">{{ payment.description }}</div>\r\n                      <div class=\"payment-time\">{{ payment.time }}</div>\r\n                    </div>\r\n                    <div class=\"payment-amount\">\r\n                      <span class=\"amount\">¥{{ payment.amount }}</span>\r\n                      <el-tag :type=\"payment.status === '已支付' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ payment.status }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n      showMemberPanel: false,\r\n      memberData: {\r\n        users: [],\r\n        lawyers: [],\r\n        staff: []\r\n      },\r\n      showUserDetail: true, // 默认打开用户详情面板\r\n      activeDetailTab: 'info', // 默认显示基本信息\r\n      currentUserDetail: {\r\n        name: '',\r\n        type: '',\r\n        avatar: '',\r\n        phone: '',\r\n        idCard: '',\r\n        registerTime: '',\r\n        lastLogin: '',\r\n        status: '',\r\n        debtors: [],\r\n        documents: [],\r\n        orders: [],\r\n        payments: []\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      _this.isEmji = !_this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      _this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      _this.imgUlr = img;\r\n      _this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      console.log('点击私聊:', index);\r\n      _this.selectId = index;\r\n      _this.quliaoIndex = -1;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    changeQun(index) {\r\n      console.log('点击群聊:', index);\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    getEmoji(item) {\r\n      _this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (_this.search) _this.isShowSeach = true;\r\n      else _this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      _this.search = \"\";\r\n      _this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (_this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 群聊测试数据\r\n      _this.quns = [\r\n        {\r\n          id: 1,\r\n          title: \"法务团队群\",\r\n          desc: \"最新消息：合同审核已完成\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          create_time: \"09:30\",\r\n          count: 3,\r\n          uid: \"1,2,3\",\r\n          lvshi_id: \"1,2\",\r\n          yuangong_id: \"1,2,3\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"客户服务群\",\r\n          desc: \"张三：请问合同什么时候能完成？\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          create_time: \"昨天\",\r\n          count: 1,\r\n          uid: \"4,5\",\r\n          lvshi_id: \"3\",\r\n          yuangong_id: \"4,5\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"紧急处理群\",\r\n          desc: \"李四：这个案件需要加急处理\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          create_time: \"15:20\",\r\n          count: 5,\r\n          uid: \"1,3,5\",\r\n          lvshi_id: \"1\",\r\n          yuangong_id: \"1,3\",\r\n          is_daiban: 1\r\n        }\r\n      ];\r\n\r\n      // 私聊用户测试数据\r\n      _this.users = [\r\n        {\r\n          id: 1,\r\n          title: \"张三（客户）\",\r\n          content: \"您好，请问我的合同审核进度如何？\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          time: \"10:30\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"李四（律师）\",\r\n          content: \"合同已经审核完毕，请查收\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          time: \"09:15\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"王五（调解员）\",\r\n          content: \"调解会议安排在明天下午2点\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          time: \"昨天\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"赵六（客户）\",\r\n          content: \"谢谢您的帮助！\",\r\n          pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n          time: \"16:45\"\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据用于搜索\r\n      _this.yuanshiquns = [..._this.quns];\r\n      _this.yuanshiusers = [..._this.users];\r\n\r\n      // 设置默认选中第一个群聊\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = 0;\r\n\r\n      // 加载第一个群聊的消息\r\n      setTimeout(() => {\r\n        _this.loadTestMessages();\r\n      }, 500);\r\n    },\r\n    // 加载测试消息数据\r\n    loadTestMessages() {\r\n      console.log('加载测试消息, selectId:', _this.selectId, 'quliaoIndex:', _this.quliaoIndex);\r\n      // 设置当前聊天标题\r\n      if (_this.selectId !== -1) {\r\n        _this.title = _this.users[_this.selectId].title;\r\n        // 加载私聊消息\r\n        _this.loadPrivateMessages();\r\n      } else {\r\n        const qun = _this.quns[_this.quliaoIndex];\r\n        const count = qun.uid.split(',').length + qun.lvshi_id.split(',').length + qun.yuangong_id.split(',').length;\r\n        _this.title = qun.title + \"(\" + count + \")\";\r\n        // 加载群聊消息\r\n        _this.loadGroupMessages();\r\n      }\r\n\r\n      // 滚动到底部\r\n      _this.$nextTick(() => {\r\n        if (_this.$refs.list) {\r\n          _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n        }\r\n      });\r\n    },\r\n    // 加载群聊消息\r\n    loadGroupMessages() {\r\n      const groupMessages = {\r\n        0: [ // 法务团队群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:00:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"大家好，今天我们讨论一下最新的合同审核流程\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:05:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张专员\",\r\n            type: \"text\",\r\n            content: \"好的，我这边已经准备好相关材料了\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 09:10:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"请大家查看一下这份合同模板\"\r\n          },\r\n          {\r\n            id: 4,\r\n            create_time: \"2024-01-22 09:12:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"image\",\r\n            content: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          },\r\n          {\r\n            id: 5,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"这个模板看起来不错，我们可以在此基础上进行修改\"\r\n          }\r\n        ],\r\n        1: [ // 客户服务群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:00:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"请问合同什么时候能完成？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，合同预计明天下午可以完成审核\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:10:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢！\"\r\n          }\r\n        ],\r\n        2: [ // 紧急处理群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 15:00:00\",\r\n            yuangong_id: 5,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李四\",\r\n            type: \"text\",\r\n            content: \"这个案件需要加急处理\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 15:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，我立即安排处理\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 15:10:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"我这边也会配合加急处理\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = groupMessages[_this.quliaoIndex] || [];\r\n      console.log('群聊消息加载完成:', _this.list.length);\r\n    },\r\n    // 加载私聊消息\r\n    loadPrivateMessages() {\r\n      const privateMessages = {\r\n        0: [ // 张三（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:30:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"您好，请问我的合同审核进度如何？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:35:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，您的合同正在审核中，预计今天下午可以完成\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:40:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢您！\"\r\n          }\r\n        ],\r\n        1: [ // 李四（律师）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"合同已经审核完毕，请查收\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:20:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，辛苦了！\"\r\n          }\r\n        ],\r\n        2: [ // 王五（调解员）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-21 16:00:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"王调解员\",\r\n            type: \"text\",\r\n            content: \"调解会议安排在明天下午2点\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-21 16:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"好的，我会准时参加\"\r\n          }\r\n        ],\r\n        3: [ // 赵六（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 16:45:00\",\r\n            yuangong_id: 6,\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n            title: \"赵六\",\r\n            type: \"text\",\r\n            content: \"谢谢您的帮助！\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 16:50:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"不客气，有问题随时联系我\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = privateMessages[_this.selectId] || [];\r\n      console.log('私聊消息加载完成:', _this.list.length);\r\n    },\r\n\r\n\r\n\r\n    // 新的群成员面板切换方法\r\n    toggleMemberPanel() {\r\n      console.log('切换群成员面板');\r\n      _this.showMemberPanel = !_this.showMemberPanel;\r\n      if (_this.showMemberPanel) {\r\n        _this.loadMemberData();\r\n      }\r\n    },\r\n\r\n    // 加载群成员数据\r\n    loadMemberData() {\r\n      console.log('加载群成员数据');\r\n      _this.memberData = {\r\n        users: [\r\n          {\r\n            id: 1,\r\n            name: \"张三\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 2,\r\n            name: \"李四\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 3,\r\n            name: \"王五\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          }\r\n        ],\r\n        lawyers: [\r\n          {\r\n            id: 4,\r\n            name: \"李律师\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 5,\r\n            name: \"陈律师\",\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          }\r\n        ],\r\n        staff: [\r\n          {\r\n            id: 6,\r\n            name: \"王调解员\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          },\r\n          {\r\n            id: 7,\r\n            name: \"张专员\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 8,\r\n            name: \"赵专员\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    // 加载用户详情数据\r\n    loadUserDetailData() {\r\n      // 根据当前选中的群聊加载对应用户信息\r\n      const currentQun = _this.quns[_this.quliaoIndex];\r\n      if (currentQun) {\r\n        _this.currentUserDetail = {\r\n          name: currentQun.title.replace(/群$/, '') + '用户',\r\n          type: '客户',\r\n          avatar: currentQun.pic_path,\r\n          phone: '138****8888',\r\n          idCard: '320***********1234',\r\n          registerTime: '2024-01-15 10:30:00',\r\n          lastLogin: '2024-01-22 09:15:00',\r\n          status: '正常',\r\n          debtors: [\r\n            {\r\n              id: 1,\r\n              name: '张三',\r\n              amount: '50000.00',\r\n              status: 'overdue',\r\n              statusText: '逾期'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '李四',\r\n              amount: '30000.00',\r\n              status: 'normal',\r\n              statusText: '正常'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '王五',\r\n              amount: '80000.00',\r\n              status: 'settled',\r\n              statusText: '已结清'\r\n            }\r\n          ],\r\n          documents: [\r\n            {\r\n              id: 1,\r\n              name: '身份证正面.jpg',\r\n              type: 'image',\r\n              size: '2.5MB',\r\n              uploadTime: '2024-01-15'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '营业执照.pdf',\r\n              type: 'pdf',\r\n              size: '1.8MB',\r\n              uploadTime: '2024-01-16'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '合同文件.docx',\r\n              type: 'word',\r\n              size: '856KB',\r\n              uploadTime: '2024-01-18'\r\n            }\r\n          ],\r\n          orders: [\r\n            {\r\n              id: 1,\r\n              title: '合同审核申请',\r\n              type: '法律咨询',\r\n              status: '已完成',\r\n              createTime: '2024-01-20'\r\n            },\r\n            {\r\n              id: 2,\r\n              title: '债务追讨服务',\r\n              type: '债务处理',\r\n              status: '处理中',\r\n              createTime: '2024-01-21'\r\n            },\r\n            {\r\n              id: 3,\r\n              title: '法律文书起草',\r\n              type: '文书服务',\r\n              status: '待处理',\r\n              createTime: '2024-01-22'\r\n            }\r\n          ],\r\n          payments: [\r\n            {\r\n              id: 1,\r\n              description: '法律咨询费用',\r\n              amount: '500.00',\r\n              status: '已支付',\r\n              time: '2024-01-20 14:30:00'\r\n            },\r\n            {\r\n              id: 2,\r\n              description: '合同审核费用',\r\n              amount: '800.00',\r\n              status: '已支付',\r\n              time: '2024-01-21 10:15:00'\r\n            },\r\n            {\r\n              id: 3,\r\n              description: '债务处理服务费',\r\n              amount: '1200.00',\r\n              status: '待支付',\r\n              time: '2024-01-22 09:00:00'\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    // 获取文档图标\r\n    getDocIcon(type) {\r\n      const iconMap = {\r\n        image: 'el-icon-picture',\r\n        pdf: 'el-icon-document',\r\n        word: 'el-icon-document',\r\n        excel: 'el-icon-s-grid',\r\n        default: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || iconMap.default;\r\n    },\r\n\r\n    // 获取工单状态类型\r\n    getOrderStatusType(status) {\r\n      const typeMap = {\r\n        '已完成': 'success',\r\n        '处理中': 'warning',\r\n        '待处理': 'info',\r\n        '已取消': 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 下载文档\r\n    downloadDoc(doc) {\r\n      console.log('下载文档:', doc.name);\r\n      _this.$message.success('开始下载: ' + doc.name);\r\n    },\r\n\r\n    // 预览文档\r\n    previewDoc(doc) {\r\n      console.log('预览文档:', doc.name);\r\n      _this.$message.info('预览功能开发中...');\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    // 使用测试数据替代API调用\r\n    _this.loadTestData();\r\n    _this.loadUserDetailData();\r\n    _this.yon_id = 1; // 设置当前用户ID\r\n    // 注释掉定时器，避免不必要的API调用\r\n    // _this.timer = setInterval(() => {\r\n    //   _this.getMoreList();\r\n    // }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 用户详情侧边栏 */\r\n.user-detail-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -600px;\r\n  width: 600px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  display: flex;\r\n  height: calc(100% - 80px);\r\n}\r\n\r\n.detail-menu {\r\n  width: 200px;\r\n  background: #f8f9fa;\r\n  border-right: 1px solid #e9ecef;\r\n  padding: 20px 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  color: #6c757d;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    color: #495057;\r\n  }\r\n\r\n  &.active {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n  }\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  span {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.detail-main {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.profile-avatar {\r\n  margin-right: 20px;\r\n\r\n  img {\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 3px solid #ffffff;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.profile-info {\r\n  h4 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .user-type {\r\n    margin: 0;\r\n    font-size: 14px;\r\n    color: #6c757d;\r\n    padding: 4px 12px;\r\n    background: #e9ecef;\r\n    border-radius: 20px;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.info-section {\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    label {\r\n      width: 120px;\r\n      font-weight: 500;\r\n      color: #495057;\r\n      margin: 0;\r\n    }\r\n\r\n    span {\r\n      color: #212529;\r\n    }\r\n  }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .count-badge {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n    padding: 4px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.debtors-list {\r\n  .debtor-card {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n      transform: translateY(-1px);\r\n    }\r\n  }\r\n\r\n  .debtor-info {\r\n    .debtor-name {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .debtor-details {\r\n      display: flex;\r\n      gap: 15px;\r\n\r\n      .debt-amount {\r\n        color: #dc3545;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .debt-status {\r\n        padding: 2px 8px;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n\r\n        &.overdue {\r\n          background: #f8d7da;\r\n          color: #721c24;\r\n        }\r\n\r\n        &.normal {\r\n          background: #d4edda;\r\n          color: #155724;\r\n        }\r\n\r\n        &.settled {\r\n          background: #cce7ff;\r\n          color: #004085;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.documents-list {\r\n  .document-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 8px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .doc-icon {\r\n    margin-right: 12px;\r\n    color: #6c757d;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .doc-info {\r\n    flex: 1;\r\n\r\n    .doc-name {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .doc-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .doc-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n.orders-list {\r\n  .order-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .order-info {\r\n    .order-title {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .order-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n\r\n      .order-type {\r\n        padding: 2px 8px;\r\n        background: #e9ecef;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.payments-list {\r\n  .payment-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .payment-info {\r\n    .payment-desc {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .payment-time {\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .payment-amount {\r\n    text-align: right;\r\n\r\n    .amount {\r\n      font-weight: 600;\r\n      color: #28a745;\r\n      margin-bottom: 4px;\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n.member-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 用户详情按钮样式 */\r\n.user-detail-btn {\r\n  color: #606266;\r\n  font-size: 16px;\r\n  margin-right: 8px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #409eff;\r\n    transform: scale(1.1);\r\n  }\r\n\r\n  &.active {\r\n    color: #409eff;\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n\r\n/* 手机端适配 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    flex-direction: column;\r\n    height: 100vh;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    max-height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    overflow-y: hidden;\r\n  }\r\n\r\n  .sidebar-header {\r\n    padding: 10px 15px;\r\n  }\r\n\r\n  .search-box {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .contact-list {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 10px;\r\n    padding: 0 15px 15px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 120px;\r\n    flex-shrink: 0;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .chat-main {\r\n    flex: 1;\r\n    height: calc(100vh - 200px);\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .chat-header {\r\n    padding: 10px 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .chat-actions {\r\n    gap: 5px;\r\n  }\r\n\r\n  .user-detail-btn,\r\n  .more-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-list {\r\n    flex: 1;\r\n    padding: 10px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .message-item {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 85%;\r\n  }\r\n\r\n  .message-avatar img {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n\r\n  .message-bubble {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .sender-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 15px;\r\n    border-top: 1px solid #e9ecef;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 3px;\r\n    margin-bottom: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-input {\r\n    ::v-deep .el-textarea__inner {\r\n      font-size: 14px;\r\n      padding: 8px 12px;\r\n    }\r\n  }\r\n\r\n  .send-btn {\r\n    padding: 8px 16px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 用户详情侧边栏手机端适配 */\r\n  .user-detail-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n    z-index: 1000;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .detail-header {\r\n    padding: 15px;\r\n    border-bottom: 1px solid #e9ecef;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-content {\r\n    flex-direction: column;\r\n    height: calc(100vh - 60px);\r\n  }\r\n\r\n  .detail-menu {\r\n    width: 100%;\r\n    height: auto;\r\n    flex-direction: row;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    padding: 0;\r\n  }\r\n\r\n  .menu-item {\r\n    min-width: 100px;\r\n    flex-shrink: 0;\r\n    padding: 12px 15px;\r\n    border-bottom: none;\r\n    border-right: 1px solid #e9ecef;\r\n\r\n    &:last-child {\r\n      border-right: none;\r\n    }\r\n\r\n    span {\r\n      font-size: 12px;\r\n    }\r\n\r\n    i {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-main {\r\n    flex: 1;\r\n    padding: 15px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .user-profile {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .profile-avatar img {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .profile-info h4 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .info-item {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .section-header {\r\n    margin-bottom: 15px;\r\n\r\n    h4 {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .debtor-card,\r\n  .document-item,\r\n  .order-item,\r\n  .payment-item {\r\n    padding: 12px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 群成员面板手机端适配 */\r\n  .member-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .member-header {\r\n    padding: 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .member-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));\r\n    gap: 10px;\r\n    padding: 15px;\r\n  }\r\n\r\n  .member-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n\r\n  .member-name {\r\n    font-size: 11px;\r\n  }\r\n\r\n  /* 表情面板手机端适配 */\r\n  .emoji-panel {\r\n    width: 280px !important;\r\n    height: 180px !important;\r\n    bottom: 45px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(6, 1fr) !important;\r\n    padding: 10px !important;\r\n  }\r\n\r\n  .emoji-item {\r\n    padding: 8px !important;\r\n    font-size: 18px !important;\r\n  }\r\n}\r\n\r\n/* 超小屏幕适配 (小于480px) */\r\n@media (max-width: 480px) {\r\n  .sidebar {\r\n    max-height: 150px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 100px;\r\n  }\r\n\r\n  .chat-main {\r\n    height: calc(100vh - 150px);\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 90%;\r\n  }\r\n\r\n  .message-bubble {\r\n    font-size: 13px;\r\n    padding: 6px 10px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 10px;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 2px;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .detail-menu {\r\n    .menu-item {\r\n      min-width: 80px;\r\n      padding: 10px 12px;\r\n\r\n      span {\r\n        font-size: 11px;\r\n      }\r\n\r\n      i {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .emoji-panel {\r\n    width: 250px !important;\r\n    height: 160px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(5, 1fr) !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA2kBA,IAAAA,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,iBAAA;MACAX,SAAA,EAAAA,SAAA;MACAY,UAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,EAAA;MACAC,WAAA;MACAC,IAAA;MACAC,KAAA;MAEAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAZ,QAAA;MACAa,OAAA;MACAC,QAAA;MACAC,SAAA;MACAC,IAAA;MACAC,KAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MACAC,UAAA;QACAT,KAAA;QACAU,OAAA;QACAC,KAAA;MACA;MACAC,cAAA;MAAA;MACAC,eAAA;MAAA;MACAC,iBAAA;QACA3C,IAAA;QACAmB,IAAA;QACAyB,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAAtC,EAAA;MACA,IAAApB,KAAA;MACA,IAAAoB,EAAA;QACA,KAAAuC,OAAA,CAAAvC,EAAA;MACA;QACA,KAAAT,QAAA;UACA0B,KAAA;UACAuB,IAAA;QACA;MACA;IACA;IACAC,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAAtD,QAAA,gBAAAmD,GAAA,CAAAzD,IAAA,CAAA6D,GAAA;MACA;QACA,KAAAF,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACAT,QAAAvC,EAAA;MACA,IAAApB,KAAA;MACA,KAAAqE,UAAA,uBAAAjD,EAAA,EAAAkD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAW,QAAA,GAAA4D,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAY,iBAAA;QACA;UACAZ,KAAA,CAAAgE,QAAA;YACA1C,IAAA;YACAkD,OAAA,EAAAD,IAAA,CAAAH;UACA;QACA;MACA;IACA;IAEAK,SAAA;MACA,IAAAzE,KAAA;MACA,KAAA0E,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,uBAAAlE,QAAA,EAAA2D,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAR,IAAA;cACA/D,KAAA,CAAAgE,QAAA;gBACA1C,IAAA;gBACAkD,OAAA,EAAAD,IAAA,CAAAH;cACA;cACA,KAAAU,OAAA;cACA9E,KAAA,CAAAY,iBAAA;YACA;cACAZ,KAAA,CAAAgE,QAAA;gBACA1C,IAAA;gBACAkD,OAAA,EAAAD,IAAA,CAAAH;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAW,YAAA;MACA,IAAAC,GAAA,QAAA/C,IAAA,MAAAC,WAAA;MACAlC,KAAA,CAAAS,KAAA;MACAT,KAAA,CAAA6E,WAAA;QAAAG,GAAA,EAAAA;MAAA,GAAAV,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAU,QAAA,GAAA6D,IAAA,CAAAlE,IAAA;QACA;MACA;IACA;IACA4E,WAAAC,SAAA;MACA,KAAArE,UAAA,GAAAqE,SAAA;MACAlF,KAAA,CACA6E,WAAA;QAAAK,SAAA,EAAAA;MAAA,GACAZ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAiC,IAAA,GAAAsC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAiC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAd,KAAA,CAAAmF,OAAA;QACA;MACA;IACA;IACAC,cAAAC,CAAA;MACA,IAAApD,IAAA,GAAAjC,KAAA,CAAAsC,WAAA;MACA,IAAAN,KAAA,GAAAhC,KAAA,CAAAuC,YAAA;MACA,IAAAvB,MAAA,GAAAqE,CAAA,CAAAC,MAAA,CAAAC,KAAA;MAEA,KAAAlE,WAAA,GAAAL,MAAA,CAAAwE,MAAA;MAEAxF,KAAA,CAAAiC,IAAA,GAAAA,IAAA,CAAAwD,MAAA,CAAApF,IAAA,IAAAA,IAAA,CAAAgC,KAAA,CAAAqD,WAAA,GAAAC,QAAA,CAAA3E,MAAA,CAAA0E,WAAA;MACA1F,KAAA,CAAAgC,KAAA,GAAAA,KAAA,CAAAyD,MAAA,CAAApF,IAAA,IAAAA,IAAA,CAAAgC,KAAA,CAAAqD,WAAA,GAAAC,QAAA,CAAA3E,MAAA,CAAA0E,WAAA;IACA;IACAE,OAAA;MACA,IAAAxE,EAAA,QAAAa,IAAA,MAAAC,WAAA;MACA,IAAAgD,SAAA,QAAAjD,IAAA,MAAAC,WAAA;MACAlC,KAAA,CACA6E,WAAA;QAAAzD,EAAA,EAAAA,EAAA;QAAA8D,SAAA,EAAAA;MAAA,GACAZ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAiC,IAAA,MAAAC,WAAA,iBAAAgD,SAAA;UACAlF,KAAA,CAAAgE,QAAA,CAAAC,OAAA,CAAAM,IAAA,CAAAH,GAAA;QACA;UACApE,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACAyB,SAAA;MACA7F,KAAA,CAAAoC,MAAA,IAAApC,KAAA,CAAAoC,MAAA;MACA0D,OAAA,CAAAC,GAAA;IACA;IACAC,WAAAC,KAAA;MACAjG,KAAA,CAAAsB,IAAA,GAAA2E,KAAA;IACA;IACAC,SAAAhC,GAAA;MACAiC,MAAA,CAAAC,IAAA,CAAAlC,GAAA;IACA;IACAmC,QAAAC,GAAA;MACAtG,KAAA,CAAAkB,MAAA,GAAAoF,GAAA;MACAtG,KAAA,CAAAyB,WAAA;MACAqE,OAAA,CAAAC,GAAA,eAAAO,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,IAAAlF,IAAA,GAAAkF,IAAA,CAAAlF,IAAA;MACAwE,OAAA,CAAAC,GAAA,CAAAzE,IAAA;MACA,IACA,CAAAkF,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,qBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,sBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,qBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,qBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,qBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,sBACA,CAAAD,IAAA,CAAAlF,IAAA,CAAAmF,KAAA,oBACA;QACAzG,KAAA,CAAAgE,QAAA;UACA0C,SAAA;UACAlC,OAAA;UACAlD,IAAA;QACA;QACA;MACA;IACA;IACAqF,cAAA7C,GAAA;MACA,IAAA9D,KAAA;MACA8F,OAAA,CAAAC,GAAA,CAAAjC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA/D,KAAA,CAAA4G,OAAA,CAAA9C,GAAA,CAAAzD,IAAA,CAAA6D,GAAA;MACA;QACAlE,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACAyC,eAAA/C,GAAA,EAAAgD,IAAA;MACA,IAAAhD,GAAA,CAAAC,IAAA;QACA/D,KAAA,CAAA+G,QAAA,CAAAjD,GAAA,CAAAzD,IAAA,CAAA6D,GAAA,EAAA4C,IAAA;MACA;QACA9G,KAAA,CAAAgE,QAAA;UACA0C,SAAA;UACAlC,OAAA;UACAlD,IAAA;QACA;MACA;IACA;IACA0F,WAAAC,KAAA;MACAnB,OAAA,CAAAC,GAAA,UAAAkB,KAAA;MACAjH,KAAA,CAAAc,QAAA,GAAAmG,KAAA;MACAjH,KAAA,CAAAkC,WAAA;MACAlC,KAAA,CAAAwC,eAAA;MACAxC,KAAA,CAAAkH,gBAAA;MACAlH,KAAA,CAAAmH,kBAAA;IACA;IACAC,UAAAH,KAAA;MACAnB,OAAA,CAAAC,GAAA,UAAAkB,KAAA;MACAjH,KAAA,CAAAc,QAAA;MACAd,KAAA,CAAAkC,WAAA,GAAA+E,KAAA;MACAjH,KAAA,CAAAiC,IAAA,CAAAgF,KAAA,EAAAI,KAAA;MACArH,KAAA,CAAAwC,eAAA;MACAxC,KAAA,CAAAkH,gBAAA;MACAlH,KAAA,CAAAmH,kBAAA;IACA;IACAG,SAAAC,IAAA;MACAvH,KAAA,CAAA0B,WAAA,IAAA6F,IAAA;IACA;IACAC,OAAAnC,CAAA;MACA,IAAArF,KAAA,CAAAgB,MAAA,EAAAhB,KAAA,CAAAqB,WAAA,aACArB,KAAA,CAAAqB,WAAA;IACA;IACAoG,IAAA;MACAzH,KAAA,CAAAgB,MAAA;MACAhB,KAAA,CAAAqB,WAAA;IACA;IACAqG,aAAArC,CAAA;MACA,IAAArF,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA;QACA7B,OAAA,CAAAC,GAAA;MACA;IACA;IACA6B,KAAA;MACA5H,KAAA,CAAA6H,WAAA,CAAA7H,KAAA,CAAA0B,WAAA;MACA1B,KAAA,CAAA0B,WAAA;IACA;IACAyD,QAAA;MACA,IAAAnF,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAuB,KAAA;QAEArC,KAAA,CAAA6E,WAAA;UAAAG,GAAA,EAAA5D;QAAA,GAAAkD,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlE,IAAA,CAAAmF,MAAA;cACAxF,KAAA,CAAA8B,IAAA,GAAAyC,IAAA,CAAAlE,IAAA;cAEAL,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GAAA3H,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAAgG,YAAA;YACA;UACA;UACA9H,KAAA,CAAA+H,OAAA;QACA;MACA;QACA,IAAA3G,EAAA,GAAApB,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAd,EAAA;QACA,IAAAiG,KAAA,GACArH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8C,GAAA,CAAAQ,MAAA,OACAxF,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8F,QAAA,CAAAxC,MAAA,OACAxF,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA+F,WAAA,CAAAzC,MAAA;QACAxF,KAAA,CAAAoB,EAAA,GAAAA,EAAA;QACA0E,OAAA,CAAAC,GAAA,CAAA/F,KAAA,CAAAoB,EAAA;QAEApB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAG,KAAA,SAAAgF,KAAA;QACArH,KAAA,CAAA6E,WAAA;UAAAqD,MAAA,EAAA9G;QAAA,GAAAkD,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlE,IAAA,CAAAmF,MAAA;cACAxF,KAAA,CAAA8B,IAAA,GAAAyC,IAAA,CAAAlE,IAAA;cACAL,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GAAA3H,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAAgG,YAAA;YACA;cACA9H,KAAA,CAAA8B,IAAA;YACA;YAEAqG,UAAA,CACA,WAAAzD,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,QAAAjD,KAAA,CAAA5C,IAAA,CAAAgG,YAAA,EACA,CACA;UACA;UACA9H,KAAA,CAAA+H,OAAA;QACA;MACA;IACA;IACAK,YAAA;MACA,IAAApI,KAAA,CAAAc,QAAA;QACA,IAAAkE,GAAA,GAAAhF,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAuB,KAAA;QAEA,IAAAjB,EAAA;QACA,IAAApB,KAAA,CAAA8B,IAAA,CAAA0D,MAAA;UACApE,EAAA,GAAApB,KAAA,CAAA8B,IAAA,CAAA9B,KAAA,CAAA8B,IAAA,CAAA0D,MAAA,MAAApE,EAAA;UACApB,KAAA,CACA6E,WAAA;YAAAG,GAAA,EAAAA,GAAA;YAAA5D,EAAA,EAAAA;UAAA,GACAkD,IAAA,CAAAC,IAAA;YACAvE,KAAA,CAAAqI,OAAA;YACA,IAAA9D,IAAA,CAAAR,IAAA;cACA,IAAAQ,IAAA,CAAAlE,IAAA,CAAAmF,MAAA;gBACAxF,KAAA,CAAA8B,IAAA,CAAAwG,IAAA,CAAA/D,IAAA,CAAAlE,IAAA;gBACA8H,UAAA,CACA,MACA,KAAAzD,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GACA,KAAAjD,KAAA,CAAA5C,IAAA,CAAAgG,YAAA,EACA,IACA;cACA;YACA;YACA9H,KAAA,CAAA+H,OAAA;UACA;QACA;MACA;QACA,IAAAG,MAAA,GAAAlI,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAd,EAAA;QACA,IAAAiG,KAAA,GACArH,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8F,QAAA,CAAAxC,MAAA,OACAxF,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA+F,WAAA,CAAAzC,MAAA,OACA;QAEAxF,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAG,KAAA,SAAAgF,KAAA;QACA,IAAAjG,EAAA;QACA,IAAApB,KAAA,CAAA8B,IAAA,CAAA0D,MAAA;UACApE,EAAA,GAAApB,KAAA,CAAA8B,IAAA,CAAA9B,KAAA,CAAA8B,IAAA,CAAA0D,MAAA,MAAApE,EAAA;UACApB,KAAA,CACA6E,WAAA;YAAAqD,MAAA,EAAAA,MAAA;YAAA9G,EAAA,EAAAA;UAAA,GACAkD,IAAA,CAAAC,IAAA;YACAvE,KAAA,CAAAqI,OAAA;YACA,IAAA9D,IAAA,CAAAR,IAAA;cACA/D,KAAA,CAAA8B,IAAA,CAAAwG,IAAA,CAAA/D,IAAA,CAAAlE,IAAA;cAEA8H,UAAA,CACA,MACAnI,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GACA3H,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAAgG,YAAA,EACA,IACA;YACA;YACA9H,KAAA,CAAA+H,OAAA;UACA;QACA;UACA3G,EAAA;UACApB,KAAA,CACA6E,WAAA;YAAAqD,MAAA,EAAAA,MAAA;YAAA9G,EAAA,EAAAA;UAAA,GACAkD,IAAA,CAAAC,IAAA;YACAvE,KAAA,CAAAqI,OAAA;YACA,IAAA9D,IAAA,CAAAR,IAAA;cACA/D,KAAA,CAAA8B,IAAA,CAAAwG,IAAA,CAAA/D,IAAA,CAAAlE,IAAA;cAEA8H,UAAA,CACA,MACAnI,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GACA3H,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAAgG,YAAA,EACA,IACA;YACA;YACA9H,KAAA,CAAA+H,OAAA;UACA;QACA;MACA;IACA;IACAF,YAAAU,OAAA;MACA,IAAAvI,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAoH,SAAA;QACAxI,KAAA,CACA6E,WAAA;UACAG,GAAA,EAAA5D,EAAA;UACAqH,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAlE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAY,GAAA,GAAAhF,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8C,GAAA;QACA,IAAAkD,MAAA,GAAAlI,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAd,EAAA;QACApB,KAAA,CACA6E,WAAA;UACA4D,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA5D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA2C,SAAAwB,OAAA,EAAAG,KAAA;MACA,IAAA1I,KAAA,CAAAc,QAAA;QACA,IAAA0H,SAAA;QACAxI,KAAA,CACA6E,WAAA;UACA4D,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA,SAAA;UACAE,KAAA,EAAAA;QACA,GACApE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAA8D,MAAA,GAAAlI,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAd,EAAA;QACApB,KAAA,CACA6E,WAAA;UACA4D,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA,MAAA;UACAQ,KAAA,EAAAA;QACA,GACApE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACAwC,QAAA2B,OAAA;MACA,IAAAvI,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAoH,SAAA;QACAxI,KAAA,CACA6E,WAAA;UACAG,GAAA,EAAA5D,EAAA;UACAqH,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAlE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAY,GAAA,GAAAhF,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAA8C,GAAA;QACA,IAAAkD,MAAA,GAAAlI,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA,EAAAd,EAAA;QACApB,KAAA,CACA6E,WAAA;UACAG,GAAA,EAAAA,GAAA;UACAyD,SAAA;UACAnH,IAAA;UACAiH,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA5D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/D,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACAuE,YAAA;MACA3I,KAAA,CAAA6E,WAAA,sBAAAP,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAgC,KAAA,GAAAuC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAuC,YAAA,GAAAgC,IAAA,CAAAlE,IAAA;QACA;MACA;IACA;IACAuI,OAAA;MACA5I,KAAA,CAAA6E,WAAA,iBAAAP,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAiC,IAAA,GAAAsC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAiC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAqH,UAAA;YACAnI,KAAA,CAAAmF,OAAA;UACA;QACA;MACA;IACA;IACAkD,QAAA;MACArI,KAAA,CAAA6E,WAAA,iBAAAP,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAiC,IAAA,GAAAsC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAsC,WAAA,GAAAiC,IAAA,CAAAlE,IAAA;UACAL,KAAA,CAAAc,QAAA;QACA;MACA;IACA;IACA+H,YAAA;MACA,IAAA7I,KAAA;MAEA8I,QAAA,CAAAC,SAAA,GAAA1D,CAAA;QACA,IAAA2D,IAAA,GAAA7C,MAAA,CAAA8C,KAAA,CAAAC,OAAA;QAEA,IAAAF,IAAA;UACAhJ,KAAA,CAAA4H,IAAA;QACA;MACA;IACA;IACAuB,SAAA3C,IAAA,EAAA4C,QAAA;MACA,IAAApJ,KAAA;MACAA,KAAA,CAAAqE,UAAA,gCAAAmC,IAAA,EAAAlC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/D,KAAA,CAAAW,QAAA,CAAAyI,QAAA;UAEApJ,KAAA,CAAAgE,QAAA,CAAAC,OAAA;QACA;UACAjE,KAAA,CAAAgE,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACA;IACAiF,aAAA;MACA;MACArJ,KAAA,CAAAiC,IAAA,IACA;QACAb,EAAA;QACAiB,KAAA;QACAuB,IAAA;QACAhC,QAAA;QACA0H,WAAA;QACAjC,KAAA;QACArC,GAAA;QACAgD,QAAA;QACAC,WAAA;QACA/C,SAAA;MACA,GACA;QACA9D,EAAA;QACAiB,KAAA;QACAuB,IAAA;QACAhC,QAAA;QACA0H,WAAA;QACAjC,KAAA;QACArC,GAAA;QACAgD,QAAA;QACAC,WAAA;QACA/C,SAAA;MACA,GACA;QACA9D,EAAA;QACAiB,KAAA;QACAuB,IAAA;QACAhC,QAAA;QACA0H,WAAA;QACAjC,KAAA;QACArC,GAAA;QACAgD,QAAA;QACAC,WAAA;QACA/C,SAAA;MACA,EACA;;MAEA;MACAlF,KAAA,CAAAgC,KAAA,IACA;QACAZ,EAAA;QACAiB,KAAA;QACAkG,OAAA;QACA3G,QAAA;QACA2H,IAAA;MACA,GACA;QACAnI,EAAA;QACAiB,KAAA;QACAkG,OAAA;QACA3G,QAAA;QACA2H,IAAA;MACA,GACA;QACAnI,EAAA;QACAiB,KAAA;QACAkG,OAAA;QACA3G,QAAA;QACA2H,IAAA;MACA,GACA;QACAnI,EAAA;QACAiB,KAAA;QACAkG,OAAA;QACA3G,QAAA;QACA2H,IAAA;MACA,EACA;;MAEA;MACAvJ,KAAA,CAAAsC,WAAA,OAAAtC,KAAA,CAAAiC,IAAA;MACAjC,KAAA,CAAAuC,YAAA,OAAAvC,KAAA,CAAAgC,KAAA;;MAEA;MACAhC,KAAA,CAAAc,QAAA;MACAd,KAAA,CAAAkC,WAAA;;MAEA;MACAiG,UAAA;QACAnI,KAAA,CAAAkH,gBAAA;MACA;IACA;IACA;IACAA,iBAAA;MACApB,OAAA,CAAAC,GAAA,sBAAA/F,KAAA,CAAAc,QAAA,kBAAAd,KAAA,CAAAkC,WAAA;MACA;MACA,IAAAlC,KAAA,CAAAc,QAAA;QACAd,KAAA,CAAAqC,KAAA,GAAArC,KAAA,CAAAgC,KAAA,CAAAhC,KAAA,CAAAc,QAAA,EAAAuB,KAAA;QACA;QACArC,KAAA,CAAAwJ,mBAAA;MACA;QACA,MAAAC,GAAA,GAAAzJ,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA;QACA,MAAAmF,KAAA,GAAAoC,GAAA,CAAAzE,GAAA,CAAAyB,KAAA,MAAAjB,MAAA,GAAAiE,GAAA,CAAAzB,QAAA,CAAAvB,KAAA,MAAAjB,MAAA,GAAAiE,GAAA,CAAAxB,WAAA,CAAAxB,KAAA,MAAAjB,MAAA;QACAxF,KAAA,CAAAqC,KAAA,GAAAoH,GAAA,CAAApH,KAAA,SAAAgF,KAAA;QACA;QACArH,KAAA,CAAA0J,iBAAA;MACA;;MAEA;MACA1J,KAAA,CAAA2J,SAAA;QACA,IAAA3J,KAAA,CAAA0E,KAAA,CAAA5C,IAAA;UACA9B,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAA6F,SAAA,GAAA3H,KAAA,CAAA0E,KAAA,CAAA5C,IAAA,CAAAgG,YAAA;QACA;MACA;IACA;IACA;IACA4B,kBAAA;MACA,MAAAE,aAAA;QACA;QAAA;QACA;UACAxI,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA;MAEA;MAEAvI,KAAA,CAAA8B,IAAA,GAAA8H,aAAA,CAAA5J,KAAA,CAAAkC,WAAA;MACA4D,OAAA,CAAAC,GAAA,cAAA/F,KAAA,CAAA8B,IAAA,CAAA0D,MAAA;IACA;IACA;IACAgE,oBAAA;MACA,MAAAK,eAAA;QACA;QAAA;QACA;UACAzI,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,EACA;QACA;QAAA;QACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA,GACA;UACAnH,EAAA;UACAkI,WAAA;UACArB,WAAA;UACAlF,MAAA;UACAV,KAAA;UACAf,IAAA;UACAiH,OAAA;QACA;MAEA;MAEAvI,KAAA,CAAA8B,IAAA,GAAA+H,eAAA,CAAA7J,KAAA,CAAAc,QAAA;MACAgF,OAAA,CAAAC,GAAA,cAAA/F,KAAA,CAAA8B,IAAA,CAAA0D,MAAA;IACA;IAIA;IACAsE,kBAAA;MACAhE,OAAA,CAAAC,GAAA;MACA/F,KAAA,CAAAwC,eAAA,IAAAxC,KAAA,CAAAwC,eAAA;MACA,IAAAxC,KAAA,CAAAwC,eAAA;QACAxC,KAAA,CAAA+J,cAAA;MACA;IACA;IAEA;IACAA,eAAA;MACAjE,OAAA,CAAAC,GAAA;MACA/F,KAAA,CAAAyC,UAAA;QACAT,KAAA,GACA;UACAZ,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,GACA;UACA3B,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,GACA;UACA3B,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,EACA;QACAL,OAAA,GACA;UACAtB,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,GACA;UACA3B,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,EACA;QACAJ,KAAA,GACA;UACAvB,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,GACA;UACA3B,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA,GACA;UACA3B,EAAA;UACAjB,IAAA;UACA4C,MAAA;QACA;MAEA;IACA;IAEA;IACAoE,mBAAA;MACA;MACA,MAAA6C,UAAA,GAAAhK,KAAA,CAAAiC,IAAA,CAAAjC,KAAA,CAAAkC,WAAA;MACA,IAAA8H,UAAA;QACAhK,KAAA,CAAA8C,iBAAA;UACA3C,IAAA,EAAA6J,UAAA,CAAA3H,KAAA,CAAA4H,OAAA;UACA3I,IAAA;UACAyB,MAAA,EAAAiH,UAAA,CAAApI,QAAA;UACAoB,KAAA;UACAC,MAAA;UACAC,YAAA;UACAC,SAAA;UACAC,MAAA;UACAC,OAAA,GACA;YACAjC,EAAA;YACAjB,IAAA;YACA+J,MAAA;YACA9G,MAAA;YACA+G,UAAA;UACA,GACA;YACA/I,EAAA;YACAjB,IAAA;YACA+J,MAAA;YACA9G,MAAA;YACA+G,UAAA;UACA,GACA;YACA/I,EAAA;YACAjB,IAAA;YACA+J,MAAA;YACA9G,MAAA;YACA+G,UAAA;UACA,EACA;UACA7G,SAAA,GACA;YACAlC,EAAA;YACAjB,IAAA;YACAmB,IAAA;YACA8I,IAAA;YACAC,UAAA;UACA,GACA;YACAjJ,EAAA;YACAjB,IAAA;YACAmB,IAAA;YACA8I,IAAA;YACAC,UAAA;UACA,GACA;YACAjJ,EAAA;YACAjB,IAAA;YACAmB,IAAA;YACA8I,IAAA;YACAC,UAAA;UACA,EACA;UACA9G,MAAA,GACA;YACAnC,EAAA;YACAiB,KAAA;YACAf,IAAA;YACA8B,MAAA;YACAkH,UAAA;UACA,GACA;YACAlJ,EAAA;YACAiB,KAAA;YACAf,IAAA;YACA8B,MAAA;YACAkH,UAAA;UACA,GACA;YACAlJ,EAAA;YACAiB,KAAA;YACAf,IAAA;YACA8B,MAAA;YACAkH,UAAA;UACA,EACA;UACA9G,QAAA,GACA;YACApC,EAAA;YACAmJ,WAAA;YACAL,MAAA;YACA9G,MAAA;YACAmG,IAAA;UACA,GACA;YACAnI,EAAA;YACAmJ,WAAA;YACAL,MAAA;YACA9G,MAAA;YACAmG,IAAA;UACA,GACA;YACAnI,EAAA;YACAmJ,WAAA;YACAL,MAAA;YACA9G,MAAA;YACAmG,IAAA;UACA;QAEA;MACA;IACA;IAEA;IACAiB,WAAAlJ,IAAA;MACA,MAAAmJ,OAAA;QACAC,KAAA;QACAC,GAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;MACA;MACA,OAAAL,OAAA,CAAAnJ,IAAA,KAAAmJ,OAAA,CAAAK,OAAA;IACA;IAEA;IACAC,mBAAA3H,MAAA;MACA,MAAA4H,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA5H,MAAA;IACA;IAEA;IACA6H,YAAAC,GAAA;MACApF,OAAA,CAAAC,GAAA,UAAAmF,GAAA,CAAA/K,IAAA;MACAH,KAAA,CAAAgE,QAAA,CAAAC,OAAA,YAAAiH,GAAA,CAAA/K,IAAA;IACA;IAEA;IACAgL,WAAAD,GAAA;MACApF,OAAA,CAAAC,GAAA,UAAAmF,GAAA,CAAA/K,IAAA;MACAH,KAAA,CAAAgE,QAAA,CAAAoH,IAAA;IACA;EACA;EACAC,cAAA;IACAvF,OAAA,CAAAC,GAAA;IACAuF,aAAA,MAAAvJ,KAAA;EACA;EACAwJ,QAAA;IACAvL,KAAA;IACA;IACAA,KAAA,CAAAqJ,YAAA;IACArJ,KAAA,CAAAmH,kBAAA;IACAnH,KAAA,CAAAmB,MAAA;IACA;IACA;IACA;IACA;IACAnB,KAAA,CAAA6I,WAAA;EACA;AACA", "ignoreList": []}]}