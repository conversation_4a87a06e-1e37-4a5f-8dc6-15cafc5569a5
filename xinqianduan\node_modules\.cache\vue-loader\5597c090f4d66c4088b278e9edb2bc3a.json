{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1748508325739}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["user.vue"], "names": [], "mappings": ";AAk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file": "user.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n    <div class=\"page-wrapper\">\r\n        <div class=\"page-container\">\r\n            <!-- 页面标题 -->\r\n            <div class=\"page-title\">\r\n                {{ this.$router.currentRoute.name }}\r\n                <el-button\r\n                        style=\"float: right\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                        icon=\"el-icon-refresh\"\r\n                >刷新\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 搜索筛选区域 -->\r\n            <div class=\"search-container\">\r\n                <el-form :model=\"search\" class=\"search-form\" label-width=\"80px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户名称\">\r\n                                <el-input\r\n                                    v-model=\"search.nickname\"\r\n                                    placeholder=\"请输入用户名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"手机号码\">\r\n                                <el-input\r\n                                    v-model=\"search.phone\"\r\n                                    placeholder=\"请输入手机号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"公司名称\">\r\n                                <el-input\r\n                                    v-model=\"search.company\"\r\n                                    placeholder=\"请输入公司名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户来源\">\r\n                                <el-select\r\n                                    v-model=\"search.yuangong_id\"\r\n                                    placeholder=\"请选择来源\"\r\n                                    clearable\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                    <el-option label=\"小程序注册\" value=\"小程序注册\"></el-option>\r\n                                    <el-option label=\"后台创建\" value=\"后台创建\"></el-option>\r\n                                    <el-option label=\"直接注册\" value=\"直接注册\"></el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系人\">\r\n                                <el-input\r\n                                    v-model=\"search.linkman\"\r\n                                    placeholder=\"请输入联系人\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系号码\">\r\n                                <el-input\r\n                                    v-model=\"search.linkphone\"\r\n                                    placeholder=\"请输入联系号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                            <el-form-item label=\"注册时间\">\r\n                                <el-date-picker\r\n                                    v-model=\"search.dateRange\"\r\n                                    type=\"daterange\"\r\n                                    range-separator=\"至\"\r\n                                    start-placeholder=\"开始日期\"\r\n                                    end-placeholder=\"结束日期\"\r\n                                    format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                            <el-form-item>\r\n                                <div class=\"search-buttons\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        icon=\"el-icon-search\"\r\n                                        @click=\"searchData()\"\r\n                                        size=\"small\">\r\n                                        搜索\r\n                                    </el-button>\r\n                                    <el-button\r\n                                        icon=\"el-icon-refresh\"\r\n                                        @click=\"resetSearch()\"\r\n                                        size=\"small\">\r\n                                        重置\r\n                                    </el-button>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n\r\n                <!-- 操作按钮区域 -->\r\n                <div class=\"action-buttons\">\r\n                    <el-button\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        icon=\"el-icon-download\"\r\n                        @click=\"exportSelectedData\">\r\n                        {{ selectedUsers.length > 0 ? `导出选中数据 (${selectedUsers.length})` : '导出全部数据' }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\" @click=\"openUpload\">\r\n                        导入用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addUser\">\r\n                        添加用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 数据表格 -->\r\n            <div class=\"data-table\">\r\n                <el-table\r\n                        :data=\"list\"\r\n                        style=\"width: 100%\"\r\n                        v-loading=\"loading\"\r\n                        @sort-change=\"handleSortChange\"\r\n                        @selection-change=\"handleSelectionChange\"\r\n                        :border=\"true\"\r\n                        :header-cell-style=\"{background:'#fafafa',color:'#606266'}\"\r\n                        ref=\"userTable\"\r\n                >\r\n                    <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n                    \r\n                    <!-- 第一字段：用户名称 -->\r\n                    <el-table-column prop=\"nickname\" label=\"用户名称\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"user-name clickable\" @click=\"viewData(scope.row.id)\">\r\n                                {{ scope.row.nickname || '未设置' }}\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第二字段：头像 -->\r\n                    <el-table-column prop=\"\" label=\"头像\" width=\"80\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"avatar-container\" @click=\"viewData(scope.row.id)\">\r\n                                <div v-if=\"scope.row.headimg && scope.row.headimg !== ''\" class=\"avatar-wrapper clickable\">\r\n                                    <img\r\n                                        class=\"user-avatar\"\r\n                                        :src=\"scope.row.headimg\"\r\n                                    />\r\n                                </div>\r\n                                <div v-else class=\"no-avatar clickable\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第三字段：注册手机号码和联系号码 -->\r\n                    <el-table-column label=\"手机号码\" min-width=\"140\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"phone-info\">\r\n                                <div class=\"phone-item clickable\" @click=\"viewData(scope.row.id)\">\r\n                                    <span class=\"phone-label\">注册:</span>\r\n                                    <span class=\"phone-number\">{{ scope.row.phone || '未设置' }}</span>\r\n                                </div>\r\n                                <div class=\"phone-item\" v-if=\"scope.row.linkphone\">\r\n                                    <span class=\"phone-label\">联系:</span>\r\n                                    <span class=\"phone-number\">{{ scope.row.linkphone }}</span>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第四字段：公司名称 -->\r\n                    <el-table-column prop=\"company\" label=\"公司名称\" sortable min-width=\"150\"></el-table-column>\r\n                    \r\n                    <!-- 保留其他字段 -->\r\n                    <el-table-column prop=\"linkman\" label=\"联系人\" sortable min-width=\"100\"></el-table-column>\r\n                    <el-table-column label=\"债务人数量\" min-width=\"100\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" :type=\"getDebtCountType(scope.row.debts)\">\r\n                                {{ getDebtCount(scope.row.debts) }}人\r\n                            </el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务总金额\" min-width=\"120\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"debt-amount\" :class=\"{ 'has-debt': getTotalDebtAmount(scope.row.debts) > 0 }\">\r\n                                ¥{{ formatAmount(getTotalDebtAmount(scope.row.debts)) }}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"end_time\" label=\"到期时间\" min-width=\"120\" sortable></el-table-column>\r\n                    <el-table-column prop=\"create_time\" label=\"注册时间\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.create_time }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最后登录\" min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.last_login_time || '未登录' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"action-buttons-table\">\r\n                                <el-button type=\"primary\" size=\"mini\" @click=\"viewData(scope.row.id)\">\r\n                                    查看\r\n                                </el-button>\r\n                                <el-button type=\"success\" size=\"mini\" @click=\"editData(scope.row.id)\">\r\n                                    编辑\r\n                                </el-button>\r\n                                <el-dropdown trigger=\"click\">\r\n                                    <el-button size=\"mini\">\r\n                                        更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu slot=\"dropdown\">\r\n                                        <el-dropdown-item @click.native=\"order(scope.row)\">制作订单</el-dropdown-item>\r\n                                        <el-dropdown-item v-if=\"is_del\" @click.native=\"delData(scope.$index, scope.row.id)\" style=\"color: #f56c6c;\">移除用户</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n                <div class=\"pagination-info\">\r\n                    <span>共 {{ total }} 条</span>\r\n                    <span>第 {{ page }} 页</span>\r\n                </div>\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"size\"\r\n                        :current-page=\"page\"\r\n                        layout=\"sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                        background\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 用户详情抽屉 -->\r\n        <el-drawer\r\n            title=\"用户详情\"\r\n            :visible.sync=\"drawerViewVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleTabSelect\">\r\n                        <el-menu-item index=\"customer\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>客户信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"member\">\r\n                            <i class=\"el-icon-medal\"></i>\r\n                            <span>会员信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"debts\">\r\n                            <i class=\"el-icon-document\"></i>\r\n                            <span>债务人信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"attachments\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>附件信息</span>\r\n                        </el-menu-item>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 编辑模式切换按钮 -->\r\n                    <div class=\"edit-mode-toggle\" v-if=\"activeTab === 'customer'\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            :icon=\"isEditMode ? 'el-icon-view' : 'el-icon-edit'\"\r\n                            @click=\"toggleEditMode\">\r\n                            {{ isEditMode ? '查看模式' : '编辑模式' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"success\"\r\n                            icon=\"el-icon-check\"\r\n                            @click=\"saveUserData\">\r\n                            保存\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"info\"\r\n                            icon=\"el-icon-close\"\r\n                            @click=\"cancelEdit\">\r\n                            取消\r\n                        </el-button>\r\n                    </div>\r\n\r\n                    <!-- 客户信息标签页 -->\r\n                    <div v-if=\"activeTab === 'customer'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                客户信息\r\n                            </div>\r\n                    <!-- 查看模式 -->\r\n                    <el-descriptions v-if=\"!isEditMode\" :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            currentUserInfo.company || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            currentUserInfo.phone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户名称\">{{\r\n                            currentUserInfo.nickname || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            currentUserInfo.linkman || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系电话\">{{\r\n                            currentUserInfo.linkphone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ currentUserInfo.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"立案专员\">{{\r\n                            currentUserInfo.lian_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"调解员\">{{\r\n                            currentUserInfo.tiaojie_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"法务专员\">{{\r\n                            currentUserInfo.fawu_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"合同上传专员\">{{\r\n                            currentUserInfo.htsczy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"律师\">{{\r\n                            currentUserInfo.ls_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"业务员\">{{\r\n                            currentUserInfo.ywy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"currentUserInfo.headimg && currentUserInfo.headimg !== ''\"\r\n                                     :src=\"currentUserInfo.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(currentUserInfo.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"注册时间\" :span=\"2\">{{\r\n                            currentUserInfo.create_time || '未知'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"最后登录\" :span=\"2\">{{\r\n                            currentUserInfo.last_login_time || '从未登录'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"会员到期\" :span=\"2\">{{\r\n                            currentUserInfo.end_time || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n\r\n                    <!-- 编辑模式 -->\r\n                    <el-form v-if=\"isEditMode\" :model=\"editForm\" :rules=\"rules\" ref=\"editForm\" label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"公司名称\" prop=\"company\">\r\n                                    <el-input v-model=\"editForm.company\" placeholder=\"请输入公司名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"手机号\" prop=\"phone\">\r\n                                    <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号\" disabled></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"用户名称\" prop=\"nickname\">\r\n                                    <el-input v-model=\"editForm.nickname\" placeholder=\"请输入用户名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                                    <el-input v-model=\"editForm.linkman\" placeholder=\"请输入联系人\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"linkphone\">\r\n                                    <el-input v-model=\"editForm.linkphone\" placeholder=\"请输入联系电话\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"登录密码\" prop=\"password\">\r\n                                    <el-input v-model=\"editForm.password\" placeholder=\"请输入新密码（留空不修改）\" type=\"password\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"调解员\" prop=\"tiaojie_id\">\r\n                                    <el-select v-model=\"editForm.tiaojie_id\" placeholder=\"请选择调解员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in tiaojies\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"法务专员\" prop=\"fawu_id\">\r\n                                    <el-select v-model=\"editForm.fawu_id\" placeholder=\"请选择法务专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in fawus\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"立案专员\" prop=\"lian_id\">\r\n                                    <el-select v-model=\"editForm.lian_id\" placeholder=\"请选择立案专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in lians\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\">\r\n                                    <el-select v-model=\"editForm.htsczy_id\" placeholder=\"请选择合同上传专用\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in htsczy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"律师\" prop=\"ls_id\">\r\n                                    <el-select v-model=\"editForm.ls_id\" placeholder=\"请选择律师\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ls\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"业务员\" prop=\"ywy_id\">\r\n                                    <el-select v-model=\"editForm.ywy_id\" placeholder=\"请选择业务员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ywy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"开始时间\" prop=\"start_time\">\r\n                                    <el-date-picker\r\n                                        v-model=\"editForm.start_time\"\r\n                                        type=\"date\"\r\n                                        format=\"yyyy-MM-dd\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        placeholder=\"选择开始时间\">\r\n                                    </el-date-picker>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"会员年限\" prop=\"year\">\r\n                                    <el-input-number\r\n                                        v-model=\"editForm.year\"\r\n                                        :min=\"0\"\r\n                                        :max=\"99\"\r\n                                        placeholder=\"请输入会员年限\">\r\n                                    </el-input-number>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-form-item label=\"头像\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"editForm.headimg && editForm.headimg !== ''\"\r\n                                     :src=\"editForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(editForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 会员信息标签页 -->\r\n                    <div v-if=\"activeTab === 'member'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-medal\"></i>\r\n                                会员信息\r\n                            </div>\r\n                            <el-descriptions :column=\"2\" border>\r\n                                <el-descriptions-item label=\"开始时间\">{{\r\n                                    currentUserInfo.start_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员年限\">{{\r\n                                    currentUserInfo.year || 0\r\n                                    }}年\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"注册时间\">{{\r\n                                    currentUserInfo.create_time || '未知'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"最后登录\">{{\r\n                                    currentUserInfo.last_login_time || '从未登录'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员到期\">{{\r\n                                    currentUserInfo.end_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 债务人信息标签页 -->\r\n                    <div v-if=\"activeTab === 'debts'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                债务人信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addDebt\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加债务人\r\n                                </el-button>\r\n                            </div>\r\n                            <el-table\r\n                                :data=\"currentUserInfo.debts || []\"\r\n                                style=\"width: 100%\"\r\n                                size=\"small\"\r\n                                v-if=\"currentUserInfo.debts && currentUserInfo.debts.length > 0\">\r\n                                <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"130\"></el-table-column>\r\n                                <el-table-column prop=\"money\" label=\"债务金额（元）\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-tag :type=\"scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info'\" size=\"small\">\r\n                                            {{ scope.row.status }}\r\n                                        </el-tag>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column label=\"操作\" width=\"150\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" size=\"mini\" @click=\"editDebt(scope.row, scope.$index)\">编辑</el-button>\r\n                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteDebt(scope.$index)\">删除</el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                            <div v-else class=\"no-data\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>暂无债务人信息</span>\r\n                                <br>\r\n                                <el-button type=\"primary\" size=\"small\" @click=\"addDebt\" style=\"margin-top: 10px;\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加第一个债务人\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 附件信息标签页 -->\r\n                    <div v-if=\"activeTab === 'attachments'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder-opened\"></i>\r\n                                附件信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addAttachment\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传附件\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"attachment-grid\">\r\n                                <!-- 身份证照片 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">身份证照片</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.idCard && currentUserInfo.attachments.idCard.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.idCard\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('idCard', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-picture\"></i>\r\n                                            <span>暂无身份证照片</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadIdCard\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传身份证\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 营业执照 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">营业执照</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.license && currentUserInfo.attachments.license.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.license\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('license', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-document\"></i>\r\n                                            <span>暂无营业执照</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadLicense\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传营业执照\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 其他附件 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">其他附件</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.others && currentUserInfo.attachments.others.length > 0\" class=\"file-list\">\r\n                                            <div v-for=\"(file, index) in currentUserInfo.attachments.others\" :key=\"index\" class=\"file-item\">\r\n                                                <div class=\"file-icon\">\r\n                                                    <i :class=\"getFileIcon(file.type)\" class=\"file-type-icon\"></i>\r\n                                                </div>\r\n                                                <div class=\"file-info\">\r\n                                                    <div class=\"file-name\">{{ file.name }}</div>\r\n                                                    <div class=\"file-meta\">\r\n                                                        <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                                                        <span class=\"upload-time\">{{ file.uploadTime }}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div class=\"file-actions\">\r\n                                                    <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(file)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                    <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('others', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-folder\"></i>\r\n                                            <span>暂无其他附件</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadOthers\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传其他附件\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n\r\n        <!-- 编辑用户抽屉 -->\r\n        <el-drawer\r\n            title=\"编辑用户\"\r\n            :visible.sync=\"drawerEditVisible\"\r\n            direction=\"rtl\"\r\n            size=\"50%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content\">\r\n                <!-- 客户信息展示 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                        客户信息\r\n                    </div>\r\n                    <el-descriptions :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            ruleForm.company\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            ruleForm.phone\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"名称\">{{\r\n                            ruleForm.nickname\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            ruleForm.linkman\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"ruleForm.headimg && ruleForm.headimg !== ''\"\r\n                                     :src=\"ruleForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(ruleForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\" :span=\"2\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ ruleForm.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 信息编辑表单 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-edit\"></i>\r\n                        信息编辑\r\n                    </div>\r\n                    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n                    </el-form>\r\n\r\n                    <!-- 保存按钮 -->\r\n                    <div class=\"drawer-footer\">\r\n                        <el-button @click=\"drawerEditVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveData()\">保 存</el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog :visible.sync=\"dialogVisible\" width=\"50%\" center>\r\n            <img :src=\"show_image\" style=\"width: 100%; height: auto;\" />\r\n        </el-dialog>\r\n\r\n        <!-- 债务人编辑抽屉 -->\r\n        <el-drawer\r\n            title=\"债务人管理\"\r\n            :visible.sync=\"debtDrawerVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDebtDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeDebtTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleDebtTabSelect\">\r\n                        <el-menu-item index=\"details\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>债务人详情</span>\r\n                        </el-menu-item>\r\n                        <el-submenu index=\"evidence\">\r\n                            <template slot=\"title\">\r\n                                <i class=\"el-icon-folder\"></i>\r\n                                <span>证据</span>\r\n                            </template>\r\n                            <el-menu-item index=\"evidence-all\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>全部</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-video\">\r\n                                <i class=\"el-icon-video-camera\"></i>\r\n                                <span>视频</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-image\">\r\n                                <i class=\"el-icon-picture\"></i>\r\n                                <span>图片</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-audio\">\r\n                                <i class=\"el-icon-microphone\"></i>\r\n                                <span>语音</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-document\">\r\n                                <i class=\"el-icon-document-copy\"></i>\r\n                                <span>文档</span>\r\n                            </el-menu-item>\r\n                        </el-submenu>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 债务人详情标签页 -->\r\n                    <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                {{ debtDialogTitle }}\r\n                            </div>\r\n                            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"120px\">\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                                            <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                                            <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"身份证号码\" prop=\"idcard\">\r\n                                            <el-input v-model=\"debtForm.idcard\" placeholder=\"请输入身份证号码\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务金额\" prop=\"money\">\r\n                                            <el-input-number\r\n                                                v-model=\"debtForm.money\"\r\n                                                :min=\"0\"\r\n                                                :precision=\"2\"\r\n                                                placeholder=\"请输入债务金额\"\r\n                                                style=\"width: 100%\">\r\n                                            </el-input-number>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"状态\" prop=\"status\">\r\n                                            <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                                                <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                                                <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                                                <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                                            </el-select>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人地址\" prop=\"address\">\r\n                                            <el-input v-model=\"debtForm.address\" placeholder=\"请输入债务人地址\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-form-item label=\"案由描述\" prop=\"case_desc\">\r\n                                    <el-input\r\n                                        v-model=\"debtForm.case_desc\"\r\n                                        type=\"textarea\"\r\n                                        :rows=\"4\"\r\n                                        placeholder=\"请输入案由描述\">\r\n                                    </el-input>\r\n                                </el-form-item>\r\n                            </el-form>\r\n                            \r\n                            <div class=\"drawer-footer\">\r\n                                <el-button @click=\"handleDebtDrawerClose\">取消</el-button>\r\n                                <el-button type=\"primary\" @click=\"saveDebt\">保存</el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 证据管理标签页 -->\r\n                    <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder\"></i>\r\n                                {{ getEvidenceTitle() }}\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传证据\r\n                                </el-button>\r\n                            </div>\r\n                            \r\n                            <!-- 证据列表 -->\r\n                            <div class=\"evidence-container\">\r\n                                <div v-if=\"getFilteredEvidence().length > 0\" class=\"evidence-grid\">\r\n                                    <div v-for=\"(evidence, index) in getFilteredEvidence()\" :key=\"index\" class=\"evidence-item\">\r\n                                        <div class=\"evidence-preview\">\r\n                                            <!-- 图片预览 -->\r\n                                            <div v-if=\"evidence.type === 'image'\" class=\"image-preview\">\r\n                                                <img :src=\"evidence.url\" @click=\"showImage(evidence.url)\" class=\"evidence-image\">\r\n                                            </div>\r\n                                            <!-- 视频预览 -->\r\n                                            <div v-else-if=\"evidence.type === 'video'\" class=\"video-preview\">\r\n                                                <video :src=\"evidence.url\" controls class=\"evidence-video\"></video>\r\n                                            </div>\r\n                                            <!-- 音频预览 -->\r\n                                            <div v-else-if=\"evidence.type === 'audio'\" class=\"audio-preview\">\r\n                                                <div class=\"audio-icon\">\r\n                                                    <i class=\"el-icon-microphone\"></i>\r\n                                                </div>\r\n                                                <audio :src=\"evidence.url\" controls class=\"evidence-audio\"></audio>\r\n                                            </div>\r\n                                            <!-- 文档预览 -->\r\n                                            <div v-else class=\"document-preview\">\r\n                                                <div class=\"document-icon\">\r\n                                                    <i :class=\"getFileIcon(evidence.type)\"></i>\r\n                                                </div>\r\n                                                <div class=\"document-name\">{{ evidence.name }}</div>\r\n                                            </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"evidence-info\">\r\n                                            <div class=\"evidence-name\">{{ evidence.name }}</div>\r\n                                            <div class=\"evidence-meta\">\r\n                                                <span class=\"evidence-size\">{{ formatFileSize(evidence.size) }}</span>\r\n                                                <span class=\"evidence-time\">{{ evidence.uploadTime }}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"evidence-actions\">\r\n                                            <el-button type=\"primary\" size=\"mini\" @click=\"downloadEvidence(evidence)\" icon=\"el-icon-download\">下载</el-button>\r\n                                            <el-button type=\"danger\" size=\"mini\" @click=\"deleteEvidence(index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-else class=\"no-evidence\">\r\n                                    <i class=\"el-icon-folder-opened\"></i>\r\n                                    <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                                    <br>\r\n                                    <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                                        <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                                    </el-button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                fileList: [], // 上传文件列表\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                currentUserInfo: {},\r\n                search: {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                selectedUsers: [], // 选中的用户列表\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                drawerViewVisible: false,\r\n                drawerEditVisible: false,\r\n                isEditMode: false,\r\n                editForm: {},\r\n                originalUserInfo: {},\r\n                activeTab: 'customer',\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                // 债务人表单相关\r\n                debtDrawerVisible: false,\r\n                debtDialogTitle: '添加债务人',\r\n                isEditingDebt: false,\r\n                editingDebtIndex: -1,\r\n                debtForm: {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                },\r\n                debtRules: {\r\n                    name: [\r\n                        { required: true, message: '请输入债务人姓名', trigger: 'blur' }\r\n                    ],\r\n                    tel: [\r\n                        { required: true, message: '请输入债务人电话', trigger: 'blur' },\r\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n                    ],\r\n                    money: [\r\n                        { required: true, message: '请输入债务金额', trigger: 'blur' }\r\n                    ],\r\n                    status: [\r\n                        { required: true, message: '请选择状态', trigger: 'change' }\r\n                    ],\r\n                    idcard: [\r\n                        { required: true, message: '请输入身份证号码', trigger: 'blur' },\r\n                        { pattern: /^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }\r\n                    ],\r\n                    address: [\r\n                        { required: true, message: '请输入地址', trigger: 'blur' }\r\n                    ],\r\n                    case_desc: [\r\n                        { required: true, message: '请输入案件描述', trigger: 'blur' }\r\n                    ]\r\n                },\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                activeDebtTab: 'details',\r\n                evidence: [],\r\n                evidenceTypes: ['all', 'video', 'image', 'audio', 'document'],\r\n                selectedEvidenceType: 'all',\r\n            };\r\n        },\r\n        mounted() {\r\n            // 使用测试数据，注释掉API调用\r\n            // this.getData();\r\n            // 添加测试数据\r\n            this.addTestData();\r\n        },\r\n        methods: {\r\n            // 获取原始测试数据\r\n            getOriginalTestData() {\r\n                return [\r\n                    {\r\n                        id: 1,\r\n                        phone: '13800138001',\r\n                        nickname: '张三',\r\n                        company: '北京科技有限公司',\r\n                        linkman: '张三',\r\n                        linkphone: '13800138001',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-12-31',\r\n                        create_time: '2023-01-15 10:30:00',\r\n                        last_login_time: '2024-01-20 15:45:00',\r\n                        headimg: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n                        license: '',\r\n                        start_time: '2023-01-15',\r\n                        year: 2,\r\n                        // 专员信息\r\n                        lian_name: '陈专员',\r\n                        tiaojie_name: '朱调解',\r\n                        fawu_name: '严法务',\r\n                        htsczy_name: '合同专员',\r\n                        ls_name: '王律师',\r\n                        ywy_name: '业务员张三',\r\n                        debts: [\r\n                            {\r\n                                name: '王某某',\r\n                                tel: '13912345678',\r\n                                money: '50000',\r\n                                status: '处理中',\r\n                                idcard: '110101199001010001',\r\n                                address: '北京市朝阳区',\r\n                                case_desc: '欠款案件'\r\n                            },\r\n                            {\r\n                                name: '李某某',\r\n                                tel: '13987654321',\r\n                                money: '30000',\r\n                                status: '已完成',\r\n                                idcard: '110101199001010002',\r\n                                address: '北京市海淀区',\r\n                                case_desc: '债权人诉讼案件'\r\n                            }\r\n                        ],\r\n                        attachments: {\r\n                            idCard: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '身份证正面.jpg',\r\n                                    uploadTime: '2024-01-15 10:30:00'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '身份证反面.jpg',\r\n                                    uploadTime: '2024-01-15 10:31:00'\r\n                                }\r\n                            ],\r\n                            license: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                                    name: '营业执照.jpg',\r\n                                    uploadTime: '2024-01-15 10:32:00'\r\n                                }\r\n                            ],\r\n                            image: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '证据图片1.jpg',\r\n                                    type: 'image',\r\n                                    size: 1024000,\r\n                                    uploadTime: '2024-01-20 14:30:00'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '证据图片2.png',\r\n                                    type: 'image',\r\n                                    size: 2048000,\r\n                                    uploadTime: '2024-01-20 14:35:00'\r\n                                }\r\n                            ],\r\n                            video: [\r\n                                {\r\n                                    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\r\n                                    name: '证据视频1.mp4',\r\n                                    type: 'video',\r\n                                    size: 10485760,\r\n                                    uploadTime: '2024-01-21 09:15:00'\r\n                                }\r\n                            ],\r\n                            audio: [\r\n                                {\r\n                                    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',\r\n                                    name: '录音证据1.wav',\r\n                                    type: 'audio',\r\n                                    size: 5242880,\r\n                                    uploadTime: '2024-01-21 10:20:00'\r\n                                }\r\n                            ],\r\n                            document: [\r\n                                {\r\n                                    name: '合同文件.pdf',\r\n                                    url: '/files/contract.pdf',\r\n                                    type: 'document',\r\n                                    size: 3145728,\r\n                                    uploadTime: '2024-01-22 11:00:00'\r\n                                },\r\n                                {\r\n                                    name: '借条扫描件.pdf',\r\n                                    url: '/files/iou.pdf',\r\n                                    type: 'document',\r\n                                    size: 1572864,\r\n                                    uploadTime: '2024-01-22 11:15:00'\r\n                                }\r\n                            ],\r\n                            others: [\r\n                                {\r\n                                    name: '其他证据.txt',\r\n                                    url: '/files/other.txt',\r\n                                    type: 'document',\r\n                                    size: 1024,\r\n                                    uploadTime: '2024-01-22 12:00:00'\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        phone: '13900139002',\r\n                        nickname: '李四',\r\n                        company: '上海贸易公司',\r\n                        linkman: '李四',\r\n                        linkphone: '13900139002',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-06-30',\r\n                        create_time: '2023-02-20 14:20:00',\r\n                        last_login_time: '2024-01-18 09:15:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-02-20',\r\n                        year: 1,\r\n                        // 专员信息\r\n                        lian_name: '李专员',\r\n                        tiaojie_name: '调解员王五',\r\n                        fawu_name: '法务李四',\r\n                        htsczy_name: '合同专员B',\r\n                        ls_name: '律师张三',\r\n                        ywy_name: '业务员李四',\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13811112222',\r\n                                money: '80000',\r\n                                status: '处理中',\r\n                                idcard: '110101199001010003',\r\n                                address: '上海市浦东新区',\r\n                                case_desc: '债务人诉讼案件'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        phone: '13700137003',\r\n                        nickname: '王五',\r\n                        company: '深圳创新科技',\r\n                        linkman: '王五',\r\n                        linkphone: '13700137003',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2025-03-15',\r\n                        create_time: '2023-03-10 16:40:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                        license: '',\r\n                        start_time: '2023-03-10',\r\n                        year: 2,\r\n                        debts: [\r\n                            {\r\n                                name: '陈某某',\r\n                                tel: '13765432109',\r\n                                money: '80000',\r\n                                status: '待处理',\r\n                                idcard: '110101199001010004',\r\n                                address: '深圳市南山区',\r\n                                case_desc: '债权人诉讼案件'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 4,\r\n                        phone: '13600136004',\r\n                        nickname: '赵六',\r\n                        company: '广州物流集团',\r\n                        linkman: '赵六',\r\n                        linkphone: '13600136004',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-09-20',\r\n                        create_time: '2023-04-05 11:30:00',\r\n                        last_login_time: '2024-01-19 14:22:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-04-05',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 5,\r\n                        phone: '13500135005',\r\n                        nickname: '孙七',\r\n                        company: '杭州电商有限公司',\r\n                        linkman: '孙七',\r\n                        linkphone: '13500135005',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-11-10',\r\n                        create_time: '2023-05-12 09:15:00',\r\n                        last_login_time: '2024-01-21 16:30:00',\r\n                        headimg: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                        license: '',\r\n                        start_time: '2023-05-12',\r\n                        year: 1,\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13654321098',\r\n                                money: '25000',\r\n                                status: '已完成',\r\n                                idcard: '110101199001010005',\r\n                                address: '杭州市西湖区',\r\n                                case_desc: '债权人诉讼案件'\r\n                            },\r\n                            {\r\n                                name: '钱某某',\r\n                                tel: '13543210987',\r\n                                money: '15000',\r\n                                status: '处理中',\r\n                                idcard: '110101199001010006',\r\n                                address: '杭州市滨江区',\r\n                                case_desc: '债务人诉讼案件'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 6,\r\n                        phone: '13400134006',\r\n                        nickname: '周八',\r\n                        company: '成都软件开发',\r\n                        linkman: '周八',\r\n                        linkphone: '13400134006',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-08-15',\r\n                        create_time: '2023-06-18 13:25:00',\r\n                        last_login_time: '2024-01-22 10:12:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-06-18',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 7,\r\n                        phone: '13300133007',\r\n                        nickname: '吴九',\r\n                        company: '武汉贸易有限公司',\r\n                        linkman: '吴九',\r\n                        linkphone: '13300133007',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-10-30',\r\n                        create_time: '2023-07-22 15:45:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                        license: '',\r\n                        start_time: '2023-07-22',\r\n                        year: 1,\r\n                        debts: []\r\n                    }\r\n                ];\r\n            },\r\n            addTestData() {\r\n                // 添加测试数据\r\n                this.list = this.getOriginalTestData();\r\n                this.total = this.list.length;\r\n                this.loading = false;\r\n            },\r\n            // 过滤测试数据（模拟搜索功能）\r\n            filterTestData() {\r\n                this.loading = true;\r\n\r\n                // 获取原始测试数据\r\n                const originalData = this.getOriginalTestData();\r\n                let filteredData = [...originalData];\r\n\r\n                // 根据搜索条件过滤数据\r\n                if (this.search.nickname) {\r\n                    const nickname = this.search.nickname.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.nickname && user.nickname.toLowerCase().includes(nickname)\r\n                    );\r\n                }\r\n\r\n                if (this.search.phone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.phone && user.phone.includes(this.search.phone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkman) {\r\n                    const linkman = this.search.linkman.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkman && user.linkman.toLowerCase().includes(linkman)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkphone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkphone && user.linkphone.includes(this.search.linkphone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.company) {\r\n                    const company = this.search.company.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.company && user.company.toLowerCase().includes(company)\r\n                    );\r\n                }\r\n\r\n                if (this.search.yuangong_id) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.yuangong_id === this.search.yuangong_id\r\n                    );\r\n                }\r\n\r\n                // 注册时间范围过滤\r\n                if (this.search.dateRange && this.search.dateRange.length === 2) {\r\n                    const startDate = new Date(this.search.dateRange[0]);\r\n                    const endDate = new Date(this.search.dateRange[1]);\r\n                    filteredData = filteredData.filter(user => {\r\n                        if (user.create_time) {\r\n                            const createDate = new Date(user.create_time.split(' ')[0]);\r\n                            return createDate >= startDate && createDate <= endDate;\r\n                        }\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                // 更新列表和总数\r\n                this.list = filteredData;\r\n                this.total = filteredData.length;\r\n                this.loading = false;\r\n\r\n                // 显示搜索结果提示\r\n                const hasSearchCondition = this.search.nickname || this.search.phone || this.search.linkman ||\r\n                                         this.search.linkphone || this.search.company || this.search.yuangong_id ||\r\n                                         (this.search.dateRange && this.search.dateRange.length === 2);\r\n\r\n                if (hasSearchCondition) {\r\n                    this.$message.success(`搜索完成，找到 ${filteredData.length} 条匹配记录`);\r\n                }\r\n            },\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                console.log('viewData called with id:', id);\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                    // 从测试数据中找到对应的用户信息\r\n                    this.currentUserInfo = this.list.find(user => user.id === id) || {};\r\n                    console.log('Found user info:', this.currentUserInfo);\r\n                    // 重置编辑模式\r\n                    this.isEditMode = false;\r\n                    this.editForm = {};\r\n                    this.originalUserInfo = {};\r\n                    // 重置到客户信息标签页\r\n                    this.activeTab = 'customer';\r\n                }\r\n                _this.drawerViewVisible = true;\r\n                console.log('Drawer should be visible:', _this.drawerViewVisible);\r\n            },\r\n            handleTabSelect(key) {\r\n                this.activeTab = key;\r\n                // 如果切换到其他标签页，退出编辑模式\r\n                if (key !== 'customer') {\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            toggleEditMode() {\r\n                if (!this.isEditMode) {\r\n                    // 进入编辑模式\r\n                    this.isEditMode = true;\r\n                    // 保存原始数据用于取消时恢复\r\n                    this.originalUserInfo = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 复制当前用户信息到编辑表单\r\n                    this.editForm = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 获取员工数据用于下拉选择\r\n                    this.getYuangongs();\r\n                } else {\r\n                    // 退出编辑模式\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            cancelEdit() {\r\n                // 恢复原始数据\r\n                this.currentUserInfo = JSON.parse(JSON.stringify(this.originalUserInfo));\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.$message.info('已取消编辑');\r\n            },\r\n            saveUserData() {\r\n                // 验证表单\r\n                this.$refs.editForm.validate((valid) => {\r\n                    if (valid) {\r\n                        // 更新当前用户信息\r\n                        this.currentUserInfo = JSON.parse(JSON.stringify(this.editForm));\r\n\r\n                        // 更新列表中的数据\r\n                        const index = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1, this.currentUserInfo);\r\n                        }\r\n\r\n                        // 退出编辑模式\r\n                        this.isEditMode = false;\r\n                        this.editForm = {};\r\n\r\n                        this.$message.success('保存成功！');\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n                _this.drawerEditVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            handleDrawerClose() {\r\n                this.drawerViewVisible = false;\r\n                this.drawerEditVisible = false;\r\n                // 重置编辑模式\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.originalUserInfo = {};\r\n                // 重置标签页\r\n                this.activeTab = 'customer';\r\n            },\r\n            // 债务人管理方法\r\n            addDebt() {\r\n                this.debtDrawerVisible = true;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                };\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.isEditingDebt = false;\r\n                this.activeDebtTab = 'details';\r\n            },\r\n            editDebt(debt, index) {\r\n                this.debtDrawerVisible = true;\r\n                this.debtForm = {\r\n                    name: debt.name,\r\n                    tel: debt.tel,\r\n                    money: parseFloat(debt.money),\r\n                    status: debt.status,\r\n                    idcard: debt.idcard || '',\r\n                    address: debt.address || '',\r\n                    case_desc: debt.case_desc || ''\r\n                };\r\n                this.debtDialogTitle = '编辑债务人';\r\n                this.isEditingDebt = true;\r\n                this.editingDebtIndex = index;\r\n                this.activeDebtTab = 'details';\r\n            },\r\n            saveDebt() {\r\n                this.$refs.debtForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const debtData = {\r\n                            name: this.debtForm.name,\r\n                            tel: this.debtForm.tel,\r\n                            money: this.debtForm.money.toString(),\r\n                            status: this.debtForm.status,\r\n                            idcard: this.debtForm.idcard,\r\n                            address: this.debtForm.address,\r\n                            case_desc: this.debtForm.case_desc\r\n                        };\r\n\r\n                        if (this.isEditingDebt) {\r\n                            // 编辑模式\r\n                            this.currentUserInfo.debts[this.editingDebtIndex] = debtData;\r\n                            this.$message.success('债务人信息修改成功！');\r\n                        } else {\r\n                            // 添加模式\r\n                            if (!this.currentUserInfo.debts) {\r\n                                this.currentUserInfo.debts = [];\r\n                            }\r\n                            this.currentUserInfo.debts.push(debtData);\r\n                            this.$message.success('债务人添加成功！');\r\n                        }\r\n\r\n                        // 更新主列表中的数据\r\n                        const userIndex = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (userIndex !== -1) {\r\n                            this.list[userIndex].debts = [...this.currentUserInfo.debts];\r\n                        }\r\n\r\n                        this.handleDebtDrawerClose();\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleDebtDrawerClose() {\r\n                this.debtDrawerVisible = false;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                };\r\n                this.isEditingDebt = false;\r\n                this.editingDebtIndex = -1;\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.activeDebtTab = 'details';\r\n                // 清除表单验证\r\n                this.$nextTick(() => {\r\n                    if (this.$refs.debtForm) {\r\n                        this.$refs.debtForm.clearValidate();\r\n                    }\r\n                });\r\n            },\r\n            handleDebtTabSelect(key) {\r\n                this.activeDebtTab = key;\r\n            },\r\n            getEvidenceTitle() {\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        return '全部证据';\r\n                    case 'evidence-video':\r\n                        return '视频证据';\r\n                    case 'evidence-image':\r\n                        return '图片证据';\r\n                    case 'evidence-audio':\r\n                        return '语音证据';\r\n                    case 'evidence-document':\r\n                        return '文档证据';\r\n                    default:\r\n                        return '证据管理';\r\n                }\r\n            },\r\n            getFilteredEvidence() {\r\n                if (!this.currentUserInfo.attachments) {\r\n                    return [];\r\n                }\r\n                \r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        // 返回所有类型的证据\r\n                        const allEvidence = [];\r\n                        ['image', 'video', 'audio', 'document'].forEach(type => {\r\n                            if (this.currentUserInfo.attachments[type]) {\r\n                                allEvidence.push(...this.currentUserInfo.attachments[type]);\r\n                            }\r\n                        });\r\n                        return allEvidence;\r\n                    case 'evidence-video':\r\n                        return this.currentUserInfo.attachments.video || [];\r\n                    case 'evidence-image':\r\n                        return this.currentUserInfo.attachments.image || [];\r\n                    case 'evidence-audio':\r\n                        return this.currentUserInfo.attachments.audio || [];\r\n                    case 'evidence-document':\r\n                        return this.currentUserInfo.attachments.document || [];\r\n                    default:\r\n                        return [];\r\n                }\r\n            },\r\n            getEvidenceTypeText() {\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        return '全部';\r\n                    case 'evidence-video':\r\n                        return '视频';\r\n                    case 'evidence-image':\r\n                        return '图片';\r\n                    case 'evidence-audio':\r\n                        return '语音';\r\n                    case 'evidence-document':\r\n                        return '文档';\r\n                    default:\r\n                        return '';\r\n                }\r\n            },\r\n            formatFileSize(size) {\r\n                if (size < 1024) {\r\n                    return size + 'B';\r\n                } else if (size < 1024 * 1024) {\r\n                    return (size / 1024).toFixed(2) + 'KB';\r\n                } else {\r\n                    return (size / (1024 * 1024)).toFixed(2) + 'MB';\r\n                }\r\n            },\r\n            getFileIcon(type) {\r\n                switch (type) {\r\n                    case 'pdf':\r\n                        return 'el-icon-document-copy';\r\n                    case 'doc':\r\n                    case 'docx':\r\n                        return 'el-icon-document';\r\n                    case 'xls':\r\n                    case 'xlsx':\r\n                        return 'el-icon-document-checked';\r\n                    case 'ppt':\r\n                    case 'pptx':\r\n                        return 'el-icon-document-copy';\r\n                    case 'txt':\r\n                        return 'el-icon-document';\r\n                    default:\r\n                        return 'el-icon-document';\r\n                }\r\n            },\r\n            showImage(url) {\r\n                this.show_image = url;\r\n                this.dialogVisible = true;\r\n            },\r\n            downloadEvidence(evidence) {\r\n                // 下载证据的逻辑\r\n            },\r\n            deleteEvidence(index) {\r\n                // 删除证据的逻辑\r\n            },\r\n            uploadEvidence() {\r\n                // 上传证据的逻辑\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n.page-title {\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-container {\r\n    background: #fff;\r\n    padding: 24px;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #e8e8e8;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-form {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.search-form .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.search-form .el-input__inner,\r\n.search-form .el-select .el-input__inner {\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.search-form .el-input__inner:focus,\r\n.search-form .el-select .el-input__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.search-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n}\r\n\r\n.search-buttons .el-button {\r\n    min-width: 80px;\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-wrap: wrap;\r\n    margin-top: 20px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-buttons .el-button {\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.data-table {\r\n    margin-top: 20px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table .el-table {\r\n    border-radius: 8px;\r\n}\r\n\r\n.data-table .el-table th {\r\n    background-color: #fafafa !important;\r\n    color: #606266 !important;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table .el-table td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 头像样式 */\r\n.avatar-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    border: 2px solid #e8e8e8;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.user-avatar {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.no-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-name.clickable {\r\n    cursor: pointer;\r\n    color: #1890ff;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.user-name.clickable:hover {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n.user-phone {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 手机号码信息样式 */\r\n.phone-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.phone-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    font-size: 13px;\r\n}\r\n\r\n.phone-item.clickable {\r\n    cursor: pointer;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.phone-item.clickable:hover {\r\n    color: #1890ff;\r\n}\r\n\r\n.phone-label {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n    min-width: 32px;\r\n    font-weight: 500;\r\n}\r\n\r\n.phone-number {\r\n    color: #262626;\r\n    font-weight: 500;\r\n}\r\n\r\n.phone-item.clickable .phone-number {\r\n    color: #1890ff;\r\n}\r\n\r\n.phone-item.clickable:hover .phone-number {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* 头像点击样式优化 */\r\n.avatar-container.clickable,\r\n.no-avatar.clickable,\r\n.avatar-wrapper.clickable {\r\n    cursor: pointer;\r\n}\r\n\r\n.avatar-container:hover .avatar-wrapper,\r\n.avatar-container:hover .no-avatar {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.no-avatar.clickable:hover {\r\n    background-color: #e6f7ff;\r\n    color: #1890ff;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons-table {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.action-buttons-table .el-button {\r\n    padding: 5px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 16px 0;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    color: #8c8c8c;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 编辑模式切换按钮样式 */\r\n.edit-mode-toggle {\r\n    display: flex;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.edit-mode-toggle .el-button {\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.custom-dialog .el-dialog__body {\r\n    padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.card {\r\n    background: #fff;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.card-header {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.el-input, .el-select {\r\n    width: 100%;\r\n}\r\n\r\n/* 搜索表单特殊样式 */\r\n.search-form .el-input,\r\n.search-form .el-select,\r\n.search-form .el-date-picker {\r\n    width: 100%;\r\n}\r\n\r\n.search-form .el-date-picker {\r\n    width: 100% !important;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.data-table .el-table tbody tr:hover {\r\n    background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n    .search-form .el-col {\r\n        margin-bottom: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .search-form .el-col {\r\n        width: 100% !important;\r\n        flex: 0 0 100% !important;\r\n        max-width: 100% !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n    }\r\n\r\n    .action-buttons {\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 16px;\r\n        margin: 8px;\r\n    }\r\n\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: center;\r\n    }\r\n\r\n    .pagination-info {\r\n        order: 2;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.drawer-content-wrapper {\r\n    display: flex;\r\n    height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n    width: 200px;\r\n    border-right: 1px solid #e6e6e6;\r\n    background-color: #fafafa;\r\n}\r\n\r\n.drawer-menu {\r\n    border-right: none;\r\n    background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.drawer-content {\r\n    flex: 1;\r\n    padding: 20px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n    height: 100%;\r\n}\r\n\r\n.drawer-content .card {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.drawer-content .card-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.drawer-content .card-header i {\r\n    color: #1890ff;\r\n    font-size: 18px;\r\n}\r\n\r\n.drawer-footer {\r\n    margin-top: 24px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n    text-align: right;\r\n}\r\n\r\n.drawer-footer .el-button {\r\n    margin-left: 12px;\r\n}\r\n\r\n/* 头像显示样式 */\r\n.avatar-display {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.detail-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    border: 2px solid #e8e8e8;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.detail-avatar:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.no-avatar-large {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 12px;\r\n    border: 2px solid #e8e8e8;\r\n}\r\n\r\n.no-avatar-large i {\r\n    font-size: 24px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n/* 抽屉内表单样式 */\r\n.drawer-content .el-form-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.drawer-content .el-descriptions-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n}\r\n\r\n/* 无数据显示样式 */\r\n.no-data {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #ccc;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-data i {\r\n    font-size: 48px;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.attachment-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n}\r\n\r\n.attachment-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n}\r\n\r\n.attachment-title {\r\n    background: #f5f5f5;\r\n    padding: 12px 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.attachment-content {\r\n    padding: 16px;\r\n}\r\n\r\n.image-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: box-shadow 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n.attachment-image {\r\n    width: 100%;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.attachment-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\r\n    color: white;\r\n    padding: 12px;\r\n    transform: translateY(100%);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover .image-overlay {\r\n    transform: translateY(0);\r\n}\r\n\r\n.image-info {\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-info .file-name {\r\n    display: block;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.image-info .upload-time {\r\n    font-size: 11px;\r\n    opacity: 0.8;\r\n}\r\n\r\n.image-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n}\r\n\r\n.image-actions .el-button {\r\n    flex: 1;\r\n    font-size: 11px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.file-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    background: #fafafa;\r\n    transition: background-color 0.2s;\r\n}\r\n\r\n.file-item:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n.file-icon {\r\n    margin-right: 12px;\r\n}\r\n\r\n.file-type-icon {\r\n    font-size: 24px;\r\n    color: #1890ff;\r\n}\r\n\r\n.file-info {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.file-info .file-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.file-meta {\r\n    display: flex;\r\n    gap: 12px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.file-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    margin-left: 12px;\r\n}\r\n\r\n.no-attachment {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #999;\r\n}\r\n\r\n.no-attachment i {\r\n    font-size: 48px;\r\n    color: #d9d9d9;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n}\r\n\r\n.no-attachment span {\r\n    display: block;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 导入用户对话框样式 */\r\n.upload-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.upload-tips {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-tips .el-alert__description p {\r\n    margin: 5px 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-actions {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-area .el-upload-dragger {\r\n    width: 100%;\r\n    height: 180px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: border-color 0.2s;\r\n}\r\n\r\n.upload-area .el-upload-dragger:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-area .el-upload-dragger .el-icon-upload {\r\n    font-size: 67px;\r\n    color: #c0c4cc;\r\n    margin: 40px 0 16px;\r\n    line-height: 50px;\r\n}\r\n\r\n.upload-area .el-upload__text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area .el-upload__text em {\r\n    color: #409eff;\r\n    font-style: normal;\r\n}\r\n\r\n.upload-area .el-upload__tip {\r\n    font-size: 12px;\r\n    color: #606266;\r\n    margin-top: 7px;\r\n}\r\n\r\n.upload-options {\r\n    text-align: center;\r\n}\r\n\r\n.upload-options .el-checkbox {\r\n    color: #606266;\r\n}\r\n\r\n/* 债务金额样式 */\r\n.debt-amount {\r\n    font-weight: 500;\r\n    color: #8c8c8c;\r\n}\r\n\r\n.debt-amount.has-debt {\r\n    color: #f56c6c;\r\n    font-weight: 600;\r\n}\r\n\r\n/* 证据管理样式 */\r\n.evidence-container {\r\n    margin-top: 16px;\r\n}\r\n\r\n.evidence-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 20px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.evidence-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.evidence-item:hover {\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n    position: relative;\r\n    height: 180px;\r\n    background: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.evidence-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.evidence-video {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.evidence-audio {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n}\r\n\r\n.audio-preview {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px;\r\n}\r\n\r\n.audio-icon {\r\n    font-size: 48px;\r\n    color: #1890ff;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.document-preview {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.document-icon {\r\n    font-size: 48px;\r\n    color: #52c41a;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.document-name {\r\n    font-size: 14px;\r\n    color: #262626;\r\n    font-weight: 500;\r\n    word-break: break-all;\r\n}\r\n\r\n.evidence-info {\r\n    padding: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.evidence-name {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 8px;\r\n    word-break: break-all;\r\n    line-height: 1.4;\r\n}\r\n\r\n.evidence-meta {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 12px;\r\n    color: #8c8c8c;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.evidence-size {\r\n    font-weight: 500;\r\n}\r\n\r\n.evidence-time {\r\n    font-style: italic;\r\n}\r\n\r\n.evidence-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.evidence-actions .el-button {\r\n    flex: 1;\r\n    font-size: 12px;\r\n}\r\n\r\n.no-evidence {\r\n    text-align: center;\r\n    padding: 60px 20px;\r\n    color: #bfbfbf;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-evidence i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 子菜单样式优化 */\r\n.drawer-menu .el-submenu .el-menu-item {\r\n    height: 40px;\r\n    line-height: 40px;\r\n    padding-left: 40px !important;\r\n    font-size: 13px;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item i {\r\n    margin-right: 6px;\r\n    font-size: 14px;\r\n}\r\n\r\n.drawer-menu .el-submenu__title {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-submenu__title i {\r\n    margin-right: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .evidence-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n    .drawer-content-wrapper {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .drawer-sidebar {\r\n        width: 100%;\r\n        border-right: none;\r\n        border-bottom: 1px solid #e6e6e6;\r\n    }\r\n    \r\n    .drawer-menu {\r\n        display: flex;\r\n        overflow-x: auto;\r\n    }\r\n    \r\n    .drawer-menu .el-menu-item,\r\n    .drawer-menu .el-submenu {\r\n        flex-shrink: 0;\r\n    }\r\n}\r\n</style>\r\n"]}]}