{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue", "mtime": 1748463119363}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["kecheng.vue"], "names": [], "mappings": ";AA+dA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "kecheng.vue", "sourceRoot": "src/views/pages/shipin", "sourcesContent": ["<template>\r\n  <div class=\"course-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-video-play\"></i>\r\n            课程列表\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和维护在线课程内容</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增课程\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入课程标题或关键词\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程类型</label>\r\n              <el-select\r\n                v-model=\"search.is_free\"\r\n                placeholder=\"请选择课程类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"免费课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"付费课程\" :value=\"2\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">热门推荐</label>\r\n              <el-select\r\n                v-model=\"search.is_hot\"\r\n                placeholder=\"请选择是否热门\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"热门课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"普通课程\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-video-play\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总课程数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon free\">\r\n              <i class=\"el-icon-present\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ freeCount }}</div>\r\n              <div class=\"stat-label\">免费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon paid\">\r\n              <i class=\"el-icon-coin\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ paidCount }}</div>\r\n              <div class=\"stat-label\">付费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon hot\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ hotCount }}</div>\r\n              <div class=\"stat-label\">热门课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 课程列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            课程列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"course-table\"\r\n            stripe\r\n            @sort-change=\"handleSortChange\"\r\n          >\r\n            <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-title-cell\">\r\n                  <div class=\"course-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"course-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-cover\">\r\n                  <img\r\n                    :src=\"scope.row.pic_path\"\r\n                    @click=\"showImage(scope.row.pic_path)\"\r\n                    class=\"cover-image\"\r\n                    :alt=\"scope.row.title\"\r\n                  />\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"price\" label=\"价格\" width=\"100\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"price-cell\">\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <span v-else class=\"price-amount\">¥{{ scope.row.price || 0 }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"status-cell\">\r\n                  <el-tag v-if=\"scope.row.is_hot === 1\" type=\"warning\" size=\"small\">\r\n                    <i class=\"el-icon-star-on\"></i>\r\n                    热门\r\n                  </el-tag>\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <el-tag v-else type=\"info\" size=\"small\">\r\n                    付费\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"course in list\" :key=\"course.id\" class=\"course-card-col\">\r\n              <div class=\"course-card\">\r\n                <div class=\"card-cover\" @click=\"showImage(course.pic_path)\">\r\n                  <img :src=\"course.pic_path\" :alt=\"course.title\" />\r\n                  <div class=\"cover-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-header\">\r\n                    <h3 class=\"card-title\" :title=\"course.title\">{{ course.title }}</h3>\r\n                    <div class=\"card-badges\">\r\n                      <el-tag v-if=\"course.is_hot === 1\" type=\"warning\" size=\"mini\">\r\n                        <i class=\"el-icon-star-on\"></i>\r\n                        热门\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-desc\" v-if=\"course.desc\">{{ course.desc }}</div>\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-price\">\r\n                      <el-tag v-if=\"course.is_free === 1\" type=\"success\" size=\"small\">\r\n                        免费课程\r\n                      </el-tag>\r\n                      <span v-else class=\"price\">¥{{ course.price || 0 }}</span>\r\n                    </div>\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ course.create_time }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-actions\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      size=\"small\"\r\n                      @click=\"editData(course.id)\"\r\n                      icon=\"el-icon-edit\"\r\n                      plain\r\n                    >\r\n                      编辑\r\n                    </el-button>\r\n                    <el-button\r\n                      type=\"danger\"\r\n                      size=\"small\"\r\n                      @click=\"delData(list.indexOf(course), course.id)\"\r\n                      icon=\"el-icon-delete\"\r\n                      plain\r\n                    >\r\n                      删除\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否免费\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"2\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"首页热门\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"0\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_free == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"课程视频\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"showImage(ruleForm.file_path)\"\r\n              >查看\r\n            </el-button> -->\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/kecheng/\",\r\n      title: \"课程\",\r\n      info: {},\r\n      filed: \"\",\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table | card\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传视频\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 免费课程数量\r\n    freeCount() {\r\n      return this.list.filter(item => item.is_free === 1).length;\r\n    },\r\n    // 付费课程数量\r\n    paidCount() {\r\n      return this.list.filter(item => item.is_free === 2).length;\r\n    },\r\n    // 热门课程数量\r\n    hotCount() {\r\n      return this.list.filter(item => item.is_hot === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理排序变化\r\n    handleSortChange({ column, prop, order }) {\r\n      console.log('排序变化:', { column, prop, order });\r\n      // 这里可以添加排序逻辑\r\n    },\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_free: 2,\r\n          file_path: \"\",\r\n          pic_path: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n\r\n        // 模拟搜索过滤\r\n        let filteredList = [\r\n          {\r\n            id: 1,\r\n            title: \"Vue.js 从入门到精通\",\r\n            desc: \"全面学习Vue.js框架，包括组件开发、路由管理、状态管理等核心概念\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Vue.js\",\r\n            create_time: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"React 实战开发教程\",\r\n            desc: \"深入学习React框架，掌握现代前端开发技能\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/2196F3/white?text=React\",\r\n            create_time: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"JavaScript 基础入门\",\r\n            desc: \"零基础学习JavaScript编程语言，为前端开发打下坚实基础\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF9800/white?text=JavaScript\",\r\n            create_time: \"2024-01-13 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"Node.js 后端开发\",\r\n            desc: \"学习使用Node.js进行后端开发，包括Express框架和数据库操作\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Node.js\",\r\n            create_time: \"2024-01-12 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"CSS3 动画与特效\",\r\n            desc: \"掌握CSS3高级特性，创建炫酷的网页动画和视觉效果\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/E91E63/white?text=CSS3\",\r\n            create_time: \"2024-01-11 11:30:00\"\r\n          },\r\n          {\r\n            id: 6,\r\n            title: \"TypeScript 进阶指南\",\r\n            desc: \"深入学习TypeScript，提升JavaScript开发的类型安全性\",\r\n            price: 249,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/3F51B5/white?text=TypeScript\",\r\n            create_time: \"2024-01-10 13:20:00\"\r\n          },\r\n          {\r\n            id: 7,\r\n            title: \"HTML5 移动端开发\",\r\n            desc: \"学习HTML5移动端开发技术，创建响应式移动应用\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF5722/white?text=HTML5\",\r\n            create_time: \"2024-01-09 15:10:00\"\r\n          },\r\n          {\r\n            id: 8,\r\n            title: \"微信小程序开发实战\",\r\n            desc: \"从零开始学习微信小程序开发，包括组件使用、API调用等\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/00BCD4/white?text=小程序\",\r\n            create_time: \"2024-01-08 10:00:00\"\r\n          },\r\n          {\r\n            id: 9,\r\n            title: \"前端工程化实践\",\r\n            desc: \"学习现代前端工程化工具和流程，提升开发效率\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/9C27B0/white?text=工程化\",\r\n            create_time: \"2024-01-07 14:30:00\"\r\n          },\r\n          {\r\n            id: 10,\r\n            title: \"Web安全基础\",\r\n            desc: \"了解常见的Web安全漏洞和防护措施，保障应用安全\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/795548/white?text=安全\",\r\n            create_time: \"2024-01-06 09:45:00\"\r\n          },\r\n          {\r\n            id: 11,\r\n            title: \"数据可视化技术\",\r\n            desc: \"学习使用D3.js、ECharts等工具创建数据可视化图表\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/607D8B/white?text=可视化\",\r\n            create_time: \"2024-01-05 12:15:00\"\r\n          },\r\n          {\r\n            id: 12,\r\n            title: \"PWA 渐进式Web应用\",\r\n            desc: \"学习PWA技术，创建类似原生应用体验的Web应用\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/8BC34A/white?text=PWA\",\r\n            create_time: \"2024-01-04 16:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 应用搜索过滤\r\n        if (_this.search.keyword) {\r\n          filteredList = filteredList.filter(item =>\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.desc.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_free !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_free === _this.search.is_free);\r\n        }\r\n\r\n        if (_this.search.is_hot !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_hot === _this.search.is_hot);\r\n        }\r\n\r\n        // 分页处理\r\n        const startIndex = (_this.page - 1) * _this.size;\r\n        const endIndex = startIndex + _this.size;\r\n        _this.list = filteredList.slice(startIndex, endIndex);\r\n        _this.total = filteredList.length;\r\n\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      } else {\r\n        if (\r\n          !file.type.split(\"/\")[1] == \"mp4\" ||\r\n          !file.type.split(\"/\")[1] == \"qlv\" ||\r\n          !file.type.split(\"/\")[1] == \"qsv\" ||\r\n          !file.type.split(\"/\")[1] == \"oga\" ||\r\n          !file.type.split(\"/\")[1] == \"flv\" ||\r\n          !file.type.split(\"/\")[1] == \"avi\" ||\r\n          !file.type.split(\"/\")[1] == \"wmv\" ||\r\n          !file.type.split(\"/\")[1] == \"rmvb\"\r\n        ) {\r\n          this.$message({\r\n            showClose: true,\r\n            message: \"请选择'.mp4,.qlv,.qsv,.oga,.flv,.avi,.wmv,.rmvb'文件\",\r\n            type: \"error\",\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 课程管理容器 */\r\n.course-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.free {\r\n  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\r\n}\r\n\r\n.stat-icon.paid {\r\n  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);\r\n}\r\n\r\n.stat-icon.hot {\r\n  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #262626;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.course-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.course-title-cell {\r\n  padding: 8px 0;\r\n}\r\n\r\n.course-title {\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.course-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  max-width: 300px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.course-cover {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.cover-image {\r\n  width: 80px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.price-amount {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 16px;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #8c8c8c;\r\n  font-size: 13px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n/* 卡片视图样式 */\r\n.card-view {\r\n  padding: 0 24px 24px;\r\n  min-height: 400px;\r\n}\r\n\r\n.course-card-col {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.course-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.course-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-cover {\r\n  position: relative;\r\n  height: 200px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.card-cover img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: all 0.3s ease;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.card-cover:hover .cover-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.card-cover:hover img {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-badges {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.card-desc {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-price .price {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 18px;\r\n}\r\n\r\n.card-time {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .course-card-col {\r\n    span: 12;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .course-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .course-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .table-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}