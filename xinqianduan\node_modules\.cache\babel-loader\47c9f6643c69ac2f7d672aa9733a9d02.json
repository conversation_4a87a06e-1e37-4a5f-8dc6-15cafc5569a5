{"remainingRequest": "H:\\fdbfront\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\yonghu\\qun.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\yonghu\\qun.vue", "mtime": 1732626900100}, {"path": "H:\\fdbfront\\babel.config.js", "mtime": 1732626900032}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748278547513}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gQCBpcyBhbiBhbGlhcyB0byAvc3JjCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImxpc3QiLAogIGNvbXBvbmVudHM6IHt9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBwcm9wczogewogICAgICAgIG11bHRpcGxlOiB0cnVlCiAgICAgIH0sCiAgICAgIGFsbFNpemU6ICJtaW5pIiwKICAgICAgbGlzdDogW10sCiAgICAgIHRvdGFsOiAxLAogICAgICBwYWdlOiAxLAogICAgICBzaXplOiAyMCwKICAgICAgc2VhcmNoOiB7CiAgICAgICAga2V5d29yZDogIiIKICAgICAgfSwKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgdXJsOiAiL3F1bi8iLAogICAgICB0aXRsZTogIuW3peS9nOe+pCIsCiAgICAgIGluZm86IHt9LAogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIHNob3dfaW1hZ2U6ICIiLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZUZvcm06IHsKICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgaXNfbnVtOiAwCiAgICAgIH0sCiAgICAgIHVpZDogW10sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdGl0bGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7floavlhpnmoIfpopgiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgZm9ybUxhYmVsV2lkdGg6ICIxMjBweCIsCiAgICAgIHVzZXJzOiBbXSwKICAgICAgbHZzaGlzOiBbXSwKICAgICAgeXVhbmdvbmdzOiBbXQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldERhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGVkaXREYXRhKGlkKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIGlmIChpZCAhPSAwKSB7CiAgICAgICAgdGhpcy5nZXRJbmZvKGlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJ1bGVGb3JtID0gewogICAgICAgICAgdGl0bGU6ICIiLAogICAgICAgICAgZGVzYzogIiIsCiAgICAgICAgICB1aWQ6ICIiLAogICAgICAgICAgcGljX3BhdGg6ICIiLAogICAgICAgICAgeXVhbmdvbmdfaWQ6ICIiLAogICAgICAgICAgbHZzaGlfaWQ6ICIiCiAgICAgICAgfTsKICAgICAgfQogICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIF90aGlzLmdldEx2c2hpKCk7CiAgICAgIF90aGlzLmdldFl1YW9uZygpOwogICAgICBfdGhpcy5nZXRVc2VyKCk7CiAgICB9LAogICAgZ2V0THZzaGkoKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmdldFJlcXVlc3QoX3RoaXMudXJsICsgImdldEx2c2hpIikudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMubHZzaGlzID0gcmVzcC5kYXRhOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0WXVhb25nKCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KF90aGlzLnVybCArICJnZXRZdWFuZ29uZyIpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzLnl1YW5nb25ncyA9IHJlc3AuZGF0YTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldFVzZXIoKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmdldFJlcXVlc3QoX3RoaXMudXJsICsgImdldEtlaHUiKS50aGVuKHJlc3AgPT4gewogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBsZXQgdXNlcnMgPSByZXNwLmRhdGE7CiAgICAgICAgICB1c2Vycy5mb3JFYWNoKChpdGVtLCBrZXkpID0+IHsKICAgICAgICAgICAgaXRlbS5sYWJlbCA9IGl0ZW0ubmlja25hbWU7CiAgICAgICAgICAgIGl0ZW0udmFsdWUgPSBpdGVtLmlkOwogICAgICAgICAgfSk7CiAgICAgICAgICBfdGhpcy51c2VycyA9IHVzZXJzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0SW5mbyhpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KF90aGlzLnVybCArICJyZWFkP2lkPSIgKyBpZCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcCkgewogICAgICAgICAgX3RoaXMucnVsZUZvcm0gPSByZXNwLmRhdGE7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBkZWxEYXRhKGluZGV4LCBpZCkgewogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbliKDpmaTor6Xkv6Hmga8/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZGVsZXRlUmVxdWVzdCh0aGlzLnVybCArICJkZWxldGU/aWQ9IiArIGlkKS50aGVuKHJlc3AgPT4gewogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5saXN0LnNwbGljZShpbmRleCwgMSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICBtZXNzYWdlOiAi5Y+W5raI5Yig6ZmkISIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgcmVmdWxzaCgpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKDApOwogICAgfSwKICAgIHNlYXJjaERhdGEoKSB7CiAgICAgIHRoaXMucGFnZSA9IDE7CiAgICAgIHRoaXMuc2l6ZSA9IDIwOwogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICBnZXREYXRhKCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgX3RoaXMucG9zdFJlcXVlc3QoX3RoaXMudXJsICsgImluZGV4P3BhZ2U9IiArIF90aGlzLnBhZ2UgKyAiJnNpemU9IiArIF90aGlzLnNpemUsIF90aGlzLnNlYXJjaCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMubGlzdCA9IHJlc3AuZGF0YTsKICAgICAgICAgIF90aGlzLnRvdGFsID0gcmVzcC5jb3VudDsKICAgICAgICB9CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBzYXZlRGF0YSgpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlIiwgdGhpcy5ydWxlRm9ybSkudGhlbihyZXNwID0+IHsKICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLnNpemUgPSB2YWw7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgaGFuZGxlU3VjY2VzcyhyZXMpIHsKICAgICAgdGhpcy5ydWxlRm9ybS5waWNfcGF0aCA9IHJlcy5kYXRhLnVybDsKICAgIH0sCiAgICBzaG93SW1hZ2UoZmlsZSkgewogICAgICB0aGlzLnNob3dfaW1hZ2UgPSBmaWxlOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzVHlwZVRydWUgPSAvXmltYWdlXC8oanBlZ3xwbmd8anBnKSQvLnRlc3QoZmlsZS50eXBlKTsKICAgICAgaWYgKCFpc1R5cGVUcnVlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5Zu+54mH5qC85byP5LiN5a+5ISIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgfSwKICAgIGRlbEltYWdlKGZpbGUsIGZpbGVOYW1lKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmdldFJlcXVlc3QoIi9VcGxvYWQvZGVsSW1hZ2U/ZmlsZU5hbWU9IiArIGZpbGUpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzLnJ1bGVGb3JtW2ZpbGVOYW1lXSA9ICIiOwogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfISIpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "components", "data", "props", "multiple", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "is_num", "uid", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "users", "lvshis", "yuangongs", "mounted", "getData", "methods", "editData", "id", "_this", "getInfo", "desc", "pic_path", "yuangong_id", "lvshi_id", "getLvshi", "get<PERSON><PERSON>ong", "getUser", "getRequest", "then", "resp", "code", "for<PERSON>ach", "item", "key", "label", "nickname", "value", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "postRequest", "count", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success"], "sources": ["src/views/pages/yonghu/qun.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n          <div class=\"el-upload__tip\">96rpx* 96rpx</div>\r\n        </el-form-item>\r\n        <el-form-item label=\"员工\" :label-width=\"formLabelWidth\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            :options=\"yuangongs\"\r\n            :props=\"props\"\r\n            filterable\r\n          ></el-cascader>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"客户\" :label-width=\"formLabelWidth\">\r\n    \r\n\t\t  <el-cascader\r\n\t\t    v-model=\"ruleForm.uid\"\r\n\t\t    :options=\"users\"\r\n\t\t    :props=\"props\"\r\n\t\t    filterable\r\n\t\t  ></el-cascader>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      props: { multiple: true },\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/qun/\",\r\n      title: \"工作群\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\t  uid:[],\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      users: [],\r\n      lvshis: [],\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n\t\t  \r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n\t\t  uid:\"\",\r\n          pic_path: \"\",\r\n          yuangong_id: \"\",\r\n          lvshi_id: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getLvshi();\r\n      _this.getYuaong();\r\n      _this.getUser();\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getLvshi\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.lvshis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getYuaong() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getYuangong\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getUser() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getKehu\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          let users = resp.data;\r\n\t\t  users.forEach((item,key) => {\r\n\t\t\t\titem.label  =  item.nickname\r\n\t\t\t\titem.value\t= item.id\r\n\t\t  \t});\r\n\t\t\t_this.users = users\r\n        }\r\n      });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n\t\t\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": "AAsJA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,KAAA;QAAAC,QAAA;MAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,MAAA;MACA;MACAC,GAAA;MACAC,KAAA;QACAR,KAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;MACAC,KAAA;MACAC,MAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QAEA,KAAAf,QAAA;UACAL,KAAA;UACAuB,IAAA;UACAhB,GAAA;UACAiB,QAAA;UACAC,WAAA;UACAC,QAAA;QACA;MACA;MAEAL,KAAA,CAAAnB,iBAAA;MACAmB,KAAA,CAAAM,QAAA;MACAN,KAAA,CAAAO,SAAA;MACAP,KAAA,CAAAQ,OAAA;IACA;IACAF,SAAA;MACA,IAAAN,KAAA;MACAA,KAAA,CAAAS,UAAA,CAAAT,KAAA,CAAAtB,GAAA,eAAAgC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAZ,KAAA,CAAAP,MAAA,GAAAkB,IAAA,CAAA5C,IAAA;QACA;MACA;IACA;IACAwC,UAAA;MACA,IAAAP,KAAA;MACAA,KAAA,CAAAS,UAAA,CAAAT,KAAA,CAAAtB,GAAA,kBAAAgC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAZ,KAAA,CAAAN,SAAA,GAAAiB,IAAA,CAAA5C,IAAA;QACA;MACA;IACA;IACAyC,QAAA;MACA,IAAAR,KAAA;MACAA,KAAA,CAAAS,UAAA,CAAAT,KAAA,CAAAtB,GAAA,cAAAgC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACA,IAAApB,KAAA,GAAAmB,IAAA,CAAA5C,IAAA;UACAyB,KAAA,CAAAqB,OAAA,EAAAC,IAAA,EAAAC,GAAA;YACAD,IAAA,CAAAE,KAAA,GAAAF,IAAA,CAAAG,QAAA;YACAH,IAAA,CAAAI,KAAA,GAAAJ,IAAA,CAAAf,EAAA;UACA;UACAC,KAAA,CAAAR,KAAA,GAAAA,KAAA;QACA;MACA;IACA;IACAS,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAS,UAAA,CAAAT,KAAA,CAAAtB,GAAA,gBAAAqB,EAAA,EAAAW,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UAEAX,KAAA,CAAAhB,QAAA,GAAA2B,IAAA,CAAA5C,IAAA;QACA;MACA;IACA;IACAoD,QAAAC,KAAA,EAAArB,EAAA;MACA,KAAAsB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAd,IAAA;QACA,KAAAe,aAAA,MAAA/C,GAAA,kBAAAqB,EAAA,EAAAW,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAc,QAAA;cACAF,IAAA;cACAnC,OAAA;YACA;YACA,KAAAlB,IAAA,CAAAwD,MAAA,CAAAP,KAAA;UACA;QACA;MACA,GACAQ,KAAA;QACA,KAAAF,QAAA;UACAF,IAAA;UACAnC,OAAA;QACA;MACA;IACA;IACAwC,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA3D,IAAA;MACA,KAAAC,IAAA;MACA,KAAAsB,OAAA;IACA;IAEAA,QAAA;MACA,IAAAI,KAAA;MAEAA,KAAA,CAAAvB,OAAA;MACAuB,KAAA,CACAiC,WAAA,CACAjC,KAAA,CAAAtB,GAAA,mBAAAsB,KAAA,CAAA3B,IAAA,cAAA2B,KAAA,CAAA1B,IAAA,EACA0B,KAAA,CAAAzB,MACA,EACAmC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAZ,KAAA,CAAA7B,IAAA,GAAAwC,IAAA,CAAA5C,IAAA;UACAiC,KAAA,CAAA5B,KAAA,GAAAuC,IAAA,CAAAuB,KAAA;QACA;QACAlC,KAAA,CAAAvB,OAAA;MACA;IACA;IACA0D,SAAA;MACA,IAAAnC,KAAA;MACA,KAAAoC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAL,WAAA,CAAAjC,KAAA,CAAAtB,GAAA,gBAAAM,QAAA,EAAA0B,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAZ,KAAA,CAAA0B,QAAA;gBACAF,IAAA;gBACAnC,OAAA,EAAAsB,IAAA,CAAA4B;cACA;cACA,KAAA3C,OAAA;cACAI,KAAA,CAAAnB,iBAAA;YACA;cACAmB,KAAA,CAAA0B,QAAA;gBACAF,IAAA;gBACAnC,OAAA,EAAAsB,IAAA,CAAA4B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAAnE,IAAA,GAAAmE,GAAA;MAEA,KAAA7C,OAAA;IACA;IACA8C,oBAAAD,GAAA;MACA,KAAApE,IAAA,GAAAoE,GAAA;MACA,KAAA7C,OAAA;IACA;IACA+C,cAAAC,GAAA;MACA,KAAA5D,QAAA,CAAAmB,QAAA,GAAAyC,GAAA,CAAA7E,IAAA,CAAAW,GAAA;IACA;IAEAmE,UAAAC,IAAA;MACA,KAAAhE,UAAA,GAAAgE,IAAA;MACA,KAAA/D,aAAA;IACA;IACAgE,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAAtB,IAAA;MACA,KAAAwB,UAAA;QACA,KAAAtB,QAAA,CAAAwB,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAApD,KAAA;MACAA,KAAA,CAAAS,UAAA,gCAAAqC,IAAA,EAAApC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAZ,KAAA,CAAAhB,QAAA,CAAAoE,QAAA;UAEApD,KAAA,CAAA0B,QAAA,CAAA2B,OAAA;QACA;UACArD,KAAA,CAAA0B,QAAA,CAAAwB,KAAA,CAAAvC,IAAA,CAAA4B,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}