{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748542316945}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSG9tZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBtb25leV9jb3VudDogMCwKICAgICAgdXNlcl9jb3VudDogMCwKICAgICAgdmlzaXRfY291bnQ6IDEyMzQsCiAgICAgIC8vIOa8lOekuuaVsOaNrgogICAgICBzZWFyY2hfY291bnQ6IDAsCiAgICAgIGV4cG9ydF9jb3VudDogMCwKICAgICAgb3JkZXJfY291bnQ6IDAsCiAgICAgIGdhb2RlX2NvdW50OiAwLAogICAgICB0ZW5neHVuX2NvdW50OiAwLAogICAgICBiYWlkdV9jb3VudDogMCwKICAgICAgc2h1bnFpd2FuZ19jb3VudDogMCwKICAgICAgc2hvd19pbWFnZTogIiIsCiAgICAgIG1lbnVzOiBbXSwKICAgICAgdXJsOiAiL1l1YW5nb25nLyIKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgbmFtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RJVExFOwogICAgfSwKICAgIC8vIOiOt+WPluW9k+WJjemhtemdoueahOeItue6p+iPnOWNleWQjeensAogICAgY3VycmVudFBhcmVudE1lbnUoKSB7CiAgICAgIGNvbnN0IGN1cnJlbnRQYXRoID0gdGhpcy4kcm91dGUucGF0aDsKCiAgICAgIC8vIOWFiOS7jui3r+eUsemFjee9ruS4reafpeaJvueItue6p+i3r+eUsQogICAgICBjb25zdCByb3V0ZXMgPSB0aGlzLiRyb3V0ZXIub3B0aW9ucy5yb3V0ZXM7CiAgICAgIGZvciAobGV0IHJvdXRlIG9mIHJvdXRlcykgewogICAgICAgIGlmIChyb3V0ZS5jaGlsZHJlbikgewogICAgICAgICAgZm9yIChsZXQgY2hpbGQgb2Ygcm91dGUuY2hpbGRyZW4pIHsKICAgICAgICAgICAgaWYgKGNoaWxkLnBhdGggPT09IGN1cnJlbnRQYXRoKSB7CiAgICAgICAgICAgICAgLy8g54m55q6K5aSE55CG77ya5aaC5p6c54i26Lev55Sx5ZCN56ew5ZKM5a2Q6Lev55Sx5ZCN56ew55u45ZCM77yM6K+05piO6L+Z5piv5LiA5Liq54us56uL55qE5Li76I+c5Y2V6aG5CiAgICAgICAgICAgICAgaWYgKHJvdXRlLm5hbWUgPT09IGNoaWxkLm5hbWUpIHsKICAgICAgICAgICAgICAgIHJldHVybiBudWxsOyAvLyDkuI3mmL7npLrniLbnuqfoj5zljZXvvIzlm6DkuLrlroPku6zmmK/lkIzkuIDkuKoKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIHJvdXRlLm5hbWU7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOi3r+eUsemFjee9ruS4reayoeaJvuWIsO+8jOWGjeS7juiPnOWNleaVsOaNruS4reafpeaJvgogICAgICBmb3IgKGxldCBtZW51IG9mIHRoaXMubWVudXMpIHsKICAgICAgICBpZiAobWVudS5jaGlsZHJlbikgewogICAgICAgICAgZm9yIChsZXQgY2hpbGQgb2YgbWVudS5jaGlsZHJlbikgewogICAgICAgICAgICBpZiAoY2hpbGQucGF0aCA9PT0gY3VycmVudFBhdGgpIHsKICAgICAgICAgICAgICAvLyDlkIzmoLfnmoTnibnmrorlpITnkIYKICAgICAgICAgICAgICBpZiAobWVudS5uYW1lID09PSBjaGlsZC5uYW1lKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIG1lbnUubmFtZTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICByZXR1cm4gbnVsbDsKICAgIH0sCiAgICAvLyDojrflj5blvZPliY3pobXpnaLlkI3np7AKICAgIGN1cnJlbnRQYWdlTmFtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHJvdXRlLm5hbWUgfHwgJ+acquefpemhtemdoic7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs6Lev55Sx5Y+Y5YyW77yM56Gu5L+d6Z2i5YyF5bGR5Y+K5pe25pu05pawCiAgICAnJHJvdXRlJyh0bywgZnJvbSkgewogICAgICAvLyDot6/nlLHlj5jljJbml7bvvIzorqHnrpflsZ7mgKfkvJroh6rliqjph43mlrDorqHnrpcKICAgICAgY29uc29sZS5sb2coJ+i3r+eUseWPmOWMljonLCBmcm9tLnBhdGgsICctPicsIHRvLnBhdGgpOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOe6r+WJjeerr+aooeW8jyAtIOebtOaOpeaPkOS+m+iPnOWNleaVsOaNrgogICAgdGhpcy5tZW51cyA9IFt7CiAgICAgIHBhdGg6ICIvamljaHUiLAogICAgICBuYW1lOiAi5Z+656GA566h55CGIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi9jb25maWciLAogICAgICAgIG5hbWU6ICLln7rnoYDorr7nva4iCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL2Jhbm5lciIsCiAgICAgICAgbmFtZTogIui9ruaSreWbviIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvbmF2IiwKICAgICAgICBuYW1lOiAi6aaW6aG15a+86IiqIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9nb25nZ2FvIiwKICAgICAgICBuYW1lOiAi5YWs5ZGKIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3lvbmdodSIsCiAgICAgIG5hbWU6ICLnlKjmiLfnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3VzZXIiLAogICAgICAgIG5hbWU6ICLnlKjmiLfliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIvZGVidCIsCiAgICAgIG5hbWU6ICLlgLrmnYPnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2RlYnRzIiwKICAgICAgICBuYW1lOiAi5YC65Yqh5Lq65YiX6KGoIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3hpYWRhbiIsCiAgICAgIG5hbWU6ICLorqLljZXnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3R5cGUiLAogICAgICAgIG5hbWU6ICLmnI3liqHnsbvlnosiCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL3Rhb2NhbiIsCiAgICAgICAgbmFtZTogIuWll+mkkOexu+WeiyIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvZGluZ2RhbiIsCiAgICAgICAgbmFtZTogIuetvue6pueUqOaIt+WIl+ihqCIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvcXVuIiwKICAgICAgICBuYW1lOiAi562+57qm5a6i5oi3576kIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3poaWZ1IiwKICAgICAgbmFtZTogIuaUr+S7mOWIl+ihqCIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvb3JkZXIiLAogICAgICAgIG5hbWU6ICLmlK/ku5jliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIvbGlhb3RpYW4iLAogICAgICBuYW1lOiAi6IGK5aSp5YiX6KGoIiwKICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgcGF0aDogIi9jaGF0IiwKICAgICAgICBuYW1lOiAi6IGK5aSp5YiX6KGoIgogICAgICB9XQogICAgfSwgewogICAgICBwYXRoOiAiL3dlbnNodWd1YW5saSIsCiAgICAgIG5hbWU6ICLmlofkuabnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2Rpbmd6aGkiLAogICAgICAgIG5hbWU6ICLlkIjlkIzlrprliLYiCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL3NoZW5oZSIsCiAgICAgICAgbmFtZTogIuWQiOWQjOWuoeaguCIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvY2F0ZSIsCiAgICAgICAgbmFtZTogIuWQiOWQjOexu+WeiyIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvaGV0b25nIiwKICAgICAgICBuYW1lOiAi5ZCI5ZCM5YiX6KGoIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9sYXd5ZXIiLAogICAgICAgIG5hbWU6ICLlj5HlvovluIjlh70iCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL2tlY2hlbmciLAogICAgICAgIG5hbWU6ICLor77nqIvliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIveGlud2VuIiwKICAgICAgbmFtZTogIuahiOS+i+euoeeQhiIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvYW5saSIsCiAgICAgICAgbmFtZTogIuahiOS+i+WIl+ihqCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi9mdXd1IiwKICAgICAgbmFtZTogIuacjeWKoeeuoeeQhiIsCiAgICAgIGNoaWxkcmVuOiBbewogICAgICAgIHBhdGg6ICIvZnV3dSIsCiAgICAgICAgbmFtZTogIuacjeWKoeWIl+ihqCIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi95dWFuZ29uZyIsCiAgICAgIG5hbWU6ICLlkZjlt6XnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3poaXdlaSIsCiAgICAgICAgbmFtZTogIuiBjOS9jSIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIveXVhbmdvbmciLAogICAgICAgIG5hbWU6ICLlkZjlt6UiCiAgICAgIH0sIHsKICAgICAgICBwYXRoOiAiL2x2c2hpIiwKICAgICAgICBuYW1lOiAi5b6L5biIIgogICAgICB9LCB7CiAgICAgICAgcGF0aDogIi9xdWFueGlhbiIsCiAgICAgICAgbmFtZTogIuadg+mZkOeuoeeQhiIKICAgICAgfV0KICAgIH0sIHsKICAgICAgcGF0aDogIi9sdnNoaWd1YW5saSIsCiAgICAgIG5hbWU6ICLkuJPkuJrnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL3podWFueWUiLAogICAgICAgIG5hbWU6ICLkuJPkuJrliJfooagiCiAgICAgIH1dCiAgICB9LCB7CiAgICAgIHBhdGg6ICIvYXJjaGl2ZSIsCiAgICAgIG5hbWU6ICLlvZLmoaPnrqHnkIYiLAogICAgICBjaGlsZHJlbjogW3sKICAgICAgICBwYXRoOiAiL2FyY2hpdmUvZmlsZSIsCiAgICAgICAgbmFtZTogIuaWh+S7tuW9kuahoyIKICAgICAgfSwgewogICAgICAgIHBhdGg6ICIvYXJjaGl2ZS9zZWFyY2giLAogICAgICAgIG5hbWU6ICLmoaPmoYjmo4DntKIiCiAgICAgIH1dCiAgICB9XTsKICAgIGNvbnNvbGUubG9nKCLoj5zljZXmlbDmja7lt7LliqDovb06IiwgdGhpcy5tZW51cyk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBzaG93UXJjb2RlKCkgewogICAgICAvLyDnuq/liY3nq6/mqKHlvI8gLSDmmL7npLrmvJTnpLrkuoznu7TnoIEKICAgICAgdGhpcy5zaG93X2ltYWdlID0gImRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU1qQXdJaUJvWldsbmFIUTlJakl3TUNJZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWo0OGNtVmpkQ0IzYVdSMGFEMGlNakF3SWlCb1pXbG5hSFE5SWpJd01DSWdabWxzYkQwaUkyWm1aaUl2UGp4MFpYaDBJSGc5SWpFd01DSWdlVDBpTVRBd0lpQjBaWGgwTFdGdVkyaHZjajBpYldsa1pHeGxJaUJtYjI1MExYTnBlbVU5SWpFMElqN21qN0xucGI3a3Vvem51N1Rub0lFOEwzUmxlSFErUEM5emRtYysiOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIG1lbnVDbGljayhpbmRleCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaChpbmRleCk7CiAgICB9LAogICAgZ2V0UXVhbnhpYW4oKSB7CiAgICAgIC8vIOe6r+WJjeerr+aooeW8jyAtIOS4jemcgOimgeWQjuerr+adg+mZkOmqjOivgQogICAgICBjb25zb2xlLmxvZygi57qv5YmN56uv5qih5byP77yM6Lez6L+H5p2D6ZmQ6aqM6K+BIik7CiAgICB9LAogICAgZ2V0Q291bnRBbGwoKSB7CiAgICAgIC8vIOe6r+WJjeerr+aooeW8jyAtIOS9v+eUqOa8lOekuuaVsOaNrgogICAgICBjb25zb2xlLmxvZygi57qv5YmN56uv5qih5byP77yM5L2/55So5ryU56S65pWw5o2uIik7CiAgICB9LAogICAgbG9nb3V0KCkgewogICAgICB0aGlzLiRzdG9yZS5jb21taXQoIklOSVRfVE9LRU4iLCAiIik7CiAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgiSU5JVF9USVRMRSIsICIiKTsKICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgIG1lc3NhZ2U6ICLpgIDlh7rmiJDlip8iCiAgICAgIH0pOwogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy4kcm91dGVyLnB1c2goIi9sb2dpbiIpOwogICAgICB9LCAxNTAwKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "data", "dialogVisible", "money_count", "user_count", "visit_count", "search_count", "export_count", "order_count", "gaode_count", "tengxun_count", "baidu_count", "shunqiwang_count", "show_image", "menus", "url", "computed", "$store", "getters", "GET_TITLE", "currentParentMenu", "currentPath", "$route", "path", "routes", "$router", "options", "route", "children", "child", "menu", "currentPageName", "watch", "to", "from", "console", "log", "mounted", "methods", "showQrcode", "menuClick", "index", "push", "getQuanxian", "getCountAll", "logout", "commit", "$message", "type", "message", "_this", "setTimeout"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <!-- 按顺序渲染菜单项 -->\r\n          <template v-for=\"(item, index) in menus\">\r\n            <!-- 如果有多个子菜单，显示为下拉菜单 -->\r\n            <el-submenu\r\n              v-if=\"item.children && item.children.length > 1\"\r\n              :key=\"'submenu-' + index\"\r\n              :index=\"item.path\"\r\n              popper-class=\"vertical-submenu\"\r\n            >\r\n              <template slot=\"title\">{{ item.name }}</template>\r\n              <el-menu-item\r\n                v-for=\"(child, indexj) in item.children\"\r\n                :key=\"indexj\"\r\n                :index=\"child.path\"\r\n              >\r\n                {{ child.name }}\r\n              </el-menu-item>\r\n            </el-submenu>\r\n            <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\r\n            <el-menu-item\r\n              v-else\r\n              :key=\"'menuitem-' + index\"\r\n              :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\r\n            >\r\n              {{ item.name }}\r\n            </el-menu-item>\r\n          </template>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">\r\n            <i class=\"el-icon-user\"></i>\r\n            管理员\r\n          </span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item>\r\n              <div @click=\"menuClick('/profile')\" class=\"dropdown-item\">\r\n                <i class=\"el-icon-user\"></i>\r\n                个人信息\r\n              </div>\r\n            </el-dropdown-item>\r\n            <el-dropdown-item>\r\n              <div @click=\"menuClick('/changePwd')\" class=\"dropdown-item\">\r\n                <i class=\"el-icon-lock\"></i>\r\n                修改密码\r\n              </div>\r\n            </el-dropdown-item>\r\n            <el-dropdown-item divided>\r\n              <div @click=\"logout()\" class=\"dropdown-item logout\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                退出登录\r\n              </div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item v-if=\"currentParentMenu\">{{ currentParentMenu }}</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{ currentPageName }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n    // 获取当前页面的父级菜单名称\r\n    currentParentMenu() {\r\n      const currentPath = this.$route.path;\r\n\r\n      // 先从路由配置中查找父级路由\r\n      const routes = this.$router.options.routes;\r\n      for (let route of routes) {\r\n        if (route.children) {\r\n          for (let child of route.children) {\r\n            if (child.path === currentPath) {\r\n              // 特殊处理：如果父路由名称和子路由名称相同，说明这是一个独立的主菜单项\r\n              if (route.name === child.name) {\r\n                return null; // 不显示父级菜单，因为它们是同一个\r\n              }\r\n              return route.name;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 如果路由配置中没找到，再从菜单数据中查找\r\n      for (let menu of this.menus) {\r\n        if (menu.children) {\r\n          for (let child of menu.children) {\r\n            if (child.path === currentPath) {\r\n              // 同样的特殊处理\r\n              if (menu.name === child.name) {\r\n                return null;\r\n              }\r\n              return menu.name;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n    // 获取当前页面名称\r\n    currentPageName() {\r\n      return this.$route.name || '未知页面';\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听路由变化，确保面包屑及时更新\r\n    '$route'(to, from) {\r\n      // 路由变化时，计算属性会自动重新计算\r\n      console.log('路由变化:', from.path, '->', to.path);\r\n    }\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/debt\",\r\n        name: \"债权管理\",\r\n        children: [\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/zhifu\",\r\n        name: \"支付列表\",\r\n        children: [\r\n          { path: \"/order\", name: \"支付列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/liaotian\",\r\n        name: \"聊天列表\",\r\n        children: [\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" },\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" },\r\n          { path: \"/lvshi\", name: \"律师\" },\r\n          { path: \"/quanxian\", name: \"权限管理\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"专业管理\",\r\n        children: [\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/archive\",\r\n        name: \"归档管理\",\r\n        children: [\r\n          { path: \"/archive/file\", name: \"文件归档\" },\r\n          { path: \"/archive/search\", name: \"档案检索\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n  min-width: 1400px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 140px;\r\n  min-width: 140px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n  min-width: 0;\r\n  max-width: calc(100% - 300px);\r\n  margin-right: 20px;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  flex-wrap: nowrap !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n  flex-wrap: nowrap !important;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 8px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n  font-size: 13px;\r\n  min-width: auto;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 8px;\r\n  border-bottom: none !important;\r\n  font-size: 13px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-submenu__drop-down .el-menu .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* 强制下拉菜单容器为垂直布局 */\r\n.el-submenu__drop-down,\r\n.el-menu--horizontal .el-submenu__drop-down,\r\n.el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 确保子菜单内的ul也是垂直的 */\r\n.el-submenu__drop-down ul,\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  list-style: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制覆盖任何可能的inline样式 */\r\n.el-submenu__drop-down .el-menu-item[style] {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  position: relative !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24px;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb {\r\n  font-size: 14px;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item {\r\n  color: #666;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item:last-child .el-breadcrumb__inner {\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item .el-breadcrumb__inner.is-link {\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item .el-breadcrumb__inner.is-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 使用更高的CSS优先级 */\r\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对自定义popper-class的样式 - 美化版本 */\r\n.vertical-submenu {\r\n  display: block !important;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\r\n  overflow: hidden !important;\r\n  min-width: 180px !important;\r\n  padding: 8px 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 44px !important;\r\n  line-height: 44px !important;\r\n  padding: 0 20px !important;\r\n  margin: 2px 8px !important;\r\n  background-color: rgba(255, 255, 255, 0.95) !important;\r\n  color: #2c3e50 !important;\r\n  text-align: left !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  font-weight: 500 !important;\r\n  font-size: 14px !important;\r\n  transition: all 0.3s ease !important;\r\n  position: relative !important;\r\n  width: calc(100% - 16px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  color: #fff !important;\r\n  transform: translateX(4px) !important;\r\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:active {\r\n  transform: translateX(2px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:last-child {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n/* 添加一些动画效果 */\r\n.vertical-submenu .el-menu-item::before {\r\n  content: '' !important;\r\n  position: absolute !important;\r\n  left: 0 !important;\r\n  top: 50% !important;\r\n  transform: translateY(-50%) !important;\r\n  width: 3px !important;\r\n  height: 0 !important;\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border-radius: 0 2px 2px 0 !important;\r\n  transition: height 0.3s ease !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover::before {\r\n  height: 20px !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 100px;\r\n  min-width: 100px;\r\n  text-align: right;\r\n  position: relative;\r\n  z-index: 1001;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 10px;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  transition: background-color 0.3s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 0;\r\n  color: #333;\r\n  transition: color 0.3s;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.dropdown-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.dropdown-item i {\r\n  font-size: 14px;\r\n  width: 16px;\r\n}\r\n\r\n.dropdown-item.logout {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.dropdown-item.logout:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 下拉菜单容器样式 */\r\n.el-dropdown-menu {\r\n  min-width: 140px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item {\r\n  padding: 0 16px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 主内容区域样式 - 新UI风格 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f5f5f5;\r\n  padding: 16px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 页面内容容器 */\r\n.main-content .page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 标签页导航样式 */\r\n.tab-navigation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tab-navigation .el-tabs__header {\r\n  margin: 0;\r\n}\r\n\r\n.tab-navigation .el-tabs__nav-wrap::after {\r\n  height: 1px;\r\n  background-color: #e8e8e8;\r\n}\r\n\r\n/* 搜索筛选区域样式 */\r\n.search-section {\r\n  background: #fafafa;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-left: auto;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table {\r\n  margin-top: 16px;\r\n}\r\n\r\n.data-table .el-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n}\r\n\r\n.data-table .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.data-table .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding: 16px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .top-header {\r\n    flex-direction: column;\r\n    height: auto;\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-center {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .top-menu {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 用户信息和下拉菜单样式 */\r\n.header-right {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  transition: background-color 0.3s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 0;\r\n  color: #333;\r\n  transition: color 0.3s;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.dropdown-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.dropdown-item i {\r\n  font-size: 14px;\r\n  width: 16px;\r\n}\r\n\r\n.dropdown-item.logout {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.dropdown-item.logout:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 下拉菜单容器样式 */\r\n.el-dropdown-menu {\r\n  min-width: 140px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item {\r\n  padding: 0 16px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n</style>\r\n"], "mappings": ";AAiGA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MAAA;MACAC,YAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACAhB,KAAA;MACA,YAAAiB,MAAA,CAAAC,OAAA,CAAAC,SAAA;IACA;IACA;IACAC,kBAAA;MACA,MAAAC,WAAA,QAAAC,MAAA,CAAAC,IAAA;;MAEA;MACA,MAAAC,MAAA,QAAAC,OAAA,CAAAC,OAAA,CAAAF,MAAA;MACA,SAAAG,KAAA,IAAAH,MAAA;QACA,IAAAG,KAAA,CAAAC,QAAA;UACA,SAAAC,KAAA,IAAAF,KAAA,CAAAC,QAAA;YACA,IAAAC,KAAA,CAAAN,IAAA,KAAAF,WAAA;cACA;cACA,IAAAM,KAAA,CAAA3B,IAAA,KAAA6B,KAAA,CAAA7B,IAAA;gBACA;cACA;cACA,OAAA2B,KAAA,CAAA3B,IAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,SAAA8B,IAAA,SAAAhB,KAAA;QACA,IAAAgB,IAAA,CAAAF,QAAA;UACA,SAAAC,KAAA,IAAAC,IAAA,CAAAF,QAAA;YACA,IAAAC,KAAA,CAAAN,IAAA,KAAAF,WAAA;cACA;cACA,IAAAS,IAAA,CAAA9B,IAAA,KAAA6B,KAAA,CAAA7B,IAAA;gBACA;cACA;cACA,OAAA8B,IAAA,CAAA9B,IAAA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;IACA+B,gBAAA;MACA,YAAAT,MAAA,CAAAtB,IAAA;IACA;EACA;EACAgC,KAAA;IACA;IACA,QAAAV,CAAAW,EAAA,EAAAC,IAAA;MACA;MACAC,OAAA,CAAAC,GAAA,UAAAF,IAAA,CAAAX,IAAA,QAAAU,EAAA,CAAAV,IAAA;IACA;EACA;EACAc,QAAA;IACA;IACA,KAAAvB,KAAA,IACA;MACAS,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA;IAEA,GACA;MACAuB,IAAA;MACAvB,IAAA;MACA4B,QAAA,GACA;QAAAL,IAAA;QAAAvB,IAAA;MAAA,GACA;QAAAuB,IAAA;QAAAvB,IAAA;MAAA;IAEA,EACA;IAEAmC,OAAA,CAAAC,GAAA,kBAAAtB,KAAA;EACA;EACAwB,OAAA;IACAC,WAAA;MACA;MACA,KAAA1B,UAAA;MACA,KAAAX,aAAA;IACA;IACAsC,UAAAC,KAAA;MACA,KAAAhB,OAAA,CAAAiB,IAAA,CAAAD,KAAA;IACA;IACAE,YAAA;MACA;MACAR,OAAA,CAAAC,GAAA;IACA;IACAQ,YAAA;MACA;MACAT,OAAA,CAAAC,GAAA;IACA;IACAS,OAAA;MACA,KAAA5B,MAAA,CAAA6B,MAAA;MACA,KAAA7B,MAAA,CAAA6B,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,IAAAC,KAAA;MACAC,UAAA;QACAD,KAAA,CAAAzB,OAAA,CAAAiB,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}