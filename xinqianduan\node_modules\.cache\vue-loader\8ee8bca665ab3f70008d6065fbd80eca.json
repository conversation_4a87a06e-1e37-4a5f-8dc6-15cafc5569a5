{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue?vue&type=style&index=0&id=6879a05a&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue", "mtime": 1748483892216}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["xinwen.vue"], "names": [], "mappings": ";AA+YA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "xinwen.vue", "sourceRoot": "src/views/pages/xinwen", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <h2 class=\"page-title\">{{ this.$router.currentRoute.name }}</h2>\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"search-section\">\r\n        <div class=\"search-controls\">\r\n          <el-input\r\n            placeholder=\"请输入案例标题进行搜索\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <el-button\r\n              slot=\"append\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n            ></el-button>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"action-controls\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n          >\r\n            新增案例\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-section\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"data-table\"\r\n          stripe\r\n          @sort-change=\"handleSortChange\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"案例标题\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"case-title-cell\">\r\n                <div class=\"case-title\">{{ scope.row.title }}</div>\r\n                <div class=\"case-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"case-cover\" v-if=\"scope.row.pic_path\">\r\n                <img\r\n                  :src=\"scope.row.pic_path\"\r\n                  @click=\"showImage(scope.row.pic_path)\"\r\n                  class=\"cover-image\"\r\n                  :alt=\"scope.row.title\"\r\n                />\r\n              </div>\r\n              <span v-else class=\"no-cover\">暂无封面</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"封面\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          >\r\n            <template slot=\"append\">280rpx*200rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/anli/\",\r\n      title: \"案例\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n      isClear: false, // 添加编辑器清空标志\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          pic_path: \"\",\r\n          content: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp && resp.code == 200) {\r\n            // 确保 list 始终是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n          } else {\r\n            // 如果请求失败，设置为空数组\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据失败:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSortChange(column) {\r\n      // 处理排序变化\r\n      console.log('排序变化:', column);\r\n    },\r\n    change(val) {\r\n      // 编辑器内容变化回调\r\n      this.ruleForm.content = val;\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 搜索和操作区域 */\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  gap: 16px;\r\n}\r\n\r\n.search-controls {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.data-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 案例标题单元格 */\r\n.case-title-cell {\r\n  padding: 4px 0;\r\n}\r\n\r\n.case-title {\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.case-title:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.case-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 封面样式 */\r\n.case-cover {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.cover-image {\r\n  width: 60px;\r\n  height: 40px;\r\n  object-fit: cover;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.no-cover {\r\n  color: #d9d9d9;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.edit-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 分页容器 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-controls {\r\n    max-width: none;\r\n  }\r\n\r\n  .action-controls {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table ::v-deep .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table ::v-deep .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  color: #262626;\r\n}\r\n\r\n.data-table ::v-deep .el-table tr:hover > td {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped:hover td {\r\n  background-color: #f0f0f0;\r\n}\r\n</style>\r\n"]}]}