{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\components\\DebtDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\components\\DebtDetail.vue", "mtime": 1748425644019}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEZWJ0RGV0YWlsJywKICBwcm9wczogewogICAgaWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGluZm86IFtdIC8vIOeUqOS6juWtmOWCqOaOpeWPo+i/lOWbnueahOaVsOaNrgogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBpZDogewogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIC8vIOe7hOS7tuWIm+W7uuaXtueri+WNs+inpuWPkQogICAgICBoYW5kbGVyKG5ld0lkKSB7CiAgICAgICAgdGhpcy5nZXRJbmZvKG5ld0lkKTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0SW5mbyhpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KCIvZGVidC92aWV3P2lkPSIgKyBpZCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMuaW5mbyA9IHJlc3AuZGF0YTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBkb3dubG9hZEZpbGVzKGltZ3MpIHsKICAgICAgaW1ncy5mb3JFYWNoKGZpbGUgPT4gewogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7CiAgICAgICAgbGluay5ocmVmID0gZmlsZS5wYXRoOwogICAgICAgIGxpbmsuZG93bmxvYWQgPSBmaWxlLm5hbWU7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICB9KTsKICAgIH0sCiAgICBleHBvcnRzOiBmdW5jdGlvbiAoKSB7CiAgICAgIC8v5a+85Ye66KGo5qC8CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIGxvY2F0aW9uLmhyZWYgPSAiL2FkbWluL2RlYnQvdmlldz90b2tlbj0iICsgX3RoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RPS0VOICsgIiZleHBvcnQ9MSZpZD0iICsgX3RoaXMucnVsZUZvcm0uaWQ7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "props", "id", "type", "String", "required", "data", "info", "watch", "immediate", "handler", "newId", "getInfo", "methods", "_this", "getRequest", "then", "resp", "code", "$message", "message", "msg", "downloadFiles", "imgs", "for<PERSON>ach", "file", "link", "document", "createElement", "href", "path", "download", "click", "exports", "location", "$store", "getters", "GET_TOKEN", "ruleForm"], "sources": ["src/components/DebtDetail.vue"], "sourcesContent": ["<template>\r\n    <el-row>\r\n    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" style=\"margin-bottom: 10px;\" @click=\"exports\">导出跟进记录</el-button>\r\n\r\n    <el-descriptions title=\"债务信息\">\r\n        <el-descriptions-item label=\"用户姓名\">{{info.nickname}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人姓名\">{{info.name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人电话\">{{info.tel}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人地址\">{{info.address}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务金额\">{{info.money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"合计回款\">{{info.back_money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"未回款\">{{info.un_money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"提交时间\">{{info.ctime}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"最后一次修改时间\">{{info.utime}}</el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"债务人身份信息\" :colon=\"false\">\r\n        <el-descriptions-item><div style=\"width: 100%;display: table-cell;\" v-if=\"info.cards[0]\">\r\n            <div style=\"float: left;margin-left:2px;\"\r\n                 v-for=\"(item4, index4) in info.cards\"\r\n                 :key=\"index4\"\r\n                 class=\"image-list\"\r\n            >\r\n                <img :src=\"item4\" style=\"width: 100px; height: 100px\" @click=\"showImage(item4)\" mode=\"aspectFit\" />\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"案由\" :colon=\"false\">\r\n        <el-descriptions-item>{{info.case_des}}</el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"证据图片\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <el-button v-if=\"info.images[0]\" style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" @click=\"downloadFiles(info.images_download)\">全部下载</el-button>\r\n            <div style=\"width: 100%;display: table-cell;\" v-if=\"info.images[0]\">\r\n            <div style=\"float: left;margin-left:2px;\"\r\n                 v-for=\"(item2, index2) in info.images\"\r\n                 :key=\"index2\"\r\n                 class=\"image-list\"\r\n            >\r\n                <!--<img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />-->\r\n                <el-image\r\n                        style=\"width: 100px; height: 100px\"\r\n                        :src=\"item2\"\r\n                        :preview-src-list=\"info.images\">\r\n                </el-image>\r\n                <a style=\"\" :href=\"item2\" target=\"_blank\" :download=\"'evidence.'+item2.split('.')[1]\">下载</a>\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"证据文件\" v-if=\"info.attach_path[0]\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n            <div\r\n                    v-for=\"(item3, index3) in info.attach_path\"\r\n                    :key=\"index3\"\r\n            >\r\n                <div v-if=\"item3\">\r\n                    <div >文件{{ index3 + 1 + '->' + item3.split(\".\")[1] }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">下载</a></div><br />\r\n                </div>\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"跟进记录\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <el-table\r\n                    :data=\"info.debttrans\"\r\n                    style=\"width: 100%; margin-top: 10px\"\r\n                    v-loading=\"loading\"\r\n                    size=\"mini\"\r\n            >\r\n                <el-table-column prop=\"day\" label=\"跟进日期\"> </el-table-column>\r\n                <el-table-column prop=\"ctime\" label=\"提交时间\"> </el-table-column>\r\n                <el-table-column prop=\"au_id\" label=\"操作人员\"> </el-table-column>\r\n                <el-table-column prop=\"type\" label=\"进度类型\"> </el-table-column>\r\n                <el-table-column prop=\"total_price\" label=\"费用金额/手续费\"> </el-table-column>\r\n                <el-table-column prop=\"content\" label=\"费用内容\"> </el-table-column>\r\n                <el-table-column prop=\"rate\" label=\"手续费比率\"></el-table-column>\r\n                <el-table-column prop=\"back_money\" label=\"回款金额\"> </el-table-column>\r\n                <el-table-column prop=\"pay_type\" label=\"支付状态\"> </el-table-column>\r\n                <el-table-column prop=\"pay_time\" label=\"支付时间\"> </el-table-column>\r\n                <el-table-column prop=\"pay_order_type\" label=\"支付方式\"> </el-table-column>\r\n                <el-table-column prop=\"desc\" label=\"进度描述\"> </el-table-column>\r\n                <el-table-column fixed=\"right\" label=\"操作\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button\r\n                                @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                                type=\"text\"\r\n                                size=\"small\"\r\n                        >\r\n                            移除\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table></el-descriptions-item>\r\n    </el-descriptions>\r\n    </el-row>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'DebtDetail',\r\n    props: {\r\n      id: {\r\n        type: String,\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: [] // 用于存储接口返回的数据\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        _this.getRequest(\"/debt/view?id=\" + id).then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            _this.$message({\r\n              type: \"error\",\r\n              message: resp.msg,\r\n            });\r\n          }\r\n        });\r\n      },\r\n        downloadFiles(imgs) {\r\n            imgs.forEach((file) => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = file.path;\r\n                link.download = file.name;\r\n                link.click();\r\n            });\r\n        },\r\n        exports:function () { //导出表格\r\n            let _this = this;\r\n            location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n        }\r\n    }\r\n  }\r\n</script>\r\n"], "mappings": "AAkGA;EACAA,IAAA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,IAAA;IACA;EACA;EACAC,KAAA;IACAN,EAAA;MACAO,SAAA;MAAA;MACAC,QAAAC,KAAA;QACA,KAAAC,OAAA,CAAAD,KAAA;MACA;IACA;EACA;EACAE,OAAA;IACAD,QAAAV,EAAA;MACA,IAAAY,KAAA;MACAA,KAAA,CAAAC,UAAA,oBAAAb,EAAA,EAAAc,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAP,IAAA,GAAAU,IAAA,CAAAX,IAAA;QACA;UACAQ,KAAA,CAAAK,QAAA;YACAhB,IAAA;YACAiB,OAAA,EAAAH,IAAA,CAAAI;UACA;QACA;MACA;IACA;IACAC,cAAAC,IAAA;MACAA,IAAA,CAAAC,OAAA,CAAAC,IAAA;QACA,MAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAJ,IAAA,CAAAK,IAAA;QACAJ,IAAA,CAAAK,QAAA,GAAAN,IAAA,CAAAzB,IAAA;QACA0B,IAAA,CAAAM,KAAA;MACA;IACA;IACAC,OAAA,WAAAA,CAAA;MAAA;MACA,IAAAnB,KAAA;MACAoB,QAAA,CAAAL,IAAA,+BAAAf,KAAA,CAAAqB,MAAA,CAAAC,OAAA,CAAAC,SAAA,qBAAAvB,KAAA,CAAAwB,QAAA,CAAApC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}