{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue", "mtime": 1748540171917}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "specialty", "loading", "zhuanyes", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "viewMode", "<PERSON><PERSON><PERSON><PERSON>", "originalList", "ruleForm", "is_num", "rules", "required", "message", "trigger", "yuangong_id", "lvsuo", "age", "laywer_card", "phone", "pic_path", "card_path", "form<PERSON>abe<PERSON><PERSON>", "field", "yuangongs", "computed", "activeCount", "filter", "lawyer", "status", "length", "specialtyCount", "specialties", "Set", "for<PERSON>ach", "Array", "isArray", "add", "firmCount", "firms", "mounted", "loadTestData", "getZhuanyes", "getData", "methods", "switchView", "mode", "$message", "success", "getSpecialtyNames", "specialtyIds", "map", "id", "find", "z", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "resetSearch", "exportData", "handleSelectionChange", "selection", "create_time", "changeField", "getLvshi", "_this", "getRequest", "then", "resp", "code", "editData", "getInfo", "address", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "splice", "catch", "refulsh", "$router", "go", "searchData", "filterTestData", "filteredList", "toLowerCase", "includes", "setTimeout", "postRequest", "count", "console", "log", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName"], "sources": ["src/views/pages/lvshi/lvshi.vue"], "sourcesContent": ["<template>\r\n  <div class=\"lawyer-management\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <div class=\"page-title\">\r\n            <i class=\"el-icon-user-solid\"></i>\r\n            <span>律师管理</span>\r\n          </div>\r\n          <div class=\"page-subtitle\">管理系统中的律师信息和专业资质</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增律师\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            circle\r\n            class=\"refresh-btn\"\r\n          ></el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div class=\"stats-cards\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon lawyer-icon\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ total }}</div>\r\n          <div class=\"stat-label\">律师总数</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon active-icon\">\r\n          <i class=\"el-icon-check\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ activeCount }}</div>\r\n          <div class=\"stat-label\">在职律师</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon specialty-icon\">\r\n          <i class=\"el-icon-medal\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ specialtyCount }}</div>\r\n          <div class=\"stat-label\">专业领域</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon firm-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ firmCount }}</div>\r\n          <div class=\"stat-label\">合作律所</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <el-card shadow=\"never\" class=\"content-card\">\r\n        <!-- 搜索和筛选区域 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-left\">\r\n            <div class=\"search-input-group\">\r\n              <el-input\r\n                placeholder=\"搜索律师姓名、律所、证号...\"\r\n                v-model=\"search.keyword\"\r\n                class=\"search-input\"\r\n                clearable\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  icon=\"el-icon-search\"\r\n                  @click=\"searchData()\"\r\n                ></el-button>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-select\r\n              v-model=\"search.specialty\"\r\n              placeholder=\"专业领域\"\r\n              clearable\r\n              class=\"filter-select\"\r\n              @change=\"searchData\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in zhuanyes\"\r\n                :key=\"item.id\"\r\n                :label=\"item.title\"\r\n                :value=\"item.id\"\r\n              ></el-option>\r\n            </el-select>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"resetSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视图切换 -->\r\n        <div class=\"view-controls\">\r\n          <div class=\"view-tabs\">\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'table' }\"\r\n              @click=\"switchView('table')\"\r\n            >\r\n              <i class=\"el-icon-menu\"></i>\r\n              <span>列表视图</span>\r\n            </div>\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'card' }\"\r\n              @click=\"switchView('card')\"\r\n            >\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              <span>卡片视图</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"view-actions\">\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-download\"\r\n              size=\"small\"\r\n              @click=\"exportData\"\r\n            >\r\n              导出\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <!-- 列表视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"lawyer-table\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n\r\n            <el-table-column label=\"律师信息\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"lawyer-info-cell\">\r\n                  <div class=\"lawyer-avatar\">\r\n                    <el-avatar\r\n                      :src=\"scope.row.pic_path\"\r\n                      :size=\"50\"\r\n                      @click.native=\"showImage(scope.row.pic_path)\"\r\n                      class=\"clickable-avatar\"\r\n                    >\r\n                      <i class=\"el-icon-user-solid\"></i>\r\n                    </el-avatar>\r\n                  </div>\r\n                  <div class=\"lawyer-details\">\r\n                    <div class=\"lawyer-name\">{{ scope.row.title }}</div>\r\n                    <div class=\"lawyer-card\">证号：{{ scope.row.laywer_card || '暂无' }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"律所\" prop=\"lvsuo\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"firm-info\">\r\n                  <i class=\"el-icon-office-building\"></i>\r\n                  <span>{{ scope.row.lvsuo || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"专业领域\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"specialties\">\r\n                  <el-tag\r\n                    v-for=\"specialty in getSpecialtyNames(scope.row.zhuanyes)\"\r\n                    :key=\"specialty\"\r\n                    size=\"mini\"\r\n                    class=\"specialty-tag\"\r\n                  >\r\n                    {{ specialty }}\r\n                  </el-tag>\r\n                  <span v-if=\"!scope.row.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"联系方式\" prop=\"phone\" min-width=\"130\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"contact-info\">\r\n                  <i class=\"el-icon-phone\"></i>\r\n                  <span>{{ scope.row.phone || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"证书\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.card_path\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"showImage(scope.row.card_path)\"\r\n                  class=\"view-cert-btn\"\r\n                >\r\n                  查看\r\n                </el-button>\r\n                <span v-else class=\"no-data\">暂无</span>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"注册时间\" prop=\"create_time\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-info\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(scope.row.create_time) }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    circle\r\n                    title=\"编辑\"\r\n                  ></el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    circle\r\n                    title=\"删除\"\r\n                  ></el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-else class=\"card-view\">\r\n          <div class=\"lawyer-cards\" v-loading=\"loading\">\r\n            <div\r\n              v-for=\"lawyer in list\"\r\n              :key=\"lawyer.id\"\r\n              class=\"lawyer-card\"\r\n            >\r\n              <div class=\"card-header\">\r\n                <div class=\"lawyer-avatar-large\">\r\n                  <el-avatar\r\n                    :src=\"lawyer.pic_path\"\r\n                    :size=\"80\"\r\n                    @click.native=\"showImage(lawyer.pic_path)\"\r\n                    class=\"clickable-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"lawyer-basic-info\">\r\n                  <div class=\"lawyer-name-large\">{{ lawyer.title }}</div>\r\n                  <div class=\"lawyer-firm\">\r\n                    <i class=\"el-icon-office-building\"></i>\r\n                    <span>{{ lawyer.lvsuo || '暂无律所' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-phone\"></i>\r\n                    联系方式\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.phone || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    证件号码\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.laywer_card || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-collection-tag\"></i>\r\n                    专业领域\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-tag\r\n                      v-for=\"specialty in getSpecialtyNames(lawyer.zhuanyes)\"\r\n                      :key=\"specialty\"\r\n                      size=\"mini\"\r\n                      class=\"specialty-tag\"\r\n                    >\r\n                      {{ specialty }}\r\n                    </el-tag>\r\n                    <span v-if=\"!lawyer.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    执业证书\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-button\r\n                      v-if=\"lawyer.card_path\"\r\n                      type=\"text\"\r\n                      size=\"mini\"\r\n                      @click=\"showImage(lawyer.card_path)\"\r\n                      class=\"view-cert-btn\"\r\n                    >\r\n                      查看证书\r\n                    </el-button>\r\n                    <span v-else class=\"no-data\">暂无证书</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-footer\">\r\n                <div class=\"register-time\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(lawyer.create_time) }}</span>\r\n                </div>\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(lawyer.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(lawyer), lawyer.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '姓名'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"绑定员工\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"yuangong_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option-group\r\n              v-for=\"group in yuangongs\"\r\n              :key=\"group.label\"\r\n              :label=\"group.label\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in group.options\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-option-group>\r\n          </el-select>\r\n          <!-- <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in yuangongs\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select> -->\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"专业\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhuanyes\"\r\n        >\r\n          <el-select v-model=\"ruleForm.zhuanyes\" multiple placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in zhuanyes\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"律所\" :label-width=\"formLabelWidth\" prop=\"lvsuo\">\r\n          <el-input v-model=\"ruleForm.lvsuo\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"职业年薪\" :label-width=\"formLabelWidth\" prop=\"age\">\r\n          <el-input\r\n            v-model=\"ruleForm.age\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"联系方式\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证件号\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"laywer_card\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.laywer_card\"\r\n            autocomplete=\"off\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          >\r\n            <template slot=\"append\">330rpx*300rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证书\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"card_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.card_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('card_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"showImage(ruleForm.card_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"delImage(ruleForm.card_path, 'card_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12, // 改为12，适合卡片视图\r\n      search: {\r\n        keyword: \"\",\r\n        specialty: \"\", // 新增专业筛选\r\n      },\r\n      loading: true,\r\n      zhuanyes: [],\r\n      url: \"/lvshi/\",\r\n      title: \"律师\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table 或 card\r\n      selectedLawyers: [], // 选中的律师\r\n      originalList: [], // 原始数据，用于搜索\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        yuangong_id: [\r\n          {\r\n            required: true,\r\n            message: \"请绑定员工\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhuanyes: [\r\n          {\r\n            required: true,\r\n            message: \"请选择专业\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lvsuo: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律所\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        age: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职业年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        laywer_card: [\r\n          {\r\n            required: true,\r\n            message: \"请填写证件号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师联系方式\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        card_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传证书\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      field: \"\",\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 在职律师数量\r\n    activeCount() {\r\n      return this.list.filter(lawyer => lawyer.status === 1).length;\r\n    },\r\n    // 专业领域数量\r\n    specialtyCount() {\r\n      const specialties = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.zhuanyes && Array.isArray(lawyer.zhuanyes)) {\r\n          lawyer.zhuanyes.forEach(specialty => specialties.add(specialty));\r\n        }\r\n      });\r\n      return specialties.size;\r\n    },\r\n    // 合作律所数量\r\n    firmCount() {\r\n      const firms = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.lvsuo) {\r\n          firms.add(lawyer.lvsuo);\r\n        }\r\n      });\r\n      return firms.size;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTestData(); // 先加载测试数据\r\n    this.getZhuanyes(); // 加载专业数据用于筛选\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 切换视图模式\r\n    switchView(mode) {\r\n      this.viewMode = mode;\r\n      this.$message.success(`已切换到${mode === 'table' ? '列表' : '卡片'}视图`);\r\n    },\r\n\r\n    // 获取专业名称\r\n    getSpecialtyNames(specialtyIds) {\r\n      if (!specialtyIds || !Array.isArray(specialtyIds)) return [];\r\n      if (!this.zhuanyes || !Array.isArray(this.zhuanyes)) return [];\r\n      return specialtyIds.map(id => {\r\n        const specialty = this.zhuanyes.find(z => z.id === id);\r\n        return specialty ? specialty.title : '未知专业';\r\n      });\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '暂无';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('zh-CN');\r\n    },\r\n\r\n    // 重置搜索\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        specialty: \"\"\r\n      };\r\n      // 重新加载完整的测试数据\r\n      this.loadTestData();\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      if (this.selectedLawyers.length > 0) {\r\n        this.$message.success(`导出选中的 ${this.selectedLawyers.length} 条律师数据`);\r\n      } else {\r\n        this.$message.success('导出全部律师数据');\r\n      }\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedLawyers = selection;\r\n    },\r\n\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 测试专业数据\r\n      this.zhuanyes = [\r\n        { id: 1, title: '民事诉讼' },\r\n        { id: 2, title: '刑事辩护' },\r\n        { id: 3, title: '商事仲裁' },\r\n        { id: 4, title: '知识产权' },\r\n        { id: 5, title: '劳动争议' },\r\n        { id: 6, title: '房产纠纷' },\r\n        { id: 7, title: '合同纠纷' },\r\n        { id: 8, title: '公司法务' }\r\n      ];\r\n\r\n      // 测试律师数据\r\n      this.list = [\r\n        {\r\n          id: 1,\r\n          title: '张明华',\r\n          lvsuo: '北京德恒律师事务所',\r\n          zhuanyes: [1, 3, 7],\r\n          phone: '13800138001',\r\n          laywer_card: '*********',\r\n          age: 8,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-03-15 09:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '李晓雯',\r\n          lvsuo: '上海锦天城律师事务所',\r\n          zhuanyes: [2, 4],\r\n          phone: '13800138002',\r\n          laywer_card: 'A20210002',\r\n          age: 12,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-08-22 14:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '王建国',\r\n          lvsuo: '广东广和律师事务所',\r\n          zhuanyes: [5, 6, 8],\r\n          phone: '13800138003',\r\n          laywer_card: 'A20210003',\r\n          age: 15,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-12-10 11:45:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '陈美玲',\r\n          lvsuo: '深圳君合律师事务所',\r\n          zhuanyes: [1, 4, 7],\r\n          phone: '13800138004',\r\n          laywer_card: 'A20210004',\r\n          age: 6,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-01-18 16:10:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '刘志强',\r\n          lvsuo: '北京金杜律师事务所',\r\n          zhuanyes: [2, 3, 8],\r\n          phone: '13800138005',\r\n          laywer_card: 'A20210005',\r\n          age: 20,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2018-05-30 10:25:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '赵雅琴',\r\n          lvsuo: '上海方达律师事务所',\r\n          zhuanyes: [4, 5, 6],\r\n          phone: '13800138006',\r\n          laywer_card: 'A20210006',\r\n          age: 9,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-07-12 13:55:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 7,\r\n          title: '孙文博',\r\n          lvsuo: '广州广信君达律师事务所',\r\n          zhuanyes: [1, 2, 7],\r\n          phone: '13800138007',\r\n          laywer_card: 'A20210007',\r\n          age: 11,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-11-08 15:40:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 8,\r\n          title: '周慧敏',\r\n          lvsuo: '深圳市律师协会',\r\n          zhuanyes: [3, 6, 8],\r\n          phone: '13800138008',\r\n          laywer_card: 'A20210008',\r\n          age: 7,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-09-25 12:15:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 9,\r\n          title: '吴国强',\r\n          lvsuo: '北京市中伦律师事务所',\r\n          zhuanyes: [1, 5, 7],\r\n          phone: '13800138009',\r\n          laywer_card: 'A20210009',\r\n          age: 13,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-04-14 09:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 10,\r\n          title: '郑小红',\r\n          lvsuo: '上海市汇业律师事务所',\r\n          zhuanyes: [2, 4, 6],\r\n          phone: '13800138010',\r\n          laywer_card: 'A20210010',\r\n          age: 5,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-06-03 14:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 11,\r\n          title: '马云飞',\r\n          lvsuo: '广东信达律师事务所',\r\n          zhuanyes: [3, 7, 8],\r\n          phone: '13800138011',\r\n          laywer_card: 'A20210011',\r\n          age: 16,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-02-28 11:05:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 12,\r\n          title: '林静怡',\r\n          lvsuo: '深圳市盈科律师事务所',\r\n          zhuanyes: [1, 4, 5],\r\n          phone: '13800138012',\r\n          laywer_card: 'A20210012',\r\n          age: 10,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-01-20 16:45:00',\r\n          status: 1\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据\r\n      this.originalList = [...this.list];\r\n      // 设置总数\r\n      this.total = this.list.length;\r\n      this.loading = false;\r\n    },\r\n\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(\"/yuangong/getMoreList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getZhuanyes() {\r\n      let _this = this;\r\n      _this.getRequest(\"/zhuanye/getList\").then((resp) => {\r\n        if (resp) {\r\n          _this.zhuanyes = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          phone: \"\",\r\n          address: \"\",\r\n          pic_path: \"\",\r\n          card_path: \"\",\r\n          zhuanyes: \"\",\r\n          age: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getZhuanyes();\r\n      _this.getLvshi();\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n\r\n      // 如果有原始测试数据，在本地进行搜索\r\n      if (this.originalList.length > 0) {\r\n        this.filterTestData();\r\n      } else {\r\n        this.size = 20;\r\n        this.getData();\r\n      }\r\n    },\r\n\r\n    // 在测试数据中进行筛选\r\n    filterTestData() {\r\n      let filteredList = [...this.originalList];\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.title.toLowerCase().includes(keyword) ||\r\n          lawyer.lvsuo.toLowerCase().includes(keyword) ||\r\n          lawyer.laywer_card.toLowerCase().includes(keyword) ||\r\n          lawyer.phone.includes(keyword)\r\n        );\r\n      }\r\n\r\n      // 专业筛选\r\n      if (this.search.specialty) {\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.zhuanyes && lawyer.zhuanyes.includes(this.search.specialty)\r\n        );\r\n      }\r\n\r\n      // 这里可以添加分页逻辑，但为了演示简单，直接显示所有结果\r\n      this.total = filteredList.length;\r\n\r\n      // 模拟搜索延迟\r\n      this.loading = true;\r\n      setTimeout(() => {\r\n        this.list = filteredList;\r\n        this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      // 如果已经有测试数据，直接使用\r\n      if (_this.list.length > 0) {\r\n        return;\r\n      }\r\n\r\n      _this.loading = true;\r\n\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          } else {\r\n            // 如果接口失败，使用测试数据\r\n            console.log('使用测试数据');\r\n            _this.loadTestData();\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 接口错误时也使用测试数据\r\n          console.log('接口错误，使用测试数据');\r\n          _this.loadTestData();\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm[this.field] = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 主容器 */\r\n.lawyer-management {\r\n  padding: 0;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.add-btn {\r\n  padding: 12px 24px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  padding: 0 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.lawyer-icon {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.specialty-icon {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.firm-icon {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  line-height: 1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  padding: 0 24px;\r\n}\r\n\r\n.content-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n}\r\n\r\n.search-input-group {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-input {\r\n  min-width: 300px;\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.filter-select {\r\n  width: 150px;\r\n}\r\n\r\n.search-btn, .reset-btn {\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.view-tabs {\r\n  display: flex;\r\n  background: #f5f7fa;\r\n  border-radius: 8px;\r\n  padding: 4px;\r\n}\r\n\r\n.view-tab {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.view-tab.active {\r\n  background: white;\r\n  color: #409eff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.view-tab:hover:not(.active) {\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格视图 */\r\n.table-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.lawyer-info-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.lawyer-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.clickable-avatar {\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.clickable-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.lawyer-details {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.lawyer-card {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.firm-info, .contact-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.specialties {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n.specialty-tag {\r\n  background: #ecf5ff;\r\n  color: #409eff;\r\n  border: 1px solid #d9ecff;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n  font-size: 12px;\r\n}\r\n\r\n.view-cert-btn {\r\n  color: #67c23a;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 6px;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 0;\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n.action-buttons .el-button.is-circle {\r\n  border-radius: 50%;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.lawyer-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.lawyer-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  display: flex;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.lawyer-avatar-large {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.lawyer-basic-info {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name-large {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.lawyer-firm {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #f5f7fa;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  text-align: right;\r\n  color: #303133;\r\n}\r\n\r\n.card-footer {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.register-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 24px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.pagination {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 12px 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-right {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .view-controls {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .stats-cards {\r\n    grid-template-columns: 1fr;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .lawyer-cards {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .card-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .info-row {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-value {\r\n    text-align: left;\r\n  }\r\n\r\n  .card-footer {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n/* 原有样式保留 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AA8jBA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MAAA;MACAC,MAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;MAAA;MACAC,eAAA;MAAA;MACAC,YAAA;MAAA;MACAC,QAAA;QACAR,KAAA;QACAS,MAAA;MACA;MAEAC,KAAA;QACAV,KAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,WAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAf,QAAA,GACA;UACAa,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,KAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAG,GAAA,GACA;UACAL,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAI,WAAA,GACA;UACAN,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAK,KAAA,GACA;UACAP,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAM,QAAA,GACA;UACAR,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAO,SAAA,GACA;UACAT,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAQ,cAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA;MACA,YAAAnC,IAAA,CAAAoC,MAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAC,MAAA,QAAAC,MAAA;IACA;IACA;IACAC,eAAA;MACA,MAAAC,WAAA,OAAAC,GAAA;MACA,KAAA1C,IAAA,CAAA2C,OAAA,CAAAN,MAAA;QACA,IAAAA,MAAA,CAAA7B,QAAA,IAAAoC,KAAA,CAAAC,OAAA,CAAAR,MAAA,CAAA7B,QAAA;UACA6B,MAAA,CAAA7B,QAAA,CAAAmC,OAAA,CAAArC,SAAA,IAAAmC,WAAA,CAAAK,GAAA,CAAAxC,SAAA;QACA;MACA;MACA,OAAAmC,WAAA,CAAAtC,IAAA;IACA;IACA;IACA4C,UAAA;MACA,MAAAC,KAAA,OAAAN,GAAA;MACA,KAAA1C,IAAA,CAAA2C,OAAA,CAAAN,MAAA;QACA,IAAAA,MAAA,CAAAZ,KAAA;UACAuB,KAAA,CAAAF,GAAA,CAAAT,MAAA,CAAAZ,KAAA;QACA;MACA;MACA,OAAAuB,KAAA,CAAA7C,IAAA;IACA;EACA;EACA8C,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,WAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,WAAAC,IAAA;MACA,KAAAxC,QAAA,GAAAwC,IAAA;MACA,KAAAC,QAAA,CAAAC,OAAA,QAAAF,IAAA;IACA;IAEA;IACAG,kBAAAC,YAAA;MACA,KAAAA,YAAA,KAAAf,KAAA,CAAAC,OAAA,CAAAc,YAAA;MACA,UAAAnD,QAAA,KAAAoC,KAAA,CAAAC,OAAA,MAAArC,QAAA;MACA,OAAAmD,YAAA,CAAAC,GAAA,CAAAC,EAAA;QACA,MAAAvD,SAAA,QAAAE,QAAA,CAAAsD,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAF,EAAA,KAAAA,EAAA;QACA,OAAAvD,SAAA,GAAAA,SAAA,CAAAI,KAAA;MACA;IACA;IAEA;IACAsD,WAAAC,UAAA;MACA,KAAAA,UAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,UAAA;MACA,OAAAC,IAAA,CAAAE,kBAAA;IACA;IAEA;IACAC,YAAA;MACA,KAAAjE,MAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACA;MACA,KAAA4C,YAAA;IACA;IAEA;IACAoB,WAAA;MACA,SAAAtD,eAAA,CAAAuB,MAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA,eAAAzC,eAAA,CAAAuB,MAAA;MACA;QACA,KAAAiB,QAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAc,sBAAAC,SAAA;MACA,KAAAxD,eAAA,GAAAwD,SAAA;IACA;IAEA;IACAtB,aAAA;MACA;MACA,KAAA1C,QAAA,IACA;QAAAqD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,GACA;QAAAmD,EAAA;QAAAnD,KAAA;MAAA,EACA;;MAEA;MACA,KAAAV,IAAA,IACA;QACA6D,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,GACA;QACAuB,EAAA;QACAnD,KAAA;QACAe,KAAA;QACAjB,QAAA;QACAoB,KAAA;QACAD,WAAA;QACAD,GAAA;QACAG,QAAA;QACAC,SAAA;QACA2C,WAAA;QACAnC,MAAA;MACA,EACA;;MAEA;MACA,KAAArB,YAAA,YAAAjB,IAAA;MACA;MACA,KAAAC,KAAA,QAAAD,IAAA,CAAAuC,MAAA;MACA,KAAAhC,OAAA;IACA;IAEAmE,YAAA1C,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACA2C,SAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAC,UAAA,0BAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA3C,SAAA,GAAA8C,IAAA,CAAAjF,IAAA;QACA;MACA;IACA;IACAqD,YAAA;MACA,IAAAyB,KAAA;MACAA,KAAA,CAAAC,UAAA,qBAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAH,KAAA,CAAApE,QAAA,GAAAuE,IAAA,CAAAjF,IAAA;QACA;MACA;IACA;IACAmF,SAAApB,EAAA;MACA,IAAAe,KAAA;MACA,IAAAf,EAAA;QACA,KAAAqB,OAAA,CAAArB,EAAA;MACA;QACA,KAAA3C,QAAA;UACAR,KAAA;UACAkB,KAAA;UACAuD,OAAA;UACAtD,QAAA;UACAC,SAAA;UACAtB,QAAA;UACAkB,GAAA;QACA;MACA;MAEAkD,KAAA,CAAAhE,iBAAA;MACAgE,KAAA,CAAAzB,WAAA;MACAyB,KAAA,CAAAD,QAAA;IACA;IACAO,QAAArB,EAAA;MACA,IAAAe,KAAA;MACAA,KAAA,CAAAC,UAAA,CAAAD,KAAA,CAAAnE,GAAA,gBAAAoD,EAAA,EAAAiB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAH,KAAA,CAAA1D,QAAA,GAAA6D,IAAA,CAAAjF,IAAA;QACA;MACA;IACA;IACAsF,QAAAC,KAAA,EAAAxB,EAAA;MACA,KAAAyB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAX,IAAA;QACA,KAAAY,aAAA,MAAAjF,GAAA,kBAAAoD,EAAA,EAAAiB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAxB,QAAA;cACAiC,IAAA;cACAnE,OAAA;YACA;YACA,KAAAtB,IAAA,CAAA2F,MAAA,CAAAN,KAAA;UACA;QACA;MACA,GACAO,KAAA;QACA,KAAApC,QAAA;UACAiC,IAAA;UACAnE,OAAA;QACA;MACA;IACA;IACAuE,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA9F,IAAA;;MAEA;MACA,SAAAe,YAAA,CAAAsB,MAAA;QACA,KAAA0D,cAAA;MACA;QACA,KAAA9F,IAAA;QACA,KAAAiD,OAAA;MACA;IACA;IAEA;IACA6C,eAAA;MACA,IAAAC,YAAA,YAAAjF,YAAA;;MAEA;MACA,SAAAb,MAAA,CAAAC,OAAA;QACA,MAAAA,OAAA,QAAAD,MAAA,CAAAC,OAAA,CAAA8F,WAAA;QACAD,YAAA,GAAAA,YAAA,CAAA9D,MAAA,CAAAC,MAAA,IACAA,MAAA,CAAA3B,KAAA,CAAAyF,WAAA,GAAAC,QAAA,CAAA/F,OAAA,KACAgC,MAAA,CAAAZ,KAAA,CAAA0E,WAAA,GAAAC,QAAA,CAAA/F,OAAA,KACAgC,MAAA,CAAAV,WAAA,CAAAwE,WAAA,GAAAC,QAAA,CAAA/F,OAAA,KACAgC,MAAA,CAAAT,KAAA,CAAAwE,QAAA,CAAA/F,OAAA,CACA;MACA;;MAEA;MACA,SAAAD,MAAA,CAAAE,SAAA;QACA4F,YAAA,GAAAA,YAAA,CAAA9D,MAAA,CAAAC,MAAA,IACAA,MAAA,CAAA7B,QAAA,IAAA6B,MAAA,CAAA7B,QAAA,CAAA4F,QAAA,MAAAhG,MAAA,CAAAE,SAAA,CACA;MACA;;MAEA;MACA,KAAAL,KAAA,GAAAiG,YAAA,CAAA3D,MAAA;;MAEA;MACA,KAAAhC,OAAA;MACA8F,UAAA;QACA,KAAArG,IAAA,GAAAkG,YAAA;QACA,KAAA3F,OAAA;MACA;IACA;IAEA6C,QAAA;MACA,IAAAwB,KAAA;;MAEA;MACA,IAAAA,KAAA,CAAA5E,IAAA,CAAAuC,MAAA;QACA;MACA;MAEAqC,KAAA,CAAArE,OAAA;MAEAqE,KAAA,CACA0B,WAAA,CACA1B,KAAA,CAAAnE,GAAA,mBAAAmE,KAAA,CAAA1E,IAAA,cAAA0E,KAAA,CAAAzE,IAAA,EACAyE,KAAA,CAAAxE,MACA,EACA0E,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA5E,IAAA,GAAA+E,IAAA,CAAAjF,IAAA;UACA8E,KAAA,CAAA3E,KAAA,GAAA8E,IAAA,CAAAwB,KAAA;QACA;UACA;UACAC,OAAA,CAAAC,GAAA;UACA7B,KAAA,CAAA1B,YAAA;QACA;QACA0B,KAAA,CAAArE,OAAA;MACA,GACAqF,KAAA;QACA;QACAY,OAAA,CAAAC,GAAA;QACA7B,KAAA,CAAA1B,YAAA;QACA0B,KAAA,CAAArE,OAAA;MACA;IACA;IACAmG,SAAA;MACA,IAAA9B,KAAA;MACA,KAAA+B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAP,WAAA,CAAA1B,KAAA,CAAAnE,GAAA,gBAAAS,QAAA,EAAA4D,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAJ,KAAA,CAAApB,QAAA;gBACAiC,IAAA;gBACAnE,OAAA,EAAAyD,IAAA,CAAA+B;cACA;cACA,KAAA1D,OAAA;cACAwB,KAAA,CAAAhE,iBAAA;YACA;cACAgE,KAAA,CAAApB,QAAA;gBACAiC,IAAA;gBACAnE,OAAA,EAAAyD,IAAA,CAAA+B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA7G,IAAA,GAAA6G,GAAA;MAEA,KAAA5D,OAAA;IACA;IACA6D,oBAAAD,GAAA;MACA,KAAA9G,IAAA,GAAA8G,GAAA;MACA,KAAA5D,OAAA;IACA;IACA8D,cAAAC,GAAA;MACA,KAAAjG,QAAA,MAAAc,KAAA,IAAAmF,GAAA,CAAArH,IAAA,CAAAW,GAAA;IACA;IAEA2G,UAAAC,IAAA;MACA,KAAAxG,UAAA,GAAAwG,IAAA;MACA,KAAAvG,aAAA;IACA;IACAwG,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA5B,IAAA;MACA,KAAA8B,UAAA;QACA,KAAA/D,QAAA,CAAAiE,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAA/C,KAAA;MACAA,KAAA,CAAAC,UAAA,gCAAAwC,IAAA,EAAAvC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA1D,QAAA,CAAAyG,QAAA;UAEA/C,KAAA,CAAApB,QAAA,CAAAC,OAAA;QACA;UACAmB,KAAA,CAAApB,QAAA,CAAAiE,KAAA,CAAA1C,IAAA,CAAA+B,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}