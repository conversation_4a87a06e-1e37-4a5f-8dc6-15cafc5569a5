{"version": 3, "sources": ["webpack:///./src/utils/fileUtils.js", "webpack:///./src/views/pages/archive/Search.vue", "webpack:///src/views/pages/archive/Search.vue", "webpack:///./src/views/pages/archive/Search.vue?be5e", "webpack:///./src/views/pages/archive/Search.vue?6269", "webpack:///./src/views/pages/archive/Search.vue?a8bd"], "names": ["formatFileSize", "size", "toFixed", "render", "_vm", "this", "_c", "_self", "staticClass", "attrs", "searchForm", "staticStyle", "model", "value", "keyword", "callback", "$$v", "$set", "expression", "fileType", "category", "date<PERSON><PERSON><PERSON>", "on", "handleSearch", "_v", "handleReset", "_s", "filteredFiles", "length", "viewMode", "scopedSlots", "_u", "key", "fn", "scope", "class", "getFileIcon", "row", "fileName", "$event", "handlePreview", "handleDownload", "handleDelete", "_e", "_l", "file", "id", "uploadTime", "split", "currentPage", "pageSize", "handleSizeChange", "handleCurrentChange", "staticRenderFns", "name", "data", "allFiles", "computed", "files", "toLowerCase", "filter", "includes", "startDate", "endDate", "fileDate", "methods", "iconMap", "$message", "success", "info", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "index", "findIndex", "f", "splice", "catch", "val", "component"], "mappings": "kHAKO,SAASA,EAAeC,GAC7B,OAAIA,EAAO,KACFA,EAAO,KACLA,EAAO,SACRA,EAAO,MAAMC,QAAQ,GAAK,MACzBD,EAAO,YACRA,EAAO,SAAeC,QAAQ,GAAK,OAEnCD,EAAO,YAAsBC,QAAQ,GAAK,MAbtD,mC,kECAA,IAAIC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcC,MAAM,CAAC,MAAQL,EAAIM,WAAW,QAAS,IAAO,CAACJ,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,QAAQ,CAACH,EAAG,WAAW,CAACK,YAAY,CAAC,MAAQ,SAASF,MAAM,CAAC,YAAc,eAAe,UAAY,IAAIG,MAAM,CAACC,MAAOT,EAAIM,WAAWI,QAASC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAIM,WAAY,UAAWM,IAAME,WAAW,yBAAyB,GAAGZ,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,SAAS,CAACH,EAAG,YAAY,CAACG,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIG,MAAM,CAACC,MAAOT,EAAIM,WAAWS,SAAUJ,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAIM,WAAY,WAAYM,IAAME,WAAW,wBAAwB,CAACZ,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAMH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,QAAQ,MAAQ,SAASH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,SAAS,MAAQ,SAASH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,UAAU,MAAQ,SAASH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,WAAWH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,YAAY,IAAI,GAAGH,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,CAACH,EAAG,YAAY,CAACG,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIG,MAAM,CAACC,MAAOT,EAAIM,WAAWU,SAAUL,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAIM,WAAY,WAAYM,IAAME,WAAW,wBAAwB,CAACZ,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAMH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,QAAQ,MAAQ,YAAY,IAAI,GAAGH,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,SAAS,CAACH,EAAG,iBAAiB,CAACG,MAAM,CAAC,KAAO,YAAY,kBAAkB,IAAI,oBAAoB,OAAO,kBAAkB,OAAO,OAAS,aAAa,eAAe,cAAcG,MAAM,CAACC,MAAOT,EAAIM,WAAWW,UAAWN,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAIM,WAAY,YAAaM,IAAME,WAAW,2BAA2B,GAAGZ,EAAG,eAAe,CAACA,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,WAAWa,GAAG,CAAC,MAAQlB,EAAImB,eAAe,CAACjB,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIoB,GAAG,UAAUlB,EAAG,YAAY,CAACgB,GAAG,CAAC,MAAQlB,EAAIqB,cAAc,CAACnB,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIoB,GAAG,WAAW,IAAI,IAAI,GAAGlB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIoB,GAAG,OAAOpB,EAAIsB,GAAGtB,EAAIuB,cAAcC,QAAQ,UAAUtB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,KAAO,SAASG,MAAM,CAACC,MAAOT,EAAIyB,SAAUd,SAAS,SAAUC,GAAMZ,EAAIyB,SAASb,GAAKE,WAAW,aAAa,CAACZ,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,SAAS,CAACL,EAAIoB,GAAG,UAAUlB,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,SAAS,CAACL,EAAIoB,GAAG,WAAW,IAAI,KAAuB,SAAjBpB,EAAIyB,SAAqBvB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACK,YAAY,CAAC,MAAQ,QAAQF,MAAM,CAAC,KAAOL,EAAIuB,gBAAgB,CAACrB,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,YAAY,OAAOqB,YAAY1B,EAAI2B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC5B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,YAAY2B,MAAM/B,EAAIgC,YAAYF,EAAMG,IAAIlB,YAAYb,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIoB,GAAGpB,EAAIsB,GAAGQ,EAAMG,IAAIC,mBAAmB,MAAK,EAAM,cAAchC,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,KAAK,MAAQ,SAASH,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,OAAOqB,YAAY1B,EAAI2B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAIoB,GAAG,IAAIpB,EAAIsB,GAAGtB,EAAIJ,eAAekC,EAAMG,IAAIpC,OAAO,SAAS,MAAK,EAAM,cAAcK,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,MAAQ,SAASH,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUqB,YAAY1B,EAAI2B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC5B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,WAAWa,GAAG,CAAC,MAAQ,SAASiB,GAAQ,OAAOnC,EAAIoC,cAAcN,EAAMG,QAAQ,CAACjC,EAAIoB,GAAG,UAAUlB,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,WAAWa,GAAG,CAAC,MAAQ,SAASiB,GAAQ,OAAOnC,EAAIqC,eAAeP,EAAMG,QAAQ,CAACjC,EAAIoB,GAAG,UAAUlB,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUa,GAAG,CAAC,MAAQ,SAASiB,GAAQ,OAAOnC,EAAIsC,aAAaR,EAAMG,QAAQ,CAACjC,EAAIoB,GAAG,WAAW,OAAO,MAAK,EAAM,eAAe,IAAI,GAAGpB,EAAIuC,KAAuB,SAAjBvC,EAAIyB,SAAqBvB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAIwC,GAAIxC,EAAIuB,eAAe,SAASkB,GAAM,OAAOvC,EAAG,MAAM,CAAC0B,IAAIa,EAAKC,GAAGtC,YAAY,YAAYc,GAAG,CAAC,MAAQ,SAASiB,GAAQ,OAAOnC,EAAIoC,cAAcK,MAAS,CAACvC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkB2B,MAAM/B,EAAIgC,YAAYS,EAAK1B,cAAcb,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,MAAM,CAAC,MAAQoC,EAAKP,WAAW,CAAClC,EAAIoB,GAAGpB,EAAIsB,GAAGmB,EAAKP,aAAahC,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIoB,GAAGpB,EAAIsB,GAAGtB,EAAIJ,eAAe6C,EAAK5C,UAAUK,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIoB,GAAGpB,EAAIsB,GAAGmB,EAAKE,WAAWC,MAAM,KAAK,gBAAe,KAAK5C,EAAIuC,KAAKrC,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACG,MAAM,CAAC,eAAeL,EAAI6C,YAAY,aAAa,CAAC,GAAI,GAAI,GAAI,KAAK,YAAY7C,EAAI8C,SAAS,OAAS,0CAA0C,MAAQ9C,EAAIuB,cAAcC,QAAQN,GAAG,CAAC,cAAclB,EAAI+C,iBAAiB,iBAAiB/C,EAAIgD,wBAAwB,QAEjvKC,EAAkB,G,YCuJP,GACfC,KAAA,gBACAC,OACA,OACA7C,WAAA,CACAI,QAAA,GACAK,SAAA,GACAC,SAAA,GACAC,UAAA,IAEAQ,SAAA,OACAoB,YAAA,EACAC,SAAA,GAEAM,SAAA,CACA,CACAV,GAAA,EACAR,SAAA,WACAnB,SAAA,MACAC,SAAA,OACAnB,KAAA,OACA8C,WAAA,uBAEA,CACAD,GAAA,EACAR,SAAA,YACAnB,SAAA,OACAC,SAAA,OACAnB,KAAA,MACA8C,WAAA,uBAEA,CACAD,GAAA,EACAR,SAAA,WACAnB,SAAA,MACAC,SAAA,OACAnB,KAAA,KACA8C,WAAA,uBAEA,CACAD,GAAA,EACAR,SAAA,YACAnB,SAAA,MACAC,SAAA,QACAnB,KAAA,OACA8C,WAAA,uBAEA,CACAD,GAAA,EACAR,SAAA,YACAnB,SAAA,OACAC,SAAA,OACAnB,KAAA,MACA8C,WAAA,uBAEA,CACAD,GAAA,EACAR,SAAA,WACAnB,SAAA,MACAC,SAAA,OACAnB,KAAA,MACA8C,WAAA,0BAKAU,SAAA,CACA9B,gBACA,IAAA+B,EAAA,KAAAF,SAGA,QAAA9C,WAAAI,QAAA,CACA,MAAAA,EAAA,KAAAJ,WAAAI,QAAA6C,cACAD,IAAAE,OAAAf,GACAA,EAAAP,SAAAqB,cAAAE,SAAA/C,IACA+B,EAAAzB,SAAAuC,cAAAE,SAAA/C,IAyBA,GApBA,KAAAJ,WAAAS,WACAuC,IAAAE,OAAAf,GACA,aAAAnC,WAAAS,SACA,eAAA0C,SAAAhB,EAAA1B,UACA,aAAAT,WAAAS,SACA,eAAA0C,SAAAhB,EAAA1B,UACA,eAAAT,WAAAS,SACA,iCAAA0C,SAAAhB,EAAA1B,UAEA0B,EAAA1B,WAAA,KAAAT,WAAAS,WAMA,KAAAT,WAAAU,WACAsC,IAAAE,OAAAf,KAAAzB,WAAA,KAAAV,WAAAU,WAIA,KAAAV,WAAAW,WAAA,SAAAX,WAAAW,UAAAO,OAAA,CACA,MAAAkC,EAAAC,GAAA,KAAArD,WAAAW,UACAqC,IAAAE,OAAAf,IACA,MAAAmB,EAAAnB,EAAAE,WAAAC,MAAA,QACA,OAAAgB,GAAAF,GAAAE,GAAAD,IAIA,OAAAL,IAGAO,QAAA,CACAjE,sBAEAoC,YAAAjB,GACA,MAAA+C,EAAA,CACA,uBACA,uBACA,wBACA,qBACA,sBACA,4BACA,sBACA,uBACA,sBACA,uBAEA,OAAAA,EAAA/C,IAAA,oBAGAI,eACA,KAAA0B,YAAA,EACA,KAAAkB,SAAAC,QAAA,SAGA3C,cACA,KAAAf,WAAA,CACAI,QAAA,GACAK,SAAA,GACAC,SAAA,GACAC,UAAA,IAEA,KAAA4B,YAAA,EACA,KAAAkB,SAAAE,KAAA,YAGA7B,cAAAK,GACA,KAAAsB,SAAAE,KAAA,SAAAxB,EAAAP,WAIAG,eAAAI,GACA,KAAAsB,SAAAC,QAAA,SAAAvB,EAAAP,WAIAI,aAAAG,GACA,KAAAyB,SAAA,WAAAzB,EAAAP,cAAA,QACAiC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YACAC,KAAA,KAEA,MAAAC,EAAA,KAAAnB,SAAAoB,UAAAC,KAAA/B,KAAAD,EAAAC,IACA6B,GAAA,IACA,KAAAnB,SAAAsB,OAAAH,EAAA,GACA,KAAAR,SAAAC,QAAA,MAAAvB,EAAAP,mBAEAyC,MAAA,KACA,KAAAZ,SAAAE,KAAA,YAIAlB,iBAAA6B,GACA,KAAA9B,SAAA8B,GAGA5B,oBAAA4B,GACA,KAAA/B,YAAA+B,KC3U6W,I,wBCQzWC,EAAY,eACd,EACA9E,EACAkD,GACA,EACA,KACA,WACA,MAIa,aAAA4B,E,2CCnBf", "file": "js/chunk-40102a1f.4dfafaff.js", "sourcesContent": ["/**\r\n * 格式化文件大小\r\n * @param {number} size 文件大小（字节）\r\n * @returns {string} 格式化后的文件大小\r\n */\r\nexport function formatFileSize(size) {\r\n  if (size < 1024) {\r\n    return size + ' B'\r\n  } else if (size < 1024 * 1024) {\r\n    return (size / 1024).toFixed(2) + ' KB'\r\n  } else if (size < 1024 * 1024 * 1024) {\r\n    return (size / (1024 * 1024)).toFixed(2) + ' MB'\r\n  } else {\r\n    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'\r\n  }\r\n}\r\n\r\n/**\r\n * 获取文件类型\r\n * @param {string} fileName 文件名\r\n * @returns {string} 文件类型\r\n */\r\nexport function getFileType(fileName) {\r\n  const extension = fileName.split('.').pop().toLowerCase()\r\n  const typeMap = {\r\n    // 图片\r\n    'jpg': 'image',\r\n    'jpeg': 'image',\r\n    'png': 'image',\r\n    'gif': 'image',\r\n    'bmp': 'image',\r\n    // 文档\r\n    'pdf': 'pdf',\r\n    'doc': 'doc',\r\n    'docx': 'doc',\r\n    'xls': 'xls',\r\n    'xlsx': 'xls',\r\n    'ppt': 'ppt',\r\n    'pptx': 'ppt',\r\n    // 文本\r\n    'txt': 'text',\r\n    'md': 'text',\r\n    // 压缩文件\r\n    'zip': 'archive',\r\n    'rar': 'archive',\r\n    '7z': 'archive'\r\n  }\r\n  return typeMap[extension] || 'other'\r\n}\r\n\r\n/**\r\n * 获取文件分类\r\n * @param {string} fileName 文件名\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 文件分类\r\n */\r\nexport function getFileCategory(fileName, fileType) {\r\n  // 根据文件名和类型智能分类\r\n  const name = fileName.toLowerCase()\r\n  \r\n  // 案件文书\r\n  if (name.includes('案件') || name.includes('诉讼') || name.includes('判决')) {\r\n    return '案件文书'\r\n  }\r\n  \r\n  // 合同文件\r\n  if (name.includes('合同') || name.includes('协议') || name.includes('契约')) {\r\n    return '合同文件'\r\n  }\r\n  \r\n  // 咨询记录\r\n  if (name.includes('咨询') || name.includes('记录') || name.includes('纪要')) {\r\n    return '咨询记录'\r\n  }\r\n  \r\n  // 根据文件类型分类\r\n  switch (fileType) {\r\n    case 'image':\r\n      return '图片文件'\r\n    case 'pdf':\r\n      return 'PDF文档'\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'Word文档'\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'Excel文档'\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'PPT文档'\r\n    case 'text':\r\n      return '文本文件'\r\n    case 'archive':\r\n      return '压缩文件'\r\n    default:\r\n      return '其他文件'\r\n  }\r\n}\r\n\r\n/**\r\n * 检查文件是否可预览\r\n * @param {string} fileType 文件类型\r\n * @returns {boolean} 是否可预览\r\n */\r\nexport function isPreviewable(fileType) {\r\n  const previewableTypes = ['image', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']\r\n  return previewableTypes.includes(fileType)\r\n}\r\n\r\n/**\r\n * 生成文件预览URL\r\n * @param {string} fileId 文件ID\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 预览URL\r\n */\r\nexport function generatePreviewUrl(fileId, fileType) {\r\n  const baseUrl = process.env.VUE_APP_BASE_API\r\n  if (fileType === 'image') {\r\n    return `${baseUrl}/archive/preview/image/${fileId}`\r\n  } else if (fileType === 'pdf') {\r\n    return `${baseUrl}/archive/preview/pdf/${fileId}`\r\n  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileType)) {\r\n    return `${baseUrl}/archive/preview/office/${fileId}`\r\n  }\r\n  return ''\r\n} ", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"archive-search-container\"},[_c('div',{staticClass:\"search-section\"},[_c('el-form',{staticClass:\"search-form\",attrs:{\"model\":_vm.searchForm,\"inline\":true}},[_c('el-form-item',{attrs:{\"label\":\"关键词\"}},[_c('el-input',{staticStyle:{\"width\":\"200px\"},attrs:{\"placeholder\":\"请输入文件名或内容关键词\",\"clearable\":\"\"},model:{value:(_vm.searchForm.keyword),callback:function ($$v) {_vm.$set(_vm.searchForm, \"keyword\", $$v)},expression:\"searchForm.keyword\"}})],1),_c('el-form-item',{attrs:{\"label\":\"文件类型\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择文件类型\",\"clearable\":\"\"},model:{value:(_vm.searchForm.fileType),callback:function ($$v) {_vm.$set(_vm.searchForm, \"fileType\", $$v)},expression:\"searchForm.fileType\"}},[_c('el-option',{attrs:{\"label\":\"全部\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"PDF文档\",\"value\":\"pdf\"}}),_c('el-option',{attrs:{\"label\":\"Word文档\",\"value\":\"doc\"}}),_c('el-option',{attrs:{\"label\":\"Excel表格\",\"value\":\"xls\"}}),_c('el-option',{attrs:{\"label\":\"图片\",\"value\":\"image\"}}),_c('el-option',{attrs:{\"label\":\"其他\",\"value\":\"other\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"分类\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择分类\",\"clearable\":\"\"},model:{value:(_vm.searchForm.category),callback:function ($$v) {_vm.$set(_vm.searchForm, \"category\", $$v)},expression:\"searchForm.category\"}},[_c('el-option',{attrs:{\"label\":\"全部\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"案件文书\",\"value\":\"案件文书\"}}),_c('el-option',{attrs:{\"label\":\"合同文件\",\"value\":\"合同文件\"}}),_c('el-option',{attrs:{\"label\":\"咨询记录\",\"value\":\"咨询记录\"}}),_c('el-option',{attrs:{\"label\":\"法律意见书\",\"value\":\"法律意见书\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"时间范围\"}},[_c('el-date-picker',{attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.searchForm.dateRange),callback:function ($$v) {_vm.$set(_vm.searchForm, \"dateRange\", $$v)},expression:\"searchForm.dateRange\"}})],1),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleSearch}},[_c('i',{staticClass:\"el-icon-search\"}),_vm._v(\" 搜索 \")]),_c('el-button',{on:{\"click\":_vm.handleReset}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 重置 \")])],1)],1)],1),_c('div',{staticClass:\"search-results\"},[_c('div',{staticClass:\"result-header\"},[_c('span',{staticClass:\"result-count\"},[_vm._v(\"共找到 \"+_vm._s(_vm.filteredFiles.length)+\" 个文件\")]),_c('div',{staticClass:\"view-mode\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.viewMode),callback:function ($$v) {_vm.viewMode=$$v},expression:\"viewMode\"}},[_c('el-radio-button',{attrs:{\"label\":\"list\"}},[_vm._v(\"列表视图\")]),_c('el-radio-button',{attrs:{\"label\":\"grid\"}},[_vm._v(\"网格视图\")])],1)],1)]),(_vm.viewMode === 'list')?_c('div',{staticClass:\"list-view\"},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.filteredFiles}},[_c('el-table-column',{attrs:{\"prop\":\"fileName\",\"label\":\"文件名\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"file-info\"},[_c('i',{staticClass:\"file-icon\",class:_vm.getFileIcon(scope.row.fileType)}),_c('span',{staticClass:\"file-name\"},[_vm._v(_vm._s(scope.row.fileName))])])]}}],null,false,1567857376)}),_c('el-table-column',{attrs:{\"prop\":\"category\",\"label\":\"分类\",\"width\":\"120\"}}),_c('el-table-column',{attrs:{\"prop\":\"size\",\"label\":\"大小\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(_vm.formatFileSize(scope.row.size))+\" \")]}}],null,false,4091220313)}),_c('el-table-column',{attrs:{\"prop\":\"uploadTime\",\"label\":\"上传时间\",\"width\":\"160\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"240\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handlePreview(scope.row)}}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"success\"},on:{\"click\":function($event){return _vm.handleDownload(scope.row)}}},[_vm._v(\" 下载 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"danger\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row)}}},[_vm._v(\" 删除 \")])],1)]}}],null,false,3648303130)})],1)],1):_vm._e(),(_vm.viewMode === 'grid')?_c('div',{staticClass:\"grid-view\"},[_c('div',{staticClass:\"file-grid\"},_vm._l((_vm.filteredFiles),function(file){return _c('div',{key:file.id,staticClass:\"file-card\",on:{\"click\":function($event){return _vm.handlePreview(file)}}},[_c('div',{staticClass:\"file-thumbnail\"},[_c('i',{staticClass:\"file-icon-large\",class:_vm.getFileIcon(file.fileType)})]),_c('div',{staticClass:\"file-info\"},[_c('div',{staticClass:\"file-name\",attrs:{\"title\":file.fileName}},[_vm._v(_vm._s(file.fileName))]),_c('div',{staticClass:\"file-meta\"},[_c('span',{staticClass:\"file-size\"},[_vm._v(_vm._s(_vm.formatFileSize(file.size)))]),_c('span',{staticClass:\"file-date\"},[_vm._v(_vm._s(file.uploadTime.split(' ')[0]))])])])])}),0)]):_vm._e(),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.currentPage,\"page-sizes\":[10, 20, 50, 100],\"page-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.filteredFiles.length},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"archive-search-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"关键词\">\r\n          <el-input\r\n            v-model=\"searchForm.keyword\"\r\n            placeholder=\"请输入文件名或内容关键词\"\r\n            style=\"width: 200px\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\">\r\n          <el-select v-model=\"searchForm.fileType\" placeholder=\"请选择文件类型\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"PDF文档\" value=\"pdf\" />\r\n            <el-option label=\"Word文档\" value=\"doc\" />\r\n            <el-option label=\"Excel表格\" value=\"xls\" />\r\n            <el-option label=\"图片\" value=\"image\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"分类\">\r\n          <el-select v-model=\"searchForm.category\" placeholder=\"请选择分类\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"案件文书\" value=\"案件文书\" />\r\n            <el-option label=\"合同文件\" value=\"合同文件\" />\r\n            <el-option label=\"咨询记录\" value=\"咨询记录\" />\r\n            <el-option label=\"法律意见书\" value=\"法律意见书\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"时间范围\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">\r\n            <i class=\"el-icon-search\"></i> 搜索\r\n          </el-button>\r\n          <el-button @click=\"handleReset\">\r\n            <i class=\"el-icon-refresh\"></i> 重置\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 搜索结果 -->\r\n    <div class=\"search-results\">\r\n      <div class=\"result-header\">\r\n        <span class=\"result-count\">共找到 {{ filteredFiles.length }} 个文件</span>\r\n        <div class=\"view-mode\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"list\">列表视图</el-radio-button>\r\n            <el-radio-button label=\"grid\">网格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 列表视图 -->\r\n      <div v-if=\"viewMode === 'list'\" class=\"list-view\">\r\n        <el-table :data=\"filteredFiles\" style=\"width: 100%\">\r\n          <el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"getFileIcon(scope.row.fileType)\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.fileName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"category\" label=\"分类\" width=\"120\" />\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatFileSize(scope.row.size) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"160\" />\r\n          <el-table-column label=\"操作\" width=\"240\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"primary\"\r\n                  @click=\"handlePreview(scope.row)\">\r\n                  预览\r\n                </el-button>\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"success\" \r\n                  @click=\"handleDownload(scope.row)\">\r\n                  下载\r\n                </el-button>\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"danger\" \r\n                  @click=\"handleDelete(scope.row)\">\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"grid-view\">\r\n        <div class=\"file-grid\">\r\n          <div\r\n            v-for=\"file in filteredFiles\"\r\n            :key=\"file.id\"\r\n            class=\"file-card\"\r\n            @click=\"handlePreview(file)\"\r\n          >\r\n            <div class=\"file-thumbnail\">\r\n              <i :class=\"getFileIcon(file.fileType)\" class=\"file-icon-large\"></i>\r\n            </div>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\" :title=\"file.fileName\">{{ file.fileName }}</div>\r\n              <div class=\"file-meta\">\r\n                <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                <span class=\"file-date\">{{ file.uploadTime.split(' ')[0] }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"filteredFiles.length\"\r\n        />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { formatFileSize } from '@/utils/fileUtils'\r\n\r\nexport default {\r\n  name: 'ArchiveSearch',\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      },\r\n      viewMode: 'list',\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      // 模拟文件数据\r\n      allFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: \"合同模板.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 1024000,\r\n          uploadTime: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: \"案件资料.docx\",\r\n          fileType: \"docx\",\r\n          category: \"案件文书\",\r\n          size: 512000,\r\n          uploadTime: \"2024-01-14 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: \"咨询记录.txt\",\r\n          fileType: \"txt\",\r\n          category: \"咨询记录\",\r\n          size: 8192,\r\n          uploadTime: \"2024-01-13 16:45:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: \"法律意见书.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"法律意见书\",\r\n          size: 2048000,\r\n          uploadTime: \"2024-01-12 09:15:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: \"证据清单.xlsx\",\r\n          fileType: \"xlsx\",\r\n          category: \"案件文书\",\r\n          size: 256000,\r\n          uploadTime: \"2024-01-11 15:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: \"委托协议.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 768000,\r\n          uploadTime: \"2024-01-10 11:20:00\"\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredFiles() {\r\n      let files = this.allFiles\r\n\r\n      // 关键词搜索\r\n      if (this.searchForm.keyword) {\r\n        const keyword = this.searchForm.keyword.toLowerCase()\r\n        files = files.filter(file => \r\n          file.fileName.toLowerCase().includes(keyword) ||\r\n          file.category.toLowerCase().includes(keyword)\r\n        )\r\n      }\r\n\r\n      // 文件类型筛选\r\n      if (this.searchForm.fileType) {\r\n        files = files.filter(file => {\r\n          if (this.searchForm.fileType === 'doc') {\r\n            return ['doc', 'docx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'xls') {\r\n            return ['xls', 'xlsx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'image') {\r\n            return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(file.fileType)\r\n          } else {\r\n            return file.fileType === this.searchForm.fileType\r\n          }\r\n        })\r\n      }\r\n\r\n      // 分类筛选\r\n      if (this.searchForm.category) {\r\n        files = files.filter(file => file.category === this.searchForm.category)\r\n      }\r\n\r\n      // 时间范围筛选\r\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\r\n        const [startDate, endDate] = this.searchForm.dateRange\r\n        files = files.filter(file => {\r\n          const fileDate = file.uploadTime.split(' ')[0]\r\n          return fileDate >= startDate && fileDate <= endDate\r\n        })\r\n      }\r\n\r\n      return files\r\n    }\r\n  },\r\n  methods: {\r\n    formatFileSize,\r\n    \r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'txt': 'el-icon-document-copy',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'gif': 'el-icon-picture'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n\r\n    handleSearch() {\r\n      this.currentPage = 1\r\n      this.$message.success('搜索完成')\r\n    },\r\n\r\n    handleReset() {\r\n      this.searchForm = {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      }\r\n      this.currentPage = 1\r\n      this.$message.info('搜索条件已重置')\r\n    },\r\n\r\n    handlePreview(file) {\r\n      this.$message.info(`预览文件: ${file.fileName}`)\r\n      // 这里可以实现文件预览功能\r\n    },\r\n\r\n    handleDownload(file) {\r\n      this.$message.success(`开始下载: ${file.fileName}`)\r\n      // 这里可以实现文件下载功能\r\n    },\r\n\r\n    handleDelete(file) {\r\n      this.$confirm(`确定要删除文件\"${file.fileName}\"吗？`, '删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 从数组中删除文件\r\n        const index = this.allFiles.findIndex(f => f.id === file.id)\r\n        if (index > -1) {\r\n          this.allFiles.splice(index, 1)\r\n          this.$message.success(`文件\"${file.fileName}\"已删除`)\r\n        }\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.pageSize = val\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.archive-search-container {\r\n  padding: 20px;\r\n}\r\n\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-results {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  \r\n  .el-table {\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    \r\n    .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background-color: #fafafa;\r\n          color: #606266;\r\n          font-weight: 600;\r\n          border-bottom: 1px solid #ebeef5;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f5f7fa;\r\n          }\r\n          \r\n          td {\r\n            border-bottom: 1px solid #f0f0f0;\r\n            padding: 12px 0;\r\n            vertical-align: middle;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.result-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n  \r\n  .el-button {\r\n    margin: 0;\r\n    min-width: 60px;\r\n    height: 28px;\r\n    font-size: 12px;\r\n    padding: 5px 12px;\r\n    border-radius: 4px;\r\n    \r\n    &.el-button--mini {\r\n      padding: 5px 12px;\r\n    }\r\n  }\r\n  \r\n  .el-button + .el-button {\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n/* 网格视图样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.file-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-card:hover {\r\n  border-color: #409EFF;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.file-thumbnail {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-icon-large {\r\n  font-size: 48px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-card .file-name {\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.file-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .result-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .file-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 15px;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Search.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Search.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Search.vue?vue&type=template&id=289a265a&scoped=true\"\nimport script from \"./Search.vue?vue&type=script&lang=js\"\nexport * from \"./Search.vue?vue&type=script&lang=js\"\nimport style0 from \"./Search.vue?vue&type=style&index=0&id=289a265a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"289a265a\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Search.vue?vue&type=style&index=0&id=289a265a&prod&scoped=true&lang=css\""], "sourceRoot": ""}