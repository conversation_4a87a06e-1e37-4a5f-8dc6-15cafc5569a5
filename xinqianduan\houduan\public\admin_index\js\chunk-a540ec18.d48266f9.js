(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a540ec18"],{"38ad":function(e,t,l){},a4c8:function(e,t,l){"use strict";l("38ad")},d625:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("el-row",{staticStyle:{width:"600px"}},[t("el-input",{attrs:{placeholder:"请输入内容",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.getData()}},slot:"append"})],1)],1),t("el-row",{staticClass:"page-top"},[t("el-button",{attrs:{type:"primary",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v("新增")]),t("el-button",{attrs:{type:"success",size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v("刷新")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table",attrs:{data:e.tableData,size:e.allSize}},[t("el-table-column",{attrs:{prop:"title",label:"标题"}}),t("el-table-column",{attrs:{prop:"desc",label:"描述"}}),t("el-table-column",{attrs:{prop:"price",label:"价格"}}),t("el-table-column",{attrs:{prop:"year",label:"年份"}}),t("el-table-column",{attrs:{prop:"sort",label:"排序"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建日期"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(l.row.id)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(l.$index,l.row.id)}}},[e._v(" 移除 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:"详情内容",visible:e.dialogFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"套餐名称","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"套餐价格","label-width":e.formLabelWidth,prop:"price"}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.price,callback:function(t){e.$set(e.ruleForm,"price",t)},expression:"ruleForm.price"}})],1),t("el-form-item",{attrs:{label:"年份","label-width":e.formLabelWidth,prop:"year"}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,"year",t)},expression:"ruleForm.year"}})],1),t("el-form-item",{attrs:{label:"套餐内容","label-width":e.formLabelWidth,prop:"good"}},[t("el-checkbox-group",{model:{value:e.ruleForm.good,callback:function(t){e.$set(e.ruleForm,"good",t)},expression:"ruleForm.good"}},e._l(e.types,(function(l,a){return t("el-row",{key:a,staticStyle:{display:"flex"}},[t("el-col",{attrs:{span:16}},[t("el-checkbox",{attrs:{label:l.id}},[e._v(" "+e._s(l.title)+" ")])],1),t("el-col",{attrs:{span:8}},[1==l.is_num?t("el-input-number",{attrs:{min:1,max:999,size:"mini",label:"描述文字"},model:{value:l.value,callback:function(t){e.$set(l,"value",t)},expression:"item.value"}}):e._e()],1)],1)})),1)],1),t("el-form-item",{attrs:{label:"套餐描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"排序","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,"sort",t)},expression:"ruleForm.sort"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1)],1)},r=[],o=(l("14d9"),{name:"list",components:{},data(){return{allSize:"mini",tableData:[],loading:!0,total:1,page:1,size:20,search:{keyword:""},ruleForm:{title:"",price:"",year:"",desc:"",sort:0,good:[],num:[]},num:0,rules:{title:[{required:!0,message:"请填写名称",trigger:"blur"}],price:[{required:!0,message:"请填写价格",trigger:"blur"}],year:[{required:!0,message:"请填写年份",trigger:"blur"}]},dialogFormVisible:!1,formLabelWidth:"80px",url:"/taocan/",types:[]}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):(this.ruleForm={title:"",price:"",year:"",desc:"",sort:0,good:[],num:[]},t.getTypes()),t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data,t.types=t.ruleForm.num)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.tableData.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},getTypes(){this.postRequest("/type/getList",{}).then(e=>{200==e.code&&(this.types=e.data)})},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.tableData=t.data,e.total=t.count),e.loading=!1})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},saveData(){let e=this,t=this.ruleForm.good,l=[];this.types.forEach(e=>{for(let a=0;a<t.length;a++){const r=t[a];e.id==r&&l.push(e)}}),this.ruleForm.num=l,this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code&&(e.$message({type:"success",message:t.msg}),e.dialogFormVisible=!1)})})}}}),s=o,i=(l("a4c8"),l("2877")),n=Object(i["a"])(s,a,r,!1,null,"0e653135",null);t["default"]=n.exports}}]);
//# sourceMappingURL=chunk-a540ec18.d48266f9.js.map