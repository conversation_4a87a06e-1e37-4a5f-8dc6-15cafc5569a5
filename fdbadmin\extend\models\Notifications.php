<?php
namespace models;

use think\Model;

/**
 * 系统通知模型
 */
class Notifications extends Model
{
    // 设置表名
    protected $table = 'notifications';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'title'       => 'string',
        'content'     => 'string',
        'type'        => 'string',
        'level'       => 'string',
        'target_type' => 'string',
        'target_id'   => 'int',
        'is_read'     => 'int',
        'is_global'   => 'int',
        'created_by'  => 'int',
        'create_time' => 'int',
        'update_time' => 'int',
        'read_time'   => 'int',
        'expire_time' => 'int',
        'status'      => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 获取未读通知数量
     * @param int $userId 用户ID，为空表示所有用户
     * @return int
     */
    public static function getUnreadCount($userId = null)
    {
        $where = [
            ['status', '=', 1],
            ['is_read', '=', 0]
        ];
        
        if ($userId) {
            $where[] = ['target_type', 'in', ['all', 'admin']];
            // 可以根据用户类型进一步筛选
        } else {
            $where[] = ['target_type', '=', 'all'];
        }
        
        // 检查是否过期
        $where[] = function($query) {
            $query->whereNull('expire_time')->whereOr('expire_time', '>', time());
        };
        
        return self::where($where)->count();
    }

    /**
     * 获取通知列表
     * @param int $userId 用户ID
     * @param int $limit 限制数量
     * @return array
     */
    public static function getNotificationList($userId = null, $limit = 10)
    {
        $where = [
            ['status', '=', 1]
        ];
        
        if ($userId) {
            $where[] = ['target_type', 'in', ['all', 'admin']];
        } else {
            $where[] = ['target_type', '=', 'all'];
        }
        
        // 检查是否过期
        $where[] = function($query) {
            $query->whereNull('expire_time')->whereOr('expire_time', '>', time());
        };
        
        return self::where($where)
            ->order('create_time desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 标记为已读
     * @param int $id 通知ID
     * @param int $userId 用户ID
     * @return bool
     */
    public static function markAsRead($id, $userId = null)
    {
        $data = [
            'is_read' => 1,
            'read_time' => time()
        ];
        
        $where = [
            ['id', '=', $id],
            ['status', '=', 1]
        ];
        
        if ($userId) {
            $where[] = ['target_type', 'in', ['all', 'admin']];
        }
        
        return self::where($where)->update($data);
    }

    /**
     * 创建通知
     * @param array $data 通知数据
     * @return bool|int
     */
    public static function createNotification($data)
    {
        $notification = new self();
        $notification->title = $data['title'];
        $notification->content = $data['content'] ?? '';
        $notification->type = $data['type'] ?? 'system';
        $notification->level = $data['level'] ?? 'info';
        $notification->target_type = $data['target_type'] ?? 'all';
        $notification->target_id = $data['target_id'] ?? null;
        $notification->is_global = $data['is_global'] ?? 1;
        $notification->created_by = $data['created_by'] ?? null;
        $notification->expire_time = $data['expire_time'] ?? null;
        
        return $notification->save();
    }
}
