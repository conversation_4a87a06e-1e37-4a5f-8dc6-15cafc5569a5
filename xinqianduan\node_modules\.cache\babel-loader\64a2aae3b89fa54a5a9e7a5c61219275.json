{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue?vue&type=template&id=d4a8e664&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\File.vue", "mtime": 1748617691744}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "type", "on", "click", "handleUpload", "_v", "disabled", "selectedFiles", "length", "handleBatchArchive", "handleBatchDownload", "handleBatchDelete", "staticStyle", "width", "data", "fileList", "handleSelectionChange", "prop", "label", "scopedSlots", "_u", "key", "fn", "scope", "class", "getFileIcon", "row", "fileType", "_s", "fileName", "formatFileSize", "size", "align", "$event", "handlePreview", "handleDownload", "handleDelete", "title", "visible", "uploadDialogVisible", "update:visible", "drag", "multiple", "action", "uploadUrl", "beforeUpload", "handleProgress", "handleUploadSuccess", "handleUploadError", "uploadFileList", "slot", "previewDialogVisible", "fullscreen", "isImage", "src", "previewUrl", "alt", "isPdf", "height", "isOffice", "staticRenderFns", "_withStripped"], "sources": ["H:/fdbfront/xinqianduan/src/views/pages/archive/File.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"archive-container\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"operation-bar\" },\n        [\n          _c(\n            \"el-button-group\",\n            [\n              _c(\n                \"el-button\",\n                { attrs: { type: \"primary\" }, on: { click: _vm.handleUpload } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-upload\" }),\n                  _vm._v(\" 上传文件 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"success\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchArchive },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-folder-add\" }),\n                  _vm._v(\" 批量归档 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"warning\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchDownload },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-download\" }),\n                  _vm._v(\" 批量下载 \"),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"danger\",\n                    disabled: !_vm.selectedFiles.length,\n                  },\n                  on: { click: _vm.handleBatchDelete },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-delete\" }),\n                  _vm._v(\" 批量删除 \"),\n                ]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"file-list-container\" },\n        [\n          _c(\n            \"el-table\",\n            {\n              staticStyle: { width: \"100%\" },\n              attrs: { data: _vm.fileList },\n              on: { \"selection-change\": _vm.handleSelectionChange },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"fileName\",\n                  label: \"文件名\",\n                  \"min-width\": \"200\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"div\", { staticClass: \"file-name-cell\" }, [\n                          _c(\"i\", {\n                            class: _vm.getFileIcon(scope.row.fileType),\n                          }),\n                          _c(\"span\", [_vm._v(_vm._s(scope.row.fileName))]),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"fileType\", label: \"类型\", width: \"100\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"category\", label: \"分类\", width: \"120\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"size\", label: \"大小\", width: \"120\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \" \" + _vm._s(_vm.formatFileSize(scope.row.size)) + \" \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"uploadTime\", label: \"上传时间\", width: \"180\" },\n              }),\n              _c(\"el-table-column\", {\n                attrs: { label: \"操作\", width: \"240\", align: \"center\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"action-buttons\" },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { size: \"mini\", type: \"primary\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handlePreview(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 预览 \")]\n                            ),\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { size: \"mini\", type: \"success\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleDownload(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 下载 \")]\n                            ),\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { size: \"mini\", type: \"danger\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleDelete(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\" 删除 \")]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"文件上传\",\n            visible: _vm.uploadDialogVisible,\n            width: \"500px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.uploadDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-upload\",\n            {\n              staticClass: \"upload-demo\",\n              attrs: {\n                drag: \"\",\n                multiple: \"\",\n                action: _vm.uploadUrl,\n                \"before-upload\": _vm.beforeUpload,\n                \"on-progress\": _vm.handleProgress,\n                \"on-success\": _vm.handleUploadSuccess,\n                \"on-error\": _vm.handleUploadError,\n                \"file-list\": _vm.uploadFileList,\n              },\n            },\n            [\n              _c(\"i\", { staticClass: \"el-icon-upload\" }),\n              _c(\"div\", { staticClass: \"el-upload__text\" }, [\n                _vm._v(\"将文件拖到此处，或\"),\n                _c(\"em\", [_vm._v(\"点击上传\")]),\n              ]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"el-upload__tip\",\n                  attrs: { slot: \"tip\" },\n                  slot: \"tip\",\n                },\n                [_vm._v(\" 支持任意格式文件，单个文件不超过500MB \")]\n              ),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"文件预览\",\n            visible: _vm.previewDialogVisible,\n            width: \"80%\",\n            fullscreen: true,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.previewDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\"div\", { staticClass: \"preview-container\" }, [\n            _vm.isImage\n              ? _c(\"div\", { staticClass: \"image-preview\" }, [\n                  _c(\"img\", {\n                    attrs: { src: _vm.previewUrl, alt: \"预览图片\" },\n                  }),\n                ])\n              : _vm.isPdf\n              ? _c(\"div\", { staticClass: \"pdf-preview\" }, [\n                  _c(\"iframe\", {\n                    attrs: {\n                      src: _vm.previewUrl,\n                      width: \"100%\",\n                      height: \"600px\",\n                    },\n                  }),\n                ])\n              : _vm.isOffice\n              ? _c(\"div\", { staticClass: \"office-preview\" }, [\n                  _c(\"iframe\", {\n                    attrs: {\n                      src: _vm.previewUrl,\n                      width: \"100%\",\n                      height: \"600px\",\n                    },\n                  }),\n                ])\n              : _c(\"div\", { staticClass: \"other-preview\" }, [\n                  _c(\"p\", [_vm._v(\"该文件类型暂不支持预览，请下载后查看\")]),\n                ]),\n          ]),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,iBAAiB,EACjB,CACEA,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACQ;IAAa;EAAE,CAAC,EAC/D,CACEP,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,SAAS;MACfK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACa;IAAmB;EACtC,CAAC,EACD,CACEZ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,SAAS;MACfK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACc;IAAoB;EACvC,CAAC,EACD,CACEb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLC,IAAI,EAAE,QAAQ;MACdK,QAAQ,EAAE,CAACV,GAAG,CAACW,aAAa,CAACC;IAC/B,CAAC;IACDN,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACe;IAAkB;EACrC,CAAC,EACD,CACEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CACA,UAAU,EACV;IACEe,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9Bb,KAAK,EAAE;MAAEc,IAAI,EAAElB,GAAG,CAACmB;IAAS,CAAC;IAC7Bb,EAAE,EAAE;MAAE,kBAAkB,EAAEN,GAAG,CAACoB;IAAsB;EACtD,CAAC,EACD,CACEnB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEC,IAAI,EAAE,WAAW;MAAEY,KAAK,EAAE;IAAK;EAC1C,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MACLiB,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,KAAK;MACZ,WAAW,EAAE;IACf,CAAC;IACDC,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1B,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,GAAG,EAAE;UACN2B,KAAK,EAAE5B,GAAG,CAAC6B,WAAW,CAACF,KAAK,CAACG,GAAG,CAACC,QAAQ;QAC3C,CAAC,CAAC,EACF9B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACS,EAAE,CAACT,GAAG,CAACgC,EAAE,CAACL,KAAK,CAACG,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFhC,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM;EACvD,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM;EACvD,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE;IAAM,CAAC;IAClDM,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL3B,GAAG,CAACS,EAAE,CACJ,GAAG,GAAGT,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACkC,cAAc,CAACP,KAAK,CAACG,GAAG,CAACK,IAAI,CAAC,CAAC,GAAG,GACrD,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFlC,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEiB,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,MAAM;MAAEL,KAAK,EAAE;IAAM;EAC3D,CAAC,CAAC,EACFhB,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAEkB,KAAK,EAAE,IAAI;MAAEL,KAAK,EAAE,KAAK;MAAEmB,KAAK,EAAE;IAAS,CAAC;IACrDb,WAAW,EAAEvB,GAAG,CAACwB,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1B,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAiB,CAAC,EACjC,CACEF,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE,MAAM;YAAE9B,IAAI,EAAE;UAAU,CAAC;UACxCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU8B,MAAM,EAAE;cACvB,OAAOrC,GAAG,CAACsC,aAAa,CAACX,KAAK,CAACG,GAAG,CAAC;YACrC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE,MAAM;YAAE9B,IAAI,EAAE;UAAU,CAAC;UACxCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU8B,MAAM,EAAE;cACvB,OAAOrC,GAAG,CAACuC,cAAc,CAACZ,KAAK,CAACG,GAAG,CAAC;YACtC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAE+B,IAAI,EAAE,MAAM;YAAE9B,IAAI,EAAE;UAAS,CAAC;UACvCC,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAU8B,MAAM,EAAE;cACvB,OAAOrC,GAAG,CAACwC,YAAY,CAACb,KAAK,CAACG,GAAG,CAAC;YACpC;UACF;QACF,CAAC,EACD,CAAC9B,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLqC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE1C,GAAG,CAAC2C,mBAAmB;MAChC1B,KAAK,EAAE;IACT,CAAC;IACDX,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAsC,CAAUP,MAAM,EAAE;QAClCrC,GAAG,CAAC2C,mBAAmB,GAAGN,MAAM;MAClC;IACF;EACF,CAAC,EACD,CACEpC,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MACLyC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE/C,GAAG,CAACgD,SAAS;MACrB,eAAe,EAAEhD,GAAG,CAACiD,YAAY;MACjC,aAAa,EAAEjD,GAAG,CAACkD,cAAc;MACjC,YAAY,EAAElD,GAAG,CAACmD,mBAAmB;MACrC,UAAU,EAAEnD,GAAG,CAACoD,iBAAiB;MACjC,WAAW,EAAEpD,GAAG,CAACqD;IACnB;EACF,CAAC,EACD,CACEpD,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACS,EAAE,CAAC,WAAW,CAAC,EACnBR,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEkD,IAAI,EAAE;IAAM,CAAC;IACtBA,IAAI,EAAE;EACR,CAAC,EACD,CAACtD,GAAG,CAACS,EAAE,CAAC,yBAAyB,CAAC,CACpC,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLqC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE1C,GAAG,CAACuD,oBAAoB;MACjCtC,KAAK,EAAE,KAAK;MACZuC,UAAU,EAAE;IACd,CAAC;IACDlD,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAsC,CAAUP,MAAM,EAAE;QAClCrC,GAAG,CAACuD,oBAAoB,GAAGlB,MAAM;MACnC;IACF;EACF,CAAC,EACD,CACEpC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACyD,OAAO,GACPxD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MAAEsD,GAAG,EAAE1D,GAAG,CAAC2D,UAAU;MAAEC,GAAG,EAAE;IAAO;EAC5C,CAAC,CAAC,CACH,CAAC,GACF5D,GAAG,CAAC6D,KAAK,GACT5D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,QAAQ,EAAE;IACXG,KAAK,EAAE;MACLsD,GAAG,EAAE1D,GAAG,CAAC2D,UAAU;MACnB1C,KAAK,EAAE,MAAM;MACb6C,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,CAAC,GACF9D,GAAG,CAAC+D,QAAQ,GACZ9D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,QAAQ,EAAE;IACXG,KAAK,EAAE;MACLsD,GAAG,EAAE1D,GAAG,CAAC2D,UAAU;MACnB1C,KAAK,EAAE,MAAM;MACb6C,MAAM,EAAE;IACV;EACF,CAAC,CAAC,CACH,CAAC,GACF7D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACS,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CACxC,CAAC,CACP,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIuD,eAAe,GAAG,EAAE;AACxBjE,MAAM,CAACkE,aAAa,GAAG,IAAI;AAE3B,SAASlE,MAAM,EAAEiE,eAAe", "ignoreList": []}]}