{"name": "@vue/eslint-config-prettier", "version": "6.0.0", "description": "eslint-config-prettier for Vue CLI", "main": "index.js", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-prettier.git"}, "keywords": ["vue", "cli"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-prettier/issues"}, "homepage": "https://github.com/vuejs/eslint-config-prettier#readme", "dependencies": {"eslint-config-prettier": "^6.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "eslint-plugin-prettier": "^3.1.0", "prettier": ">= 1.13.0"}}