{"version": 3, "names": ["_core", "require", "_helperFunctionName", "_helperSplitExportDeclaration", "_decorators", "_semver", "_fields", "_decorators2", "_misc", "_features", "_typescript", "version<PERSON>ey", "createClassFeaturePlugin", "name", "feature", "loose", "manipulateOptions", "api", "inherits", "decoratorVersion", "_api$assumption", "FEATURES", "decorators", "createDecoratorTransform", "_api", "assumption", "setPublicClassFields", "privateFieldsAsSymbols", "privateFieldsAsProperties", "noUninitializedPrivateFieldAccess", "constant<PERSON>uper", "noDocumentAll", "Error", "privateFieldsAsSymbolsOrProperties", "explicit", "undefined", "push", "length", "console", "warn", "join", "pre", "file", "enableFeature", "get", "set", "semver", "lt", "visitor", "Class", "path", "_ref", "shouldTransform", "pathIsClassDeclaration", "isClassDeclaration", "assertFieldTransformed", "isLoose", "constructor", "isDecorated", "hasDecorators", "node", "props", "elements", "computedPaths", "privateNames", "Set", "body", "isClassProperty", "isClassMethod", "computed", "isPrivate", "key", "id", "getName", "setName", "isClassPrivateMethod", "kind", "has", "buildCodeFrameError", "add", "isProperty", "isStaticBlock", "innerBinding", "ref", "nameFunction", "scope", "generateUidIdentifier", "classRefForDefine", "t", "cloneNode", "privateNamesMap", "buildPrivateNamesMap", "privateNamesNodes", "buildPrivateNamesNodes", "transformPrivateNamesUsage", "keysNodes", "staticNodes", "instanceNodes", "lastInstanceNodeReturnsThis", "pureStaticNodes", "classBindingNode", "wrapClass", "buildDecoratedClass", "extractComputedKeys", "buildFieldsInitNodes", "superClass", "injectInitialization", "referenceVisitor", "state", "prop", "static", "traverse", "<PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "find", "parent", "isStatement", "isDeclaration", "ExportDefaultDeclaration", "decl", "splitExportDeclaration", "type"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginAPI, PluginObject, NodePath } from \"@babel/core\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport createDecoratorTransform from \"./decorators.ts\";\nimport type { DecoratorVersionKind } from \"./decorators.ts\";\n\nimport semver from \"semver\";\n\nimport {\n  buildPrivateNamesNodes,\n  buildPrivateNamesMap,\n  transformPrivateNamesUsage,\n  buildFieldsInitNodes,\n  buildCheckInRHS,\n} from \"./fields.ts\";\nimport type { PropPath } from \"./fields.ts\";\nimport { buildDecoratedClass, hasDecorators } from \"./decorators-2018-09.ts\";\nimport { injectInitialization, extractComputedKeys } from \"./misc.ts\";\nimport {\n  enableFeature,\n  FEATURES,\n  isLoose,\n  shouldTransform,\n} from \"./features.ts\";\nimport { assertFieldTransformed } from \"./typescript.ts\";\n\nexport { FEATURES, enableFeature, injectInitialization, buildCheckInRHS };\n\nconst versionKey = \"@babel/plugin-class-features/version\";\n\ninterface Options {\n  name: string;\n  feature: number;\n  loose?: boolean;\n  inherits?: PluginObject[\"inherits\"];\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n  api?: PluginAPI;\n  decoratorVersion?: DecoratorVersionKind | \"2018-09\";\n}\n\nexport function createClassFeaturePlugin({\n  name,\n  feature,\n  loose,\n  manipulateOptions,\n  api,\n  inherits,\n  decoratorVersion,\n}: Options): PluginObject {\n  if (feature & FEATURES.decorators) {\n    if (process.env.BABEL_8_BREAKING) {\n      return createDecoratorTransform(api, { loose }, \"2023-11\", inherits);\n    } else {\n      if (\n        decoratorVersion === \"2023-11\" ||\n        decoratorVersion === \"2023-05\" ||\n        decoratorVersion === \"2023-01\" ||\n        decoratorVersion === \"2022-03\" ||\n        decoratorVersion === \"2021-12\"\n      ) {\n        return createDecoratorTransform(\n          api,\n          { loose },\n          decoratorVersion,\n          inherits,\n        );\n      }\n    }\n  }\n  if (!process.env.BABEL_8_BREAKING) {\n    api ??= { assumption: () => void 0 as any } as any;\n  }\n  const setPublicClassFields = api.assumption(\"setPublicClassFields\");\n  const privateFieldsAsSymbols = api.assumption(\"privateFieldsAsSymbols\");\n  const privateFieldsAsProperties = api.assumption(\"privateFieldsAsProperties\");\n  const noUninitializedPrivateFieldAccess =\n    api.assumption(\"noUninitializedPrivateFieldAccess\") ?? false;\n  const constantSuper = api.assumption(\"constantSuper\");\n  const noDocumentAll = api.assumption(\"noDocumentAll\");\n\n  if (privateFieldsAsProperties && privateFieldsAsSymbols) {\n    throw new Error(\n      `Cannot enable both the \"privateFieldsAsProperties\" and ` +\n        `\"privateFieldsAsSymbols\" assumptions as the same time.`,\n    );\n  }\n  const privateFieldsAsSymbolsOrProperties =\n    privateFieldsAsProperties || privateFieldsAsSymbols;\n\n  if (loose === true) {\n    type AssumptionName = Parameters<PluginAPI[\"assumption\"]>[0];\n    const explicit: `\"${AssumptionName}\"`[] = [];\n\n    if (setPublicClassFields !== undefined) {\n      explicit.push(`\"setPublicClassFields\"`);\n    }\n    if (privateFieldsAsProperties !== undefined) {\n      explicit.push(`\"privateFieldsAsProperties\"`);\n    }\n    if (privateFieldsAsSymbols !== undefined) {\n      explicit.push(`\"privateFieldsAsSymbols\"`);\n    }\n    if (explicit.length !== 0) {\n      console.warn(\n        `[${name}]: You are using the \"loose: true\" option and you are` +\n          ` explicitly setting a value for the ${explicit.join(\" and \")}` +\n          ` assumption${explicit.length > 1 ? \"s\" : \"\"}. The \"loose\" option` +\n          ` can cause incompatibilities with the other class features` +\n          ` plugins, so it's recommended that you replace it with the` +\n          ` following top-level option:\\n` +\n          `\\t\"assumptions\": {\\n` +\n          `\\t\\t\"setPublicClassFields\": true,\\n` +\n          `\\t\\t\"privateFieldsAsSymbols\": true\\n` +\n          `\\t}`,\n      );\n    }\n  }\n\n  return {\n    name,\n    manipulateOptions,\n    inherits,\n\n    pre(file) {\n      enableFeature(file, feature, loose);\n\n      if (!process.env.BABEL_8_BREAKING) {\n        // Until 7.21.4, we used to encode the version as a number.\n        // If file.get(versionKey) is a number, it has thus been\n        // set by an older version of this plugin.\n        if (typeof file.get(versionKey) === \"number\") {\n          file.set(versionKey, PACKAGE_JSON.version);\n          return;\n        }\n      }\n      if (\n        !file.get(versionKey) ||\n        semver.lt(file.get(versionKey), PACKAGE_JSON.version)\n      ) {\n        file.set(versionKey, PACKAGE_JSON.version);\n      }\n    },\n\n    visitor: {\n      Class(path, { file }) {\n        if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n        if (!shouldTransform(path, file)) return;\n\n        const pathIsClassDeclaration = path.isClassDeclaration();\n\n        if (pathIsClassDeclaration) assertFieldTransformed(path);\n\n        const loose = isLoose(file, feature);\n\n        let constructor: NodePath<t.ClassMethod>;\n        const isDecorated = hasDecorators(path.node);\n        const props: PropPath[] = [];\n        const elements = [];\n        const computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[] = [];\n        const privateNames = new Set<string>();\n        const body = path.get(\"body\");\n\n        for (const path of body.get(\"body\")) {\n          if (\n            // check path.node.computed is enough, but ts will complain\n            (path.isClassProperty() || path.isClassMethod()) &&\n            path.node.computed\n          ) {\n            computedPaths.push(path);\n          }\n\n          if (path.isPrivate()) {\n            const { name } = path.node.key.id;\n            const getName = `get ${name}`;\n            const setName = `set ${name}`;\n\n            if (path.isClassPrivateMethod()) {\n              if (path.node.kind === \"get\") {\n                if (\n                  privateNames.has(getName) ||\n                  (privateNames.has(name) && !privateNames.has(setName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(getName).add(name);\n              } else if (path.node.kind === \"set\") {\n                if (\n                  privateNames.has(setName) ||\n                  (privateNames.has(name) && !privateNames.has(getName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(setName).add(name);\n              }\n            } else {\n              if (\n                (privateNames.has(name) &&\n                  !privateNames.has(getName) &&\n                  !privateNames.has(setName)) ||\n                (privateNames.has(name) &&\n                  (privateNames.has(getName) || privateNames.has(setName)))\n              ) {\n                throw path.buildCodeFrameError(\"Duplicate private field\");\n              }\n\n              privateNames.add(name);\n            }\n          }\n\n          if (path.isClassMethod({ kind: \"constructor\" })) {\n            constructor = path;\n          } else {\n            elements.push(path);\n            if (\n              path.isProperty() ||\n              path.isPrivate() ||\n              path.isStaticBlock?.()\n            ) {\n              props.push(path as PropPath);\n            }\n          }\n        }\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (!props.length) return;\n        } else {\n          if (!props.length && !isDecorated) return;\n        }\n\n        const innerBinding = path.node.id;\n        let ref: t.Identifier | null;\n        if (!innerBinding || !pathIsClassDeclaration) {\n          nameFunction(path as NodePath<t.ClassExpression>);\n          ref = path.scope.generateUidIdentifier(innerBinding?.name || \"Class\");\n        }\n        const classRefForDefine = ref ?? t.cloneNode(innerBinding);\n\n        const privateNamesMap = buildPrivateNamesMap(\n          classRefForDefine.name,\n          privateFieldsAsSymbolsOrProperties ?? loose,\n          props,\n          file,\n        );\n        const privateNamesNodes = buildPrivateNamesNodes(\n          privateNamesMap,\n          privateFieldsAsProperties ?? loose,\n          privateFieldsAsSymbols ?? false,\n          file,\n        );\n\n        transformPrivateNamesUsage(\n          classRefForDefine,\n          path,\n          privateNamesMap,\n          {\n            privateFieldsAsProperties:\n              privateFieldsAsSymbolsOrProperties ?? loose,\n            noUninitializedPrivateFieldAccess,\n            noDocumentAll,\n            innerBinding,\n          },\n          file,\n        );\n\n        let keysNodes: t.Statement[],\n          staticNodes: t.Statement[],\n          instanceNodes: t.ExpressionStatement[],\n          lastInstanceNodeReturnsThis: boolean,\n          pureStaticNodes: t.FunctionDeclaration[],\n          classBindingNode: t.Statement | null,\n          wrapClass: (path: NodePath<t.Class>) => NodePath;\n\n        if (!process.env.BABEL_8_BREAKING) {\n          if (isDecorated) {\n            staticNodes = pureStaticNodes = keysNodes = [];\n            ({ instanceNodes, wrapClass } = buildDecoratedClass(\n              classRefForDefine,\n              path,\n              elements,\n              file,\n            ));\n          } else {\n            keysNodes = extractComputedKeys(path, computedPaths, file);\n            ({\n              staticNodes,\n              pureStaticNodes,\n              instanceNodes,\n              lastInstanceNodeReturnsThis,\n              classBindingNode,\n              wrapClass,\n            } = buildFieldsInitNodes(\n              ref,\n              path.node.superClass,\n              props,\n              privateNamesMap,\n              file,\n              setPublicClassFields ?? loose,\n              privateFieldsAsSymbolsOrProperties ?? loose,\n              noUninitializedPrivateFieldAccess,\n              constantSuper ?? loose,\n              innerBinding,\n            ));\n          }\n        } else {\n          keysNodes = extractComputedKeys(path, computedPaths, file);\n          ({\n            staticNodes,\n            pureStaticNodes,\n            instanceNodes,\n            lastInstanceNodeReturnsThis,\n            classBindingNode,\n            wrapClass,\n          } = buildFieldsInitNodes(\n            ref,\n            path.node.superClass,\n            props,\n            privateNamesMap,\n            file,\n            setPublicClassFields ?? loose,\n            privateFieldsAsSymbolsOrProperties ?? loose,\n            noUninitializedPrivateFieldAccess,\n            constantSuper ?? loose,\n            innerBinding,\n          ));\n        }\n\n        if (instanceNodes.length > 0) {\n          injectInitialization(\n            path,\n            constructor,\n            instanceNodes,\n            (referenceVisitor, state) => {\n              if (!process.env.BABEL_8_BREAKING) {\n                if (isDecorated) return;\n              }\n              for (const prop of props) {\n                // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n                if (t.isStaticBlock?.(prop.node) || prop.node.static) continue;\n                prop.traverse(referenceVisitor, state);\n              }\n            },\n            lastInstanceNodeReturnsThis,\n          );\n        }\n\n        // rename to make ts happy\n        const wrappedPath = wrapClass(path);\n        wrappedPath.insertBefore([...privateNamesNodes, ...keysNodes]);\n        if (staticNodes.length > 0) {\n          wrappedPath.insertAfter(staticNodes);\n        }\n        if (pureStaticNodes.length > 0) {\n          wrappedPath\n            .find(parent => parent.isStatement() || parent.isDeclaration())\n            .insertAfter(pureStaticNodes);\n        }\n        if (classBindingNode != null && pathIsClassDeclaration) {\n          wrappedPath.insertAfter(classBindingNode);\n        }\n      },\n\n      ExportDefaultDeclaration(path, { file }) {\n        if (!process.env.BABEL_8_BREAKING) {\n          if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n          const decl = path.get(\"declaration\");\n\n          if (decl.isClassDeclaration() && hasDecorators(decl.node)) {\n            if (decl.node.id) {\n              // export default class Foo {}\n              //   -->\n              // class Foo {} export { Foo as default }\n              splitExportDeclaration(path);\n            } else {\n              // @ts-expect-error Anonymous class declarations can be\n              // transformed as if they were expressions\n              decl.node.type = \"ClassExpression\";\n            }\n          }\n        }\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,6BAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAGA,IAAAI,OAAA,GAAAJ,OAAA;AAEA,IAAAK,OAAA,GAAAL,OAAA;AAQA,IAAAM,YAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;AAMA,IAAAS,WAAA,GAAAT,OAAA;AAIA,MAAMU,UAAU,GAAG,sCAAsC;AAYlD,SAASC,wBAAwBA,CAAC;EACvCC,IAAI;EACJC,OAAO;EACPC,KAAK;EACLC,iBAAiB;EACjBC,GAAG;EACHC,QAAQ;EACRC;AACO,CAAC,EAAgB;EAAA,IAAAC,eAAA;EACxB,IAAIN,OAAO,GAAGO,kBAAQ,CAACC,UAAU,EAAE;IAG1B;MACL,IACEH,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,EAC9B;QACA,OAAO,IAAAI,mBAAwB,EAC7BN,GAAG,EACH;UAAEF;QAAM,CAAC,EACTI,gBAAgB,EAChBD,QACF,CAAC;MACH;IACF;EACF;EACmC;IAAA,IAAAM,IAAA;IACjC,CAAAA,IAAA,GAAAP,GAAG,YAAAO,IAAA,GAAHP,GAAG,GAAK;MAAEQ,UAAU,EAAEA,CAAA,KAAM,KAAK;IAAS,CAAC;EAC7C;EACA,MAAMC,oBAAoB,GAAGT,GAAG,CAACQ,UAAU,CAAC,sBAAsB,CAAC;EACnE,MAAME,sBAAsB,GAAGV,GAAG,CAACQ,UAAU,CAAC,wBAAwB,CAAC;EACvE,MAAMG,yBAAyB,GAAGX,GAAG,CAACQ,UAAU,CAAC,2BAA2B,CAAC;EAC7E,MAAMI,iCAAiC,IAAAT,eAAA,GACrCH,GAAG,CAACQ,UAAU,CAAC,mCAAmC,CAAC,YAAAL,eAAA,GAAI,KAAK;EAC9D,MAAMU,aAAa,GAAGb,GAAG,CAACQ,UAAU,CAAC,eAAe,CAAC;EACrD,MAAMM,aAAa,GAAGd,GAAG,CAACQ,UAAU,CAAC,eAAe,CAAC;EAErD,IAAIG,yBAAyB,IAAID,sBAAsB,EAAE;IACvD,MAAM,IAAIK,KAAK,CACZ,yDAAwD,GACtD,wDACL,CAAC;EACH;EACA,MAAMC,kCAAkC,GACtCL,yBAAyB,IAAID,sBAAsB;EAErD,IAAIZ,KAAK,KAAK,IAAI,EAAE;IAElB,MAAMmB,QAAiC,GAAG,EAAE;IAE5C,IAAIR,oBAAoB,KAAKS,SAAS,EAAE;MACtCD,QAAQ,CAACE,IAAI,CAAE,wBAAuB,CAAC;IACzC;IACA,IAAIR,yBAAyB,KAAKO,SAAS,EAAE;MAC3CD,QAAQ,CAACE,IAAI,CAAE,6BAA4B,CAAC;IAC9C;IACA,IAAIT,sBAAsB,KAAKQ,SAAS,EAAE;MACxCD,QAAQ,CAACE,IAAI,CAAE,0BAAyB,CAAC;IAC3C;IACA,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MACzBC,OAAO,CAACC,IAAI,CACT,IAAG1B,IAAK,uDAAsD,GAC5D,uCAAsCqB,QAAQ,CAACM,IAAI,CAAC,OAAO,CAAE,EAAC,GAC9D,cAAaN,QAAQ,CAACG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,sBAAqB,GACjE,4DAA2D,GAC3D,4DAA2D,GAC3D,gCAA+B,GAC/B,sBAAqB,GACrB,qCAAoC,GACpC,sCAAqC,GACrC,KACL,CAAC;IACH;EACF;EAEA,OAAO;IACLxB,IAAI;IACJG,iBAAiB;IACjBE,QAAQ;IAERuB,GAAGA,CAACC,IAAI,EAAE;MACR,IAAAC,uBAAa,EAACD,IAAI,EAAE5B,OAAO,EAAEC,KAAK,CAAC;MAEA;QAIjC,IAAI,OAAO2B,IAAI,CAACE,GAAG,CAACjC,UAAU,CAAC,KAAK,QAAQ,EAAE;UAC5C+B,IAAI,CAACG,GAAG,CAAClC,UAAU,UAAsB,CAAC;UAC1C;QACF;MACF;MACA,IACE,CAAC+B,IAAI,CAACE,GAAG,CAACjC,UAAU,CAAC,IACrBmC,OAAM,CAACC,EAAE,CAACL,IAAI,CAACE,GAAG,CAACjC,UAAU,CAAC,UAAsB,CAAC,EACrD;QACA+B,IAAI,CAACG,GAAG,CAAClC,UAAU,UAAsB,CAAC;MAC5C;IACF,CAAC;IAEDqC,OAAO,EAAE;MACPC,KAAKA,CAACC,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QAAA,IAAAS,IAAA;QACpB,IAAIT,IAAI,CAACE,GAAG,CAACjC,UAAU,CAAC,aAAyB,EAAE;QAEnD,IAAI,CAAC,IAAAyC,yBAAe,EAACF,IAAI,EAAER,IAAI,CAAC,EAAE;QAElC,MAAMW,sBAAsB,GAAGH,IAAI,CAACI,kBAAkB,CAAC,CAAC;QAExD,IAAID,sBAAsB,EAAE,IAAAE,kCAAsB,EAACL,IAAI,CAAC;QAExD,MAAMnC,KAAK,GAAG,IAAAyC,iBAAO,EAACd,IAAI,EAAE5B,OAAO,CAAC;QAEpC,IAAI2C,WAAoC;QACxC,MAAMC,WAAW,GAAG,IAAAC,0BAAa,EAACT,IAAI,CAACU,IAAI,CAAC;QAC5C,MAAMC,KAAiB,GAAG,EAAE;QAC5B,MAAMC,QAAQ,GAAG,EAAE;QACnB,MAAMC,aAA0D,GAAG,EAAE;QACrE,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;QACtC,MAAMC,IAAI,GAAGhB,IAAI,CAACN,GAAG,CAAC,MAAM,CAAC;QAE7B,KAAK,MAAMM,IAAI,IAAIgB,IAAI,CAACtB,GAAG,CAAC,MAAM,CAAC,EAAE;UACnC,IAEE,CAACM,IAAI,CAACiB,eAAe,CAAC,CAAC,IAAIjB,IAAI,CAACkB,aAAa,CAAC,CAAC,KAC/ClB,IAAI,CAACU,IAAI,CAACS,QAAQ,EAClB;YACAN,aAAa,CAAC3B,IAAI,CAACc,IAAI,CAAC;UAC1B;UAEA,IAAIA,IAAI,CAACoB,SAAS,CAAC,CAAC,EAAE;YACpB,MAAM;cAAEzD;YAAK,CAAC,GAAGqC,IAAI,CAACU,IAAI,CAACW,GAAG,CAACC,EAAE;YACjC,MAAMC,OAAO,GAAI,OAAM5D,IAAK,EAAC;YAC7B,MAAM6D,OAAO,GAAI,OAAM7D,IAAK,EAAC;YAE7B,IAAIqC,IAAI,CAACyB,oBAAoB,CAAC,CAAC,EAAE;cAC/B,IAAIzB,IAAI,CAACU,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBAC5B,IACEZ,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IACxBT,YAAY,CAACa,GAAG,CAAChE,IAAI,CAAC,IAAI,CAACmD,YAAY,CAACa,GAAG,CAACH,OAAO,CAAE,EACtD;kBACA,MAAMxB,IAAI,CAAC4B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACN,OAAO,CAAC,CAACM,GAAG,CAAClE,IAAI,CAAC;cACrC,CAAC,MAAM,IAAIqC,IAAI,CAACU,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBACnC,IACEZ,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IACxBV,YAAY,CAACa,GAAG,CAAChE,IAAI,CAAC,IAAI,CAACmD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAE,EACtD;kBACA,MAAMvB,IAAI,CAAC4B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACL,OAAO,CAAC,CAACK,GAAG,CAAClE,IAAI,CAAC;cACrC;YACF,CAAC,MAAM;cACL,IACGmD,YAAY,CAACa,GAAG,CAAChE,IAAI,CAAC,IACrB,CAACmD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAC1B,CAACT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IAC3BV,YAAY,CAACa,GAAG,CAAChE,IAAI,CAAC,KACpBmD,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAAIT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,CAAE,EAC3D;gBACA,MAAMxB,IAAI,CAAC4B,mBAAmB,CAAC,yBAAyB,CAAC;cAC3D;cAEAd,YAAY,CAACe,GAAG,CAAClE,IAAI,CAAC;YACxB;UACF;UAEA,IAAIqC,IAAI,CAACkB,aAAa,CAAC;YAAEQ,IAAI,EAAE;UAAc,CAAC,CAAC,EAAE;YAC/CnB,WAAW,GAAGP,IAAI;UACpB,CAAC,MAAM;YACLY,QAAQ,CAAC1B,IAAI,CAACc,IAAI,CAAC;YACnB,IACEA,IAAI,CAAC8B,UAAU,CAAC,CAAC,IACjB9B,IAAI,CAACoB,SAAS,CAAC,CAAC,IAChBpB,IAAI,CAAC+B,aAAa,YAAlB/B,IAAI,CAAC+B,aAAa,CAAG,CAAC,EACtB;cACApB,KAAK,CAACzB,IAAI,CAACc,IAAgB,CAAC;YAC9B;UACF;QACF;QAIO;UACL,IAAI,CAACW,KAAK,CAACxB,MAAM,IAAI,CAACqB,WAAW,EAAE;QACrC;QAEA,MAAMwB,YAAY,GAAGhC,IAAI,CAACU,IAAI,CAACY,EAAE;QACjC,IAAIW,GAAwB;QAC5B,IAAI,CAACD,YAAY,IAAI,CAAC7B,sBAAsB,EAAE;UAC5C,IAAA+B,2BAAY,EAAClC,IAAmC,CAAC;UACjDiC,GAAG,GAAGjC,IAAI,CAACmC,KAAK,CAACC,qBAAqB,CAAC,CAAAJ,YAAY,oBAAZA,YAAY,CAAErE,IAAI,KAAI,OAAO,CAAC;QACvE;QACA,MAAM0E,iBAAiB,IAAApC,IAAA,GAAGgC,GAAG,YAAAhC,IAAA,GAAIqC,WAAC,CAACC,SAAS,CAACP,YAAY,CAAC;QAE1D,MAAMQ,eAAe,GAAG,IAAAC,4BAAoB,EAC1CJ,iBAAiB,CAAC1E,IAAI,EACtBoB,kCAAkC,WAAlCA,kCAAkC,GAAIlB,KAAK,EAC3C8C,KAAK,EACLnB,IACF,CAAC;QACD,MAAMkD,iBAAiB,GAAG,IAAAC,8BAAsB,EAC9CH,eAAe,EACf9D,yBAAyB,WAAzBA,yBAAyB,GAAIb,KAAK,EAClCY,sBAAsB,WAAtBA,sBAAsB,GAAI,KAAK,EAC/Be,IACF,CAAC;QAED,IAAAoD,kCAA0B,EACxBP,iBAAiB,EACjBrC,IAAI,EACJwC,eAAe,EACf;UACE9D,yBAAyB,EACvBK,kCAAkC,WAAlCA,kCAAkC,GAAIlB,KAAK;UAC7Cc,iCAAiC;UACjCE,aAAa;UACbmD;QACF,CAAC,EACDxC,IACF,CAAC;QAED,IAAIqD,SAAwB,EAC1BC,WAA0B,EAC1BC,aAAsC,EACtCC,2BAAoC,EACpCC,eAAwC,EACxCC,gBAAoC,EACpCC,SAAgD;QAEf;UACjC,IAAI3C,WAAW,EAAE;YACfsC,WAAW,GAAGG,eAAe,GAAGJ,SAAS,GAAG,EAAE;YAC9C,CAAC;cAAEE,aAAa;cAAEI;YAAU,CAAC,GAAG,IAAAC,gCAAmB,EACjDf,iBAAiB,EACjBrC,IAAI,EACJY,QAAQ,EACRpB,IACF,CAAC;UACH,CAAC,MAAM;YACLqD,SAAS,GAAG,IAAAQ,yBAAmB,EAACrD,IAAI,EAAEa,aAAa,EAAErB,IAAI,CAAC;YAC1D,CAAC;cACCsD,WAAW;cACXG,eAAe;cACfF,aAAa;cACbC,2BAA2B;cAC3BE,gBAAgB;cAChBC;YACF,CAAC,GAAG,IAAAG,4BAAoB,EACtBrB,GAAG,EACHjC,IAAI,CAACU,IAAI,CAAC6C,UAAU,EACpB5C,KAAK,EACL6B,eAAe,EACfhD,IAAI,EACJhB,oBAAoB,WAApBA,oBAAoB,GAAIX,KAAK,EAC7BkB,kCAAkC,WAAlCA,kCAAkC,GAAIlB,KAAK,EAC3Cc,iCAAiC,EACjCC,aAAa,WAAbA,aAAa,GAAIf,KAAK,EACtBmE,YACF,CAAC;UACH;QACF;QAuBA,IAAIe,aAAa,CAAC5D,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAAqE,0BAAoB,EAClBxD,IAAI,EACJO,WAAW,EACXwC,aAAa,EACb,CAACU,gBAAgB,EAAEC,KAAK,KAAK;YACQ;cACjC,IAAIlD,WAAW,EAAE;YACnB;YACA,KAAK,MAAMmD,IAAI,IAAIhD,KAAK,EAAE;cAExB,IAAI2B,WAAC,CAACP,aAAa,YAAfO,WAAC,CAACP,aAAa,CAAG4B,IAAI,CAACjD,IAAI,CAAC,IAAIiD,IAAI,CAACjD,IAAI,CAACkD,MAAM,EAAE;cACtDD,IAAI,CAACE,QAAQ,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;YACxC;UACF,CAAC,EACDV,2BACF,CAAC;QACH;QAGA,MAAMc,WAAW,GAAGX,SAAS,CAACnD,IAAI,CAAC;QACnC8D,WAAW,CAACC,YAAY,CAAC,CAAC,GAAGrB,iBAAiB,EAAE,GAAGG,SAAS,CAAC,CAAC;QAC9D,IAAIC,WAAW,CAAC3D,MAAM,GAAG,CAAC,EAAE;UAC1B2E,WAAW,CAACE,WAAW,CAAClB,WAAW,CAAC;QACtC;QACA,IAAIG,eAAe,CAAC9D,MAAM,GAAG,CAAC,EAAE;UAC9B2E,WAAW,CACRG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,WAAW,CAAC,CAAC,IAAID,MAAM,CAACE,aAAa,CAAC,CAAC,CAAC,CAC9DJ,WAAW,CAACf,eAAe,CAAC;QACjC;QACA,IAAIC,gBAAgB,IAAI,IAAI,IAAI/C,sBAAsB,EAAE;UACtD2D,WAAW,CAACE,WAAW,CAACd,gBAAgB,CAAC;QAC3C;MACF,CAAC;MAEDmB,wBAAwBA,CAACrE,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QACJ;UACjC,IAAIA,IAAI,CAACE,GAAG,CAACjC,UAAU,CAAC,aAAyB,EAAE;UAEnD,MAAM6G,IAAI,GAAGtE,IAAI,CAACN,GAAG,CAAC,aAAa,CAAC;UAEpC,IAAI4E,IAAI,CAAClE,kBAAkB,CAAC,CAAC,IAAI,IAAAK,0BAAa,EAAC6D,IAAI,CAAC5D,IAAI,CAAC,EAAE;YACzD,IAAI4D,IAAI,CAAC5D,IAAI,CAACY,EAAE,EAAE;cAIhB,IAAAiD,qCAAsB,EAACvE,IAAI,CAAC;YAC9B,CAAC,MAAM;cAGLsE,IAAI,CAAC5D,IAAI,CAAC8D,IAAI,GAAG,iBAAiB;YACpC;UACF;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}