{"map": "{\"version\":3,\"sources\":[\"js/chunk-1ca8c59e.f755cc21.js\"],\"names\":[\"window\",\"push\",\"03b7\",\"module\",\"exports\",\"__webpack_require__\",\"0798\",\"__webpack_exports__\",\"bafd\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"_m\",\"attrs\",\"type\",\"icon\",\"on\",\"click\",\"refulsh\",\"_v\",\"shadow\",\"placeholder\",\"clearable\",\"nativeOn\",\"keyup\",\"$event\",\"indexOf\",\"_k\",\"keyCode\",\"key\",\"searchData\",\"apply\",\"arguments\",\"model\",\"value\",\"search\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"slot\",\"is_deal\",\"_l\",\"options1\",\"item\",\"id\",\"label\",\"title\",\"clearSearch\",\"_s\",\"total\",\"directives\",\"name\",\"rawName\",\"loading\",\"data\",\"list\",\"stripe\",\"border\",\"empty-text\",\"prop\",\"width\",\"align\",\"scopedSlots\",\"_u\",\"fn\",\"scope\",\"row\",\"order_sn\",\"size\",\"min-width\",\"desc\",\"length\",\"substring\",\"getStatusType\",\"getStatusText\",\"uid\",\"showDebtorDetail\",\"dt_name\",\"create_time\",\"fixed\",\"plain\",\"disabled\",\"editData\",\"delData\",\"$index\",\"page-sizes\",\"page-size\",\"layout\",\"background\",\"size-change\",\"handleSizeChange\",\"current-change\",\"handleCurrentChange\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"close\",\"handleDialogClose\",\"ref\",\"ruleForm\",\"rules\",\"label-position\",\"gutter\",\"span\",\"readonly\",\"type_title\",\"rows\",\"aiGenerating\",\"generateLawyerLetter\",\"aiGeneratedContent\",\"useAiContent\",\"regenerateContent\",\"_e\",\"change\",\"onProcessMethodChange\",\"processMethod\",\"description\",\"closable\",\"show-icon\",\"fileGenerating\",\"generateAiFile\",\"aiGeneratedFile\",\"file_path\",\"changeFile\",\"staticStyle\",\"display\",\"action\",\"show-file-list\",\"on-success\",\"handleSuccess\",\"delImage\",\"content\",\"cancelDialog\",\"saveData\",\"dialogVisible\",\"src\",\"show_image\",\"class\",\"panel-open\",\"showDebtorPanel\",\"closeDebtorPanel\",\"currentDebtor\",\"active\",\"activeTab\",\"id_card\",\"phone\",\"address\",\"debt_amount\",\"debt_type\",\"user_name\",\"user_phone\",\"user_register_time\",\"user_status\",\"files\",\"file\",\"getFileIcon\",\"upload_time\",\"previewFile\",\"downloadFile\",\"history\",\"record\",\"time\",\"staticRenderFns\",\"wangEnduit\",\"lawyervue_type_script_lang_js\",\"components\",\"EditorBar\",\"[object Object]\",\"allSize\",\"page\",\"is_pay\",\"url\",\"info\",\"contractTypes\",\"is_num\",\"required\",\"message\",\"trigger\",\"formLabelWidth\",\"options\",\"getData\",\"getContractTypes\",\"document\",\"addEventListener\",\"handleKeyDown\",\"removeEventListener\",\"methods\",\"status\",\"statusMap\",\"0\",\"1\",\"2\",\"$refs\",\"resetFields\",\"setTimeout\",\"template_file\",\"template_name\",\"template_size\",\"template_content\",\"matchedTemplate\",\"findMatchingTemplate\",\"$message\",\"warning\",\"debtorInfo\",\"getDebtorInfo\",\"userInfo\",\"getUserInfo\",\"simulateAiGeneration\",\"success\",\"error\",\"console\",\"toLowerCase\",\"typeTitle\",\"matchRules\",\"keywords\",\"templateId\",\"rule\",\"some\",\"includes\",\"find\",\"Promise\",\"resolve\",\"debt_date\",\"repay_date\",\"register_time\",\"template\",\"replace\",\"Date\",\"toLocaleDateString\",\"trim\",\"method\",\"fileName\",\"getTime\",\"path\",\"filed\",\"log\",\"getInfo\",\"_this\",\"testData\",\"foundData\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"then\",\"deleteRequest\",\"resp\",\"code\",\"msg\",\"catch\",\"index\",\"splice\",\"$router\",\"go\",\"event\",\"generateIdCard\",\"generatePhone\",\"generateAddress\",\"generateDebtAmount\",\"generateDebtType\",\"generateUserName\",\"generateRegisterTime\",\"generateFiles\",\"generateHistory\",\"fileType\",\"iconMap\",\"pdf\",\"doc\",\"docx\",\"jpg\",\"jpeg\",\"png\",\"zip\",\"rar\",\"prefixes\",\"prefix\",\"Math\",\"floor\",\"random\",\"year\",\"month\",\"String\",\"padStart\",\"day\",\"suffix\",\"addresses\",\"amounts\",\"types\",\"surnames\",\"names\",\"surname\",\"debtorName\",\"filteredData\",\"filter\",\"startIndex\",\"endIndex\",\"pageData\",\"slice\",\"validate\",\"valid\",\"val\",\"res\",\"isTypeTrue\",\"test\",\"getRequest\",\"yonghu_lawyervue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,KAM3BC,OACA,SAAUH,EAAQI,EAAqBF,GAE7C,aAC+cA,EAAoB,SAO7dG,KACA,SAAUL,EAAQI,EAAqBF,GAE7C,aAEAA,EAAoBI,EAAEF,GAGtB,IAAIG,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,2BACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,cACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,mBAEVC,GAAI,CACFC,MAASV,EAAIW,UAEd,CAACX,EAAIY,GAAG,aAAc,OAAQV,EAAG,MAAO,CACzCE,YAAa,kBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,cACbE,MAAO,CACLO,OAAU,UAEX,CAACX,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIY,GAAG,WAAYV,EAAG,WAAY,CACpCE,YAAa,eACbE,MAAO,CACLQ,YAAe,kBACfC,UAAa,IAEfC,SAAU,CACRC,MAAS,SAAUC,GACjB,OAAKA,EAAOX,KAAKY,QAAQ,QAAUnB,EAAIoB,GAAGF,EAAOG,QAAS,QAAS,GAAIH,EAAOI,IAAK,SAAiB,KAC7FtB,EAAIuB,WAAWC,MAAM,KAAMC,aAGtCC,MAAO,CACLC,MAAO3B,EAAI4B,OAAOC,QAClBC,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI4B,OAAQ,UAAWG,IAElCE,WAAY,mBAEb,CAAC/B,EAAG,IAAK,CACVE,YAAa,gCACbE,MAAO,CACL4B,KAAQ,UAEVA,KAAM,cACD,GAAIhC,EAAG,MAAO,CACnBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIY,GAAG,UAAWV,EAAG,YAAa,CACpCE,YAAa,gBACbE,MAAO,CACLQ,YAAe,UACfC,UAAa,IAEfW,MAAO,CACLC,MAAO3B,EAAI4B,OAAOO,QAClBL,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI4B,OAAQ,UAAWG,IAElCE,WAAY,mBAEbjC,EAAIoC,GAAGpC,EAAIqC,UAAU,SAAUC,GAChC,OAAOpC,EAAG,YAAa,CACrBoB,IAAKgB,EAAKC,GACVjC,MAAO,CACLkC,MAASF,EAAKG,MACdd,MAASW,EAAKC,SAGhB,IAAK,KAAMrC,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,aACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,kBAEVC,GAAI,CACFC,MAASV,EAAIuB,aAEd,CAACvB,EAAIY,GAAG,UAAWV,EAAG,YAAa,CACpCE,YAAa,YACbE,MAAO,CACLE,KAAQ,wBAEVC,GAAI,CACFC,MAASV,EAAI0C,cAEd,CAAC1C,EAAIY,GAAG,WAAY,QAAS,GAAIV,EAAG,MAAO,CAC5CE,YAAa,iBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,aACbE,MAAO,CACLO,OAAU,UAEX,CAACX,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,KAAM,CAACA,EAAG,IAAK,CACpBE,YAAa,oBACXJ,EAAIY,GAAG,eAAgBV,EAAG,OAAQ,CACpCE,YAAa,eACZ,CAACJ,EAAIY,GAAG,KAAOZ,EAAI2C,GAAG3C,EAAI4C,OAAS,cAAe1C,EAAG,WAAY,CAClE2C,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTpB,MAAO3B,EAAIgD,QACXf,WAAY,YAEd7B,YAAa,eACbE,MAAO,CACL2C,KAAQjD,EAAIkD,KACZC,OAAU,GACVC,OAAU,GACVC,aAAc,cAEf,CAACnD,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,WACRd,MAAS,MACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,0BACXF,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACJ,EAAIY,GAAGZ,EAAI2C,GAAGiB,EAAMC,IAAIC,sBAG9B5D,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,OACRd,MAAS,OACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,SAAU,CACnBI,MAAO,CACLC,KAAQ,UACRwD,KAAQ,UAET,CAAC/D,EAAIY,GAAG,IAAMZ,EAAI2C,GAAGiB,EAAMC,IAAItD,MAAQ,OAAS,cAGrDL,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,QACRd,MAAS,OACTwB,YAAa,OAEfP,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,cACZ,CAACF,EAAG,OAAQ,CACbE,YAAa,aACbE,MAAO,CACLmC,MAASmB,EAAMC,IAAIpB,QAEpB,CAACzC,EAAIY,GAAGZ,EAAI2C,GAAGiB,EAAMC,IAAIpB,mBAG9BvC,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,OACRd,MAAS,OACTwB,YAAa,OAEfP,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,aACZ,CAACF,EAAG,OAAQ,CACbE,YAAa,YACbE,MAAO,CACLmC,MAASmB,EAAMC,IAAII,OAEpB,CAACjE,EAAIY,GAAG,IAAMZ,EAAI2C,GAAGiB,EAAMC,IAAII,KAAOL,EAAMC,IAAII,KAAKC,OAAS,GAAKN,EAAMC,IAAII,KAAKE,UAAU,EAAG,IAAM,MAAQP,EAAMC,IAAII,KAAO,KAAO,gBAG1I/D,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,UACRd,MAAS,OACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,SAAU,CACnBE,YAAa,aACbE,MAAO,CACLC,KAAQP,EAAIoE,cAAcR,EAAMC,IAAI1B,SACpC4B,KAAQ,UAET,CAAC/D,EAAIY,GAAG,IAAMZ,EAAI2C,GAAG3C,EAAIqE,cAAcT,EAAMC,IAAI1B,UAAY,cAGlEjC,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,MACRd,MAAS,OACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,kBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAGiB,EAAMC,IAAIS,KAAO,iBAGjDpE,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,UACRd,MAAS,MACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,wBACbK,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAIuE,iBAAiBX,EAAMC,QAGrC,CAAC3D,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CACbE,YAAa,eACZ,CAACJ,EAAIY,GAAGZ,EAAI2C,GAAGiB,EAAMC,IAAIW,SAAW,QAAStE,EAAG,IAAK,CACtDE,YAAa,4CAIjBF,EAAG,kBAAmB,CACxBI,MAAO,CACLgD,KAAQ,cACRd,MAAS,OACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAGiB,EAAMC,IAAIY,yBAG1CvE,EAAG,kBAAmB,CACxBI,MAAO,CACLoE,MAAS,QACTlC,MAAS,KACTe,MAAS,MACTC,MAAS,UAEXC,YAAazD,EAAI0D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC1D,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,aACbE,MAAO,CACLC,KAAQ,UACRwD,KAAQ,OACRvD,KAAQ,gBACRmE,MAAS,GACTC,SAAkC,IAAtBhB,EAAMC,IAAI1B,SAExB1B,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAI6E,SAASjB,EAAMC,IAAItB,OAGjC,CAACvC,EAAIY,GAAG,IAAMZ,EAAI2C,GAAyB,IAAtBiB,EAAMC,IAAI1B,QAAgB,MAAQ,QAAU,OAAQjC,EAAG,YAAa,CAC1FE,YAAa,aACbE,MAAO,CACLC,KAAQ,SACRwD,KAAQ,OACRvD,KAAQ,gBACRmE,MAAS,IAEXlE,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAI8E,QAAQlB,EAAMmB,OAAQnB,EAAMC,IAAItB,OAG9C,CAACvC,EAAIY,GAAG,WAAY,WAGxB,GAAIV,EAAG,MAAO,CACjBE,YAAa,sBACZ,CAACF,EAAG,gBAAiB,CACtBI,MAAO,CACL0E,aAAc,CAAC,GAAI,GAAI,IAAK,KAC5BC,YAAajF,EAAI+D,KACjBmB,OAAU,0CACVtC,MAAS5C,EAAI4C,MACbuC,WAAc,IAEhB1E,GAAI,CACF2E,cAAepF,EAAIqF,iBACnBC,iBAAkBtF,EAAIuF,wBAErB,IAAK,IAAK,GAAIrF,EAAG,YAAa,CACjCE,YAAa,iBACbE,MAAO,CACLmC,MAAS,UACT+C,QAAWxF,EAAIyF,kBACfC,wBAAwB,EACxBnC,MAAS,OAEX9C,GAAI,CACFkF,iBAAkB,SAAUzE,GAC1BlB,EAAIyF,kBAAoBvE,GAE1B0E,MAAS5F,EAAI6F,oBAEd,CAAC3F,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,UAAW,CAChB4F,IAAK,WACLxF,MAAO,CACLoB,MAAS1B,EAAI+F,SACbC,MAAShG,EAAIgG,MACbC,iBAAkB,QAEnB,CAAC/F,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,cAAeV,EAAG,SAAU,CACrCI,MAAO,CACL4F,OAAU,KAEX,CAAChG,EAAG,SAAU,CACfI,MAAO,CACL6F,KAAQ,KAET,CAACjG,EAAG,eAAgB,CACrBI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,WAAY,CACjBE,YAAa,iBACbE,MAAO,CACL8F,SAAY,IAEd1E,MAAO,CACLC,MAAO3B,EAAI+F,SAASM,WACpBvE,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,aAAchE,IAEvCE,WAAY,wBAEb,CAAC/B,EAAG,IAAK,CACVE,YAAa,iBACbE,MAAO,CACL4B,KAAQ,UAEVA,KAAM,cACD,IAAK,GAAIhC,EAAG,SAAU,CAC3BI,MAAO,CACL6F,KAAQ,KAET,CAACjG,EAAG,eAAgB,CACrBI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,WAAY,CACjBE,YAAa,iBACbE,MAAO,CACL8F,SAAY,IAEd1E,MAAO,CACLC,MAAO3B,EAAI+F,SAAStD,MACpBX,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,QAAShE,IAElCE,WAAY,mBAEb,CAAC/B,EAAG,IAAK,CACVE,YAAa,mBACbE,MAAO,CACL4B,KAAQ,UAEVA,KAAM,cACD,IAAK,IAAK,GAAIhC,EAAG,eAAgB,CACtCI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,WAAY,CACjBE,YAAa,oBACbE,MAAO,CACL8F,SAAY,GACZ7F,KAAQ,WACR+F,KAAQ,GAEV5E,MAAO,CACLC,MAAO3B,EAAI+F,SAAS9B,KACpBnC,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,OAAQhE,IAEjCE,WAAY,oBAEX,IAAK,GAAI/B,EAAG,MAAO,CACtBE,YAAa,gBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,oBACXJ,EAAIY,GAAG,cAAeV,EAAG,eAAgB,CAC3CI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,iBAAkB,CACvBE,YAAa,qBACbsB,MAAO,CACLC,MAAO3B,EAAI+F,SAAS5D,QACpBL,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,UAAWhE,IAEpCE,WAAY,qBAEb,CAAC/B,EAAG,WAAY,CACjBE,YAAa,eACbE,MAAO,CACLkC,MAAS,IAEV,CAACtC,EAAG,IAAK,CACVE,YAAa,oBACXJ,EAAIY,GAAG,WAAYV,EAAG,WAAY,CACpCE,YAAa,eACbE,MAAO,CACLkC,MAAS,IAEV,CAACtC,EAAG,IAAK,CACVE,YAAa,kBACXJ,EAAIY,GAAG,YAAa,IAAK,IAAK,GAAIV,EAAG,MAAO,CAC9CE,YAAa,2BACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,gBACXJ,EAAIY,GAAG,cAAeV,EAAG,MAAO,CAClCE,YAAa,yBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,IAAK,CAACA,EAAG,IAAK,CACnBE,YAAa,iBACXJ,EAAIY,GAAG,uCAAwCV,EAAG,MAAO,CAC3DE,YAAa,cACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,kBACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,cACRwC,QAAWhD,EAAIuG,cAEjB9F,GAAI,CACFC,MAASV,EAAIwG,uBAEd,CAACxG,EAAIY,GAAG,IAAMZ,EAAI2C,GAAG3C,EAAIuG,aAAe,WAAa,WAAa,QAAS,GAAIvG,EAAIyG,mBAAqBvG,EAAG,MAAO,CACnHE,YAAa,aACZ,CAACF,EAAG,KAAM,CACXE,YAAa,gBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,kBACXJ,EAAIY,GAAG,cAAeV,EAAG,MAAO,CAClCE,YAAa,qBACZ,CAACF,EAAG,WAAY,CACjBE,YAAa,sBACbE,MAAO,CACLC,KAAQ,WACR+F,KAAQ,EACRxF,YAAe,uBAEjBY,MAAO,CACLC,MAAO3B,EAAIyG,mBACX3E,SAAU,SAAUC,GAClB/B,EAAIyG,mBAAqB1E,GAE3BE,WAAY,yBAEX,GAAI/B,EAAG,MAAO,CACjBE,YAAa,qBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRC,KAAQ,gBACRuD,KAAQ,SAEVtD,GAAI,CACFC,MAASV,EAAI0G,eAEd,CAAC1G,EAAIY,GAAG,aAAcV,EAAG,YAAa,CACvCI,MAAO,CACLC,KAAQ,UACRC,KAAQ,kBACRuD,KAAQ,SAEVtD,GAAI,CACFC,MAASV,EAAI2G,oBAEd,CAAC3G,EAAIY,GAAG,aAAc,KAAOZ,EAAI4G,SAAkC,GAAxB5G,EAAI+F,SAAS5D,QAAejC,EAAG,MAAO,CAClFE,YAAa,mCACZ,CAACF,EAAG,KAAM,CACXE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,mBACXJ,EAAIY,GAAG,YAAaV,EAAG,eAAgB,CACzCE,YAAa,sBACbE,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,iBAAkB,CACvBE,YAAa,qBACbK,GAAI,CACFoG,OAAU7G,EAAI8G,uBAEhBpF,MAAO,CACLC,MAAO3B,EAAI+G,cACXjF,SAAU,SAAUC,GAClB/B,EAAI+G,cAAgBhF,GAEtBE,WAAY,kBAEb,CAAC/B,EAAG,WAAY,CACjBE,YAAa,eACbE,MAAO,CACLkC,MAAS,OAEV,CAACtC,EAAG,IAAK,CACVE,YAAa,gBACXJ,EAAIY,GAAG,cAAeV,EAAG,WAAY,CACvCE,YAAa,eACbE,MAAO,CACLkC,MAAS,WAEV,CAACtC,EAAG,IAAK,CACVE,YAAa,mBACXJ,EAAIY,GAAG,eAAgB,IAAK,GAA0B,OAAtBZ,EAAI+G,cAAyB7G,EAAG,MAAO,CACzEE,YAAa,sBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,mBACZ,CAACF,EAAG,WAAY,CACjBE,YAAa,gBACbE,MAAO,CACLmC,MAAS,SACTuE,YAAe,kCACfzG,KAAQ,OACR0G,UAAY,EACZC,YAAa,OAEZ,GAAIlH,EAAIyG,mBAAqBvG,EAAG,MAAO,CAC1CE,YAAa,sBACZ,CAACF,EAAG,eAAgB,CACrBI,MAAO,CACLkC,MAAS,WAEV,CAACtC,EAAG,WAAY,CACjBE,YAAa,sBACbE,MAAO,CACLC,KAAQ,WACR+F,KAAQ,EACRxF,YAAe,gBACfsF,SAAY,IAEd1E,MAAO,CACLC,MAAO3B,EAAIyG,mBACX3E,SAAU,SAAUC,GAClB/B,EAAIyG,mBAAqB1E,GAE3BE,WAAY,yBAEX,IAAK,GAAKjC,EAAI4G,KAAM1G,EAAG,eAAgB,CAC1CI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,MAAO,CACZE,YAAa,sBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,oBACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,mBACRwC,QAAWhD,EAAImH,gBAEjB1G,GAAI,CACFC,MAASV,EAAIoH,iBAEd,CAACpH,EAAIY,GAAG,IAAMZ,EAAI2C,GAAG3C,EAAImH,eAAiB,WAAa,WAAa,OAAQnH,EAAIqH,gBAAkBnH,EAAG,MAAO,CAC7GE,YAAa,uBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIqH,gBAAgBvE,SAAU5C,EAAG,SAAU,CACvEI,MAAO,CACLC,KAAQ,UACRwD,KAAQ,SAET,CAAC/D,EAAIY,GAAG,UAAW,GAAKZ,EAAI4G,MAAO,MAAO,GAAK5G,EAAI4G,KAA4B,WAAtB5G,EAAI+G,cAA6B7G,EAAG,MAAO,CACrGE,YAAa,0BACZ,CAACF,EAAG,eAAgB,CACrBE,YAAa,mBACbE,MAAO,CACLkC,MAAS,QACTc,KAAQ,cAET,CAACpD,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,WAAY,CACjBE,YAAa,aACbE,MAAO,CACLQ,YAAe,WACfsF,SAAY,IAEd1E,MAAO,CACLC,MAAO3B,EAAI+F,SAASuB,UACpBxF,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,YAAahE,IAEtCE,WAAY,uBAEb,CAAC/B,EAAG,IAAK,CACVE,YAAa,mBACbE,MAAO,CACL4B,KAAQ,UAEVA,KAAM,aACFhC,EAAG,MAAO,CACdE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRC,KAAQ,kBAEVC,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAIuH,WAAW,gBAGzB,CAACrH,EAAG,YAAa,CAClBsH,YAAa,CACXC,QAAW,gBAEbnH,MAAO,CACLoH,OAAU,2BACVC,kBAAkB,EAClBC,aAAc5H,EAAI6H,gBAEnB,CAAC7H,EAAIY,GAAG,aAAc,GAAIZ,EAAI+F,SAASuB,UAAYpH,EAAG,YAAa,CACpEI,MAAO,CACLC,KAAQ,SACRC,KAAQ,kBAEVC,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAI8H,SAAS9H,EAAI+F,SAASuB,UAAW,gBAG/C,CAACtH,EAAIY,GAAG,YAAcZ,EAAI4G,MAAO,IAAK,MAAO,GAAK5G,EAAI4G,KAAM1G,EAAG,eAAgB,CAChFI,MAAO,CACLkC,MAAS,SAEV,CAACtC,EAAG,WAAY,CACjBE,YAAa,mBACbE,MAAO,CACLC,KAAQ,WACR+F,KAAQ,EACRxF,YAAe,mBAEjBY,MAAO,CACLC,MAAO3B,EAAI+F,SAASgC,QACpBjG,SAAU,SAAUC,GAClB/B,EAAIgC,KAAKhC,EAAI+F,SAAU,UAAWhE,IAEpCE,WAAY,uBAEX,IAAK,GAAKjC,EAAI4G,QAAS,GAAI1G,EAAG,MAAO,CACxCE,YAAa,gBACbE,MAAO,CACL4B,KAAQ,UAEVA,KAAM,UACL,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACLE,KAAQ,iBAEVC,GAAI,CACFC,MAASV,EAAIgI,eAEd,CAAChI,EAAIY,GAAG,SAAUV,EAAG,YAAa,CACnCI,MAAO,CACLC,KAAQ,UACRC,KAAQ,iBAEVC,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAIiI,cAGd,CAACjI,EAAIY,GAAG,UAAW,KAAMV,EAAG,YAAa,CAC1CI,MAAO,CACLmC,MAAS,OACT+C,QAAWxF,EAAIkI,cACf3E,MAAS,OAEX9C,GAAI,CACFkF,iBAAkB,SAAUzE,GAC1BlB,EAAIkI,cAAgBhH,KAGvB,CAAChB,EAAG,WAAY,CACjBI,MAAO,CACL6H,IAAOnI,EAAIoI,eAEV,GAAIlI,EAAG,MAAO,CACjBE,YAAa,sBACbiI,MAAO,CACLC,aAActI,EAAIuI,kBAEnB,CAACrI,EAAG,MAAO,CACZE,YAAa,gBACbK,GAAI,CACFC,MAASV,EAAIwI,oBAEbtI,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,IAAK,CACrBE,YAAa,kBACZ,CAACJ,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcjE,cAAetE,EAAG,YAAa,CACjEE,YAAa,YACbE,MAAO,CACLC,KAAQ,OACRC,KAAQ,iBAEVC,GAAI,CACFC,MAASV,EAAIwI,qBAEZ,GAAItI,EAAG,MAAO,CACjBE,YAAa,cACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,YACbiI,MAAO,CACLK,OAA0B,UAAlB1I,EAAI2I,WAEdlI,GAAI,CACFC,MAAS,SAAUQ,GACjBlB,EAAI2I,UAAY,WAGnB,CAACzI,EAAG,IAAK,CACVE,YAAa,uBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAG,YAAaV,EAAG,MAAO,CAC5CE,YAAa,YACbiI,MAAO,CACLK,OAA0B,SAAlB1I,EAAI2I,WAEdlI,GAAI,CACFC,MAAS,SAAUQ,GACjBlB,EAAI2I,UAAY,UAGnB,CAACzI,EAAG,IAAK,CACVE,YAAa,kBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAG,YAAaV,EAAG,MAAO,CAC5CE,YAAa,YACbiI,MAAO,CACLK,OAA0B,UAAlB1I,EAAI2I,WAEdlI,GAAI,CACFC,MAAS,SAAUQ,GACjBlB,EAAI2I,UAAY,WAGnB,CAACzI,EAAG,IAAK,CACVE,YAAa,mBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAG,YAAaV,EAAG,MAAO,CAC5CE,YAAa,YACbiI,MAAO,CACLK,OAA0B,YAAlB1I,EAAI2I,WAEdlI,GAAI,CACFC,MAAS,SAAUQ,GACjBlB,EAAI2I,UAAY,aAGnB,CAACzI,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CAACF,EAAIY,GAAG,cAAeV,EAAG,MAAO,CAC9CE,YAAa,gBACZ,CAAmB,UAAlBJ,EAAI2I,UAAwBzI,EAAG,MAAO,CACxCE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,SAAUV,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcjE,cAAetE,EAAG,MAAO,CACrGE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,WAAYV,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcG,SAAW,YAAa1I,EAAG,MAAO,CAChHE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,WAAYV,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcI,OAAS,YAAa3I,EAAG,MAAO,CAC9GE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,SAAUV,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcK,SAAW,YAAa5I,EAAG,MAAO,CAC9GE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,WAAYV,EAAG,OAAQ,CAC7CE,YAAa,eACZ,CAACJ,EAAIY,GAAG,IAAMZ,EAAI2C,GAAG3C,EAAIyI,cAAcM,aAAe,aAAc7I,EAAG,MAAO,CAC/EE,YAAa,aACZ,CAACF,EAAG,QAAS,CAACF,EAAIY,GAAG,WAAYV,EAAG,OAAQ,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcO,WAAa,kBAAoBhJ,EAAI4G,KAAwB,SAAlB5G,EAAI2I,UAAuBzI,EAAG,MAAO,CAC9JE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,aACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,aACZ,CAACF,EAAG,KAAM,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG3C,EAAIyI,cAAcQ,cAAe/I,EAAG,IAAK,CAACF,EAAIY,GAAG,OAASZ,EAAI2C,GAAG3C,EAAIyI,cAAcS,eAAgBhJ,EAAG,IAAK,CAACF,EAAIY,GAAG,QAAUZ,EAAI2C,GAAG3C,EAAIyI,cAAcU,uBAAwBjJ,EAAG,IAAK,CAACF,EAAIY,GAAG,UAAWV,EAAG,SAAU,CAChPI,MAAO,CACLC,KAA0C,WAAlCP,EAAIyI,cAAcW,YAA2B,UAAY,UACjErF,KAAQ,SAET,CAAC/D,EAAIY,GAAG,IAAMZ,EAAI2C,GAAqC,WAAlC3C,EAAIyI,cAAcW,YAA2B,KAAO,MAAQ,QAAS,WAAapJ,EAAI4G,KAAwB,UAAlB5G,EAAI2I,UAAwBzI,EAAG,MAAO,CACxJE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,aACZ,CAACJ,EAAIoC,GAAGpC,EAAIyI,cAAcY,OAAO,SAAUC,GAC5C,OAAOpJ,EAAG,MAAO,CACfoB,IAAKgI,EAAK/G,GACVnC,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVmI,MAAOrI,EAAIuJ,YAAYD,EAAK/I,UACxBL,EAAG,MAAO,CACdE,YAAa,aACZ,CAACF,EAAG,KAAM,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG2G,EAAKxG,SAAU5C,EAAG,IAAK,CAACF,EAAIY,GAAGZ,EAAI2C,GAAG2G,EAAKE,gBAAiBtJ,EAAG,IAAK,CAC9FE,YAAa,aACZ,CAACJ,EAAIY,GAAGZ,EAAI2C,GAAG2G,EAAKvF,WAAY7D,EAAG,MAAO,CAC3CE,YAAa,gBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,OACRwD,KAAQ,QAEVtD,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAIyJ,YAAYH,MAG1B,CAACpJ,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,UAAWV,EAAG,YAAa,CACpCI,MAAO,CACLC,KAAQ,OACRwD,KAAQ,QAEVtD,GAAI,CACFC,MAAS,SAAUQ,GACjB,OAAOlB,EAAI0J,aAAaJ,MAG3B,CAACpJ,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIY,GAAG,WAAY,QACpBZ,EAAIyI,cAAcY,OAA4C,IAAnCrJ,EAAIyI,cAAcY,MAAMnF,OAIpBlE,EAAI4G,KAJ+B1G,EAAG,MAAO,CAC/EE,YAAa,eACZ,CAACF,EAAG,IAAK,CACVE,YAAa,0BACXF,EAAG,IAAK,CAACF,EAAIY,GAAG,eAA2B,OAASZ,EAAI4G,KAAwB,YAAlB5G,EAAI2I,UAA0BzI,EAAG,MAAO,CACxGE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,oBACZ,CAACJ,EAAIoC,GAAGpC,EAAIyI,cAAckB,SAAS,SAAUC,GAC9C,OAAO1J,EAAG,MAAO,CACfoB,IAAKsI,EAAOrH,GACZnC,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACXF,EAAG,MAAO,CACZE,YAAa,oBACZ,CAACF,EAAG,KAAM,CAACF,EAAIY,GAAGZ,EAAI2C,GAAGiH,EAAOlC,WAAYxH,EAAG,IAAK,CAACF,EAAIY,GAAGZ,EAAI2C,GAAGiH,EAAO5C,gBAAiB9G,EAAG,OAAQ,CACvGE,YAAa,iBACZ,CAACJ,EAAIY,GAAGZ,EAAI2C,GAAGiH,EAAOC,gBACtB7J,EAAIyI,cAAckB,SAAgD,IAArC3J,EAAIyI,cAAckB,QAAQzF,OAIxBlE,EAAI4G,KAJmC1G,EAAG,MAAO,CACnFE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,IAAK,CAACF,EAAIY,GAAG,eAA2B,OAASZ,EAAI4G,cAAe,IAEzEkD,EAAkB,CAAC,WACrB,IAAI9J,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,iBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIY,GAAG,cAAeV,EAAG,IAAK,CAChCE,YAAa,iBACZ,CAACJ,EAAIY,GAAG,qBACV,WACD,IAAIZ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,KAAM,CACdE,YAAa,eACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,cACV,WACD,IAAIZ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,KAAM,CACdE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,gBACV,WACD,IAAIZ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,KAAM,CACdE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,eACV,WACD,IAAIZ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,eACZ,CAACF,EAAG,IAAK,CACVE,YAAa,0BAEd,WACD,IAAIJ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,KAAM,CACdE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,mBACXJ,EAAIY,GAAG,aACV,WACD,IAAIZ,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,KAAM,CACdE,YAAa,iBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIY,GAAG,cAMTmJ,EAAarK,EAAoB,QAKJsK,EAAgC,CAC/DlH,KAAM,OACNmH,WAAY,CACVC,UAAWH,EAAW,MAExBI,OACE,MAAO,CACLC,QAAS,OACTlH,KAAM,GACNN,MAAO,EACPyH,KAAM,EACNtG,KAAM,GACNnC,OAAQ,CACNC,QAAS,GACTyI,QAAS,EACTnI,SAAU,GAEZa,SAAS,EACTuH,IAAK,WACL9H,MAAO,MACP+H,KAAM,GACN/E,mBAAmB,EACnB2C,WAAY,GACZF,eAAe,EAEfK,iBAAiB,EACjBI,UAAW,QACXF,cAAe,GAEflC,cAAc,EACdE,mBAAoB,GACpBgE,cAAe,GAGf1D,cAAe,SAEfI,gBAAgB,EAEhBE,gBAAiB,KAEjBtB,SAAU,CACRtD,MAAO,GACPiI,OAAQ,GAEV1E,MAAO,CACLvD,MAAO,CAAC,CACNkI,UAAU,EACVC,QAAS,QACTC,QAAS,SAEXvD,UAAW,CAAC,CACVqD,UAAU,EACVC,QAAS,QACTC,QAAS,UAGbC,eAAgB,QAChBC,QAAS,CAAC,CACRxI,IAAK,EACLE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OAETJ,SAAU,CAAC,CACTE,IAAK,EACLE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,OACN,CACDF,GAAI,EACJE,MAAO,UAIb0H,UACElK,KAAK+K,UACL/K,KAAKgL,mBAELC,SAASC,iBAAiB,UAAWlL,KAAKmL,gBAE5CjB,gBAEEe,SAASG,oBAAoB,UAAWpL,KAAKmL,gBAE/CE,QAAS,CAEPnB,cAAcoB,GACZ,MAAMC,EAAY,CAChBC,EAAG,UAEHC,EAAG,UAEHC,EAAG,WAEL,OAAOH,EAAUD,IAAW,QAG9BpB,cAAcoB,GACZ,MAAMC,EAAY,CAChBC,EAAG,MACHC,EAAG,MACHC,EAAG,OAEL,OAAOH,EAAUD,IAAW,MAG9BpB,aACElK,KAAKoK,KAAO,EACZpK,KAAK8D,KAAO,GACZ9D,KAAK+K,WAGPb,cACElK,KAAK2B,OAAS,CACZC,QAAS,GACTM,SAAU,GAEZlC,KAAKsB,cAGP4I,oBAEMlK,KAAK2L,MAAM7F,UACb9F,KAAK2L,MAAM7F,SAAS8F,cAEtB5L,KAAK8F,SAAW,CACdtD,MAAO,GACPiI,OAAQ,EACRvI,QAAS,EACTkE,WAAY,GACZpC,KAAM,GACNqD,UAAW,GACXS,QAAS,KAIboC,eACElK,KAAK4F,oBACL5F,KAAKwF,mBAAoB,EAEzBxF,KAAKwG,mBAAqB,IAG5B0D,mBAEE2B,WAAW,KACT7L,KAAKwK,cAAgB,CAAC,CACpBlI,GAAI,EACJE,MAAO,OACPsJ,cAAe,mDACfC,cAAe,iBACfC,cAAe,OACfC,iBAAkB,6fAwBjB,CACD3J,GAAI,EACJE,MAAO,OACPsJ,cAAe,kDACfC,cAAe,gBACfC,cAAe,MACfC,iBAAkB,wYA0BjB,CACD3J,GAAI,EACJE,MAAO,SACPsJ,cAAe,kDACfC,cAAe,kBACfC,cAAe,OACfC,iBAAkB,+WAyBjB,CACD3J,GAAI,EACJE,MAAO,OACPsJ,cAAe,iDACfC,cAAe,iBACfC,cAAe,OACfC,iBAAkB,wSAoBjB,CACD3J,GAAI,EACJE,MAAO,OACPsJ,cAAe,gDACfC,cAAe,kBACfC,cAAe,OACfC,iBAAkB,kTAuBnB,MAGL/B,6BACE,GAAKlK,KAAK8F,SAAStD,OAAUxC,KAAK8F,SAASM,WAA3C,CAIApG,KAAKsG,cAAe,EACpB,IAEE,MAAM4F,EAAkBlM,KAAKmM,uBAC7B,IAAKD,EAGH,OAFAlM,KAAKoM,SAASC,QAAQ,qCACtBrM,KAAKsG,cAAe,GAKtB,MAAMgG,QAAmBtM,KAAKuM,gBAGxBC,QAAiBxM,KAAKyM,oBAGtBzM,KAAK0M,qBAAqBR,EAAiBI,EAAYE,GAC7DxM,KAAKoM,SAASO,QAAQ,cAGK,OAAvB3M,KAAK8G,eACP+E,WAAW,KACT7L,KAAKmH,kBACJ,KAEL,MAAOyF,GACPC,QAAQD,MAAM,UAAWA,GACzB5M,KAAKoM,SAASQ,MAAM,cACpB,QACA5M,KAAKsG,cAAe,QAjCpBtG,KAAKoM,SAASC,QAAQ,eAqC1BnC,uBAEE,MAAM1H,EAAQxC,KAAK8F,SAAStD,MAAMsK,cAC5BC,EAAY/M,KAAK8F,SAASM,WAAW0G,cAGrCE,EAAa,CAAC,CAClBC,SAAU,CAAC,KAAM,KAAM,KAAM,MAC7BC,WAAY,GACX,CACDD,SAAU,CAAC,KAAM,KAAM,MACvBC,WAAY,GACX,CACDD,SAAU,CAAC,OAAQ,KAAM,KAAM,MAC/BC,WAAY,GACX,CACDD,SAAU,CAAC,KAAM,KAAM,MACvBC,WAAY,GACX,CACDD,SAAU,CAAC,KAAM,KAAM,MACvBC,WAAY,IAEd,IAAK,MAAMC,KAAQH,EACjB,GAAIG,EAAKF,SAASG,KAAKxL,GAAWY,EAAM6K,SAASzL,IAAYmL,EAAUM,SAASzL,IAC9E,OAAO5B,KAAKwK,cAAc8C,KAAKhN,GAAQA,EAAKgC,KAAO6K,EAAKD,YAK5D,OAAOlN,KAAKwK,cAAc8C,KAAKhN,GAAoB,IAAZA,EAAKgC,KAG9C4H,sBAEE,OAAO,IAAIqD,QAAQC,IACjB3B,WAAW,KACT2B,EAAQ,CACN3K,KAAM7C,KAAK8F,SAASvB,SAAW,KAC/BoE,QAAS,qBACTC,MAAO,cACPC,QAAS,iBACTC,YAAa,YACbC,UAAW,OACX0E,UAAW,aACXC,WAAY,gBAEb,QAIPxD,oBAEE,OAAO,IAAIqD,QAAQC,IACjB3B,WAAW,KACT2B,EAAQ,CACN3K,KAAM,KACN+F,MAAO5I,KAAK8F,SAASzB,KAAO,cAC5BsJ,cAAe,aACfrC,OAAQ,YAET,QAIPpB,2BAA2B0D,EAAUtB,EAAYE,GAC/C,OAAO,IAAIe,QAAQC,IACjB3B,WAAW,KAET,IAAI/D,EAAU8F,EAAS3B,iBAGvBnE,EAAUA,EAAQ+F,QAAQ,uBAAwBvB,EAAWzJ,MAC7DiF,EAAUA,EAAQ+F,QAAQ,uBAAwBvB,EAAWxD,aAC7DhB,EAAUA,EAAQ+F,QAAQ,qBAAsBvB,EAAWmB,WAC3D3F,EAAUA,EAAQ+F,QAAQ,sBAAuBvB,EAAWoB,YAG5D5F,EAAUA,EAAQ+F,QAAQ,qBAAsBrB,EAAS3J,MAGzDiF,EAAUA,EAAQ+F,QAAQ,yBAAyB,IAAIC,MAAOC,mBAAmB,UACjFjG,EAAUA,EAAQ+F,QAAQ,yBAA0B,cACpD/F,EAAUA,EAAQ+F,QAAQ,yBAA0B,gBACpD/F,EAAUA,EAAQ+F,QAAQ,4BAA6B,iBAGnD7N,KAAK8F,SAAS9B,OAChB8D,EAAUA,EAAQ+F,QAAQ,0BAA2B7N,KAAK8F,SAAS9B,MACnE8D,EAAUA,EAAQ+F,QAAQ,2BAA4B7N,KAAK8F,SAAS9B,MACpE8D,EAAUA,EAAQ+F,QAAQ,gCAAiC7N,KAAK8F,SAAS9B,MACzE8D,EAAUA,EAAQ+F,QAAQ,gCAAiC7N,KAAK8F,SAAS9B,OAI3E8D,EAAUA,EAAQ+F,QAAQ,iBAAkB,SAC5C7N,KAAKwG,mBAAqBsB,EAAQkG,OAClCR,KACC,QAIPtD,eACOlK,KAAKwG,oBAMVxG,KAAK8F,SAASgC,QAAU9H,KAAKwG,mBAG7BxG,KAAK8F,SAAS5D,QAAU,EACxBlC,KAAKoM,SAASO,QAAQ,kBATpB3M,KAAKoM,SAASC,QAAQ,gBAY1BnC,oBACElK,KAAKwG,mBAAqB,GAC1BxG,KAAKuG,wBAGP2D,sBAAsB+D,GAEL,OAAXA,GAEFjO,KAAK8F,SAASuB,UAAY,GAC1BrH,KAAKoH,gBAAkB,OAGvBpH,KAAKwG,mBAAqB,GAC1BxG,KAAKoH,gBAAkB,OAI3B8C,uBACE,GAAKlK,KAAKwG,mBAAV,CAIAxG,KAAKkH,gBAAiB,EACtB,UAEQ,IAAIqG,QAAQC,GAAW3B,WAAW2B,EAAS,OAIjD,MAAMU,EAAW,OAAOlO,KAAK8F,SAASvB,SAAW,UAAS,IAAIuJ,MAAOK,iBACrEnO,KAAKoH,gBAAkB,CACrBvE,KAAMqL,EACNE,KAAM,2BAA2BF,EACjCpK,KAAM,QAIR9D,KAAK8F,SAASuB,UAAYrH,KAAKoH,gBAAgBgH,KAC/CpO,KAAKoM,SAASO,QAAQ,UACtB,MAAOC,GACPC,QAAQD,MAAM,UAAWA,GACzB5M,KAAKoM,SAASQ,MAAM,cACpB,QACA5M,KAAKkH,gBAAiB,QAxBtBlH,KAAKoM,SAASC,QAAQ,cA2B1BnC,WAAWmE,GACTrO,KAAKqO,MAAQA,EACbxB,QAAQyB,IAAItO,KAAKqO,QAEnBnE,YACElK,KAAK2B,OAAS,CACZC,QAAS,GACTyI,OAAQ,IAEVrK,KAAK+K,WAEPb,SAAS5H,GAEG,GAANA,EACFtC,KAAKuO,QAAQjM,GAEbtC,KAAK8F,SAAW,CACdtD,MAAO,GACPwB,KAAM,KAIZkG,QAAQ5H,GACN,IAAIkM,EAAQxO,KAGZ,MAAMyO,EAAW,CAAC,CAChBnM,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,mDACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,UACZiB,UAAW,GACXS,QAAS,IACR,CACDxF,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,gCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,UACZiB,UAAW,GACXS,QAAS,IACR,CACDxF,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,YACPwB,KAAM,uCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,YACZiB,UAAW,0CACXS,QAAS,qBACR,CACDxF,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,iCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,UACZiB,UAAW,GACXS,QAAS,IACR,CACDxF,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,YACPwB,KAAM,mCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,YACZiB,UAAW,GACXS,QAAS,KAIL4G,EAAYD,EAASnB,KAAKjL,GAAQA,EAAKC,KAAOA,GAChDoM,GACFF,EAAM1I,SAAW,IACZ4I,GAELF,EAAMhJ,mBAAoB,EAC1BqH,QAAQyB,IAAI,aAAcE,EAAM1I,WAEhC0I,EAAMpC,SAAS,CACb9L,KAAM,QACNqK,QAAS,iBAmBfT,QAAQ5H,GACNtC,KAAK2O,SAAS,UAAW,KAAM,CAC7BC,kBAAmB,KACnBC,iBAAkB,KAClBvO,KAAM,YACLwO,KAAK,KACN9O,KAAK+O,cAAc/O,KAAKsK,IAAM,cAAgBhI,GAAIwM,KAAKE,IACpC,KAAbA,EAAKC,KACPjP,KAAKoM,SAAS,CACZ9L,KAAM,UACNqK,QAASqE,EAAKE,MAGhBlP,KAAKoM,SAAS,CACZ9L,KAAM,QACNqK,QAASqE,EAAKE,UAInBC,MAAM,KACPnP,KAAKoM,SAAS,CACZ9L,KAAM,QACNqK,QAAS,aAIfT,QAAQkF,EAAO9M,GACbtC,KAAK2O,SAAS,WAAY,KAAM,CAC9BC,kBAAmB,KACnBC,iBAAkB,KAClBvO,KAAM,YACLwO,KAAK,KACN9O,KAAK+O,cAAc/O,KAAKsK,IAAM,aAAehI,GAAIwM,KAAKE,IACnC,KAAbA,EAAKC,OACPjP,KAAKoM,SAAS,CACZ9L,KAAM,UACNqK,QAAS,UAEX3K,KAAKiD,KAAKoM,OAAOD,EAAO,QAG3BD,MAAM,KACPnP,KAAKoM,SAAS,CACZ9L,KAAM,QACNqK,QAAS,aAIfT,UACElK,KAAKsP,QAAQC,GAAG,IAElBrF,aACElK,KAAKoK,KAAO,EACZpK,KAAK8D,KAAO,GACZ9D,KAAK+K,WAGPb,cAAcsF,GAEU,KAAlBA,EAAMpO,UACJpB,KAAKwF,mBACPxF,KAAK+H,eAEH/H,KAAKsI,iBACPtI,KAAKuI,qBAKX2B,iBAAiBtG,GACfiJ,QAAQyB,IAAI,WAAY1K,GAGxB5D,KAAKwI,cAAgB,IAChB5E,EAEH+E,QAAS3I,KAAKyP,iBACd7G,MAAO5I,KAAK0P,gBACZ7G,QAAS7I,KAAK2P,kBACd7G,YAAa9I,KAAK4P,qBAClB7G,UAAW/I,KAAK6P,mBAEhB7G,UAAWhJ,KAAK8P,iBAAiBlM,EAAIS,KACrC4E,WAAYrF,EAAIS,IAChB6E,mBAAoBlJ,KAAK+P,uBACzB5G,YAAa,SAEbC,MAAOpJ,KAAKgQ,cAAcpM,EAAIW,SAE9BmF,QAAS1J,KAAKiQ,gBAAgBrM,EAAIW,UAEpCvE,KAAK0I,UAAY,QACjB1I,KAAKsI,iBAAkB,GAGzB4B,mBACElK,KAAKsI,iBAAkB,EACvBtI,KAAKwI,cAAgB,IAGvB0B,YAAYgG,GACV,MAAMC,EAAU,CACdC,IAAO,mBACPC,IAAO,mBACPC,KAAQ,mBACRC,IAAO,kBACPC,KAAQ,kBACRC,IAAO,kBACPC,IAAO,wBACPC,IAAO,yBAET,OAAOR,EAAQD,IAAa,oBAG9BhG,YAAYb,GACVwD,QAAQyB,IAAI,QAASjF,GACrBrJ,KAAKoM,SAAS7B,KAAK,iBAGrBL,aAAab,GACXwD,QAAQyB,IAAI,QAASjF,GACrBrJ,KAAKoM,SAASO,QAAQ,WAAatD,EAAKxG,OAG1CqH,iBACE,MAAM0G,EAAW,CAAC,SAAU,SAAU,SAAU,UAC1CC,EAASD,EAASE,KAAKC,MAAMD,KAAKE,SAAWJ,EAAS3M,SACtDgN,EAAO,KAAOH,KAAKC,MAAsB,GAAhBD,KAAKE,UAC9BE,EAAQC,OAAOL,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAAGI,SAAS,EAAG,KAC/DC,EAAMF,OAAOL,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAAGI,SAAS,EAAG,KAC7DE,EAASH,OAAOL,KAAKC,MAAsB,KAAhBD,KAAKE,WAAkBI,SAAS,EAAG,KACpE,MAAO,GAAGP,IAASI,IAAOC,IAAQG,IAAMC,KAE1CpH,gBACE,MAAM0G,EAAW,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAC/CC,EAASD,EAASE,KAAKC,MAAMD,KAAKE,SAAWJ,EAAS3M,SACtDqN,EAASH,OAAOL,KAAKC,MAAsB,IAAhBD,KAAKE,WAAuBI,SAAS,EAAG,KACzE,MAAO,GAAGP,IAASS,KAErBpH,kBACE,MAAMqH,EAAY,CAAC,iBAAkB,oBAAqB,oBAAqB,mBAAoB,gBACnG,OAAOA,EAAUT,KAAKC,MAAMD,KAAKE,SAAWO,EAAUtN,UAExDiG,qBACE,MAAMsH,EAAU,CAAC,WAAY,YAAa,YAAa,YAAa,cACpE,OAAOA,EAAQV,KAAKC,MAAMD,KAAKE,SAAWQ,EAAQvN,UAEpDiG,mBACE,MAAMuH,EAAQ,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,QAC/C,OAAOA,EAAMX,KAAKC,MAAMD,KAAKE,SAAWS,EAAMxN,UAEhDiG,iBAAiBtB,GACf,MAAM8I,EAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACzDC,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACtDC,EAAUF,EAASZ,KAAKC,MAAMD,KAAKE,SAAWU,EAASzN,SACvDpB,EAAO8O,EAAMb,KAAKC,MAAMD,KAAKE,SAAWW,EAAM1N,SACpD,MAAO,GAAG2N,IAAU/O,KAEtBqH,uBACE,MAAM+G,EAAO,KAAOH,KAAKC,MAAsB,EAAhBD,KAAKE,UAC9BE,EAAQC,OAAOL,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAAGI,SAAS,EAAG,KAC/DC,EAAMF,OAAOL,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,GAAGI,SAAS,EAAG,KACnE,MAAO,GAAGH,KAAQC,KAASG,cAE7BnH,cAAc2H,GACZ,MAAO,CAAC,CACNvP,GAAI,EACJO,KAASgP,EAAH,WACNvR,KAAM,MACNwD,KAAM,QACNyF,YAAa,uBACZ,CACDjH,GAAI,EACJO,KAASgP,EAAH,YACNvR,KAAM,MACNwD,KAAM,QACNyF,YAAa,uBACZ,CACDjH,GAAI,EACJO,KAASgP,EAAH,YACNvR,KAAM,MACNwD,KAAM,QACNyF,YAAa,yBAGjBW,gBAAgB2H,GACd,MAAO,CAAC,CACNvP,GAAI,EACJmF,OAAQ,UACRV,YAAa,MAAM8K,UACnBjI,KAAM,uBACL,CACDtH,GAAI,EACJmF,OAAQ,SACRV,YAAa,iBACb6C,KAAM,uBACL,CACDtH,GAAI,EACJmF,OAAQ,QACRV,YAAa,kBACb6C,KAAM,uBACL,CACDtH,GAAI,EACJmF,OAAQ,SACRV,YAAa,eACb6C,KAAM,yBAGVM,UACE,IAAIsE,EAAQxO,KACZwO,EAAMzL,SAAU,EAGhB,MAAM0L,EAAW,CAAC,CAChBnM,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,mDACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,WACX,CACD9D,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,gCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,WACX,CACD9D,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,YACPwB,KAAM,uCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,YACZiB,UAAW,0CACXS,QAAS,qBACR,CACDxF,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,UACPwB,KAAM,iCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,WACX,CACD9D,GAAI,EACJuB,SAAU,cACVvD,KAAM,MACNkC,MAAO,YACPwB,KAAM,mCACN9B,QAAS,EACTmC,IAAK,cACLE,QAAS,KACTC,YAAa,sBACb4B,WAAY,cAIdyF,WAAW,KACT,IACEgB,QAAQyB,IAAI,kBAGZ,IAAIwD,EAAerD,EACnB,GAAID,EAAM7M,OAAOC,SAAW4M,EAAM7M,OAAOC,QAAQoM,OAAQ,CACvD,MAAMpM,EAAU4M,EAAM7M,OAAOC,QAAQoM,OAAOlB,cAC5CgF,EAAerD,EAASsD,OAAO1P,GAAQA,EAAKwB,SAASiJ,cAAcO,SAASzL,IAAYS,EAAKG,MAAMsK,cAAcO,SAASzL,IAAYS,EAAKgC,IAAIgJ,SAASzL,IAAYS,EAAKkC,QAAQ8I,SAASzL,KAI9J,IAA1B4M,EAAM7M,OAAOO,SAA2C,KAAzBsM,EAAM7M,OAAOO,UAC9C4P,EAAeA,EAAaC,OAAO1P,GAAQA,EAAKH,UAAYsM,EAAM7M,OAAOO,UAI3E,MAAM8P,GAAcxD,EAAMpE,KAAO,GAAKoE,EAAM1K,KACtCmO,EAAWD,EAAaxD,EAAM1K,KAC9BoO,EAAWJ,EAAaK,MAAMH,EAAYC,GAChDzD,EAAMvL,KAAOiP,EACb1D,EAAM7L,MAAQmP,EAAa7N,OAC3BuK,EAAMzL,SAAU,EAChB8J,QAAQyB,IAAI,aAAcE,EAAMvL,MAChC4J,QAAQyB,IAAI,MAAOE,EAAM7L,OACzB,MAAOiK,GACPC,QAAQD,MAAM,eAAgBA,GAC9B4B,EAAMvL,KAAO,GACbuL,EAAM7L,MAAQ,EACd6L,EAAMzL,SAAU,IAEjB,MAkBLmH,WACE,IAAIsE,EAAQxO,KACZA,KAAK2L,MAAM,YAAYyG,SAASC,IAC9B,IAAIA,EAiCF,OAAO,EA/BPxF,QAAQyB,IAAI,WAAYtO,KAAK8F,UAG7B+F,WAAW,KACT2C,EAAMpC,SAAS,CACb9L,KAAM,UACNqK,QAAS,iBAEX3K,KAAK+K,UACLyD,EAAMhJ,mBAAoB,GACzB,QAyBT0E,iBAAiBoI,GACftS,KAAK8D,KAAOwO,EACZtS,KAAK+K,WAEPb,oBAAoBoI,GAClBtS,KAAKoK,KAAOkI,EACZtS,KAAK+K,WAEPb,cAAcqI,GACI,KAAZA,EAAItD,MACNjP,KAAKoM,SAASO,QAAQ,QACtB3M,KAAK8F,SAAS9F,KAAKqO,OAASkE,EAAIvP,KAAKsH,KAErCtK,KAAKoM,SAASQ,MAAM2F,EAAIrD,MAG5BhF,UAAUb,GACRrJ,KAAKmI,WAAakB,EAClBrJ,KAAKiI,eAAgB,GAEvBiC,aAAab,GACX,MAAMmJ,EAAa,0BAA0BC,KAAKpJ,EAAK/I,MAClDkS,GACHxS,KAAKoM,SAASQ,MAAM,cAIxB1C,SAASb,EAAM6E,GACb,IAAIM,EAAQxO,KACZwO,EAAMkE,WAAW,6BAA+BrJ,GAAMyF,KAAKE,IACxC,KAAbA,EAAKC,MACPT,EAAM1I,SAASoI,GAAY,GAC3BM,EAAMpC,SAASO,QAAQ,UAEvB6B,EAAMpC,SAASQ,MAAMoC,EAAKE,UAOFyD,EAAuC,EAKrEC,GAHqEnT,EAAoB,QAGnEA,EAAoB,SAW1CoT,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACA7S,EACA+J,GACA,EACA,KACA,WACA,MAIwClK,EAAoB,WAAckT,EAAiB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-1ca8c59e\"],{\"03b7\":function(e,t,s){},\"0798\":function(e,t,s){\"use strict\";s(\"03b7\")},bafd:function(e,t,s){\"use strict\";s.r(t);var a=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"lawyer-letter-container\"},[t(\"div\",{staticClass:\"page-header\"},[t(\"div\",{staticClass:\"header-content\"},[e._m(0),t(\"div\",{staticClass:\"header-actions\"},[t(\"el-button\",{staticClass:\"refresh-btn\",attrs:{type:\"primary\",icon:\"el-icon-refresh\"},on:{click:e.refulsh}},[e._v(\" 刷新数据 \")])],1)])]),t(\"div\",{staticClass:\"search-section\"},[t(\"el-card\",{staticClass:\"search-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"search-form\"},[t(\"div\",{staticClass:\"search-row\"},[t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"关键词搜索\")]),t(\"el-input\",{staticClass:\"search-input\",attrs:{placeholder:\"请输入工单号/标题/用户手机号\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,\"keyword\",t)},expression:\"search.keyword\"}},[t(\"i\",{staticClass:\"el-input__icon el-icon-search\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"处理状态\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择处理状态\",clearable:\"\"},model:{value:e.search.is_deal,callback:function(t){e.$set(e.search,\"is_deal\",t)},expression:\"search.is_deal\"}},e._l(e.options1,(function(e){return t(\"el-option\",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1)]),t(\"div\",{staticClass:\"search-actions\"},[t(\"el-button\",{staticClass:\"search-btn\",attrs:{type:\"primary\",icon:\"el-icon-search\"},on:{click:e.searchData}},[e._v(\" 搜索 \")]),t(\"el-button\",{staticClass:\"reset-btn\",attrs:{icon:\"el-icon-refresh-left\"},on:{click:e.clearSearch}},[e._v(\" 重置 \")])],1)])])],1),t(\"div\",{staticClass:\"table-section\"},[t(\"el-card\",{staticClass:\"table-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"table-header\"},[t(\"div\",{staticClass:\"table-title\"},[t(\"h3\",[t(\"i\",{staticClass:\"el-icon-tickets\"}),e._v(\" 律师函工单列表 \")]),t(\"span\",{staticClass:\"table-count\"},[e._v(\"共 \"+e._s(e.total)+\" 条记录\")])])]),t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"lawyer-table\",attrs:{data:e.list,stripe:\"\",border:\"\",\"empty-text\":\"暂无律师函工单数据\"}},[t(\"el-table-column\",{attrs:{prop:\"order_sn\",label:\"工单号\",width:\"140\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"order-cell\"},[t(\"i\",{staticClass:\"el-icon-document-copy\"}),t(\"span\",{staticClass:\"order-text\"},[e._v(e._s(s.row.order_sn))])])]}}])}),t(\"el-table-column\",{attrs:{prop:\"type\",label:\"工单类型\",width:\"120\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-tag\",{attrs:{type:\"primary\",size:\"small\"}},[e._v(\" \"+e._s(s.row.type||\"律师函\")+\" \")])]}}])}),t(\"el-table-column\",{attrs:{prop:\"title\",label:\"工单标题\",\"min-width\":\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"title-cell\"},[t(\"span\",{staticClass:\"title-text\",attrs:{title:s.row.title}},[e._v(e._s(s.row.title))])])]}}])}),t(\"el-table-column\",{attrs:{prop:\"desc\",label:\"工单内容\",\"min-width\":\"250\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"desc-cell\"},[t(\"span\",{staticClass:\"desc-text\",attrs:{title:s.row.desc}},[e._v(\" \"+e._s(s.row.desc?s.row.desc.length>50?s.row.desc.substring(0,50)+\"...\":s.row.desc:\"-\")+\" \")])])]}}])}),t(\"el-table-column\",{attrs:{prop:\"is_deal\",label:\"处理状态\",width:\"120\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"el-tag\",{staticClass:\"status-tag\",attrs:{type:e.getStatusType(s.row.is_deal),size:\"small\"}},[e._v(\" \"+e._s(e.getStatusText(s.row.is_deal))+\" \")])]}}])}),t(\"el-table-column\",{attrs:{prop:\"uid\",label:\"用户手机\",width:\"130\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"phone-cell\"},[t(\"i\",{staticClass:\"el-icon-phone\"}),t(\"span\",[e._v(e._s(s.row.uid||\"-\"))])])]}}])}),t(\"el-table-column\",{attrs:{prop:\"dt_name\",label:\"债务人\",width:\"120\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"debtor-cell clickable\",on:{click:function(t){return e.showDebtorDetail(s.row)}}},[t(\"i\",{staticClass:\"el-icon-user\"}),t(\"span\",{staticClass:\"debtor-name\"},[e._v(e._s(s.row.dt_name||\"-\"))]),t(\"i\",{staticClass:\"el-icon-arrow-right arrow-icon\"})])]}}])}),t(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"发起时间\",width:\"180\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"time-cell\"},[t(\"i\",{staticClass:\"el-icon-time\"}),t(\"span\",[e._v(e._s(s.row.create_time))])])]}}])}),t(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\",width:\"200\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(s){return[t(\"div\",{staticClass:\"action-buttons\"},[t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-check\",plain:\"\",disabled:2===s.row.is_deal},on:{click:function(t){return e.editData(s.row.id)}}},[e._v(\" \"+e._s(2===s.row.is_deal?\"已完成\":\"完成制作\")+\" \")]),t(\"el-button\",{staticClass:\"action-btn\",attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-close\",plain:\"\"},on:{click:function(t){return e.delData(s.$index,s.row.id)}}},[e._v(\" 取消 \")])],1)]}}])})],1),t(\"div\",{staticClass:\"pagination-wrapper\"},[t(\"el-pagination\",{attrs:{\"page-sizes\":[20,50,100,200],\"page-size\":e.size,layout:\"total, sizes, prev, pager, next, jumper\",total:e.total,background:\"\"},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handleCurrentChange}})],1)],1)],1),t(\"el-dialog\",{staticClass:\"process-dialog\",attrs:{title:\"律师函制作处理\",visible:e.dialogFormVisible,\"close-on-click-modal\":!1,width:\"70%\"},on:{\"update:visible\":function(t){e.dialogFormVisible=t},close:e.handleDialogClose}},[t(\"div\",{staticClass:\"dialog-content\"},[t(\"el-form\",{ref:\"ruleForm\",attrs:{model:e.ruleForm,rules:e.rules,\"label-position\":\"top\"}},[t(\"div\",{staticClass:\"form-section\"},[t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-info\"}),e._v(\" 工单基本信息 \")]),t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"工单类型\"}},[t(\"el-input\",{staticClass:\"readonly-input\",attrs:{readonly:\"\"},model:{value:e.ruleForm.type_title,callback:function(t){e.$set(e.ruleForm,\"type_title\",t)},expression:\"ruleForm.type_title\"}},[t(\"i\",{staticClass:\"el-icon-folder\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1)],1),t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"工单标题\"}},[t(\"el-input\",{staticClass:\"readonly-input\",attrs:{readonly:\"\"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,\"title\",t)},expression:\"ruleForm.title\"}},[t(\"i\",{staticClass:\"el-icon-document\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1)],1)],1),t(\"el-form-item\",{attrs:{label:\"工单描述\"}},[t(\"el-input\",{staticClass:\"readonly-textarea\",attrs:{readonly:\"\",type:\"textarea\",rows:3},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,\"desc\",t)},expression:\"ruleForm.desc\"}})],1)],1),t(\"div\",{staticClass:\"form-section\"},[t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-setting\"}),e._v(\" 处理状态设置 \")]),t(\"el-form-item\",{attrs:{label:\"制作状态\"}},[t(\"el-radio-group\",{staticClass:\"status-radio-group\",model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,\"is_deal\",t)},expression:\"ruleForm.is_deal\"}},[t(\"el-radio\",{staticClass:\"status-radio\",attrs:{label:1}},[t(\"i\",{staticClass:\"el-icon-loading\"}),e._v(\" 处理中 \")]),t(\"el-radio\",{staticClass:\"status-radio\",attrs:{label:2}},[t(\"i\",{staticClass:\"el-icon-check\"}),e._v(\" 已完成 \")])],1)],1)],1),t(\"div\",{staticClass:\"form-section ai-section\"},[t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-cpu\"}),e._v(\" AI智能生成 \")]),t(\"div\",{staticClass:\"ai-generation-content\"},[t(\"div\",{staticClass:\"ai-description\"},[t(\"p\",[t(\"i\",{staticClass:\"el-icon-info\"}),e._v(\" AI将根据工单信息、债务人详情和合同模板自动生成律师函内容 \")])]),t(\"div\",{staticClass:\"ai-actions\"},[t(\"el-button\",{staticClass:\"ai-generate-btn\",attrs:{type:\"primary\",icon:\"el-icon-cpu\",loading:e.aiGenerating},on:{click:e.generateLawyerLetter}},[e._v(\" \"+e._s(e.aiGenerating?\"AI生成中...\":\"AI生成律师函\")+\" \")])],1),e.aiGeneratedContent?t(\"div\",{staticClass:\"ai-result\"},[t(\"h5\",{staticClass:\"result-title\"},[t(\"i\",{staticClass:\"el-icon-check\"}),e._v(\" AI生成结果 \")]),t(\"div\",{staticClass:\"generated-content\"},[t(\"el-input\",{staticClass:\"ai-content-textarea\",attrs:{type:\"textarea\",rows:8,placeholder:\"AI生成的律师函内容将显示在这里...\"},model:{value:e.aiGeneratedContent,callback:function(t){e.aiGeneratedContent=t},expression:\"aiGeneratedContent\"}})],1),t(\"div\",{staticClass:\"ai-result-actions\"},[t(\"el-button\",{attrs:{type:\"success\",icon:\"el-icon-check\",size:\"small\"},on:{click:e.useAiContent}},[e._v(\" 使用此内容 \")]),t(\"el-button\",{attrs:{type:\"warning\",icon:\"el-icon-refresh\",size:\"small\"},on:{click:e.regenerateContent}},[e._v(\" 重新生成 \")])],1)]):e._e()])]),2==e.ruleForm.is_deal?t(\"div\",{staticClass:\"form-section completion-section\"},[t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-upload\"}),e._v(\" 完成处理 \")]),t(\"el-form-item\",{staticClass:\"process-method-item\",attrs:{label:\"处理方式\"}},[t(\"el-radio-group\",{staticClass:\"method-radio-group\",on:{change:e.onProcessMethodChange},model:{value:e.processMethod,callback:function(t){e.processMethod=t},expression:\"processMethod\"}},[t(\"el-radio\",{staticClass:\"method-radio\",attrs:{label:\"ai\"}},[t(\"i\",{staticClass:\"el-icon-cpu\"}),e._v(\" AI智能生成 \")]),t(\"el-radio\",{staticClass:\"method-radio\",attrs:{label:\"upload\"}},[t(\"i\",{staticClass:\"el-icon-upload\"}),e._v(\" 手动上传文件 \")])],1)],1),\"ai\"===e.processMethod?t(\"div\",{staticClass:\"ai-process-section\"},[t(\"div\",{staticClass:\"ai-process-info\"},[t(\"el-alert\",{staticClass:\"ai-mode-alert\",attrs:{title:\"AI生成模式\",description:\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\",type:\"info\",closable:!1,\"show-icon\":\"\"}})],1),e.aiGeneratedContent?t(\"div\",{staticClass:\"ai-content-preview\"},[t(\"el-form-item\",{attrs:{label:\"生成内容预览\"}},[t(\"el-input\",{staticClass:\"ai-preview-textarea\",attrs:{type:\"textarea\",rows:6,placeholder:\"AI生成的律师函内容...\",readonly:\"\"},model:{value:e.aiGeneratedContent,callback:function(t){e.aiGeneratedContent=t},expression:\"aiGeneratedContent\"}})],1)],1):e._e(),t(\"el-form-item\",{attrs:{label:\"文件生成\"}},[t(\"div\",{staticClass:\"ai-file-generation\"},[t(\"el-button\",{staticClass:\"generate-file-btn\",attrs:{type:\"success\",icon:\"el-icon-document\",loading:e.fileGenerating},on:{click:e.generateAiFile}},[e._v(\" \"+e._s(e.fileGenerating?\"生成文件中...\":\"生成律师函文件\")+\" \")]),e.aiGeneratedFile?t(\"div\",{staticClass:\"generated-file-info\"},[t(\"i\",{staticClass:\"el-icon-document\"}),t(\"span\",[e._v(e._s(e.aiGeneratedFile.name))]),t(\"el-tag\",{attrs:{type:\"success\",size:\"mini\"}},[e._v(\"已生成\")])],1):e._e()],1)])],1):e._e(),\"upload\"===e.processMethod?t(\"div\",{staticClass:\"upload-process-section\"},[t(\"el-form-item\",{staticClass:\"file-upload-item\",attrs:{label:\"律师函文件\",prop:\"file_path\"}},[t(\"div\",{staticClass:\"upload-area\"},[t(\"el-input\",{staticClass:\"file-input\",attrs:{placeholder:\"请上传律师函文件\",readonly:\"\"},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,\"file_path\",t)},expression:\"ruleForm.file_path\"}},[t(\"i\",{staticClass:\"el-icon-document\",attrs:{slot:\"prefix\"},slot:\"prefix\"})]),t(\"div\",{staticClass:\"upload-buttons\"},[t(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-upload\"},on:{click:function(t){return e.changeFile(\"file_path\")}}},[t(\"el-upload\",{staticStyle:{display:\"inline-block\"},attrs:{action:\"/admin/Upload/uploadFile\",\"show-file-list\":!1,\"on-success\":e.handleSuccess}},[e._v(\" 上传文件 \")])],1),e.ruleForm.file_path?t(\"el-button\",{attrs:{type:\"danger\",icon:\"el-icon-delete\"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,\"file_path\")}}},[e._v(\" 删除文件 \")]):e._e()],1)],1)])],1):e._e(),t(\"el-form-item\",{attrs:{label:\"处理说明\"}},[t(\"el-input\",{staticClass:\"content-textarea\",attrs:{type:\"textarea\",rows:4,placeholder:\"请输入处理说明或备注信息...\"},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,\"content\",t)},expression:\"ruleForm.content\"}})],1)],1):e._e()])],1),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{attrs:{icon:\"el-icon-close\"},on:{click:e.cancelDialog}},[e._v(\"取 消\")]),t(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-check\"},on:{click:function(t){return e.saveData()}}},[e._v(\"确 定\")])],1)]),t(\"el-dialog\",{attrs:{title:\"图片查看\",visible:e.dialogVisible,width:\"30%\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[t(\"el-image\",{attrs:{src:e.show_image}})],1),t(\"div\",{staticClass:\"debtor-detail-panel\",class:{\"panel-open\":e.showDebtorPanel}},[t(\"div\",{staticClass:\"panel-overlay\",on:{click:e.closeDebtorPanel}}),t(\"div\",{staticClass:\"panel-content\"},[t(\"div\",{staticClass:\"panel-header\"},[t(\"div\",{staticClass:\"header-info\"},[e._m(1),t(\"p\",{staticClass:\"panel-subtitle\"},[e._v(e._s(e.currentDebtor.dt_name))])]),t(\"el-button\",{staticClass:\"close-btn\",attrs:{type:\"text\",icon:\"el-icon-close\"},on:{click:e.closeDebtorPanel}})],1),t(\"div\",{staticClass:\"panel-body\"},[t(\"div\",{staticClass:\"sidebar-menu\"},[t(\"div\",{staticClass:\"menu-item\",class:{active:\"basic\"===e.activeTab},on:{click:function(t){e.activeTab=\"basic\"}}},[t(\"i\",{staticClass:\"el-icon-user-solid\"}),t(\"span\",[e._v(\"基本信息\")])]),t(\"div\",{staticClass:\"menu-item\",class:{active:\"user\"===e.activeTab},on:{click:function(t){e.activeTab=\"user\"}}},[t(\"i\",{staticClass:\"el-icon-phone\"}),t(\"span\",[e._v(\"关联用户\")])]),t(\"div\",{staticClass:\"menu-item\",class:{active:\"files\"===e.activeTab},on:{click:function(t){e.activeTab=\"files\"}}},[t(\"i\",{staticClass:\"el-icon-folder\"}),t(\"span\",[e._v(\"相关文件\")])]),t(\"div\",{staticClass:\"menu-item\",class:{active:\"history\"===e.activeTab},on:{click:function(t){e.activeTab=\"history\"}}},[t(\"i\",{staticClass:\"el-icon-time\"}),t(\"span\",[e._v(\"历史记录\")])])]),t(\"div\",{staticClass:\"content-area\"},[\"basic\"===e.activeTab?t(\"div\",{staticClass:\"tab-content\"},[t(\"div\",{staticClass:\"info-section\"},[e._m(2),t(\"div\",{staticClass:\"info-grid\"},[t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"姓名：\")]),t(\"span\",[e._v(e._s(e.currentDebtor.dt_name))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"身份证号：\")]),t(\"span\",[e._v(e._s(e.currentDebtor.id_card||\"未提供\"))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"联系电话：\")]),t(\"span\",[e._v(e._s(e.currentDebtor.phone||\"未提供\"))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"地址：\")]),t(\"span\",[e._v(e._s(e.currentDebtor.address||\"未提供\"))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"债务金额：\")]),t(\"span\",{staticClass:\"debt-amount\"},[e._v(\"¥\"+e._s(e.currentDebtor.debt_amount||\"0.00\"))])]),t(\"div\",{staticClass:\"info-item\"},[t(\"label\",[e._v(\"债务类型：\")]),t(\"span\",[e._v(e._s(e.currentDebtor.debt_type||\"未分类\"))])])])])]):e._e(),\"user\"===e.activeTab?t(\"div\",{staticClass:\"tab-content\"},[t(\"div\",{staticClass:\"info-section\"},[e._m(3),t(\"div\",{staticClass:\"user-card\"},[e._m(4),t(\"div\",{staticClass:\"user-info\"},[t(\"h5\",[e._v(e._s(e.currentDebtor.user_name))]),t(\"p\",[e._v(\"手机号：\"+e._s(e.currentDebtor.user_phone))]),t(\"p\",[e._v(\"注册时间：\"+e._s(e.currentDebtor.user_register_time))]),t(\"p\",[e._v(\"用户状态： \"),t(\"el-tag\",{attrs:{type:\"active\"===e.currentDebtor.user_status?\"success\":\"warning\",size:\"mini\"}},[e._v(\" \"+e._s(\"active\"===e.currentDebtor.user_status?\"正常\":\"异常\")+\" \")])],1)])])])]):e._e(),\"files\"===e.activeTab?t(\"div\",{staticClass:\"tab-content\"},[t(\"div\",{staticClass:\"info-section\"},[e._m(5),t(\"div\",{staticClass:\"file-list\"},[e._l(e.currentDebtor.files,(function(s){return t(\"div\",{key:s.id,staticClass:\"file-item\"},[t(\"div\",{staticClass:\"file-icon\"},[t(\"i\",{class:e.getFileIcon(s.type)})]),t(\"div\",{staticClass:\"file-info\"},[t(\"h6\",[e._v(e._s(s.name))]),t(\"p\",[e._v(e._s(s.upload_time))]),t(\"p\",{staticClass:\"file-size\"},[e._v(e._s(s.size))])]),t(\"div\",{staticClass:\"file-actions\"},[t(\"el-button\",{attrs:{type:\"text\",size:\"mini\"},on:{click:function(t){return e.previewFile(s)}}},[t(\"i\",{staticClass:\"el-icon-view\"}),e._v(\" 预览 \")]),t(\"el-button\",{attrs:{type:\"text\",size:\"mini\"},on:{click:function(t){return e.downloadFile(s)}}},[t(\"i\",{staticClass:\"el-icon-download\"}),e._v(\" 下载 \")])],1)])})),e.currentDebtor.files&&0!==e.currentDebtor.files.length?e._e():t(\"div\",{staticClass:\"empty-files\"},[t(\"i\",{staticClass:\"el-icon-folder-opened\"}),t(\"p\",[e._v(\"暂无相关文件\")])])],2)])]):e._e(),\"history\"===e.activeTab?t(\"div\",{staticClass:\"tab-content\"},[t(\"div\",{staticClass:\"info-section\"},[e._m(6),t(\"div\",{staticClass:\"history-timeline\"},[e._l(e.currentDebtor.history,(function(s){return t(\"div\",{key:s.id,staticClass:\"timeline-item\"},[t(\"div\",{staticClass:\"timeline-dot\"}),t(\"div\",{staticClass:\"timeline-content\"},[t(\"h6\",[e._v(e._s(s.action))]),t(\"p\",[e._v(e._s(s.description))]),t(\"span\",{staticClass:\"timeline-time\"},[e._v(e._s(s.time))])])])})),e.currentDebtor.history&&0!==e.currentDebtor.history.length?e._e():t(\"div\",{staticClass:\"empty-history\"},[t(\"i\",{staticClass:\"el-icon-time\"}),t(\"p\",[e._v(\"暂无历史记录\")])])],2)])]):e._e()])])])])],1)},i=[function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"title-section\"},[t(\"h2\",{staticClass:\"page-title\"},[t(\"i\",{staticClass:\"el-icon-document\"}),e._v(\" 发律师函管理 \")]),t(\"p\",{staticClass:\"page-subtitle\"},[e._v(\"管理和处理律师函制作工单\")])])},function(){var e=this,t=e._self._c;return t(\"h3\",{staticClass:\"panel-title\"},[t(\"i\",{staticClass:\"el-icon-user\"}),e._v(\" 债务人详情 \")])},function(){var e=this,t=e._self._c;return t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-info\"}),e._v(\" 债务人基本信息 \")])},function(){var e=this,t=e._self._c;return t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-user\"}),e._v(\" 关联用户信息 \")])},function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"user-avatar\"},[t(\"i\",{staticClass:\"el-icon-user-solid\"})])},function(){var e=this,t=e._self._c;return t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-folder\"}),e._v(\" 相关文件 \")])},function(){var e=this,t=e._self._c;return t(\"h4\",{staticClass:\"section-title\"},[t(\"i\",{staticClass:\"el-icon-time\"}),e._v(\" 历史记录 \")])}],l=s(\"0c98\"),n={name:\"list\",components:{EditorBar:l[\"a\"]},data(){return{allSize:\"mini\",list:[],total:1,page:1,size:20,search:{keyword:\"\",is_pay:-1,is_deal:-1},loading:!0,url:\"/lawyer/\",title:\"律师函\",info:{},dialogFormVisible:!1,show_image:\"\",dialogVisible:!1,showDebtorPanel:!1,activeTab:\"basic\",currentDebtor:{},aiGenerating:!1,aiGeneratedContent:\"\",contractTypes:[],processMethod:\"upload\",fileGenerating:!1,aiGeneratedFile:null,ruleForm:{title:\"\",is_num:0},rules:{title:[{required:!0,message:\"请填写标题\",trigger:\"blur\"}],file_path:[{required:!0,message:\"请上传文件\",trigger:\"blur\"}]},formLabelWidth:\"120px\",options:[{id:-1,title:\"请选择\"},{id:1,title:\"未支付\"},{id:2,title:\"已支付\"},{id:3,title:\"退款\"}],options1:[{id:-1,title:\"请选择\"},{id:0,title:\"待处理\"},{id:1,title:\"处理中\"},{id:2,title:\"已处理\"}]}},mounted(){this.getData(),this.getContractTypes(),document.addEventListener(\"keydown\",this.handleKeyDown)},beforeDestroy(){document.removeEventListener(\"keydown\",this.handleKeyDown)},methods:{getStatusType(e){const t={0:\"warning\",1:\"primary\",2:\"success\"};return t[e]||\"info\"},getStatusText(e){const t={0:\"待处理\",1:\"处理中\",2:\"已处理\"};return t[e]||\"未知\"},searchData(){this.page=1,this.size=20,this.getData()},clearSearch(){this.search={keyword:\"\",is_deal:-1},this.searchData()},handleDialogClose(){this.$refs.ruleForm&&this.$refs.ruleForm.resetFields(),this.ruleForm={title:\"\",is_num:0,is_deal:1,type_title:\"\",desc:\"\",file_path:\"\",content:\"\"}},cancelDialog(){this.handleDialogClose(),this.dialogFormVisible=!1,this.aiGeneratedContent=\"\"},getContractTypes(){setTimeout(()=>{this.contractTypes=[{id:1,title:\"债务催收\",template_file:\"/uploads/templates/debt_collection_template.docx\",template_name:\"债务催收律师函模板.docx\",template_size:245760,template_content:\"\\n尊敬的{{debtor_name}}先生/女士：\\n\\n我们是{{user_name}}的法律代理人。现就您欠付我方当事人的债务事宜，特致函如下：\\n\\n一、债务事实\\n根据相关证据材料显示，您于{{debt_date}}向我方当事人借款人民币{{debt_amount}}元，约定还款期限为{{repay_date}}。但截至目前，您仍未履行还款义务，已构成违约。\\n\\n二、法律后果\\n您的上述行为已构成违约，根据《中华人民共和国民法典》相关规定，您应当承担相应的法律责任。\\n\\n三、催告要求\\n现特函告知，请您在收到本函后7日内，将所欠款项{{debt_amount}}元及相应利息一次性支付给我方当事人。\\n\\n四、法律警告\\n如您在上述期限内仍不履行还款义务，我方将依法采取包括但不限于向人民法院提起诉讼等法律手段维护我方当事人的合法权益，由此产生的一切法律后果由您承担。\\n\\n特此函告！\\n\\n{{law_firm_name}}\\n{{current_date}}\\n联系电话：{{contact_phone}}\\n地址：{{law_firm_address}}\\n            \"},{id:2,title:\"合同违约\",template_file:\"/uploads/templates/contract_breach_template.pdf\",template_name:\"合同违约律师函模板.pdf\",template_size:512e3,template_content:\"\\n尊敬的{{debtor_name}}先生/女士：\\n\\n我们是{{user_name}}的法律代理人。现就您违反合同约定的事宜，特致函如下：\\n\\n一、合同事实\\n您与我方当事人于{{contract_date}}签订了《{{contract_title}}》，约定了双方的权利义务。\\n\\n二、违约事实\\n根据合同约定及相关证据，您存在以下违约行为：\\n{{breach_details}}\\n\\n三、法律后果\\n您的违约行为已给我方当事人造成了经济损失，根据合同约定及法律规定，您应当承担违约责任。\\n\\n四、要求\\n请您在收到本函后7日内：\\n1. 立即停止违约行为\\n2. 履行合同义务\\n3. 赔偿相应损失\\n\\n如您拒不履行，我方将依法追究您的法律责任。\\n\\n{{law_firm_name}}\\n{{current_date}}\\n            \"},{id:3,title:\"知识产权侵权\",template_file:\"/uploads/templates/ip_infringement_template.doc\",template_name:\"知识产权侵权律师函模板.doc\",template_size:327680,template_content:\"\\n尊敬的{{debtor_name}}先生/女士：\\n\\n我们是{{user_name}}的法律代理人。现就您侵犯我方当事人知识产权的事宜，特致函如下：\\n\\n一、权利基础\\n我方当事人依法享有{{ip_type}}的专有权利，该权利受法律保护。\\n\\n二、侵权事实\\n经调查发现，您未经我方当事人许可，擅自{{infringement_details}}，侵犯了我方当事人的合法权益。\\n\\n三、法律后果\\n您的行为构成侵权，应当承担停止侵害、赔偿损失等法律责任。\\n\\n四、要求\\n请您在收到本函后立即：\\n1. 停止一切侵权行为\\n2. 销毁侵权产品\\n3. 赔偿经济损失\\n\\n否则我方将依法追究您的法律责任。\\n\\n{{law_firm_name}}\\n{{current_date}}\\n            \"},{id:4,title:\"劳动争议\",template_file:\"/uploads/templates/labor_dispute_template.docx\",template_name:\"劳动争议律师函模板.docx\",template_size:298760,template_content:\"\\n尊敬的{{debtor_name}}先生/女士：\\n\\n我们是{{user_name}}的法律代理人。现就劳动争议事宜，特致函如下：\\n\\n一、劳动关系\\n我方当事人与您存在劳动关系，期间为{{employment_period}}。\\n\\n二、争议事实\\n{{dispute_details}}\\n\\n三、法律依据\\n根据《劳动法》、《劳动合同法》等相关法律法规，您应当履行相应义务。\\n\\n四、要求\\n请您在收到本函后7日内妥善处理相关事宜，否则我方将通过法律途径解决。\\n\\n{{law_firm_name}}\\n{{current_date}}\\n            \"},{id:5,title:\"房屋租赁\",template_file:\"/uploads/templates/lease_dispute_template.pdf\",template_name:\"房屋租赁纠纷律师函模板.pdf\",template_size:445760,template_content:\"\\n尊敬的{{debtor_name}}先生/女士：\\n\\n我们是{{user_name}}的法律代理人。现就房屋租赁纠纷事宜，特致函如下：\\n\\n一、租赁关系\\n您与我方当事人签订了房屋租赁合同，租赁期限为{{lease_period}}。\\n\\n二、违约事实\\n{{lease_breach_details}}\\n\\n三、要求\\n请您在收到本函后立即：\\n1. {{specific_requirements}}\\n2. 支付相关费用\\n3. 配合解决纠纷\\n\\n如不配合，我方将依法维权。\\n\\n{{law_firm_name}}\\n{{current_date}}\\n            \"}]},100)},async generateLawyerLetter(){if(this.ruleForm.title&&this.ruleForm.type_title){this.aiGenerating=!0;try{const e=this.findMatchingTemplate();if(!e)return this.$message.warning(\"未找到匹配的律师函模板，请先在合同类型管理中上传相应模板\"),void(this.aiGenerating=!1);const t=await this.getDebtorInfo(),s=await this.getUserInfo();await this.simulateAiGeneration(e,t,s),this.$message.success(\"AI律师函生成完成！\"),\"ai\"===this.processMethod&&setTimeout(()=>{this.generateAiFile()},500)}catch(e){console.error(\"AI生成失败:\",e),this.$message.error(\"AI生成失败，请重试\")}finally{this.aiGenerating=!1}}else this.$message.warning(\"请先确保工单信息完整\")},findMatchingTemplate(){const e=this.ruleForm.title.toLowerCase(),t=this.ruleForm.type_title.toLowerCase(),s=[{keywords:[\"债务\",\"催收\",\"欠款\",\"借款\"],templateId:1},{keywords:[\"合同\",\"违约\",\"违反\"],templateId:2},{keywords:[\"知识产权\",\"侵权\",\"商标\",\"专利\"],templateId:3},{keywords:[\"劳动\",\"工资\",\"员工\"],templateId:4},{keywords:[\"租赁\",\"房屋\",\"租金\"],templateId:5}];for(const a of s)if(a.keywords.some(s=>e.includes(s)||t.includes(s)))return this.contractTypes.find(e=>e.id===a.templateId);return this.contractTypes.find(e=>1===e.id)},async getDebtorInfo(){return new Promise(e=>{setTimeout(()=>{e({name:this.ruleForm.dt_name||\"张三\",id_card:\"110101199001011234\",phone:\"13800138001\",address:\"北京市朝阳区建国门外大街1号\",debt_amount:\"100000.00\",debt_type:\"借款纠纷\",debt_date:\"2023-06-15\",repay_date:\"2023-12-15\"})},500)})},async getUserInfo(){return new Promise(e=>{setTimeout(()=>{e({name:\"李明\",phone:this.ruleForm.uid||\"13900139001\",register_time:\"2023-01-15\",status:\"active\"})},300)})},async simulateAiGeneration(e,t,s){return new Promise(a=>{setTimeout(()=>{let i=e.template_content;i=i.replace(/\\{\\{debtor_name\\}\\}/g,t.name),i=i.replace(/\\{\\{debt_amount\\}\\}/g,t.debt_amount),i=i.replace(/\\{\\{debt_date\\}\\}/g,t.debt_date),i=i.replace(/\\{\\{repay_date\\}\\}/g,t.repay_date),i=i.replace(/\\{\\{user_name\\}\\}/g,s.name),i=i.replace(/\\{\\{current_date\\}\\}/g,(new Date).toLocaleDateString(\"zh-CN\")),i=i.replace(/\\{\\{law_firm_name\\}\\}/g,\"北京市XX律师事务所\"),i=i.replace(/\\{\\{contact_phone\\}\\}/g,\"010-12345678\"),i=i.replace(/\\{\\{law_firm_address\\}\\}/g,\"北京市朝阳区XX大厦XX层\"),this.ruleForm.desc&&(i=i.replace(/\\{\\{breach_details\\}\\}/g,this.ruleForm.desc),i=i.replace(/\\{\\{dispute_details\\}\\}/g,this.ruleForm.desc),i=i.replace(/\\{\\{infringement_details\\}\\}/g,this.ruleForm.desc),i=i.replace(/\\{\\{lease_breach_details\\}\\}/g,this.ruleForm.desc)),i=i.replace(/\\{\\{[^}]+\\}\\}/g,\"[待填写]\"),this.aiGeneratedContent=i.trim(),a()},2e3)})},useAiContent(){this.aiGeneratedContent?(this.ruleForm.content=this.aiGeneratedContent,this.ruleForm.is_deal=2,this.$message.success(\"已应用AI生成的律师函内容\")):this.$message.warning(\"没有可用的AI生成内容\")},regenerateContent(){this.aiGeneratedContent=\"\",this.generateLawyerLetter()},onProcessMethodChange(e){\"ai\"===e?(this.ruleForm.file_path=\"\",this.aiGeneratedFile=null):(this.aiGeneratedContent=\"\",this.aiGeneratedFile=null)},async generateAiFile(){if(this.aiGeneratedContent){this.fileGenerating=!0;try{await new Promise(e=>setTimeout(e,1500));const e=`律师函_${this.ruleForm.dt_name||\"债务人\"}_${(new Date).getTime()}.docx`;this.aiGeneratedFile={name:e,path:\"/uploads/lawyer_letters/\"+e,size:\"25KB\"},this.ruleForm.file_path=this.aiGeneratedFile.path,this.$message.success(\"文件生成成功\")}catch(e){console.error(\"文件生成失败:\",e),this.$message.error(\"文件生成失败，请重试\")}finally{this.fileGenerating=!1}}else this.$message.warning(\"请先生成律师函内容\")},changeFile(e){this.filed=e,console.log(this.filed)},clearData(){this.search={keyword:\"\",is_pay:\"\"},this.getData()},editData(e){0!=e?this.getInfo(e):this.ruleForm={title:\"\",desc:\"\"}},getInfo(e){let t=this;const s=[{id:1,order_sn:\"LF202401001\",type:\"律师函\",title:\"债务催收律师函\",desc:\"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",is_deal:0,uid:\"13800138001\",dt_name:\"张三\",create_time:\"2024-01-15 09:30:00\",type_title:\"债务催收律师函\",file_path:\"\",content:\"\"},{id:2,order_sn:\"LF202401002\",type:\"律师函\",title:\"合同违约律师函\",desc:\"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",is_deal:1,uid:\"13900139002\",dt_name:\"李四\",create_time:\"2024-01-16 14:20:00\",type_title:\"合同违约律师函\",file_path:\"\",content:\"\"},{id:3,order_sn:\"LF202401003\",type:\"律师函\",title:\"知识产权侵权律师函\",desc:\"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",is_deal:2,uid:\"13700137003\",dt_name:\"王五\",create_time:\"2024-01-17 11:45:00\",type_title:\"知识产权侵权律师函\",file_path:\"/uploads/lawyer_letters/LF202401003.pdf\",content:\"已完成律师函制作，已发送给当事人。\"},{id:4,order_sn:\"LF202401004\",type:\"律师函\",title:\"劳动争议律师函\",desc:\"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",is_deal:0,uid:\"13600136004\",dt_name:\"赵六\",create_time:\"2024-01-18 16:10:00\",type_title:\"劳动争议律师函\",file_path:\"\",content:\"\"},{id:5,order_sn:\"LF202401005\",type:\"律师函\",title:\"房屋租赁纠纷律师函\",desc:\"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",is_deal:1,uid:\"13500135005\",dt_name:\"田七\",create_time:\"2024-01-19 10:25:00\",type_title:\"房屋租赁纠纷律师函\",file_path:\"\",content:\"\"}],a=s.find(t=>t.id===e);a?(t.ruleForm={...a},t.dialogFormVisible=!0,console.log(\"加载律师函详情数据:\",t.ruleForm)):t.$message({type:\"error\",message:\"未找到对应的律师函数据\"})},tuikuan(e){this.$confirm(\"是否申请退款?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"tuikuan?id=\"+e).then(e=>{200==e.code?this.$message({type:\"success\",message:e.msg}):this.$message({type:\"error\",message:e.msg})})}).catch(()=>{this.$message({type:\"error\",message:\"取消退款!\"})})},delData(e,t){this.$confirm(\"是否删除该信息?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"delete?id=\"+t).then(t=>{200==t.code&&(this.$message({type:\"success\",message:\"删除成功!\"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:\"error\",message:\"取消删除!\"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},handleKeyDown(e){27===e.keyCode&&(this.dialogFormVisible&&this.cancelDialog(),this.showDebtorPanel&&this.closeDebtorPanel())},showDebtorDetail(e){console.log(\"显示债务人详情:\",e),this.currentDebtor={...e,id_card:this.generateIdCard(),phone:this.generatePhone(),address:this.generateAddress(),debt_amount:this.generateDebtAmount(),debt_type:this.generateDebtType(),user_name:this.generateUserName(e.uid),user_phone:e.uid,user_register_time:this.generateRegisterTime(),user_status:\"active\",files:this.generateFiles(e.dt_name),history:this.generateHistory(e.dt_name)},this.activeTab=\"basic\",this.showDebtorPanel=!0},closeDebtorPanel(){this.showDebtorPanel=!1,this.currentDebtor={}},getFileIcon(e){const t={pdf:\"el-icon-document\",doc:\"el-icon-document\",docx:\"el-icon-document\",jpg:\"el-icon-picture\",jpeg:\"el-icon-picture\",png:\"el-icon-picture\",zip:\"el-icon-folder-opened\",rar:\"el-icon-folder-opened\"};return t[e]||\"el-icon-document\"},previewFile(e){console.log(\"预览文件:\",e),this.$message.info(\"文件预览功能开发中...\")},downloadFile(e){console.log(\"下载文件:\",e),this.$message.success(\"开始下载文件: \"+e.name)},generateIdCard(){const e=[\"110101\",\"310101\",\"440101\",\"500101\"],t=e[Math.floor(Math.random()*e.length)],s=1980+Math.floor(30*Math.random()),a=String(Math.floor(12*Math.random())+1).padStart(2,\"0\"),i=String(Math.floor(28*Math.random())+1).padStart(2,\"0\"),l=String(Math.floor(9999*Math.random())).padStart(4,\"0\");return`${t}${s}${a}${i}${l}`},generatePhone(){const e=[\"138\",\"139\",\"150\",\"151\",\"188\",\"189\"],t=e[Math.floor(Math.random()*e.length)],s=String(Math.floor(1e8*Math.random())).padStart(8,\"0\");return`${t}${s}`},generateAddress(){const e=[\"北京市朝阳区建国门外大街1号\",\"上海市浦东新区陆家嘴环路1000号\",\"广州市天河区珠江新城花城大道85号\",\"深圳市南山区深南大道10000号\",\"杭州市西湖区文三路90号\"];return e[Math.floor(Math.random()*e.length)]},generateDebtAmount(){const e=[\"50000.00\",\"100000.00\",\"200000.00\",\"500000.00\",\"1000000.00\"];return e[Math.floor(Math.random()*e.length)]},generateDebtType(){const e=[\"借款纠纷\",\"合同违约\",\"房屋租赁\",\"劳动争议\",\"知识产权\"];return e[Math.floor(Math.random()*e.length)]},generateUserName(e){const t=[\"张\",\"李\",\"王\",\"刘\",\"陈\",\"杨\",\"赵\",\"黄\",\"周\",\"吴\"],s=[\"伟\",\"芳\",\"娜\",\"敏\",\"静\",\"丽\",\"强\",\"磊\",\"军\",\"洋\"],a=t[Math.floor(Math.random()*t.length)],i=s[Math.floor(Math.random()*s.length)];return`${a}${i}`},generateRegisterTime(){const e=2020+Math.floor(4*Math.random()),t=String(Math.floor(12*Math.random())+1).padStart(2,\"0\"),s=String(Math.floor(28*Math.random())+1).padStart(2,\"0\");return`${e}-${t}-${s} 10:30:00`},generateFiles(e){return[{id:1,name:e+\"_身份证.jpg\",type:\"jpg\",size:\"2.5MB\",upload_time:\"2024-01-15 14:30:00\"},{id:2,name:e+\"_借款合同.pdf\",type:\"pdf\",size:\"1.2MB\",upload_time:\"2024-01-16 09:15:00\"},{id:3,name:e+\"_银行流水.pdf\",type:\"pdf\",size:\"3.8MB\",upload_time:\"2024-01-17 16:45:00\"}]},generateHistory(e){return[{id:1,action:\"创建债务人档案\",description:`创建了${e}的债务人档案`,time:\"2024-01-15 09:30:00\"},{id:2,action:\"上传相关文件\",description:\"上传了身份证、合同等相关文件\",time:\"2024-01-15 14:30:00\"},{id:3,action:\"发起律师函\",description:\"针对债务纠纷发起律师函制作申请\",time:\"2024-01-16 10:20:00\"},{id:4,action:\"更新债务信息\",description:\"更新了债务金额和联系方式\",time:\"2024-01-17 15:10:00\"}]},getData(){let e=this;e.loading=!0;const t=[{id:1,order_sn:\"LF202401001\",type:\"律师函\",title:\"债务催收律师函\",desc:\"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",is_deal:0,uid:\"13800138001\",dt_name:\"张三\",create_time:\"2024-01-15 09:30:00\",type_title:\"债务催收律师函\"},{id:2,order_sn:\"LF202401002\",type:\"律师函\",title:\"合同违约律师函\",desc:\"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",is_deal:1,uid:\"13900139002\",dt_name:\"李四\",create_time:\"2024-01-16 14:20:00\",type_title:\"合同违约律师函\"},{id:3,order_sn:\"LF202401003\",type:\"律师函\",title:\"知识产权侵权律师函\",desc:\"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",is_deal:2,uid:\"13700137003\",dt_name:\"王五\",create_time:\"2024-01-17 11:45:00\",type_title:\"知识产权侵权律师函\",file_path:\"/uploads/lawyer_letters/LF202401003.pdf\",content:\"已完成律师函制作，已发送给当事人。\"},{id:4,order_sn:\"LF202401004\",type:\"律师函\",title:\"劳动争议律师函\",desc:\"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",is_deal:0,uid:\"13600136004\",dt_name:\"赵六\",create_time:\"2024-01-18 16:10:00\",type_title:\"劳动争议律师函\"},{id:5,order_sn:\"LF202401005\",type:\"律师函\",title:\"房屋租赁纠纷律师函\",desc:\"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",is_deal:1,uid:\"13500135005\",dt_name:\"田七\",create_time:\"2024-01-19 10:25:00\",type_title:\"房屋租赁纠纷律师函\"}];setTimeout(()=>{try{console.log(\"开始加载律师函测试数据...\");let s=t;if(e.search.keyword&&e.search.keyword.trim()){const a=e.search.keyword.trim().toLowerCase();s=t.filter(e=>e.order_sn.toLowerCase().includes(a)||e.title.toLowerCase().includes(a)||e.uid.includes(a)||e.dt_name.includes(a))}-1!==e.search.is_deal&&\"\"!==e.search.is_deal&&(s=s.filter(t=>t.is_deal===e.search.is_deal));const a=(e.page-1)*e.size,i=a+e.size,l=s.slice(a,i);e.list=l,e.total=s.length,e.loading=!1,console.log(\"律师函数据加载完成:\",e.list),console.log(\"总数:\",e.total)}catch(s){console.error(\"加载律师函测试数据出错:\",s),e.list=[],e.total=0,e.loading=!1}},300)},saveData(){let e=this;this.$refs[\"ruleForm\"].validate(t=>{if(!t)return!1;console.log(\"保存律师函数据:\",this.ruleForm),setTimeout(()=>{e.$message({type:\"success\",message:\"律师函处理状态更新成功！\"}),this.getData(),e.dialogFormVisible=!1},500)})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success(\"上传成功\"),this.ruleForm[this.filed]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error(\"上传图片格式不对!\")},delImage(e,t){let s=this;s.getRequest(\"/Upload/delImage?fileName=\"+e).then(e=>{200==e.code?(s.ruleForm[t]=\"\",s.$message.success(\"删除成功!\")):s.$message.error(e.msg)})}}},r=n,o=(s(\"0798\"),s(\"2877\")),c=Object(o[\"a\"])(r,a,i,!1,null,\"4a81e5cf\",null);t[\"default\"]=c.exports}}]);", "extractedComments": []}