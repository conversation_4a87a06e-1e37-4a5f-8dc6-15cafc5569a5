<?php
namespace app\index\controller;
use think\Request;
use untils\{JsonService,LiveA};
use models\{Banners,Lines,Configs,<PERSON>anyes,Lvshicates,Lvshis,District,Tiaojies,Tiaojiegengjings,Vips,Kechengs,Orders,Huifangs,Zhubos,Wenshucates,Wenshus,Companys,Gonggaos,Anlis,Hetongcates,Hetongs,Servers,Users,Chats,Gongdans,Navs,Quns,Qunliaos,Yuangongs,Zhiweis,Notes,Debts,Debttrans
};
use think\Controller;
use think\facade\View;

class Index extends Controller{

	private $configs = [];
	//private $url  = 'https://fdb.zhongfabang.cn';
	private $url  = 'https://web.faduobang.com';
	public function __construct(Configs $model){
		$configs = $model->getAllData();
        $this->configs =$configs;
	}

	public function index(){
		//跳转至后台
		header("location:https://web.faduobang.com/admin_index/#/login");
	}

	//创建直播
	public function zhiboCreate(LiveA $model){
	/**
     * 创建是直播地址
     * @param $appName 应用名称 ，自定义
     * @param $streamName 房间名称，自定义，该应用下唯一
     * @param $endTime 结束时间
     * @return array|bool  播放流（观看者）：alive_url，推流（直播者）：tlive_url
     */
		$res = $model->createdLive('lvdian1','1111',time()+3600);
		dump($res);

	}
	public function getYinsi(Configs $model){
	    $yinsi =  $model
	    ->where(['name'=>'yinsi'])
	    ->value('value');
	    if(!empty($yinsi)) return JsonService::successful("1",$yinsi);
		else return JsonService::fail("2");
	}

	public function indexList(
	    Gonggaos $Gonggaos,
		Banners $Banners,
		Lvshis $Lvshis,
        Yuangongs $Fawus,
		Servers $Servers,
		Kechengs $Kechengs,
		Navs $Navs,
		Anlis $Anlis){

		$banner = $Banners::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->select();
		$navs = $Navs::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->order("sort", "desc")->select();
        $gonggao = $Gonggaos::withAttr('time',function($v,$d){
        	return date('Y-m-d',$d['create_time']);
        })->withAttr('pages',function($value,$data){
			return "/pages/index/goodsDetails?id=".$data['id'];
		})->append(['time'])
        ->select();
        $shipin = $Kechengs::withAttr('pic_path',function($value,$data){
		    if(!empty($value)) 	return $this->url.$value;
		})->limit(2)->select();
        $anlis = $Anlis::withAttr('create_time',function($v,$d){
        	return date('Y-m-d',$d['create_time']);
        })->withAttr('pic_path',function($value,$data){
		    if(!empty($value)) 	return $this->url.$value;
		})->withAttr('pages',function($value,$data){
			return "/pages/index/newinfo?id=".$data['id'];
		})
        ->limit(6)->append(['pages'])->select();

        $servers = $Servers::withAttr('create_time',function($v,$d){
        	return date('Y-m-d',$d['create_time']);
        })->withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('pages',function($value,$data){
			return "/pages/index/goodsDetails?id=".$data['id'];
		})
        ->limit(6)->append(['pages'])->select();
        $lvshi  = $Lvshis::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('pages',function($v,$d){
			return "/pages/message/chat?id=".$d['id'];
		})->where(['is_delete'=>0])->append(['pages'])
		->limit(8)->select();

		if(count($lvshi)){
            $t_yu = count($lvshi)/2;

            for($i=0;$i<$t_yu;$i++){
                $newteams[]=array_slice($lvshi->toArray(),$i*2,2);
            }
            $lvshi=$newteams;
        }

        $fawu  = $Fawus::withAttr('pic_path',function($value,$data){
            return $this->url.$value;
        })->withAttr('pages',function($v,$d){
            return "/pages/message/chat?id=".$d['id'];
        })->where(['is_delete'=>0,'zhiwei_id'=>3])->append(['pages'])
            ->limit(8)->select();

        if(count($fawu)){
            $t_yu = count($fawu)/2;

            for($i=0;$i<$t_yu;$i++){
                $newteams2[]=array_slice($fawu->toArray(),$i*2,2);
            }
            $fawu=$newteams2;
        }
		return JsonService::successful('1',compact('banner','gonggao','lvshi','fawu','anlis','servers','navs','shipin'));
	}

	public function getMyList(){
		$configs = $this->configs;

		$configs['about_path'] = $this->url.$configs['about_path'];
		$configs['lvshi'] = Lvshis::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->find($configs['lvshi']);
		return JsonService::successful('',$configs);
	}
	public function anliList(Anlis $model){
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('url',function($v,$d){
			return "/pages/index/newinfo?id=".$d['id'];
		})
		->append(['url'])->select();

		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
	public function anliInfo(Request $request,Anlis $model){
		$id=$request->post('id');
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->find($id);
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res->toArray());
	}
	public function gonggaoInfo(Request $request,Gonggaos $model){
		$id=$request->post('id');
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->find($id);
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res->toArray());
	}
	public function hetongList(Hetongcates $model){
		$res = $model::withAttr('url',function($v,$d){
			return "/pages/index/hetongInfo?id=".$d['id'];
		})->withAttr('children',function($v,$d){
			return Hetongs::withAttr('file_path',function($value,$data){
        			return $this->url.$value;
        		    })->where('cate_id',$d['id'])->select();
		})
		->append(['url','children'])->select();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}

	public function serverList(Servers $model){
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('content',function($value,$data){
	         return replace_pic_url($value,$this->url);
	    })
		->select();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}

	public function makeOrder(Request $request,Orders $model){
		$form = $request->post();
		$form['order_sn']="FW".get_order_num();
		$res = $model->save($form);
		if(!empty($res)) return JsonService::successful("下单成功",$form['order_sn']);
		else return JsonService::fail("下单失败");
	}
	public function makeHetongOrder(Request $request,Orders $model){
		$form = $request->post();
		$form['order_sn']="FW".get_order_num();
		$res = $model->save($form);
		if(!empty($res)) return JsonService::successful("下单成功",$form['order_sn']);
		else return JsonService::fail("下单失败");
	}

	public function checkHetong(Request $request,Orders $model){
	    	$form = $request->post();
	    	$count = $model->where($form)->count();
	    	if(!empty($count))  return JsonService::successful("已购买");
	    	else return JsonService::fail("未购买");
	}

	//工单
	public function gongdanList(Gongdans $model,Request $request){
	    $post = $request->post();
	    if(empty($post['uid'])) return JsonService::fail('无数据');
		$res = $model::withAttr('file_path',function($value,$data){
			if(!empty($value)) return $value;
			else return "";
		})->withAttr('type_title',function($v,$d){
			switch ($d['type']) {
				case '1':
					return '合同审核';
					break;

				case '2':
					return '合同定制';
					break;
				case '3':
					return '发律师函';
					break;
			}
		})->withAttr('is_deal_title',function($v,$d){
			if($d['is_deal']==1){
				return '处理中';
			}else{
				return '已处理';
			}
		})->append(['is_deal_title','type_title'])
		->where(['is_delete'=>0])
		->where($post)
        ->order(['create_time'=>'desc'])
		->select();
        if(!empty($res)){
            foreach ($res as &$val){
				$file_types = array();
				$val["attach_path_type"] = '';
                $val["attach_path"] = explode(",",$val["attach_path"]) ;
				if($val["attach_path"]){
					foreach($val["attach_path"] as $item){
						$file_types[] =  return_file_type($item);
					}

					$val["attach_path_type"] = $file_types;
				}
                $val["images"] = explode(",",$val["images"]) ;
            }
        }
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
    public function checkVip(Request $request){
        $form = $request->post();
        if(empty($form['uid'])) return JsonService::fail('请先登录');
        $info = Users::withAttr('end_time',function($v,$d){
            if(!empty($d['year'])) return $d['start_time']*1+$d['year']*1*365*60*60*24;
        })
        ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
        ->where(['id'=>$form['uid']])
        ->find();
        if(empty($info['end_time'])) return JsonService::fail('该功能需要会员才可以在线预览');
        if(time()>$info['end_time']*1) return JsonService::fail('会员时间已到期,请联系客服升级');
        return JsonService::successful("会员正常");
    }

	public function saveGongdan(Gongdans $model,Request $request){
		$form = $request->post();
		$form['order_sn']='GD'.get_order_num();
		if(empty($form['uid'])) return JsonService::fail('请先登录');
		$info = Users::withAttr('end_time',function($v,$d){
            if(!empty($d['year'])) return $d['start_time']*1+$d['year']*1*365*60*60*24;
        })
        ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
        ->where(['id'=>$form['uid']])
        ->find();
        if(empty($info['end_time'])) return JsonService::fail('该功能需要会员才可发布');
        if(time()>$info['end_time']*1) return JsonService::fail('会员时间已到期,请联系客服升级');
        if(!empty($form['images'])){
            $img_arr = array();
            foreach ($form['images'] as $img) {
                $tmp_arr = array();
                $tmp_arr = parse_url($img);
                $img_arr[] = $tmp_arr["path"];
            }
            $form['images'] = implode(",",$img_arr);
        }
        if(!empty($form['files'])){
            $file_arr = array();
            foreach ($form['files'] as $tmp_file) {
                $tmp_arr = array();
                $file_arr[] = $tmp_file;
            }
            $form['attach_path'] = implode(",",$file_arr);
        }
		$res = $model->save($form);
		if(!empty($res)) return JsonService::successful("下单成功");
		else return JsonService::fail("下单失败");
	}
	public function huifangList(Zhubos $Zhubos){
	   $huifang  = $Zhubos::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->whereNotNull('huifang')
		->withAttr('pages',function($value,$data){
		    return "/pages/live/huifang?id=".$data['id'];
		})
		->append(['pages'])
		->select();
		JsonService::successful("1",$huifang);
	}
	public function changeOrder(Orders $model, Debttrans $debttrans, Request $request){
	    $post =$request->post();
	    if(empty($post['id'])) return  JsonService::fail("未查询到订单");

        $new_sn = "FW".get_order_num();

	    $info = $model->where(['id'=>$post['id']])->find();
        $pre_order_sn = $info["order_sn"];
        $info->order_sn = $new_sn;
	    $res = $info->save();

        $info2 = $debttrans->where(['order_sn'=>$pre_order_sn])->find();
        if($info2){
            $info2->order_sn = $new_sn;
            $info2->save();
        }

	    if(!empty(($res))) return JsonService::successful("获取成功",$info->order_sn);
	    else JsonService::fail("获取失败");

	}

	public function orderList(Orders $model,Orders $Orders,Request $request){
		$post = $request->post();
		$res = $model::withAttr('content',function($v,$d){
			switch ($d['type']) {
				case 1:
					return Servers::where(['id'=>$d['type_id']])->find();
					break;
			}
		})->withAttr('is_pay_title',function($v,$d){
			if($d['is_pay']==1){
				return '未支付';
			}else{
				return '已支付';
			}
		})->withAttr('file_path',function($v,$d){
			if($v!=''){
				return $this->url.$v;
			}else{
				return $v;
			}

		})->withAttr('pic_path',function($v,$d){
			return $this->url.Servers::where('id',$d['type_id'])->value('pic_path');
		})
		->where($post)
		->append(['content','is_pay_title','pic_path'])
		->order(['create_time'=>'desc'])
		->select();
		if($res){
		    foreach ($res as &$v){
                $v["dt_name"] = Debts::where(['id'=>$v['dt_id']])->value("name");
            }
        }
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}

	public function chatList(Chats $Chats,Request $request){
	    $post = $request->post();
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
				return $this->url.Lvshis::where('id',$d['orther_id'])->value('pic_path');
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('flies',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time'])
		->where($post)
		->select();
		 $Chats::where($post)
	    ->update(['is_read'=>1]);
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}

	public function getMoreList(Chats $Chats,Request $request){
	    $post = $request->post();
	    $where[]=['orther_id','=',$post['orther_id']];
	    $where[]=['id','>',$post['id']];
	    $where[]=['uid','=',$post['uid']];
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return  $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
				return $this->url.Lvshis::where('id',$d['orther_id'])->value('pic_path');
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('flies',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time'])
		->where($where)
		->find();
		if(!empty($res)){

		        $res->is_read = 1;
		        $res->save();

		}

		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
	public function lvshiList(Lvshis $Lvshis){
		$res  = $Lvshis::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})
		->withAttr('pages',function($value,$data){
		    return "/pages/message/chat?id=".$data['id'];
		})->withAttr('zhuanyes',function($v,$d){
		    if(!empty($v)) return implode(',',Zhuanyes::where('id','in',unserialize($v))->column('title'));
		})
		->append(['pages'])
		->where(['is_delete'=>0])
		->select();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
    public function fawuList(Yuangongs $Fawus){
        $res  = $Fawus::withAttr('pic_path',function($value,$data){
            return $this->url.$value;
        })
            ->withAttr('pages',function($value,$data){
                return "/pages/message/chat?id=".$data['id'];
            })
            ->append(['pages'])
            ->where(['is_delete'=>0,'zhiwei_id'=>3])
            ->select();
        if(!empty($res)) return JsonService::successful("1",$res);
        else return JsonService::fail("没有数据");
    }
	public function getLvshiInfo(Lvshis $Lvshis,Request $request){
	    $id = $request->post('id');
	    $info = $Lvshis
	    ->where(['id'=>$id])
	    ->where(['is_delete'=>0])
	    ->find();
	    if(empty($info)) return JsonService::fail('律师不在');
	    return JsonService::successful('成功',$info);


	}
	public function sendMessage(Chats $Chats,Request $request){
		$post = $request->post();
	//	var_dump($post);
	//	$post['direction'] = 'right';
	//	$post['type'] = 'text';
	//	$post['content'] = '777777';
	//	$post['orther_id'] = '7';
	//	$post['uid'] = '71';

		if($post){
			if($post['type']=='text' || $post['type']=='voice'){
				$post['content']=emoji_to_html($post['content']);
			}

			if($post['type']=='file'){
				$post['flies']=serialize($post['flies']);
			}
			$res = $Chats->save($post);
		}

		if(!empty($res)) return JsonService::successful("1");
		else return JsonService::fail("没有数据");
	}

	public function sendQunMessage(Quns $quns,Qunliaos $Chats,Request $request,Notes $notes){
        $post = $request->post();
         if($post['type']=='text' || $post['type']=='voice'){
	        $post['content']=emoji_to_html($post['content']);
	    }

	   if($post['type']=='file'){
		    $post['files']=serialize($post['flies']);
		}

	      $qus =$quns
	    ->where(['id'=>$post['qun_id']])

	    ->find();
		$res = $Chats->save($post);
		  $my_id = $Chats->id;
        $user = $qus['uid'];
        $newList = [];
        foreach ($user as $k=>$vv){

        if($vv[0] != $post['uid']){
            $newList[$k]['uid']=$vv[0];
            $newList[$k]['mag_id']=$my_id;
            $newList[$k]['qun_id']=$post['qun_id'];
        }
        }
          $notes->saveAll($newList);

        $yuangong = $qus['yuangong_id'];
        $yongList = [];
        foreach ($yuangong as $k=>$vv){
            $yongList[$k]['yuangong_id']=$vv[1];
            $yongList[$k]['mag_id']=$my_id;
            $yongList[$k]['qun_id']=$post['qun_id'];
        }
        $notes->saveAll($yongList);
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
    }


    	public function getQun(Request $request,Qunliaos $Chats){
	    $post = $request->post();

	    $uid = $post['uid'];
	    $res = Quns::select();

	    $isres = false;
	    $newList = [];

	    foreach ($res as $kk=>$vv){
	        $vv['pic_path']=$this->url.$vv['pic_path'];
	        $vv['count'] = Notes::where(['uid'=>$uid,'qun_id'=>$vv['id']])->count();
	        $vv['content']=preg_emoji($Chats->where(['qun_id'=>$vv['id']])->order(['id'=>'desc'])->find());
	        $vv['time']=date('Y-m-d',$Chats->where(['qun_id'=>$vv['id']])->order(['id'=>'desc'])->value('create_time'));
	        $yuangong = $vv['uid'];
	        foreach ($yuangong as $k=>$v){

    	        if($v[0]==$post['uid']){
    	            $newList[]= $vv;
    	        }
	        }
	    }


	    if(!empty($res)) return JsonService::successful("1",$newList);
		else return JsonService::fail("没有数据");
	}



    public function getMoreQunList(Request $request,Qunliaos $Chats,Notes $notes){
        $post = $request->post();
     //   $where[]=['uid','=',$post['uid']];
        $where[]=['qun_id','=',$post['qun_id']];
        $where[]=['id','>',$post['id']];
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return  $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
			    if(!empty($d['lvshi_id'])) return $this->url.Lvshis::where('id',$d['lvshi_id'])->value('pic_path');
			    else return $this->url.Yuangongs::where('id',$d['yuangong_id'])->value('pic_path');

			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return  $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})->withAttr('title',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('nickname');
			}else{
			    if(!empty($d['lvshi_id'])) return Lvshis::where('id',$d['lvshi_id'])->value('title');
			    else return Yuangongs::where('id',$d['yuangong_id'])->value('title');

			}
		})
		->append(['time'])
		->where($where)
	    ->order(['id'=>'desc'])
		->find();

	       $eres[]=['qun_id','=',$post['qun_id']];
	        $eres[]=['uid','=',$post['uid']];
	     $note =  $notes->where($eres)->select();

	        foreach ($note as $k=>$v){
	            $notes->destroy($v['id']);
	        }
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
    }
    public function qunliaojiluList(Request $request,Qunliaos $Chats){
        $post = $request->post();
        $where[]=['uid','=',$post['uid']];
        $where[]=['qun_id','=',$post['qun_id']];
        if(!empty($post['content']))$where[]=['content','like','%'.$post['content'].'%'];
        $where[]=['type','=',$post['type']];
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('headimg');
			}else{
			    if(!empty($d['lvshi_id'])) return $this->url.Lvshis::where('id',$d['lvshi_id'])->value('pic_path');
			    else return $this->url.Yuangongs::where('id',$d['yuangong_id'])->value('pic_path');

			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return  $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})->withAttr('title',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('nickname');
			}else{
			    if(!empty($d['lvshi_id'])) return Lvshis::where('id',$d['lvshi_id'])->value('title');
			    else return Yuangongs::where('id',$d['yuangong_id'])->value('title');

			}
		})
		->append(['time','title'])
		->where($post)
		->select()->toArray();
	    $Chats::where($post)
	    ->where(['direction'=>'left'])
	    ->update(['is_read'=>1]);
		if(empty($res)) return JsonService::fail("没有数据");
		else return JsonService::successful("1",$res);
    }


    public function qunliaoList(Request $request,Qunliaos $Chats,Notes $notes){
        $post = $request->post();
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
			    if(!empty($d['lvshi_id'])) return $this->url.Lvshis::where('id',$d['lvshi_id'])->value('pic_path');
			    else return $this->url.Yuangongs::where('id',$d['yuangong_id'])->value('pic_path');

			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return  $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})->withAttr('title',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('nickname');
			}else{
			    if(!empty($d['lvshi_id'])) return Lvshis::where('id',$d['lvshi_id'])->value('title');
			    else return Yuangongs::where('id',$d['yuangong_id'])->value('title');

			}
		})
		->append(['time','title'])
		->where(['qun_id'=>$post['qun_id']])
		->select()->toArray();

	    $Chats::where($post);
	     $eres[]=['qun_id','=',$post['qun_id']];
	     $eres[]=['uid','=',$post['uid']];
	     $note =  $notes->where($eres)->select();

	        foreach ($note as $k=>$v){
	            $notes->destroy($v['id']);
	        }

		if(empty($res)) return JsonService::fail("没有数据");
		else return JsonService::successful("1",$res);
    }
	public function getQunInfo(Quns $Quns,Request $request){
	    $id = $request->post('id');
	    $info = $Quns
	    ->where(['id'=>$id])

	    ->find();
	    if(empty($info)) return JsonService::fail('工作群组不存在');
	    return JsonService::successful('成功',$info);


	}

	public function getQunMoreInfo(Request $request,Quns $Quns){
	    $id = $request->post('id');
	    $info = $Quns
	    ->where(['id'=>$id])

	    ->find();
	   // $users = Users::where(['id'=>$info['uid']])->select();
	    $lvshis = Lvshis::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->where('id','in',$info['lvshi_id'])->select();
		$needDeal = [];
		foreach ($info['yuangong_id'] as $k=>$v){
		   	$needDeal[$v[0]][]=$v[1];
		}
		$userDeal = [];
			foreach ($info['uid'] as $k=>$v){
		   	$userDeal[$v[0]][]=$v[0];
		}

        $users = [];
	    foreach ($userDeal as $k=>$v){
	     //   $zhiwei = Zhiweis::where(['id'=>$k])->value('title');
	        $users[]=[
	           'list'=>Users::withAttr('headimg',function($value,$data){
        			return $this->url.$value;
        		})->where('id','in',$v)->select()
	        ];
	    }
	    //先获职位
	    $yuangongs = [];
	    foreach ($needDeal as $k=>$v){
	        $zhiwei = Zhiweis::where(['id'=>$k])->value('title');
	        $yuangongs[]=[
	           'zhiwei'=>$zhiwei,
	           'list'=>Yuangongs::withAttr('pic_path',function($value,$data){
        			return $this->url.$value;
        		})->where('id','in',$v)->select()
	        ];
	    }
	    if(empty($info)) return JsonService::fail('工作群组不存在');
	    return JsonService::successful('成功',compact('users','info','lvshis','yuangongs'));
	}
	public function chatAllList(Chats $Chats,Request $request){
		$post = $request->post();
		$uid = $post['uid'];
		if(empty($post['uid'])) return JsonService::fail("没有数据");
		$res = $Chats
		->distinct(true)
		->field('orther_id')
		->where($post)
		->select();
		$newList  = [];
		foreach ($res as $key => $value) {

		    $info = Lvshis::where('id',$value['orther_id'])->where(['is_delete'=>0])->find();


		    if(!empty($info)){
		        $newList[]= [
			    'id'=>$info['id'],
				'pic_path'=>$this->url.$info['pic_path'],
				'count'=>$Chats->where(['uid'=>$post['uid'],'orther_id'=>$value['orther_id']])->where(['is_read'=>0])->count(),
				'title'=>$info['title'],
				'time'=>date('Y-m-d',$Chats->where(['uid'=>$uid,'orther_id'=>$value['orther_id']])->order(['id'=>'desc'])->value('create_time')),
				'content'=>	preg_emoji($Chats->where(['uid'=>$uid,'orther_id'=>$value['orther_id']])->order(['id'=>'desc'])->find()
		)
			];
		    }

		}
		if(!empty($res)) return JsonService::successful("1",$newList);
		else return JsonService::fail("没有数据");

	}
	public function kechengList(Kechengs $model){
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('url',function($value,$data){
			return "/pages/shipin/info?id=".$data['id'];
		})->append(['url'])
		->order(['id'=>'desc'])
		->select();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
	public function kechenginfo(Kechengs $model,Orders $Orders,Request $request){
		$id = $request->post('id');
		$uid = $request->post('uid');
		$isPay = false;
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('file_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('content',function($value,$data){
	         return replace_pic_url($value,$this->url);
	    })->find($id);
	    if($res['price']==0){
	    	$isPay = true;
	    }else{
	    	if(!empty($uid)){

				$count = $Orders
				->where(['type'=>3,'is_pay'=>2,'uid'=>$uid,'type_id'=>$id])
				->count();
				$vipcount = $Orders->where(['type'=>1,'is_pay'=>2,'uid'=>$uid])->count();
				if(!empty($count)||!empty($vipcount)) $isPay=true;
			}
	    }

		$res['isPay']=$isPay;
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}

	public function huifanginfo(Zhubos $model, Orders $Orders, Request $request){
	    $id = $request->post('id');
		$uid = $request->post('uid');
		$isPay = false;
		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('content',function($value,$data){
	         return replace_pic_url($value,$this->url);
	    })->withAttr('huifang',function($value,$data){
	         return unserialize($value)[0]['RecordUrl'];
	    })->withAttr('lvshi_title',function($value,$data){
	         return Lvshis::where(['id'=>$data['lvshi_id']])->value('title');
	    })->withAttr('lvshi_path',function($value,$data){
	         return $this->url.Lvshis::where(['id'=>$data['lvshi_id']])->value('pic_path');
	    })->append(['lvshi_title','lvshi_path'])->find($id);
	    if($res['price']==0){
	    	$isPay = true;
	    }else{
	    	if(!empty($uid)){

				$count = $Orders
				->where(['type'=>3,'is_pay'=>2,'uid'=>$uid,'type_id'=>$id])
				->count();
				$vipcount = $Orders->where(['type'=>1,'is_pay'=>2,'uid'=>$uid])->count();

				if(!empty($count)||!empty($vipcount)) $isPay=true;
			}
	    }

		$res['isPay']=$isPay;
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
	public function zhuboList(Zhubos $model,Request $request){

		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('file_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('content',function($value,$data){
	         return replace_pic_url($value,$this->url);
	    })->withAttr('lvshi_title',function($value,$data){
	         return Lvshis::where(['id'=>$data['lvshi_id']])->value('title');
	    })->withAttr('lvshi_path',function($value,$data){
	         return $this->url.Lvshis::where(['id'=>$data['lvshi_id']])->value('pic_path');
	    })->withAttr('is_zb',function($v,$d){
            if(time()*1>$d['start_time']*1) {


                if(time()*1>$d['end_time']*1){
                    return 3;
                }else{

                    return $this->hasLiveStatus($d['appname'],$d['streamname']);
                }
            }else{
                return 1;
            }

        })->withAttr('start_time',function($v,$d){
            return date('Y-m-d H:i:s',$v);
        })->withAttr('end_time',function($v,$d){
            return date('Y-m-d H:i:s',$v);
        })->withAttr('user_count',function($v,$d){
            if(time()*1>$d['end_time']*1){
                    return 0;
            }else{
                return Lines::where(['zhubo_id'=>$d['id'],'is_line'=>1])->count();
            }
        })->withAttr('pages',function($value,$data){
			return "/pages/live/zhubo?id=".$data['id'];
		})->append(['is_zb','lvshi_title','lvshi_path','pages','user_count'])->select();
        if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");

	}

	public function hasLiveStatus($appname,$streamname){
        $res = LiveA::getLiveStatus($appname,$streamname);
        if($res['StreamState']=='offline'){
           return 4;
        }else{
           return 2;
        }

    }
    public function getOnlineUserNum($appname,$streamname){
        $res = LiveA::getOnlineUserNum($appname,$streamname);
        return $res['UserCount'];
    }
	public function zhuboinfo(Zhubos $model,Request $request){
	    $id = $request->post('id');

		$res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('file_path',function($value,$data){
			return $this->url.$value;
		})->withAttr('content',function($value,$data){
	         return replace_pic_url($value,$this->url);
	    })->withAttr('lvshi_title',function($value,$data){
	         return Lvshis::where(['id'=>$data['lvshi_id']])->value('title');
	    })->withAttr('lvshi_path',function($value,$data){
	         return $this->url.Lvshis::where(['id'=>$data['lvshi_id']])->value('pic_path');
	    })->withAttr('is_zb',function($v,$d){
           if(time()*1>$d['start_time']*1) {

                if(time()*1>$d['end_time']*1){
                    return 3;
                }else{
                    return $this->hasLiveStatus($d['appname'],$d['streamname']);
                }
            }else{
                return 1;
            }
        })->withAttr('user_count',function($v,$d){
            if(time()*1>$d['end_time']*1){
                    return 0;
            }else{
                return Lines::where(['zhubo_id'=>$d['id'],'is_line'=>1])->count();
            }
        })
        ->append(['is_zb','lvshi_title','lvshi_path','user_count'])->find($id);
        if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("已下架");

	}
	public function bannerList(Banners $model){
		$res = $model->select();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
	public  function lawyerCateList(Zhuanyes $model){
		$list = $model::withAttr('id',function($v,$d){
			return $v.'';
		})->withAttr('isChecked',function($v,$d){
			return false;
		})->append(['isChecked'])->select();
		if(!empty($list)) return JsonService::successful("1",$list);
		else return JsonService::fail("2");

	}
	public function lawyerArea(){
		$model = new District();
		$res = $model::withAttr('children',function($value,$data){
			return District::field('title as text,id as value')->where(['upid'=>$data['value']])->select();
		})

		->field('title as text,id as value')
		->append(['children'])
		->where(['type'=>1])
		->select();
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res);
	}
	public function lawyerCate(){
		$model  = new Zhuanyes();
		$title = $model->column('title');
		$id=$model->column('id');
		array_unshift($title,"类型");
		array_unshift($id,0);
		return JsonService::successful("1",['title'=>$title,'id'=>$id]);
	}
	public function lawyerType(Lvshicates $model){
		$title = $model->column('title');
		$id=$model->column('id');
		array_unshift($title,"全部");
		array_unshift($id,0);
		return JsonService::successful("",['title'=>$title,'id'=>$id]);
	}
	//保存律师
	public function lawyerSave(Request $request,Companys $model){
		$form = $request->post();
		$validate = new \app\index\validate\Company;
		if (!$validate->check($form))  return JsonService::fail( $validate->getError());
		$res =$model->saveData($form);
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res);
	}
	//律师页面
	public function lawyerList(Request $request,Lvshis $model){
		$search=$request->post();
		$where = [];
		// $where['city_id']=empty($search['cid'])?'':$search['cid'];
		// $where['pid']=empty($search['pid'])?'':$search['pid'];
		// $where['cate_id']=empty($search['cate_id'])?'':$search['cate_id'];
		// $where['title']=empty($search['title'])?'':$search['title'];

		$limit = 10;
		$res = $model::withAttr('zhuanyes',function($value,$data){
			if(!empty($value)) return Zhuanyes::where('id','in',$value)->column('title');
		})->withAttr('city_id',function($value,$data){
			if(!empty($value)) return District::where(['id'=>$value])->value('title');;
		})->withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})
		->where($where)
		->order(['click'=>'desc'])
		->limit($limit)
		->select();


		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res->toArray());
	}

	public function lawyerInfo(Request $request,Lvshis $model){
		$id=$request->post('id');
		$res = $model::withAttr('zhuanyes',function($value,$data){
			if(!empty($value)) return Zhuanyes::where('id','in',$value)->column('title');
		})->withAttr('city_id',function($value,$data){
			if(!empty($value)) return District::where(['id'=>$value])->value('title');;
		})->withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->find();
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res->toArray());
	}
	public function aboutList(){
		$res =[
			replace_pic_url($this->configs['index_about_content'],$this->url),
			replace_pic_url($this->configs['index_team_content'],$this->url),
			replace_pic_url($this->configs['index_peixun_content']),
			replace_pic_url($this->configs['index_xuexi_content']),
		];

		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
	public function city(){
		$model = new District();
    	$p = $model->where('upid',0)->column('title');
	    $pid = $model->where('upid',0)->column('id');
		array_unshift($p,"全部");
		array_unshift($pid,'0');
		$c=$model->where(['upid'=>$pid[0]])->column('title');
		$cid=$model->where(['upid'=>$pid[0]])->column('id');
		array_unshift($c,"全部");
		array_unshift($cid,'0');
		$d=$model->where(['upid'=>$cid[0]])->column('title');
		$did=$model->where(['upid'=>$cid[0]])->column('id');
		array_unshift($d,"全部");
		array_unshift($did,'0');
		$city = [$p,$c,$d];
		$id=[
			$pid,$cid,$did
		];
        JsonService::successful('加载成功',['city'=>$city,'id'=>$id]);
	}

	public function changeCity(Request $request){
		$model = new District();
		$ptitle=$model->where(['upid'=>0])->column('title');
		$pid=$model->where(['upid'=>0])->column('id');
		array_unshift($ptitle,"全部");
		array_unshift($pid,'0');
		$upid= $request->get('upid');
		$where = ['upid'=>$upid];
		$ctitle=$model->where($where)->column('title');
		$cid=$model->where($where)->column('id');

		$dtitle=$model->where(['upid'=>$cid[0]])->column('title');
		$did=$model->where(['upid'=>$cid[0]])->column('id');
		array_unshift($ctitle,"全部");
		array_unshift($cid,'0');
		array_unshift($dtitle,"全部");
		array_unshift($did,'0');
			$title=[
			$ptitle,
			$ctitle,
			$dtitle,
			];
		$id=[
			$pid,$cid,$did
			];
		if(!empty($title)) JsonService::successful('加载成功',['title'=>$title,'id'=>$id]);
		else return JsonService::fail('加载失败');
	}

	public function changeDist(Request $request){
		$model = new District();
		$upid= $request->get('upid');
		$where = ['upid'=>$upid];
		$dtitle=$model->where($where)->column('title');
		$did=$model->where($where)->column('id');
		array_unshift($dtitle,"全部");
		array_unshift($did,'0');
		if(!empty($dtitle)) JsonService::successful('加载成功',['title'=>$dtitle,'id'=>$did]);
		else return JsonService::fail('加载失败');
	}

	//案件调解
	//
	public function caseSave(Request $request,Tiaojies $model){
		$form=$request->post();

		if(empty($form['uid'])) return JsonService::fail('请先登陆');
		$validate = new \app\index\validate\LawCase;
		if (!$validate->check($form))  return JsonService::fail( $validate->getError());
		$res =$model->saveData($form);
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success');
	}

	public function myCaseList(Request $request,Tiaojies $model){
		$res =$model::withAttr('cate_id',function($value,$data){
			return Zhuanyes::where(['id'=>$value])->value('title');
		})->withAttr('gengjing',function($v,$d){
			return Tiaojiegengjings::withAttr('sliger_images',function($value,$data){
				$data = unserialize($value);
				foreach ($data as $key => $value) {
					$data[$key]['url'] = $this->url.$value['url'];
				}
				return $data;
			})
			->where(['tiaojie_id'=>$d['id']])
			->order(['create_time'=>'desc'])
			->select();
		})
		->append(['gengjing'])
		->select();
		if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res);
	}

	public function getWenshuCate(Wenshucates $model){
	   	$res = $model->select();
	   	$info = current($res->toArray());

		if(!empty($res)) return JsonService::successful("1",['cates'=>$res,'cate_id'=>$info['id']]);
		else return JsonService::fail("2");
	}

	public function getWenshuList(Wenshus $model,Request $request){
	    $cate_id = $request->post('cate_id');
	    $res = $model::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})
	    ->where(['cate_id'=>$cate_id])

	    ->select();
	    if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res);
	}
	public static function get_column_cate($where,$limit='0,3'){
		$model  = new Zhuanyes();
		$list = $model->where($where)->limit($limit)->column('title');
		return empty($list) ?[]:$list;
	}

	public static function get_city_name($id){
		$model  = new District();
		$list = District::where(['id'=>$id])->value('title');
		return empty($list) ?[]:$list;
	}


    public function findYewuyuan(Request $request,Yuangongs $model){
        $post = $request->post();
        if(empty($post['id'])) return JsonService::fail('未查询到相关业务员');
        $res = $model->find($post['id']);
        if(empty($res)) return JsonService::fail('fail');
		else return JsonService::successful('success',$res);

    }

    public function bindYewuyuan(Request $request,Yuangongs $model,Users $Users){
        $post = $request->post();
        if(empty($post['yuangong_id'])) return JsonService::fail('未查询到相关业务员');
        $res = $model->find($post['yuangong_id']);
        if(empty($res)) return JsonService::fail('业务员离职');
        $info = $Users->where(['phone'=>$post['phone']])->find();
        if(empty($info)) return JsonService::fail('尊敬的用户,未查询到您的相关信息,请先注册');
        if(!empty($info->yuangong_id)) return JsonService::fail('尊敬的用户,您已绑定业务员,请勿重复绑定');
        try {
            $info->yuangong_id = $post['yuangong_id'];
            $info->save();
            return JsonService::successful('绑定成功');
        } catch (\Exception $e) {
            return JsonService::fail('系统异常:'.$e->getMessage());
        }

    }


}
