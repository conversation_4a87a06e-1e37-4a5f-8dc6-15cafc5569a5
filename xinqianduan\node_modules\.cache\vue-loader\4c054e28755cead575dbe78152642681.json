{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue?vue&type=style&index=0&id=0f8e2eec&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue", "mtime": 1748617965012}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["type.vue"], "names": [], "mappings": ";AA8p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file": "type.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n  <div class=\"service-type-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-menu\"></i>\r\n          {{ this.$router.currentRoute.name }}\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务分类和类型配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-menu\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">服务类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeTypes }}</div>\r\n              <div class=\"stat-label\">活跃类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 正常\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon usage-icon\">\r\n              <i class=\"el-icon-data-analysis\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">85%</div>\r\n              <div class=\"stat-label\">使用率</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +3%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <span class=\"card-title\">\r\n            <i class=\"el-icon-search\"></i>\r\n            搜索与筛选\r\n          </span>\r\n          <div class=\"card-subtitle\">快速查找和管理服务类型</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button-group class=\"action-group\">\r\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n              导出\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n              刷新\r\n            </el-button>\r\n          </el-button-group>\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\" class=\"primary-action\">\r\n            新增类型\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n              <el-input \r\n                placeholder=\"请输入类型名称或描述关键词...\" \r\n                v-model=\"search.keyword\" \r\n                clearable\r\n                prefix-icon=\"el-icon-search\"\r\n                class=\"search-input\"\r\n                @keyup.enter.native=\"searchData()\"\r\n              />\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"服务类型\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.serviceType\" \r\n                placeholder=\"选择服务类型\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部类型\" value=\"\" />\r\n                <el-option label=\"计次服务\" value=\"1\" />\r\n                <el-option label=\"不限次数\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"状态筛选\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.status\" \r\n                placeholder=\"选择状态\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部状态\" value=\"\" />\r\n                <el-option label=\"正常\" value=\"1\" />\r\n                <el-option label=\"待完善\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item class=\"search-actions-item\">\r\n              <div class=\"search-actions\">\r\n                <el-button type=\"primary\" @click=\"searchData\" icon=\"el-icon-search\" class=\"search-btn\">\r\n                  搜索\r\n                </el-button>\r\n                <el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n                  重置\r\n                </el-button>\r\n                <el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n                  <i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n                  {{ showAdvanced ? '收起' : '高级筛选' }}\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 高级筛选区域 -->\r\n          <transition name=\"slide-fade\">\r\n            <div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n              <el-divider content-position=\"left\">\r\n                <i class=\"el-icon-setting\"></i>\r\n                高级筛选选项\r\n              </el-divider>\r\n              <div class=\"advanced-content\">\r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"创建时间范围\" class=\"advanced-item\">\r\n                    <el-date-picker\r\n                      v-model=\"search.dateRange\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      class=\"date-picker\"\r\n                    />\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.sortBy\" placeholder=\"选择排序\" class=\"sort-select\">\r\n                      <el-option label=\"创建时间\" value=\"create_time\" />\r\n                      <el-option label=\"名称字母\" value=\"title\" />\r\n                      <el-option label=\"使用频率\" value=\"usage\" />\r\n                      <el-option label=\"更新时间\" value=\"update_time\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序顺序\" class=\"advanced-item\">\r\n                    <el-radio-group v-model=\"search.sortOrder\" size=\"small\" class=\"sort-order\">\r\n                      <el-radio-button label=\"desc\">\r\n                        <i class=\"el-icon-sort-down\"></i> 降序\r\n                      </el-radio-button>\r\n                      <el-radio-button label=\"asc\">\r\n                        <i class=\"el-icon-sort-up\"></i> 升序\r\n                      </el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </div>\r\n                \r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"使用频率\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.usageLevel\" placeholder=\"选择使用频率\" class=\"usage-select\">\r\n                      <el-option label=\"全部频率\" value=\"\" />\r\n                      <el-option label=\"高频使用\" value=\"high\" />\r\n                      <el-option label=\"中频使用\" value=\"medium\" />\r\n                      <el-option label=\"低频使用\" value=\"low\" />\r\n                      <el-option label=\"未使用\" value=\"none\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"类型特性\" class=\"advanced-item\">\r\n                    <el-checkbox-group v-model=\"search.features\" class=\"feature-checkboxes\">\r\n                      <el-checkbox label=\"popular\">热门类型</el-checkbox>\r\n                      <el-checkbox label=\"new\">新增类型</el-checkbox>\r\n                      <el-checkbox label=\"recommended\">推荐类型</el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item class=\"advanced-actions\">\r\n                    <el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n                      应用筛选\r\n                    </el-button>\r\n                    <el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n                      清空高级选项\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <el-card shadow=\"hover\" class=\"table-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          类型列表\r\n        </span>\r\n        <div class=\"table-actions\">\r\n          <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n            导出数据\r\n          </el-button>\r\n          <el-button size=\"small\" @click=\"batchDelete\" icon=\"el-icon-delete\">\r\n            批量删除\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"modern-table\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        \r\n        <el-table-column label=\"类型信息\" min-width=\"300\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-info\">\r\n              <div class=\"type-header\">\r\n                <div class=\"type-icon\">\r\n                  <i class=\"el-icon-star-on\"></i>\r\n                </div>\r\n                <div class=\"type-details\">\r\n                  <div class=\"type-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"type-desc\" v-if=\"scope.row.desc\">\r\n                    {{ scope.row.desc }}\r\n                  </div>\r\n                  <div class=\"type-features\">\r\n                    <el-tag \r\n        size=\"mini\"\r\n                      :type=\"scope.row.is_num == 1 ? 'success' : 'info'\"\r\n                    >\r\n                      {{ scope.row.is_num == 1 ? '计次服务' : '不限次数' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              {{ formatDate(scope.row.create_time) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row)\" effect=\"dark\">\r\n              {{ getStatusText(scope.row) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button \r\n                type=\"text\" \r\n                size=\"small\" \r\n                @click=\"editData(scope.row.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                <i class=\"el-icon-edit\"></i>\r\n                编辑\r\n              </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                class=\"delete-btn\"\r\n            >\r\n                <i class=\"el-icon-delete\"></i>\r\n                删除\r\n            </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"600px\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-form-item label=\"类型名称\" prop=\"title\">\r\n          <el-input \r\n            v-model=\"ruleForm.title\" \r\n            placeholder=\"请输入服务类型名称\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"计次设置\">\r\n          <el-radio-group v-model=\"ruleForm.is_num\">\r\n            <el-radio :label=\"1\">计次服务</el-radio>\r\n            <el-radio :label=\"0\">不限次数</el-radio>\r\n          </el-radio-group>\r\n          <div class=\"form-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            计次服务将限制使用次数，不限次数则可无限使用\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"类型描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入服务类型的详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <div class=\"image-viewer\">\r\n        <el-image :src=\"show_image\" fit=\"contain\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      showAdvanced: false,\r\n      search: {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      },\r\n      loading: true,\r\n      url: \"/type/\",\r\n      title: \"服务类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      saveLoading: false,\r\n      selectedRows: [],\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写类型名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    activeTypes() {\r\n      return Array.isArray(this.list) ? this.list.filter(item => item.title && item.title.trim() !== '').length : 0;\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑服务类型' : '新增服务类型';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_num: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 新增方法\r\n    getStatusType(row) {\r\n      // 根据数据判断状态类型\r\n      if (row.title && row.title.trim() !== '') {\r\n        return 'success';\r\n      }\r\n      return 'info';\r\n    },\r\n    getStatusText(row) {\r\n      // 根据数据判断状态文本\r\n      if (row.title && row.title.trim() !== '') {\r\n        return '正常';\r\n      }\r\n      return '待完善';\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      };\r\n      this.showAdvanced = false;\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    toggleAdvanced() {\r\n      this.showAdvanced = !this.showAdvanced;\r\n    },\r\n    refreshData() {\r\n      this.getData();\r\n      this.$message.success('数据已刷新');\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n    exportData() {\r\n      this.$message.success('数据导出功能开发中...');\r\n    },\r\n    batchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请先选择要删除的数据');\r\n        return;\r\n      }\r\n      \r\n      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条数据吗？`, '批量删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里实现批量删除逻辑\r\n        this.$message.success('批量删除功能开发中...');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    applyAdvancedSearch() {\r\n      this.page = 1;\r\n      this.getData();\r\n      this.$message.success('高级筛选已应用');\r\n    },\r\n    clearAdvancedSearch() {\r\n      this.search.dateRange = [];\r\n      this.search.sortBy = \"create_time\";\r\n      this.search.sortOrder = \"desc\";\r\n      this.search.usageLevel = \"\";\r\n      this.search.features = [];\r\n      this.$message.info('高级筛选选项已清空');\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.service-type-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.usage-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.sort-select,\r\n.usage-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.sort-select .el-input__inner,\r\n.usage-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sort-select .el-input__inner:focus,\r\n.usage-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.sort-order {\r\n  width: 100%;\r\n}\r\n\r\n.sort-order .el-radio-button__inner {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sort-order .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-color: #667eea;\r\n  color: white;\r\n  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.feature-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n  width: 100%;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox {\r\n  margin: 0;\r\n  flex: 1;\r\n  min-width: 80px;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox__label {\r\n  font-size: 13px;\r\n  color: #495057;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox__input.is-checked .el-checkbox__inner {\r\n  background-color: #667eea;\r\n  border-color: #667eea;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 类型信息样式 */\r\n.type-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.type-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.type-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.type-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.type-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 6px;\r\n  line-height: 1.4;\r\n  max-height: 40px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.type-features {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  margin-top: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.form-tip i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .service-type-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-radio-group {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .type-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .type-title {\r\n    font-size: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}