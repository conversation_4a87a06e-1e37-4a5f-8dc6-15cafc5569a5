{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\DebtDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\DebtDetail.vue", "mtime": 1748616140800}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DebtDetail.vue"], "names": [], "mappings": ";AAkUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DebtDetail.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div class=\"debt-detail-container\">\r\n    <!-- 操作按钮区域 -->\r\n    <div class=\"action-bar\">\r\n      <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-download\" @click=\"exports\">\r\n        导出跟进记录\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 债务基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务基本信息</span>\r\n        <div class=\"debt-status\">\r\n          <el-tag :type=\"getDebtStatusType()\" size=\"medium\">\r\n            {{ getDebtStatusText() }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">委托人</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人姓名</div>\r\n            <div class=\"info-value\">{{ info.name || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人电话</div>\r\n            <div class=\"info-value phone-number\">{{ info.tel || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人地址</div>\r\n            <div class=\"info-value\">{{ info.address || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card debt-amount\">\r\n            <div class=\"amount-label\">债务总金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card back-amount\">\r\n            <div class=\"amount-label\">已回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.back_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card remaining-amount\">\r\n            <div class=\"amount-label\">未回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.un_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">提交时间</div>\r\n            <div class=\"info-value\">{{ info.ctime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">最后修改时间</div>\r\n            <div class=\"info-value\">{{ info.utime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n    <!-- 债务人身份信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.cards && info.cards.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-postcard\"></i>\r\n        <span class=\"card-title\">债务人身份信息</span>\r\n      </div>\r\n\r\n      <div class=\"id-cards-grid\">\r\n        <div\r\n          v-for=\"(card, index) in info.cards\"\r\n          :key=\"index\"\r\n          class=\"id-card-item\"\r\n          @click=\"showImage(card)\">\r\n          <el-image\r\n            :src=\"card\"\r\n            fit=\"cover\"\r\n            class=\"id-card-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"id-card-label\">\r\n            {{ index === 0 ? '身份证正面' : '身份证反面' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 案由信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span class=\"card-title\">案由描述</span>\r\n      </div>\r\n\r\n      <div class=\"case-description\">\r\n        <p>{{ info.case_des || '暂无案由描述' }}</p>\r\n      </div>\r\n    </el-card>\r\n    <!-- 证据图片卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.images && info.images.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-picture\"></i>\r\n        <span class=\"card-title\">证据图片</span>\r\n        <el-button\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"downloadFiles(info.images_download)\"\r\n          class=\"header-action\">\r\n          全部下载\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"evidence-images-grid\">\r\n        <div\r\n          v-for=\"(image, index) in info.images\"\r\n          :key=\"index\"\r\n          class=\"evidence-image-item\">\r\n          <el-image\r\n            :src=\"image\"\r\n            :preview-src-list=\"info.images\"\r\n            fit=\"cover\"\r\n            class=\"evidence-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"evidence-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(image, `evidence_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 证据文件卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.attach_path && info.attach_path.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-folder\"></i>\r\n        <span class=\"card-title\">证据文件</span>\r\n      </div>\r\n\r\n      <div class=\"evidence-files-list\">\r\n        <div\r\n          v-for=\"(file, index) in info.attach_path\"\r\n          :key=\"index\"\r\n          class=\"file-item\"\r\n          v-if=\"file\">\r\n          <div class=\"file-info\">\r\n            <i :class=\"getFileIcon(file)\" class=\"file-icon\"></i>\r\n            <div class=\"file-details\">\r\n              <div class=\"file-name\">文件{{ index + 1 }}</div>\r\n              <div class=\"file-type\">{{ getFileExtension(file) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"file-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"viewFile(file)\">\r\n              查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(file, `file_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n    <!-- 跟进记录卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-time\"></i>\r\n        <span class=\"card-title\">跟进记录</span>\r\n        <div class=\"record-count\">\r\n          共 {{ info.debttrans ? info.debttrans.length : 0 }} 条记录\r\n        </div>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debttrans\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n        class=\"follow-up-table\">\r\n        <el-table-column prop=\"day\" label=\"跟进日期\" width=\"110\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-date\"></i>\r\n            {{ scope.row.day }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-time\"></i>\r\n            {{ scope.row.ctime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"au_id\" label=\"操作人员\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"info\" size=\"small\">{{ scope.row.au_id }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"进度类型\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getProgressType(scope.row.type)\" size=\"small\">\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"total_price\" label=\"费用金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text\" v-if=\"scope.row.total_price && scope.row.total_price !== '0'\">\r\n              ¥{{ scope.row.total_price }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"content\" label=\"费用内容\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.content || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"回款金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text success\" v-if=\"scope.row.back_money && scope.row.back_money !== '0'\">\r\n              ¥{{ scope.row.back_money }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_type\" label=\"支付状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.pay_type === '已支付' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.pay_type || '未支付' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_time\" label=\"支付时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_time || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_order_type\" label=\"支付方式\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_order_type || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"进度描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"desc-content\">{{ scope.row.desc }}</div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n              class=\"danger-btn\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debttrans || info.debttrans.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无跟进记录</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%;\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'DebtDetail',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {\r\n            nickname: '',\r\n            name: '',\r\n            tel: '',\r\n            address: '',\r\n            money: '',\r\n            back_money: '',\r\n            un_money: '',\r\n            ctime: '',\r\n            utime: '',\r\n            case_des: '',\r\n            cards: [],\r\n            images: [],\r\n            images_download: [],\r\n            attach_path: [],\r\n            debttrans: []\r\n          }, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取债务详情，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testDebtData = {\r\n            id: id,\r\n            nickname: \"张三\",\r\n            name: \"债务人李四\",\r\n            tel: \"13900139001\",\r\n            address: \"北京市朝阳区测试街道123号\",\r\n            money: \"50000\",\r\n            back_money: \"10000\",\r\n            un_money: \"40000\",\r\n            ctime: \"2024-01-01 10:00:00\",\r\n            utime: \"2024-01-15 15:30:00\",\r\n            case_des: \"借款纠纷，借款人未按约定时间还款，现申请追讨欠款及利息。\",\r\n            cards: [\r\n              \"/static/images/id_card_front.jpg\",\r\n              \"/static/images/id_card_back.jpg\"\r\n            ],\r\n            images: [\r\n              \"/static/images/evidence1.jpg\",\r\n              \"/static/images/evidence2.jpg\",\r\n              \"/static/images/evidence3.jpg\"\r\n            ],\r\n            images_download: [\r\n              { name: \"证据1.jpg\", path: \"/static/images/evidence1.jpg\" },\r\n              { name: \"证据2.jpg\", path: \"/static/images/evidence2.jpg\" }\r\n            ],\r\n            attach_path: [\r\n              \"/static/files/contract.pdf\",\r\n              \"/static/files/bank_record.xlsx\"\r\n            ],\r\n            debttrans: [\r\n              {\r\n                id: 1,\r\n                day: \"2024-01-15\",\r\n                ctime: \"2024-01-15 10:30:00\",\r\n                au_id: \"调解员王五\",\r\n                type: \"电话联系\",\r\n                total_price: \"0\",\r\n                content: \"联系费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"未支付\",\r\n                pay_time: \"\",\r\n                pay_order_type: \"\",\r\n                desc: \"已与债务人取得联系，对方表示将在本月底前还款\"\r\n              },\r\n              {\r\n                id: 2,\r\n                day: \"2024-01-10\",\r\n                ctime: \"2024-01-10 14:20:00\",\r\n                au_id: \"法务赵六\",\r\n                type: \"发送催款函\",\r\n                total_price: \"200\",\r\n                content: \"律师函费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"已支付\",\r\n                pay_time: \"2024-01-10 14:25:00\",\r\n                pay_order_type: \"微信支付\",\r\n                desc: \"向债务人发送正式催款函，要求在15日内还款\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testDebtData;\r\n          _this.loading = false;\r\n          console.log('债务详情数据加载完成:', testDebtData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/debt/view?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取债务详情失败:', resp);\r\n            _this.$message({\r\n              type: \"error\",\r\n              message: resp.msg || \"获取数据失败\",\r\n            });\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n        downloadFiles(imgs) {\r\n            imgs.forEach((file) => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = file.path;\r\n                link.download = file.name;\r\n                link.click();\r\n            });\r\n        },\r\n        exports:function () { //导出表格\r\n            let _this = this;\r\n            location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n        },\r\n\r\n        // 显示图片\r\n        showImage(imageUrl) {\r\n          this.show_image = imageUrl;\r\n          this.dialogVisible = true;\r\n        },\r\n\r\n        // 下载单个文件\r\n        downloadSingleFile(url, filename) {\r\n          const link = document.createElement(\"a\");\r\n          link.href = url;\r\n          link.download = filename;\r\n          link.click();\r\n        },\r\n\r\n        // 查看文件\r\n        viewFile(url) {\r\n          window.open(url, '_blank');\r\n        },\r\n\r\n        // 获取文件图标\r\n        getFileIcon(filename) {\r\n          const ext = this.getFileExtension(filename).toLowerCase();\r\n          const iconMap = {\r\n            'pdf': 'el-icon-document',\r\n            'doc': 'el-icon-document',\r\n            'docx': 'el-icon-document',\r\n            'xls': 'el-icon-s-grid',\r\n            'xlsx': 'el-icon-s-grid',\r\n            'txt': 'el-icon-document',\r\n            'zip': 'el-icon-folder',\r\n            'rar': 'el-icon-folder'\r\n          };\r\n          return iconMap[ext] || 'el-icon-document';\r\n        },\r\n\r\n        // 获取文件扩展名\r\n        getFileExtension(filename) {\r\n          return filename.split('.').pop() || '';\r\n        },\r\n\r\n        // 格式化金额\r\n        formatMoney(amount) {\r\n          if (!amount || amount === '0') return '0.00';\r\n          return parseFloat(amount).toLocaleString('zh-CN', {\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2\r\n          });\r\n        },\r\n\r\n        // 获取债务状态类型\r\n        getDebtStatusType() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return 'success';\r\n          if (unMoney > 0) return 'warning';\r\n          return 'info';\r\n        },\r\n\r\n        // 获取债务状态文本\r\n        getDebtStatusText() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return '已结清';\r\n          if (unMoney > 0) return '未结清';\r\n          return '处理中';\r\n        },\r\n\r\n        // 获取进度类型\r\n        getProgressType(type) {\r\n          const typeMap = {\r\n            '电话联系': 'primary',\r\n            '发送催款函': 'warning',\r\n            '法院起诉': 'danger',\r\n            '调解成功': 'success',\r\n            '回款确认': 'success'\r\n          };\r\n          return typeMap[type] || 'info';\r\n        },\r\n\r\n        // 删除数据\r\n        delData(index, id) {\r\n          this.$confirm('确定要移除这条跟进记录吗？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 这里应该调用删除API\r\n            this.info.debttrans.splice(index, 1);\r\n            this.$message.success('删除成功');\r\n          }).catch(() => {\r\n            this.$message.info('已取消删除');\r\n          });\r\n        }\r\n    }\r\n  }\r\n</script>\r\n"]}]}