{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748608591384}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings": ";AA2kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\" @click=\"showMemberPanel = false\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"用户详情\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-user\"\r\n                @click.stop=\"showUserDetail = !showUserDetail\"\r\n                class=\"user-detail-btn\"\r\n                :class=\"{ 'active': showUserDetail }\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-more\"\r\n                @click.stop=\"toggleMemberPanel\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户详情侧边栏 -->\r\n        <div class=\"user-detail-sidebar\" :class=\"{ 'show': showUserDetail }\" @click.stop>\r\n          <div class=\"detail-header\">\r\n            <h3>用户详情</h3>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"showUserDetail = false\"\r\n              class=\"close-btn\"\r\n            ></el-button>\r\n          </div>\r\n\r\n          <div class=\"detail-content\">\r\n            <!-- 左侧子菜单 -->\r\n            <div class=\"detail-menu\">\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'info' }\"\r\n                   @click=\"activeDetailTab = 'info'\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>基本信息</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'debtors' }\"\r\n                   @click=\"activeDetailTab = 'debtors'\">\r\n                <i class=\"el-icon-s-custom\"></i>\r\n                <span>关联债务人</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'documents' }\"\r\n                   @click=\"activeDetailTab = 'documents'\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>相关文档</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'orders' }\"\r\n                   @click=\"activeDetailTab = 'orders'\">\r\n                <i class=\"el-icon-tickets\"></i>\r\n                <span>工单记录</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'payments' }\"\r\n                   @click=\"activeDetailTab = 'payments'\">\r\n                <i class=\"el-icon-money\"></i>\r\n                <span>支付记录</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧内容区域 -->\r\n            <div class=\"detail-main\">\r\n              <!-- 基本信息 -->\r\n              <div v-if=\"activeDetailTab === 'info'\" class=\"tab-content\">\r\n                <div class=\"user-profile\">\r\n                  <div class=\"profile-avatar\">\r\n                    <img :src=\"currentUserDetail.avatar\" alt=\"用户头像\" />\r\n                  </div>\r\n                  <div class=\"profile-info\">\r\n                    <h4>{{ currentUserDetail.name }}</h4>\r\n                    <p class=\"user-type\">{{ currentUserDetail.type }}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"info-section\">\r\n                  <div class=\"info-item\">\r\n                    <label>手机号码：</label>\r\n                    <span>{{ currentUserDetail.phone }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentUserDetail.idCard }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>注册时间：</label>\r\n                    <span>{{ currentUserDetail.registerTime }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>最后登录：</label>\r\n                    <span>{{ currentUserDetail.lastLogin }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>用户状态：</label>\r\n                    <el-tag :type=\"currentUserDetail.status === '正常' ? 'success' : 'danger'\">\r\n                      {{ currentUserDetail.status }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 关联债务人 -->\r\n              <div v-if=\"activeDetailTab === 'debtors'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>关联债务人列表</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.debtors.length }}人</span>\r\n                </div>\r\n                <div class=\"debtors-list\">\r\n                  <div v-for=\"debtor in currentUserDetail.debtors\" :key=\"debtor.id\" class=\"debtor-card\">\r\n                    <div class=\"debtor-info\">\r\n                      <div class=\"debtor-name\">{{ debtor.name }}</div>\r\n                      <div class=\"debtor-details\">\r\n                        <span class=\"debt-amount\">欠款金额：¥{{ debtor.amount }}</span>\r\n                        <span class=\"debt-status\" :class=\"debtor.status\">{{ debtor.statusText }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"debtor-actions\">\r\n                      <el-button type=\"text\" size=\"small\">查看详情</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 相关文档 -->\r\n              <div v-if=\"activeDetailTab === 'documents'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>相关文档</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.documents.length }}个</span>\r\n                </div>\r\n                <div class=\"documents-list\">\r\n                  <div v-for=\"doc in currentUserDetail.documents\" :key=\"doc.id\" class=\"document-item\">\r\n                    <div class=\"doc-icon\">\r\n                      <i :class=\"getDocIcon(doc.type)\"></i>\r\n                    </div>\r\n                    <div class=\"doc-info\">\r\n                      <div class=\"doc-name\">{{ doc.name }}</div>\r\n                      <div class=\"doc-meta\">\r\n                        <span>{{ doc.size }}</span>\r\n                        <span>{{ doc.uploadTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"doc-actions\">\r\n                      <el-button type=\"text\" size=\"small\" @click=\"downloadDoc(doc)\">下载</el-button>\r\n                      <el-button type=\"text\" size=\"small\" @click=\"previewDoc(doc)\">预览</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 工单记录 -->\r\n              <div v-if=\"activeDetailTab === 'orders'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>工单记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.orders.length }}个</span>\r\n                </div>\r\n                <div class=\"orders-list\">\r\n                  <div v-for=\"order in currentUserDetail.orders\" :key=\"order.id\" class=\"order-item\">\r\n                    <div class=\"order-info\">\r\n                      <div class=\"order-title\">{{ order.title }}</div>\r\n                      <div class=\"order-meta\">\r\n                        <span class=\"order-type\">{{ order.type }}</span>\r\n                        <span class=\"order-time\">{{ order.createTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"order-status\">\r\n                      <el-tag :type=\"getOrderStatusType(order.status)\">{{ order.status }}</el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 支付记录 -->\r\n              <div v-if=\"activeDetailTab === 'payments'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>支付记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.payments.length }}笔</span>\r\n                </div>\r\n                <div class=\"payments-list\">\r\n                  <div v-for=\"payment in currentUserDetail.payments\" :key=\"payment.id\" class=\"payment-item\">\r\n                    <div class=\"payment-info\">\r\n                      <div class=\"payment-desc\">{{ payment.description }}</div>\r\n                      <div class=\"payment-time\">{{ payment.time }}</div>\r\n                    </div>\r\n                    <div class=\"payment-amount\">\r\n                      <span class=\"amount\">¥{{ payment.amount }}</span>\r\n                      <el-tag :type=\"payment.status === '已支付' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ payment.status }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n      showMemberPanel: false,\r\n      memberData: {\r\n        users: [],\r\n        lawyers: [],\r\n        staff: []\r\n      },\r\n      showUserDetail: true, // 默认打开用户详情面板\r\n      activeDetailTab: 'info', // 默认显示基本信息\r\n      currentUserDetail: {\r\n        name: '',\r\n        type: '',\r\n        avatar: '',\r\n        phone: '',\r\n        idCard: '',\r\n        registerTime: '',\r\n        lastLogin: '',\r\n        status: '',\r\n        debtors: [],\r\n        documents: [],\r\n        orders: [],\r\n        payments: []\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      _this.isEmji = !_this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      _this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      _this.imgUlr = img;\r\n      _this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      console.log('点击私聊:', index);\r\n      _this.selectId = index;\r\n      _this.quliaoIndex = -1;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    changeQun(index) {\r\n      console.log('点击群聊:', index);\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    getEmoji(item) {\r\n      _this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (_this.search) _this.isShowSeach = true;\r\n      else _this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      _this.search = \"\";\r\n      _this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (_this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 群聊测试数据\r\n      _this.quns = [\r\n        {\r\n          id: 1,\r\n          title: \"法务团队群\",\r\n          desc: \"最新消息：合同审核已完成\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          create_time: \"09:30\",\r\n          count: 3,\r\n          uid: \"1,2,3\",\r\n          lvshi_id: \"1,2\",\r\n          yuangong_id: \"1,2,3\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"客户服务群\",\r\n          desc: \"张三：请问合同什么时候能完成？\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          create_time: \"昨天\",\r\n          count: 1,\r\n          uid: \"4,5\",\r\n          lvshi_id: \"3\",\r\n          yuangong_id: \"4,5\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"紧急处理群\",\r\n          desc: \"李四：这个案件需要加急处理\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          create_time: \"15:20\",\r\n          count: 5,\r\n          uid: \"1,3,5\",\r\n          lvshi_id: \"1\",\r\n          yuangong_id: \"1,3\",\r\n          is_daiban: 1\r\n        }\r\n      ];\r\n\r\n      // 私聊用户测试数据\r\n      _this.users = [\r\n        {\r\n          id: 1,\r\n          title: \"张三（客户）\",\r\n          content: \"您好，请问我的合同审核进度如何？\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          time: \"10:30\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"李四（律师）\",\r\n          content: \"合同已经审核完毕，请查收\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          time: \"09:15\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"王五（调解员）\",\r\n          content: \"调解会议安排在明天下午2点\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          time: \"昨天\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"赵六（客户）\",\r\n          content: \"谢谢您的帮助！\",\r\n          pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n          time: \"16:45\"\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据用于搜索\r\n      _this.yuanshiquns = [..._this.quns];\r\n      _this.yuanshiusers = [..._this.users];\r\n\r\n      // 设置默认选中第一个群聊\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = 0;\r\n\r\n      // 加载第一个群聊的消息\r\n      setTimeout(() => {\r\n        _this.loadTestMessages();\r\n      }, 500);\r\n    },\r\n    // 加载测试消息数据\r\n    loadTestMessages() {\r\n      console.log('加载测试消息, selectId:', _this.selectId, 'quliaoIndex:', _this.quliaoIndex);\r\n      // 设置当前聊天标题\r\n      if (_this.selectId !== -1) {\r\n        _this.title = _this.users[_this.selectId].title;\r\n        // 加载私聊消息\r\n        _this.loadPrivateMessages();\r\n      } else {\r\n        const qun = _this.quns[_this.quliaoIndex];\r\n        const count = qun.uid.split(',').length + qun.lvshi_id.split(',').length + qun.yuangong_id.split(',').length;\r\n        _this.title = qun.title + \"(\" + count + \")\";\r\n        // 加载群聊消息\r\n        _this.loadGroupMessages();\r\n      }\r\n\r\n      // 滚动到底部\r\n      _this.$nextTick(() => {\r\n        if (_this.$refs.list) {\r\n          _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n        }\r\n      });\r\n    },\r\n    // 加载群聊消息\r\n    loadGroupMessages() {\r\n      const groupMessages = {\r\n        0: [ // 法务团队群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:00:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"大家好，今天我们讨论一下最新的合同审核流程\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:05:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张专员\",\r\n            type: \"text\",\r\n            content: \"好的，我这边已经准备好相关材料了\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 09:10:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"请大家查看一下这份合同模板\"\r\n          },\r\n          {\r\n            id: 4,\r\n            create_time: \"2024-01-22 09:12:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"image\",\r\n            content: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          },\r\n          {\r\n            id: 5,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"这个模板看起来不错，我们可以在此基础上进行修改\"\r\n          }\r\n        ],\r\n        1: [ // 客户服务群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:00:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"请问合同什么时候能完成？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，合同预计明天下午可以完成审核\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:10:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢！\"\r\n          }\r\n        ],\r\n        2: [ // 紧急处理群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 15:00:00\",\r\n            yuangong_id: 5,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李四\",\r\n            type: \"text\",\r\n            content: \"这个案件需要加急处理\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 15:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，我立即安排处理\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 15:10:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"我这边也会配合加急处理\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = groupMessages[_this.quliaoIndex] || [];\r\n      console.log('群聊消息加载完成:', _this.list.length);\r\n    },\r\n    // 加载私聊消息\r\n    loadPrivateMessages() {\r\n      const privateMessages = {\r\n        0: [ // 张三（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:30:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"您好，请问我的合同审核进度如何？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:35:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，您的合同正在审核中，预计今天下午可以完成\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:40:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢您！\"\r\n          }\r\n        ],\r\n        1: [ // 李四（律师）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"合同已经审核完毕，请查收\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:20:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，辛苦了！\"\r\n          }\r\n        ],\r\n        2: [ // 王五（调解员）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-21 16:00:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"王调解员\",\r\n            type: \"text\",\r\n            content: \"调解会议安排在明天下午2点\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-21 16:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"好的，我会准时参加\"\r\n          }\r\n        ],\r\n        3: [ // 赵六（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 16:45:00\",\r\n            yuangong_id: 6,\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n            title: \"赵六\",\r\n            type: \"text\",\r\n            content: \"谢谢您的帮助！\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 16:50:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"不客气，有问题随时联系我\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = privateMessages[_this.selectId] || [];\r\n      console.log('私聊消息加载完成:', _this.list.length);\r\n    },\r\n\r\n\r\n\r\n    // 新的群成员面板切换方法\r\n    toggleMemberPanel() {\r\n      console.log('切换群成员面板');\r\n      _this.showMemberPanel = !_this.showMemberPanel;\r\n      if (_this.showMemberPanel) {\r\n        _this.loadMemberData();\r\n      }\r\n    },\r\n\r\n    // 加载群成员数据\r\n    loadMemberData() {\r\n      console.log('加载群成员数据');\r\n      _this.memberData = {\r\n        users: [\r\n          {\r\n            id: 1,\r\n            name: \"张三\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 2,\r\n            name: \"李四\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 3,\r\n            name: \"王五\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          }\r\n        ],\r\n        lawyers: [\r\n          {\r\n            id: 4,\r\n            name: \"李律师\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 5,\r\n            name: \"陈律师\",\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          }\r\n        ],\r\n        staff: [\r\n          {\r\n            id: 6,\r\n            name: \"王调解员\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          },\r\n          {\r\n            id: 7,\r\n            name: \"张专员\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 8,\r\n            name: \"赵专员\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    // 加载用户详情数据\r\n    loadUserDetailData() {\r\n      // 根据当前选中的群聊加载对应用户信息\r\n      const currentQun = _this.quns[_this.quliaoIndex];\r\n      if (currentQun) {\r\n        _this.currentUserDetail = {\r\n          name: currentQun.title.replace(/群$/, '') + '用户',\r\n          type: '客户',\r\n          avatar: currentQun.pic_path,\r\n          phone: '138****8888',\r\n          idCard: '320***********1234',\r\n          registerTime: '2024-01-15 10:30:00',\r\n          lastLogin: '2024-01-22 09:15:00',\r\n          status: '正常',\r\n          debtors: [\r\n            {\r\n              id: 1,\r\n              name: '张三',\r\n              amount: '50000.00',\r\n              status: 'overdue',\r\n              statusText: '逾期'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '李四',\r\n              amount: '30000.00',\r\n              status: 'normal',\r\n              statusText: '正常'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '王五',\r\n              amount: '80000.00',\r\n              status: 'settled',\r\n              statusText: '已结清'\r\n            }\r\n          ],\r\n          documents: [\r\n            {\r\n              id: 1,\r\n              name: '身份证正面.jpg',\r\n              type: 'image',\r\n              size: '2.5MB',\r\n              uploadTime: '2024-01-15'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '营业执照.pdf',\r\n              type: 'pdf',\r\n              size: '1.8MB',\r\n              uploadTime: '2024-01-16'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '合同文件.docx',\r\n              type: 'word',\r\n              size: '856KB',\r\n              uploadTime: '2024-01-18'\r\n            }\r\n          ],\r\n          orders: [\r\n            {\r\n              id: 1,\r\n              title: '合同审核申请',\r\n              type: '法律咨询',\r\n              status: '已完成',\r\n              createTime: '2024-01-20'\r\n            },\r\n            {\r\n              id: 2,\r\n              title: '债务追讨服务',\r\n              type: '债务处理',\r\n              status: '处理中',\r\n              createTime: '2024-01-21'\r\n            },\r\n            {\r\n              id: 3,\r\n              title: '法律文书起草',\r\n              type: '文书服务',\r\n              status: '待处理',\r\n              createTime: '2024-01-22'\r\n            }\r\n          ],\r\n          payments: [\r\n            {\r\n              id: 1,\r\n              description: '法律咨询费用',\r\n              amount: '500.00',\r\n              status: '已支付',\r\n              time: '2024-01-20 14:30:00'\r\n            },\r\n            {\r\n              id: 2,\r\n              description: '合同审核费用',\r\n              amount: '800.00',\r\n              status: '已支付',\r\n              time: '2024-01-21 10:15:00'\r\n            },\r\n            {\r\n              id: 3,\r\n              description: '债务处理服务费',\r\n              amount: '1200.00',\r\n              status: '待支付',\r\n              time: '2024-01-22 09:00:00'\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    // 获取文档图标\r\n    getDocIcon(type) {\r\n      const iconMap = {\r\n        image: 'el-icon-picture',\r\n        pdf: 'el-icon-document',\r\n        word: 'el-icon-document',\r\n        excel: 'el-icon-s-grid',\r\n        default: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || iconMap.default;\r\n    },\r\n\r\n    // 获取工单状态类型\r\n    getOrderStatusType(status) {\r\n      const typeMap = {\r\n        '已完成': 'success',\r\n        '处理中': 'warning',\r\n        '待处理': 'info',\r\n        '已取消': 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 下载文档\r\n    downloadDoc(doc) {\r\n      console.log('下载文档:', doc.name);\r\n      _this.$message.success('开始下载: ' + doc.name);\r\n    },\r\n\r\n    // 预览文档\r\n    previewDoc(doc) {\r\n      console.log('预览文档:', doc.name);\r\n      _this.$message.info('预览功能开发中...');\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    // 使用测试数据替代API调用\r\n    _this.loadTestData();\r\n    _this.loadUserDetailData();\r\n    _this.yon_id = 1; // 设置当前用户ID\r\n    // 注释掉定时器，避免不必要的API调用\r\n    // _this.timer = setInterval(() => {\r\n    //   _this.getMoreList();\r\n    // }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 用户详情侧边栏 */\r\n.user-detail-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -600px;\r\n  width: 600px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  display: flex;\r\n  height: calc(100% - 80px);\r\n}\r\n\r\n.detail-menu {\r\n  width: 200px;\r\n  background: #f8f9fa;\r\n  border-right: 1px solid #e9ecef;\r\n  padding: 20px 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  color: #6c757d;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    color: #495057;\r\n  }\r\n\r\n  &.active {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n  }\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  span {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.detail-main {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.profile-avatar {\r\n  margin-right: 20px;\r\n\r\n  img {\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 3px solid #ffffff;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.profile-info {\r\n  h4 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .user-type {\r\n    margin: 0;\r\n    font-size: 14px;\r\n    color: #6c757d;\r\n    padding: 4px 12px;\r\n    background: #e9ecef;\r\n    border-radius: 20px;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.info-section {\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    label {\r\n      width: 120px;\r\n      font-weight: 500;\r\n      color: #495057;\r\n      margin: 0;\r\n    }\r\n\r\n    span {\r\n      color: #212529;\r\n    }\r\n  }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .count-badge {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n    padding: 4px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.debtors-list {\r\n  .debtor-card {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n      transform: translateY(-1px);\r\n    }\r\n  }\r\n\r\n  .debtor-info {\r\n    .debtor-name {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .debtor-details {\r\n      display: flex;\r\n      gap: 15px;\r\n\r\n      .debt-amount {\r\n        color: #dc3545;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .debt-status {\r\n        padding: 2px 8px;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n\r\n        &.overdue {\r\n          background: #f8d7da;\r\n          color: #721c24;\r\n        }\r\n\r\n        &.normal {\r\n          background: #d4edda;\r\n          color: #155724;\r\n        }\r\n\r\n        &.settled {\r\n          background: #cce7ff;\r\n          color: #004085;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.documents-list {\r\n  .document-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 8px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .doc-icon {\r\n    margin-right: 12px;\r\n    color: #6c757d;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .doc-info {\r\n    flex: 1;\r\n\r\n    .doc-name {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .doc-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .doc-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n.orders-list {\r\n  .order-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .order-info {\r\n    .order-title {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .order-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n\r\n      .order-type {\r\n        padding: 2px 8px;\r\n        background: #e9ecef;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.payments-list {\r\n  .payment-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .payment-info {\r\n    .payment-desc {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .payment-time {\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .payment-amount {\r\n    text-align: right;\r\n\r\n    .amount {\r\n      font-weight: 600;\r\n      color: #28a745;\r\n      margin-bottom: 4px;\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n.member-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 用户详情按钮样式 */\r\n.user-detail-btn {\r\n  color: #606266;\r\n  font-size: 16px;\r\n  margin-right: 8px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #409eff;\r\n    transform: scale(1.1);\r\n  }\r\n\r\n  &.active {\r\n    color: #409eff;\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n\r\n/* 手机端适配 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    flex-direction: column;\r\n    height: 100vh;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    max-height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    overflow-y: hidden;\r\n  }\r\n\r\n  .sidebar-header {\r\n    padding: 10px 15px;\r\n  }\r\n\r\n  .search-box {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .contact-list {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 10px;\r\n    padding: 0 15px 15px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 120px;\r\n    flex-shrink: 0;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .chat-main {\r\n    flex: 1;\r\n    height: calc(100vh - 200px);\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .chat-header {\r\n    padding: 10px 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .chat-actions {\r\n    gap: 5px;\r\n  }\r\n\r\n  .user-detail-btn,\r\n  .more-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-list {\r\n    flex: 1;\r\n    padding: 10px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .message-item {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 85%;\r\n  }\r\n\r\n  .message-avatar img {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n\r\n  .message-bubble {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .sender-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 15px;\r\n    border-top: 1px solid #e9ecef;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 3px;\r\n    margin-bottom: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-input {\r\n    ::v-deep .el-textarea__inner {\r\n      font-size: 14px;\r\n      padding: 8px 12px;\r\n    }\r\n  }\r\n\r\n  .send-btn {\r\n    padding: 8px 16px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 用户详情侧边栏手机端适配 */\r\n  .user-detail-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n    z-index: 1000;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .detail-header {\r\n    padding: 15px;\r\n    border-bottom: 1px solid #e9ecef;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-content {\r\n    flex-direction: column;\r\n    height: calc(100vh - 60px);\r\n  }\r\n\r\n  .detail-menu {\r\n    width: 100%;\r\n    height: auto;\r\n    flex-direction: row;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    padding: 0;\r\n  }\r\n\r\n  .menu-item {\r\n    min-width: 100px;\r\n    flex-shrink: 0;\r\n    padding: 12px 15px;\r\n    border-bottom: none;\r\n    border-right: 1px solid #e9ecef;\r\n\r\n    &:last-child {\r\n      border-right: none;\r\n    }\r\n\r\n    span {\r\n      font-size: 12px;\r\n    }\r\n\r\n    i {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-main {\r\n    flex: 1;\r\n    padding: 15px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .user-profile {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .profile-avatar img {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .profile-info h4 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .info-item {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .section-header {\r\n    margin-bottom: 15px;\r\n\r\n    h4 {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .debtor-card,\r\n  .document-item,\r\n  .order-item,\r\n  .payment-item {\r\n    padding: 12px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 群成员面板手机端适配 */\r\n  .member-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .member-header {\r\n    padding: 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .member-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));\r\n    gap: 10px;\r\n    padding: 15px;\r\n  }\r\n\r\n  .member-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n\r\n  .member-name {\r\n    font-size: 11px;\r\n  }\r\n\r\n  /* 表情面板手机端适配 */\r\n  .emoji-panel {\r\n    width: 280px !important;\r\n    height: 180px !important;\r\n    bottom: 45px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(6, 1fr) !important;\r\n    padding: 10px !important;\r\n  }\r\n\r\n  .emoji-item {\r\n    padding: 8px !important;\r\n    font-size: 18px !important;\r\n  }\r\n}\r\n\r\n/* 超小屏幕适配 (小于480px) */\r\n@media (max-width: 480px) {\r\n  .sidebar {\r\n    max-height: 150px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 100px;\r\n  }\r\n\r\n  .chat-main {\r\n    height: calc(100vh - 150px);\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 90%;\r\n  }\r\n\r\n  .message-bubble {\r\n    font-size: 13px;\r\n    padding: 6px 10px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 10px;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 2px;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .detail-menu {\r\n    .menu-item {\r\n      min-width: 80px;\r\n      padding: 10px 12px;\r\n\r\n      span {\r\n        font-size: 11px;\r\n      }\r\n\r\n      i {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .emoji-panel {\r\n    width: 250px !important;\r\n    height: 160px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(5, 1fr) !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}