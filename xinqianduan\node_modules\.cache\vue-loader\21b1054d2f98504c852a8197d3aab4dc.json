{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=template&id=ab8b1f14&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748446032426}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748336507383}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}