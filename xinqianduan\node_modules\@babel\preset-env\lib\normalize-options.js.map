{"version": 3, "names": ["_semver", "require", "_pluginsCompatData", "_moduleTransformations", "_options", "_helperValidatorOption", "_babel7Plugins", "corejs3Polyfills", "JSON", "parse", "readFileSync", "resolve", "v", "OptionValidator", "allPluginsList", "Object", "keys", "pluginsList", "modulePlugins", "moduleTransformations", "map", "m", "getValidIncludesAndExcludes", "type", "corejs", "set", "Set", "add", "babel7", "corejs2Polyfills", "Array", "from", "flatMap", "array", "fn", "prototype", "concat", "apply", "normalizePluginName", "plugin", "replace", "exports", "expandIncludesAndExcludes", "filterList", "length", "filterableItems", "invalidFilters", "selected<PERSON><PERSON><PERSON>", "filter", "re", "RegExp", "_", "push", "items", "item", "test", "invariant", "join", "checkDuplicateIncludeExcludes", "include", "exclude", "duplicates", "opt", "includes", "normalizeTargets", "targets", "isArray", "browsers", "assign", "validateModulesOption", "modulesOpt", "ModulesOption", "auto", "toString", "false", "validateUseBuiltInsOption", "builtInsOpt", "UseBuiltInsOption", "normalizeCoreJSOption", "useBuiltIns", "proposals", "rawVersion", "undefined", "console", "warn", "version", "Boolean", "semver", "coerce", "String", "major", "RangeError", "normalizeOptions", "opts", "validateTopLevelOptions", "TopLevelOptions", "validateBooleanOption", "loose", "spec", "bugfixes", "config<PERSON><PERSON>", "validateStringOption", "process", "cwd", "debug", "forceAllTransforms", "ignoreBrowserslistConfig", "modules", "shippedProposals", "browserslistEnv"], "sources": ["../src/normalize-options.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport corejs3Polyfills from \"core-js-compat/data.json\" with { type: \"json\" };\nimport { plugins as pluginsList } from \"./plugins-compat-data.ts\";\nimport moduleTransformations from \"./module-transformations.ts\";\nimport {\n  TopLevelOptions,\n  ModulesOption,\n  UseBuiltInsOption,\n} from \"./options.ts\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\n\n// TODO(Babel 8): Remove this\nimport babel7 from \"./polyfills/babel-7-plugins.cjs\";\n\nimport type {\n  BuiltInsOption,\n  CorejsOption,\n  ModuleOption,\n  Options,\n  PluginListOption,\n} from \"./types.ts\";\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nconst allPluginsList = Object.keys(pluginsList);\n\n// NOTE: Since module plugins are handled separately compared to other plugins (via the \"modules\" option) it\n// should only be possible to exclude and not include module plugins, otherwise it's possible that preset-env\n// will add a module plugin twice.\nconst modulePlugins = [\n  \"transform-dynamic-import\",\n  ...Object.keys(moduleTransformations).map(m => moduleTransformations[m]),\n];\n\nconst getValidIncludesAndExcludes = (\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) => {\n  const set = new Set(allPluginsList);\n  if (type === \"exclude\") modulePlugins.map(set.add, set);\n  if (corejs) {\n    if (!process.env.BABEL_8_BREAKING && corejs === 2) {\n      Object.keys(babel7.corejs2Polyfills).map(set.add, set);\n      set.add(\"web.timers\").add(\"web.immediate\").add(\"web.dom.iterable\");\n    } else {\n      Object.keys(corejs3Polyfills).map(set.add, set);\n    }\n  }\n  return Array.from(set);\n};\n\nfunction flatMap<T, U>(array: Array<T>, fn: (item: T) => Array<U>): Array<U> {\n  return Array.prototype.concat.apply([], array.map(fn));\n}\n\nexport const normalizePluginName = (plugin: string) =>\n  plugin.replace(/^(@babel\\/|babel-)(plugin-)?/, \"\");\n\nconst expandIncludesAndExcludes = (\n  filterList: PluginListOption = [],\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) => {\n  if (filterList.length === 0) return [];\n\n  const filterableItems = getValidIncludesAndExcludes(type, corejs);\n\n  const invalidFilters: PluginListOption = [];\n  const selectedPlugins = flatMap(filterList, filter => {\n    let re: RegExp;\n    if (typeof filter === \"string\") {\n      try {\n        re = new RegExp(`^${normalizePluginName(filter)}$`);\n      } catch (_) {\n        invalidFilters.push(filter);\n        return [];\n      }\n    } else {\n      re = filter;\n    }\n    const items = filterableItems.filter(item => {\n      return process.env.BABEL_8_BREAKING\n        ? re.test(item)\n        : re.test(item) ||\n            // For backwards compatibility, we also support matching against the\n            // proposal- name.\n            re.test(item.replace(/^transform-/, \"proposal-\"));\n    });\n    if (items.length === 0) invalidFilters.push(filter);\n    return items;\n  });\n\n  v.invariant(\n    invalidFilters.length === 0,\n    `The plugins/built-ins '${invalidFilters.join(\n      \", \",\n    )}' passed to the '${type}' option are not\n    valid. Please check data/[plugin-features|built-in-features].js in babel-preset-env`,\n  );\n\n  return selectedPlugins;\n};\n\nexport const checkDuplicateIncludeExcludes = (\n  include: Array<string> = [],\n  exclude: Array<string> = [],\n) => {\n  const duplicates = include.filter(opt => exclude.includes(opt));\n\n  v.invariant(\n    duplicates.length === 0,\n    `The plugins/built-ins '${duplicates.join(\n      \", \",\n    )}' were found in both the \"include\" and\n    \"exclude\" options.`,\n  );\n};\n\nconst normalizeTargets = (\n  targets: string | string[] | Options[\"targets\"],\n): Options[\"targets\"] => {\n  // TODO: Allow to use only query or strings as a targets from next breaking change.\n  if (typeof targets === \"string\" || Array.isArray(targets)) {\n    return { browsers: targets };\n  }\n  return { ...targets };\n};\n\nexport const validateModulesOption = (\n  modulesOpt: ModuleOption = ModulesOption.auto,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    ModulesOption[modulesOpt.toString()] || modulesOpt === ModulesOption.false,\n    `The 'modules' option must be one of \\n` +\n      ` - 'false' to indicate no module processing\\n` +\n      ` - a specific module type: 'commonjs', 'amd', 'umd', 'systemjs'` +\n      ` - 'auto' (default) which will automatically select 'false' if the current\\n` +\n      `   process is known to support ES module syntax, or \"commonjs\" otherwise\\n`,\n  );\n\n  return modulesOpt;\n};\n\nexport const validateUseBuiltInsOption = (\n  builtInsOpt: BuiltInsOption = false,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    UseBuiltInsOption[builtInsOpt.toString()] ||\n      builtInsOpt === UseBuiltInsOption.false,\n    `The 'useBuiltIns' option must be either\n    'false' (default) to indicate no polyfill,\n    '\"entry\"' to indicate replacing the entry polyfill, or\n    '\"usage\"' to import only used polyfills per file`,\n  );\n\n  return builtInsOpt;\n};\n\nexport type NormalizedCorejsOption = {\n  proposals: boolean;\n  version: SemVer | null | false;\n};\n\nexport function normalizeCoreJSOption(\n  corejs: CorejsOption | undefined | null,\n  useBuiltIns: BuiltInsOption,\n): NormalizedCorejsOption {\n  let proposals = false;\n  let rawVersion: false | string | number | undefined | null;\n\n  if (useBuiltIns && corejs === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"When using the `useBuiltIns` option you must specify\" +\n          ' the code-js version you are using, such as `\"corejs\": \"3.32.0\"`.',\n      );\n    } else {\n      rawVersion = 2;\n      console.warn(\n        \"\\nWARNING (@babel/preset-env): We noticed you're using the `useBuiltIns` option without declaring a \" +\n          `core-js version. Currently, we assume version 2.x when no version ` +\n          \"is passed. Since this default version will likely change in future \" +\n          \"versions of Babel, we recommend explicitly setting the core-js version \" +\n          \"you are using via the `corejs` option.\\n\" +\n          \"\\nYou should also be sure that the version you pass to the `corejs` \" +\n          \"option matches the version specified in your `package.json`'s \" +\n          \"`dependencies` section. If it doesn't, you need to run one of the \" +\n          \"following commands:\\n\\n\" +\n          \"  npm install --save core-js@2    npm install --save core-js@3\\n\" +\n          \"  yarn add core-js@2              yarn add core-js@3\\n\\n\" +\n          \"More info about useBuiltIns: https://babeljs.io/docs/en/babel-preset-env#usebuiltins\\n\" +\n          \"More info about core-js: https://babeljs.io/docs/en/babel-preset-env#corejs\",\n      );\n    }\n  } else if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs as false | string | number | undefined | null;\n  }\n\n  const version = rawVersion ? semver.coerce(String(rawVersion)) : false;\n\n  if (version) {\n    if (useBuiltIns) {\n      if (process.env.BABEL_8_BREAKING) {\n        if (version.major !== 3) {\n          throw new RangeError(\n            \"Invalid Option: The version passed to `corejs` is invalid. Currently, \" +\n              \"only core-js@3 is supported.\",\n          );\n        }\n\n        if (\n          typeof rawVersion !== \"string\" ||\n          !String(rawVersion).includes(\".\")\n        ) {\n          throw new Error(\n            'Invalid Option: The version passed to `corejs` is invalid. Please use string and specify the minor version, such as `\"3.33\"`.',\n          );\n        }\n      } else {\n        if (version.major < 2 || version.major > 3) {\n          throw new RangeError(\n            \"Invalid Option: The version passed to `corejs` is invalid. Currently, \" +\n              \"only core-js@2 and core-js@3 are supported.\",\n          );\n        }\n      }\n    } else {\n      console.warn(\n        \"\\nWARNING (@babel/preset-env): The `corejs` option only has an effect when the `useBuiltIns` option is not `false`\\n\",\n      );\n    }\n  }\n\n  return { version, proposals };\n}\n\nexport default function normalizeOptions(opts: Options) {\n  v.validateTopLevelOptions(opts, TopLevelOptions);\n\n  const useBuiltIns = validateUseBuiltInsOption(opts.useBuiltIns);\n\n  const corejs = normalizeCoreJSOption(opts.corejs, useBuiltIns);\n\n  const include = expandIncludesAndExcludes(\n    opts.include,\n    TopLevelOptions.include,\n    !!corejs.version && corejs.version.major,\n  );\n\n  const exclude = expandIncludesAndExcludes(\n    opts.exclude,\n    TopLevelOptions.exclude,\n    !!corejs.version && corejs.version.major,\n  );\n\n  checkDuplicateIncludeExcludes(include, exclude);\n\n  if (!process.env.BABEL_8_BREAKING) {\n    v.validateBooleanOption(\"loose\", opts.loose);\n    v.validateBooleanOption(\"spec\", opts.spec);\n  }\n\n  return {\n    bugfixes: v.validateBooleanOption(\n      TopLevelOptions.bugfixes,\n      opts.bugfixes,\n      process.env.BABEL_8_BREAKING ? true : false,\n    ),\n    configPath: v.validateStringOption(\n      TopLevelOptions.configPath,\n      opts.configPath,\n      process.cwd(),\n    ),\n    corejs,\n    debug: v.validateBooleanOption(TopLevelOptions.debug, opts.debug, false),\n    include,\n    exclude,\n    forceAllTransforms: v.validateBooleanOption(\n      TopLevelOptions.forceAllTransforms,\n      opts.forceAllTransforms,\n      false,\n    ),\n    ignoreBrowserslistConfig: v.validateBooleanOption(\n      TopLevelOptions.ignoreBrowserslistConfig,\n      opts.ignoreBrowserslistConfig,\n      false,\n    ),\n    modules: validateModulesOption(opts.modules),\n    shippedProposals: v.validateBooleanOption(\n      TopLevelOptions.shippedProposals,\n      opts.shippedProposals,\n      false,\n    ),\n    targets: normalizeTargets(opts.targets),\n    useBuiltIns: useBuiltIns,\n    browserslistEnv: v.validateStringOption(\n      TopLevelOptions.browserslistEnv,\n      opts.browserslistEnv,\n    ),\n  };\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,sBAAA,GAAAJ,OAAA;AAGA,IAAAK,cAAA,GAAAL,OAAA;AAAqD,MAX9CM,gBAAgB,GAAAC,IAAA,CAAAC,KAAA,CAAAR,OAAA,OAAAS,YAAA,CAAAT,OAAA,CAAAU,OAAA,CAAM,0BAA0B;AAqBvD,MAAMC,CAAC,GAAG,IAAIC,sCAAe,oBAAkB,CAAC;AAEhD,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACC,0BAAW,CAAC;AAK/C,MAAMC,aAAa,GAAG,CACpB,0BAA0B,EAC1B,GAAGH,MAAM,CAACC,IAAI,CAACG,8BAAqB,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIF,8BAAqB,CAACE,CAAC,CAAC,CAAC,CACzE;AAED,MAAMC,2BAA2B,GAAGA,CAClCC,IAA2B,EAC3BC,MAAsB,KACnB;EACH,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACZ,cAAc,CAAC;EACnC,IAAIS,IAAI,KAAK,SAAS,EAAEL,aAAa,CAACE,GAAG,CAACK,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;EACvD,IAAID,MAAM,EAAE;IACV,IAAqCA,MAAM,KAAK,CAAC,EAAE;MACjDT,MAAM,CAACC,IAAI,CAACY,cAAM,CAACC,gBAAgB,CAAC,CAACT,GAAG,CAACK,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;MACtDA,GAAG,CAACE,GAAG,CAAC,YAAY,CAAC,CAACA,GAAG,CAAC,eAAe,CAAC,CAACA,GAAG,CAAC,kBAAkB,CAAC;IACpE,CAAC,MAAM;MACLZ,MAAM,CAACC,IAAI,CAACT,gBAAgB,CAAC,CAACa,GAAG,CAACK,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;IACjD;EACF;EACA,OAAOK,KAAK,CAACC,IAAI,CAACN,GAAG,CAAC;AACxB,CAAC;AAED,SAASO,OAAOA,CAAOC,KAAe,EAAEC,EAAyB,EAAY;EAC3E,OAAOJ,KAAK,CAACK,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAEJ,KAAK,CAACb,GAAG,CAACc,EAAE,CAAC,CAAC;AACxD;AAEO,MAAMI,mBAAmB,GAAIC,MAAc,IAChDA,MAAM,CAACC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;AAACC,OAAA,CAAAH,mBAAA,GAAAA,mBAAA;AAErD,MAAMI,yBAAyB,GAAGA,CAChCC,UAA4B,GAAG,EAAE,EACjCpB,IAA2B,EAC3BC,MAAsB,KACnB;EACH,IAAImB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAEtC,MAAMC,eAAe,GAAGvB,2BAA2B,CAACC,IAAI,EAAEC,MAAM,CAAC;EAEjE,MAAMsB,cAAgC,GAAG,EAAE;EAC3C,MAAMC,eAAe,GAAGf,OAAO,CAACW,UAAU,EAAEK,MAAM,IAAI;IACpD,IAAIC,EAAU;IACd,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC9B,IAAI;QACFC,EAAE,GAAG,IAAIC,MAAM,CAAE,IAAGZ,mBAAmB,CAACU,MAAM,CAAE,GAAE,CAAC;MACrD,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVL,cAAc,CAACM,IAAI,CAACJ,MAAM,CAAC;QAC3B,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACLC,EAAE,GAAGD,MAAM;IACb;IACA,MAAMK,KAAK,GAAGR,eAAe,CAACG,MAAM,CAACM,IAAI,IAAI;MAC3C,OAEIL,EAAE,CAACM,IAAI,CAACD,IAAI,CAAC,IAGXL,EAAE,CAACM,IAAI,CAACD,IAAI,CAACd,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC,CAAC;IACF,IAAIa,KAAK,CAACT,MAAM,KAAK,CAAC,EAAEE,cAAc,CAACM,IAAI,CAACJ,MAAM,CAAC;IACnD,OAAOK,KAAK;EACd,CAAC,CAAC;EAEFzC,CAAC,CAAC4C,SAAS,CACTV,cAAc,CAACF,MAAM,KAAK,CAAC,EAC1B,0BAAyBE,cAAc,CAACW,IAAI,CAC3C,IACF,CAAE,oBAAmBlC,IAAK;AAC9B,wFACE,CAAC;EAED,OAAOwB,eAAe;AACxB,CAAC;AAEM,MAAMW,6BAA6B,GAAGA,CAC3CC,OAAsB,GAAG,EAAE,EAC3BC,OAAsB,GAAG,EAAE,KACxB;EACH,MAAMC,UAAU,GAAGF,OAAO,CAACX,MAAM,CAACc,GAAG,IAAIF,OAAO,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EAE/DlD,CAAC,CAAC4C,SAAS,CACTK,UAAU,CAACjB,MAAM,KAAK,CAAC,EACtB,0BAAyBiB,UAAU,CAACJ,IAAI,CACvC,IACF,CAAE;AACN,uBACE,CAAC;AACH,CAAC;AAAChB,OAAA,CAAAiB,6BAAA,GAAAA,6BAAA;AAEF,MAAMM,gBAAgB,GACpBC,OAA+C,IACxB;EAEvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAInC,KAAK,CAACoC,OAAO,CAACD,OAAO,CAAC,EAAE;IACzD,OAAO;MAAEE,QAAQ,EAAEF;IAAQ,CAAC;EAC9B;EACA,OAAAlD,MAAA,CAAAqD,MAAA,KAAYH,OAAO;AACrB,CAAC;AAEM,MAAMI,qBAAqB,GAAGA,CACnCC,UAAwB,GAAGC,sBAAa,CAACC,IAAI,KAC1C;EACH5D,CAAC,CAAC4C,SAAS,CAETe,sBAAa,CAACD,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC,IAAIH,UAAU,KAAKC,sBAAa,CAACG,KAAK,EACzE,wCAAuC,GACrC,+CAA8C,GAC9C,iEAAgE,GAChE,8EAA6E,GAC7E,4EACL,CAAC;EAED,OAAOJ,UAAU;AACnB,CAAC;AAAC7B,OAAA,CAAA4B,qBAAA,GAAAA,qBAAA;AAEK,MAAMM,yBAAyB,GAAGA,CACvCC,WAA2B,GAAG,KAAK,KAChC;EACHhE,CAAC,CAAC4C,SAAS,CAETqB,0BAAiB,CAACD,WAAW,CAACH,QAAQ,CAAC,CAAC,CAAC,IACvCG,WAAW,KAAKC,0BAAiB,CAACH,KAAK,EACxC;AACL;AACA;AACA,qDACE,CAAC;EAED,OAAOE,WAAW;AACpB,CAAC;AAACnC,OAAA,CAAAkC,yBAAA,GAAAA,yBAAA;AAOK,SAASG,qBAAqBA,CACnCtD,MAAuC,EACvCuD,WAA2B,EACH;EACxB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAsD;EAE1D,IAAIF,WAAW,IAAIvD,MAAM,KAAK0D,SAAS,EAAE;IAMhC;MACLD,UAAU,GAAG,CAAC;MACdE,OAAO,CAACC,IAAI,CACV,sGAAsG,GACnG,oEAAmE,GACpE,qEAAqE,GACrE,yEAAyE,GACzE,0CAA0C,GAC1C,sEAAsE,GACtE,gEAAgE,GAChE,oEAAoE,GACpE,yBAAyB,GACzB,kEAAkE,GAClE,0DAA0D,GAC1D,wFAAwF,GACxF,6EACJ,CAAC;IACH;EACF,CAAC,MAAM,IAAI,OAAO5D,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACxDyD,UAAU,GAAGzD,MAAM,CAAC6D,OAAO;IAC3BL,SAAS,GAAGM,OAAO,CAAC9D,MAAM,CAACwD,SAAS,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAGzD,MAAoD;EACnE;EAEA,MAAM6D,OAAO,GAAGJ,UAAU,GAAGM,OAAM,CAACC,MAAM,CAACC,MAAM,CAACR,UAAU,CAAC,CAAC,GAAG,KAAK;EAEtE,IAAII,OAAO,EAAE;IACX,IAAIN,WAAW,EAAE;MAiBR;QACL,IAAIM,OAAO,CAACK,KAAK,GAAG,CAAC,IAAIL,OAAO,CAACK,KAAK,GAAG,CAAC,EAAE;UAC1C,MAAM,IAAIC,UAAU,CAClB,wEAAwE,GACtE,6CACJ,CAAC;QACH;MACF;IACF,CAAC,MAAM;MACLR,OAAO,CAACC,IAAI,CACV,sHACF,CAAC;IACH;EACF;EAEA,OAAO;IAAEC,OAAO;IAAEL;EAAU,CAAC;AAC/B;AAEe,SAASY,gBAAgBA,CAACC,IAAa,EAAE;EACtDjF,CAAC,CAACkF,uBAAuB,CAACD,IAAI,EAAEE,wBAAe,CAAC;EAEhD,MAAMhB,WAAW,GAAGJ,yBAAyB,CAACkB,IAAI,CAACd,WAAW,CAAC;EAE/D,MAAMvD,MAAM,GAAGsD,qBAAqB,CAACe,IAAI,CAACrE,MAAM,EAAEuD,WAAW,CAAC;EAE9D,MAAMpB,OAAO,GAAGjB,yBAAyB,CACvCmD,IAAI,CAAClC,OAAO,EACZoC,wBAAe,CAACpC,OAAO,EACvB,CAAC,CAACnC,MAAM,CAAC6D,OAAO,IAAI7D,MAAM,CAAC6D,OAAO,CAACK,KACrC,CAAC;EAED,MAAM9B,OAAO,GAAGlB,yBAAyB,CACvCmD,IAAI,CAACjC,OAAO,EACZmC,wBAAe,CAACnC,OAAO,EACvB,CAAC,CAACpC,MAAM,CAAC6D,OAAO,IAAI7D,MAAM,CAAC6D,OAAO,CAACK,KACrC,CAAC;EAEDhC,6BAA6B,CAACC,OAAO,EAAEC,OAAO,CAAC;EAEZ;IACjChD,CAAC,CAACoF,qBAAqB,CAAC,OAAO,EAAEH,IAAI,CAACI,KAAK,CAAC;IAC5CrF,CAAC,CAACoF,qBAAqB,CAAC,MAAM,EAAEH,IAAI,CAACK,IAAI,CAAC;EAC5C;EAEA,OAAO;IACLC,QAAQ,EAAEvF,CAAC,CAACoF,qBAAqB,CAC/BD,wBAAe,CAACI,QAAQ,EACxBN,IAAI,CAACM,QAAQ,EACyB,KACxC,CAAC;IACDC,UAAU,EAAExF,CAAC,CAACyF,oBAAoB,CAChCN,wBAAe,CAACK,UAAU,EAC1BP,IAAI,CAACO,UAAU,EACfE,OAAO,CAACC,GAAG,CAAC,CACd,CAAC;IACD/E,MAAM;IACNgF,KAAK,EAAE5F,CAAC,CAACoF,qBAAqB,CAACD,wBAAe,CAACS,KAAK,EAAEX,IAAI,CAACW,KAAK,EAAE,KAAK,CAAC;IACxE7C,OAAO;IACPC,OAAO;IACP6C,kBAAkB,EAAE7F,CAAC,CAACoF,qBAAqB,CACzCD,wBAAe,CAACU,kBAAkB,EAClCZ,IAAI,CAACY,kBAAkB,EACvB,KACF,CAAC;IACDC,wBAAwB,EAAE9F,CAAC,CAACoF,qBAAqB,CAC/CD,wBAAe,CAACW,wBAAwB,EACxCb,IAAI,CAACa,wBAAwB,EAC7B,KACF,CAAC;IACDC,OAAO,EAAEtC,qBAAqB,CAACwB,IAAI,CAACc,OAAO,CAAC;IAC5CC,gBAAgB,EAAEhG,CAAC,CAACoF,qBAAqB,CACvCD,wBAAe,CAACa,gBAAgB,EAChCf,IAAI,CAACe,gBAAgB,EACrB,KACF,CAAC;IACD3C,OAAO,EAAED,gBAAgB,CAAC6B,IAAI,CAAC5B,OAAO,CAAC;IACvCc,WAAW,EAAEA,WAAW;IACxB8B,eAAe,EAAEjG,CAAC,CAACyF,oBAAoB,CACrCN,wBAAe,CAACc,eAAe,EAC/BhB,IAAI,CAACgB,eACP;EACF,CAAC;AACH", "ignoreList": []}