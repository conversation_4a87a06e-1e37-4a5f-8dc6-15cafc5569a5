--- %YAML:1.0
test: Some characters at the beginning of a string must be escaped
brief: >
    Some characters at the beginning of a string must be escaped
yaml: |
    foo: '| bar'
php: |
    array('foo' => '| bar')
---
test: A key can be a quoted string
brief: >
  A key can be a quoted string
yaml: |
    "foo1": bar
    'foo2': bar
    "foo \" bar": bar
    'foo '' bar': bar
    'foo3: ': bar
    "foo4: ": bar
    foo5: { "foo \" bar: ": bar, 'foo '' bar: ': bar }
php: |
    array(
      'foo1' => 'bar',
      'foo2' => 'bar',
      'foo " bar' => 'bar',
      'foo \' bar' => 'bar',
      'foo3: ' => 'bar',
      'foo4: ' => 'bar',
      'foo5' => array(
        'foo " bar: ' => 'bar',
        'foo \' bar: ' => 'bar',
      ),
    )
