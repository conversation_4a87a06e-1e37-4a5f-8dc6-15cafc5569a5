{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\profile\\index.vue", "mtime": 1748540171919}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/profile", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            个人信息\r\n          </h2>\r\n          <div class=\"page-subtitle\">管理您的个人资料和账户设置</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 个人信息卡片 -->\r\n      <div class=\"profile-section\">\r\n        <div class=\"profile-card\">\r\n          <!-- 头像区域 -->\r\n          <div class=\"avatar-section\">\r\n            <div class=\"avatar-container\">\r\n              <img \r\n                :src=\"userInfo.avatar || defaultAvatar\" \r\n                :alt=\"userInfo.name\"\r\n                class=\"avatar-image\"\r\n              />\r\n              <div class=\"avatar-overlay\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleAvatarSuccess\"\r\n                  :before-upload=\"beforeAvatarUpload\"\r\n                  class=\"avatar-uploader\"\r\n                >\r\n                  <i class=\"el-icon-camera avatar-uploader-icon\"></i>\r\n                </el-upload>\r\n              </div>\r\n            </div>\r\n            <div class=\"user-basic-info\">\r\n              <h3 class=\"user-name\">{{ userInfo.name || '管理员' }}</h3>\r\n              <p class=\"user-role\">{{ userInfo.role || '系统管理员' }}</p>\r\n              <p class=\"user-department\">{{ userInfo.department || '法律服务部' }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 信息表单 -->\r\n          <div class=\"form-section\">\r\n            <el-form \r\n              :model=\"userInfo\" \r\n              :rules=\"rules\" \r\n              ref=\"userForm\" \r\n              label-width=\"120px\"\r\n              class=\"profile-form\"\r\n            >\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"姓名\" prop=\"name\" class=\"form-item\">\r\n                  <el-input \r\n                    v-model=\"userInfo.name\" \r\n                    placeholder=\"请输入姓名\"\r\n                    :disabled=\"!editMode\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"工号\" prop=\"employee_id\" class=\"form-item\">\r\n                  <el-input \r\n                    v-model=\"userInfo.employee_id\" \r\n                    placeholder=\"请输入工号\"\r\n                    :disabled=\"!editMode\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"手机号\" prop=\"phone\" class=\"form-item\">\r\n                  <el-input \r\n                    v-model=\"userInfo.phone\" \r\n                    placeholder=\"请输入手机号\"\r\n                    :disabled=\"!editMode\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"邮箱\" prop=\"email\" class=\"form-item\">\r\n                  <el-input \r\n                    v-model=\"userInfo.email\" \r\n                    placeholder=\"请输入邮箱\"\r\n                    :disabled=\"!editMode\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <el-form-item label=\"部门\" prop=\"department\" class=\"form-item\">\r\n                  <el-select \r\n                    v-model=\"userInfo.department\" \r\n                    placeholder=\"请选择部门\"\r\n                    :disabled=\"!editMode\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"法律服务部\" value=\"法律服务部\"></el-option>\r\n                    <el-option label=\"客户服务部\" value=\"客户服务部\"></el-option>\r\n                    <el-option label=\"财务部\" value=\"财务部\"></el-option>\r\n                    <el-option label=\"人事部\" value=\"人事部\"></el-option>\r\n                    <el-option label=\"技术部\" value=\"技术部\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"职位\" prop=\"position\" class=\"form-item\">\r\n                  <el-input \r\n                    v-model=\"userInfo.position\" \r\n                    placeholder=\"请输入职位\"\r\n                    :disabled=\"!editMode\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <el-form-item label=\"个人简介\" prop=\"bio\">\r\n                <el-input \r\n                  v-model=\"userInfo.bio\" \r\n                  type=\"textarea\" \r\n                  :rows=\"4\"\r\n                  placeholder=\"请输入个人简介\"\r\n                  :disabled=\"!editMode\"\r\n                ></el-input>\r\n              </el-form-item>\r\n\r\n              <!-- 操作按钮 -->\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  v-if=\"!editMode\"\r\n                  type=\"primary\" \r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"enableEdit\"\r\n                >\r\n                  编辑资料\r\n                </el-button>\r\n                \r\n                <template v-else>\r\n                  <el-button \r\n                    type=\"primary\" \r\n                    icon=\"el-icon-check\"\r\n                    @click=\"saveProfile\"\r\n                    :loading=\"saving\"\r\n                  >\r\n                    保存修改\r\n                  </el-button>\r\n                  <el-button \r\n                    icon=\"el-icon-close\"\r\n                    @click=\"cancelEdit\"\r\n                  >\r\n                    取消\r\n                  </el-button>\r\n                </template>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 安全设置卡片 -->\r\n        <div class=\"security-card\">\r\n          <div class=\"card-header\">\r\n            <h3 class=\"card-title\">\r\n              <i class=\"el-icon-lock\"></i>\r\n              安全设置\r\n            </h3>\r\n          </div>\r\n          \r\n          <div class=\"security-items\">\r\n            <div class=\"security-item\">\r\n              <div class=\"security-info\">\r\n                <div class=\"security-title\">登录密码</div>\r\n                <div class=\"security-desc\">定期更换密码，保护账户安全</div>\r\n              </div>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"changePassword\"\r\n                class=\"security-action\"\r\n              >\r\n                修改密码\r\n              </el-button>\r\n            </div>\r\n            \r\n            <div class=\"security-item\">\r\n              <div class=\"security-info\">\r\n                <div class=\"security-title\">最后登录</div>\r\n                <div class=\"security-desc\">{{ lastLoginTime }}</div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"security-item\">\r\n              <div class=\"security-info\">\r\n                <div class=\"security-title\">登录IP</div>\r\n                <div class=\"security-desc\">{{ lastLoginIP }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Profile\",\r\n  data() {\r\n    return {\r\n      editMode: false,\r\n      saving: false,\r\n      defaultAvatar: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzk5OSI+5aS05YOP</dGV4dD48L3N2Zz4=\",\r\n      userInfo: {\r\n        name: \"管理员\",\r\n        employee_id: \"ADMIN001\",\r\n        phone: \"13800138000\",\r\n        email: \"<EMAIL>\",\r\n        department: \"法律服务部\",\r\n        position: \"系统管理员\",\r\n        role: \"系统管理员\",\r\n        bio: \"负责系统管理和维护工作，确保系统稳定运行。\",\r\n        avatar: \"\"\r\n      },\r\n      originalUserInfo: {},\r\n      lastLoginTime: \"2024-01-15 09:30:25\",\r\n      lastLoginIP: \"*************\",\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"请输入姓名\", trigger: \"blur\" }\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"请输入手机号\", trigger: \"blur\" },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\r\n          { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.loadUserInfo();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      // 这里可以从后端加载用户信息\r\n      // 目前使用默认数据\r\n      this.originalUserInfo = { ...this.userInfo };\r\n    },\r\n    enableEdit() {\r\n      this.editMode = true;\r\n      this.originalUserInfo = { ...this.userInfo };\r\n    },\r\n    cancelEdit() {\r\n      this.editMode = false;\r\n      this.userInfo = { ...this.originalUserInfo };\r\n    },\r\n    saveProfile() {\r\n      this.$refs.userForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          // 模拟保存过程\r\n          setTimeout(() => {\r\n            this.saving = false;\r\n            this.editMode = false;\r\n            this.$message.success(\"个人信息保存成功！\");\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    changePassword() {\r\n      this.$router.push(\"/changePwd\");\r\n    },\r\n    handleAvatarSuccess(res) {\r\n      if (res && res.data && res.data.url) {\r\n        this.userInfo.avatar = res.data.url;\r\n        this.$message.success(\"头像上传成功！\");\r\n      }\r\n    },\r\n    beforeAvatarUpload(file) {\r\n      const isImage = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      const isLt2M = file.size / 1024 / 1024 < 2;\r\n\r\n      if (!isImage) {\r\n        this.$message.error(\"上传头像图片只能是 JPG/PNG 格式!\");\r\n        return false;\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error(\"上传头像图片大小不能超过 2MB!\");\r\n        return false;\r\n      }\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0 0 8px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #1890ff;\r\n  font-size: 22px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin: 0;\r\n}\r\n\r\n/* 个人信息区域 */\r\n.profile-section {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 24px;\r\n}\r\n\r\n/* 个人信息卡片 */\r\n.profile-card {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 头像区域 */\r\n.avatar-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 24px;\r\n  color: white;\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100px;\r\n  height: 100px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 4px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.avatar-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.avatar-container:hover .avatar-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.avatar-uploader {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.user-basic-info {\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.user-role {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  margin: 0 0 4px 0;\r\n}\r\n\r\n.user-department {\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  margin: 0;\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  padding: 32px;\r\n}\r\n\r\n.profile-form {\r\n  max-width: 600px;\r\n}\r\n\r\n.form-row {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 24px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  margin-top: 32px;\r\n  padding-top: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 安全设置卡片 */\r\n.security-card {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  height: fit-content;\r\n}\r\n\r\n.card-header {\r\n  padding: 24px 24px 16px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-title i {\r\n  color: #1890ff;\r\n}\r\n\r\n.security-items {\r\n  padding: 16px 24px 24px 24px;\r\n}\r\n\r\n.security-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.security-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.security-info {\r\n  flex: 1;\r\n}\r\n\r\n.security-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.security-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n}\r\n\r\n.security-action {\r\n  color: #1890ff;\r\n  font-size: 14px;\r\n}\r\n\r\n.security-action:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .profile-section {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .avatar-section {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n\r\n  .form-row {\r\n    grid-template-columns: 1fr;\r\n    gap: 0;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 24px 16px;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 表单样式优化 */\r\n.profile-form ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.profile-form ::v-deep .el-input__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.profile-form ::v-deep .el-input__inner:focus {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.profile-form ::v-deep .el-textarea__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.profile-form ::v-deep .el-select {\r\n  width: 100%;\r\n}\r\n\r\n/* 禁用状态样式 */\r\n.profile-form ::v-deep .el-input.is-disabled .el-input__inner {\r\n  background-color: #f5f5f5;\r\n  border-color: #e4e7ed;\r\n  color: #606266;\r\n}\r\n</style>\r\n"]}]}