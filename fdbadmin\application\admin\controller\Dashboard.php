<?php
namespace app\admin\controller;

use think\Request;
use untils\JsonService;
use models\{Users, Debts, Dingdans, Yuangongs, Quns};
use think\Db;

/**
 * 仪表板控制器
 * 提供首页统计数据和活动信息
 */
class Dashboard extends Base
{
    /**
     * 获取仪表板统计数据
     * @return \think\response\Json
     */
    public function getStats()
    {
        try {
            // 获取基础统计数据
            $totalUsers = Users::count();
            $totalDebts = Debts::count();
            $totalOrders = Dingdans::count();

            // 计算总收入（已支付订单）
            $totalRevenue = Dingdans::where('is_pay', 2)->sum('total_price') ?: 0;
            
            // 今日新增数据
            $today = date('Y-m-d');
            $todayStart = strtotime($today . ' 00:00:00');
            $todayEnd = strtotime($today . ' 23:59:59');
            
            $todayUsers = Users::where('create_time', 'between', [$todayStart, $todayEnd])->count();
            $todayOrders = Dingdans::where('create_time', 'between', [$todayStart, $todayEnd])->count();
            $todayDebts = Debts::where('ctime', 'between', [$todayStart, $todayEnd])->count();

            // 本月数据
            $monthStart = strtotime(date('Y-m-01 00:00:00'));
            $monthEnd = strtotime(date('Y-m-t 23:59:59'));

            $monthUsers = Users::where('create_time', 'between', [$monthStart, $monthEnd])->count();
            $monthOrders = Dingdans::where('create_time', 'between', [$monthStart, $monthEnd])->count();
            $monthRevenue = Dingdans::where('is_pay', 2)
                ->where('create_time', 'between', [$monthStart, $monthEnd])
                ->sum('total_price') ?: 0;
            
            // 债务状态统计
            $pendingDebts = Debts::where('status', 1)->count(); // 待处理
            $processingDebts = Debts::where('status', 2)->count(); // 调解中
            $litigationDebts = Debts::where('status', 3)->count(); // 诉讼中
            $closedDebts = Debts::where('status', 4)->count(); // 已结案
            
            // 订单状态统计
            $pendingOrders = Dingdans::where('is_pay', 1)->count(); // 未支付
            $paidOrders = Dingdans::where('is_pay', 2)->count(); // 已支付
            $refundOrders = Dingdans::where('is_pay', 3)->count(); // 退款

            // 债务金额统计
            $totalDebtAmount = Debts::sum('money') ?: 0;
            $totalBackAmount = Debts::sum('back_money') ?: 0;
            $remainingAmount = $totalDebtAmount - $totalBackAmount;
            
            $stats = [
                // 基础统计
                'totalUsers' => $totalUsers,
                'totalDebts' => $totalDebts,
                'totalOrders' => $totalOrders,
                'totalRevenue' => number_format($totalRevenue, 2),
                
                // 今日数据
                'todayUsers' => $todayUsers,
                'todayOrders' => $todayOrders,
                'todayDebts' => $todayDebts,
                
                // 本月数据
                'monthUsers' => $monthUsers,
                'monthOrders' => $monthOrders,
                'monthRevenue' => number_format($monthRevenue, 2),
                
                // 债务状态统计
                'pendingDebts' => $pendingDebts,
                'processingDebts' => $processingDebts,
                'litigationDebts' => $litigationDebts,
                'closedDebts' => $closedDebts,
                
                // 订单状态统计
                'pendingOrders' => $pendingOrders,
                'paidOrders' => $paidOrders,
                'refundOrders' => $refundOrders,
                
                // 债务金额统计
                'totalDebtAmount' => number_format($totalDebtAmount, 2),
                'totalBackAmount' => number_format($totalBackAmount, 2),
                'remainingAmount' => number_format($remainingAmount, 2),
                
                // 回款率
                'recoveryRate' => $totalDebtAmount > 0 ? round(($totalBackAmount / $totalDebtAmount) * 100, 2) : 0
            ];
            
            return JsonService::successful('获取成功', $stats);

        } catch (\Exception $e) {
            return JsonService::fail('获取统计数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取最近活动
     * @return \think\response\Json
     */
    public function getActivities()
    {
        try {
            $activities = [];

            // 最近用户注册（最近5个）
            $recentUsers = Users::order('create_time desc')->limit(5)->select();
            foreach ($recentUsers as $user) {
                $activities[] = [
                    'id' => 'user_' . $user->id,
                    'type' => 'user_register',
                    'icon' => 'el-icon-user-solid',
                    'color' => '#409EFF',
                    'title' => '新用户注册',
                    'description' => ($user->nickname ?: '新用户') . ' 注册了账户',
                    'time' => $this->timeAgo($user->create_time),
                    'timestamp' => $user->create_time
                ];
            }
            
            // 最近订单（最近5个）
            $recentOrders = Dingdans::order('create_time desc')->limit(5)->select();
            foreach ($recentOrders as $order) {
                $activities[] = [
                    'id' => 'order_' . $order->id,
                    'type' => 'order_create',
                    'icon' => 'el-icon-shopping-cart-2',
                    'color' => '#67C23A',
                    'title' => '新订单创建',
                    'description' => '订单 ' . $order->order_sn . ' 已创建，金额：¥' . $order->total_price,
                    'time' => $this->timeAgo($order->create_time),
                    'timestamp' => $order->create_time
                ];
            }
            
            // 最近债务提交（最近5个）
            $recentDebts = Debts::order('ctime desc')->limit(5)->select();
            foreach ($recentDebts as $debt) {
                $activities[] = [
                    'id' => 'debt_' . $debt->id,
                    'type' => 'debt_submit',
                    'icon' => 'el-icon-document',
                    'color' => '#E6A23C',
                    'title' => '债务提交',
                    'description' => '债务人 ' . $debt->name . ' 的债务已提交，金额：¥' . number_format($debt->money, 2),
                    'time' => $this->timeAgo($debt->ctime),
                    'timestamp' => $debt->ctime
                ];
            }
            
            // 按时间排序
            usort($activities, function($a, $b) {
                return $b['timestamp'] - $a['timestamp'];
            });
            
            // 只返回最近10条
            $activities = array_slice($activities, 0, 10);
            
            // 移除timestamp字段
            foreach ($activities as &$activity) {
                unset($activity['timestamp']);
            }
            
            return JsonService::successful('获取成功', $activities);

        } catch (\Exception $e) {
            return JsonService::fail('获取活动数据失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取待办事项
     * @return \think\response\Json
     */
    public function getTodos()
    {
        try {
            $todos = [];
            
            // 待处理债务
            $pendingDebts = Debts::where('status', 1)->limit(5)->select();
            foreach ($pendingDebts as $debt) {
                $todos[] = [
                    'id' => 'debt_' . $debt->id,
                    'type' => 'debt',
                    'title' => '处理债务：' . $debt->name,
                    'description' => '债务金额：¥' . number_format($debt->money, 2),
                    'priority' => 'high',
                    'completed' => false,
                    'url' => '/debt/detail/' . $debt->id
                ];
            }
            
            // 未支付订单
            $unpaidOrders = Dingdans::where('is_pay', 1)->limit(5)->select();
            foreach ($unpaidOrders as $order) {
                $todos[] = [
                    'id' => 'order_' . $order->id,
                    'type' => 'order',
                    'title' => '跟进订单：' . $order->order_sn,
                    'description' => '订单金额：¥' . $order->total_price,
                    'priority' => 'medium',
                    'completed' => false,
                    'url' => '/order/detail/' . $order->id
                ];
            }
            
            // 即将到期的用户
            $expiringSoon = Users::where('end_time', '>', 0)
                ->where('end_time', '<', strtotime('+7 days'))
                ->limit(5)
                ->select();
            foreach ($expiringSoon as $user) {
                $todos[] = [
                    'id' => 'expire_' . $user->id,
                    'type' => 'expire',
                    'title' => '用户即将到期：' . ($user->nickname ?: $user->phone),
                    'description' => '到期时间：' . date('Y-m-d', $user->end_time),
                    'priority' => 'medium',
                    'completed' => false,
                    'url' => '/user/detail/' . $user->id
                ];
            }
            
            return JsonService::successful('获取成功', $todos);

        } catch (\Exception $e) {
            return JsonService::fail('获取待办事项失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取图表数据
     * @return \think\response\Json
     */
    public function getChartData()
    {
        try {
            // 最近7天的数据
            $days = [];
            $userCounts = [];
            $orderCounts = [];
            $debtCounts = [];

            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $dayStart = strtotime($date . ' 00:00:00');
                $dayEnd = strtotime($date . ' 23:59:59');

                $days[] = date('m-d', strtotime($date));
                $userCounts[] = Users::where('create_time', 'between', [$dayStart, $dayEnd])->count();
                $orderCounts[] = Dingdans::where('create_time', 'between', [$dayStart, $dayEnd])->count();
                $debtCounts[] = Debts::where('ctime', 'between', [$dayStart, $dayEnd])->count();
            }

            // 债务状态分布
            $debtStatusData = [
                ['name' => '待处理', 'value' => Debts::where('status', 1)->count()],
                ['name' => '调解中', 'value' => Debts::where('status', 2)->count()],
                ['name' => '诉讼中', 'value' => Debts::where('status', 3)->count()],
                ['name' => '已结案', 'value' => Debts::where('status', 4)->count()],
                ['name' => '已取消', 'value' => Debts::where('status', 5)->count()]
            ];

            // 订单类型分布
            $orderTypeData = [
                ['name' => 'VIP服务', 'value' => Dingdans::where('type', 1)->count()],
                ['name' => '合同购买', 'value' => Dingdans::where('type', 2)->count()],
                ['name' => '单案服务', 'value' => Dingdans::where('type', 3)->count()],
                ['name' => '其他服务', 'value' => Dingdans::where('type', 5)->count()]
            ];

            // 最近12个月收入趋势
            $monthlyRevenue = [];
            $months = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-{$i} months"));
                $monthStart = strtotime($month . '-01 00:00:00');
                $monthEnd = strtotime(date('Y-m-t 23:59:59', $monthStart));

                $months[] = date('Y-m', strtotime($month));
                $revenue = Dingdans::where('is_pay', 2)
                    ->where('create_time', 'between', [$monthStart, $monthEnd])
                    ->sum('total_price') ?: 0;
                $monthlyRevenue[] = floatval($revenue);
            }

            $chartData = [
                'dailyTrend' => [
                    'days' => $days,
                    'users' => $userCounts,
                    'orders' => $orderCounts,
                    'debts' => $debtCounts
                ],
                'debtStatus' => $debtStatusData,
                'orderType' => $orderTypeData,
                'monthlyRevenue' => [
                    'months' => $months,
                    'revenue' => $monthlyRevenue
                ]
            ];

            return JsonService::successful('获取成功', $chartData);

        } catch (\Exception $e) {
            return JsonService::fail('获取图表数据失败：' . $e->getMessage());
        }
    }

    /**
     * 更新待办事项状态
     * @param Request $request
     * @return \think\response\Json
     */
    public function updateTodo(Request $request)
    {
        try {
            $id = $request->post('id');
            $completed = $request->post('completed', false);

            // 这里可以根据需要实现具体的待办事项状态更新逻辑
            // 目前只是返回成功，实际项目中可能需要存储到数据库

            return JsonService::successful('更新成功');

        } catch (\Exception $e) {
            return JsonService::fail('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取系统通知
     * @return \think\response\Json
     */
    public function getNotifications()
    {
        try {
            // 这里可以从数据库获取真实的系统通知
            // 目前返回一些示例数据
            $notifications = [
                [
                    'id' => 1,
                    'title' => '系统维护通知',
                    'content' => '系统将于今晚进行维护升级',
                    'time' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                    'read' => false,
                    'type' => 'system'
                ],
                [
                    'id' => 2,
                    'title' => '新版本更新',
                    'content' => '系统已更新到最新版本',
                    'time' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'read' => false,
                    'type' => 'update'
                ],
                [
                    'id' => 3,
                    'title' => '数据备份完成',
                    'content' => '系统数据备份已成功完成',
                    'time' => date('Y-m-d H:i:s', strtotime('-1 day -6 hours')),
                    'read' => true,
                    'type' => 'backup'
                ]
            ];

            return JsonService::successful('获取成功', $notifications);

        } catch (\Exception $e) {
            return JsonService::fail('获取通知失败：' . $e->getMessage());
        }
    }

    /**
     * 标记通知为已读
     * @param Request $request
     * @return \think\response\Json
     */
    public function markNotificationRead(Request $request)
    {
        try {
            $id = $request->post('id');

            // 这里可以更新数据库中的通知状态
            // 目前只是返回成功

            return JsonService::successful('标记成功');

        } catch (\Exception $e) {
            return JsonService::fail('标记失败：' . $e->getMessage());
        }
    }

    /**
     * 获取快捷操作数据
     * @return \think\response\Json
     */
    public function getQuickActions()
    {
        try {
            $actions = [
                [
                    'title' => '添加用户',
                    'icon' => 'el-icon-user-solid',
                    'color' => '#409EFF',
                    'url' => '/user/add',
                    'count' => Users::count()
                ],
                [
                    'title' => '新增债务',
                    'icon' => 'el-icon-document',
                    'color' => '#E6A23C',
                    'url' => '/debt/add',
                    'count' => Debts::where('status', 1)->count()
                ],
                [
                    'title' => '订单管理',
                    'icon' => 'el-icon-shopping-cart-2',
                    'color' => '#67C23A',
                    'url' => '/order/list',
                    'count' => Dingdans::where('is_pay', 1)->count()
                ],
                [
                    'title' => '聊天管理',
                    'icon' => 'el-icon-chat-dot-round',
                    'color' => '#F56C6C',
                    'url' => '/chat/list',
                    'count' => Quns::count()
                ]
            ];

            return JsonService::successful('获取成功', $actions);

        } catch (\Exception $e) {
            return JsonService::fail('获取快捷操作失败：' . $e->getMessage());
        }
    }

    /**
     * 时间格式化
     * @param int $timestamp
     * @return string
     */
    private function timeAgo($timestamp)
    {
        if (!$timestamp) return '未知时间';

        $time = time() - $timestamp;

        if ($time < 60) {
            return '刚刚';
        } elseif ($time < 3600) {
            return floor($time / 60) . '分钟前';
        } elseif ($time < 86400) {
            return floor($time / 3600) . '小时前';
        } elseif ($time < 2592000) {
            return floor($time / 86400) . '天前';
        } else {
            return date('Y-m-d', $timestamp);
        }
    }
}
