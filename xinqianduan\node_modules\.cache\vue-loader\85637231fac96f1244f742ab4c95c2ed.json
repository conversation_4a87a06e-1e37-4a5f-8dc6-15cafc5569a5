{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=style&index=0&id=ab8b1f14&lang=scss", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748425644041}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1748425641927}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings": ";;AAqs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file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"body-div\" @click=\"isEmji = false\">\r\n    <div class=\"content\">\r\n      <div class=\"msglist\">\r\n        <!-- 搜索 -->\r\n        <div class=\"wrapper\">\r\n          <div class=\"search-wrapper\">\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"searchInput\"\r\n              placeholder=\"搜索\"\r\n              @change=\"changeKeyword\"\r\n            />\r\n            <div\r\n              v-if=\"isShowSeach\"\r\n              class=\"searchInput-delete\"\r\n              @click=\"del\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <el-tag @click=\"showDaiban('2')\">群聊</el-tag>\r\n        <el-tag type=\"success\" @click=\"showDaiban('1')\">代办</el-tag>\r\n\r\n        <!-- 好友列表 -->\r\n        <ul class=\"msg-left-box\">\r\n          <!--工作群-->\r\n          <li\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === quliaoIndex }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"38\" height=\"38\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.create_time }}</span>\r\n              <p class=\"lastmsg\">{{ item.desc }}</p>\r\n              <p class=\"number-badge\" v-if=\"item.count > 0\">{{ item.count }}</p>\r\n            </div>\r\n          </li>\r\n          <li\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === selectId }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"42\" height=\"42\" :src=\"item.pic_path\" />\r\n              <span>99</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.time }}</span>\r\n              <p class=\"lastmsg\">{{ item.content }}</p>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"chatbox\">\r\n        <!-- v-loading=\"listLoading\" -->\r\n        <div class=\"message\">\r\n          <header class=\"header\">\r\n            <div class=\"friendname\" v-if=\"true\">\r\n              <!-- {{ lists.user.name }} -->\r\n              {{ title }}\r\n            </div>\r\n          </header>\r\n        </div>\r\n        <!-- 聊天框 -->\r\n        <div ref=\"list\" class=\"message-wrapper\" @scroll=\"handleScroll()\">\r\n          <div class=\"msg-box\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <div class=\"msg-time\">\r\n              <span>{{ item.create_time }}</span>\r\n            </div>\r\n            <div\r\n              :class=\"['chatMsg-box', { oneself: item.yuangong_id == yon_id }]\"\r\n            >\r\n              <div class=\"chat-name\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div :class=\"['chatMsg-flex']\">\r\n                <div class=\"flex-view\">\r\n                  <img :src=\"item.avatar\" />\r\n                  <!-- <span>{{ item.title }}</span> -->\r\n                </div>\r\n                <div class=\"flex-view\">\r\n                  <!-- 图片 -->\r\n                  <div class=\"chatMsg-img\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'voice'\">\r\n\t\t\t\t\t  <div class=\"\" style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t  \t<audioplay :recordFile=\"item.content\"></audioplay>\r\n\t\t\t\t\t  \t<div>{{ item.datas }}</div>\r\n\t\t\t\t\t  </div>\r\n                   \r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <!-- 文件 -->\r\n                  <div class=\"file-box\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-flex\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-name\">{{ item.files.name }}</div>\r\n                      <div class=\"file-size\">{{ item.files.size }}</div>\r\n                    </div>\r\n                    <div class=\"file-flex\">\r\n                      <img src=\"img/wenjian.png\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t<div style=\"position: absolute; top: 14px;right: 20px; font-size: 32px;\r\n    cursor: pointer;\" @click=\"quanyuan\">···</div>\r\n\t\t<div class=\"\" style=\"position: absolute;      overflow-y: auto;  width: 200px;\r\n    background: #f2f2f2;\r\n    height: 690px;right:-200px;top: 0px;\" v-if=\"la==true\">\r\n\t\t<div class=\"\">\r\n\t\t\t<div class=\"chat-list-box\" >\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t用户\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\"> \r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(item, index) in userss\" :key=\"index\">\r\n\t\t\t\t\t\t<div v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<img :src=\"value.headimg\"/>\r\n\t\t\t\t\t\t\t<div class=\"sl\">{{value.nickname}}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\r\n\t\t\t<div class=\"chat-list-box\" v-for=\"(item,index) in yuangongss\" :key=\"index\">\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t{{item.zhiwei}}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\">\r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t<img :src=\"value.pic_path\" />\r\n\t\t\t\t\t\t<div class=\"sl\">{{value.title}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\r\n\t</div>\r\n        <!-- 输入框 -->\r\n        <div class=\"input-box\">\r\n          <div class=\"workbar-box\">\r\n            <div class=\"upload-emji\">\r\n              <img src=\"img/biaoqing.png\" alt=\"\" @click.stop=\"openEmji\" />\r\n              <div class=\"emji-box\" v-show=\"isEmji\">\r\n                <div class=\"biao-box\">\r\n                  <div\r\n                    class=\"biao-flex\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"upload-file\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                <img src=\"img/insert_img.png\" alt=\"\"\r\n              /></el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"changeFile('image')\">\r\n              <!-- <input\r\n                type=\"file\"\r\n                title=\"选择文件发送\"\r\n                autocomplete=\"off\"\r\n                accept=\"application/*\"\r\n              /> -->\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <img src=\"img/wenjian.png\" alt=\"\" />\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"daiban\">\r\n              <img src=\"img/daiban.png\" alt=\"\" />\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"showgongdan\">\r\n              <img src=\"img/gongdan.png\" alt=\"\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"input-text\">\r\n            <textarea\r\n              placeholder=\"您想说什么？\"\r\n              v-model=\"textContent\"\r\n            ></textarea>\r\n          </div>\r\n          <div class=\"input-btn\">\r\n            <span @click=\"send\">发送Enter</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"img-popup\" :class=\"{ 'show-popup': isShowPopup }\">\r\n      <div>\r\n        <div class=\"img-div\">\r\n          <img :src=\"imgUlr\" alt=\"\" />\r\n        </div>\r\n        <div class=\"close\">\r\n          <span @click=\"isShowPopup = false\">×</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n\t\tuserss:[],\r\n\t\tlvshiss:[],\r\n\t\tyuangongss:[],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      // 得知当前选择的是哪个对话\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n\t  id:0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      //聊天记录\r\n      lists: [],\r\n\t  la:false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\tquanyuan(){\r\n\t\t_this.la =!_this.la\r\n\t\t_this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n\t\t  if (resp.code == 200) {\r\n\t\t    _this.userss = resp.data.users\r\n\t\t    _this.lvshiss = resp.data.lvshis\r\n\t\t    _this.yuangongss = resp.data.yuangongs\r\n\t\t\t\r\n\t\t  }\r\n\t\t});\r\n\t},\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n\t\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target._value;\r\n\r\n      _this.quns = quns.filter((data) => data.title.search(search) != -1);\r\n      _this.users = users.filter(\r\n        (data) =>\r\n          !search || data.title.toLowerCase().includes(search.toLowerCase())\r\n      );\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    //查看图片\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n\t\t !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n\t_this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n\t  _this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    //发送\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n      //   _this.getList();\r\n      /* setTimeout(\r\n         () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n         0*/\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\t\t\r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n\t\t\t_this.id = id;\r\n\tconsole.log(_this.id)\r\n\t\t\t\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n\t\t\t\r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  //    _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n\t\t\tid = 1;\r\n\t\t\t_this\r\n\t\t\t  .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n\t\t\t  .then((resp) => {\r\n\t\t\t    _this.getQun1();\r\n\t\t\t    if (resp.code == 200) {\r\n\t\t\t      _this.list.push(resp.data);\r\n\t\t\t\r\n\t\t\t      setTimeout(\r\n\t\t\t        () =>\r\n\t\t\t          (_this.$refs.list.scrollTop =\r\n\t\t\t            _this.$refs.list.scrollHeight),\r\n\t\t\t        1000\r\n\t\t\t      );\r\n\t\t\t    }\r\n\t\t\t    _this.loading = false;\r\n\t\t\t  });\r\n\t\t}\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        //  let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            //  uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        //      let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n\t\t\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        //f7 118 f8 119 f10 120\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n\r\n    //默认获取列表第一个\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t\r\n\t.chat-list-box{\r\n\t\tmargin-bottom: 10px;\r\n\t\t.chat-list-title{\r\n\t\t\tbackground: #ededed;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tpadding: 10px 5px;\r\n\t\t}\r\n\t\t.chat-list{\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\twidth: 100%;\r\n\t\t\t\r\n\t\t\t.chat-flex{\r\n\t\t\t\tpadding: 0px 8px;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 30px;\r\n\t\t\t\timg{\r\n\t\t\t\t\twidth: 45px;\r\n\t\t\t\t\theight: 45px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t}\r\n\t\t\t\t.sl{\r\n\t\t\t\t\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tmax-width:100px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n.chat-name {\r\n  padding-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #333333;\r\n  text-align: left;\r\n}\r\n.oneself {\r\n  .chat-name {\r\n    text-align: right !important;\r\n  }\r\n}\r\nhtml,\r\nbody,\r\ndiv,\r\nspan,\r\napplet,\r\nobject,\r\niframe,\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\np,\r\nblockquote,\r\npre,\r\na,\r\nabbr,\r\nacronym,\r\naddress,\r\nbig,\r\ncite,\r\ncode,\r\ndel,\r\ndfn,\r\nem,\r\nimg,\r\nins,\r\nkbd,\r\nq,\r\ns,\r\nsamp,\r\nsmall,\r\nstrike,\r\nstrong,\r\nsub,\r\nsup,\r\ntt,\r\nvar,\r\nb,\r\nu,\r\ni,\r\ncenter,\r\ndl,\r\ndt,\r\ndd,\r\nol,\r\nul,\r\nli,\r\nfieldset,\r\nform,\r\nlabel,\r\nlegend,\r\ntable,\r\ncaption,\r\ntbody,\r\ntfoot,\r\nthead,\r\ntr,\r\nth,\r\ntd,\r\narticle,\r\naside,\r\ncanvas,\r\ndetails,\r\nembed,\r\nfigure,\r\nfigcaption,\r\nfooter,\r\nheader,\r\nmenu,\r\nnav,\r\noutput,\r\nruby,\r\nsection,\r\nsummary,\r\ntime,\r\nmark,\r\naudio,\r\nvideo,\r\ninput {\r\n  margin: 0;\r\n  padding: 0;\r\n  border: 0;\r\n  font-size: 100%;\r\n  font-weight: normal;\r\n  vertical-align: baseline;\r\n}\r\n\r\n.body-div {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 87vh;\r\n  background: #999999;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 6px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content {\r\n\tposition: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  width: 900px;\r\n  background: #f2f2f2;\r\n  height: 690px;\r\n}\r\n\r\n.content .msglist {\r\n  width: 350px;\r\n  background: #e6e6e6;\r\n  overflow-y: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box {\r\n  height: calc(100% - 60px);\r\n  overflow: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  padding: 12px;\r\n  -webkit-transition: background-color 0.1s;\r\n  transition: background-color 0.1s;\r\n  font-size: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist:hover {\r\n  background-color: gainsboro;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist.active {\r\n  background-color: #c4c4c4;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left {\r\n  position: relative;\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left span {\r\n  position: absolute;\r\n  width: 15px;\r\n  height: 15px;\r\n  text-align: center;\r\n  background: red;\r\n  color: #ffffff;\r\n  top: 0;\r\n  right: 0;\r\n  border-radius: 50%;\r\n  font-size: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-right {\r\n  position: relative;\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  margin-top: 4px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .name {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  font-size: 14px;\r\n  width: 190px;\r\n  // width: 100px;\r\n  //   overflow: hidden;\r\n  //   white-space: nowrap;\r\n  //   text-overflow: ellipsis;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .time {\r\n  float: right;\r\n  color: #999;\r\n  font-size: 10px;\r\n  vertical-align: top;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .lastmsg {\r\n  position: absolute;\r\n  font-size: 12px;\r\n  width: 130px;\r\n  height: 15px;\r\n  line-height: 15px;\r\n  color: #999;\r\n  bottom: 8px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n.content .msglist .msg-left-box .sessionlist .number-badge {\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: red;\r\n    color: white;\r\n    width: 25px;\r\n    height: 25px;\r\n    border-radius: 50%;\r\n    font-size: 13px;\r\n    position: absolute;\r\n    top: 24px;\r\n    left: 223px;\r\n}\r\n.content .msglist .wrapper {\r\n  padding: 22px 12px 12px 12px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper {\r\n  position: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  height: 26px;\r\n  width: 100%;\r\n  background-color: #e5e3e2;\r\n  border: 1px solid #d9d7d6;\r\n  border-radius: 2px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 6px;\r\n  background-color: #e5e3e2;\r\n  outline: none;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput:focus {\r\n  background-color: #f2efee;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .icon-search {\r\n  display: inline-block;\r\n  width: 24px;\r\n  height: 24px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput-delete {\r\n  display: block;\r\n  position: absolute;\r\n  outline: none;\r\n  top: 0;\r\n  right: 0;\r\n  width: 24px;\r\n  height: 100%;\r\n  background-image: url(\"/img/delete.png\");\r\n  background-size: 26px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .message {\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message .header {\r\n  height: 60px;\r\n  padding: 28px 30px 0;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  border-bottom: 1px solid #e7e7e7;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: justify;\r\n  -ms-flex-pack: justify;\r\n  justify-content: space-between;\r\n}\r\n\r\n.content .chatbox .message .header .friendname {\r\n  font-size: 18px;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message-wrapper {\r\n  height: calc(100% - 240px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box {\r\n  padding: 0 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:last-child {\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time {\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time span {\r\n  display: inline-block;\r\n  padding: 3px 6px;\r\n  border-radius: 3px;\r\n  background-color: #dcdcdc;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img {\r\n  width: 200px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img img {\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-flex {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: start;\r\n  -ms-flex-align: start;\r\n  align-items: flex-start;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1) {\r\n  width: 44px;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  img {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 6px;\r\n  display: block;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  span {\r\n  display: block;\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: left;\r\n  padding-top: 3px;\r\n  width: 50px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding: 0 15px;\r\n  padding-right: 30px;\r\n  text-align: left;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content {\r\n  padding: 10px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  text-align: justify;\r\n  display: inline-block;\r\n  position: relative;\r\n  max-width: 260px;\r\n  min-height: 20px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content::after {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 7px solid transparent;\r\n  left: -14px;\r\n  top: 10px;\r\n  border-right: 7px solid #ffffff;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-img\r\n  img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.content .chatbox .input-box {\r\n  height: 180px;\r\n  background: #fff;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box {\r\n  height: 40px;\r\n  padding: 0 10px;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: start;\r\n  -ms-flex-pack: start;\r\n  justify-content: flex-start;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file {\r\n  position: relative;\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 10px;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 20px;\r\n  opacity: 0;\r\n  z-index: 10;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input[type=\"file\"] {\r\n  color: transparent;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file img {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  -webkit-transform: translate(-50%, -50%);\r\n  transform: translate(-50%, -50%);\r\n  display: block;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.content .chatbox .input-box .input-text {\r\n  width: 100%;\r\n  height: 90px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-text textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  padding: 0 10px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  outline: none;\r\n  font-size: 16px;\r\n  resize: none;\r\n  border: none;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 5px 15px;\r\n  height: 50px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span {\r\n  padding: 5px 15px;\r\n  font-size: 16px;\r\n  background: #f2f2f2;\r\n  cursor: pointer;\r\n  border-radius: 2px;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span:hover {\r\n  background: #129611;\r\n  color: #ffffff;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.oneself .flex-view:nth-child(1) {\r\n  -webkit-box-ordinal-group: 3;\r\n  -ms-flex-order: 2;\r\n  order: 2;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-ordinal-group: 2;\r\n  -ms-flex-order: 1;\r\n  order: 1;\r\n  padding-left: 30px !important;\r\n  padding-right: 15px !important;\r\n  text-align: right !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content {\r\n  position: relative;\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::after {\r\n  border: none !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 8px solid transparent;\r\n  right: -16px;\r\n  top: 12px;\r\n  border-left: 8px solid #b2e281;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .file-box {\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.file-box {\r\n  width: 230px;\r\n  height: 60px;\r\n  -webkit-box-shadow: 0px 0px 5px #ededed;\r\n  box-shadow: 0px 0px 5px #ededed;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding-right: 10px;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-name {\r\n  width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-size {\r\n  font-size: 12px;\r\n  padding-top: 10px;\r\n  color: #999999;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) {\r\n  width: 34px;\r\n  height: 34px;\r\n  text-align: center;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.img-popup {\r\n  position: fixed;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  opacity: 0;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.img-popup .close {\r\n  text-align: center;\r\n}\r\n\r\n.img-popup .close span {\r\n  width: 100px;\r\n  height: 100px;\r\n  font-size: 60px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n\r\n.img-popup .img-div {\r\n  height: 500px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.img-popup .img-div img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.show-popup {\r\n  opacity: 1;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n  pointer-events: all;\r\n}\r\n\r\n.upload-emji {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  img {\r\n    width: 20px;\r\n    height: 20px;\r\n    cursor: pointer;\r\n  }\r\n  .emji-box {\r\n    position: absolute;\r\n    top: -250px;\r\n    width: 440px;\r\n    height: 250px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 0px 10px #ededed;\r\n    border-radius: 12px;\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\";\r\n      border: 7px solid transparent;\r\n      border-top: 7px solid #ffffff;\r\n      bottom: -14px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    .biao-box {\r\n      padding: 10px;\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      .biao-flex {\r\n        text-align: center;\r\n        width: 32px;\r\n        margin: 5px;\r\n        font-size: 20px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n/*# sourceMappingURL=all.css.map */\r\n</style>\r\n"]}]}