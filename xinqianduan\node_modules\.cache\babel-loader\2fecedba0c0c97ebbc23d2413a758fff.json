{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue", "mtime": 1748483892216}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "is_num", "isClear", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "mounted", "getData", "methods", "editData", "id", "_this", "getInfo", "desc", "pic_path", "content", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "postRequest", "Array", "isArray", "count", "error", "console", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSortChange", "column", "log", "change", "handleSuccess", "res", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "success"], "sources": ["src/views/pages/xinwen/xinwen.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <h2 class=\"page-title\">{{ this.$router.currentRoute.name }}</h2>\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"search-section\">\r\n        <div class=\"search-controls\">\r\n          <el-input\r\n            placeholder=\"请输入案例标题进行搜索\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <el-button\r\n              slot=\"append\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n            ></el-button>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"action-controls\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n          >\r\n            新增案例\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-section\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"data-table\"\r\n          stripe\r\n          @sort-change=\"handleSortChange\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"案例标题\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"case-title-cell\">\r\n                <div class=\"case-title\">{{ scope.row.title }}</div>\r\n                <div class=\"case-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"case-cover\" v-if=\"scope.row.pic_path\">\r\n                <img\r\n                  :src=\"scope.row.pic_path\"\r\n                  @click=\"showImage(scope.row.pic_path)\"\r\n                  class=\"cover-image\"\r\n                  :alt=\"scope.row.title\"\r\n                />\r\n              </div>\r\n              <span v-else class=\"no-cover\">暂无封面</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"封面\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          >\r\n            <template slot=\"append\">280rpx*200rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/anli/\",\r\n      title: \"案例\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n      isClear: false, // 添加编辑器清空标志\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          pic_path: \"\",\r\n          content: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp && resp.code == 200) {\r\n            // 确保 list 始终是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n          } else {\r\n            // 如果请求失败，设置为空数组\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据失败:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSortChange(column) {\r\n      // 处理排序变化\r\n      console.log('排序变化:', column);\r\n    },\r\n    change(val) {\r\n      // 编辑器内容变化回调\r\n      this.ruleForm.content = val;\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 搜索和操作区域 */\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  gap: 16px;\r\n}\r\n\r\n.search-controls {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.data-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 案例标题单元格 */\r\n.case-title-cell {\r\n  padding: 4px 0;\r\n}\r\n\r\n.case-title {\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.case-title:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.case-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 封面样式 */\r\n.case-cover {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.cover-image {\r\n  width: 60px;\r\n  height: 40px;\r\n  object-fit: cover;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.no-cover {\r\n  color: #d9d9d9;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.edit-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 分页容器 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-controls {\r\n    max-width: none;\r\n  }\r\n\r\n  .action-controls {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table ::v-deep .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table ::v-deep .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  color: #262626;\r\n}\r\n\r\n.data-table ::v-deep .el-table tr:hover > td {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped:hover td {\r\n  background-color: #f0f0f0;\r\n}\r\n</style>\r\n"], "mappings": "AAuMA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,MAAA;MACA;MACAC,OAAA;MAAA;;MAEAC,KAAA;QACAR,KAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAAZ,QAAA;UACAL,KAAA;UACAoB,IAAA;UACAC,QAAA;UACAC,OAAA;QACA;MACA;MAEAJ,KAAA,CAAAhB,iBAAA;IACA;IACAiB,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAK,UAAA,CAAAL,KAAA,CAAAnB,GAAA,gBAAAkB,EAAA,EAAAO,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAP,KAAA,CAAAb,QAAA,GAAAoB,IAAA,CAAAnC,IAAA;QACA;MACA;IACA;IACAoC,QAAAC,KAAA,EAAAV,EAAA;MACA,KAAAW,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAjC,GAAA,kBAAAkB,EAAA,EAAAO,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACArB,OAAA;YACA;YACA,KAAAlB,IAAA,CAAA2C,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACArB,OAAA;QACA;MACA;IACA;IACA2B,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA9C,IAAA;MACA,KAAAC,IAAA;MACA,KAAAmB,OAAA;IACA;IAEAA,QAAA;MACA,IAAAI,KAAA;MAEAA,KAAA,CAAApB,OAAA;MACAoB,KAAA,CACAuB,WAAA,CACAvB,KAAA,CAAAnB,GAAA,mBAAAmB,KAAA,CAAAxB,IAAA,cAAAwB,KAAA,CAAAvB,IAAA,EACAuB,KAAA,CAAAtB,MACA,EACA4B,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAQ,IAAA;UACA;UACAf,KAAA,CAAA1B,IAAA,GAAAkD,KAAA,CAAAC,OAAA,CAAAlB,IAAA,CAAAnC,IAAA,IAAAmC,IAAA,CAAAnC,IAAA;UACA4B,KAAA,CAAAzB,KAAA,GAAAgC,IAAA,CAAAmB,KAAA;QACA;UACA;UACA1B,KAAA,CAAA1B,IAAA;UACA0B,KAAA,CAAAzB,KAAA;QACA;QACAyB,KAAA,CAAApB,OAAA;MACA,GACAsC,KAAA,CAAAS,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA3B,KAAA,CAAA1B,IAAA;QACA0B,KAAA,CAAAzB,KAAA;QACAyB,KAAA,CAAApB,OAAA;MACA;IACA;IACAiD,SAAA;MACA,IAAA7B,KAAA;MACA,KAAA8B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAT,WAAA,CAAAvB,KAAA,CAAAnB,GAAA,gBAAAM,QAAA,EAAAmB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAf,KAAA,CAAAgB,QAAA;gBACAH,IAAA;gBACArB,OAAA,EAAAe,IAAA,CAAA0B;cACA;cACA,KAAArC,OAAA;cACAI,KAAA,CAAAhB,iBAAA;YACA;cACAgB,KAAA,CAAAgB,QAAA;gBACAH,IAAA;gBACArB,OAAA,EAAAe,IAAA,CAAA0B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA1D,IAAA,GAAA0D,GAAA;MAEA,KAAAvC,OAAA;IACA;IACAwC,oBAAAD,GAAA;MACA,KAAA3D,IAAA,GAAA2D,GAAA;MACA,KAAAvC,OAAA;IACA;IACAyC,iBAAAC,MAAA;MACA;MACAV,OAAA,CAAAW,GAAA,UAAAD,MAAA;IACA;IACAE,OAAAL,GAAA;MACA;MACA,KAAAhD,QAAA,CAAAiB,OAAA,GAAA+B,GAAA;IACA;IACAM,cAAAC,GAAA;MACA,KAAAvD,QAAA,CAAAgB,QAAA,GAAAuC,GAAA,CAAAtE,IAAA,CAAAS,GAAA;IACA;IAEA8D,UAAAC,IAAA;MACA,KAAA3D,UAAA,GAAA2D,IAAA;MACA,KAAA1D,aAAA;IACA;IACA2D,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA/B,IAAA;MACA,KAAAiC,UAAA;QACA,KAAA9B,QAAA,CAAAW,KAAA;QACA;MACA;IACA;IACAqB,SAAAJ,IAAA,EAAAK,QAAA;MACA,IAAAjD,KAAA;MACAA,KAAA,CAAAK,UAAA,gCAAAuC,IAAA,EAAAtC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAf,KAAA,CAAAb,QAAA,CAAA8D,QAAA;UAEAjD,KAAA,CAAAgB,QAAA,CAAAkC,OAAA;QACA;UACAlD,KAAA,CAAAgB,QAAA,CAAAW,KAAA,CAAApB,IAAA,CAAA0B,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}