{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue?vue&type=style&index=0&id=3d1d58bc&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue", "mtime": 1748454232524}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lawyer.vue"], "names": [], "mappings": ";AAww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file": "lawyer.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"lawyer-letter-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            发律师函管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和处理律师函制作工单</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">关键词搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入工单号/标题/用户手机号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">处理状态</label>\r\n              <el-select\r\n                v-model=\"search.is_deal\"\r\n                placeholder=\"请选择处理状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in options1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.title\"\r\n                  :value=\"item.id\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <h3>\r\n              <i class=\"el-icon-tickets\"></i>\r\n              律师函工单列表\r\n            </h3>\r\n            <span class=\"table-count\">共 {{ total }} 条记录</span>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"lawyer-table\"\r\n          stripe\r\n          border\r\n          empty-text=\"暂无律师函工单数据\"\r\n        >\r\n          <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"order-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"order-text\">{{ scope.row.order_sn }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"type\" label=\"工单类型\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ scope.row.type || '律师函' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <span class=\"title-text\" :title=\"scope.row.title\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"desc-cell\">\r\n                <span class=\"desc-text\" :title=\"scope.row.desc\">\r\n                  {{ scope.row.desc ? (scope.row.desc.length > 50 ? scope.row.desc.substring(0, 50) + '...' : scope.row.desc) : '-' }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getStatusType(scope.row.is_deal)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getStatusText(scope.row.is_deal) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"uid\" label=\"用户手机\" width=\"130\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"phone-cell\">\r\n                <i class=\"el-icon-phone\"></i>\r\n                <span>{{ scope.row.uid || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"dt_name\" label=\"债务人\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"debtor-cell clickable\" @click=\"showDebtorDetail(scope.row)\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"debtor-name\">{{ scope.row.dt_name || '-' }}</span>\r\n                <i class=\"el-icon-arrow-right arrow-icon\"></i>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-check\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                  :disabled=\"scope.row.is_deal === 2\"\r\n                >\r\n                  {{ scope.row.is_deal === 2 ? '已完成' : '完成制作' }}\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-close\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  取消\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <!-- 律师函处理对话框 -->\r\n    <el-dialog\r\n      title=\"律师函制作处理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"process-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <div class=\"dialog-content\">\r\n        <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-position=\"top\">\r\n          <!-- 工单基本信息 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-info\"></i>\r\n              工单基本信息\r\n            </h4>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单类型\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.type_title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-folder\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单标题\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"工单描述\">\r\n              <el-input\r\n                v-model=\"ruleForm.desc\"\r\n                readonly\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                class=\"readonly-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 处理状态设置 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              处理状态设置\r\n            </h4>\r\n            <el-form-item label=\"制作状态\">\r\n              <el-radio-group v-model=\"ruleForm.is_deal\" class=\"status-radio-group\">\r\n                <el-radio :label=\"1\" class=\"status-radio\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  处理中\r\n                </el-radio>\r\n                <el-radio :label=\"2\" class=\"status-radio\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  已完成\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- AI生成律师函 -->\r\n          <div class=\"form-section ai-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-cpu\"></i>\r\n              AI智能生成\r\n            </h4>\r\n            <div class=\"ai-generation-content\">\r\n              <div class=\"ai-description\">\r\n                <p>\r\n                  <i class=\"el-icon-info\"></i>\r\n                  AI将根据工单信息、债务人详情和合同模板自动生成律师函内容\r\n                </p>\r\n              </div>\r\n              <div class=\"ai-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-cpu\"\r\n                  @click=\"generateLawyerLetter\"\r\n                  :loading=\"aiGenerating\"\r\n                  class=\"ai-generate-btn\"\r\n                >\r\n                  {{ aiGenerating ? 'AI生成中...' : 'AI生成律师函' }}\r\n                </el-button>\r\n              </div>\r\n              <!-- AI生成结果展示 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-result\">\r\n                <h5 class=\"result-title\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  AI生成结果\r\n                </h5>\r\n                <div class=\"generated-content\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"8\"\r\n                    placeholder=\"AI生成的律师函内容将显示在这里...\"\r\n                    class=\"ai-content-textarea\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"ai-result-actions\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-check\"\r\n                    @click=\"useAiContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    使用此内容\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"regenerateContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    重新生成\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 完成处理区域 -->\r\n          <div v-if=\"ruleForm.is_deal == 2\" class=\"form-section completion-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-upload\"></i>\r\n              完成处理\r\n            </h4>\r\n\r\n            <!-- 处理方式选择 -->\r\n            <el-form-item label=\"处理方式\" class=\"process-method-item\">\r\n              <el-radio-group v-model=\"processMethod\" @change=\"onProcessMethodChange\" class=\"method-radio-group\">\r\n                <el-radio label=\"ai\" class=\"method-radio\">\r\n                  <i class=\"el-icon-cpu\"></i>\r\n                  AI智能生成\r\n                </el-radio>\r\n                <el-radio label=\"upload\" class=\"method-radio\">\r\n                  <i class=\"el-icon-upload\"></i>\r\n                  手动上传文件\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <!-- AI生成方式 -->\r\n            <div v-if=\"processMethod === 'ai'\" class=\"ai-process-section\">\r\n              <div class=\"ai-process-info\">\r\n                <el-alert\r\n                  title=\"AI生成模式\"\r\n                  description=\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\"\r\n                  type=\"info\"\r\n                  :closable=\"false\"\r\n                  show-icon\r\n                  class=\"ai-mode-alert\"\r\n                ></el-alert>\r\n              </div>\r\n\r\n              <!-- AI生成的内容预览 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-content-preview\">\r\n                <el-form-item label=\"生成内容预览\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"6\"\r\n                    placeholder=\"AI生成的律师函内容...\"\r\n                    class=\"ai-preview-textarea\"\r\n                    readonly\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <el-form-item label=\"文件生成\">\r\n                <div class=\"ai-file-generation\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"generateAiFile\"\r\n                    :loading=\"fileGenerating\"\r\n                    class=\"generate-file-btn\"\r\n                  >\r\n                    {{ fileGenerating ? '生成文件中...' : '生成律师函文件' }}\r\n                  </el-button>\r\n                  <div v-if=\"aiGeneratedFile\" class=\"generated-file-info\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <span>{{ aiGeneratedFile.name }}</span>\r\n                    <el-tag type=\"success\" size=\"mini\">已生成</el-tag>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <!-- 手动上传方式 -->\r\n            <div v-if=\"processMethod === 'upload'\" class=\"upload-process-section\">\r\n              <el-form-item\r\n                label=\"律师函文件\"\r\n                prop=\"file_path\"\r\n                class=\"file-upload-item\"\r\n              >\r\n                <div class=\"upload-area\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.file_path\"\r\n                    placeholder=\"请上传律师函文件\"\r\n                    readonly\r\n                    class=\"file-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                  <div class=\"upload-buttons\">\r\n                    <el-button @click=\"changeFile('file_path')\" type=\"primary\" icon=\"el-icon-upload\">\r\n                      <el-upload\r\n                        action=\"/admin/Upload/uploadFile\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleSuccess\"\r\n                        style=\"display: inline-block;\"\r\n                      >\r\n                        上传文件\r\n                      </el-upload>\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"ruleForm.file_path\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n                    >\r\n                      删除文件\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <el-form-item label=\"处理说明\">\r\n              <el-input\r\n                v-model=\"ruleForm.content\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入处理说明或备注信息...\"\r\n                class=\"content-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\" icon=\"el-icon-close\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" icon=\"el-icon-check\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 债务人详情右侧滑出面板 -->\r\n    <div class=\"debtor-detail-panel\" :class=\"{ 'panel-open': showDebtorPanel }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDebtorPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"header-info\">\r\n            <h3 class=\"panel-title\">\r\n              <i class=\"el-icon-user\"></i>\r\n              债务人详情\r\n            </h3>\r\n            <p class=\"panel-subtitle\">{{ currentDebtor.dt_name }}</p>\r\n          </div>\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"closeDebtorPanel\"\r\n            class=\"close-btn\"\r\n          ></el-button>\r\n        </div>\r\n\r\n        <!-- 左侧菜单 -->\r\n        <div class=\"panel-body\">\r\n          <div class=\"sidebar-menu\">\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'basic' }\"\r\n                 @click=\"activeTab = 'basic'\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n              <span>基本信息</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'user' }\"\r\n                 @click=\"activeTab = 'user'\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>关联用户</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'files' }\"\r\n                 @click=\"activeTab = 'files'\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              <span>相关文件</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'history' }\"\r\n                 @click=\"activeTab = 'history'\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>历史记录</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"content-area\">\r\n            <!-- 基本信息 -->\r\n            <div v-if=\"activeTab === 'basic'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-info\"></i>\r\n                  债务人基本信息\r\n                </h4>\r\n                <div class=\"info-grid\">\r\n                  <div class=\"info-item\">\r\n                    <label>姓名：</label>\r\n                    <span>{{ currentDebtor.dt_name }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentDebtor.id_card || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>联系电话：</label>\r\n                    <span>{{ currentDebtor.phone || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>地址：</label>\r\n                    <span>{{ currentDebtor.address || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务金额：</label>\r\n                    <span class=\"debt-amount\">¥{{ currentDebtor.debt_amount || '0.00' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务类型：</label>\r\n                    <span>{{ currentDebtor.debt_type || '未分类' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 关联用户信息 -->\r\n            <div v-if=\"activeTab === 'user'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-user\"></i>\r\n                  关联用户信息\r\n                </h4>\r\n                <div class=\"user-card\">\r\n                  <div class=\"user-avatar\">\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </div>\r\n                  <div class=\"user-info\">\r\n                    <h5>{{ currentDebtor.user_name }}</h5>\r\n                    <p>手机号：{{ currentDebtor.user_phone }}</p>\r\n                    <p>注册时间：{{ currentDebtor.user_register_time }}</p>\r\n                    <p>用户状态：\r\n                      <el-tag :type=\"currentDebtor.user_status === 'active' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ currentDebtor.user_status === 'active' ? '正常' : '异常' }}\r\n                      </el-tag>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 相关文件 -->\r\n            <div v-if=\"activeTab === 'files'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-folder\"></i>\r\n                  相关文件\r\n                </h4>\r\n                <div class=\"file-list\">\r\n                  <div v-for=\"file in currentDebtor.files\" :key=\"file.id\" class=\"file-item\">\r\n                    <div class=\"file-icon\">\r\n                      <i :class=\"getFileIcon(file.type)\"></i>\r\n                    </div>\r\n                    <div class=\"file-info\">\r\n                      <h6>{{ file.name }}</h6>\r\n                      <p>{{ file.upload_time }}</p>\r\n                      <p class=\"file-size\">{{ file.size }}</p>\r\n                    </div>\r\n                    <div class=\"file-actions\">\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"previewFile(file)\">\r\n                        <i class=\"el-icon-view\"></i>\r\n                        预览\r\n                      </el-button>\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"downloadFile(file)\">\r\n                        <i class=\"el-icon-download\"></i>\r\n                        下载\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.files || currentDebtor.files.length === 0\" class=\"empty-files\">\r\n                    <i class=\"el-icon-folder-opened\"></i>\r\n                    <p>暂无相关文件</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 历史记录 -->\r\n            <div v-if=\"activeTab === 'history'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  历史记录\r\n                </h4>\r\n                <div class=\"history-timeline\">\r\n                  <div v-for=\"record in currentDebtor.history\" :key=\"record.id\" class=\"timeline-item\">\r\n                    <div class=\"timeline-dot\"></div>\r\n                    <div class=\"timeline-content\">\r\n                      <h6>{{ record.action }}</h6>\r\n                      <p>{{ record.description }}</p>\r\n                      <span class=\"timeline-time\">{{ record.time }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.history || currentDebtor.history.length === 0\" class=\"empty-history\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    <p>暂无历史记录</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      // 债务人详情面板相关\r\n      showDebtorPanel: false,\r\n      activeTab: 'basic',\r\n      currentDebtor: {},\r\n      // AI生成相关\r\n      aiGenerating: false,\r\n      aiGeneratedContent: '',\r\n      contractTypes: [], // 合同类型列表\r\n      // 处理方式相关\r\n      processMethod: 'upload', // 默认为手动上传，可选值：'ai' | 'upload'\r\n      fileGenerating: false, // 文件生成状态\r\n      aiGeneratedFile: null, // AI生成的文件信息\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n  methods: {\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        0: 'warning',  // 待处理\r\n        1: 'primary',  // 处理中\r\n        2: 'success'   // 已处理\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待处理',\r\n        1: '处理中',\r\n        2: '已处理'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_deal: -1,\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        is_deal: 1,\r\n        type_title: \"\",\r\n        desc: \"\",\r\n        file_path: \"\",\r\n        content: \"\"\r\n      };\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      this.handleDialogClose();\r\n      this.dialogFormVisible = false;\r\n      // 重置AI生成内容\r\n      this.aiGeneratedContent = '';\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"债务催收\",\r\n            template_file: \"/uploads/templates/debt_collection_template.docx\",\r\n            template_name: \"债务催收律师函模板.docx\",\r\n            template_size: 245760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您欠付我方当事人的债务事宜，特致函如下：\r\n\r\n一、债务事实\r\n根据相关证据材料显示，您于{{debt_date}}向我方当事人借款人民币{{debt_amount}}元，约定还款期限为{{repay_date}}。但截至目前，您仍未履行还款义务，已构成违约。\r\n\r\n二、法律后果\r\n您的上述行为已构成违约，根据《中华人民共和国民法典》相关规定，您应当承担相应的法律责任。\r\n\r\n三、催告要求\r\n现特函告知，请您在收到本函后7日内，将所欠款项{{debt_amount}}元及相应利息一次性支付给我方当事人。\r\n\r\n四、法律警告\r\n如您在上述期限内仍不履行还款义务，我方将依法采取包括但不限于向人民法院提起诉讼等法律手段维护我方当事人的合法权益，由此产生的一切法律后果由您承担。\r\n\r\n特此函告！\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n联系电话：{{contact_phone}}\r\n地址：{{law_firm_address}}\r\n            `\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"合同违约\",\r\n            template_file: \"/uploads/templates/contract_breach_template.pdf\",\r\n            template_name: \"合同违约律师函模板.pdf\",\r\n            template_size: 512000,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您违反合同约定的事宜，特致函如下：\r\n\r\n一、合同事实\r\n您与我方当事人于{{contract_date}}签订了《{{contract_title}}》，约定了双方的权利义务。\r\n\r\n二、违约事实\r\n根据合同约定及相关证据，您存在以下违约行为：\r\n{{breach_details}}\r\n\r\n三、法律后果\r\n您的违约行为已给我方当事人造成了经济损失，根据合同约定及法律规定，您应当承担违约责任。\r\n\r\n四、要求\r\n请您在收到本函后7日内：\r\n1. 立即停止违约行为\r\n2. 履行合同义务\r\n3. 赔偿相应损失\r\n\r\n如您拒不履行，我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"知识产权侵权\",\r\n            template_file: \"/uploads/templates/ip_infringement_template.doc\",\r\n            template_name: \"知识产权侵权律师函模板.doc\",\r\n            template_size: 327680,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您侵犯我方当事人知识产权的事宜，特致函如下：\r\n\r\n一、权利基础\r\n我方当事人依法享有{{ip_type}}的专有权利，该权利受法律保护。\r\n\r\n二、侵权事实\r\n经调查发现，您未经我方当事人许可，擅自{{infringement_details}}，侵犯了我方当事人的合法权益。\r\n\r\n三、法律后果\r\n您的行为构成侵权，应当承担停止侵害、赔偿损失等法律责任。\r\n\r\n四、要求\r\n请您在收到本函后立即：\r\n1. 停止一切侵权行为\r\n2. 销毁侵权产品\r\n3. 赔偿经济损失\r\n\r\n否则我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"劳动争议\",\r\n            template_file: \"/uploads/templates/labor_dispute_template.docx\",\r\n            template_name: \"劳动争议律师函模板.docx\",\r\n            template_size: 298760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就劳动争议事宜，特致函如下：\r\n\r\n一、劳动关系\r\n我方当事人与您存在劳动关系，期间为{{employment_period}}。\r\n\r\n二、争议事实\r\n{{dispute_details}}\r\n\r\n三、法律依据\r\n根据《劳动法》、《劳动合同法》等相关法律法规，您应当履行相应义务。\r\n\r\n四、要求\r\n请您在收到本函后7日内妥善处理相关事宜，否则我方将通过法律途径解决。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"房屋租赁\",\r\n            template_file: \"/uploads/templates/lease_dispute_template.pdf\",\r\n            template_name: \"房屋租赁纠纷律师函模板.pdf\",\r\n            template_size: 445760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就房屋租赁纠纷事宜，特致函如下：\r\n\r\n一、租赁关系\r\n您与我方当事人签订了房屋租赁合同，租赁期限为{{lease_period}}。\r\n\r\n二、违约事实\r\n{{lease_breach_details}}\r\n\r\n三、要求\r\n请您在收到本函后立即：\r\n1. {{specific_requirements}}\r\n2. 支付相关费用\r\n3. 配合解决纠纷\r\n\r\n如不配合，我方将依法维权。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成律师函\r\n    async generateLawyerLetter() {\r\n      if (!this.ruleForm.title || !this.ruleForm.type_title) {\r\n        this.$message.warning('请先确保工单信息完整');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n\r\n      try {\r\n        // 1. 根据工单标题和类型找到对应的合同模板\r\n        const matchedTemplate = this.findMatchingTemplate();\r\n\r\n        if (!matchedTemplate) {\r\n          this.$message.warning('未找到匹配的律师函模板，请先在合同类型管理中上传相应模板');\r\n          this.aiGenerating = false;\r\n          return;\r\n        }\r\n\r\n        // 2. 获取债务人详情信息\r\n        const debtorInfo = await this.getDebtorInfo();\r\n\r\n        // 3. 获取关联用户信息\r\n        const userInfo = await this.getUserInfo();\r\n\r\n        // 4. 模拟AI生成过程\r\n        await this.simulateAiGeneration(matchedTemplate, debtorInfo, userInfo);\r\n\r\n        this.$message.success('AI律师函生成完成！');\r\n\r\n        // 如果是AI模式，自动生成文件\r\n        if (this.processMethod === 'ai') {\r\n          setTimeout(() => {\r\n            this.generateAiFile();\r\n          }, 500);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI生成失败:', error);\r\n        this.$message.error('AI生成失败，请重试');\r\n      } finally {\r\n        this.aiGenerating = false;\r\n      }\r\n    },\r\n\r\n    // 查找匹配的模板\r\n    findMatchingTemplate() {\r\n      // 根据工单标题和类型匹配模板\r\n      const title = this.ruleForm.title.toLowerCase();\r\n      const typeTitle = this.ruleForm.type_title.toLowerCase();\r\n\r\n      // 匹配规则\r\n      const matchRules = [\r\n        { keywords: ['债务', '催收', '欠款', '借款'], templateId: 1 },\r\n        { keywords: ['合同', '违约', '违反'], templateId: 2 },\r\n        { keywords: ['知识产权', '侵权', '商标', '专利'], templateId: 3 },\r\n        { keywords: ['劳动', '工资', '员工'], templateId: 4 },\r\n        { keywords: ['租赁', '房屋', '租金'], templateId: 5 }\r\n      ];\r\n\r\n      for (const rule of matchRules) {\r\n        if (rule.keywords.some(keyword =>\r\n          title.includes(keyword) || typeTitle.includes(keyword)\r\n        )) {\r\n          return this.contractTypes.find(type => type.id === rule.templateId);\r\n        }\r\n      }\r\n\r\n      // 默认返回债务催收模板\r\n      return this.contractTypes.find(type => type.id === 1);\r\n    },\r\n\r\n    // 获取债务人信息\r\n    async getDebtorInfo() {\r\n      // 模拟获取债务人详细信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: this.ruleForm.dt_name || '张三',\r\n            id_card: '110101199001011234',\r\n            phone: '13800138001',\r\n            address: '北京市朝阳区建国门外大街1号',\r\n            debt_amount: '100000.00',\r\n            debt_type: '借款纠纷',\r\n            debt_date: '2023-06-15',\r\n            repay_date: '2023-12-15'\r\n          });\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      // 模拟获取关联用户信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: '李明',\r\n            phone: this.ruleForm.uid || '13900139001',\r\n            register_time: '2023-01-15',\r\n            status: 'active'\r\n          });\r\n        }, 300);\r\n      });\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    async simulateAiGeneration(template, debtorInfo, userInfo) {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          // 替换模板中的变量\r\n          let content = template.template_content;\r\n\r\n          // 替换债务人信息\r\n          content = content.replace(/\\{\\{debtor_name\\}\\}/g, debtorInfo.name);\r\n          content = content.replace(/\\{\\{debt_amount\\}\\}/g, debtorInfo.debt_amount);\r\n          content = content.replace(/\\{\\{debt_date\\}\\}/g, debtorInfo.debt_date);\r\n          content = content.replace(/\\{\\{repay_date\\}\\}/g, debtorInfo.repay_date);\r\n\r\n          // 替换用户信息\r\n          content = content.replace(/\\{\\{user_name\\}\\}/g, userInfo.name);\r\n\r\n          // 替换其他信息\r\n          content = content.replace(/\\{\\{current_date\\}\\}/g, new Date().toLocaleDateString('zh-CN'));\r\n          content = content.replace(/\\{\\{law_firm_name\\}\\}/g, '北京市XX律师事务所');\r\n          content = content.replace(/\\{\\{contact_phone\\}\\}/g, '010-12345678');\r\n          content = content.replace(/\\{\\{law_firm_address\\}\\}/g, '北京市朝阳区XX大厦XX层');\r\n\r\n          // 根据工单描述添加具体内容\r\n          if (this.ruleForm.desc) {\r\n            content = content.replace(/\\{\\{breach_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{dispute_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{infringement_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{lease_breach_details\\}\\}/g, this.ruleForm.desc);\r\n          }\r\n\r\n          // 清理未替换的变量\r\n          content = content.replace(/\\{\\{[^}]+\\}\\}/g, '[待填写]');\r\n\r\n          this.aiGeneratedContent = content.trim();\r\n          resolve();\r\n        }, 2000); // 模拟2秒的AI生成时间\r\n      });\r\n    },\r\n\r\n    // 使用AI生成的内容\r\n    useAiContent() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('没有可用的AI生成内容');\r\n        return;\r\n      }\r\n\r\n      // 将AI生成的内容设置到处理说明中\r\n      this.ruleForm.content = this.aiGeneratedContent;\r\n\r\n      // 自动设置为已完成状态\r\n      this.ruleForm.is_deal = 2;\r\n\r\n      this.$message.success('已应用AI生成的律师函内容');\r\n    },\r\n\r\n    // 重新生成内容\r\n    regenerateContent() {\r\n      this.aiGeneratedContent = '';\r\n      this.generateLawyerLetter();\r\n    },\r\n\r\n    // 处理方式切换\r\n    onProcessMethodChange(method) {\r\n      // 清空之前的数据\r\n      if (method === 'ai') {\r\n        // 切换到AI模式，清空上传的文件\r\n        this.ruleForm.file_path = '';\r\n        this.aiGeneratedFile = null;\r\n      } else {\r\n        // 切换到上传模式，清空AI生成的内容\r\n        this.aiGeneratedContent = '';\r\n        this.aiGeneratedFile = null;\r\n      }\r\n    },\r\n\r\n    // AI生成文件\r\n    async generateAiFile() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('请先生成律师函内容');\r\n        return;\r\n      }\r\n\r\n      this.fileGenerating = true;\r\n      try {\r\n        // 模拟文件生成过程\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n        // 这里应该调用实际的文件生成接口\r\n        // 将AI生成的内容转换为文件\r\n        const fileName = `律师函_${this.ruleForm.dt_name || '债务人'}_${new Date().getTime()}.docx`;\r\n\r\n        this.aiGeneratedFile = {\r\n          name: fileName,\r\n          path: `/uploads/lawyer_letters/${fileName}`,\r\n          size: '25KB'\r\n        };\r\n\r\n        // 将生成的文件路径设置到表单中\r\n        this.ruleForm.file_path = this.aiGeneratedFile.path;\r\n\r\n        this.$message.success('文件生成成功');\r\n      } catch (error) {\r\n        console.error('文件生成失败:', error);\r\n        this.$message.error('文件生成失败，请重试');\r\n      } finally {\r\n        this.fileGenerating = false;\r\n      }\r\n    },\r\n\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n\r\n      // 使用模拟数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        }\r\n      ];\r\n\r\n      // 查找对应的数据\r\n      const foundData = testData.find(item => item.id === id);\r\n\r\n      if (foundData) {\r\n        _this.ruleForm = { ...foundData };\r\n        _this.dialogFormVisible = true;\r\n        console.log('加载律师函详情数据:', _this.ruleForm);\r\n      } else {\r\n        _this.$message({\r\n          type: \"error\",\r\n          message: \"未找到对应的律师函数据\",\r\n        });\r\n      }\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27) {\r\n        if (this.dialogFormVisible) {\r\n          this.cancelDialog();\r\n        }\r\n        if (this.showDebtorPanel) {\r\n          this.closeDebtorPanel();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 显示债务人详情\r\n    showDebtorDetail(row) {\r\n      console.log('显示债务人详情:', row);\r\n\r\n      // 模拟获取债务人详细信息\r\n      this.currentDebtor = {\r\n        ...row,\r\n        // 基本信息\r\n        id_card: this.generateIdCard(),\r\n        phone: this.generatePhone(),\r\n        address: this.generateAddress(),\r\n        debt_amount: this.generateDebtAmount(),\r\n        debt_type: this.generateDebtType(),\r\n\r\n        // 关联用户信息\r\n        user_name: this.generateUserName(row.uid),\r\n        user_phone: row.uid,\r\n        user_register_time: this.generateRegisterTime(),\r\n        user_status: 'active',\r\n\r\n        // 相关文件\r\n        files: this.generateFiles(row.dt_name),\r\n\r\n        // 历史记录\r\n        history: this.generateHistory(row.dt_name)\r\n      };\r\n\r\n      this.activeTab = 'basic';\r\n      this.showDebtorPanel = true;\r\n    },\r\n\r\n    // 关闭债务人详情面板\r\n    closeDebtorPanel() {\r\n      this.showDebtorPanel = false;\r\n      this.currentDebtor = {};\r\n    },\r\n\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'zip': 'el-icon-folder-opened',\r\n        'rar': 'el-icon-folder-opened'\r\n      };\r\n      return iconMap[fileType] || 'el-icon-document';\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      console.log('预览文件:', file);\r\n      this.$message.info('文件预览功能开发中...');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      console.log('下载文件:', file);\r\n      this.$message.success('开始下载文件: ' + file.name);\r\n    },\r\n\r\n    // 生成模拟数据的辅助方法\r\n    generateIdCard() {\r\n      const prefixes = ['110101', '310101', '440101', '500101'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const year = 1980 + Math.floor(Math.random() * 30);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      const suffix = String(Math.floor(Math.random() * 9999)).padStart(4, '0');\r\n      return `${prefix}${year}${month}${day}${suffix}`;\r\n    },\r\n\r\n    generatePhone() {\r\n      const prefixes = ['138', '139', '150', '151', '188', '189'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const suffix = String(Math.floor(Math.random() * 100000000)).padStart(8, '0');\r\n      return `${prefix}${suffix}`;\r\n    },\r\n\r\n    generateAddress() {\r\n      const addresses = [\r\n        '北京市朝阳区建国门外大街1号',\r\n        '上海市浦东新区陆家嘴环路1000号',\r\n        '广州市天河区珠江新城花城大道85号',\r\n        '深圳市南山区深南大道10000号',\r\n        '杭州市西湖区文三路90号'\r\n      ];\r\n      return addresses[Math.floor(Math.random() * addresses.length)];\r\n    },\r\n\r\n    generateDebtAmount() {\r\n      const amounts = ['50000.00', '100000.00', '200000.00', '500000.00', '1000000.00'];\r\n      return amounts[Math.floor(Math.random() * amounts.length)];\r\n    },\r\n\r\n    generateDebtType() {\r\n      const types = ['借款纠纷', '合同违约', '房屋租赁', '劳动争议', '知识产权'];\r\n      return types[Math.floor(Math.random() * types.length)];\r\n    },\r\n\r\n    generateUserName(phone) {\r\n      const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴'];\r\n      const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];\r\n      const surname = surnames[Math.floor(Math.random() * surnames.length)];\r\n      const name = names[Math.floor(Math.random() * names.length)];\r\n      return `${surname}${name}`;\r\n    },\r\n\r\n    generateRegisterTime() {\r\n      const year = 2020 + Math.floor(Math.random() * 4);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      return `${year}-${month}-${day} 10:30:00`;\r\n    },\r\n\r\n    generateFiles(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: `${debtorName}_身份证.jpg`,\r\n          type: 'jpg',\r\n          size: '2.5MB',\r\n          upload_time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: `${debtorName}_借款合同.pdf`,\r\n          type: 'pdf',\r\n          size: '1.2MB',\r\n          upload_time: '2024-01-16 09:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: `${debtorName}_银行流水.pdf`,\r\n          type: 'pdf',\r\n          size: '3.8MB',\r\n          upload_time: '2024-01-17 16:45:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    generateHistory(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          action: '创建债务人档案',\r\n          description: `创建了${debtorName}的债务人档案`,\r\n          time: '2024-01-15 09:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          action: '上传相关文件',\r\n          description: '上传了身份证、合同等相关文件',\r\n          time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          action: '发起律师函',\r\n          description: '针对债务纠纷发起律师函制作申请',\r\n          time: '2024-01-16 10:20:00'\r\n        },\r\n        {\r\n          id: 4,\r\n          action: '更新债务信息',\r\n          description: '更新了债务金额和联系方式',\r\n          time: '2024-01-17 15:10:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载律师函测试数据...');\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.order_sn.toLowerCase().includes(keyword) ||\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.uid.includes(keyword) ||\r\n              item.dt_name.includes(keyword)\r\n            );\r\n          }\r\n\r\n          // 状态筛选\r\n          if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n            filteredData = filteredData.filter(item => item.is_deal === _this.search.is_deal);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('律师函数据加载完成:', _this.list);\r\n          console.log('总数:', _this.total);\r\n        } catch (error) {\r\n          console.error('加载律师函测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 300);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存操作\r\n          console.log('保存律师函数据:', this.ruleForm);\r\n\r\n          // 模拟API延迟\r\n          setTimeout(() => {\r\n            _this.$message({\r\n              type: \"success\",\r\n              message: \"律师函处理状态更新成功！\",\r\n            });\r\n            this.getData();\r\n            _this.dialogFormVisible = false;\r\n          }, 500);\r\n\r\n          // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n          /*\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n          */\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.lawyer-letter-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section h2.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  align-items: flex-end;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input,\r\n.search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  border: none;\r\n  padding: 10px 24px;\r\n}\r\n\r\n.reset-btn {\r\n  padding: 10px 24px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.table-header {\r\n  padding: 20px 24px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.table-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.lawyer-table {\r\n  margin: 0 24px 20px;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.order-cell,\r\n.title-cell,\r\n.desc-cell,\r\n.phone-cell,\r\n.debtor-cell,\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.order-text,\r\n.title-text,\r\n.desc-text {\r\n  font-weight: 500;\r\n}\r\n\r\n.desc-text {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 债务人单元格可点击样式 */\r\n.debtor-cell.clickable {\r\n  cursor: pointer;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n}\r\n\r\n.debtor-cell.clickable:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.debtor-name {\r\n  font-weight: 500;\r\n  color: #409eff;\r\n}\r\n\r\n.arrow-icon {\r\n  font-size: 12px;\r\n  opacity: 0.6;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.debtor-cell.clickable:hover .arrow-icon {\r\n  opacity: 1;\r\n  transform: translateX(2px);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.process-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.dialog-content {\r\n  padding: 0 8px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 32px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.section-title {\r\n  margin: 0 0 20px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.readonly-input,\r\n.readonly-textarea {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.status-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.status-radio {\r\n  padding: 12px 20px;\r\n  border: 2px solid #dcdfe6;\r\n  border-radius: 8px;\r\n  background-color: white;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.status-radio:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.status-radio.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.completion-section {\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.file-input {\r\n  flex: 1;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-textarea {\r\n  margin-top: 16px;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  padding: 10px 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .lawyer-letter-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 债务人详情右侧滑出面板 */\r\n.debtor-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debtor-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 800px;\r\n  height: 100%;\r\n  background-color: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-width: 90vw;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n/* 面板头部 */\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.header-info h3.panel-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-subtitle {\r\n  margin: 4px 0 0 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.close-btn {\r\n  color: white !important;\r\n  font-size: 18px;\r\n}\r\n\r\n.close-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1) !important;\r\n}\r\n\r\n/* 面板主体 */\r\n.panel-body {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧菜单 */\r\n.sidebar-menu {\r\n  width: 200px;\r\n  background-color: #f8f9fa;\r\n  border-right: 1px solid #ebeef5;\r\n  padding: 16px 0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.menu-item.active {\r\n  background-color: #409eff;\r\n  color: white;\r\n  position: relative;\r\n}\r\n\r\n.menu-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 20px;\r\n  background-color: white;\r\n}\r\n\r\n.menu-item i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 信息区块 */\r\n.info-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.info-section .section-title {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  min-height: 60px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n  padding-top: 2px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n  line-height: 1.5;\r\n}\r\n\r\n.debt-amount {\r\n  color: #f56c6c !important;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.user-avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.user-info h5 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  color: #303133;\r\n}\r\n\r\n.user-info p {\r\n  margin: 4px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表 */\r\n.file-list {\r\n  space-y: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.file-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 6px;\r\n  background-color: #409eff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-info h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-info p {\r\n  margin: 2px 0;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.file-size {\r\n  color: #409eff !important;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.empty-files,\r\n.empty-history {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-files i,\r\n.empty-history i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n/* 历史时间线 */\r\n.history-timeline {\r\n  position: relative;\r\n  padding-left: 20px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.timeline-item:not(:last-child)::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -15px;\r\n  top: 20px;\r\n  width: 2px;\r\n  height: calc(100% - 10px);\r\n  background-color: #e4e7ed;\r\n}\r\n\r\n.timeline-dot {\r\n  position: absolute;\r\n  left: -20px;\r\n  top: 5px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #409eff;\r\n  border: 2px solid white;\r\n  box-shadow: 0 0 0 2px #409eff;\r\n}\r\n\r\n.timeline-content {\r\n  background-color: #fafbfc;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.timeline-content h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.timeline-content p {\r\n  margin: 0 0 8px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .panel-content {\r\n    width: 700px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n    max-width: 100vw;\r\n  }\r\n\r\n  .panel-body {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    width: 100%;\r\n    display: flex;\r\n    overflow-x: auto;\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .menu-item {\r\n    white-space: nowrap;\r\n    min-width: 120px;\r\n    justify-content: center;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n/* AI生成功能样式 */\r\n.ai-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.ai-section .section-title {\r\n  color: white;\r\n  border-bottom-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.ai-generation-content {\r\n  padding: 16px 0;\r\n}\r\n\r\n.ai-description {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.ai-description p {\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.ai-actions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-generate-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  font-weight: 600;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ai-generate-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.ai-result {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.result-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: white;\r\n}\r\n\r\n.generated-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ai-content-textarea {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 6px;\r\n}\r\n\r\n.ai-content-textarea .el-textarea__inner {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: #333;\r\n  font-family: 'Microsoft YaHei', sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.ai-result-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.ai-result-actions .el-button {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.ai-result-actions .el-button:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 处理方式选择样式 */\r\n.process-method-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.method-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.method-radio {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border: 2px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  background: #fff;\r\n}\r\n\r\n.method-radio:hover {\r\n  border-color: #409eff;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.method-radio.is-checked {\r\n  border-color: #409eff;\r\n  background: #e6f7ff;\r\n  color: #409eff;\r\n}\r\n\r\n.method-radio i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* AI处理区域样式 */\r\n.ai-process-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-mode-alert {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-content-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-preview-textarea .el-textarea__inner {\r\n  background: #fff;\r\n  border: 1px solid #dcdfe6;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.ai-file-generation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.generate-file-btn {\r\n  border-radius: 6px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.generated-file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  color: #409eff;\r\n}\r\n\r\n.generated-file-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 手动上传区域样式 */\r\n.upload-process-section {\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n</style>\r\n"]}]}