{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=template&id=64ff9702&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748464417190}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "attrs", "type", "icon", "on", "click", "$event", "editData", "_v", "refulsh", "shadow", "placeholder", "clearable", "nativeOn", "keyup", "indexOf", "_k", "keyCode", "key", "searchData", "apply", "arguments", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "label", "status", "clearSearch", "viewMode", "size", "data", "treeData", "props", "treeProps", "scopedSlots", "_u", "fn", "node", "class", "getNodeIcon", "_s", "plain", "id", "<PERSON><PERSON><PERSON><PERSON>", "delData", "_e", "directives", "name", "rawName", "loading", "tableData", "stripe", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prop", "scope", "style", "paddingLeft", "row", "level", "staticStyle", "width", "align", "getTypeColor", "getTypeLabel", "change", "changeStatus", "fixed", "stopPropagation", "title", "dialogTitle", "visible", "dialogFormVisible", "update:visible", "ref", "ruleForm", "rules", "gutter", "span", "code", "options", "parentOptions", "cascaderProps", "parent_id", "min", "max", "sort", "rows", "description", "path", "saveData", "staticRenderFns", "_withStripped"], "sources": ["H:/fdbfront/xinqianduan/src/views/pages/yuangong/quanxian.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"permission-container\" },\n    [\n      _c(\"div\", { staticClass: \"page-header\" }, [\n        _c(\"div\", { staticClass: \"header-content\" }, [\n          _vm._m(0),\n          _c(\n            \"div\",\n            { staticClass: \"header-actions\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"add-btn\",\n                  attrs: { type: \"primary\", icon: \"el-icon-plus\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.editData(0)\n                    },\n                  },\n                },\n                [_vm._v(\" 新增权限 \")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"refresh-btn\",\n                  attrs: { icon: \"el-icon-refresh\" },\n                  on: { click: _vm.refulsh },\n                },\n                [_vm._v(\" 刷新 \")]\n              ),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"search-section\" },\n        [\n          _c(\n            \"el-card\",\n            { staticClass: \"search-card\", attrs: { shadow: \"never\" } },\n            [\n              _c(\"div\", { staticClass: \"search-form\" }, [\n                _c(\"div\", { staticClass: \"search-row\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"search-item\" },\n                    [\n                      _c(\"label\", { staticClass: \"search-label\" }, [\n                        _vm._v(\"权限搜索\"),\n                      ]),\n                      _c(\n                        \"el-input\",\n                        {\n                          staticClass: \"search-input\",\n                          attrs: {\n                            placeholder: \"请输入权限名称或描述\",\n                            clearable: \"\",\n                          },\n                          nativeOn: {\n                            keyup: function ($event) {\n                              if (\n                                !$event.type.indexOf(\"key\") &&\n                                _vm._k(\n                                  $event.keyCode,\n                                  \"enter\",\n                                  13,\n                                  $event.key,\n                                  \"Enter\"\n                                )\n                              )\n                                return null\n                              return _vm.searchData.apply(null, arguments)\n                            },\n                          },\n                          model: {\n                            value: _vm.search.keyword,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.search, \"keyword\", $$v)\n                            },\n                            expression: \"search.keyword\",\n                          },\n                        },\n                        [\n                          _c(\"i\", {\n                            staticClass: \"el-input__icon el-icon-search\",\n                            attrs: { slot: \"prefix\" },\n                            slot: \"prefix\",\n                          }),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"search-item\" },\n                    [\n                      _c(\"label\", { staticClass: \"search-label\" }, [\n                        _vm._v(\"权限类型\"),\n                      ]),\n                      _c(\n                        \"el-select\",\n                        {\n                          staticClass: \"search-select\",\n                          attrs: {\n                            placeholder: \"请选择权限类型\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.search.type,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.search, \"type\", $$v)\n                            },\n                            expression: \"search.type\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: { label: \"菜单权限\", value: \"menu\" },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"操作权限\", value: \"action\" },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"数据权限\", value: \"data\" },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"search-item\" },\n                    [\n                      _c(\"label\", { staticClass: \"search-label\" }, [\n                        _vm._v(\"状态\"),\n                      ]),\n                      _c(\n                        \"el-select\",\n                        {\n                          staticClass: \"search-select\",\n                          attrs: { placeholder: \"请选择状态\", clearable: \"\" },\n                          model: {\n                            value: _vm.search.status,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.search, \"status\", $$v)\n                            },\n                            expression: \"search.status\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: { label: \"启用\", value: 1 },\n                          }),\n                          _c(\"el-option\", {\n                            attrs: { label: \"禁用\", value: 0 },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"search-actions\" },\n                  [\n                    _c(\n                      \"el-button\",\n                      {\n                        attrs: { type: \"primary\", icon: \"el-icon-search\" },\n                        on: { click: _vm.searchData },\n                      },\n                      [_vm._v(\" 搜索 \")]\n                    ),\n                    _c(\n                      \"el-button\",\n                      {\n                        attrs: { icon: \"el-icon-refresh-left\" },\n                        on: { click: _vm.clearSearch },\n                      },\n                      [_vm._v(\" 重置 \")]\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"tree-section\" },\n        [\n          _c(\n            \"el-card\",\n            { staticClass: \"tree-card\", attrs: { shadow: \"never\" } },\n            [\n              _c(\"div\", { staticClass: \"tree-header\" }, [\n                _c(\"div\", { staticClass: \"tree-title\" }, [\n                  _c(\"i\", { staticClass: \"el-icon-menu\" }),\n                  _vm._v(\" 权限树形结构 \"),\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"tree-tools\" },\n                  [\n                    _c(\n                      \"el-button-group\",\n                      [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: {\n                              type: _vm.viewMode === \"tree\" ? \"primary\" : \"\",\n                              icon: \"el-icon-s-grid\",\n                              size: \"small\",\n                            },\n                            on: {\n                              click: function ($event) {\n                                _vm.viewMode = \"tree\"\n                              },\n                            },\n                          },\n                          [_vm._v(\" 树形视图 \")]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: {\n                              type: _vm.viewMode === \"table\" ? \"primary\" : \"\",\n                              icon: \"el-icon-menu\",\n                              size: \"small\",\n                            },\n                            on: {\n                              click: function ($event) {\n                                _vm.viewMode = \"table\"\n                              },\n                            },\n                          },\n                          [_vm._v(\" 列表视图 \")]\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n              _vm.viewMode === \"tree\"\n                ? _c(\n                    \"div\",\n                    { staticClass: \"tree-view\" },\n                    [\n                      _c(\"el-tree\", {\n                        staticClass: \"permission-tree\",\n                        attrs: {\n                          data: _vm.treeData,\n                          props: _vm.treeProps,\n                          \"default-expand-all\": true,\n                          \"node-key\": \"id\",\n                        },\n                        scopedSlots: _vm._u(\n                          [\n                            {\n                              key: \"default\",\n                              fn: function ({ node, data }) {\n                                return _c(\n                                  \"span\",\n                                  { staticClass: \"tree-node\" },\n                                  [\n                                    _c(\"div\", { staticClass: \"node-content\" }, [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"node-info\" },\n                                        [\n                                          _c(\"i\", {\n                                            class: _vm.getNodeIcon(data.type),\n                                          }),\n                                          _c(\n                                            \"span\",\n                                            { staticClass: \"node-label\" },\n                                            [_vm._v(_vm._s(data.label))]\n                                          ),\n                                          _c(\n                                            \"el-tag\",\n                                            {\n                                              staticClass: \"node-status\",\n                                              attrs: {\n                                                type:\n                                                  data.status === 1\n                                                    ? \"success\"\n                                                    : \"danger\",\n                                                size: \"mini\",\n                                              },\n                                            },\n                                            [\n                                              _vm._v(\n                                                \" \" +\n                                                  _vm._s(\n                                                    data.status === 1\n                                                      ? \"启用\"\n                                                      : \"禁用\"\n                                                  ) +\n                                                  \" \"\n                                              ),\n                                            ]\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"node-actions\" },\n                                        [\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"primary\",\n                                                size: \"mini\",\n                                                icon: \"el-icon-edit\",\n                                                plain: \"\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.editData(data.id)\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\" 编辑 \")]\n                                          ),\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"success\",\n                                                size: \"mini\",\n                                                icon: \"el-icon-plus\",\n                                                plain: \"\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.addChild(data)\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\" 添加子权限 \")]\n                                          ),\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"danger\",\n                                                size: \"mini\",\n                                                icon: \"el-icon-delete\",\n                                                plain: \"\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.delData(data.id)\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\" 删除 \")]\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                    ]),\n                                  ]\n                                )\n                              },\n                            },\n                          ],\n                          null,\n                          false,\n                          369117358\n                        ),\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.viewMode === \"table\"\n                ? _c(\n                    \"div\",\n                    { staticClass: \"table-view\" },\n                    [\n                      _c(\n                        \"el-table\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.loading,\n                              expression: \"loading\",\n                            },\n                          ],\n                          staticClass: \"permission-table\",\n                          attrs: {\n                            data: _vm.tableData,\n                            stripe: \"\",\n                            \"row-key\": \"id\",\n                            \"tree-props\": {\n                              children: \"children\",\n                              hasChildren: \"hasChildren\",\n                            },\n                            \"default-expand-all\": false,\n                          },\n                        },\n                        [\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"label\",\n                              label: \"权限名称\",\n                              \"min-width\": \"200\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"permission-name-cell\",\n                                          style: {\n                                            paddingLeft:\n                                              (scope.row.level || 0) * 20 +\n                                              \"px\",\n                                          },\n                                        },\n                                        [\n                                          _c(\"i\", {\n                                            class: _vm.getNodeIcon(\n                                              scope.row.type\n                                            ),\n                                            staticStyle: {\n                                              \"margin-right\": \"8px\",\n                                            },\n                                          }),\n                                          _c(\n                                            \"span\",\n                                            { staticClass: \"permission-name\" },\n                                            [_vm._v(_vm._s(scope.row.label))]\n                                          ),\n                                        ]\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              296214969\n                            ),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"code\",\n                              label: \"权限代码\",\n                              width: \"180\",\n                              align: \"center\",\n                              \"show-overflow-tooltip\": \"\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"type\",\n                              label: \"权限类型\",\n                              width: \"120\",\n                              align: \"center\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\n                                        \"el-tag\",\n                                        {\n                                          attrs: {\n                                            type: _vm.getTypeColor(\n                                              scope.row.type\n                                            ),\n                                            size: \"small\",\n                                          },\n                                        },\n                                        [\n                                          _vm._v(\n                                            \" \" +\n                                              _vm._s(\n                                                _vm.getTypeLabel(scope.row.type)\n                                              ) +\n                                              \" \"\n                                          ),\n                                        ]\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              417277375\n                            ),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"status\",\n                              label: \"状态\",\n                              width: \"100\",\n                              align: \"center\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\"el-switch\", {\n                                        attrs: {\n                                          \"active-value\": 1,\n                                          \"inactive-value\": 0,\n                                        },\n                                        on: {\n                                          change: function ($event) {\n                                            return _vm.changeStatus(scope.row)\n                                          },\n                                        },\n                                        model: {\n                                          value: scope.row.status,\n                                          callback: function ($$v) {\n                                            _vm.$set(scope.row, \"status\", $$v)\n                                          },\n                                          expression: \"scope.row.status\",\n                                        },\n                                      }),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              2880962836\n                            ),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"sort\",\n                              label: \"排序\",\n                              width: \"80\",\n                              align: \"center\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              prop: \"create_time\",\n                              label: \"创建时间\",\n                              width: \"160\",\n                              align: \"center\",\n                            },\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              fixed: \"right\",\n                              label: \"操作\",\n                              width: \"240\",\n                              align: \"center\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"action-buttons\" },\n                                        [\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"primary\",\n                                                size: \"mini\",\n                                                icon: \"el-icon-edit\",\n                                                plain: \"\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  $event.stopPropagation()\n                                                  return _vm.editData(\n                                                    scope.row.id\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\" 编辑 \")]\n                                          ),\n                                          scope.row.type === \"menu\"\n                                            ? _c(\n                                                \"el-button\",\n                                                {\n                                                  attrs: {\n                                                    type: \"success\",\n                                                    size: \"mini\",\n                                                    icon: \"el-icon-plus\",\n                                                    plain: \"\",\n                                                  },\n                                                  on: {\n                                                    click: function ($event) {\n                                                      $event.stopPropagation()\n                                                      return _vm.addChild(\n                                                        scope.row\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [_vm._v(\" 添加子权限 \")]\n                                              )\n                                            : _vm._e(),\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"danger\",\n                                                size: \"mini\",\n                                                icon: \"el-icon-delete\",\n                                                plain: \"\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  $event.stopPropagation()\n                                                  return _vm.delData(\n                                                    scope.row.id\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\" 删除 \")]\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              2647926959\n                            ),\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.dialogTitle,\n            visible: _vm.dialogFormVisible,\n            \"close-on-click-modal\": false,\n            width: \"60%\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogFormVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"ruleForm\",\n              attrs: {\n                model: _vm.ruleForm,\n                rules: _vm.rules,\n                \"label-width\": \"120px\",\n              },\n            },\n            [\n              _c(\n                \"el-row\",\n                { attrs: { gutter: 24 } },\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"权限名称\", prop: \"label\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入权限名称\" },\n                            model: {\n                              value: _vm.ruleForm.label,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.ruleForm, \"label\", $$v)\n                              },\n                              expression: \"ruleForm.label\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"权限代码\", prop: \"code\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入权限代码\" },\n                            model: {\n                              value: _vm.ruleForm.code,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.ruleForm, \"code\", $$v)\n                              },\n                              expression: \"ruleForm.code\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-row\",\n                { attrs: { gutter: 24 } },\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"权限类型\", prop: \"type\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticStyle: { width: \"100%\" },\n                              attrs: { placeholder: \"请选择权限类型\" },\n                              model: {\n                                value: _vm.ruleForm.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.ruleForm, \"type\", $$v)\n                                },\n                                expression: \"ruleForm.type\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"菜单权限\", value: \"menu\" },\n                              }),\n                              _c(\"el-option\", {\n                                attrs: { label: \"操作权限\", value: \"action\" },\n                              }),\n                              _c(\"el-option\", {\n                                attrs: { label: \"数据权限\", value: \"data\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"父级权限\" } },\n                        [\n                          _c(\"el-cascader\", {\n                            staticStyle: { width: \"100%\" },\n                            attrs: {\n                              options: _vm.parentOptions,\n                              props: _vm.cascaderProps,\n                              placeholder: \"请选择父级权限\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.ruleForm.parent_id,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.ruleForm, \"parent_id\", $$v)\n                              },\n                              expression: \"ruleForm.parent_id\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-row\",\n                { attrs: { gutter: 24 } },\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"排序\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            staticStyle: { width: \"100%\" },\n                            attrs: { min: 0, max: 999 },\n                            model: {\n                              value: _vm.ruleForm.sort,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.ruleForm, \"sort\", $$v)\n                              },\n                              expression: \"ruleForm.sort\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"状态\" } },\n                        [\n                          _c(\"el-switch\", {\n                            attrs: {\n                              \"active-value\": 1,\n                              \"inactive-value\": 0,\n                              \"active-text\": \"启用\",\n                              \"inactive-text\": \"禁用\",\n                            },\n                            model: {\n                              value: _vm.ruleForm.status,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.ruleForm, \"status\", $$v)\n                              },\n                              expression: \"ruleForm.status\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"权限描述\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: {\n                      type: \"textarea\",\n                      rows: 3,\n                      placeholder: \"请输入权限描述\",\n                    },\n                    model: {\n                      value: _vm.ruleForm.description,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.ruleForm, \"description\", $$v)\n                      },\n                      expression: \"ruleForm.description\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm.ruleForm.type === \"menu\"\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"路由路径\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入路由路径\" },\n                        model: {\n                          value: _vm.ruleForm.path,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.ruleForm, \"path\", $$v)\n                          },\n                          expression: \"ruleForm.path\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.ruleForm.type === \"menu\"\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"图标\" } },\n                    [\n                      _c(\n                        \"el-input\",\n                        {\n                          attrs: { placeholder: \"请输入图标类名\" },\n                          model: {\n                            value: _vm.ruleForm.icon,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.ruleForm, \"icon\", $$v)\n                            },\n                            expression: \"ruleForm.icon\",\n                          },\n                        },\n                        [\n                          _c(\"template\", { slot: \"prepend\" }, [\n                            _c(\"i\", {\n                              class: _vm.ruleForm.icon || \"el-icon-menu\",\n                            }),\n                          ]),\n                        ],\n                        2\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.dialogFormVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"取 消\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.saveData()\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"title-section\" }, [\n      _c(\"h2\", { staticClass: \"page-title\" }, [\n        _c(\"i\", { staticClass: \"el-icon-key\" }),\n        _vm._v(\" 权限管理 \"),\n      ]),\n      _c(\"p\", { staticClass: \"page-subtitle\" }, [\n        _vm._v(\"管理系统功能权限和访问控制\"),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,SAAS;IACtBE,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAe,CAAC;IAChDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACW,QAAQ,CAAC,CAAC,CAAC;MACxB;IACF;EACF,CAAC,EACD,CAACX,GAAG,CAACY,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAkB,CAAC;IAClCC,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACa;IAAQ;EAC3B,CAAC,EACD,CAACb,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,EACFX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE,aAAa;IAAEE,KAAK,EAAE;MAAES,MAAM,EAAE;IAAQ;EAAE,CAAC,EAC1D,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,OAAO,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC3CH,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFX,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,cAAc;IAC3BE,KAAK,EAAE;MACLU,WAAW,EAAE,YAAY;MACzBC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE,SAAAA,CAAUR,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACJ,IAAI,CAACa,OAAO,CAAC,KAAK,CAAC,IAC3BnB,GAAG,CAACoB,EAAE,CACJV,MAAM,CAACW,OAAO,EACd,OAAO,EACP,EAAE,EACFX,MAAM,CAACY,GAAG,EACV,OACF,CAAC,EAED,OAAO,IAAI;QACb,OAAOtB,GAAG,CAACuB,UAAU,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C;IACF,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAAC4B,MAAM,CAACC,OAAO;MACzBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAAC4B,MAAM,EAAE,SAAS,EAAEG,GAAG,CAAC;MACtC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,+BAA+B;IAC5CE,KAAK,EAAE;MAAE6B,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,EACDjC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,OAAO,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC3CH,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFX,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MACLU,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE;IACb,CAAC;IACDU,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAAC4B,MAAM,CAACtB,IAAI;MACtBwB,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAAC4B,MAAM,EAAE,MAAM,EAAEG,GAAG,CAAC;MACnC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAO;EACxC,CAAC,CAAC,EACF1B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAS;EAC1C,CAAC,CAAC,EACF1B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAO;EACxC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,OAAO,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC3CH,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFX,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MAAEU,WAAW,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAG,CAAC;IAC9CU,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAAC4B,MAAM,CAACQ,MAAM;MACxBN,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAAC4B,MAAM,EAAE,QAAQ,EAAEG,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,IAAI;MAAER,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,EACF1B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,IAAI;MAAER,KAAK,EAAE;IAAE;EACjC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAiB,CAAC;IAClDC,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACuB;IAAW;EAC9B,CAAC,EACD,CAACvB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAuB,CAAC;IACvCC,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACqC;IAAY;EAC/B,CAAC,EACD,CAACrC,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE,WAAW;IAAEE,KAAK,EAAE;MAAES,MAAM,EAAE;IAAQ;EAAE,CAAC,EACxD,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,GAAG,CAACY,EAAE,CAAC,UAAU,CAAC,CACnB,CAAC,EACFX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CACA,iBAAiB,EACjB,CACEA,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACLC,IAAI,EAAEN,GAAG,CAACsC,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,EAAE;MAC9C/B,IAAI,EAAE,gBAAgB;MACtBgC,IAAI,EAAE;IACR,CAAC;IACD/B,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBV,GAAG,CAACsC,QAAQ,GAAG,MAAM;MACvB;IACF;EACF,CAAC,EACD,CAACtC,GAAG,CAACY,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACLC,IAAI,EAAEN,GAAG,CAACsC,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,EAAE;MAC/C/B,IAAI,EAAE,cAAc;MACpBgC,IAAI,EAAE;IACR,CAAC;IACD/B,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBV,GAAG,CAACsC,QAAQ,GAAG,OAAO;MACxB;IACF;EACF,CAAC,EACD,CAACtC,GAAG,CAACY,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFZ,GAAG,CAACsC,QAAQ,KAAK,MAAM,GACnBrC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,SAAS,EAAE;IACZE,WAAW,EAAE,iBAAiB;IAC9BE,KAAK,EAAE;MACLmC,IAAI,EAAExC,GAAG,CAACyC,QAAQ;MAClBC,KAAK,EAAE1C,GAAG,CAAC2C,SAAS;MACpB,oBAAoB,EAAE,IAAI;MAC1B,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE5C,GAAG,CAAC6C,EAAE,CACjB,CACE;MACEvB,GAAG,EAAE,SAAS;MACdwB,EAAE,EAAE,SAAAA,CAAU;QAAEC,IAAI;QAAEP;MAAK,CAAC,EAAE;QAC5B,OAAOvC,EAAE,CACP,MAAM,EACN;UAAEE,WAAW,EAAE;QAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;UAAEE,WAAW,EAAE;QAAe,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,GAAG,EAAE;UACN+C,KAAK,EAAEhD,GAAG,CAACiD,WAAW,CAACT,IAAI,CAAClC,IAAI;QAClC,CAAC,CAAC,EACFL,EAAE,CACA,MAAM,EACN;UAAEE,WAAW,EAAE;QAAa,CAAC,EAC7B,CAACH,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACkD,EAAE,CAACV,IAAI,CAACL,KAAK,CAAC,CAAC,CAC7B,CAAC,EACDlC,EAAE,CACA,QAAQ,EACR;UACEE,WAAW,EAAE,aAAa;UAC1BE,KAAK,EAAE;YACLC,IAAI,EACFkC,IAAI,CAACJ,MAAM,KAAK,CAAC,GACb,SAAS,GACT,QAAQ;YACdG,IAAI,EAAE;UACR;QACF,CAAC,EACD,CACEvC,GAAG,CAACY,EAAE,CACJ,GAAG,GACDZ,GAAG,CAACkD,EAAE,CACJV,IAAI,CAACJ,MAAM,KAAK,CAAC,GACb,IAAI,GACJ,IACN,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDnC,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,SAAS;YACfiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,cAAc;YACpB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAOV,GAAG,CAACW,QAAQ,CAAC6B,IAAI,CAACY,EAAE,CAAC;YAC9B;UACF;QACF,CAAC,EACD,CAACpD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,SAAS;YACfiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,cAAc;YACpB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAOV,GAAG,CAACqD,QAAQ,CAACb,IAAI,CAAC;YAC3B;UACF;QACF,CAAC,EACD,CAACxC,GAAG,CAACY,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,QAAQ;YACdiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,gBAAgB;YACtB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAOV,GAAG,CAACsD,OAAO,CAACd,IAAI,CAACY,EAAE,CAAC;YAC7B;UACF;QACF,CAAC,EACD,CAACpD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,SACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDZ,GAAG,CAACuD,EAAE,CAAC,CAAC,EACZvD,GAAG,CAACsC,QAAQ,KAAK,OAAO,GACpBrC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CACA,UAAU,EACV;IACEuD,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpB/B,KAAK,EAAE3B,GAAG,CAAC2D,OAAO;MAClB1B,UAAU,EAAE;IACd,CAAC,CACF;IACD9B,WAAW,EAAE,kBAAkB;IAC/BE,KAAK,EAAE;MACLmC,IAAI,EAAExC,GAAG,CAAC4D,SAAS;MACnBC,MAAM,EAAE,EAAE;MACV,SAAS,EAAE,IAAI;MACf,YAAY,EAAE;QACZC,QAAQ,EAAE,UAAU;QACpBC,WAAW,EAAE;MACf,CAAC;MACD,oBAAoB,EAAE;IACxB;EACF,CAAC,EACD,CACE9D,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,OAAO;MACb7B,KAAK,EAAE,MAAM;MACb,WAAW,EAAE;IACf,CAAC;IACDS,WAAW,EAAE5C,GAAG,CAAC6C,EAAE,CACjB,CACE;MACEvB,GAAG,EAAE,SAAS;MACdwB,EAAE,EAAE,SAAAA,CAAUmB,KAAK,EAAE;QACnB,OAAO,CACLhE,EAAE,CACA,KAAK,EACL;UACEE,WAAW,EAAE,sBAAsB;UACnC+D,KAAK,EAAE;YACLC,WAAW,EACT,CAACF,KAAK,CAACG,GAAG,CAACC,KAAK,IAAI,CAAC,IAAI,EAAE,GAC3B;UACJ;QACF,CAAC,EACD,CACEpE,EAAE,CAAC,GAAG,EAAE;UACN+C,KAAK,EAAEhD,GAAG,CAACiD,WAAW,CACpBgB,KAAK,CAACG,GAAG,CAAC9D,IACZ,CAAC;UACDgE,WAAW,EAAE;YACX,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,EACFrE,EAAE,CACA,MAAM,EACN;UAAEE,WAAW,EAAE;QAAkB,CAAC,EAClC,CAACH,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACkD,EAAE,CAACe,KAAK,CAACG,GAAG,CAACjC,KAAK,CAAC,CAAC,CAClC,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,SACF;EACF,CAAC,CAAC,EACFlC,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,MAAM;MACZ7B,KAAK,EAAE,MAAM;MACboC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,QAAQ;MACf,uBAAuB,EAAE;IAC3B;EACF,CAAC,CAAC,EACFvE,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,MAAM;MACZ7B,KAAK,EAAE,MAAM;MACboC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACD5B,WAAW,EAAE5C,GAAG,CAAC6C,EAAE,CACjB,CACE;MACEvB,GAAG,EAAE,SAAS;MACdwB,EAAE,EAAE,SAAAA,CAAUmB,KAAK,EAAE;QACnB,OAAO,CACLhE,EAAE,CACA,QAAQ,EACR;UACEI,KAAK,EAAE;YACLC,IAAI,EAAEN,GAAG,CAACyE,YAAY,CACpBR,KAAK,CAACG,GAAG,CAAC9D,IACZ,CAAC;YACDiC,IAAI,EAAE;UACR;QACF,CAAC,EACD,CACEvC,GAAG,CAACY,EAAE,CACJ,GAAG,GACDZ,GAAG,CAACkD,EAAE,CACJlD,GAAG,CAAC0E,YAAY,CAACT,KAAK,CAACG,GAAG,CAAC9D,IAAI,CACjC,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,SACF;EACF,CAAC,CAAC,EACFL,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,QAAQ;MACd7B,KAAK,EAAE,IAAI;MACXoC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACD5B,WAAW,EAAE5C,GAAG,CAAC6C,EAAE,CACjB,CACE;MACEvB,GAAG,EAAE,SAAS;MACdwB,EAAE,EAAE,SAAAA,CAAUmB,KAAK,EAAE;QACnB,OAAO,CACLhE,EAAE,CAAC,WAAW,EAAE;UACdI,KAAK,EAAE;YACL,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE;UACpB,CAAC;UACDG,EAAE,EAAE;YACFmE,MAAM,EAAE,SAAAA,CAAUjE,MAAM,EAAE;cACxB,OAAOV,GAAG,CAAC4E,YAAY,CAACX,KAAK,CAACG,GAAG,CAAC;YACpC;UACF,CAAC;UACD1C,KAAK,EAAE;YACLC,KAAK,EAAEsC,KAAK,CAACG,GAAG,CAAChC,MAAM;YACvBN,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;cACvB/B,GAAG,CAACgC,IAAI,CAACiC,KAAK,CAACG,GAAG,EAAE,QAAQ,EAAErC,GAAG,CAAC;YACpC,CAAC;YACDE,UAAU,EAAE;UACd;QACF,CAAC,CAAC,CACH;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,EACFhC,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,MAAM;MACZ7B,KAAK,EAAE,IAAI;MACXoC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFvE,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL2D,IAAI,EAAE,aAAa;MACnB7B,KAAK,EAAE,MAAM;MACboC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFvE,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLwE,KAAK,EAAE,OAAO;MACd1C,KAAK,EAAE,IAAI;MACXoC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;IACT,CAAC;IACD5B,WAAW,EAAE5C,GAAG,CAAC6C,EAAE,CACjB,CACE;MACEvB,GAAG,EAAE,SAAS;MACdwB,EAAE,EAAE,SAAAA,CAAUmB,KAAK,EAAE;QACnB,OAAO,CACLhE,EAAE,CACA,KAAK,EACL;UAAEE,WAAW,EAAE;QAAiB,CAAC,EACjC,CACEF,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,SAAS;YACfiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,cAAc;YACpB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvBA,MAAM,CAACoE,eAAe,CAAC,CAAC;cACxB,OAAO9E,GAAG,CAACW,QAAQ,CACjBsD,KAAK,CAACG,GAAG,CAAChB,EACZ,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDqD,KAAK,CAACG,GAAG,CAAC9D,IAAI,KAAK,MAAM,GACrBL,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,SAAS;YACfiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,cAAc;YACpB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvBA,MAAM,CAACoE,eAAe,CAAC,CAAC;cACxB,OAAO9E,GAAG,CAACqD,QAAQ,CACjBY,KAAK,CAACG,GACR,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpE,GAAG,CAACY,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,GACDZ,GAAG,CAACuD,EAAE,CAAC,CAAC,EACZtD,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLC,IAAI,EAAE,QAAQ;YACdiC,IAAI,EAAE,MAAM;YACZhC,IAAI,EAAE,gBAAgB;YACtB4C,KAAK,EAAE;UACT,CAAC;UACD3C,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvBA,MAAM,CAACoE,eAAe,CAAC,CAAC;cACxB,OAAO9E,GAAG,CAACsD,OAAO,CAChBW,KAAK,CAACG,GAAG,CAAChB,EACZ,CAAC;YACH;UACF;QACF,CAAC,EACD,CAACpD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDZ,GAAG,CAACuD,EAAE,CAAC,CAAC,CAEhB,CAAC,CACF,EACD,CACF,CAAC,EACDtD,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACL0E,KAAK,EAAE/E,GAAG,CAACgF,WAAW;MACtBC,OAAO,EAAEjF,GAAG,CAACkF,iBAAiB;MAC9B,sBAAsB,EAAE,KAAK;MAC7BX,KAAK,EAAE;IACT,CAAC;IACD/D,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAA2E,CAAUzE,MAAM,EAAE;QAClCV,GAAG,CAACkF,iBAAiB,GAAGxE,MAAM;MAChC;IACF;EACF,CAAC,EACD,CACET,EAAE,CACA,SAAS,EACT;IACEmF,GAAG,EAAE,UAAU;IACf/E,KAAK,EAAE;MACLqB,KAAK,EAAE1B,GAAG,CAACqF,QAAQ;MACnBC,KAAK,EAAEtF,GAAG,CAACsF,KAAK;MAChB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACErF,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEkF,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEtF,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAE6B,IAAI,EAAE;IAAQ;EAAE,CAAC,EAC3C,CACE/D,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MAAEU,WAAW,EAAE;IAAU,CAAC;IACjCW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAAClD,KAAK;MACzBL,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,OAAO,EAAEtD,GAAG,CAAC;MACtC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAE6B,IAAI,EAAE;IAAO;EAAE,CAAC,EAC1C,CACE/D,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MAAEU,WAAW,EAAE;IAAU,CAAC;IACjCW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACI,IAAI;MACxB3D,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,MAAM,EAAEtD,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEkF,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEtF,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAE6B,IAAI,EAAE;IAAO;EAAE,CAAC,EAC1C,CACE/D,EAAE,CACA,WAAW,EACX;IACEqE,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BlE,KAAK,EAAE;MAAEU,WAAW,EAAE;IAAU,CAAC;IACjCW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAAC/E,IAAI;MACxBwB,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,MAAM,EAAEtD,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAO;EACxC,CAAC,CAAC,EACF1B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAS;EAC1C,CAAC,CAAC,EACF1B,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MAAE8B,KAAK,EAAE,MAAM;MAAER,KAAK,EAAE;IAAO;EACxC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD1B,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACElC,EAAE,CAAC,aAAa,EAAE;IAChBqE,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BlE,KAAK,EAAE;MACLqF,OAAO,EAAE1F,GAAG,CAAC2F,aAAa;MAC1BjD,KAAK,EAAE1C,GAAG,CAAC4F,aAAa;MACxB7E,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE;IACb,CAAC;IACDU,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACQ,SAAS;MAC7B/D,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,WAAW,EAAEtD,GAAG,CAAC;MAC1C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEkF,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEtF,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACElC,EAAE,CAAC,iBAAiB,EAAE;IACpBqE,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BlE,KAAK,EAAE;MAAEyF,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAI,CAAC;IAC3BrE,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACW,IAAI;MACxBlE,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,MAAM,EAAEtD,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEmF,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEvF,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACElC,EAAE,CAAC,WAAW,EAAE;IACdI,KAAK,EAAE;MACL,cAAc,EAAE,CAAC;MACjB,gBAAgB,EAAE,CAAC;MACnB,aAAa,EAAE,IAAI;MACnB,eAAe,EAAE;IACnB,CAAC;IACDqB,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACjD,MAAM;MAC1BN,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,QAAQ,EAAEtD,GAAG,CAAC;MACvC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACElC,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLC,IAAI,EAAE,UAAU;MAChB2F,IAAI,EAAE,CAAC;MACPlF,WAAW,EAAE;IACf,CAAC;IACDW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACa,WAAW;MAC/BpE,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,aAAa,EAAEtD,GAAG,CAAC;MAC5C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDjC,GAAG,CAACqF,QAAQ,CAAC/E,IAAI,KAAK,MAAM,GACxBL,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACElC,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MAAEU,WAAW,EAAE;IAAU,CAAC;IACjCW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAACc,IAAI;MACxBrE,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,MAAM,EAAEtD,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDjC,GAAG,CAACuD,EAAE,CAAC,CAAC,EACZvD,GAAG,CAACqF,QAAQ,CAAC/E,IAAI,KAAK,MAAM,GACxBL,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAE8B,KAAK,EAAE;IAAK;EAAE,CAAC,EAC1B,CACElC,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MAAEU,WAAW,EAAE;IAAU,CAAC;IACjCW,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAACqF,QAAQ,CAAC9E,IAAI;MACxBuB,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB/B,GAAG,CAACgC,IAAI,CAAChC,GAAG,CAACqF,QAAQ,EAAE,MAAM,EAAEtD,GAAG,CAAC;MACrC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,UAAU,EAAE;IAAEiC,IAAI,EAAE;EAAU,CAAC,EAAE,CAClCjC,EAAE,CAAC,GAAG,EAAE;IACN+C,KAAK,EAAEhD,GAAG,CAACqF,QAAQ,CAAC9E,IAAI,IAAI;EAC9B,CAAC,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDP,GAAG,CAACuD,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDtD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BE,KAAK,EAAE;MAAE6B,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACEjC,EAAE,CACA,WAAW,EACX;IACEO,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBV,GAAG,CAACkF,iBAAiB,GAAG,KAAK;MAC/B;IACF;EACF,CAAC,EACD,CAAClF,GAAG,CAACY,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC1BE,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACoG,QAAQ,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EACD,CAACpG,GAAG,CAACY,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIyF,eAAe,GAAG,CACpB,YAAY;EACV,IAAIrG,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACjDF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACtCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,EACvCH,GAAG,CAACY,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFX,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACxCH,GAAG,CAACY,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDb,MAAM,CAACuG,aAAa,GAAG,IAAI;AAE3B,SAASvG,MAAM,EAAEsG,eAAe", "ignoreList": []}]}