{"map": "{\"version\":3,\"sources\":[\"js/chunk-a540ec18.5b70960c.js\"],\"names\":[\"window\",\"push\",\"38ad\",\"module\",\"exports\",\"__webpack_require__\",\"a4c8\",\"__webpack_exports__\",\"d625\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"attrs\",\"shadow\",\"staticStyle\",\"width\",\"placeholder\",\"size\",\"allSize\",\"model\",\"value\",\"search\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"slot\",\"icon\",\"on\",\"click\",\"$event\",\"getData\",\"staticClass\",\"type\",\"editData\",\"_v\",\"directives\",\"name\",\"rawName\",\"loading\",\"data\",\"tableData\",\"prop\",\"label\",\"fixed\",\"scopedSlots\",\"_u\",\"key\",\"fn\",\"scope\",\"row\",\"id\",\"nativeOn\",\"preventDefault\",\"delData\",\"$index\",\"page-sizes\",\"page-size\",\"layout\",\"total\",\"size-change\",\"handleSizeChange\",\"current-change\",\"handleCurrentChange\",\"title\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"ref\",\"ruleForm\",\"rules\",\"label-width\",\"formLabelWidth\",\"autocomplete\",\"price\",\"year\",\"good\",\"_l\",\"types\",\"item\",\"index\",\"display\",\"span\",\"_s\",\"is_num\",\"min\",\"max\",\"_e\",\"desc\",\"sort\",\"saveData\",\"staticRenderFns\",\"taocanvue_type_script_lang_js\",\"components\",\"[object Object]\",\"page\",\"num\",\"required\",\"message\",\"trigger\",\"url\",\"methods\",\"_this\",\"getInfo\",\"getTypes\",\"getRequest\",\"then\",\"resp\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"deleteRequest\",\"code\",\"$message\",\"splice\",\"catch\",\"postRequest\",\"count\",\"val\",\"forEach\",\"element\",\"length\",\"$refs\",\"validate\",\"valid\",\"msg\",\"taocan_taocanvue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,KAM3BC,KACA,SAAUH,EAAQI,EAAqBF,GAE7C,aAC+cA,EAAoB,SAO7dG,KACA,SAAUL,EAAQI,EAAqBF,GAE7C,aAEAA,EAAoBI,EAAEF,GAGtB,IAAIG,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CAACA,EAAG,UAAW,CAC9BE,MAAO,CACLC,OAAU,WAEX,CAACH,EAAG,SAAU,CACfI,YAAa,CACXC,MAAS,UAEV,CAACL,EAAG,WAAY,CACjBE,MAAO,CACLI,YAAe,QACfC,KAAQT,EAAIU,SAEdC,MAAO,CACLC,MAAOZ,EAAIa,OAAOC,QAClBC,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAIa,OAAQ,UAAWG,IAElCE,WAAY,mBAEb,CAAChB,EAAG,YAAa,CAClBE,MAAO,CACLe,KAAQ,SACRC,KAAQ,kBAEVC,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOvB,EAAIwB,YAGfL,KAAM,YACH,IAAK,GAAIjB,EAAG,SAAU,CACzBuB,YAAa,YACZ,CAACvB,EAAG,YAAa,CAClBE,MAAO,CACLsB,KAAQ,UACRjB,KAAQT,EAAIU,SAEdW,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOvB,EAAI2B,SAAS,MAGvB,CAAC3B,EAAI4B,GAAG,QAAS1B,EAAG,YAAa,CAClCE,MAAO,CACLsB,KAAQ,UACRjB,KAAQT,EAAIU,SAEdW,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOvB,EAAIwB,aAGd,CAACxB,EAAI4B,GAAG,SAAU,GAAI1B,EAAG,WAAY,CACtC2B,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTnB,MAAOZ,EAAIgC,QACXd,WAAY,YAEdO,YAAa,QACbrB,MAAO,CACL6B,KAAQjC,EAAIkC,UACZzB,KAAQT,EAAIU,UAEb,CAACR,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,QACRC,MAAS,QAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,OACRC,MAAS,QAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,QACRC,MAAS,QAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,OACRC,MAAS,QAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,OACRC,MAAS,QAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACL+B,KAAQ,cACRC,MAAS,UAETlC,EAAG,kBAAmB,CACxBE,MAAO,CACLiC,MAAS,QACTD,MAAS,MAEXE,YAAatC,EAAIuC,GAAG,CAAC,CACnBC,IAAK,UACLC,GAAI,SAAUC,GACZ,MAAO,CAACxC,EAAG,YAAa,CACtBE,MAAO,CACLsB,KAAQ,OACRjB,KAAQ,SAEVY,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOvB,EAAI2B,SAASe,EAAMC,IAAIC,OAGjC,CAAC5C,EAAI4B,GAAG,QAAS1B,EAAG,YAAa,CAClCE,MAAO,CACLsB,KAAQ,OACRjB,KAAQ,SAEVoC,SAAU,CACRvB,MAAS,SAAUC,GAEjB,OADAA,EAAOuB,iBACA9C,EAAI+C,QAAQL,EAAMM,OAAQN,EAAMC,IAAIC,OAG9C,CAAC5C,EAAI4B,GAAG,kBAGZ,GAAI1B,EAAG,MAAO,CACjBuB,YAAa,YACZ,CAACvB,EAAG,gBAAiB,CACtBE,MAAO,CACL6C,aAAc,CAAC,GAAI,IAAK,IAAK,IAAK,KAClCC,YAAalD,EAAIS,KACjB0C,OAAU,0CACVC,MAASpD,EAAIoD,OAEf/B,GAAI,CACFgC,cAAerD,EAAIsD,iBACnBC,iBAAkBvD,EAAIwD,wBAErB,IAAK,GAAItD,EAAG,YAAa,CAC5BE,MAAO,CACLqD,MAAS,OACTC,QAAW1D,EAAI2D,kBACfC,wBAAwB,GAE1BvC,GAAI,CACFwC,iBAAkB,SAAUtC,GAC1BvB,EAAI2D,kBAAoBpC,KAG3B,CAACrB,EAAG,UAAW,CAChB4D,IAAK,WACL1D,MAAO,CACLO,MAASX,EAAI+D,SACbC,MAAShE,EAAIgE,QAEd,CAAC9D,EAAG,eAAgB,CACrBE,MAAO,CACLgC,MAAS,OACT6B,cAAejE,EAAIkE,eACnB/B,KAAQ,UAET,CAACjC,EAAG,WAAY,CACjBE,MAAO,CACL+D,aAAgB,OAElBxD,MAAO,CACLC,MAAOZ,EAAI+D,SAASN,MACpB1C,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,QAAS/C,IAElCE,WAAY,qBAEX,GAAIhB,EAAG,eAAgB,CAC1BE,MAAO,CACLgC,MAAS,OACT6B,cAAejE,EAAIkE,eACnB/B,KAAQ,UAET,CAACjC,EAAG,WAAY,CACjBE,MAAO,CACL+D,aAAgB,MAChBzC,KAAQ,UAEVf,MAAO,CACLC,MAAOZ,EAAI+D,SAASK,MACpBrD,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,QAAS/C,IAElCE,WAAY,qBAEX,GAAIhB,EAAG,eAAgB,CAC1BE,MAAO,CACLgC,MAAS,KACT6B,cAAejE,EAAIkE,eACnB/B,KAAQ,SAET,CAACjC,EAAG,WAAY,CACjBE,MAAO,CACL+D,aAAgB,MAChBzC,KAAQ,UAEVf,MAAO,CACLC,MAAOZ,EAAI+D,SAASM,KACpBtD,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,OAAQ/C,IAEjCE,WAAY,oBAEX,GAAIhB,EAAG,eAAgB,CAC1BE,MAAO,CACLgC,MAAS,OACT6B,cAAejE,EAAIkE,eACnB/B,KAAQ,SAET,CAACjC,EAAG,oBAAqB,CAC1BS,MAAO,CACLC,MAAOZ,EAAI+D,SAASO,KACpBvD,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,OAAQ/C,IAEjCE,WAAY,kBAEblB,EAAIuE,GAAGvE,EAAIwE,OAAO,SAAUC,EAAMC,GACnC,OAAOxE,EAAG,SAAU,CAClBsC,IAAKkC,EACLpE,YAAa,CACXqE,QAAW,SAEZ,CAACzE,EAAG,SAAU,CACfE,MAAO,CACLwE,KAAQ,KAET,CAAC1E,EAAG,cAAe,CACpBE,MAAO,CACLgC,MAASqC,EAAK7B,KAEf,CAAC5C,EAAI4B,GAAG,IAAM5B,EAAI6E,GAAGJ,EAAKhB,OAAS,QAAS,GAAIvD,EAAG,SAAU,CAC9DE,MAAO,CACLwE,KAAQ,IAET,CAAgB,GAAfH,EAAKK,OAAc5E,EAAG,kBAAmB,CAC3CE,MAAO,CACL2E,IAAO,EACPC,IAAO,IACPvE,KAAQ,OACR2B,MAAS,QAEXzB,MAAO,CACLC,MAAO6D,EAAK7D,MACZG,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKwD,EAAM,QAASzD,IAE1BE,WAAY,gBAEXlB,EAAIiF,MAAO,IAAK,MACnB,IAAK,GAAI/E,EAAG,eAAgB,CAC9BE,MAAO,CACLgC,MAAS,OACT6B,cAAejE,EAAIkE,iBAEpB,CAAChE,EAAG,WAAY,CACjBE,MAAO,CACL+D,aAAgB,OAElBxD,MAAO,CACLC,MAAOZ,EAAI+D,SAASmB,KACpBnE,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,OAAQ/C,IAEjCE,WAAY,oBAEX,GAAIhB,EAAG,eAAgB,CAC1BE,MAAO,CACLgC,MAAS,KACT6B,cAAejE,EAAIkE,iBAEpB,CAAChE,EAAG,WAAY,CACjBE,MAAO,CACL+D,aAAgB,MAChBzC,KAAQ,UAEVf,MAAO,CACLC,MAAOZ,EAAI+D,SAASoB,KACpBpE,SAAU,SAAUC,GAClBhB,EAAIiB,KAAKjB,EAAI+D,SAAU,OAAQ/C,IAEjCE,WAAY,oBAEX,IAAK,GAAIhB,EAAG,MAAO,CACtBuB,YAAa,gBACbrB,MAAO,CACLe,KAAQ,UAEVA,KAAM,UACL,CAACjB,EAAG,YAAa,CAClBmB,GAAI,CACFC,MAAS,SAAUC,GACjBvB,EAAI2D,mBAAoB,KAG3B,CAAC3D,EAAI4B,GAAG,SAAU1B,EAAG,YAAa,CACnCE,MAAO,CACLsB,KAAQ,WAEVL,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOvB,EAAIoF,cAGd,CAACpF,EAAI4B,GAAG,UAAW,IAAK,IAAK,IAE9ByD,EAAkB,GAWWC,GANb5F,EAAoB,QAMyB,CAC/DoC,KAAM,OACNyD,WAAY,GACZC,OACE,MAAO,CACL9E,QAAS,OACTwB,UAAW,GACXF,SAAS,EACToB,MAAO,EACPqC,KAAM,EACNhF,KAAM,GACNI,OAAQ,CACNC,QAAS,IAEXiD,SAAU,CACRN,MAAO,GACPW,MAAO,GACPC,KAAM,GACNa,KAAM,GACNC,KAAM,EACNb,KAAM,GACNoB,IAAK,IAEPA,IAAK,EACL1B,MAAO,CACLP,MAAO,CAAC,CACNkC,UAAU,EACVC,QAAS,QACTC,QAAS,SAEXzB,MAAO,CAAC,CACNuB,UAAU,EACVC,QAAS,QACTC,QAAS,SAEXxB,KAAM,CAAC,CACLsB,UAAU,EACVC,QAAS,QACTC,QAAS,UAGblC,mBAAmB,EACnBO,eAAgB,OAChB4B,IAAK,WACLtB,MAAO,KAGXgB,UACEvF,KAAKuB,WAEPuE,QAAS,CACPP,SAAS5C,GACP,IAAIoD,EAAQ/F,KACF,GAAN2C,EACF3C,KAAKgG,QAAQrD,IAEb3C,KAAK8D,SAAW,CACdN,MAAO,GACPW,MAAO,GACPC,KAAM,GACNa,KAAM,GACNC,KAAM,EACNb,KAAM,GACNoB,IAAK,IAEPM,EAAME,YAERF,EAAMrC,mBAAoB,GAE5B6B,QAAQ5C,GACN,IAAIoD,EAAQ/F,KACZ+F,EAAMG,WAAWH,EAAMF,IAAM,WAAalD,GAAIwD,KAAKC,IAC7CA,IACFL,EAAMjC,SAAWsC,EAAKpE,KACtB+D,EAAMxB,MAAQwB,EAAMjC,SAAS2B,QAInCF,QAAQd,EAAO9B,GACb3C,KAAKqG,SAAS,WAAY,KAAM,CAC9BC,kBAAmB,KACnBC,iBAAkB,KAClB9E,KAAM,YACL0E,KAAK,KACNnG,KAAKwG,cAAcxG,KAAK6F,IAAM,aAAelD,GAAIwD,KAAKC,IACnC,KAAbA,EAAKK,OACPzG,KAAK0G,SAAS,CACZjF,KAAM,UACNkE,QAAS,UAEX3F,KAAKiC,UAAU0E,OAAOlC,EAAO,QAGhCmC,MAAM,KACP5G,KAAK0G,SAAS,CACZjF,KAAM,QACNkE,QAAS,aAIfJ,WACEvF,KAAK6G,YAAY,gBAAiB,IAAIV,KAAKC,IACxB,KAAbA,EAAKK,OACPzG,KAAKuE,MAAQ6B,EAAKpE,SAIxBuD,UACE,IAAIQ,EAAQ/F,KACZ+F,EAAMhE,SAAU,EAChBgE,EAAMc,YAAYd,EAAMF,IAAM,cAAgBE,EAAMP,KAAO,SAAWO,EAAMvF,KAAMuF,EAAMnF,QAAQuF,KAAKC,IAClF,KAAbA,EAAKK,OACPV,EAAM9D,UAAYmE,EAAKpE,KACvB+D,EAAM5C,MAAQiD,EAAKU,OAErBf,EAAMhE,SAAU,KAGpBwD,iBAAiBwB,GACf/G,KAAKQ,KAAOuG,EACZ/G,KAAKuB,WAEPgE,oBAAoBwB,GAClB/G,KAAKwF,KAAOuB,EACZ/G,KAAKuB,WAEPgE,WACE,IAAIQ,EAAQ/F,KACRqE,EAAOrE,KAAK8D,SAASO,KACrBE,EAAQ,GACZvE,KAAKuE,MAAMyC,QAAQC,IACjB,IAAK,IAAIxC,EAAQ,EAAGA,EAAQJ,EAAK6C,OAAQzC,IAAS,CAChD,MAAM9B,EAAK0B,EAAKI,GACZwC,EAAQtE,IAAMA,GAChB4B,EAAMlF,KAAK4H,MAIjBjH,KAAK8D,SAAS2B,IAAMlB,EACpBvE,KAAKmH,MAAM,YAAYC,SAASC,IAC9B,IAAIA,EAWF,OAAO,EAVPrH,KAAK6G,YAAYd,EAAMF,IAAM,OAAQ7F,KAAK8D,UAAUqC,KAAKC,IACtC,KAAbA,EAAKK,OACPV,EAAMW,SAAS,CACbjF,KAAM,UACNkE,QAASS,EAAKkB,MAEhBvB,EAAMrC,mBAAoB,WAWN6D,EAAuC,EAKrEC,GAHqE/H,EAAoB,QAGnEA,EAAoB,SAW1CgI,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACAzH,EACAsF,GACA,EACA,KACA,WACA,MAIwCzF,EAAoB,WAAc8H,EAAiB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-a540ec18\"],{\"38ad\":function(e,t,l){},a4c8:function(e,t,l){\"use strict\";l(\"38ad\")},d625:function(e,t,l){\"use strict\";l.r(t);var a=function(){var e=this,t=e._self._c;return t(\"div\",[t(\"el-card\",{attrs:{shadow:\"always\"}},[t(\"el-row\",{staticStyle:{width:\"600px\"}},[t(\"el-input\",{attrs:{placeholder:\"请输入内容\",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,\"keyword\",t)},expression:\"search.keyword\"}},[t(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-search\"},on:{click:function(t){return e.getData()}},slot:\"append\"})],1)],1),t(\"el-row\",{staticClass:\"page-top\"},[t(\"el-button\",{attrs:{type:\"primary\",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v(\"新增\")]),t(\"el-button\",{attrs:{type:\"success\",size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v(\"刷新\")])],1),t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"table\",attrs:{data:e.tableData,size:e.allSize}},[t(\"el-table-column\",{attrs:{prop:\"title\",label:\"标题\"}}),t(\"el-table-column\",{attrs:{prop:\"desc\",label:\"描述\"}}),t(\"el-table-column\",{attrs:{prop:\"price\",label:\"价格\"}}),t(\"el-table-column\",{attrs:{prop:\"year\",label:\"年份\"}}),t(\"el-table-column\",{attrs:{prop:\"sort\",label:\"排序\"}}),t(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"创建日期\"}}),t(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\"},scopedSlots:e._u([{key:\"default\",fn:function(l){return[t(\"el-button\",{attrs:{type:\"text\",size:\"small\"},on:{click:function(t){return e.editData(l.row.id)}}},[e._v(\"编辑\")]),t(\"el-button\",{attrs:{type:\"text\",size:\"small\"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(l.$index,l.row.id)}}},[e._v(\" 移除 \")])]}}])})],1),t(\"div\",{staticClass:\"page-top\"},[t(\"el-pagination\",{attrs:{\"page-sizes\":[20,100,200,300,400],\"page-size\":e.size,layout:\"total, sizes, prev, pager, next, jumper\",total:e.total},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handleCurrentChange}})],1)],1),t(\"el-dialog\",{attrs:{title:\"详情内容\",visible:e.dialogFormVisible,\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.dialogFormVisible=t}}},[t(\"el-form\",{ref:\"ruleForm\",attrs:{model:e.ruleForm,rules:e.rules}},[t(\"el-form-item\",{attrs:{label:\"套餐名称\",\"label-width\":e.formLabelWidth,prop:\"title\"}},[t(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,\"title\",t)},expression:\"ruleForm.title\"}})],1),t(\"el-form-item\",{attrs:{label:\"套餐价格\",\"label-width\":e.formLabelWidth,prop:\"price\"}},[t(\"el-input\",{attrs:{autocomplete:\"off\",type:\"number\"},model:{value:e.ruleForm.price,callback:function(t){e.$set(e.ruleForm,\"price\",t)},expression:\"ruleForm.price\"}})],1),t(\"el-form-item\",{attrs:{label:\"年份\",\"label-width\":e.formLabelWidth,prop:\"year\"}},[t(\"el-input\",{attrs:{autocomplete:\"off\",type:\"number\"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,\"year\",t)},expression:\"ruleForm.year\"}})],1),t(\"el-form-item\",{attrs:{label:\"套餐内容\",\"label-width\":e.formLabelWidth,prop:\"good\"}},[t(\"el-checkbox-group\",{model:{value:e.ruleForm.good,callback:function(t){e.$set(e.ruleForm,\"good\",t)},expression:\"ruleForm.good\"}},e._l(e.types,(function(l,a){return t(\"el-row\",{key:a,staticStyle:{display:\"flex\"}},[t(\"el-col\",{attrs:{span:16}},[t(\"el-checkbox\",{attrs:{label:l.id}},[e._v(\" \"+e._s(l.title)+\" \")])],1),t(\"el-col\",{attrs:{span:8}},[1==l.is_num?t(\"el-input-number\",{attrs:{min:1,max:999,size:\"mini\",label:\"描述文字\"},model:{value:l.value,callback:function(t){e.$set(l,\"value\",t)},expression:\"item.value\"}}):e._e()],1)],1)})),1)],1),t(\"el-form-item\",{attrs:{label:\"套餐描述\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,\"desc\",t)},expression:\"ruleForm.desc\"}})],1),t(\"el-form-item\",{attrs:{label:\"排序\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\",type:\"number\"},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,\"sort\",t)},expression:\"ruleForm.sort\"}})],1)],1),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(\"取 消\")]),t(\"el-button\",{attrs:{type:\"primary\"},on:{click:function(t){return e.saveData()}}},[e._v(\"确 定\")])],1)],1)],1)},r=[],o=(l(\"14d9\"),{name:\"list\",components:{},data(){return{allSize:\"mini\",tableData:[],loading:!0,total:1,page:1,size:20,search:{keyword:\"\"},ruleForm:{title:\"\",price:\"\",year:\"\",desc:\"\",sort:0,good:[],num:[]},num:0,rules:{title:[{required:!0,message:\"请填写名称\",trigger:\"blur\"}],price:[{required:!0,message:\"请填写价格\",trigger:\"blur\"}],year:[{required:!0,message:\"请填写年份\",trigger:\"blur\"}]},dialogFormVisible:!1,formLabelWidth:\"80px\",url:\"/taocan/\",types:[]}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):(this.ruleForm={title:\"\",price:\"\",year:\"\",desc:\"\",sort:0,good:[],num:[]},t.getTypes()),t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+\"read?id=\"+e).then(e=>{e&&(t.ruleForm=e.data,t.types=t.ruleForm.num)})},delData(e,t){this.$confirm(\"是否删除该信息?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"delete?id=\"+t).then(t=>{200==t.code&&(this.$message({type:\"success\",message:\"删除成功!\"}),this.tableData.splice(e,1))})}).catch(()=>{this.$message({type:\"error\",message:\"取消删除!\"})})},getTypes(){this.postRequest(\"/type/getList\",{}).then(e=>{200==e.code&&(this.types=e.data)})},getData(){let e=this;e.loading=!0,e.postRequest(e.url+\"index?page=\"+e.page+\"&size=\"+e.size,e.search).then(t=>{200==t.code&&(e.tableData=t.data,e.total=t.count),e.loading=!1})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},saveData(){let e=this,t=this.ruleForm.good,l=[];this.types.forEach(e=>{for(let a=0;a<t.length;a++){const r=t[a];e.id==r&&l.push(e)}}),this.ruleForm.num=l,this.$refs[\"ruleForm\"].validate(t=>{if(!t)return!1;this.postRequest(e.url+\"save\",this.ruleForm).then(t=>{200==t.code&&(e.$message({type:\"success\",message:t.msg}),e.dialogFormVisible=!1)})})}}}),s=o,i=(l(\"a4c8\"),l(\"2877\")),n=Object(i[\"a\"])(s,a,r,!1,null,\"0e653135\",null);t[\"default\"]=n.exports}}]);", "extractedComments": []}