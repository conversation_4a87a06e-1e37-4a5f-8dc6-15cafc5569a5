{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue?vue&type=style&index=0&id=527fd6ab&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue", "mtime": 1748604247125}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SystemMonitor.vue"], "names": [], "mappings": ";AAsMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "SystemMonitor.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div class=\"system-monitor\">\r\n    <el-card shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">系统状态监控</span>\r\n        <el-button type=\"text\" @click=\"refreshData\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新\r\n        </el-button>\r\n      </div>\r\n      \r\n      <div class=\"monitor-content\">\r\n        <!-- 系统状态指示器 -->\r\n        <div class=\"status-indicators\">\r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon online\">\r\n              <i class=\"el-icon-success\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">系统状态</div>\r\n              <div class=\"status-value\">正常运行</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">运行时间</div>\r\n              <div class=\"status-value\">{{ systemUptime }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">在线用户</div>\r\n              <div class=\"status-value\">{{ onlineUsers }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性能指标 -->\r\n        <div class=\"performance-metrics\">\r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">CPU使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.cpu }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.cpu\" \r\n              :color=\"getProgressColor(systemMetrics.cpu)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">内存使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.memory }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.memory\" \r\n              :color=\"getProgressColor(systemMetrics.memory)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">磁盘使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.disk }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.disk\" \r\n              :color=\"getProgressColor(systemMetrics.disk)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务状态 -->\r\n        <div class=\"service-status\">\r\n          <div class=\"service-title\">服务状态</div>\r\n          <div class=\"service-list\">\r\n            <div \r\n              v-for=\"service in services\" \r\n              :key=\"service.name\"\r\n              class=\"service-item\"\r\n            >\r\n              <div class=\"service-info\">\r\n                <span class=\"service-name\">{{ service.name }}</span>\r\n                <span \r\n                  class=\"service-status-badge\" \r\n                  :class=\"service.status\"\r\n                >\r\n                  {{ service.status === 'online' ? '正常' : '异常' }}\r\n                </span>\r\n              </div>\r\n              <div class=\"service-details\">\r\n                <span class=\"service-version\">{{ service.version }}</span>\r\n                <span class=\"service-uptime\">运行 {{ service.uptime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SystemMonitor',\r\n  data() {\r\n    return {\r\n      systemUptime: '7天 12小时 35分钟',\r\n      onlineUsers: 23,\r\n      systemMetrics: {\r\n        cpu: 45,\r\n        memory: 62,\r\n        disk: 38\r\n      },\r\n      services: [\r\n        {\r\n          name: 'Web服务器',\r\n          status: 'online',\r\n          version: 'v2.1.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '数据库',\r\n          status: 'online',\r\n          version: 'MySQL 8.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '文件服务',\r\n          status: 'online',\r\n          version: 'v1.5.2',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '邮件服务',\r\n          status: 'online',\r\n          version: 'v3.2.1',\r\n          uptime: '6天'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.startMonitoring()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.monitorTimer) {\r\n      clearInterval(this.monitorTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    startMonitoring() {\r\n      // 模拟实时数据更新\r\n      this.monitorTimer = setInterval(() => {\r\n        this.updateMetrics()\r\n      }, 5000)\r\n    },\r\n    \r\n    updateMetrics() {\r\n      // 模拟性能指标变化\r\n      this.systemMetrics.cpu = Math.floor(Math.random() * 30) + 30\r\n      this.systemMetrics.memory = Math.floor(Math.random() * 20) + 50\r\n      this.systemMetrics.disk = Math.floor(Math.random() * 15) + 30\r\n      \r\n      // 模拟在线用户数变化\r\n      this.onlineUsers = Math.floor(Math.random() * 10) + 20\r\n    },\r\n    \r\n    refreshData() {\r\n      this.updateMetrics()\r\n      this.$message.success('数据已刷新')\r\n    },\r\n    \r\n    getProgressColor(percentage) {\r\n      if (percentage < 50) {\r\n        return '#67C23A'\r\n      } else if (percentage < 80) {\r\n        return '#E6A23C'\r\n      } else {\r\n        return '#F56C6C'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.system-monitor {\r\n  height: 100%;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.monitor-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicators {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n.status-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #f0f0f0;\r\n  color: #666;\r\n  font-size: 18px;\r\n}\r\n\r\n.status-icon.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-info {\r\n  flex: 1;\r\n}\r\n\r\n.status-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 性能指标 */\r\n.performance-metrics {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.metric-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.metric-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 服务状态 */\r\n.service-status {\r\n  border-top: 1px solid #f0f0f0;\r\n  padding-top: 20px;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.service-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.service-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.service-status-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.service-status-badge.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.service-status-badge.offline {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.service-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.service-version,\r\n.service-uptime {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .status-indicators {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .status-item {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-details {\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style> "]}]}