{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\components\\audioplay.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\components\\audioplay.vue", "mtime": 1732626900069}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgcmVjb3JkRmlsZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgIH0sDQogIH0sDQogIG5hbWU6ICJhdWRpb3BsYXkiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBpc1BsYXk6IGZhbHNlLA0KICAgICAgbXlBdXRvOiBuZXcgQXVkaW8odGhpcy5yZWNvcmRGaWxlKSwNCiAgICB9Ow0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXV0b1BsYXkoKSB7DQogICAgICB0aGlzLmlzUGxheSA9ICF0aGlzLmlzUGxheTsNCiAgICAgIGlmICh0aGlzLmlzUGxheSkgew0KICAgICAgICB0aGlzLm15QXV0by5wbGF5KCk7DQogICAgICAgIHRoaXMucGFseUVuZCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5teUF1dG8ucGF1c2UoKTsNCiAgICAgICAgdGhpcy5wYWx5RW5kKCk7DQogICAgICB9DQogICAgfSwNCiAgICBwYWx5RW5kKCkgew0KICAgICAgdGhpcy5teUF1dG8uYWRkRXZlbnRMaXN0ZW5lcigiZW5kZWQiLCAoKSA9PiB7DQogICAgICAgIHRoaXMuaXNQbGF5ID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["audioplay.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "audioplay.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <i\r\n    slot=\"reference\"\r\n    :style=\"isPlay == false ? '' : 'color: red;'\"\r\n    :class=\"isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause'\"\r\n    @click=\"autoPlay\"\r\n    style=\"\r\n      cursor: pointer;\r\n      margin-right: 10px;\r\n      margin-top: 3px;\r\n      font-size: 25px;\r\n    \"\r\n  ></i>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    recordFile: {\r\n      type: String,\r\n    },\r\n  },\r\n  name: \"audioplay\",\r\n  data() {\r\n    return {\r\n      isPlay: false,\r\n      myAuto: new Audio(this.recordFile),\r\n    };\r\n  },\r\n  methods: {\r\n    autoPlay() {\r\n      this.isPlay = !this.isPlay;\r\n      if (this.isPlay) {\r\n        this.myAuto.play();\r\n        this.palyEnd();\r\n      } else {\r\n        this.myAuto.pause();\r\n        this.palyEnd();\r\n      }\r\n    },\r\n    palyEnd() {\r\n      this.myAuto.addEventListener(\"ended\", () => {\r\n        this.isPlay = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}