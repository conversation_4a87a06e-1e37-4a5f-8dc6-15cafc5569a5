{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue", "mtime": 1748442914244}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\babel.config.js", "mtime": 1748336479385}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetails", "name", "components", "data", "allSize", "list", "total", "currentId", "page", "size", "search", "keyword", "is_pay", "is_deal", "loading", "url", "title", "info", "dialogFormVisible", "dialogViewUserDetail", "dialogPreview", "dialogReview", "dialogProgress", "show_image", "dialogVisible", "previewData", "reviewData", "progressData", "reviewSteps", "reviewSubmitting", "reviewForm", "result", "reason", "comment", "reviewRules", "required", "message", "trigger", "ruleForm", "is_num", "images", "attach_path", "rules", "file_path", "form<PERSON>abe<PERSON><PERSON>", "options", "id", "options1", "mounted", "getData", "methods", "changeFile", "filed", "console", "log", "clearData", "viewUserData", "_this", "editData", "getInfo", "desc", "getRequest", "then", "resp", "code", "$message", "type", "msg", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "delData", "index", "splice", "refulsh", "$router", "go", "searchData", "setTimeout", "allData", "order_sn", "review_status", "current_reviewer", "nickname", "phone", "uid", "create_time", "filteredData", "filter", "item", "includes", "length", "saveData", "$refs", "validate", "valid", "postRequest", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "previewContract", "row", "viewFile", "fileUrl", "window", "open", "downloadFile", "link", "document", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "getStatusType", "status", "statusMap", "getStatusText", "canReview", "startReview", "submitReview", "currentData", "findIndex", "getNextReviewStatus", "currentItem", "statusFlow", "reviewerMap", "nextStatus", "viewReviewProgress", "generateReviewSteps", "allSteps", "reviewer", "time", "push", "submitToFiling"], "sources": ["src/views/pages/wenshu/shenhe.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-review-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document-checked\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入工单号/用户名/合同标题\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">审核状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同审核数据\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"order-sn\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ scope.row.order_sn }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.type === '合同审核' ? 'warning' : 'info'\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"title\" label=\"合同标题\" min-width=\"150\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"contract-title\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"desc\" label=\"审核要求\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"review-desc\">\r\n              {{ scope.row.desc }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"review_status\" label=\"审核状态\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getStatusType(scope.row.review_status)\"\r\n              size=\"small\"\r\n            >\r\n              {{ getStatusText(scope.row.review_status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"current_reviewer\" label=\"当前审核人\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.current_reviewer\">{{ scope.row.current_reviewer }}</span>\r\n            <span v-else class=\"text-muted\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-user\"></i>\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-phone\"></i>\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"提交时间\" width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- 审核按钮 -->\r\n              <el-button\r\n                v-if=\"canReview(scope.row)\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"startReview(scope.row)\"\r\n                icon=\"el-icon-edit-outline\"\r\n                class=\"action-btn\"\r\n              >\r\n                审核\r\n              </el-button>\r\n\r\n              <!-- 查看审核进度 -->\r\n              <el-button\r\n                type=\"info\"\r\n                size=\"mini\"\r\n                @click=\"viewReviewProgress(scope.row)\"\r\n                icon=\"el-icon-view\"\r\n                class=\"action-btn\"\r\n              >\r\n                进度\r\n              </el-button>\r\n\r\n              <!-- 预览合同 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"previewContract(scope.row)\"\r\n                icon=\"el-icon-document\"\r\n                class=\"action-btn\"\r\n              >\r\n                预览\r\n              </el-button>\r\n\r\n              <!-- 立案按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.review_status === 'approved'\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitToFiling(scope.row)\"\r\n                icon=\"el-icon-folder-add\"\r\n                class=\"action-btn\"\r\n              >\r\n                立案\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同内容\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同图片\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.images[0]\">\r\n            <div style=\"width: 100%;display: table-cell;\">\r\n          <div style=\"float: left;margin-left:2px;\"\r\n            v-for=\"(item2, index2) in ruleForm.images\"\r\n            :key=\"index2\"\r\n            class=\"image-list\"\r\n          >\r\n            <img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />\r\n          </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同文件\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.attach_path[0]\">\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n          <div\r\n            v-for=\"(item3, index3) in ruleForm.attach_path\"\r\n            :key=\"index3\"\r\n          >\r\n            <div v-if=\"item3\">\r\n              <div >文件{{ index3 +1 }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\">下载</a></div><br />\r\n            </div>\r\n          </div>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 合同预览对话框 -->\r\n    <el-dialog\r\n      title=\"合同预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"contract-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ previewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ previewData.nickname }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-time\"></i>\r\n              提交时间：{{ previewData.create_time }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div class=\"section\">\r\n            <h4>审核要求</h4>\r\n            <p>{{ previewData.desc }}</p>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.images && previewData.images.length\">\r\n            <h4>合同图片</h4>\r\n            <div class=\"image-gallery\">\r\n              <div\r\n                v-for=\"(image, index) in previewData.images\"\r\n                :key=\"index\"\r\n                class=\"image-item\"\r\n                @click=\"showImage(image)\"\r\n              >\r\n                <img :src=\"image\" alt=\"合同图片\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.attach_path && previewData.attach_path.length\">\r\n            <h4>合同文件</h4>\r\n            <div class=\"file-list\">\r\n              <div\r\n                v-for=\"(file, index) in previewData.attach_path\"\r\n                :key=\"index\"\r\n                class=\"file-item\"\r\n                v-if=\"file\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>文件{{ index + 1 }}</span>\r\n                <div class=\"file-actions\">\r\n                  <el-button type=\"text\" @click=\"viewFile(file)\">查看</el-button>\r\n                  <el-button type=\"text\" @click=\"downloadFile(file)\">下载</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog\r\n      title=\"合同审核\"\r\n      :visible.sync=\"dialogReview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"review-dialog\"\r\n    >\r\n      <div class=\"review-content\">\r\n        <div class=\"review-header\">\r\n          <h3>{{ reviewData.title }}</h3>\r\n          <div class=\"review-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ reviewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ reviewData.nickname }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"review-form\">\r\n          <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewForm\" label-width=\"100px\">\r\n            <el-form-item label=\"审核结果\" prop=\"result\">\r\n              <el-radio-group v-model=\"reviewForm.result\">\r\n                <el-radio label=\"approved\">通过</el-radio>\r\n                <el-radio label=\"rejected\">不通过</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              v-if=\"reviewForm.result === 'rejected'\"\r\n              label=\"不通过理由\"\r\n              prop=\"reason\"\r\n            >\r\n              <el-select\r\n                v-model=\"reviewForm.reason\"\r\n                placeholder=\"请选择不通过理由\"\r\n                style=\"width: 100%\"\r\n                clearable\r\n              >\r\n                <el-option label=\"合同条款不完整\" value=\"incomplete_terms\"></el-option>\r\n                <el-option label=\"法律条款有误\" value=\"legal_error\"></el-option>\r\n                <el-option label=\"格式不规范\" value=\"format_error\"></el-option>\r\n                <el-option label=\"内容与需求不符\" value=\"content_mismatch\"></el-option>\r\n                <el-option label=\"缺少必要附件\" value=\"missing_attachments\"></el-option>\r\n                <el-option label=\"其他问题\" value=\"other\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              label=\"审核意见\"\r\n              prop=\"comment\"\r\n              :rules=\"reviewForm.result === 'rejected' ? [{ required: true, message: '请填写审核意见', trigger: 'blur' }] : []\"\r\n            >\r\n              <el-input\r\n                type=\"textarea\"\r\n                v-model=\"reviewForm.comment\"\r\n                :rows=\"4\"\r\n                placeholder=\"请填写详细的审核意见...\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogReview = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitReview\" :loading=\"reviewSubmitting\">\r\n          提交审核\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核进度抽屉 -->\r\n    <el-drawer\r\n      title=\"审核进度\"\r\n      :visible.sync=\"dialogProgress\"\r\n      direction=\"rtl\"\r\n      size=\"50%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"progress-drawer\"\r\n    >\r\n      <div class=\"progress-drawer-content\">\r\n        <div class=\"progress-header\">\r\n          <h3>{{ progressData.title }}</h3>\r\n          <div class=\"progress-meta\">\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>工单号：{{ progressData.order_sn }}</span>\r\n            </div>\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-info\"></i>\r\n              <span>当前状态：{{ getStatusText(progressData.review_status) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"progress-timeline\">\r\n          <el-timeline>\r\n            <el-timeline-item\r\n              v-for=\"(step, index) in reviewSteps\"\r\n              :key=\"index\"\r\n              :timestamp=\"step.time\"\r\n              :type=\"step.status === 'completed' ? 'success' : step.status === 'current' ? 'primary' : 'info'\"\r\n              :icon=\"step.status === 'completed' ? 'el-icon-check' : step.status === 'current' ? 'el-icon-loading' : 'el-icon-time'\"\r\n              placement=\"top\"\r\n            >\r\n              <div class=\"timeline-content\">\r\n                <h4>{{ step.title }}</h4>\r\n                <p>{{ step.reviewer }}</p>\r\n                <div v-if=\"step.comment\" class=\"step-comment\">\r\n                  <strong>审核意见：</strong>{{ step.comment }}\r\n                </div>\r\n                <div v-if=\"step.reason\" class=\"step-reason\">\r\n                  <strong>不通过理由：</strong>{{ step.reason }}\r\n                </div>\r\n              </div>\r\n            </el-timeline-item>\r\n          </el-timeline>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/shenhe/\",\r\n      title: \"合同审核\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogPreview: false,\r\n      dialogReview: false,\r\n      dialogProgress: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewData: {},\r\n      reviewData: {},\r\n      progressData: {},\r\n      reviewSteps: [],\r\n      reviewSubmitting: false,\r\n\r\n      // 审核表单\r\n      reviewForm: {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      },\r\n\r\n      // 审核表单验证规则\r\n      reviewRules: {\r\n        result: [\r\n          { required: true, message: '请选择审核结果', trigger: 'change' }\r\n        ]\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        images:{},\r\n        attach_path:{}\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"SH202403001\",\r\n            type: \"合同审核\",\r\n            title: \"劳动合同审核\",\r\n            desc: \"请帮忙审核劳动合同条款，检查是否符合劳动法规定，特别关注薪资、工作时间、福利待遇等条款的合法性\",\r\n            review_status: \"mediator_review\", // 调解员审核中\r\n            current_reviewer: \"调解员\",\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\",\r\n            images: [\r\n              \"/uploads/contracts/labor_contract_page1.jpg\",\r\n              \"/uploads/contracts/labor_contract_page2.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/labor_contract.pdf\",\r\n              \"/uploads/contracts/labor_contract_supplement.docx\"\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"SH202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"房屋租赁合同审核，需要检查租金条款、押金规定、违约责任等是否合理\",\r\n            review_status: \"business_review\", // 业务员审核中\r\n            current_reviewer: \"业务员\",\r\n            nickname: \"李四\",\r\n            phone: \"***********\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/lease_contract_page1.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/lease_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"SH202403003\",\r\n            type: \"合同审核\",\r\n            title: \"买卖合同审核\",\r\n            desc: \"商品买卖合同审核，重点关注货物交付、付款方式、质量保证等条款\",\r\n            review_status: \"approved\", // 全部审核通过\r\n            current_reviewer: \"\",\r\n            nickname: \"王五\",\r\n            phone: \"***********\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\",\r\n            images: [],\r\n            attach_path: [\r\n              \"/uploads/contracts/sale_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"SH202403004\",\r\n            type: \"合同审核\",\r\n            title: \"服务合同审核\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            review_status: \"lawyer_review\", // 律师审核中\r\n            current_reviewer: \"律师\",\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\",\r\n            images: [],\r\n            attach_path: []\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"SH202403005\",\r\n            type: \"合同审核\",\r\n            title: \"借款合同审核\",\r\n            desc: \"个人借款合同审核，需要确认利率、还款方式、担保条款等是否符合法律规定\",\r\n            review_status: \"rejected\", // 审核不通过\r\n            current_reviewer: \"\",\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/loan_contract_page1.jpg\",\r\n              \"/uploads/contracts/loan_contract_page2.jpg\",\r\n              \"/uploads/contracts/loan_contract_page3.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/loan_contract.pdf\"\r\n            ]\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 预览合同\r\n    previewContract(row) {\r\n      console.log('预览合同:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 查看文件\r\n    viewFile(fileUrl) {\r\n      window.open(fileUrl, '_blank');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        'submitted': 'info',\r\n        'mediator_review': 'warning',\r\n        'business_review': 'warning',\r\n        'lawyer_review': 'warning',\r\n        'final_review': 'warning',\r\n        'approved': 'success',\r\n        'rejected': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'submitted': '已提交',\r\n        'mediator_review': '调解员审核中',\r\n        'business_review': '业务员审核中',\r\n        'lawyer_review': '律师审核中',\r\n        'final_review': '最终审核中',\r\n        'approved': '审核通过',\r\n        'rejected': '审核不通过'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 判断是否可以审核\r\n    canReview(row) {\r\n      // 这里可以根据当前用户角色和审核状态判断\r\n      // 简化处理：只要不是已通过或已拒绝的都可以审核\r\n      return row.review_status !== 'approved' && row.review_status !== 'rejected';\r\n    },\r\n\r\n    // 开始审核\r\n    startReview(row) {\r\n      console.log('开始审核:', row);\r\n      this.reviewData = row;\r\n\r\n      // 重置审核表单\r\n      this.reviewForm = {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      };\r\n\r\n      this.dialogReview = true;\r\n    },\r\n\r\n    // 提交审核\r\n    submitReview() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewSubmitting = true;\r\n\r\n          // 模拟提交审核\r\n          setTimeout(() => {\r\n            const result = this.reviewForm.result;\r\n            const currentData = this.reviewData;\r\n\r\n            // 更新列表中的数据\r\n            const index = this.list.findIndex(item => item.id === currentData.id);\r\n            if (index !== -1) {\r\n              if (result === 'approved') {\r\n                // 通过审核，进入下一个审核环节\r\n                this.list[index] = this.getNextReviewStatus(this.list[index]);\r\n              } else {\r\n                // 审核不通过\r\n                this.list[index].review_status = 'rejected';\r\n                this.list[index].current_reviewer = '';\r\n              }\r\n            }\r\n\r\n            this.reviewSubmitting = false;\r\n            this.dialogReview = false;\r\n            this.$message.success('审核提交成功！');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取下一个审核状态\r\n    getNextReviewStatus(currentItem) {\r\n      const statusFlow = {\r\n        'submitted': 'mediator_review',\r\n        'mediator_review': 'business_review',\r\n        'business_review': 'lawyer_review',\r\n        'lawyer_review': 'final_review',\r\n        'final_review': 'approved'\r\n      };\r\n\r\n      const reviewerMap = {\r\n        'mediator_review': '调解员',\r\n        'business_review': '业务员',\r\n        'lawyer_review': '律师',\r\n        'final_review': '最终审核人',\r\n        'approved': ''\r\n      };\r\n\r\n      const nextStatus = statusFlow[currentItem.review_status];\r\n      return {\r\n        ...currentItem,\r\n        review_status: nextStatus,\r\n        current_reviewer: reviewerMap[nextStatus]\r\n      };\r\n    },\r\n\r\n    // 查看审核进度\r\n    viewReviewProgress(row) {\r\n      console.log('查看审核进度:', row);\r\n      this.progressData = row;\r\n\r\n      // 模拟审核步骤数据\r\n      this.reviewSteps = this.generateReviewSteps(row);\r\n      this.dialogProgress = true;\r\n    },\r\n\r\n    // 生成审核步骤\r\n    generateReviewSteps(row) {\r\n      const allSteps = [\r\n        {\r\n          title: '法务提交',\r\n          reviewer: '法务部门',\r\n          status: 'completed',\r\n          time: row.create_time,\r\n          comment: '合同已完成，提交审核'\r\n        },\r\n        {\r\n          title: '调解员审核',\r\n          reviewer: '调解员',\r\n          status: row.review_status === 'submitted' ? 'pending' :\r\n                  row.review_status === 'mediator_review' ? 'current' : 'completed',\r\n          time: row.review_status === 'submitted' ? '' : '2024-03-20 11:00:00',\r\n          comment: row.review_status === 'submitted' ? '' : '合同条款符合调解要求'\r\n        },\r\n        {\r\n          title: '业务员审核',\r\n          reviewer: '业务员',\r\n          status: ['submitted', 'mediator_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'business_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '2024-03-20 14:30:00',\r\n          comment: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '业务流程审核通过'\r\n        },\r\n        {\r\n          title: '律师审核',\r\n          reviewer: '律师',\r\n          status: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'lawyer_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '2024-03-20 16:00:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '法律条款审核通过'\r\n        },\r\n        {\r\n          title: '最终审核',\r\n          reviewer: '最终审核人',\r\n          status: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'final_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '2024-03-20 17:30:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '最终审核通过'\r\n        }\r\n      ];\r\n\r\n      // 如果审核被拒绝，添加拒绝信息\r\n      if (row.review_status === 'rejected') {\r\n        allSteps.push({\r\n          title: '审核不通过',\r\n          reviewer: '审核人员',\r\n          status: 'completed',\r\n          time: '2024-03-20 18:00:00',\r\n          comment: '',\r\n          reason: '合同条款不完整，需要重新修改'\r\n        });\r\n      }\r\n\r\n      return allSteps;\r\n    },\r\n\r\n    // 提交立案\r\n    submitToFiling(row) {\r\n      this.$confirm('确认将此合同提交到立案部门？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交立案\r\n        setTimeout(() => {\r\n          this.$message.success('已成功提交到立案部门！');\r\n\r\n          // 更新状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            this.list[index].review_status = 'filed';\r\n            this.list[index].current_reviewer = '立案部门';\r\n          }\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-review-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.order-sn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.order-sn i {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.contract-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.contract-title i {\r\n  color: #e6a23c;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-desc {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.user-link {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n}\r\n\r\n.user-link i {\r\n  font-size: 12px;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 合同预览对话框样式 */\r\n.contract-preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #409eff;\r\n}\r\n\r\n.preview-body .section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.preview-body .section h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-body .section p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.image-gallery {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.image-item {\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n}\r\n\r\n.file-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-item i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 审核对话框样式 */\r\n.review-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.review-content {\r\n  padding: 24px;\r\n}\r\n\r\n.review-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.review-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.review-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.review-form {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #f093fb;\r\n}\r\n\r\n/* 审核进度抽屉样式 */\r\n.progress-drawer >>> .el-drawer {\r\n  border-radius: 0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.progress-drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n.progress-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.progress-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 16px 0;\r\n}\r\n\r\n.progress-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.progress-meta .meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.progress-meta .meta-item i {\r\n  color: #4facfe;\r\n  font-size: 16px;\r\n}\r\n\r\n.progress-timeline {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  border-left: 4px solid #4facfe;\r\n}\r\n\r\n.timeline-content h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.timeline-content p {\r\n  color: #606266;\r\n  margin: 0 0 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.step-comment,\r\n.step-reason {\r\n  background: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.step-comment {\r\n  border-left: 3px solid #67c23a;\r\n}\r\n\r\n.step-reason {\r\n  border-left: 3px solid #f56c6c;\r\n}\r\n\r\n.step-comment strong,\r\n.step-reason strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-muted {\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-review-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .image-gallery {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": ";AA4iBA;AACA,OAAAA,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,cAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,gBAAA;MAEA;MACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,OAAA;MACA;MAEA;MACAC,WAAA;QACAH,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA;QACAtB,KAAA;QACAuB,MAAA;QACAC,MAAA;QACAC,WAAA;MACA;MAEAC,KAAA;QACA1B,KAAA,GACA;UACAmB,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAM,SAAA,GACA;UACAR,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAO,cAAA;MACAC,OAAA,GACA;QACAC,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA,EACA;MACA+B,QAAA,GACA;QACAD,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA,GACA;QACA8B,EAAA;QACA9B,KAAA;MACA;IAEA;EACA;EACAgC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;MACAC,OAAA,CAAAC,GAAA,MAAAF,KAAA;IACA;IACAG,UAAA;MACA,KAAA7C,MAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAAqC,OAAA;IACA;IACAO,aAAAV,EAAA;MACA,IAAAW,KAAA;MACA,IAAAX,EAAA;QACA,KAAAvC,SAAA,GAAAuC,EAAA;MACA;MAEAW,KAAA,CAAAtC,oBAAA;IACA;IACAuC,SAAAZ,EAAA;MACA,IAAAW,KAAA;MACA,IAAAX,EAAA;QACA,KAAAa,OAAA,CAAAb,EAAA;MACA;QACA,KAAAR,QAAA;UACAtB,KAAA;UACA4C,IAAA;QACA;MACA;IACA;IACAD,QAAAb,EAAA;MACA,IAAAW,KAAA;MACAA,KAAA,CAAAI,UAAA,CAAAJ,KAAA,CAAA1C,GAAA,gBAAA+B,EAAA,EAAAgB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAnB,QAAA,GAAAyB,IAAA,CAAA5D,IAAA;UACAsD,KAAA,CAAAvC,iBAAA;QACA;UACAuC,KAAA,CAAAQ,QAAA;YACAC,IAAA;YACA9B,OAAA,EAAA2B,IAAA,CAAAI;UACA;QACA;MACA;IACA;IACAC,QAAAtB,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAL,IAAA;MACA,GACAJ,IAAA;QACA,KAAAU,aAAA,MAAAzD,GAAA,mBAAA+B,EAAA,EAAAgB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAC,IAAA;cACA9B,OAAA,EAAA2B,IAAA,CAAAI;YACA;UACA;YACA,KAAAF,QAAA;cACAC,IAAA;cACA9B,OAAA,EAAA2B,IAAA,CAAAI;YACA;UACA;QACA;MACA,GACAM,KAAA;QACA,KAAAR,QAAA;UACAC,IAAA;UACA9B,OAAA;QACA;MACA;IACA;IACAsC,QAAAC,KAAA,EAAA7B,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAL,IAAA;MACA,GACAJ,IAAA;QACA,KAAAU,aAAA,MAAAzD,GAAA,kBAAA+B,EAAA,EAAAgB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAC,IAAA;cACA9B,OAAA;YACA;YACA,KAAA/B,IAAA,CAAAuE,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAR,QAAA;UACAC,IAAA;UACA9B,OAAA;QACA;MACA;IACA;IACAyC,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAAxE,IAAA;MACA,KAAAC,IAAA;MACA,KAAAwC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAQ,KAAA;MAEAA,KAAA,CAAA3C,OAAA;;MAEA;MACAmE,UAAA;QACA,IAAAC,OAAA,IACA;UACApC,EAAA;UACAqC,QAAA;UACAjB,IAAA;UACAlD,KAAA;UACA4C,IAAA;UACAwB,aAAA;UAAA;UACAC,gBAAA;UACAC,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;UACAjD,MAAA,GACA,+CACA,8CACA;UACAC,WAAA,GACA,yCACA;QAEA,GACA;UACAK,EAAA;UACAqC,QAAA;UACAjB,IAAA;UACAlD,KAAA;UACA4C,IAAA;UACAwB,aAAA;UAAA;UACAC,gBAAA;UACAC,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;UACAjD,MAAA,GACA,8CACA;UACAC,WAAA,GACA;QAEA,GACA;UACAK,EAAA;UACAqC,QAAA;UACAjB,IAAA;UACAlD,KAAA;UACA4C,IAAA;UACAwB,aAAA;UAAA;UACAC,gBAAA;UACAC,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;UACAjD,MAAA;UACAC,WAAA,GACA;QAEA,GACA;UACAK,EAAA;UACAqC,QAAA;UACAjB,IAAA;UACAlD,KAAA;UACA4C,IAAA;UACAwB,aAAA;UAAA;UACAC,gBAAA;UACAC,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;UACAjD,MAAA;UACAC,WAAA;QACA,GACA;UACAK,EAAA;UACAqC,QAAA;UACAjB,IAAA;UACAlD,KAAA;UACA4C,IAAA;UACAwB,aAAA;UAAA;UACAC,gBAAA;UACAC,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;UACAjD,MAAA,GACA,8CACA,8CACA,6CACA;UACAC,WAAA,GACA;QAEA,EACA;;QAEA;QACA,IAAAiD,YAAA,GAAAR,OAAA;QACA,IAAAzB,KAAA,CAAA/C,MAAA,CAAAC,OAAA;UACA+E,YAAA,GAAAR,OAAA,CAAAS,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAT,QAAA,CAAAU,QAAA,CAAApC,KAAA,CAAA/C,MAAA,CAAAC,OAAA,KACAiF,IAAA,CAAA5E,KAAA,CAAA6E,QAAA,CAAApC,KAAA,CAAA/C,MAAA,CAAAC,OAAA,KACAiF,IAAA,CAAAN,QAAA,CAAAO,QAAA,CAAApC,KAAA,CAAA/C,MAAA,CAAAC,OAAA,KACAiF,IAAA,CAAAL,KAAA,CAAAM,QAAA,CAAApC,KAAA,CAAA/C,MAAA,CAAAC,OAAA,CACA;QACA;QAEA,IAAA8C,KAAA,CAAA/C,MAAA,CAAAG,OAAA,WAAA4C,KAAA,CAAA/C,MAAA,CAAAG,OAAA;UACA6E,YAAA,GAAAA,YAAA,CAAAC,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAA/E,OAAA,IAAA4C,KAAA,CAAA/C,MAAA,CAAAG,OACA;QACA;QAEA4C,KAAA,CAAApD,IAAA,GAAAqF,YAAA;QACAjC,KAAA,CAAAnD,KAAA,GAAAoF,YAAA,CAAAI,MAAA;QACArC,KAAA,CAAA3C,OAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAiF,SAAA;MACA,IAAAtC,KAAA;MACA,KAAAuC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAA1C,KAAA,CAAA1C,GAAA,gBAAAuB,QAAA,EAAAwB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA;gBACAC,IAAA;gBACA9B,OAAA,EAAA2B,IAAA,CAAAI;cACA;cACA,KAAAlB,OAAA;cACAQ,KAAA,CAAAvC,iBAAA;YACA;cACAuC,KAAA,CAAAQ,QAAA;gBACAC,IAAA;gBACA9B,OAAA,EAAA2B,IAAA,CAAAI;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAiC,iBAAAC,GAAA;MACA,KAAA5F,IAAA,GAAA4F,GAAA;MAEA,KAAApD,OAAA;IACA;IACAqD,oBAAAD,GAAA;MACA,KAAA7F,IAAA,GAAA6F,GAAA;MACA,KAAApD,OAAA;IACA;IACAsD,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAAxC,IAAA;QACA,KAAAC,QAAA,CAAAwC,OAAA;QACA,KAAAnE,QAAA,MAAAc,KAAA,IAAAoD,GAAA,CAAArG,IAAA,CAAAY,GAAA;MACA;QACA,KAAAkD,QAAA,CAAAyC,KAAA,CAAAF,GAAA,CAAArC,GAAA;MACA;IACA;IAEAwC,UAAAC,IAAA;MACA,KAAArF,UAAA,GAAAqF,IAAA;MACA,KAAApF,aAAA;IACA;IACAqF,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA1C,IAAA;MACA,KAAA4C,UAAA;QACA,KAAA7C,QAAA,CAAAyC,KAAA;QACA;MACA;IACA;IACAM,SAAAJ,IAAA,EAAAK,QAAA;MACA,IAAAxD,KAAA;MACAA,KAAA,CAAAI,UAAA,gCAAA+C,IAAA,EAAA9C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAnB,QAAA,CAAA2E,QAAA;UAEAxD,KAAA,CAAAQ,QAAA,CAAAwC,OAAA;QACA;UACAhD,KAAA,CAAAQ,QAAA,CAAAyC,KAAA,CAAA3C,IAAA,CAAAI,GAAA;QACA;MACA;IACA;IAEA;IACA+C,gBAAAC,GAAA;MACA9D,OAAA,CAAAC,GAAA,UAAA6D,GAAA;MACA,KAAA1F,WAAA,GAAA0F,GAAA;MACA,KAAA/F,aAAA;IACA;IAEA;IACAgG,SAAAC,OAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,OAAA;IACA;IAEA;IACAG,aAAAH,OAAA;MACA,MAAAI,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAP,OAAA;MACAI,IAAA,CAAAI,QAAA,GAAAR,OAAA,CAAAS,KAAA,MAAAC,GAAA;MACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAR,IAAA;MACAA,IAAA,CAAAS,KAAA;MACAR,QAAA,CAAAM,IAAA,CAAAG,WAAA,CAAAV,IAAA;MACA,KAAAxD,QAAA,CAAAwC,OAAA;IACA;IAEA;IACA2B,cAAAC,MAAA;MACA,MAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,cAAAF,MAAA;MACA,MAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAG,UAAArB,GAAA;MACA;MACA;MACA,OAAAA,GAAA,CAAA/B,aAAA,mBAAA+B,GAAA,CAAA/B,aAAA;IACA;IAEA;IACAqD,YAAAtB,GAAA;MACA9D,OAAA,CAAAC,GAAA,UAAA6D,GAAA;MACA,KAAAzF,UAAA,GAAAyF,GAAA;;MAEA;MACA,KAAArF,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,OAAA;MACA;MAEA,KAAAZ,YAAA;IACA;IAEA;IACAqH,aAAA;MACA,KAAA1C,KAAA,CAAAlE,UAAA,CAAAmE,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAArE,gBAAA;;UAEA;UACAoD,UAAA;YACA,MAAAlD,MAAA,QAAAD,UAAA,CAAAC,MAAA;YACA,MAAA4G,WAAA,QAAAjH,UAAA;;YAEA;YACA,MAAAiD,KAAA,QAAAtE,IAAA,CAAAuI,SAAA,CAAAhD,IAAA,IAAAA,IAAA,CAAA9C,EAAA,KAAA6F,WAAA,CAAA7F,EAAA;YACA,IAAA6B,KAAA;cACA,IAAA5C,MAAA;gBACA;gBACA,KAAA1B,IAAA,CAAAsE,KAAA,SAAAkE,mBAAA,MAAAxI,IAAA,CAAAsE,KAAA;cACA;gBACA;gBACA,KAAAtE,IAAA,CAAAsE,KAAA,EAAAS,aAAA;gBACA,KAAA/E,IAAA,CAAAsE,KAAA,EAAAU,gBAAA;cACA;YACA;YAEA,KAAAxD,gBAAA;YACA,KAAAR,YAAA;YACA,KAAA4C,QAAA,CAAAwC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAoC,oBAAAC,WAAA;MACA,MAAAC,UAAA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,MAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,MAAAC,UAAA,GAAAF,UAAA,CAAAD,WAAA,CAAA1D,aAAA;MACA;QACA,GAAA0D,WAAA;QACA1D,aAAA,EAAA6D,UAAA;QACA5D,gBAAA,EAAA2D,WAAA,CAAAC,UAAA;MACA;IACA;IAEA;IACAC,mBAAA/B,GAAA;MACA9D,OAAA,CAAAC,GAAA,YAAA6D,GAAA;MACA,KAAAxF,YAAA,GAAAwF,GAAA;;MAEA;MACA,KAAAvF,WAAA,QAAAuH,mBAAA,CAAAhC,GAAA;MACA,KAAA7F,cAAA;IACA;IAEA;IACA6H,oBAAAhC,GAAA;MACA,MAAAiC,QAAA,IACA;QACApI,KAAA;QACAqI,QAAA;QACAhB,MAAA;QACAiB,IAAA,EAAAnC,GAAA,CAAA1B,WAAA;QACAxD,OAAA;MACA,GACA;QACAjB,KAAA;QACAqI,QAAA;QACAhB,MAAA,EAAAlB,GAAA,CAAA/B,aAAA,+BACA+B,GAAA,CAAA/B,aAAA;QACAkE,IAAA,EAAAnC,GAAA,CAAA/B,aAAA;QACAnD,OAAA,EAAAkF,GAAA,CAAA/B,aAAA;MACA,GACA;QACApE,KAAA;QACAqI,QAAA;QACAhB,MAAA,mCAAAxC,QAAA,CAAAsB,GAAA,CAAA/B,aAAA,gBACA+B,GAAA,CAAA/B,aAAA;QACAkE,IAAA,mCAAAzD,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;QACAnD,OAAA,mCAAA4D,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;MACA,GACA;QACApE,KAAA;QACAqI,QAAA;QACAhB,MAAA,sDAAAxC,QAAA,CAAAsB,GAAA,CAAA/B,aAAA,gBACA+B,GAAA,CAAA/B,aAAA;QACAkE,IAAA,sDAAAzD,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;QACAnD,OAAA,sDAAA4D,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;MACA,GACA;QACApE,KAAA;QACAqI,QAAA;QACAhB,MAAA,uEAAAxC,QAAA,CAAAsB,GAAA,CAAA/B,aAAA,gBACA+B,GAAA,CAAA/B,aAAA;QACAkE,IAAA,uEAAAzD,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;QACAnD,OAAA,uEAAA4D,QAAA,CAAAsB,GAAA,CAAA/B,aAAA;MACA,EACA;;MAEA;MACA,IAAA+B,GAAA,CAAA/B,aAAA;QACAgE,QAAA,CAAAG,IAAA;UACAvI,KAAA;UACAqI,QAAA;UACAhB,MAAA;UACAiB,IAAA;UACArH,OAAA;UACAD,MAAA;QACA;MACA;MAEA,OAAAoH,QAAA;IACA;IAEA;IACAI,eAAArC,GAAA;MACA,KAAA9C,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAL,IAAA;MACA,GAAAJ,IAAA;QACA;QACAmB,UAAA;UACA,KAAAhB,QAAA,CAAAwC,OAAA;;UAEA;UACA,MAAA9B,KAAA,QAAAtE,IAAA,CAAAuI,SAAA,CAAAhD,IAAA,IAAAA,IAAA,CAAA9C,EAAA,KAAAqE,GAAA,CAAArE,EAAA;UACA,IAAA6B,KAAA;YACA,KAAAtE,IAAA,CAAAsE,KAAA,EAAAS,aAAA;YACA,KAAA/E,IAAA,CAAAsE,KAAA,EAAAU,gBAAA;UACA;QACA;MACA,GAAAZ,KAAA;QACA,KAAAR,QAAA,CAAAhD,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}