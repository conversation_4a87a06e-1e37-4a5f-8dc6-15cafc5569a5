(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-461f593c"],{"0091":function(e,t,a){"use strict";a("67a0")},"13d5":function(e,t,a){"use strict";var i=a("23e7"),s=a("d58f").left,l=a("a640"),r=a("2d00"),o=a("605d"),n=!o&&r>79&&r<83,c=n||!l("reduce");i({target:"Array",proto:!0,forced:c},{reduce:function(e){var t=arguments.length;return s(this,e,t,t>1?arguments[1]:void 0)}})},"271a":function(e,t,a){"use strict";var i=a("cb2d"),s=a("e330"),l=a("577e"),r=a("d6d6"),o=URLSearchParams,n=o.prototype,c=s(n.getAll),d=s(n.has),m=new o("a=1");!m.has("a",2)&&m.has("a",void 0)||i(n,"has",(function(e){var t=arguments.length,a=t<2?void 0:arguments[1];if(t&&void 0===a)return d(this,e);var i=c(this,e);r(t,1);var s=l(a),o=0;while(o<i.length)if(i[o++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},5494:function(e,t,a){"use strict";var i=a("83ab"),s=a("e330"),l=a("edd0"),r=URLSearchParams.prototype,o=s(r.forEach);i&&!("size"in r)&&l(r,"size",{get:function(){var e=0;return o(this,(function(){e++})),e},configurable:!0,enumerable:!0})},"605d":function(e,t,a){"use strict";var i=a("da84"),s=a("c6b6");e.exports="process"===s(i.process)},"67a0":function(e,t,a){},7355:function(e,t,a){},"85d3":function(e,t,a){"use strict";a("7355")},"88a7":function(e,t,a){"use strict";var i=a("cb2d"),s=a("e330"),l=a("577e"),r=a("d6d6"),o=URLSearchParams,n=o.prototype,c=s(n.append),d=s(n["delete"]),m=s(n.forEach),u=s([].push),p=new o("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&i(n,"delete",(function(e){var t=arguments.length,a=t<2?void 0:arguments[1];if(t&&void 0===a)return d(this,e);var i=[];m(this,(function(e,t){u(i,{key:t,value:e})})),r(t,1);var s,o=l(e),n=l(a),p=0,h=0,f=!1,b=i.length;while(p<b)s=i[p++],f||s.key===o?(f=!0,d(this,s.key)):h++;while(h<b)s=i[h++],s.key===o&&s.value===n||c(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},a640:function(e,t,a){"use strict";var i=a("d039");e.exports=function(e,t){var a=[][e];return!!a&&i((function(){a.call(null,t||function(){return 1},1)}))}},bfef:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-wrapper"},[t("div",{staticClass:"page-container"},[t("div",{staticClass:"page-title"},[e._v(" "+e._s(this.$router.currentRoute.name)+" "),t("el-button",{staticStyle:{float:"right"},attrs:{type:"text",icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v("刷新 ")])],1),t("div",{staticClass:"search-container"},[t("el-form",{staticClass:"search-form",attrs:{model:e.search,"label-width":"80px"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"用户名称"}},[t("el-input",{attrs:{placeholder:"请输入用户名称",clearable:"",size:"small"},model:{value:e.search.nickname,callback:function(t){e.$set(e.search,"nickname",t)},expression:"search.nickname"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"手机号码"}},[t("el-input",{attrs:{placeholder:"请输入手机号码",clearable:"",size:"small"},model:{value:e.search.phone,callback:function(t){e.$set(e.search,"phone",t)},expression:"search.phone"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"公司名称"}},[t("el-input",{attrs:{placeholder:"请输入公司名称",clearable:"",size:"small"},model:{value:e.search.company,callback:function(t){e.$set(e.search,"company",t)},expression:"search.company"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"用户来源"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择来源",clearable:"",size:"small"},model:{value:e.search.yuangong_id,callback:function(t){e.$set(e.search,"yuangong_id",t)},expression:"search.yuangong_id"}},[t("el-option",{attrs:{label:"小程序注册",value:"小程序注册"}}),t("el-option",{attrs:{label:"后台创建",value:"后台创建"}}),t("el-option",{attrs:{label:"直接注册",value:"直接注册"}})],1)],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"联系人"}},[t("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:e.search.linkman,callback:function(t){e.$set(e.search,"linkman",t)},expression:"search.linkman"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"联系号码"}},[t("el-input",{attrs:{placeholder:"请输入联系号码",clearable:"",size:"small"},model:{value:e.search.linkphone,callback:function(t){e.$set(e.search,"linkphone",t)},expression:"search.linkphone"}})],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"注册时间"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",size:"small"},model:{value:e.search.dateRange,callback:function(t){e.$set(e.search,"dateRange",t)},expression:"search.dateRange"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-form-item",[t("div",{staticClass:"search-buttons"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:function(t){return e.searchData()}}},[e._v(" 搜索 ")]),t("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:function(t){return e.resetSearch()}}},[e._v(" 重置 ")])],1)])],1)],1)],1),t("div",{staticClass:"action-buttons"},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-download"},on:{click:e.exportSelectedData}},[e._v(" "+e._s(e.selectedUsers.length>0?`导出选中数据 (${e.selectedUsers.length})`:"导出全部数据")+" ")]),t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload2"},on:{click:e.openUpload}},[e._v(" 导入用户 ")]),t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:e.addUser}},[e._v(" 添加用户 ")]),t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-download"},on:{click:e.downloadTemplate}},[e._v(" 下载导入模板 ")])],1)],1),t("div",{staticClass:"data-table"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"userTable",staticStyle:{width:"100%"},attrs:{data:e.list,border:!0,"header-cell-style":{background:"#fafafa",color:"#606266"}},on:{"sort-change":e.handleSortChange,"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"",label:"头像",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"avatar-container"},[a.row.headimg&&""!==a.row.headimg?t("div",{staticClass:"avatar-wrapper"},[t("img",{staticClass:"user-avatar",attrs:{src:a.row.headimg},on:{click:function(t){return e.showImage(a.row.headimg)}}})]):t("div",{staticClass:"no-avatar"},[t("i",{staticClass:"el-icon-user-solid"})])])]}}])}),t("el-table-column",{attrs:{prop:"nickname",label:"用户名称",sortable:"","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"user-info"},[t("div",{staticClass:"user-name clickable",on:{click:function(t){return e.viewData(a.row.id)}}},[e._v(e._s(a.row.nickname||"未设置"))]),t("div",{staticClass:"user-phone"},[e._v(e._s(a.row.phone))])])]}}])}),t("el-table-column",{attrs:{prop:"company",label:"公司名称",sortable:"","min-width":"150"}}),t("el-table-column",{attrs:{prop:"linkman",label:"联系人",sortable:"","min-width":"100"}}),t("el-table-column",{attrs:{prop:"linkphone",label:"联系号码",sortable:"","min-width":"120"}}),t("el-table-column",{attrs:{prop:"yuangong_id",label:"用户来源","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{size:"small",type:"info"}},[e._v(e._s(a.row.yuangong_id||"直接注册"))])]}}])}),t("el-table-column",{attrs:{label:"债务人数量","min-width":"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{size:"small",type:e.getDebtCountType(a.row.debts)}},[e._v(" "+e._s(e.getDebtCount(a.row.debts))+"人 ")])]}}])}),t("el-table-column",{attrs:{label:"债务总金额","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"debt-amount",class:{"has-debt":e.getTotalDebtAmount(a.row.debts)>0}},[e._v(" ¥"+e._s(e.formatAmount(e.getTotalDebtAmount(a.row.debts)))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"end_time",label:"到期时间","min-width":"120",sortable:""}}),t("el-table-column",{attrs:{prop:"create_time",label:"注册时间",sortable:"","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.create_time))])]}}])}),t("el-table-column",{attrs:{label:"最后登录","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(a.row.last_login_time||"未登录"))])]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"action-buttons-table"},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.viewData(a.row.id)}}},[e._v(" 查看 ")]),t("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v(" 编辑 ")]),t("el-dropdown",{attrs:{trigger:"click"}},[t("el-button",{attrs:{size:"mini"}},[e._v(" 更多"),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{nativeOn:{click:function(t){return e.order(a.row)}}},[e._v("制作订单")]),e.is_del?t("el-dropdown-item",{staticStyle:{color:"#f56c6c"},nativeOn:{click:function(t){return e.delData(a.$index,a.row.id)}}},[e._v("移除用户")]):e._e()],1)],1)],1)]}}])})],1)],1),t("div",{staticClass:"pagination-container"},[t("div",{staticClass:"pagination-info"},[t("span",[e._v("共 "+e._s(e.total)+" 条")]),t("span",[e._v("第 "+e._s(e.page)+" 页")])]),t("el-pagination",{attrs:{"page-sizes":[10,20,50,100],"page-size":e.size,"current-page":e.page,layout:"sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("el-drawer",{attrs:{title:"用户详情",visible:e.drawerViewVisible,direction:"rtl",size:"60%","before-close":e.handleDrawerClose},on:{"update:visible":function(t){e.drawerViewVisible=t}}},[t("div",{staticClass:"drawer-content-wrapper"},[t("div",{staticClass:"drawer-sidebar"},[t("el-menu",{staticClass:"drawer-menu",attrs:{"default-active":e.activeTab},on:{select:e.handleTabSelect}},[t("el-menu-item",{attrs:{index:"customer"}},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("客户信息")])]),t("el-menu-item",{attrs:{index:"member"}},[t("i",{staticClass:"el-icon-medal"}),t("span",[e._v("会员信息")])]),t("el-menu-item",{attrs:{index:"debts"}},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("债务人信息")])]),t("el-menu-item",{attrs:{index:"attachments"}},[t("i",{staticClass:"el-icon-folder-opened"}),t("span",[e._v("附件信息")])])],1)],1),t("div",{staticClass:"drawer-content"},["customer"===e.activeTab?t("div",{staticClass:"edit-mode-toggle"},[t("el-button",{attrs:{type:"primary",icon:e.isEditMode?"el-icon-view":"el-icon-edit"},on:{click:e.toggleEditMode}},[e._v(" "+e._s(e.isEditMode?"查看模式":"编辑模式")+" ")]),e.isEditMode?t("el-button",{attrs:{type:"success",icon:"el-icon-check"},on:{click:e.saveUserData}},[e._v(" 保存 ")]):e._e(),e.isEditMode?t("el-button",{attrs:{type:"info",icon:"el-icon-close"},on:{click:e.cancelEdit}},[e._v(" 取消 ")]):e._e()],1):e._e(),"customer"===e.activeTab?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-user"}),e._v(" 客户信息 ")]),e.isEditMode?e._e():t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.currentUserInfo.company||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.currentUserInfo.phone||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"用户名称"}},[e._v(e._s(e.currentUserInfo.nickname||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.currentUserInfo.linkman||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(e._s(e.currentUserInfo.linkphone||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[t("el-tag",{attrs:{size:"small",type:"info"}},[e._v(e._s(e.currentUserInfo.yuangong_id||"直接注册"))])],1),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.currentUserInfo.lian_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.currentUserInfo.tiaojie_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.currentUserInfo.fawu_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专员"}},[e._v(e._s(e.currentUserInfo.htsczy_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.currentUserInfo.ls_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.currentUserInfo.ywy_name||"未分配")+" ")]),t("el-descriptions-item",{attrs:{label:"头像",span:2}},[t("div",{staticClass:"avatar-display"},[e.currentUserInfo.headimg&&""!==e.currentUserInfo.headimg?t("img",{staticClass:"detail-avatar",attrs:{src:e.currentUserInfo.headimg},on:{click:function(t){return e.showImage(e.currentUserInfo.headimg)}}}):t("div",{staticClass:"no-avatar-large"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",[e._v("无头像")])])])]),t("el-descriptions-item",{attrs:{label:"注册时间",span:2}},[e._v(e._s(e.currentUserInfo.create_time||"未知")+" ")]),t("el-descriptions-item",{attrs:{label:"最后登录",span:2}},[e._v(e._s(e.currentUserInfo.last_login_time||"从未登录")+" ")]),t("el-descriptions-item",{attrs:{label:"会员到期",span:2}},[e._v(e._s(e.currentUserInfo.end_time||"未设置")+" ")])],1),e.isEditMode?t("el-form",{ref:"editForm",attrs:{model:e.editForm,rules:e.rules,"label-width":"120px"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"公司名称",prop:"company"}},[t("el-input",{attrs:{placeholder:"请输入公司名称"},model:{value:e.editForm.company,callback:function(t){e.$set(e.editForm,"company",t)},expression:"editForm.company"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号",disabled:""},model:{value:e.editForm.phone,callback:function(t){e.$set(e.editForm,"phone",t)},expression:"editForm.phone"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"用户名称",prop:"nickname"}},[t("el-input",{attrs:{placeholder:"请输入用户名称"},model:{value:e.editForm.nickname,callback:function(t){e.$set(e.editForm,"nickname",t)},expression:"editForm.nickname"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系人",prop:"linkman"}},[t("el-input",{attrs:{placeholder:"请输入联系人"},model:{value:e.editForm.linkman,callback:function(t){e.$set(e.editForm,"linkman",t)},expression:"editForm.linkman"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系电话",prop:"linkphone"}},[t("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.editForm.linkphone,callback:function(t){e.$set(e.editForm,"linkphone",t)},expression:"editForm.linkphone"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"登录密码",prop:"password"}},[t("el-input",{attrs:{placeholder:"请输入新密码（留空不修改）",type:"password"},model:{value:e.editForm.password,callback:function(t){e.$set(e.editForm,"password",t)},expression:"editForm.password"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"调解员",prop:"tiaojie_id"}},[t("el-select",{attrs:{placeholder:"请选择调解员",filterable:"",clearable:""},model:{value:e.editForm.tiaojie_id,callback:function(t){e.$set(e.editForm,"tiaojie_id",t)},expression:"editForm.tiaojie_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.tiaojies,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"法务专员",prop:"fawu_id"}},[t("el-select",{attrs:{placeholder:"请选择法务专员",filterable:"",clearable:""},model:{value:e.editForm.fawu_id,callback:function(t){e.$set(e.editForm,"fawu_id",t)},expression:"editForm.fawu_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.fawus,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"立案专员",prop:"lian_id"}},[t("el-select",{attrs:{placeholder:"请选择立案专员",filterable:"",clearable:""},model:{value:e.editForm.lian_id,callback:function(t){e.$set(e.editForm,"lian_id",t)},expression:"editForm.lian_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lians,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"合同上传专用",prop:"htsczy_id"}},[t("el-select",{attrs:{placeholder:"请选择合同上传专用",filterable:"",clearable:""},model:{value:e.editForm.htsczy_id,callback:function(t){e.$set(e.editForm,"htsczy_id",t)},expression:"editForm.htsczy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.htsczy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"律师",prop:"ls_id"}},[t("el-select",{attrs:{placeholder:"请选择律师",filterable:"",clearable:""},model:{value:e.editForm.ls_id,callback:function(t){e.$set(e.editForm,"ls_id",t)},expression:"editForm.ls_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ls,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"业务员",prop:"ywy_id"}},[t("el-select",{attrs:{placeholder:"请选择业务员",filterable:"",clearable:""},model:{value:e.editForm.ywy_id,callback:function(t){e.$set(e.editForm,"ywy_id",t)},expression:"editForm.ywy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ywy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"开始时间",prop:"start_time"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择开始时间"},model:{value:e.editForm.start_time,callback:function(t){e.$set(e.editForm,"start_time",t)},expression:"editForm.start_time"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"会员年限",prop:"year"}},[t("el-input-number",{attrs:{min:0,max:99,placeholder:"请输入会员年限"},model:{value:e.editForm.year,callback:function(t){e.$set(e.editForm,"year",t)},expression:"editForm.year"}})],1)],1)],1),t("el-form-item",{attrs:{label:"头像"}},[t("div",{staticClass:"avatar-display"},[e.editForm.headimg&&""!==e.editForm.headimg?t("img",{staticClass:"detail-avatar",attrs:{src:e.editForm.headimg},on:{click:function(t){return e.showImage(e.editForm.headimg)}}}):t("div",{staticClass:"no-avatar-large"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",[e._v("无头像")])])])])],1):e._e()],1)]):e._e(),"member"===e.activeTab?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-medal"}),e._v(" 会员信息 ")]),t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(e._s(e.currentUserInfo.start_time||"未设置")+" ")]),t("el-descriptions-item",{attrs:{label:"会员年限"}},[e._v(e._s(e.currentUserInfo.year||0)+"年 ")]),t("el-descriptions-item",{attrs:{label:"注册时间"}},[e._v(e._s(e.currentUserInfo.create_time||"未知")+" ")]),t("el-descriptions-item",{attrs:{label:"最后登录"}},[e._v(e._s(e.currentUserInfo.last_login_time||"从未登录")+" ")]),t("el-descriptions-item",{attrs:{label:"会员到期"}},[e._v(e._s(e.currentUserInfo.end_time||"未设置")+" ")])],1)],1)]):e._e(),"debts"===e.activeTab?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-document"}),e._v(" 债务人信息 "),t("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"mini"},on:{click:e.addDebt}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 添加债务人 ")])],1),e.currentUserInfo.debts&&e.currentUserInfo.debts.length>0?t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.currentUserInfo.debts||[],size:"small"}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名",width:"120"}}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话",width:"130"}}),t("el-table-column",{attrs:{prop:"money",label:"债务金额（元）",width:"120"}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:"已完成"===a.row.status?"success":"处理中"===a.row.status?"warning":"info",size:"small"}},[e._v(" "+e._s(a.row.status)+" ")])]}}],null,!1,3932961750)}),t("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editDebt(a.row,a.$index)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.deleteDebt(a.$index)}}},[e._v("删除")])]}}],null,!1,1147729191)})],1):t("div",{staticClass:"no-data"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("暂无债务人信息")]),t("br"),t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.addDebt}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 添加第一个债务人 ")])],1)],1)]):e._e(),"attachments"===e.activeTab?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-folder-opened"}),e._v(" 附件信息 "),t("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"mini"},on:{click:e.addAttachment}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 上传附件 ")])],1),t("div",{staticClass:"attachment-grid"},[t("div",{staticClass:"attachment-item"},[t("div",{staticClass:"attachment-title"},[e._v("身份证照片")]),t("div",{staticClass:"attachment-content"},[e.currentUserInfo.attachments&&e.currentUserInfo.attachments.idCard&&e.currentUserInfo.attachments.idCard.length>0?t("div",{staticClass:"image-list"},e._l(e.currentUserInfo.attachments.idCard,(function(a,i){return t("div",{key:i,staticClass:"image-item"},[t("img",{staticClass:"attachment-image",attrs:{src:a.url},on:{click:function(t){return e.showImage(a.url)}}}),t("div",{staticClass:"image-overlay"},[t("div",{staticClass:"image-info"},[t("span",{staticClass:"file-name"},[e._v(e._s(a.name))]),t("span",{staticClass:"upload-time"},[e._v(e._s(a.uploadTime))])]),t("div",{staticClass:"image-actions"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(t){return e.downloadFile(a)}}},[e._v("下载")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.deleteAttachment("idCard",i)}}},[e._v("删除")])],1)])])})),0):t("div",{staticClass:"no-attachment"},[t("i",{staticClass:"el-icon-picture"}),t("span",[e._v("暂无身份证照片")])]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.uploadIdCard}},[t("i",{staticClass:"el-icon-upload"}),e._v(" 上传身份证 ")])],1)]),t("div",{staticClass:"attachment-item"},[t("div",{staticClass:"attachment-title"},[e._v("营业执照")]),t("div",{staticClass:"attachment-content"},[e.currentUserInfo.attachments&&e.currentUserInfo.attachments.license&&e.currentUserInfo.attachments.license.length>0?t("div",{staticClass:"image-list"},e._l(e.currentUserInfo.attachments.license,(function(a,i){return t("div",{key:i,staticClass:"image-item"},[t("img",{staticClass:"attachment-image",attrs:{src:a.url},on:{click:function(t){return e.showImage(a.url)}}}),t("div",{staticClass:"image-overlay"},[t("div",{staticClass:"image-info"},[t("span",{staticClass:"file-name"},[e._v(e._s(a.name))]),t("span",{staticClass:"upload-time"},[e._v(e._s(a.uploadTime))])]),t("div",{staticClass:"image-actions"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(t){return e.downloadFile(a)}}},[e._v("下载")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.deleteAttachment("license",i)}}},[e._v("删除")])],1)])])})),0):t("div",{staticClass:"no-attachment"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("暂无营业执照")])]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.uploadLicense}},[t("i",{staticClass:"el-icon-upload"}),e._v(" 上传营业执照 ")])],1)]),t("div",{staticClass:"attachment-item"},[t("div",{staticClass:"attachment-title"},[e._v("其他附件")]),t("div",{staticClass:"attachment-content"},[e.currentUserInfo.attachments&&e.currentUserInfo.attachments.others&&e.currentUserInfo.attachments.others.length>0?t("div",{staticClass:"file-list"},e._l(e.currentUserInfo.attachments.others,(function(a,i){return t("div",{key:i,staticClass:"file-item"},[t("div",{staticClass:"file-icon"},[t("i",{staticClass:"file-type-icon",class:e.getFileIcon(a.type)})]),t("div",{staticClass:"file-info"},[t("div",{staticClass:"file-name"},[e._v(e._s(a.name))]),t("div",{staticClass:"file-meta"},[t("span",{staticClass:"file-size"},[e._v(e._s(e.formatFileSize(a.size)))]),t("span",{staticClass:"upload-time"},[e._v(e._s(a.uploadTime))])])]),t("div",{staticClass:"file-actions"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(t){return e.downloadFile(a)}}},[e._v("下载")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.deleteAttachment("others",i)}}},[e._v("删除")])],1)])})),0):t("div",{staticClass:"no-attachment"},[t("i",{staticClass:"el-icon-folder"}),t("span",[e._v("暂无其他附件")])]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.uploadOthers}},[t("i",{staticClass:"el-icon-upload"}),e._v(" 上传其他附件 ")])],1)])])])]):e._e()])])]),t("el-drawer",{attrs:{title:"编辑用户",visible:e.drawerEditVisible,direction:"rtl",size:"50%","before-close":e.handleDrawerClose},on:{"update:visible":function(t){e.drawerEditVisible=t}}},[t("div",{staticClass:"drawer-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-user"}),e._v(" 客户信息 ")]),t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.ruleForm.company)+" ")]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.ruleForm.phone)+" ")]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.ruleForm.nickname)+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.ruleForm.linkman)+" ")]),t("el-descriptions-item",{attrs:{label:"头像",span:2}},[t("div",{staticClass:"avatar-display"},[e.ruleForm.headimg&&""!==e.ruleForm.headimg?t("img",{staticClass:"detail-avatar",attrs:{src:e.ruleForm.headimg},on:{click:function(t){return e.showImage(e.ruleForm.headimg)}}}):t("div",{staticClass:"no-avatar-large"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",[e._v("无头像")])])])]),t("el-descriptions-item",{attrs:{label:"用户来源",span:2}},[t("el-tag",{attrs:{size:"small",type:"info"}},[e._v(e._s(e.ruleForm.yuangong_id||"直接注册"))])],1)],1)],1),t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-edit"}),e._v(" 信息编辑 ")]),t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"公司名称","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.company,callback:function(t){e.$set(e.ruleForm,"company",t)},expression:"ruleForm.company"}})],1),t("el-form-item",{attrs:{label:"联系人","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkman,callback:function(t){e.$set(e.ruleForm,"linkman",t)},expression:"ruleForm.linkman"}})],1),t("el-form-item",{attrs:{label:"联系方式","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkphone,callback:function(t){e.$set(e.ruleForm,"linkphone",t)},expression:"ruleForm.linkphone"}})],1),t("el-form-item",{attrs:{label:"登录密码","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),t("el-form-item",{attrs:{label:"调解员",prop:"tiaojie_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.tiaojie_id,callback:function(t){e.$set(e.ruleForm,"tiaojie_id",t)},expression:"ruleForm.tiaojie_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.tiaojies,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"法务专员",prop:"fawu_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.fawu_id,callback:function(t){e.$set(e.ruleForm,"fawu_id",t)},expression:"ruleForm.fawu_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.fawus,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"立案专员",prop:"lian_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.lian_id,callback:function(t){e.$set(e.ruleForm,"lian_id",t)},expression:"ruleForm.lian_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lians,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"合同上传专用",prop:"htsczy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.htsczy_id,callback:function(t){e.$set(e.ruleForm,"htsczy_id",t)},expression:"ruleForm.htsczy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.htsczy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"律师",prop:"ls_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ls_id,callback:function(t){e.$set(e.ruleForm,"ls_id",t)},expression:"ruleForm.ls_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ls,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"业务员",prop:"ywy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ywy_id,callback:function(t){e.$set(e.ruleForm,"ywy_id",t)},expression:"ruleForm.ywy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ywy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{"label-width":e.formLabelWidth,label:"营业执照"}},[""!=e.ruleForm.license&&null!=e.ruleForm.license?t("img",{staticStyle:{width:"400px",height:"400px"},attrs:{src:e.ruleForm.license},on:{click:function(t){return e.showImage(e.ruleForm.license)}}}):e._e()]),t("el-form-item",{attrs:{label:"开始时间","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleForm.start_time,callback:function(t){e.$set(e.ruleForm,"start_time",t)},expression:"ruleForm.start_time"}})],1),t("el-form-item",{attrs:{label:"会员年限","label-width":e.formLabelWidth}},[t("el-input-number",{attrs:{min:0,max:99,label:"请输入年份"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,"year",t)},expression:"ruleForm.year"}})],1)],1),t("div",{staticClass:"drawer-footer"},[t("el-button",{on:{click:function(t){e.drawerEditVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("保 存")])],1)],1)])]),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-dialog",{attrs:{title:"制作订单",visible:e.dialogFormOrder,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogFormOrder=t}}},[t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company)+" ")]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone)+" ")]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname)+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman)+" ")]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.ruleForm.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id)+" ")]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")])],1)],1),t("el-descriptions",{attrs:{title:"下单内容"}}),t("el-form",{ref:"orderForm",attrs:{model:e.orderForm,rules:e.rules2,"label-width":"80px",mode:"left"}},[t("el-form-item",{attrs:{label:"套餐",prop:"taocan_id"}},[t("el-select",{attrs:{placeholder:"请选择"},on:{change:e.changeTaocan},model:{value:e.orderForm.taocan_id,callback:function(t){e.$set(e.orderForm,"taocan_id",t)},expression:"orderForm.taocan_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.taocans,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"总金额"}},[t("el-input",{staticClass:"el_input2",attrs:{type:"number",placeholder:"请输入内容"},model:{value:e.orderForm.total_price,callback:function(t){e.$set(e.orderForm,"total_price",t)},expression:"orderForm.total_price"}})],1),t("el-form-item",{attrs:{label:"实际支付",prop:"pay_price"}},[t("el-input",{staticClass:"el_input2",attrs:{placeholder:"请输入内容"},model:{value:e.orderForm.pay_price,callback:function(t){e.$set(e.orderForm,"pay_price",t)},expression:"orderForm.pay_price"}})],1),t("el-form-item",{attrs:{label:"客户描述"}},[t("el-input",{staticClass:"el_input2",attrs:{type:"textarea",rows:3,placeholder:"请输入内容"},model:{value:e.orderForm.desc,callback:function(t){e.$set(e.orderForm,"desc",t)},expression:"orderForm.desc"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormOrder=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData2()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"导入用户",visible:e.uploadVisible,width:"30%"},on:{"update:visible":function(t){e.uploadVisible=t},close:e.closeUploadDialog}},[t("el-form",{ref:"uploadForm",attrs:{"label-position":"right","label-width":"110px"}},[t("el-form-item",{attrs:{label:"选择文件:"}},[t("el-upload",{ref:"upload",attrs:{"auto-upload":!1,action:e.uploadAction,data:e.uploadData,"on-success":e.uploadSuccess,"before-upload":e.checkFile,accept:".xls,.xlsx",limit:"1",multiple:"false"}},[t("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选择文件")])],1)],1),t("div",{staticStyle:{"text-align":"right"}},[t("el-button",{attrs:{type:"primary",size:"small",loading:e.submitOrderLoading2},on:{click:e.submitUpload}},[e._v("提交 ")]),t("el-button",{attrs:{size:"small"},on:{click:e.closeDialog}},[e._v("取消")])],1)],1)],1),t("el-dialog",{attrs:{title:"用户详情",visible:e.dialogViewUserDetail,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogViewUserDetail=t}}},[t("user-details",{attrs:{id:e.currentId}})],1),t("el-dialog",{attrs:{title:"新增用户",visible:e.dialogAddUser,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogAddUser=t}}},[t("el-descriptions",{attrs:{title:"信息添加"}}),t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"手机账号","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),t("el-form-item",{attrs:{label:"公司名称","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.company,callback:function(t){e.$set(e.ruleForm,"company",t)},expression:"ruleForm.company"}})],1),t("el-form-item",{attrs:{label:"联系人","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkman,callback:function(t){e.$set(e.ruleForm,"linkman",t)},expression:"ruleForm.linkman"}})],1),t("el-form-item",{attrs:{label:"联系方式","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkphone,callback:function(t){e.$set(e.ruleForm,"linkphone",t)},expression:"ruleForm.linkphone"}})],1),t("el-form-item",{attrs:{label:"登录密码","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),t("el-form-item",{attrs:{label:"调解员",prop:"tiaojie_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.tiaojie_id,callback:function(t){e.$set(e.ruleForm,"tiaojie_id",t)},expression:"ruleForm.tiaojie_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.tiaojies,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"法务专员",prop:"fawu_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.fawu_id,callback:function(t){e.$set(e.ruleForm,"fawu_id",t)},expression:"ruleForm.fawu_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.fawus,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"立案专员",prop:"lian_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.lian_id,callback:function(t){e.$set(e.ruleForm,"lian_id",t)},expression:"ruleForm.lian_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lians,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"合同上传专用",prop:"htsczy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.htsczy_id,callback:function(t){e.$set(e.ruleForm,"htsczy_id",t)},expression:"ruleForm.htsczy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.htsczy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"律师",prop:"ls_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ls_id,callback:function(t){e.$set(e.ruleForm,"ls_id",t)},expression:"ruleForm.ls_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ls,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"业务员",prop:"ywy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ywy_id,callback:function(t){e.$set(e.ruleForm,"ywy_id",t)},expression:"ruleForm.ywy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ywy,(function(e,a){return t("el-option",{key:a,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{"label-width":e.formLabelWidth,label:"营业执照"}},[""!=e.ruleForm.license&&null!=e.ruleForm.license?t("img",{staticStyle:{width:"400px",height:"400px"},attrs:{src:e.ruleForm.license},on:{click:function(t){return e.showImage(e.ruleForm.license)}}}):e._e()]),t("el-form-item",{attrs:{label:"开始时间","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleForm.start_time,callback:function(t){e.$set(e.ruleForm,"start_time",t)},expression:"ruleForm.start_time"}})],1),t("el-form-item",{attrs:{label:"会员年限","label-width":e.formLabelWidth}},[t("el-input-number",{attrs:{min:0,max:99,label:"请输入年份"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,"year",t)},expression:"ruleForm.year"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogAddUser=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{visible:e.dialogVisible,width:"50%",center:""},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{staticStyle:{width:"100%",height:"auto"},attrs:{src:e.show_image}})]),t("el-dialog",{attrs:{title:e.debtDialogTitle,visible:e.debtDialogVisible,width:"500px"},on:{"update:visible":function(t){e.debtDialogVisible=t},close:e.closeDebtDialog}},[t("el-form",{ref:"debtForm",attrs:{model:e.debtForm,rules:e.debtRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"债务人姓名",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入债务人姓名"},model:{value:e.debtForm.name,callback:function(t){e.$set(e.debtForm,"name",t)},expression:"debtForm.name"}})],1),t("el-form-item",{attrs:{label:"债务人电话",prop:"tel"}},[t("el-input",{attrs:{placeholder:"请输入债务人电话"},model:{value:e.debtForm.tel,callback:function(t){e.$set(e.debtForm,"tel",t)},expression:"debtForm.tel"}})],1),t("el-form-item",{attrs:{label:"债务金额",prop:"money"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入债务金额"},model:{value:e.debtForm.money,callback:function(t){e.$set(e.debtForm,"money",t)},expression:"debtForm.money"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择状态"},model:{value:e.debtForm.status,callback:function(t){e.$set(e.debtForm,"status",t)},expression:"debtForm.status"}},[t("el-option",{attrs:{label:"待处理",value:"待处理"}}),t("el-option",{attrs:{label:"处理中",value:"处理中"}}),t("el-option",{attrs:{label:"已完成",value:"已完成"}})],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.closeDebtDialog}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveDebt}},[e._v("确定")])],1)],1),t("el-dialog",{attrs:{title:"导入用户",visible:e.uploadVisible,width:"600px"},on:{"update:visible":function(t){e.uploadVisible=t},close:e.closeUploadDialog}},[t("div",{staticClass:"upload-container"},[t("div",{staticClass:"upload-tips"},[t("el-alert",{attrs:{title:"导入说明",type:"info",closable:!1,"show-icon":""}},[t("div",{attrs:{slot:"description"},slot:"description"},[t("p",[e._v("1. 请先下载导入模板，按照模板格式填写用户信息")]),t("p",[e._v("2. 支持的文件格式：.xls, .xlsx")]),t("p",[e._v("3. 单次最多导入1000条用户数据")]),t("p",[e._v("4. 手机号码为必填项，且不能重复")])])])],1),t("div",{staticClass:"upload-actions"},[t("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:e.downloadTemplate}},[e._v(" 下载导入模板 ")])],1),t("div",{staticClass:"upload-area"},[t("el-upload",{ref:"upload",attrs:{action:e.uploadAction,data:e.uploadData,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"before-upload":e.beforeUpload,"auto-upload":!1,limit:1,"file-list":e.fileList,accept:".xls,.xlsx",drag:""}},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传xls/xlsx文件，且不超过10MB")])])],1),t("div",{staticClass:"upload-options"},[t("el-checkbox",{model:{value:e.uploadData.review,callback:function(t){e.$set(e.uploadData,"review",t)},expression:"uploadData.review"}},[e._v("导入前预览数据")])],1)]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.closeUploadDialog}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.submitOrderLoading2},on:{click:e.submitUpload}},[e._v(" "+e._s(e.submitOrderLoading2?"导入中...":"开始导入")+" ")])],1)])],1)},s=[],l=(a("14d9"),a("13d5"),a("88a7"),a("271a"),a("5494"),a("d522")),r={name:"list",components:{UserDetails:l["a"]},data(){return{uploadAction:"/admin/user/import?token="+this.$store.getters.GET_TOKEN,uploadVisible:!1,submitOrderLoading2:!1,uploadData:{review:!1},fileList:[],allSize:"mini",list:[],total:1,page:1,size:20,currentId:0,currentUserInfo:{},search:{nickname:"",phone:"",linkman:"",linkphone:"",company:"",yuangong_id:"",dateRange:[],prop:"",order:""},is_del:!1,loading:!0,url:"/user/",title:"用户",info:{},selectedUsers:[],dialogFormVisible:!1,dialogViewUserDetail:!1,dialogAddUser:!1,drawerViewVisible:!1,drawerEditVisible:!1,isEditMode:!1,editForm:{},originalUserInfo:{},activeTab:"customer",show_image:"",dialogVisible:!1,debtDialogVisible:!1,debtDialogTitle:"添加债务人",isEditingDebt:!1,editingDebtIndex:-1,debtForm:{name:"",tel:"",money:"",status:"待处理"},debtRules:{name:[{required:!0,message:"请输入债务人姓名",trigger:"blur"}],tel:[{required:!0,message:"请输入债务人电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],money:[{required:!0,message:"请输入债务金额",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}]},formLabelWidth:"120px",dialogFormOrder:!1,taocans:[],tiaojies:[],fawus:[],lians:[],htsczy:[],ls:[],ywy:[],orderForm:{client_id:"",taocan_id:"",tiaojie_id:"",fawu_id:"",lian_id:"",htsczy_id:"",ls_id:"",ywy_id:"",total_price:"",pay_price:0,pay_path:"",desc:"",pay_type:1,qishu:2,taocan_year:"",taocan_content:[],taocan_type:1,fenqi:[{date:"",price:"",pay_path:""},{date:"",price:"",pay_path:""}]},rules2:{taocan_id:[{required:!0,message:"请选择套餐",trigger:"blur"}],pay_path:[{required:!0,message:"请上传凭证",trigger:"blur"}],taocan_year:[{required:!0,message:"请填写年份",trigger:"blur"}],pay_price:[{required:!0,message:"请填写支付金额",trigger:"blur"}],desc:[{required:!0,message:"请填写内容",trigger:"blur"}]}}},mounted(){this.addTestData()},methods:{getOriginalTestData(){return[{id:1,phone:"13800138001",nickname:"张三",company:"北京科技有限公司",linkman:"张三",linkphone:"13800138001",yuangong_id:"小程序注册",end_time:"2024-12-31",create_time:"2023-01-15 10:30:00",last_login_time:"2024-01-20 15:45:00",headimg:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",license:"",start_time:"2023-01-15",year:2,lian_name:"陈专员",tiaojie_name:"朱调解",fawu_name:"严法务",htsczy_name:"合同专员",ls_name:"王律师",ywy_name:"业务员张三",debts:[{name:"王某某",tel:"13912345678",money:"50000",status:"处理中"},{name:"李某某",tel:"13987654321",money:"30000",status:"已完成"}],attachments:{idCard:[{url:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",name:"身份证正面.jpg"},{url:"https://cube.elemecdn.com/3/7c/********************************.png",name:"身份证反面.jpg"}],license:[{url:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",name:"营业执照.jpg"}],others:[{name:"合同文件.pdf",url:"/files/contract.pdf",type:"pdf"}]}},{id:2,phone:"13900139002",nickname:"李四",company:"上海贸易公司",linkman:"李四",linkphone:"13900139002",yuangong_id:"后台创建",end_time:"2024-06-30",create_time:"2023-02-20 14:20:00",last_login_time:"2024-01-18 09:15:00",headimg:"",license:"",start_time:"2023-02-20",year:1,lian_name:"李专员",tiaojie_name:"调解员王五",fawu_name:"法务李四",htsczy_name:"合同专员B",ls_name:"律师张三",ywy_name:"业务员李四",debts:[{name:"赵某某",tel:"13811112222",money:"80000",status:"处理中"}]},{id:3,phone:"13700137003",nickname:"王五",company:"深圳创新科技",linkman:"王五",linkphone:"13700137003",yuangong_id:"小程序注册",end_time:"2025-03-15",create_time:"2023-03-10 16:40:00",last_login_time:"",headimg:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",license:"",start_time:"2023-03-10",year:2,debts:[{name:"陈某某",tel:"13765432109",money:"80000",status:"待处理"}]},{id:4,phone:"13600136004",nickname:"赵六",company:"广州物流集团",linkman:"赵六",linkphone:"13600136004",yuangong_id:"后台创建",end_time:"2024-09-20",create_time:"2023-04-05 11:30:00",last_login_time:"2024-01-19 14:22:00",headimg:"",license:"",start_time:"2023-04-05",year:1,debts:[]},{id:5,phone:"13500135005",nickname:"孙七",company:"杭州电商有限公司",linkman:"孙七",linkphone:"13500135005",yuangong_id:"小程序注册",end_time:"2024-11-10",create_time:"2023-05-12 09:15:00",last_login_time:"2024-01-21 16:30:00",headimg:"https://cube.elemecdn.com/3/7c/********************************.png",license:"",start_time:"2023-05-12",year:1,debts:[{name:"赵某某",tel:"13654321098",money:"25000",status:"已完成"},{name:"钱某某",tel:"13543210987",money:"15000",status:"处理中"}]},{id:6,phone:"13400134006",nickname:"周八",company:"成都软件开发",linkman:"周八",linkphone:"13400134006",yuangong_id:"小程序注册",end_time:"2024-08-15",create_time:"2023-06-18 13:25:00",last_login_time:"2024-01-22 10:12:00",headimg:"",license:"",start_time:"2023-06-18",year:1,debts:[]},{id:7,phone:"13300133007",nickname:"吴九",company:"武汉贸易有限公司",linkman:"吴九",linkphone:"13300133007",yuangong_id:"后台创建",end_time:"2024-10-30",create_time:"2023-07-22 15:45:00",last_login_time:"",headimg:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",license:"",start_time:"2023-07-22",year:1,debts:[]}]},addTestData(){this.list=this.getOriginalTestData(),this.total=this.list.length,this.loading=!1},filterTestData(){this.loading=!0;const e=this.getOriginalTestData();let t=[...e];if(this.search.nickname){const e=this.search.nickname.toLowerCase();t=t.filter(t=>t.nickname&&t.nickname.toLowerCase().includes(e))}if(this.search.phone&&(t=t.filter(e=>e.phone&&e.phone.includes(this.search.phone))),this.search.linkman){const e=this.search.linkman.toLowerCase();t=t.filter(t=>t.linkman&&t.linkman.toLowerCase().includes(e))}if(this.search.linkphone&&(t=t.filter(e=>e.linkphone&&e.linkphone.includes(this.search.linkphone))),this.search.company){const e=this.search.company.toLowerCase();t=t.filter(t=>t.company&&t.company.toLowerCase().includes(e))}if(this.search.yuangong_id&&(t=t.filter(e=>e.yuangong_id===this.search.yuangong_id)),this.search.dateRange&&2===this.search.dateRange.length){const e=new Date(this.search.dateRange[0]),a=new Date(this.search.dateRange[1]);t=t.filter(t=>{if(t.create_time){const i=new Date(t.create_time.split(" ")[0]);return i>=e&&i<=a}return!1})}this.list=t,this.total=t.length,this.loading=!1;const a=this.search.nickname||this.search.phone||this.search.linkman||this.search.linkphone||this.search.company||this.search.yuangong_id||this.search.dateRange&&2===this.search.dateRange.length;a&&this.$message.success(`搜索完成，找到 ${t.length} 条匹配记录`)},order(e){this.dialogFormOrder=!0,this.info=e,this.orderForm={client_id:e.id,taocan_id:"",total_price:"",pay_price:0,pay_path:"",desc:"",pay_type:1},this.$nextTick(()=>{this.getTaocans()})},saveData2(){let e=this;this.$refs["orderForm"].validate(t=>{if(!t)return!1;this.postRequest("/dingdan/save",this.orderForm).then(t=>{200==t.code&&(e.$message({type:"success",message:t.msg}),e.dialogFormOrder=!1)})})},changeTaocan(e){this.orderForm.taocan_content=[],this.orderForm.taocan_type=1,this.getRequest("/taocan/read?id="+e).then(e=>{200==e.code&&(this.orderForm.total_price=e.data.price,this.orderForm.pay_price=e.data.price)})},getTaocans(){this.postRequest("/taocan/getList",{}).then(e=>{200==e.code&&(this.taocans=e.data)})},getYuangongs(){let e=this;this.postRequest("/yuangong/getList",{}).then(t=>{200==t.code&&(e.tiaojies=t.data.filter(e=>6==e.zhiwei_id),e.fawus=t.data.filter(e=>5==e.zhiwei_id),e.lians=t.data.filter(e=>12==e.zhiwei_id),e.ywy=t.data.filter(e=>3==e.zhiwei_id),e.ls=t.data.filter(e=>4==e.zhiwei_id),e.htsczy=t.data.filter(e=>9==e.zhiwei_id))})},viewData(e){console.log("viewData called with id:",e);let t=this;0!=e&&(this.currentId=e,this.currentUserInfo=this.list.find(t=>t.id===e)||{},console.log("Found user info:",this.currentUserInfo),this.isEditMode=!1,this.editForm={},this.originalUserInfo={},this.activeTab="customer"),t.drawerViewVisible=!0,console.log("Drawer should be visible:",t.drawerViewVisible)},handleTabSelect(e){this.activeTab=e,"customer"!==e&&(this.isEditMode=!1)},toggleEditMode(){this.isEditMode?this.isEditMode=!1:(this.isEditMode=!0,this.originalUserInfo=JSON.parse(JSON.stringify(this.currentUserInfo)),this.editForm=JSON.parse(JSON.stringify(this.currentUserInfo)),this.getYuangongs())},cancelEdit(){this.currentUserInfo=JSON.parse(JSON.stringify(this.originalUserInfo)),this.isEditMode=!1,this.editForm={},this.$message.info("已取消编辑")},saveUserData(){this.$refs.editForm.validate(e=>{if(!e)return this.$message.error("请检查表单填写是否正确"),!1;{this.currentUserInfo=JSON.parse(JSON.stringify(this.editForm));const e=this.list.findIndex(e=>e.id===this.currentUserInfo.id);-1!==e&&this.list.splice(e,1,this.currentUserInfo),this.isEditMode=!1,this.editForm={},this.$message.success("保存成功！")}})},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""},t.drawerEditVisible=!0,t.getYuangongs()},handleDrawerClose(){this.drawerViewVisible=!1,this.drawerEditVisible=!1,this.isEditMode=!1,this.editForm={},this.originalUserInfo={},this.activeTab="customer"},addDebt(){this.debtDialogVisible=!0,this.debtForm={name:"",tel:"",money:"",status:"待处理"},this.debtDialogTitle="添加债务人",this.isEditingDebt=!1},editDebt(e,t){this.debtDialogVisible=!0,this.debtForm={name:e.name,tel:e.tel,money:parseFloat(e.money),status:e.status},this.debtDialogTitle="编辑债务人",this.isEditingDebt=!0,this.editingDebtIndex=t},saveDebt(){this.$refs.debtForm.validate(e=>{if(!e)return this.$message.error("请检查表单填写是否正确"),!1;{const e={name:this.debtForm.name,tel:this.debtForm.tel,money:this.debtForm.money.toString(),status:this.debtForm.status};this.isEditingDebt?(this.currentUserInfo.debts[this.editingDebtIndex]=e,this.$message.success("债务人信息修改成功！")):(this.currentUserInfo.debts||(this.currentUserInfo.debts=[]),this.currentUserInfo.debts.push(e),this.$message.success("债务人添加成功！"));const t=this.list.findIndex(e=>e.id===this.currentUserInfo.id);-1!==t&&(this.list[t].debts=[...this.currentUserInfo.debts]),this.closeDebtDialog()}})},closeDebtDialog(){this.debtDialogVisible=!1,this.debtForm={name:"",tel:"",money:"",status:"待处理"},this.isEditingDebt=!1,this.editingDebtIndex=-1,this.debtDialogTitle="添加债务人",this.$nextTick(()=>{this.$refs.debtForm&&this.$refs.debtForm.clearValidate()})},deleteDebt(e){this.$confirm("确定要删除这个债务人吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.currentUserInfo.debts.splice(e,1),this.$message.success("删除成功！")})},addAttachment(){this.$message.info("请选择具体的附件类型进行上传")},uploadIdCard(){this.createFileInput("image/*",e=>{this.handleFileUpload(e,"idCard","身份证照片")})},uploadLicense(){this.createFileInput("image/*",e=>{this.handleFileUpload(e,"license","营业执照")})},uploadOthers(){this.createFileInput("*",e=>{this.handleFileUpload(e,"others","其他附件")})},createFileInput(e,t){const a=document.createElement("input");a.type="file",a.accept=e,a.multiple=!0,a.style.display="none",a.onchange=e=>{const i=Array.from(e.target.files);i.length>0&&t(i),document.body.removeChild(a)},document.body.appendChild(a),a.click()},handleFileUpload(e,t,a){if(e&&0!==e.length){for(let i of e){if("others"!==t&&!i.type.startsWith("image/"))return void this.$message.error(a+"只能上传图片文件");if(i.size>10485760)return void this.$message.error(`文件 ${i.name} 大小超过10MB限制`)}this.currentUserInfo.attachments||(this.currentUserInfo.attachments={}),this.currentUserInfo.attachments[t]||(this.currentUserInfo.attachments[t]=[]),this.$message.info(`正在上传 ${e.length} 个文件...`),e.forEach((i,s)=>{const l=URL.createObjectURL(i);setTimeout(()=>{const r={name:i.name,url:l,size:i.size,type:i.type,uploadTime:(new Date).toLocaleString()};this.currentUserInfo.attachments[t].push(r),s===e.length-1&&this.$message.success(`${a}上传完成！共上传 ${e.length} 个文件`)},500*(s+1))})}else this.$message.warning("请选择要上传的文件")},deleteAttachment(e,t){this.$confirm("确定要删除这个附件吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{if(this.currentUserInfo.attachments||(this.currentUserInfo.attachments={}),this.currentUserInfo.attachments[e]){const a=this.currentUserInfo.attachments[e][t];a&&a.url&&a.url.startsWith("blob:")&&URL.revokeObjectURL(a.url),this.currentUserInfo.attachments[e].splice(t,1),this.$message.success("删除成功！")}})},downloadFile(e){if(e&&e.url)try{const t=document.createElement("a");t.href=e.url,t.download=e.name||"附件",t.style.display="none",document.body.appendChild(t),t.click(),document.body.removeChild(t),this.$message.success("开始下载: "+e.name)}catch(t){this.$message.error("下载失败，请重试"),console.error("下载错误:",t)}else this.$message.error("文件链接无效")},getFileIcon(e){return e?e.startsWith("image/")?"el-icon-picture":e.includes("pdf")||e.includes("word")||e.includes("doc")?"el-icon-document":e.includes("excel")||e.includes("sheet")?"el-icon-s-grid":e.includes("zip")||e.includes("rar")?"el-icon-folder-opened":e.includes("video")?"el-icon-video-camera":e.includes("audio")?"el-icon-headset":"el-icon-document":"el-icon-document"},formatFileSize(e){if(0===e)return"0 B";const t=1024,a=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+a[i]},getDebtCount(e){return e&&e.length?e.length:0},getDebtCountType(e){const t=this.getDebtCount(e);return 0===t?"info":t<=2?"success":t<=5?"warning":"danger"},getTotalDebtAmount(e){return e&&e.length?e.reduce((e,t)=>e+(parseFloat(t.money)||0),0):0},formatAmount(e){return 0===e?"0":e.toLocaleString("zh-CN",{minimumFractionDigits:0,maximumFractionDigits:2})},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(e.data.tiaojie_id=0==e.data.tiaojie_id?"":e.data.tiaojie_id,e.data.fawu_id=0==e.data.fawu_id?"":e.data.fawu_id,e.data.lian_id=0==e.data.lian_id?"":e.data.lian_id,e.data.ywy_id=0==e.data.ywy_id?"":e.data.ywy_id,e.data.htsczy_id=0==e.data.htsczy_id?"":e.data.htsczy_id,e.data.ls_id=0==e.data.ls_id?"":e.data.ls_id,t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.postRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.filterTestData()},resetSearch(){this.search={nickname:"",phone:"",linkman:"",linkphone:"",company:"",yuangong_id:"",dateRange:[],prop:"",order:""},this.page=1,this.size=20,this.addTestData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count,"超级管理员"==t.msg&&(e.is_del=!0)),e.loading=!1})},saveData(){let e=this;console.log(this.ruleForm),this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.addTestData(),e.dialogFormVisible=!1,e.dialogAddUser=!1,e.drawerEditVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.addTestData()},handleCurrentChange(e){this.page=e,this.addTestData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let a=this;a.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(a.ruleForm[t]="",a.$message.success("删除成功!")):a.$message.error(e.msg)})},handleSortChange({column:e,prop:t,order:a}){this.search.prop=t,this.search.order=a,this.addTestData()},exports:function(){let e=this;location.href="/admin/user/export2?token="+e.$store.getters.GET_TOKEN+"&keyword="+e.search.keyword},closeUploadDialog(){this.uploadVisible=!1,this.$refs.upload.clearFiles(),this.uploadData.review=!1},uploadSuccess(e){200===e.code?(this.$message({type:"success",message:e.msg}),this.uploadVisible=!1,this.addTestData(),console.log(e)):this.$message({type:"warning",message:e.msg}),this.submitOrderLoading2=!1,this.$refs.upload.clearFiles()},checkFile(e){let t=["xls","xlsx"],a=e.name.split(".").slice(-1)[0].toLowerCase();return!!t.includes(a)||(this.$message({type:"warning",message:"文件格式错误仅支持 xls xlxs 文件"}),!1)},submitUpload(){this.submitOrderLoading2=!0,this.$refs.upload.submit()},closeDialog(){this.addVisible=!1,this.uploadVisible=!1,this.form={id:"",nickname:"",mobile:"",school_id:0,grade_id:"",class_id:"",sex:"",is_poor:"",is_display:"",number:"",remark:"",is_remark_option:0,remark_option:[],mobile_checked:!1},this.$refs.form.resetFields()},openUpload(){this.uploadVisible=!0,this.fileList=[],this.uploadData.review=!1},closeUploadDialog(){this.uploadVisible=!1,this.fileList=[],this.uploadData.review=!1,this.$refs.upload&&this.$refs.upload.clearFiles()},beforeUpload(e){const t="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type,a=e.size/1024/1024<10;return t?!!a||(this.$message.error("上传文件大小不能超过 10MB!"),!1):(this.$message.error("只能上传 Excel 文件!"),!1)},submitUpload(){0!==this.fileList.length?(this.submitOrderLoading2=!0,this.$refs.upload.submit()):this.$message.warning("请先选择要上传的文件")},handleUploadSuccess(e,t,a){this.submitOrderLoading2=!1,200===e.code?(this.$message.success(`导入成功！共导入 ${e.count||0} 条用户数据`),this.closeUploadDialog(),this.addTestData()):this.$message.error(e.msg||"导入失败")},handleUploadError(e,t,a){this.submitOrderLoading2=!1,this.$message.error("文件上传失败，请重试"),console.error("Upload error:",e)},addUser(){this.dialogAddUser=!0,this.ruleForm={},this.getYuangongs()},downloadTemplate(){const e="/import_templete/user.xls",t=document.createElement("a");t.href=e,t.download="用户导入模板.xls",document.body.appendChild(t),t.click(),document.body.removeChild(t),this.$message.success("模板下载中...")},handleSelectionChange(e){this.selectedUsers=e},exportSelectedData(){let e,t;if(this.selectedUsers.length>0){const a=this.selectedUsers.map(e=>e.id).join(",");e=`/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&ids=${a}`,t=`正在导出 ${this.selectedUsers.length} 个用户的数据`}else e=`/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&keyword=${this.search.keyword||""}`,t="正在导出全部用户数据";location.href=e,this.$message.success(t)},batchDeleteUsers(){0!==this.selectedUsers.length?this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{const e=this.selectedUsers.map(e=>e.id);e.forEach(e=>{const t=this.list.findIndex(t=>t.id===e);-1!==t&&this.list.splice(t,1)}),this.total=this.list.length,this.selectedUsers=[],this.$message.success(`成功删除 ${e.length} 个用户`),this.$refs.userTable.clearSelection()}).catch(()=>{this.$message.info("已取消删除操作")}):this.$message.warning("请先选择要删除的用户")}}},o=r,n=(a("85d3"),a("2877")),c=Object(n["a"])(o,i,s,!1,null,"4acd38ca",null);t["default"]=c.exports},d522:function(e,t,a){"use strict";var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-detail-container"},[t("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("i",{staticClass:"el-icon-user"}),t("span",{staticClass:"card-title"},[e._v("客户基本信息")])]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("公司名称")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.company||"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("手机号")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.phone||"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("客户姓名")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.nickname||"未填写"))])])])],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("联系人")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.linkman||"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("联系方式")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.linkphone||"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("用户来源")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.yuangong_id||"未填写"))])])])],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("开始时间")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.start_time||"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("会员年限")]),t("div",{staticClass:"info-value"},[e._v(e._s(e.info.year?e.info.year+"年":"未填写"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("头像")]),t("div",{staticClass:"info-value"},[e.info.headimg&&""!==e.info.headimg?t("el-avatar",{staticStyle:{cursor:"pointer"},attrs:{src:e.info.headimg,size:50},nativeOn:{click:function(t){return e.showImage(e.info.headimg)}}}):t("span",{staticClass:"no-data"},[e._v("未上传")])],1)])])],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:24}},[t("div",{staticClass:"info-item"},[t("div",{staticClass:"info-label"},[e._v("营业执照")]),t("div",{staticClass:"info-value"},[e.info.license&&""!==e.info.license?t("el-image",{staticStyle:{width:"100px",height:"100px",cursor:"pointer"},attrs:{src:e.info.license,fit:"cover"},on:{click:function(t){return e.showImage(e.info.license)}}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):t("span",{staticClass:"no-data"},[e._v("未上传")])],1)])])],1)],1),t("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("i",{staticClass:"el-icon-s-custom"}),t("span",{staticClass:"card-title"},[e._v("服务团队")])]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("调解员")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.tiaojie_name||"未分配"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("法务专员")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.fawu_name||"未分配"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("立案专员")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.lian_name||"未分配"))])])])],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("合同专员")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.htsczy_name||"未分配"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("律师")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.ls_name||"未分配"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"team-item"},[t("div",{staticClass:"team-role"},[e._v("业务员")]),t("div",{staticClass:"team-name"},[e._v(e._s(e.info.ywy_name||"未分配"))])])])],1)],1),t("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("i",{staticClass:"el-icon-money"}),t("span",{staticClass:"card-title"},[e._v("债务人信息")])]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.info.debts,size:"medium",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(e._s(a.row.name))])]}}])}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"phone-number"},[e._v(e._s(a.row.tel))])]}}])}),t("el-table-column",{attrs:{prop:"money",label:"债务金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"money-amount"},[e._v("¥"+e._s(a.row.money))])]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:"已完成"===a.row.status?"success":"warning",size:"small"}},[e._v(" "+e._s(a.row.status)+" ")])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewDebtDetail(a.row)}}},[t("i",{staticClass:"el-icon-view"}),e._v(" 详情 ")])]}}])})],1),e.info.debts&&0!==e.info.debts.length?e._e():t("div",{staticClass:"empty-data"},[t("i",{staticClass:"el-icon-document"}),t("p",[e._v("暂无债务人信息")])])],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},s=[],l={name:"UserDetails",props:{id:{type:[String,Number],required:!0}},data(){return{info:{},loading:!1,dialogVisible:!1,show_image:""}},watch:{id:{immediate:!0,handler(e){e&&0!=e&&(console.log("UserDetails 接收到 ID:",e),this.getInfo(e))}}},methods:{getInfo(e){let t=this;console.log("正在获取用户信息，ID:",e),t.loading=!0,setTimeout(()=>{const a={id:e,company:"测试公司有限公司",phone:"13800138001",nickname:"张三",linkman:"李四",headimg:"",yuangong_id:"微信小程序",linkphone:"13800138002",tiaojie_name:"王调解员",fawu_name:"赵法务",lian_name:"钱立案员",htsczy_name:"孙合同员",ls_name:"周律师",ywy_name:"吴业务员",license:"",start_time:"2024-01-01",year:1,debts:[{name:"债务人A",tel:"13900139001",money:"50000",status:"处理中"},{name:"债务人B",tel:"13900139002",money:"30000",status:"已完成"}]};t.info=a,t.loading=!1,console.log("用户数据加载完成:",a)},500)},showImage(e){this.show_image=e,this.dialogVisible=!0},viewDebtDetail(e){console.log("查看债务人详情:",e),this.$message.info("债务人详情功能待开发")}}},r=l,o=(a("0091"),a("2877")),n=Object(o["a"])(r,i,s,!1,null,"4468717a",null);t["a"]=n.exports},d58f:function(e,t,a){"use strict";var i=a("59ed"),s=a("7b0b"),l=a("44ad"),r=a("07fa"),o=TypeError,n="Reduce of empty array with no initial value",c=function(e){return function(t,a,c,d){var m=s(t),u=l(m),p=r(m);if(i(a),0===p&&c<2)throw new o(n);var h=e?p-1:0,f=e?-1:1;if(c<2)while(1){if(h in u){d=u[h],h+=f;break}if(h+=f,e?h<0:p<=h)throw new o(n)}for(;e?h>=0:p>h;h+=f)h in u&&(d=a(d,u[h],h,m));return d}};e.exports={left:c(!1),right:c(!0)}},d6d6:function(e,t,a){"use strict";var i=TypeError;e.exports=function(e,t){if(e<t)throw new i("Not enough arguments");return e}},edd0:function(e,t,a){"use strict";var i=a("13d2"),s=a("9bf2");e.exports=function(e,t,a){return a.get&&i(a.get,t,{getter:!0}),a.set&&i(a.set,t,{setter:!0}),s.f(e,t,a)}}}]);
//# sourceMappingURL=chunk-461f593c.d747781c.js.map