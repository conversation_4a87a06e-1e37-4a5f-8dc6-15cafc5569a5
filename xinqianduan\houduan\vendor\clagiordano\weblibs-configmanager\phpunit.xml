<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         verbose="true"
         printerClass="clagiordano\PhpunitResultPrinter\ResultPrinter"
>
    <testsuites>
        <testsuite name="Package Test Suite">
            <directory suffix=".php">./tests/</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist>
            <directory suffix=".php">src/</directory>
        </whitelist>
    </filter>

    <php>
        <ini name="display_errors" value="On"/>
        <ini name="error_reporting" value="E_ALL"/>
        <ini name="display_startup_errors" value="On"/>
    </php>

    <logging>
        <log type="coverage-html" target="build/coverage/"/>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
        <log type="coverage-crap4j" target="build/logs/crap4j.xml"/>
        <log type="junit" target="build/logs/junit.xml"/>
        <log type="coverage-text" target="build/logs/coverage.txt" showUncoveredFiles="true" />
    </logging>
</phpunit>
