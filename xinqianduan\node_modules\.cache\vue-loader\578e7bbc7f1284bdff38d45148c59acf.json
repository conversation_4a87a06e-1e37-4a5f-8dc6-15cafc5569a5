{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue?vue&type=template&id=527fd6ab&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\SystemMonitor.vue", "mtime": 1748604247125}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}