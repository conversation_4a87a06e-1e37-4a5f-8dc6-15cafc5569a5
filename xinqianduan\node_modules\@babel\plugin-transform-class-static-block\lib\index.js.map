{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCreateClassFeaturesPlugin", "generateUid", "scope", "denyList", "name", "uid", "i", "_generateUid", "has", "_default", "exports", "default", "declare", "types", "t", "template", "assertVersion", "version", "inherits", "undefined", "pre", "enableFeature", "file", "FEATURES", "staticBlocks", "visitor", "ClassBody", "classBody", "privateNames", "Set", "body", "get", "path", "isPrivate", "add", "node", "isStaticBlock", "staticBlockPrivateId", "staticBlockRef", "privateName", "identifier", "replacement", "blockBody", "length", "isExpressionStatement", "inheritsComments", "expression", "ast", "replaceWith", "classPrivateProperty"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { Scope } from \"@babel/core\";\n\nimport {\n  enableFeature,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\n\n/**\n * Generate a uid that is not in `denyList`\n *\n * @param {Scope} scope\n * @param {Set<string>} denyList a deny list that the generated uid should avoid\n * @returns\n */\nfunction generateUid(scope: Scope, denyList: Set<string>) {\n  const name = \"\";\n  let uid;\n  let i = 1;\n  do {\n    uid = scope._generateUid(name, i);\n    i++;\n  } while (denyList.has(uid));\n  return uid;\n}\n\nexport default declare(({ types: t, template, assertVersion, version }) => {\n  assertVersion(REQUIRED_VERSION(\"^7.12.0\"));\n\n  return {\n    name: \"transform-class-static-block\",\n    inherits:\n      USE_ESM || IS_STANDALONE || version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-class-static-block\").default,\n\n    pre() {\n      // Enable this in @babel/helper-create-class-features-plugin, so that it\n      // can be handled by the private fields and methods transform.\n      enableFeature(this.file, FEATURES.staticBlocks, /* loose */ false);\n    },\n\n    visitor: {\n      // Run on ClassBody and not on class so that if @babel/helper-create-class-features-plugin\n      // is enabled it can generate optimized output without passing from the intermediate\n      // private fields representation.\n      ClassBody(classBody) {\n        const { scope } = classBody;\n        const privateNames = new Set<string>();\n        const body = classBody.get(\"body\");\n        for (const path of body) {\n          if (path.isPrivate()) {\n            privateNames.add(path.get(\"key.id\").node.name);\n          }\n        }\n        for (const path of body) {\n          if (!path.isStaticBlock()) continue;\n          const staticBlockPrivateId = generateUid(scope, privateNames);\n          privateNames.add(staticBlockPrivateId);\n          const staticBlockRef = t.privateName(\n            t.identifier(staticBlockPrivateId),\n          );\n\n          let replacement;\n          const blockBody = path.node.body;\n          // We special-case the single expression case to avoid the iife, since\n          // it's common.\n          if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n            replacement = t.inheritsComments(\n              blockBody[0].expression,\n              blockBody[0],\n            );\n          } else {\n            replacement = template.expression.ast`(() => { ${blockBody} })()`;\n          }\n\n          path.replaceWith(\n            t.classPrivateProperty(\n              staticBlockRef,\n              replacement,\n              [],\n              /* static */ true,\n            ),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAGA,IAAAC,gCAAA,GAAAD,OAAA;AAYA,SAASE,WAAWA,CAACC,KAAY,EAAEC,QAAqB,EAAE;EACxD,MAAMC,IAAI,GAAG,EAAE;EACf,IAAIC,GAAG;EACP,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACDD,GAAG,GAAGH,KAAK,CAACK,YAAY,CAACH,IAAI,EAAEE,CAAC,CAAC;IACjCA,CAAC,EAAE;EACL,CAAC,QAAQH,QAAQ,CAACK,GAAG,CAACH,GAAG,CAAC;EAC1B,OAAOA,GAAG;AACZ;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC,QAAQ;EAAEC,aAAa;EAAEC;AAAQ,CAAC,KAAK;EACzED,aAAa,sCAA4B,CAAC;EAE1C,OAAO;IACLZ,IAAI,EAAE,8BAA8B;IACpCc,QAAQ,EACsBD,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC1CE,SAAS,GAETpB,OAAO,CAAC,yCAAyC,CAAC,CAACY,OAAO;IAEhES,GAAGA,CAAA,EAAG;MAGJ,IAAAC,8CAAa,EAAC,IAAI,CAACC,IAAI,EAAEC,yCAAQ,CAACC,YAAY,EAAc,KAAK,CAAC;IACpE,CAAC;IAEDC,OAAO,EAAE;MAIPC,SAASA,CAACC,SAAS,EAAE;QACnB,MAAM;UAAEzB;QAAM,CAAC,GAAGyB,SAAS;QAC3B,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;QACtC,MAAMC,IAAI,GAAGH,SAAS,CAACI,GAAG,CAAC,MAAM,CAAC;QAClC,KAAK,MAAMC,IAAI,IAAIF,IAAI,EAAE;UACvB,IAAIE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;YACpBL,YAAY,CAACM,GAAG,CAACF,IAAI,CAACD,GAAG,CAAC,QAAQ,CAAC,CAACI,IAAI,CAAC/B,IAAI,CAAC;UAChD;QACF;QACA,KAAK,MAAM4B,IAAI,IAAIF,IAAI,EAAE;UACvB,IAAI,CAACE,IAAI,CAACI,aAAa,CAAC,CAAC,EAAE;UAC3B,MAAMC,oBAAoB,GAAGpC,WAAW,CAACC,KAAK,EAAE0B,YAAY,CAAC;UAC7DA,YAAY,CAACM,GAAG,CAACG,oBAAoB,CAAC;UACtC,MAAMC,cAAc,GAAGxB,CAAC,CAACyB,WAAW,CAClCzB,CAAC,CAAC0B,UAAU,CAACH,oBAAoB,CACnC,CAAC;UAED,IAAII,WAAW;UACf,MAAMC,SAAS,GAAGV,IAAI,CAACG,IAAI,CAACL,IAAI;UAGhC,IAAIY,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI7B,CAAC,CAAC8B,qBAAqB,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YACnED,WAAW,GAAG3B,CAAC,CAAC+B,gBAAgB,CAC9BH,SAAS,CAAC,CAAC,CAAC,CAACI,UAAU,EACvBJ,SAAS,CAAC,CAAC,CACb,CAAC;UACH,CAAC,MAAM;YACLD,WAAW,GAAG1B,QAAQ,CAAC+B,UAAU,CAACC,GAAI,YAAWL,SAAU,OAAM;UACnE;UAEAV,IAAI,CAACgB,WAAW,CACdlC,CAAC,CAACmC,oBAAoB,CACpBX,cAAc,EACdG,WAAW,EACX,EAAE,EACW,IACf,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}