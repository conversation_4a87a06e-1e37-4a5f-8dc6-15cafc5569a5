{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748464417190}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "viewMode", "loading", "search", "keyword", "type", "status", "treeData", "originalData", "dialogFormVisible", "dialogTitle", "parentOptions", "ruleForm", "id", "label", "code", "parent_id", "sort", "description", "path", "icon", "rules", "required", "message", "trigger", "treeProps", "children", "cascaderProps", "value", "checkStrictly", "computed", "tableData", "flattenTreeForTable", "mounted", "getData", "methods", "setTimeout", "mockData", "create_time", "JSON", "parse", "stringify", "updateParentOptions", "tree", "level", "result", "for<PERSON>ach", "node", "flatNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "push", "buildCascaderOptions", "map", "searchData", "clearSearch", "refulsh", "$router", "go", "editData", "permission", "findPermissionById", "<PERSON><PERSON><PERSON><PERSON>", "parentData", "found", "delData", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$message", "success", "catch", "info", "saveData", "$refs", "validate", "valid", "changeStatus", "row", "getNodeIcon", "iconMap", "menu", "action", "getTypeColor", "colorMap", "getTypeLabel", "labelMap"], "sources": ["src/views/pages/yuangong/quanxian.vue"], "sourcesContent": ["<template>\n  <div class=\"permission-container\">\n    <!-- 页面标题区域 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-key\"></i>\n            权限管理\n          </h2>\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\n        </div>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"editData(0)\"\n            class=\"add-btn\"\n          >\n            新增权限\n          </el-button>\n          <el-button\n            icon=\"el-icon-refresh\"\n            @click=\"refulsh\"\n            class=\"refresh-btn\"\n          >\n            刷新\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <el-card shadow=\"never\" class=\"search-card\">\n        <div class=\"search-form\">\n          <div class=\"search-row\">\n            <div class=\"search-item\">\n              <label class=\"search-label\">权限搜索</label>\n              <el-input\n                v-model=\"search.keyword\"\n                placeholder=\"请输入权限名称或描述\"\n                class=\"search-input\"\n                clearable\n                @keyup.enter.native=\"searchData\"\n              >\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\n              </el-input>\n            </div>\n            \n            <div class=\"search-item\">\n              <label class=\"search-label\">权限类型</label>\n              <el-select\n                v-model=\"search.type\"\n                placeholder=\"请选择权限类型\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </div>\n\n            <div class=\"search-item\">\n              <label class=\"search-label\">状态</label>\n              <el-select\n                v-model=\"search.status\"\n                placeholder=\"请选择状态\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"启用\" :value=\"1\"></el-option>\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"search-actions\">\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\n              搜索\n            </el-button>\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\n              重置\n            </el-button>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 权限树形结构 -->\n    <div class=\"tree-section\">\n      <el-card shadow=\"never\" class=\"tree-card\">\n        <div class=\"tree-header\">\n          <div class=\"tree-title\">\n            <i class=\"el-icon-menu\"></i>\n            权限树形结构\n          </div>\n          <div class=\"tree-tools\">\n            <el-button-group>\n              <el-button \n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \n                icon=\"el-icon-s-grid\"\n                @click=\"viewMode = 'tree'\"\n                size=\"small\"\n              >\n                树形视图\n              </el-button>\n              <el-button \n                :type=\"viewMode === 'table' ? 'primary' : ''\" \n                icon=\"el-icon-menu\"\n                @click=\"viewMode = 'table'\"\n                size=\"small\"\n              >\n                列表视图\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 树形视图 -->\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\n          <el-tree\n            :data=\"treeData\"\n            :props=\"treeProps\"\n            :default-expand-all=\"true\"\n            node-key=\"id\"\n            class=\"permission-tree\"\n          >\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\n              <div class=\"node-content\">\n                <div class=\"node-info\">\n                  <i :class=\"getNodeIcon(data.type)\"></i>\n                  <span class=\"node-label\">{{ data.label }}</span>\n                  <el-tag \n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \n                    size=\"mini\"\n                    class=\"node-status\"\n                  >\n                    {{ data.status === 1 ? '启用' : '禁用' }}\n                  </el-tag>\n                </div>\n                <div class=\"node-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click=\"editData(data.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click=\"addChild(data)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click=\"delData(data.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </span>\n          </el-tree>\n        </div>\n\n        <!-- 表格视图 -->\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\n          <el-table\n            :data=\"tableData\"\n            v-loading=\"loading\"\n            class=\"permission-table\"\n            stripe\n            row-key=\"id\"\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n            :default-expand-all=\"false\"\n          >\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\n              <template slot-scope=\"scope\">\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\n            </el-table-column>\n\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-tag\n                  :type=\"getTypeColor(scope.row.type)\"\n                  size=\"small\"\n                >\n                  {{ getTypeLabel(scope.row.type) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-switch\n                  v-model=\"scope.row.status\"\n                  :active-value=\"1\"\n                  :inactive-value=\"0\"\n                  @change=\"changeStatus(scope.row)\"\n                >\n                </el-switch>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <div class=\"action-buttons\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click.stop=\"editData(scope.row.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click.stop=\"addChild(scope.row)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                    v-if=\"scope.row.type === 'menu'\"\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click.stop=\"delData(scope.row.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 编辑权限对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogFormVisible\"\n      :close-on-click-modal=\"false\"\n      width=\"60%\"\n    >\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限名称\" prop=\"label\">\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限代码\" prop=\"code\">\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限类型\" prop=\"type\">\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"父级权限\">\n              <el-cascader\n                v-model=\"ruleForm.parent_id\"\n                :options=\"parentOptions\"\n                :props=\"cascaderProps\"\n                placeholder=\"请选择父级权限\"\n                clearable\n                style=\"width: 100%\"\n              ></el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"排序\">\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-switch\n                v-model=\"ruleForm.status\"\n                :active-value=\"1\"\n                :inactive-value=\"0\"\n                active-text=\"启用\"\n                inactive-text=\"禁用\"\n              >\n              </el-switch>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"权限描述\">\n          <el-input\n            v-model=\"ruleForm.description\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入权限描述\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\n            <template slot=\"prepend\">\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"PermissionManagement\",\n  data() {\n    return {\n      viewMode: 'tree', // tree | table\n      loading: false,\n      search: {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      },\n      treeData: [],\n      originalData: [], // 保存原始数据\n      dialogFormVisible: false,\n      dialogTitle: \"新增权限\",\n      parentOptions: [],\n      ruleForm: {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"menu\",\n        parent_id: null,\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      },\n      rules: {\n        label: [\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\n        ],\n        code: [\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\n        ]\n      },\n      treeProps: {\n        children: 'children',\n        label: 'label'\n      },\n      cascaderProps: {\n        value: 'id',\n        label: 'label',\n        children: 'children',\n        checkStrictly: true\n      }\n    };\n  },\n  computed: {\n    // 表格数据 - 将树形数据扁平化但保持层级关系\n    tableData() {\n      return this.flattenTreeForTable(this.treeData);\n    }\n  },\n  mounted() {\n    this.getData();\n  },\n  methods: {\n    // 获取权限数据\n    getData() {\n      this.loading = true;\n      \n      // 使用测试数据\n      setTimeout(() => {\n        this.loading = false;\n        \n        const mockData = [\n          {\n            id: 1,\n            label: \"系统管理\",\n            code: \"system\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 1,\n            status: 1,\n            description: \"系统管理模块\",\n            path: \"/system\",\n            icon: \"el-icon-setting\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 11,\n                label: \"用户管理\",\n                code: \"system:user\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 1,\n                status: 1,\n                description: \"用户管理功能\",\n                path: \"/user\",\n                icon: \"el-icon-user\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 111,\n                    label: \"查看用户\",\n                    code: \"system:user:view\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看用户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 112,\n                    label: \"新增用户\",\n                    code: \"system:user:add\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 113,\n                    label: \"编辑用户\",\n                    code: \"system:user:edit\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑用户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 114,\n                    label: \"删除用户\",\n                    code: \"system:user:delete\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 12,\n                label: \"职位管理\",\n                code: \"system:position\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 2,\n                status: 1,\n                description: \"职位管理功能\",\n                path: \"/zhiwei\",\n                icon: \"el-icon-postcard\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 121,\n                    label: \"查看职位\",\n                    code: \"system:position:view\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看职位列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 122,\n                    label: \"新增职位\",\n                    code: \"system:position:add\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 123,\n                    label: \"编辑职位\",\n                    code: \"system:position:edit\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑职位信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 124,\n                    label: \"删除职位\",\n                    code: \"system:position:delete\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 13,\n                label: \"权限管理\",\n                code: \"system:permission\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 3,\n                status: 1,\n                description: \"权限管理功能\",\n                path: \"/quanxian\",\n                icon: \"el-icon-key\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 131,\n                    label: \"查看权限\",\n                    code: \"system:permission:view\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看权限列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 132,\n                    label: \"新增权限\",\n                    code: \"system:permission:add\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 133,\n                    label: \"编辑权限\",\n                    code: \"system:permission:edit\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑权限信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 134,\n                    label: \"删除权限\",\n                    code: \"system:permission:delete\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 2,\n            label: \"业务管理\",\n            code: \"business\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 2,\n            status: 1,\n            description: \"业务管理模块\",\n            path: \"/business\",\n            icon: \"el-icon-suitcase\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 21,\n                label: \"订单管理\",\n                code: \"business:order\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 1,\n                status: 1,\n                description: \"订单管理功能\",\n                path: \"/dingdan\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 211,\n                    label: \"查看订单\",\n                    code: \"business:order:view\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看订单列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 212,\n                    label: \"新增订单\",\n                    code: \"business:order:add\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 213,\n                    label: \"编辑订单\",\n                    code: \"business:order:edit\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑订单信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 214,\n                    label: \"删除订单\",\n                    code: \"business:order:delete\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 215,\n                    label: \"导出订单\",\n                    code: \"business:order:export\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 5,\n                    status: 1,\n                    description: \"导出订单数据\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 22,\n                label: \"客户管理\",\n                code: \"business:customer\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 2,\n                status: 1,\n                description: \"客户管理功能\",\n                path: \"/customer\",\n                icon: \"el-icon-user-solid\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 221,\n                    label: \"查看客户\",\n                    code: \"business:customer:view\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看客户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 222,\n                    label: \"新增客户\",\n                    code: \"business:customer:add\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 223,\n                    label: \"编辑客户\",\n                    code: \"business:customer:edit\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑客户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 224,\n                    label: \"删除客户\",\n                    code: \"business:customer:delete\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 3,\n            label: \"文书管理\",\n            code: \"document\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 3,\n            status: 1,\n            description: \"文书管理模块\",\n            path: \"/document\",\n            icon: \"el-icon-document-copy\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 31,\n                label: \"合同管理\",\n                code: \"document:contract\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 1,\n                status: 1,\n                description: \"合同管理功能\",\n                path: \"/hetong\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 311,\n                    label: \"查看合同\",\n                    code: \"document:contract:view\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看合同列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 312,\n                    label: \"新增合同\",\n                    code: \"document:contract:add\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 313,\n                    label: \"编辑合同\",\n                    code: \"document:contract:edit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑合同信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 314,\n                    label: \"删除合同\",\n                    code: \"document:contract:delete\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 315,\n                    label: \"审核合同\",\n                    code: \"document:contract:audit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 5,\n                    status: 1,\n                    description: \"审核合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 32,\n                label: \"律师函管理\",\n                code: \"document:lawyer\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 2,\n                status: 1,\n                description: \"律师函管理功能\",\n                path: \"/lawyer\",\n                icon: \"el-icon-message\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 321,\n                    label: \"查看律师函\",\n                    code: \"document:lawyer:view\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看律师函列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 322,\n                    label: \"发送律师函\",\n                    code: \"document:lawyer:send\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 2,\n                    status: 1,\n                    description: \"发送律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 323,\n                    label: \"编辑律师函\",\n                    code: \"document:lawyer:edit\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 33,\n                label: \"课程管理\",\n                code: \"document:course\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 3,\n                status: 1,\n                description: \"课程管理功能\",\n                path: \"/kecheng\",\n                icon: \"el-icon-video-play\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 331,\n                    label: \"查看课程\",\n                    code: \"document:course:view\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看课程列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 332,\n                    label: \"新增课程\",\n                    code: \"document:course:add\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 333,\n                    label: \"编辑课程\",\n                    code: \"document:course:edit\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 334,\n                    label: \"删除课程\",\n                    code: \"document:course:delete\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 4,\n            label: \"财务管理\",\n            code: \"finance\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 4,\n            status: 1,\n            description: \"财务管理模块\",\n            path: \"/finance\",\n            icon: \"el-icon-coin\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 41,\n                label: \"支付管理\",\n                code: \"finance:payment\",\n                type: \"menu\",\n                parent_id: 4,\n                sort: 1,\n                status: 1,\n                description: \"支付管理功能\",\n                path: \"/order\",\n                icon: \"el-icon-money\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 411,\n                    label: \"查看支付记录\",\n                    code: \"finance:payment:view\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看支付记录\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 412,\n                    label: \"处理退款\",\n                    code: \"finance:payment:refund\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 2,\n                    status: 1,\n                    description: \"处理退款申请\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 413,\n                    label: \"导出财务报表\",\n                    code: \"finance:payment:export\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 3,\n                    status: 1,\n                    description: \"导出财务报表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          }\n        ];\n        \n        this.treeData = mockData;\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\n        this.updateParentOptions();\n      }, 500);\n    },\n\n    // 将树形数据扁平化用于表格显示（保持层级结构）\n    flattenTreeForTable(tree, level = 0, result = []) {\n      tree.forEach(node => {\n        const flatNode = {\n          ...node,\n          level: level,\n          hasChildren: node.children && node.children.length > 0\n        };\n        // 移除children属性避免表格渲染问题\n        delete flatNode.children;\n        result.push(flatNode);\n\n        // 递归处理子节点\n        if (node.children && node.children.length > 0) {\n          this.flattenTreeForTable(node.children, level + 1, result);\n        }\n      });\n      return result;\n    },\n\n    // 更新父级权限选项\n    updateParentOptions() {\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\n    },\n\n    // 构建级联选择器选项\n    buildCascaderOptions(tree) {\n      return tree.map(node => ({\n        id: node.id,\n        label: node.label,\n        children: node.children ? this.buildCascaderOptions(node.children) : []\n      }));\n    },\n\n    // 搜索数据\n    searchData() {\n      this.getData();\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.search = {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      };\n      this.searchData();\n    },\n\n    // 刷新页面\n    refulsh() {\n      this.$router.go(0);\n    },\n\n    // 编辑权限\n    editData(id) {\n      if (id === 0) {\n        this.dialogTitle = \"新增权限\";\n        this.ruleForm = {\n          id: null,\n          label: \"\",\n          code: \"\",\n          type: \"menu\",\n          parent_id: null,\n          sort: 0,\n          status: 1,\n          description: \"\",\n          path: \"\",\n          icon: \"\"\n        };\n      } else {\n        this.dialogTitle = \"编辑权限\";\n        const permission = this.findPermissionById(id);\n        if (permission) {\n          this.ruleForm = { ...permission };\n        }\n      }\n      this.dialogFormVisible = true;\n    },\n\n    // 添加子权限\n    addChild(parentData) {\n      this.dialogTitle = \"新增子权限\";\n      this.ruleForm = {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"action\",\n        parent_id: [parentData.id],\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      };\n      this.dialogFormVisible = true;\n    },\n\n    // 根据ID查找权限（在原始数据中查找）\n    findPermissionById(id, tree = this.originalData) {\n      for (let node of tree) {\n        if (node.id === id) {\n          return node;\n        }\n        if (node.children) {\n          const found = this.findPermissionById(id, node.children);\n          if (found) return found;\n        }\n      }\n      return null;\n    },\n\n    // 删除权限\n    delData(id) {\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        // 模拟删除\n        this.$message.success(\"删除成功！\");\n        this.getData();\n      }).catch(() => {\n        this.$message.info(\"已取消删除\");\n      });\n    },\n\n    // 保存权限\n    saveData() {\n      this.$refs[\"ruleForm\"].validate((valid) => {\n        if (valid) {\n          // 模拟保存\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\n          this.dialogFormVisible = false;\n          this.getData();\n        }\n      });\n    },\n\n    // 改变状态\n    changeStatus(row) {\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\n    },\n\n    // 获取节点图标\n    getNodeIcon(type) {\n      const iconMap = {\n        menu: 'el-icon-menu',\n        action: 'el-icon-setting',\n        data: 'el-icon-document'\n      };\n      return iconMap[type] || 'el-icon-menu';\n    },\n\n    // 获取类型颜色\n    getTypeColor(type) {\n      const colorMap = {\n        menu: 'primary',\n        action: 'success',\n        data: 'warning'\n      };\n      return colorMap[type] || 'primary';\n    },\n\n    // 获取类型标签\n    getTypeLabel(type) {\n      const labelMap = {\n        menu: '菜单权限',\n        action: '操作权限',\n        data: '数据权限'\n      };\n      return labelMap[type] || '菜单权限';\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 权限管理容器 */\n.permission-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding: 24px;\n}\n\n/* 页面标题区域 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 24px 32px;\n  border-radius: 12px;\n  color: white;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.title-section h2.page-title {\n  margin: 0;\n  font-size: 28px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.title-section .page-subtitle {\n  margin: 8px 0 0 0;\n  opacity: 0.9;\n  font-size: 14px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.add-btn, .refresh-btn {\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.add-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.add-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.refresh-btn {\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 24px;\n}\n\n.search-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form {\n  padding: 8px;\n}\n\n.search-row {\n  display: flex;\n  gap: 24px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n}\n\n.search-item {\n  display: flex;\n  flex-direction: column;\n  min-width: 200px;\n}\n\n.search-label {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 8px;\n  font-weight: 500;\n}\n\n.search-input, .search-select {\n  width: 240px;\n}\n\n.search-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n/* 树形结构区域 */\n.tree-section {\n  margin-bottom: 24px;\n}\n\n.tree-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.tree-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.tree-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #262626;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.tree-tools {\n  display: flex;\n  gap: 12px;\n}\n\n/* 树形视图样式 */\n.tree-view {\n  padding: 24px;\n}\n\n.permission-tree {\n  background: transparent;\n}\n\n.tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.node-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.node-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.node-label {\n  font-weight: 500;\n  color: #262626;\n}\n\n.node-status {\n  margin-left: 8px;\n}\n\n.node-actions {\n  display: flex;\n  gap: 4px;\n}\n\n/* 表格视图样式 */\n.table-view {\n  padding: 0 24px 24px;\n}\n\n.permission-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.permission-name-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.permission-name {\n  font-weight: 500;\n  color: #262626;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.action-buttons .el-button {\n  margin: 2px;\n}\n\n/* 表格行层级样式 */\n.permission-table .el-table__row[data-level=\"0\"] {\n  background-color: #fafafa;\n  font-weight: 600;\n}\n\n.permission-table .el-table__row[data-level=\"1\"] {\n  background-color: #f5f5f5;\n}\n\n.permission-table .el-table__row[data-level=\"2\"] {\n  background-color: #ffffff;\n}\n\n/* 对话框样式 */\n.dialog-footer {\n  text-align: right;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .permission-container {\n    padding: 16px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .search-row {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .search-item {\n    min-width: auto;\n  }\n\n  .search-input, .search-select {\n    width: 100%;\n  }\n\n  .tree-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n\n  .node-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .node-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n}\n</style>\n"], "mappings": ";AA2WA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,OAAA;MACAC,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACAC,QAAA;MACAC,YAAA;MAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,aAAA;MACAC,QAAA;QACAC,EAAA;QACAC,KAAA;QACAC,IAAA;QACAV,IAAA;QACAW,SAAA;QACAC,IAAA;QACAX,MAAA;QACAY,WAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAP,KAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,IAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,IAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,SAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACAa,aAAA;QACAC,KAAA;QACAd,KAAA;QACAY,QAAA;QACAG,aAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA;MACA,YAAAC,mBAAA,MAAAzB,QAAA;IACA;EACA;EACA0B,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,QAAA;MACA,KAAAhC,OAAA;;MAEA;MACAkC,UAAA;QACA,KAAAlC,OAAA;QAEA,MAAAmC,QAAA,IACA;UACAxB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,EACA;QAEA,KAAA/B,QAAA,GAAA8B,QAAA;QACA,KAAA7B,YAAA,GAAA+B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,QAAA;QACA,KAAAK,mBAAA;MACA;IACA;IAEA;IACAV,oBAAAW,IAAA,EAAAC,KAAA,MAAAC,MAAA;MACAF,IAAA,CAAAG,OAAA,CAAAC,IAAA;QACA,MAAAC,QAAA;UACA,GAAAD,IAAA;UACAH,KAAA,EAAAA,KAAA;UACAK,WAAA,EAAAF,IAAA,CAAArB,QAAA,IAAAqB,IAAA,CAAArB,QAAA,CAAAwB,MAAA;QACA;QACA;QACA,OAAAF,QAAA,CAAAtB,QAAA;QACAmB,MAAA,CAAAM,IAAA,CAAAH,QAAA;;QAEA;QACA,IAAAD,IAAA,CAAArB,QAAA,IAAAqB,IAAA,CAAArB,QAAA,CAAAwB,MAAA;UACA,KAAAlB,mBAAA,CAAAe,IAAA,CAAArB,QAAA,EAAAkB,KAAA,MAAAC,MAAA;QACA;MACA;MACA,OAAAA,MAAA;IACA;IAEA;IACAH,oBAAA;MACA,KAAA/B,aAAA,QAAAyC,oBAAA,MAAA7C,QAAA;IACA;IAEA;IACA6C,qBAAAT,IAAA;MACA,OAAAA,IAAA,CAAAU,GAAA,CAAAN,IAAA;QACAlC,EAAA,EAAAkC,IAAA,CAAAlC,EAAA;QACAC,KAAA,EAAAiC,IAAA,CAAAjC,KAAA;QACAY,QAAA,EAAAqB,IAAA,CAAArB,QAAA,QAAA0B,oBAAA,CAAAL,IAAA,CAAArB,QAAA;MACA;IACA;IAEA;IACA4B,WAAA;MACA,KAAApB,OAAA;IACA;IAEA;IACAqB,YAAA;MACA,KAAApD,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACA,KAAAgD,UAAA;IACA;IAEA;IACAE,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,SAAA9C,EAAA;MACA,IAAAA,EAAA;QACA,KAAAH,WAAA;QACA,KAAAE,QAAA;UACAC,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;QACA;MACA;QACA,KAAAV,WAAA;QACA,MAAAkD,UAAA,QAAAC,kBAAA,CAAAhD,EAAA;QACA,IAAA+C,UAAA;UACA,KAAAhD,QAAA;YAAA,GAAAgD;UAAA;QACA;MACA;MACA,KAAAnD,iBAAA;IACA;IAEA;IACAqD,SAAAC,UAAA;MACA,KAAArD,WAAA;MACA,KAAAE,QAAA;QACAC,EAAA;QACAC,KAAA;QACAC,IAAA;QACAV,IAAA;QACAW,SAAA,GAAA+C,UAAA,CAAAlD,EAAA;QACAI,IAAA;QACAX,MAAA;QACAY,WAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,KAAAX,iBAAA;IACA;IAEA;IACAoD,mBAAAhD,EAAA,EAAA8B,IAAA,QAAAnC,YAAA;MACA,SAAAuC,IAAA,IAAAJ,IAAA;QACA,IAAAI,IAAA,CAAAlC,EAAA,KAAAA,EAAA;UACA,OAAAkC,IAAA;QACA;QACA,IAAAA,IAAA,CAAArB,QAAA;UACA,MAAAsC,KAAA,QAAAH,kBAAA,CAAAhD,EAAA,EAAAkC,IAAA,CAAArB,QAAA;UACA,IAAAsC,KAAA,SAAAA,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAC,QAAApD,EAAA;MACA,KAAAqD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/D,IAAA;MACA,GAAAgE,IAAA;QACA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAArC,OAAA;MACA,GAAAsC,KAAA;QACA,KAAAF,QAAA,CAAAG,IAAA;MACA;IACA;IAEA;IACAC,SAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAP,QAAA,CAAAC,OAAA,MAAA3D,QAAA,CAAAC,EAAA;UACA,KAAAJ,iBAAA;UACA,KAAAyB,OAAA;QACA;MACA;IACA;IAEA;IACA4C,aAAAC,GAAA;MACA,KAAAT,QAAA,CAAAC,OAAA,SAAAQ,GAAA,CAAAzE,MAAA;IACA;IAEA;IACA0E,YAAA3E,IAAA;MACA,MAAA4E,OAAA;QACAC,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAiF,OAAA,CAAA5E,IAAA;IACA;IAEA;IACA+E,aAAA/E,IAAA;MACA,MAAAgF,QAAA;QACAH,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAqF,QAAA,CAAAhF,IAAA;IACA;IAEA;IACAiF,aAAAjF,IAAA;MACA,MAAAkF,QAAA;QACAL,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAuF,QAAA,CAAAlF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}