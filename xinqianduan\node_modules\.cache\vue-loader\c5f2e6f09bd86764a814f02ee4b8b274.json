{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue?vue&type=template&id=4acd38ca&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1748608840004}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}