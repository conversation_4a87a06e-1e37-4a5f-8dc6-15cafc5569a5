2022-09-23 07:06:18 pid:1 think\exception\PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php:687
Stack trace:
#0 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(844): think\db\Connection->query('SELECT * FROM `...', Array, false, false)
#1 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(3152): think\db\Connection->find(Object(think\db\Query))
#2 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\application\worker\Worker.php(39): think\db\Query->find()
#3 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Connection\TcpConnection.php(656): app\worker\Worker->onMessage(Object(Workerman\Connection\TcpConnection), 'comment|list|9')
#4 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Events\Select.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #389)
#5 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(2408): Workerman\Events\Select->loop()
#6 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1404): Workerman\Worker->run()
#7 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1347): Workerman\Worker::forkWorkersForWindows()
#8 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(546): Workerman\Worker::forkWorkers()
#9 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\topthink\think-worker\src\command\Server.php(75): Workerman\Worker::runAll()
#10 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\console\Command.php(175): think\worker\command\Server->execute(Object(think\console\Input), Object(think\console\Output))
#11 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(675): think\console\Command->run(Object(think\console\Input), Object(think\console\Output))
#12 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(261): think\Console->doRunCommand(Object(think\worker\command\Server), Object(think\console\Input), Object(think\console\Output))
#13 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(198): think\Console->doRun(Object(think\console\Input), Object(think\console\Output))
#14 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(115): think\Console->run()
#15 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\think(22): think\Console::init()
#16 {main}
2022-09-23 07:06:18 pid:1 Worker process terminated
2022-09-23 07:21:08 pid:1 think\exception\PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php:687
Stack trace:
#0 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(844): think\db\Connection->query('SELECT * FROM `...', Array, false, false)
#1 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(3152): think\db\Connection->find(Object(think\db\Query))
#2 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\application\worker\Worker.php(39): think\db\Query->find()
#3 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Connection\TcpConnection.php(656): app\worker\Worker->onMessage(Object(Workerman\Connection\TcpConnection), 'comment|list|9')
#4 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Events\Select.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #526)
#5 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(2408): Workerman\Events\Select->loop()
#6 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1404): Workerman\Worker->run()
#7 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1347): Workerman\Worker::forkWorkersForWindows()
#8 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(546): Workerman\Worker::forkWorkers()
#9 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\topthink\think-worker\src\command\Server.php(75): Workerman\Worker::runAll()
#10 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\console\Command.php(175): think\worker\command\Server->execute(Object(think\console\Input), Object(think\console\Output))
#11 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(675): think\console\Command->run(Object(think\console\Input), Object(think\console\Output))
#12 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(261): think\Console->doRunCommand(Object(think\worker\command\Server), Object(think\console\Input), Object(think\console\Output))
#13 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(198): think\Console->doRun(Object(think\console\Input), Object(think\console\Output))
#14 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(115): think\Console->run()
#15 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\think(22): think\Console::init()
#16 {main}
2022-09-23 07:21:08 pid:1 Worker process terminated
2022-09-23 08:04:11 pid:1 think\exception\PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php:687
Stack trace:
#0 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(844): think\db\Connection->query('SELECT * FROM `...', Array, false, false)
#1 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(3152): think\db\Connection->find(Object(think\db\Query))
#2 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\application\worker\Worker.php(39): think\db\Query->find()
#3 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Connection\TcpConnection.php(656): app\worker\Worker->onMessage(Object(Workerman\Connection\TcpConnection), 'comment|list|9')
#4 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Events\Select.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #1059)
#5 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(2408): Workerman\Events\Select->loop()
#6 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1404): Workerman\Worker->run()
#7 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1347): Workerman\Worker::forkWorkersForWindows()
#8 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(546): Workerman\Worker::forkWorkers()
#9 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\topthink\think-worker\src\command\Server.php(75): Workerman\Worker::runAll()
#10 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\console\Command.php(175): think\worker\command\Server->execute(Object(think\console\Input), Object(think\console\Output))
#11 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(675): think\console\Command->run(Object(think\console\Input), Object(think\console\Output))
#12 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(261): think\Console->doRunCommand(Object(think\worker\command\Server), Object(think\console\Input), Object(think\console\Output))
#13 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(198): think\Console->doRun(Object(think\console\Input), Object(think\console\Output))
#14 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(115): think\Console->run()
#15 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\think(22): think\Console::init()
#16 {main}
2022-09-23 08:04:11 pid:1 Worker process terminated
2022-09-23 08:23:37 pid:1 think\exception\PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php:687
Stack trace:
#0 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(1299): think\db\Connection->query('SELECT COUNT(*)...', Array, false, true)
#1 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(1327): think\db\Connection->value(Object(think\db\Query), Array, 0, false)
#2 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(643): think\db\Connection->aggregate(Object(think\db\Query), 'COUNT', 'COUNT(*) AS tp_...')
#3 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(678): think\db\Query->aggregate('COUNT', '*', true)
#4 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\application\worker\Worker.php(32): think\db\Query->count()
#5 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Connection\TcpConnection.php(656): app\worker\Worker->onMessage(Object(Workerman\Connection\TcpConnection), 'line|list|undef...')
#6 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Events\Select.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #219)
#7 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(2408): Workerman\Events\Select->loop()
#8 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1404): Workerman\Worker->run()
#9 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1347): Workerman\Worker::forkWorkersForWindows()
#10 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(546): Workerman\Worker::forkWorkers()
#11 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\topthink\think-worker\src\command\Server.php(75): Workerman\Worker::runAll()
#12 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\console\Command.php(175): think\worker\command\Server->execute(Object(think\console\Input), Object(think\console\Output))
#13 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(675): think\console\Command->run(Object(think\console\Input), Object(think\console\Output))
#14 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(261): think\Console->doRunCommand(Object(think\worker\command\Server), Object(think\console\Input), Object(think\console\Output))
#15 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(198): think\Console->doRun(Object(think\console\Input), Object(think\console\Output))
#16 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(115): think\Console->run()
#17 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\think(22): think\Console::init()
#18 {main}
2022-09-23 08:23:37 pid:1 Worker process terminated
2022-09-23 11:48:15 pid:1 think\exception\PDOException: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away in C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php:687
Stack trace:
#0 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(1299): think\db\Connection->query('SELECT COUNT(*)...', Array, false, true)
#1 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Connection.php(1327): think\db\Connection->value(Object(think\db\Query), Array, 0, false)
#2 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(643): think\db\Connection->aggregate(Object(think\db\Query), 'COUNT', 'COUNT(*) AS tp_...')
#3 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\db\Query.php(678): think\db\Query->aggregate('COUNT', '*', true)
#4 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\application\worker\Worker.php(32): think\db\Query->count()
#5 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Connection\TcpConnection.php(656): app\worker\Worker->onMessage(Object(Workerman\Connection\TcpConnection), 'line|list|undef...')
#6 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Events\Select.php(292): Workerman\Connection\TcpConnection->baseRead(Resource id #974)
#7 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(2408): Workerman\Events\Select->loop()
#8 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1404): Workerman\Worker->run()
#9 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(1347): Workerman\Worker::forkWorkersForWindows()
#10 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\workerman\workerman\Worker.php(546): Workerman\Worker::forkWorkers()
#11 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\vendor\topthink\think-worker\src\command\Server.php(75): Workerman\Worker::runAll()
#12 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\console\Command.php(175): think\worker\command\Server->execute(Object(think\console\Input), Object(think\console\Output))
#13 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(675): think\console\Command->run(Object(think\console\Input), Object(think\console\Output))
#14 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(261): think\Console->doRunCommand(Object(think\worker\command\Server), Object(think\console\Input), Object(think\console\Output))
#15 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(198): think\Console->doRun(Object(think\console\Input), Object(think\console\Output))
#16 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\thinkphp\library\think\Console.php(115): think\Console->run()
#17 C:\phpstudy\phpstudy_pro\WWW\www.lvdian2.com\think(22): think\Console::init()
#18 {main}
2022-09-23 11:48:15 pid:1 Worker process terminated
2023-07-07 21:58:04 pid:5964 Workerman[think] start in DEBUG mode
2023-07-08 13:48:40 pid:7283 Workerman[think] start in DEBUG mode
2023-07-08 13:48:55 pid:7285 Workerman[think] start in DEBUG mode
2023-07-08 13:48:55 pid:7285 Workerman[think] already running
2023-07-09 13:34:07 pid:9137 Workerman[think] start in DEBUG mode
2023-07-09 13:34:47 pid:9165 Workerman[think] start in DEBUG mode
2023-07-09 13:43:47 pid:9165 Workerman[think] reloading
2023-07-09 13:45:49 pid:9165 Workerman[think] reloading
2023-07-09 13:46:03 pid:9165 Workerman[think] reloading
2023-07-29 11:03:10 pid:9584 Workerman[think] start in DEBUG mode
