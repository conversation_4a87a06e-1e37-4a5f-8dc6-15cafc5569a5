{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue", "mtime": 1749572353407}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getRequest", "postRequest", "deleteRequest", "name", "mixins", "methods", "data", "loading", "showAddDialog", "notificationList", "totalCount", "unreadCount", "filterForm", "is_read", "type", "level", "pagination", "page", "size", "total", "newNotification", "title", "content", "target_type", "notificationRules", "required", "message", "trigger", "mounted", "loadNotifications", "loadStats", "params", "response", "code", "list", "error", "console", "$message", "unread", "mark<PERSON><PERSON><PERSON>", "notification", "id", "read", "Math", "max", "success", "markAllAsRead", "$confirm", "confirmButtonText", "cancelButtonText", "for<PERSON>ach", "item", "deleteNotification", "publishNotification", "$refs", "notificationForm", "validate", "resetForm", "resetFields", "resetFilter", "handleSizeChange", "handleCurrentChange", "getLevelType", "map", "info", "warning", "getLevelText", "getTypeColor", "system", "update", "backup", "getTypeText"], "sources": ["src/views/pages/NotificationList.vue"], "sourcesContent": ["<template>\n  <div class=\"notification-container\">\n    <div class=\"page-header\">\n      <h2>系统通知</h2>\n      <div class=\"header-actions\">\n        <el-button @click=\"markAllAsRead\" :disabled=\"unreadCount === 0\">\n          <i class=\"el-icon-check\"></i> 全部标记为已读\n        </el-button>\n        <el-button type=\"primary\" @click=\"showAddDialog = true\">\n          <i class=\"el-icon-plus\"></i> 发布通知\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"stats-row\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ totalCount }}</div>\n            <div class=\"stat-label\">总通知数</div>\n          </div>\n          <i class=\"el-icon-bell stat-icon\"></i>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number unread\">{{ unreadCount }}</div>\n            <div class=\"stat-label\">未读通知</div>\n          </div>\n          <i class=\"el-icon-message stat-icon\"></i>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 筛选条件 -->\n    <el-card class=\"filter-card\" shadow=\"never\">\n      <el-form :inline=\"true\" :model=\"filterForm\" class=\"filter-form\">\n        <el-form-item label=\"状态\">\n          <el-select v-model=\"filterForm.is_read\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未读\" value=\"0\"></el-option>\n            <el-option label=\"已读\" value=\"1\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"类型\">\n          <el-select v-model=\"filterForm.type\" placeholder=\"请选择类型\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\">\n          <el-select v-model=\"filterForm.level\" placeholder=\"请选择级别\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"loadNotifications\">查询</el-button>\n          <el-button @click=\"resetFilter\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 通知列表 -->\n    <el-card class=\"list-card\">\n      <div class=\"notification-list\" v-loading=\"loading\">\n        <div \n          v-for=\"notification in notificationList\" \n          :key=\"notification.id\"\n          class=\"notification-item\"\n          :class=\"{ 'unread': !notification.read }\"\n        >\n          <div class=\"notification-header\">\n            <div class=\"notification-meta\">\n              <el-tag \n                :type=\"getLevelType(notification.level)\" \n                size=\"small\"\n                class=\"level-tag\"\n              >\n                {{ getLevelText(notification.level) }}\n              </el-tag>\n              <el-tag \n                :type=\"getTypeColor(notification.type)\" \n                size=\"small\"\n                class=\"type-tag\"\n              >\n                {{ getTypeText(notification.type) }}\n              </el-tag>\n              <span class=\"notification-time\">{{ notification.time }}</span>\n            </div>\n            <div class=\"notification-actions\">\n              <el-button \n                v-if=\"!notification.read\" \n                type=\"text\" \n                size=\"small\" \n                @click=\"markAsRead(notification)\"\n              >\n                标记已读\n              </el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"deleteNotification(notification)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n          <div class=\"notification-content\">\n            <h4 class=\"notification-title\">{{ notification.title }}</h4>\n            <p class=\"notification-desc\">{{ notification.content }}</p>\n          </div>\n        </div>\n\n        <div v-if=\"notificationList.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-bell\"></i>\n          <p>暂无通知</p>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.page\"\n          :page-sizes=\"[10, 20, 50]\"\n          :page-size=\"pagination.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 发布通知对话框 -->\n    <el-dialog \n      title=\"发布通知\" \n      :visible.sync=\"showAddDialog\"\n      width=\"600px\"\n    >\n      <el-form :model=\"newNotification\" :rules=\"notificationRules\" ref=\"notificationForm\" label-width=\"100px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"newNotification.title\" placeholder=\"请输入通知标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"内容\" prop=\"content\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"newNotification.content\" \n            placeholder=\"请输入通知内容\"\n            :rows=\"4\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"type\">\n          <el-select v-model=\"newNotification.type\" placeholder=\"请选择类型\">\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"level\">\n          <el-select v-model=\"newNotification.level\" placeholder=\"请选择级别\">\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"目标用户\">\n          <el-select v-model=\"newNotification.target_type\" placeholder=\"请选择目标用户\">\n            <el-option label=\"所有用户\" value=\"all\"></el-option>\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\n            <el-option label=\"普通用户\" value=\"user\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showAddDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"publishNotification\">发布</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\n\nexport default {\n  name: 'NotificationList',\n  mixins: [{ methods: { getRequest, postRequest, deleteRequest } }],\n  data() {\n    return {\n      loading: false,\n      showAddDialog: false,\n      notificationList: [],\n      totalCount: 0,\n      unreadCount: 0,\n      filterForm: {\n        is_read: '',\n        type: '',\n        level: ''\n      },\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      },\n      newNotification: {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      },\n      notificationRules: {\n        title: [\n          { required: true, message: '请输入标题', trigger: 'blur' }\n        ],\n        content: [\n          { required: true, message: '请输入内容', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择类型', trigger: 'change' }\n        ],\n        level: [\n          { required: true, message: '请选择级别', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadNotifications()\n    this.loadStats()\n  },\n  methods: {\n    async loadNotifications() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          size: this.pagination.size,\n          ...this.filterForm\n        }\n        const response = await this.getRequest('/notification/list', params)\n        if (response.code === 200) {\n          this.notificationList = response.data.list || []\n          this.pagination.total = response.data.total || 0\n        }\n      } catch (error) {\n        console.error('加载通知失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadStats() {\n      try {\n        const response = await this.getRequest('/notification/stats')\n        if (response.code === 200) {\n          this.totalCount = response.data.total || 0\n          this.unreadCount = response.data.unread || 0\n        }\n      } catch (error) {\n        console.error('加载统计失败:', error)\n      }\n    },\n\n    async markAsRead(notification) {\n      try {\n        const response = await this.postRequest('/dashboard/markNotificationRead', {\n          id: notification.id\n        })\n        if (response.code === 200) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n          this.$message.success('标记成功')\n        }\n      } catch (error) {\n        console.error('标记失败:', error)\n        this.$message.error('操作失败')\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await this.$confirm('确定要将所有未读通知标记为已读吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.postRequest('/notification/markAllRead')\n        if (response.code === 200) {\n          this.notificationList.forEach(item => item.read = true)\n          this.unreadCount = 0\n          this.$message.success('操作成功')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('操作失败:', error)\n          this.$message.error('操作失败')\n        }\n      }\n    },\n\n    async deleteNotification(notification) {\n      try {\n        await this.$confirm('确定要删除这条通知吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        const response = await this.deleteRequest('/notification/delete', { id: notification.id })\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async publishNotification() {\n      try {\n        await this.$refs.notificationForm.validate()\n        \n        const response = await this.postRequest('/notification/create', this.newNotification)\n        if (response.code === 200) {\n          this.$message.success('发布成功')\n          this.showAddDialog = false\n          this.resetForm()\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        console.error('发布失败:', error)\n        this.$message.error('发布失败')\n      }\n    },\n\n    resetForm() {\n      this.newNotification = {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      }\n      this.$refs.notificationForm && this.$refs.notificationForm.resetFields()\n    },\n\n    resetFilter() {\n      this.filterForm = {\n        is_read: '',\n        type: '',\n        level: ''\n      }\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleSizeChange(size) {\n      this.pagination.size = size\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleCurrentChange(page) {\n      this.pagination.page = page\n      this.loadNotifications()\n    },\n\n    getLevelType(level) {\n      const map = {\n        info: 'info',\n        warning: 'warning',\n        error: 'danger',\n        success: 'success'\n      }\n      return map[level] || 'info'\n    },\n\n    getLevelText(level) {\n      const map = {\n        info: '信息',\n        warning: '警告',\n        error: '错误',\n        success: '成功'\n      }\n      return map[level] || '信息'\n    },\n\n    getTypeColor(type) {\n      const map = {\n        system: '',\n        update: 'success',\n        backup: 'info',\n        warning: 'warning'\n      }\n      return map[type] || ''\n    },\n\n    getTypeText(type) {\n      const map = {\n        system: '系统通知',\n        update: '更新通知',\n        backup: '备份通知',\n        warning: '警告通知'\n      }\n      return map[type] || '系统通知'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notification-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.stats-row {\n  margin-bottom: 20px;\n}\n\n.stat-card {\n  padding: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.stat-number.unread {\n  color: #f56c6c;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.stat-icon {\n  font-size: 40px;\n  color: #c0c4cc;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form {\n  margin-bottom: 0;\n}\n\n.list-card {\n  margin-bottom: 20px;\n}\n\n.notification-list {\n  min-height: 400px;\n}\n\n.notification-item {\n  border: 1px solid #ebeef5;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 12px;\n  transition: all 0.3s;\n}\n\n.notification-item:hover {\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.notification-item.unread {\n  border-left: 4px solid #409eff;\n  background-color: #f0f9ff;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.level-tag, .type-tag {\n  margin-right: 8px;\n}\n\n.notification-time {\n  color: #909399;\n  font-size: 12px;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.notification-title {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.notification-desc {\n  margin: 0;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 0;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": "AA6LA,SAAAA,UAAA,EAAAC,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,MAAA;IAAAC,OAAA;MAAAL,UAAA;MAAAC,WAAA;MAAAC;IAAA;EAAA;EACAI,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;QACAC,OAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,eAAA;QACAC,KAAA;QACAC,OAAA;QACAR,IAAA;QACAC,KAAA;QACAQ,WAAA;MACA;MACAC,iBAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,OAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,IAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,KAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,SAAA;EACA;EACAzB,OAAA;IACA,MAAAwB,kBAAA;MACA,KAAAtB,OAAA;MACA;QACA,MAAAwB,MAAA;UACAd,IAAA,OAAAD,UAAA,CAAAC,IAAA;UACAC,IAAA,OAAAF,UAAA,CAAAE,IAAA;UACA,QAAAN;QACA;QACA,MAAAoB,QAAA,cAAAhC,UAAA,uBAAA+B,MAAA;QACA,IAAAC,QAAA,CAAAC,IAAA;UACA,KAAAxB,gBAAA,GAAAuB,QAAA,CAAA1B,IAAA,CAAA4B,IAAA;UACA,KAAAlB,UAAA,CAAAG,KAAA,GAAAa,QAAA,CAAA1B,IAAA,CAAAa,KAAA;QACA;MACA,SAAAgB,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAA5B,OAAA;MACA;IACA;IAEA,MAAAuB,UAAA;MACA;QACA,MAAAE,QAAA,cAAAhC,UAAA;QACA,IAAAgC,QAAA,CAAAC,IAAA;UACA,KAAAvB,UAAA,GAAAsB,QAAA,CAAA1B,IAAA,CAAAa,KAAA;UACA,KAAAR,WAAA,GAAAqB,QAAA,CAAA1B,IAAA,CAAAgC,MAAA;QACA;MACA,SAAAH,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACA;IACA;IAEA,MAAAI,WAAAC,YAAA;MACA;QACA,MAAAR,QAAA,cAAA/B,WAAA;UACAwC,EAAA,EAAAD,YAAA,CAAAC;QACA;QACA,IAAAT,QAAA,CAAAC,IAAA;UACAO,YAAA,CAAAE,IAAA;UACA,KAAA/B,WAAA,GAAAgC,IAAA,CAAAC,GAAA,SAAAjC,WAAA;UACA,KAAA0B,QAAA,CAAAQ,OAAA;QACA;MACA,SAAAV,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEA,MAAAW,cAAA;MACA;QACA,WAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAnC,IAAA;QACA;QAEA,MAAAkB,QAAA,cAAA/B,WAAA;QACA,IAAA+B,QAAA,CAAAC,IAAA;UACA,KAAAxB,gBAAA,CAAAyC,OAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAT,IAAA;UACA,KAAA/B,WAAA;UACA,KAAA0B,QAAA,CAAAQ,OAAA;QACA;MACA,SAAAV,KAAA;QACA,IAAAA,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA;QACA;MACA;IACA;IAEA,MAAAiB,mBAAAZ,YAAA;MACA;QACA,WAAAO,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAnC,IAAA;QACA;QAEA,MAAAkB,QAAA,cAAA9B,aAAA;UAAAuC,EAAA,EAAAD,YAAA,CAAAC;QAAA;QACA,IAAAT,QAAA,CAAAC,IAAA;UACA,KAAAI,QAAA,CAAAQ,OAAA;UACA,KAAAhB,iBAAA;UACA,KAAAC,SAAA;QACA;MACA,SAAAK,KAAA;QACA,IAAAA,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA;QACA;MACA;IACA;IAEA,MAAAkB,oBAAA;MACA;QACA,WAAAC,KAAA,CAAAC,gBAAA,CAAAC,QAAA;QAEA,MAAAxB,QAAA,cAAA/B,WAAA,8BAAAmB,eAAA;QACA,IAAAY,QAAA,CAAAC,IAAA;UACA,KAAAI,QAAA,CAAAQ,OAAA;UACA,KAAArC,aAAA;UACA,KAAAiD,SAAA;UACA,KAAA5B,iBAAA;UACA,KAAAC,SAAA;QACA;MACA,SAAAK,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEAsB,UAAA;MACA,KAAArC,eAAA;QACAC,KAAA;QACAC,OAAA;QACAR,IAAA;QACAC,KAAA;QACAQ,WAAA;MACA;MACA,KAAA+B,KAAA,CAAAC,gBAAA,SAAAD,KAAA,CAAAC,gBAAA,CAAAG,WAAA;IACA;IAEAC,YAAA;MACA,KAAA/C,UAAA;QACAC,OAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACA,KAAAC,UAAA,CAAAC,IAAA;MACA,KAAAY,iBAAA;IACA;IAEA+B,iBAAA1C,IAAA;MACA,KAAAF,UAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAF,UAAA,CAAAC,IAAA;MACA,KAAAY,iBAAA;IACA;IAEAgC,oBAAA5C,IAAA;MACA,KAAAD,UAAA,CAAAC,IAAA,GAAAA,IAAA;MACA,KAAAY,iBAAA;IACA;IAEAiC,aAAA/C,KAAA;MACA,MAAAgD,GAAA;QACAC,IAAA;QACAC,OAAA;QACA9B,KAAA;QACAU,OAAA;MACA;MACA,OAAAkB,GAAA,CAAAhD,KAAA;IACA;IAEAmD,aAAAnD,KAAA;MACA,MAAAgD,GAAA;QACAC,IAAA;QACAC,OAAA;QACA9B,KAAA;QACAU,OAAA;MACA;MACA,OAAAkB,GAAA,CAAAhD,KAAA;IACA;IAEAoD,aAAArD,IAAA;MACA,MAAAiD,GAAA;QACAK,MAAA;QACAC,MAAA;QACAC,MAAA;QACAL,OAAA;MACA;MACA,OAAAF,GAAA,CAAAjD,IAAA;IACA;IAEAyD,YAAAzD,IAAA;MACA,MAAAiD,GAAA;QACAK,MAAA;QACAC,MAAA;QACAC,MAAA;QACAL,OAAA;MACA;MACA,OAAAF,GAAA,CAAAjD,IAAA;IACA;EACA;AACA", "ignoreList": []}]}