(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-lvshi-ruzhu"],{"0015":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-015d368c]{background-color:#4866ad}.image[data-v-015d368c]{width:%?160?%;height:%?160?%!important}.demo[data-v-015d368c]{text-align:center;background:#fff;padding:%?0?%;width:%?160?%}.demo1[data-v-015d368c]{height:%?160?%;line-height:%?160?%;margin:0 auto;text-align:center;background:#fff;width:%?160?%}.head[data-v-015d368c]{display:flex;flex-wrap:wrap;height:%?380?%}.cols[data-v-015d368c]{color:#fff!important}.head .title[data-v-015d368c],\n.harmoniou[data-v-015d368c],\n.harmonious[data-v-015d368c]{width:100%}.ov[data-v-015d368c]{display:none}.head .title[data-v-015d368c]{height:18%;display:flex}.head .harmoniou[data-v-015d368c]{height:30%;color:#fff;font-size:%?60?%;line-height:%?100?%;text-align:center}.head .harmonious[data-v-015d368c]{height:25%;color:#fff;font-size:%?50?%;line-height:%?80?%;text-align:center}.head .title .title-img[data-v-015d368c]{display:flex;width:100%;height:100%}.head .title .title-img uni-image[data-v-015d368c]{margin:auto;width:%?150?%;height:%?150?%;line-height:%?80?%}.back[data-v-015d368c]{padding-bottom:%?120?%!important}.btn[data-v-015d368c]{width:100%;bottom:0;position:fixed;background:#577eee;font-size:%?28?%;z-index:999;line-height:%?84?%;color:#fff;border-radius:%?0?%}.btn[data-v-015d368c]::after{border:none}.tui-center[data-v-015d368c]{text-align:center;color:#fff}.content-class[data-v-015d368c]{width:90%;margin:%?20?% auto;display:flex;flex-flow:row wrap}.content-class .item[data-v-015d368c]{width:30%;height:%?60?%;font-size:%?28?%;line-height:%?60?%;border-radius:%?30?%;color:#fff;margin-right:%?30?%;margin-bottom:%?20?%;text-align:center;box-sizing:border-box;border:%?1?% solid #fff}.content-class .item uni-checkbox[data-v-015d368c]{display:none}.content-class .item[data-v-015d368c]:nth-of-type(3n){margin-right:0}.content-class .on[data-v-015d368c]{border:none;background-color:#3f82e7;color:#fff}.had[data-v-015d368c]{height:%?165?%!important}.texts[data-v-015d368c]{height:29vh!important}.mui-input-row[data-v-015d368c]{position:relative;margin:%?0?% auto %?20?%;height:%?97?%;width:95%;font-size:%?28?%}.mui-input-row uni-label[data-v-015d368c]{position:absolute;top:%?23.5?%;font-size:%?28?%;line-height:%?50?%;color:#333;margin-left:%?20?%;left:0;right:auto}.mui-input-row uni-textarea[data-v-015d368c]{background-color:#fff;border-radius:%?10?%;text-align:left;height:25vh;width:calc(100% - %?55?%);padding:%?25?% %?25?% %?25?% %?30?%}.mui-input-row .vt[data-v-015d368c]{border-radius:%?10?%;text-align:right;display:flex;margin-left:72px}.mui-input-row .tmr[data-v-015d368c]{background-color:#fff;border-radius:%?10?%;text-align:right;padding:%?25?% %?30?% %?25?% %?150?%}.mui-input-row uni-input[data-v-015d368c]{background-color:#fff;border-radius:%?10?%;text-align:right;padding:%?25?% %?30?% %?25?% %?150?%}.mui-input-row .picker[data-v-015d368c]{background-color:#fff;border-radius:%?10?%;text-align:right}body.?%PAGE?%[data-v-015d368c]{background-color:#4866ad}",""]),t.exports=e},"1bfb":function(t,e,a){var i=a("0015");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("6ce1ef2a",i,!0,{sourceMap:!1,shadowMode:!1})},"7a037":function(t,e,a){"use strict";a.r(e);var i=a("c103"),n=a.n(i);for(var r in i)"default"!==r&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"8b67":function(t,e,a){"use strict";var i=a("1bfb"),n=a.n(i);n.a},a481:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uniDataPicker:a("ce50").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"back"},[a("v-uni-view",{staticClass:"head"},[a("v-uni-view",{staticClass:"title"}),a("v-uni-view",{staticClass:"harmoniou"},[a("v-uni-text",[t._v("欢迎入驻律点科技")])],1),a("v-uni-view",{staticClass:"harmonious"},[a("v-uni-text",[t._v("让律点科技，走进千家万户。")])],1)],1),a("v-uni-form",{on:{submit:function(e){arguments[0]=e=t.$handleEvent(e),t.formSubmit.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"mui-input-row"},[a("v-uni-label",[t._v("联系姓名")]),a("v-uni-input",{attrs:{type:"text",placeholder:"请输入姓名",name:"title",maxlength:"10",value:""}})],1),a("v-uni-view",{staticClass:"mui-input-row"},[a("v-uni-label",[t._v("手机号码")]),a("v-uni-input",{attrs:{type:"number",placeholder:"请输入手机号码",name:"phone",maxlength:"11",value:""}})],1),a("v-uni-view",{staticClass:"mui-input-row"},[a("v-uni-label",[t._v("地址")]),a("v-uni-input",{attrs:{type:"text",placeholder:"请输入地址",name:"address",maxlength:"30",value:""}})],1),a("v-uni-view",{staticClass:"mui-input-row"},[a("uni-data-picker",{attrs:{localdata:t.areas,"popup-title":"请选择班级"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindRegionChange.apply(void 0,arguments)}}},[a("v-uni-label",[t._v("选择地区")]),a("v-uni-input",{attrs:{type:"text",placeholder:"市",disabled:!0,value:t.area}})],1)],1),a("v-uni-view",{staticClass:"mui-input-row texts"},[a("v-uni-textarea",{attrs:{name:"desc",placeholder:"请输入介绍"}})],1),a("v-uni-view",{staticClass:"mui-input-row had"},[a("v-uni-label",{staticClass:"cols"},[t._v("上传执照")]),a("v-uni-view",{staticClass:"vt"},[a("v-uni-view",{staticClass:"uni-padding-wrap uni-common-mt",staticStyle:{width:"100%","text-align":"center"}},[a("v-uni-view",{staticClass:"demo"},[t.imageSrc1?[a("v-uni-image",{staticClass:"image",attrs:{src:t.imageSrc1||"/static/img/logo14.png",mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseImage1.apply(void 0,arguments)}}})]:[a("v-uni-view",{staticClass:"demo1"},[a("v-uni-view",{staticClass:"uni-hello-addfile",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseImage1.apply(void 0,arguments)}}},[t._v("+ 执照")])],1)]],2)],1)],1)],1),a("v-uni-button",{staticClass:"btn",attrs:{"form-type":"submit"}},[t._v("提交资料")])],1)],1)},r=[]},c103:function(t,e,a){"use strict";var i=a("4ea4");a("c975"),a("fb6a"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;i(a("aec5"));var n,r=i(a("ce50")),o={components:{uniDataPicker:r.default},data:function(){return{area:"市",citycode:0,pcode:0,region:[["省","省1"],["市","市1"]],lawyertype:[],typeindex:0,maleLike:[],currentArr:[],oldArr:[],hasPass:!1,imageSrc:"",imageSrc1:"",cardData:[],lawyer_from:{title:"",phone:"",type_id:"",age:"",uid:"",laywer_card:"",address:"",desc:"",city_id:"",pic_path:"",card_path:"",pid:""},status:0,areas:[]}},onLoad:function(){n=this,n.lawyerType(),n.lawyerCateList(),n.lawyerArea()},onShow:function(){},methods:{bindPickerChange:function(t){n.typeindex=t.detail.value},bindRegionChange:function(t){console.log(t),this.area=t.detail.value[0].text+t.detail.value[1].text,this.citycode=t.detail.value[1].value,this.pcode=t.detail.value[0].value},initData:function(){if(null==n.$store.user_info)return n.$myutil.showModalNoCancel("未登录",(function(t){uni.navigateTo({url:"../../login/login"})})),!1},lawyerType:function(){n.$post({url:"index/lawyerType.html",data:{}},(function(t){n.lawyertype=t.data.title}))},lawyerArea:function(){n.$post({url:"index/lawyerArea.html",data:{}},(function(t){n.areas=t.data}))},lawyerCateList:function(){n.$post({url:"index/lawyerCateList.html",data:{}},(function(t){n.maleLike=t.data}))},chooseImage:function(){var t=n.$myconfig.myconfig.url+"upload/uploadImage.html";uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"],success:function(e){var a=e.tempFilePaths[0];uni.uploadFile({url:t,filePath:a,fileType:"image",name:"file",success:function(t){var e=JSON.parse(t.data||"{}");console.log(e),200==e.code?(uni.showToast({title:"上传成功",icon:"success",duration:1e3}),n.imageSrc=n.$myconfig.myconfig.pic_url+e.data.url):uni.showModal({content:e.msg,showCancel:!1})},fail:function(t){uni.showModal({content:t.errMsg,showCancel:!1})}})},fail:function(t){}})},chooseImage1:function(){var t=n.$myconfig.myconfig.url+"upload/uploadImage.html";uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album"],success:function(e){var a=e.tempFilePaths[0];uni.uploadFile({url:t,filePath:a,fileType:"image",name:"file",success:function(t){var e=JSON.parse(t.data||"{}");200==e.code?(uni.showToast({title:"上传成功",icon:"success",duration:1e3}),n.imageSrc1=n.$myconfig.myconfig.pic_url+e.data.url):uni.showModal({content:e.msg,showCancel:!1})},fail:function(t){console.log("uploadImage fail",t),uni.showModal({content:t.errMsg,showCancel:!1})}})},fail:function(t){console.log("chooseImage fail",t)}})},chooseMaleLike:function(t){if(t.detail.value.length>3)if(this.hasPass)if(t.detail.value.length>this.oldArr.length){var e=t.detail.value.length;this.currentArr.length<3?this.currentArr.push(t.detail.value[e-1]):uni.showToast({title:"最多3个",icon:"none"})}else{var a=[];for(o=0;o<this.currentArr.length;o++){var i=t.detail.value.indexOf(this.currentArr[o]);-1!==i&&a.push(this.currentArr[o])}this.currentArr=a}else if(this.hasPass=!0,t.detail.value.length>this.oldArr.length)this.currentArr=t.detail.value.slice(0,3),uni.showToast({title:"最多3个",icon:"none"});else{for(var r=[],o=0;o<this.currentArr.length;o++)for(var l=0;l<3;l++)this.currentArr[o]===t.detail.value[l]&&r.push(this.currentArr[o]);this.currentArr=r}else{if(this.hasPass)if(t.detail.value.length<this.oldArr.length){var c=[];for(o=0;o<this.currentArr.length;o++){var s=t.detail.value.indexOf(this.currentArr[o]);-1!==s&&c.push(this.currentArr[o])}this.currentArr=c}else{var u=t.detail.value.length;this.currentArr.push(t.detail.value[u-1])}else this.currentArr=t.detail.value;0===t.detail.value.length&&(this.hasPass=!1)}o=0;for(var d=this.maleLike.length;o<d;++o){this.maleLike[o].isChecked=!1;l=0;for(var h=this.currentArr.length;l<h;++l)if(String(this.maleLike[o].id)===String(this.currentArr[l])){this.maleLike[o].isChecked=!0;break}}this.oldArr=t.detail.value,console.log(n.currentArr)},formSubmit:function(t){n.lawyer_from.title=t.detail.value.title,n.lawyer_from.phone=t.detail.value.phone,n.lawyer_from.address=t.detail.value.address,n.lawyer_from.desc=t.detail.value.desc,n.lawyer_from.card_path=n.imageSrc1,n.lawyer_from.city_id=n.citycode,n.lawyer_from.pid=n.pcode,n.$post({url:"index/lawyerSave.html",data:n.lawyer_from},(function(t){200==t.code?(n.$api.msg("提交成功"),setTimeout((function(){}),2e3)):n.$api.msg(t.msg)}))}}};e.default=o},fce0:function(t,e,a){"use strict";a.r(e);var i=a("a481"),n=a("7a037");for(var r in n)"default"!==r&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("8b67");var o,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"015d368c",null,!1,i["a"],o);e["default"]=c.exports}}]);