# @babel/plugin-transform-class-properties

> This plugin transforms static class properties as well as properties declared with the property initializer syntax

See our website [@babel/plugin-transform-class-properties](https://babeljs.io/docs/babel-plugin-transform-class-properties) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-class-properties
```

or using yarn:

```sh
yarn add @babel/plugin-transform-class-properties --dev
```
