(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-14875124"],{"306c":function(t,s,a){"use strict";a("a85a")},a85a:function(t,s,a){},b3ec:function(t,s,a){"use strict";a("e2a9")},c693:function(t,s,a){"use strict";a.r(s);var i=function(){var t=this,s=t._self._c;return s("div",{staticClass:"dashboard-container"},[s("div",{staticClass:"welcome-section"},[s("div",{staticClass:"welcome-content"},[s("h1",{staticClass:"welcome-title"},[t._v("欢迎使用法律服务管理系统")]),s("p",{staticClass:"welcome-subtitle"},[t._v(t._s(t.getCurrentTime())+" | 管理员，您好！")])]),s("div",{staticClass:"welcome-actions"},[s("el-button",{attrs:{type:"primary"},on:{click:function(s){return t.handleQuickAction("new-case")}}},[s("i",{staticClass:"el-icon-plus"}),t._v(" 新建案件 ")]),s("el-button",{attrs:{type:"success"},on:{click:function(s){return t.handleQuickAction("new-contract")}}},[s("i",{staticClass:"el-icon-document-add"}),t._v(" 新建合同 ")]),s("el-button",{attrs:{type:"info",loading:t.loading},on:{click:t.refreshData}},[s("i",{staticClass:"el-icon-refresh"}),t._v(" 刷新数据 ")])],1)]),s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"stats-section"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[s("div",{staticClass:"stat-card"},[s("div",{staticClass:"stat-icon user-icon"},[s("i",{staticClass:"el-icon-user"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.totalUsers))]),s("div",{staticClass:"stat-label"},[t._v("总用户数")]),s("div",{staticClass:"stat-change positive"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" +12% ")])])])]),s("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[s("div",{staticClass:"stat-card"},[s("div",{staticClass:"stat-icon case-icon"},[s("i",{staticClass:"el-icon-folder"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.totalCases))]),s("div",{staticClass:"stat-label"},[t._v("案件总数")]),s("div",{staticClass:"stat-change positive"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" +8% ")])])])]),s("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[s("div",{staticClass:"stat-card"},[s("div",{staticClass:"stat-icon contract-icon"},[s("i",{staticClass:"el-icon-document"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.totalContracts))]),s("div",{staticClass:"stat-label"},[t._v("合同数量")]),s("div",{staticClass:"stat-change positive"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" +15% ")])])])]),s("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[s("div",{staticClass:"stat-card"},[s("div",{staticClass:"stat-icon revenue-icon"},[s("i",{staticClass:"el-icon-money"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v("¥"+t._s(t.stats.totalRevenue))]),s("div",{staticClass:"stat-label"},[t._v("总收入")]),s("div",{staticClass:"stat-change positive"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" +22% ")])])])])],1)],1),s("el-row",{staticClass:"main-content",attrs:{gutter:20}},[s("el-col",{attrs:{xs:24,sm:24,md:16,lg:16,xl:16}},[s("div",{staticClass:"chart-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("业务数据趋势")]),s("div",{staticClass:"chart-controls"},[s("el-radio-group",{attrs:{size:"small"},model:{value:t.chartPeriod,callback:function(s){t.chartPeriod=s},expression:"chartPeriod"}},[s("el-radio-button",{attrs:{label:"week"}},[t._v("本周")]),s("el-radio-button",{attrs:{label:"month"}},[t._v("本月")]),s("el-radio-button",{attrs:{label:"year"}},[t._v("本年")])],1)],1)]),s("div",{staticClass:"chart-container"},[s("div",{staticClass:"chart-placeholder"},[s("i",{staticClass:"el-icon-data-line chart-icon"}),s("p",[t._v("数据图表区域")]),s("p",{staticClass:"chart-desc"},[t._v("这里可以集成 ECharts 或其他图表库显示业务数据趋势")])])])])],1),s("div",{staticClass:"activity-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("最近活动")]),s("el-button",{attrs:{type:"text"},on:{click:t.viewAllActivities}},[t._v("查看全部")])],1),s("div",{staticClass:"activity-list"},t._l(t.recentActivities,(function(a){return s("div",{key:a.id,staticClass:"activity-item"},[s("div",{staticClass:"activity-avatar"},[s("i",{class:a.icon,style:{color:a.color}})]),s("div",{staticClass:"activity-content"},[s("div",{staticClass:"activity-title"},[t._v(t._s(a.title))]),s("div",{staticClass:"activity-desc"},[t._v(t._s(a.description))]),s("div",{staticClass:"activity-time"},[t._v(t._s(a.time))])])])})),0)])],1)]),s("el-col",{attrs:{xs:24,sm:24,md:8,lg:8,xl:8}},[s("div",{staticClass:"quick-actions-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("快捷操作")])]),s("div",{staticClass:"quick-actions"},t._l(t.quickActions,(function(a){return s("div",{key:a.id,staticClass:"quick-action-item",on:{click:function(s){return t.handleQuickAction(a.action)}}},[s("div",{staticClass:"action-icon",style:{backgroundColor:a.color}},[s("i",{class:a.icon})]),s("div",{staticClass:"action-content"},[s("div",{staticClass:"action-title"},[t._v(t._s(a.title))]),s("div",{staticClass:"action-desc"},[t._v(t._s(a.description))])])])})),0)])],1),s("div",{staticClass:"todo-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("待办事项")]),s("el-badge",{staticClass:"todo-badge",attrs:{value:t.todoList.filter(t=>!t.completed).length}},[s("el-button",{attrs:{type:"text"},on:{click:t.viewAllTodos}},[t._v("查看全部")])],1)],1),s("div",{staticClass:"todo-list"},t._l(t.todoList.slice(0,5),(function(a){return s("div",{key:a.id,staticClass:"todo-item",class:{completed:a.completed}},[s("el-checkbox",{on:{change:function(s){return t.handleTodoChange(a)}},model:{value:a.completed,callback:function(s){t.$set(a,"completed",s)},expression:"todo.completed"}},[t._v(" "+t._s(a.title)+" ")]),s("div",{staticClass:"todo-priority",class:a.priority},[t._v(" "+t._s(t.getPriorityText(a.priority))+" ")])],1)})),0)])],1),s("div",{staticClass:"notification-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("系统通知")]),s("el-badge",{staticClass:"notification-badge",attrs:{value:t.notifications.filter(t=>!t.read).length}},[s("el-button",{attrs:{type:"text"},on:{click:t.viewAllNotifications}},[t._v("查看全部")])],1)],1),s("div",{staticClass:"notification-list"},t._l(t.notifications.slice(0,3),(function(a){return s("div",{key:a.id,staticClass:"notification-item",class:{unread:!a.read},on:{click:function(s){return t.markAsRead(a)}}},[s("div",{staticClass:"notification-content"},[s("div",{staticClass:"notification-title"},[t._v(t._s(a.title))]),s("div",{staticClass:"notification-time"},[t._v(t._s(a.time))])]),a.read?t._e():s("div",{staticClass:"notification-dot"})])})),0)])],1),s("div",{staticClass:"system-monitor-section"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("系统监控")])]),s("div",{staticClass:"system-monitor-content"},[s("system-monitor")],1)])],1)])],1)],1)},e=[],c=(a("14d9"),function(){var t=this,s=t._self._c;return s("div",{staticClass:"system-monitor"},[s("el-card",{attrs:{shadow:"hover"}},[s("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[s("span",{staticClass:"card-title"},[t._v("系统状态监控")]),s("el-button",{attrs:{type:"text"},on:{click:t.refreshData}},[s("i",{staticClass:"el-icon-refresh"}),t._v(" 刷新 ")])],1),s("div",{staticClass:"monitor-content"},[s("div",{staticClass:"status-indicators"},[s("div",{staticClass:"status-item"},[s("div",{staticClass:"status-icon online"},[s("i",{staticClass:"el-icon-success"})]),s("div",{staticClass:"status-info"},[s("div",{staticClass:"status-label"},[t._v("系统状态")]),s("div",{staticClass:"status-value"},[t._v("正常运行")])])]),s("div",{staticClass:"status-item"},[s("div",{staticClass:"status-icon"},[s("i",{staticClass:"el-icon-time"})]),s("div",{staticClass:"status-info"},[s("div",{staticClass:"status-label"},[t._v("运行时间")]),s("div",{staticClass:"status-value"},[t._v(t._s(t.systemUptime))])])]),s("div",{staticClass:"status-item"},[s("div",{staticClass:"status-icon"},[s("i",{staticClass:"el-icon-user"})]),s("div",{staticClass:"status-info"},[s("div",{staticClass:"status-label"},[t._v("在线用户")]),s("div",{staticClass:"status-value"},[t._v(t._s(t.onlineUsers))])])])]),s("div",{staticClass:"performance-metrics"},[s("div",{staticClass:"metric-item"},[s("div",{staticClass:"metric-header"},[s("span",{staticClass:"metric-label"},[t._v("CPU使用率")]),s("span",{staticClass:"metric-value"},[t._v(t._s(t.systemMetrics.cpu)+"%")])]),s("el-progress",{attrs:{percentage:t.systemMetrics.cpu,color:t.getProgressColor(t.systemMetrics.cpu),"show-text":!1}})],1),s("div",{staticClass:"metric-item"},[s("div",{staticClass:"metric-header"},[s("span",{staticClass:"metric-label"},[t._v("内存使用率")]),s("span",{staticClass:"metric-value"},[t._v(t._s(t.systemMetrics.memory)+"%")])]),s("el-progress",{attrs:{percentage:t.systemMetrics.memory,color:t.getProgressColor(t.systemMetrics.memory),"show-text":!1}})],1),s("div",{staticClass:"metric-item"},[s("div",{staticClass:"metric-header"},[s("span",{staticClass:"metric-label"},[t._v("磁盘使用率")]),s("span",{staticClass:"metric-value"},[t._v(t._s(t.systemMetrics.disk)+"%")])]),s("el-progress",{attrs:{percentage:t.systemMetrics.disk,color:t.getProgressColor(t.systemMetrics.disk),"show-text":!1}})],1)]),s("div",{staticClass:"service-status"},[s("div",{staticClass:"service-title"},[t._v("服务状态")]),s("div",{staticClass:"service-list"},t._l(t.services,(function(a){return s("div",{key:a.name,staticClass:"service-item"},[s("div",{staticClass:"service-info"},[s("span",{staticClass:"service-name"},[t._v(t._s(a.name))]),s("span",{staticClass:"service-status-badge",class:a.status},[t._v(" "+t._s("online"===a.status?"正常":"异常")+" ")])]),s("div",{staticClass:"service-details"},[s("span",{staticClass:"service-version"},[t._v(t._s(a.version))]),s("span",{staticClass:"service-uptime"},[t._v("运行 "+t._s(a.uptime))])])])})),0)])])])],1)}),o=[],l={name:"SystemMonitor",data(){return{systemUptime:"7天 12小时 35分钟",onlineUsers:23,systemMetrics:{cpu:45,memory:62,disk:38},services:[{name:"Web服务器",status:"online",version:"v2.1.0",uptime:"7天"},{name:"数据库",status:"online",version:"MySQL 8.0",uptime:"7天"},{name:"文件服务",status:"online",version:"v1.5.2",uptime:"7天"},{name:"邮件服务",status:"online",version:"v3.2.1",uptime:"6天"}]}},mounted(){this.startMonitoring()},beforeDestroy(){this.monitorTimer&&clearInterval(this.monitorTimer)},methods:{startMonitoring(){this.monitorTimer=setInterval(()=>{this.updateMetrics()},5e3)},updateMetrics(){this.systemMetrics.cpu=Math.floor(30*Math.random())+30,this.systemMetrics.memory=Math.floor(20*Math.random())+50,this.systemMetrics.disk=Math.floor(15*Math.random())+30,this.onlineUsers=Math.floor(10*Math.random())+20},refreshData(){this.updateMetrics(),this.$message.success("数据已刷新")},getProgressColor(t){return t<50?"#67C23A":t<80?"#E6A23C":"#F56C6C"}}},r=l,n=(a("306c"),a("2877")),d=Object(n["a"])(r,c,o,!1,null,"d0a6f122",null),v=d.exports,u={name:"Dashboard",components:{SystemMonitor:v},data(){return{chartPeriod:"month",loading:!1,stats:{totalUsers:0,totalCases:0,totalContracts:0,totalRevenue:"0"},recentActivities:[],quickActions:[{id:1,icon:"el-icon-plus",color:"#409EFF",title:"新建案件",description:"创建新的法律案件",action:"new-case"},{id:2,icon:"el-icon-document-add",color:"#67C23A",title:"新建合同",description:"创建新的合同文档",action:"new-contract"},{id:3,icon:"el-icon-user-solid",color:"#E6A23C",title:"添加客户",description:"添加新的客户信息",action:"new-client"},{id:4,icon:"el-icon-upload",color:"#F56C6C",title:"文件归档",description:"上传并归档文件",action:"upload-file"}],todoList:[{id:1,title:"审核张三的合同申请",completed:!1,priority:"high"},{id:2,title:"回复李四的法律咨询",completed:!1,priority:"medium"},{id:3,title:"准备明天的庭审材料",completed:!0,priority:"high"},{id:4,title:"更新客户联系信息",completed:!1,priority:"low"},{id:5,title:"整理本月财务报表",completed:!1,priority:"medium"}],notifications:[{id:1,title:"系统维护通知",time:"今天 14:30",read:!1},{id:2,title:"新版本更新",time:"昨天 16:20",read:!1},{id:3,title:"数据备份完成",time:"昨天 09:15",read:!0}]}},mounted(){this.loadDashboardData()},methods:{async loadDashboardData(){this.loading=!0;try{await Promise.all([this.loadStats(),this.loadActivities(),this.loadTodos(),this.loadQuickActions(),this.loadNotifications()])}catch(t){console.error("加载仪表板数据失败:",t),this.$message.error("加载数据失败，请刷新页面重试")}finally{this.loading=!1}},async loadStats(){try{const t=await this.getRequest("/dashboard/getStats");if(200===t.code){const s=t.data;this.stats={totalUsers:s.totalUsers||0,totalCases:s.totalDebts||0,totalContracts:s.totalOrders||0,totalRevenue:s.totalRevenue||"0"}}}catch(t){console.error("加载统计数据失败:",t)}},async loadActivities(){try{const t=await this.getRequest("/dashboard/getActivities");200===t.code&&(this.recentActivities=t.data||[])}catch(t){console.error("加载活动数据失败:",t)}},async loadTodos(){try{const t=await this.getRequest("/dashboard/getTodos");200===t.code&&(this.todoList=t.data||[])}catch(t){console.error("加载待办事项失败:",t)}},async loadQuickActions(){try{const t=await this.getRequest("/dashboard/getQuickActions");if(200===t.code){const s=t.data||[];this.quickActions=s.map(t=>({id:t.title,icon:t.icon,color:t.color,title:t.title,description:"当前数量: "+t.count,action:t.url}))}}catch(t){console.error("加载快捷操作失败:",t)}},async loadNotifications(){try{const t=await this.getRequest("/dashboard/getNotifications");200===t.code&&(this.notifications=t.data||[])}catch(t){console.error("加载通知失败:",t)}},getCurrentTime(){const t=new Date,s={year:"numeric",month:"long",day:"numeric",weekday:"long"};return t.toLocaleDateString("zh-CN",s)},handleQuickAction(t){if(t.startsWith("/"))this.$router.push(t);else switch(t){case"new-case":this.$router.push("/debts");break;case"new-contract":this.$router.push("/dingdan");break;case"new-client":this.$router.push("/user");break;case"upload-file":this.$router.push("/archive/file");break;default:this.$message.info("执行操作: "+t)}},viewAllActivities(){this.$message.info("查看所有活动")},viewAllTodos(){this.$message.info("查看所有待办事项")},viewAllNotifications(){this.$message.info("查看所有通知")},async handleTodoChange(t){try{const s=await this.postRequest("/dashboard/updateTodo",{id:t.id,completed:t.completed});200===s.code&&this.$message.success(t.completed?"任务已完成":"任务已重新激活")}catch(s){console.error("更新待办事项失败:",s),t.completed=!t.completed,this.$message.error("更新失败，请重试")}},async markAsRead(t){try{const s=await this.postRequest("/dashboard/markNotificationRead",{id:t.id});200===s.code&&(t.read=!0,this.$message.success("通知已标记为已读"))}catch(s){console.error("标记通知失败:",s),this.$message.error("操作失败，请重试")}},getPriorityText(t){const s={high:"高",medium:"中",low:"低"};return s[t]||"中"},refreshData(){this.loadDashboardData(),this.$message.success("数据已刷新")}}},m=u,h=(a("b3ec"),Object(n["a"])(m,i,e,!1,null,"2aa23989",null));s["default"]=h.exports},e2a9:function(t,s,a){}}]);
//# sourceMappingURL=chunk-14875124.9fc649dd.js.map