{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue?vue&type=style&index=0&id=8f6e51f2&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue", "mtime": 1749567732258}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["NotificationList.vue"], "names": [], "mappings": ";AA0aA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "NotificationList.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\n  <div class=\"notification-container\">\n    <div class=\"page-header\">\n      <h2>系统通知</h2>\n      <div class=\"header-actions\">\n        <el-button @click=\"markAllAsRead\" :disabled=\"unreadCount === 0\">\n          <i class=\"el-icon-check\"></i> 全部标记为已读\n        </el-button>\n        <el-button type=\"primary\" @click=\"showAddDialog = true\">\n          <i class=\"el-icon-plus\"></i> 发布通知\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"stats-row\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ totalCount }}</div>\n            <div class=\"stat-label\">总通知数</div>\n          </div>\n          <i class=\"el-icon-bell stat-icon\"></i>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number unread\">{{ unreadCount }}</div>\n            <div class=\"stat-label\">未读通知</div>\n          </div>\n          <i class=\"el-icon-message stat-icon\"></i>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 筛选条件 -->\n    <el-card class=\"filter-card\" shadow=\"never\">\n      <el-form :inline=\"true\" :model=\"filterForm\" class=\"filter-form\">\n        <el-form-item label=\"状态\">\n          <el-select v-model=\"filterForm.is_read\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未读\" value=\"0\"></el-option>\n            <el-option label=\"已读\" value=\"1\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"类型\">\n          <el-select v-model=\"filterForm.type\" placeholder=\"请选择类型\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\">\n          <el-select v-model=\"filterForm.level\" placeholder=\"请选择级别\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"loadNotifications\">查询</el-button>\n          <el-button @click=\"resetFilter\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 通知列表 -->\n    <el-card class=\"list-card\">\n      <div class=\"notification-list\" v-loading=\"loading\">\n        <div \n          v-for=\"notification in notificationList\" \n          :key=\"notification.id\"\n          class=\"notification-item\"\n          :class=\"{ 'unread': !notification.read }\"\n        >\n          <div class=\"notification-header\">\n            <div class=\"notification-meta\">\n              <el-tag \n                :type=\"getLevelType(notification.level)\" \n                size=\"small\"\n                class=\"level-tag\"\n              >\n                {{ getLevelText(notification.level) }}\n              </el-tag>\n              <el-tag \n                :type=\"getTypeColor(notification.type)\" \n                size=\"small\"\n                class=\"type-tag\"\n              >\n                {{ getTypeText(notification.type) }}\n              </el-tag>\n              <span class=\"notification-time\">{{ notification.time }}</span>\n            </div>\n            <div class=\"notification-actions\">\n              <el-button \n                v-if=\"!notification.read\" \n                type=\"text\" \n                size=\"small\" \n                @click=\"markAsRead(notification)\"\n              >\n                标记已读\n              </el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"deleteNotification(notification)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n          <div class=\"notification-content\">\n            <h4 class=\"notification-title\">{{ notification.title }}</h4>\n            <p class=\"notification-desc\">{{ notification.content }}</p>\n          </div>\n        </div>\n\n        <div v-if=\"notificationList.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-bell\"></i>\n          <p>暂无通知</p>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.page\"\n          :page-sizes=\"[10, 20, 50]\"\n          :page-size=\"pagination.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 发布通知对话框 -->\n    <el-dialog \n      title=\"发布通知\" \n      :visible.sync=\"showAddDialog\"\n      width=\"600px\"\n    >\n      <el-form :model=\"newNotification\" :rules=\"notificationRules\" ref=\"notificationForm\" label-width=\"100px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"newNotification.title\" placeholder=\"请输入通知标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"内容\" prop=\"content\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"newNotification.content\" \n            placeholder=\"请输入通知内容\"\n            :rows=\"4\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"type\">\n          <el-select v-model=\"newNotification.type\" placeholder=\"请选择类型\">\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"level\">\n          <el-select v-model=\"newNotification.level\" placeholder=\"请选择级别\">\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"目标用户\">\n          <el-select v-model=\"newNotification.target_type\" placeholder=\"请选择目标用户\">\n            <el-option label=\"所有用户\" value=\"all\"></el-option>\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\n            <el-option label=\"普通用户\" value=\"user\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showAddDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"publishNotification\">发布</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\n\nexport default {\n  name: 'NotificationList',\n  mixins: [{ methods: { getRequest, postRequest, deleteRequest } }],\n  data() {\n    return {\n      loading: false,\n      showAddDialog: false,\n      notificationList: [],\n      totalCount: 0,\n      unreadCount: 0,\n      filterForm: {\n        is_read: '',\n        type: '',\n        level: ''\n      },\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      },\n      newNotification: {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      },\n      notificationRules: {\n        title: [\n          { required: true, message: '请输入标题', trigger: 'blur' }\n        ],\n        content: [\n          { required: true, message: '请输入内容', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择类型', trigger: 'change' }\n        ],\n        level: [\n          { required: true, message: '请选择级别', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadNotifications()\n    this.loadStats()\n  },\n  methods: {\n    async loadNotifications() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          size: this.pagination.size,\n          ...this.filterForm\n        }\n        const response = await this.getRequest('/notification/list', params)\n        if (response.code === 200) {\n          this.notificationList = response.data.list || []\n          this.pagination.total = response.data.total || 0\n        }\n      } catch (error) {\n        console.error('加载通知失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadStats() {\n      try {\n        const response = await this.getRequest('/notification/stats')\n        if (response.code === 200) {\n          this.totalCount = response.data.total || 0\n          this.unreadCount = response.data.unread || 0\n        }\n      } catch (error) {\n        console.error('加载统计失败:', error)\n      }\n    },\n\n    async markAsRead(notification) {\n      try {\n        const response = await this.postRequest('/dashboard/markNotificationRead', {\n          id: notification.id\n        })\n        if (response.code === 200) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n          this.$message.success('标记成功')\n        }\n      } catch (error) {\n        console.error('标记失败:', error)\n        this.$message.error('操作失败')\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await this.$confirm('确定要将所有未读通知标记为已读吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.postRequest('/notification/markAllRead')\n        if (response.code === 200) {\n          this.notificationList.forEach(item => item.read = true)\n          this.unreadCount = 0\n          this.$message.success('操作成功')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('操作失败:', error)\n          this.$message.error('操作失败')\n        }\n      }\n    },\n\n    async deleteNotification(notification) {\n      try {\n        await this.$confirm('确定要删除这条通知吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.deleteRequest('/notification/delete', { id: notification.id })\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async publishNotification() {\n      try {\n        await this.$refs.notificationForm.validate()\n        \n        const response = await this.postRequest('/notification/create', this.newNotification)\n        if (response.code === 200) {\n          this.$message.success('发布成功')\n          this.showAddDialog = false\n          this.resetForm()\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        console.error('发布失败:', error)\n        this.$message.error('发布失败')\n      }\n    },\n\n    resetForm() {\n      this.newNotification = {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      }\n      this.$refs.notificationForm && this.$refs.notificationForm.resetFields()\n    },\n\n    resetFilter() {\n      this.filterForm = {\n        is_read: '',\n        type: '',\n        level: ''\n      }\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleSizeChange(size) {\n      this.pagination.size = size\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleCurrentChange(page) {\n      this.pagination.page = page\n      this.loadNotifications()\n    },\n\n    getLevelType(level) {\n      const map = {\n        info: 'info',\n        warning: 'warning',\n        error: 'danger',\n        success: 'success'\n      }\n      return map[level] || 'info'\n    },\n\n    getLevelText(level) {\n      const map = {\n        info: '信息',\n        warning: '警告',\n        error: '错误',\n        success: '成功'\n      }\n      return map[level] || '信息'\n    },\n\n    getTypeColor(type) {\n      const map = {\n        system: '',\n        update: 'success',\n        backup: 'info',\n        warning: 'warning'\n      }\n      return map[type] || ''\n    },\n\n    getTypeText(type) {\n      const map = {\n        system: '系统通知',\n        update: '更新通知',\n        backup: '备份通知',\n        warning: '警告通知'\n      }\n      return map[type] || '系统通知'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notification-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.stats-row {\n  margin-bottom: 20px;\n}\n\n.stat-card {\n  padding: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.stat-number.unread {\n  color: #f56c6c;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.stat-icon {\n  font-size: 40px;\n  color: #c0c4cc;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form {\n  margin-bottom: 0;\n}\n\n.list-card {\n  margin-bottom: 20px;\n}\n\n.notification-list {\n  min-height: 400px;\n}\n\n.notification-item {\n  border: 1px solid #ebeef5;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 12px;\n  transition: all 0.3s;\n}\n\n.notification-item:hover {\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.notification-item.unread {\n  border-left: 4px solid #409eff;\n  background-color: #f0f9ff;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.level-tag, .type-tag {\n  margin-right: 8px;\n}\n\n.notification-time {\n  color: #909399;\n  font-size: 12px;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.notification-title {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.notification-desc {\n  margin: 0;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 0;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}