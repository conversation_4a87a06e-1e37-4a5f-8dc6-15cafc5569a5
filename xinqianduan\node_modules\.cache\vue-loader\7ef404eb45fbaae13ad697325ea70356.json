{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue?vue&type=style&index=0&id=794dfcc4&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue", "mtime": 1748428274747}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudGVzdC1wYWdlIHsNCiAgcGFkZGluZzogMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0K"}, {"version": 3, "sources": ["test.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA", "file": "test.vue", "sourceRoot": "src/views/pages/archive", "sourcesContent": ["<template>\r\n  <div class=\"test-page\">\r\n    <h1>归档管理测试页面</h1>\r\n    <p>如果你能看到这个页面，说明归档管理路由配置正确。</p>\r\n    <el-button type=\"primary\" @click=\"goToFileArchive\">前往文件归档</el-button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ArchiveTest',\r\n  methods: {\r\n    goToFileArchive() {\r\n      this.$router.push('/archive/file')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.test-page {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n</style> "]}]}