{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue?vue&type=style&index=0&id=d9d4a85a&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue", "mtime": 1748439252432}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dingzhi.vue"], "names": [], "mappings": ";AAwlCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "dingzhi.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-custom-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">{{ this.$router.currentRoute.name }}</h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">处理状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"150\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"200\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.is_deal == 2 ? 'success' : scope.row.is_deal == 1 ? 'warning' : 'info'\"\r\n              size=\"small\"\r\n            >\r\n              {{ scope.row.is_deal == 2 ? '已处理' : scope.row.is_deal == 1 ? '处理中' : '待处理' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"160\">\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- AI生成按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.type === '合同定制'\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"generateAIContract(scope.row)\"\r\n                icon=\"el-icon-magic-stick\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                AI生成\r\n              </el-button>\r\n\r\n              <!-- 完成制作按钮 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-check\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                完成制作\r\n              </el-button>\r\n\r\n              <!-- 提交审核按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.is_deal === 2\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitForReview(scope.row)\"\r\n                icon=\"el-icon-upload\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                提交审核\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-close\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同要求\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- AI生成合同对话框 -->\r\n    <el-dialog\r\n      title=\"AI生成合同\"\r\n      :visible.sync=\"dialogAIGenerate\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"ai-generate-dialog\"\r\n    >\r\n      <div class=\"ai-generate-content\">\r\n        <!-- 工单信息展示 -->\r\n        <div class=\"order-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            工单信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>工单标题：</label>\r\n              <span>{{ currentOrder.title }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>工单内容：</label>\r\n              <span>{{ currentOrder.desc }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>合同类型：</label>\r\n              <span>{{ matchedContractType.title || '未匹配到合同类型' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            用户信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>用户姓名：</label>\r\n              <span>{{ currentUserInfo.nickname }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>联系电话：</label>\r\n              <span>{{ currentUserInfo.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>身份证号：</label>\r\n              <span>{{ currentUserInfo.id_card || '未填写' }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>地址：</label>\r\n              <span>{{ currentUserInfo.address || '未填写' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息展示 -->\r\n        <div class=\"template-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document-copy\"></i>\r\n            合同模板\r\n          </h3>\r\n          <div v-if=\"matchedContractType.template_file\" class=\"template-info\">\r\n            <div class=\"template-file\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ matchedContractType.template_name }}</span>\r\n              <el-tag type=\"success\" size=\"mini\">已找到模板</el-tag>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"no-template\">\r\n            <el-alert\r\n              title=\"未找到对应的合同模板\"\r\n              type=\"warning\"\r\n              description=\"请先在合同类型管理中为该类型上传模板文件\"\r\n              show-icon\r\n              :closable=\"false\">\r\n            </el-alert>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- AI生成进度 -->\r\n        <div v-if=\"aiGenerating\" class=\"ai-progress-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            AI生成中...\r\n          </h3>\r\n          <el-progress\r\n            :percentage=\"aiProgress\"\r\n            :status=\"aiProgress === 100 ? 'success' : null\"\r\n            :stroke-width=\"8\"\r\n          >\r\n          </el-progress>\r\n          <p class=\"progress-text\">{{ aiProgressText }}</p>\r\n        </div>\r\n\r\n        <!-- 生成结果 -->\r\n        <div v-if=\"generatedContract\" class=\"result-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-check\"></i>\r\n            生成结果\r\n          </h3>\r\n          <div class=\"contract-preview\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"generatedContract\"\r\n              :rows=\"15\"\r\n              placeholder=\"AI生成的合同内容将显示在这里...\"\r\n              class=\"contract-content\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogAIGenerate = false\">取消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"startAIGeneration\"\r\n          :loading=\"aiGenerating\"\r\n          :disabled=\"!matchedContractType.template_file\"\r\n        >\r\n          {{ aiGenerating ? 'AI生成中...' : '开始AI生成' }}\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"generatedContract\"\r\n          type=\"success\"\r\n          @click=\"saveGeneratedContract\"\r\n        >\r\n          保存合同\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/dingzhi/\",\r\n      title: \"合同定制\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogAIGenerate: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      // AI生成相关数据\r\n      currentOrder: {},\r\n      currentUserInfo: {},\r\n      matchedContractType: {},\r\n      contractTypes: [], // 合同类型列表\r\n      aiGenerating: false,\r\n      aiProgress: 0,\r\n      aiProgressText: '',\r\n      generatedContract: '',\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      console.log('viewUserData 被调用，传入的 ID:', id);\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n        console.log('设置 currentId 为:', this.currentId);\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n      console.log('打开用户详情抽屉');\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      console.log('getInfo 被调用，ID:', id);\r\n\r\n      // 使用测试数据，因为API可能不可用\r\n      setTimeout(() => {\r\n        // 从列表数据中找到对应的项目\r\n        const item = _this.list.find(item => item.id === id);\r\n        if (item) {\r\n          _this.ruleForm = {\r\n            id: item.id,\r\n            title: item.title,\r\n            desc: item.desc,\r\n            is_deal: item.is_deal,\r\n            type: item.type === \"合同定制\" ? 1 : 2,\r\n            content: \"\",\r\n            file_path: \"\"\r\n          };\r\n          console.log('设置表单数据:', _this.ruleForm);\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: \"未找到对应的数据\",\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n      // 原始API调用（注释掉）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"WD202403001\",\r\n            type: \"合同定制\",\r\n            title: \"劳动合同定制\",\r\n            desc: \"需要定制一份标准的劳动合同模板，包含薪资、工作时间、福利待遇等条款\",\r\n            is_deal: 0,\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"WD202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"请帮忙审核房屋租赁合同，检查条款是否合理，有无法律风险\",\r\n            is_deal: 1,\r\n            nickname: \"李四\",\r\n            phone: \"13800138002\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"WD202403003\",\r\n            type: \"合同定制\",\r\n            title: \"买卖合同定制\",\r\n            desc: \"需要定制商品买卖合同，涉及货物交付、付款方式、违约责任等\",\r\n            is_deal: 2,\r\n            nickname: \"王五\",\r\n            phone: \"13800138003\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"WD202403004\",\r\n            type: \"法律咨询\",\r\n            title: \"服务合同咨询\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            is_deal: 1,\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"WD202403005\",\r\n            type: \"合同定制\",\r\n            title: \"借款合同定制\",\r\n            desc: \"需要定制个人借款合同，明确借款金额、利率、还款方式等条款\",\r\n            is_deal: 0,\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成合同\r\n    generateAIContract(orderData) {\r\n      console.log('开始AI生成合同，工单数据:', orderData);\r\n\r\n      // 设置当前工单数据\r\n      this.currentOrder = orderData;\r\n\r\n      // 根据工单标题匹配合同类型\r\n      this.matchContractType(orderData.title);\r\n\r\n      // 获取用户详情信息\r\n      this.getUserInfo(orderData.uid);\r\n\r\n      // 重置AI生成状态\r\n      this.aiGenerating = false;\r\n      this.aiProgress = 0;\r\n      this.aiProgressText = '';\r\n      this.generatedContract = '';\r\n\r\n      // 打开AI生成对话框\r\n      this.dialogAIGenerate = true;\r\n    },\r\n\r\n    // 匹配合同类型\r\n    matchContractType(orderTitle) {\r\n      // 根据工单标题关键词匹配合同类型\r\n      const keywords = {\r\n        '劳动': '劳动合同',\r\n        '租赁': '租赁合同',\r\n        '买卖': '买卖合同',\r\n        '服务': '服务合同',\r\n        '借款': '借款合同'\r\n      };\r\n\r\n      let matchedType = null;\r\n      for (let keyword in keywords) {\r\n        if (orderTitle.includes(keyword)) {\r\n          matchedType = this.contractTypes.find(type => type.title === keywords[keyword]);\r\n          break;\r\n        }\r\n      }\r\n\r\n      this.matchedContractType = matchedType || {};\r\n      console.log('匹配到的合同类型:', this.matchedContractType);\r\n    },\r\n\r\n    // 获取用户信息\r\n    getUserInfo(uid) {\r\n      // 模拟获取用户详情信息\r\n      const userInfoMap = {\r\n        1: {\r\n          nickname: \"张三\",\r\n          phone: \"13800138001\",\r\n          id_card: \"110101199001011234\",\r\n          address: \"北京市朝阳区某某街道123号\"\r\n        },\r\n        2: {\r\n          nickname: \"李四\",\r\n          phone: \"13800138002\",\r\n          id_card: \"110101199002022345\",\r\n          address: \"上海市浦东新区某某路456号\"\r\n        },\r\n        3: {\r\n          nickname: \"王五\",\r\n          phone: \"13800138003\",\r\n          id_card: \"110101199003033456\",\r\n          address: \"广州市天河区某某大道789号\"\r\n        },\r\n        4: {\r\n          nickname: \"赵六\",\r\n          phone: \"13800138004\",\r\n          id_card: \"110101199004044567\",\r\n          address: \"深圳市南山区某某街101号\"\r\n        },\r\n        5: {\r\n          nickname: \"孙七\",\r\n          phone: \"13800138005\",\r\n          id_card: \"110101199005055678\",\r\n          address: \"杭州市西湖区某某路202号\"\r\n        }\r\n      };\r\n\r\n      this.currentUserInfo = userInfoMap[uid] || {\r\n        nickname: \"未知用户\",\r\n        phone: \"\",\r\n        id_card: \"\",\r\n        address: \"\"\r\n      };\r\n\r\n      console.log('获取到的用户信息:', this.currentUserInfo);\r\n    },\r\n\r\n    // 开始AI生成\r\n    startAIGeneration() {\r\n      if (!this.matchedContractType.template_file) {\r\n        this.$message.error('未找到对应的合同模板，无法进行AI生成');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n      this.aiProgress = 0;\r\n      this.generatedContract = '';\r\n\r\n      // 模拟AI生成过程\r\n      this.simulateAIGeneration();\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    simulateAIGeneration() {\r\n      const steps = [\r\n        { progress: 20, text: '正在分析工单内容...' },\r\n        { progress: 40, text: '正在解析合同模板...' },\r\n        { progress: 60, text: '正在整合用户信息...' },\r\n        { progress: 80, text: '正在生成合同条款...' },\r\n        { progress: 100, text: 'AI生成完成！' }\r\n      ];\r\n\r\n      let currentStep = 0;\r\n\r\n      const updateProgress = () => {\r\n        if (currentStep < steps.length) {\r\n          this.aiProgress = steps[currentStep].progress;\r\n          this.aiProgressText = steps[currentStep].text;\r\n          currentStep++;\r\n\r\n          setTimeout(updateProgress, 1000);\r\n        } else {\r\n          // 生成完成，显示模拟的合同内容\r\n          this.generateContractContent();\r\n          this.aiGenerating = false;\r\n        }\r\n      };\r\n\r\n      updateProgress();\r\n    },\r\n\r\n    // 生成合同内容\r\n    generateContractContent() {\r\n      const contractTemplate = `${this.matchedContractType.title}\r\n\r\n甲方（委托方）：${this.currentUserInfo.nickname}\r\n身份证号：${this.currentUserInfo.id_card}\r\n联系电话：${this.currentUserInfo.phone}\r\n地址：${this.currentUserInfo.address}\r\n\r\n乙方（受托方）：[待填写]\r\n\r\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就以下事项达成一致，签订本合同：\r\n\r\n一、合同内容\r\n${this.currentOrder.desc}\r\n\r\n二、合同条款\r\n[根据AI分析生成的具体条款内容]\r\n\r\n1. 权利义务\r\n   甲方权利：[根据合同类型和用户需求生成]\r\n   甲方义务：[根据合同类型和用户需求生成]\r\n   乙方权利：[根据合同类型和用户需求生成]\r\n   乙方义务：[根据合同类型和用户需求生成]\r\n\r\n2. 履行期限\r\n   [根据工单内容分析生成具体期限]\r\n\r\n3. 违约责任\r\n   [根据合同类型生成标准违约条款]\r\n\r\n4. 争议解决\r\n   因履行本合同发生的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉。\r\n\r\n5. 其他约定\r\n   [根据具体需求生成其他条款]\r\n\r\n三、合同生效\r\n本合同自双方签字（盖章）之日起生效。\r\n\r\n甲方签字：_________________ 日期：_________________\r\n\r\n乙方签字：_________________ 日期：_________________\r\n\r\n---\r\n本合同由AI智能生成，请仔细核对内容后使用。\r\n生成时间：${new Date().toLocaleString()}\r\n工单号：${this.currentOrder.order_sn}`;\r\n\r\n      this.generatedContract = contractTemplate;\r\n      this.$message.success('AI合同生成完成！');\r\n    },\r\n\r\n    // 保存生成的合同\r\n    saveGeneratedContract() {\r\n      if (!this.generatedContract) {\r\n        this.$message.warning('没有可保存的合同内容');\r\n        return;\r\n      }\r\n\r\n      // 这里可以调用API保存合同内容\r\n      // 模拟保存过程\r\n      this.$message.success('合同保存成功！');\r\n\r\n      // 更新工单状态为处理中\r\n      const orderIndex = this.list.findIndex(item => item.id === this.currentOrder.id);\r\n      if (orderIndex !== -1) {\r\n        this.list[orderIndex].is_deal = 1; // 设置为处理中\r\n      }\r\n\r\n      // 关闭对话框\r\n      this.dialogAIGenerate = false;\r\n    },\r\n\r\n    // 提交审核\r\n    submitForReview(row) {\r\n      console.log('提交审核:', row);\r\n\r\n      this.$confirm('确认将此合同提交审核？提交后将进入审核流程。', '提示', {\r\n        confirmButtonText: '确定提交',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交审核过程\r\n        setTimeout(() => {\r\n          // 更新工单状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            // 添加审核相关字段\r\n            this.list[index] = {\r\n              ...this.list[index],\r\n              review_status: 'submitted', // 已提交审核\r\n              submit_time: new Date().toLocaleString(),\r\n              review_step: 'mediator_review' // 下一步：调解员审核\r\n            };\r\n          }\r\n\r\n          this.$message.success('合同已成功提交审核！将进入审核流程。');\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-custom-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 抽屉动画优化 */\r\n.user-detail-drawer >>> .el-drawer__container {\r\n  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\r\n}\r\n\r\n/* 操作按钮纵向排列 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  width: 80px;\r\n  margin: 0 !important;\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-custom-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* AI生成对话框样式 */\r\n.ai-generate-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.ai-generate-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.order-info-section,\r\n.user-info-section,\r\n.template-info-section,\r\n.ai-progress-section,\r\n.result-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.template-info {\r\n  padding: 16px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.template-file {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.template-file i {\r\n  color: #67c23a;\r\n  font-size: 20px;\r\n}\r\n\r\n.template-file span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.no-template {\r\n  padding: 16px;\r\n}\r\n\r\n.ai-progress-section {\r\n  border-left-color: #e6a23c;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  margin-top: 12px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-section {\r\n  border-left-color: #67c23a;\r\n}\r\n\r\n.contract-preview {\r\n  background: white;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.contract-content >>> .el-textarea__inner {\r\n  font-family: 'Courier New', monospace;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .ai-generate-content {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .info-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n"]}]}