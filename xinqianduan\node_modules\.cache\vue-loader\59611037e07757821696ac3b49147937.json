{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue?vue&type=style&index=0&id=1b775610&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue", "mtime": 1748469881447}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lvshi.vue"], "names": [], "mappings": ";AAgo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file": "lvshi.vue", "sourceRoot": "src/views/pages/lvshi", "sourcesContent": ["<template>\r\n  <div class=\"lawyer-management\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <div class=\"page-title\">\r\n            <i class=\"el-icon-user-solid\"></i>\r\n            <span>律师管理</span>\r\n          </div>\r\n          <div class=\"page-subtitle\">管理系统中的律师信息和专业资质</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增律师\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            circle\r\n            class=\"refresh-btn\"\r\n          ></el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div class=\"stats-cards\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon lawyer-icon\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ total }}</div>\r\n          <div class=\"stat-label\">律师总数</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon active-icon\">\r\n          <i class=\"el-icon-check\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ activeCount }}</div>\r\n          <div class=\"stat-label\">在职律师</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon specialty-icon\">\r\n          <i class=\"el-icon-medal\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ specialtyCount }}</div>\r\n          <div class=\"stat-label\">专业领域</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon firm-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ firmCount }}</div>\r\n          <div class=\"stat-label\">合作律所</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <el-card shadow=\"never\" class=\"content-card\">\r\n        <!-- 搜索和筛选区域 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-left\">\r\n            <div class=\"search-input-group\">\r\n              <el-input\r\n                placeholder=\"搜索律师姓名、律所、证号...\"\r\n                v-model=\"search.keyword\"\r\n                class=\"search-input\"\r\n                clearable\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  icon=\"el-icon-search\"\r\n                  @click=\"searchData()\"\r\n                ></el-button>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-select\r\n              v-model=\"search.specialty\"\r\n              placeholder=\"专业领域\"\r\n              clearable\r\n              class=\"filter-select\"\r\n              @change=\"searchData\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in zhuanyes\"\r\n                :key=\"item.id\"\r\n                :label=\"item.title\"\r\n                :value=\"item.id\"\r\n              ></el-option>\r\n            </el-select>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"resetSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视图切换 -->\r\n        <div class=\"view-controls\">\r\n          <div class=\"view-tabs\">\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'table' }\"\r\n              @click=\"switchView('table')\"\r\n            >\r\n              <i class=\"el-icon-menu\"></i>\r\n              <span>列表视图</span>\r\n            </div>\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'card' }\"\r\n              @click=\"switchView('card')\"\r\n            >\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              <span>卡片视图</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"view-actions\">\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-download\"\r\n              size=\"small\"\r\n              @click=\"exportData\"\r\n            >\r\n              导出\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <!-- 列表视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"lawyer-table\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n\r\n            <el-table-column label=\"律师信息\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"lawyer-info-cell\">\r\n                  <div class=\"lawyer-avatar\">\r\n                    <el-avatar\r\n                      :src=\"scope.row.pic_path\"\r\n                      :size=\"50\"\r\n                      @click.native=\"showImage(scope.row.pic_path)\"\r\n                      class=\"clickable-avatar\"\r\n                    >\r\n                      <i class=\"el-icon-user-solid\"></i>\r\n                    </el-avatar>\r\n                  </div>\r\n                  <div class=\"lawyer-details\">\r\n                    <div class=\"lawyer-name\">{{ scope.row.title }}</div>\r\n                    <div class=\"lawyer-card\">证号：{{ scope.row.laywer_card || '暂无' }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"律所\" prop=\"lvsuo\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"firm-info\">\r\n                  <i class=\"el-icon-office-building\"></i>\r\n                  <span>{{ scope.row.lvsuo || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"专业领域\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"specialties\">\r\n                  <el-tag\r\n                    v-for=\"specialty in getSpecialtyNames(scope.row.zhuanyes)\"\r\n                    :key=\"specialty\"\r\n                    size=\"mini\"\r\n                    class=\"specialty-tag\"\r\n                  >\r\n                    {{ specialty }}\r\n                  </el-tag>\r\n                  <span v-if=\"!scope.row.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"联系方式\" prop=\"phone\" min-width=\"130\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"contact-info\">\r\n                  <i class=\"el-icon-phone\"></i>\r\n                  <span>{{ scope.row.phone || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"证书\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.card_path\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"showImage(scope.row.card_path)\"\r\n                  class=\"view-cert-btn\"\r\n                >\r\n                  查看\r\n                </el-button>\r\n                <span v-else class=\"no-data\">暂无</span>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"注册时间\" prop=\"create_time\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-info\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(scope.row.create_time) }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    circle\r\n                    title=\"编辑\"\r\n                  ></el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    circle\r\n                    title=\"删除\"\r\n                  ></el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-else class=\"card-view\">\r\n          <div class=\"lawyer-cards\" v-loading=\"loading\">\r\n            <div\r\n              v-for=\"lawyer in list\"\r\n              :key=\"lawyer.id\"\r\n              class=\"lawyer-card\"\r\n            >\r\n              <div class=\"card-header\">\r\n                <div class=\"lawyer-avatar-large\">\r\n                  <el-avatar\r\n                    :src=\"lawyer.pic_path\"\r\n                    :size=\"80\"\r\n                    @click.native=\"showImage(lawyer.pic_path)\"\r\n                    class=\"clickable-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"lawyer-basic-info\">\r\n                  <div class=\"lawyer-name-large\">{{ lawyer.title }}</div>\r\n                  <div class=\"lawyer-firm\">\r\n                    <i class=\"el-icon-office-building\"></i>\r\n                    <span>{{ lawyer.lvsuo || '暂无律所' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-phone\"></i>\r\n                    联系方式\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.phone || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    证件号码\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.laywer_card || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-collection-tag\"></i>\r\n                    专业领域\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-tag\r\n                      v-for=\"specialty in getSpecialtyNames(lawyer.zhuanyes)\"\r\n                      :key=\"specialty\"\r\n                      size=\"mini\"\r\n                      class=\"specialty-tag\"\r\n                    >\r\n                      {{ specialty }}\r\n                    </el-tag>\r\n                    <span v-if=\"!lawyer.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    执业证书\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-button\r\n                      v-if=\"lawyer.card_path\"\r\n                      type=\"text\"\r\n                      size=\"mini\"\r\n                      @click=\"showImage(lawyer.card_path)\"\r\n                      class=\"view-cert-btn\"\r\n                    >\r\n                      查看证书\r\n                    </el-button>\r\n                    <span v-else class=\"no-data\">暂无证书</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-footer\">\r\n                <div class=\"register-time\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(lawyer.create_time) }}</span>\r\n                </div>\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(lawyer.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(lawyer), lawyer.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '姓名'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"绑定员工\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"yuangong_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option-group\r\n              v-for=\"group in yuangongs\"\r\n              :key=\"group.label\"\r\n              :label=\"group.label\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in group.options\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-option-group>\r\n          </el-select>\r\n          <!-- <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in yuangongs\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select> -->\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"专业\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhuanyes\"\r\n        >\r\n          <el-select v-model=\"ruleForm.zhuanyes\" multiple placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in zhuanyes\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"律所\" :label-width=\"formLabelWidth\" prop=\"lvsuo\">\r\n          <el-input v-model=\"ruleForm.lvsuo\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"职业年薪\" :label-width=\"formLabelWidth\" prop=\"age\">\r\n          <el-input\r\n            v-model=\"ruleForm.age\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"联系方式\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证件号\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"laywer_card\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.laywer_card\"\r\n            autocomplete=\"off\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          >\r\n            <template slot=\"append\">330rpx*300rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证书\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"card_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.card_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('card_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"showImage(ruleForm.card_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"delImage(ruleForm.card_path, 'card_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12, // 改为12，适合卡片视图\r\n      search: {\r\n        keyword: \"\",\r\n        specialty: \"\", // 新增专业筛选\r\n      },\r\n      loading: true,\r\n      zhuanyes: [],\r\n      url: \"/lvshi/\",\r\n      title: \"律师\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table 或 card\r\n      selectedLawyers: [], // 选中的律师\r\n      originalList: [], // 原始数据，用于搜索\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        yuangong_id: [\r\n          {\r\n            required: true,\r\n            message: \"请绑定员工\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhuanyes: [\r\n          {\r\n            required: true,\r\n            message: \"请选择专业\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lvsuo: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律所\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        age: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职业年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        laywer_card: [\r\n          {\r\n            required: true,\r\n            message: \"请填写证件号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师联系方式\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        card_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传证书\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      field: \"\",\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 在职律师数量\r\n    activeCount() {\r\n      return this.list.filter(lawyer => lawyer.status === 1).length;\r\n    },\r\n    // 专业领域数量\r\n    specialtyCount() {\r\n      const specialties = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.zhuanyes && Array.isArray(lawyer.zhuanyes)) {\r\n          lawyer.zhuanyes.forEach(specialty => specialties.add(specialty));\r\n        }\r\n      });\r\n      return specialties.size;\r\n    },\r\n    // 合作律所数量\r\n    firmCount() {\r\n      const firms = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.lvsuo) {\r\n          firms.add(lawyer.lvsuo);\r\n        }\r\n      });\r\n      return firms.size;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTestData(); // 先加载测试数据\r\n    this.getZhuanyes(); // 加载专业数据用于筛选\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 切换视图模式\r\n    switchView(mode) {\r\n      this.viewMode = mode;\r\n      this.$message.success(`已切换到${mode === 'table' ? '列表' : '卡片'}视图`);\r\n    },\r\n\r\n    // 获取专业名称\r\n    getSpecialtyNames(specialtyIds) {\r\n      if (!specialtyIds || !Array.isArray(specialtyIds)) return [];\r\n      if (!this.zhuanyes || !Array.isArray(this.zhuanyes)) return [];\r\n      return specialtyIds.map(id => {\r\n        const specialty = this.zhuanyes.find(z => z.id === id);\r\n        return specialty ? specialty.title : '未知专业';\r\n      });\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '暂无';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('zh-CN');\r\n    },\r\n\r\n    // 重置搜索\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        specialty: \"\"\r\n      };\r\n      // 重新加载完整的测试数据\r\n      this.loadTestData();\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      if (this.selectedLawyers.length > 0) {\r\n        this.$message.success(`导出选中的 ${this.selectedLawyers.length} 条律师数据`);\r\n      } else {\r\n        this.$message.success('导出全部律师数据');\r\n      }\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedLawyers = selection;\r\n    },\r\n\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 测试专业数据\r\n      this.zhuanyes = [\r\n        { id: 1, title: '民事诉讼' },\r\n        { id: 2, title: '刑事辩护' },\r\n        { id: 3, title: '商事仲裁' },\r\n        { id: 4, title: '知识产权' },\r\n        { id: 5, title: '劳动争议' },\r\n        { id: 6, title: '房产纠纷' },\r\n        { id: 7, title: '合同纠纷' },\r\n        { id: 8, title: '公司法务' }\r\n      ];\r\n\r\n      // 测试律师数据\r\n      this.list = [\r\n        {\r\n          id: 1,\r\n          title: '张明华',\r\n          lvsuo: '北京德恒律师事务所',\r\n          zhuanyes: [1, 3, 7],\r\n          phone: '13800138001',\r\n          laywer_card: '*********',\r\n          age: 8,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-03-15 09:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '李晓雯',\r\n          lvsuo: '上海锦天城律师事务所',\r\n          zhuanyes: [2, 4],\r\n          phone: '13800138002',\r\n          laywer_card: 'A20210002',\r\n          age: 12,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-08-22 14:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '王建国',\r\n          lvsuo: '广东广和律师事务所',\r\n          zhuanyes: [5, 6, 8],\r\n          phone: '13800138003',\r\n          laywer_card: 'A20210003',\r\n          age: 15,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-12-10 11:45:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '陈美玲',\r\n          lvsuo: '深圳君合律师事务所',\r\n          zhuanyes: [1, 4, 7],\r\n          phone: '13800138004',\r\n          laywer_card: 'A20210004',\r\n          age: 6,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-01-18 16:10:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '刘志强',\r\n          lvsuo: '北京金杜律师事务所',\r\n          zhuanyes: [2, 3, 8],\r\n          phone: '13800138005',\r\n          laywer_card: 'A20210005',\r\n          age: 20,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2018-05-30 10:25:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '赵雅琴',\r\n          lvsuo: '上海方达律师事务所',\r\n          zhuanyes: [4, 5, 6],\r\n          phone: '13800138006',\r\n          laywer_card: 'A20210006',\r\n          age: 9,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-07-12 13:55:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 7,\r\n          title: '孙文博',\r\n          lvsuo: '广州广信君达律师事务所',\r\n          zhuanyes: [1, 2, 7],\r\n          phone: '13800138007',\r\n          laywer_card: 'A20210007',\r\n          age: 11,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-11-08 15:40:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 8,\r\n          title: '周慧敏',\r\n          lvsuo: '深圳市律师协会',\r\n          zhuanyes: [3, 6, 8],\r\n          phone: '13800138008',\r\n          laywer_card: 'A20210008',\r\n          age: 7,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-09-25 12:15:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 9,\r\n          title: '吴国强',\r\n          lvsuo: '北京市中伦律师事务所',\r\n          zhuanyes: [1, 5, 7],\r\n          phone: '13800138009',\r\n          laywer_card: 'A20210009',\r\n          age: 13,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-04-14 09:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 10,\r\n          title: '郑小红',\r\n          lvsuo: '上海市汇业律师事务所',\r\n          zhuanyes: [2, 4, 6],\r\n          phone: '13800138010',\r\n          laywer_card: 'A20210010',\r\n          age: 5,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-06-03 14:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 11,\r\n          title: '马云飞',\r\n          lvsuo: '广东信达律师事务所',\r\n          zhuanyes: [3, 7, 8],\r\n          phone: '13800138011',\r\n          laywer_card: 'A20210011',\r\n          age: 16,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-02-28 11:05:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 12,\r\n          title: '林静怡',\r\n          lvsuo: '深圳市盈科律师事务所',\r\n          zhuanyes: [1, 4, 5],\r\n          phone: '13800138012',\r\n          laywer_card: 'A20210012',\r\n          age: 10,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-01-20 16:45:00',\r\n          status: 1\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据\r\n      this.originalList = [...this.list];\r\n      // 设置总数\r\n      this.total = this.list.length;\r\n      this.loading = false;\r\n    },\r\n\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(\"/yuangong/getMoreList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getZhuanyes() {\r\n      let _this = this;\r\n      _this.getRequest(\"/zhuanye/getList\").then((resp) => {\r\n        if (resp) {\r\n          _this.zhuanyes = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          phone: \"\",\r\n          address: \"\",\r\n          pic_path: \"\",\r\n          card_path: \"\",\r\n          zhuanyes: \"\",\r\n          age: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getZhuanyes();\r\n      _this.getLvshi();\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n\r\n      // 如果有原始测试数据，在本地进行搜索\r\n      if (this.originalList.length > 0) {\r\n        this.filterTestData();\r\n      } else {\r\n        this.size = 20;\r\n        this.getData();\r\n      }\r\n    },\r\n\r\n    // 在测试数据中进行筛选\r\n    filterTestData() {\r\n      let filteredList = [...this.originalList];\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.title.toLowerCase().includes(keyword) ||\r\n          lawyer.lvsuo.toLowerCase().includes(keyword) ||\r\n          lawyer.laywer_card.toLowerCase().includes(keyword) ||\r\n          lawyer.phone.includes(keyword)\r\n        );\r\n      }\r\n\r\n      // 专业筛选\r\n      if (this.search.specialty) {\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.zhuanyes && lawyer.zhuanyes.includes(this.search.specialty)\r\n        );\r\n      }\r\n\r\n      // 这里可以添加分页逻辑，但为了演示简单，直接显示所有结果\r\n      this.total = filteredList.length;\r\n\r\n      // 模拟搜索延迟\r\n      this.loading = true;\r\n      setTimeout(() => {\r\n        this.list = filteredList;\r\n        this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      // 如果已经有测试数据，直接使用\r\n      if (_this.list.length > 0) {\r\n        return;\r\n      }\r\n\r\n      _this.loading = true;\r\n\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          } else {\r\n            // 如果接口失败，使用测试数据\r\n            console.log('使用测试数据');\r\n            _this.loadTestData();\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 接口错误时也使用测试数据\r\n          console.log('接口错误，使用测试数据');\r\n          _this.loadTestData();\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm[this.field] = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 主容器 */\r\n.lawyer-management {\r\n  padding: 0;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.add-btn {\r\n  padding: 12px 24px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  padding: 0 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.lawyer-icon {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.specialty-icon {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.firm-icon {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  line-height: 1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  padding: 0 24px;\r\n}\r\n\r\n.content-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n}\r\n\r\n.search-input-group {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-input {\r\n  min-width: 300px;\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.filter-select {\r\n  width: 150px;\r\n}\r\n\r\n.search-btn, .reset-btn {\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.view-tabs {\r\n  display: flex;\r\n  background: #f5f7fa;\r\n  border-radius: 8px;\r\n  padding: 4px;\r\n}\r\n\r\n.view-tab {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.view-tab.active {\r\n  background: white;\r\n  color: #409eff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.view-tab:hover:not(.active) {\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格视图 */\r\n.table-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.lawyer-info-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.lawyer-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.clickable-avatar {\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.clickable-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.lawyer-details {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.lawyer-card {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.firm-info, .contact-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.specialties {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n.specialty-tag {\r\n  background: #ecf5ff;\r\n  color: #409eff;\r\n  border: 1px solid #d9ecff;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n  font-size: 12px;\r\n}\r\n\r\n.view-cert-btn {\r\n  color: #67c23a;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 6px;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 0;\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n.action-buttons .el-button.is-circle {\r\n  border-radius: 50%;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.lawyer-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.lawyer-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  display: flex;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.lawyer-avatar-large {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.lawyer-basic-info {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name-large {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.lawyer-firm {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #f5f7fa;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  text-align: right;\r\n  color: #303133;\r\n}\r\n\r\n.card-footer {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.register-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 24px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.pagination {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 12px 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-right {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .view-controls {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .stats-cards {\r\n    grid-template-columns: 1fr;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .lawyer-cards {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .card-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .info-row {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-value {\r\n    text-align: left;\r\n  }\r\n\r\n  .card-footer {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n/* 原有样式保留 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}