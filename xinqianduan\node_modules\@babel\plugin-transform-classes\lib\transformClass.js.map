{"version": 3, "names": ["_helperFunctionName", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_core", "_helperAnnotateAsPure", "_inlineCallSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "classBodyPath", "get", "isClassMethod", "kind", "params", "constructor", "template", "expression", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "isClassPrivateProperty", "buildCodeFrameError", "decorators", "isConstructor", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "returnStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "_bareSuperNode$argume", "bareSuperNodeArguments", "addCallSuperHelper", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "replaceWith", "maxGuaranteedSuperBeforeIndex", "ref", "generateDeclaredUidIdentifier", "buildAssertThisInitialized", "bareSupers", "Super", "isCallExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "Math", "min", "isLoop", "isConditional", "thisPath", "isMemberExpression", "object", "thisIndex", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "guaranteedSuperBefore<PERSON>inish", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "fn", "toExpression", "isStringLiteral", "_nameFunction", "nameFunction", "id", "descriptor", "has", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "generator", "async", "_nameFunction2", "expr", "expressionStatement", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "hasInstanceDescriptors", "hasStaticDescriptors", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { <PERSON>de<PERSON><PERSON>, Scope, Visitor, File } from \"@babel/core\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport { traverse, template, types as t } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCallSuperHelper from \"./inline-callSuper-helpers.ts\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = traverse.visitors.merge([\n    environmentVisitor,\n    {\n      ThisExpression(path) {\n        classState.superThises.push(path);\n      },\n    },\n  ]);\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is one\n   */\n  function maybeCreateConstructor() {\n    const classBodyPath = classState.path.get(\"body\");\n    for (const path of classBodyPath.get(\"body\")) {\n      if (path.isClassMethod({ kind: \"constructor\" })) return;\n    }\n\n    let params: t.FunctionExpression[\"params\"], body;\n\n    if (classState.isDerived) {\n      const constructor = template.expression.ast`\n        (function () {\n          super(...arguments);\n        })\n      ` as t.FunctionExpression;\n      params = constructor.params;\n      body = constructor.body;\n    } else {\n      params = [];\n      body = t.blockStatement([]);\n    }\n\n    classBodyPath.unshiftContainer(\n      \"body\",\n      t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n    );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty() || path.isClassPrivateProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          traverse.visitors.merge([\n            environmentVisitor,\n            {\n              ReturnStatement(path) {\n                if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                  superReturns.push(path);\n                }\n              },\n            },\n          ]),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          pushMethod(node, path);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.returnStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      const args: t.Expression[] = [\n        t.thisExpression(),\n        t.cloneNode(classState.classRef),\n      ];\n      if (bareSuperNode.arguments?.length) {\n        const bareSuperNodeArguments = bareSuperNode.arguments as (\n          | t.Expression\n          | t.SpreadElement\n        )[];\n\n        /**\n         * test262/test/language/expressions/super/call-spread-err-sngl-err-itr-get-get.js\n         *\n         * var iter = {};\n         * Object.defineProperty(iter, Symbol.iterator, {\n         *   get: function() {\n         *     throw new Test262Error();\n         *   }\n         * })\n         * super(...iter);\n         */\n\n        if (\n          bareSuperNodeArguments.length === 1 &&\n          t.isSpreadElement(bareSuperNodeArguments[0]) &&\n          t.isIdentifier(bareSuperNodeArguments[0].argument, {\n            name: \"arguments\",\n          })\n        ) {\n          args.push(bareSuperNodeArguments[0].argument);\n        } else {\n          args.push(t.arrayExpression(bareSuperNodeArguments));\n        }\n      }\n      call = t.callExpression(addCallSuperHelper(classState.file), args);\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    const constructorBody = path.get(\"body\");\n\n    let maxGuaranteedSuperBeforeIndex = constructorBody.node.body.length;\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      maxGuaranteedSuperBeforeIndex++;\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    const buildAssertThisInitialized = function () {\n      return t.callExpression(\n        classState.file.addHelper(\"assertThisInitialized\"),\n        [thisRef()],\n      );\n    };\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      traverse.visitors.merge([\n        environmentVisitor,\n        {\n          Super(path) {\n            const { node, parentPath } = path;\n            if (parentPath.isCallExpression({ callee: node })) {\n              bareSupers.unshift(parentPath);\n            }\n          },\n        } as Visitor,\n      ]),\n    );\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (maxGuaranteedSuperBeforeIndex >= 0) {\n        let lastParentPath: NodePath;\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === constructorBody) {\n            maxGuaranteedSuperBeforeIndex = Math.min(\n              maxGuaranteedSuperBeforeIndex,\n              lastParentPath.key as number,\n            );\n            return true;\n          }\n\n          if (\n            parentPath.isLoop() ||\n            parentPath.isConditional() ||\n            parentPath.isArrowFunctionExpression()\n          ) {\n            maxGuaranteedSuperBeforeIndex = -1;\n            return true;\n          }\n\n          lastParentPath = parentPath;\n        });\n      }\n    }\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n\n      let thisIndex: number;\n      thisPath.find(function (parentPath) {\n        if (parentPath.parentPath === constructorBody) {\n          thisIndex = parentPath.key as number;\n          return true;\n        }\n      });\n\n      if (\n        maxGuaranteedSuperBeforeIndex !== -1 &&\n        thisIndex > maxGuaranteedSuperBeforeIndex\n      ) {\n        thisPath.replaceWith(thisRef());\n      } else {\n        thisPath.replaceWith(buildAssertThisInitialized());\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = buildAssertThisInitialized();\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    const guaranteedSuperBeforeFinish =\n      maxGuaranteedSuperBeforeIndex !== -1 &&\n      maxGuaranteedSuperBeforeIndex < bodyPaths.length;\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish\n            ? thisRef()\n            : buildAssertThisInitialized(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, path?: NodePath) {\n    const scope = path ? path.scope : classState.scope;\n\n    if (node.kind === \"method\") {\n      if (processMethod(node, scope)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n\n    let fn: t.Expression = t.toExpression(node);\n\n    if (t.isStringLiteral(key)) {\n      // infer function name\n      if (node.kind === \"method\") {\n        // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n        // does not seem to support it\n        fn =\n          nameFunction(\n            // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n            // does not seem to support it\n            { id: key, node: node, scope },\n            undefined,\n            supportUnicodeId,\n          ) ?? fn;\n      }\n    } else {\n      // todo(flow->ts) find a way to avoid \"key as t.StringLiteral\" below which relies on this assignment\n      methods.hasComputed = true;\n    }\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod, scope: Scope) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      let func: t.Expression = t.functionExpression(\n        null,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const key = t.toComputedKey(node, node.key);\n      if (t.isStringLiteral(key)) {\n        // @ts-expect-error: requires strictNullCheck\n        func =\n          nameFunction(\n            {\n              node: func,\n              id: key,\n              scope,\n            },\n            undefined,\n            supportUnicodeId,\n          ) ?? func;\n      }\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    classState.pushedInherits = true;\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = classState.classId && body.length === 0;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? classState.construct.body.directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(classState.construct);\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    if (!classState.pushedCreateClass) {\n      body.push(\n        t.returnStatement(\n          classState.isLoose\n            ? t.cloneNode(classState.classRef)\n            : createClassHelper([t.cloneNode(classState.classRef)]),\n        ),\n      );\n    }\n\n    body.unshift(classState.construct);\n\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAJ,OAAA;AAEA,IAAAK,uBAAA,GAAAL,OAAA;AAWA,SAASM,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA6De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAEP,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BQ,eAAe,EAAER,SAAS;IAC1BS,mBAAmB,EAAET,SAAS;IAC9BU,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBpB,OAAO,EAAE,KAAK;IAEdqB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC7B,UAAU,EAAE2B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAChDC,iCAAkB,EAClB;IACEC,cAAcA,CAACzC,IAAI,EAAE;MACnBM,UAAU,CAACc,WAAW,CAACsB,IAAI,CAAC1C,IAAI,CAAC;IACnC;EACF,CAAC,CACF,CAAC;EAEF,SAAS2C,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAOjD,WAAC,CAACkD,cAAc,CAACvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,MAAMC,aAAa,GAAG1C,UAAU,CAACN,IAAI,CAACiD,GAAG,CAAC,MAAM,CAAC;IACjD,KAAK,MAAMjD,IAAI,IAAIgD,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5C,IAAIjD,IAAI,CAACkD,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;IACnD;IAEA,IAAIC,MAAsC,EAAEjC,IAAI;IAEhD,IAAIb,UAAU,CAACO,SAAS,EAAE;MACxB,MAAMwC,WAAW,GAAGC,cAAQ,CAACC,UAAU,CAACC,GAAI;AAClD;AACA;AACA;AACA,OAA+B;MACzBJ,MAAM,GAAGC,WAAW,CAACD,MAAM;MAC3BjC,IAAI,GAAGkC,WAAW,CAAClC,IAAI;IACzB,CAAC,MAAM;MACLiC,MAAM,GAAG,EAAE;MACXjC,IAAI,GAAGxB,WAAC,CAAC8D,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEAT,aAAa,CAACU,gBAAgB,CAC5B,MAAM,EACN/D,WAAC,CAACgE,WAAW,CAAC,aAAa,EAAEhE,WAAC,CAACiE,UAAU,CAAC,aAAa,CAAC,EAAER,MAAM,EAAEjC,IAAI,CACxE,CAAC;EACH;EAEA,SAAS0C,SAASA,CAAA,EAAG;IACnBd,sBAAsB,CAAC,CAAC;IACxBe,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAIzD,UAAU,CAACU,eAAe,EAAE;MAC9B,MAAM;QAAExB,eAAe;QAAEwB,eAAe;QAAED;MAAU,CAAC,GAAGT,UAAU;MAElEd,eAAe,CAAC2B,IAAI,CAACuB,IAAI,CAAC,GAAG1B,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDxB,WAAC,CAACG,QAAQ,CAACiB,SAAS,EAAEC,eAAe,CAAC;MACtCrB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEwB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA6C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAG3D,UAAU,CAACN,IAAI,CAACiD,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAMjD,IAAI,IAAIiE,cAAc,EAAE;MACjC,MAAMxE,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAACkE,eAAe,CAAC,CAAC,IAAIlE,IAAI,CAACmE,sBAAsB,CAAC,CAAC,EAAE;QAC3D,MAAMnE,IAAI,CAACoE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAI3E,IAAI,CAAC4E,UAAU,EAAE;QACnB,MAAMrE,IAAI,CAACoE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAIzE,WAAC,CAACuD,aAAa,CAACzD,IAAI,CAAC,EAAE;QACzB,MAAM6E,aAAa,GAAG7E,IAAI,CAAC0D,IAAI,KAAK,aAAa;QAEjD,MAAMoB,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAEzE,IAAI;UAChB0E,SAAS,EAAEpE,UAAU,CAACf,QAAQ;UAC9BoF,QAAQ,EAAErE,UAAU,CAACK,SAAS;UAC9BiE,aAAa,EAAExE,WAAW,CAACwE,aAAa;UACxC3E,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrB4E,aAAa,EAAEvE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEFgF,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAMlE,YAA2C,GAAG,EAAE;QACtDZ,IAAI,CAACqC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;UACEuC,eAAeA,CAAC/E,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAACgF,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDrE,YAAY,CAAC8B,IAAI,CAAC1C,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACF,CACH,CAAC;QAED,IAAIsE,aAAa,EAAE;UACjBY,eAAe,CAACtE,YAAY,EAAEnB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UACLmF,UAAU,CAAC1F,IAAI,EAAEO,IAAI,CAAC;QACxB;MACF;IACF;EACF;EAEA,SAASgE,eAAeA,CAAA,EAAG;IACzBoB,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEjE;IAAK,CAAC,GAAGb,UAAU;IAE3B,MAAM+E,KAAmB,GAAG;MAC1B1D,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAMuD,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIhF,UAAU,CAACoB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAAC0D,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGhF,UAAU,CAACoB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAACC,GAAG,CAAC0D,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAG9F,WAAC,CAAC+F,gBAAgB,CAAC,CAC7B/F,WAAC,CAACgG,cAAc,CAAChG,WAAC,CAACiE,UAAU,CAAC,KAAK,CAAC,EAAE4B,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAMzC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAIqC,IAAI,CAACrC,IAAI,CAAC,IAAI,IAAI,EAAE;cACtBsC,GAAG,CAACI,UAAU,CAACnD,IAAI,CACjB/C,WAAC,CAACgG,cAAc,CAAChG,WAAC,CAACiE,UAAU,CAACT,IAAI,CAAC,EAAEqC,IAAI,CAACrC,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAOsC,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAAC1D,QAAQ,IAAI0D,KAAK,CAACtD,MAAM,EAAE;MAClC,IAAIa,IAAI,GAAG,CACTjD,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChC8F,KAAK,CAAC1D,QAAQ,GAAGhC,WAAC,CAACmG,eAAe,CAACT,KAAK,CAAC1D,QAAQ,CAAC,GAAGhC,WAAC,CAACoG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAACtD,MAAM,GAAGpC,WAAC,CAACmG,eAAe,CAACT,KAAK,CAACtD,MAAM,CAAC,GAAGpC,WAAC,CAACoG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,IAAI,CAAC2C,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAACtG,WAAC,CAACuG,aAAa,CAACtD,IAAI,CAACqD,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACArD,IAAI,GAAGA,IAAI,CAACuD,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1C7E,IAAI,CAACuB,IAAI,CAAC/C,WAAC,CAACyG,eAAe,CAACzD,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACrDtC,UAAU,CAACgB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAAS+E,aAAaA,CACpBC,SAAqC,EACrC3B,QAAsB,EACtB4B,OAA2B,EAC3BpF,IAAgC,EAChC;IACA,MAAMqF,aAAa,GAAGF,SAAS,CAAC7G,IAAI;IACpC,IAAIgH,IAAI;IAER,IAAIrG,WAAW,CAACsG,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAACjH,WAAC,CAACkH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpC5F,WAAC,CAACmH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7ChH,WAAC,CAACoH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAGvH,WAAC,CAACwH,gBAAgB,CACvCxH,WAAC,CAACE,SAAS,CAAC8E,QAAQ,CAAC,EACrBhF,WAAC,CAACiE,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACL4C,aAAa,CAACU,MAAM,GAAGvH,WAAC,CAACwH,gBAAgB,CACvCxH,WAAC,CAACE,SAAS,CAAC8E,QAAQ,CAAC,EACrBhF,WAAC,CAACiE,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEA6C,IAAI,GAAG9G,WAAC,CAACyH,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAE7G,WAAC,CAACkH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAAQ,qBAAA;MACL,MAAMzE,IAAoB,GAAG,CAC3BjD,WAAC,CAACkH,cAAc,CAAC,CAAC,EAClBlH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC;MACD,KAAA8H,qBAAA,GAAIb,aAAa,CAACG,SAAS,aAAvBU,qBAAA,CAAyB9B,MAAM,EAAE;QACnC,MAAM+B,sBAAsB,GAAGd,aAAa,CAACG,SAG1C;QAcH,IACEW,sBAAsB,CAAC/B,MAAM,KAAK,CAAC,IACnC5F,WAAC,CAACmH,eAAe,CAACQ,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAC5C3H,WAAC,CAACoH,YAAY,CAACO,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,EAAE;UACjDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACArE,IAAI,CAACF,IAAI,CAAC4E,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC;QAC/C,CAAC,MAAM;UACLpE,IAAI,CAACF,IAAI,CAAC/C,WAAC,CAACmG,eAAe,CAACwB,sBAAsB,CAAC,CAAC;QACtD;MACF;MACAb,IAAI,GAAG9G,WAAC,CAACkD,cAAc,CAAC,IAAA0E,+BAAkB,EAACjH,UAAU,CAACL,IAAI,CAAC,EAAE2C,IAAI,CAAC;IACpE;IAEA,IACE0D,SAAS,CAACkB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CnB,SAAS,CAACkB,UAAU,CAACE,SAAS,KAAKvG,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,IACjDA,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,CAACoE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACkB,UAAU,CAAC5B,GAAG,EACtD;MAIA,IAAItF,UAAU,CAACc,WAAW,CAACmE,MAAM,EAAE;QACjCkB,IAAI,GAAG9G,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACkB,UAAU,CAACI,WAAW,CAACjI,WAAC,CAACyG,eAAe,CAACK,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACsB,WAAW,CAACjI,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAAS1C,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAACzD,UAAU,CAACO,SAAS,EAAE;IAE3B,MAAMb,IAAI,GAAGM,UAAU,CAACW,mBAAmB;IAC3C,MAAME,IAAI,GAAGnB,IAAI,CAACiD,GAAG,CAAC,MAAM,CAAC;IAE7B,MAAMzD,eAAe,GAAGQ,IAAI,CAACiD,GAAG,CAAC,MAAM,CAAC;IAExC,IAAI4E,6BAA6B,GAAGrI,eAAe,CAACC,IAAI,CAAC0B,IAAI,CAACoE,MAAM;IAEpEvF,IAAI,CAACqC,QAAQ,CAACD,iBAAiB,CAAC;IAEhC,IAAImE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMuB,GAAG,GAAG9H,IAAI,CAACS,KAAK,CAACsH,6BAA6B,CAAC,MAAM,CAAC;MAC5DF,6BAA6B,EAAE;MAC/BtB,OAAO,GAAGA,CAAA,KAAM5G,WAAC,CAACE,SAAS,CAACiI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,MAAME,0BAA0B,GAAG,SAAAA,CAAA,EAAY;MAC7C,OAAOrI,WAAC,CAACkD,cAAc,CACrBvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAACyD,OAAO,CAAC,CAAC,CACZ,CAAC;IACH,CAAC;IAED,MAAM0B,UAAwC,GAAG,EAAE;IACnDjI,IAAI,CAACqC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;MACE0F,KAAKA,CAAClI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAE+H;QAAW,CAAC,GAAGxH,IAAI;QACjC,IAAIwH,UAAU,CAACW,gBAAgB,CAAC;UAAEjB,MAAM,EAAEzH;QAAK,CAAC,CAAC,EAAE;UACjDwI,UAAU,CAACrB,OAAO,CAACY,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACF,CACH,CAAC;IAED,KAAK,MAAMlB,SAAS,IAAI2B,UAAU,EAAE;MAClC5B,aAAa,CAACC,SAAS,EAAEhG,UAAU,CAACK,SAAS,EAAE4F,OAAO,EAAEpF,IAAI,CAAC;MAE7D,IAAI0G,6BAA6B,IAAI,CAAC,EAAE;QACtC,IAAIO,cAAwB;QAC5B9B,SAAS,CAAC+B,IAAI,CAAC,UAAUb,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKhI,eAAe,EAAE;YAClCqI,6BAA6B,GAAGS,IAAI,CAACC,GAAG,CACtCV,6BAA6B,EAC7BO,cAAc,CAACxC,GACjB,CAAC;YACD,OAAO,IAAI;UACb;UAEA,IACE4B,UAAU,CAACgB,MAAM,CAAC,CAAC,IACnBhB,UAAU,CAACiB,aAAa,CAAC,CAAC,IAC1BjB,UAAU,CAACvC,yBAAyB,CAAC,CAAC,EACtC;YACA4C,6BAA6B,GAAG,CAAC,CAAC;YAClC,OAAO,IAAI;UACb;UAEAO,cAAc,GAAGZ,UAAU;QAC7B,CAAC,CAAC;MACJ;IACF;IAEA,KAAK,MAAMkB,QAAQ,IAAIpI,UAAU,CAACc,WAAW,EAAE;MAC7C,MAAM;QAAE3B,IAAI;QAAE+H;MAAW,CAAC,GAAGkB,QAAQ;MACrC,IAAIlB,UAAU,CAACmB,kBAAkB,CAAC;QAAEC,MAAM,EAAEnJ;MAAK,CAAC,CAAC,EAAE;QACnDiJ,QAAQ,CAACd,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MAEA,IAAIsC,SAAiB;MACrBH,QAAQ,CAACL,IAAI,CAAC,UAAUb,UAAU,EAAE;QAClC,IAAIA,UAAU,CAACA,UAAU,KAAKhI,eAAe,EAAE;UAC7CqJ,SAAS,GAAGrB,UAAU,CAAC5B,GAAa;UACpC,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MAEF,IACEiC,6BAA6B,KAAK,CAAC,CAAC,IACpCgB,SAAS,GAAGhB,6BAA6B,EACzC;QACAa,QAAQ,CAACd,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM;QACLmC,QAAQ,CAACd,WAAW,CAACI,0BAA0B,CAAC,CAAC,CAAC;MACpD;IACF;IAEA,IAAIc,UAAU;IAEd,IAAIxI,UAAU,CAACH,OAAO,EAAE;MACtB2I,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAGhB,0BAA0B,CAAC,CAAC;QAC7C,OAAOe,SAAS,GACZpJ,WAAC,CAACyH,iBAAiB,CAAC,IAAI,EAAE2B,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIwC,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAACvG,IAAI,CAACqG,SAAS,CAAC;QAC9B;QACA,OAAOpJ,WAAC,CAACkD,cAAc,CACrBvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CAAC,2BAA2B,CAAC,EACtDmG,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAG/H,IAAI,CAAC8B,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMkG,2BAA2B,GAC/BtB,6BAA6B,KAAK,CAAC,CAAC,IACpCA,6BAA6B,GAAGqB,SAAS,CAAC3D,MAAM;IAClD,IAAI,CAAC2D,SAAS,CAAC3D,MAAM,IAAI,CAAC2D,SAAS,CAACE,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7DlI,IAAI,CAACmI,aAAa,CAChB,MAAM,EACN3J,WAAC,CAACyG,eAAe,CACf+C,2BAA2B,GACvB5C,OAAO,CAAC,CAAC,GACTyB,0BAA0B,CAAC,CACjC,CACF,CAAC;IACH;IAEA,KAAK,MAAMuB,UAAU,IAAIjJ,UAAU,CAACM,YAAY,EAAE;MAChD2I,UAAU,CACPtG,GAAG,CAAC,UAAU,CAAC,CACf2E,WAAW,CAACkB,UAAU,CAACS,UAAU,CAAC9J,IAAI,CAACuH,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC1F,IAAmB,EAAEO,IAAe,EAAE;IACxD,MAAMS,KAAK,GAAGT,IAAI,GAAGA,IAAI,CAACS,KAAK,GAAGH,UAAU,CAACG,KAAK;IAElD,IAAIhB,IAAI,CAAC0D,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAIqG,aAAa,CAAC/J,IAAI,EAAEgB,KAAK,CAAC,EAAE;IAClC;IAEA,MAAM6E,SAAS,GAAG7F,IAAI,CAACsC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGpB,UAAU,CAACoB,OAAO,CAAC4D,SAAS,CAAC;IAE7C,MAAMmE,OAAO,GAAGhK,IAAI,CAAC0D,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAG1D,IAAI,CAAC0D,IAAI;IAC5D,MAAMyC,GAAG,GACPjG,WAAC,CAAC+J,gBAAgB,CAACjK,IAAI,CAACmG,GAAG,CAAC,IAAIjG,WAAC,CAACgK,eAAe,CAAClK,IAAI,CAACmG,GAAG,CAAC,GACvDjG,WAAC,CAACiK,aAAa,CAACC,MAAM,CAACpK,IAAI,CAACmG,GAAG,CAACkE,KAAK,CAAC,CAAC,GACvCnK,WAAC,CAACoK,aAAa,CAACtK,IAAI,CAAC;IAE3B,IAAIuK,EAAgB,GAAGrK,WAAC,CAACsK,YAAY,CAACxK,IAAI,CAAC;IAE3C,IAAIE,WAAC,CAACuK,eAAe,CAACtE,GAAG,CAAC,EAAE;MAE1B,IAAInG,IAAI,CAAC0D,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAAgH,aAAA;QAG1BH,EAAE,IAAAG,aAAA,GACA,IAAAC,2BAAY,EAGV;UAAEC,EAAE,EAAEzE,GAAG;UAAEnG,IAAI,EAAEA,IAAI;UAAEgB;QAAM,CAAC,EAC9BD,SAAS,EACTH,gBACF,CAAC,YAAA8J,aAAA,GAAIH,EAAE;MACX;IACF,CAAC,MAAM;MAELtI,OAAO,CAACE,WAAW,GAAG,IAAI;IAC5B;IAEA,IAAI0I,UAAsB;IAC1B,IACE,CAAC5I,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAACyI,GAAG,CAAE3E,GAAG,CAAqBkE,KAAK,CAAC,EAC/C;MACAQ,UAAU,GAAG5I,OAAO,CAACI,GAAG,CAACmB,GAAG,CAAE2C,GAAG,CAAqBkE,KAAK,CAAC;MAC5DQ,UAAU,CAACb,OAAO,CAAC,GAAGO,EAAE;MAExB,IAAIP,OAAO,KAAK,OAAO,EAAE;QACvBa,UAAU,CAACrH,GAAG,GAAG,IAAI;QACrBqH,UAAU,CAACE,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLF,UAAU,CAACR,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLQ,UAAU,GAAG;QACX1E,GAAG,EAEDA,GAAmB;QACrB,CAAC6D,OAAO,GAAGO;MACb,CAAe;MACftI,OAAO,CAACG,IAAI,CAACa,IAAI,CAAC4H,UAAU,CAAC;MAE7B,IAAI,CAAC5I,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAAC0I,GAAG,CAAE5E,GAAG,CAAqBkE,KAAK,EAAEQ,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASd,aAAaA,CAAC/J,IAAmB,EAAEgB,KAAY,EAAE;IACxD,IAAIL,WAAW,CAACqK,eAAe,IAAI,CAAChL,IAAI,CAAC4E,UAAU,EAAE;MAEnD,IAAI;QAAE9E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACsC,MAAM,EAAE;QAChB2I,oBAAoB,CAAC,CAAC;QACtBnL,QAAQ,GAAGe,UAAU,CAACiB,UAAU;MAClC;MACA,MAAMoJ,UAAU,GAAGhL,WAAC,CAACwH,gBAAgB,CACnCxH,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACmG,GAAG,EACRnG,IAAI,CAACmL,QAAQ,IAAIjL,WAAC,CAACkL,SAAS,CAACpL,IAAI,CAACmG,GAAG,CACvC,CAAC;MAED,IAAIlG,IAAkB,GAAGC,WAAC,CAACmL,kBAAkB,CAC3C,IAAI,EAEJrL,IAAI,CAAC2D,MAAM,EACX3D,IAAI,CAAC0B,IAAI,EACT1B,IAAI,CAACsL,SAAS,EACdtL,IAAI,CAACuL,KACP,CAAC;MACDrL,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMmG,GAAG,GAAGjG,WAAC,CAACoK,aAAa,CAACtK,IAAI,EAAEA,IAAI,CAACmG,GAAG,CAAC;MAC3C,IAAIjG,WAAC,CAACuK,eAAe,CAACtE,GAAG,CAAC,EAAE;QAAA,IAAAqF,cAAA;QAE1BvL,IAAI,IAAAuL,cAAA,GACF,IAAAb,2BAAY,EACV;UACE3K,IAAI,EAAEC,IAAI;UACV2K,EAAE,EAAEzE,GAAG;UACPnF;QACF,CAAC,EACDD,SAAS,EACTH,gBACF,CAAC,YAAA4K,cAAA,GAAIvL,IAAI;MACb;MAEA,MAAMwL,IAAI,GAAGvL,WAAC,CAACwL,mBAAmB,CAChCxL,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAEgD,UAAU,EAAEjL,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACyL,gBAAgB,CAACF,IAAI,EAAEzL,IAAI,CAAC;MAC9Ba,UAAU,CAACa,IAAI,CAACuB,IAAI,CAACwI,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAIpK,UAAU,CAACiB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAEjB,UAAU,CAACG,KAAK,CAAC4K,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAG3L,WAAC,CAACwH,gBAAgB,CACnC7G,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAACiE,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAM2H,gBAAgB,GAAG5L,WAAC,CAAC6L,mBAAmB,CAAC,KAAK,EAAE,CACpD7L,WAAC,CAAC8L,kBAAkB,CAACnL,UAAU,CAACiB,UAAU,EAAE+J,UAAU,CAAC,CACxD,CAAC;MAEFhL,UAAU,CAACa,IAAI,CAACuB,IAAI,CAAC6I,gBAAgB,CAAC;IACxC;EACF;EAKA,SAASrG,eAAeA,CACtBtE,YAA2C,EAC3C8K,MAAwB,EACxB1L,IAAgC,EAChC;IACAgC,QAAQ,CAAC;MACPf,mBAAmB,EAAEjB,IAAI;MACzBgB,eAAe,EAAE0K,MAAM;MACvBxK,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGT,UAAU;IAEhCX,WAAC,CAACyL,gBAAgB,CAACrK,SAAS,EAAE2K,MAAM,CAAC;IAGrC3K,SAAS,CAACqC,MAAM,GAAGsI,MAAM,CAACtI,MAAM;IAEhCzD,WAAC,CAACG,QAAQ,CAACiB,SAAS,CAACI,IAAI,EAAEuK,MAAM,CAACvK,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACwK,UAAU,GAAGD,MAAM,CAACvK,IAAI,CAACwK,UAAU;IAIlD,IAAIrL,UAAU,CAACsL,sBAAsB,IAAItL,UAAU,CAACuL,oBAAoB,EAAE;MACxE7H,eAAe,CAAC,CAAC;IACnB;IAEAoB,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC9E,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACe,cAAc,EAAE;IAExDf,UAAU,CAACe,cAAc,GAAG,IAAI;IAKhCf,UAAU,CAACa,IAAI,CAACyF,OAAO,CACrBjH,WAAC,CAACwL,mBAAmB,CACnBxL,WAAC,CAACkD,cAAc,CACdvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CACvBxC,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAASmL,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAEtK,WAAW;MAAE/B,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMyL,IAAI,IAAItM,IAAI,CAAC0B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACxB,WAAC,CAACuD,aAAa,CAAC6I,IAAI,CAAC,IAAI,CAACA,IAAI,CAACnB,QAAQ,EAAE;MAC9C,IAAInK,KAAK,CAACuL,MAAM,CAACD,IAAI,CAACnG,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMyE,EAAE,GAAG5J,KAAK,CAACwL,gCAAgC,CAACF,IAAI,CAACnG,GAAG,CAAC;MAC3DpE,WAAW,CAACgJ,GAAG,CAACH,EAAE,CAACpD,IAAI,EAAE8E,IAAI,CAACnG,GAAG,CAAC;MAElCmG,IAAI,CAACnG,GAAG,GAAGyE,EAAE;IACf;EACF;EAEA,SAAS6B,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAEvL,SAAS;MAAEa;IAAY,CAAC,GAAGlB,UAAU;IAC7C,MAAM6L,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAI9L,UAAU,CAACO,SAAS,EAAE;MACxB,IAAIwL,GAAG,GAAG1M,WAAC,CAACE,SAAS,CAACc,SAAS,CAAC;MAChC,IAAIL,UAAU,CAACQ,aAAa,EAAE;QAC5BuL,GAAG,GAAG1M,WAAC,CAACkD,cAAc,CAACvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEuJ,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACTjM,UAAU,CAACG,KAAK,CAACwL,gCAAgC,CAACtL,SAAS,CAAC;MAE9DwL,aAAa,CAACzJ,IAAI,CAAC6J,KAAK,CAAC;MACzBH,WAAW,CAAC1J,IAAI,CAAC2J,GAAG,CAAC;MAErBrK,QAAQ,CAAC;QAAErB,SAAS,EAAEhB,WAAC,CAACE,SAAS,CAAC0M,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAACtF,IAAI,EAAE6C,KAAK,CAAC,IAAItI,WAAW,EAAE;MACvC2K,aAAa,CAACzJ,IAAI,CAAC/C,WAAC,CAACiE,UAAU,CAACqD,IAAI,CAAC,CAAC;MACtCmF,WAAW,CAAC1J,IAAI,CAACoH,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEqC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBxM,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA6B,QAAQ,CAAC;MACPzB,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF6B,QAAQ,CAAC;MACPtB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAAC4K,EAAE;MAE3B9K,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAAC4K,EAAE,GACxB1K,WAAC,CAACiE,UAAU,CAACtD,UAAU,CAACb,IAAI,CAAC4K,EAAE,CAACpD,IAAI,CAAC,GACrC3G,UAAU,CAACG,KAAK,CAAC4K,qBAAqB,CAAC,OAAO,CAAC;MACnD1K,SAAS,EAAEL,UAAU,CAACb,IAAI,CAACgN,UAAU;MACrC5L,SAAS,EAAE,CAAC,CAACP,UAAU,CAACb,IAAI,CAACgN,UAAU;MACvCjN,eAAe,EAAEG,WAAC,CAAC8D,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFzB,QAAQ,CAAC;MACPlB,aAAa,EACXnB,WAAC,CAACoH,YAAY,CAACzG,UAAU,CAACK,SAAS,CAAC,IACpCT,cAAc,CAACqK,GAAG,CAACjK,UAAU,CAACK,SAAS,CAACsG,IAAI,CAAC,IAC7C,CAAC3G,UAAU,CAACG,KAAK,CAACiM,UAAU,CAC1BpM,UAAU,CAACK,SAAS,CAACsG,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE1H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD0B,QAAQ,CAAC;MACPjB,SAAS,EAAEzB,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEFqM,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAE3K;IAAK,CAAC,GAAGb,UAAU;IAC3B,MAAM;MAAE6L,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/DrI,SAAS,CAAC,CAAC;IAGX,IAAI,CAACzD,WAAW,CAACuM,YAAY,EAAE;MAC7BnN,eAAe,CAAC2B,IAAI,CAACyF,OAAO,CAC1BjH,WAAC,CAACwL,mBAAmB,CACnBxL,WAAC,CAACkD,cAAc,CAACvC,UAAU,CAACL,IAAI,CAAC6C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DnD,WAAC,CAACkH,cAAc,CAAC,CAAC,EAClBlH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAMqN,QAAQ,GAAG5M,IAAI,CAAC6M,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGxM,UAAU,CAACI,OAAO,IAAIS,IAAI,CAACoE,MAAM,KAAK,CAAC;IAC7D,IAAIuH,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAIjM,UAAU,CAACS,SAAS,CAACqC,MAAM,EAAE;QAI/C,IAAI,CAACzD,WAAC,CAACoH,YAAY,CAACwF,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMnB,UAAU,GAAGmB,eAAe,GAC9BxM,UAAU,CAACS,SAAS,CAACI,IAAI,CAACwK,UAAU,GACpC,EAAE;IACN,IAAI,CAACiB,QAAQ,EAAE;MACbjB,UAAU,CAACjJ,IAAI,CAAC/C,WAAC,CAACoN,SAAS,CAACpN,WAAC,CAACqN,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM5B,IAAI,GAAGvL,WAAC,CAACsK,YAAY,CAAC3J,UAAU,CAACS,SAAS,CAAC;MACjD,OAAOT,UAAU,CAACH,OAAO,GAAG+K,IAAI,GAAGvI,iBAAiB,CAAC,CAACuI,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAI,CAAC5K,UAAU,CAACgB,iBAAiB,EAAE;MACjCH,IAAI,CAACuB,IAAI,CACP/C,WAAC,CAACyG,eAAe,CACf9F,UAAU,CAACH,OAAO,GACdR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,GAChCoD,iBAAiB,CAAC,CAAChD,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CAAC,CAC1D,CACF,CAAC;IACH;IAEA4B,IAAI,CAACyF,OAAO,CAACtG,UAAU,CAACS,SAAS,CAAC;IAElC,MAAM2G,SAAS,GAAG/H,WAAC,CAACsN,uBAAuB,CACzCd,aAAa,EACbxM,WAAC,CAAC8D,cAAc,CAACtC,IAAI,EAAEwK,UAAU,CACnC,CAAC;IACD,OAAOhM,WAAC,CAACkD,cAAc,CAAC6E,SAAS,EAAE0E,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAACxM,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D", "ignoreList": []}