{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\src\\utils\\api.js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\utils\\api.js", "mtime": 1748425644024}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "Message", "router", "store", "MOCK_MODE", "mockData", "token", "user", "id", "name", "role", "mockResponse", "data", "delay", "Promise", "resolve", "setTimeout", "code", "msg", "interceptors", "response", "use", "success", "error", "status", "message", "replace", "base", "postKeyValueRequest", "url", "params", "includes", "valid", "method", "transformRequest", "ret", "i", "encodeURIComponent", "headers", "getters", "GET_TOKEN", "postRequest", "putRequest", "getRequest", "deleteRequest"], "sources": ["D:/G<PERSON>e/xinqianduan/src/utils/api.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport { Message } from \"element-ui\";\r\nimport router from \"../router\";\r\nimport store from \"../store\";\r\n\r\n// 纯前端模式 - 模拟API响应\r\nconst MOCK_MODE = true;\r\n\r\n// 模拟数据\r\nconst mockData = {\r\n  token: \"mock-token-123456\",\r\n  user: {\r\n    id: 1,\r\n    name: \"管理员\",\r\n    role: \"admin\"\r\n  }\r\n};\r\n\r\n// 模拟API响应函数\r\nconst mockResponse = (data, delay = 300) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve({\r\n        code: 200,\r\n        msg: \"success\",\r\n        data: data\r\n      });\r\n    }, delay);\r\n  });\r\n};\r\n\r\nif (!MOCK_MODE) {\r\n  // axios.defaults.baseURL = \"http://localhost:8000/index.php\";\r\n\r\n  axios.interceptors.response.use(\r\n    (success) => {\r\n      return success.data;\r\n    },\r\n    (error) => {\r\n      if (error.response.status == 504 || error.response.status == 404) {\r\n        Message.error({ message: \"服务器被吃了( ╯□╰ )\" });\r\n      } else if (error.response.status == 403) {\r\n        Message.error({ message: \"权限不足，请联系管理员\" });\r\n      } else if (error.response.status == 401) {\r\n        router.replace(\"/\");\r\n      } else {\r\n        if (error.response.data.msg) {\r\n          Message.error({ message: error.response.data.msg });\r\n        } else {\r\n          Message.error({ message: \"未知错误!\" });\r\n        }\r\n      }\r\n      return;\r\n    }\r\n  );\r\n}\r\n\r\nlet base = \"/admin\";\r\n\r\nexport const postKeyValueRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    // 模拟不同的API响应\r\n    if (url.includes('/Login/checkToken')) {\r\n      return mockResponse({ valid: true });\r\n    }\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"post\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    transformRequest: [\r\n      function (data) {\r\n        let ret = \"\";\r\n        for (let i in data) {\r\n          ret +=\r\n            encodeURIComponent(i) + \"=\" + encodeURIComponent(data[i]) + \"&\";\r\n        }\r\n        return ret;\r\n      },\r\n    ],\r\n    headers: {\r\n      \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n      \"Request-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const postRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    // 模拟不同的API响应\r\n    if (url.includes('/Login/checkToken')) {\r\n      return mockResponse({ valid: true });\r\n    }\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"post\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const putRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"put\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const getRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"get\",\r\n    url: `${base}${url}`,\r\n    params: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const deleteRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse({ success: true });\r\n  }\r\n\r\n  return axios({\r\n    method: \"delete\",\r\n    url: `${base}${url}`,\r\n    params: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,KAAK,MAAM,UAAU;;AAE5B;AACA,MAAMC,SAAS,GAAG,IAAI;;AAEtB;AACA,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,EAAE;IACJC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,GAAG,GAAG,KAAK;EAC1C,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9BC,UAAU,CAAC,MAAM;MACfD,OAAO,CAAC;QACNE,IAAI,EAAE,GAAG;QACTC,GAAG,EAAE,SAAS;QACdN,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,EAAEC,KAAK,CAAC;EACX,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,CAACT,SAAS,EAAE;EACd;;EAEAJ,KAAK,CAACmB,YAAY,CAACC,QAAQ,CAACC,GAAG,CAC5BC,OAAO,IAAK;IACX,OAAOA,OAAO,CAACV,IAAI;EACrB,CAAC,EACAW,KAAK,IAAK;IACT,IAAIA,KAAK,CAACH,QAAQ,CAACI,MAAM,IAAI,GAAG,IAAID,KAAK,CAACH,QAAQ,CAACI,MAAM,IAAI,GAAG,EAAE;MAChEvB,OAAO,CAACsB,KAAK,CAAC;QAAEE,OAAO,EAAE;MAAgB,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIF,KAAK,CAACH,QAAQ,CAACI,MAAM,IAAI,GAAG,EAAE;MACvCvB,OAAO,CAACsB,KAAK,CAAC;QAAEE,OAAO,EAAE;MAAc,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAIF,KAAK,CAACH,QAAQ,CAACI,MAAM,IAAI,GAAG,EAAE;MACvCtB,MAAM,CAACwB,OAAO,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM;MACL,IAAIH,KAAK,CAACH,QAAQ,CAACR,IAAI,CAACM,GAAG,EAAE;QAC3BjB,OAAO,CAACsB,KAAK,CAAC;UAAEE,OAAO,EAAEF,KAAK,CAACH,QAAQ,CAACR,IAAI,CAACM;QAAI,CAAC,CAAC;MACrD,CAAC,MAAM;QACLjB,OAAO,CAACsB,KAAK,CAAC;UAAEE,OAAO,EAAE;QAAQ,CAAC,CAAC;MACrC;IACF;IACA;EACF,CACF,CAAC;AACH;AAEA,IAAIE,IAAI,GAAG,QAAQ;AAEnB,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,MAAM,KAAK;EAClD,IAAI1B,SAAS,EAAE;IACb;IACA,IAAIyB,GAAG,CAACE,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACrC,OAAOpB,YAAY,CAAC;QAAEqB,KAAK,EAAE;MAAK,CAAC,CAAC;IACtC;IACA,OAAOrB,YAAY,CAACN,QAAQ,CAAC;EAC/B;EAEA,OAAOL,KAAK,CAAC;IACXiC,MAAM,EAAE,MAAM;IACdJ,GAAG,EAAE,GAAGF,IAAI,GAAGE,GAAG,EAAE;IACpBjB,IAAI,EAAEkB,MAAM;IACZI,gBAAgB,EAAE,CAChB,UAAUtB,IAAI,EAAE;MACd,IAAIuB,GAAG,GAAG,EAAE;MACZ,KAAK,IAAIC,CAAC,IAAIxB,IAAI,EAAE;QAClBuB,GAAG,IACDE,kBAAkB,CAACD,CAAC,CAAC,GAAG,GAAG,GAAGC,kBAAkB,CAACzB,IAAI,CAACwB,CAAC,CAAC,CAAC,GAAG,GAAG;MACnE;MACA,OAAOD,GAAG;IACZ,CAAC,CACF;IACDG,OAAO,EAAE;MACP,cAAc,EAAE,mCAAmC;MACnD,eAAe,EAAEnC,KAAK,CAACoC,OAAO,CAACC,SAAS,IAAI;IAC9C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAACZ,GAAG,EAAEC,MAAM,KAAK;EAC1C,IAAI1B,SAAS,EAAE;IACb;IACA,IAAIyB,GAAG,CAACE,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACrC,OAAOpB,YAAY,CAAC;QAAEqB,KAAK,EAAE;MAAK,CAAC,CAAC;IACtC;IACA,OAAOrB,YAAY,CAACN,QAAQ,CAAC;EAC/B;EAEA,OAAOL,KAAK,CAAC;IACXiC,MAAM,EAAE,MAAM;IACdJ,GAAG,EAAE,GAAGF,IAAI,GAAGE,GAAG,EAAE;IACpBjB,IAAI,EAAEkB,MAAM;IACZQ,OAAO,EAAE;MACP,aAAa,EAAEnC,KAAK,CAACoC,OAAO,CAACC,SAAS,IAAI;IAC5C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAME,UAAU,GAAGA,CAACb,GAAG,EAAEC,MAAM,KAAK;EACzC,IAAI1B,SAAS,EAAE;IACb,OAAOO,YAAY,CAACN,QAAQ,CAAC;EAC/B;EAEA,OAAOL,KAAK,CAAC;IACXiC,MAAM,EAAE,KAAK;IACbJ,GAAG,EAAE,GAAGF,IAAI,GAAGE,GAAG,EAAE;IACpBjB,IAAI,EAAEkB,MAAM;IACZQ,OAAO,EAAE;MACP,aAAa,EAAEnC,KAAK,CAACoC,OAAO,CAACC,SAAS,IAAI;IAC5C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,UAAU,GAAGA,CAACd,GAAG,EAAEC,MAAM,KAAK;EACzC,IAAI1B,SAAS,EAAE;IACb,OAAOO,YAAY,CAACN,QAAQ,CAAC;EAC/B;EAEA,OAAOL,KAAK,CAAC;IACXiC,MAAM,EAAE,KAAK;IACbJ,GAAG,EAAE,GAAGF,IAAI,GAAGE,GAAG,EAAE;IACpBC,MAAM,EAAEA,MAAM;IACdQ,OAAO,EAAE;MACP,aAAa,EAAEnC,KAAK,CAACoC,OAAO,CAACC,SAAS,IAAI;IAC5C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMI,aAAa,GAAGA,CAACf,GAAG,EAAEC,MAAM,KAAK;EAC5C,IAAI1B,SAAS,EAAE;IACb,OAAOO,YAAY,CAAC;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;EACxC;EAEA,OAAOtB,KAAK,CAAC;IACXiC,MAAM,EAAE,QAAQ;IAChBJ,GAAG,EAAE,GAAGF,IAAI,GAAGE,GAAG,EAAE;IACpBC,MAAM,EAAEA,MAAM;IACdQ,OAAO,EAAE;MACP,aAAa,EAAEnC,KAAK,CAACoC,OAAO,CAACC,SAAS,IAAI;IAC5C;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}]}