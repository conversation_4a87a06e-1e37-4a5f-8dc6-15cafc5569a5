法多邦系统功能优化需求详细说明

一、用户界面数据展示优化

1. 用户列表页面改进
   1.1 列表显示顺序调整（从左到右）：
       - 第一列：用户姓名（可点击进入详情）
       - 第二列：用户头像（可点击进入详情）
       - 第三列：联系电话（可点击进入详情）
       - 第四列：公司名称
   
   1.2 功能优化：
       - 支持录入多个联系电话（主要电话突出显示）
       - 移除用户来源显示（移至用户详情页）
       - 增加快速跳转功能（点击姓名/头像/电话都可进入详情页）
       - 鼠标悬停时显示"点击查看详情"提示

2. 用户详情页面改进
   2.1 个人基本信息完善：
       - 客户姓名
       - 联系电话（支持多个）
       - 身份证号（自动校验18位）
       - 性别选择
       - 出生日期（从身份证自动提取）
       - 民族
       - 婚姻状况
       - 居住地址
       - 户籍地址

   2.2 职业信息：
       - 工作单位
       - 年收入范围（下拉选择）

   2.3 证件信息管理：
       - 身份证正面照片上传
       - 身份证反面照片上传
       - 手持身份证照片上传
       - 智能识别功能：
         * 自动提取身份证信息
         * 识别有效期
         * 提取签发机关

   2.4 紧急联系人信息：
       - 联系人姓名
       - 联系人电话

二、案件管理系统优化

1. 案件基础信息完善
   1.1 案件信息：
       - 案件编号（支持自动生成或手动输入）
       - 服务阶段标记
       - 人员分配功能：
         * 调解专员选择
         * 法务专员选择
         * 立案专员选择
         * 律师选择
       - 案件备注功能（支持富文本编辑）
   
   1.2 重要日期记录：
       - 立案日期
       - 调解开始日期
       - 自动记录各阶段时间节点

2. 案件进度管理系统
   2.1 案件状态详细划分：
       A. 待处理（初始状态）
       
       B. 调解阶段
          - 调解跟进中
          - 调解中止
          - 调解成功
       
       C. 案件诉求选择
          - 仅调解
          - 仅起诉
          - 调解+起诉
       
       D. 立案阶段
          - 准备立案材料
          - 材料准备中
          - 材料已邮寄（需填写）：
            * 快递单号
            * 受理法院
            * 立案庭电话
            * 自动查询快递状态
          - 立案驳回（需填写驳回原因）
          - 法院立案成功（需填写）：
            * 案件编号
            * 主审法官姓名
            * 书记员姓名
            * 联系电话
          - 等待开庭（需填写）：
            * 开庭时间
            * 法院文书上传
          - 已判决：
            * 判决结果选择（胜诉/败诉/法庭调解）
            * 判决文书上传
            * 判决生效时间
       
       E. 执行阶段
          - 申请执行中（上传强制执行文书）
          - 执行进行中（需填写）：
            * 执行案号
            * 执行法官
            * 联系电话
          - 执行完毕
          - 案件关闭

三、债务人信息管理优化

1. 基础信息扩展
   1.1 主体类型选择：
       - 个人债务人
       - 公司债务人

   1.2 联系方式管理：
       - 支持添加多个联系电话
       - 支持添加多个联系地址
       - 标记常用联系方式

   1.3 证件管理：
       - 支持多种证件上传
       - 智能识别证件信息
       - 自动提取关键信息

   1.4 债务信息：
       - 债务金额记录
       - 案由详细说明
       - 关联调档信息

2. 证据材料管理系统
   2.1 文件管理功能：
       - 支持多种格式上传：
         * 图片（jpg、png等）
         * 文档（pdf、word等）
         * 音频（mp3、wav等）
         * 视频文件
       
   2.2 文件预览功能：
       - 图片在线预览
       - 文档在线阅读
       - 音频在线播放
       
   2.3 证据展示优化：
       - 主页面显示证据数量统计
       - 显示最新3张证据预览图
       - 提供详细的证据清单页面
       
   2.4 下载功能：
       - 支持一键打包下载
       - 自动按用户信息命名文件夹
       - 证据材料自动分类存放

四、支付系统优化

1. 支付列表显示优化
   1.1 列表显示调整：
       - 增加用户姓名显示（第二列）
       - 删除套餐显示字段
       
   1.2 支付管理功能：
       - 设置支付时效（3天）
       - 到期自动提醒：
         * 客服提醒
         * 调解员提醒
         * 业务员提醒

2. 财务统计功能
   2.1 金额统计：
       - 总金额统计
       - 已支付金额统计
       - 未支付金额统计
       
   2.2 筛选功能：
       - 按时间段筛选
       - 按用户名称筛选
       - 导出统计报表

五、工作群管理优化

1. 群组自动化管理
   1.1 自动建群规则：
       - 公司客户命名规则：公司名+VIP法务群
       - 个人客户命名规则：个人名+VIP法务群
       
   1.2 群组设置：
       - 自动分配群成员
       - 自动设置群封面（使用用户头像）
       - 自动设置群公告

2. 群聊功能增强
   2.1 界面优化：
       - 支持全屏显示
       - 优化媒体查看：
         * 图片集中查看
         * 文件分类查看
       
   2.2 管理功能：
       - 发言管理
       - 记录删除权限
       - 记录隐藏功能
       - 查看用户关联债务人信息

六、权限系统优化

1. 部门权限管理
   1.1 案件权限分配：
       - 调解部门权限设置
       - 法务部门权限设置
       
   1.2 数据访问控制：
       - 上级可查看下级数据
       - 同级数据互相隔离

2. 员工任务分配
   2.1 自动分配规则：
       - 按工作量均衡分配
       - 按部门顺序分配：
         * 调解部门
         * 文书部门
         * 立案部门
         * 律师部门

七、系统集成优化

1. 外部服务对接
   1.1 快递查询：
       - 对接快递100 API
       - 自动跟踪快递状态
       
   1.2 AI服务：
       - 集成DeepSeek AI
       - 智能辅助功能
       
   1.3 其他对接：
       - 支付网关对接
       - 小程序对接

2. 数据导出功能
   2.1 信息导出：
       - 用户信息导出
       - 债务人信息导出
       
   2.2 导出选项：
       - 自定义导出字段
       - 支持批量导出
       - 多种格式支持

八、员工工作平台优化

1. 员工信息管理
   1.1 基础信息完善：
       - 入职时间
       - 岗位信息
       - 工作编号
       - 联系方式
       - 小程序账号关联
       
   1.2 工作状态显示：
       - 在线状态显示
       - 工作量统计
       - 任务完成率
       - 绩效指标展示

2. 岗位工作台

   2.1 法务岗位工作台
       A. 案件任务管理：
          - 待处理起诉状列表
          - 待处理律师函列表
          - 待审核合同列表
       
       B. 数据统计：
          - 本月完成起诉文书数量
          - 总计完成起诉文书数量
          - 本月完成律师函数量
          - 总计完成律师函数数量
       
       C. 案件进度追踪：
          - 立案进度查看
          - 开庭安排查看
          - 判决跟进记录
       
       D. 文书处理工具：
          - 文书模板库
          - AI辅助撰写
          - 在线编辑功能

   2.2 调解岗位工作台
       A. 调解任务管理：
          - 今日调解电话清单
          - 待跟进案件列表
       
       B. 调解进度追踪：
          - 调解记录管理
          - 通话记录记录
          - 调解协议管理
       
       C. 工作统计：
          - 调解成功率统计
          - 案件处理量统计
          - 回访记录管理

   2.3 客服岗位工作台
       A. 任务管理：
          - 未分配任务列表
          - 待处理咨询列表
          - 证据待整理列表
          - 支付跟进提醒
       
       B. 客户服务：
          - 客户问题记录
          - 回访任务管理
          - 满意度跟踪
       
       C. 数据统计：
          - 服务响应时间
          - 问题解决率统计
          - 客户满意度统计

   2.4 业务员工作台
       A. 客户管理：
          - 提单进度追踪
          - 客户跟进记录
          - 合同签约状态
       
       B. 业绩管理：
          - 签约统计
          - 收款统计
          - 业绩目标追踪
       
       C. 客户服务：
          - 待跟进客户列表
          - 续约提醒管理
          - 客户反馈处理

3. 通用工作功能

   3.1 待办事项管理
       A. 任务分类：
          - 紧急任务
          - 今日待办
          - 待跟进事项
          - 逾期任务
       
       B. 提醒功能：
          - 到期提醒
          - 重要事项提醒
          - 待办事项推送

   3.2 工作报告
       A. 报告生成：
          - 日报自动生成
          - 工作完成情况
          - 重要事项记录
       
       B. 数据分析：
          - 工作量统计
          - 效率分析
          - 质量评估

   3.3 协作功能
       A. 部门内协作：
          - 任务转交
          - 文件共享
          - 进度同步
       
       B. 跨部门协作：
          - 案件会商
          - 联合处理
          - 信息共享

4. 工作流程自动化

   4.1 智能分配系统
       A. 任务分配：
          - 工作量自动平衡
          - 专业匹配分配
          - 优先级排序
       
       B. 进度管理：
          - 状态自动更新
          - 节点自动提醒
          - 超时预警提示

   4.2 审批流程
       A. 审批管理：
          - 分级审批设置
          - 文书审批流程
          - 费用审批流程
       
       B. 流程管理：
          - 审批进度查看
          - 审批记录存档
          - 意见反馈处理

注意事项：
1. 所有功能开发需确保数据安全性
2. 界面设计需保持简洁直观
3. 操作流程需符合实际业务需求
4. 各模块之间需保持良好的兼容性
5. 系统需具备良好的扩展性 