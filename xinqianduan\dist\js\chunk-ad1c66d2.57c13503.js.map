{"version": 3, "sources": ["webpack:///./src/views/pages/wenshu/cate.vue", "webpack:///src/views/pages/wenshu/cate.vue", "webpack:///./src/views/pages/wenshu/cate.vue?bf02", "webpack:///./src/views/pages/wenshu/cate.vue?697a", "webpack:///./src/views/pages/wenshu/cate.vue?17e2"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "_s", "$router", "currentRoute", "name", "attrs", "on", "refulsh", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "$event", "searchData", "editData", "directives", "rawName", "loading", "list", "scopedSlots", "_u", "key", "fn", "scope", "row", "title", "template_file", "create_time", "id", "downloadTemplate", "_e", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "desc", "templateRules", "template_name", "formatFileSize", "template_size", "previewTemplate", "downloadCurrentTemplate", "replaceTemplate", "removeTemplate", "uploadAction", "beforeTemplateUpload", "handleTemplateSuccess", "handleTemplateError", "saveData", "dialogVisible", "show_image", "staticRenderFns", "components", "data", "allSize", "page", "url", "info", "is_num", "required", "message", "trigger", "mounted", "getData", "methods", "_this", "getInfo", "getRequest", "then", "resp", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "go", "setTimeout", "allData", "filter", "item", "includes", "length", "$refs", "validate", "valid", "postRequest", "msg", "val", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "isValidType", "isLt10M", "response", "validateField", "err", "console", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "replaceUpload", "$el", "querySelector", "warning", "component"], "mappings": "yHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,IAAIL,EAAIM,GAAGL,KAAKM,QAAQC,aAAaC,MAAM,OAAOP,EAAG,YAAY,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAIY,UAAU,CAACV,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACE,YAAY,eAAeM,MAAM,CAAC,YAAc,YAAY,UAAY,IAAIG,MAAM,CAACC,MAAOd,EAAIe,OAAOC,QAASC,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAIe,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAAClB,EAAG,IAAI,CAACE,YAAY,gCAAgCM,MAAM,CAAC,KAAO,UAAUW,KAAK,WAAWnB,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,KAAO,iBAAiB,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIuB,eAAeF,KAAK,UAAU,CAACrB,EAAIK,GAAG,WAAW,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACE,YAAY,UAAUM,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIwB,SAAS,MAAM,CAACxB,EAAIK,GAAG,eAAe,KAAKH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,WAAW,CAACuB,WAAW,CAAC,CAAChB,KAAK,UAAUiB,QAAQ,YAAYZ,MAAOd,EAAI2B,QAASP,WAAW,YAAYhB,YAAY,aAAaM,MAAM,CAAC,KAAOV,EAAI4B,KAAK,OAAS,GAAG,OAAS,GAAG,aAAa,aAAa,CAAC1B,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,SAAS,YAAY,MAAM,wBAAwB,IAAImB,YAAY7B,EAAI8B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC/B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAG2B,EAAMC,IAAIC,mBAAmBjC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,kBAAkB,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUmB,YAAY7B,EAAI8B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC/B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAOuB,EAAMC,IAAIE,cAAgB,UAAY,UAAU,KAAO,OAAO,KAAOH,EAAMC,IAAIE,cAAgB,mBAAqB,oBAAoB,CAACpC,EAAIK,GAAG,IAAIL,EAAIM,GAAG2B,EAAMC,IAAIE,cAAgB,MAAQ,OAAO,QAAQ,UAAUlC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUmB,YAAY7B,EAAI8B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC/B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAG2B,EAAMC,IAAIG,yBAAyBnC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUmB,YAAY7B,EAAI8B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC/B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIwB,SAASS,EAAMC,IAAII,OAAO,CAACtC,EAAIK,GAAG,UAAW4B,EAAMC,IAAIE,cAAelC,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIuC,iBAAiBN,EAAMC,QAAQ,CAAClC,EAAIK,GAAG,UAAUL,EAAIwC,KAAKtC,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIyC,QAAQR,EAAMS,OAAQT,EAAMC,IAAII,OAAO,CAACtC,EAAIK,GAAG,WAAW,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACQ,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,GAAI,KAAK,YAAYV,EAAI2C,KAAK,OAAS,0CAA0C,MAAQ3C,EAAI4C,MAAM,WAAa,IAAIjC,GAAG,CAAC,cAAcX,EAAI6C,iBAAiB,iBAAiB7C,EAAI8C,wBAAwB,IAAI,GAAG5C,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQV,EAAImC,MAAQ,KAAK,QAAUnC,EAAI+C,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOpC,GAAG,CAAC,iBAAiB,SAASW,GAAQtB,EAAI+C,kBAAkBzB,KAAU,CAACpB,EAAG,UAAU,CAAC8C,IAAI,WAAWtC,MAAM,CAAC,MAAQV,EAAIiD,SAAS,MAAQjD,EAAIkD,QAAQ,CAAChD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQV,EAAImC,MAAQ,KAAK,cAAcnC,EAAImD,eAAe,KAAO,UAAU,CAACjD,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOG,MAAM,CAACC,MAAOd,EAAIiD,SAASd,MAAOlB,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAIiD,SAAU,QAAS/B,IAAME,WAAW,qBAAqB,GAAGlB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,cAAcV,EAAImD,iBAAiB,CAACjD,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAGG,MAAM,CAACC,MAAOd,EAAIiD,SAASG,KAAMnC,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAIiD,SAAU,OAAQ/B,IAAME,WAAW,oBAAoB,GAAGlB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcV,EAAImD,eAAe,KAAO,gBAAgB,MAAQnD,EAAIqD,gBAAgB,CAACnD,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAAGJ,EAAIiD,SAASb,cAA0kBlC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIiD,SAASK,eAAiB,aAAapD,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIuD,eAAevD,EAAIiD,SAASO,qBAAqBtD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,eAAe,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAIyD,kBAAkB,CAACzD,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmB,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAI0D,0BAA0B,CAAC1D,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,kBAAkB,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAI2D,kBAAkB,CAAC3D,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,OAAO,KAAO,iBAAiB,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAI4D,iBAAiB,CAAC5D,EAAIK,GAAG,WAAW,KAAh8CH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAAC8C,IAAI,iBAAiB5C,YAAY,oBAAoBM,MAAM,CAAC,OAASV,EAAI6D,aAAa,gBAAgB7D,EAAI8D,qBAAqB,aAAa9D,EAAI+D,sBAAsB,WAAW/D,EAAIgE,oBAAoB,kBAAiB,EAAM,OAAS,kBAAkB,eAAc,IAAO,CAAC9D,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,mBAAmB,CAACR,EAAG,OAAO,CAACF,EAAIK,GAAG,eAAe,GAAGH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIK,GAAG,2CAA2C,GAA64BH,EAAG,YAAY,CAACuB,WAAW,CAAC,CAAChB,KAAK,OAAOiB,QAAQ,SAASZ,OAAO,EAAOM,WAAW,UAAU4B,IAAI,gBAAgBtC,MAAM,CAAC,OAASV,EAAI6D,aAAa,gBAAgB7D,EAAI8D,qBAAqB,aAAa9D,EAAI+D,sBAAsB,WAAW/D,EAAIgE,oBAAoB,kBAAiB,EAAM,OAAS,kBAAkB,eAAc,MAAS,MAAM,GAAG9D,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASW,GAAQtB,EAAI+C,mBAAoB,KAAS,CAAC/C,EAAIK,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAOtB,EAAIiE,cAAc,CAACjE,EAAIK,GAAG,UAAU,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUV,EAAIkE,cAAc,MAAQ,OAAOvD,GAAG,CAAC,iBAAiB,SAASW,GAAQtB,EAAIkE,cAAc5C,KAAU,CAACpB,EAAG,WAAW,CAACQ,MAAM,CAAC,IAAMV,EAAImE,eAAe,IAAI,IAEpzNC,EAAkB,GCuQP,GACf3D,KAAA,OACA4D,WAAA,GACAC,OACA,OACAC,QAAA,OACA3C,KAAA,GACAgB,MAAA,EACA4B,KAAA,EACA7B,KAAA,GACA5B,OAAA,CACAC,QAAA,IAEAW,SAAA,EACA8C,IAAA,eACAtC,MAAA,OACAuC,KAAA,GACA3B,mBAAA,EACAoB,WAAA,GACAD,eAAA,EACAjB,SAAA,CACAd,MAAA,GACAiB,KAAA,GACAuB,OAAA,EACAvC,cAAA,GACAkB,cAAA,GACAE,cAAA,GAGAN,MAAA,CACAf,MAAA,CACA,CACAyC,UAAA,EACAC,QAAA,QACAC,QAAA,UAMAzB,cAAA,CACA,CACAuB,UAAA,EACAC,QAAA,UACAC,QAAA,WAIA3B,eAAA,QACAU,aAAA,6BAGAkB,UACA,KAAAC,WAEAC,QAAA,CACAzD,SAAAc,GACA,IAAA4C,EAAA,KACA,GAAA5C,EACA,KAAA6C,QAAA7C,GAEA,KAAAW,SAAA,CACAd,MAAA,GACAiB,KAAA,GACAhB,cAAA,GACAkB,cAAA,GACAE,cAAA,GAIA0B,EAAAnC,mBAAA,GAEAoC,QAAA7C,GACA,IAAA4C,EAAA,KACAA,EAAAE,WAAAF,EAAAT,IAAA,WAAAnC,GAAA+C,KAAAC,IACAA,IACAJ,EAAAjC,SAAAqC,EAAAhB,SAIA7B,QAAA8C,EAAAjD,GACA,KAAAkD,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAN,KAAA,KACA,KAAAO,cAAA,KAAAnB,IAAA,aAAAnC,GAAA+C,KAAAC,IACA,KAAAA,EAAAO,OACA,KAAAC,SAAA,CACAH,KAAA,UACAd,QAAA,UAEA,KAAAjD,KAAAmE,OAAAR,EAAA,QAIAS,MAAA,KACA,KAAAF,SAAA,CACAH,KAAA,QACAd,QAAA,aAIAjE,UACA,KAAAL,QAAA0F,GAAA,IAEA1E,aACA,KAAAiD,KAAA,EACA,KAAA7B,KAAA,GAEA,IAAAuC,EAAA,KACAA,EAAAvD,SAAA,EAGAuE,WAAA,KACA,IAAAC,EAAA,CACA,CACA7D,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,kDACAkB,cAAA,cACAE,cAAA,QAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,iDACAkB,cAAA,aACAE,cAAA,OAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,GACAkB,cAAA,GACAE,cAAA,GAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,mDACAkB,cAAA,aACAE,cAAA,QAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,GACAkB,cAAA,GACAE,cAAA,IAIA0B,EAAAnE,OAAAC,QACAkE,EAAAtD,KAAAuE,EAAAC,OAAAC,GACAA,EAAAlE,MAAAmE,SAAApB,EAAAnE,OAAAC,UAGAkE,EAAAtD,KAAAuE,EAGAjB,EAAAtC,MAAAsC,EAAAtD,KAAA2E,OACArB,EAAAvD,SAAA,GACA,MAGAqD,UACA,IAAAE,EAAA,KAEAA,EAAAvD,SAAA,EAGAuE,WAAA,KACAhB,EAAAtD,KAAA,CACA,CACAU,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,kDACAkB,cAAA,cACAE,cAAA,QAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,iDACAkB,cAAA,aACAE,cAAA,OAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,GACAkB,cAAA,GACAE,cAAA,GAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,mDACAkB,cAAA,aACAE,cAAA,QAEA,CACAlB,GAAA,EACAH,MAAA,OACAE,YAAA,aACAD,cAAA,GACAkB,cAAA,GACAE,cAAA,IAGA0B,EAAAtC,MAAA,EACAsC,EAAAvD,SAAA,GACA,MAkBAsC,WACA,IAAAiB,EAAA,KACA,KAAAsB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAAzB,EAAAT,IAAA,YAAAxB,UAAAoC,KAAAC,IACA,KAAAA,EAAAO,MACAX,EAAAY,SAAA,CACAH,KAAA,UACAd,QAAAS,EAAAsB,MAEA,KAAA5B,UACAE,EAAAnC,mBAAA,GAEAmC,EAAAY,SAAA,CACAH,KAAA,QACAd,QAAAS,EAAAsB,WASA/D,iBAAAgE,GACA,KAAAlE,KAAAkE,EAEA,KAAA7B,WAEAlC,oBAAA+D,GACA,KAAArC,KAAAqC,EACA,KAAA7B,WAEA8B,cAAAC,GACA,KAAA9D,SAAA+D,SAAAD,EAAAzC,KAAAG,KAGAwC,UAAAC,GACA,KAAA/C,WAAA+C,EACA,KAAAhD,eAAA,GAEAiD,aAAAD,GACA,MAAAE,EAAA,0BAAAC,KAAAH,EAAAvB,MACAyB,GACA,KAAAtB,SAAAwB,MAAA,cAIAC,SAAAL,EAAAM,GACA,IAAAtC,EAAA,KACAA,EAAAE,WAAA,6BAAA8B,GAAA7B,KAAAC,IACA,KAAAA,EAAAO,MACAX,EAAAjC,SAAAuE,GAAA,GAEAtC,EAAAY,SAAA2B,QAAA,UAEAvC,EAAAY,SAAAwB,MAAAhC,EAAAsB,QAMA9C,qBAAAoD,GACA,MAAAQ,EAAA,qBAAAL,KAAAH,EAAAzG,MACAkH,EAAAT,EAAAvE,KAAA,aAEA,OAAA+E,EAIAC,GAKA,KAAA7B,SAAApB,KAAA,gBACA,IALA,KAAAoB,SAAAwB,MAAA,uBACA,IALA,KAAAxB,SAAAwB,MAAA,gCACA,IAWAvD,sBAAA6D,EAAAV,GACA,MAAAU,EAAA/B,MACA,KAAA5C,SAAAb,cAAAwF,EAAAtD,KAAAG,IACA,KAAAxB,SAAAK,cAAA4D,EAAAzG,KACA,KAAAwC,SAAAO,cAAA0D,EAAAvE,KACA,KAAAmD,SAAA2B,QAAA,aAGA,KAAAjB,MAAAvD,SAAA4E,cAAA,kBAEA,KAAA/B,SAAAwB,MAAAM,EAAAhB,KAAA,cAIA5C,oBAAA8D,EAAAZ,GACA,KAAApB,SAAAwB,MAAA,iBACAS,QAAAT,MAAA,yBAAAQ,IAIAvE,eAAAyE,GACA,OAAAA,EAAA,YACA,MAAAC,EAAA,KACAC,EAAA,qBACAC,EAAAC,KAAAC,MAAAD,KAAAE,IAAAN,GAAAI,KAAAE,IAAAL,IACA,OAAAM,YAAAP,EAAAI,KAAAI,IAAAP,EAAAE,IAAAM,QAAA,QAAAP,EAAAC,IAIA1E,kBACA,KAAAR,SAAAb,eAEA,KAAA0D,SAAApB,KAAA,eAMAhB,0BACA,QAAAT,SAAAb,cAAA,CACA,MAAAsG,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA,KAAA5F,SAAAb,cACAsG,EAAAI,SAAA,KAAA7F,SAAAK,eAAA,OACAqF,SAAAI,KAAAC,YAAAN,GACAA,EAAAO,QACAN,SAAAI,KAAAG,YAAAR,GACA,KAAA5C,SAAA2B,QAAA,cAKA9D,kBACA,KAAA6C,MAAA2C,cAAAC,IAAAC,cAAA,SAAAJ,SAIArF,iBACA,KAAA4B,SAAA,sBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YACAN,KAAA,KACA,KAAApC,SAAAb,cAAA,GACA,KAAAa,SAAAK,cAAA,GACA,KAAAL,SAAAO,cAAA,EACA,KAAAsC,SAAA2B,QAAA,WAGA,KAAAjB,MAAAvD,SAAA4E,cAAA,mBACA7B,MAAA,SAMAzD,iBAAAL,GACA,GAAAA,EAAAE,cAAA,CACA,MAAAsG,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA3G,EAAAE,cACAsG,EAAAI,SAAA5G,EAAAoB,eAAApB,EAAAC,MAAA,KACAwG,SAAAI,KAAAC,YAAAN,GACAA,EAAAO,QACAN,SAAAI,KAAAG,YAAAR,GACA,KAAA5C,SAAA2B,QAAA,QAAAvF,EAAAC,iBAEA,KAAA2D,SAAAwD,QAAA,kBC9pB2W,I,wBCQvWC,EAAY,eACd,EACAxJ,EACAqE,GACA,EACA,KACA,WACA,MAIa,aAAAmF,E,2CCnBf,W", "file": "js/chunk-ad1c66d2.57c13503.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"contract-type-container\"},[_c('div',{staticClass:\"page-header\"},[_c('h1',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" \"+_vm._s(this.$router.currentRoute.name)+\" \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 刷新 \")])],1),_c('div',{staticClass:\"action-section\"},[_c('div',{staticClass:\"search-area\"},[_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入合同类型名称\",\"clearable\":\"\"},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"}),_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-search\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.searchData()}},slot:\"append\"},[_vm._v(\" 搜索 \")])],1)],1),_c('div',{staticClass:\"button-area\"},[_c('el-button',{staticClass:\"add-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增合同类型 \")])],1)]),_c('div',{staticClass:\"table-section\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"data-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\",\"border\":\"\",\"empty-text\":\"暂无合同类型数据\"}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"合同类型名称\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"type-name\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_c('span',[_vm._v(_vm._s(scope.row.title))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"template_status\",\"label\":\"合同模板\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"template-status\"},[_c('el-tag',{attrs:{\"type\":scope.row.template_file ? 'success' : 'warning',\"size\":\"mini\",\"icon\":scope.row.template_file ? 'el-icon-document' : 'el-icon-warning'}},[_vm._v(\" \"+_vm._s(scope.row.template_file ? '已上传' : '未上传')+\" \")])],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建时间\",\"width\":\"180\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-info\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(scope.row.create_time))])])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" 编辑 \")]),(scope.row.template_file)?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadTemplate(scope.row)}}},[_vm._v(\" 下载 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 删除 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[10, 20, 50, 100],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":_vm.title + '标题',\"label-width\":_vm.formLabelWidth,\"prop\":\"title\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"描述\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"合同模板\",\"label-width\":_vm.formLabelWidth,\"prop\":\"template_file\",\"rules\":_vm.templateRules}},[_c('div',{staticClass:\"template-upload-area\"},[(!_vm.ruleForm.template_file)?_c('div',{staticClass:\"upload-section\"},[_c('el-upload',{ref:\"templateUpload\",staticClass:\"template-uploader\",attrs:{\"action\":_vm.uploadAction,\"before-upload\":_vm.beforeTemplateUpload,\"on-success\":_vm.handleTemplateSuccess,\"on-error\":_vm.handleTemplateError,\"show-file-list\":false,\"accept\":\".doc,.docx,.pdf\",\"auto-upload\":true}},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-upload\"}},[_c('span',[_vm._v(\"上传合同模板\")])])],1),_c('div',{staticClass:\"upload-tip\"},[_c('i',{staticClass:\"el-icon-info\"}),_c('span',[_vm._v(\"支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB\")])])],1):_c('div',{staticClass:\"uploaded-file\"},[_c('div',{staticClass:\"file-info\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',{staticClass:\"file-name\"},[_vm._v(_vm._s(_vm.ruleForm.template_name || '合同模板文件'))]),_c('span',{staticClass:\"file-size\"},[_vm._v(_vm._s(_vm.formatFileSize(_vm.ruleForm.template_size)))])]),_c('div',{staticClass:\"file-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"icon\":\"el-icon-view\",\"size\":\"mini\"},on:{\"click\":_vm.previewTemplate}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"type\":\"text\",\"icon\":\"el-icon-download\",\"size\":\"mini\"},on:{\"click\":_vm.downloadCurrentTemplate}},[_vm._v(\" 下载 \")]),_c('el-button',{attrs:{\"type\":\"text\",\"icon\":\"el-icon-refresh\",\"size\":\"mini\"},on:{\"click\":_vm.replaceTemplate}},[_vm._v(\" 替换 \")]),_c('el-button',{staticClass:\"danger-text\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-delete\",\"size\":\"mini\"},on:{\"click\":_vm.removeTemplate}},[_vm._v(\" 删除 \")])],1)]),_c('el-upload',{directives:[{name:\"show\",rawName:\"v-show\",value:(false),expression:\"false\"}],ref:\"replaceUpload\",attrs:{\"action\":_vm.uploadAction,\"before-upload\":_vm.beforeTemplateUpload,\"on-success\":_vm.handleTemplateSuccess,\"on-error\":_vm.handleTemplateError,\"show-file-list\":false,\"accept\":\".doc,.docx,.pdf\",\"auto-upload\":true}})],1)])],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"contract-type-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 操作区域 -->\r\n    <div class=\"action-section\">\r\n      <div class=\"search-area\">\r\n        <el-input\r\n          placeholder=\"请输入合同类型名称\"\r\n          v-model=\"search.keyword\"\r\n          class=\"search-input\"\r\n          clearable\r\n        >\r\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n            type=\"primary\"\r\n          >\r\n            搜索\r\n          </el-button>\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"button-area\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"editData(0)\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-btn\"\r\n        >\r\n          新增合同类型\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同类型数据\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"合同类型名称\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-name\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"template_status\" label=\"合同模板\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"template-status\">\r\n              <el-tag\r\n                :type=\"scope.row.template_file ? 'success' : 'warning'\"\r\n                size=\"mini\"\r\n                :icon=\"scope.row.template_file ? 'el-icon-document' : 'el-icon-warning'\"\r\n              >\r\n                {{ scope.row.template_file ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-edit\"\r\n                class=\"action-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.template_file\"\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"downloadTemplate(scope.row)\"\r\n                icon=\"el-icon-download\"\r\n                class=\"action-btn\"\r\n              >\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"合同模板\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"template_file\"\r\n          :rules=\"templateRules\"\r\n        >\r\n          <div class=\"template-upload-area\">\r\n            <!-- 文件上传区域 -->\r\n            <div v-if=\"!ruleForm.template_file\" class=\"upload-section\">\r\n              <el-upload\r\n                ref=\"templateUpload\"\r\n                :action=\"uploadAction\"\r\n                :before-upload=\"beforeTemplateUpload\"\r\n                :on-success=\"handleTemplateSuccess\"\r\n                :on-error=\"handleTemplateError\"\r\n                :show-file-list=\"false\"\r\n                accept=\".doc,.docx,.pdf\"\r\n                :auto-upload=\"true\"\r\n                class=\"template-uploader\"\r\n              >\r\n                <el-button type=\"primary\" icon=\"el-icon-upload\">\r\n                  <span>上传合同模板</span>\r\n                </el-button>\r\n              </el-upload>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 已上传文件显示区域 -->\r\n            <div v-else class=\"uploaded-file\">\r\n              <div class=\"file-info\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ ruleForm.template_name || '合同模板文件' }}</span>\r\n                <span class=\"file-size\">{{ formatFileSize(ruleForm.template_size) }}</span>\r\n              </div>\r\n              <div class=\"file-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"previewTemplate\"\r\n                  icon=\"el-icon-view\"\r\n                  size=\"mini\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"downloadCurrentTemplate\"\r\n                  icon=\"el-icon-download\"\r\n                  size=\"mini\"\r\n                >\r\n                  下载\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"replaceTemplate\"\r\n                  icon=\"el-icon-refresh\"\r\n                  size=\"mini\"\r\n                >\r\n                  替换\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"removeTemplate\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  class=\"danger-text\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 隐藏的替换上传组件 -->\r\n            <el-upload\r\n              v-show=\"false\"\r\n              ref=\"replaceUpload\"\r\n              :action=\"uploadAction\"\r\n              :before-upload=\"beforeTemplateUpload\"\r\n              :on-success=\"handleTemplateSuccess\"\r\n              :on-error=\"handleTemplateError\"\r\n              :show-file-list=\"false\"\r\n              accept=\".doc,.docx,.pdf\"\r\n              :auto-upload=\"true\"\r\n            >\r\n            </el-upload>\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshucate/\",\r\n      title: \"文书类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n        template_file: \"\",\r\n        template_name: \"\",\r\n        template_size: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n\r\n      // 合同模板验证规则\r\n      templateRules: [\r\n        {\r\n          required: true,\r\n          message: \"请上传合同模板\",\r\n          trigger: \"change\",\r\n        },\r\n      ],\r\n\r\n      formLabelWidth: \"120px\",\r\n      uploadAction: \"/admin/Upload/uploadFile\", // 文件上传接口\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          template_file: \"\",\r\n          template_name: \"\",\r\n          template_size: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 模拟搜索测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n\r\n        if (_this.search.keyword) {\r\n          _this.list = allData.filter(item =>\r\n            item.title.includes(_this.search.keyword)\r\n          );\r\n        } else {\r\n          _this.list = allData;\r\n        }\r\n\r\n        _this.total = _this.list.length;\r\n        _this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 合同模板相关方法\r\n    beforeTemplateUpload(file) {\r\n      const isValidType = /\\.(doc|docx|pdf)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('合同模板只能是 .doc、.docx、.pdf 格式!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('合同模板文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传合同模板...');\r\n      return true;\r\n    },\r\n\r\n    handleTemplateSuccess(response, file) {\r\n      if (response.code === 200) {\r\n        this.ruleForm.template_file = response.data.url;\r\n        this.ruleForm.template_name = file.name;\r\n        this.ruleForm.template_size = file.size;\r\n        this.$message.success('合同模板上传成功!');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      } else {\r\n        this.$message.error(response.msg || '合同模板上传失败!');\r\n      }\r\n    },\r\n\r\n    handleTemplateError(err, file) {\r\n      this.$message.error('合同模板上传失败，请重试!');\r\n      console.error('Template upload error:', err);\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    },\r\n\r\n    // 预览模板\r\n    previewTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        // 这里可以实现文件预览功能\r\n        this.$message.info('预览功能开发中...');\r\n        // window.open(this.ruleForm.template_file, '_blank');\r\n      }\r\n    },\r\n\r\n    // 下载当前模板\r\n    downloadCurrentTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = this.ruleForm.template_file;\r\n        link.download = this.ruleForm.template_name || '合同模板';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success('开始下载合同模板');\r\n      }\r\n    },\r\n\r\n    // 替换模板\r\n    replaceTemplate() {\r\n      this.$refs.replaceUpload.$el.querySelector('input').click();\r\n    },\r\n\r\n    // 删除模板\r\n    removeTemplate() {\r\n      this.$confirm('确定要删除当前合同模板吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.ruleForm.template_file = '';\r\n        this.ruleForm.template_name = '';\r\n        this.ruleForm.template_size = 0;\r\n        this.$message.success('合同模板已删除');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 下载表格中的模板\r\n    downloadTemplate(row) {\r\n      if (row.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = row.template_file;\r\n        link.download = row.template_name || `${row.title}模板`;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success(`开始下载 ${row.title} 模板`);\r\n      } else {\r\n        this.$message.warning('该合同类型暂无模板文件');\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-type-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.search-area {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.button-area {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn {\r\n  font-weight: 500;\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.type-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.type-name i {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-name span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-type-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .action-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-area {\r\n    max-width: none;\r\n  }\r\n\r\n  .button-area {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 小屏幕下操作按钮调整 */\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n}\r\n\r\n/* 操作按钮横向布局 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n}\r\n\r\n.el-button--success:hover {\r\n  background: #85ce61;\r\n  border-color: #85ce61;\r\n}\r\n\r\n.el-button--danger:hover {\r\n  background: #f78989;\r\n  border-color: #f78989;\r\n}\r\n\r\n/* 合同模板状态样式 */\r\n.template-status {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n/* 合同模板上传区域样式 */\r\n.template-upload-area {\r\n  width: 100%;\r\n}\r\n\r\n.upload-section {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-section:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.template-uploader {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tip i {\r\n  color: #409eff;\r\n}\r\n\r\n/* 已上传文件显示样式 */\r\n.uploaded-file {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.file-info i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.file-size {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-actions .el-button--text {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n}\r\n\r\n.danger-text {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n.danger-text:hover {\r\n  color: #f78989 !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .file-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .file-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cate.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cate.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./cate.vue?vue&type=template&id=031e9798&scoped=true\"\nimport script from \"./cate.vue?vue&type=script&lang=js\"\nexport * from \"./cate.vue?vue&type=script&lang=js\"\nimport style0 from \"./cate.vue?vue&type=style&index=0&id=031e9798&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"031e9798\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cate.vue?vue&type=style&index=0&id=031e9798&prod&scoped=true&lang=css\""], "sourceRoot": ""}