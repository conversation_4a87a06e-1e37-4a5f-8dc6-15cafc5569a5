<?php
namespace app\index\controller;
use think\Request;
use untils\{JsonService,LiveA,WxApi};
use models\{Zhubos,Users,Comments,Lines};
use think\Controller;
use think\facade\View;

class Live extends Controller{
    public function hasLiveStatus(Request $requst,Zhubos $model){
        
        $id = $requst->post('id');
        try {
            $info = $model->find($id);
            $res = LiveA::getLiveStatus($info['appname'],$info['streamname']);
            if($res['StreamState']=='offline'){
                return JsonService::successful('未开始');
            }
        } catch (\Exception $e) {
            return JsonService::fail('系统异常:'.$e->getMessage());
        }
       
       
    }
    
    public function getOnlineUserNum(Request $requst,Zhubos $model){
        $id = $requst->post('id');
        $info = $model->find($id);
        $res = LiveA::getOnlineUserNum($info['appname'],$info['streamname']);
        var_dump($res['UserCount']);
    }
    public function getHuifang(){
        //echo  date("Y-d-mTH:i:sZ", strtotime('2022-09-05 20:36:32'));
      
       
        $list  = Zhubos::whereNull('huifang')->select();
       
        foreach ($list as $k=>$v){
            $res =  LiveA::DescribeLiveStreamRecordIndexFiles('lvdian',$v['streamname'],date('Y-m-d',$v['end_time']).' 00:00:00',date('Y-m-d',$v['end_time']).' 23:59:59');
           
            $RecordIndexInfo = $res['RecordIndexInfoList']['RecordIndexInfo'];
            
            if(!empty($RecordIndexInfo)){
                $huifang = serialize($RecordIndexInfo);
                Zhubos::where(['id'=>$v['id']])->update(['huifang'=>$huifang]);
            }
        }
       
       
    }
    public function sendLiveMessage(){
        $zhubo = new Zhubos();
        $users = new Users();
        $model = new WxApi();
        $zhubo::beginTrans();
        try {
       
        $info  = $zhubo->where(['is_send'=>0])
        ->order(['id'=>'asc'])->find();
       
        $openids  = $users->column('openid');
        $template_id = 'gOPqhyvAd6bUhPVY4DGcEpz-OdE7grubEZIgDKfF46s';
        
        $desc = $info['title'].'案件预备讲解~';
        $id = $info['id'];
        //$url = "http://tiaojie.oecgd.com/web/#/pages/live/zhubo?id=".$id;
        $url = "https://web.faduobang.com/web/#/pages/live/zhubo?id=".$id;
        foreach($openids as $v){
        $post = [
            'touser'=>$v,
            'template_id'=>$template_id,
            'url'=>$url,
            'topcolor'=>'#FF0000',
            'data'=>[
                'first'=>['value'=>"直播提醒"],
                'keyword1'=>['value'=> $info['title'],'color'=>'#11276b'],
                'keyword2'=>['value'=>date('Y-m-d H:i',$info['start_time']),'color'=>'#11276b'],
                'remark'=>['value'=>$desc,'color'=>'#11276b']
            ]
        ];
       
        $model->sendTemplete($post);
        }
        $info->is_send = 1;
        $info->save();
        $zhubo::commitTrans();
        } catch (\Exception $e) {
            $zhubo::rollbackTrans();
            echo '系统异常:'.$e->getMessage();
        }
        
    }
    public function huifang(Request $request){
          $post = $request->post();
          file_put_contents("./huifang.txt",$post);
    }
    
    public function sendComment(Request $request,Comments $model){
        $post = $request->post();
        $post['comment_time']=time();
        $model->save($post);
        return JsonService::successful(''); 
    }
    public function findComent(Request $request,Comments $model){
        $zhubo_id = $request->post('zhubo_id');
       
        $res = $model
        ->where(['zhubo_id'=>$zhubo_id])
        ->where('is_show','=',0)
        ->order(['id'=>'asc'])
        ->find();
        if(!empty($res)) {
            $res->is_show=1;
            $res->save();
            return JsonService::successful('获取成功',$res);
        }else{
             return JsonService::fail('暂无评论');
        }
        
    }
    public function postLine(Request $request,Lines $model){
        $post  = $request->post();
        $res = $model->where(['zhubo_id'=>$post['zhubo_id'],'openid'=>$post['openid']])->find();
        if(empty($res)) {
             $model->save($post);
        }else{
            $res->is_show=0;
            $res->line=1;
            $res->save();
        }
        return JsonService::successful("成功");
    }
    public function findLine(Request $request,Lines $model){
        $zhubo_id = $request->post('zhubo_id');
        $res = $model
        ->where(['zhubo_id'=>$zhubo_id])
        ->where('is_show','=',0)
        ->where('is_line','=',1)
        ->find();
       
        if(!empty($res)) {
            $res->is_show=1;
            $res->save();
            return JsonService::successful('获取成功',$res);
        }else{
             return JsonService::fail('暂无在线认数');
        }
    }
    public function changeLine(Request $request,Lines $model){
        $post  = $request->post();
        $res = $model->where(['zhubo_id'=>$post['zhubo_id'],'openid'=>$post['openid']])->find();
      
        if(!empty($res)){
              $res->is_line = 0;
              $res->is_show = 0;
              $res->save();
        }
    }
}