{"version": 3, "sources": ["webpack:///./src/views/pages/profile/index.vue", "webpack:///src/views/pages/profile/index.vue", "webpack:///./src/views/pages/profile/index.vue?4266", "webpack:///./src/views/pages/profile/index.vue?187b", "webpack:///./src/views/pages/profile/index.vue?12c7"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "userInfo", "avatar", "defaultAvatar", "name", "handleAvatarSuccess", "beforeAvatarUpload", "_v", "_s", "role", "department", "ref", "rules", "editMode", "model", "value", "callback", "$$v", "$set", "expression", "employee_id", "phone", "email", "staticStyle", "position", "bio", "saving", "on", "saveProfile", "cancelEdit", "enableEdit", "changePassword", "lastLoginTime", "lastLoginIP", "staticRenderFns", "data", "originalUserInfo", "required", "message", "trigger", "pattern", "type", "mounted", "loadUserInfo", "methods", "$refs", "userForm", "validate", "valid", "setTimeout", "$message", "success", "$router", "push", "res", "url", "file", "isImage", "test", "isLt2M", "size", "error", "component"], "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAeE,MAAM,CAAC,IAAMN,EAAIO,SAASC,QAAUR,EAAIS,cAAc,IAAMT,EAAIO,SAASG,QAAQR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,kBAAkBE,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAaN,EAAIW,oBAAoB,gBAAgBX,EAAIY,qBAAqB,CAACV,EAAG,IAAI,CAACE,YAAY,2CAA2C,KAAKF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,aAAa,CAACJ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIO,SAASG,MAAQ,UAAUR,EAAG,IAAI,CAACE,YAAY,aAAa,CAACJ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIO,SAASQ,MAAQ,YAAYb,EAAG,IAAI,CAACE,YAAY,mBAAmB,CAACJ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIO,SAASS,YAAc,gBAAgBd,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAU,CAACe,IAAI,WAAWb,YAAY,eAAeE,MAAM,CAAC,MAAQN,EAAIO,SAAS,MAAQP,EAAIkB,MAAM,cAAc,UAAU,CAAChB,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,KAAK,KAAO,SAAS,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,QAAQ,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASG,KAAMY,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,OAAQgB,IAAME,WAAW,oBAAoB,GAAGvB,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,KAAK,KAAO,gBAAgB,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,QAAQ,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASmB,YAAaJ,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,cAAegB,IAAME,WAAW,2BAA2B,IAAI,GAAGvB,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,MAAM,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,SAAS,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASoB,MAAOL,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,QAASgB,IAAME,WAAW,qBAAqB,GAAGvB,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,KAAK,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,QAAQ,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASqB,MAAON,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,QAASgB,IAAME,WAAW,qBAAqB,IAAI,GAAGvB,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,KAAK,KAAO,eAAe,CAACJ,EAAG,YAAY,CAAC2B,YAAY,CAAC,MAAQ,QAAQvB,MAAM,CAAC,YAAc,QAAQ,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASS,WAAYM,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,aAAcgB,IAAME,WAAW,wBAAwB,CAACvB,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,WAAWJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,WAAWJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,MAAM,MAAQ,SAASJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,MAAM,MAAQ,SAASJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,MAAM,MAAQ,UAAU,IAAI,GAAGJ,EAAG,eAAe,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQ,KAAK,KAAO,aAAa,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,QAAQ,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASuB,SAAUR,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,WAAYgB,IAAME,WAAW,wBAAwB,IAAI,GAAGvB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,QAAQ,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,UAAU,UAAYN,EAAImB,UAAUC,MAAM,CAACC,MAAOrB,EAAIO,SAASwB,IAAKT,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIO,SAAU,MAAOgB,IAAME,WAAW,mBAAmB,GAAGvB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAGJ,EAAImB,SAA0H,CAACjB,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgB,QAAUN,EAAIgC,QAAQC,GAAG,CAAC,MAAQjC,EAAIkC,cAAc,CAAClC,EAAIa,GAAG,YAAYX,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,iBAAiB2B,GAAG,CAAC,MAAQjC,EAAImC,aAAa,CAACnC,EAAIa,GAAG,WAA5UX,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgB2B,GAAG,CAAC,MAAQjC,EAAIoC,aAAa,CAACpC,EAAIa,GAAG,aAAoP,IAAI,IAAI,KAAKX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,YAAY,CAACE,YAAY,kBAAkBE,MAAM,CAAC,KAAO,QAAQ2B,GAAG,CAAC,MAAQjC,EAAIqC,iBAAiB,CAACrC,EAAIa,GAAG,aAAa,GAAGX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIsC,sBAAsBpC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIuC,+BAExpJC,EAAkB,CAAC,WAAY,IAAIxC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIa,GAAG,wBAC5R,WAAY,IAAIb,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIa,GAAG,eACtK,WAAY,IAAIb,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIa,GAAG,uBCqMpL,G,UAAA,CACfH,KAAA,UACA+B,OACA,OACAtB,UAAA,EACAa,QAAA,EACAvB,cAAA,uSACAF,SAAA,CACAG,KAAA,MACAgB,YAAA,WACAC,MAAA,cACAC,MAAA,oBACAZ,WAAA,QACAc,SAAA,QACAf,KAAA,QACAgB,IAAA,wBACAvB,OAAA,IAEAkC,iBAAA,GACAJ,cAAA,sBACAC,YAAA,gBACArB,MAAA,CACAR,KAAA,CACA,CAAAiC,UAAA,EAAAC,QAAA,QAAAC,QAAA,SAEAlB,MAAA,CACA,CAAAgB,UAAA,EAAAC,QAAA,SAAAC,QAAA,QACA,CAAAC,QAAA,gBAAAF,QAAA,YAAAC,QAAA,SAEAjB,MAAA,CACA,CAAAe,UAAA,EAAAC,QAAA,QAAAC,QAAA,QACA,CAAAE,KAAA,QAAAH,QAAA,aAAAC,QAAA,YAKAG,UACA,KAAAC,gBAEAC,QAAA,CACAD,eAGA,KAAAP,iBAAA,SAAAnC,WAEA6B,aACA,KAAAjB,UAAA,EACA,KAAAuB,iBAAA,SAAAnC,WAEA4B,aACA,KAAAhB,UAAA,EACA,KAAAZ,SAAA,SAAAmC,mBAEAR,cACA,KAAAiB,MAAAC,SAAAC,SAAAC,IACAA,IACA,KAAAtB,QAAA,EAEAuB,WAAA,KACA,KAAAvB,QAAA,EACA,KAAAb,UAAA,EACA,KAAAqC,SAAAC,QAAA,cACA,SAIApB,iBACA,KAAAqB,QAAAC,KAAA,eAEAhD,oBAAAiD,GACAA,KAAAnB,MAAAmB,EAAAnB,KAAAoB,MACA,KAAAtD,SAAAC,OAAAoD,EAAAnB,KAAAoB,IACA,KAAAL,SAAAC,QAAA,aAGA7C,mBAAAkD,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAAf,MACAkB,EAAAH,EAAAI,KAAA,YAEA,OAAAH,IAIAE,IACA,KAAAT,SAAAW,MAAA,sBACA,IALA,KAAAX,SAAAW,MAAA,0BACA,OC1R4W,I,wBCQxWC,EAAY,eACd,EACArE,EACAyC,GACA,EACA,KACA,WACA,MAIa,aAAA4B,E,2CCnBf", "file": "js/chunk-0b5221ca.f8376412.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"page-wrapper\"},[_c('div',{staticClass:\"page-container\"},[_vm._m(0),_c('div',{staticClass:\"profile-section\"},[_c('div',{staticClass:\"profile-card\"},[_c('div',{staticClass:\"avatar-section\"},[_c('div',{staticClass:\"avatar-container\"},[_c('img',{staticClass:\"avatar-image\",attrs:{\"src\":_vm.userInfo.avatar || _vm.defaultAvatar,\"alt\":_vm.userInfo.name}}),_c('div',{staticClass:\"avatar-overlay\"},[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleAvatarSuccess,\"before-upload\":_vm.beforeAvatarUpload}},[_c('i',{staticClass:\"el-icon-camera avatar-uploader-icon\"})])],1)]),_c('div',{staticClass:\"user-basic-info\"},[_c('h3',{staticClass:\"user-name\"},[_vm._v(_vm._s(_vm.userInfo.name || '管理员'))]),_c('p',{staticClass:\"user-role\"},[_vm._v(_vm._s(_vm.userInfo.role || '系统管理员'))]),_c('p',{staticClass:\"user-department\"},[_vm._v(_vm._s(_vm.userInfo.department || '法律服务部'))])])]),_c('div',{staticClass:\"form-section\"},[_c('el-form',{ref:\"userForm\",staticClass:\"profile-form\",attrs:{\"model\":_vm.userInfo,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('div',{staticClass:\"form-row\"},[_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"姓名\",\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入姓名\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.name),callback:function ($$v) {_vm.$set(_vm.userInfo, \"name\", $$v)},expression:\"userInfo.name\"}})],1),_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"工号\",\"prop\":\"employee_id\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入工号\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.employee_id),callback:function ($$v) {_vm.$set(_vm.userInfo, \"employee_id\", $$v)},expression:\"userInfo.employee_id\"}})],1)],1),_c('div',{staticClass:\"form-row\"},[_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"手机号\",\"prop\":\"phone\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入手机号\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.phone),callback:function ($$v) {_vm.$set(_vm.userInfo, \"phone\", $$v)},expression:\"userInfo.phone\"}})],1),_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"邮箱\",\"prop\":\"email\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入邮箱\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.email),callback:function ($$v) {_vm.$set(_vm.userInfo, \"email\", $$v)},expression:\"userInfo.email\"}})],1)],1),_c('div',{staticClass:\"form-row\"},[_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"部门\",\"prop\":\"department\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择部门\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.department),callback:function ($$v) {_vm.$set(_vm.userInfo, \"department\", $$v)},expression:\"userInfo.department\"}},[_c('el-option',{attrs:{\"label\":\"法律服务部\",\"value\":\"法律服务部\"}}),_c('el-option',{attrs:{\"label\":\"客户服务部\",\"value\":\"客户服务部\"}}),_c('el-option',{attrs:{\"label\":\"财务部\",\"value\":\"财务部\"}}),_c('el-option',{attrs:{\"label\":\"人事部\",\"value\":\"人事部\"}}),_c('el-option',{attrs:{\"label\":\"技术部\",\"value\":\"技术部\"}})],1)],1),_c('el-form-item',{staticClass:\"form-item\",attrs:{\"label\":\"职位\",\"prop\":\"position\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入职位\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.position),callback:function ($$v) {_vm.$set(_vm.userInfo, \"position\", $$v)},expression:\"userInfo.position\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"个人简介\",\"prop\":\"bio\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":4,\"placeholder\":\"请输入个人简介\",\"disabled\":!_vm.editMode},model:{value:(_vm.userInfo.bio),callback:function ($$v) {_vm.$set(_vm.userInfo, \"bio\", $$v)},expression:\"userInfo.bio\"}})],1),_c('div',{staticClass:\"action-buttons\"},[(!_vm.editMode)?_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-edit\"},on:{\"click\":_vm.enableEdit}},[_vm._v(\" 编辑资料 \")]):[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-check\",\"loading\":_vm.saving},on:{\"click\":_vm.saveProfile}},[_vm._v(\" 保存修改 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-close\"},on:{\"click\":_vm.cancelEdit}},[_vm._v(\" 取消 \")])]],2)],1)],1)]),_c('div',{staticClass:\"security-card\"},[_vm._m(1),_c('div',{staticClass:\"security-items\"},[_c('div',{staticClass:\"security-item\"},[_vm._m(2),_c('el-button',{staticClass:\"security-action\",attrs:{\"type\":\"text\"},on:{\"click\":_vm.changePassword}},[_vm._v(\" 修改密码 \")])],1),_c('div',{staticClass:\"security-item\"},[_c('div',{staticClass:\"security-info\"},[_c('div',{staticClass:\"security-title\"},[_vm._v(\"最后登录\")]),_c('div',{staticClass:\"security-desc\"},[_vm._v(_vm._s(_vm.lastLoginTime))])])]),_c('div',{staticClass:\"security-item\"},[_c('div',{staticClass:\"security-info\"},[_c('div',{staticClass:\"security-title\"},[_vm._v(\"登录IP\")]),_c('div',{staticClass:\"security-desc\"},[_vm._v(_vm._s(_vm.lastLoginIP))])])])])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 个人信息 \")]),_c('div',{staticClass:\"page-subtitle\"},[_vm._v(\"管理您的个人资料和账户设置\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"card-header\"},[_c('h3',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-lock\"}),_vm._v(\" 安全设置 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"security-info\"},[_c('div',{staticClass:\"security-title\"},[_vm._v(\"登录密码\")]),_c('div',{staticClass:\"security-desc\"},[_vm._v(\"定期更换密码，保护账户安全\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"page-wrapper\">\n    <div class=\"page-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-user\"></i>\n            个人信息\n          </h2>\n          <div class=\"page-subtitle\">管理您的个人资料和账户设置</div>\n        </div>\n      </div>\n\n      <!-- 个人信息卡片 -->\n      <div class=\"profile-section\">\n        <div class=\"profile-card\">\n          <!-- 头像区域 -->\n          <div class=\"avatar-section\">\n            <div class=\"avatar-container\">\n              <img \n                :src=\"userInfo.avatar || defaultAvatar\" \n                :alt=\"userInfo.name\"\n                class=\"avatar-image\"\n              />\n              <div class=\"avatar-overlay\">\n                <el-upload\n                  action=\"/admin/Upload/uploadImage\"\n                  :show-file-list=\"false\"\n                  :on-success=\"handleAvatarSuccess\"\n                  :before-upload=\"beforeAvatarUpload\"\n                  class=\"avatar-uploader\"\n                >\n                  <i class=\"el-icon-camera avatar-uploader-icon\"></i>\n                </el-upload>\n              </div>\n            </div>\n            <div class=\"user-basic-info\">\n              <h3 class=\"user-name\">{{ userInfo.name || '管理员' }}</h3>\n              <p class=\"user-role\">{{ userInfo.role || '系统管理员' }}</p>\n              <p class=\"user-department\">{{ userInfo.department || '法律服务部' }}</p>\n            </div>\n          </div>\n\n          <!-- 信息表单 -->\n          <div class=\"form-section\">\n            <el-form \n              :model=\"userInfo\" \n              :rules=\"rules\" \n              ref=\"userForm\" \n              label-width=\"120px\"\n              class=\"profile-form\"\n            >\n              <div class=\"form-row\">\n                <el-form-item label=\"姓名\" prop=\"name\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.name\" \n                    placeholder=\"请输入姓名\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n                \n                <el-form-item label=\"工号\" prop=\"employee_id\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.employee_id\" \n                    placeholder=\"请输入工号\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <div class=\"form-row\">\n                <el-form-item label=\"手机号\" prop=\"phone\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.phone\" \n                    placeholder=\"请输入手机号\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n                \n                <el-form-item label=\"邮箱\" prop=\"email\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.email\" \n                    placeholder=\"请输入邮箱\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <div class=\"form-row\">\n                <el-form-item label=\"部门\" prop=\"department\" class=\"form-item\">\n                  <el-select \n                    v-model=\"userInfo.department\" \n                    placeholder=\"请选择部门\"\n                    :disabled=\"!editMode\"\n                    style=\"width: 100%\"\n                  >\n                    <el-option label=\"法律服务部\" value=\"法律服务部\"></el-option>\n                    <el-option label=\"客户服务部\" value=\"客户服务部\"></el-option>\n                    <el-option label=\"财务部\" value=\"财务部\"></el-option>\n                    <el-option label=\"人事部\" value=\"人事部\"></el-option>\n                    <el-option label=\"技术部\" value=\"技术部\"></el-option>\n                  </el-select>\n                </el-form-item>\n                \n                <el-form-item label=\"职位\" prop=\"position\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.position\" \n                    placeholder=\"请输入职位\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <el-form-item label=\"个人简介\" prop=\"bio\">\n                <el-input \n                  v-model=\"userInfo.bio\" \n                  type=\"textarea\" \n                  :rows=\"4\"\n                  placeholder=\"请输入个人简介\"\n                  :disabled=\"!editMode\"\n                ></el-input>\n              </el-form-item>\n\n              <!-- 操作按钮 -->\n              <div class=\"action-buttons\">\n                <el-button \n                  v-if=\"!editMode\"\n                  type=\"primary\" \n                  icon=\"el-icon-edit\"\n                  @click=\"enableEdit\"\n                >\n                  编辑资料\n                </el-button>\n                \n                <template v-else>\n                  <el-button \n                    type=\"primary\" \n                    icon=\"el-icon-check\"\n                    @click=\"saveProfile\"\n                    :loading=\"saving\"\n                  >\n                    保存修改\n                  </el-button>\n                  <el-button \n                    icon=\"el-icon-close\"\n                    @click=\"cancelEdit\"\n                  >\n                    取消\n                  </el-button>\n                </template>\n              </div>\n            </el-form>\n          </div>\n        </div>\n\n        <!-- 安全设置卡片 -->\n        <div class=\"security-card\">\n          <div class=\"card-header\">\n            <h3 class=\"card-title\">\n              <i class=\"el-icon-lock\"></i>\n              安全设置\n            </h3>\n          </div>\n          \n          <div class=\"security-items\">\n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">登录密码</div>\n                <div class=\"security-desc\">定期更换密码，保护账户安全</div>\n              </div>\n              <el-button \n                type=\"text\" \n                @click=\"changePassword\"\n                class=\"security-action\"\n              >\n                修改密码\n              </el-button>\n            </div>\n            \n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">最后登录</div>\n                <div class=\"security-desc\">{{ lastLoginTime }}</div>\n              </div>\n            </div>\n            \n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">登录IP</div>\n                <div class=\"security-desc\">{{ lastLoginIP }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Profile\",\n  data() {\n    return {\n      editMode: false,\n      saving: false,\n      defaultAvatar: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzk5OSI+5aS05YOP</dGV4dD48L3N2Zz4=\",\n      userInfo: {\n        name: \"管理员\",\n        employee_id: \"ADMIN001\",\n        phone: \"13800138000\",\n        email: \"<EMAIL>\",\n        department: \"法律服务部\",\n        position: \"系统管理员\",\n        role: \"系统管理员\",\n        bio: \"负责系统管理和维护工作，确保系统稳定运行。\",\n        avatar: \"\"\n      },\n      originalUserInfo: {},\n      lastLoginTime: \"2024-01-15 09:30:25\",\n      lastLoginIP: \"*************\",\n      rules: {\n        name: [\n          { required: true, message: \"请输入姓名\", trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n          { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  mounted() {\n    this.loadUserInfo();\n  },\n  methods: {\n    loadUserInfo() {\n      // 这里可以从后端加载用户信息\n      // 目前使用默认数据\n      this.originalUserInfo = { ...this.userInfo };\n    },\n    enableEdit() {\n      this.editMode = true;\n      this.originalUserInfo = { ...this.userInfo };\n    },\n    cancelEdit() {\n      this.editMode = false;\n      this.userInfo = { ...this.originalUserInfo };\n    },\n    saveProfile() {\n      this.$refs.userForm.validate((valid) => {\n        if (valid) {\n          this.saving = true;\n          // 模拟保存过程\n          setTimeout(() => {\n            this.saving = false;\n            this.editMode = false;\n            this.$message.success(\"个人信息保存成功！\");\n          }, 1000);\n        }\n      });\n    },\n    changePassword() {\n      this.$router.push(\"/changePwd\");\n    },\n    handleAvatarSuccess(res) {\n      if (res && res.data && res.data.url) {\n        this.userInfo.avatar = res.data.url;\n        this.$message.success(\"头像上传成功！\");\n      }\n    },\n    beforeAvatarUpload(file) {\n      const isImage = /^image\\/(jpeg|png|jpg)$/.test(file.type);\n      const isLt2M = file.size / 1024 / 1024 < 2;\n\n      if (!isImage) {\n        this.$message.error(\"上传头像图片只能是 JPG/PNG 格式!\");\n        return false;\n      }\n      if (!isLt2M) {\n        this.$message.error(\"上传头像图片大小不能超过 2MB!\");\n        return false;\n      }\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 页面布局样式 */\n.page-wrapper {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 16px;\n}\n\n.page-container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 页面头部 */\n.page-header {\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #ffffff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.page-title i {\n  color: #1890ff;\n  font-size: 22px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #8c8c8c;\n  margin: 0;\n}\n\n/* 个人信息区域 */\n.profile-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n}\n\n/* 个人信息卡片 */\n.profile-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n/* 头像区域 */\n.avatar-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 32px;\n  display: flex;\n  align-items: center;\n  gap: 24px;\n  color: white;\n}\n\n.avatar-container {\n  position: relative;\n  flex-shrink: 0;\n}\n\n.avatar-image {\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n}\n\n.avatar-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s;\n  cursor: pointer;\n}\n\n.avatar-container:hover .avatar-overlay {\n  opacity: 1;\n}\n\n.avatar-uploader {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar-uploader-icon {\n  font-size: 24px;\n  color: white;\n}\n\n.user-basic-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 24px;\n  font-weight: 500;\n  margin: 0 0 8px 0;\n}\n\n.user-role {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0 0 4px 0;\n}\n\n.user-department {\n  font-size: 14px;\n  opacity: 0.8;\n  margin: 0;\n}\n\n/* 表单区域 */\n.form-section {\n  padding: 32px;\n}\n\n.profile-form {\n  max-width: 600px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24px;\n  margin-bottom: 0;\n}\n\n.form-item {\n  margin-bottom: 24px;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  gap: 12px;\n}\n\n/* 安全设置卡片 */\n.security-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  height: fit-content;\n}\n\n.card-header {\n  padding: 24px 24px 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.card-title i {\n  color: #1890ff;\n}\n\n.security-items {\n  padding: 16px 24px 24px 24px;\n}\n\n.security-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.security-item:last-child {\n  border-bottom: none;\n}\n\n.security-info {\n  flex: 1;\n}\n\n.security-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n  margin-bottom: 4px;\n}\n\n.security-desc {\n  font-size: 12px;\n  color: #8c8c8c;\n}\n\n.security-action {\n  color: #1890ff;\n  font-size: 14px;\n}\n\n.security-action:hover {\n  color: #40a9ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .profile-section {\n    grid-template-columns: 1fr;\n  }\n\n  .avatar-section {\n    flex-direction: column;\n    text-align: center;\n    gap: 16px;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 0;\n  }\n\n  .form-section {\n    padding: 24px 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n}\n\n/* 表单样式优化 */\n.profile-form ::v-deep .el-form-item__label {\n  color: #262626;\n  font-weight: 500;\n}\n\n.profile-form ::v-deep .el-input__inner {\n  border-radius: 6px;\n}\n\n.profile-form ::v-deep .el-input__inner:focus {\n  border-color: #1890ff;\n}\n\n.profile-form ::v-deep .el-textarea__inner {\n  border-radius: 6px;\n}\n\n.profile-form ::v-deep .el-select {\n  width: 100%;\n}\n\n/* 禁用状态样式 */\n.profile-form ::v-deep .el-input.is-disabled .el-input__inner {\n  background-color: #f5f5f5;\n  border-color: #e4e7ed;\n  color: #606266;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=1b7c37e7&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1b7c37e7&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1b7c37e7\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=1b7c37e7&prod&scoped=true&lang=css\""], "sourceRoot": ""}