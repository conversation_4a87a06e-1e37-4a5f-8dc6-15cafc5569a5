{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748525288783}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAu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file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <el-container class=\"cont\">\n    <el-header class=\"top-header\">\n      <!-- 顶部导航栏 -->\n      <div class=\"header-left\">\n        <span class=\"logo\">{{ name }}</span>\n      </div>\n      <div class=\"header-center\">\n        <el-menu\n          class=\"top-menu\"\n          @select=\"menuClick\"\n          mode=\"horizontal\"\n          background-color=\"#001529\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/\">首页</el-menu-item>\n          <!-- 如果有子菜单，显示为下拉菜单 -->\n          <el-submenu\n            v-for=\"(item, index) in menus\"\n            v-if=\"item.children && item.children.length > 1\"\n            :key=\"'submenu-' + index\"\n            :index=\"item.path\"\n            popper-class=\"vertical-submenu\"\n          >\n            <template slot=\"title\">{{ item.name }}</template>\n            <el-menu-item\n              v-for=\"(child, indexj) in item.children\"\n              :key=\"indexj\"\n              :index=\"child.path\"\n            >\n              {{ child.name }}\n            </el-menu-item>\n          </el-submenu>\n          <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\n          <el-menu-item\n            v-for=\"(item, index) in menus\"\n            v-if=\"!item.children || item.children.length <= 1\"\n            :key=\"'menuitem-' + index\"\n            :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\n          >\n            {{ item.name }}\n          </el-menu-item>\n        </el-menu>\n      </div>\n      <div class=\"header-right\">\n        <el-dropdown trigger=\"click\">\n          <span class=\"user-info\">管理员</span>\n          <el-dropdown-menu>\n            <el-dropdown-item\n              ><div @click=\"menuClick('/changePwd')\">\n                修改密码\n              </div></el-dropdown-item\n            >\n            <el-dropdown-item>\n              <div @click=\"logout()\">退出登录</div>\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </div>\n    </el-header>\n\n    <el-container class=\"content-container\">\n      <el-header class=\"breadcrumb-header\">\n        <el-breadcrumb separator=\"/\">\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n          <el-breadcrumb-item>{{\n            this.$router.currentRoute.name\n          }}</el-breadcrumb-item>\n        </el-breadcrumb>\n      </el-header>\n\n      <el-main class=\"main-content\">\n        <router-view></router-view>\n      </el-main>\n    </el-container>\n\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\n      <el-image :src=\"show_image\"></el-image>\n    </el-dialog>\n  </el-container>\n</template>\n\n<script>\nexport default {\n  name: \"Home\",\n  data() {\n    return {\n      dialogVisible: false,\n      money_count: 0,\n      user_count: 0,\n      visit_count: 1234, // 演示数据\n      search_count: 0,\n      export_count: 0,\n      order_count: 0,\n      gaode_count: 0,\n      tengxun_count: 0,\n      baidu_count: 0,\n      shunqiwang_count: 0,\n      show_image: \"\",\n      menus: [],\n      url: \"/Yuangong/\",\n    };\n  },\n  computed: {\n    name() {\n      return this.$store.getters.GET_TITLE;\n    },\n  },\n  mounted() {\n    // 纯前端模式 - 直接提供菜单数据\n    this.menus = [\n      {\n        path: \"/jichu\",\n        name: \"基础管理\",\n        children: [\n          { path: \"/config\", name: \"基础设置\" },\n          { path: \"/banner\", name: \"轮播图\" },\n          { path: \"/nav\", name: \"首页导航\" },\n          { path: \"/gonggao\", name: \"公告\" }\n        ]\n      },\n      {\n        path: \"/xiadan\",\n        name: \"订单管理\",\n        children: [\n          { path: \"/type\", name: \"服务类型\" },\n          { path: \"/taocan\", name: \"套餐类型\" },\n          { path: \"/dingdan\", name: \"签约用户列表\" },\n          { path: \"/qun\", name: \"签约客户群\" }\n        ]\n      },\n      {\n        path: \"/yonghu\",\n        name: \"用户管理\",\n        children: [\n          { path: \"/user\", name: \"用户列表\" }\n        ]\n      },\n      {\n        path: \"/zhifu\",\n        name: \"支付列表\",\n        children: [\n          { path: \"/order\", name: \"支付列表\" }\n        ]\n      },\n      {\n        path: \"/liaotian\",\n        name: \"聊天列表\",\n        children: [\n          { path: \"/chat\", name: \"聊天列表\" }\n        ]\n      },\n      {\n        path: \"/debt\",\n        name: \"债权管理\",\n        children: [\n          { path: \"/debts\", name: \"债务人列表\" }\n        ]\n      },\n      {\n        path: \"/wenshuguanli\",\n        name: \"文书管理\",\n        children: [\n          { path: \"/dingzhi\", name: \"合同定制\" },\n          { path: \"/shenhe\", name: \"合同审核\" },\n          { path: \"/cate\", name: \"合同类型\" },\n          { path: \"/hetong\", name: \"合同列表\" },\n          { path: \"/lawyer\", name: \"发律师函\" },\n          { path: \"/kecheng\", name: \"课程列表\" }\n        ]\n      },\n      {\n        path: \"/yuangong\",\n        name: \"员工管理\",\n        children: [\n          { path: \"/zhiwei\", name: \"职位\" },\n          { path: \"/yuangong\", name: \"员工\" },\n          { path: \"/lvshi\", name: \"律师\" }\n        ]\n      },\n      {\n        path: \"/fuwu\",\n        name: \"服务管理\",\n        children: [\n          { path: \"/fuwu\", name: \"服务列表\" }\n        ]\n      },\n      {\n        path: \"/xinwen\",\n        name: \"案例管理\",\n        children: [\n          { path: \"/anli\", name: \"案例列表\" }\n        ]\n      },\n      {\n        path: \"/lvshiguanli\",\n        name: \"专业管理\",\n        children: [\n          { path: \"/zhuanye\", name: \"专业列表\" }\n        ]\n      },\n      {\n        path: \"/archive\",\n        name: \"归档管理\",\n        children: [\n          { path: \"/archive/file\", name: \"文件归档\" },\n          { path: \"/archive/search\", name: \"档案检索\" }\n        ]\n      }\n    ];\n\n    console.log(\"菜单数据已加载:\", this.menus);\n  },\n  methods: {\n    showQrcode() {\n      // 纯前端模式 - 显示演示二维码\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\n      this.dialogVisible = true;\n    },\n    menuClick(index) {\n      this.$router.push(index);\n    },\n    getQuanxian() {\n      // 纯前端模式 - 不需要后端权限验证\n      console.log(\"纯前端模式，跳过权限验证\");\n    },\n    getCountAll() {\n      // 纯前端模式 - 使用演示数据\n      console.log(\"纯前端模式，使用演示数据\");\n    },\n    logout() {\n      this.$store.commit(\"INIT_TOKEN\", \"\");\n      this.$store.commit(\"INIT_TITLE\", \"\");\n      this.$message({\n        type: \"success\",\n        message: \"退出成功\",\n      });\n      let _this = this;\n      setTimeout(function () {\n        _this.$router.push(\"/login\");\n      }, 1500);\n    },\n  },\n};\n</script>\n<style scoped>\n.cont {\n  height: 100vh;\n  overflow: hidden;\n  flex-direction: column;\n}\n\n.content-container {\n  flex: 1;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* 顶部导航栏样式 */\n.top-header {\n  height: 60px;\n  background-color: #001529;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 1000;\n}\n\n.header-left {\n  flex: 0 0 200px;\n  min-width: 200px;\n}\n\n.logo {\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.header-center {\n  flex: 1;\n  display: flex;\n  justify-content: flex-start;\n  overflow: hidden;\n}\n\n.top-menu {\n  border: none !important;\n  background-color: transparent !important;\n  width: 100%;\n  min-width: 0;\n}\n\n/* 强制水平排列 */\n.top-menu {\n  display: flex !important;\n  flex-direction: row !important;\n}\n\n.top-menu >>> .el-menu {\n  display: flex !important;\n  flex-direction: row !important;\n  width: 100%;\n}\n\n.top-menu >>> .el-menu-item,\n.top-menu >>> .el-submenu {\n  border-bottom: none !important;\n  height: 60px;\n  line-height: 60px;\n  padding: 0 15px;\n  white-space: nowrap;\n  flex: 0 0 auto;\n  display: inline-flex !important;\n  align-items: center;\n  float: none !important;\n}\n\n/* 确保Element UI的默认样式被覆盖 */\n.el-menu--horizontal {\n  display: flex !important;\n  flex-direction: row !important;\n}\n\n.el-menu--horizontal .el-menu-item,\n.el-menu--horizontal .el-submenu {\n  float: none !important;\n  display: inline-flex !important;\n}\n\n.top-menu .el-submenu__title {\n  height: 60px;\n  line-height: 60px;\n  padding: 0 15px;\n  border-bottom: none !important;\n}\n\n/* 强制子菜单垂直排列 - 最高优先级 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  margin: 0 !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 使用更高优先级的选择器 */\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 针对所有可能的子菜单容器 */\n.el-submenu__drop-down .el-menu-item,\n.el-submenu .el-submenu__drop-down .el-menu-item,\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n}\n\n/* 覆盖任何可能的水平布局 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n.el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\n.el-submenu__drop-down .el-menu .el-menu-item,\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  box-sizing: border-box !important;\n}\n\n/* 强制下拉菜单容器为垂直布局 */\n.el-submenu__drop-down,\n.el-menu--horizontal .el-submenu__drop-down,\n.el-submenu .el-submenu__drop-down {\n  display: block !important;\n  flex-direction: column !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n/* 确保子菜单内的ul也是垂直的 */\n.el-submenu__drop-down ul,\n.el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  list-style: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\n  display: block !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  border-right: none !important;\n  border-left: none !important;\n  border-top: none !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\n  border-bottom: none !important;\n}\n\n/* 强制覆盖任何可能的inline样式 */\n.el-submenu__drop-down .el-menu-item[style] {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n}\n\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  border-right: none !important;\n  border-left: none !important;\n  border-top: none !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n  position: relative !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down {\n  display: block !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* 使用更高的CSS优先级 */\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 针对自定义popper-class的样式 - 美化版本 */\n.vertical-submenu {\n  display: block !important;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\n  overflow: hidden !important;\n  min-width: 180px !important;\n  padding: 8px 0 !important;\n}\n\n.vertical-submenu .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n.vertical-submenu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 44px !important;\n  line-height: 44px !important;\n  padding: 0 20px !important;\n  margin: 2px 8px !important;\n  background-color: rgba(255, 255, 255, 0.95) !important;\n  color: #2c3e50 !important;\n  text-align: left !important;\n  border: none !important;\n  border-radius: 6px !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n  font-weight: 500 !important;\n  font-size: 14px !important;\n  transition: all 0.3s ease !important;\n  position: relative !important;\n  width: calc(100% - 16px) !important;\n}\n\n.vertical-submenu .el-menu-item:hover {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\n  color: #fff !important;\n  transform: translateX(4px) !important;\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\n}\n\n.vertical-submenu .el-menu-item:active {\n  transform: translateX(2px) !important;\n}\n\n.vertical-submenu .el-menu-item:last-child {\n  margin-bottom: 0 !important;\n}\n\n/* 添加一些动画效果 */\n.vertical-submenu .el-menu-item::before {\n  content: '' !important;\n  position: absolute !important;\n  left: 0 !important;\n  top: 50% !important;\n  transform: translateY(-50%) !important;\n  width: 3px !important;\n  height: 0 !important;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\n  border-radius: 0 2px 2px 0 !important;\n  transition: height 0.3s ease !important;\n}\n\n.vertical-submenu .el-menu-item:hover::before {\n  height: 20px !important;\n}\n\n.header-right {\n  flex: 0 0 150px;\n  min-width: 150px;\n  text-align: right;\n}\n\n.user-info {\n  color: #fff;\n  cursor: pointer;\n  padding: 0 15px;\n}\n\n.user-info:hover {\n  color: #ffd04b;\n}\n\n/* 面包屑导航样式 */\n.breadcrumb-header {\n  height: 50px;\n  background-color: #f5f5f5;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n  flex-shrink: 0;\n}\n\n.el-breadcrumb {\n  line-height: 50px;\n}\n\n/* 主内容区域样式 - 新UI风格 */\n.main-content {\n  flex: 1;\n  overflow: auto;\n  background-color: #f5f5f5;\n  padding: 16px;\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\n}\n\n/* 页面内容容器 */\n.main-content .page-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  padding: 24px;\n  margin-bottom: 16px;\n}\n\n/* 页面标题样式 */\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 标签页导航样式 */\n.tab-navigation {\n  margin-bottom: 24px;\n}\n\n.tab-navigation .el-tabs__header {\n  margin: 0;\n}\n\n.tab-navigation .el-tabs__nav-wrap::after {\n  height: 1px;\n  background-color: #e8e8e8;\n}\n\n/* 搜索筛选区域样式 */\n.search-section {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.search-form {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  align-items: center;\n}\n\n.search-form .el-form-item {\n  margin-bottom: 0;\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  margin-left: auto;\n}\n\n/* 表格样式优化 */\n.data-table {\n  margin-top: 16px;\n}\n\n.data-table .el-table {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n}\n\n.data-table .el-table th {\n  background-color: #fafafa;\n  color: #262626;\n  font-weight: 500;\n}\n\n.data-table .el-table td {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 分页样式 */\n.pagination-wrapper {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 16px;\n  padding: 16px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .top-header {\n    flex-direction: column;\n    height: auto;\n    padding: 10px;\n  }\n\n  .header-center {\n    width: 100%;\n    justify-content: flex-start;\n    margin: 10px 0;\n  }\n\n  .top-menu {\n    width: 100%;\n  }\n}\n\n/* 移除原有的侧边栏样式 */\n.size {\n  width: 100%;\n  height: 100%;\n}\n\n.homeRouterView {\n  margin-top: 10px;\n}\n</style>\n"]}]}