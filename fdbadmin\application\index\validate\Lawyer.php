<?php
namespace app\index\validate;

use think\Validate;

class Lawyer extends Validate
{
    protected $rule =   [
     
      // 'uid'=>'require',
      'title'=>'require',
    	'phone'=>'require|checkPhone:请输入正确的手机号码',
    	'age'=>'require',
    	'laywer_card'=>'require',
    	// 'city_id'=>'require',
    	'pic_path'=>'require',
    	'address'=>'require',
    	'zhuanyes'=>'require',
    	'desc'=>'require',
    	'card_path'=>'require',
    	'cate_id'=>'require'
       
    ];
    
    protected $message  =   [
      	'uid.require' => '请先登陆',
      	'title.require'=>'名字不能为空',
      	'age.require' => '请填写执业年限',
      	'laywer_card.require' => '请填写执业证件号',
      	'pic_path.require' => '请上传头像',
      	'address.require' => '请填写工作单位',
      	'zhuanyes.require' => '请选择专业',
      	'cate_id.require' => '请选择类型',
      	'card_path.require' => '请上传执业照片',
      	'desc.require' => '请填写简介',
    ];
    
    protected $scene = [
       
    ];    
  protected  function checkPhone($value,$rule)
	{
	
		if(!preg_match("/^1[345789]\d{9}$/", $value)) return $rule;
		else return true;
			
	}
}