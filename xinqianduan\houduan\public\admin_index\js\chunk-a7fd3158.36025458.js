(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a7fd3158"],{"9c08":function(e,t,l){"use strict";l("af4b")},af4b:function(e,t,l){},dfa5:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",{staticStyle:{width:"600px"}},[t("el-input",{attrs:{placeholder:"请输入内容",size:"mini"},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.searchData()}},slot:"append"})],1)],1),t("el-row",{staticClass:"page-top"},[t("el-button",{attrs:{type:"primary",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v("新增")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"}},[t("el-table-column",{attrs:{prop:"title",label:"标题"}}),t("el-table-column",{attrs:{prop:"price",label:"价格"}}),t("el-table-column",{attrs:{prop:"pic_path",label:"封面"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("img",{staticStyle:{width:"160px",height:"80px"},attrs:{src:l.row.pic_path},on:{click:function(t){return e.showImage(l.row.pic_path)}}})]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"录入时间"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(l.row.id)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(l.$index,l.row.id)}}},[e._v(" 移除 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:e.title+"标题","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"是否免费","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1},model:{value:e.ruleForm.is_free,callback:function(t){e.$set(e.ruleForm,"is_free",t)},expression:"ruleForm.is_free"}},[e._v("是")]),t("el-radio",{attrs:{label:2},model:{value:e.ruleForm.is_free,callback:function(t){e.$set(e.ruleForm,"is_free",t)},expression:"ruleForm.is_free"}},[e._v("否")])],1)]),t("el-form-item",{attrs:{label:"首页热门","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1},model:{value:e.ruleForm.is_hot,callback:function(t){e.$set(e.ruleForm,"is_hot",t)},expression:"ruleForm.is_hot"}},[e._v("是")]),t("el-radio",{attrs:{label:0},model:{value:e.ruleForm.is_hot,callback:function(t){e.$set(e.ruleForm,"is_hot",t)},expression:"ruleForm.is_hot"}},[e._v("否")])],1)]),2==e.ruleForm.is_free?t("el-form-item",{attrs:{label:"价格","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.price,callback:function(t){e.$set(e.ruleForm,"price",t)},expression:"ruleForm.price"}})],1):e._e(),t("el-form-item",{attrs:{label:"封面","label-width":e.formLabelWidth,prop:"pic_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.pic_path,callback:function(t){e.$set(e.ruleForm,"pic_path",t)},expression:"ruleForm.pic_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFile("pic_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.pic_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.pic_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.pic_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.pic_path,"pic_path")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"课程视频","label-width":e.formLabelWidth,prop:"file_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,"file_path",t)},expression:"ruleForm.file_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFile("file_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.file_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,"file_path")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("editor-bar",{attrs:{isClear:e.isClear},on:{change:e.change},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,"content",t)},expression:"ruleForm.content"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},i=[],r=l("0c98"),s={name:"list",components:{EditorBar:r["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/kecheng/",title:"课程",info:{},filed:"",dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],pic_path:[{required:!0,message:"请上传封面",trigger:"blur"}],file_path:[{required:!0,message:"请上传视频",trigger:"blur"}]},formLabelWidth:"120px"}},mounted(){this.getData()},methods:{changeFile(e){this.filed=e,console.log(this.filed)},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:"",is_free:2,file_path:"",pic_path:""},t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success("上传成功"),this.ruleForm[this.filed]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){let t=e.type;if("pic_path"==this.filed){const e=/^image\/(jpeg|png|jpg)$/.test(t);if(!e)return void this.$message.error("上传图片格式不对!")}else if("mp4"==!e.type.split("/")[1]||"qlv"==!e.type.split("/")[1]||"qsv"==!e.type.split("/")[1]||"oga"==!e.type.split("/")[1]||"flv"==!e.type.split("/")[1]||"avi"==!e.type.split("/")[1]||"wmv"==!e.type.split("/")[1]||"rmvb"==!e.type.split("/")[1])return this.$message({showClose:!0,message:"请选择'.mp4,.qlv,.qsv,.oga,.flv,.avi,.wmv,.rmvb'文件",type:"error"}),!1},delImage(e,t){let l=this;l.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(l.ruleForm[t]="",l.$message.success("删除成功!")):l.$message.error(e.msg)})}}},o=s,n=(l("9c08"),l("2877")),u=Object(n["a"])(o,a,i,!1,null,"10b6a3de",null);t["default"]=u.exports}}]);
//# sourceMappingURL=chunk-a7fd3158.36025458.js.map