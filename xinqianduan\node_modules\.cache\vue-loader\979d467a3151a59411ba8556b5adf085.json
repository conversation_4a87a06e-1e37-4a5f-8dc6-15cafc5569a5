{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue?vue&type=style&index=0&id=44ab5769&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1748448502825}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748336490492}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748336507382}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748336500812}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["user.vue"], "names": [], "mappings": ";AAohGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "user.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n    <div class=\"page-wrapper\">\r\n        <div class=\"page-container\">\r\n            <!-- 页面标题 -->\r\n            <div class=\"page-title\">\r\n                {{ this.$router.currentRoute.name }}\r\n                <el-button\r\n                        style=\"float: right\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                        icon=\"el-icon-refresh\"\r\n                >刷新\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 搜索筛选区域 -->\r\n            <div class=\"search-container\">\r\n                <el-form :model=\"search\" class=\"search-form\" label-width=\"80px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户名称\">\r\n                                <el-input\r\n                                    v-model=\"search.nickname\"\r\n                                    placeholder=\"请输入用户名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"手机号码\">\r\n                                <el-input\r\n                                    v-model=\"search.phone\"\r\n                                    placeholder=\"请输入手机号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"公司名称\">\r\n                                <el-input\r\n                                    v-model=\"search.company\"\r\n                                    placeholder=\"请输入公司名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户来源\">\r\n                                <el-select\r\n                                    v-model=\"search.yuangong_id\"\r\n                                    placeholder=\"请选择来源\"\r\n                                    clearable\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                    <el-option label=\"小程序注册\" value=\"小程序注册\"></el-option>\r\n                                    <el-option label=\"后台创建\" value=\"后台创建\"></el-option>\r\n                                    <el-option label=\"直接注册\" value=\"直接注册\"></el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系人\">\r\n                                <el-input\r\n                                    v-model=\"search.linkman\"\r\n                                    placeholder=\"请输入联系人\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系号码\">\r\n                                <el-input\r\n                                    v-model=\"search.linkphone\"\r\n                                    placeholder=\"请输入联系号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                            <el-form-item label=\"注册时间\">\r\n                                <el-date-picker\r\n                                    v-model=\"search.dateRange\"\r\n                                    type=\"daterange\"\r\n                                    range-separator=\"至\"\r\n                                    start-placeholder=\"开始日期\"\r\n                                    end-placeholder=\"结束日期\"\r\n                                    format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                            <el-form-item>\r\n                                <div class=\"search-buttons\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        icon=\"el-icon-search\"\r\n                                        @click=\"searchData()\"\r\n                                        size=\"small\">\r\n                                        搜索\r\n                                    </el-button>\r\n                                    <el-button\r\n                                        icon=\"el-icon-refresh\"\r\n                                        @click=\"resetSearch()\"\r\n                                        size=\"small\">\r\n                                        重置\r\n                                    </el-button>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n\r\n                <!-- 操作按钮区域 -->\r\n                <div class=\"action-buttons\">\r\n                    <el-button\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        icon=\"el-icon-download\"\r\n                        @click=\"exportSelectedData\">\r\n                        {{ selectedUsers.length > 0 ? `导出选中数据 (${selectedUsers.length})` : '导出全部数据' }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\" @click=\"openUpload\">\r\n                        导入用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addUser\">\r\n                        添加用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 数据表格 -->\r\n            <div class=\"data-table\">\r\n                <el-table\r\n                        :data=\"list\"\r\n                        style=\"width: 100%\"\r\n                        v-loading=\"loading\"\r\n                        @sort-change=\"handleSortChange\"\r\n                        @selection-change=\"handleSelectionChange\"\r\n                        :border=\"true\"\r\n                        :header-cell-style=\"{background:'#fafafa',color:'#606266'}\"\r\n                        ref=\"userTable\"\r\n                >\r\n                    <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n                    \r\n                    <!-- 第一字段：用户名称 -->\r\n                    <el-table-column prop=\"nickname\" label=\"用户名称\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"user-name clickable\" @click=\"viewData(scope.row.id)\">\r\n                                {{ scope.row.nickname || '未设置' }}\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第二字段：头像 -->\r\n                    <el-table-column prop=\"\" label=\"头像\" width=\"80\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"avatar-container\" @click=\"viewData(scope.row.id)\">\r\n                                <div v-if=\"scope.row.headimg && scope.row.headimg !== ''\" class=\"avatar-wrapper clickable\">\r\n                                    <img\r\n                                        class=\"user-avatar\"\r\n                                        :src=\"scope.row.headimg\"\r\n                                    />\r\n                                </div>\r\n                                <div v-else class=\"no-avatar clickable\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第三字段：注册手机号码和联系号码 -->\r\n                    <el-table-column label=\"手机号码\" min-width=\"140\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"phone-info\">\r\n                                <div class=\"phone-item clickable\" @click=\"viewData(scope.row.id)\">\r\n                                    <span class=\"phone-label\">注册:</span>\r\n                                    <span class=\"phone-number\">{{ scope.row.phone || '未设置' }}</span>\r\n                                </div>\r\n                                <div class=\"phone-item\" v-if=\"scope.row.linkphone\">\r\n                                    <span class=\"phone-label\">联系:</span>\r\n                                    <span class=\"phone-number\">{{ scope.row.linkphone }}</span>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    \r\n                    <!-- 第四字段：公司名称 -->\r\n                    <el-table-column prop=\"company\" label=\"公司名称\" sortable min-width=\"150\"></el-table-column>\r\n                    \r\n                    <!-- 保留其他字段 -->\r\n                    <el-table-column prop=\"linkman\" label=\"联系人\" sortable min-width=\"100\"></el-table-column>\r\n                    <el-table-column label=\"债务人数量\" min-width=\"100\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" :type=\"getDebtCountType(scope.row.debts)\">\r\n                                {{ getDebtCount(scope.row.debts) }}人\r\n                            </el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务总金额\" min-width=\"120\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"debt-amount\" :class=\"{ 'has-debt': getTotalDebtAmount(scope.row.debts) > 0 }\">\r\n                                ¥{{ formatAmount(getTotalDebtAmount(scope.row.debts)) }}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"end_time\" label=\"到期时间\" min-width=\"120\" sortable></el-table-column>\r\n                    <el-table-column prop=\"create_time\" label=\"注册时间\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.create_time }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最后登录\" min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.last_login_time || '未登录' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"action-buttons-table\">\r\n                                <el-button type=\"primary\" size=\"mini\" @click=\"viewData(scope.row.id)\">\r\n                                    查看\r\n                                </el-button>\r\n                                <el-button type=\"success\" size=\"mini\" @click=\"editData(scope.row.id)\">\r\n                                    编辑\r\n                                </el-button>\r\n                                <el-dropdown trigger=\"click\">\r\n                                    <el-button size=\"mini\">\r\n                                        更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu slot=\"dropdown\">\r\n                                        <el-dropdown-item @click.native=\"order(scope.row)\">制作订单</el-dropdown-item>\r\n                                        <el-dropdown-item v-if=\"is_del\" @click.native=\"delData(scope.$index, scope.row.id)\" style=\"color: #f56c6c;\">移除用户</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n                <div class=\"pagination-info\">\r\n                    <span>共 {{ total }} 条</span>\r\n                    <span>第 {{ page }} 页</span>\r\n                </div>\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"size\"\r\n                        :current-page=\"page\"\r\n                        layout=\"sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                        background\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 用户详情抽屉 -->\r\n        <el-drawer\r\n            title=\"用户详情\"\r\n            :visible.sync=\"drawerViewVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleTabSelect\">\r\n                        <el-menu-item index=\"customer\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>客户信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"member\">\r\n                            <i class=\"el-icon-medal\"></i>\r\n                            <span>会员信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"debts\">\r\n                            <i class=\"el-icon-document\"></i>\r\n                            <span>债务人信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"attachments\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>附件信息</span>\r\n                        </el-menu-item>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 编辑模式切换按钮 -->\r\n                    <div class=\"edit-mode-toggle\" v-if=\"activeTab === 'customer'\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            :icon=\"isEditMode ? 'el-icon-view' : 'el-icon-edit'\"\r\n                            @click=\"toggleEditMode\">\r\n                            {{ isEditMode ? '查看模式' : '编辑模式' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"success\"\r\n                            icon=\"el-icon-check\"\r\n                            @click=\"saveUserData\">\r\n                            保存\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"info\"\r\n                            icon=\"el-icon-close\"\r\n                            @click=\"cancelEdit\">\r\n                            取消\r\n                        </el-button>\r\n                    </div>\r\n\r\n                    <!-- 客户信息标签页 -->\r\n                    <div v-if=\"activeTab === 'customer'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                客户信息\r\n                            </div>\r\n                    <!-- 查看模式 -->\r\n                    <el-descriptions v-if=\"!isEditMode\" :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            currentUserInfo.company || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            currentUserInfo.phone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户名称\">{{\r\n                            currentUserInfo.nickname || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            currentUserInfo.linkman || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系电话\">{{\r\n                            currentUserInfo.linkphone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ currentUserInfo.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"立案专员\">{{\r\n                            currentUserInfo.lian_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"调解员\">{{\r\n                            currentUserInfo.tiaojie_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"法务专员\">{{\r\n                            currentUserInfo.fawu_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"合同上传专员\">{{\r\n                            currentUserInfo.htsczy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"律师\">{{\r\n                            currentUserInfo.ls_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"业务员\">{{\r\n                            currentUserInfo.ywy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"currentUserInfo.headimg && currentUserInfo.headimg !== ''\"\r\n                                     :src=\"currentUserInfo.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(currentUserInfo.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"注册时间\" :span=\"2\">{{\r\n                            currentUserInfo.create_time || '未知'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"最后登录\" :span=\"2\">{{\r\n                            currentUserInfo.last_login_time || '从未登录'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"会员到期\" :span=\"2\">{{\r\n                            currentUserInfo.end_time || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n\r\n                    <!-- 编辑模式 -->\r\n                    <el-form v-if=\"isEditMode\" :model=\"editForm\" :rules=\"rules\" ref=\"editForm\" label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"公司名称\" prop=\"company\">\r\n                                    <el-input v-model=\"editForm.company\" placeholder=\"请输入公司名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"手机号\" prop=\"phone\">\r\n                                    <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号\" disabled></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"用户名称\" prop=\"nickname\">\r\n                                    <el-input v-model=\"editForm.nickname\" placeholder=\"请输入用户名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                                    <el-input v-model=\"editForm.linkman\" placeholder=\"请输入联系人\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"linkphone\">\r\n                                    <el-input v-model=\"editForm.linkphone\" placeholder=\"请输入联系电话\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"登录密码\" prop=\"password\">\r\n                                    <el-input v-model=\"editForm.password\" placeholder=\"请输入新密码（留空不修改）\" type=\"password\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"调解员\" prop=\"tiaojie_id\">\r\n                                    <el-select v-model=\"editForm.tiaojie_id\" placeholder=\"请选择调解员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in tiaojies\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"法务专员\" prop=\"fawu_id\">\r\n                                    <el-select v-model=\"editForm.fawu_id\" placeholder=\"请选择法务专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in fawus\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"立案专员\" prop=\"lian_id\">\r\n                                    <el-select v-model=\"editForm.lian_id\" placeholder=\"请选择立案专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in lians\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\">\r\n                                    <el-select v-model=\"editForm.htsczy_id\" placeholder=\"请选择合同上传专用\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in htsczy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"律师\" prop=\"ls_id\">\r\n                                    <el-select v-model=\"editForm.ls_id\" placeholder=\"请选择律师\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ls\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"业务员\" prop=\"ywy_id\">\r\n                                    <el-select v-model=\"editForm.ywy_id\" placeholder=\"请选择业务员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ywy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"开始时间\" prop=\"start_time\">\r\n                                    <el-date-picker\r\n                                        v-model=\"editForm.start_time\"\r\n                                        type=\"date\"\r\n                                        format=\"yyyy-MM-dd\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        placeholder=\"选择开始时间\">\r\n                                    </el-date-picker>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"会员年限\" prop=\"year\">\r\n                                    <el-input-number\r\n                                        v-model=\"editForm.year\"\r\n                                        :min=\"0\"\r\n                                        :max=\"99\"\r\n                                        placeholder=\"请输入会员年限\">\r\n                                    </el-input-number>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-form-item label=\"头像\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"editForm.headimg && editForm.headimg !== ''\"\r\n                                     :src=\"editForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(editForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 会员信息标签页 -->\r\n                    <div v-if=\"activeTab === 'member'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-medal\"></i>\r\n                                会员信息\r\n                            </div>\r\n                            <el-descriptions :column=\"2\" border>\r\n                                <el-descriptions-item label=\"开始时间\">{{\r\n                                    currentUserInfo.start_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员年限\">{{\r\n                                    currentUserInfo.year || 0\r\n                                    }}年\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"注册时间\">{{\r\n                                    currentUserInfo.create_time || '未知'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"最后登录\">{{\r\n                                    currentUserInfo.last_login_time || '从未登录'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员到期\">{{\r\n                                    currentUserInfo.end_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 债务人信息标签页 -->\r\n                    <div v-if=\"activeTab === 'debts'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                债务人信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addDebt\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加债务人\r\n                                </el-button>\r\n                            </div>\r\n                            <el-table\r\n                                :data=\"currentUserInfo.debts || []\"\r\n                                style=\"width: 100%\"\r\n                                size=\"small\"\r\n                                v-if=\"currentUserInfo.debts && currentUserInfo.debts.length > 0\">\r\n                                <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"130\"></el-table-column>\r\n                                <el-table-column prop=\"money\" label=\"债务金额（元）\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-tag :type=\"scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info'\" size=\"small\">\r\n                                            {{ scope.row.status }}\r\n                                        </el-tag>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column label=\"操作\" width=\"150\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" size=\"mini\" @click=\"editDebt(scope.row, scope.$index)\">编辑</el-button>\r\n                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteDebt(scope.$index)\">删除</el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                            <div v-else class=\"no-data\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>暂无债务人信息</span>\r\n                                <br>\r\n                                <el-button type=\"primary\" size=\"small\" @click=\"addDebt\" style=\"margin-top: 10px;\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加第一个债务人\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 附件信息标签页 -->\r\n                    <div v-if=\"activeTab === 'attachments'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder-opened\"></i>\r\n                                附件信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addAttachment\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传附件\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"attachment-grid\">\r\n                                <!-- 身份证照片 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">身份证照片</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.idCard && currentUserInfo.attachments.idCard.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.idCard\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('idCard', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-picture\"></i>\r\n                                            <span>暂无身份证照片</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadIdCard\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传身份证\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 营业执照 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">营业执照</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.license && currentUserInfo.attachments.license.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.license\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('license', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-document\"></i>\r\n                                            <span>暂无营业执照</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadLicense\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传营业执照\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 其他附件 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">其他附件</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.others && currentUserInfo.attachments.others.length > 0\" class=\"file-list\">\r\n                                            <div v-for=\"(file, index) in currentUserInfo.attachments.others\" :key=\"index\" class=\"file-item\">\r\n                                                <div class=\"file-icon\">\r\n                                                    <i :class=\"getFileIcon(file.type)\" class=\"file-type-icon\"></i>\r\n                                                </div>\r\n                                                <div class=\"file-info\">\r\n                                                    <div class=\"file-name\">{{ file.name }}</div>\r\n                                                    <div class=\"file-meta\">\r\n                                                        <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                                                        <span class=\"upload-time\">{{ file.uploadTime }}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div class=\"file-actions\">\r\n                                                    <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(file)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                    <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('others', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-folder\"></i>\r\n                                            <span>暂无其他附件</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadOthers\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传其他附件\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n\r\n        <!-- 编辑用户抽屉 -->\r\n        <el-drawer\r\n            title=\"编辑用户\"\r\n            :visible.sync=\"drawerEditVisible\"\r\n            direction=\"rtl\"\r\n            size=\"50%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content\">\r\n                <!-- 客户信息展示 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                        客户信息\r\n                    </div>\r\n                    <el-descriptions :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            ruleForm.company\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            ruleForm.phone\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"名称\">{{\r\n                            ruleForm.nickname\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            ruleForm.linkman\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"ruleForm.headimg && ruleForm.headimg !== ''\"\r\n                                     :src=\"ruleForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(ruleForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\" :span=\"2\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ ruleForm.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 信息编辑表单 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-edit\"></i>\r\n                        信息编辑\r\n                    </div>\r\n                    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n                    </el-form>\r\n\r\n                    <!-- 保存按钮 -->\r\n                    <div class=\"drawer-footer\">\r\n                        <el-button @click=\"drawerEditVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveData()\">保 存</el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog :visible.sync=\"dialogVisible\" width=\"50%\" center>\r\n            <img :src=\"show_image\" style=\"width: 100%; height: auto;\" />\r\n        </el-dialog>\r\n\r\n        <!-- 债务人表单对话框 -->\r\n        <el-dialog :title=\"debtDialogTitle\" :visible.sync=\"debtDialogVisible\" width=\"500px\" @close=\"closeDebtDialog\">\r\n            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"100px\">\r\n                <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                    <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                    <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务金额\" prop=\"money\">\r\n                    <el-input-number\r\n                        v-model=\"debtForm.money\"\r\n                        :min=\"0\"\r\n                        :precision=\"2\"\r\n                        placeholder=\"请输入债务金额\"\r\n                        style=\"width: 100%\">\r\n                    </el-input-number>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                        <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                        <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                        <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeDebtDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveDebt\">确定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 导入用户对话框 -->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"600px\" @close=\"closeUploadDialog\">\r\n            <div class=\"upload-container\">\r\n                <div class=\"upload-tips\">\r\n                    <el-alert\r\n                        title=\"导入说明\"\r\n                        type=\"info\"\r\n                        :closable=\"false\"\r\n                        show-icon>\r\n                        <div slot=\"description\">\r\n                            <p>1. 请先下载导入模板，按照模板格式填写用户信息</p>\r\n                            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n                            <p>3. 单次最多导入1000条用户数据</p>\r\n                            <p>4. 手机号码为必填项，且不能重复</p>\r\n                        </div>\r\n                    </el-alert>\r\n                </div>\r\n\r\n                <div class=\"upload-actions\">\r\n                    <el-button type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n\r\n                <div class=\"upload-area\">\r\n                    <el-upload\r\n                        ref=\"upload\"\r\n                        :action=\"uploadAction\"\r\n                        :data=\"uploadData\"\r\n                        :on-success=\"handleUploadSuccess\"\r\n                        :on-error=\"handleUploadError\"\r\n                        :before-upload=\"beforeUpload\"\r\n                        :auto-upload=\"false\"\r\n                        :limit=\"1\"\r\n                        :file-list=\"fileList\"\r\n                        accept=\".xls,.xlsx\"\r\n                        drag>\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n                        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xls/xlsx文件，且不超过10MB</div>\r\n                    </el-upload>\r\n                </div>\r\n\r\n                <div class=\"upload-options\">\r\n                    <el-checkbox v-model=\"uploadData.review\">导入前预览数据</el-checkbox>\r\n                </div>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeUploadDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">\r\n                    {{ submitOrderLoading2 ? '导入中...' : '开始导入' }}\r\n                </el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 债务人编辑抽屉 -->\r\n        <el-drawer\r\n            title=\"债务人管理\"\r\n            :visible.sync=\"debtDrawerVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDebtDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeDebtTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleDebtTabSelect\">\r\n                        <el-menu-item index=\"details\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>债务人详情</span>\r\n                        </el-menu-item>\r\n                        <el-submenu index=\"evidence\">\r\n                            <template slot=\"title\">\r\n                                <i class=\"el-icon-folder\"></i>\r\n                                <span>证据</span>\r\n                            </template>\r\n                            <el-menu-item index=\"evidence-all\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>全部</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-video\">\r\n                                <i class=\"el-icon-video-camera\"></i>\r\n                                <span>视频</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-image\">\r\n                                <i class=\"el-icon-picture\"></i>\r\n                                <span>图片</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-audio\">\r\n                                <i class=\"el-icon-microphone\"></i>\r\n                                <span>语音</span>\r\n                            </el-menu-item>\r\n                            <el-menu-item index=\"evidence-document\">\r\n                                <i class=\"el-icon-document-copy\"></i>\r\n                                <span>文档</span>\r\n                            </el-menu-item>\r\n                        </el-submenu>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 债务人详情标签页 -->\r\n                    <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                {{ debtDialogTitle }}\r\n                            </div>\r\n                            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"120px\">\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                                            <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                                            <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"身份证号码\" prop=\"idcard\">\r\n                                            <el-input v-model=\"debtForm.idcard\" placeholder=\"请输入身份证号码\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务金额\" prop=\"money\">\r\n                                            <el-input-number\r\n                                                v-model=\"debtForm.money\"\r\n                                                :min=\"0\"\r\n                                                :precision=\"2\"\r\n                                                placeholder=\"请输入债务金额\"\r\n                                                style=\"width: 100%\">\r\n                                            </el-input-number>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-row :gutter=\"20\">\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"状态\" prop=\"status\">\r\n                                            <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                                                <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                                                <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                                                <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                                            </el-select>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                    <el-col :span=\"12\">\r\n                                        <el-form-item label=\"债务人地址\" prop=\"address\">\r\n                                            <el-input v-model=\"debtForm.address\" placeholder=\"请输入债务人地址\"></el-input>\r\n                                        </el-form-item>\r\n                                    </el-col>\r\n                                </el-row>\r\n                                <el-form-item label=\"案由描述\" prop=\"case_desc\">\r\n                                    <el-input\r\n                                        v-model=\"debtForm.case_desc\"\r\n                                        type=\"textarea\"\r\n                                        :rows=\"4\"\r\n                                        placeholder=\"请输入案由描述\">\r\n                                    </el-input>\r\n                                </el-form-item>\r\n                            </el-form>\r\n                            \r\n                            <div class=\"drawer-footer\">\r\n                                <el-button @click=\"handleDebtDrawerClose\">取消</el-button>\r\n                                <el-button type=\"primary\" @click=\"saveDebt\">保存</el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 证据管理标签页 -->\r\n                    <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder\"></i>\r\n                                {{ getEvidenceTitle() }}\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传证据\r\n                                </el-button>\r\n                            </div>\r\n                            \r\n                            <!-- 证据列表 -->\r\n                            <div class=\"evidence-container\">\r\n                                <div v-if=\"getFilteredEvidence().length > 0\" class=\"evidence-grid\">\r\n                                    <div v-for=\"(evidence, index) in getFilteredEvidence()\" :key=\"index\" class=\"evidence-item\">\r\n                                        <div class=\"evidence-preview\">\r\n                                            <!-- 图片预览 -->\r\n                                            <div v-if=\"evidence.type === 'image'\" class=\"image-preview\">\r\n                                                <img :src=\"evidence.url\" @click=\"showImage(evidence.url)\" class=\"evidence-image\">\r\n                                            </div>\r\n                                            <!-- 视频预览 -->\r\n                                            <div v-else-if=\"evidence.type === 'video'\" class=\"video-preview\">\r\n                                                <video :src=\"evidence.url\" controls class=\"evidence-video\"></video>\r\n                                            </div>\r\n                                            <!-- 音频预览 -->\r\n                                            <div v-else-if=\"evidence.type === 'audio'\" class=\"audio-preview\">\r\n                                                <div class=\"audio-icon\">\r\n                                                    <i class=\"el-icon-microphone\"></i>\r\n                                                </div>\r\n                                                <audio :src=\"evidence.url\" controls class=\"evidence-audio\"></audio>\r\n                                            </div>\r\n                                            <!-- 文档预览 -->\r\n                                            <div v-else class=\"document-preview\">\r\n                                                <div class=\"document-icon\">\r\n                                                    <i :class=\"getFileIcon(evidence.type)\"></i>\r\n                                                </div>\r\n                                                <div class=\"document-name\">{{ evidence.name }}</div>\r\n                                            </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"evidence-info\">\r\n                                            <div class=\"evidence-name\">{{ evidence.name }}</div>\r\n                                            <div class=\"evidence-meta\">\r\n                                                <span class=\"evidence-size\">{{ formatFileSize(evidence.size) }}</span>\r\n                                                <span class=\"evidence-time\">{{ evidence.uploadTime }}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"evidence-actions\">\r\n                                            <el-button type=\"primary\" size=\"mini\" @click=\"downloadEvidence(evidence)\" icon=\"el-icon-download\">下载</el-button>\r\n                                            <el-button type=\"danger\" size=\"mini\" @click=\"deleteEvidence(index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div v-else class=\"no-evidence\">\r\n                                    <i class=\"el-icon-folder-opened\"></i>\r\n                                    <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                                    <br>\r\n                                    <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                                        <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                                    </el-button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                fileList: [], // 上传文件列表\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                currentUserInfo: {},\r\n                search: {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                selectedUsers: [], // 选中的用户列表\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                drawerViewVisible: false,\r\n                drawerEditVisible: false,\r\n                isEditMode: false,\r\n                editForm: {},\r\n                originalUserInfo: {},\r\n                activeTab: 'customer',\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                // 债务人表单相关\r\n                debtDialogVisible: false,\r\n                debtDialogTitle: '添加债务人',\r\n                isEditingDebt: false,\r\n                editingDebtIndex: -1,\r\n                debtForm: {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                },\r\n                debtRules: {\r\n                    name: [\r\n                        { required: true, message: '请输入债务人姓名', trigger: 'blur' }\r\n                    ],\r\n                    tel: [\r\n                        { required: true, message: '请输入债务人电话', trigger: 'blur' },\r\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n                    ],\r\n                    money: [\r\n                        { required: true, message: '请输入债务金额', trigger: 'blur' }\r\n                    ],\r\n                    status: [\r\n                        { required: true, message: '请选择状态', trigger: 'change' }\r\n                    ]\r\n                },\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                debtDrawerVisible: false,\r\n                activeDebtTab: 'details',\r\n            };\r\n        },\r\n        mounted() {\r\n            // 使用测试数据，注释掉API调用\r\n            // this.getData();\r\n            // 添加测试数据\r\n            this.addTestData();\r\n        },\r\n        methods: {\r\n            // 获取原始测试数据\r\n            getOriginalTestData() {\r\n                return [\r\n                    {\r\n                        id: 1,\r\n                        phone: '13800138001',\r\n                        nickname: '张三',\r\n                        company: '北京科技有限公司',\r\n                        linkman: '张三',\r\n                        linkphone: '13800138001',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-12-31',\r\n                        create_time: '2023-01-15 10:30:00',\r\n                        last_login_time: '2024-01-20 15:45:00',\r\n                        headimg: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n                        license: '',\r\n                        start_time: '2023-01-15',\r\n                        year: 2,\r\n                        // 专员信息\r\n                        lian_name: '陈专员',\r\n                        tiaojie_name: '朱调解',\r\n                        fawu_name: '严法务',\r\n                        htsczy_name: '合同专员',\r\n                        ls_name: '王律师',\r\n                        ywy_name: '业务员张三',\r\n                        debts: [\r\n                            {\r\n                                name: '王某某',\r\n                                tel: '13912345678',\r\n                                money: '50000',\r\n                                status: '处理中'\r\n                            },\r\n                            {\r\n                                name: '李某某',\r\n                                tel: '13987654321',\r\n                                money: '30000',\r\n                                status: '已完成'\r\n                            }\r\n                        ],\r\n                        attachments: {\r\n                            idCard: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '身份证正面.jpg',\r\n                                    uploadTime: '2024-01-15 10:30:00'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '身份证反面.jpg',\r\n                                    uploadTime: '2024-01-15 10:31:00'\r\n                                }\r\n                            ],\r\n                            license: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                                    name: '营业执照.jpg',\r\n                                    uploadTime: '2024-01-15 10:32:00'\r\n                                }\r\n                            ],\r\n                            image: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '证据图片1.jpg',\r\n                                    type: 'image',\r\n                                    size: 1024000,\r\n                                    uploadTime: '2024-01-20 14:30:00'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '证据图片2.png',\r\n                                    type: 'image',\r\n                                    size: 2048000,\r\n                                    uploadTime: '2024-01-20 14:35:00'\r\n                                }\r\n                            ],\r\n                            video: [\r\n                                {\r\n                                    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\r\n                                    name: '证据视频1.mp4',\r\n                                    type: 'video',\r\n                                    size: 10485760,\r\n                                    uploadTime: '2024-01-21 09:15:00'\r\n                                }\r\n                            ],\r\n                            audio: [\r\n                                {\r\n                                    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',\r\n                                    name: '录音证据1.wav',\r\n                                    type: 'audio',\r\n                                    size: 5242880,\r\n                                    uploadTime: '2024-01-21 10:20:00'\r\n                                }\r\n                            ],\r\n                            document: [\r\n                                {\r\n                                    name: '合同文件.pdf',\r\n                                    url: '/files/contract.pdf',\r\n                                    type: 'document',\r\n                                    size: 3145728,\r\n                                    uploadTime: '2024-01-22 11:00:00'\r\n                                },\r\n                                {\r\n                                    name: '借条扫描件.pdf',\r\n                                    url: '/files/iou.pdf',\r\n                                    type: 'document',\r\n                                    size: 1572864,\r\n                                    uploadTime: '2024-01-22 11:15:00'\r\n                                }\r\n                            ],\r\n                            others: [\r\n                                {\r\n                                    name: '其他证据.txt',\r\n                                    url: '/files/other.txt',\r\n                                    type: 'document',\r\n                                    size: 1024,\r\n                                    uploadTime: '2024-01-22 12:00:00'\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        phone: '13900139002',\r\n                        nickname: '李四',\r\n                        company: '上海贸易公司',\r\n                        linkman: '李四',\r\n                        linkphone: '13900139002',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-06-30',\r\n                        create_time: '2023-02-20 14:20:00',\r\n                        last_login_time: '2024-01-18 09:15:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-02-20',\r\n                        year: 1,\r\n                        // 专员信息\r\n                        lian_name: '李专员',\r\n                        tiaojie_name: '调解员王五',\r\n                        fawu_name: '法务李四',\r\n                        htsczy_name: '合同专员B',\r\n                        ls_name: '律师张三',\r\n                        ywy_name: '业务员李四',\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13811112222',\r\n                                money: '80000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        phone: '13700137003',\r\n                        nickname: '王五',\r\n                        company: '深圳创新科技',\r\n                        linkman: '王五',\r\n                        linkphone: '13700137003',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2025-03-15',\r\n                        create_time: '2023-03-10 16:40:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                        license: '',\r\n                        start_time: '2023-03-10',\r\n                        year: 2,\r\n                        debts: [\r\n                            {\r\n                                name: '陈某某',\r\n                                tel: '13765432109',\r\n                                money: '80000',\r\n                                status: '待处理'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 4,\r\n                        phone: '13600136004',\r\n                        nickname: '赵六',\r\n                        company: '广州物流集团',\r\n                        linkman: '赵六',\r\n                        linkphone: '13600136004',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-09-20',\r\n                        create_time: '2023-04-05 11:30:00',\r\n                        last_login_time: '2024-01-19 14:22:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-04-05',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 5,\r\n                        phone: '13500135005',\r\n                        nickname: '孙七',\r\n                        company: '杭州电商有限公司',\r\n                        linkman: '孙七',\r\n                        linkphone: '13500135005',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-11-10',\r\n                        create_time: '2023-05-12 09:15:00',\r\n                        last_login_time: '2024-01-21 16:30:00',\r\n                        headimg: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                        license: '',\r\n                        start_time: '2023-05-12',\r\n                        year: 1,\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13654321098',\r\n                                money: '25000',\r\n                                status: '已完成'\r\n                            },\r\n                            {\r\n                                name: '钱某某',\r\n                                tel: '13543210987',\r\n                                money: '15000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 6,\r\n                        phone: '13400134006',\r\n                        nickname: '周八',\r\n                        company: '成都软件开发',\r\n                        linkman: '周八',\r\n                        linkphone: '13400134006',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-08-15',\r\n                        create_time: '2023-06-18 13:25:00',\r\n                        last_login_time: '2024-01-22 10:12:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-06-18',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 7,\r\n                        phone: '13300133007',\r\n                        nickname: '吴九',\r\n                        company: '武汉贸易有限公司',\r\n                        linkman: '吴九',\r\n                        linkphone: '13300133007',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-10-30',\r\n                        create_time: '2023-07-22 15:45:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                        license: '',\r\n                        start_time: '2023-07-22',\r\n                        year: 1,\r\n                        debts: []\r\n                    }\r\n                ];\r\n            },\r\n            addTestData() {\r\n                // 添加测试数据\r\n                this.list = this.getOriginalTestData();\r\n                this.total = this.list.length;\r\n                this.loading = false;\r\n            },\r\n            // 过滤测试数据（模拟搜索功能）\r\n            filterTestData() {\r\n                this.loading = true;\r\n\r\n                // 获取原始测试数据\r\n                const originalData = this.getOriginalTestData();\r\n                let filteredData = [...originalData];\r\n\r\n                // 根据搜索条件过滤数据\r\n                if (this.search.nickname) {\r\n                    const nickname = this.search.nickname.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.nickname && user.nickname.toLowerCase().includes(nickname)\r\n                    );\r\n                }\r\n\r\n                if (this.search.phone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.phone && user.phone.includes(this.search.phone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkman) {\r\n                    const linkman = this.search.linkman.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkman && user.linkman.toLowerCase().includes(linkman)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkphone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkphone && user.linkphone.includes(this.search.linkphone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.company) {\r\n                    const company = this.search.company.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.company && user.company.toLowerCase().includes(company)\r\n                    );\r\n                }\r\n\r\n                if (this.search.yuangong_id) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.yuangong_id === this.search.yuangong_id\r\n                    );\r\n                }\r\n\r\n                // 注册时间范围过滤\r\n                if (this.search.dateRange && this.search.dateRange.length === 2) {\r\n                    const startDate = new Date(this.search.dateRange[0]);\r\n                    const endDate = new Date(this.search.dateRange[1]);\r\n                    filteredData = filteredData.filter(user => {\r\n                        if (user.create_time) {\r\n                            const createDate = new Date(user.create_time.split(' ')[0]);\r\n                            return createDate >= startDate && createDate <= endDate;\r\n                        }\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                // 更新列表和总数\r\n                this.list = filteredData;\r\n                this.total = filteredData.length;\r\n                this.loading = false;\r\n\r\n                // 显示搜索结果提示\r\n                const hasSearchCondition = this.search.nickname || this.search.phone || this.search.linkman ||\r\n                                         this.search.linkphone || this.search.company || this.search.yuangong_id ||\r\n                                         (this.search.dateRange && this.search.dateRange.length === 2);\r\n\r\n                if (hasSearchCondition) {\r\n                    this.$message.success(`搜索完成，找到 ${filteredData.length} 条匹配记录`);\r\n                }\r\n            },\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                console.log('viewData called with id:', id);\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                    // 从测试数据中找到对应的用户信息\r\n                    this.currentUserInfo = this.list.find(user => user.id === id) || {};\r\n                    console.log('Found user info:', this.currentUserInfo);\r\n                    // 重置编辑模式\r\n                    this.isEditMode = false;\r\n                    this.editForm = {};\r\n                    this.originalUserInfo = {};\r\n                    // 重置到客户信息标签页\r\n                    this.activeTab = 'customer';\r\n                }\r\n                _this.drawerViewVisible = true;\r\n                console.log('Drawer should be visible:', _this.drawerViewVisible);\r\n            },\r\n            handleTabSelect(key) {\r\n                this.activeTab = key;\r\n                // 如果切换到其他标签页，退出编辑模式\r\n                if (key !== 'customer') {\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            toggleEditMode() {\r\n                if (!this.isEditMode) {\r\n                    // 进入编辑模式\r\n                    this.isEditMode = true;\r\n                    // 保存原始数据用于取消时恢复\r\n                    this.originalUserInfo = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 复制当前用户信息到编辑表单\r\n                    this.editForm = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 获取员工数据用于下拉选择\r\n                    this.getYuangongs();\r\n                } else {\r\n                    // 退出编辑模式\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            cancelEdit() {\r\n                // 恢复原始数据\r\n                this.currentUserInfo = JSON.parse(JSON.stringify(this.originalUserInfo));\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.$message.info('已取消编辑');\r\n            },\r\n            saveUserData() {\r\n                // 验证表单\r\n                this.$refs.editForm.validate((valid) => {\r\n                    if (valid) {\r\n                        // 更新当前用户信息\r\n                        this.currentUserInfo = JSON.parse(JSON.stringify(this.editForm));\r\n\r\n                        // 更新列表中的数据\r\n                        const index = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1, this.currentUserInfo);\r\n                        }\r\n\r\n                        // 退出编辑模式\r\n                        this.isEditMode = false;\r\n                        this.editForm = {};\r\n\r\n                        this.$message.success('保存成功！');\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n                _this.drawerEditVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            handleDrawerClose() {\r\n                this.drawerViewVisible = false;\r\n                this.drawerEditVisible = false;\r\n                // 重置编辑模式\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.originalUserInfo = {};\r\n                // 重置标签页\r\n                this.activeTab = 'customer';\r\n            },\r\n            // 债务人管理方法\r\n            addDebt() {\r\n                this.debtDrawerVisible = true;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                };\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.isEditingDebt = false;\r\n                this.activeDebtTab = 'details';\r\n            },\r\n            editDebt(debt, index) {\r\n                this.debtDrawerVisible = true;\r\n                this.debtForm = {\r\n                    name: debt.name,\r\n                    tel: debt.tel,\r\n                    money: parseFloat(debt.money),\r\n                    status: debt.status,\r\n                    idcard: debt.idcard || '',\r\n                    address: debt.address || '',\r\n                    case_desc: debt.case_desc || ''\r\n                };\r\n                this.debtDialogTitle = '编辑债务人';\r\n                this.isEditingDebt = true;\r\n                this.editingDebtIndex = index;\r\n                this.activeDebtTab = 'details';\r\n            },\r\n            saveDebt() {\r\n                this.$refs.debtForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const debtData = {\r\n                            name: this.debtForm.name,\r\n                            tel: this.debtForm.tel,\r\n                            money: this.debtForm.money.toString(),\r\n                            status: this.debtForm.status,\r\n                            idcard: this.debtForm.idcard,\r\n                            address: this.debtForm.address,\r\n                            case_desc: this.debtForm.case_desc\r\n                        };\r\n\r\n                        if (this.isEditingDebt) {\r\n                            // 编辑模式\r\n                            this.currentUserInfo.debts[this.editingDebtIndex] = debtData;\r\n                            this.$message.success('债务人信息修改成功！');\r\n                        } else {\r\n                            // 添加模式\r\n                            if (!this.currentUserInfo.debts) {\r\n                                this.currentUserInfo.debts = [];\r\n                            }\r\n                            this.currentUserInfo.debts.push(debtData);\r\n                            this.$message.success('债务人添加成功！');\r\n                        }\r\n\r\n                        // 更新主列表中的数据\r\n                        const userIndex = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (userIndex !== -1) {\r\n                            this.list[userIndex].debts = [...this.currentUserInfo.debts];\r\n                        }\r\n\r\n                        this.handleDebtDrawerClose();\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleDebtDrawerClose() {\r\n                this.debtDrawerVisible = false;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理',\r\n                    idcard: '',\r\n                    address: '',\r\n                    case_desc: ''\r\n                };\r\n                this.isEditingDebt = false;\r\n                this.editingDebtIndex = -1;\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.activeDebtTab = 'details';\r\n                // 清除表单验证\r\n                this.$nextTick(() => {\r\n                    if (this.$refs.debtForm) {\r\n                        this.$refs.debtForm.clearValidate();\r\n                    }\r\n                });\r\n            },\r\n            deleteDebt(index) {\r\n                this.$confirm('确定要删除这个债务人吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.currentUserInfo.debts.splice(index, 1);\r\n                    this.$message.success('删除成功！');\r\n                });\r\n            },\r\n            // 附件管理方法\r\n            addAttachment() {\r\n                this.$message.info('请选择具体的附件类型进行上传');\r\n            },\r\n            uploadIdCard() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'idCard', '身份证照片');\r\n                });\r\n            },\r\n            uploadLicense() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'license', '营业执照');\r\n                });\r\n            },\r\n            uploadOthers() {\r\n                this.createFileInput('*', (files) => {\r\n                    this.handleFileUpload(files, 'others', '其他附件');\r\n                });\r\n            },\r\n            // 创建文件选择器\r\n            createFileInput(accept, callback) {\r\n                const input = document.createElement('input');\r\n                input.type = 'file';\r\n                input.accept = accept;\r\n                input.multiple = true;\r\n                input.style.display = 'none';\r\n\r\n                input.onchange = (e) => {\r\n                    const files = Array.from(e.target.files);\r\n                    if (files.length > 0) {\r\n                        callback(files);\r\n                    }\r\n                    document.body.removeChild(input);\r\n                };\r\n\r\n                document.body.appendChild(input);\r\n                input.click();\r\n            },\r\n            // 处理文件上传\r\n            handleFileUpload(files, type, typeName) {\r\n                if (!files || files.length === 0) {\r\n                    this.$message.warning('请选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                // 验证文件\r\n                for (let file of files) {\r\n                    if (type !== 'others' && !file.type.startsWith('image/')) {\r\n                        this.$message.error(`${typeName}只能上传图片文件`);\r\n                        return;\r\n                    }\r\n                    if (file.size > 10 * 1024 * 1024) { // 10MB限制\r\n                        this.$message.error(`文件 ${file.name} 大小超过10MB限制`);\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 初始化附件数据结构\r\n                if (!this.currentUserInfo.attachments) {\r\n                    this.currentUserInfo.attachments = {};\r\n                }\r\n                if (!this.currentUserInfo.attachments[type]) {\r\n                    this.currentUserInfo.attachments[type] = [];\r\n                }\r\n\r\n                // 模拟上传过程\r\n                this.$message.info(`正在上传 ${files.length} 个文件...`);\r\n\r\n                files.forEach((file, index) => {\r\n                    // 创建文件预览URL\r\n                    const fileUrl = URL.createObjectURL(file);\r\n\r\n                    // 模拟上传延迟\r\n                    setTimeout(() => {\r\n                        const fileData = {\r\n                            name: file.name,\r\n                            url: fileUrl,\r\n                            size: file.size,\r\n                            type: file.type,\r\n                            uploadTime: new Date().toLocaleString()\r\n                        };\r\n\r\n                        this.currentUserInfo.attachments[type].push(fileData);\r\n\r\n                        if (index === files.length - 1) {\r\n                            this.$message.success(`${typeName}上传完成！共上传 ${files.length} 个文件`);\r\n                        }\r\n                    }, (index + 1) * 500); // 模拟上传时间\r\n                });\r\n            },\r\n            deleteAttachment(type, index) {\r\n                this.$confirm('确定要删除这个附件吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    if (this.currentUserInfo.attachments[type]) {\r\n                        // 释放预览URL\r\n                        const file = this.currentUserInfo.attachments[type][index];\r\n                        if (file && file.url && file.url.startsWith('blob:')) {\r\n                            URL.revokeObjectURL(file.url);\r\n                        }\r\n\r\n                        this.currentUserInfo.attachments[type].splice(index, 1);\r\n                        this.$message.success('删除成功！');\r\n                    }\r\n                });\r\n            },\r\n            downloadFile(file) {\r\n                if (!file || !file.url) {\r\n                    this.$message.error('文件链接无效');\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    // 创建下载链接\r\n                    const link = document.createElement('a');\r\n                    link.href = file.url;\r\n                    link.download = file.name || '附件';\r\n                    link.style.display = 'none';\r\n\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n\r\n                    this.$message.success(`开始下载: ${file.name}`);\r\n                } catch (error) {\r\n                    this.$message.error('下载失败，请重试');\r\n                    console.error('下载错误:', error);\r\n                }\r\n            },\r\n            // 文件相关辅助方法\r\n            getFileIcon(fileType) {\r\n                if (!fileType) return 'el-icon-document';\r\n\r\n                if (fileType.startsWith('image/')) return 'el-icon-picture';\r\n                if (fileType.includes('pdf')) return 'el-icon-document';\r\n                if (fileType.includes('word') || fileType.includes('doc')) return 'el-icon-document';\r\n                if (fileType.includes('excel') || fileType.includes('sheet')) return 'el-icon-s-grid';\r\n                if (fileType.includes('zip') || fileType.includes('rar')) return 'el-icon-folder-opened';\r\n                if (fileType.includes('video')) return 'el-icon-video-camera';\r\n                if (fileType.includes('audio')) return 'el-icon-headset';\r\n\r\n                return 'el-icon-document';\r\n            },\r\n            formatFileSize(bytes) {\r\n                if (bytes === 0) return '0 B';\r\n\r\n                const k = 1024;\r\n                const sizes = ['B', 'KB', 'MB', 'GB'];\r\n                const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n            },\r\n            // 债务人相关计算方法\r\n            getDebtCount(debts) {\r\n                return debts && debts.length ? debts.length : 0;\r\n            },\r\n            getDebtCountType(debts) {\r\n                const count = this.getDebtCount(debts);\r\n                if (count === 0) return 'info';\r\n                if (count <= 2) return 'success';\r\n                if (count <= 5) return 'warning';\r\n                return 'danger';\r\n            },\r\n            getTotalDebtAmount(debts) {\r\n                if (!debts || !debts.length) return 0;\r\n                return debts.reduce((total, debt) => {\r\n                    return total + (parseFloat(debt.money) || 0);\r\n                }, 0);\r\n            },\r\n            formatAmount(amount) {\r\n                if (amount === 0) return '0';\r\n                return amount.toLocaleString('zh-CN', {\r\n                    minimumFractionDigits: 0,\r\n                    maximumFractionDigits: 2\r\n                });\r\n            },\r\n            getInfo(id) {\r\n                let _this = this;\r\n                _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n                    if (resp) {\r\n                        resp.data.tiaojie_id = resp.data.tiaojie_id == 0 ? '' : resp.data.tiaojie_id;\r\n                        resp.data.fawu_id = resp.data.fawu_id == 0 ? '' : resp.data.fawu_id;\r\n                        resp.data.lian_id = resp.data.lian_id == 0 ? '' : resp.data.lian_id;\r\n                        resp.data.ywy_id = resp.data.ywy_id == 0 ? '' : resp.data.ywy_id;\r\n                        resp.data.htsczy_id = resp.data.htsczy_id == 0 ? '' : resp.data.htsczy_id;\r\n                        resp.data.ls_id = resp.data.ls_id == 0 ? '' : resp.data.ls_id;\r\n                        _this.ruleForm = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            delData(index, id) {\r\n                this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\",\r\n                })\r\n                    .then(() => {\r\n                        this.postRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                this.$message({\r\n                                    type: \"success\",\r\n                                    message: \"删除成功!\",\r\n                                });\r\n                                this.list.splice(index, 1);\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {\r\n                        this.$message({\r\n                            type: \"error\",\r\n                            message: \"取消删除!\",\r\n                        });\r\n                    });\r\n            },\r\n            refulsh() {\r\n                this.$router.go(0);\r\n            },\r\n            searchData() {\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.filterTestData();\r\n            },\r\n            resetSearch() {\r\n                this.search = {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                };\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n\r\n            getData() {\r\n                let _this = this;\r\n\r\n                _this.loading = true;\r\n                _this\r\n                    .postRequest(\r\n                        _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n                        _this.search\r\n                    )\r\n                    .then((resp) => {\r\n                        if (resp.code == 200) {\r\n                            _this.list = resp.data;\r\n                            _this.total = resp.count;\r\n\r\n                            if (resp.msg == '超级管理员') {\r\n                                _this.is_del = true;\r\n                            }\r\n                        }\r\n                        _this.loading = false;\r\n                    });\r\n            },\r\n            saveData() {\r\n                let _this = this;\r\n                console.log(this.ruleForm);\r\n                this.$refs[\"ruleForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // 使用测试数据，注释掉API调用\r\n                                // this.getData();\r\n                                this.addTestData();\r\n                                _this.dialogFormVisible = false;\r\n                                _this.dialogAddUser = false;\r\n                                _this.drawerEditVisible = false;\r\n                            } else {\r\n                                _this.$message({\r\n                                    type: \"error\",\r\n                                    message: resp.msg,\r\n                                });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleSizeChange(val) {\r\n                this.size = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleSuccess(res) {\r\n                this.ruleForm.pic_path = res.data.url;\r\n            },\r\n\r\n            showImage(file) {\r\n                this.show_image = file;\r\n                this.dialogVisible = true;\r\n            },\r\n            beforeUpload(file) {\r\n                const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n                if (!isTypeTrue) {\r\n                    this.$message.error(\"上传图片格式不对!\");\r\n                    return;\r\n                }\r\n            },\r\n            delImage(file, fileName) {\r\n                let _this = this;\r\n                _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.ruleForm[fileName] = \"\";\r\n\r\n                        _this.$message.success(\"删除成功!\");\r\n                    } else {\r\n                        _this.$message.error(resp.msg);\r\n                    }\r\n                });\r\n            },\r\n            handleSortChange({column, prop, order}) {\r\n                this.search.prop = prop;\r\n                this.search.order = order;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n                // 根据 column, prop, order 来更新你的数据排序\r\n                // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n            },\r\n            exports: function () { //导出表格\r\n                let _this = this;\r\n                location.href = \"/admin/user/export2?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n                // _this.postRequest(\r\n                //                 _this.url + \"export\",\r\n                //                 _this.search\r\n                //         )\r\n                //         .then((resp) => {\r\n                //           if (resp.code == 200) {\r\n                //\r\n                //           }\r\n                //         });\r\n            },\r\n            closeUploadDialog() { //关闭窗口\r\n                this.uploadVisible = false;\r\n                this.$refs.upload.clearFiles();\r\n                this.uploadData.review = false;\r\n            },\r\n            uploadSuccess(response) { //导入完成回调\r\n                if (response.code === 200) {\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: response.msg\r\n                    });\r\n                    this.uploadVisible = false;\r\n                    // 使用测试数据，注释掉API调用\r\n                    // this.getData();\r\n                    this.addTestData();\r\n                    console.log(response);\r\n                } else {\r\n                    this.$message({\r\n                        type: 'warning',\r\n                        message: response.msg\r\n                    });\r\n                }\r\n\r\n                this.submitOrderLoading2 = false;\r\n                this.$refs.upload.clearFiles();\r\n            },\r\n            checkFile(file) { //导入前校验文件后缀\r\n                let fileType = ['xls', 'xlsx'];\r\n                let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n                if (!fileType.includes(type)) {\r\n                    this.$message({\r\n                        type: \"warning\",\r\n                        message: \"文件格式错误仅支持 xls xlxs 文件\"\r\n                    });\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            submitUpload() { //导入提交\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            closeDialog() { //关闭窗口\r\n                this.addVisible = false;\r\n                this.uploadVisible = false;\r\n                this.form = {\r\n                    id: '',\r\n                    nickname: \"\",\r\n                    mobile: \"\",\r\n                    school_id: 0,\r\n                    grade_id: '',\r\n                    class_id: '',\r\n                    sex: '',\r\n                    is_poor: '',\r\n                    is_display: '',\r\n                    number: '',\r\n                    remark: '',\r\n                    is_remark_option: 0,\r\n                    remark_option: [],\r\n                    mobile_checked: false,\r\n                };\r\n                this.$refs.form.resetFields();\r\n            },\r\n            openUpload() { //打开导入弹窗\r\n                this.uploadVisible = true;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n            },\r\n            // 关闭导入对话框\r\n            closeUploadDialog() {\r\n                this.uploadVisible = false;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n                if (this.$refs.upload) {\r\n                    this.$refs.upload.clearFiles();\r\n                }\r\n            },\r\n            // 上传前验证\r\n            beforeUpload(file) {\r\n                const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                               file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n                const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n                if (!isExcel) {\r\n                    this.$message.error('只能上传 Excel 文件!');\r\n                    return false;\r\n                }\r\n                if (!isLt10M) {\r\n                    this.$message.error('上传文件大小不能超过 10MB!');\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            // 提交上传\r\n            submitUpload() {\r\n                if (this.fileList.length === 0) {\r\n                    this.$message.warning('请先选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            // 上传成功回调\r\n            handleUploadSuccess(response, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                if (response.code === 200) {\r\n                    this.$message.success(`导入成功！共导入 ${response.count || 0} 条用户数据`);\r\n                    this.closeUploadDialog();\r\n                    // 重新加载数据\r\n                    this.addTestData(); // 使用测试数据，实际应该调用 this.getData();\r\n                } else {\r\n                    this.$message.error(response.msg || '导入失败');\r\n                }\r\n            },\r\n            // 上传失败回调\r\n            handleUploadError(err, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                this.$message.error('文件上传失败，请重试');\r\n                console.error('Upload error:', err);\r\n            },\r\n            addUser() {\r\n                this.dialogAddUser = true;\r\n                this.ruleForm = {};\r\n                this.getYuangongs();\r\n            },\r\n            // 下载导入模板\r\n            downloadTemplate() {\r\n                const templateUrl = '/import_templete/user.xls';\r\n                const link = document.createElement('a');\r\n                link.href = templateUrl;\r\n                link.download = '用户导入模板.xls';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.$message.success('模板下载中...');\r\n            },\r\n            // 处理表格选择变化\r\n            handleSelectionChange(selection) {\r\n                this.selectedUsers = selection;\r\n            },\r\n            // 导出用户数据（选中或全部）\r\n            exportSelectedData() {\r\n                let exportUrl;\r\n                let message;\r\n\r\n                if (this.selectedUsers.length > 0) {\r\n                    // 导出选中的用户数据\r\n                    const userIds = this.selectedUsers.map(user => user.id).join(',');\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&ids=${userIds}`;\r\n                    message = `正在导出 ${this.selectedUsers.length} 个用户的数据`;\r\n                } else {\r\n                    // 导出全部用户数据\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&keyword=${this.search.keyword || ''}`;\r\n                    message = '正在导出全部用户数据';\r\n                }\r\n\r\n                // 执行导出\r\n                location.href = exportUrl;\r\n                this.$message.success(message);\r\n            },\r\n            // 批量删除用户\r\n            batchDeleteUsers() {\r\n                if (this.selectedUsers.length === 0) {\r\n                    this.$message.warning('请先选择要删除的用户');\r\n                    return;\r\n                }\r\n\r\n                this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`, '批量删除确认', {\r\n                    confirmButtonText: '确定删除',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning',\r\n                    dangerouslyUseHTMLString: true\r\n                }).then(() => {\r\n                    // 获取选中用户的ID列表\r\n                    const userIds = this.selectedUsers.map(user => user.id);\r\n\r\n                    // 这里应该调用批量删除API\r\n                    // this.postRequest(this.url + \"batchDelete\", { ids: userIds }).then((resp) => {\r\n                    //     if (resp.code == 200) {\r\n                    //         this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n                    //         this.getData(); // 重新加载数据\r\n                    //         this.selectedUsers = []; // 清空选择\r\n                    //     }\r\n                    // });\r\n\r\n                    // 临时使用本地删除模拟\r\n                    userIds.forEach(id => {\r\n                        const index = this.list.findIndex(user => user.id === id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1);\r\n                        }\r\n                    });\r\n\r\n                    this.total = this.list.length;\r\n                    this.selectedUsers = []; // 清空选择\r\n                    this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n\r\n                    // 清空表格选择\r\n                    this.$refs.userTable.clearSelection();\r\n                }).catch(() => {\r\n                    this.$message.info('已取消删除操作');\r\n                });\r\n            },\r\n            handleDebtTabSelect(key) {\r\n                this.activeDebtTab = key;\r\n            },\r\n            getEvidenceTitle() {\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        return '全部证据';\r\n                    case 'evidence-video':\r\n                        return '视频证据';\r\n                    case 'evidence-image':\r\n                        return '图片证据';\r\n                    case 'evidence-audio':\r\n                        return '音频证据';\r\n                    case 'evidence-document':\r\n                        return '文档证据';\r\n                    default:\r\n                        return '证据管理';\r\n                }\r\n            },\r\n            getEvidenceTypeText() {\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        return '全部';\r\n                    case 'evidence-video':\r\n                        return '视频';\r\n                    case 'evidence-image':\r\n                        return '图片';\r\n                    case 'evidence-audio':\r\n                        return '音频';\r\n                    case 'evidence-document':\r\n                        return '文档';\r\n                    default:\r\n                        return '';\r\n                }\r\n            },\r\n            getFilteredEvidence() {\r\n                if (!this.currentUserInfo.attachments) {\r\n                    return [];\r\n                }\r\n                \r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-all':\r\n                        // 返回所有类型的证据\r\n                        const allEvidence = [];\r\n                        ['image', 'video', 'audio', 'document'].forEach(type => {\r\n                            if (this.currentUserInfo.attachments[type]) {\r\n                                allEvidence.push(...this.currentUserInfo.attachments[type]);\r\n                            }\r\n                        });\r\n                        return allEvidence;\r\n                    case 'evidence-video':\r\n                        return this.currentUserInfo.attachments.video || [];\r\n                    case 'evidence-image':\r\n                        return this.currentUserInfo.attachments.image || [];\r\n                    case 'evidence-audio':\r\n                        return this.currentUserInfo.attachments.audio || [];\r\n                    case 'evidence-document':\r\n                        return this.currentUserInfo.attachments.document || [];\r\n                    default:\r\n                        return [];\r\n                }\r\n            },\r\n            uploadEvidence() {\r\n                // 创建文件选择器\r\n                const input = document.createElement('input');\r\n                input.type = 'file';\r\n                input.multiple = true;\r\n                \r\n                // 根据当前标签页设置文件类型限制\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-video':\r\n                        input.accept = 'video/*';\r\n                        break;\r\n                    case 'evidence-image':\r\n                        input.accept = 'image/*';\r\n                        break;\r\n                    case 'evidence-audio':\r\n                        input.accept = 'audio/*';\r\n                        break;\r\n                    case 'evidence-document':\r\n                        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx';\r\n                        break;\r\n                    default:\r\n                        input.accept = '*';\r\n                        break;\r\n                }\r\n                \r\n                input.onchange = (event) => {\r\n                    const files = event.target.files;\r\n                    if (files && files.length > 0) {\r\n                        this.handleEvidenceUpload(files);\r\n                    }\r\n                };\r\n                \r\n                input.click();\r\n            },\r\n            handleEvidenceUpload(files) {\r\n                for (let i = 0; i < files.length; i++) {\r\n                    const file = files[i];\r\n                    const fileType = this.getFileTypeFromFile(file);\r\n                    const targetCategory = this.getTargetCategory(fileType);\r\n                    \r\n                    // 创建文件对象\r\n                    const evidenceFile = {\r\n                        name: file.name,\r\n                        size: file.size,\r\n                        type: fileType,\r\n                        url: URL.createObjectURL(file),\r\n                        uploadTime: new Date().toLocaleString()\r\n                    };\r\n                    \r\n                    // 确保附件对象存在\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    \r\n                    // 确保目标分类数组存在\r\n                    if (!this.currentUserInfo.attachments[targetCategory]) {\r\n                        this.currentUserInfo.attachments[targetCategory] = [];\r\n                    }\r\n                    \r\n                    // 添加到对应分类\r\n                    this.currentUserInfo.attachments[targetCategory].push(evidenceFile);\r\n                }\r\n                \r\n                this.$message.success(`成功上传 ${files.length} 个文件`);\r\n            },\r\n            getFileTypeFromFile(file) {\r\n                const type = file.type;\r\n                if (type.startsWith('image/')) return 'image';\r\n                if (type.startsWith('video/')) return 'video';\r\n                if (type.startsWith('audio/')) return 'audio';\r\n                return 'document';\r\n            },\r\n            getTargetCategory(fileType) {\r\n                switch (this.activeDebtTab) {\r\n                    case 'evidence-video':\r\n                        return 'video';\r\n                    case 'evidence-image':\r\n                        return 'image';\r\n                    case 'evidence-audio':\r\n                        return 'audio';\r\n                    case 'evidence-document':\r\n                        return 'document';\r\n                    default:\r\n                        return fileType === 'image' ? 'image' : \r\n                               fileType === 'video' ? 'video' : \r\n                               fileType === 'audio' ? 'audio' : 'document';\r\n                }\r\n            },\r\n            downloadEvidence(evidence) {\r\n                // 创建下载链接\r\n                const link = document.createElement('a');\r\n                link.href = evidence.url;\r\n                link.download = evidence.name;\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.$message.success('开始下载文件');\r\n            },\r\n            deleteEvidence(index) {\r\n                this.$confirm('确定要删除这个证据吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    if (this.currentUserInfo.attachments[this.activeDebtTab]) {\r\n                        // 释放预览URL\r\n                        const file = this.currentUserInfo.attachments[this.activeDebtTab][index];\r\n                        if (file && file.url && file.url.startsWith('blob:')) {\r\n                            URL.revokeObjectURL(file.url);\r\n                        }\r\n\r\n                        this.currentUserInfo.attachments[this.activeDebtTab].splice(index, 1);\r\n                        this.$message.success('删除成功！');\r\n                    }\r\n                });\r\n            },\r\n        },\r\n    };\r\n</script>\r\n<style scoped>\r\n/* 页面特定样式 */\r\n.page-wrapper {\r\n    background-color: #f5f5f5;\r\n    min-height: calc(100vh - 110px);\r\n    padding: 16px;\r\n}\r\n\r\n.page-container {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    padding: 24px;\r\n}\r\n\r\n.page-title {\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-container {\r\n    background: #fff;\r\n    padding: 24px;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #e8e8e8;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-form {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.search-form .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.search-form .el-input__inner,\r\n.search-form .el-select .el-input__inner {\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.search-form .el-input__inner:focus,\r\n.search-form .el-select .el-input__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.search-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n}\r\n\r\n.search-buttons .el-button {\r\n    min-width: 80px;\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-wrap: wrap;\r\n    margin-top: 20px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-buttons .el-button {\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.data-table {\r\n    margin-top: 20px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table .el-table {\r\n    border-radius: 8px;\r\n}\r\n\r\n.data-table .el-table th {\r\n    background-color: #fafafa !important;\r\n    color: #606266 !important;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table .el-table td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 头像样式 */\r\n.avatar-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    border: 2px solid #e8e8e8;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.user-avatar {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.no-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-name.clickable {\r\n    cursor: pointer;\r\n    color: #1890ff;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.user-name.clickable:hover {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n.user-phone {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 手机号码信息样式 */\r\n.phone-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.phone-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    font-size: 13px;\r\n}\r\n\r\n.phone-item.clickable {\r\n    cursor: pointer;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.phone-item.clickable:hover {\r\n    color: #1890ff;\r\n}\r\n\r\n.phone-label {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n    min-width: 32px;\r\n    font-weight: 500;\r\n}\r\n\r\n.phone-number {\r\n    color: #262626;\r\n    font-weight: 500;\r\n}\r\n\r\n.phone-item.clickable .phone-number {\r\n    color: #1890ff;\r\n}\r\n\r\n.phone-item.clickable:hover .phone-number {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* 头像点击样式优化 */\r\n.avatar-container.clickable,\r\n.no-avatar.clickable,\r\n.avatar-wrapper.clickable {\r\n    cursor: pointer;\r\n}\r\n\r\n.avatar-container:hover .avatar-wrapper,\r\n.avatar-container:hover .no-avatar {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.no-avatar.clickable:hover {\r\n    background-color: #e6f7ff;\r\n    color: #1890ff;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons-table {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.action-buttons-table .el-button {\r\n    padding: 5px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 16px 0;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    color: #8c8c8c;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 编辑模式切换按钮样式 */\r\n.edit-mode-toggle {\r\n    display: flex;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.edit-mode-toggle .el-button {\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.custom-dialog .el-dialog__body {\r\n    padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.card {\r\n    background: #fff;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.card-header {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.el-input, .el-select {\r\n    width: 100%;\r\n}\r\n\r\n/* 搜索表单特殊样式 */\r\n.search-form .el-input,\r\n.search-form .el-select,\r\n.search-form .el-date-picker {\r\n    width: 100%;\r\n}\r\n\r\n.search-form .el-date-picker {\r\n    width: 100% !important;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.data-table .el-table tbody tr:hover {\r\n    background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n    .search-form .el-col {\r\n        margin-bottom: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .search-form .el-col {\r\n        width: 100% !important;\r\n        flex: 0 0 100% !important;\r\n        max-width: 100% !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n    }\r\n\r\n    .action-buttons {\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 16px;\r\n        margin: 8px;\r\n    }\r\n\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: center;\r\n    }\r\n\r\n    .pagination-info {\r\n        order: 2;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.drawer-content-wrapper {\r\n    display: flex;\r\n    height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n    width: 200px;\r\n    border-right: 1px solid #e6e6e6;\r\n    background-color: #fafafa;\r\n}\r\n\r\n.drawer-menu {\r\n    border-right: none;\r\n    background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.drawer-content {\r\n    flex: 1;\r\n    padding: 20px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n    height: 100%;\r\n}\r\n\r\n.drawer-content .card {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.drawer-content .card-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.drawer-content .card-header i {\r\n    color: #1890ff;\r\n    font-size: 18px;\r\n}\r\n\r\n.drawer-footer {\r\n    margin-top: 24px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n    text-align: right;\r\n}\r\n\r\n.drawer-footer .el-button {\r\n    margin-left: 12px;\r\n}\r\n\r\n/* 头像显示样式 */\r\n.avatar-display {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.detail-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    border: 2px solid #e8e8e8;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.detail-avatar:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.no-avatar-large {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 12px;\r\n    border: 2px solid #e8e8e8;\r\n}\r\n\r\n.no-avatar-large i {\r\n    font-size: 24px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n/* 抽屉内表单样式 */\r\n.drawer-content .el-form-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.drawer-content .el-descriptions-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n}\r\n\r\n/* 无数据显示样式 */\r\n.no-data {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #ccc;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-data i {\r\n    font-size: 48px;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.attachment-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n}\r\n\r\n.attachment-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n}\r\n\r\n.attachment-title {\r\n    background: #f5f5f5;\r\n    padding: 12px 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.attachment-content {\r\n    padding: 16px;\r\n}\r\n\r\n.image-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: box-shadow 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n.attachment-image {\r\n    width: 100%;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.attachment-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\r\n    color: white;\r\n    padding: 12px;\r\n    transform: translateY(100%);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover .image-overlay {\r\n    transform: translateY(0);\r\n}\r\n\r\n.image-info {\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-info .file-name {\r\n    display: block;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.image-info .upload-time {\r\n    font-size: 11px;\r\n    opacity: 0.8;\r\n}\r\n\r\n.image-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n}\r\n\r\n.image-actions .el-button {\r\n    flex: 1;\r\n    font-size: 11px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.file-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    background: #fafafa;\r\n    transition: background-color 0.2s;\r\n}\r\n\r\n.file-item:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n.file-icon {\r\n    margin-right: 12px;\r\n}\r\n\r\n.file-type-icon {\r\n    font-size: 24px;\r\n    color: #1890ff;\r\n}\r\n\r\n.file-info {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.file-info .file-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.file-meta {\r\n    display: flex;\r\n    gap: 12px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.file-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    margin-left: 12px;\r\n}\r\n\r\n.no-attachment {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #999;\r\n}\r\n\r\n.no-attachment i {\r\n    font-size: 48px;\r\n    color: #d9d9d9;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n}\r\n\r\n.no-attachment span {\r\n    display: block;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 导入用户对话框样式 */\r\n.upload-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.upload-tips {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-tips .el-alert__description p {\r\n    margin: 5px 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-actions {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-area .el-upload-dragger {\r\n    width: 100%;\r\n    height: 180px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: border-color 0.2s;\r\n}\r\n\r\n.upload-area .el-upload-dragger:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-area .el-upload-dragger .el-icon-upload {\r\n    font-size: 67px;\r\n    color: #c0c4cc;\r\n    margin: 40px 0 16px;\r\n    line-height: 50px;\r\n}\r\n\r\n.upload-area .el-upload__text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area .el-upload__text em {\r\n    color: #409eff;\r\n    font-style: normal;\r\n}\r\n\r\n.upload-area .el-upload__tip {\r\n    font-size: 12px;\r\n    color: #606266;\r\n    margin-top: 7px;\r\n}\r\n\r\n.upload-options {\r\n    text-align: center;\r\n}\r\n\r\n.upload-options .el-checkbox {\r\n    color: #606266;\r\n}\r\n\r\n/* 债务金额样式 */\r\n.debt-amount {\r\n    font-weight: 500;\r\n    color: #8c8c8c;\r\n}\r\n\r\n.debt-amount.has-debt {\r\n    color: #f56c6c;\r\n    font-weight: 600;\r\n}\r\n\r\n/* 证据管理样式 */\r\n.evidence-container {\r\n    margin-top: 16px;\r\n}\r\n\r\n.evidence-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 20px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.evidence-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.evidence-item:hover {\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n    position: relative;\r\n    height: 180px;\r\n    background: #f8f9fa;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.evidence-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.evidence-video {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.evidence-audio {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n}\r\n\r\n.audio-preview {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px;\r\n}\r\n\r\n.audio-icon {\r\n    font-size: 48px;\r\n    color: #1890ff;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.document-preview {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.document-icon {\r\n    font-size: 48px;\r\n    color: #52c41a;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.document-name {\r\n    font-size: 14px;\r\n    color: #262626;\r\n    font-weight: 500;\r\n    word-break: break-all;\r\n}\r\n\r\n.evidence-info {\r\n    padding: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.evidence-name {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 8px;\r\n    word-break: break-all;\r\n    line-height: 1.4;\r\n}\r\n\r\n.evidence-meta {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 12px;\r\n    color: #8c8c8c;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.evidence-size {\r\n    font-weight: 500;\r\n}\r\n\r\n.evidence-time {\r\n    font-style: italic;\r\n}\r\n\r\n.evidence-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.evidence-actions .el-button {\r\n    flex: 1;\r\n    font-size: 12px;\r\n}\r\n\r\n.no-evidence {\r\n    text-align: center;\r\n    padding: 60px 20px;\r\n    color: #bfbfbf;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-evidence i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 子菜单样式优化 */\r\n.drawer-menu .el-submenu .el-menu-item {\r\n    height: 40px;\r\n    line-height: 40px;\r\n    padding-left: 40px !important;\r\n    font-size: 13px;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item i {\r\n    margin-right: 6px;\r\n    font-size: 14px;\r\n}\r\n\r\n.drawer-menu .el-submenu__title {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-submenu__title i {\r\n    margin-right: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .evidence-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n    .drawer-content-wrapper {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .drawer-sidebar {\r\n        width: 100%;\r\n        border-right: none;\r\n        border-bottom: 1px solid #e6e6e6;\r\n    }\r\n    \r\n    .drawer-menu {\r\n        display: flex;\r\n        overflow-x: auto;\r\n    }\r\n    \r\n    .drawer-menu .el-menu-item,\r\n    .drawer-menu .el-submenu {\r\n        flex-shrink: 0;\r\n    }\r\n}\r\n</style>\r\n"]}]}