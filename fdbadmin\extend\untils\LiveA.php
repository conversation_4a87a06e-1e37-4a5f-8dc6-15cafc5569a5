<?php
/**
 *
 * @author: 神台猫屎<<EMAIL>>
 * @day: 2017/10/24
 */

namespace untils;

use Illuminate\Http\Request;
use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;

class LiveA
{
	private $error;
    const DOMAIN_NAME='guanzhong.oecgd.com';//你的加速域名，和推流域名一致。
    const REGION_ID='cn-shenzhen';//区域
    const ACCESS_KEY_ID='LTAI5tCWzvbvkuabGRW2rAFv';		//阿里云秘钥
    const ACCESS_KEY_SECRET='******************************';//阿里云秘钥
    const LIVE_HOST='live.aliyuncs.com';//写死，阿里云直播CDN域名
    const ALIVE_URL='guanzhong.oecgd.com';		  //推域名
    const ALIVE_KEY='qjyLgtmytQA8xgaq';       //推流鉴权KEY，后台拿
    const TLIVE_URL='zhubo.oecgd.com';       //关联的播域名
    const TLIVE_KEY='LHehGgxnqw1VWYcO';       //播流鉴权KEY，后台拿

	/**
     * 创建签名
     * @param $path 播放地址 域名后面所有
     * @param $exp //结束时间
     * @param $key
     * @return string
     */
    private static function createSign($path,$exp,$key)
    {
        $rand=0;
        $uid=0;
        $str=sprintf("%s-%s-%s-%s-%s",$path,(string)$exp,(string)$rand,(string)$uid,$key);
        $hashValue=md5($str);
        $authKey=sprintf("%s-%s-%s-%s",(string)$exp,(string)$rand,(string)$uid,$hashValue);
        return "auth_key=$authKey";
    }
 
    /**
     * 创建是直播地址
     * @param $appName 应用名称 ，自定义
     * @param $streamName 房间名称，自定义，该应用下唯一
     * @param $endTime 结束时间
     * @return array|bool  播放流（观看者）：alive_url，推流（直播者）：tlive_url
     */
    public static function createdLive($appName,$streamName,$endTime)
    {
        if(!$appName || !$streamName || !$endTime || $endTime < time()){
            return false;
        }
        //创建播流
        $path="/$appName/$streamName";
        $aliveUrl='rtmp://'.self::ALIVE_URL."$path?".self::createSign($path,$endTime,self::ALIVE_KEY);
 
        //创建推流
        $rtmp_sstring = '/'.$appName.'/'.$streamName.'-'.$endTime.'-0-0-'.self::TLIVE_KEY;
        $rtmp_md5hash = md5($rtmp_sstring);
        $rtmp_play_url = 'rtmp://'.self::TLIVE_URL.'/'.$appName.'/'.$streamName.'?auth_key='.$endTime.'-0-0-'.$rtmp_md5hash;
 
        $flv_sstring = '/'.$appName.'/'.$streamName.'.flv-'.$endTime.'-0-0-'.self::TLIVE_KEY;
        $flv_md5hash = md5($flv_sstring);
        $flv_play_url = 'http://'.self::TLIVE_URL.'/'.$appName.'/'.$streamName.'.flv?auth_key='.$endTime.'-0-0-'.$flv_md5hash;
 
        $hls_sstring = '/'.$appName.'/'.$streamName.'.m3u8-'.$endTime.'-0-0-'.self::TLIVE_KEY;
        $hls_md5hash = md5($hls_sstring);
        $tliveUrlM3U8 = 'http://'.self::TLIVE_URL.'/'.$appName.'/'.$streamName.'.m3u8?auth_key='.$endTime.'-0-0-'.$hls_md5hash;
        $data = [
            'rtmp'=>$rtmp_play_url,
            'flv'=>$flv_play_url,
            'hls'=>$tliveUrlM3U8,
        ];
        return [
            'alive_url'=>$aliveUrl,
            'tlive_url'=>$data,
            
        ];
    }
    //停止直播
    public static function stopLive($appName,$streamName)
    {
        $query=[
            'RegionId' => self::REGION_ID,
            'AppName' => $appName,
            'StreamName' => $streamName,
            'LiveStreamType' => "publisher",
            'DomainName' => self::DOMAIN_NAME,
            // 'ResumeTime'=>'',
        ];
        $_this=new static();
        $result=$_this->request('ForbidLiveStream',$query);
        return $result;
    }

    //恢复直播
    public static function resumeLive($appName,$streamName)
    {
        $query=[
            'RegionId' => self::REGION_ID,
            'LiveStreamType' => "publisher",
            'AppName' => $appName,
            'StreamName' => $streamName,
            'DomainName' => self::DOMAIN_NAME,
        ];
        $_this=new static();
        $result=$_this->request('ResumeLiveStream',$query);
        return $result;
    }
    
    //获取直播在线人数
    public static function getOnlineUserNum($appName,$streamNma)
    {
        $query=[
            'RegionId' => self::REGION_ID,
            'DomainName' => self::DOMAIN_NAME,
            'AppName'=>$appName,
            'StreamName' =>$streamNma,
        ];
        $_this=new static();//DescribeLiveDomainOnlineUserNum
        $result=$_this->request('DescribeLiveDomainOnlineUserNum',$query);
        return $result;
    }
    
    public static function getLiveStatus($appName,$streamNma){
        $query=[
            'RegionId' => self::REGION_ID,
            'DomainName' => self::DOMAIN_NAME,
            'AppName'=>$appName,
            'StreamName' =>$streamNma,
        ];
        $_this=new static();//DescribeLiveDomainOnlineUserNum
        $result=$_this->request('DescribeLiveStreamState',$query);
        return $result;
    }
    public static function DescribeLiveStreamRecordIndexFiles($appName,$streamNma,$StartTime,$EndTime){
        $query=[
         
            'DomainName' => self::TLIVE_URL,
            'AppName'=>$appName,
            'StreamName' =>$streamNma,
            'EndTime'=>date("Y-m-d\TH:i:s\Z", strtotime($EndTime)),
            'StartTime'=>date("Y-m-d\TH:i:s\Z", strtotime($StartTime))
        ];
        
        $_this=new static();//DescribeLiveDomainOnlineUserNum
        $result=$_this->request('DescribeLiveStreamRecordIndexFiles',$query);
        return $result;
    }
    public static function DescribeLiveStreamRecordIndexFile($appName,$streamNma){
        $query=[
            'RegionId' => self::REGION_ID,
            'DomainName' => self::DOMAIN_NAME,
            'AppName'=>$appName,
            'StreamName' =>$streamNma,
        ];
        $_this=new static();//DescribeLiveDomainOnlineUserNum
        $result=$_this->request('DescribeLiveStreamRecordIndexFile',$query);
        return $result;
    }
    public static function DescribeLiveRecordConfig(){
        $query=[
            'RegionId' => self::REGION_ID,
            'DomainName' => self::DOMAIN_NAME,
            'AppName'=>$appName,
            'StreamName' =>$streamNma,
        ];
        $_this=new static();//DescribeLiveDomainOnlineUserNum
        $result=$_this->request('DescribeLiveStreamState',$query);
        return $result;
    }
    //获取错误
    public static function getError()
    {
        return (new static())->error;
    }

    //请求
    private function request($action,Array $query)
    {
        AlibabaCloud::accessKeyClient(self::ACCESS_KEY_ID, self::ACCESS_KEY_SECRET)
            ->regionId(self::REGION_ID)
            ->asDefaultClient();
        try {
            $result = AlibabaCloud::rpc()
                ->product('live')
                ->scheme('https') // https | http
                ->version('2016-11-01')
                ->action($action)
                ->method('POST')
                ->host(self::LIVE_HOST)
                ->options([
                    'query' => $query,
                ])
                ->request();
            return $result->toArray();
        } catch (ClientException $e) {
            $this->error=$e->getMessage();
            return false;
        } catch (ServerException $e) {
            $this->error=$e->getMessage();
            return false;
        }
    }



}