{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\fuwu\\index.vue?vue&type=style&index=0&id=30c341f5&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\fuwu\\index.vue", "mtime": 1748484179969}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmiBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/fuwu", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-service\"></i>\r\n            {{ this.$router.currentRoute.name }}\r\n          </h2>\r\n          <div class=\"page-subtitle\">管理法律服务产品和套餐</div>\r\n        </div>\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 统计信息卡片 -->\r\n      <div class=\"stats-section\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-service\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ total }}</div>\r\n            <div class=\"stat-label\">服务产品</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon active\">\r\n            <i class=\"el-icon-money\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ averagePrice }}</div>\r\n            <div class=\"stat-label\">平均价格</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon premium\">\r\n            <i class=\"el-icon-star-on\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ premiumServices }}</div>\r\n            <div class=\"stat-label\">高端服务</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"search-section\">\r\n        <div class=\"search-controls\">\r\n          <el-input\r\n            placeholder=\"请输入服务名称进行搜索\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <el-button\r\n              slot=\"append\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n            ></el-button>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"action-controls\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n          >\r\n            新增服务\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-section\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"data-table\"\r\n          stripe\r\n          @sort-change=\"handleSortChange\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"服务名称\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"service-title-cell\">\r\n                <div class=\"service-icon\">\r\n                  <i class=\"el-icon-service\"></i>\r\n                </div>\r\n                <div class=\"service-info\">\r\n                  <div class=\"service-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"service-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"service-cover\" v-if=\"scope.row.pic_path\">\r\n                <img\r\n                  :src=\"scope.row.pic_path\"\r\n                  @click=\"showImage(scope.row.pic_path)\"\r\n                  class=\"cover-image\"\r\n                  :alt=\"scope.row.title\"\r\n                />\r\n              </div>\r\n              <span v-else class=\"no-cover\">暂无封面</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"价格信息\" min-width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"price-cell\">\r\n                <div class=\"current-price\">\r\n                  <span class=\"price-label\">现价：</span>\r\n                  <span class=\"price-value\">¥{{ scope.row.price }}</span>\r\n                </div>\r\n                <div class=\"market-price\" v-if=\"scope.row.shop_price\">\r\n                  <span class=\"price-label\">市场价：</span>\r\n                  <span class=\"price-value original\">¥{{ scope.row.shop_price }}</span>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"day\" label=\"有效期\" width=\"100\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"validity-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.day }}年</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-calendar\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getServiceStatusType(scope.row)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getServiceStatusText(scope.row) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"有效期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n          <el-input v-model=\"ruleForm.day\" autocomplete=\"off\" type=\"number\"\r\n            ><template slot=\"append\">年</template></el-input\r\n          >\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\" prop=\"price\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"市场价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"shop_price\"\r\n        >\r\n          <el-input v-model=\"ruleForm.shop_price\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"封面\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n            ><template slot=\"append\">330rpx*300rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/server/\",\r\n      title: \"服务\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n      isClear: false, // 添加编辑器清空标志\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        day: [\r\n          {\r\n            required: true,\r\n            message: \"请填写有效期\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        shop_price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写市场价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 平均价格\r\n    averagePrice() {\r\n      if (this.list.length === 0) return '0';\r\n      const total = this.list.reduce((sum, item) => sum + parseFloat(item.price || 0), 0);\r\n      return Math.round(total / this.list.length);\r\n    },\r\n    // 高端服务数量（价格超过1000的服务）\r\n    premiumServices() {\r\n      return this.list.filter(item => parseFloat(item.price || 0) > 1000).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          price: \"\",\r\n          day: 0,\r\n          shop_price: \"\",\r\n          pic_path: \"\",\r\n          content: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp && resp.code == 200) {\r\n            // 确保 list 始终是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n          } else {\r\n            // 如果请求失败，设置为空数组\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据失败:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSortChange(column) {\r\n      // 处理排序变化\r\n      console.log('排序变化:', column);\r\n    },\r\n    change(val) {\r\n      // 编辑器内容变化回调\r\n      this.ruleForm.content = val;\r\n    },\r\n    getServiceStatusType(row) {\r\n      // 根据价格返回状态类型\r\n      const price = parseFloat(row.price || 0);\r\n      if (price > 1000) return 'success';\r\n      if (price > 500) return 'warning';\r\n      return 'info';\r\n    },\r\n    getServiceStatusText(row) {\r\n      // 根据价格返回状态文本\r\n      const price = parseFloat(row.price || 0);\r\n      if (price > 1000) return '高端';\r\n      if (price > 500) return '标准';\r\n      return '基础';\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0 0 8px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #1890ff;\r\n  font-size: 22px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 统计信息卡片 */\r\n.stats-section {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  min-width: 200px;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\r\n}\r\n\r\n.stat-card:nth-child(2) {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  box-shadow: 0 4px 12px rgba(245, 87, 108, 0.15);\r\n}\r\n\r\n.stat-card:nth-child(3) {\r\n  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\r\n  box-shadow: 0 4px 12px rgba(252, 182, 159, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 搜索和操作区域 */\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  gap: 16px;\r\n}\r\n\r\n.search-controls {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.data-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 服务标题单元格 */\r\n.service-title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 4px 0;\r\n}\r\n\r\n.service-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.service-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.service-title {\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.service-title:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.service-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 服务封面 */\r\n.service-cover {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.cover-image {\r\n  width: 60px;\r\n  height: 40px;\r\n  object-fit: cover;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.no-cover {\r\n  color: #d9d9d9;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 价格单元格 */\r\n.price-cell {\r\n  padding: 4px 0;\r\n}\r\n\r\n.current-price {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.market-price {\r\n  font-size: 12px;\r\n}\r\n\r\n.price-label {\r\n  color: #8c8c8c;\r\n  font-size: 12px;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 500;\r\n  color: #f5222d;\r\n  margin-left: 4px;\r\n}\r\n\r\n.price-value.original {\r\n  text-decoration: line-through;\r\n  color: #bfbfbf;\r\n}\r\n\r\n/* 有效期单元格 */\r\n.validity-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #595959;\r\n}\r\n\r\n.validity-cell i {\r\n  color: #8c8c8c;\r\n}\r\n\r\n/* 时间单元格 */\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #595959;\r\n}\r\n\r\n.time-cell i {\r\n  color: #8c8c8c;\r\n}\r\n\r\n/* 状态标签 */\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.edit-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 分页容器 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .stats-section {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .stat-card {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-controls {\r\n    max-width: none;\r\n  }\r\n\r\n  .action-controls {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n\r\n  .service-title-cell {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table ::v-deep .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table ::v-deep .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  color: #262626;\r\n}\r\n\r\n.data-table ::v-deep .el-table tr:hover > td {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped:hover td {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n/* 保留原有样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}