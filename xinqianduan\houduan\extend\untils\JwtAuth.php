<?php
namespace untils;
use Firebase\JWT\JWT;
use think\facade\Request;

class JwtAuth{
    public static $token = '';
    private static $config;
    /**
     * @param array $tokenData
     * @return string
     */
    public static function getToken($tokenData = [])
    {
        if (empty($tokenData) || !is_array($tokenData)) {
            $tokenData = [];
        }
        $config = self::getConfig();
        $key = $config['salt'];  //这里是自定义的一个随机字串，应该写在config文件中的，解密时也会用，相当    于加密中常用的 盐  salt
        $time = time();
        $token = [
            "iss" => "",  //签发者 可以为空
            "aud" => "", //面象的用户，可以为空
            "iat" => $time, //签发时间
            "nbf" => $time + $config['nbf'], //在什么时候jwt开始生效  （这里表示生成100秒后才生效）
            "exp" => $time + $config['exp'], //token 过期时间
        ];
        $mergeArray = array_merge($token, $tokenData);
        $jwt = JWT::encode($mergeArray, $key, "HS256"); //根据参数生成了 token
        return $jwt;
    }
    /**
     * @return bool|object|string
     * @return 成功返回数据对象  失败 debug开返回失败信息，debug关返回false
     */
    public static function getInfo()
    {
        $config = self::getConfig();
        switch ($config['request']) {
            case 'post':
                $jwt = Request::post($config['tokenName']);
                break;
            case 'get':
                $jwt = Request::get($config['tokenName']);
                break;
            default:
                $jwt = Request::header($config['tokenName']);
        }
       
        $key = $config['salt'];  //上一个方法中的 $key 本应该配置在 config文件中的
        try {
            $info = JWT::decode($jwt, $key, ["HS256"]); //解密jwt  返回完整的token数组
            $info->token = $jwt;
        } catch (\Exception $e) {
            if (config('app_debug')) {
                $info = $e->getMessage();
            } else {
                $info = false;
            }
        }
        return $info;
    }

    /**
     * <AUTHOR>
     * @name getInfoByToken
     * @Date: 2019/9/27
     * @Time: 16:03
     * @param $token
     * @return bool|object|string
     */
     
    public static function getInfoByToken($token)
    {
        $config = self::getConfig();

        $jwt = $token;
        $key = $config['salt'];  //上一个方法中的 $key 本应该配置在 config文件中的
        try {
            $info = JWT::decode($jwt, $key, ["HS256"]); //解密jwt  返回完整的token数组
        } catch (\Exception $e) {
            if (config('app_debug')) {
                $info = $e->getMessage();
            } else {
                $info = false;
            }
        }
        return $info;
    }
    /**
     * <AUTHOR>
     * @name initConfig
     * @Date: 2019/10/11
     * @Time: 1:08
     * @return mixed
     */
    public static function initConfig()
    {
        $config = config('jwt');
        self::$config = $config;
    }
    
    /**
     * <AUTHOR>
     * @name getConfig
     * @Date: 2019/9/27
     * @Time: 16:01
     * @return array|mixed
     */
    public static function getConfig()
    {
        $config = config()['jwt'];
        
        if(!empty(self::$config)){
            $config = array_merge($config,self::$config);
        }
        return $config;
    }
    /**
     * <AUTHOR>
     * @name setConfig
     * @Date: 2019/9/27
     * @Time: 15:50
     * @param $config
     */
    public static function setConfig($config)
    {
        self::$config = $config;
    }
}