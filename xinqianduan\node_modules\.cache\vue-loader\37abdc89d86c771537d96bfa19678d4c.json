{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue?vue&type=style&index=0&id=11206f30&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue", "mtime": 1748450855160}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8yBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-list-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            合同列表管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统中的所有合同模板和文书</p>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增合同\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索和筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-content\">\r\n          <div class=\"search-left\">\r\n            <el-input\r\n              placeholder=\"搜索合同标题、类型...\"\r\n              v-model=\"search.keyword\"\r\n              class=\"search-input\"\r\n              clearable\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch()\"\r\n              class=\"clear-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"contract-table\"\r\n          stripe\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"文书标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"title-text\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"cate_id\" label=\"文书类型\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ getCategoryName(scope.row.cate_id) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"price\" label=\"价格\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"price-text\">\r\n                <i class=\"el-icon-money\"></i>\r\n                ¥{{ scope.row.price || '0.00' }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"file_path\" label=\"文件状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.file_path ? 'success' : 'warning'\"\r\n                size=\"small\"\r\n              >\r\n                {{ scope.row.file_path ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"录入时间\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-edit\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.file_path\"\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"previewContract(scope.row)\"\r\n                  icon=\"el-icon-view\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-delete\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"form-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"文书类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cate_id\"\r\n        >\r\n          <el-select v-model=\"ruleForm.cate_id\" placeholder=\"请选择\" filterable>\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in cates\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"文件上传\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changefield('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 文书预览对话框 -->\r\n    <el-dialog\r\n      title=\"文书预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              类型：{{ getCategoryName(previewData.cate_id) }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-money\"></i>\r\n              价格：¥{{ previewData.price || '0.00' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div v-if=\"previewData.file_path\" class=\"file-preview\">\r\n            <div class=\"file-info\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ previewData.file_path.split('/').pop() }}</span>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"downloadFile(previewData.file_path)\"\r\n                icon=\"el-icon-download\"\r\n              >\r\n                下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"previewData.content\" class=\"content-preview\">\r\n            <h4>文书内容：</h4>\r\n            <div class=\"content-html\" v-html=\"previewData.content\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshu/\",\r\n      field: \"\",\r\n      title: \"文书\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogPreview: false,\r\n      previewData: {},\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      isClear: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        cate_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择文书类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      cates: [],\r\n      expireTimeOption: {\r\n        disabledDate(date) {\r\n          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean\r\n          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    console.log('页面挂载完成，开始加载数据...');\r\n    this.getData();\r\n    this.getLvshi(); // 获取分类数据\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n\r\n    // 添加调试信息\r\n    this.$nextTick(() => {\r\n      console.log('页面渲染完成');\r\n      console.log('当前list数据:', this.list);\r\n      console.log('当前loading状态:', this.loading);\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  watch: {\r\n    dialogFormVisible(newVal, oldVal) {\r\n      console.log('对话框可见性变化:', newVal, oldVal);\r\n      if (!newVal && oldVal) {\r\n        // 对话框关闭时重置表单\r\n        this.handleDialogClose();\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    change() {},\r\n    changefield(field) {\r\n      this.field = field;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n      _this.getLvshi();\r\n      _this.dialogFormVisible = true;\r\n    },\r\n\r\n    getLvshi() {\r\n      // 添加测试分类数据\r\n      const testCategories = [\r\n        { id: 1, title: \"民事诉讼\", desc: \"民事纠纷相关文书\" },\r\n        { id: 2, title: \"商事诉讼\", desc: \"商业纠纷相关文书\" },\r\n        { id: 3, title: \"侵权诉讼\", desc: \"侵权纠纷相关文书\" },\r\n        { id: 4, title: \"婚姻家庭\", desc: \"婚姻家庭纠纷文书\" },\r\n        { id: 5, title: \"知识产权\", desc: \"知识产权纠纷文书\" },\r\n        { id: 6, title: \"劳动争议\", desc: \"劳动关系纠纷文书\" },\r\n        { id: 7, title: \"行政诉讼\", desc: \"行政纠纷相关文书\" },\r\n        { id: 8, title: \"刑事辩护\", desc: \"刑事案件相关文书\" }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        this.cates = testCategories;\r\n        console.log('加载测试分类数据:', this.cates);\r\n      }, 50);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      this.postRequest(\"/wenshucate/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          // 确保返回的数据是数组\r\n          this.cates = Array.isArray(resp.data) ? resp.data : [];\r\n          console.log('获取到的分类数据:', this.cates);\r\n        } else {\r\n          console.error('获取分类失败:', resp);\r\n          this.cates = [];\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取分类出错:', error);\r\n        this.cates = [];\r\n      });\r\n      */\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search.keyword = '';\r\n      this.searchData();\r\n    },\r\n\r\n    // 获取分类名称\r\n    getCategoryName(cateId) {\r\n      // 确保 cates 是数组\r\n      if (!Array.isArray(this.cates)) {\r\n        console.warn('cates is not an array:', this.cates);\r\n        return '未分类';\r\n      }\r\n      const category = this.cates.find(item => item.id === cateId);\r\n      return category ? category.title : '未分类';\r\n    },\r\n\r\n    // 预览文书\r\n    previewContract(row) {\r\n      console.log('预览文书:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      console.log('对话框关闭事件触发');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      console.log('取消按钮点击');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n      this.dialogFormVisible = false;\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27 && this.dialogFormVisible) {\r\n        this.cancelDialog();\r\n      }\r\n    },\r\n\r\n\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          title: \"民事起诉状模板\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/civil_complaint_template.docx\",\r\n          price: \"500.00\",\r\n          content: \"<p>这是一份标准的民事起诉状模板，适用于一般民事纠纷案件。包含完整的格式要求和必要条款。</p><p>主要内容包括：</p><ul><li>当事人基本信息</li><li>诉讼请求</li><li>事实与理由</li><li>证据清单</li></ul>\",\r\n          create_time: \"2024-01-15 10:30:00\",\r\n          update_time: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"劳动合同纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/labor_dispute_complaint.pdf\",\r\n          price: \"800.00\",\r\n          content: \"<p>专门针对劳动合同纠纷的起诉书模板，涵盖工资拖欠、违法解除等常见情形。</p><p>适用范围：</p><ul><li>工资拖欠纠纷</li><li>违法解除劳动合同</li><li>加班费争议</li><li>经济补偿金纠纷</li></ul>\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          update_time: \"2024-01-16 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"房屋买卖合同纠纷诉状\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/property_sale_dispute.docx\",\r\n          price: \"1200.00\",\r\n          content: \"<p>房屋买卖合同纠纷专用诉讼文书，包含房产交易中的各种争议处理。</p><p>涵盖问题：</p><ul><li>房屋质量问题</li><li>逾期交房</li><li>产权过户纠纷</li><li>定金违约</li></ul>\",\r\n          create_time: \"2024-01-17 09:15:00\",\r\n          update_time: \"2024-01-17 09:15:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"交通事故赔偿起诉书\",\r\n          cate_id: 3,\r\n          file_path: \"/uploads/documents/traffic_accident_claim.pdf\",\r\n          price: \"600.00\",\r\n          content: \"<p>交通事故人身损害赔偿起诉书模板，适用于各类交通事故赔偿案件。</p><p>赔偿项目：</p><ul><li>医疗费</li><li>误工费</li><li>护理费</li><li>精神损害抚慰金</li></ul>\",\r\n          create_time: \"2024-01-18 16:45:00\",\r\n          update_time: \"2024-01-18 16:45:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"借款合同纠纷起诉状\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/loan_dispute_complaint.docx\",\r\n          price: \"400.00\",\r\n          content: \"<p>民间借贷纠纷起诉状模板，适用于个人借款、企业借贷等各类借款纠纷。</p><p>主要条款：</p><ul><li>借款本金确认</li><li>利息计算标准</li><li>违约责任</li><li>担保责任</li></ul>\",\r\n          create_time: \"2024-01-19 11:30:00\",\r\n          update_time: \"2024-01-19 11:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"离婚纠纷起诉书\",\r\n          cate_id: 4,\r\n          file_path: \"/uploads/documents/divorce_complaint.pdf\",\r\n          price: \"900.00\",\r\n          content: \"<p>离婚纠纷起诉书模板，包含财产分割、子女抚养等完整内容。</p><p>主要内容：</p><ul><li>夫妻感情破裂事实</li><li>财产分割方案</li><li>子女抚养安排</li><li>债务承担</li></ul>\",\r\n          create_time: \"2024-01-20 13:20:00\",\r\n          update_time: \"2024-01-20 13:20:00\"\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"知识产权侵权起诉状\",\r\n          cate_id: 5,\r\n          file_path: \"/uploads/documents/ip_infringement_complaint.docx\",\r\n          price: \"1500.00\",\r\n          content: \"<p>知识产权侵权起诉状模板，适用于商标、专利、著作权等侵权案件。</p><p>保护范围：</p><ul><li>商标权侵权</li><li>专利权侵权</li><li>著作权侵权</li><li>商业秘密侵权</li></ul>\",\r\n          create_time: \"2024-01-21 15:10:00\",\r\n          update_time: \"2024-01-21 15:10:00\"\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"公司股权纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/equity_dispute_complaint.pdf\",\r\n          price: \"2000.00\",\r\n          content: \"<p>公司股权纠纷起诉书模板，处理股东权益、公司治理等复杂商事纠纷。</p><p>争议类型：</p><ul><li>股权转让纠纷</li><li>股东知情权</li><li>利润分配争议</li><li>公司决议效力</li></ul>\",\r\n          create_time: \"2024-01-22 10:00:00\",\r\n          update_time: \"2024-01-22 10:00:00\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载测试数据...');\r\n          console.log('原始测试数据:', testData);\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.content.toLowerCase().includes(keyword)\r\n            );\r\n            console.log('搜索关键词:', keyword);\r\n            console.log('搜索结果:', filteredData);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('当前页:', _this.page);\r\n          console.log('每页大小:', _this.size);\r\n          console.log('分页后的数据:', pageData);\r\n          console.log('设置到list的数据:', _this.list);\r\n          console.log('总数:', _this.total);\r\n          console.log('加载状态:', _this.loading);\r\n        } catch (error) {\r\n          console.error('加载测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 100); // 减少延迟到100ms\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            // 确保返回的数据是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n            console.log('获取到的列表数据:', _this.list);\r\n          } else {\r\n            console.error('获取数据失败:', resp);\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.field] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-list-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left .page-title i {\r\n  font-size: 28px;\r\n}\r\n\r\n.header-left .page-subtitle {\r\n  margin: 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.add-btn:hover, .refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e4e7ed;\r\n  padding-left: 40px;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn {\r\n  border: 2px solid #e4e7ed;\r\n  color: #606266;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn:hover {\r\n  border-color: #c0c4cc;\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格区域样式 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.contract-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.contract-table >>> .el-table__row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.title-cell i {\r\n  color: #667eea;\r\n  font-size: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.time-cell i {\r\n  color: #909399;\r\n}\r\n\r\n.price-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #e6a23c;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-text i {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n  border-width: 1px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #667eea;\r\n}\r\n\r\n.preview-body {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #667eea;\r\n}\r\n\r\n.file-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  background: white;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-info i {\r\n  color: #667eea;\r\n  font-size: 18px;\r\n}\r\n\r\n.content-preview h4 {\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.content-html {\r\n  background: white;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-list-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-left {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表单对话框样式 */\r\n.form-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__close {\r\n  color: white !important;\r\n  font-size: 20px !important;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__footer {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}