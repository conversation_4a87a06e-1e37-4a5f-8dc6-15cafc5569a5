{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./src/views/pages/taocan/taocan.vue?644d", "webpack:///./node_modules/core-js/internals/array-reduce.js", "webpack:///./src/views/pages/taocan/taocan.vue", "webpack:///src/views/pages/taocan/taocan.vue", "webpack:///./src/views/pages/taocan/taocan.vue?ee97", "webpack:///./src/views/pages/taocan/taocan.vue?8cf4"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "CHROME_BUG", "FORCED", "target", "proto", "forced", "reduce", "callbackfn", "length", "arguments", "this", "undefined", "global", "classof", "module", "exports", "process", "fails", "METHOD_NAME", "argument", "method", "call", "aCallable", "toObject", "IndexedObject", "lengthOfArrayLike", "$TypeError", "TypeError", "REDUCE_EMPTY", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "i", "right", "render", "_vm", "_c", "_self", "staticClass", "_m", "attrs", "on", "getData", "_v", "_s", "total", "averagePrice", "premiumPackages", "averageYear", "slot", "$event", "editData", "search", "staticStyle", "model", "value", "keyword", "callback", "$$v", "$set", "expression", "minPrice", "maxPrice", "resetSearch", "viewMode", "directives", "name", "rawName", "loading", "_l", "filteredPackages", "pkg", "key", "id", "title", "price", "year", "sort", "desc", "services", "slice", "service", "_e", "formatDate", "create_time", "delData", "scopedSlots", "_u", "fn", "scope", "row", "$index", "size", "handleSizeChange", "handleCurrentChange", "dialogTitle", "dialogFormVisible", "ref", "ruleForm", "rules", "types", "item", "checked", "is_num", "saveLoading", "saveData", "staticRenderFns", "components", "data", "allSize", "tableData", "page", "good", "num", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "url", "computed", "Array", "isArray", "sum", "parseFloat", "Math", "round", "toLocaleString", "filter", "parseInt", "filtered", "toLowerCase", "includes", "mounted", "methods", "_this", "getInfo", "getTypes", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "postRequest", "count", "val", "dateStr", "Date", "toLocaleDateString", "$refs", "validate", "valid", "map", "setTimeout", "success", "component"], "mappings": "kHACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAIlBC,GAAcD,GAAWD,EAAiB,IAAMA,EAAiB,GACjEG,EAASD,IAAeH,EAAoB,UAIhDH,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,OAAQH,GAAU,CAClDI,OAAQ,SAAgBC,GACtB,IAAIC,EAASC,UAAUD,OACvB,OAAOZ,EAAQc,KAAMH,EAAYC,EAAQA,EAAS,EAAIC,UAAU,QAAKE,O,6DChBzE,IAAIC,EAAS,EAAQ,QACjBC,EAAU,EAAQ,QAEtBC,EAAOC,QAAsC,YAA5BF,EAAQD,EAAOI,U,kCCHhC,IAAIC,EAAQ,EAAQ,QAEpBH,EAAOC,QAAU,SAAUG,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,OAAO,GAAM,Q,kCCP7D,W,kCCCA,IAAIG,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAoB,EAAQ,QAE5BC,EAAaC,UAEbC,EAAe,8CAGfC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMxB,EAAYyB,EAAiBC,GAClD,IAAIC,EAAIX,EAASQ,GACbI,EAAOX,EAAcU,GACrB1B,EAASiB,EAAkBS,GAE/B,GADAZ,EAAUf,GACK,IAAXC,GAAgBwB,EAAkB,EAAG,MAAM,IAAIN,EAAWE,GAC9D,IAAIQ,EAAQN,EAAWtB,EAAS,EAAI,EAChC6B,EAAIP,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASC,EACT,MAGF,GADAD,GAASC,EACLP,EAAWM,EAAQ,EAAI5B,GAAU4B,EACnC,MAAM,IAAIV,EAAWE,GAGzB,KAAME,EAAWM,GAAS,EAAI5B,EAAS4B,EAAOA,GAASC,EAAOD,KAASD,IACrEF,EAAO1B,EAAW0B,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIXnB,EAAOC,QAAU,CAGflB,KAAMgC,GAAa,GAGnBS,MAAOT,GAAa,K,yCC5CtB,IAAIU,EAAS,WAAkB,IAAIC,EAAI9B,KAAK+B,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAII,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmBC,GAAG,CAAC,MAAQN,EAAIO,UAAU,CAACP,EAAIQ,GAAG,aAAa,KAAKP,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,UAAUT,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAIQ,GAAG,UAAUP,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAIQ,GAAG,kBAAkBP,EAAG,SAAS,CAACI,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIW,iBAAiBV,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAIQ,GAAG,UAAUP,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAIQ,GAAG,iBAAiBP,EAAG,SAAS,CAACI,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACE,YAAY,sBAAsBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIY,oBAAoBX,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAIQ,GAAG,UAAUP,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAIQ,GAAG,iBAAiBP,EAAG,SAAS,CAACI,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIa,aAAa,OAAOZ,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAIQ,GAAG,UAAUP,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBH,EAAIQ,GAAG,iBAAiB,IAAI,GAAGP,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,UAAUS,KAAK,UAAU,CAACb,EAAG,OAAO,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAIQ,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIgB,SAAS,MAAM,CAAChB,EAAIQ,GAAG,aAAa,KAAKP,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,MAAQL,EAAIiB,OAAO,QAAS,IAAO,CAAChB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAG,WAAW,CAACiB,YAAY,CAAC,MAAQ,SAASb,MAAM,CAAC,YAAc,aAAa,UAAY,IAAIc,MAAM,CAACC,MAAOpB,EAAIiB,OAAOI,QAASC,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIiB,OAAQ,UAAWM,IAAME,WAAW,mBAAmB,CAACxB,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIO,YAAYO,KAAK,YAAY,IAAI,GAAGb,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,kBAAkB,CAACiB,YAAY,CAAC,MAAQ,SAASb,MAAM,CAAC,YAAc,OAAO,IAAM,GAAGc,MAAM,CAACC,MAAOpB,EAAIiB,OAAOS,SAAUJ,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIiB,OAAQ,WAAYM,IAAME,WAAW,qBAAqBxB,EAAG,OAAO,CAACiB,YAAY,CAAC,OAAS,UAAU,CAAClB,EAAIQ,GAAG,OAAOP,EAAG,kBAAkB,CAACiB,YAAY,CAAC,MAAQ,SAASb,MAAM,CAAC,YAAc,OAAO,IAAM,GAAGc,MAAM,CAACC,MAAOpB,EAAIiB,OAAOU,SAAUL,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAIiB,OAAQ,WAAYM,IAAME,WAAW,sBAAsB,GAAGxB,EAAG,eAAe,CAACA,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,mBAAmBC,GAAG,CAAC,MAAQN,EAAI4B,cAAc,CAAC5B,EAAIQ,GAAG,WAAW,IAAI,IAAI,KAAKP,EAAG,UAAU,CAACE,YAAY,eAAeE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,UAAUS,KAAK,UAAU,CAACb,EAAG,OAAO,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAIQ,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,iBAAiB,CAACI,MAAM,CAAC,KAAO,SAASc,MAAM,CAACC,MAAOpB,EAAI6B,SAAUP,SAAS,SAAUC,GAAMvB,EAAI6B,SAASN,GAAKE,WAAW,aAAa,CAACxB,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACL,EAAIQ,GAAG,UAAUP,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,UAAU,CAACL,EAAIQ,GAAG,WAAW,IAAI,KAAuB,SAAjBR,EAAI6B,SAAqB5B,EAAG,MAAM,CAAC6B,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYZ,MAAOpB,EAAIiC,QAASR,WAAW,YAAYtB,YAAY,gBAAgBH,EAAIkC,GAAIlC,EAAImC,kBAAkB,SAASC,GAAK,OAAOnC,EAAG,MAAM,CAACoC,IAAID,EAAIE,GAAGnC,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAGR,EAAIS,GAAG2B,EAAIG,UAAUtC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,IAAIR,EAAIS,GAAG2B,EAAII,YAAYvC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACD,EAAIQ,GAAGR,EAAIS,GAAG2B,EAAIK,MAAM,WAAWxC,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACD,EAAIQ,GAAG,OAAOR,EAAIS,GAAG2B,EAAIM,aAAazC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACH,EAAIQ,GAAG,IAAIR,EAAIS,GAAG2B,EAAIO,MAAQ,QAAQ,OAAQP,EAAIQ,UAAYR,EAAIQ,SAAS5E,OAAS,EAAGiC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,WAAWP,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACH,EAAIkC,GAAIE,EAAIQ,SAASC,MAAM,EAAG,IAAI,SAASC,GAAS,OAAO7C,EAAG,SAAS,CAACoC,IAAIS,EAAQR,GAAGnC,YAAY,cAAcE,MAAM,CAAC,KAAO,SAAS,CAACL,EAAIQ,GAAG,IAAIR,EAAIS,GAAGqC,EAAQf,MAAM,UAAUK,EAAIQ,SAAS5E,OAAS,EAAGiC,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,KAAKR,EAAIS,GAAG2B,EAAIQ,SAAS5E,OAAS,GAAG,OAAOgC,EAAI+C,MAAM,KAAK/C,EAAI+C,OAAO9C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIgD,WAAWZ,EAAIa,mBAAmBhD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,YAAY,CAACE,YAAY,WAAWE,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIgB,SAASoB,EAAIE,OAAO,CAACtC,EAAIQ,GAAG,UAAUP,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIkD,SAAS,EAAGd,EAAIE,OAAO,CAACtC,EAAIQ,GAAG,WAAW,UAAS,GAAGR,EAAI+C,KAAuB,UAAjB/C,EAAI6B,SAAsB5B,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC6B,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYZ,MAAOpB,EAAIiC,QAASR,WAAW,YAAYtB,YAAY,eAAeE,MAAM,CAAC,KAAOL,EAAImC,mBAAmB,CAAClC,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAO8C,YAAYnD,EAAIoD,GAAG,CAAC,CAACf,IAAI,UAAUgB,GAAG,SAASC,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACH,EAAIQ,GAAGR,EAAIS,GAAG6C,EAAMC,IAAIhB,UAAUtC,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACH,EAAIQ,GAAGR,EAAIS,GAAG6C,EAAMC,IAAIZ,MAAQ,iBAAiB,MAAK,EAAM,cAAc1C,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAO8C,YAAYnD,EAAIoD,GAAG,CAAC,CAACf,IAAI,UAAUgB,GAAG,SAASC,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,IAAIR,EAAIS,GAAG6C,EAAMC,IAAIf,cAAc,MAAK,EAAM,cAAcvC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,OAAO8C,YAAYnD,EAAIoD,GAAG,CAAC,CAACf,IAAI,UAAUgB,GAAG,SAASC,GAAO,MAAO,CAACrD,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,UAAU,CAACL,EAAIQ,GAAGR,EAAIS,GAAG6C,EAAMC,IAAId,MAAM,WAAW,MAAK,EAAM,cAAcxC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,QAAQJ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,OAAO8C,YAAYnD,EAAIoD,GAAG,CAAC,CAACf,IAAI,UAAUgB,GAAG,SAASC,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIgD,WAAWM,EAAMC,IAAIN,cAAc,WAAW,MAAK,EAAM,cAAchD,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,OAAO8C,YAAYnD,EAAIoD,GAAG,CAAC,CAACf,IAAI,UAAUgB,GAAG,SAASC,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,WAAWE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIgB,SAASsC,EAAMC,IAAIjB,OAAO,CAACrC,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAIQ,GAAG,UAAUP,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIkD,QAAQI,EAAME,OAAQF,EAAMC,IAAIjB,OAAO,CAACrC,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAIQ,GAAG,WAAW,OAAO,MAAK,EAAM,eAAe,IAAI,GAAGR,EAAI+C,KAAK9C,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACI,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,GAAI,KAAK,YAAYL,EAAIyD,KAAK,OAAS,0CAA0C,MAAQzD,EAAIU,MAAM,WAAa,IAAIJ,GAAG,CAAC,cAAcN,EAAI0D,iBAAiB,iBAAiB1D,EAAI2D,wBAAwB,KAAK1D,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,MAAQL,EAAI4D,YAAY,QAAU5D,EAAI6D,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOvD,GAAG,CAAC,iBAAiB,SAASS,GAAQf,EAAI6D,kBAAkB9C,KAAU,CAACd,EAAG,UAAU,CAAC6D,IAAI,WAAWzD,MAAM,CAAC,MAAQL,EAAI+D,SAAS,MAAQ/D,EAAIgE,MAAM,cAAc,UAAU,CAAC/D,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,UAAU,aAAe,OAAOc,MAAM,CAACC,MAAOpB,EAAI+D,SAASxB,MAAOjB,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAI+D,SAAU,QAASxC,IAAME,WAAW,qBAAqB,IAAI,GAAGxB,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,kBAAkB,CAACiB,YAAY,CAAC,MAAQ,QAAQb,MAAM,CAAC,IAAM,EAAE,IAAM,OAAO,UAAY,EAAE,YAAc,SAASc,MAAM,CAACC,MAAOpB,EAAI+D,SAASvB,MAAOlB,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAI+D,SAAU,QAASxC,IAAME,WAAW,qBAAqB,IAAI,IAAI,GAAGxB,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACJ,EAAG,kBAAkB,CAACiB,YAAY,CAAC,MAAQ,QAAQb,MAAM,CAAC,IAAM,EAAE,IAAM,GAAG,YAAc,SAASc,MAAM,CAACC,MAAOpB,EAAI+D,SAAStB,KAAMnB,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAI+D,SAAU,OAAQxC,IAAME,WAAW,oBAAoB,IAAI,GAAGxB,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,kBAAkB,CAACiB,YAAY,CAAC,MAAQ,QAAQb,MAAM,CAAC,IAAM,EAAE,IAAM,IAAI,YAAc,aAAac,MAAM,CAACC,MAAOpB,EAAI+D,SAASrB,KAAMpB,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAI+D,SAAU,OAAQxC,IAAME,WAAW,oBAAoB,IAAI,IAAI,GAAGxB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACJ,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,gBAAgBP,EAAG,MAAM,CAACE,YAAY,gBAAgBH,EAAIkC,GAAIlC,EAAIiE,OAAO,SAASC,EAAKtE,GAAO,OAAOK,EAAG,MAAM,CAACoC,IAAIzC,EAAMO,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQ6D,EAAK5B,IAAInB,MAAM,CAACC,MAAO8C,EAAKC,QAAS7C,SAAS,SAAUC,GAAMvB,EAAIwB,KAAK0C,EAAM,UAAW3C,IAAME,WAAW,iBAAiB,CAACzB,EAAIQ,GAAG,IAAIR,EAAIS,GAAGyD,EAAK3B,OAAO,QAAQ,GAAmB,GAAf2B,EAAKE,QAAeF,EAAKC,QAASlE,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,kBAAkB,CAACI,MAAM,CAAC,IAAM,EAAE,IAAM,IAAI,KAAO,QAAQ,YAAc,MAAMc,MAAM,CAACC,MAAO8C,EAAK9C,MAAOE,SAAS,SAAUC,GAAMvB,EAAIwB,KAAK0C,EAAM,QAAS3C,IAAME,WAAW,gBAAgBxB,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAIQ,GAAG,QAAQ,GAAI0D,EAAKC,QAASlE,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,YAAY,CAACL,EAAIQ,GAAG,WAAW,GAAGR,EAAI+C,UAAS,OAAO9C,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,eAAe,aAAe,OAAOc,MAAM,CAACC,MAAOpB,EAAI+D,SAASpB,KAAMrB,SAAS,SAAUC,GAAMvB,EAAIwB,KAAKxB,EAAI+D,SAAU,OAAQxC,IAAME,WAAW,oBAAoB,IAAI,GAAGxB,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUS,KAAK,UAAU,CAACb,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASS,GAAQf,EAAI6D,mBAAoB,KAAS,CAAC7D,EAAIQ,GAAG,QAAQP,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,QAAUL,EAAIqE,aAAa/D,GAAG,CAAC,MAAQ,SAASS,GAAQ,OAAOf,EAAIsE,cAAc,CAACtE,EAAIQ,GAAG,WAAW,IAAI,IAAI,IAEzpX+D,EAAkB,CAAC,WAAY,IAAIvE,EAAI9B,KAAK+B,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBH,EAAIQ,GAAG,cAAcP,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAIQ,GAAG,yBC+Z1O,G,UAAA,CACfuB,KAAA,oBACAyC,WAAA,GACAC,OACA,OACAC,QAAA,OACAC,UAAA,GACA1C,SAAA,EACAvB,MAAA,EACAkE,KAAA,EACAnB,KAAA,GACA5B,SAAA,OACAwC,aAAA,EACApD,OAAA,CACAI,QAAA,GACAK,SAAA,KACAC,SAAA,MAEAoC,SAAA,CACAxB,MAAA,GACAC,MAAA,GACAC,KAAA,GACAE,KAAA,GACAD,KAAA,EACAmC,KAAA,GACAC,IAAA,IAEAA,IAAA,EACAd,MAAA,CACAzB,MAAA,CACA,CACAwC,UAAA,EACAC,QAAA,UACAC,QAAA,SAGAzC,MAAA,CACA,CACAuC,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAxC,KAAA,CACA,CACAsC,UAAA,EACAC,QAAA,QACAC,QAAA,UAIApB,mBAAA,EACAqB,eAAA,QACAC,IAAA,WACAlB,MAAA,KAGAmB,SAAA,CAEAzE,eACA,IAAA0E,MAAAC,QAAA,KAAAX,YAAA,SAAAA,UAAA3G,OAAA,UACA,MAAA0C,EAAA,KAAAiE,UAAA7G,OAAA,CAAAyH,EAAArB,IAAAqB,GAAAC,WAAAtB,EAAA1B,QAAA,MACA,OAAAiD,KAAAC,MAAAhF,EAAA,KAAAiE,UAAA3G,QAAA2H,kBAEA/E,kBACA,OAAAyE,MAAAC,QAAA,KAAAX,WAAA,KAAAA,UAAAiB,OAAA1B,GAAAsB,WAAAtB,EAAA1B,OAAA,KAAAxE,OAAA,GAEA6C,cACA,IAAAwE,MAAAC,QAAA,KAAAX,YAAA,SAAAA,UAAA3G,OAAA,SACA,MAAA0C,EAAA,KAAAiE,UAAA7G,OAAA,CAAAyH,EAAArB,IAAAqB,GAAAM,SAAA3B,EAAAzB,OAAA,MACA,OAAAgD,KAAAC,MAAAhF,EAAA,KAAAiE,UAAA3G,SAEA4F,cACA,YAAAG,SAAAzB,GAAA,eAEAH,mBACA,IAAAkD,MAAAC,QAAA,KAAAX,WAAA,SACA,IAAAmB,EAAA,KAAAnB,UAGA,QAAA1D,OAAAI,QAAA,CACA,MAAAA,EAAA,KAAAJ,OAAAI,QAAA0E,cACAD,IAAAF,OAAA1B,GACAA,EAAA3B,OAAA2B,EAAA3B,MAAAwD,cAAAC,SAAA3E,IACA6C,EAAAvB,MAAAuB,EAAAvB,KAAAoD,cAAAC,SAAA3E,IAaA,OARA,YAAAJ,OAAAS,WACAoE,IAAAF,OAAA1B,GAAAsB,WAAAtB,EAAA1B,QAAA,KAAAvB,OAAAS,WAGA,YAAAT,OAAAU,WACAmE,IAAAF,OAAA1B,GAAAsB,WAAAtB,EAAA1B,QAAA,KAAAvB,OAAAU,WAGAmE,IAGAG,UACA,KAAA1F,WAEA2F,QAAA,CACAlF,SAAAsB,GACA,IAAA6D,EAAA,KACA,GAAA7D,EACA,KAAA8D,QAAA9D,IAEA,KAAAyB,SAAA,CACAxB,MAAA,GACAC,MAAA,GACAC,KAAA,GACAE,KAAA,GACAD,KAAA,EACAmC,KAAA,GACAC,IAAA,IAEAqB,EAAAE,YAEAF,EAAAtC,mBAAA,GAEAuC,QAAA9D,GACA,IAAA6D,EAAA,KACAA,EAAAG,WAAAH,EAAAhB,IAAA,WAAA7C,GAAAiE,KAAAC,IACAA,IACAL,EAAApC,SAAAyC,EAAA/B,KACA0B,EAAAlC,MAAAkC,EAAApC,SAAAe,QAIA5B,QAAAtD,EAAA0C,GACA,KAAAmE,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,KAAAM,cAAA,KAAA1B,IAAA,aAAA7C,GAAAiE,KAAAC,IACA,KAAAA,EAAAM,OACA,KAAAC,SAAA,CACAH,KAAA,UACA5B,QAAA,UAEA,KAAAL,UAAAqC,OAAApH,EAAA,QAIAqH,MAAA,KACA,KAAAF,SAAA,CACAH,KAAA,QACA5B,QAAA,aAIAqB,WACA,KAAAa,YAAA,oBAAAX,KAAAC,IACA,KAAAA,EAAAM,OACA,KAAA7C,MAAAuC,EAAA/B,SAIAlE,UACA,IAAA4F,EAAA,KACAA,EAAAlE,SAAA,EACAkE,EACAe,YACAf,EAAAhB,IAAA,cAAAgB,EAAAvB,KAAA,SAAAuB,EAAA1C,KACA0C,EAAAlF,QAEAsF,KAAAC,IACA,KAAAA,EAAAM,OACAX,EAAAxB,UAAA6B,EAAA/B,KACA0B,EAAAzF,MAAA8F,EAAAW,OAEAhB,EAAAlE,SAAA,KAGAyB,iBAAA0D,GACA,KAAA3D,KAAA2D,EACA,KAAA7G,WAEAoD,oBAAAyD,GACA,KAAAxC,KAAAwC,EACA,KAAA7G,WAEAyC,WAAAqE,GACA,OAAAA,EACA,IAAAC,KAAAD,GAAAE,mBAAA,SADA,OAGA3F,cACA,KAAAX,OAAA,CACAI,QAAA,GACAK,SAAA,KACAC,SAAA,MAEA,KAAAiD,KAAA,EACA,KAAArE,WAEA+D,WACA,KAAAkD,MAAAzD,SAAA0D,SAAAC,IACA,IAAAA,EAeA,SAdA,KAAArD,aAAA,EAEA,KAAAN,SAAAc,KAAA,KAAAZ,MACA2B,OAAAgB,KAAAzC,SACAwD,IAAAf,KAAAtE,IAGAsF,WAAA,KACA,KAAAvD,aAAA,EACA,KAAAR,mBAAA,EACA,KAAAkD,SAAAc,QAAA,QACA,KAAAtH,WACA,WCvnB6W,I,wBCQzWuH,EAAY,eACd,EACA/H,EACAwE,GACA,EACA,KACA,WACA,MAIa,aAAAuD,E", "file": "js/chunk-7963da7c.21bd86b0.js", "sourcesContent": ["'use strict';\r\nvar $ = require('../internals/export');\r\nvar $reduce = require('../internals/array-reduce').left;\r\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\r\nvar CHROME_VERSION = require('../internals/engine-v8-version');\r\nvar IS_NODE = require('../internals/engine-is-node');\r\n\r\n// Chrome 80-82 has a critical bug\r\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\r\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\r\nvar FORCED = CHROME_BUG || !arrayMethodIsStrict('reduce');\r\n\r\n// `Array.prototype.reduce` method\r\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n$({ target: 'Array', proto: true, forced: FORCED }, {\r\n  reduce: function reduce(callbackfn /* , initialValue */) {\r\n    var length = arguments.length;\r\n    return $reduce(this, callbackfn, length, length > 1 ? arguments[1] : undefined);\r\n  }\r\n});\r\n", "'use strict';\r\nvar global = require('../internals/global');\r\nvar classof = require('../internals/classof-raw');\r\n\r\nmodule.exports = classof(global.process) === 'process';\r\n", "'use strict';\r\nvar fails = require('../internals/fails');\r\n\r\nmodule.exports = function (METHOD_NAME, argument) {\r\n  var method = [][METHOD_NAME];\r\n  return !!method && fails(function () {\r\n    // eslint-disable-next-line no-useless-call -- required for testing\r\n    method.call(null, argument || function () { return 1; }, 1);\r\n  });\r\n};\r\n", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=style&index=0&id=4b11a6a1&prod&scoped=true&lang=css\"", "'use strict';\r\nvar aCallable = require('../internals/a-callable');\r\nvar toObject = require('../internals/to-object');\r\nvar IndexedObject = require('../internals/indexed-object');\r\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\r\n\r\nvar $TypeError = TypeError;\r\n\r\nvar REDUCE_EMPTY = 'Reduce of empty array with no initial value';\r\n\r\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\r\nvar createMethod = function (IS_RIGHT) {\r\n  return function (that, callbackfn, argumentsLength, memo) {\r\n    var O = toObject(that);\r\n    var self = IndexedObject(O);\r\n    var length = lengthOfArrayLike(O);\r\n    aCallable(callbackfn);\r\n    if (length === 0 && argumentsLength < 2) throw new $TypeError(REDUCE_EMPTY);\r\n    var index = IS_RIGHT ? length - 1 : 0;\r\n    var i = IS_RIGHT ? -1 : 1;\r\n    if (argumentsLength < 2) while (true) {\r\n      if (index in self) {\r\n        memo = self[index];\r\n        index += i;\r\n        break;\r\n      }\r\n      index += i;\r\n      if (IS_RIGHT ? index < 0 : length <= index) {\r\n        throw new $TypeError(REDUCE_EMPTY);\r\n      }\r\n    }\r\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\r\n      memo = callbackfn(memo, self[index], index, O);\r\n    }\r\n    return memo;\r\n  };\r\n};\r\n\r\nmodule.exports = {\r\n  // `Array.prototype.reduce` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n  left: createMethod(false),\r\n  // `Array.prototype.reduceRight` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\r\n  right: createMethod(true)\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"package-management-container\"},[_c('div',{staticClass:\"page-header\"},[_vm._m(0),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.getData}},[_vm._v(\" 刷新数据 \")])],1)]),_c('div',{staticClass:\"stats-section\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon total-icon\"},[_c('i',{staticClass:\"el-icon-box\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.total))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"套餐总数\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +12% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon price-icon\"},[_c('i',{staticClass:\"el-icon-money\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(\"¥\"+_vm._s(_vm.averagePrice))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"平均价格\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +5% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon premium-icon\"},[_c('i',{staticClass:\"el-icon-star-on\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.premiumPackages))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"高端套餐\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +8% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon duration-icon\"},[_c('i',{staticClass:\"el-icon-time\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.averageYear)+\"年\")]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"平均年限\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" 稳定 \")])])])])],1)],1),_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-search\"}),_vm._v(\" 搜索管理 \")]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增套餐 \")])],1)]),_c('div',{staticClass:\"search-section\"},[_c('el-form',{staticClass:\"search-form\",attrs:{\"model\":_vm.search,\"inline\":true}},[_c('el-form-item',{attrs:{\"label\":\"关键词\"}},[_c('el-input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":\"请输入套餐名称或描述\",\"clearable\":\"\"},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.getData()}},slot:\"append\"})],1)],1),_c('el-form-item',{attrs:{\"label\":\"价格范围\"}},[_c('el-input-number',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"最低价格\",\"min\":0},model:{value:(_vm.search.minPrice),callback:function ($$v) {_vm.$set(_vm.search, \"minPrice\", $$v)},expression:\"search.minPrice\"}}),_c('span',{staticStyle:{\"margin\":\"0 8px\"}},[_vm._v(\"-\")]),_c('el-input-number',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"最高价格\",\"min\":0},model:{value:(_vm.search.maxPrice),callback:function ($$v) {_vm.$set(_vm.search, \"maxPrice\", $$v)},expression:\"search.maxPrice\"}})],1),_c('el-form-item',[_c('el-button',{attrs:{\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.resetSearch}},[_vm._v(\" 重置 \")])],1)],1)],1)]),_c('el-card',{staticClass:\"package-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-tickets\"}),_vm._v(\" 套餐列表 \")]),_c('div',{staticClass:\"view-controls\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.viewMode),callback:function ($$v) {_vm.viewMode=$$v},expression:\"viewMode\"}},[_c('el-radio-button',{attrs:{\"label\":\"grid\"}},[_vm._v(\"卡片视图\")]),_c('el-radio-button',{attrs:{\"label\":\"table\"}},[_vm._v(\"表格视图\")])],1)],1)]),(_vm.viewMode === 'grid')?_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"package-grid\"},_vm._l((_vm.filteredPackages),function(pkg){return _c('div',{key:pkg.id,staticClass:\"package-item\"},[_c('div',{staticClass:\"package-header\"},[_c('div',{staticClass:\"package-title\"},[_vm._v(_vm._s(pkg.title))]),_c('div',{staticClass:\"package-price\"},[_vm._v(\"¥\"+_vm._s(pkg.price))])]),_c('div',{staticClass:\"package-content\"},[_c('div',{staticClass:\"package-info\"},[_c('div',{staticClass:\"info-item\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(pkg.year)+\"年服务\")])]),_c('div',{staticClass:\"info-item\"},[_c('i',{staticClass:\"el-icon-sort\"}),_c('span',[_vm._v(\"排序: \"+_vm._s(pkg.sort))])])]),_c('div',{staticClass:\"package-desc\"},[_vm._v(\" \"+_vm._s(pkg.desc || '暂无描述')+\" \")]),(pkg.services && pkg.services.length > 0)?_c('div',{staticClass:\"package-features\"},[_c('div',{staticClass:\"feature-title\"},[_vm._v(\"包含服务:\")]),_c('div',{staticClass:\"feature-list\"},[_vm._l((pkg.services.slice(0, 3)),function(service){return _c('el-tag',{key:service.id,staticClass:\"feature-tag\",attrs:{\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(service.name)+\" \")])}),(pkg.services.length > 3)?_c('span',{staticClass:\"more-services\"},[_vm._v(\" +\"+_vm._s(pkg.services.length - 3)+\" \")]):_vm._e()],2)]):_vm._e()]),_c('div',{staticClass:\"package-footer\"},[_c('div',{staticClass:\"package-meta\"},[_c('span',{staticClass:\"create-time\"},[_vm._v(_vm._s(_vm.formatDate(pkg.create_time)))])]),_c('div',{staticClass:\"package-actions\"},[_c('el-button',{staticClass:\"edit-btn\",attrs:{\"type\":\"text\"},on:{\"click\":function($event){return _vm.editData(pkg.id)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{staticClass:\"delete-btn\",attrs:{\"type\":\"text\"},on:{\"click\":function($event){return _vm.delData(-1, pkg.id)}}},[_vm._v(\" 删除 \")])],1)])])}),0):_vm._e(),(_vm.viewMode === 'table')?_c('div',[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"modern-table\",attrs:{\"data\":_vm.filteredPackages}},[_c('el-table-column',{attrs:{\"label\":\"套餐信息\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"table-package-info\"},[_c('div',{staticClass:\"table-package-title\"},[_vm._v(_vm._s(scope.row.title))]),_c('div',{staticClass:\"table-package-desc\"},[_vm._v(_vm._s(scope.row.desc || '暂无描述'))])])]}}],null,false,3323292265)}),_c('el-table-column',{attrs:{\"label\":\"价格\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"price-display\"},[_vm._v(\"¥\"+_vm._s(scope.row.price))])]}}],null,false,2696510062)}),_c('el-table-column',{attrs:{\"prop\":\"year\",\"label\":\"年限\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"info\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.year)+\"年\")])]}}],null,false,3902229530)}),_c('el-table-column',{attrs:{\"prop\":\"sort\",\"label\":\"排序\",\"width\":\"80\"}}),_c('el-table-column',{attrs:{\"label\":\"创建时间\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-info\"},[_vm._v(\" \"+_vm._s(_vm.formatDate(scope.row.create_time))+\" \")])]}}],null,false,2692560985)}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{staticClass:\"edit-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\" 编辑 \")]),_c('el-button',{staticClass:\"delete-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_c('i',{staticClass:\"el-icon-delete\"}),_vm._v(\" 删除 \")])],1)]}}],null,false,1323445013)})],1)],1):_vm._e(),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[12, 20, 50, 100],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)]),_c('el-dialog',{staticClass:\"edit-dialog\",attrs:{\"title\":_vm.dialogTitle,\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"套餐名称\",\"prop\":\"title\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入套餐名称\",\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"套餐价格\",\"prop\":\"price\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"max\":999999,\"precision\":2,\"placeholder\":\"请输入价格\"},model:{value:(_vm.ruleForm.price),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"price\", $$v)},expression:\"ruleForm.price\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"服务年限\",\"prop\":\"year\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":1,\"max\":10,\"placeholder\":\"请输入年限\"},model:{value:(_vm.ruleForm.year),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"year\", $$v)},expression:\"ruleForm.year\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"排序\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"max\":999,\"placeholder\":\"数字越小排序越靠前\"},model:{value:(_vm.ruleForm.sort),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"sort\", $$v)},expression:\"ruleForm.sort\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"套餐内容\",\"prop\":\"good\"}},[_c('div',{staticClass:\"service-selection\"},[_c('div',{staticClass:\"service-title\"},[_vm._v(\"选择包含的服务类型:\")]),_c('div',{staticClass:\"service-list\"},_vm._l((_vm.types),function(item,index){return _c('div',{key:index,staticClass:\"service-item\"},[_c('div',{staticClass:\"service-checkbox\"},[_c('el-checkbox',{attrs:{\"label\":item.id},model:{value:(item.checked),callback:function ($$v) {_vm.$set(item, \"checked\", $$v)},expression:\"item.checked\"}},[_vm._v(\" \"+_vm._s(item.title)+\" \")])],1),(item.is_num == 1 && item.checked)?_c('div',{staticClass:\"service-input\"},[_c('el-input-number',{attrs:{\"min\":1,\"max\":999,\"size\":\"small\",\"placeholder\":\"次数\"},model:{value:(item.value),callback:function ($$v) {_vm.$set(item, \"value\", $$v)},expression:\"item.value\"}}),_c('span',{staticClass:\"input-suffix\"},[_vm._v(\"次\")])],1):(item.checked)?_c('div',{staticClass:\"service-unlimited\"},[_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"success\"}},[_vm._v(\"不限次数\")])],1):_vm._e()])}),0)])]),_c('el-form-item',{attrs:{\"label\":\"套餐描述\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入套餐详细描述...\",\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.saveLoading},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\" 保存 \")])],1)],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-box\"}),_vm._v(\" 套餐类型管理 \")]),_c('div',{staticClass:\"page-subtitle\"},[_vm._v(\"管理法律服务套餐产品和价格配置\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"package-management-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-box\"></i>\r\n          套餐类型管理\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务套餐产品和价格配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"getData\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-box\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">套餐总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon price-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ averagePrice }}</div>\r\n              <div class=\"stat-label\">平均价格</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon premium-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ premiumPackages }}</div>\r\n              <div class=\"stat-label\">高端套餐</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon duration-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ averageYear }}年</div>\r\n              <div class=\"stat-label\">平均年限</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 稳定\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新增套餐\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入套餐名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"价格范围\">\r\n            <el-input-number \r\n              v-model=\"search.minPrice\" \r\n              placeholder=\"最低价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n            <span style=\"margin: 0 8px\">-</span>\r\n            <el-input-number \r\n              v-model=\"search.maxPrice\" \r\n              placeholder=\"最高价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 套餐展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"package-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          套餐列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"package-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"pkg in filteredPackages\" \r\n          :key=\"pkg.id\"\r\n          class=\"package-item\"\r\n        >\r\n          <div class=\"package-header\">\r\n            <div class=\"package-title\">{{ pkg.title }}</div>\r\n            <div class=\"package-price\">¥{{ pkg.price }}</div>\r\n          </div>\r\n          \r\n          <div class=\"package-content\">\r\n            <div class=\"package-info\">\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ pkg.year }}年服务</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-sort\"></i>\r\n                <span>排序: {{ pkg.sort }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"package-desc\">\r\n              {{ pkg.desc || '暂无描述' }}\r\n            </div>\r\n            \r\n            <div class=\"package-features\" v-if=\"pkg.services && pkg.services.length > 0\">\r\n              <div class=\"feature-title\">包含服务:</div>\r\n              <div class=\"feature-list\">\r\n                <el-tag \r\n                  v-for=\"service in pkg.services.slice(0, 3)\" \r\n                  :key=\"service.id\"\r\n                  size=\"mini\"\r\n                  class=\"feature-tag\"\r\n                >\r\n                  {{ service.name }}\r\n                </el-tag>\r\n                <span v-if=\"pkg.services.length > 3\" class=\"more-services\">\r\n                  +{{ pkg.services.length - 3 }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"package-footer\">\r\n            <div class=\"package-meta\">\r\n              <span class=\"create-time\">{{ formatDate(pkg.create_time) }}</span>\r\n            </div>\r\n            <div class=\"package-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(pkg.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, pkg.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n          :data=\"filteredPackages\" \r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"套餐信息\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-package-info\">\r\n                <div class=\"table-package-title\">{{ scope.row.title }}</div>\r\n                <div class=\"table-package-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"价格\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"price-display\">¥{{ scope.row.price }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"year\" label=\"年限\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"info\" size=\"small\">{{ scope.row.year }}年</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" />\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐名称\" prop=\"title\">\r\n              <el-input \r\n                v-model=\"ruleForm.title\" \r\n                placeholder=\"请输入套餐名称\"\r\n                autocomplete=\"off\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐价格\" prop=\"price\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.price\"\r\n                :min=\"0\"\r\n                :max=\"999999\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入价格\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务年限\" prop=\"year\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.year\"\r\n                :min=\"1\"\r\n                :max=\"10\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入年限\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number\r\n                v-model=\"ruleForm.sort\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"数字越小排序越靠前\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-form-item label=\"套餐内容\" prop=\"good\">\r\n          <div class=\"service-selection\">\r\n            <div class=\"service-title\">选择包含的服务类型:</div>\r\n            <div class=\"service-list\">\r\n              <div \r\n              v-for=\"(item, index) in types\"\r\n              :key=\"index\"\r\n                class=\"service-item\"\r\n            >\r\n                <div class=\"service-checkbox\">\r\n                  <el-checkbox v-model=\"item.checked\" :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n                </div>\r\n                <div class=\"service-input\" v-if=\"item.is_num == 1 && item.checked\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                    size=\"small\"\r\n                    placeholder=\"次数\"\r\n                  />\r\n                  <span class=\"input-suffix\">次</span>\r\n                </div>\r\n                <div class=\"service-unlimited\" v-else-if=\"item.checked\">\r\n                  <el-tag size=\"small\" type=\"success\">不限次数</el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"套餐描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入套餐详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"PackageManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写套餐名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"120px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    averagePrice() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return '0';\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\r\n      return Math.round(total / this.tableData.length).toLocaleString();\r\n    },\r\n    premiumPackages() {\r\n      return Array.isArray(this.tableData) ? this.tableData.filter(item => parseFloat(item.price) > 10000).length : 0;\r\n    },\r\n    averageYear() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return 1;\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseInt(item.year) || 1), 0);\r\n      return Math.round(total / this.tableData.length);\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑套餐' : '新增套餐';\r\n    },\r\n    filteredPackages() {\r\n      if (!Array.isArray(this.tableData)) return [];\r\n      let filtered = this.tableData;\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filtered = filtered.filter(item =>\r\n          (item.title && item.title.toLowerCase().includes(keyword)) ||\r\n          (item.desc && item.desc.toLowerCase().includes(keyword))\r\n        );\r\n      }\r\n\r\n      // 价格范围筛选\r\n      if (this.search.minPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) >= this.search.minPrice);\r\n      }\r\n\r\n      if (this.search.maxPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) <= this.search.maxPrice);\r\n      }\r\n\r\n      return filtered;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n          // 处理服务类型选择\r\n          this.ruleForm.good = this.types\r\n            .filter(type => type.checked)\r\n            .map(type => type.id);\r\n          \r\n          // 模拟保存操作\r\n          setTimeout(() => {\r\n            this.saveLoading = false;\r\n            this.dialogFormVisible = false;\r\n            this.$message.success('保存成功');\r\n            this.getData();\r\n          }, 1000);\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.package-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.price-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.premium-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.duration-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .package-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 套餐网格视图 */\r\n.package-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.package-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.package-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.package-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.package-price {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #ffd04b;\r\n}\r\n\r\n.package-content {\r\n  padding: 20px;\r\n}\r\n\r\n.package-info {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.package-desc {\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  min-height: 40px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.package-features {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.feature-title {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.feature-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  align-items: center;\r\n}\r\n\r\n.feature-tag {\r\n  background: #f0f9ff;\r\n  color: #0369a1;\r\n  border: 1px solid #e0f2fe;\r\n}\r\n\r\n.more-services {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  font-style: italic;\r\n}\r\n\r\n.package-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.package-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.package-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-package-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-package-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.price-display {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 服务选择区域 */\r\n.service-selection {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background: #fafafa;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-item:hover {\r\n  border-color: #409EFF;\r\n  background: #f8f9ff;\r\n}\r\n\r\n.service-checkbox {\r\n  flex: 1;\r\n}\r\n\r\n.service-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.input-suffix {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.service-unlimited {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .package-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n  width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .package-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .package-header {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-input {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./taocan.vue?vue&type=template&id=4b11a6a1&scoped=true\"\nimport script from \"./taocan.vue?vue&type=script&lang=js\"\nexport * from \"./taocan.vue?vue&type=script&lang=js\"\nimport style0 from \"./taocan.vue?vue&type=style&index=0&id=4b11a6a1&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4b11a6a1\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}