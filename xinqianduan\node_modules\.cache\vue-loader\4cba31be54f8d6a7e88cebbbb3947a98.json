{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=style&index=0&id=0bf8986a&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748616302957}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["debts.vue"], "names": [], "mappings": ";AA0qDA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "debts.vue", "sourceRoot": "src/views/pages/debt", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <div @click=\"viewUserData(scope.row.uid)\" class=\"clickable-text\">{{scope.row.users.nickname}}</div>\r\n            </template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\" class=\"clickable-text\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <!-- 债务人编辑抽屉 -->\r\n    <el-drawer\r\n      title=\"债务人管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleDrawerClose\">\r\n      <div class=\"drawer-content-wrapper\">\r\n        <!-- 左侧导航菜单 -->\r\n        <div class=\"drawer-sidebar\">\r\n          <el-menu\r\n            :default-active=\"activeDebtTab\"\r\n            class=\"drawer-menu\"\r\n            @select=\"handleDebtTabSelect\">\r\n            <el-menu-item index=\"details\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>债务人详情</span>\r\n            </el-menu-item>\r\n            <el-submenu index=\"evidence\">\r\n              <template slot=\"title\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据</span>\r\n              </template>\r\n              <el-menu-item index=\"evidence-all\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>全部</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-video\">\r\n                <i class=\"el-icon-video-camera\"></i>\r\n                <span>视频</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-image\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                <span>图片</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-audio\">\r\n                <i class=\"el-icon-microphone\"></i>\r\n                <span>语音</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-document\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span>文档</span>\r\n              </el-menu-item>\r\n            </el-submenu>\r\n          </el-menu>\r\n        </div>\r\n\r\n        <!-- 右侧内容区域 -->\r\n        <div class=\"drawer-content\">\r\n          <!-- 债务人详情标签页 -->\r\n          <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                债务人详情\r\n              </div>\r\n              \r\n              <div v-if=\"ruleForm.is_user == 1\">\r\n                <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n              </div>\r\n              \r\n              <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 20px;\">\r\n                <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" style=\"margin-top: 20px;\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择用户\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n                      <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">选择用户</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"用户信息\" v-if=\"ruleForm.utel\">\r\n                      {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人姓名\">\r\n                      <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人电话\">\r\n                      <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"身份证号码\">\r\n                      <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务金额\">\r\n                      <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"债务人地址\">\r\n                  <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"案由描述\">\r\n                  <el-input v-model=\"ruleForm.case_des\" autocomplete=\"off\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n              </el-form>\r\n\r\n              <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 30px;\">\r\n                <el-descriptions-item>\r\n                  <el-table :data=\"ruleForm.debttrans\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"></el-table-column>\r\n                    <el-table-column prop=\"status_name\" label=\"跟进状态\"></el-table-column>\r\n                    <el-table-column prop=\"type_name\" label=\"跟进类型\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额（元）\"></el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"></el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">移除</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"drawer-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确定</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 证据管理标签页 -->\r\n          <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                {{ getEvidenceTitle() }}\r\n                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                  <i class=\"el-icon-plus\"></i> 上传证据\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 证据列表 -->\r\n              <div class=\"evidence-container\">\r\n                <!-- 身份证照片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>身份证照片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('cards')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传身份证\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.cards && ruleForm.cards.length > 0\">\r\n                      <div v-for=\"(item7, index7) in ruleForm.cards\" :key=\"index7\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <img :src=\"item7\" @click=\"showImage(item7)\" class=\"evidence-image\" />\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item7, 'cards', index7)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据图片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据图片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('images')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传图片\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.images && ruleForm.images.length > 0\">\r\n                      <div v-for=\"(item5, index5) in ruleForm.images\" :key=\"index5\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <el-image style=\"width: 100%; height: 150px;\" :src=\"item5\" :preview-src-list=\"ruleForm.images\" fit=\"cover\"></el-image>\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item5, 'images', index5)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的图片 -->\r\n                    <div v-if=\"ruleForm.del_images && ruleForm.del_images.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的图片</h5>\r\n                      <div class=\"evidence-grid\">\r\n                        <div v-for=\"(item8, index8) in ruleForm.del_images\" :key=\"index8\" class=\"evidence-item\">\r\n                          <div class=\"evidence-preview\">\r\n                            <el-image style=\"width: 100%; height: 150px;\" :src=\"item8\" :preview-src-list=\"ruleForm.del_images\" fit=\"cover\"></el-image>\r\n                          </div>\r\n                          <div class=\"evidence-actions\">\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item8, 'del_images', index8)\">删除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据文件 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-document'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据文件</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('attach_path')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传文件\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"file-list\" v-if=\"ruleForm.attach_path && ruleForm.attach_path.length > 0\">\r\n                      <div v-for=\"(item6, index6) in ruleForm.attach_path\" :key=\"index6\" class=\"file-item\" v-if=\"item6\">\r\n                        <div class=\"file-icon\">\r\n                          <i class=\"el-icon-document file-type-icon\"></i>\r\n                        </div>\r\n                        <div class=\"file-info\">\r\n                          <div class=\"file-name\">文件{{ index6 + 1 }}</div>\r\n                        </div>\r\n                        <div class=\"file-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                          </el-button>\r\n                          <el-button type=\"success\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item6, 'attach_path', index6)\">移除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的文件 -->\r\n                    <div v-if=\"ruleForm.del_attach_path && ruleForm.del_attach_path.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的文件</h5>\r\n                      <div class=\"file-list\">\r\n                        <div v-for=\"(item9, index9) in ruleForm.del_attach_path\" :key=\"index9\" class=\"file-item\" v-if=\"item9\">\r\n                          <div class=\"file-icon\">\r\n                            <i class=\"el-icon-document file-type-icon\"></i>\r\n                          </div>\r\n                          <div class=\"file-info\">\r\n                            <div class=\"file-name\">文件{{ index9 + 1 }}</div>\r\n                          </div>\r\n                          <div class=\"file-actions\">\r\n                            <el-button type=\"primary\" size=\"mini\">\r\n                              <a :href=\"item9\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                            </el-button>\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item9, 'del_attach_path', index9)\">移除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空状态 -->\r\n                <div v-if=\"!hasEvidence()\" class=\"no-evidence\">\r\n                  <i class=\"el-icon-folder-opened\"></i>\r\n                  <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                  <br>\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                    <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n            title=\"用户列表\"\r\n            :visible.sync=\"dialogUserFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\">\r\n\r\n        <el-row style=\"width: 300px\">\r\n            <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"searchUserData()\"\r\n                ></el-button>\r\n            </el-input>\r\n        </el-row>\r\n\r\n        <el-table\r\n                :data=\"listUser\"\r\n                style=\"width: 100%; margin-top: 10px\"\r\n                size=\"mini\"\r\n                @current-change=\"selUserData\"\r\n        >\r\n            <el-table-column label=\"选择\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n            <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n            <el-table-column prop=\"\" label=\"头像\">\r\n                <template slot-scope=\"scope\">\r\n                    <div>\r\n\r\n                        <el-row v-if=\"scope.row.headimg==''\">\r\n                            <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                        </el-row>\r\n                        <el-row v-else>\r\n                            <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                        </el-row>\r\n\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n            <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n            <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n            <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n            <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        </el-table>\r\n\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n      <!-- 债务人详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewDebtDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleDebtDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span>债务人详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeDebtDetailTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleDebtDetailTabSelect\">\r\n              <el-menu-item index=\"details\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>债务详情</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"progress\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>跟进记录</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据材料</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"documents\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>相关文档</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\" style=\"overflow-x: auto; max-width: 100%;\">\r\n                <!-- 债务详情 -->\r\n                <div v-if=\"activeDebtDetailTab === 'details'\">\r\n                  <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n                </div>\r\n\r\n                <!-- 跟进记录 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'progress'\">\r\n                  <h3 class=\"section-title\">跟进记录</h3>\r\n                  <el-timeline>\r\n                    <el-timeline-item timestamp=\"2024-01-15 10:30\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>电话联系</h4>\r\n                        <p>已与债务人取得联系，对方表示将在本月底前还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-10 14:20\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>发送催款函</h4>\r\n                        <p>向债务人发送正式催款函，要求在15日内还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-05 09:15\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>案件受理</h4>\r\n                        <p>案件正式受理，开始债务追讨程序</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                  </el-timeline>\r\n                </div>\r\n\r\n                <!-- 证据材料 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'evidence'\">\r\n                  <h3 class=\"section-title\">证据材料</h3>\r\n                  <div class=\"evidence-grid\">\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-picture\"></i>\r\n                      <span>借条照片</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-chat-line-square\"></i>\r\n                      <span>聊天记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-bank-card\"></i>\r\n                      <span>转账记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 相关文档 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'documents'\">\r\n                  <h3 class=\"section-title\">相关文档</h3>\r\n                  <el-table :data=\"debtDocuments\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"文档名称\"></el-table-column>\r\n                    <el-table-column prop=\"type\" label=\"文档类型\"></el-table-column>\r\n                    <el-table-column prop=\"uploadTime\" label=\"上传时间\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\">下载</el-button>\r\n                        <el-button type=\"text\">预览</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n\r\n\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!-- 用户详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewUserDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleUserDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          <span>用户详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeUserTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleUserTabSelect\">\r\n              <el-menu-item index=\"customer\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>客户信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"member\">\r\n                <i class=\"el-icon-medal\"></i>\r\n                <span>会员信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"debts\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>债务人信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"attachments\">\r\n                <i class=\"el-icon-folder-opened\"></i>\r\n                <span>附件信息</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\">\r\n                <!-- 客户信息 -->\r\n                <div v-if=\"activeUserTab === 'customer'\">\r\n                  <user-detail :id=\"currentId\"></user-detail>\r\n                </div>\r\n\r\n                <!-- 会员信息 -->\r\n                <div v-else-if=\"activeUserTab === 'member'\">\r\n                  <h3 class=\"section-title\">会员信息</h3>\r\n                  <el-descriptions :column=\"2\" border>\r\n                    <el-descriptions-item label=\"会员等级\">普通会员</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员状态\">正常</el-descriptions-item>\r\n                    <el-descriptions-item label=\"注册时间\">2024-01-01</el-descriptions-item>\r\n                    <el-descriptions-item label=\"最后登录\">2024-01-15</el-descriptions-item>\r\n                    <el-descriptions-item label=\"积分余额\">1000</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员权益\">基础服务</el-descriptions-item>\r\n                  </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 债务人信息 -->\r\n                <div v-else-if=\"activeUserTab === 'debts'\">\r\n                  <h3 class=\"section-title\">关联债务人信息</h3>\r\n                  <el-table :data=\"userDebtsList\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"债务人姓名\"></el-table-column>\r\n                    <el-table-column prop=\"phone\" label=\"联系电话\"></el-table-column>\r\n                    <el-table-column prop=\"amount\" label=\"债务金额\"></el-table-column>\r\n                    <el-table-column prop=\"status\" label=\"状态\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" @click=\"viewDebtData(scope.row.id)\">查看详情</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n\r\n                <!-- 附件信息 -->\r\n                <div v-else-if=\"activeUserTab === 'attachments'\">\r\n                  <h3 class=\"section-title\">相关附件</h3>\r\n                  <div class=\"attachment-grid\">\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证正面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证反面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>营业执照</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetail from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetail, DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [\r\n        {\r\n          id: 1,\r\n          uid: 1001,\r\n          name: \"张三\",\r\n          tel: \"13800138001\",\r\n          money: \"50000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"50000\",\r\n          ctime: \"2024-01-15 10:30:00\",\r\n          address: \"北京市朝阳区建国路88号\",\r\n          idcard_no: \"110101199001011234\",\r\n          case_des: \"借款纠纷，借款人未按约定时间还款\",\r\n          users: {\r\n            nickname: \"李四\"\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          uid: 1002,\r\n          name: \"王五\",\r\n          tel: \"13900139002\",\r\n          money: \"120000\",\r\n          status: \"调节中\",\r\n          back_money: \"30000\",\r\n          un_money: \"90000\",\r\n          ctime: \"2024-01-10 14:20:00\",\r\n          address: \"上海市浦东新区陆家嘴金融区\",\r\n          idcard_no: \"310101199205155678\",\r\n          case_des: \"合同纠纷，未按合同约定支付货款\",\r\n          users: {\r\n            nickname: \"赵六\"\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          uid: 1003,\r\n          name: \"陈七\",\r\n          tel: \"13700137003\",\r\n          money: \"80000\",\r\n          status: \"诉讼中\",\r\n          back_money: \"20000\",\r\n          un_money: \"60000\",\r\n          ctime: \"2024-01-05 09:15:00\",\r\n          address: \"广州市天河区珠江新城\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"服务费纠纷，拒绝支付约定的服务费用\",\r\n          users: {\r\n            nickname: \"孙八\"\r\n          }\r\n        },\r\n        {\r\n          id: 4,\r\n          uid: 1004,\r\n          name: \"刘九\",\r\n          tel: \"13600136004\",\r\n          money: \"200000\",\r\n          status: \"已结案\",\r\n          back_money: \"200000\",\r\n          un_money: \"0\",\r\n          ctime: \"2023-12-20 16:45:00\",\r\n          address: \"深圳市南山区科技园\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"投资纠纷，已通过调解达成一致\",\r\n          users: {\r\n            nickname: \"周十\"\r\n          }\r\n        },\r\n        {\r\n          id: 5,\r\n          uid: 1005,\r\n          name: \"吴十一\",\r\n          tel: \"13500135005\",\r\n          money: \"75000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"75000\",\r\n          ctime: \"2024-01-18 11:30:00\",\r\n          address: \"杭州市西湖区文三路\",\r\n          idcard_no: \"330101199406067890\",\r\n          case_des: \"租赁纠纷，拖欠房租及违约金\",\r\n          users: {\r\n            nickname: \"郑十二\"\r\n          }\r\n        },\r\n        {\r\n          id: 6,\r\n          uid: 1006,\r\n          name: \"马十三\",\r\n          tel: \"13400134006\",\r\n          money: \"150000\",\r\n          status: \"调节中\",\r\n          back_money: \"50000\",\r\n          un_money: \"100000\",\r\n          ctime: \"2024-01-12 13:20:00\",\r\n          address: \"成都市锦江区春熙路\",\r\n          idcard_no: \"510101199009091234\",\r\n          case_des: \"买卖合同纠纷，货物质量问题导致损失\",\r\n          users: {\r\n            nickname: \"冯十四\"\r\n          }\r\n        }\r\n      ],\r\n      total: 6,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      drawerViewUserDetail: false,\r\n      drawerViewDebtDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n      activeDebtTab: 'details',\r\n      activeUserTab: 'customer',\r\n      activeDebtDetailTab: 'details',\r\n      userDebtsList: [\r\n        {\r\n          id: 1,\r\n          name: \"债务人A\",\r\n          phone: \"13900139001\",\r\n          amount: \"50000\",\r\n          status: \"处理中\"\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"债务人B\",\r\n          phone: \"13900139002\",\r\n          amount: \"30000\",\r\n          status: \"已完成\"\r\n        }\r\n      ],\r\n      debtDocuments: [\r\n        {\r\n          name: \"借款合同.pdf\",\r\n          type: \"合同文件\",\r\n          uploadTime: \"2024-01-10\"\r\n        },\r\n        {\r\n          name: \"催款函.doc\",\r\n          type: \"法律文书\",\r\n          uploadTime: \"2024-01-12\"\r\n        },\r\n        {\r\n          name: \"还款计划.xlsx\",\r\n          type: \"财务文件\",\r\n          uploadTime: \"2024-01-15\"\r\n        }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n      _this.activeDebtTab = 'details';\r\n      _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.drawerViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.drawerViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      \r\n      // 开发模式：使用示例数据，不发送HTTP请求\r\n      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';\r\n      \r\n      if (isDevelopment) {\r\n        // 模拟异步加载\r\n        setTimeout(() => {\r\n          // 这里的数据已经在data()中定义了，所以直接设置loading为false\r\n          _this.loading = false;\r\n        }, 500);\r\n        return;\r\n      }\r\n      \r\n      // 生产模式：发送HTTP请求获取真实数据\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n    handleDrawerClose() {\r\n      this.dialogFormVisible = false;\r\n    },\r\n    handleUserDetailDrawerClose() {\r\n      this.drawerViewUserDetail = false;\r\n      this.activeUserTab = 'customer';\r\n    },\r\n    handleDebtDetailDrawerClose() {\r\n      this.drawerViewDebtDetail = false;\r\n      this.activeDebtDetailTab = 'details';\r\n    },\r\n    handleUserTabSelect(index) {\r\n      this.activeUserTab = index;\r\n    },\r\n    handleDebtDetailTabSelect(index) {\r\n      this.activeDebtDetailTab = index;\r\n    },\r\n    handleDebtTabSelect(index) {\r\n      this.activeDebtTab = index;\r\n    },\r\n    getEvidenceTitle() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部证据';\r\n        case 'evidence-video':\r\n          return '视频证据';\r\n        case 'evidence-image':\r\n          return '图片证据';\r\n        case 'evidence-audio':\r\n          return '语音证据';\r\n        case 'evidence-document':\r\n          return '文档证据';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    getEvidenceTypeText() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部';\r\n        case 'evidence-video':\r\n          return '视频';\r\n        case 'evidence-image':\r\n          return '图片';\r\n        case 'evidence-audio':\r\n          return '语音';\r\n        case 'evidence-document':\r\n          return '文档';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    hasEvidence() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return this.ruleForm.cards.length > 0 || this.ruleForm.images.length > 0 || this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-video':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-image':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-audio':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-document':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    uploadEvidence() {\r\n      // Implementation of uploadEvidence method\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.drawer-content-wrapper {\r\n  display: flex;\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.drawer-sidebar {\r\n  width: 220px;\r\n  padding: 20px 10px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-right: none;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawer-menu {\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n  border-radius: 8px;\r\n  margin-bottom: 8px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n}\r\n\r\n.drawer-menu .el-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.drawer-menu .el-menu-item.is-active {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item {\r\n  padding-left: 40px;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  padding: 25px;\r\n  overflow-y: auto;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.tab-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  padding: 25px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8ecf0;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* 抽屉标题样式 */\r\n.drawer-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.drawer-title i {\r\n  margin-right: 8px;\r\n  font-size: 20px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 现代抽屉样式 */\r\n.modern-drawer .el-drawer__header {\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e8ecf0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n.modern-drawer .el-drawer__body {\r\n  padding: 0;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn {\r\n  color: #606266;\r\n  font-size: 18px;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn:hover {\r\n  color: #409eff;\r\n}\r\n\r\n.card-header {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.evidence-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.evidence-section h4 {\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.evidence-section h5 {\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.evidence-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: #fff;\r\n}\r\n\r\n.evidence-item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n  width: 100%;\r\n  height: 150px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.evidence-actions {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #409eff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.file-type-icon {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.drawer-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.no-evidence {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n  border: 2px dashed #dcdfe6;\r\n}\r\n\r\n.no-evidence i {\r\n  font-size: 48px;\r\n  margin-bottom: 15px;\r\n  display: block;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .drawer-content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .drawer-sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n  \r\n  .evidence-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 10px;\r\n  }\r\n  \r\n  .file-item {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .file-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 可点击文本样式 */\r\n.clickable-text {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n.clickable-text:hover {\r\n  color: #ffffff;\r\n  background-color: #409eff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 标题样式 */\r\n.section-title {\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n  position: relative;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 30px;\r\n  height: 2px;\r\n  background-color: #67c23a;\r\n}\r\n\r\n/* 附件网格样式 */\r\n.attachment-grid, .evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.attachment-item, .evidence-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e8ecf0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.attachment-item:hover, .evidence-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.attachment-item i, .evidence-item i {\r\n  font-size: 24px;\r\n  color: #409eff;\r\n  margin-right: 10px;\r\n}\r\n\r\n.attachment-item span, .evidence-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n.el-timeline-item__content .el-card {\r\n  margin-bottom: 0;\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-timeline-item__content .el-card h4 {\r\n  color: #409eff;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-timeline-item__content .el-card p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 表格溢出处理 */\r\n.drawer-content .card {\r\n  overflow-x: auto;\r\n  max-width: 100%;\r\n}\r\n\r\n.drawer-content .el-table {\r\n  min-width: 1200px; /* 设置表格最小宽度 */\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n  overflow-x: auto;\r\n}\r\n\r\n/* 抽屉内容区域样式优化 */\r\n.drawer-content-wrapper {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  padding: 0 20px 20px 0;\r\n}\r\n</style>\r\n"]}]}