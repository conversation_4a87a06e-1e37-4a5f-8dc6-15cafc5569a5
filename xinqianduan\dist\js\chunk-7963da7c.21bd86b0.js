(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7963da7c"],{"13d5":function(t,e,a){"use strict";var s=a("23e7"),i=a("d58f").left,l=a("a640"),r=a("2d00"),c=a("605d"),o=!c&&r>79&&r<83,n=o||!l("reduce");s({target:"Array",proto:!0,forced:n},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"51a4":function(t,e,a){},"605d":function(t,e,a){"use strict";var s=a("da84"),i=a("c6b6");t.exports="process"===i(s.process)},a640:function(t,e,a){"use strict";var s=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&s((function(){a.call(null,e||function(){return 1},1)}))}},d564:function(t,e,a){"use strict";a("51a4")},d58f:function(t,e,a){"use strict";var s=a("59ed"),i=a("7b0b"),l=a("44ad"),r=a("07fa"),c=TypeError,o="Reduce of empty array with no initial value",n=function(t){return function(e,a,n,d){var u=i(e),m=l(u),p=r(u);if(s(a),0===p&&n<2)throw new c(o);var v=t?p-1:0,h=t?-1:1;if(n<2)while(1){if(v in m){d=m[v],v+=h;break}if(v+=h,t?v<0:p<=v)throw new c(o)}for(;t?v>=0:p>v;v+=h)v in m&&(d=a(d,m[v],v,u));return d}};t.exports={left:n(!1),right:n(!0)}},d625:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"package-management-container"},[e("div",{staticClass:"page-header"},[t._m(0),e("div",{staticClass:"header-actions"},[e("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.getData}},[t._v(" 刷新数据 ")])],1)]),e("div",{staticClass:"stats-section"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon total-icon"},[e("i",{staticClass:"el-icon-box"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.total))]),e("div",{staticClass:"stat-label"},[t._v("套餐总数")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +12% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon price-icon"},[e("i",{staticClass:"el-icon-money"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v("¥"+t._s(t.averagePrice))]),e("div",{staticClass:"stat-label"},[t._v("平均价格")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +5% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon premium-icon"},[e("i",{staticClass:"el-icon-star-on"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.premiumPackages))]),e("div",{staticClass:"stat-label"},[t._v("高端套餐")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" +8% ")])])])]),e("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon duration-icon"},[e("i",{staticClass:"el-icon-time"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.averageYear)+"年")]),e("div",{staticClass:"stat-label"},[t._v("平均年限")]),e("div",{staticClass:"stat-change positive"},[e("i",{staticClass:"el-icon-check"}),t._v(" 稳定 ")])])])])],1)],1),e("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"card-title"},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索管理 ")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.editData(0)}}},[t._v(" 新增套餐 ")])],1)]),e("div",{staticClass:"search-section"},[e("el-form",{staticClass:"search-form",attrs:{model:t.search,inline:!0}},[e("el-form-item",{attrs:{label:"关键词"}},[e("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入套餐名称或描述",clearable:""},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.getData()}},slot:"append"})],1)],1),e("el-form-item",{attrs:{label:"价格范围"}},[e("el-input-number",{staticStyle:{width:"120px"},attrs:{placeholder:"最低价格",min:0},model:{value:t.search.minPrice,callback:function(e){t.$set(t.search,"minPrice",e)},expression:"search.minPrice"}}),e("span",{staticStyle:{margin:"0 8px"}},[t._v("-")]),e("el-input-number",{staticStyle:{width:"120px"},attrs:{placeholder:"最高价格",min:0},model:{value:t.search.maxPrice,callback:function(e){t.$set(t.search,"maxPrice",e)},expression:"search.maxPrice"}})],1),e("el-form-item",[e("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetSearch}},[t._v(" 重置 ")])],1)],1)],1)]),e("el-card",{staticClass:"package-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"card-title"},[e("i",{staticClass:"el-icon-tickets"}),t._v(" 套餐列表 ")]),e("div",{staticClass:"view-controls"},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.viewMode,callback:function(e){t.viewMode=e},expression:"viewMode"}},[e("el-radio-button",{attrs:{label:"grid"}},[t._v("卡片视图")]),e("el-radio-button",{attrs:{label:"table"}},[t._v("表格视图")])],1)],1)]),"grid"===t.viewMode?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"package-grid"},t._l(t.filteredPackages,(function(a){return e("div",{key:a.id,staticClass:"package-item"},[e("div",{staticClass:"package-header"},[e("div",{staticClass:"package-title"},[t._v(t._s(a.title))]),e("div",{staticClass:"package-price"},[t._v("¥"+t._s(a.price))])]),e("div",{staticClass:"package-content"},[e("div",{staticClass:"package-info"},[e("div",{staticClass:"info-item"},[e("i",{staticClass:"el-icon-time"}),e("span",[t._v(t._s(a.year)+"年服务")])]),e("div",{staticClass:"info-item"},[e("i",{staticClass:"el-icon-sort"}),e("span",[t._v("排序: "+t._s(a.sort))])])]),e("div",{staticClass:"package-desc"},[t._v(" "+t._s(a.desc||"暂无描述")+" ")]),a.services&&a.services.length>0?e("div",{staticClass:"package-features"},[e("div",{staticClass:"feature-title"},[t._v("包含服务:")]),e("div",{staticClass:"feature-list"},[t._l(a.services.slice(0,3),(function(a){return e("el-tag",{key:a.id,staticClass:"feature-tag",attrs:{size:"mini"}},[t._v(" "+t._s(a.name)+" ")])})),a.services.length>3?e("span",{staticClass:"more-services"},[t._v(" +"+t._s(a.services.length-3)+" ")]):t._e()],2)]):t._e()]),e("div",{staticClass:"package-footer"},[e("div",{staticClass:"package-meta"},[e("span",{staticClass:"create-time"},[t._v(t._s(t.formatDate(a.create_time)))])]),e("div",{staticClass:"package-actions"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text"},on:{click:function(e){return t.editData(a.id)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text"},on:{click:function(e){return t.delData(-1,a.id)}}},[t._v(" 删除 ")])],1)])])})),0):t._e(),"table"===t.viewMode?e("div",[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:t.filteredPackages}},[e("el-table-column",{attrs:{label:"套餐信息","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"table-package-info"},[e("div",{staticClass:"table-package-title"},[t._v(t._s(a.row.title))]),e("div",{staticClass:"table-package-desc"},[t._v(t._s(a.row.desc||"暂无描述"))])])]}}],null,!1,3323292265)}),e("el-table-column",{attrs:{label:"价格",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"price-display"},[t._v("¥"+t._s(a.row.price))])]}}],null,!1,2696510062)}),e("el-table-column",{attrs:{prop:"year",label:"年限",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"info",size:"small"}},[t._v(t._s(a.row.year)+"年")])]}}],null,!1,3902229530)}),e("el-table-column",{attrs:{prop:"sort",label:"排序",width:"80"}}),e("el-table-column",{attrs:{label:"创建时间",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"time-info"},[t._v(" "+t._s(t.formatDate(a.row.create_time))+" ")])]}}],null,!1,2692560985)}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"action-buttons"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.editData(a.row.id)}}},[e("i",{staticClass:"el-icon-edit"}),t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.delData(a.$index,a.row.id)}}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 删除 ")])],1)]}}],null,!1,1323445013)})],1)],1):t._e(),e("div",{staticClass:"pagination-wrapper"},[e("el-pagination",{attrs:{"page-sizes":[12,20,50,100],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total,background:""},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{staticClass:"edit-dialog",attrs:{title:t.dialogTitle,visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"120px"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"套餐名称",prop:"title"}},[e("el-input",{attrs:{placeholder:"请输入套餐名称",autocomplete:"off"},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"套餐价格",prop:"price"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:999999,precision:2,placeholder:"请输入价格"},model:{value:t.ruleForm.price,callback:function(e){t.$set(t.ruleForm,"price",e)},expression:"ruleForm.price"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"服务年限",prop:"year"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:10,placeholder:"请输入年限"},model:{value:t.ruleForm.year,callback:function(e){t.$set(t.ruleForm,"year",e)},expression:"ruleForm.year"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"排序"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:999,placeholder:"数字越小排序越靠前"},model:{value:t.ruleForm.sort,callback:function(e){t.$set(t.ruleForm,"sort",e)},expression:"ruleForm.sort"}})],1)],1)],1),e("el-form-item",{attrs:{label:"套餐内容",prop:"good"}},[e("div",{staticClass:"service-selection"},[e("div",{staticClass:"service-title"},[t._v("选择包含的服务类型:")]),e("div",{staticClass:"service-list"},t._l(t.types,(function(a,s){return e("div",{key:s,staticClass:"service-item"},[e("div",{staticClass:"service-checkbox"},[e("el-checkbox",{attrs:{label:a.id},model:{value:a.checked,callback:function(e){t.$set(a,"checked",e)},expression:"item.checked"}},[t._v(" "+t._s(a.title)+" ")])],1),1==a.is_num&&a.checked?e("div",{staticClass:"service-input"},[e("el-input-number",{attrs:{min:1,max:999,size:"small",placeholder:"次数"},model:{value:a.value,callback:function(e){t.$set(a,"value",e)},expression:"item.value"}}),e("span",{staticClass:"input-suffix"},[t._v("次")])],1):a.checked?e("div",{staticClass:"service-unlimited"},[e("el-tag",{attrs:{size:"small",type:"success"}},[t._v("不限次数")])],1):t._e()])})),0)])]),e("el-form-item",{attrs:{label:"套餐描述"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入套餐详细描述...",autocomplete:"off"},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.saveLoading},on:{click:function(e){return t.saveData()}}},[t._v(" 保存 ")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-left"},[e("h2",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-box"}),t._v(" 套餐类型管理 ")]),e("div",{staticClass:"page-subtitle"},[t._v("管理法律服务套餐产品和价格配置")])])}],l=(a("13d5"),{name:"PackageManagement",components:{},data(){return{allSize:"mini",tableData:[],loading:!0,total:1,page:1,size:12,viewMode:"grid",saveLoading:!1,search:{keyword:"",minPrice:null,maxPrice:null},ruleForm:{title:"",price:"",year:"",desc:"",sort:0,good:[],num:[]},num:0,rules:{title:[{required:!0,message:"请填写套餐名称",trigger:"blur"}],price:[{required:!0,message:"请填写价格",trigger:"blur"}],year:[{required:!0,message:"请填写年限",trigger:"blur"}]},dialogFormVisible:!1,formLabelWidth:"120px",url:"/taocan/",types:[]}},computed:{averagePrice(){if(!Array.isArray(this.tableData)||0===this.tableData.length)return"0";const t=this.tableData.reduce((t,e)=>t+(parseFloat(e.price)||0),0);return Math.round(t/this.tableData.length).toLocaleString()},premiumPackages(){return Array.isArray(this.tableData)?this.tableData.filter(t=>parseFloat(t.price)>1e4).length:0},averageYear(){if(!Array.isArray(this.tableData)||0===this.tableData.length)return 1;const t=this.tableData.reduce((t,e)=>t+(parseInt(e.year)||1),0);return Math.round(t/this.tableData.length)},dialogTitle(){return this.ruleForm.id?"编辑套餐":"新增套餐"},filteredPackages(){if(!Array.isArray(this.tableData))return[];let t=this.tableData;if(this.search.keyword){const e=this.search.keyword.toLowerCase();t=t.filter(t=>t.title&&t.title.toLowerCase().includes(e)||t.desc&&t.desc.toLowerCase().includes(e))}return null!==this.search.minPrice&&(t=t.filter(t=>parseFloat(t.price)>=this.search.minPrice)),null!==this.search.maxPrice&&(t=t.filter(t=>parseFloat(t.price)<=this.search.maxPrice)),t}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):(this.ruleForm={title:"",price:"",year:"",desc:"",sort:0,good:[],num:[]},e.getTypes()),e.dialogFormVisible=!0},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{t&&(e.ruleForm=t.data,e.types=e.ruleForm.num)})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"删除成功!"}),this.tableData.splice(t,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},getTypes(){this.postRequest("/type/getList",{}).then(t=>{200==t.code&&(this.types=t.data)})},getData(){let t=this;t.loading=!0,t.postRequest(t.url+"index?page="+t.page+"&size="+t.size,t.search).then(e=>{200==e.code&&(t.tableData=e.data,t.total=e.count),t.loading=!1})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},formatDate(t){return t?new Date(t).toLocaleDateString("zh-CN"):"未设置"},resetSearch(){this.search={keyword:"",minPrice:null,maxPrice:null},this.page=1,this.getData()},saveData(){this.$refs.ruleForm.validate(t=>{if(!t)return!1;this.saveLoading=!0,this.ruleForm.good=this.types.filter(t=>t.checked).map(t=>t.id),setTimeout(()=>{this.saveLoading=!1,this.dialogFormVisible=!1,this.$message.success("保存成功"),this.getData()},1e3)})}}}),r=l,c=(a("d564"),a("2877")),o=Object(c["a"])(r,s,i,!1,null,"4b11a6a1",null);e["default"]=o.exports}}]);
//# sourceMappingURL=chunk-7963da7c.21bd86b0.js.map