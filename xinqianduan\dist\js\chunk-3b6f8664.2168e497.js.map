{"version": 3, "sources": ["webpack:///./src/components/SystemMonitor.vue?a290", "webpack:///./src/views/pages/Dashboard.vue", "webpack:///./src/components/SystemMonitor.vue", "webpack:///src/components/SystemMonitor.vue", "webpack:///./src/components/SystemMonitor.vue?a5f4", "webpack:///./src/components/SystemMonitor.vue?cb5b", "webpack:///src/views/pages/Dashboard.vue", "webpack:///./src/views/pages/Dashboard.vue?0f0b", "webpack:///./src/views/pages/Dashboard.vue?2172", "webpack:///./src/views/pages/Dashboard.vue?bfcc"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "_s", "getCurrentTime", "attrs", "on", "$event", "handleQuickAction", "loading", "refreshData", "directives", "name", "rawName", "value", "expression", "stats", "totalUsers", "totalCases", "totalContracts", "totalRevenue", "slot", "model", "chartPeriod", "callback", "$$v", "viewAllActivities", "_l", "recentActivities", "activity", "key", "id", "class", "icon", "style", "color", "title", "description", "time", "quickActions", "action", "backgroundColor", "todoList", "filter", "item", "completed", "length", "viewAllTodos", "slice", "todo", "handleTodoChange", "$set", "priority", "getPriorityText", "notifications", "read", "viewAllNotifications", "notification", "unread", "mark<PERSON><PERSON><PERSON>", "_e", "staticRenderFns", "systemUptime", "onlineUsers", "systemMetrics", "cpu", "getProgressColor", "memory", "disk", "services", "service", "status", "version", "uptime", "data", "mounted", "startMonitoring", "<PERSON><PERSON><PERSON><PERSON>", "monitorTimer", "clearInterval", "methods", "setInterval", "updateMetrics", "Math", "floor", "random", "$message", "success", "percentage", "component", "components", "SystemMonitor", "loadDashboardData", "Promise", "all", "loadStats", "loadActivities", "loadTodos", "loadQuickActions", "loadNotifications", "error", "console", "response", "getRequest", "code", "totalDebts", "totalOrders", "actions", "map", "count", "url", "now", "Date", "options", "year", "month", "day", "weekday", "toLocaleDateString", "startsWith", "$router", "push", "info", "postRequest", "high", "medium", "low"], "mappings": "2IAAA,W,gECAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,kBAAkBH,EAAG,IAAI,CAACE,YAAY,oBAAoB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIO,kBAAkB,kBAAkBL,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIW,kBAAkB,eAAe,CAACT,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIW,kBAAkB,mBAAmB,CAACT,EAAG,IAAI,CAACE,YAAY,yBAAyBJ,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,OAAO,QAAUR,EAAIY,SAASH,GAAG,CAAC,MAAQT,EAAIa,cAAc,CAACX,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIK,GAAG,aAAa,KAAKH,EAAG,MAAM,CAACY,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYC,MAAOjB,EAAIY,QAASM,WAAW,YAAYd,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImB,MAAMC,eAAelB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,kBAAkBH,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImB,MAAME,eAAenB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,iBAAiBH,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImB,MAAMG,mBAAmBpB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,kBAAkBH,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,IAAIL,EAAIM,GAAGN,EAAImB,MAAMI,iBAAiBrB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,mBAAmB,IAAI,GAAGH,EAAG,SAAS,CAACE,YAAY,eAAeI,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,KAAK,CAACN,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,iBAAiB,CAACM,MAAM,CAAC,KAAO,SAASiB,MAAM,CAACR,MAAOjB,EAAI0B,YAAaC,SAAS,SAAUC,GAAM5B,EAAI0B,YAAYE,GAAKV,WAAW,gBAAgB,CAAChB,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAIK,GAAG,QAAQH,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,UAAU,CAACR,EAAIK,GAAG,QAAQH,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAIK,GAAG,SAAS,IAAI,KAAKH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,IAAI,CAACE,YAAY,iCAAiCF,EAAG,IAAI,CAACF,EAAIK,GAAG,YAAYH,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,0CAA0C,GAAGH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAI6B,oBAAoB,CAAC7B,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI8B,GAAI9B,EAAI+B,kBAAkB,SAASC,GAAU,OAAO9B,EAAG,MAAM,CAAC+B,IAAID,EAASE,GAAG9B,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACiC,MAAMH,EAASI,KAAKC,MAAO,CAAEC,MAAON,EAASM,WAAapC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAGL,EAAIM,GAAG0B,EAASO,UAAUrC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAGL,EAAIM,GAAG0B,EAASQ,gBAAgBtC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAGL,EAAIM,GAAG0B,EAASS,gBAAe,MAAM,KAAKvC,EAAG,SAAS,CAACM,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACN,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI8B,GAAI9B,EAAI0C,cAAc,SAASC,GAAQ,OAAOzC,EAAG,MAAM,CAAC+B,IAAIU,EAAOT,GAAG9B,YAAY,oBAAoBK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIW,kBAAkBgC,EAAOA,WAAW,CAACzC,EAAG,MAAM,CAACE,YAAY,cAAciC,MAAO,CAAEO,gBAAiBD,EAAOL,QAAU,CAACpC,EAAG,IAAI,CAACiC,MAAMQ,EAAOP,SAASlC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGqC,EAAOJ,UAAUrC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAGL,EAAIM,GAAGqC,EAAOH,uBAAsB,MAAM,GAAGtC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,WAAW,CAACE,YAAY,aAAaI,MAAM,CAAC,MAAQR,EAAI6C,SAASC,OAAOC,IAASA,EAAKC,WAAWC,SAAS,CAAC/C,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAIkD,eAAe,CAAClD,EAAIK,GAAG,WAAW,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAI8B,GAAI9B,EAAI6C,SAASM,MAAM,EAAG,IAAI,SAASC,GAAM,OAAOlD,EAAG,MAAM,CAAC+B,IAAImB,EAAKlB,GAAG9B,YAAY,YAAY+B,MAAM,CAAEa,UAAWI,EAAKJ,YAAa,CAAC9C,EAAG,cAAc,CAACO,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOV,EAAIqD,iBAAiBD,KAAQ3B,MAAM,CAACR,MAAOmC,EAAKJ,UAAWrB,SAAS,SAAUC,GAAM5B,EAAIsD,KAAKF,EAAM,YAAaxB,IAAMV,WAAW,mBAAmB,CAAClB,EAAIK,GAAG,IAAIL,EAAIM,GAAG8C,EAAKb,OAAO,OAAOrC,EAAG,MAAM,CAACE,YAAY,gBAAgB+B,MAAMiB,EAAKG,UAAU,CAACvD,EAAIK,GAAG,IAAIL,EAAIM,GAAGN,EAAIwD,gBAAgBJ,EAAKG,WAAW,QAAQ,MAAK,MAAM,GAAGrD,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,WAAW,CAACE,YAAY,qBAAqBI,MAAM,CAAC,MAAQR,EAAIyD,cAAcX,OAAOC,IAASA,EAAKW,MAAMT,SAAS,CAAC/C,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAI2D,uBAAuB,CAAC3D,EAAIK,GAAG,WAAW,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,qBAAqBJ,EAAI8B,GAAI9B,EAAIyD,cAAcN,MAAM,EAAG,IAAI,SAASS,GAAc,OAAO1D,EAAG,MAAM,CAAC+B,IAAI2B,EAAa1B,GAAG9B,YAAY,oBAAoB+B,MAAM,CAAE0B,QAASD,EAAaF,MAAOjD,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAI8D,WAAWF,MAAiB,CAAC1D,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGsD,EAAarB,UAAUrC,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGsD,EAAanB,WAAamB,EAAaF,KAAiD1D,EAAI+D,KAA/C7D,EAAG,MAAM,CAACE,YAAY,0BAAkC,MAAM,GAAGF,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,mBAAmB,MAAM,MAAM,IAAI,IAExtP8D,EAAkB,GCFlBjE,G,UAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACM,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACE,YAAY,cAAcI,MAAM,CAAC,KAAO,UAAUgB,KAAK,UAAU,CAACtB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAIa,cAAc,CAACX,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,IAAI,CAACE,YAAY,sBAAsBF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,cAAcH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIiE,qBAAqB/D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIkE,sBAAsBhE,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,YAAYH,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImE,cAAcC,KAAK,SAASlE,EAAG,cAAc,CAACM,MAAM,CAAC,WAAaR,EAAImE,cAAcC,IAAI,MAAQpE,EAAIqE,iBAAiBrE,EAAImE,cAAcC,KAAK,aAAY,MAAU,GAAGlE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImE,cAAcG,QAAQ,SAASpE,EAAG,cAAc,CAACM,MAAM,CAAC,WAAaR,EAAImE,cAAcG,OAAO,MAAQtE,EAAIqE,iBAAiBrE,EAAImE,cAAcG,QAAQ,aAAY,MAAU,GAAGpE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAImE,cAAcI,MAAM,SAASrE,EAAG,cAAc,CAACM,MAAM,CAAC,WAAaR,EAAImE,cAAcI,KAAK,MAAQvE,EAAIqE,iBAAiBrE,EAAImE,cAAcI,MAAM,aAAY,MAAU,KAAKrE,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAI8B,GAAI9B,EAAIwE,UAAU,SAASC,GAAS,OAAOvE,EAAG,MAAM,CAAC+B,IAAIwC,EAAQ1D,KAAKX,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGmE,EAAQ1D,SAASb,EAAG,OAAO,CAACE,YAAY,uBAAuB+B,MAAMsC,EAAQC,QAAQ,CAAC1E,EAAIK,GAAG,IAAIL,EAAIM,GAAsB,WAAnBmE,EAAQC,OAAsB,KAAO,MAAM,SAASxE,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,OAAO,CAACE,YAAY,mBAAmB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGmE,EAAQE,YAAYzE,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,MAAML,EAAIM,GAAGmE,EAAQG,kBAAiB,UAAU,KAEtnGZ,EAAkB,GCgHP,GACfjD,KAAA,gBACA8D,OACA,OACAZ,aAAA,eACAC,YAAA,GACAC,cAAA,CACAC,IAAA,GACAE,OAAA,GACAC,KAAA,IAEAC,SAAA,CACA,CACAzD,KAAA,SACA2D,OAAA,SACAC,QAAA,SACAC,OAAA,MAEA,CACA7D,KAAA,MACA2D,OAAA,SACAC,QAAA,YACAC,OAAA,MAEA,CACA7D,KAAA,OACA2D,OAAA,SACAC,QAAA,SACAC,OAAA,MAEA,CACA7D,KAAA,OACA2D,OAAA,SACAC,QAAA,SACAC,OAAA,SAKAE,UACA,KAAAC,mBAEAC,gBACA,KAAAC,cACAC,cAAA,KAAAD,eAGAE,QAAA,CACAJ,kBAEA,KAAAE,aAAAG,YAAA,KACA,KAAAC,iBACA,MAGAA,gBAEA,KAAAlB,cAAAC,IAAAkB,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GACA,KAAArB,cAAAG,OAAAgB,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GACA,KAAArB,cAAAI,KAAAe,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GAGA,KAAAtB,YAAAoB,KAAAC,MAAA,GAAAD,KAAAE,UAAA,IAGA3E,cACA,KAAAwE,gBACA,KAAAI,SAAAC,QAAA,UAGArB,iBAAAsB,GACA,OAAAA,EAAA,GACA,UACAA,EAAA,GACA,UAEA,aC9LsV,I,wBCQlVC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QC6NA,GACf7E,KAAA,YACA8E,WAAA,CACAC,iBAEAjB,OACA,OACAnD,YAAA,QACAd,SAAA,EACAO,MAAA,CACAC,WAAA,EACAC,WAAA,EACAC,eAAA,EACAC,aAAA,KAEAQ,iBAAA,GACAW,aAAA,CACA,CACAR,GAAA,EACAE,KAAA,eACAE,MAAA,UACAC,MAAA,OACAC,YAAA,WACAG,OAAA,YAEA,CACAT,GAAA,EACAE,KAAA,uBACAE,MAAA,UACAC,MAAA,OACAC,YAAA,WACAG,OAAA,gBAEA,CACAT,GAAA,EACAE,KAAA,qBACAE,MAAA,UACAC,MAAA,OACAC,YAAA,WACAG,OAAA,cAEA,CACAT,GAAA,EACAE,KAAA,iBACAE,MAAA,UACAC,MAAA,OACAC,YAAA,UACAG,OAAA,gBAGAE,SAAA,CACA,CACAX,GAAA,EACAK,MAAA,YACAS,WAAA,EACAO,SAAA,QAEA,CACArB,GAAA,EACAK,MAAA,YACAS,WAAA,EACAO,SAAA,UAEA,CACArB,GAAA,EACAK,MAAA,YACAS,WAAA,EACAO,SAAA,QAEA,CACArB,GAAA,EACAK,MAAA,WACAS,WAAA,EACAO,SAAA,OAEA,CACArB,GAAA,EACAK,MAAA,WACAS,WAAA,EACAO,SAAA,WAGAE,cAAA,CACA,CACAvB,GAAA,EACAK,MAAA,SACAE,KAAA,WACAiB,MAAA,GAEA,CACAxB,GAAA,EACAK,MAAA,QACAE,KAAA,WACAiB,MAAA,GAEA,CACAxB,GAAA,EACAK,MAAA,SACAE,KAAA,WACAiB,MAAA,MAKAoB,UACA,KAAAiB,qBAEAZ,QAAA,CAEA,0BACA,KAAAvE,SAAA,EACA,UACAoF,QAAAC,IAAA,CACA,KAAAC,YACA,KAAAC,iBACA,KAAAC,YACA,KAAAC,mBACA,KAAAC,sBAEA,MAAAC,GACAC,QAAAD,MAAA,aAAAA,GACA,KAAAd,SAAAc,MAAA,kBACA,QACA,KAAA3F,SAAA,IAKA,kBACA,IACA,MAAA6F,QAAA,KAAAC,WAAA,uBACA,SAAAD,EAAAE,KAAA,CACA,MAAA9B,EAAA4B,EAAA5B,KACA,KAAA1D,MAAA,CACAC,WAAAyD,EAAAzD,YAAA,EACAC,WAAAwD,EAAA+B,YAAA,EACAtF,eAAAuD,EAAAgC,aAAA,EACAtF,aAAAsD,EAAAtD,cAAA,MAGA,MAAAgF,GACAC,QAAAD,MAAA,YAAAA,KAKA,uBACA,IACA,MAAAE,QAAA,KAAAC,WAAA,4BACA,MAAAD,EAAAE,OACA,KAAA5E,iBAAA0E,EAAA5B,MAAA,IAEA,MAAA0B,GACAC,QAAAD,MAAA,YAAAA,KAKA,kBACA,IACA,MAAAE,QAAA,KAAAC,WAAA,uBACA,MAAAD,EAAAE,OACA,KAAA9D,SAAA4D,EAAA5B,MAAA,IAEA,MAAA0B,GACAC,QAAAD,MAAA,YAAAA,KAKA,yBACA,IACA,MAAAE,QAAA,KAAAC,WAAA,8BACA,SAAAD,EAAAE,KAAA,CACA,MAAAG,EAAAL,EAAA5B,MAAA,GAEA,KAAAnC,aAAAoE,EAAAC,IAAApE,IAAA,CACAT,GAAAS,EAAAJ,MACAH,KAAAO,EAAAP,KACAE,MAAAK,EAAAL,MACAC,MAAAI,EAAAJ,MACAC,YAAA,SAAAG,EAAAqE,MACArE,SAAAsE,QAGA,MAAAV,GACAC,QAAAD,MAAA,YAAAA,KAKA,0BACA,IACA,MAAAE,QAAA,KAAAC,WAAA,+BACA,MAAAD,EAAAE,OACA,KAAAlD,cAAAgD,EAAA5B,MAAA,IAEA,MAAA0B,GACAC,QAAAD,MAAA,UAAAA,KAIAhG,iBACA,MAAA2G,EAAA,IAAAC,KACAC,EAAA,CACAC,KAAA,UACAC,MAAA,OACAC,IAAA,UACAC,QAAA,QAEA,OAAAN,EAAAO,mBAAA,QAAAL,IAGAzG,kBAAAgC,GAEA,GAAAA,EAAA+E,WAAA,KACA,KAAAC,QAAAC,KAAAjF,QAKA,OAAAA,GACA,eACA,KAAAgF,QAAAC,KAAA,UACA,MACA,mBACA,KAAAD,QAAAC,KAAA,YACA,MACA,iBACA,KAAAD,QAAAC,KAAA,SACA,MACA,kBACA,KAAAD,QAAAC,KAAA,iBACA,MACA,QACA,KAAAnC,SAAAoC,KAAA,SAAAlF,KAIAd,oBAEA,KAAA4D,SAAAoC,KAAA,cAGA3E,eAEA,KAAAyE,QAAAC,KAAA,eAGAjE,uBAEA,KAAAgE,QAAAC,KAAA,mBAGA,uBAAAxE,GACA,IACA,MAAAqD,QAAA,KAAAqB,YAAA,yBACA5F,GAAAkB,EAAAlB,GACAc,UAAAI,EAAAJ,YAEA,MAAAyD,EAAAE,MACA,KAAAlB,SAAAC,QAAAtC,EAAAJ,UAAA,mBAEA,MAAAuD,GACAC,QAAAD,MAAA,YAAAA,GAEAnD,EAAAJ,WAAAI,EAAAJ,UACA,KAAAyC,SAAAc,MAAA,cAIA,iBAAA3C,GACA,IACA,MAAA6C,QAAA,KAAAqB,YAAA,mCACA5F,GAAA0B,EAAA1B,KAEA,MAAAuE,EAAAE,OACA/C,EAAAF,MAAA,EACA,KAAA+B,SAAAC,QAAA,aAEA,MAAAa,GACAC,QAAAD,MAAA,UAAAA,GACA,KAAAd,SAAAc,MAAA,cAIA/C,gBAAAD,GACA,MAAAwD,EAAA,CACAgB,KAAA,IACAC,OAAA,IACAC,IAAA,KAEA,OAAAlB,EAAAxD,IAAA,KAIA1C,cACA,KAAAkF,oBACA,KAAAN,SAAAC,QAAA,YC1hBiW,ICQ7V,G,UAAY,eACd,EACA3F,EACAiE,GACA,EACA,KACA,WACA,OAIa,e,2CCnBf", "file": "js/chunk-3b6f8664.2168e497.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SystemMonitor.vue?vue&type=style&index=0&id=d0a6f122&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"dashboard-container\"},[_c('div',{staticClass:\"welcome-section\"},[_c('div',{staticClass:\"welcome-content\"},[_c('h1',{staticClass:\"welcome-title\"},[_vm._v(\"欢迎使用法律服务管理系统\")]),_c('p',{staticClass:\"welcome-subtitle\"},[_vm._v(_vm._s(_vm.getCurrentTime())+\" | 管理员，您好！\")])]),_c('div',{staticClass:\"welcome-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handleQuickAction('new-case')}}},[_c('i',{staticClass:\"el-icon-plus\"}),_vm._v(\" 新建案件 \")]),_c('el-button',{attrs:{\"type\":\"success\"},on:{\"click\":function($event){return _vm.handleQuickAction('new-contract')}}},[_c('i',{staticClass:\"el-icon-document-add\"}),_vm._v(\" 新建合同 \")]),_c('el-button',{attrs:{\"type\":\"info\",\"loading\":_vm.loading},on:{\"click\":_vm.refreshData}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 刷新数据 \")])],1)]),_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"stats-section\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon user-icon\"},[_c('i',{staticClass:\"el-icon-user\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.stats.totalUsers))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总用户数\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +12% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon case-icon\"},[_c('i',{staticClass:\"el-icon-folder\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.stats.totalCases))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"案件总数\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +8% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon contract-icon\"},[_c('i',{staticClass:\"el-icon-document\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.stats.totalContracts))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"合同数量\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +15% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon revenue-icon\"},[_c('i',{staticClass:\"el-icon-money\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(\"¥\"+_vm._s(_vm.stats.totalRevenue))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总收入\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +22% \")])])])])],1)],1),_c('el-row',{staticClass:\"main-content\",attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":16,\"lg\":16,\"xl\":16}},[_c('div',{staticClass:\"chart-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"业务数据趋势\")]),_c('div',{staticClass:\"chart-controls\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.chartPeriod),callback:function ($$v) {_vm.chartPeriod=$$v},expression:\"chartPeriod\"}},[_c('el-radio-button',{attrs:{\"label\":\"week\"}},[_vm._v(\"本周\")]),_c('el-radio-button',{attrs:{\"label\":\"month\"}},[_vm._v(\"本月\")]),_c('el-radio-button',{attrs:{\"label\":\"year\"}},[_vm._v(\"本年\")])],1)],1)]),_c('div',{staticClass:\"chart-container\"},[_c('div',{staticClass:\"chart-placeholder\"},[_c('i',{staticClass:\"el-icon-data-line chart-icon\"}),_c('p',[_vm._v(\"数据图表区域\")]),_c('p',{staticClass:\"chart-desc\"},[_vm._v(\"这里可以集成 ECharts 或其他图表库显示业务数据趋势\")])])])])],1),_c('div',{staticClass:\"activity-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"最近活动\")]),_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":_vm.viewAllActivities}},[_vm._v(\"查看全部\")])],1),_c('div',{staticClass:\"activity-list\"},_vm._l((_vm.recentActivities),function(activity){return _c('div',{key:activity.id,staticClass:\"activity-item\"},[_c('div',{staticClass:\"activity-avatar\"},[_c('i',{class:activity.icon,style:({ color: activity.color })})]),_c('div',{staticClass:\"activity-content\"},[_c('div',{staticClass:\"activity-title\"},[_vm._v(_vm._s(activity.title))]),_c('div',{staticClass:\"activity-desc\"},[_vm._v(_vm._s(activity.description))]),_c('div',{staticClass:\"activity-time\"},[_vm._v(_vm._s(activity.time))])])])}),0)])],1)]),_c('el-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":8,\"lg\":8,\"xl\":8}},[_c('div',{staticClass:\"quick-actions-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"快捷操作\")])]),_c('div',{staticClass:\"quick-actions\"},_vm._l((_vm.quickActions),function(action){return _c('div',{key:action.id,staticClass:\"quick-action-item\",on:{\"click\":function($event){return _vm.handleQuickAction(action.action)}}},[_c('div',{staticClass:\"action-icon\",style:({ backgroundColor: action.color })},[_c('i',{class:action.icon})]),_c('div',{staticClass:\"action-content\"},[_c('div',{staticClass:\"action-title\"},[_vm._v(_vm._s(action.title))]),_c('div',{staticClass:\"action-desc\"},[_vm._v(_vm._s(action.description))])])])}),0)])],1),_c('div',{staticClass:\"todo-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"待办事项\")]),_c('el-badge',{staticClass:\"todo-badge\",attrs:{\"value\":_vm.todoList.filter(item => !item.completed).length}},[_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":_vm.viewAllTodos}},[_vm._v(\"查看全部\")])],1)],1),_c('div',{staticClass:\"todo-list\"},_vm._l((_vm.todoList.slice(0, 5)),function(todo){return _c('div',{key:todo.id,staticClass:\"todo-item\",class:{ completed: todo.completed }},[_c('el-checkbox',{on:{\"change\":function($event){return _vm.handleTodoChange(todo)}},model:{value:(todo.completed),callback:function ($$v) {_vm.$set(todo, \"completed\", $$v)},expression:\"todo.completed\"}},[_vm._v(\" \"+_vm._s(todo.title)+\" \")]),_c('div',{staticClass:\"todo-priority\",class:todo.priority},[_vm._v(\" \"+_vm._s(_vm.getPriorityText(todo.priority))+\" \")])],1)}),0)])],1),_c('div',{staticClass:\"notification-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"系统通知\")]),_c('el-badge',{staticClass:\"notification-badge\",attrs:{\"value\":_vm.notifications.filter(item => !item.read).length}},[_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":_vm.viewAllNotifications}},[_vm._v(\"查看全部\")])],1)],1),_c('div',{staticClass:\"notification-list\"},_vm._l((_vm.notifications.slice(0, 3)),function(notification){return _c('div',{key:notification.id,staticClass:\"notification-item\",class:{ unread: !notification.read },on:{\"click\":function($event){return _vm.markAsRead(notification)}}},[_c('div',{staticClass:\"notification-content\"},[_c('div',{staticClass:\"notification-title\"},[_vm._v(_vm._s(notification.title))]),_c('div',{staticClass:\"notification-time\"},[_vm._v(_vm._s(notification.time))])]),(!notification.read)?_c('div',{staticClass:\"notification-dot\"}):_vm._e()])}),0)])],1),_c('div',{staticClass:\"system-monitor-section\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"系统监控\")])]),_c('div',{staticClass:\"system-monitor-content\"},[_c('system-monitor')],1)])],1)])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"system-monitor\"},[_c('el-card',{attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"系统状态监控\")]),_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":_vm.refreshData}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 刷新 \")])],1),_c('div',{staticClass:\"monitor-content\"},[_c('div',{staticClass:\"status-indicators\"},[_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-icon online\"},[_c('i',{staticClass:\"el-icon-success\"})]),_c('div',{staticClass:\"status-info\"},[_c('div',{staticClass:\"status-label\"},[_vm._v(\"系统状态\")]),_c('div',{staticClass:\"status-value\"},[_vm._v(\"正常运行\")])])]),_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-icon\"},[_c('i',{staticClass:\"el-icon-time\"})]),_c('div',{staticClass:\"status-info\"},[_c('div',{staticClass:\"status-label\"},[_vm._v(\"运行时间\")]),_c('div',{staticClass:\"status-value\"},[_vm._v(_vm._s(_vm.systemUptime))])])]),_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-icon\"},[_c('i',{staticClass:\"el-icon-user\"})]),_c('div',{staticClass:\"status-info\"},[_c('div',{staticClass:\"status-label\"},[_vm._v(\"在线用户\")]),_c('div',{staticClass:\"status-value\"},[_vm._v(_vm._s(_vm.onlineUsers))])])])]),_c('div',{staticClass:\"performance-metrics\"},[_c('div',{staticClass:\"metric-item\"},[_c('div',{staticClass:\"metric-header\"},[_c('span',{staticClass:\"metric-label\"},[_vm._v(\"CPU使用率\")]),_c('span',{staticClass:\"metric-value\"},[_vm._v(_vm._s(_vm.systemMetrics.cpu)+\"%\")])]),_c('el-progress',{attrs:{\"percentage\":_vm.systemMetrics.cpu,\"color\":_vm.getProgressColor(_vm.systemMetrics.cpu),\"show-text\":false}})],1),_c('div',{staticClass:\"metric-item\"},[_c('div',{staticClass:\"metric-header\"},[_c('span',{staticClass:\"metric-label\"},[_vm._v(\"内存使用率\")]),_c('span',{staticClass:\"metric-value\"},[_vm._v(_vm._s(_vm.systemMetrics.memory)+\"%\")])]),_c('el-progress',{attrs:{\"percentage\":_vm.systemMetrics.memory,\"color\":_vm.getProgressColor(_vm.systemMetrics.memory),\"show-text\":false}})],1),_c('div',{staticClass:\"metric-item\"},[_c('div',{staticClass:\"metric-header\"},[_c('span',{staticClass:\"metric-label\"},[_vm._v(\"磁盘使用率\")]),_c('span',{staticClass:\"metric-value\"},[_vm._v(_vm._s(_vm.systemMetrics.disk)+\"%\")])]),_c('el-progress',{attrs:{\"percentage\":_vm.systemMetrics.disk,\"color\":_vm.getProgressColor(_vm.systemMetrics.disk),\"show-text\":false}})],1)]),_c('div',{staticClass:\"service-status\"},[_c('div',{staticClass:\"service-title\"},[_vm._v(\"服务状态\")]),_c('div',{staticClass:\"service-list\"},_vm._l((_vm.services),function(service){return _c('div',{key:service.name,staticClass:\"service-item\"},[_c('div',{staticClass:\"service-info\"},[_c('span',{staticClass:\"service-name\"},[_vm._v(_vm._s(service.name))]),_c('span',{staticClass:\"service-status-badge\",class:service.status},[_vm._v(\" \"+_vm._s(service.status === 'online' ? '正常' : '异常')+\" \")])]),_c('div',{staticClass:\"service-details\"},[_c('span',{staticClass:\"service-version\"},[_vm._v(_vm._s(service.version))]),_c('span',{staticClass:\"service-uptime\"},[_vm._v(\"运行 \"+_vm._s(service.uptime))])])])}),0)])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"system-monitor\">\r\n    <el-card shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">系统状态监控</span>\r\n        <el-button type=\"text\" @click=\"refreshData\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新\r\n        </el-button>\r\n      </div>\r\n      \r\n      <div class=\"monitor-content\">\r\n        <!-- 系统状态指示器 -->\r\n        <div class=\"status-indicators\">\r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon online\">\r\n              <i class=\"el-icon-success\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">系统状态</div>\r\n              <div class=\"status-value\">正常运行</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">运行时间</div>\r\n              <div class=\"status-value\">{{ systemUptime }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">在线用户</div>\r\n              <div class=\"status-value\">{{ onlineUsers }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性能指标 -->\r\n        <div class=\"performance-metrics\">\r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">CPU使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.cpu }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.cpu\" \r\n              :color=\"getProgressColor(systemMetrics.cpu)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">内存使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.memory }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.memory\" \r\n              :color=\"getProgressColor(systemMetrics.memory)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">磁盘使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.disk }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.disk\" \r\n              :color=\"getProgressColor(systemMetrics.disk)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务状态 -->\r\n        <div class=\"service-status\">\r\n          <div class=\"service-title\">服务状态</div>\r\n          <div class=\"service-list\">\r\n            <div \r\n              v-for=\"service in services\" \r\n              :key=\"service.name\"\r\n              class=\"service-item\"\r\n            >\r\n              <div class=\"service-info\">\r\n                <span class=\"service-name\">{{ service.name }}</span>\r\n                <span \r\n                  class=\"service-status-badge\" \r\n                  :class=\"service.status\"\r\n                >\r\n                  {{ service.status === 'online' ? '正常' : '异常' }}\r\n                </span>\r\n              </div>\r\n              <div class=\"service-details\">\r\n                <span class=\"service-version\">{{ service.version }}</span>\r\n                <span class=\"service-uptime\">运行 {{ service.uptime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SystemMonitor',\r\n  data() {\r\n    return {\r\n      systemUptime: '7天 12小时 35分钟',\r\n      onlineUsers: 23,\r\n      systemMetrics: {\r\n        cpu: 45,\r\n        memory: 62,\r\n        disk: 38\r\n      },\r\n      services: [\r\n        {\r\n          name: 'Web服务器',\r\n          status: 'online',\r\n          version: 'v2.1.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '数据库',\r\n          status: 'online',\r\n          version: 'MySQL 8.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '文件服务',\r\n          status: 'online',\r\n          version: 'v1.5.2',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '邮件服务',\r\n          status: 'online',\r\n          version: 'v3.2.1',\r\n          uptime: '6天'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.startMonitoring()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.monitorTimer) {\r\n      clearInterval(this.monitorTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    startMonitoring() {\r\n      // 模拟实时数据更新\r\n      this.monitorTimer = setInterval(() => {\r\n        this.updateMetrics()\r\n      }, 5000)\r\n    },\r\n    \r\n    updateMetrics() {\r\n      // 模拟性能指标变化\r\n      this.systemMetrics.cpu = Math.floor(Math.random() * 30) + 30\r\n      this.systemMetrics.memory = Math.floor(Math.random() * 20) + 50\r\n      this.systemMetrics.disk = Math.floor(Math.random() * 15) + 30\r\n      \r\n      // 模拟在线用户数变化\r\n      this.onlineUsers = Math.floor(Math.random() * 10) + 20\r\n    },\r\n    \r\n    refreshData() {\r\n      this.updateMetrics()\r\n      this.$message.success('数据已刷新')\r\n    },\r\n    \r\n    getProgressColor(percentage) {\r\n      if (percentage < 50) {\r\n        return '#67C23A'\r\n      } else if (percentage < 80) {\r\n        return '#E6A23C'\r\n      } else {\r\n        return '#F56C6C'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.system-monitor {\r\n  height: 100%;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.monitor-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicators {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n.status-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #f0f0f0;\r\n  color: #666;\r\n  font-size: 18px;\r\n}\r\n\r\n.status-icon.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-info {\r\n  flex: 1;\r\n}\r\n\r\n.status-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 性能指标 */\r\n.performance-metrics {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.metric-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.metric-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 服务状态 */\r\n.service-status {\r\n  border-top: 1px solid #f0f0f0;\r\n  padding-top: 20px;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.service-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.service-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.service-status-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.service-status-badge.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.service-status-badge.offline {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.service-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.service-version,\r\n.service-uptime {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .status-indicators {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .status-item {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-details {\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SystemMonitor.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SystemMonitor.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SystemMonitor.vue?vue&type=template&id=d0a6f122&scoped=true\"\nimport script from \"./SystemMonitor.vue?vue&type=script&lang=js\"\nexport * from \"./SystemMonitor.vue?vue&type=script&lang=js\"\nimport style0 from \"./SystemMonitor.vue?vue&type=style&index=0&id=d0a6f122&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d0a6f122\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- 欢迎区域 -->\r\n    <div class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <h1 class=\"welcome-title\">欢迎使用法律服务管理系统</h1>\r\n        <p class=\"welcome-subtitle\">{{ getCurrentTime() }} | 管理员，您好！</p>\r\n      </div>\r\n      <div class=\"welcome-actions\">\r\n        <el-button type=\"primary\" @click=\"handleQuickAction('new-case')\">\r\n          <i class=\"el-icon-plus\"></i> 新建案件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleQuickAction('new-contract')\">\r\n          <i class=\"el-icon-document-add\"></i> 新建合同\r\n        </el-button>\r\n        <el-button type=\"info\" @click=\"refreshData\" :loading=\"loading\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <div class=\"stats-section\" v-loading=\"loading\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalUsers }}</div>\r\n              <div class=\"stat-label\">总用户数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon case-icon\">\r\n              <i class=\"el-icon-folder\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalCases }}</div>\r\n              <div class=\"stat-label\">案件总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon contract-icon\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalContracts }}</div>\r\n              <div class=\"stat-label\">合同数量</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon revenue-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ stats.totalRevenue }}</div>\r\n              <div class=\"stat-label\">总收入</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +22%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 左侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"16\" :lg=\"16\" :xl=\"16\">\r\n        <!-- 图表区域 -->\r\n        <div class=\"chart-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">业务数据趋势</span>\r\n              <div class=\"chart-controls\">\r\n                <el-radio-group v-model=\"chartPeriod\" size=\"small\">\r\n                  <el-radio-button label=\"week\">本周</el-radio-button>\r\n                  <el-radio-button label=\"month\">本月</el-radio-button>\r\n                  <el-radio-button label=\"year\">本年</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"chart-container\">\r\n              <div class=\"chart-placeholder\">\r\n                <i class=\"el-icon-data-line chart-icon\"></i>\r\n                <p>数据图表区域</p>\r\n                <p class=\"chart-desc\">这里可以集成 ECharts 或其他图表库显示业务数据趋势</p>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 最近活动 -->\r\n        <div class=\"activity-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">最近活动</span>\r\n              <el-button type=\"text\" @click=\"viewAllActivities\">查看全部</el-button>\r\n            </div>\r\n            <div class=\"activity-list\">\r\n              <div\r\n                v-for=\"activity in recentActivities\"\r\n                :key=\"activity.id\"\r\n                class=\"activity-item\"\r\n              >\r\n                <div class=\"activity-avatar\">\r\n                  <i :class=\"activity.icon\" :style=\"{ color: activity.color }\"></i>\r\n                </div>\r\n                <div class=\"activity-content\">\r\n                  <div class=\"activity-title\">{{ activity.title }}</div>\r\n                  <div class=\"activity-desc\">{{ activity.description }}</div>\r\n                  <div class=\"activity-time\">{{ activity.time }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 右侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n        <!-- 快捷操作 -->\r\n        <div class=\"quick-actions-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">快捷操作</span>\r\n            </div>\r\n            <div class=\"quick-actions\">\r\n              <div\r\n                v-for=\"action in quickActions\"\r\n                :key=\"action.id\"\r\n                class=\"quick-action-item\"\r\n                @click=\"handleQuickAction(action.action)\"\r\n              >\r\n                <div class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\r\n                  <i :class=\"action.icon\"></i>\r\n                </div>\r\n                <div class=\"action-content\">\r\n                  <div class=\"action-title\">{{ action.title }}</div>\r\n                  <div class=\"action-desc\">{{ action.description }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 待办事项 -->\r\n        <div class=\"todo-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">待办事项</span>\r\n              <el-badge :value=\"todoList.filter(item => !item.completed).length\" class=\"todo-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllTodos\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"todo-list\">\r\n              <div\r\n                v-for=\"todo in todoList.slice(0, 5)\"\r\n                :key=\"todo.id\"\r\n                class=\"todo-item\"\r\n                :class=\"{ completed: todo.completed }\"\r\n              >\r\n                <el-checkbox\r\n                  v-model=\"todo.completed\"\r\n                  @change=\"handleTodoChange(todo)\"\r\n                >\r\n                  {{ todo.title }}\r\n                </el-checkbox>\r\n                <div class=\"todo-priority\" :class=\"todo.priority\">\r\n                  {{ getPriorityText(todo.priority) }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 系统通知 -->\r\n        <div class=\"notification-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统通知</span>\r\n              <el-badge :value=\"notifications.filter(item => !item.read).length\" class=\"notification-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllNotifications\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"notification-list\">\r\n              <div\r\n                v-for=\"notification in notifications.slice(0, 3)\"\r\n                :key=\"notification.id\"\r\n                class=\"notification-item\"\r\n                :class=\"{ unread: !notification.read }\"\r\n                @click=\"markAsRead(notification)\"\r\n              >\r\n                <div class=\"notification-content\">\r\n                  <div class=\"notification-title\">{{ notification.title }}</div>\r\n                  <div class=\"notification-time\">{{ notification.time }}</div>\r\n                </div>\r\n                <div v-if=\"!notification.read\" class=\"notification-dot\"></div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- System Monitor -->\r\n        <div class=\"system-monitor-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统监控</span>\r\n            </div>\r\n            <div class=\"system-monitor-content\">\r\n              <system-monitor></system-monitor>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport SystemMonitor from '@/components/SystemMonitor.vue'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: {\r\n    SystemMonitor\r\n  },\r\n  data() {\r\n    return {\r\n      chartPeriod: 'month',\r\n      loading: false,\r\n      stats: {\r\n        totalUsers: 0,\r\n        totalCases: 0,\r\n        totalContracts: 0,\r\n        totalRevenue: '0'\r\n      },\r\n      recentActivities: [],\r\n      quickActions: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-plus',\r\n          color: '#409EFF',\r\n          title: '新建案件',\r\n          description: '创建新的法律案件',\r\n          action: 'new-case'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document-add',\r\n          color: '#67C23A',\r\n          title: '新建合同',\r\n          description: '创建新的合同文档',\r\n          action: 'new-contract'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#E6A23C',\r\n          title: '添加客户',\r\n          description: '添加新的客户信息',\r\n          action: 'new-client'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-upload',\r\n          color: '#F56C6C',\r\n          title: '文件归档',\r\n          description: '上传并归档文件',\r\n          action: 'upload-file'\r\n        }\r\n      ],\r\n      todoList: [\r\n        {\r\n          id: 1,\r\n          title: '审核张三的合同申请',\r\n          completed: false,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '回复李四的法律咨询',\r\n          completed: false,\r\n          priority: 'medium'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '准备明天的庭审材料',\r\n          completed: true,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '更新客户联系信息',\r\n          completed: false,\r\n          priority: 'low'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '整理本月财务报表',\r\n          completed: false,\r\n          priority: 'medium'\r\n        }\r\n      ],\r\n      notifications: [\r\n        {\r\n          id: 1,\r\n          title: '系统维护通知',\r\n          time: '今天 14:30',\r\n          read: false\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '新版本更新',\r\n          time: '昨天 16:20',\r\n          read: false\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '数据备份完成',\r\n          time: '昨天 09:15',\r\n          read: true\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDashboardData()\r\n  },\r\n  methods: {\r\n    // 加载仪表板数据\r\n    async loadDashboardData() {\r\n      this.loading = true\r\n      try {\r\n        await Promise.all([\r\n          this.loadStats(),\r\n          this.loadActivities(),\r\n          this.loadTodos(),\r\n          this.loadQuickActions(),\r\n          this.loadNotifications()\r\n        ])\r\n      } catch (error) {\r\n        console.error('加载仪表板数据失败:', error)\r\n        this.$message.error('加载数据失败，请刷新页面重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStats() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getStats')\r\n        if (response.code === 200) {\r\n          const data = response.data\r\n          this.stats = {\r\n            totalUsers: data.totalUsers || 0,\r\n            totalCases: data.totalDebts || 0, // 债务数作为案件数\r\n            totalContracts: data.totalOrders || 0, // 订单数作为合同数\r\n            totalRevenue: data.totalRevenue || '0'\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载最近活动\r\n    async loadActivities() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getActivities')\r\n        if (response.code === 200) {\r\n          this.recentActivities = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载活动数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载待办事项\r\n    async loadTodos() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getTodos')\r\n        if (response.code === 200) {\r\n          this.todoList = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载待办事项失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载快捷操作\r\n    async loadQuickActions() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getQuickActions')\r\n        if (response.code === 200) {\r\n          const actions = response.data || []\r\n          // 转换为前端需要的格式\r\n          this.quickActions = actions.map(action => ({\r\n            id: action.title,\r\n            icon: action.icon,\r\n            color: action.color,\r\n            title: action.title,\r\n            description: `当前数量: ${action.count}`,\r\n            action: action.url\r\n          }))\r\n        }\r\n      } catch (error) {\r\n        console.error('加载快捷操作失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载系统通知\r\n    async loadNotifications() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getNotifications')\r\n        if (response.code === 200) {\r\n          this.notifications = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载通知失败:', error)\r\n      }\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date()\r\n      const options = {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric',\r\n        weekday: 'long'\r\n      }\r\n      return now.toLocaleDateString('zh-CN', options)\r\n    },\r\n\r\n    handleQuickAction(action) {\r\n      // 如果action是URL路径，直接跳转\r\n      if (action.startsWith('/')) {\r\n        this.$router.push(action)\r\n        return\r\n      }\r\n\r\n      // 兼容原有的action处理\r\n      switch (action) {\r\n        case 'new-case':\r\n          this.$router.push('/debts')\r\n          break\r\n        case 'new-contract':\r\n          this.$router.push('/dingdan')\r\n          break\r\n        case 'new-client':\r\n          this.$router.push('/user')\r\n          break\r\n        case 'upload-file':\r\n          this.$router.push('/archive/file')\r\n          break\r\n        default:\r\n          this.$message.info(`执行操作: ${action}`)\r\n      }\r\n    },\r\n\r\n    viewAllActivities() {\r\n      // 跳转到活动日志页面（如果有的话）\r\n      this.$message.info('活动日志功能开发中')\r\n    },\r\n\r\n    viewAllTodos() {\r\n      // 可以创建一个专门的待办事项管理页面，或者跳转到相关管理页面\r\n      this.$router.push('/todo/list')\r\n    },\r\n\r\n    viewAllNotifications() {\r\n      // 可以创建一个专门的通知管理页面\r\n      this.$router.push('/notifications')\r\n    },\r\n\r\n    async handleTodoChange(todo) {\r\n      try {\r\n        const response = await this.postRequest('/dashboard/updateTodo', {\r\n          id: todo.id,\r\n          completed: todo.completed\r\n        })\r\n        if (response.code === 200) {\r\n          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\r\n        }\r\n      } catch (error) {\r\n        console.error('更新待办事项失败:', error)\r\n        // 回滚状态\r\n        todo.completed = !todo.completed\r\n        this.$message.error('更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    async markAsRead(notification) {\r\n      try {\r\n        const response = await this.postRequest('/dashboard/markNotificationRead', {\r\n          id: notification.id\r\n        })\r\n        if (response.code === 200) {\r\n          notification.read = true\r\n          this.$message.success('通知已标记为已读')\r\n        }\r\n      } catch (error) {\r\n        console.error('标记通知失败:', error)\r\n        this.$message.error('操作失败，请重试')\r\n      }\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const map = {\r\n        high: '高',\r\n        medium: '中',\r\n        low: '低'\r\n      }\r\n      return map[priority] || '中'\r\n    },\r\n\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.loadDashboardData()\r\n      this.$message.success('数据已刷新')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 欢迎区域 */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  padding: 30px;\r\n  margin-bottom: 20px;\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n.case-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n.contract-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n.revenue-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.chart-section,\r\n.activity-section,\r\n.quick-actions-section,\r\n.todo-section,\r\n.notification-section,\r\n.system-monitor-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 图表区域 */\r\n.chart-container {\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  text-align: center;\r\n  color: #95a5a6;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.chart-desc {\r\n  margin: 8px 0 0 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 活动列表 */\r\n.activity-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.activity-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.activity-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.activity-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.activity-content {\r\n  flex: 1;\r\n}\r\n\r\n.activity-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-time {\r\n  color: #bdc3c7;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 快捷操作 */\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-action-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  background-color: #f8f9fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quick-action-item:hover {\r\n  background-color: #e9ecef;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.action-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.action-desc {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 待办事项 */\r\n.todo-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.todo-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.todo-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.todo-item.completed {\r\n  opacity: 0.6;\r\n}\r\n\r\n.todo-priority {\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.todo-priority.high {\r\n  background-color: #fee;\r\n  color: #e74c3c;\r\n}\r\n\r\n.todo-priority.medium {\r\n  background-color: #fff3cd;\r\n  color: #f39c12;\r\n}\r\n\r\n.todo-priority.low {\r\n  background-color: #d4edda;\r\n  color: #27ae60;\r\n}\r\n\r\n/* 通知列表 */\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.notification-item:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.notification-item.unread {\r\n  background-color: #e3f2fd;\r\n}\r\n\r\n.notification-content {\r\n  flex: 1;\r\n}\r\n\r\n.notification-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-time {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #409EFF;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .welcome-section {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 20px;\r\n  }\r\n\r\n  .welcome-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 12px;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Dashboard.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Dashboard.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Dashboard.vue?vue&type=template&id=74108510&scoped=true\"\nimport script from \"./Dashboard.vue?vue&type=script&lang=js\"\nexport * from \"./Dashboard.vue?vue&type=script&lang=js\"\nimport style0 from \"./Dashboard.vue?vue&type=style&index=0&id=74108510&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"74108510\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Dashboard.vue?vue&type=style&index=0&id=74108510&prod&scoped=true&lang=css\""], "sourceRoot": ""}