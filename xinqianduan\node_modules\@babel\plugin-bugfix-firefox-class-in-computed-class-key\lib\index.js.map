{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperEnvironmentVisitor", "_default", "exports", "default", "declare", "types", "t", "traverse", "assertVersion", "containsClassExpressionVisitor", "ClassExpression", "path", "state", "found", "stop", "Function", "skip", "containsYieldOrAwaitVisitor", "visitors", "merge", "YieldExpression", "yield", "await", "AwaitExpression", "environmentVisitor", "containsClassExpression", "isClassExpression", "node", "isFunction", "wrap", "context", "isYieldExpression", "isAwaitExpression", "replacement", "fn", "functionExpression", "blockStatement", "returnStatement", "yieldExpression", "callExpression", "memberExpression", "identifier", "thisExpression", "arrowFunctionExpression", "awaitExpression", "replaceWith", "name", "visitor", "Class", "hasPrivateElement", "body", "some", "isPrivate", "elem", "get", "computed"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { types as t, NodePath, Visitor } from \"@babel/core\";\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nexport default declare(({ types: t, traverse, assertVersion }) => {\n  assertVersion(REQUIRED_VERSION(7));\n\n  const containsClassExpressionVisitor: Visitor<{ found: boolean }> = {\n    ClassExpression(path, state) {\n      state.found = true;\n      path.stop();\n    },\n    Function(path) {\n      path.skip();\n    },\n  };\n\n  const containsYieldOrAwaitVisitor = traverse.visitors.merge([\n    {\n      YieldExpression(path, state) {\n        state.yield = true;\n        if (state.await) path.stop();\n      },\n      AwaitExpression(path, state) {\n        state.await = true;\n        if (state.yield) path.stop();\n      },\n    } satisfies Visitor<{ yield: boolean; await: boolean }>,\n    environmentVisitor,\n  ]);\n\n  function containsClassExpression(path: NodePath<t.Node>) {\n    if (t.isClassExpression(path.node)) return true;\n    if (t.isFunction(path.node)) return false;\n    const state = { found: false };\n    path.traverse(containsClassExpressionVisitor, state);\n    return state.found;\n  }\n\n  function wrap(path: NodePath<t.Expression>) {\n    const context = {\n      yield: t.isYieldExpression(path.node),\n      await: t.isAwaitExpression(path.node),\n    };\n    path.traverse(containsYieldOrAwaitVisitor, context);\n\n    let replacement;\n\n    if (context.yield) {\n      const fn = t.functionExpression(\n        null,\n        [],\n        t.blockStatement([t.returnStatement(path.node)]),\n        /* generator */ true,\n        /* async */ context.await,\n      );\n\n      replacement = t.yieldExpression(\n        t.callExpression(t.memberExpression(fn, t.identifier(\"call\")), [\n          t.thisExpression(),\n          // NOTE: In some context arguments is invalid (it might not be defined\n          // in the top-level scope, or it's a syntax error in static class blocks).\n          // However, `yield` is also invalid in those contexts, so we can safely\n          // inject a reference to arguments.\n          t.identifier(\"arguments\"),\n        ]),\n        true,\n      );\n    } else {\n      const fn = t.arrowFunctionExpression([], path.node, context.await);\n\n      replacement = t.callExpression(fn, []);\n      if (context.await) replacement = t.awaitExpression(replacement);\n    }\n\n    path.replaceWith(replacement);\n  }\n\n  return {\n    name: \"bugfix-firefox-class-in-computed-class-key\",\n\n    visitor: {\n      Class(path) {\n        const hasPrivateElement = path.node.body.body.some(node =>\n          t.isPrivate(node),\n        );\n        if (!hasPrivateElement) return;\n\n        for (const elem of path.get(\"body.body\")) {\n          if (\n            \"computed\" in elem.node &&\n            elem.node.computed &&\n            containsClassExpression(elem.get(\"key\"))\n          ) {\n            wrap(\n              // @ts-expect-error .key also includes t.PrivateName\n              elem.get(\"key\") satisfies NodePath<t.Expression>,\n            );\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AACA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AAAmE,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEpD,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC,QAAQ;EAAEC;AAAc,CAAC,KAAK;EAChEA,aAAa,CAAkB,CAAE,CAAC;EAElC,MAAMC,8BAA2D,GAAG;IAClEC,eAAeA,CAACC,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACC,KAAK,GAAG,IAAI;MAClBF,IAAI,CAACG,IAAI,CAAC,CAAC;IACb,CAAC;IACDC,QAAQA,CAACJ,IAAI,EAAE;MACbA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMC,2BAA2B,GAAGV,QAAQ,CAACW,QAAQ,CAACC,KAAK,CAAC,CAC1D;IACEC,eAAeA,CAACT,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACS,KAAK,GAAG,IAAI;MAClB,IAAIT,KAAK,CAACU,KAAK,EAAEX,IAAI,CAACG,IAAI,CAAC,CAAC;IAC9B,CAAC;IACDS,eAAeA,CAACZ,IAAI,EAAEC,KAAK,EAAE;MAC3BA,KAAK,CAACU,KAAK,GAAG,IAAI;MAClB,IAAIV,KAAK,CAACS,KAAK,EAAEV,IAAI,CAACG,IAAI,CAAC,CAAC;IAC9B;EACF,CAAC,EACDU,iCAAkB,CACnB,CAAC;EAEF,SAASC,uBAAuBA,CAACd,IAAsB,EAAE;IACvD,IAAIL,CAAC,CAACoB,iBAAiB,CAACf,IAAI,CAACgB,IAAI,CAAC,EAAE,OAAO,IAAI;IAC/C,IAAIrB,CAAC,CAACsB,UAAU,CAACjB,IAAI,CAACgB,IAAI,CAAC,EAAE,OAAO,KAAK;IACzC,MAAMf,KAAK,GAAG;MAAEC,KAAK,EAAE;IAAM,CAAC;IAC9BF,IAAI,CAACJ,QAAQ,CAACE,8BAA8B,EAAEG,KAAK,CAAC;IACpD,OAAOA,KAAK,CAACC,KAAK;EACpB;EAEA,SAASgB,IAAIA,CAAClB,IAA4B,EAAE;IAC1C,MAAMmB,OAAO,GAAG;MACdT,KAAK,EAAEf,CAAC,CAACyB,iBAAiB,CAACpB,IAAI,CAACgB,IAAI,CAAC;MACrCL,KAAK,EAAEhB,CAAC,CAAC0B,iBAAiB,CAACrB,IAAI,CAACgB,IAAI;IACtC,CAAC;IACDhB,IAAI,CAACJ,QAAQ,CAACU,2BAA2B,EAAEa,OAAO,CAAC;IAEnD,IAAIG,WAAW;IAEf,IAAIH,OAAO,CAACT,KAAK,EAAE;MACjB,MAAMa,EAAE,GAAG5B,CAAC,CAAC6B,kBAAkB,CAC7B,IAAI,EACJ,EAAE,EACF7B,CAAC,CAAC8B,cAAc,CAAC,CAAC9B,CAAC,CAAC+B,eAAe,CAAC1B,IAAI,CAACgB,IAAI,CAAC,CAAC,CAAC,EAChC,IAAI,EACRG,OAAO,CAACR,KACtB,CAAC;MAEDW,WAAW,GAAG3B,CAAC,CAACgC,eAAe,CAC7BhC,CAAC,CAACiC,cAAc,CAACjC,CAAC,CAACkC,gBAAgB,CAACN,EAAE,EAAE5B,CAAC,CAACmC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAC7DnC,CAAC,CAACoC,cAAc,CAAC,CAAC,EAKlBpC,CAAC,CAACmC,UAAU,CAAC,WAAW,CAAC,CAC1B,CAAC,EACF,IACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMP,EAAE,GAAG5B,CAAC,CAACqC,uBAAuB,CAAC,EAAE,EAAEhC,IAAI,CAACgB,IAAI,EAAEG,OAAO,CAACR,KAAK,CAAC;MAElEW,WAAW,GAAG3B,CAAC,CAACiC,cAAc,CAACL,EAAE,EAAE,EAAE,CAAC;MACtC,IAAIJ,OAAO,CAACR,KAAK,EAAEW,WAAW,GAAG3B,CAAC,CAACsC,eAAe,CAACX,WAAW,CAAC;IACjE;IAEAtB,IAAI,CAACkC,WAAW,CAACZ,WAAW,CAAC;EAC/B;EAEA,OAAO;IACLa,IAAI,EAAE,4CAA4C;IAElDC,OAAO,EAAE;MACPC,KAAKA,CAACrC,IAAI,EAAE;QACV,MAAMsC,iBAAiB,GAAGtC,IAAI,CAACgB,IAAI,CAACuB,IAAI,CAACA,IAAI,CAACC,IAAI,CAACxB,IAAI,IACrDrB,CAAC,CAAC8C,SAAS,CAACzB,IAAI,CAClB,CAAC;QACD,IAAI,CAACsB,iBAAiB,EAAE;QAExB,KAAK,MAAMI,IAAI,IAAI1C,IAAI,CAAC2C,GAAG,CAAC,WAAW,CAAC,EAAE;UACxC,IACE,UAAU,IAAID,IAAI,CAAC1B,IAAI,IACvB0B,IAAI,CAAC1B,IAAI,CAAC4B,QAAQ,IAClB9B,uBAAuB,CAAC4B,IAAI,CAACC,GAAG,CAAC,KAAK,CAAC,CAAC,EACxC;YACAzB,IAAI,CAEFwB,IAAI,CAACC,GAAG,CAAC,KAAK,CAChB,CAAC;UACH;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}