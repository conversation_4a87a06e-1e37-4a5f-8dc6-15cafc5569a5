{"version": 3, "sources": ["webpack:///./src/views/pages/yuangong/zhiwei.vue", "webpack:///src/views/pages/yuangong/zhiwei.vue", "webpack:///./src/views/pages/yuangong/zhiwei.vue?a7e5", "webpack:///./src/views/pages/yuangong/zhiwei.vue?7b19", "webpack:///./src/views/pages/yuangong/zhiwei.vue?8ae4"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "$event", "editData", "_v", "refulsh", "nativeOn", "type", "indexOf", "_k", "keyCode", "key", "searchData", "apply", "arguments", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "permission_level", "status", "clearSearch", "_s", "total", "adminCount", "userCount", "activeCount", "viewMode", "directives", "name", "rawName", "loading", "list", "scopedSlots", "_u", "fn", "scope", "row", "title", "level", "_e", "_l", "getPermissionLabels", "quanxian", "permission", "index", "staticStyle", "getPermissionTagType", "changeStatus", "create_time", "id", "delData", "$index", "position", "desc", "size", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "options", "props", "saveData", "dialogVisible", "show_image", "staticRenderFns", "components", "data", "multiple", "allSize", "page", "url", "info", "required", "message", "trigger", "computed", "filter", "item", "includes", "length", "mounted", "getData", "methods", "permissions", "Array", "isArray", "permissionMap", "map", "Boolean", "$message", "success", "change", "_this", "getInfo", "getQuanxians", "getPermissionsFromManagement", "permissionData", "label", "children", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "code", "splice", "msg", "catch", "$router", "go", "setTimeout", "$refs", "validate", "valid", "postRequest", "val", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "component"], "mappings": "gJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,UAAUE,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAAS,MAAM,CAACT,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIW,UAAU,CAACX,EAAIU,GAAG,WAAW,OAAOR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,aAAa,UAAY,IAAIM,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAIA,EAAOK,KAAKC,QAAQ,QAAQd,EAAIe,GAAGP,EAAOQ,QAAQ,QAAQ,GAAGR,EAAOS,IAAI,SAAgB,KAAYjB,EAAIkB,WAAWC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOtB,EAAIuB,OAAOC,QAASC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAAC1B,EAAG,IAAI,CAACE,YAAY,gCAAgCE,MAAM,CAAC,KAAO,UAAUuB,KAAK,cAAc,GAAG3B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOO,iBAAkBL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,mBAAoBG,IAAME,WAAW,4BAA4B,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,WAAWJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,MAAM,MAAQ,WAAWJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,WAAW,IAAI,GAAGJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,QAAQR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOQ,OAAQN,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,SAAUG,IAAME,WAAW,kBAAkB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,KAAKJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,IAAI,KAAKJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAIkB,aAAa,CAAClB,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQP,EAAIgC,cAAc,CAAChC,EAAIU,GAAG,WAAW,QAAQ,GAAGR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAIkC,UAAUhC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,gBAAgBR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,yBAAyBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAImC,eAAejC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,gBAAgBR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAIoC,cAAclC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,gBAAgBR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACE,YAAY,2BAA2BF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAIqC,gBAAgBnC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,iBAAiB,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACE,YAAY,aAAaE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,UAAjBN,EAAIsC,SAAuB,UAAY,GAAG,KAAO,eAAe,KAAO,SAAS/B,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIsC,SAAW,WAAW,CAACtC,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,SAAjBN,EAAIsC,SAAsB,UAAY,GAAG,KAAO,iBAAiB,KAAO,SAAS/B,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIsC,SAAW,UAAU,CAACtC,EAAIU,GAAG,aAAa,IAAI,KAAuB,UAAjBV,EAAIsC,SAAsBpC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAACqC,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYnB,MAAOtB,EAAI0C,QAASd,WAAW,YAAYxB,YAAY,iBAAiBE,MAAM,CAAC,KAAON,EAAI2C,KAAK,OAAS,KAAK,CAACzC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,OAAOsC,YAAY5C,EAAI6C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC7C,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGc,EAAMC,IAAIC,UAAWF,EAAMC,IAAIE,MAAOhD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGc,EAAMC,IAAIE,UAAUlD,EAAImD,WAAW,MAAK,EAAM,aAAajD,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,YAAY,MAAM,wBAAwB,MAAMJ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUsC,YAAY5C,EAAI6C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC7C,EAAG,MAAM,CAACE,YAAY,mBAAmBJ,EAAIoD,GAAIpD,EAAIqD,oBAAoBN,EAAMC,IAAIM,WAAW,SAASC,EAAWC,GAAO,OAAOtD,EAAG,SAAS,CAACe,IAAIuC,EAAMC,YAAY,CAAC,OAAS,OAAOnD,MAAM,CAAC,KAAO,OAAO,KAAON,EAAI0D,qBAAqBH,KAAc,CAACvD,EAAIU,GAAG,IAAIV,EAAIiC,GAAGsB,GAAY,UAAS,OAAO,MAAK,EAAM,aAAarD,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUsC,YAAY5C,EAAI6C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC7C,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,GAAGC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOR,EAAI2D,aAAaZ,EAAMC,OAAO3B,MAAM,CAACC,MAAOyB,EAAMC,IAAIjB,OAAQN,SAAS,SAAUC,GAAM1B,EAAI2B,KAAKoB,EAAMC,IAAK,SAAUtB,IAAME,WAAW,0BAA0B,MAAK,EAAM,cAAc1B,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUsC,YAAY5C,EAAI6C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC7C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,IAAIV,EAAIiC,GAAGc,EAAMC,IAAIY,aAAa,WAAW,MAAK,EAAM,cAAc1D,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUsC,YAAY5C,EAAI6C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC7C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAASsC,EAAMC,IAAIa,OAAO,CAAC7D,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI8D,QAAQf,EAAMgB,OAAQhB,EAAMC,IAAIa,OAAO,CAAC7D,EAAIU,GAAG,WAAW,OAAO,MAAK,EAAM,eAAe,IAAI,GAAGV,EAAImD,KAAuB,SAAjBnD,EAAIsC,SAAqBpC,EAAG,MAAM,CAACqC,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYnB,MAAOtB,EAAI0C,QAASd,WAAW,YAAYxB,YAAY,aAAa,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAKN,EAAIoD,GAAIpD,EAAI2C,MAAM,SAASqB,GAAU,OAAO9D,EAAG,SAAS,CAACe,IAAI+C,EAASH,GAAGzD,YAAY,oBAAoBE,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIU,GAAG,IAAIV,EAAIiC,GAAG+B,EAASf,OAAO,OAAO/C,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,EAAE,KAAO,SAASC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOR,EAAI2D,aAAaK,KAAY3C,MAAM,CAACC,MAAO0C,EAASjC,OAAQN,SAAS,SAAUC,GAAM1B,EAAI2B,KAAKqC,EAAU,SAAUtC,IAAME,WAAW,sBAAsB,KAAK1B,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIU,GAAGV,EAAIiC,GAAG+B,EAASC,SAAS/D,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACJ,EAAIU,GAAG,WAAWR,EAAG,MAAM,CAACE,YAAY,mBAAmBJ,EAAIoD,GAAIpD,EAAIqD,oBAAoBW,EAASV,WAAW,SAASC,EAAWC,GAAO,OAAOtD,EAAG,SAAS,CAACe,IAAIuC,EAAMC,YAAY,CAAC,OAAS,OAAOnD,MAAM,CAAC,KAAO,OAAO,KAAON,EAAI0D,qBAAqBH,KAAc,CAACvD,EAAIU,GAAG,IAAIV,EAAIiC,GAAGsB,GAAY,UAAS,KAAKrD,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,IAAIV,EAAIiC,GAAG+B,EAASJ,aAAa,WAAW1D,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAASuD,EAASH,OAAO,CAAC7D,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQ,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAI8D,QAAQ9D,EAAI2C,KAAK7B,QAAQkD,GAAWA,EAASH,OAAO,CAAC7D,EAAIU,GAAG,WAAW,UAAS,IAAI,GAAGV,EAAImD,KAAKjD,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,gBAAgB,CAACE,YAAY,aAAaE,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,GAAI,IAAI,YAAYN,EAAIkE,KAAK,OAAS,0CAA0C,MAAQlE,EAAIkC,OAAO3B,GAAG,CAAC,cAAcP,EAAImE,iBAAiB,iBAAiBnE,EAAIoE,wBAAwB,MAAM,GAAGlE,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQN,EAAIiD,MAAQ,KAAK,QAAUjD,EAAIqE,kBAAkB,wBAAuB,EAAM,MAAQ,OAAO9D,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAIqE,kBAAkB7D,KAAU,CAACN,EAAG,UAAU,CAACoE,IAAI,WAAWhE,MAAM,CAAC,MAAQN,EAAIuE,SAAS,MAAQvE,EAAIwE,QAAQ,CAACtE,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQN,EAAIiD,MAAQ,KAAK,cAAcjD,EAAIyE,eAAe,KAAO,UAAU,CAACvE,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,OAAOe,MAAM,CAACC,MAAOtB,EAAIuE,SAAStB,MAAOxB,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuE,SAAU,QAAS7C,IAAME,WAAW,qBAAqB,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,cAAcN,EAAIyE,iBAAiB,CAACvE,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAGe,MAAM,CAACC,MAAOtB,EAAIuE,SAASN,KAAMxC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuE,SAAU,OAAQ7C,IAAME,WAAW,oBAAoB,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,cAAcN,EAAIyE,iBAAiB,CAACvE,EAAG,cAAc,CAACI,MAAM,CAAC,QAAUN,EAAI0E,QAAQ,MAAQ1E,EAAI2E,MAAM,UAAY,IAAItD,MAAM,CAACC,MAAOtB,EAAIuE,SAASjB,SAAU7B,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuE,SAAU,WAAY7C,IAAME,WAAW,wBAAwB,IAAI,GAAG1B,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUuB,KAAK,UAAU,CAAC3B,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIqE,mBAAoB,KAAS,CAACrE,EAAIU,GAAG,SAASR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI4E,cAAc,CAAC5E,EAAIU,GAAG,UAAU,IAAI,GAAGR,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAI6E,cAAc,MAAQ,OAAOtE,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAI6E,cAAcrE,KAAU,CAACN,EAAG,WAAW,CAACI,MAAM,CAAC,IAAMN,EAAI8E,eAAe,IAAI,IAErqVC,EAAkB,CAAC,WAAY,IAAI/E,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIU,GAAG,YAAYR,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIU,GAAG,uBCiY7O,GACf8B,KAAA,OACAwC,WAAA,GACAC,OACA,OACA3C,SAAA,QACAqC,MAAA,CAAAO,UAAA,GACAR,QAAA,GACAS,QAAA,OACAxC,KAAA,GACAT,MAAA,EACAkD,KAAA,EACAlB,KAAA,GACA3C,OAAA,CACAC,QAAA,GACAM,iBAAA,GACAC,OAAA,IAEAW,SAAA,EACA2C,IAAA,WACApC,MAAA,KACAqC,KAAA,GACAjB,mBAAA,EACAS,WAAA,GACAD,eAAA,EACAN,SAAA,CACAtB,MAAA,GACAgB,KAAA,GACAX,SAAA,GACAvB,OAAA,GAEAyC,MAAA,CACAvB,MAAA,CACA,CACAsC,UAAA,EACAC,QAAA,UACAC,QAAA,SAGAxB,KAAA,CACA,CACAsB,UAAA,EACAC,QAAA,UACAC,QAAA,UAIAhB,eAAA,UAGAiB,SAAA,CAEAvD,aACA,YAAAQ,KAAAgD,OAAAC,GACAA,EAAAtC,WAAAsC,EAAAtC,SAAAuC,SAAA,IAAAD,EAAAtC,SAAAuC,SAAA,MACAC,QAGA1D,YACA,YAAAO,KAAAgD,OAAAC,GACAA,EAAAtC,WAAAsC,EAAAtC,SAAAuC,SAAA,KAAAD,EAAAtC,SAAAuC,SAAA,KACAC,QAGAzD,cACA,YAAAM,KAAAgD,OAAAC,GAAA,IAAAA,EAAA7D,QAAA+D,SAGAC,UACA,KAAAC,WAEAC,QAAA,CAEA5C,oBAAA6C,GACA,IAAAA,IAAAC,MAAAC,QAAAF,GAAA,SAEA,MAAAG,EAAA,CACA,SACA,SACA,SACA,SACA,UACA,UACA,UACA,UACA,UACA,UACA,WACA,UACA,WAGA,OAAAH,EAAAI,IAAAzC,GAAAwC,EAAAxC,IAAA,KAAAA,GAAA8B,OAAAY,UAIA7C,qBAAAH,GACA,OAAAA,EAAAsC,SAAA,OAAAtC,EAAAsC,SAAA,eACAtC,EAAAsC,SAAA,OAAAtC,EAAAsC,SAAA,gBACAtC,EAAAsC,SAAA,OAAAtC,EAAAsC,SAAA,gBACAtC,EAAAsC,SAAA,OAAAtC,EAAAsC,SAAA,gBACA,QAIAlC,aAAAX,GACA,KAAAwD,SAAAC,QAAA,MAAAzD,EAAAC,YAAAD,EAAAjB,OAAA,cAIAC,cACA,KAAAT,OAAA,CACAC,QAAA,GACAM,iBAAA,GACAC,OAAA,IAEA,KAAAb,cAGAwF,WACAjG,SAAAoD,GACA,IAAA8C,EAAA,KACA,GAAA9C,EACA,KAAA+C,QAAA/C,GAEA,KAAAU,SAAA,CACAtB,MAAA,GACAgB,KAAA,IAIA0C,EAAAtC,mBAAA,EACAsC,EAAAE,gBAEAA,eAEA,KAAAC,gCAIAA,+BAEA,MAAAC,EAAA,CACA,CACAzF,MAAA,EACA0F,MAAA,OACAC,SAAA,CACA,CACA3F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,UAGA,CACA1F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,UAGA,CACA1F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,YAKA,CACA1F,MAAA,EACA0F,MAAA,OACAC,SAAA,CACA,CACA3F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,UAGA,CACA1F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,YAKA,CACA1F,MAAA,EACA0F,MAAA,OACAC,SAAA,CACA,CACA3F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,UAGA,CACA1F,MAAA,GACA0F,MAAA,QACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,SACA,CAAA1F,MAAA,IAAA0F,MAAA,SACA,CAAA1F,MAAA,IAAA0F,MAAA,WAGA,CACA1F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,YAKA,CACA1F,MAAA,EACA0F,MAAA,OACAC,SAAA,CACA,CACA3F,MAAA,GACA0F,MAAA,OACAC,SAAA,CACA,CAAA3F,MAAA,IAAA0F,MAAA,UACA,CAAA1F,MAAA,IAAA0F,MAAA,QACA,CAAA1F,MAAA,IAAA0F,MAAA,eAOA,KAAAtC,QAAAqC,GAEAH,QAAA/C,GACA,IAAA8C,EAAA,KACAA,EAAAO,WAAAP,EAAAtB,IAAA,WAAAxB,GAAAsD,KAAAC,IACAA,IACAT,EAAApC,SAAA6C,EAAAnC,SAIAnB,QAAAN,EAAAK,GACA,KAAAwD,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACA1G,KAAA,YAEAsG,KAAA,KACA,KAAAK,cAAA,KAAAnC,IAAA,aAAAxB,GAAAsD,KAAAC,IACA,KAAAA,EAAAK,MACA,KAAAjB,SAAA,CACA3F,KAAA,UACA2E,QAAA,UAEA,KAAA7C,KAAA+E,OAAAlE,EAAA,IAEA,KAAAgD,SAAA,CACA3F,KAAA,QACA2E,QAAA4B,EAAAO,UAKAC,MAAA,KACA,KAAApB,SAAA,CACA3F,KAAA,QACA2E,QAAA,aAIA7E,UACA,KAAAkH,QAAAC,GAAA,IAEA5G,aACA,KAAAkE,KAAA,EACA,KAAAlB,KAAA,GACA,KAAA8B,WAGAA,UACA,IAAAW,EAAA,KACAA,EAAAjE,SAAA,EAGAqF,WAAA,KACApB,EAAAjE,SAAA,EACAiE,EAAAhE,KAAA,CACA,CACAkB,GAAA,EACAZ,MAAA,QACAgB,KAAA,uBACAX,SAAA,UACAvB,OAAA,EACAmB,MAAA,QACAU,YAAA,uBAEA,CACAC,GAAA,EACAZ,MAAA,OACAgB,KAAA,kBACAX,SAAA,MACAvB,OAAA,EACAmB,MAAA,MACAU,YAAA,uBAEA,CACAC,GAAA,EACAZ,MAAA,OACAgB,KAAA,oBACAX,SAAA,IACAvB,OAAA,EACAmB,MAAA,OACAU,YAAA,uBAEA,CACAC,GAAA,EACAZ,MAAA,OACAgB,KAAA,mBACAX,SAAA,IACAvB,OAAA,EACAmB,MAAA,OACAU,YAAA,uBAEA,CACAC,GAAA,EACAZ,MAAA,OACAgB,KAAA,mBACAX,SAAA,KACAvB,OAAA,EACAmB,MAAA,OACAU,YAAA,wBAGA+C,EAAAzE,MAAA,GACA,MAkBA0C,WACA,IAAA+B,EAAA,KACA,KAAAqB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAAxB,EAAAtB,IAAA,YAAAd,UAAA4C,KAAAC,IACA,KAAAA,EAAAK,MACAd,EAAAH,SAAA,CACA3F,KAAA,UACA2E,QAAA4B,EAAAO,MAEA,KAAA3B,UACAW,EAAAtC,mBAAA,GAEAsC,EAAAH,SAAA,CACA3F,KAAA,QACA2E,QAAA4B,EAAAO,WASAxD,iBAAAiE,GACA,KAAAlE,KAAAkE,EAEA,KAAApC,WAEA5B,oBAAAgE,GACA,KAAAhD,KAAAgD,EACA,KAAApC,WAEAqC,cAAAC,GACA,KAAA/D,SAAAgE,SAAAD,EAAArD,KAAAI,KAGAmD,UAAAC,GACA,KAAA3D,WAAA2D,EACA,KAAA5D,eAAA,GAEA6D,aAAAD,GACA,MAAAE,EAAA,0BAAAC,KAAAH,EAAA5H,MACA8H,GACA,KAAAnC,SAAAqC,MAAA,cAIAC,SAAAL,EAAAM,GACA,IAAApC,EAAA,KACAA,EAAAO,WAAA,6BAAAuB,GAAAtB,KAAAC,IACA,KAAAA,EAAAK,MACAd,EAAApC,SAAAwE,GAAA,GAEApC,EAAAH,SAAAC,QAAA,UAEAE,EAAAH,SAAAqC,MAAAzB,EAAAO,UCvzB6W,I,wBCQzWqB,EAAY,eACd,EACAjJ,EACAgF,GACA,EACA,KACA,WACA,MAIa,aAAAiE,E,2CCnBf", "file": "js/chunk-5f4caf1e.756f9b60.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"position-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"add-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增职位 \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新 \")])],1)])]),_c('div',{staticClass:\"search-section\"},[_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-row\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"职位搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入职位名称或描述\",\"clearable\":\"\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchData.apply(null, arguments)}},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"权限级别\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择权限级别\",\"clearable\":\"\"},model:{value:(_vm.search.permission_level),callback:function ($$v) {_vm.$set(_vm.search, \"permission_level\", $$v)},expression:\"search.permission_level\"}},[_c('el-option',{attrs:{\"label\":\"超级管理员\",\"value\":\"super\"}}),_c('el-option',{attrs:{\"label\":\"管理员\",\"value\":\"admin\"}}),_c('el-option',{attrs:{\"label\":\"普通用户\",\"value\":\"user\"}})],1)],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.status),callback:function ($$v) {_vm.$set(_vm.search, \"status\", $$v)},expression:\"search.status\"}},[_c('el-option',{attrs:{\"label\":\"启用\",\"value\":1}}),_c('el-option',{attrs:{\"label\":\"禁用\",\"value\":0}})],1)],1)]),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchData}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.clearSearch}},[_vm._v(\" 重置 \")])],1)])])],1),_c('div',{staticClass:\"stats-section\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon total\"},[_c('i',{staticClass:\"el-icon-postcard\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.total))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总职位数\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon admin\"},[_c('i',{staticClass:\"el-icon-user-solid\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.adminCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"管理职位\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon user\"},[_c('i',{staticClass:\"el-icon-user\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.userCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"普通职位\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon active\"},[_c('i',{staticClass:\"el-icon-circle-check\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.activeCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"启用职位\")])])])])],1)],1),_c('div',{staticClass:\"table-section\"},[_c('el-card',{staticClass:\"table-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"table-header\"},[_c('div',{staticClass:\"table-title\"},[_c('i',{staticClass:\"el-icon-menu\"}),_vm._v(\" 职位列表 \")]),_c('div',{staticClass:\"table-tools\"},[_c('el-button-group',[_c('el-button',{attrs:{\"type\":_vm.viewMode === 'table' ? 'primary' : '',\"icon\":\"el-icon-menu\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'table'}}},[_vm._v(\" 列表视图 \")]),_c('el-button',{attrs:{\"type\":_vm.viewMode === 'card' ? 'primary' : '',\"icon\":\"el-icon-s-grid\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'card'}}},[_vm._v(\" 卡片视图 \")])],1)],1)]),(_vm.viewMode === 'table')?_c('div',{staticClass:\"table-view\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"position-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"职位名称\",\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"position-title-cell\"},[_c('div',{staticClass:\"position-title\"},[_vm._v(_vm._s(scope.row.title))]),(scope.row.level)?_c('div',{staticClass:\"position-level\"},[_vm._v(_vm._s(scope.row.level))]):_vm._e()])]}}],null,false,414999880)}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"职位描述\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"label\":\"权限配置\",\"width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"permission-tags\"},_vm._l((_vm.getPermissionLabels(scope.row.quanxian)),function(permission,index){return _c('el-tag',{key:index,staticStyle:{\"margin\":\"2px\"},attrs:{\"size\":\"mini\",\"type\":_vm.getPermissionTagType(permission)}},[_vm._v(\" \"+_vm._s(permission)+\" \")])}),1)]}}],null,false,110400083)}),_c('el-table-column',{attrs:{\"label\":\"状态\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0},on:{\"change\":function($event){return _vm.changeStatus(scope.row)}},model:{value:(scope.row.status),callback:function ($$v) {_vm.$set(scope.row, \"status\", $$v)},expression:\"scope.row.status\"}})]}}],null,false,2880962836)}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建时间\",\"width\":\"160\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-cell\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" \"+_vm._s(scope.row.create_time)+\" \")])]}}],null,false,3001843918)}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"180\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 删除 \")])],1)]}}],null,false,2809669383)})],1)],1):_vm._e(),(_vm.viewMode === 'card')?_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"card-view\"},[_c('el-row',{attrs:{\"gutter\":20}},_vm._l((_vm.list),function(position){return _c('el-col',{key:position.id,staticClass:\"position-card-col\",attrs:{\"span\":8}},[_c('div',{staticClass:\"position-card\"},[_c('div',{staticClass:\"card-header\"},[_c('div',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-postcard\"}),_vm._v(\" \"+_vm._s(position.title)+\" \")]),_c('div',{staticClass:\"card-status\"},[_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0,\"size\":\"small\"},on:{\"change\":function($event){return _vm.changeStatus(position)}},model:{value:(position.status),callback:function ($$v) {_vm.$set(position, \"status\", $$v)},expression:\"position.status\"}})],1)]),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-desc\"},[_vm._v(_vm._s(position.desc))]),_c('div',{staticClass:\"card-permissions\"},[_c('div',{staticClass:\"permission-title\"},[_vm._v(\"权限配置：\")]),_c('div',{staticClass:\"permission-tags\"},_vm._l((_vm.getPermissionLabels(position.quanxian)),function(permission,index){return _c('el-tag',{key:index,staticStyle:{\"margin\":\"2px\"},attrs:{\"size\":\"mini\",\"type\":_vm.getPermissionTagType(permission)}},[_vm._v(\" \"+_vm._s(permission)+\" \")])}),1)]),_c('div',{staticClass:\"card-footer\"},[_c('div',{staticClass:\"card-time\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" \"+_vm._s(position.create_time)+\" \")])])]),_c('div',{staticClass:\"card-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.editData(position.id)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){_vm.delData(_vm.list.indexOf(position), position.id)}}},[_vm._v(\" 删除 \")])],1)])])}),1)],1):_vm._e(),_c('div',{staticClass:\"pagination-container\"},[_c('el-pagination',{staticClass:\"pagination\",attrs:{\"page-sizes\":[12, 24, 48, 96],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)])],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":_vm.title + '标题',\"label-width\":_vm.formLabelWidth,\"prop\":\"title\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"描述\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"权限\",\"label-width\":_vm.formLabelWidth}},[_c('el-cascader',{attrs:{\"options\":_vm.options,\"props\":_vm.props,\"clearable\":\"\"},model:{value:(_vm.ruleForm.quanxian),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"quanxian\", $$v)},expression:\"ruleForm.quanxian\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"title-section\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-postcard\"}),_vm._v(\" 职位管理 \")]),_c('p',{staticClass:\"page-subtitle\"},[_vm._v(\"管理系统职位信息和权限配置\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"position-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-postcard\"></i>\r\n            职位管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统职位信息和权限配置</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增职位\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">职位搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入职位名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限级别</label>\r\n              <el-select\r\n                v-model=\"search.permission_level\"\r\n                placeholder=\"请选择权限级别\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"超级管理员\" value=\"super\"></el-option>\r\n                <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n                <el-option label=\"普通用户\" value=\"user\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-postcard\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总职位数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon admin\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ adminCount }}</div>\r\n              <div class=\"stat-label\">管理职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ userCount }}</div>\r\n              <div class=\"stat-label\">普通职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <i class=\"el-icon-circle-check\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeCount }}</div>\r\n              <div class=\"stat-label\">启用职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 职位列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            职位列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"position-table\"\r\n            stripe\r\n          >\r\n            <el-table-column prop=\"title\" label=\"职位名称\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"position-title-cell\">\r\n                  <div class=\"position-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"position-level\" v-if=\"scope.row.level\">{{ scope.row.level }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"desc\" label=\"职位描述\" min-width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"权限配置\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-tags\">\r\n                  <el-tag\r\n                    v-for=\"(permission, index) in getPermissionLabels(scope.row.quanxian)\"\r\n                    :key=\"index\"\r\n                    size=\"mini\"\r\n                    :type=\"getPermissionTagType(permission)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ permission }}\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"position in list\" :key=\"position.id\" class=\"position-card-col\">\r\n              <div class=\"position-card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-title\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    {{ position.title }}\r\n                  </div>\r\n                  <div class=\"card-status\">\r\n                    <el-switch\r\n                      v-model=\"position.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      @change=\"changeStatus(position)\"\r\n                      size=\"small\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-desc\">{{ position.desc }}</div>\r\n\r\n                  <div class=\"card-permissions\">\r\n                    <div class=\"permission-title\">权限配置：</div>\r\n                    <div class=\"permission-tags\">\r\n                      <el-tag\r\n                        v-for=\"(permission, index) in getPermissionLabels(position.quanxian)\"\r\n                        :key=\"index\"\r\n                        size=\"mini\"\r\n                        :type=\"getPermissionTagType(permission)\"\r\n                        style=\"margin: 2px;\"\r\n                      >\r\n                        {{ permission }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ position.create_time }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(position.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(position), position.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"权限\" :label-width=\"formLabelWidth\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.quanxian\"\r\n            :options=\"options\"\r\n            :props=\"props\"\r\n            clearable\r\n          ></el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      viewMode: 'table', // table | card\r\n      props: { multiple: true },\r\n      options: {},\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 5,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      },\r\n      loading: true,\r\n      url: \"/zhiwei/\",\r\n      title: \"职位\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        quanxian: [],\r\n        status: 1\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        desc: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位描述\",\r\n            trigger: \"blur\",\r\n          },\r\n        ]\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 管理员职位数量\r\n    adminCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && (item.quanxian.includes(1) || item.quanxian.includes(13))\r\n      ).length;\r\n    },\r\n    // 普通用户职位数量\r\n    userCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && !item.quanxian.includes(1) && !item.quanxian.includes(13)\r\n      ).length;\r\n    },\r\n    // 启用职位数量\r\n    activeCount() {\r\n      return this.list.filter(item => item.status === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限标签\r\n    getPermissionLabels(permissions) {\r\n      if (!permissions || !Array.isArray(permissions)) return [];\r\n\r\n      const permissionMap = {\r\n        1: \"系统管理\",\r\n        2: \"业务管理\",\r\n        3: \"文书管理\",\r\n        4: \"财务管理\",\r\n        11: \"用户管理\",\r\n        12: \"角色管理\",\r\n        13: \"权限管理\",\r\n        21: \"订单管理\",\r\n        22: \"客户管理\",\r\n        31: \"合同管理\",\r\n        32: \"律师函管理\",\r\n        33: \"课程管理\",\r\n        41: \"支付管理\"\r\n      };\r\n\r\n      return permissions.map(id => permissionMap[id] || `权限${id}`).filter(Boolean);\r\n    },\r\n\r\n    // 获取权限标签类型\r\n    getPermissionTagType(permission) {\r\n      if (permission.includes('系统') || permission.includes('权限')) return 'danger';\r\n      if (permission.includes('业务') || permission.includes('订单')) return 'primary';\r\n      if (permission.includes('文书') || permission.includes('合同')) return 'success';\r\n      if (permission.includes('财务') || permission.includes('支付')) return 'warning';\r\n      return 'info';\r\n    },\r\n\r\n    // 状态切换\r\n    changeStatus(row) {\r\n      this.$message.success(`职位\"${row.title}\"状态已${row.status ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    change() {},\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getQuanxians();\r\n    },\r\n    getQuanxians() {\r\n      // 从权限管理页面获取权限数据\r\n      this.getPermissionsFromManagement();\r\n    },\r\n\r\n    // 从权限管理获取权限数据\r\n    getPermissionsFromManagement() {\r\n      // 模拟从权限管理页面获取的权限数据\r\n      const permissionData = [\r\n        {\r\n          value: 1,\r\n          label: \"系统管理\",\r\n          children: [\r\n            {\r\n              value: 11,\r\n              label: \"用户管理\",\r\n              children: [\r\n                { value: 111, label: \"查看用户\" },\r\n                { value: 112, label: \"新增用户\" },\r\n                { value: 113, label: \"编辑用户\" },\r\n                { value: 114, label: \"删除用户\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 12,\r\n              label: \"角色管理\",\r\n              children: [\r\n                { value: 121, label: \"查看角色\" },\r\n                { value: 122, label: \"新增角色\" },\r\n                { value: 123, label: \"编辑角色\" },\r\n                { value: 124, label: \"删除角色\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 13,\r\n              label: \"权限管理\",\r\n              children: [\r\n                { value: 131, label: \"查看权限\" },\r\n                { value: 132, label: \"新增权限\" },\r\n                { value: 133, label: \"编辑权限\" },\r\n                { value: 134, label: \"删除权限\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 2,\r\n          label: \"业务管理\",\r\n          children: [\r\n            {\r\n              value: 21,\r\n              label: \"订单管理\",\r\n              children: [\r\n                { value: 211, label: \"查看订单\" },\r\n                { value: 212, label: \"新增订单\" },\r\n                { value: 213, label: \"编辑订单\" },\r\n                { value: 214, label: \"删除订单\" },\r\n                { value: 215, label: \"导出订单\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 22,\r\n              label: \"客户管理\",\r\n              children: [\r\n                { value: 221, label: \"查看客户\" },\r\n                { value: 222, label: \"新增客户\" },\r\n                { value: 223, label: \"编辑客户\" },\r\n                { value: 224, label: \"删除客户\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 3,\r\n          label: \"文书管理\",\r\n          children: [\r\n            {\r\n              value: 31,\r\n              label: \"合同管理\",\r\n              children: [\r\n                { value: 311, label: \"查看合同\" },\r\n                { value: 312, label: \"新增合同\" },\r\n                { value: 313, label: \"编辑合同\" },\r\n                { value: 314, label: \"删除合同\" },\r\n                { value: 315, label: \"审核合同\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 32,\r\n              label: \"律师函管理\",\r\n              children: [\r\n                { value: 321, label: \"查看律师函\" },\r\n                { value: 322, label: \"发送律师函\" },\r\n                { value: 323, label: \"编辑律师函\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 33,\r\n              label: \"课程管理\",\r\n              children: [\r\n                { value: 331, label: \"查看课程\" },\r\n                { value: 332, label: \"新增课程\" },\r\n                { value: 333, label: \"编辑课程\" },\r\n                { value: 334, label: \"删除课程\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 4,\r\n          label: \"财务管理\",\r\n          children: [\r\n            {\r\n              value: 41,\r\n              label: \"支付管理\",\r\n              children: [\r\n                { value: 411, label: \"查看支付记录\" },\r\n                { value: 412, label: \"处理退款\" },\r\n                { value: 413, label: \"导出财务报表\" }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      this.options = permissionData;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"系统管理员\",\r\n            desc: \"拥有系统所有权限，负责系统维护和用户管理\",\r\n            quanxian: [1, 2, 3, 4], // 拥有所有模块权限\r\n            status: 1,\r\n            level: \"超级管理员\",\r\n            create_time: \"2024-01-01 10:00:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"业务经理\",\r\n            desc: \"负责业务流程管理，客户关系维护\",\r\n            quanxian: [2, 3], // 拥有业务管理和文书管理权限\r\n            status: 1,\r\n            level: \"管理员\",\r\n            create_time: \"2024-01-02 14:30:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"法务专员\",\r\n            desc: \"负责合同审核、律师函处理等法务工作\",\r\n            quanxian: [3], // 只有文书管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-03 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"财务专员\",\r\n            desc: \"负责财务管理、支付处理、报表统计\",\r\n            quanxian: [4], // 只有财务管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-04 11:20:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"客服专员\",\r\n            desc: \"负责客户咨询、问题处理、售后服务\",\r\n            quanxian: [22], // 只有客户管理权限\r\n            status: 0,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-05 16:45:00\"\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.position-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section .page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n}\r\n\r\n.search-form {\r\n  padding: 20px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.admin {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.user {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.active {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格样式 */\r\n.position-table {\r\n  margin: 0;\r\n}\r\n\r\n.position-title-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.position-title {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.position-level {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  background: #f0f2f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n  width: fit-content;\r\n}\r\n\r\n.permission-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n  justify-content: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #606266;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  padding: 20px 0;\r\n}\r\n\r\n.position-card-col {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.position-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-desc {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-permissions {\r\n  margin-bottom: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.permission-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-footer {\r\n  margin-top: auto;\r\n}\r\n\r\n.card-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.card-actions {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .position-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .position-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./zhiwei.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./zhiwei.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./zhiwei.vue?vue&type=template&id=feecce72&scoped=true\"\nimport script from \"./zhiwei.vue?vue&type=script&lang=js\"\nexport * from \"./zhiwei.vue?vue&type=script&lang=js\"\nimport style0 from \"./zhiwei.vue?vue&type=style&index=0&id=feecce72&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"feecce72\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./zhiwei.vue?vue&type=style&index=0&id=feecce72&prod&scoped=true&lang=css\""], "sourceRoot": ""}