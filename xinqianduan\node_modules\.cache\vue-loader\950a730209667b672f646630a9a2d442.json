{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue?vue&type=template&id=4b11a6a1&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue", "mtime": 1748617950297}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}