-- 系统通知表
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `type` varchar(50) DEFAULT 'system' COMMENT '通知类型：system-系统通知,update-更新通知,backup-备份通知,warning-警告通知',
  `level` varchar(20) DEFAULT 'info' COMMENT '通知级别：info-信息,warning-警告,error-错误,success-成功',
  `target_type` varchar(50) DEFAULT 'all' COMMENT '目标类型：all-所有用户,admin-管理员,user-普通用户',
  `target_id` int(11) DEFAULT NULL COMMENT '目标用户ID，为空表示所有用户',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：0-未读,1-已读',
  `is_global` tinyint(1) DEFAULT 1 COMMENT '是否全局通知：0-个人通知,1-全局通知',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `read_time` int(11) DEFAULT NULL COMMENT '阅读时间',
  `expire_time` int(11) DEFAULT NULL COMMENT '过期时间，为空表示不过期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用,1-启用',
  PRIMARY KEY (`id`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`, `is_read`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知表';

-- 插入示例数据
INSERT INTO `notifications` (`title`, `content`, `type`, `level`, `target_type`, `is_read`, `is_global`, `create_time`) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用，请提前保存数据。', 'system', 'warning', 'all', 0, 1, UNIX_TIMESTAMP()),
('新版本更新', '系统已更新到v2.1.0版本，新增了多项功能和性能优化。', 'update', 'success', 'all', 0, 1, UNIX_TIMESTAMP() - 86400),
('数据备份完成', '系统数据备份已成功完成，备份文件已保存到安全位置。', 'backup', 'info', 'admin', 1, 1, UNIX_TIMESTAMP() - 172800),
('安全提醒', '检测到异常登录行为，请及时检查账户安全。', 'warning', 'warning', 'all', 0, 1, UNIX_TIMESTAMP() - 3600);
