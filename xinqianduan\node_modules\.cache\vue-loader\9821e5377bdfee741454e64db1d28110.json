{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue", "mtime": 1748617691749}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["type.vue"], "names": [], "mappings": ";AA0YA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "type.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n  <div class=\"service-type-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-menu\"></i>\r\n          {{ this.$router.currentRoute.name }}\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务分类和类型配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-menu\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">服务类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeTypes }}</div>\r\n              <div class=\"stat-label\">活跃类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 正常\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon usage-icon\">\r\n              <i class=\"el-icon-data-analysis\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">85%</div>\r\n              <div class=\"stat-label\">使用率</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +3%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <span class=\"card-title\">\r\n            <i class=\"el-icon-search\"></i>\r\n            搜索与筛选\r\n          </span>\r\n          <div class=\"card-subtitle\">快速查找和管理服务类型</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button-group class=\"action-group\">\r\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n              导出\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n              刷新\r\n            </el-button>\r\n          </el-button-group>\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\" class=\"primary-action\">\r\n            新增类型\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n              <el-input \r\n                placeholder=\"请输入类型名称或描述关键词...\" \r\n                v-model=\"search.keyword\" \r\n                clearable\r\n                prefix-icon=\"el-icon-search\"\r\n                class=\"search-input\"\r\n                @keyup.enter.native=\"searchData()\"\r\n              />\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"服务类型\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.serviceType\" \r\n                placeholder=\"选择服务类型\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部类型\" value=\"\" />\r\n                <el-option label=\"计次服务\" value=\"1\" />\r\n                <el-option label=\"不限次数\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"状态筛选\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.status\" \r\n                placeholder=\"选择状态\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部状态\" value=\"\" />\r\n                <el-option label=\"正常\" value=\"1\" />\r\n                <el-option label=\"待完善\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item class=\"search-actions-item\">\r\n              <div class=\"search-actions\">\r\n                <el-button type=\"primary\" @click=\"searchData\" icon=\"el-icon-search\" class=\"search-btn\">\r\n                  搜索\r\n                </el-button>\r\n                <el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n                  重置\r\n                </el-button>\r\n                <el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n                  <i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n                  {{ showAdvanced ? '收起' : '高级筛选' }}\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 高级筛选区域 -->\r\n          <transition name=\"slide-fade\">\r\n            <div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n              <el-divider content-position=\"left\">\r\n                <i class=\"el-icon-setting\"></i>\r\n                高级筛选选项\r\n              </el-divider>\r\n              <div class=\"advanced-content\">\r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"创建时间范围\" class=\"advanced-item\">\r\n                    <el-date-picker\r\n                      v-model=\"search.dateRange\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      class=\"date-picker\"\r\n                    />\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.sortBy\" placeholder=\"选择排序\" class=\"sort-select\">\r\n                      <el-option label=\"创建时间\" value=\"create_time\" />\r\n                      <el-option label=\"名称字母\" value=\"title\" />\r\n                      <el-option label=\"使用频率\" value=\"usage\" />\r\n                      <el-option label=\"更新时间\" value=\"update_time\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序顺序\" class=\"advanced-item\">\r\n                    <el-radio-group v-model=\"search.sortOrder\" size=\"small\" class=\"sort-order\">\r\n                      <el-radio-button label=\"desc\">\r\n                        <i class=\"el-icon-sort-down\"></i> 降序\r\n                      </el-radio-button>\r\n                      <el-radio-button label=\"asc\">\r\n                        <i class=\"el-icon-sort-up\"></i> 升序\r\n                      </el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </div>\r\n                \r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"使用频率\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.usageLevel\" placeholder=\"选择使用频率\" class=\"usage-select\">\r\n                      <el-option label=\"全部频率\" value=\"\" />\r\n                      <el-option label=\"高频使用\" value=\"high\" />\r\n                      <el-option label=\"中频使用\" value=\"medium\" />\r\n                      <el-option label=\"低频使用\" value=\"low\" />\r\n                      <el-option label=\"未使用\" value=\"none\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"类型特性\" class=\"advanced-item\">\r\n                    <el-checkbox-group v-model=\"search.features\" class=\"feature-checkboxes\">\r\n                      <el-checkbox label=\"popular\">热门类型</el-checkbox>\r\n                      <el-checkbox label=\"new\">新增类型</el-checkbox>\r\n                      <el-checkbox label=\"recommended\">推荐类型</el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item class=\"advanced-actions\">\r\n                    <el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n                      应用筛选\r\n                    </el-button>\r\n                    <el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n                      清空高级选项\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <el-card shadow=\"hover\" class=\"table-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          类型列表\r\n        </span>\r\n        <div class=\"table-actions\">\r\n          <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n            导出数据\r\n          </el-button>\r\n          <el-button size=\"small\" @click=\"batchDelete\" icon=\"el-icon-delete\">\r\n            批量删除\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"modern-table\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        \r\n        <el-table-column label=\"类型信息\" min-width=\"300\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-info\">\r\n              <div class=\"type-header\">\r\n                <div class=\"type-icon\">\r\n                  <i class=\"el-icon-star-on\"></i>\r\n                </div>\r\n                <div class=\"type-details\">\r\n                  <div class=\"type-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"type-desc\" v-if=\"scope.row.desc\">\r\n                    {{ scope.row.desc }}\r\n                  </div>\r\n                  <div class=\"type-features\">\r\n                    <el-tag \r\n        size=\"mini\"\r\n                      :type=\"scope.row.is_num == 1 ? 'success' : 'info'\"\r\n                    >\r\n                      {{ scope.row.is_num == 1 ? '计次服务' : '不限次数' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              {{ formatDate(scope.row.create_time) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row)\" effect=\"dark\">\r\n              {{ getStatusText(scope.row) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button \r\n                type=\"text\" \r\n                size=\"small\" \r\n                @click=\"editData(scope.row.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                <i class=\"el-icon-edit\"></i>\r\n                编辑\r\n              </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                class=\"delete-btn\"\r\n            >\r\n                <i class=\"el-icon-delete\"></i>\r\n                删除\r\n            </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"600px\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-form-item label=\"类型名称\" prop=\"title\">\r\n          <el-input \r\n            v-model=\"ruleForm.title\" \r\n            placeholder=\"请输入服务类型名称\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"计次设置\">\r\n          <el-radio-group v-model=\"ruleForm.is_num\">\r\n            <el-radio :label=\"1\">计次服务</el-radio>\r\n            <el-radio :label=\"0\">不限次数</el-radio>\r\n          </el-radio-group>\r\n          <div class=\"form-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            计次服务将限制使用次数，不限次数则可无限使用\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"类型描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入服务类型的详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <div class=\"image-viewer\">\r\n        <el-image :src=\"show_image\" fit=\"contain\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      showAdvanced: false,\r\n      search: {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      },\r\n      loading: true,\r\n      url: \"/type/\",\r\n      title: \"服务类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      saveLoading: false,\r\n      selectedRows: [],\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写类型名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    activeTypes() {\r\n      return this.list.filter(item => item.title && item.title.trim() !== '').length;\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑服务类型' : '新增服务类型';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_num: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 新增方法\r\n    getStatusType(row) {\r\n      // 根据数据判断状态类型\r\n      if (row.title && row.title.trim() !== '') {\r\n        return 'success';\r\n      }\r\n      return 'info';\r\n    },\r\n    getStatusText(row) {\r\n      // 根据数据判断状态文本\r\n      if (row.title && row.title.trim() !== '') {\r\n        return '正常';\r\n      }\r\n      return '待完善';\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      };\r\n      this.showAdvanced = false;\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    toggleAdvanced() {\r\n      this.showAdvanced = !this.showAdvanced;\r\n    },\r\n    refreshData() {\r\n      this.getData();\r\n      this.$message.success('数据已刷新');\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n    exportData() {\r\n      this.$message.success('数据导出功能开发中...');\r\n    },\r\n    batchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请先选择要删除的数据');\r\n        return;\r\n      }\r\n      \r\n      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条数据吗？`, '批量删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里实现批量删除逻辑\r\n        this.$message.success('批量删除功能开发中...');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    applyAdvancedSearch() {\r\n      this.page = 1;\r\n      this.getData();\r\n      this.$message.success('高级筛选已应用');\r\n    },\r\n    clearAdvancedSearch() {\r\n      this.search.dateRange = [];\r\n      this.search.sortBy = \"create_time\";\r\n      this.search.sortOrder = \"desc\";\r\n      this.search.usageLevel = \"\";\r\n      this.search.features = [];\r\n      this.$message.info('高级筛选选项已清空');\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.service-type-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.usage-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.sort-select,\r\n.usage-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.sort-select .el-input__inner,\r\n.usage-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sort-select .el-input__inner:focus,\r\n.usage-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.sort-order {\r\n  width: 100%;\r\n}\r\n\r\n.sort-order .el-radio-button__inner {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sort-order .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-color: #667eea;\r\n  color: white;\r\n  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.feature-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n  width: 100%;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox {\r\n  margin: 0;\r\n  flex: 1;\r\n  min-width: 80px;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox__label {\r\n  font-size: 13px;\r\n  color: #495057;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox__input.is-checked .el-checkbox__inner {\r\n  background-color: #667eea;\r\n  border-color: #667eea;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 类型信息样式 */\r\n.type-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.type-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.type-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.type-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.type-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 6px;\r\n  line-height: 1.4;\r\n  max-height: 40px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.type-features {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  margin-top: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.form-tip i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .service-type-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-radio-group {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .type-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .type-title {\r\n    font-size: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}