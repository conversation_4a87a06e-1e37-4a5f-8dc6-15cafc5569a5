'use strict';

const Hoek = require('@hapi/hoek');

const Any = require('../any');
const Ref = require('../../ref');


const internals = {
    precisionRx: /(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/,
    normalizeExponent(str) {

        return str
            .replace(/\.?0+e/, 'e')
            .replace(/e\+/, 'e')
            .replace(/^\+/, '')
            .replace(/^(-?)0+([1-9])/, '$1$2');
    },
    normalizeDecimal(str) {

        str = str
            .replace(/^\+/, '')
            .replace(/\.0+$/, '')
            .replace(/^(-?)0+([1-9])/, '$1$2');

        if (str.includes('.') && str.endsWith('0')) {
            str = str.replace(/0+$/, '');
        }

        return str;
    }
};


internals.Number = class extends Any {

    constructor() {

        super();
        this._type = 'number';
        this._flags.unsafe = false;
        this._invalids.add(Infinity);
        this._invalids.add(-Infinity);
    }

    _base(value, state, options) {

        const result = {
            errors: null,
            value
        };

        if (typeof value === 'string' &&
            options.convert) {

            const matches = value.match(/^\s*[+-]?\d+(?:\.\d+)?(?:e([+-]?\d+))?\s*$/i);
            if (matches) {

                value = value.trim();
                result.value = parseFloat(value);

                if (!this._flags.unsafe) {
                    if (value.includes('e')) {
                        if (internals.normalizeExponent(`${result.value / Math.pow(10, matches[1])}e${matches[1]}`) !== internals.normalizeExponent(value)) {
                            result.errors = this.createError('number.unsafe', { value }, state, options);
                            return result;
                        }
                    }
                    else {
                        if (result.value.toString() !== internals.normalizeDecimal(value)) {
                            result.errors = this.createError('number.unsafe', { value }, state, options);
                            return result;
                        }
                    }
                }
            }
        }

        const isNumber = typeof result.value === 'number' && !isNaN(result.value);

        if (options.convert && 'precision' in this._flags && isNumber) {

            // This is conceptually equivalent to using toFixed but it should be much faster
            const precision = Math.pow(10, this._flags.precision);
            result.value = Math.round(result.value * precision) / precision;
        }

        if (isNumber) {
            if (!this._flags.unsafe &&
                (value > Number.MAX_SAFE_INTEGER || value < Number.MIN_SAFE_INTEGER)) {
                result.errors = this.createError('number.unsafe', { value }, state, options);
            }
        }
        else {
            result.errors = this.createError('number.base', { value }, state, options);
        }

        return result;
    }

    multiple(base) {

        const isRef = Ref.isRef(base);

        if (!isRef) {
            Hoek.assert(typeof base === 'number' && isFinite(base), 'multiple must be a number');
            Hoek.assert(base > 0, 'multiple must be greater than 0');
        }

        return this._test('multiple', base, function (value, state, options) {

            const divisor = isRef ? base(state.reference || state.parent, options) : base;

            if (isRef && (typeof divisor !== 'number' || !isFinite(divisor))) {
                return this.createError('number.ref', { ref: base.key }, state, options);
            }

            if (value % divisor === 0) {
                return value;
            }

            return this.createError('number.multiple', { multiple: base, value }, state, options);
        });
    }

    integer() {

        return this._test('integer', undefined, function (value, state, options) {

            return Math.trunc(value) - value === 0 ? value : this.createError('number.integer', { value }, state, options);
        });
    }

    unsafe(enabled = true) {

        Hoek.assert(typeof enabled === 'boolean', 'enabled must be a boolean');

        if (this._flags.unsafe === enabled) {
            return this;
        }

        const obj = this.clone();
        obj._flags.unsafe = enabled;
        return obj;
    }

    negative() {

        return this._test('negative', undefined, function (value, state, options) {

            if (value < 0) {
                return value;
            }

            return this.createError('number.negative', { value }, state, options);
        });
    }

    positive() {

        return this._test('positive', undefined, function (value, state, options) {

            if (value > 0) {
                return value;
            }

            return this.createError('number.positive', { value }, state, options);
        });
    }

    precision(limit) {

        Hoek.assert(Number.isSafeInteger(limit), 'limit must be an integer');
        Hoek.assert(!('precision' in this._flags), 'precision already set');

        const obj = this._test('precision', limit, function (value, state, options) {

            const places = value.toString().match(internals.precisionRx);
            const decimals = Math.max((places[1] ? places[1].length : 0) - (places[2] ? parseInt(places[2], 10) : 0), 0);
            if (decimals <= limit) {
                return value;
            }

            return this.createError('number.precision', { limit, value }, state, options);
        });

        obj._flags.precision = limit;
        return obj;
    }

    port() {

        return this._test('port', undefined, function (value, state, options) {

            if (!Number.isSafeInteger(value) || value < 0 || value > 65535) {
                return this.createError('number.port', { value }, state, options);
            }

            return value;
        });
    }

};


internals.compare = function (type, compare) {

    return function (limit) {

        const isRef = Ref.isRef(limit);
        const isNumber = typeof limit === 'number' && !isNaN(limit);

        Hoek.assert(isNumber || isRef, 'limit must be a number or reference');

        return this._test(type, limit, function (value, state, options) {

            let compareTo;
            if (isRef) {
                compareTo = limit(state.reference || state.parent, options);

                if (!(typeof compareTo === 'number' && !isNaN(compareTo))) {
                    return this.createError('number.ref', { ref: limit.key }, state, options);
                }
            }
            else {
                compareTo = limit;
            }

            if (compare(value, compareTo)) {
                return value;
            }

            return this.createError('number.' + type, { limit: compareTo, value }, state, options);
        });
    };
};


internals.Number.prototype.min = internals.compare('min', (value, limit) => value >= limit);
internals.Number.prototype.max = internals.compare('max', (value, limit) => value <= limit);
internals.Number.prototype.greater = internals.compare('greater', (value, limit) => value > limit);
internals.Number.prototype.less = internals.compare('less', (value, limit) => value < limit);


module.exports = new internals.Number();
