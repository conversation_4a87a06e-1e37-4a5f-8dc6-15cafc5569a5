
7006165724b626c36c9131d6b4c93491e4ce29ba	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v18.6.0\",\"terser-webpack-plugin\":\"1.4.5\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":true,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"b706f6e7730e0c65d6ea4711bbeb9d7a\"}","integrity":"sha512-/UWEApJkrHg5n0RKsphLETpfD2LiLCTPkKjFNBuZjrC72t9E7TvfoshAdzLLNzPuSHVbV9FVEIwpmKI4BbX7rQ==","time":1749543473601,"size":18176}