<?php return array(
    'root' => array(
        'name' => 'topthink/think',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '42fcdf9095ba6b38d7a46c9b921c500c92215fa4',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'adbario/php-dot-notation' => array(
            'pretty_version' => '2.5.0',
            'version' => '2.5.0.0',
            'reference' => '081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../adbario/php-dot-notation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alibabacloud/client' => array(
            'pretty_version' => '1.5.32',
            'version' => '1.5.32.0',
            'reference' => '5bc6f6d660797dcee2c3aef29700ab41ee764f4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alibabacloud/live' => array(
            'pretty_version' => '1.8.958',
            'version' => '1.8.958.0',
            'reference' => '2dc756e9e156cb33bc1287d28fc3fade87e4ae60',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/live',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aliyuncs/oss-sdk-php' => array(
            'pretty_version' => 'v2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'ce5d34dae9868237a32248788ea175c7e9da14b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aliyuncs/oss-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clagiordano/weblibs-configmanager' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'reference' => '8802c7396d61a923c9a73e37ead062b24bb1b273',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clagiordano/weblibs-configmanager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v5.5.1',
            'version' => '5.5.1.0',
            'reference' => '83b609028194aa042ea33b5af2d41a7427de80e6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.1',
            'version' => '7.8.1.0',
            'reference' => '41042bc7ab002487b876a0683fc8dce04ddce104',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'bbff78d96034045e58e13dedd6ad91b5d1253223',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'reference' => '45b30f99ac27b5ca93cb4831afe16285f57b8221',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'c3131244e29c08d44fefb49e0dd35021e9e39dd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '44bb1ab01811116f01fe216ab37d921dccc6c10d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'bbb69a935c2cbb0c03d7f481a238027430f6440b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.7.7',
            'version' => '1.7.7.0',
            'reference' => 'd178027d1e679832db9f38248fcc7200647dc2b7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.12.0',
            'version' => '1.12.0.0',
            'reference' => 'f79611d6dc1f6b7e8e30b738fc371b392001dbfd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.12.0',
            'version' => '7.12.0.0',
            'reference' => '96971af3cc6151b32e4a9d61001e126624100538',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v2.8.52',
            'version' => '2.8.52.0',
            'reference' => '02c1859112aa779d9ab394ae4f3381911d84052b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v5.1.42',
            'version' => '5.1.42.0',
            'reference' => 'ecf1a90d397d821ce2df58f7d47e798c17eba3ad',
            'type' => 'think-framework',
            'install_path' => __DIR__ . '/../../thinkphp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '42fcdf9095ba6b38d7a46c9b921c500c92215fa4',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-captcha' => array(
            'pretty_version' => 'v2.0.2',
            'version' => '2.0.2.0',
            'reference' => '54c8a51552f99ff9ea89ea9c272383a8f738ceee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-installer' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'reference' => '38ba647706e35d6704b5d370c06f8a160b635f88',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../topthink/think-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-worker' => array(
            'pretty_version' => 'v2.0.12',
            'version' => '2.0.12.0',
            'reference' => '922d8c95e2f095e0da66d18b9e3fbbfd8de70a3f',
            'type' => 'think-extend',
            'install_path' => __DIR__ . '/../topthink/think-worker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/gateway-worker' => array(
            'pretty_version' => 'v3.0.22',
            'version' => '3.0.22.0',
            'reference' => 'a615036c482d11f68b693998575e804752ef9068',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/gateway-worker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/workerman' => array(
            'pretty_version' => 'v3.5.35',
            'version' => '3.5.35.0',
            'reference' => '3cc0adae51ba36db38b11e7996c64250d356dbe7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/workerman',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yuanhang/easy-upload' => array(
            'pretty_version' => 'v1.1.7',
            'version' => '1.1.7.0',
            'reference' => 'e051169ffd35f2021f257599beb6744c1e9ce3ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yuanhang/easy-upload',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
