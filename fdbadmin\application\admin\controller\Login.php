<?php
namespace app\admin\controller;
use think\Request;
use untils\{JsonService,JwtAuth};
use models\{Admins,Yuangongs};
use think\captcha\Captcha;
class Login{
    
    public function verifyCode(){
        $config =    [
        // 验证码字体大小
        'fontSize'    =>    30,    
        // 验证码位数
        'length'      =>    3,   
        // 关闭验证码杂点
        'useNoise'    =>    false, 
        ];
        $captcha = new Captcha($config);
        return $captcha->entry();    
    }
    public function pwd(){
         $rand = 4455;
         $pwd = md5(md5("admin").$rand);
         echo $pwd;
    }

    public function changePwd(Admins $model,Request $request){
        $post=$request->post();
        if($post['old_password']==$post['password']){
            return JsonService::fail('原始密码不能和修改密码一样');
        }
        $info =$model->find(1);
        if(empty($info)) return JsonService::fail('请登录');
        $rand = rand(1000,9999);
        $info->password =  md5(md5($post['password']).$rand);
        $info->rand = $rand;
        $res = $info->save();
        if(empty($res)) return JsonService::fail('修改失败');
        else return JsonService::successful('修改成功');
    }
    public function doLogin(Request $request,Admins $model){
        $post = $request->post();
        if( !captcha_check($post['code'])){
            return JsonService::fail('验证码错误');
        }
        if($post['username']=='admin'){
            $info = $model->where(['username'=>$post['username']])->find();
            if(empty($info)) return JsonService::fail('账号不存在');
            $rand = $info->rand;
            $pwd = md5(md5($post['password']).$rand);
            if($pwd!=$info->password)  return JsonService::fail('账号/密码不正确');
            $token = JwtAuth::getToken(['admin_id'=>$info->id,'type'=>1]);
            $title = $info->title;
            $quanxian = 'all';
              $spbs = $info->id;
            return JsonService::successful('登录成功',compact('token','title','quanxian','spbs'));
        }else{
              $yuangong = new Yuangongs();
              $info = $yuangong->where(['account'=>$post['username']])->find();
              if(empty($info)) return JsonService::fail('账号不存在');
              $pwd = md5(md5($post['password']).'fdb');
              if($pwd!=$info->password)  return JsonService::fail('账号/密码不正确');
              $token = JwtAuth::getToken(['admin_id'=>$info->id,'type'=>2]);
              $title = $info->title;
              $spbs = $info->id;
              $quanxian = 'xianzhi';
              return JsonService::successful('登录成功',compact('token','title','quanxian','spbs'));
        }

        
    }
    
    public function checkToken(Request $request){
        $token = $request->post('token');
        if(empty($token)) return JsonService::fail('请登录');
        $res = JwtAuth::getInfoByToken($token);
        if(empty($res)) return JsonService::fail('请登录');
        if(empty($res->admin_id)) return JsonService::fail('请登录');
        return JsonService::successful("验证通过");
    }
    
}