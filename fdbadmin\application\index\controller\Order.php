<?php
namespace app\index\controller;
use think\Request;
use untils\{JsonService,WechatPay};
use models\{Orders,Kechengs,Vips,Users,Zhubos};
use think\Controller;
use think\facade\View;

class Order extends Controller{
    public function makeHuifangOrder(
        Orders $model,
	    Zhubos $Kechengs,
	    Users $Users,
	    Request $request){
		$id = $request->post('id');
		$uid = $request->post('uid');
		if(empty($id)){
			return JsonService::fail('未接受到任何参数');
		}
		if(empty($uid)){
			return JsonService::fail('请重新进入');
		}
		$form['type']=4;
		$form['type_id']=$id;
		$info =$Kechengs->find($id);
		if(empty($info)){
			return JsonService::fail('回放已下架');
		}
		
		
		$form['uid']=$uid;
		$openid = $Users::where(['id'=>$uid])->value('openid');
	
		if(empty($openid)){
			return JsonService::fail('请重新进入');
		}
		$orderInfo = $model->where($form)->where(['is_pay'=>2])->find();
		if(!empty($orderInfo)){
			return JsonService::fail('已购买该课程,请下拉刷新一下,即可观看');
		}
		$form['order_sn']="HF".get_order_num();
		$form['total_price']=$info['price'];
		$form['is_pay']=1;
		$model::beginTrans();
		try {
			$model->saveData($form);
			$result=$this->pay($form['total_price'],$form['order_sn'],$openid);
			if($result['result_code']=="FAIL"){
			    $model::rollbackTrans();
			    return JsonService::fail($result['err_code'].':'.$result['err_code_des']);
            }
			$model::commitTrans();

			return JsonService::successful('获取成功',['pay_info'=>$result]);
		} catch (\Exception $e) {
			$model::rollbackTrans();
			return JsonService::fail('系统发生错误:'.$e->getMessage());
		}
    }
	public function makeKeChengOrder(
	    Orders $model,
	    Kechengs $Kechengs,
	    Users $Users,
	    Request $request){
		$id = $request->post('id');
		$uid = $request->post('uid');
		if(empty($id)){
			return JsonService::fail('未接受到任何参数');
		}
		if(empty($uid)){
			return JsonService::fail('请重新进入');
		}
		$form['type']=3;
		$form['type_id']=$id;
		$info =$Kechengs->find($id);
		if(empty($info)){
			return JsonService::fail('课程已下架');
		}
		
		
		$form['uid']=$uid;
		$openid = $Users::where(['id'=>$uid])->value('openid');
	
		if(empty($openid)){
			return JsonService::fail('请重新进入');
		}
		$orderInfo = $model->where($form)->where(['is_pay'=>2])->find();
		if(!empty($orderInfo)){
			return JsonService::fail('已购买该课程,请下拉刷新一下,即可观看');
		}
		$form['order_sn']="KC".get_order_num();
		$form['total_price']=$info['price'];
		$form['is_pay']=1;
		$model::beginTrans();
		try {
			$model->saveData($form);
			$result=$this->pay($form['total_price'],$form['order_sn'],$openid);
			if($result['result_code']=="FAIL"){
			    $model::rollbackTrans();
			    return JsonService::fail($result['err_code'].':'.$result['err_code_des']);
            }
			$model::commitTrans();
			return JsonService::successful('获取成功',['pay_info'=>$result]);
		} catch (\Exception $e) {
			$model::rollbackTrans();
			return JsonService::fail('系统发生错误:'.$e->getMessage());
		}

	}
	public function notify(){
	    $wechatPay = new WechatPay();
	    $result = $wechatPay->_notify();
	    
	    if(!empty($result)){
	        $model = new Orders();
	        $info =$model
			->where(['order_sn'=>$result['out_trade_no']])
			->find();
			$info->is_pay=2;
			$info->pay_time=now();
			$info->save();
	    }
	}
    public function pay($price,$order_sn,$openid){
       
    	//$notify_url = 'http://tiaojie.oecgd.com/index/order/notify';
		$notify_url = 'https://web.faduobang.com/index/order/notify';
		$wechatPay = new WechatPay();
		$time = time();
		settype($time, "string");
		$order=[
		    'body'=>'测试支付',
		 //   'total_fee'=>0.1*100,
			'total_fee'=> $price*100,
		    'out_trade_no'=>$order_sn,
		    'trade_type'=>'JSAPI',
		    'notify_url'=>$notify_url,
		    'openid'=>$openid
		];
	    
		$result = $wechatPay->_unifiedOrder($order);
	  
		if($result['result_code']=="FAIL"){
			return $result; 
		}

		$data  = [
		        'appId'=>$result['appid'],
			    'timeStamp'=>$time,
			    'nonceStr'=>$result['nonce_str'],
			    'package'=>"prepay_id=".$result['prepay_id'],
			    'signType'=>"MD5",
			    
		];
	
		$data['paySign']=$wechatPay->_makeSign($data);
		$data['result_code']="SUCCESS";
		return $data;
    }
	public function makeVipOrder(Orders $model,Vips $Vips ,Users $Users,Request $request){
		$id = $request->post('id');
		$uid = $request->post('uid');
		if(empty($id)){
			return JsonService::fail('未接受到任何参数');
		}
		if(empty($uid)){
			return JsonService::fail('请重新进入');
		}
		$form['type']=1;
		$form['type_id']=$id;
		$info =$Vips->find($id);
		if(empty($info)){
			return JsonService::fail('vip已下架');
		}
		
		
		$form['uid']=$uid;
		$openid = $Users::where(['id'=>$uid])->value('openid');
	
		if(empty($openid)){
			return JsonService::fail('请重新进入');
		}
		$orderInfo = $model->where($form)->where(['is_pay'=>2])->find();
		if(!empty($orderInfo)){
			return JsonService::fail('已购买vip,请下拉刷新一下');
		}
		$form['order_sn']="HY".get_order_num();
		$form['total_price']=$info['price'];
		$form['is_pay']=1;
		$model::beginTrans();
		try {
			$model->saveData($form);
			$result=$this->pay($form['total_price'],$form['order_sn'],$openid);
			
			if($result['result_code']=="FAIL"){
			    $model::rollbackTrans();
			    return JsonService::fail($result['err_code'].':'.$result['err_code_des']);
            }
            
			$model::commitTrans();
			return JsonService::successful('获取成功',['pay_info'=>$result]);
		} catch (\Exception $e) {
			$model::rollbackTrans();
			return JsonService::fail('系统发生错误:'.$e->getMessage());
		}
	}
}