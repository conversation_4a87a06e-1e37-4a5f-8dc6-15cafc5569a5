{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\yonghu\\user.vue?vue&type=style&index=0&id=300291bf&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1732626900102}, {"path": "H:\\fdbfront\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748278548153}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748278552176}, {"path": "H:\\fdbfront\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748278549571}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wYWdlLXRvcCB7CiAgICBtYXJnaW4tdG9wOiAxNXB4Owp9CgouZWxfaW5wdXQgewogICAgd2lkdGg6IDQ3NXB4Owp9Cg=="}, {"version": 3, "sources": ["user.vue"], "names": [], "mappings": ";AAoiCA;AACA;AACA;;AAEA;AACA;AACA", "file": "user.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n    <div>\r\n        <el-card shadow=\"always\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n                <span>{{ this.$router.currentRoute.name }}</span>\r\n                <el-button\r\n                        style=\"float: right; padding: 3px 0\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                >刷新\r\n                </el-button\r\n                >\r\n            </div>\r\n            <el-row style=\"width: 600px\">\r\n                <el-input placeholder=\"请输入名称/手机号/公司名称\" v-model=\"search.keyword\" size=\"mini\">\r\n                    <el-button\r\n                            slot=\"append\"\r\n                            icon=\"el-icon-search\"\r\n                            @click=\"searchData()\"\r\n                    ></el-button>\r\n                </el-input>\r\n                <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">\r\n                    导出列表\r\n                </el-button>\r\n                <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                           @click=\"openUpload\">导入用户\r\n                </el-button>\r\n                <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" @click=\"addUser\">添加用户</el-button>\r\n                <a href=\"/import_templete/user.xls\"\r\n                   style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n\r\n            </el-row>\r\n\r\n            <el-table\r\n                    :data=\"list\"\r\n                    style=\"width: 100%; margin-top: 10px\"\r\n                    v-loading=\"loading\"\r\n                    size=\"mini\"\r\n                    @sort-change=\"handleSortChange\"\r\n            >\r\n                <el-table-column prop=\"phone\" label=\"注册手机号码\"></el-table-column>\r\n                <el-table-column prop=\"company\" label=\"公司名称\" sortable></el-table-column>\r\n                <el-table-column prop=\"nickname\" label=\"名称\" sortable></el-table-column>\r\n                <el-table-column prop=\"\" label=\"头像\">\r\n                    <template slot-scope=\"scope\">\r\n                        <div>\r\n\r\n                            <el-row v-if=\"scope.row.headimg==''\">\r\n                                <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                            </el-row>\r\n                            <el-row v-else>\r\n                                <img style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                            </el-row>\r\n\r\n                        </div>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"linkman\" label=\"联系人\" sortable></el-table-column>\r\n                <el-table-column prop=\"linkphone\" label=\"联系号码\" sortable></el-table-column>\r\n                <el-table-column prop=\"yuangong_id\" label=\"用户来源\"></el-table-column>\r\n                <el-table-column prop=\"end_time\" label=\"到期时间\"></el-table-column>\r\n                <!-- <el-table-column prop=\"headimg\" label=\"头像\">\r\n                  <template slot-scope=\"scope\">\r\n                    <img\r\n                      :src=\"scope.row.headimg\"\r\n                      style=\"width: 50px; height: 50px\"\r\n                      @click=\"showImage(scope.row.headimg)\"\r\n                    />\r\n                  </template>\r\n                </el-table-column> -->\r\n                <el-table-column prop=\"create_time\" label=\"录入时间\" sortable></el-table-column>\r\n                <el-table-column fixed=\"right\" label=\"操作\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" size=\"small\" @click=\"viewData(scope.row.id)\"\r\n                        >查看详情\r\n                        </el-button\r\n                        >\r\n                        <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n                        >编辑资料\r\n                        </el-button\r\n                        >\r\n                        <el-button type=\"text\" size=\"small\" @click=\"order(scope.row)\"\r\n                        >制作订单\r\n                        </el-button\r\n                        >\r\n                        <el-button v-if=\"is_del\"\r\n                                   @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                                   type=\"text\"\r\n                                   size=\"small\"\r\n                        >\r\n                            移除\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"page-top\">\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n                        :page-size=\"size\"\r\n                        layout=\"total, sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </el-card>\r\n        <el-dialog\r\n                :title=\"title + '内容'\"\r\n                :visible.sync=\"dialogFormVisible\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        ruleForm.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        ruleForm.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        ruleForm.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        ruleForm.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(info.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"信息编辑\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                search: {\r\n                    keyword: \"\",\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n            };\r\n        },\r\n        mounted() {\r\n            this.getData();\r\n        },\r\n        methods: {\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                }\r\n\r\n                _this.dialogViewUserDetail = true;\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n\r\n                _this.dialogFormVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            getInfo(id) {\r\n                let _this = this;\r\n                _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n                    if (resp) {\r\n                        resp.data.tiaojie_id = resp.data.tiaojie_id == 0 ? '' : resp.data.tiaojie_id;\r\n                        resp.data.fawu_id = resp.data.fawu_id == 0 ? '' : resp.data.fawu_id;\r\n                        resp.data.lian_id = resp.data.lian_id == 0 ? '' : resp.data.lian_id;\r\n                        resp.data.ywy_id = resp.data.ywy_id == 0 ? '' : resp.data.ywy_id;\r\n                        resp.data.htsczy_id = resp.data.htsczy_id == 0 ? '' : resp.data.htsczy_id;\r\n                        resp.data.ls_id = resp.data.ls_id == 0 ? '' : resp.data.ls_id;\r\n                        _this.ruleForm = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            delData(index, id) {\r\n                this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\",\r\n                })\r\n                    .then(() => {\r\n                        this.postRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                this.$message({\r\n                                    type: \"success\",\r\n                                    message: \"删除成功!\",\r\n                                });\r\n                                this.list.splice(index, 1);\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {\r\n                        this.$message({\r\n                            type: \"error\",\r\n                            message: \"取消删除!\",\r\n                        });\r\n                    });\r\n            },\r\n            refulsh() {\r\n                this.$router.go(0);\r\n            },\r\n            searchData() {\r\n                this.page = 1;\r\n                this.size = 20;\r\n                this.getData();\r\n            },\r\n\r\n            getData() {\r\n                let _this = this;\r\n\r\n                _this.loading = true;\r\n                _this\r\n                    .postRequest(\r\n                        _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n                        _this.search\r\n                    )\r\n                    .then((resp) => {\r\n                        if (resp.code == 200) {\r\n                            _this.list = resp.data;\r\n                            _this.total = resp.count;\r\n\r\n                            if (resp.msg == '超级管理员') {\r\n                                _this.is_del = true;\r\n                            }\r\n                        }\r\n                        _this.loading = false;\r\n                    });\r\n            },\r\n            saveData() {\r\n                let _this = this;\r\n                console.log(this.ruleForm);\r\n                this.$refs[\"ruleForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                this.getData();\r\n                                _this.dialogFormVisible = false;\r\n                                _this.dialogAddUser = false;\r\n                            } else {\r\n                                _this.$message({\r\n                                    type: \"error\",\r\n                                    message: resp.msg,\r\n                                });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleSizeChange(val) {\r\n                this.size = val;\r\n\r\n                this.getData();\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                this.getData();\r\n            },\r\n            handleSuccess(res) {\r\n                this.ruleForm.pic_path = res.data.url;\r\n            },\r\n\r\n            showImage(file) {\r\n                this.show_image = file;\r\n                this.dialogVisible = true;\r\n            },\r\n            beforeUpload(file) {\r\n                const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n                if (!isTypeTrue) {\r\n                    this.$message.error(\"上传图片格式不对!\");\r\n                    return;\r\n                }\r\n            },\r\n            delImage(file, fileName) {\r\n                let _this = this;\r\n                _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.ruleForm[fileName] = \"\";\r\n\r\n                        _this.$message.success(\"删除成功!\");\r\n                    } else {\r\n                        _this.$message.error(resp.msg);\r\n                    }\r\n                });\r\n            },\r\n            handleSortChange({column, prop, order}) {\r\n                this.search.prop = prop;\r\n                this.search.order = order;\r\n                this.getData();\r\n                // 根据 column, prop, order 来更新你的数据排序\r\n                // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n            },\r\n            exports: function () { //导出表格\r\n                let _this = this;\r\n                location.href = \"/admin/user/export2?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n                // _this.postRequest(\r\n                //                 _this.url + \"export\",\r\n                //                 _this.search\r\n                //         )\r\n                //         .then((resp) => {\r\n                //           if (resp.code == 200) {\r\n                //\r\n                //           }\r\n                //         });\r\n            },\r\n            closeUploadDialog() { //关闭窗口\r\n                this.uploadVisible = false;\r\n                this.$refs.upload.clearFiles();\r\n                this.uploadData.review = false;\r\n            },\r\n            uploadSuccess(response) { //导入完成回调\r\n                if (response.code === 200) {\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: response.msg\r\n                    });\r\n                    this.uploadVisible = false;\r\n                    this.getData();\r\n                    console.log(response);\r\n                } else {\r\n                    this.$message({\r\n                        type: 'warning',\r\n                        message: response.msg\r\n                    });\r\n                }\r\n\r\n                this.submitOrderLoading2 = false;\r\n                this.$refs.upload.clearFiles();\r\n            },\r\n            checkFile(file) { //导入前校验文件后缀\r\n                let fileType = ['xls', 'xlsx'];\r\n                let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n                if (!fileType.includes(type)) {\r\n                    this.$message({\r\n                        type: \"warning\",\r\n                        message: \"文件格式错误仅支持 xls xlxs 文件\"\r\n                    });\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            submitUpload() { //导入提交\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            closeDialog() { //关闭窗口\r\n                this.addVisible = false;\r\n                this.uploadVisible = false;\r\n                this.form = {\r\n                    id: '',\r\n                    nickname: \"\",\r\n                    mobile: \"\",\r\n                    school_id: 0,\r\n                    grade_id: '',\r\n                    class_id: '',\r\n                    sex: '',\r\n                    is_poor: '',\r\n                    is_display: '',\r\n                    number: '',\r\n                    remark: '',\r\n                    is_remark_option: 0,\r\n                    remark_option: [],\r\n                    mobile_checked: false,\r\n                };\r\n                this.$refs.form.resetFields();\r\n            },\r\n            openUpload() { //打开导入弹窗\r\n                this.uploadVisible = true;\r\n            },\r\n            addUser() {\r\n                this.dialogAddUser = true;\r\n                this.ruleForm = {};\r\n                this.getYuangongs();\r\n            }\r\n\r\n        },\r\n    };\r\n</script>\r\n<style scoped>\r\n    .page-top {\r\n        margin-top: 15px;\r\n    }\r\n\r\n    .el_input {\r\n        width: 475px;\r\n    }\r\n</style>\r\n"]}]}