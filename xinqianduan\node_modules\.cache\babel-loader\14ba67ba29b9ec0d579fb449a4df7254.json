{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\data\\configs.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\data\\configs.vue", "mtime": 1748489112808}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "ruleForm", "site_name", "company_name", "site_tel", "email", "site_address", "site_icp", "site_icp_url", "site_logo", "lvshi", "my_title", "my_desc", "about_path", "yinsi", "yinsi_text", "index_about_content", "about_text", "index_team_content", "team_text", "activeName", "url", "fullscreenLoading", "show_image", "dialogVisible", "previewDialogVisible", "aboutPreviewDialogVisible", "teamPreviewDialogVisible", "filedName", "isClear", "privacyEditMode", "aboutEditMode", "teamEditMode", "privacyTemplate", "id", "title", "computed", "privacyWordCount", "text", "replace", "length", "aboutWordCount", "teamWordCount", "mounted", "console", "log", "methods", "getList", "changeFiled", "fileName", "change", "getAllData", "handleSuccess", "res", "$message", "success", "beforeUpload", "file", "isTypeTrue", "test", "type", "error", "info", "delImage", "showImage", "handleClick", "useTemplate", "$confirm", "confirmButtonText", "cancelButtonText", "then", "template", "Date", "toLocaleDateString", "previewPrivacy", "resetPrivacy", "onPrivacyChange", "val", "onPrivacyTextChange", "insertText", "insertValue", "useAboutTemplate", "previewAbout", "resetAbout", "onAboutChange", "onAboutTextChange", "insertAboutText", "useTeamTemplate", "previewTeam", "resetTeam", "onTeamChange", "onTeamTextChange", "insertTeamText", "saveData", "_this", "setTimeout", "message"], "sources": ["src/views/pages/data/configs.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-title\">\r\n        基础设置\r\n      </div>\r\n\r\n      <!-- 标签页导航 -->\r\n      <div class=\"tab-container\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <div class=\"form-container\">\r\n              <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\">\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"网站名称\">\r\n                      <el-input v-model=\"ruleForm.site_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\">\r\n                      <el-input v-model=\"ruleForm.company_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系方式\">\r\n                      <el-input v-model=\"ruleForm.site_tel\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"邮箱\">\r\n                      <el-input v-model=\"ruleForm.email\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"ruleForm.site_address\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案号\">\r\n                      <el-input v-model=\"ruleForm.site_icp\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案链接\">\r\n                      <el-input v-model=\"ruleForm.site_icp_url\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-form-item label=\"网站Logo\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.site_logo\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传Logo图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('site_logo')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"showImage(ruleForm.site_logo)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"推广律师\">\r\n                  <el-select\r\n                    v-model=\"ruleForm.lvshi\"\r\n                    placeholder=\"请选择推广律师\"\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                      v-for=\"(item, index) in lvshi\"\r\n                      :key=\"index\"\r\n                      :label=\"item.title\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广标题\">\r\n                      <el-input v-model=\"ruleForm.my_title\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广语\">\r\n                      <el-input v-model=\"ruleForm.my_desc\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"推广图片\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.about_path\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传推广图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('about_path')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"showImage(ruleForm.about_path)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <div class=\"privacy-container\">\r\n              <!-- 隐私条款工具栏 -->\r\n              <div class=\"privacy-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewPrivacy\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetPrivacy\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ privacyWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"privacyEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"privacyEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.yinsi\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onPrivacyChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.yinsi_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入隐私条款内容...\"\r\n                  @input=\"onPrivacyTextChange\"\r\n                  class=\"privacy-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('邮箱地址')\"\r\n                  >\r\n                    邮箱地址\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('生效日期')\"\r\n                  >\r\n                    生效日期\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <div class=\"about-container\">\r\n              <!-- 关于我们工具栏 -->\r\n              <div class=\"about-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useAboutTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewAbout\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetAbout\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ aboutWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"aboutEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"aboutEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_about_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onAboutChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.about_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入关于我们的内容...\"\r\n                  @input=\"onAboutTextChange\"\r\n                  class=\"about-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('成立时间')\"\r\n                  >\r\n                    成立时间\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司地址')\"\r\n                  >\r\n                    公司地址\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <div class=\"team-container\">\r\n              <!-- 团队介绍工具栏 -->\r\n              <div class=\"team-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTeamTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewTeam\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetTeam\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ teamWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"teamEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"teamEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_team_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onTeamChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.team_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入团队介绍内容...\"\r\n                  @input=\"onTeamTextChange\"\r\n                  class=\"team-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('团队规模')\"\r\n                  >\r\n                    团队规模\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('专业领域')\"\r\n                  >\r\n                    专业领域\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('服务理念')\"\r\n                  >\r\n                    服务理念\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n\r\n      <!-- 提交按钮 -->\r\n      <div class=\"submit-container\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"medium\"\r\n          @click=\"saveData\"\r\n          :loading=\"fullscreenLoading\"\r\n          >保存设置\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 隐私条款预览对话框 -->\r\n    <el-dialog\r\n      title=\"隐私条款预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"privacy-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.yinsi\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"previewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 关于我们预览对话框 -->\r\n    <el-dialog\r\n      title=\"关于我们预览\"\r\n      :visible.sync=\"aboutPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"about-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_about_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"aboutPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"aboutPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 团队介绍预览对话框 -->\r\n    <el-dialog\r\n      title=\"团队介绍预览\"\r\n      :visible.sync=\"teamPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"team-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_team_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"teamPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"teamPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>2024年1月1日</p>\r\n<p><strong>最后更新：</strong>2024年1月1日</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供法律服务</li>\r\n<li>改善我们的服务质量</li>\r\n<li>与您沟通服务相关事宜</li>\r\n</ul>\r\n\r\n<h3>3. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。</p>\r\n\r\n<h3>4. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        yinsi_text: \"\",\r\n        index_about_content: `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：北京市朝阳区示例大厦<br>\r\n电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        about_text: \"\",\r\n        index_team_content: `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由20余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 3名</li>\r\n<li>合伙人律师 5名</li>\r\n<li>资深律师 8名</li>\r\n<li>执业律师 6名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过10年</li>\r\n<li>成功处理案件超过1000起</li>\r\n<li>客户满意度达到98%以上</li>\r\n<li>多名律师获得行业荣誉</li>\r\n</ul>`,\r\n        team_text: \"\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewDialogVisible: false,\r\n      aboutPreviewDialogVisible: false,\r\n      teamPreviewDialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      privacyEditMode: \"rich\", // 隐私条款编辑模式\r\n      aboutEditMode: \"rich\", // 关于我们编辑模式\r\n      teamEditMode: \"rich\", // 团队介绍编辑模式\r\n      privacyTemplate: \"\", // 隐私条款模板\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 隐私条款字数统计\r\n    privacyWordCount() {\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 去除HTML标签后计算字数\r\n        const text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.yinsi_text.length;\r\n      }\r\n    },\r\n    // 关于我们字数统计\r\n    aboutWordCount() {\r\n      if (this.aboutEditMode === 'rich') {\r\n        const text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.about_text.length;\r\n      }\r\n    },\r\n    // 团队介绍字数统计\r\n    teamWordCount() {\r\n      if (this.teamEditMode === 'rich') {\r\n        const text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.team_text.length;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n    // 初始化纯文本内容\r\n    this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    // 隐私条款相关方法\r\n    useTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>${new Date().toLocaleDateString()}</p>\r\n<p><strong>最后更新：</strong>${new Date().toLocaleDateString()}</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n<li>设备信息和使用数据</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供和改善法律服务</li>\r\n<li>处理您的请求和查询</li>\r\n<li>发送服务相关通知</li>\r\n<li>遵守法律义务</li>\r\n</ul>\r\n\r\n<h3>3. 信息共享</h3>\r\n<p>我们不会向第三方出售、交易或转让您的个人信息，除非：</p>\r\n<ul>\r\n<li>获得您的明确同意</li>\r\n<li>法律要求或政府部门要求</li>\r\n<li>为保护我们的权利和安全</li>\r\n</ul>\r\n\r\n<h3>4. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息：</p>\r\n<ul>\r\n<li>数据加密传输和存储</li>\r\n<li>访问权限控制</li>\r\n<li>定期安全审计</li>\r\n<li>员工隐私培训</li>\r\n</ul>\r\n\r\n<h3>5. 您的权利</h3>\r\n<p>您有权：</p>\r\n<ul>\r\n<li>访问和更新您的个人信息</li>\r\n<li>删除您的个人信息</li>\r\n<li>限制信息处理</li>\r\n<li>数据可携带性</li>\r\n</ul>\r\n\r\n<h3>6. Cookie使用</h3>\r\n<p>我们使用Cookie来改善用户体验，您可以通过浏览器设置管理Cookie偏好。</p>\r\n\r\n<h3>7. 政策更新</h3>\r\n<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。</p>\r\n\r\n<h3>8. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>公司名称：${this.ruleForm.company_name}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}<br>\r\n地址：${this.ruleForm.site_address}</p>`;\r\n\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = template;\r\n        } else {\r\n          this.ruleForm.yinsi_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewPrivacy() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n\r\n    resetPrivacy() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = '';\r\n        } else {\r\n          this.ruleForm.yinsi_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onPrivacyChange(val) {\r\n      this.ruleForm.yinsi = val;\r\n      // 同步更新纯文本版本\r\n      this.ruleForm.yinsi_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onPrivacyTextChange(val) {\r\n      this.ruleForm.yinsi_text = val;\r\n      // 同步更新富文本版本（简单的换行转换）\r\n      this.ruleForm.yinsi = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '邮箱地址':\r\n          insertValue = this.ruleForm.email || '[邮箱地址]';\r\n          break;\r\n        case '生效日期':\r\n          insertValue = new Date().toLocaleDateString();\r\n          break;\r\n      }\r\n\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 富文本模式下插入\r\n        this.ruleForm.yinsi += insertValue;\r\n      } else {\r\n        // 纯文本模式下插入\r\n        this.ruleForm.yinsi_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 关于我们相关方法\r\n    useAboutTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>${this.ruleForm.company_name}成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n<li>刑事辩护代理</li>\r\n<li>房地产法务</li>\r\n<li>金融法务</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n<li><strong>全程跟踪：</strong>专人负责，全程跟踪服务</li>\r\n</ul>\r\n\r\n<h3>服务承诺</h3>\r\n<p>我们承诺为每一位客户提供：</p>\r\n<ul>\r\n<li>专业的法律咨询服务</li>\r\n<li>及时的案件进展反馈</li>\r\n<li>透明的收费标准</li>\r\n<li>保密的客户信息</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：${this.ruleForm.site_address}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}</p>`;\r\n\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = template;\r\n        } else {\r\n          this.ruleForm.about_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewAbout() {\r\n      this.aboutPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetAbout() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = '';\r\n        } else {\r\n          this.ruleForm.about_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onAboutChange(val) {\r\n      this.ruleForm.index_about_content = val;\r\n      this.ruleForm.about_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onAboutTextChange(val) {\r\n      this.ruleForm.about_text = val;\r\n      this.ruleForm.index_about_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertAboutText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '成立时间':\r\n          insertValue = '2020年';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '公司地址':\r\n          insertValue = this.ruleForm.site_address || '[公司地址]';\r\n          break;\r\n      }\r\n\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.index_about_content += insertValue;\r\n      } else {\r\n        this.ruleForm.about_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 团队介绍相关方法\r\n    useTeamTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由30余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 5名</li>\r\n<li>合伙人律师 8名</li>\r\n<li>资深律师 12名</li>\r\n<li>执业律师 10名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组、公司治理</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理、违约责任</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护、侵权诉讼</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁、人事争议</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理、仲裁程序</li>\r\n<li><strong>刑事辩护：</strong>刑事案件辩护、取保候审、缓刑申请</li>\r\n<li><strong>房地产法务：</strong>房产交易、物业纠纷、拆迁补偿</li>\r\n<li><strong>金融法务：</strong>银行业务、证券投资、保险理赔</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。我们相信，只有真正理解客户的需求，才能提供最有价值的法律服务。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过12年</li>\r\n<li>成功处理案件超过2000起</li>\r\n<li>客户满意度达到99%以上</li>\r\n<li>多名律师获得省市级荣誉</li>\r\n<li>团队协作，资源共享</li>\r\n</ul>\r\n\r\n<h3>持续发展</h3>\r\n<p>我们注重团队的持续发展和专业提升，定期组织内部培训、学术交流和案例研讨，确保团队始终保持专业领先地位。</p>`;\r\n\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = template;\r\n        } else {\r\n          this.ruleForm.team_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewTeam() {\r\n      this.teamPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetTeam() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = '';\r\n        } else {\r\n          this.ruleForm.team_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onTeamChange(val) {\r\n      this.ruleForm.index_team_content = val;\r\n      this.ruleForm.team_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onTeamTextChange(val) {\r\n      this.ruleForm.team_text = val;\r\n      this.ruleForm.index_team_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertTeamText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '团队规模':\r\n          insertValue = '30余名专业律师';\r\n          break;\r\n        case '专业领域':\r\n          insertValue = '公司法务、合同纠纷、知识产权、劳动法务';\r\n          break;\r\n        case '服务理念':\r\n          insertValue = '客户至上、专业至上';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n      }\r\n\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.index_team_content += insertValue;\r\n      } else {\r\n        this.ruleForm.team_text += insertValue;\r\n      }\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 保存前同步所有内容的两种格式\r\n      // 隐私条款\r\n      if (this.privacyEditMode === 'rich') {\r\n        this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.yinsi = this.ruleForm.yinsi_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 关于我们\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_about_content = this.ruleForm.about_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 团队介绍\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_team_content = this.ruleForm.team_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tab-container {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.form-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 提交按钮容器 */\r\n.submit-container {\r\n  text-align: center;\r\n  padding: 24px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-input, .el-select, .el-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.el-textarea .el-textarea__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 标签页样式 */\r\n.el-tabs--card > .el-tabs__header .el-tabs__item {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\r\n  background-color: #fff;\r\n  border-bottom-color: #fff;\r\n}\r\n\r\n/* 上传按钮样式 */\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n/* 隐私条款、关于我们、团队介绍相关样式 */\r\n.privacy-container,\r\n.about-container,\r\n.team-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.privacy-toolbar,\r\n.about-toolbar,\r\n.team-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: #fff;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  border: 1px solid #ddd;\r\n}\r\n\r\n.edit-mode-switch {\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.rich-editor-container {\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.text-editor-container {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.privacy-textarea,\r\n.about-textarea,\r\n.team-textarea {\r\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-textarea ::v-deep .el-textarea__inner,\r\n.about-textarea ::v-deep .el-textarea__inner,\r\n.team-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 16px;\r\n  resize: vertical;\r\n}\r\n\r\n.quick-insert {\r\n  background: #f0f9ff;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 6px;\r\n  padding: 12px 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.quick-insert-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #0369a1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.quick-insert-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text {\r\n  color: #0369a1;\r\n  background: #e0f2fe;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 4px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text:hover {\r\n  background: #0369a1;\r\n  color: #fff;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.privacy-preview-dialog .preview-content,\r\n.about-preview-dialog .preview-content,\r\n.team-preview-dialog .preview-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 6px;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h2,\r\n.about-preview-dialog .preview-content h2,\r\n.team-preview-dialog .preview-content h2 {\r\n  color: #2c3e50;\r\n  border-bottom: 2px solid #3498db;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h3,\r\n.about-preview-dialog .preview-content h3,\r\n.team-preview-dialog .preview-content h3 {\r\n  color: #34495e;\r\n  margin-top: 25px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content p,\r\n.about-preview-dialog .preview-content p,\r\n.team-preview-dialog .preview-content p {\r\n  margin-bottom: 12px;\r\n  color: #555;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content ul,\r\n.about-preview-dialog .preview-content ul,\r\n.team-preview-dialog .preview-content ul {\r\n  margin-bottom: 15px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content li,\r\n.about-preview-dialog .preview-content li,\r\n.team-preview-dialog .preview-content li {\r\n  margin-bottom: 8px;\r\n  color: #666;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content strong,\r\n.about-preview-dialog .preview-content strong,\r\n.team-preview-dialog .preview-content strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-wrapper {\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .upload-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .upload-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-toolbar {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    justify-content: center;\r\n  }\r\n\r\n  .toolbar-right {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-insert-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-preview-dialog,\r\n  .about-preview-dialog,\r\n  .team-preview-dialog {\r\n    width: 95% !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAmhBA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,QAAA;QACAC,SAAA;QACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,QAAA;QACAC,YAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,KAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;QACAC,UAAA;QACAC,mBAAA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;QACAC,UAAA;QACAC,kBAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;QACAC,SAAA;MACA;MACAC,UAAA;MACAC,GAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,oBAAA;MACAC,yBAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,OAAA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,eAAA;MAAA;MACAvB,KAAA,GACA;QAAAwB,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACA;IACAC,iBAAA;MACA,SAAAP,eAAA;QACA;QACA,MAAAQ,IAAA,QAAArC,QAAA,CAAAa,KAAA,CAAAyB,OAAA;QACA,OAAAD,IAAA,CAAAE,MAAA;MACA;QACA,YAAAvC,QAAA,CAAAc,UAAA,CAAAyB,MAAA;MACA;IACA;IACA;IACAC,eAAA;MACA,SAAAV,aAAA;QACA,MAAAO,IAAA,QAAArC,QAAA,CAAAe,mBAAA,CAAAuB,OAAA;QACA,OAAAD,IAAA,CAAAE,MAAA;MACA;QACA,YAAAvC,QAAA,CAAAgB,UAAA,CAAAuB,MAAA;MACA;IACA;IACA;IACAE,cAAA;MACA,SAAAV,YAAA;QACA,MAAAM,IAAA,QAAArC,QAAA,CAAAiB,kBAAA,CAAAqB,OAAA;QACA,OAAAD,IAAA,CAAAE,MAAA;MACA;QACA,YAAAvC,QAAA,CAAAkB,SAAA,CAAAqB,MAAA;MACA;IACA;EACA;EACAG,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;IACA;IACA,KAAA5C,QAAA,CAAAc,UAAA,QAAAd,QAAA,CAAAa,KAAA,CAAAyB,OAAA;IACA,KAAAtC,QAAA,CAAAgB,UAAA,QAAAhB,QAAA,CAAAe,mBAAA,CAAAuB,OAAA;IACA,KAAAtC,QAAA,CAAAkB,SAAA,QAAAlB,QAAA,CAAAiB,kBAAA,CAAAqB,OAAA;EACA;EACAO,OAAA;IACAC,QAAA;MACA;MACAH,OAAA,CAAAC,GAAA;IACA;IACAG,YAAAC,QAAA;MACA,KAAArB,SAAA,GAAAqB,QAAA;IACA;IACAC,OAAA;IACAC,WAAA;MACA;MACAP,OAAA,CAAAC,GAAA;IACA;IACAO,cAAAC,GAAA;MACA;MACA,KAAApD,QAAA,MAAA2B,SAAA;MACA,KAAA0B,QAAA,CAAAC,OAAA;IACA;IAEAC,aAAAC,IAAA;MACA,MAAAC,UAAA,6BAAAC,IAAA,CAAAF,IAAA,CAAAG,IAAA;MACA,KAAAF,UAAA;QACA,KAAAJ,QAAA,CAAAO,KAAA;QACA;MACA;MACA;MACA,KAAAP,QAAA,CAAAQ,IAAA;MACA;IACA;IACAC,SAAAN,IAAA,EAAAR,QAAA;MACA;MACA,KAAAhD,QAAA,CAAAgD,QAAA;MACA,KAAAK,QAAA,CAAAC,OAAA;IACA;IACAS,UAAAP,IAAA;MACA,KAAAlC,UAAA,GAAAkC,IAAA;MACA,KAAAjC,aAAA;IACA;IACAyC,YAAA;IACA;IACAC,YAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,MAAAC,QAAA;AACA,+BAAAC,IAAA,GAAAC,kBAAA;AACA,+BAAAD,IAAA,GAAAC,kBAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,eAAAxE,QAAA,CAAAE,YAAA;AACA,UAAAF,QAAA,CAAAG,QAAA;AACA,UAAAH,QAAA,CAAAI,KAAA;AACA,UAAAJ,QAAA,CAAAK,YAAA;QAEA,SAAAwB,eAAA;UACA,KAAA7B,QAAA,CAAAa,KAAA,GAAAyD,QAAA;QACA;UACA,KAAAtE,QAAA,CAAAc,UAAA,GAAAwD,QAAA,CAAAhC,OAAA;QACA;QACA,KAAAe,QAAA,CAAAC,OAAA;MACA;IACA;IAEAmB,eAAA;MACA,KAAAjD,oBAAA;IACA;IAEAkD,aAAA;MACA,KAAAR,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,SAAAxC,eAAA;UACA,KAAA7B,QAAA,CAAAa,KAAA;QACA;UACA,KAAAb,QAAA,CAAAc,UAAA;QACA;QACA,KAAAuC,QAAA,CAAAC,OAAA;MACA;IACA;IAEAqB,gBAAAC,GAAA;MACA,KAAA5E,QAAA,CAAAa,KAAA,GAAA+D,GAAA;MACA;MACA,KAAA5E,QAAA,CAAAc,UAAA,GAAA8D,GAAA,CAAAtC,OAAA;IACA;IAEAuC,oBAAAD,GAAA;MACA,KAAA5E,QAAA,CAAAc,UAAA,GAAA8D,GAAA;MACA;MACA,KAAA5E,QAAA,CAAAa,KAAA,GAAA+D,GAAA,CAAAtC,OAAA;IACA;IAEAwC,WAAAnB,IAAA;MACA,IAAAoB,WAAA;MACA,QAAApB,IAAA;QACA;UACAoB,WAAA,QAAA/E,QAAA,CAAAE,YAAA;UACA;QACA;UACA6E,WAAA,QAAA/E,QAAA,CAAAG,QAAA;UACA;QACA;UACA4E,WAAA,QAAA/E,QAAA,CAAAI,KAAA;UACA;QACA;UACA2E,WAAA,OAAAR,IAAA,GAAAC,kBAAA;UACA;MACA;MAEA,SAAA3C,eAAA;QACA;QACA,KAAA7B,QAAA,CAAAa,KAAA,IAAAkE,WAAA;MACA;QACA;QACA,KAAA/E,QAAA,CAAAc,UAAA,IAAAiE,WAAA;MACA;IACA;IAEA;IACAC,iBAAA;MACA,KAAAd,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,MAAAC,QAAA;AACA;;AAEA;AACA,UAAAtE,QAAA,CAAAE,YAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAAF,QAAA,CAAAK,YAAA;AACA,UAAAL,QAAA,CAAAG,QAAA;AACA,UAAAH,QAAA,CAAAI,KAAA;QAEA,SAAA0B,aAAA;UACA,KAAA9B,QAAA,CAAAe,mBAAA,GAAAuD,QAAA;QACA;UACA,KAAAtE,QAAA,CAAAgB,UAAA,GAAAsD,QAAA,CAAAhC,OAAA;QACA;QACA,KAAAe,QAAA,CAAAC,OAAA;MACA;IACA;IAEA2B,aAAA;MACA,KAAAxD,yBAAA;IACA;IAEAyD,WAAA;MACA,KAAAhB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,SAAAvC,aAAA;UACA,KAAA9B,QAAA,CAAAe,mBAAA;QACA;UACA,KAAAf,QAAA,CAAAgB,UAAA;QACA;QACA,KAAAqC,QAAA,CAAAC,OAAA;MACA;IACA;IAEA6B,cAAAP,GAAA;MACA,KAAA5E,QAAA,CAAAe,mBAAA,GAAA6D,GAAA;MACA,KAAA5E,QAAA,CAAAgB,UAAA,GAAA4D,GAAA,CAAAtC,OAAA;IACA;IAEA8C,kBAAAR,GAAA;MACA,KAAA5E,QAAA,CAAAgB,UAAA,GAAA4D,GAAA;MACA,KAAA5E,QAAA,CAAAe,mBAAA,GAAA6D,GAAA,CAAAtC,OAAA;IACA;IAEA+C,gBAAA1B,IAAA;MACA,IAAAoB,WAAA;MACA,QAAApB,IAAA;QACA;UACAoB,WAAA,QAAA/E,QAAA,CAAAE,YAAA;UACA;QACA;UACA6E,WAAA;UACA;QACA;UACAA,WAAA,QAAA/E,QAAA,CAAAG,QAAA;UACA;QACA;UACA4E,WAAA,QAAA/E,QAAA,CAAAK,YAAA;UACA;MACA;MAEA,SAAAyB,aAAA;QACA,KAAA9B,QAAA,CAAAe,mBAAA,IAAAgE,WAAA;MACA;QACA,KAAA/E,QAAA,CAAAgB,UAAA,IAAA+D,WAAA;MACA;IACA;IAEA;IACAO,gBAAA;MACA,KAAApB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,MAAAC,QAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;QAEA,SAAAvC,YAAA;UACA,KAAA/B,QAAA,CAAAiB,kBAAA,GAAAqD,QAAA;QACA;UACA,KAAAtE,QAAA,CAAAkB,SAAA,GAAAoD,QAAA,CAAAhC,OAAA;QACA;QACA,KAAAe,QAAA,CAAAC,OAAA;MACA;IACA;IAEAiC,YAAA;MACA,KAAA7D,wBAAA;IACA;IAEA8D,UAAA;MACA,KAAAtB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAT,IAAA;MACA,GAAAU,IAAA;QACA,SAAAtC,YAAA;UACA,KAAA/B,QAAA,CAAAiB,kBAAA;QACA;UACA,KAAAjB,QAAA,CAAAkB,SAAA;QACA;QACA,KAAAmC,QAAA,CAAAC,OAAA;MACA;IACA;IAEAmC,aAAAb,GAAA;MACA,KAAA5E,QAAA,CAAAiB,kBAAA,GAAA2D,GAAA;MACA,KAAA5E,QAAA,CAAAkB,SAAA,GAAA0D,GAAA,CAAAtC,OAAA;IACA;IAEAoD,iBAAAd,GAAA;MACA,KAAA5E,QAAA,CAAAkB,SAAA,GAAA0D,GAAA;MACA,KAAA5E,QAAA,CAAAiB,kBAAA,GAAA2D,GAAA,CAAAtC,OAAA;IACA;IAEAqD,eAAAhC,IAAA;MACA,IAAAoB,WAAA;MACA,QAAApB,IAAA;QACA;UACAoB,WAAA;UACA;QACA;UACAA,WAAA;UACA;QACA;UACAA,WAAA;UACA;QACA;UACAA,WAAA,QAAA/E,QAAA,CAAAG,QAAA;UACA;MACA;MAEA,SAAA4B,YAAA;QACA,KAAA/B,QAAA,CAAAiB,kBAAA,IAAA8D,WAAA;MACA;QACA,KAAA/E,QAAA,CAAAkB,SAAA,IAAA6D,WAAA;MACA;IACA;IAEAa,SAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAxE,iBAAA;;MAEA;MACA;MACA,SAAAQ,eAAA;QACA,KAAA7B,QAAA,CAAAc,UAAA,QAAAd,QAAA,CAAAa,KAAA,CAAAyB,OAAA;MACA;QACA,KAAAtC,QAAA,CAAAa,KAAA,QAAAb,QAAA,CAAAc,UAAA,CAAAwB,OAAA;MACA;;MAEA;MACA,SAAAR,aAAA;QACA,KAAA9B,QAAA,CAAAgB,UAAA,QAAAhB,QAAA,CAAAe,mBAAA,CAAAuB,OAAA;MACA;QACA,KAAAtC,QAAA,CAAAe,mBAAA,QAAAf,QAAA,CAAAgB,UAAA,CAAAsB,OAAA;MACA;;MAEA;MACA,SAAAP,YAAA;QACA,KAAA/B,QAAA,CAAAkB,SAAA,QAAAlB,QAAA,CAAAiB,kBAAA,CAAAqB,OAAA;MACA;QACA,KAAAtC,QAAA,CAAAiB,kBAAA,QAAAjB,QAAA,CAAAkB,SAAA,CAAAoB,OAAA;MACA;;MAEA;MACAwD,UAAA;QACAD,KAAA,CAAAxC,QAAA;UACAM,IAAA;UACAoC,OAAA;QACA;QACAF,KAAA,CAAAxE,iBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}