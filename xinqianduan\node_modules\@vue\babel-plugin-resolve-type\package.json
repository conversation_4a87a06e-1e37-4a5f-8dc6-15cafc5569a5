{"name": "@vue/babel-plugin-resolve-type", "version": "1.2.2", "description": "Babel plugin for resolving Vue types.", "author": "三咲智子 <<EMAIL>>", "homepage": "https://github.com/vuejs/babel-plugin-jsx/tree/dev/packages/babel-plugin-resolve-type#readme", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/babel-plugin-jsx"}, "bugs": {"url": "https://github.com/vuejs/babel-plugin-jsx/issues"}, "files": ["dist"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dependencies": {"@babel/code-frame": "^7.23.5", "@babel/helper-module-imports": "~7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/parser": "^7.23.9", "@vue/compiler-sfc": "^3.4.15"}, "devDependencies": {"@babel/core": "^7.23.9", "@types/babel__code-frame": "^7.0.6", "vue": "^3.4.15"}, "scripts": {"build": "tsup", "watch": "tsup --watch"}}