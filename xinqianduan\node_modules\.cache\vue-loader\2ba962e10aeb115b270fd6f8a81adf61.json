{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1732626900097}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpsZXQgX3RoaXM7DQppbXBvcnQgZW1vamlEYXRhIGZyb20gIi4vZW1vamkuanMiOw0KaW1wb3J0IGF1ZGlvcGxheSBmcm9tICIvc3JjL2NvbXBvbmVudHMvYXVkaW9wbGF5LnZ1ZSI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJjaGF0IiwNCiAgY29tcG9uZW50czogeyBhdWRpb3BsYXkgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KCQl1c2Vyc3M6W10sDQoJCWx2c2hpc3M6W10sDQoJCXl1YW5nb25nc3M6W10sDQogICAgICB0YWJsZTogZmFsc2UsDQogICAgICBncmlkRGF0YTogIiIsDQogICAgICBydWxlRm9ybTogIiIsDQogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsDQogICAgICBlbW9qaURhdGE6IGVtb2ppRGF0YSwNCiAgICAgIC8vIOW+l+efpeW9k+WJjemAieaLqeeahOaYr+WTquS4quWvueivnQ0KICAgICAgc2VsZWN0SWQ6IDEsDQogICAgICBhY3RpdmVOYW1lOiAiZmlyc3QiLA0KICAgICAgc2VhcmNoOiAiIiwNCiAgICAgIGFjdGl2ZTogZmFsc2UsDQogICAgICBpbWdVbHI6ICIiLA0KICAgICAgeW9uX2lkOiAwLA0KCSAgaWQ6MCwNCiAgICAgIGlzU2hvd1NlYWNoOiBmYWxzZSwNCiAgICAgIHR5cGU6ICIiLA0KICAgICAgLy/ogYrlpKnorrDlvZUNCiAgICAgIGxpc3RzOiBbXSwNCgkgIGxhOmZhbHNlLA0KICAgICAgTmFtZXM6ICIiLA0KICAgICAgaXNTaG93UG9wdXA6IGZhbHNlLA0KICAgICAgdGV4dENvbnRlbnQ6ICIiLA0KICAgICAgc2VsZWN0SWQ6IDAsDQogICAgICBsdnNoaWlkOiAiNCIsDQogICAgICBwaWNfcGF0aDogIiIsDQogICAgICBmaWxlX3BhdGg6ICIiLA0KICAgICAgbGlzdDogW10sDQogICAgICB0aW1lcjogIiIsDQogICAgICB1c2VyczogW10sDQogICAgICBxdW5zOiBbXSwNCiAgICAgIHF1bGlhb0luZGV4OiAwLA0KICAgICAgcXVsaWFvczogW10sDQogICAgICBpc0Vtamk6IGZhbHNlLA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgeXVhbnNoaXF1bnM6IFtdLA0KICAgICAgeXVhbnNoaXVzZXJzOiBbXSwNCiAgICB9Ow0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZWRpdERhdGEoaWQpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBpZiAoaWQgIT0gMCkgew0KICAgICAgICB0aGlzLmdldEluZm8oaWQpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5ydWxlRm9ybSA9IHsNCiAgICAgICAgICB0aXRsZTogIiIsDQogICAgICAgICAgZGVzYzogIiIsDQogICAgICAgIH07DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTdWNjZXMxcyhyZXMpIHsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsNCiAgICAgICAgdGhpcy5ydWxlRm9ybVsiZmlsZV9wYXRoIl0gPSByZXMuZGF0YS51cmw7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0SW5mbyhpZCkgew0KICAgICAgbGV0IF90aGlzID0gdGhpczsNCiAgICAgIHRoaXMuZ2V0UmVxdWVzdCgiL2dvbmdkYW4vcmVhZD9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5ydWxlRm9ybSA9IHJlc3AuZGF0YTsNCiAgICAgICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KCXF1YW55dWFuKCl7DQoJCV90aGlzLmxhID0hX3RoaXMubGENCgkJX3RoaXMucG9zdFJlcXVlc3QoIi9jaGF0L2dldFF1bk1vcmVJbmZvIiwgeyBpZDpfdGhpcy5pZCB9KS50aGVuKChyZXNwKSA9PiB7DQoJCSAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCgkJICAgIF90aGlzLnVzZXJzcyA9IHJlc3AuZGF0YS51c2Vycw0KCQkgICAgX3RoaXMubHZzaGlzcyA9IHJlc3AuZGF0YS5sdnNoaXMNCgkJICAgIF90aGlzLnl1YW5nb25nc3MgPSByZXNwLmRhdGEueXVhbmdvbmdzDQoJCQkNCgkJICB9DQoJCX0pOw0KCX0sDQogICAgc2F2ZURhdGEoKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy5wb3N0UmVxdWVzdCgiL2dvbmdkYW4vc2F2ZSIsIHRoaXMucnVsZUZvcm0pLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcC5tc2csDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsNCiAgICAgICAgICAgICAgX3RoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2hvd2dvbmdkYW4oKSB7DQogICAgICBsZXQgdWlkID0gdGhpcy5xdW5zW3RoaXMucXVsaWFvSW5kZXhdWyJ1aWQiXTsNCiAgICAgIF90aGlzLnRhYmxlID0gdHJ1ZTsNCiAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9nb25nZGFuTGlzdCIsIHsgdWlkOiB1aWQgfSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLmdyaWREYXRhID0gcmVzcC5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNob3dEYWliYW4oaXNfZGFpYmFuKSB7DQoJDQogICAgICBfdGhpcw0KICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L2dldFF1biIsIHsgaXNfZGFpYmFuOiBpc19kYWliYW4gfSkNCiAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgX3RoaXMucXVucyA9IHJlc3AuZGF0YTsNCiAgICAgICAgICAgIF90aGlzLnl1YW5zaGlxdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgICAgX3RoaXMuc2VsZWN0SWQgPSAtMTsNCiAgICAgICAgICAgIF90aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgY2hhbmdlS2V5d29yZChlKSB7DQogICAgICBsZXQgcXVucyA9IF90aGlzLnl1YW5zaGlxdW5zOw0KICAgICAgbGV0IHVzZXJzID0gX3RoaXMueXVhbnNoaXVzZXJzOw0KICAgICAgbGV0IHNlYXJjaCA9IGUudGFyZ2V0Ll92YWx1ZTsNCg0KICAgICAgX3RoaXMucXVucyA9IHF1bnMuZmlsdGVyKChkYXRhKSA9PiBkYXRhLnRpdGxlLnNlYXJjaChzZWFyY2gpICE9IC0xKTsNCiAgICAgIF90aGlzLnVzZXJzID0gdXNlcnMuZmlsdGVyKA0KICAgICAgICAoZGF0YSkgPT4NCiAgICAgICAgICAhc2VhcmNoIHx8IGRhdGEudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2gudG9Mb3dlckNhc2UoKSkNCiAgICAgICk7DQogICAgfSwNCiAgICBkYWliYW4oKSB7DQogICAgICBsZXQgaWQgPSB0aGlzLnF1bnNbdGhpcy5xdWxpYW9JbmRleF1bImlkIl07DQogICAgICBsZXQgaXNfZGFpYmFuID0gdGhpcy5xdW5zW3RoaXMucXVsaWFvSW5kZXhdWyJpc19kYWliYW4iXSA9PSAxID8gMiA6IDE7DQogICAgICBfdGhpcw0KICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L2RhaWJhbiIsIHsgaWQ6IGlkLCBpc19kYWliYW46IGlzX2RhaWJhbiB9KQ0KICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICBfdGhpcy5xdW5zW3RoaXMucXVsaWFvSW5kZXhdWyJpc19kYWliYW4iXSA9IGlzX2RhaWJhbjsNCiAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzcC5tc2cpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIG9wZW5FbWppKCkgew0KICAgICAgdGhpcy5pc0VtamkgPSAhdGhpcy5pc0Vtamk7DQogICAgICBjb25zb2xlLmxvZygiLS0tLS0tLS0tLS0tLS0tLS0tLS0tLXd3MnciKTsNCiAgICB9LA0KICAgIGNoYW5nZUZpbGUoZmllbGQpIHsNCiAgICAgIHRoaXMudHlwZSA9IGZpZWxkOw0KICAgIH0sDQogICAgb3BlbkZpbGUodXJsKSB7DQogICAgICB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8v5p+l55yL5Zu+54mHDQogICAgb3BlbkltZyhpbWcpIHsNCiAgICAgIHRoaXMuaW1nVWxyID0gaW1nOw0KICAgICAgdGhpcy5pc1Nob3dQb3B1cCA9IHRydWU7DQogICAgICBjb25zb2xlLmxvZygiLS0tLS0tLS0tLSIsIGltZyk7DQogICAgfSwNCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgbGV0IHR5cGUgPSBmaWxlLnR5cGU7DQogICAgICBjb25zb2xlLmxvZyh0eXBlLCAidHlwZSIpOw0KICAgICAgaWYgKA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gImRvYyIgfHwNCgkJICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAiZG9jeCIgfHwNCiAgICAgICAgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJ4bHMiIHx8DQogICAgICAgICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAicHB0IiB8fA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gInBkZiIgfHwNCiAgICAgICAgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJ4bHN4IiB8fA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gInBwdHgiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgc2hvd0Nsb3NlOiB0cnVlLA0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6knZG9jLGRvY3gsIHhscywgcHB0LCBwZGYsIGRvY3gsIHhsc3gsIHBwdHgn5paH5Lu2IiwNCiAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU3VjY2VzcyhyZXMpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBjb25zb2xlLmxvZyhyZXMpOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICBfdGhpcy5zZW5kSW1nKHJlcy5kYXRhLnVybCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVN1Y2Nlc3MxKHJlcywgZmxpZSkgew0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICBfdGhpcy5zZW5kRmlsZShyZXMuZGF0YS51cmwsIGZsaWUpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgc2hvd0Nsb3NlOiB0cnVlLA0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6knZG9jLCB4bHMsIHBwdCwgcGRmLCB4bHN4LCBwcHR4J+aWh+S7tiIsDQogICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICByZWRTZXNzaW9uKGluZGV4KSB7DQogICAgICB0aGlzLnNlbGVjdElkID0gaW5kZXg7DQogICAgICB0aGlzLnF1bGlhb0luZGV4ID0gLTE7DQoJX3RoaXMubGEgPSBmYWxzZQ0KICAgICAgX3RoaXMuZ2V0TGlzdCgpOw0KICAgICAgLy8gc2V0VGltZW91dCgNCiAgICAgIC8vICAgKCkgPT4gKHRoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPSB0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0KSwNCiAgICAgIC8vICAgMA0KICAgICAgLy8gKTsNCiAgICB9LA0KICAgIGNoYW5nZVF1bihpbmRleCkgew0KICAgICAgdGhpcy5zZWxlY3RJZCA9IC0xOw0KICAgICAgdGhpcy5xdWxpYW9JbmRleCA9IGluZGV4Ow0KICAgICAgX3RoaXMucXVuc1tpbmRleF0uY291bnQgPSAwOw0KCSAgX3RoaXMubGEgPSBmYWxzZQ0KICAgICAgX3RoaXMuZ2V0TGlzdCgpOw0KICAgICAgLy8gc2V0VGltZW91dCgNCiAgICAgIC8vICAgKCkgPT4gKHRoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPSB0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0KSwNCiAgICAgIC8vICAgMA0KICAgICAgLy8gKTsNCiAgICB9LA0KICAgIGdldEVtb2ppKGl0ZW0pIHsNCiAgICAgIHRoaXMudGV4dENvbnRlbnQgKz0gaXRlbTsNCiAgICB9LA0KICAgIGNoYW5nZShlKSB7DQogICAgICBpZiAodGhpcy5zZWFyY2gpIHRoaXMuaXNTaG93U2VhY2ggPSB0cnVlOw0KICAgICAgZWxzZSB0aGlzLmlzU2hvd1NlYWNoID0gZmFsc2U7DQogICAgfSwNCiAgICBkZWwoKSB7DQogICAgICB0aGlzLnNlYXJjaCA9ICIiOw0KICAgICAgdGhpcy5pc1Nob3dTZWFjaCA9IGZhbHNlOw0KICAgIH0sDQogICAgaGFuZGxlU2Nyb2xsKGUpIHsNCiAgICAgIGlmICh0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID09IDApIHsNCiAgICAgICAgY29uc29sZS5sb2coIui/memHjOWkhOeQhuWKoOi9veabtOWkmiIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/lj5HpgIENCiAgICBzZW5kKCkgew0KICAgICAgX3RoaXMuc2VuZE1lc3NhZ2UoX3RoaXMudGV4dENvbnRlbnQpOw0KICAgICAgX3RoaXMudGV4dENvbnRlbnQgPSAiIjsNCiAgICAgIC8vICAgX3RoaXMuZ2V0TGlzdCgpOw0KICAgICAgLyogc2V0VGltZW91dCgNCiAgICAgICAgICgpID0+ICh0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID0gdGhpcy4kcmVmcy5saXN0LnNjcm9sbEhlaWdodCksDQogICAgICAgICAwKi8NCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICBpZiAoX3RoaXMuc2VsZWN0SWQgIT0gLTEpIHsNCiAgICAgICAgbGV0IGlkID0gX3RoaXMudXNlcnNbX3RoaXMuc2VsZWN0SWRdLmlkOw0KICAgICAgICBfdGhpcy50aXRsZSA9IF90aGlzLnVzZXJzW190aGlzLnNlbGVjdElkXS50aXRsZTsNCgkJDQogICAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9jaGF0TGlzdCIsIHsgdWlkOiBpZCB9KS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIGlmIChyZXNwLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBfdGhpcy5saXN0ID0gcmVzcC5kYXRhOw0KDQogICAgICAgICAgICAgIF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID0gX3RoaXMuJHJlZnMubGlzdC5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgaWQgPSBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS5pZDsNCiAgICAgICAgbGV0IGNvdW50ID0NCiAgICAgICAgICBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS51aWQubGVuZ3RoICogMSArDQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ubHZzaGlfaWQubGVuZ3RoICogMSArDQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ueXVhbmdvbmdfaWQubGVuZ3RoICogMTsNCgkJCV90aGlzLmlkID0gaWQ7DQoJY29uc29sZS5sb2coX3RoaXMuaWQpDQoJCQkNCiAgICAgICAgX3RoaXMudGl0bGUgPSBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS50aXRsZSArICIoIiArIGNvdW50ICsgIikiOw0KICAgICAgICBfdGhpcy5wb3N0UmVxdWVzdCgiL2NoYXQvcXVubGlhb0xpc3QiLCB7IHF1bl9pZDogaWQgfSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICBpZiAocmVzcC5kYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgX3RoaXMubGlzdCA9IHJlc3AuZGF0YTsNCiAgICAgICAgICAgICAgX3RoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPSBfdGhpcy4kcmVmcy5saXN0LnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIF90aGlzLmxpc3QgPSBbXTsNCiAgICAgICAgICAgIH0NCgkJCQ0KICAgICAgICAgICAgc2V0VGltZW91dCgNCiAgICAgICAgICAgICAgKCkgPT4gKHRoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPSB0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0KSwNCiAgICAgICAgICAgICAgMA0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9DQogICAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldE1vcmVMaXN0KCkgew0KICAgICAgaWYgKF90aGlzLnNlbGVjdElkICE9IC0xKSB7DQogICAgICAgIGxldCB1aWQgPSBfdGhpcy51c2Vyc1tfdGhpcy5zZWxlY3RJZF0uaWQ7DQogICAgICAgIF90aGlzLnRpdGxlID0gX3RoaXMudXNlcnNbX3RoaXMuc2VsZWN0SWRdLnRpdGxlOw0KDQogICAgICAgIGxldCBpZCA9IDA7DQogICAgICAgIGlmIChfdGhpcy5saXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBpZCA9IF90aGlzLmxpc3RbX3RoaXMubGlzdC5sZW5ndGggLSAxXS5pZDsNCiAgICAgICAgICBfdGhpcw0KICAgICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9nZXRNb3JlUXVuTGlzdCIsIHsgdWlkOiB1aWQsIGlkOiBpZCB9KQ0KICAgICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgICAgX3RoaXMuZ2V0UXVuMSgpOw0KICAgICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIGlmIChyZXNwLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgX3RoaXMubGlzdC5wdXNoKHJlc3AuZGF0YSk7DQogICAgICAgICAgICAgICAgICAvLyAgICBfdGhpcy4kcmVmcy5saXN0LnNjcm9sbFRvcCA9IF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0Ow0KDQogICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KA0KICAgICAgICAgICAgICAgICAgICAoKSA9Pg0KICAgICAgICAgICAgICAgICAgICAgICh0aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMubGlzdC5zY3JvbGxIZWlnaHQpLA0KICAgICAgICAgICAgICAgICAgICAxMDAwDQogICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHF1bl9pZCA9IF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLmlkOw0KICAgICAgICBsZXQgY291bnQgPQ0KICAgICAgICAgIF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLmx2c2hpX2lkLmxlbmd0aCAqIDEgKw0KICAgICAgICAgIF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLnl1YW5nb25nX2lkLmxlbmd0aCAqIDEgKw0KICAgICAgICAgIDE7DQoNCiAgICAgICAgX3RoaXMudGl0bGUgPSBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS50aXRsZSArICIoIiArIGNvdW50ICsgIikiOw0KICAgICAgICBsZXQgaWQgPSAwOw0KICAgICAgICBpZiAoX3RoaXMubGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgaWQgPSBfdGhpcy5saXN0W190aGlzLmxpc3QubGVuZ3RoIC0gMV0uaWQ7DQogICAgICAgICAgX3RoaXMNCiAgICAgICAgICAgIC5wb3N0UmVxdWVzdCgiL2NoYXQvZ2V0TW9yZVF1bkxpc3QiLCB7IHF1bl9pZDogcXVuX2lkLCBpZDogaWQgfSkNCiAgICAgICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICAgIF90aGlzLmdldFF1bjEoKTsNCiAgICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICBfdGhpcy5saXN0LnB1c2gocmVzcC5kYXRhKTsNCg0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoDQogICAgICAgICAgICAgICAgICAoKSA9Pg0KICAgICAgICAgICAgICAgICAgICAoX3RoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPQ0KICAgICAgICAgICAgICAgICAgICAgIF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0KSwNCiAgICAgICAgICAgICAgICAgIDEwMDANCiAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9ZWxzZXsNCgkJCWlkID0gMTsNCgkJCV90aGlzDQoJCQkgIC5wb3N0UmVxdWVzdCgiL2NoYXQvZ2V0TW9yZVF1bkxpc3QiLCB7IHF1bl9pZDogcXVuX2lkLCBpZDogaWQgfSkNCgkJCSAgLnRoZW4oKHJlc3ApID0+IHsNCgkJCSAgICBfdGhpcy5nZXRRdW4xKCk7DQoJCQkgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCgkJCSAgICAgIF90aGlzLmxpc3QucHVzaChyZXNwLmRhdGEpOw0KCQkJDQoJCQkgICAgICBzZXRUaW1lb3V0KA0KCQkJICAgICAgICAoKSA9Pg0KCQkJICAgICAgICAgIChfdGhpcy4kcmVmcy5saXN0LnNjcm9sbFRvcCA9DQoJCQkgICAgICAgICAgICBfdGhpcy4kcmVmcy5saXN0LnNjcm9sbEhlaWdodCksDQoJCQkgICAgICAgIDEwMDANCgkJCSAgICAgICk7DQoJCQkgICAgfQ0KCQkJICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCgkJCSAgfSk7DQoJCX0NCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbmRNZXNzYWdlKGNvbnRlbnQpIHsNCiAgICAgIGlmIChfdGhpcy5zZWxlY3RJZCAhPSAtMSkgew0KICAgICAgICBsZXQgaWQgPSBfdGhpcy51c2Vyc1tfdGhpcy5zZWxlY3RJZF0uaWQ7DQogICAgICAgIGxldCBvcnRoZXJfaWQgPSAzOw0KICAgICAgICBfdGhpcw0KICAgICAgICAgIC5wb3N0UmVxdWVzdCgiL2NoYXQvc2VuZE1lc3NhZ2UiLCB7DQogICAgICAgICAgICB1aWQ6IGlkLA0KICAgICAgICAgICAgZGlyZWN0aW9uOiAibGVmdCIsIC8v55So5p2l5Yy65YiG5piv5pys5Lq65Y+R6YCB6L+Y5piv5a+55pa5DQogICAgICAgICAgICB0eXBlOiAidGV4dCIsIC8v5Yy65YiG5piv5Zu+54mH6L+Y5piv6K+t6Z+zIHZvaWNl6K+t6Z+zICB0ZXh05paH5a2XIGltYWdl5Zu+54mHIGZpbGXmlofku7YNCiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsDQogICAgICAgICAgICBvcnRoZXJfaWQ6IG9ydGhlcl9pZCwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlICE9IDIwMCkgew0KICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdWlkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0udWlkOw0KICAgICAgICBsZXQgcXVuX2lkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0uaWQ7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kUXVuTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIC8vICB1aWQ6IHVpZCwNCiAgICAgICAgICAgIGRpcmVjdGlvbjogImxlZnQiLCAvL+eUqOadpeWMuuWIhuaYr+acrOS6uuWPkemAgei/mOaYr+WvueaWuQ0KICAgICAgICAgICAgdHlwZTogInRleHQiLCAvL+WMuuWIhuaYr+WbvueJh+i/mOaYr+ivremfsyB2b2ljZeivremfsyAgdGV4dOaWh+WtlyBpbWFnZeWbvueJhyBmaWxl5paH5Lu2DQogICAgICAgICAgICBjb250ZW50OiBjb250ZW50LA0KICAgICAgICAgICAgcXVuX2lkOiBxdW5faWQsDQogICAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3AuY29kZSAhPSAyMDApIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcC5tc2cpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2VuZEZpbGUoY29udGVudCwgZmlsZXMpIHsNCiAgICAgIGlmIChfdGhpcy5zZWxlY3RJZCAhPSAtMSkgew0KICAgICAgICAvLyAgbGV0IGlkID0gX3RoaXMudXNlcnNbX3RoaXMuc2VsZWN0SWRdLmlkOw0KICAgICAgICBsZXQgb3J0aGVyX2lkID0gMzsNCiAgICAgICAgX3RoaXMNCiAgICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L3NlbmRNZXNzYWdlIiwgew0KICAgICAgICAgICAgLy8gIHVpZDogaWQsDQogICAgICAgICAgICBkaXJlY3Rpb246ICJsZWZ0IiwgLy/nlKjmnaXljLrliIbmmK/mnKzkurrlj5HpgIHov5jmmK/lr7nmlrkNCiAgICAgICAgICAgIHR5cGU6ICJmaWxlIiwgLy/ljLrliIbmmK/lm77niYfov5jmmK/or63pn7Mgdm9pY2Xor63pn7MgIHRleHTmloflrZcgaW1hZ2Xlm77niYcgZmlsZeaWh+S7tg0KICAgICAgICAgICAgY29udGVudDogY29udGVudCwNCiAgICAgICAgICAgIG9ydGhlcl9pZDogb3J0aGVyX2lkLA0KICAgICAgICAgICAgZmlsZXM6IGZpbGVzLA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vICAgICAgbGV0IHVpZCA9IF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLnVpZDsNCiAgICAgICAgbGV0IHF1bl9pZCA9IF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLmlkOw0KICAgICAgICBfdGhpcw0KICAgICAgICAgIC5wb3N0UmVxdWVzdCgiL2NoYXQvc2VuZFF1bk1lc3NhZ2UiLCB7DQogICAgICAgICAgICAvLyAgdWlkOiB1aWQsDQogICAgICAgICAgICBkaXJlY3Rpb246ICJsZWZ0IiwgLy/nlKjmnaXljLrliIbmmK/mnKzkurrlj5HpgIHov5jmmK/lr7nmlrkNCiAgICAgICAgICAgIHR5cGU6ICJmaWxlIiwgLy/ljLrliIbmmK/lm77niYfov5jmmK/or63pn7Mgdm9pY2Xor63pn7MgIHRleHTmloflrZcgaW1hZ2Xlm77niYcgZmlsZeaWh+S7tg0KICAgICAgICAgICAgY29udGVudDogY29udGVudCwNCiAgICAgICAgICAgIHF1bl9pZDogcXVuX2lkLA0KICAgICAgICAgICAgZmlsZXM6IGZpbGVzLA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbmRJbWcoY29udGVudCkgew0KICAgICAgaWYgKF90aGlzLnNlbGVjdElkICE9IC0xKSB7DQogICAgICAgIGxldCBpZCA9IF90aGlzLnVzZXJzW190aGlzLnNlbGVjdElkXS5pZDsNCiAgICAgICAgbGV0IG9ydGhlcl9pZCA9IDM7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIHVpZDogaWQsDQogICAgICAgICAgICBkaXJlY3Rpb246ICJsZWZ0IiwgLy/nlKjmnaXljLrliIbmmK/mnKzkurrlj5HpgIHov5jmmK/lr7nmlrkNCiAgICAgICAgICAgIHR5cGU6ICJpbWFnZSIsIC8v5Yy65YiG5piv5Zu+54mH6L+Y5piv6K+t6Z+zIHZvaWNl6K+t6Z+zICB0ZXh05paH5a2XIGltYWdl5Zu+54mHIGZpbGXmlofku7YNCiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsDQogICAgICAgICAgICBvcnRoZXJfaWQ6IG9ydGhlcl9pZCwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlICE9IDIwMCkgew0KICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdWlkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0udWlkOw0KICAgICAgICBsZXQgcXVuX2lkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0uaWQ7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kUXVuTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIHVpZDogdWlkLA0KICAgICAgICAgICAgZGlyZWN0aW9uOiAibGVmdCIsIC8v55So5p2l5Yy65YiG5piv5pys5Lq65Y+R6YCB6L+Y5piv5a+55pa5DQogICAgICAgICAgICB0eXBlOiAiaW1hZ2UiLCAvL+WMuuWIhuaYr+WbvueJh+i/mOaYr+ivremfsyB2b2ljZeivremfsyAgdGV4dOaWh+WtlyBpbWFnZeWbvueJhyBmaWxl5paH5Lu2DQogICAgICAgICAgICBjb250ZW50OiBjb250ZW50LA0KICAgICAgICAgICAgcXVuX2lkOiBxdW5faWQsDQogICAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3AuY29kZSAhPSAyMDApIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcC5tc2cpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgY2hhdEFsbExpc3QoKSB7DQogICAgICBfdGhpcy5wb3N0UmVxdWVzdCgiL2NoYXQvY2hhdEFsbExpc3QiKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgX3RoaXMudXNlcnMgPSByZXNwLmRhdGE7DQogICAgICAgICAgX3RoaXMueXVhbnNoaXVzZXJzID0gcmVzcC5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFF1bigpIHsNCiAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9nZXRRdW4iKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgX3RoaXMucXVucyA9IHJlc3AuZGF0YTsNCiAgICAgICAgICBfdGhpcy55dWFuc2hpcXVucyA9IHJlc3AuZGF0YTsNCiAgICAgICAgICBfdGhpcy5zZWxlY3RJZCA9IC0xOw0KCQkNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIF90aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9LCAxNTAwKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRRdW4xKCkgew0KICAgICAgX3RoaXMucG9zdFJlcXVlc3QoIi9jaGF0L2dldFF1biIpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5xdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgIF90aGlzLnl1YW5zaGlxdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgIF90aGlzLnNlbGVjdElkID0gLTE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAga2V5dXBTdWJtaXQoKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KDQogICAgICBkb2N1bWVudC5vbmtleWRvd24gPSAoZSkgPT4gew0KICAgICAgICBsZXQgX2tleSA9IHdpbmRvdy5ldmVudC5rZXlDb2RlOw0KDQogICAgICAgIC8vZjcgMTE4IGY4IDExOSBmMTAgMTIwDQogICAgICAgIGlmIChfa2V5ID09PSAxMykgew0KICAgICAgICAgIF90aGlzLnNlbmQoKTsNCiAgICAgICAgfQ0KICAgICAgfTsNCiAgICB9LA0KICAgIGRlbEltYWdlKGZpbGUsIGZpbGVOYW1lKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgX3RoaXMuZ2V0UmVxdWVzdCgiL1VwbG9hZC9kZWxJbWFnZT9maWxlTmFtZT0iICsgZmlsZSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLnJ1bGVGb3JtW2ZpbGVOYW1lXSA9ICIiOw0KDQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfISIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICBjb25zb2xlLmxvZygi56a75byA5LmQIik7DQogICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICBfdGhpcyA9IHRoaXM7DQogICAgX3RoaXMuZ2V0UXVuKCk7DQogICAgX3RoaXMuY2hhdEFsbExpc3QoKTsNCiAgICBfdGhpcy55b25faWQgPSB3aW5kb3cuc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgic3BicyIpOw0KICAgIF90aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgX3RoaXMuZ2V0TW9yZUxpc3QoKTsNCiAgICB9LCAxNTAwKTsNCiAgICBfdGhpcy5rZXl1cFN1Ym1pdCgpOw0KDQogICAgLy/pu5jorqTojrflj5bliJfooajnrKzkuIDkuKoNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings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file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"body-div\" @click=\"isEmji = false\">\r\n    <div class=\"content\">\r\n      <div class=\"msglist\">\r\n        <!-- 搜索 -->\r\n        <div class=\"wrapper\">\r\n          <div class=\"search-wrapper\">\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"searchInput\"\r\n              placeholder=\"搜索\"\r\n              @change=\"changeKeyword\"\r\n            />\r\n            <div\r\n              v-if=\"isShowSeach\"\r\n              class=\"searchInput-delete\"\r\n              @click=\"del\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <el-tag @click=\"showDaiban('2')\">群聊</el-tag>\r\n        <el-tag type=\"success\" @click=\"showDaiban('1')\">代办</el-tag>\r\n\r\n        <!-- 好友列表 -->\r\n        <ul class=\"msg-left-box\">\r\n          <!--工作群-->\r\n          <li\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === quliaoIndex }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"38\" height=\"38\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.create_time }}</span>\r\n              <p class=\"lastmsg\">{{ item.desc }}</p>\r\n              <p class=\"number-badge\" v-if=\"item.count > 0\">{{ item.count }}</p>\r\n            </div>\r\n          </li>\r\n          <li\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"index\"\r\n            class=\"sessionlist\"\r\n            :class=\"{ active: index === selectId }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"list-left\">\r\n              <img class=\"avatar\" width=\"42\" height=\"42\" :src=\"item.pic_path\" />\r\n              <span>99</span>\r\n            </div>\r\n            <div class=\"list-right\">\r\n              <p class=\"name\">{{ item.title }}</p>\r\n              <span class=\"time\">{{ item.time }}</span>\r\n              <p class=\"lastmsg\">{{ item.content }}</p>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"chatbox\">\r\n        <!-- v-loading=\"listLoading\" -->\r\n        <div class=\"message\">\r\n          <header class=\"header\">\r\n            <div class=\"friendname\" v-if=\"true\">\r\n              <!-- {{ lists.user.name }} -->\r\n              {{ title }}\r\n            </div>\r\n          </header>\r\n        </div>\r\n        <!-- 聊天框 -->\r\n        <div ref=\"list\" class=\"message-wrapper\" @scroll=\"handleScroll()\">\r\n          <div class=\"msg-box\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <div class=\"msg-time\">\r\n              <span>{{ item.create_time }}</span>\r\n            </div>\r\n            <div\r\n              :class=\"['chatMsg-box', { oneself: item.yuangong_id == yon_id }]\"\r\n            >\r\n              <div class=\"chat-name\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div :class=\"['chatMsg-flex']\">\r\n                <div class=\"flex-view\">\r\n                  <img :src=\"item.avatar\" />\r\n                  <!-- <span>{{ item.title }}</span> -->\r\n                </div>\r\n                <div class=\"flex-view\">\r\n                  <!-- 图片 -->\r\n                  <div class=\"chatMsg-img\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <div class=\"chatMsg-content\" v-if=\"item.type == 'voice'\">\r\n\t\t\t\t\t  <div class=\"\" style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t  \t<audioplay :recordFile=\"item.content\"></audioplay>\r\n\t\t\t\t\t  \t<div>{{ item.datas }}</div>\r\n\t\t\t\t\t  </div>\r\n                   \r\n                  </div>\r\n                  <!-- 文字 -->\r\n                  <!-- 文件 -->\r\n                  <div class=\"file-box\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-flex\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-name\">{{ item.files.name }}</div>\r\n                      <div class=\"file-size\">{{ item.files.size }}</div>\r\n                    </div>\r\n                    <div class=\"file-flex\">\r\n                      <img src=\"img/wenjian.png\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t<div style=\"position: absolute; top: 14px;right: 20px; font-size: 32px;\r\n    cursor: pointer;\" @click=\"quanyuan\">···</div>\r\n\t\t<div class=\"\" style=\"position: absolute;      overflow-y: auto;  width: 200px;\r\n    background: #f2f2f2;\r\n    height: 690px;right:-200px;top: 0px;\" v-if=\"la==true\">\r\n\t\t<div class=\"\">\r\n\t\t\t<div class=\"chat-list-box\" >\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t用户\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\"> \r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(item, index) in userss\" :key=\"index\">\r\n\t\t\t\t\t\t<div v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<img :src=\"value.headimg\"/>\r\n\t\t\t\t\t\t\t<div class=\"sl\">{{value.nickname}}</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\r\n\t\t\t<div class=\"chat-list-box\" v-for=\"(item,index) in yuangongss\" :key=\"index\">\r\n\t\t\t\t<div class=\"chat-list-title\">\r\n\t\t\t\t\t{{item.zhiwei}}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"chat-list\">\r\n\t\t\t\t\t<div class=\"chat-flex\" v-for=\"(value,key) in item.list\" :key=\"key\">\r\n\t\t\t\t\t\t<img :src=\"value.pic_path\" />\r\n\t\t\t\t\t\t<div class=\"sl\">{{value.title}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\r\n\t</div>\r\n        <!-- 输入框 -->\r\n        <div class=\"input-box\">\r\n          <div class=\"workbar-box\">\r\n            <div class=\"upload-emji\">\r\n              <img src=\"img/biaoqing.png\" alt=\"\" @click.stop=\"openEmji\" />\r\n              <div class=\"emji-box\" v-show=\"isEmji\">\r\n                <div class=\"biao-box\">\r\n                  <div\r\n                    class=\"biao-flex\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"upload-file\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                <img src=\"img/insert_img.png\" alt=\"\"\r\n              /></el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"changeFile('image')\">\r\n              <!-- <input\r\n                type=\"file\"\r\n                title=\"选择文件发送\"\r\n                autocomplete=\"off\"\r\n                accept=\"application/*\"\r\n              /> -->\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <img src=\"img/wenjian.png\" alt=\"\" />\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"daiban\">\r\n              <img src=\"img/daiban.png\" alt=\"\" />\r\n            </div>\r\n            <div class=\"upload-file\" @click=\"showgongdan\">\r\n              <img src=\"img/gongdan.png\" alt=\"\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"input-text\">\r\n            <textarea\r\n              placeholder=\"您想说什么？\"\r\n              v-model=\"textContent\"\r\n            ></textarea>\r\n          </div>\r\n          <div class=\"input-btn\">\r\n            <span @click=\"send\">发送Enter</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"img-popup\" :class=\"{ 'show-popup': isShowPopup }\">\r\n      <div>\r\n        <div class=\"img-div\">\r\n          <img :src=\"imgUlr\" alt=\"\" />\r\n        </div>\r\n        <div class=\"close\">\r\n          <span @click=\"isShowPopup = false\">×</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n\t\tuserss:[],\r\n\t\tlvshiss:[],\r\n\t\tyuangongss:[],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      // 得知当前选择的是哪个对话\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n\t  id:0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      //聊天记录\r\n      lists: [],\r\n\t  la:false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\tquanyuan(){\r\n\t\t_this.la =!_this.la\r\n\t\t_this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n\t\t  if (resp.code == 200) {\r\n\t\t    _this.userss = resp.data.users\r\n\t\t    _this.lvshiss = resp.data.lvshis\r\n\t\t    _this.yuangongss = resp.data.yuangongs\r\n\t\t\t\r\n\t\t  }\r\n\t\t});\r\n\t},\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n\t\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target._value;\r\n\r\n      _this.quns = quns.filter((data) => data.title.search(search) != -1);\r\n      _this.users = users.filter(\r\n        (data) =>\r\n          !search || data.title.toLowerCase().includes(search.toLowerCase())\r\n      );\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    //查看图片\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n\t\t !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n\t_this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n\t  _this.la = false\r\n      _this.getList();\r\n      // setTimeout(\r\n      //   () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n      //   0\r\n      // );\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    //发送\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n      //   _this.getList();\r\n      /* setTimeout(\r\n         () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n         0*/\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\t\t\r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n\t\t\t_this.id = id;\r\n\tconsole.log(_this.id)\r\n\t\t\t\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n\t\t\t\r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  //    _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n\t\t\tid = 1;\r\n\t\t\t_this\r\n\t\t\t  .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n\t\t\t  .then((resp) => {\r\n\t\t\t    _this.getQun1();\r\n\t\t\t    if (resp.code == 200) {\r\n\t\t\t      _this.list.push(resp.data);\r\n\t\t\t\r\n\t\t\t      setTimeout(\r\n\t\t\t        () =>\r\n\t\t\t          (_this.$refs.list.scrollTop =\r\n\t\t\t            _this.$refs.list.scrollHeight),\r\n\t\t\t        1000\r\n\t\t\t      );\r\n\t\t\t    }\r\n\t\t\t    _this.loading = false;\r\n\t\t\t  });\r\n\t\t}\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"text\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        //  let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            //  uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        //      let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            //  uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"file\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\", //用来区分是本人发送还是对方\r\n            type: \"image\", //区分是图片还是语音 voice语音  text文字 image图片 file文件\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n\t\t\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        //f7 118 f8 119 f10 120\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n\r\n    //默认获取列表第一个\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t\r\n\t.chat-list-box{\r\n\t\tmargin-bottom: 10px;\r\n\t\t.chat-list-title{\r\n\t\t\tbackground: #ededed;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tpadding: 10px 5px;\r\n\t\t}\r\n\t\t.chat-list{\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\twidth: 100%;\r\n\t\t\t\r\n\t\t\t.chat-flex{\r\n\t\t\t\tpadding: 0px 8px;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 30px;\r\n\t\t\t\timg{\r\n\t\t\t\t\twidth: 45px;\r\n\t\t\t\t\theight: 45px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t}\r\n\t\t\t\t.sl{\r\n\t\t\t\t\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tmax-width:100px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n.chat-name {\r\n  padding-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #333333;\r\n  text-align: left;\r\n}\r\n.oneself {\r\n  .chat-name {\r\n    text-align: right !important;\r\n  }\r\n}\r\nhtml,\r\nbody,\r\ndiv,\r\nspan,\r\napplet,\r\nobject,\r\niframe,\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\np,\r\nblockquote,\r\npre,\r\na,\r\nabbr,\r\nacronym,\r\naddress,\r\nbig,\r\ncite,\r\ncode,\r\ndel,\r\ndfn,\r\nem,\r\nimg,\r\nins,\r\nkbd,\r\nq,\r\ns,\r\nsamp,\r\nsmall,\r\nstrike,\r\nstrong,\r\nsub,\r\nsup,\r\ntt,\r\nvar,\r\nb,\r\nu,\r\ni,\r\ncenter,\r\ndl,\r\ndt,\r\ndd,\r\nol,\r\nul,\r\nli,\r\nfieldset,\r\nform,\r\nlabel,\r\nlegend,\r\ntable,\r\ncaption,\r\ntbody,\r\ntfoot,\r\nthead,\r\ntr,\r\nth,\r\ntd,\r\narticle,\r\naside,\r\ncanvas,\r\ndetails,\r\nembed,\r\nfigure,\r\nfigcaption,\r\nfooter,\r\nheader,\r\nmenu,\r\nnav,\r\noutput,\r\nruby,\r\nsection,\r\nsummary,\r\ntime,\r\nmark,\r\naudio,\r\nvideo,\r\ninput {\r\n  margin: 0;\r\n  padding: 0;\r\n  border: 0;\r\n  font-size: 100%;\r\n  font-weight: normal;\r\n  vertical-align: baseline;\r\n}\r\n\r\n.body-div {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 87vh;\r\n  background: #999999;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 6px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.content {\r\n\tposition: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  width: 900px;\r\n  background: #f2f2f2;\r\n  height: 690px;\r\n}\r\n\r\n.content .msglist {\r\n  width: 350px;\r\n  background: #e6e6e6;\r\n  overflow-y: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box {\r\n  height: calc(100% - 60px);\r\n  overflow: auto;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  padding: 12px;\r\n  -webkit-transition: background-color 0.1s;\r\n  transition: background-color 0.1s;\r\n  font-size: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist:hover {\r\n  background-color: gainsboro;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist.active {\r\n  background-color: #c4c4c4;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left {\r\n  position: relative;\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-left span {\r\n  position: absolute;\r\n  width: 15px;\r\n  height: 15px;\r\n  text-align: center;\r\n  background: red;\r\n  color: #ffffff;\r\n  top: 0;\r\n  right: 0;\r\n  border-radius: 50%;\r\n  font-size: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .list-right {\r\n  position: relative;\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  margin-top: 4px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .name {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  font-size: 14px;\r\n  width: 190px;\r\n  // width: 100px;\r\n  //   overflow: hidden;\r\n  //   white-space: nowrap;\r\n  //   text-overflow: ellipsis;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .time {\r\n  float: right;\r\n  color: #999;\r\n  font-size: 10px;\r\n  vertical-align: top;\r\n}\r\n\r\n.content .msglist .msg-left-box .sessionlist .lastmsg {\r\n  position: absolute;\r\n  font-size: 12px;\r\n  width: 130px;\r\n  height: 15px;\r\n  line-height: 15px;\r\n  color: #999;\r\n  bottom: 8px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n.content .msglist .msg-left-box .sessionlist .number-badge {\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: red;\r\n    color: white;\r\n    width: 25px;\r\n    height: 25px;\r\n    border-radius: 50%;\r\n    font-size: 13px;\r\n    position: absolute;\r\n    top: 24px;\r\n    left: 223px;\r\n}\r\n.content .msglist .wrapper {\r\n  padding: 22px 12px 12px 12px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper {\r\n  position: relative;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  height: 26px;\r\n  width: 100%;\r\n  background-color: #e5e3e2;\r\n  border: 1px solid #d9d7d6;\r\n  border-radius: 2px;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 6px;\r\n  background-color: #e5e3e2;\r\n  outline: none;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput:focus {\r\n  background-color: #f2efee;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .icon-search {\r\n  display: inline-block;\r\n  width: 24px;\r\n  height: 24px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.content .msglist .wrapper .search-wrapper .searchInput-delete {\r\n  display: block;\r\n  position: absolute;\r\n  outline: none;\r\n  top: 0;\r\n  right: 0;\r\n  width: 24px;\r\n  height: 100%;\r\n  background-image: url(\"/img/delete.png\");\r\n  background-size: 26px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .message {\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message .header {\r\n  height: 60px;\r\n  padding: 28px 30px 0;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  border-bottom: 1px solid #e7e7e7;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: justify;\r\n  -ms-flex-pack: justify;\r\n  justify-content: space-between;\r\n}\r\n\r\n.content .chatbox .message .header .friendname {\r\n  font-size: 18px;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.content .chatbox .message-wrapper {\r\n  height: calc(100% - 240px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box {\r\n  padding: 0 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:last-child {\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box:first-child {\r\n  margin-top: 0;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time {\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  color: #ffffff;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .msg-time span {\r\n  display: inline-block;\r\n  padding: 3px 6px;\r\n  border-radius: 3px;\r\n  background-color: #dcdcdc;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img {\r\n  width: 200px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-img img {\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .message-wrapper .msg-box .chatMsg-flex {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: start;\r\n  -ms-flex-align: start;\r\n  align-items: flex-start;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1) {\r\n  width: 44px;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  img {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 6px;\r\n  display: block;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(1)\r\n  span {\r\n  display: block;\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: left;\r\n  padding-top: 3px;\r\n  width: 50px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding: 0 15px;\r\n  padding-right: 30px;\r\n  text-align: left;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content {\r\n  padding: 10px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  text-align: justify;\r\n  display: inline-block;\r\n  position: relative;\r\n  max-width: 260px;\r\n  min-height: 20px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-content::after {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 7px solid transparent;\r\n  left: -14px;\r\n  top: 10px;\r\n  border-right: 7px solid #ffffff;\r\n}\r\n\r\n.content\r\n  .chatbox\r\n  .message-wrapper\r\n  .msg-box\r\n  .chatMsg-flex\r\n  .flex-view:nth-child(2)\r\n  .chatMsg-img\r\n  img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.content .chatbox .input-box {\r\n  height: 180px;\r\n  background: #fff;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box {\r\n  height: 40px;\r\n  padding: 0 10px;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: start;\r\n  -ms-flex-pack: start;\r\n  justify-content: flex-start;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file {\r\n  position: relative;\r\n  width: 20px;\r\n  height: 20px;\r\n  padding: 10px;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 20px;\r\n  opacity: 0;\r\n  z-index: 10;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file input[type=\"file\"] {\r\n  color: transparent;\r\n  cursor: pointer;\r\n}\r\n\r\n.content .chatbox .input-box .workbar-box .upload-file img {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  -webkit-transform: translate(-50%, -50%);\r\n  transform: translate(-50%, -50%);\r\n  display: block;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.content .chatbox .input-box .input-text {\r\n  width: 100%;\r\n  height: 90px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-text textarea {\r\n  width: 100%;\r\n  height: 80px;\r\n  padding: 0 10px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  outline: none;\r\n  font-size: 16px;\r\n  resize: none;\r\n  border: none;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 5px 15px;\r\n  height: 50px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span {\r\n  padding: 5px 15px;\r\n  font-size: 16px;\r\n  background: #f2f2f2;\r\n  cursor: pointer;\r\n  border-radius: 2px;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.content .chatbox .input-box .input-btn span:hover {\r\n  background: #129611;\r\n  color: #ffffff;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.oneself .flex-view:nth-child(1) {\r\n  -webkit-box-ordinal-group: 3;\r\n  -ms-flex-order: 2;\r\n  order: 2;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: end;\r\n  -ms-flex-pack: end;\r\n  justify-content: flex-end;\r\n  -webkit-box-ordinal-group: 2;\r\n  -ms-flex-order: 1;\r\n  order: 1;\r\n  padding-left: 30px !important;\r\n  padding-right: 15px !important;\r\n  text-align: right !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content {\r\n  position: relative;\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::after {\r\n  border: none !important;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .chatMsg-content::before {\r\n  position: absolute;\r\n  content: \"\";\r\n  border: 8px solid transparent;\r\n  right: -16px;\r\n  top: 12px;\r\n  border-left: 8px solid #b2e281;\r\n}\r\n\r\n.oneself .flex-view:nth-child(2) .file-box {\r\n  background: #b2e281 !important;\r\n}\r\n\r\n.file-box {\r\n  width: 230px;\r\n  height: 60px;\r\n  -webkit-box-shadow: 0px 0px 5px #ededed;\r\n  box-shadow: 0px 0px 5px #ededed;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) {\r\n  -webkit-box-flex: 1;\r\n  -ms-flex: 1;\r\n  flex: 1;\r\n  padding-right: 10px;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-name {\r\n  width: 180px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.file-box .file-flex:nth-child(1) .file-size {\r\n  font-size: 12px;\r\n  padding-top: 10px;\r\n  color: #999999;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) {\r\n  width: 34px;\r\n  height: 34px;\r\n  text-align: center;\r\n}\r\n\r\n.file-box .file-flex:nth-child(2) img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.img-popup {\r\n  position: fixed;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  opacity: 0;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n}\r\n\r\n.img-popup .close {\r\n  text-align: center;\r\n}\r\n\r\n.img-popup .close span {\r\n  width: 100px;\r\n  height: 100px;\r\n  font-size: 60px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n\r\n.img-popup .img-div {\r\n  height: 500px;\r\n  -o-object-fit: cover;\r\n  object-fit: cover;\r\n}\r\n\r\n.img-popup .img-div img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.show-popup {\r\n  opacity: 1;\r\n  -webkit-transition: all ease 0.3s;\r\n  transition: all ease 0.3s;\r\n  pointer-events: all;\r\n}\r\n\r\n.upload-emji {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  img {\r\n    width: 20px;\r\n    height: 20px;\r\n    cursor: pointer;\r\n  }\r\n  .emji-box {\r\n    position: absolute;\r\n    top: -250px;\r\n    width: 440px;\r\n    height: 250px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 0px 10px #ededed;\r\n    border-radius: 12px;\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\";\r\n      border: 7px solid transparent;\r\n      border-top: 7px solid #ffffff;\r\n      bottom: -14px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    .biao-box {\r\n      padding: 10px;\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      .biao-flex {\r\n        text-align: center;\r\n        width: 32px;\r\n        margin: 5px;\r\n        font-size: 20px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n/*# sourceMappingURL=all.css.map */\r\n</style>\r\n"]}]}