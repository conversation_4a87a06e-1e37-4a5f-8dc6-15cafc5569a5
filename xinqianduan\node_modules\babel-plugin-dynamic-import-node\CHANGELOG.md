## Unreleased

## v2.3.3
- [Fix] Generate webpack-compatible output with static string arg (#84)
- [Fix] <PERSON><PERSON> visiting the same path gracefully (#71)

## v2.3.2
- [Fix] avoid using reserved param name in babel 7 templates
- [<PERSON>] update `airbnb-js-shims`

## v2.3.1
- [Fix] Evaluate `import()` argument synchronously (#85)
- [<PERSON>] update `eslint`, `eslint-config-airbnb-base`, `in-publish`, `rimraf`, `safe-publish-latest`, `tape`

## v2.3.0
- [New] expose createDynamicImportTransform and getImportSource (#75)
- [Docs] Document noInterop option (#70)

## v2.2.0
- [Refactor] remove dependency on babel-plugin-syntax-dynamic-import
- [<PERSON>] update `airbnb-js-shims`, `babel-preset-airbnb`, `eslint`

## v2.1.0
- [New] add `noInterop` option (#57)
- [Docs] Fix typo "correct" -> "correctly" in readme (#55)
- [<PERSON>] update `airbnb-js-shims`, `babel-eslint`, `babel-preset-airbnb`, `eslint`, `eslint-config-airbnb-base`, `eslint-plugin-import`, `safe-publish-latest`

## v2.0.0
- [Breaking] always return a module namespace object (#52, #47)
- [Breaking] remove `.default` on entry points (#27)
- [Docs] removed $ before npm command (#35)
- [Docs] Improve README.md with a code example (#41)
- [Dev Deps] update `airbnb-js-shims`, `babel-core`, `babel-eslint`, `eslint`, `eslint-plugin-import`
- [Tests] switch from mocha to tape, so we can support older nodes

## v1.2.0
- [New] support comments (#37)
- [Refactor] Use template and types from the babel object (#32)
- [Tests] on `node` `v9`; pin included builds to LTS
- [Dev Deps] update `eslint`, `eslint-config-airbnb-base`, `mocha`, `rimraf`

## v1.1.0
- Visit Import nodes instead of CallExpressions (#30)
- [Deps] update `babel-template`, `babel-types`
- [Dev Deps] update `airbnb-js-shims`, `babel-cli`, `babel-core`, `babel-preset-airbnb`, `babel-register`, `chai`, `eslint`, `eslint-config-airbnb-base`, `eslint-plugin-import`, `mocha`
- [Tests] on `node` `v8`
- [Tests] use `nvm install-latest-npm` so newer npm doesn’t break older node

## v1.0.2
- [Fix] Ensure it works with the ES2015 preset too (#12, #16)
- [Deps] update `babel-template`, `babel-types`
- [Dev Deps] update `babel-cli`, `babel-core`, `babel-eslint`, `babel-register`, `eslint`, `eslint-config-airbnb-base`, `mocha`

## v1.0.1
- [Fix] Move `in-publish` to devDeps (#11)
- [Fix] ensure dynamic `import()` input is properly stringified (#2)
- [Fix] async timing of dynamic import to match spec (#3)
- [Fix] Remove spaces in template strings and update Babel (#10)
- [Deps] update `babel-template`, `babel-types`
- [Deps] update `babel-types` (#4, #5, #6)
- [Dev Deps] update `babel-cli`, `babel-core`, `babel-eslint`, `babel-register`, `eslint`, `eslint-config-airbnb-base`, `eslint-plugin-import`, `mocha`, `rimraf`

## v1.0.0
- Initial full release.
