{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748540171931}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "viewMode", "loading", "search", "keyword", "type", "status", "treeData", "originalData", "dialogFormVisible", "dialogTitle", "parentOptions", "ruleForm", "id", "label", "code", "parent_id", "sort", "description", "path", "icon", "rules", "required", "message", "trigger", "treeProps", "children", "cascaderProps", "value", "checkStrictly", "computed", "tableData", "flattenTreeForTable", "mounted", "getData", "methods", "setTimeout", "mockData", "create_time", "JSON", "parse", "stringify", "updateParentOptions", "tree", "level", "result", "for<PERSON>ach", "node", "flatNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "push", "buildCascaderOptions", "map", "searchData", "clearSearch", "refulsh", "$router", "go", "editData", "permission", "findPermissionById", "<PERSON><PERSON><PERSON><PERSON>", "parentData", "found", "delData", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$message", "success", "catch", "info", "saveData", "$refs", "validate", "valid", "changeStatus", "row", "getNodeIcon", "iconMap", "menu", "action", "getTypeColor", "colorMap", "getTypeLabel", "labelMap"], "sources": ["src/views/pages/yuangong/quanxian.vue"], "sourcesContent": ["<template>\r\n  <div class=\"permission-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-key\"></i>\r\n            权限管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增权限\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入权限名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n            \r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限类型</label>\r\n              <el-select\r\n                v-model=\"search.type\"\r\n                placeholder=\"请选择权限类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 权限树形结构 -->\r\n    <div class=\"tree-section\">\r\n      <el-card shadow=\"never\" class=\"tree-card\">\r\n        <div class=\"tree-header\">\r\n          <div class=\"tree-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            权限树形结构\r\n          </div>\r\n          <div class=\"tree-tools\">\r\n            <el-button-group>\r\n              <el-button \r\n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'tree'\"\r\n                size=\"small\"\r\n              >\r\n                树形视图\r\n              </el-button>\r\n              <el-button \r\n                :type=\"viewMode === 'table' ? 'primary' : ''\" \r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 树形视图 -->\r\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\r\n          <el-tree\r\n            :data=\"treeData\"\r\n            :props=\"treeProps\"\r\n            :default-expand-all=\"true\"\r\n            node-key=\"id\"\r\n            class=\"permission-tree\"\r\n          >\r\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\r\n              <div class=\"node-content\">\r\n                <div class=\"node-info\">\r\n                  <i :class=\"getNodeIcon(data.type)\"></i>\r\n                  <span class=\"node-label\">{{ data.label }}</span>\r\n                  <el-tag \r\n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \r\n                    size=\"mini\"\r\n                    class=\"node-status\"\r\n                  >\r\n                    {{ data.status === 1 ? '启用' : '禁用' }}\r\n                  </el-tag>\r\n                </div>\r\n                <div class=\"node-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(data.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click=\"addChild(data)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(data.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </span>\r\n          </el-tree>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"tableData\"\r\n            v-loading=\"loading\"\r\n            class=\"permission-table\"\r\n            stripe\r\n            row-key=\"id\"\r\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n            :default-expand-all=\"false\"\r\n          >\r\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\r\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\r\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  :type=\"getTypeColor(scope.row.type)\"\r\n                  size=\"small\"\r\n                >\r\n                  {{ getTypeLabel(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"addChild(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                    v-if=\"scope.row.type === 'menu'\"\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"delData(scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 编辑权限对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"60%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限名称\" prop=\"label\">\r\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限代码\" prop=\"code\">\r\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限类型\" prop=\"type\">\r\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"父级权限\">\r\n              <el-cascader\r\n                v-model=\"ruleForm.parent_id\"\r\n                :options=\"parentOptions\"\r\n                :props=\"cascaderProps\"\r\n                placeholder=\"请选择父级权限\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              ></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-switch\r\n                v-model=\"ruleForm.status\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"启用\"\r\n                inactive-text=\"禁用\"\r\n              >\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"权限描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.description\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入权限描述\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\r\n            <template slot=\"prepend\">\r\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\r\n            </template>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PermissionManagement\",\r\n  data() {\r\n    return {\r\n      viewMode: 'tree', // tree | table\r\n      loading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      },\r\n      treeData: [],\r\n      originalData: [], // 保存原始数据\r\n      dialogFormVisible: false,\r\n      dialogTitle: \"新增权限\",\r\n      parentOptions: [],\r\n      ruleForm: {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"menu\",\r\n        parent_id: null,\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      },\r\n      rules: {\r\n        label: [\r\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\r\n        ],\r\n        code: [\r\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\r\n        ],\r\n        type: [\r\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      cascaderProps: {\r\n        value: 'id',\r\n        label: 'label',\r\n        children: 'children',\r\n        checkStrictly: true\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 表格数据 - 将树形数据扁平化但保持层级关系\r\n    tableData() {\r\n      return this.flattenTreeForTable(this.treeData);\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限数据\r\n    getData() {\r\n      this.loading = true;\r\n      \r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        this.loading = false;\r\n        \r\n        const mockData = [\r\n          {\r\n            id: 1,\r\n            label: \"系统管理\",\r\n            code: \"system\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 1,\r\n            status: 1,\r\n            description: \"系统管理模块\",\r\n            path: \"/system\",\r\n            icon: \"el-icon-setting\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 11,\r\n                label: \"用户管理\",\r\n                code: \"system:user\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"用户管理功能\",\r\n                path: \"/user\",\r\n                icon: \"el-icon-user\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 111,\r\n                    label: \"查看用户\",\r\n                    code: \"system:user:view\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看用户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 112,\r\n                    label: \"新增用户\",\r\n                    code: \"system:user:add\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 113,\r\n                    label: \"编辑用户\",\r\n                    code: \"system:user:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑用户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 114,\r\n                    label: \"删除用户\",\r\n                    code: \"system:user:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 12,\r\n                label: \"职位管理\",\r\n                code: \"system:position\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"职位管理功能\",\r\n                path: \"/zhiwei\",\r\n                icon: \"el-icon-postcard\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 121,\r\n                    label: \"查看职位\",\r\n                    code: \"system:position:view\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看职位列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 122,\r\n                    label: \"新增职位\",\r\n                    code: \"system:position:add\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 123,\r\n                    label: \"编辑职位\",\r\n                    code: \"system:position:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑职位信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 124,\r\n                    label: \"删除职位\",\r\n                    code: \"system:position:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 13,\r\n                label: \"权限管理\",\r\n                code: \"system:permission\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"权限管理功能\",\r\n                path: \"/quanxian\",\r\n                icon: \"el-icon-key\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 131,\r\n                    label: \"查看权限\",\r\n                    code: \"system:permission:view\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看权限列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 132,\r\n                    label: \"新增权限\",\r\n                    code: \"system:permission:add\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 133,\r\n                    label: \"编辑权限\",\r\n                    code: \"system:permission:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑权限信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 134,\r\n                    label: \"删除权限\",\r\n                    code: \"system:permission:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            label: \"业务管理\",\r\n            code: \"business\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 2,\r\n            status: 1,\r\n            description: \"业务管理模块\",\r\n            path: \"/business\",\r\n            icon: \"el-icon-suitcase\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 21,\r\n                label: \"订单管理\",\r\n                code: \"business:order\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"订单管理功能\",\r\n                path: \"/dingdan\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 211,\r\n                    label: \"查看订单\",\r\n                    code: \"business:order:view\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看订单列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 212,\r\n                    label: \"新增订单\",\r\n                    code: \"business:order:add\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 213,\r\n                    label: \"编辑订单\",\r\n                    code: \"business:order:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑订单信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 214,\r\n                    label: \"删除订单\",\r\n                    code: \"business:order:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 215,\r\n                    label: \"导出订单\",\r\n                    code: \"business:order:export\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"导出订单数据\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 22,\r\n                label: \"客户管理\",\r\n                code: \"business:customer\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"客户管理功能\",\r\n                path: \"/customer\",\r\n                icon: \"el-icon-user-solid\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 221,\r\n                    label: \"查看客户\",\r\n                    code: \"business:customer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看客户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 222,\r\n                    label: \"新增客户\",\r\n                    code: \"business:customer:add\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 223,\r\n                    label: \"编辑客户\",\r\n                    code: \"business:customer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑客户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 224,\r\n                    label: \"删除客户\",\r\n                    code: \"business:customer:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            label: \"文书管理\",\r\n            code: \"document\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 3,\r\n            status: 1,\r\n            description: \"文书管理模块\",\r\n            path: \"/document\",\r\n            icon: \"el-icon-document-copy\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 31,\r\n                label: \"合同管理\",\r\n                code: \"document:contract\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"合同管理功能\",\r\n                path: \"/hetong\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 311,\r\n                    label: \"查看合同\",\r\n                    code: \"document:contract:view\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看合同列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 312,\r\n                    label: \"新增合同\",\r\n                    code: \"document:contract:add\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 313,\r\n                    label: \"编辑合同\",\r\n                    code: \"document:contract:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑合同信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 314,\r\n                    label: \"删除合同\",\r\n                    code: \"document:contract:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 315,\r\n                    label: \"审核合同\",\r\n                    code: \"document:contract:audit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"审核合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 32,\r\n                label: \"律师函管理\",\r\n                code: \"document:lawyer\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"律师函管理功能\",\r\n                path: \"/lawyer\",\r\n                icon: \"el-icon-message\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 321,\r\n                    label: \"查看律师函\",\r\n                    code: \"document:lawyer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看律师函列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 322,\r\n                    label: \"发送律师函\",\r\n                    code: \"document:lawyer:send\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"发送律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 323,\r\n                    label: \"编辑律师函\",\r\n                    code: \"document:lawyer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 33,\r\n                label: \"课程管理\",\r\n                code: \"document:course\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"课程管理功能\",\r\n                path: \"/kecheng\",\r\n                icon: \"el-icon-video-play\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 331,\r\n                    label: \"查看课程\",\r\n                    code: \"document:course:view\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看课程列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 332,\r\n                    label: \"新增课程\",\r\n                    code: \"document:course:add\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 333,\r\n                    label: \"编辑课程\",\r\n                    code: \"document:course:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 334,\r\n                    label: \"删除课程\",\r\n                    code: \"document:course:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            label: \"财务管理\",\r\n            code: \"finance\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 4,\r\n            status: 1,\r\n            description: \"财务管理模块\",\r\n            path: \"/finance\",\r\n            icon: \"el-icon-coin\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 41,\r\n                label: \"支付管理\",\r\n                code: \"finance:payment\",\r\n                type: \"menu\",\r\n                parent_id: 4,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"支付管理功能\",\r\n                path: \"/order\",\r\n                icon: \"el-icon-money\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 411,\r\n                    label: \"查看支付记录\",\r\n                    code: \"finance:payment:view\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看支付记录\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 412,\r\n                    label: \"处理退款\",\r\n                    code: \"finance:payment:refund\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"处理退款申请\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 413,\r\n                    label: \"导出财务报表\",\r\n                    code: \"finance:payment:export\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"导出财务报表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          }\r\n        ];\r\n        \r\n        this.treeData = mockData;\r\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\r\n        this.updateParentOptions();\r\n      }, 500);\r\n    },\r\n\r\n    // 将树形数据扁平化用于表格显示（保持层级结构）\r\n    flattenTreeForTable(tree, level = 0, result = []) {\r\n      tree.forEach(node => {\r\n        const flatNode = {\r\n          ...node,\r\n          level: level,\r\n          hasChildren: node.children && node.children.length > 0\r\n        };\r\n        // 移除children属性避免表格渲染问题\r\n        delete flatNode.children;\r\n        result.push(flatNode);\r\n\r\n        // 递归处理子节点\r\n        if (node.children && node.children.length > 0) {\r\n          this.flattenTreeForTable(node.children, level + 1, result);\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n\r\n    // 更新父级权限选项\r\n    updateParentOptions() {\r\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\r\n    },\r\n\r\n    // 构建级联选择器选项\r\n    buildCascaderOptions(tree) {\r\n      return tree.map(node => ({\r\n        id: node.id,\r\n        label: node.label,\r\n        children: node.children ? this.buildCascaderOptions(node.children) : []\r\n      }));\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 刷新页面\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n\r\n    // 编辑权限\r\n    editData(id) {\r\n      if (id === 0) {\r\n        this.dialogTitle = \"新增权限\";\r\n        this.ruleForm = {\r\n          id: null,\r\n          label: \"\",\r\n          code: \"\",\r\n          type: \"menu\",\r\n          parent_id: null,\r\n          sort: 0,\r\n          status: 1,\r\n          description: \"\",\r\n          path: \"\",\r\n          icon: \"\"\r\n        };\r\n      } else {\r\n        this.dialogTitle = \"编辑权限\";\r\n        const permission = this.findPermissionById(id);\r\n        if (permission) {\r\n          this.ruleForm = { ...permission };\r\n        }\r\n      }\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 添加子权限\r\n    addChild(parentData) {\r\n      this.dialogTitle = \"新增子权限\";\r\n      this.ruleForm = {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"action\",\r\n        parent_id: [parentData.id],\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      };\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 根据ID查找权限（在原始数据中查找）\r\n    findPermissionById(id, tree = this.originalData) {\r\n      for (let node of tree) {\r\n        if (node.id === id) {\r\n          return node;\r\n        }\r\n        if (node.children) {\r\n          const found = this.findPermissionById(id, node.children);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 删除权限\r\n    delData(id) {\r\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        // 模拟删除\r\n        this.$message.success(\"删除成功！\");\r\n        this.getData();\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n\r\n    // 保存权限\r\n    saveData() {\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存\r\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\r\n          this.dialogFormVisible = false;\r\n          this.getData();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 改变状态\r\n    changeStatus(row) {\r\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 获取节点图标\r\n    getNodeIcon(type) {\r\n      const iconMap = {\r\n        menu: 'el-icon-menu',\r\n        action: 'el-icon-setting',\r\n        data: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || 'el-icon-menu';\r\n    },\r\n\r\n    // 获取类型颜色\r\n    getTypeColor(type) {\r\n      const colorMap = {\r\n        menu: 'primary',\r\n        action: 'success',\r\n        data: 'warning'\r\n      };\r\n      return colorMap[type] || 'primary';\r\n    },\r\n\r\n    // 获取类型标签\r\n    getTypeLabel(type) {\r\n      const labelMap = {\r\n        menu: '菜单权限',\r\n        action: '操作权限',\r\n        data: '数据权限'\r\n      };\r\n      return labelMap[type] || '菜单权限';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 权限管理容器 */\r\n.permission-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 树形结构区域 */\r\n.tree-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tree-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tree-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tree-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.tree-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 树形视图样式 */\r\n.tree-view {\r\n  padding: 24px;\r\n}\r\n\r\n.permission-tree {\r\n  background: transparent;\r\n}\r\n\r\n.tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.node-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n}\r\n\r\n.node-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.node-label {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.node-status {\r\n  margin-left: 8px;\r\n}\r\n\r\n.node-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.permission-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.permission-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.permission-name {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 4px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 2px;\r\n}\r\n\r\n/* 表格行层级样式 */\r\n.permission-table .el-table__row[data-level=\"0\"] {\r\n  background-color: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"1\"] {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"2\"] {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 对话框样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .permission-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .tree-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .node-content {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n\r\n  .node-actions {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA2WA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,OAAA;MACAC,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACAC,QAAA;MACAC,YAAA;MAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,aAAA;MACAC,QAAA;QACAC,EAAA;QACAC,KAAA;QACAC,IAAA;QACAV,IAAA;QACAW,SAAA;QACAC,IAAA;QACAX,MAAA;QACAY,WAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAP,KAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,IAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,IAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,SAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACAa,aAAA;QACAC,KAAA;QACAd,KAAA;QACAY,QAAA;QACAG,aAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA;MACA,YAAAC,mBAAA,MAAAzB,QAAA;IACA;EACA;EACA0B,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,QAAA;MACA,KAAAhC,OAAA;;MAEA;MACAkC,UAAA;QACA,KAAAlC,OAAA;QAEA,MAAAmC,QAAA,IACA;UACAxB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA,GACA;YACAzB,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,GACA;UACAzB,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;UACAkB,WAAA;UACAZ,QAAA,GACA;YACAb,EAAA;YACAC,KAAA;YACAC,IAAA;YACAV,IAAA;YACAW,SAAA;YACAC,IAAA;YACAX,MAAA;YACAY,WAAA;YACAC,IAAA;YACAC,IAAA;YACAkB,WAAA;YACAZ,QAAA,GACA;cACAb,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA,GACA;cACAzB,EAAA;cACAC,KAAA;cACAC,IAAA;cACAV,IAAA;cACAW,SAAA;cACAC,IAAA;cACAX,MAAA;cACAY,WAAA;cACAoB,WAAA;YACA;UAEA;QAEA,EACA;QAEA,KAAA/B,QAAA,GAAA8B,QAAA;QACA,KAAA7B,YAAA,GAAA+B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,QAAA;QACA,KAAAK,mBAAA;MACA;IACA;IAEA;IACAV,oBAAAW,IAAA,EAAAC,KAAA,MAAAC,MAAA;MACAF,IAAA,CAAAG,OAAA,CAAAC,IAAA;QACA,MAAAC,QAAA;UACA,GAAAD,IAAA;UACAH,KAAA,EAAAA,KAAA;UACAK,WAAA,EAAAF,IAAA,CAAArB,QAAA,IAAAqB,IAAA,CAAArB,QAAA,CAAAwB,MAAA;QACA;QACA;QACA,OAAAF,QAAA,CAAAtB,QAAA;QACAmB,MAAA,CAAAM,IAAA,CAAAH,QAAA;;QAEA;QACA,IAAAD,IAAA,CAAArB,QAAA,IAAAqB,IAAA,CAAArB,QAAA,CAAAwB,MAAA;UACA,KAAAlB,mBAAA,CAAAe,IAAA,CAAArB,QAAA,EAAAkB,KAAA,MAAAC,MAAA;QACA;MACA;MACA,OAAAA,MAAA;IACA;IAEA;IACAH,oBAAA;MACA,KAAA/B,aAAA,QAAAyC,oBAAA,MAAA7C,QAAA;IACA;IAEA;IACA6C,qBAAAT,IAAA;MACA,OAAAA,IAAA,CAAAU,GAAA,CAAAN,IAAA;QACAlC,EAAA,EAAAkC,IAAA,CAAAlC,EAAA;QACAC,KAAA,EAAAiC,IAAA,CAAAjC,KAAA;QACAY,QAAA,EAAAqB,IAAA,CAAArB,QAAA,QAAA0B,oBAAA,CAAAL,IAAA,CAAArB,QAAA;MACA;IACA;IAEA;IACA4B,WAAA;MACA,KAAApB,OAAA;IACA;IAEA;IACAqB,YAAA;MACA,KAAApD,MAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA;MACA,KAAAgD,UAAA;IACA;IAEA;IACAE,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,SAAA9C,EAAA;MACA,IAAAA,EAAA;QACA,KAAAH,WAAA;QACA,KAAAE,QAAA;UACAC,EAAA;UACAC,KAAA;UACAC,IAAA;UACAV,IAAA;UACAW,SAAA;UACAC,IAAA;UACAX,MAAA;UACAY,WAAA;UACAC,IAAA;UACAC,IAAA;QACA;MACA;QACA,KAAAV,WAAA;QACA,MAAAkD,UAAA,QAAAC,kBAAA,CAAAhD,EAAA;QACA,IAAA+C,UAAA;UACA,KAAAhD,QAAA;YAAA,GAAAgD;UAAA;QACA;MACA;MACA,KAAAnD,iBAAA;IACA;IAEA;IACAqD,SAAAC,UAAA;MACA,KAAArD,WAAA;MACA,KAAAE,QAAA;QACAC,EAAA;QACAC,KAAA;QACAC,IAAA;QACAV,IAAA;QACAW,SAAA,GAAA+C,UAAA,CAAAlD,EAAA;QACAI,IAAA;QACAX,MAAA;QACAY,WAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACA,KAAAX,iBAAA;IACA;IAEA;IACAoD,mBAAAhD,EAAA,EAAA8B,IAAA,QAAAnC,YAAA;MACA,SAAAuC,IAAA,IAAAJ,IAAA;QACA,IAAAI,IAAA,CAAAlC,EAAA,KAAAA,EAAA;UACA,OAAAkC,IAAA;QACA;QACA,IAAAA,IAAA,CAAArB,QAAA;UACA,MAAAsC,KAAA,QAAAH,kBAAA,CAAAhD,EAAA,EAAAkC,IAAA,CAAArB,QAAA;UACA,IAAAsC,KAAA,SAAAA,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAC,QAAApD,EAAA;MACA,KAAAqD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/D,IAAA;MACA,GAAAgE,IAAA;QACA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAArC,OAAA;MACA,GAAAsC,KAAA;QACA,KAAAF,QAAA,CAAAG,IAAA;MACA;IACA;IAEA;IACAC,SAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAP,QAAA,CAAAC,OAAA,MAAA3D,QAAA,CAAAC,EAAA;UACA,KAAAJ,iBAAA;UACA,KAAAyB,OAAA;QACA;MACA;IACA;IAEA;IACA4C,aAAAC,GAAA;MACA,KAAAT,QAAA,CAAAC,OAAA,SAAAQ,GAAA,CAAAzE,MAAA;IACA;IAEA;IACA0E,YAAA3E,IAAA;MACA,MAAA4E,OAAA;QACAC,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAiF,OAAA,CAAA5E,IAAA;IACA;IAEA;IACA+E,aAAA/E,IAAA;MACA,MAAAgF,QAAA;QACAH,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAqF,QAAA,CAAAhF,IAAA;IACA;IAEA;IACAiF,aAAAjF,IAAA;MACA,MAAAkF,QAAA;QACAL,IAAA;QACAC,MAAA;QACAnF,IAAA;MACA;MACA,OAAAuF,QAAA,CAAAlF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}