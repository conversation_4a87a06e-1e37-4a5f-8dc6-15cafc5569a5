{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue", "mtime": 1748442914242}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dingzhi.vue"], "names": [], "mappings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file": "dingzhi.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-custom-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">{{ this.$router.currentRoute.name }}</h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">处理状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"150\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"200\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.is_deal == 2 ? 'success' : scope.row.is_deal == 1 ? 'warning' : 'info'\"\r\n              size=\"small\"\r\n            >\r\n              {{ scope.row.is_deal == 2 ? '已处理' : scope.row.is_deal == 1 ? '处理中' : '待处理' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"160\">\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- AI生成按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.type === '合同定制'\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"generateAIContract(scope.row)\"\r\n                icon=\"el-icon-magic-stick\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                AI生成\r\n              </el-button>\r\n\r\n              <!-- 完成制作按钮 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-check\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                完成制作\r\n              </el-button>\r\n\r\n              <!-- 提交审核按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.is_deal === 2\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitForReview(scope.row)\"\r\n                icon=\"el-icon-upload\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                提交审核\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-close\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同要求\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- AI生成合同对话框 -->\r\n    <el-dialog\r\n      title=\"AI生成合同\"\r\n      :visible.sync=\"dialogAIGenerate\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"ai-generate-dialog\"\r\n    >\r\n      <div class=\"ai-generate-content\">\r\n        <!-- 工单信息展示 -->\r\n        <div class=\"order-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            工单信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>工单标题：</label>\r\n              <span>{{ currentOrder.title }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>工单内容：</label>\r\n              <span>{{ currentOrder.desc }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>合同类型：</label>\r\n              <span>{{ matchedContractType.title || '未匹配到合同类型' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            用户信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>用户姓名：</label>\r\n              <span>{{ currentUserInfo.nickname }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>联系电话：</label>\r\n              <span>{{ currentUserInfo.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>身份证号：</label>\r\n              <span>{{ currentUserInfo.id_card || '未填写' }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>地址：</label>\r\n              <span>{{ currentUserInfo.address || '未填写' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息展示 -->\r\n        <div class=\"template-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document-copy\"></i>\r\n            合同模板\r\n          </h3>\r\n          <div v-if=\"matchedContractType.template_file\" class=\"template-info\">\r\n            <div class=\"template-file\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ matchedContractType.template_name }}</span>\r\n              <el-tag type=\"success\" size=\"mini\">已找到模板</el-tag>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"no-template\">\r\n            <el-alert\r\n              title=\"未找到对应的合同模板\"\r\n              type=\"warning\"\r\n              description=\"请先在合同类型管理中为该类型上传模板文件\"\r\n              show-icon\r\n              :closable=\"false\">\r\n            </el-alert>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- AI生成进度 -->\r\n        <div v-if=\"aiGenerating\" class=\"ai-progress-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            AI生成中...\r\n          </h3>\r\n          <el-progress\r\n            :percentage=\"aiProgress\"\r\n            :status=\"aiProgress === 100 ? 'success' : null\"\r\n            :stroke-width=\"8\"\r\n          >\r\n          </el-progress>\r\n          <p class=\"progress-text\">{{ aiProgressText }}</p>\r\n        </div>\r\n\r\n        <!-- 生成结果 -->\r\n        <div v-if=\"generatedContract\" class=\"result-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-check\"></i>\r\n            生成结果\r\n          </h3>\r\n          <div class=\"contract-preview\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"generatedContract\"\r\n              :rows=\"15\"\r\n              placeholder=\"AI生成的合同内容将显示在这里...\"\r\n              class=\"contract-content\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogAIGenerate = false\">取消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"startAIGeneration\"\r\n          :loading=\"aiGenerating\"\r\n          :disabled=\"!matchedContractType.template_file\"\r\n        >\r\n          {{ aiGenerating ? 'AI生成中...' : '开始AI生成' }}\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"generatedContract\"\r\n          type=\"success\"\r\n          @click=\"saveGeneratedContract\"\r\n        >\r\n          保存合同\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/dingzhi/\",\r\n      title: \"合同定制\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogAIGenerate: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      // AI生成相关数据\r\n      currentOrder: {},\r\n      currentUserInfo: {},\r\n      matchedContractType: {},\r\n      contractTypes: [], // 合同类型列表\r\n      aiGenerating: false,\r\n      aiProgress: 0,\r\n      aiProgressText: '',\r\n      generatedContract: '',\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      console.log('viewUserData 被调用，传入的 ID:', id);\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n        console.log('设置 currentId 为:', this.currentId);\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n      console.log('打开用户详情抽屉');\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      console.log('getInfo 被调用，ID:', id);\r\n\r\n      // 使用测试数据，因为API可能不可用\r\n      setTimeout(() => {\r\n        // 从列表数据中找到对应的项目\r\n        const item = _this.list.find(item => item.id === id);\r\n        if (item) {\r\n          _this.ruleForm = {\r\n            id: item.id,\r\n            title: item.title,\r\n            desc: item.desc,\r\n            is_deal: item.is_deal,\r\n            type: item.type === \"合同定制\" ? 1 : 2,\r\n            content: \"\",\r\n            file_path: \"\"\r\n          };\r\n          console.log('设置表单数据:', _this.ruleForm);\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: \"未找到对应的数据\",\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n      // 原始API调用（注释掉）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"WD202403001\",\r\n            type: \"合同定制\",\r\n            title: \"劳动合同定制\",\r\n            desc: \"需要定制一份标准的劳动合同模板，包含薪资、工作时间、福利待遇等条款\",\r\n            is_deal: 0,\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"WD202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"请帮忙审核房屋租赁合同，检查条款是否合理，有无法律风险\",\r\n            is_deal: 1,\r\n            nickname: \"李四\",\r\n            phone: \"13800138002\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"WD202403003\",\r\n            type: \"合同定制\",\r\n            title: \"买卖合同定制\",\r\n            desc: \"需要定制商品买卖合同，涉及货物交付、付款方式、违约责任等\",\r\n            is_deal: 2,\r\n            nickname: \"王五\",\r\n            phone: \"13800138003\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"WD202403004\",\r\n            type: \"法律咨询\",\r\n            title: \"服务合同咨询\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            is_deal: 1,\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"WD202403005\",\r\n            type: \"合同定制\",\r\n            title: \"借款合同定制\",\r\n            desc: \"需要定制个人借款合同，明确借款金额、利率、还款方式等条款\",\r\n            is_deal: 0,\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成合同\r\n    generateAIContract(orderData) {\r\n      console.log('开始AI生成合同，工单数据:', orderData);\r\n\r\n      // 设置当前工单数据\r\n      this.currentOrder = orderData;\r\n\r\n      // 根据工单标题匹配合同类型\r\n      this.matchContractType(orderData.title);\r\n\r\n      // 获取用户详情信息\r\n      this.getUserInfo(orderData.uid);\r\n\r\n      // 重置AI生成状态\r\n      this.aiGenerating = false;\r\n      this.aiProgress = 0;\r\n      this.aiProgressText = '';\r\n      this.generatedContract = '';\r\n\r\n      // 打开AI生成对话框\r\n      this.dialogAIGenerate = true;\r\n    },\r\n\r\n    // 匹配合同类型\r\n    matchContractType(orderTitle) {\r\n      // 根据工单标题关键词匹配合同类型\r\n      const keywords = {\r\n        '劳动': '劳动合同',\r\n        '租赁': '租赁合同',\r\n        '买卖': '买卖合同',\r\n        '服务': '服务合同',\r\n        '借款': '借款合同'\r\n      };\r\n\r\n      let matchedType = null;\r\n      for (let keyword in keywords) {\r\n        if (orderTitle.includes(keyword)) {\r\n          matchedType = this.contractTypes.find(type => type.title === keywords[keyword]);\r\n          break;\r\n        }\r\n      }\r\n\r\n      this.matchedContractType = matchedType || {};\r\n      console.log('匹配到的合同类型:', this.matchedContractType);\r\n    },\r\n\r\n    // 获取用户信息\r\n    getUserInfo(uid) {\r\n      // 模拟获取用户详情信息\r\n      const userInfoMap = {\r\n        1: {\r\n          nickname: \"张三\",\r\n          phone: \"13800138001\",\r\n          id_card: \"110101199001011234\",\r\n          address: \"北京市朝阳区某某街道123号\"\r\n        },\r\n        2: {\r\n          nickname: \"李四\",\r\n          phone: \"13800138002\",\r\n          id_card: \"110101199002022345\",\r\n          address: \"上海市浦东新区某某路456号\"\r\n        },\r\n        3: {\r\n          nickname: \"王五\",\r\n          phone: \"13800138003\",\r\n          id_card: \"110101199003033456\",\r\n          address: \"广州市天河区某某大道789号\"\r\n        },\r\n        4: {\r\n          nickname: \"赵六\",\r\n          phone: \"13800138004\",\r\n          id_card: \"110101199004044567\",\r\n          address: \"深圳市南山区某某街101号\"\r\n        },\r\n        5: {\r\n          nickname: \"孙七\",\r\n          phone: \"13800138005\",\r\n          id_card: \"110101199005055678\",\r\n          address: \"杭州市西湖区某某路202号\"\r\n        }\r\n      };\r\n\r\n      this.currentUserInfo = userInfoMap[uid] || {\r\n        nickname: \"未知用户\",\r\n        phone: \"\",\r\n        id_card: \"\",\r\n        address: \"\"\r\n      };\r\n\r\n      console.log('获取到的用户信息:', this.currentUserInfo);\r\n    },\r\n\r\n    // 开始AI生成\r\n    startAIGeneration() {\r\n      if (!this.matchedContractType.template_file) {\r\n        this.$message.error('未找到对应的合同模板，无法进行AI生成');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n      this.aiProgress = 0;\r\n      this.generatedContract = '';\r\n\r\n      // 模拟AI生成过程\r\n      this.simulateAIGeneration();\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    simulateAIGeneration() {\r\n      const steps = [\r\n        { progress: 20, text: '正在分析工单内容...' },\r\n        { progress: 40, text: '正在解析合同模板...' },\r\n        { progress: 60, text: '正在整合用户信息...' },\r\n        { progress: 80, text: '正在生成合同条款...' },\r\n        { progress: 100, text: 'AI生成完成！' }\r\n      ];\r\n\r\n      let currentStep = 0;\r\n\r\n      const updateProgress = () => {\r\n        if (currentStep < steps.length) {\r\n          this.aiProgress = steps[currentStep].progress;\r\n          this.aiProgressText = steps[currentStep].text;\r\n          currentStep++;\r\n\r\n          setTimeout(updateProgress, 1000);\r\n        } else {\r\n          // 生成完成，显示模拟的合同内容\r\n          this.generateContractContent();\r\n          this.aiGenerating = false;\r\n        }\r\n      };\r\n\r\n      updateProgress();\r\n    },\r\n\r\n    // 生成合同内容\r\n    generateContractContent() {\r\n      const contractTemplate = `${this.matchedContractType.title}\r\n\r\n甲方（委托方）：${this.currentUserInfo.nickname}\r\n身份证号：${this.currentUserInfo.id_card}\r\n联系电话：${this.currentUserInfo.phone}\r\n地址：${this.currentUserInfo.address}\r\n\r\n乙方（受托方）：[待填写]\r\n\r\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就以下事项达成一致，签订本合同：\r\n\r\n一、合同内容\r\n${this.currentOrder.desc}\r\n\r\n二、合同条款\r\n[根据AI分析生成的具体条款内容]\r\n\r\n1. 权利义务\r\n   甲方权利：[根据合同类型和用户需求生成]\r\n   甲方义务：[根据合同类型和用户需求生成]\r\n   乙方权利：[根据合同类型和用户需求生成]\r\n   乙方义务：[根据合同类型和用户需求生成]\r\n\r\n2. 履行期限\r\n   [根据工单内容分析生成具体期限]\r\n\r\n3. 违约责任\r\n   [根据合同类型生成标准违约条款]\r\n\r\n4. 争议解决\r\n   因履行本合同发生的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉。\r\n\r\n5. 其他约定\r\n   [根据具体需求生成其他条款]\r\n\r\n三、合同生效\r\n本合同自双方签字（盖章）之日起生效。\r\n\r\n甲方签字：_________________ 日期：_________________\r\n\r\n乙方签字：_________________ 日期：_________________\r\n\r\n---\r\n本合同由AI智能生成，请仔细核对内容后使用。\r\n生成时间：${new Date().toLocaleString()}\r\n工单号：${this.currentOrder.order_sn}`;\r\n\r\n      this.generatedContract = contractTemplate;\r\n      this.$message.success('AI合同生成完成！');\r\n    },\r\n\r\n    // 保存生成的合同\r\n    saveGeneratedContract() {\r\n      if (!this.generatedContract) {\r\n        this.$message.warning('没有可保存的合同内容');\r\n        return;\r\n      }\r\n\r\n      // 这里可以调用API保存合同内容\r\n      // 模拟保存过程\r\n      this.$message.success('合同保存成功！');\r\n\r\n      // 更新工单状态为处理中\r\n      const orderIndex = this.list.findIndex(item => item.id === this.currentOrder.id);\r\n      if (orderIndex !== -1) {\r\n        this.list[orderIndex].is_deal = 1; // 设置为处理中\r\n      }\r\n\r\n      // 关闭对话框\r\n      this.dialogAIGenerate = false;\r\n    },\r\n\r\n    // 提交审核\r\n    submitForReview(row) {\r\n      console.log('提交审核:', row);\r\n\r\n      this.$confirm('确认将此合同提交审核？提交后将进入审核流程。', '提示', {\r\n        confirmButtonText: '确定提交',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交审核过程\r\n        setTimeout(() => {\r\n          // 更新工单状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            // 添加审核相关字段\r\n            this.list[index] = {\r\n              ...this.list[index],\r\n              review_status: 'submitted', // 已提交审核\r\n              submit_time: new Date().toLocaleString(),\r\n              review_step: 'mediator_review' // 下一步：调解员审核\r\n            };\r\n          }\r\n\r\n          this.$message.success('合同已成功提交审核！将进入审核流程。');\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-custom-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 抽屉动画优化 */\r\n.user-detail-drawer >>> .el-drawer__container {\r\n  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\r\n}\r\n\r\n/* 操作按钮纵向排列 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  width: 80px;\r\n  margin: 0 !important;\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-custom-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* AI生成对话框样式 */\r\n.ai-generate-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.ai-generate-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.order-info-section,\r\n.user-info-section,\r\n.template-info-section,\r\n.ai-progress-section,\r\n.result-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.template-info {\r\n  padding: 16px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.template-file {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.template-file i {\r\n  color: #67c23a;\r\n  font-size: 20px;\r\n}\r\n\r\n.template-file span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.no-template {\r\n  padding: 16px;\r\n}\r\n\r\n.ai-progress-section {\r\n  border-left-color: #e6a23c;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  margin-top: 12px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-section {\r\n  border-left-color: #67c23a;\r\n}\r\n\r\n.contract-preview {\r\n  background: white;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.contract-content >>> .el-textarea__inner {\r\n  font-family: 'Courier New', monospace;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .ai-generate-content {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .info-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n"]}]}