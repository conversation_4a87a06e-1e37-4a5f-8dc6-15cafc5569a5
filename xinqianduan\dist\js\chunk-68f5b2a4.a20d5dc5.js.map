{"version": 3, "sources": ["webpack:///./src/views/pages/data/configs.vue?b908", "webpack:///./src/views/pages/data/configs.vue", "webpack:///src/views/pages/data/configs.vue", "webpack:///./src/views/pages/data/configs.vue?4f59", "webpack:///./src/views/pages/data/configs.vue?8c7a"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "attrs", "on", "handleClick", "model", "value", "activeName", "callback", "$$v", "expression", "ref", "ruleForm", "site_name", "$set", "company_name", "site_tel", "email", "site_address", "site_icp", "site_icp_url", "site_logo", "$event", "changeFiled", "handleSuccess", "beforeUpload", "showImage", "_e", "delImage", "staticStyle", "lvshi", "_l", "item", "index", "key", "title", "id", "my_title", "my_desc", "about_path", "useTemplate", "previewPrivacy", "resetPrivacy", "_s", "privacyWordCount", "privacyEditMode", "isClear", "onPrivacyChange", "yinsi", "onPrivacyTextChange", "yinsi_text", "insertText", "useAboutTemplate", "previewAbout", "resetAbout", "aboutWordCount", "aboutEditMode", "onAboutChange", "index_about_content", "onAboutTextChange", "about_text", "insertAboutText", "useTeamTemplate", "previewTeam", "resetTeam", "teamWordCount", "teamEditMode", "onTeamChange", "index_team_content", "onTeamTextChange", "team_text", "insertTeamText", "fullscreenLoading", "saveData", "dialogVisible", "show_image", "previewDialogVisible", "domProps", "slot", "aboutPreviewDialogVisible", "teamPreviewDialogVisible", "staticRenderFns", "name", "components", "EditorBar", "data", "url", "filedName", "privacyTemplate", "computed", "text", "replace", "length", "mounted", "console", "log", "methods", "getList", "fileName", "change", "getAllData", "res", "$message", "success", "file", "isTypeTrue", "test", "type", "info", "error", "$confirm", "confirmButtonText", "cancelButtonText", "then", "template", "Date", "toLocaleDateString", "val", "insertValue", "_this", "setTimeout", "message", "component"], "mappings": "gHAAA,W,gECAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACI,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,YAAYP,EAAIQ,aAAaC,MAAM,CAACC,MAAOV,EAAIW,WAAYC,SAAS,SAAUC,GAAMb,EAAIW,WAAWE,GAAKC,WAAW,eAAe,CAACZ,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACa,IAAI,WAAWT,MAAM,CAAC,MAAQN,EAAIgB,SAAS,cAAc,UAAU,CAACd,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASC,UAAWL,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,YAAaH,IAAMC,WAAW,yBAAyB,IAAI,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASG,aAAcP,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,eAAgBH,IAAMC,WAAW,4BAA4B,IAAI,IAAI,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASI,SAAUR,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,WAAYH,IAAMC,WAAW,wBAAwB,IAAI,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASK,MAAOT,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,QAASH,IAAMC,WAAW,qBAAqB,IAAI,IAAI,GAAGZ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASM,aAAcV,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,eAAgBH,IAAMC,WAAW,4BAA4B,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASO,SAAUX,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,WAAYH,IAAMC,WAAW,wBAAwB,IAAI,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASQ,aAAcZ,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,eAAgBH,IAAMC,WAAW,4BAA4B,IAAI,IAAI,GAAGZ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,WAAW,CAACI,MAAM,CAAC,UAAW,EAAK,YAAc,aAAaG,MAAM,CAACC,MAAOV,EAAIgB,SAASS,UAAWb,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,YAAaH,IAAMC,WAAW,wBAAwBZ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2B,YAAY,gBAAgB,CAACzB,EAAG,YAAY,CAACI,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAaN,EAAI4B,cAAc,gBAAgB5B,EAAI6B,eAAe,CAAC7B,EAAIK,GAAG,WAAW,GAAIL,EAAIgB,SAASS,UAAWvB,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI8B,UAAU9B,EAAIgB,SAASS,cAAc,CAACzB,EAAIK,GAAG,SAASL,EAAI+B,KAAM/B,EAAIgB,SAASS,UAAWvB,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIgC,SAAShC,EAAIgB,SAASS,UAAW,gBAAgB,CAACzB,EAAIK,GAAG,QAAQL,EAAI+B,MAAM,IAAI,KAAK7B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,YAAY,CAAC+B,YAAY,CAAC,MAAQ,QAAQ3B,MAAM,CAAC,YAAc,UAAU,WAAa,IAAIG,MAAM,CAACC,MAAOV,EAAIgB,SAASkB,MAAOtB,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,QAASH,IAAMC,WAAW,mBAAmB,CAACZ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,CAACN,EAAIK,GAAG,SAASL,EAAImC,GAAInC,EAAIkC,OAAO,SAASE,EAAKC,GAAO,OAAOnC,EAAG,YAAY,CAACoC,IAAID,EAAM/B,MAAM,CAAC,MAAQ8B,EAAKG,MAAM,MAAQH,EAAKI,UAAS,IAAI,GAAGtC,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAASyB,SAAU7B,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,WAAYH,IAAMC,WAAW,wBAAwB,IAAI,GAAGZ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAG,WAAW,CAACO,MAAM,CAACC,MAAOV,EAAIgB,SAAS0B,QAAS9B,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,UAAWH,IAAMC,WAAW,uBAAuB,IAAI,IAAI,GAAGZ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,WAAW,CAACI,MAAM,CAAC,UAAW,EAAK,YAAc,WAAWG,MAAM,CAACC,MAAOV,EAAIgB,SAAS2B,WAAY/B,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,aAAcH,IAAMC,WAAW,yBAAyBZ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2B,YAAY,iBAAiB,CAACzB,EAAG,YAAY,CAACI,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAaN,EAAI4B,cAAc,gBAAgB5B,EAAI6B,eAAe,CAAC7B,EAAIK,GAAG,WAAW,GAAIL,EAAIgB,SAAS2B,WAAYzC,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI8B,UAAU9B,EAAIgB,SAAS2B,eAAe,CAAC3C,EAAIK,GAAG,SAASL,EAAI+B,KAAM/B,EAAIgB,SAAS2B,WAAYzC,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIgC,SAAShC,EAAIgB,SAAS2B,WAAY,iBAAiB,CAAC3C,EAAIK,GAAG,QAAQL,EAAI+B,MAAM,IAAI,MAAM,IAAI,KAAK7B,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQP,EAAI4C,cAAc,CAAC5C,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBC,GAAG,CAAC,MAAQP,EAAI6C,iBAAiB,CAAC7C,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAI8C,eAAe,CAAC9C,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,MAAML,EAAI+C,GAAG/C,EAAIgD,yBAAyB9C,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,iBAAiB,CAACI,MAAM,CAAC,KAAO,SAASG,MAAM,CAACC,MAAOV,EAAIiD,gBAAiBrC,SAAS,SAAUC,GAAMb,EAAIiD,gBAAgBpC,GAAKC,WAAW,oBAAoB,CAACZ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,WAAWH,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,YAAY,IAAI,GAA4B,SAAxBL,EAAIiD,gBAA4B/C,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,aAAa,CAACI,MAAM,CAAC,QAAUN,EAAIkD,QAAQ,OAAS,KAAK3C,GAAG,CAAC,OAASP,EAAImD,iBAAiB1C,MAAM,CAACC,MAAOV,EAAIgB,SAASoC,MAAOxC,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,QAASH,IAAMC,WAAW,qBAAqB,GAAGZ,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,KAAO,WAAW,KAAO,GAAG,YAAc,gBAAgBC,GAAG,CAAC,MAAQP,EAAIqD,qBAAqB5C,MAAM,CAACC,MAAOV,EAAIgB,SAASsC,WAAY1C,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,aAAcH,IAAMC,WAAW,0BAA0B,GAAGZ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIuD,WAAW,WAAW,CAACvD,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIuD,WAAW,WAAW,CAACvD,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIuD,WAAW,WAAW,CAACvD,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIuD,WAAW,WAAW,CAACvD,EAAIK,GAAG,aAAa,SAASH,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQP,EAAIwD,mBAAmB,CAACxD,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBC,GAAG,CAAC,MAAQP,EAAIyD,eAAe,CAACzD,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAI0D,aAAa,CAAC1D,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,MAAML,EAAI+C,GAAG/C,EAAI2D,uBAAuBzD,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,iBAAiB,CAACI,MAAM,CAAC,KAAO,SAASG,MAAM,CAACC,MAAOV,EAAI4D,cAAehD,SAAS,SAAUC,GAAMb,EAAI4D,cAAc/C,GAAKC,WAAW,kBAAkB,CAACZ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,WAAWH,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,YAAY,IAAI,GAA0B,SAAtBL,EAAI4D,cAA0B1D,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,aAAa,CAACI,MAAM,CAAC,QAAUN,EAAIkD,QAAQ,OAAS,KAAK3C,GAAG,CAAC,OAASP,EAAI6D,eAAepD,MAAM,CAACC,MAAOV,EAAIgB,SAAS8C,oBAAqBlD,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,sBAAuBH,IAAMC,WAAW,mCAAmC,GAAGZ,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,WAAW,CAACE,YAAY,iBAAiBE,MAAM,CAAC,KAAO,WAAW,KAAO,GAAG,YAAc,iBAAiBC,GAAG,CAAC,MAAQP,EAAI+D,mBAAmBtD,MAAM,CAACC,MAAOV,EAAIgB,SAASgD,WAAYpD,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,aAAcH,IAAMC,WAAW,0BAA0B,GAAGZ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIiE,gBAAgB,WAAW,CAACjE,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIiE,gBAAgB,WAAW,CAACjE,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIiE,gBAAgB,WAAW,CAACjE,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAIiE,gBAAgB,WAAW,CAACjE,EAAIK,GAAG,aAAa,SAASH,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQP,EAAIkE,kBAAkB,CAAClE,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBC,GAAG,CAAC,MAAQP,EAAImE,cAAc,CAACnE,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIoE,YAAY,CAACpE,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,MAAML,EAAI+C,GAAG/C,EAAIqE,sBAAsBnE,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,iBAAiB,CAACI,MAAM,CAAC,KAAO,SAASG,MAAM,CAACC,MAAOV,EAAIsE,aAAc1D,SAAS,SAAUC,GAAMb,EAAIsE,aAAazD,GAAKC,WAAW,iBAAiB,CAACZ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,WAAWH,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIK,GAAG,YAAY,IAAI,GAAyB,SAArBL,EAAIsE,aAAyBpE,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,aAAa,CAACI,MAAM,CAAC,QAAUN,EAAIkD,QAAQ,OAAS,KAAK3C,GAAG,CAAC,OAASP,EAAIuE,cAAc9D,MAAM,CAACC,MAAOV,EAAIgB,SAASwD,mBAAoB5D,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,qBAAsBH,IAAMC,WAAW,kCAAkC,GAAGZ,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,WAAW,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,WAAW,KAAO,GAAG,YAAc,gBAAgBC,GAAG,CAAC,MAAQP,EAAIyE,kBAAkBhE,MAAM,CAACC,MAAOV,EAAIgB,SAAS0D,UAAW9D,SAAS,SAAUC,GAAMb,EAAIkB,KAAKlB,EAAIgB,SAAU,YAAaH,IAAMC,WAAW,yBAAyB,GAAGZ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2E,eAAe,WAAW,CAAC3E,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2E,eAAe,WAAW,CAAC3E,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2E,eAAe,WAAW,CAAC3E,EAAIK,GAAG,YAAYH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASmB,GAAQ,OAAO1B,EAAI2E,eAAe,WAAW,CAAC3E,EAAIK,GAAG,aAAa,UAAU,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,SAAS,QAAUN,EAAI4E,mBAAmBrE,GAAG,CAAC,MAAQP,EAAI6E,WAAW,CAAC7E,EAAIK,GAAG,YAAY,KAAKH,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAI8E,cAAc,MAAQ,OAAOvE,GAAG,CAAC,iBAAiB,SAASmB,GAAQ1B,EAAI8E,cAAcpD,KAAU,CAACxB,EAAG,WAAW,CAAC+B,YAAY,CAAC,MAAQ,QAAQ3B,MAAM,CAAC,IAAMN,EAAI+E,eAAe,GAAG7E,EAAG,YAAY,CAACE,YAAY,yBAAyBE,MAAM,CAAC,MAAQ,SAAS,QAAUN,EAAIgF,qBAAqB,MAAQ,OAAOzE,GAAG,CAAC,iBAAiB,SAASmB,GAAQ1B,EAAIgF,qBAAqBtD,KAAU,CAACxB,EAAG,MAAM,CAACE,YAAY,kBAAkB6E,SAAS,CAAC,UAAYjF,EAAI+C,GAAG/C,EAAIgB,SAASoC,UAAUlD,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAU4E,KAAK,UAAU,CAAChF,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAIgF,sBAAuB,KAAS,CAAChF,EAAIK,GAAG,QAAQH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAIgF,sBAAuB,KAAS,CAAChF,EAAIK,GAAG,SAAS,KAAKH,EAAG,YAAY,CAACE,YAAY,uBAAuBE,MAAM,CAAC,MAAQ,SAAS,QAAUN,EAAImF,0BAA0B,MAAQ,OAAO5E,GAAG,CAAC,iBAAiB,SAASmB,GAAQ1B,EAAImF,0BAA0BzD,KAAU,CAACxB,EAAG,MAAM,CAACE,YAAY,kBAAkB6E,SAAS,CAAC,UAAYjF,EAAI+C,GAAG/C,EAAIgB,SAAS8C,wBAAwB5D,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAU4E,KAAK,UAAU,CAAChF,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAImF,2BAA4B,KAAS,CAACnF,EAAIK,GAAG,QAAQH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAImF,2BAA4B,KAAS,CAACnF,EAAIK,GAAG,SAAS,KAAKH,EAAG,YAAY,CAACE,YAAY,sBAAsBE,MAAM,CAAC,MAAQ,SAAS,QAAUN,EAAIoF,yBAAyB,MAAQ,OAAO7E,GAAG,CAAC,iBAAiB,SAASmB,GAAQ1B,EAAIoF,yBAAyB1D,KAAU,CAACxB,EAAG,MAAM,CAACE,YAAY,kBAAkB6E,SAAS,CAAC,UAAYjF,EAAI+C,GAAG/C,EAAIgB,SAASwD,uBAAuBtE,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAU4E,KAAK,UAAU,CAAChF,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAIoF,0BAA2B,KAAS,CAACpF,EAAIK,GAAG,QAAQH,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASmB,GAAQ1B,EAAIoF,0BAA2B,KAAS,CAACpF,EAAIK,GAAG,SAAS,MAAM,IAEx/cgF,EAAkB,G,YCmhBP,GACfC,KAAA,OACAC,WAAA,CAAAC,kBACAC,OACA,OACAzE,SAAA,CACAC,UAAA,WACAE,aAAA,WACAC,SAAA,eACAC,MAAA,mBACAC,aAAA,aACAC,SAAA,iBACAC,aAAA,6BACAC,UAAA,GACAS,MAAA,GACAO,SAAA,SACAC,QAAA,iBACAC,WAAA,GACAS,MAAA,kgBA2BAE,WAAA,GACAQ,oBAAA,wjBA2BAE,WAAA,GACAQ,mBAAA,+pBAgCAE,UAAA,IAEA/D,WAAA,QACA+E,IAAA,WACAd,mBAAA,EACAG,WAAA,GACAD,eAAA,EACAE,sBAAA,EACAG,2BAAA,EACAC,0BAAA,EACAO,UAAA,GACAzC,SAAA,EACAD,gBAAA,OACAW,cAAA,OACAU,aAAA,OACAsB,gBAAA,GACA1D,MAAA,CACA,CAAAM,GAAA,EAAAD,MAAA,OACA,CAAAC,GAAA,EAAAD,MAAA,OACA,CAAAC,GAAA,EAAAD,MAAA,UAIAsD,SAAA,CAEA7C,mBACA,iBAAAC,gBAAA,CAEA,MAAA6C,EAAA,KAAA9E,SAAAoC,MAAA2C,QAAA,eACA,OAAAD,EAAAE,OAEA,YAAAhF,SAAAsC,WAAA0C,QAIArC,iBACA,iBAAAC,cAAA,CACA,MAAAkC,EAAA,KAAA9E,SAAA8C,oBAAAiC,QAAA,eACA,OAAAD,EAAAE,OAEA,YAAAhF,SAAAgD,WAAAgC,QAIA3B,gBACA,iBAAAC,aAAA,CACA,MAAAwB,EAAA,KAAA9E,SAAAwD,mBAAAuB,QAAA,eACA,OAAAD,EAAAE,OAEA,YAAAhF,SAAA0D,UAAAsB,SAIAC,UAEAC,QAAAC,IAAA,mBAEA,KAAAnF,SAAAsC,WAAA,KAAAtC,SAAAoC,MAAA2C,QAAA,eACA,KAAA/E,SAAAgD,WAAA,KAAAhD,SAAA8C,oBAAAiC,QAAA,eACA,KAAA/E,SAAA0D,UAAA,KAAA1D,SAAAwD,mBAAAuB,QAAA,gBAEAK,QAAA,CACAC,UAEAH,QAAAC,IAAA,kBAEAxE,YAAA2E,GACA,KAAAX,UAAAW,GAEAC,WACAC,aAEAN,QAAAC,IAAA,kBAEAvE,cAAA6E,GAEA,KAAAzF,SAAA,KAAA2E,WAAA,qBACA,KAAAe,SAAAC,QAAA,aAGA9E,aAAA+E,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAAG,MACA,OAAAF,GAKA,KAAAH,SAAAM,KAAA,oBACA,IALA,KAAAN,SAAAO,MAAA,cACA,IAMAjF,SAAA4E,EAAAN,GAEA,KAAAtF,SAAAsF,GAAA,GACA,KAAAI,SAAAC,QAAA,aAEA7E,UAAA8E,GACA,KAAA7B,WAAA6B,EACA,KAAA9B,eAAA,GAEAtE,gBAEAoC,cACA,KAAAsE,SAAA,0BACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,MAAAC,EAAA,4CAC2B,IAA3BC,MAAAC,uDAC2B,IAA3BD,MAAAC,+3BAsDUvH,KAAVe,SAAAG,wBACKlB,KAALe,SAAAI,oBACKnB,KAALe,SAAAK,iBACKpB,KAALe,SAAAM,mBAEA,cAAA2B,gBACA,KAAAjC,SAAAoC,MAAAkE,EAEA,KAAAtG,SAAAsC,WAAAgE,EAAAvB,QAAA,eAEA,KAAAW,SAAAC,QAAA,YAIA9D,iBACA,KAAAmC,sBAAA,GAGAlC,eACA,KAAAoE,SAAA,wBACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,cAAApE,gBACA,KAAAjC,SAAAoC,MAAA,GAEA,KAAApC,SAAAsC,WAAA,GAEA,KAAAoD,SAAAC,QAAA,YAIAxD,gBAAAsE,GACA,KAAAzG,SAAAoC,MAAAqE,EAEA,KAAAzG,SAAAsC,WAAAmE,EAAA1B,QAAA,gBAGA1C,oBAAAoE,GACA,KAAAzG,SAAAsC,WAAAmE,EAEA,KAAAzG,SAAAoC,MAAAqE,EAAA1B,QAAA,eAGAxC,WAAAwD,GACA,IAAAW,EAAA,GACA,OAAAX,GACA,WACAW,EAAA,KAAA1G,SAAAG,cAAA,SACA,MACA,WACAuG,EAAA,KAAA1G,SAAAI,UAAA,SACA,MACA,WACAsG,EAAA,KAAA1G,SAAAK,OAAA,SACA,MACA,WACAqG,GAAA,IAAAH,MAAAC,qBACA,MAGA,cAAAvE,gBAEA,KAAAjC,SAAAoC,OAAAsE,EAGA,KAAA1G,SAAAsC,YAAAoE,GAKAlE,mBACA,KAAA0D,SAAA,0BACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,MAAAC,EAAA,mFAIKrH,KAALe,SAAAG,opBAiCQlB,KAARe,SAAAM,wBACKrB,KAALe,SAAAI,oBACKnB,KAALe,SAAAK,YAEA,cAAAuC,cACA,KAAA5C,SAAA8C,oBAAAwD,EAEA,KAAAtG,SAAAgD,WAAAsD,EAAAvB,QAAA,eAEA,KAAAW,SAAAC,QAAA,YAIAlD,eACA,KAAA0B,2BAAA,GAGAzB,aACA,KAAAwD,SAAA,wBACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,cAAAzD,cACA,KAAA5C,SAAA8C,oBAAA,GAEA,KAAA9C,SAAAgD,WAAA,GAEA,KAAA0C,SAAAC,QAAA,YAIA9C,cAAA4D,GACA,KAAAzG,SAAA8C,oBAAA2D,EACA,KAAAzG,SAAAgD,WAAAyD,EAAA1B,QAAA,gBAGAhC,kBAAA0D,GACA,KAAAzG,SAAAgD,WAAAyD,EACA,KAAAzG,SAAA8C,oBAAA2D,EAAA1B,QAAA,eAGA9B,gBAAA8C,GACA,IAAAW,EAAA,GACA,OAAAX,GACA,WACAW,EAAA,KAAA1G,SAAAG,cAAA,SACA,MACA,WACAuG,EAAA,QACA,MACA,WACAA,EAAA,KAAA1G,SAAAI,UAAA,SACA,MACA,WACAsG,EAAA,KAAA1G,SAAAM,cAAA,SACA,MAGA,cAAAsC,cACA,KAAA5C,SAAA8C,qBAAA4D,EAEA,KAAA1G,SAAAgD,YAAA0D,GAKAxD,kBACA,KAAAgD,SAAA,0BACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,MAAAC,EAAA,28BAwCA,cAAAhD,aACA,KAAAtD,SAAAwD,mBAAA8C,EAEA,KAAAtG,SAAA0D,UAAA4C,EAAAvB,QAAA,eAEA,KAAAW,SAAAC,QAAA,YAIAxC,cACA,KAAAiB,0BAAA,GAGAhB,YACA,KAAA8C,SAAA,wBACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YACAM,KAAA,KACA,cAAA/C,aACA,KAAAtD,SAAAwD,mBAAA,GAEA,KAAAxD,SAAA0D,UAAA,GAEA,KAAAgC,SAAAC,QAAA,YAIApC,aAAAkD,GACA,KAAAzG,SAAAwD,mBAAAiD,EACA,KAAAzG,SAAA0D,UAAA+C,EAAA1B,QAAA,gBAGAtB,iBAAAgD,GACA,KAAAzG,SAAA0D,UAAA+C,EACA,KAAAzG,SAAAwD,mBAAAiD,EAAA1B,QAAA,eAGApB,eAAAoC,GACA,IAAAW,EAAA,GACA,OAAAX,GACA,WACAW,EAAA,WACA,MACA,WACAA,EAAA,sBACA,MACA,WACAA,EAAA,YACA,MACA,WACAA,EAAA,KAAA1G,SAAAI,UAAA,SACA,MAGA,cAAAkD,aACA,KAAAtD,SAAAwD,oBAAAkD,EAEA,KAAA1G,SAAA0D,WAAAgD,GAIA7C,WACA,IAAA8C,EAAA,KACAA,EAAA/C,mBAAA,EAIA,cAAA3B,gBACA,KAAAjC,SAAAsC,WAAA,KAAAtC,SAAAoC,MAAA2C,QAAA,eAEA,KAAA/E,SAAAoC,MAAA,KAAApC,SAAAsC,WAAAyC,QAAA,cAIA,cAAAnC,cACA,KAAA5C,SAAAgD,WAAA,KAAAhD,SAAA8C,oBAAAiC,QAAA,eAEA,KAAA/E,SAAA8C,oBAAA,KAAA9C,SAAAgD,WAAA+B,QAAA,cAIA,cAAAzB,aACA,KAAAtD,SAAA0D,UAAA,KAAA1D,SAAAwD,mBAAAuB,QAAA,eAEA,KAAA/E,SAAAwD,mBAAA,KAAAxD,SAAA0D,UAAAqB,QAAA,cAIA6B,WAAA,KACAD,EAAAjB,SAAA,CACAK,KAAA,UACAc,QAAA,aAEAF,EAAA/C,mBAAA,GACA,QCrmC8W,I,wBCQ1WkD,EAAY,eACd,EACA/H,EACAsF,GACA,EACA,KACA,WACA,MAIa,aAAAyC,E", "file": "js/chunk-68f5b2a4.a20d5dc5.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./configs.vue?vue&type=style&index=0&id=7a1f1a6e&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"page-wrapper\"},[_c('div',{staticClass:\"page-container\"},[_c('div',{staticClass:\"page-title\"},[_vm._v(\" 基础设置 \")]),_c('div',{staticClass:\"tab-container\"},[_c('el-tabs',{attrs:{\"type\":\"card\"},on:{\"tab-click\":_vm.handleClick},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},[_c('el-tab-pane',{attrs:{\"label\":\"基础管理\",\"name\":\"first\"}},[_c('div',{staticClass:\"form-container\"},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"label-width\":\"140px\"}},[_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"网站名称\"}},[_c('el-input',{model:{value:(_vm.ruleForm.site_name),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_name\", $$v)},expression:\"ruleForm.site_name\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"公司名称\"}},[_c('el-input',{model:{value:(_vm.ruleForm.company_name),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"company_name\", $$v)},expression:\"ruleForm.company_name\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"联系方式\"}},[_c('el-input',{model:{value:(_vm.ruleForm.site_tel),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_tel\", $$v)},expression:\"ruleForm.site_tel\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"邮箱\"}},[_c('el-input',{model:{value:(_vm.ruleForm.email),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"email\", $$v)},expression:\"ruleForm.email\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"地址\"}},[_c('el-input',{model:{value:(_vm.ruleForm.site_address),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_address\", $$v)},expression:\"ruleForm.site_address\"}})],1),_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"ICP备案号\"}},[_c('el-input',{model:{value:(_vm.ruleForm.site_icp),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_icp\", $$v)},expression:\"ruleForm.site_icp\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"ICP备案链接\"}},[_c('el-input',{model:{value:(_vm.ruleForm.site_icp_url),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_icp_url\", $$v)},expression:\"ruleForm.site_icp_url\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"网站Logo\"}},[_c('div',{staticClass:\"upload-container\"},[_c('el-input',{attrs:{\"disabled\":true,\"placeholder\":\"请上传Logo图片\"},model:{value:(_vm.ruleForm.site_logo),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"site_logo\", $$v)},expression:\"ruleForm.site_logo\"}}),_c('div',{staticClass:\"upload-actions\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.changeFiled('site_logo')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess,\"before-upload\":_vm.beforeUpload}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.site_logo)?_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.site_logo)}}},[_vm._v(\"查看 \")]):_vm._e(),(_vm.ruleForm.site_logo)?_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.site_logo, 'site_logo')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1)]),_c('el-form-item',{attrs:{\"label\":\"推广律师\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择推广律师\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.lvshi),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"lvshi\", $$v)},expression:\"ruleForm.lvshi\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.lvshi),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"推广标题\"}},[_c('el-input',{model:{value:(_vm.ruleForm.my_title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"my_title\", $$v)},expression:\"ruleForm.my_title\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"推广语\"}},[_c('el-input',{model:{value:(_vm.ruleForm.my_desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"my_desc\", $$v)},expression:\"ruleForm.my_desc\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"推广图片\"}},[_c('div',{staticClass:\"upload-container\"},[_c('el-input',{attrs:{\"disabled\":true,\"placeholder\":\"请上传推广图片\"},model:{value:(_vm.ruleForm.about_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"about_path\", $$v)},expression:\"ruleForm.about_path\"}}),_c('div',{staticClass:\"upload-actions\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.changeFiled('about_path')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess,\"before-upload\":_vm.beforeUpload}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.about_path)?_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.about_path)}}},[_vm._v(\"查看 \")]):_vm._e(),(_vm.ruleForm.about_path)?_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.about_path, 'about_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1)])],1)],1)]),_c('el-tab-pane',{attrs:{\"label\":\"隐私条款\",\"name\":\"yinsi\"}},[_c('div',{staticClass:\"privacy-container\"},[_c('div',{staticClass:\"privacy-toolbar\"},[_c('div',{staticClass:\"toolbar-left\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.useTemplate}},[_vm._v(\" 使用模板 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-view\"},on:{\"click\":_vm.previewPrivacy}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.resetPrivacy}},[_vm._v(\" 重置 \")])],1),_c('div',{staticClass:\"toolbar-right\"},[_c('span',{staticClass:\"word-count\"},[_vm._v(\"字数：\"+_vm._s(_vm.privacyWordCount))])])]),_c('div',{staticClass:\"edit-mode-switch\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.privacyEditMode),callback:function ($$v) {_vm.privacyEditMode=$$v},expression:\"privacyEditMode\"}},[_c('el-radio-button',{attrs:{\"label\":\"rich\"}},[_vm._v(\"富文本编辑\")]),_c('el-radio-button',{attrs:{\"label\":\"text\"}},[_vm._v(\"纯文本编辑\")])],1)],1),(_vm.privacyEditMode === 'rich')?_c('div',{staticClass:\"rich-editor-container\"},[_c('editor-bar',{attrs:{\"isClear\":_vm.isClear,\"height\":400},on:{\"change\":_vm.onPrivacyChange},model:{value:(_vm.ruleForm.yinsi),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"yinsi\", $$v)},expression:\"ruleForm.yinsi\"}})],1):_c('div',{staticClass:\"text-editor-container\"},[_c('el-input',{staticClass:\"privacy-textarea\",attrs:{\"type\":\"textarea\",\"rows\":20,\"placeholder\":\"请输入隐私条款内容...\"},on:{\"input\":_vm.onPrivacyTextChange},model:{value:(_vm.ruleForm.yinsi_text),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"yinsi_text\", $$v)},expression:\"ruleForm.yinsi_text\"}})],1),_c('div',{staticClass:\"quick-insert\"},[_c('div',{staticClass:\"quick-insert-title\"},[_vm._v(\"快捷插入：\")]),_c('div',{staticClass:\"quick-insert-buttons\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertText('公司名称')}}},[_vm._v(\" 公司名称 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertText('联系方式')}}},[_vm._v(\" 联系方式 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertText('邮箱地址')}}},[_vm._v(\" 邮箱地址 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertText('生效日期')}}},[_vm._v(\" 生效日期 \")])],1)])])]),_c('el-tab-pane',{attrs:{\"label\":\"关于我们\",\"name\":\"about\"}},[_c('div',{staticClass:\"about-container\"},[_c('div',{staticClass:\"about-toolbar\"},[_c('div',{staticClass:\"toolbar-left\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.useAboutTemplate}},[_vm._v(\" 使用模板 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-view\"},on:{\"click\":_vm.previewAbout}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.resetAbout}},[_vm._v(\" 重置 \")])],1),_c('div',{staticClass:\"toolbar-right\"},[_c('span',{staticClass:\"word-count\"},[_vm._v(\"字数：\"+_vm._s(_vm.aboutWordCount))])])]),_c('div',{staticClass:\"edit-mode-switch\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.aboutEditMode),callback:function ($$v) {_vm.aboutEditMode=$$v},expression:\"aboutEditMode\"}},[_c('el-radio-button',{attrs:{\"label\":\"rich\"}},[_vm._v(\"富文本编辑\")]),_c('el-radio-button',{attrs:{\"label\":\"text\"}},[_vm._v(\"纯文本编辑\")])],1)],1),(_vm.aboutEditMode === 'rich')?_c('div',{staticClass:\"rich-editor-container\"},[_c('editor-bar',{attrs:{\"isClear\":_vm.isClear,\"height\":400},on:{\"change\":_vm.onAboutChange},model:{value:(_vm.ruleForm.index_about_content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"index_about_content\", $$v)},expression:\"ruleForm.index_about_content\"}})],1):_c('div',{staticClass:\"text-editor-container\"},[_c('el-input',{staticClass:\"about-textarea\",attrs:{\"type\":\"textarea\",\"rows\":20,\"placeholder\":\"请输入关于我们的内容...\"},on:{\"input\":_vm.onAboutTextChange},model:{value:(_vm.ruleForm.about_text),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"about_text\", $$v)},expression:\"ruleForm.about_text\"}})],1),_c('div',{staticClass:\"quick-insert\"},[_c('div',{staticClass:\"quick-insert-title\"},[_vm._v(\"快捷插入：\")]),_c('div',{staticClass:\"quick-insert-buttons\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertAboutText('公司名称')}}},[_vm._v(\" 公司名称 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertAboutText('成立时间')}}},[_vm._v(\" 成立时间 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertAboutText('联系方式')}}},[_vm._v(\" 联系方式 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertAboutText('公司地址')}}},[_vm._v(\" 公司地址 \")])],1)])])]),_c('el-tab-pane',{attrs:{\"label\":\"团队介绍\",\"name\":\"team\"}},[_c('div',{staticClass:\"team-container\"},[_c('div',{staticClass:\"team-toolbar\"},[_c('div',{staticClass:\"toolbar-left\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.useTeamTemplate}},[_vm._v(\" 使用模板 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-view\"},on:{\"click\":_vm.previewTeam}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.resetTeam}},[_vm._v(\" 重置 \")])],1),_c('div',{staticClass:\"toolbar-right\"},[_c('span',{staticClass:\"word-count\"},[_vm._v(\"字数：\"+_vm._s(_vm.teamWordCount))])])]),_c('div',{staticClass:\"edit-mode-switch\"},[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.teamEditMode),callback:function ($$v) {_vm.teamEditMode=$$v},expression:\"teamEditMode\"}},[_c('el-radio-button',{attrs:{\"label\":\"rich\"}},[_vm._v(\"富文本编辑\")]),_c('el-radio-button',{attrs:{\"label\":\"text\"}},[_vm._v(\"纯文本编辑\")])],1)],1),(_vm.teamEditMode === 'rich')?_c('div',{staticClass:\"rich-editor-container\"},[_c('editor-bar',{attrs:{\"isClear\":_vm.isClear,\"height\":400},on:{\"change\":_vm.onTeamChange},model:{value:(_vm.ruleForm.index_team_content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"index_team_content\", $$v)},expression:\"ruleForm.index_team_content\"}})],1):_c('div',{staticClass:\"text-editor-container\"},[_c('el-input',{staticClass:\"team-textarea\",attrs:{\"type\":\"textarea\",\"rows\":20,\"placeholder\":\"请输入团队介绍内容...\"},on:{\"input\":_vm.onTeamTextChange},model:{value:(_vm.ruleForm.team_text),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"team_text\", $$v)},expression:\"ruleForm.team_text\"}})],1),_c('div',{staticClass:\"quick-insert\"},[_c('div',{staticClass:\"quick-insert-title\"},[_vm._v(\"快捷插入：\")]),_c('div',{staticClass:\"quick-insert-buttons\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertTeamText('团队规模')}}},[_vm._v(\" 团队规模 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertTeamText('专业领域')}}},[_vm._v(\" 专业领域 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertTeamText('服务理念')}}},[_vm._v(\" 服务理念 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.insertTeamText('联系方式')}}},[_vm._v(\" 联系方式 \")])],1)])])])],1)],1),_c('div',{staticClass:\"submit-container\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"loading\":_vm.fullscreenLoading},on:{\"click\":_vm.saveData}},[_vm._v(\"保存设置 \")])],1)]),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{staticClass:\"privacy-preview-dialog\",attrs:{\"title\":\"隐私条款预览\",\"visible\":_vm.previewDialogVisible,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.previewDialogVisible=$event}}},[_c('div',{staticClass:\"preview-content\",domProps:{\"innerHTML\":_vm._s(_vm.ruleForm.yinsi)}}),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.previewDialogVisible = false}}},[_vm._v(\"关闭\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.previewDialogVisible = false}}},[_vm._v(\"确定\")])],1)]),_c('el-dialog',{staticClass:\"about-preview-dialog\",attrs:{\"title\":\"关于我们预览\",\"visible\":_vm.aboutPreviewDialogVisible,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.aboutPreviewDialogVisible=$event}}},[_c('div',{staticClass:\"preview-content\",domProps:{\"innerHTML\":_vm._s(_vm.ruleForm.index_about_content)}}),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.aboutPreviewDialogVisible = false}}},[_vm._v(\"关闭\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.aboutPreviewDialogVisible = false}}},[_vm._v(\"确定\")])],1)]),_c('el-dialog',{staticClass:\"team-preview-dialog\",attrs:{\"title\":\"团队介绍预览\",\"visible\":_vm.teamPreviewDialogVisible,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.teamPreviewDialogVisible=$event}}},[_c('div',{staticClass:\"preview-content\",domProps:{\"innerHTML\":_vm._s(_vm.ruleForm.index_team_content)}}),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.teamPreviewDialogVisible = false}}},[_vm._v(\"关闭\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.teamPreviewDialogVisible = false}}},[_vm._v(\"确定\")])],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-title\">\r\n        基础设置\r\n      </div>\r\n\r\n      <!-- 标签页导航 -->\r\n      <div class=\"tab-container\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <div class=\"form-container\">\r\n              <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\">\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"网站名称\">\r\n                      <el-input v-model=\"ruleForm.site_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\">\r\n                      <el-input v-model=\"ruleForm.company_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系方式\">\r\n                      <el-input v-model=\"ruleForm.site_tel\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"邮箱\">\r\n                      <el-input v-model=\"ruleForm.email\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"ruleForm.site_address\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案号\">\r\n                      <el-input v-model=\"ruleForm.site_icp\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案链接\">\r\n                      <el-input v-model=\"ruleForm.site_icp_url\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-form-item label=\"网站Logo\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.site_logo\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传Logo图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('site_logo')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"showImage(ruleForm.site_logo)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"推广律师\">\r\n                  <el-select\r\n                    v-model=\"ruleForm.lvshi\"\r\n                    placeholder=\"请选择推广律师\"\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                      v-for=\"(item, index) in lvshi\"\r\n                      :key=\"index\"\r\n                      :label=\"item.title\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广标题\">\r\n                      <el-input v-model=\"ruleForm.my_title\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广语\">\r\n                      <el-input v-model=\"ruleForm.my_desc\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"推广图片\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.about_path\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传推广图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('about_path')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"showImage(ruleForm.about_path)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <div class=\"privacy-container\">\r\n              <!-- 隐私条款工具栏 -->\r\n              <div class=\"privacy-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewPrivacy\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetPrivacy\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ privacyWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"privacyEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"privacyEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.yinsi\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onPrivacyChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.yinsi_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入隐私条款内容...\"\r\n                  @input=\"onPrivacyTextChange\"\r\n                  class=\"privacy-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('邮箱地址')\"\r\n                  >\r\n                    邮箱地址\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('生效日期')\"\r\n                  >\r\n                    生效日期\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <div class=\"about-container\">\r\n              <!-- 关于我们工具栏 -->\r\n              <div class=\"about-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useAboutTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewAbout\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetAbout\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ aboutWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"aboutEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"aboutEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_about_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onAboutChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.about_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入关于我们的内容...\"\r\n                  @input=\"onAboutTextChange\"\r\n                  class=\"about-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('成立时间')\"\r\n                  >\r\n                    成立时间\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司地址')\"\r\n                  >\r\n                    公司地址\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <div class=\"team-container\">\r\n              <!-- 团队介绍工具栏 -->\r\n              <div class=\"team-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTeamTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewTeam\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetTeam\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ teamWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"teamEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"teamEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_team_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onTeamChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.team_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入团队介绍内容...\"\r\n                  @input=\"onTeamTextChange\"\r\n                  class=\"team-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('团队规模')\"\r\n                  >\r\n                    团队规模\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('专业领域')\"\r\n                  >\r\n                    专业领域\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('服务理念')\"\r\n                  >\r\n                    服务理念\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n\r\n      <!-- 提交按钮 -->\r\n      <div class=\"submit-container\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"medium\"\r\n          @click=\"saveData\"\r\n          :loading=\"fullscreenLoading\"\r\n          >保存设置\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 隐私条款预览对话框 -->\r\n    <el-dialog\r\n      title=\"隐私条款预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"privacy-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.yinsi\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"previewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 关于我们预览对话框 -->\r\n    <el-dialog\r\n      title=\"关于我们预览\"\r\n      :visible.sync=\"aboutPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"about-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_about_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"aboutPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"aboutPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 团队介绍预览对话框 -->\r\n    <el-dialog\r\n      title=\"团队介绍预览\"\r\n      :visible.sync=\"teamPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"team-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_team_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"teamPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"teamPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>2024年1月1日</p>\r\n<p><strong>最后更新：</strong>2024年1月1日</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供法律服务</li>\r\n<li>改善我们的服务质量</li>\r\n<li>与您沟通服务相关事宜</li>\r\n</ul>\r\n\r\n<h3>3. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。</p>\r\n\r\n<h3>4. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        yinsi_text: \"\",\r\n        index_about_content: `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：北京市朝阳区示例大厦<br>\r\n电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        about_text: \"\",\r\n        index_team_content: `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由20余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 3名</li>\r\n<li>合伙人律师 5名</li>\r\n<li>资深律师 8名</li>\r\n<li>执业律师 6名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过10年</li>\r\n<li>成功处理案件超过1000起</li>\r\n<li>客户满意度达到98%以上</li>\r\n<li>多名律师获得行业荣誉</li>\r\n</ul>`,\r\n        team_text: \"\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewDialogVisible: false,\r\n      aboutPreviewDialogVisible: false,\r\n      teamPreviewDialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      privacyEditMode: \"rich\", // 隐私条款编辑模式\r\n      aboutEditMode: \"rich\", // 关于我们编辑模式\r\n      teamEditMode: \"rich\", // 团队介绍编辑模式\r\n      privacyTemplate: \"\", // 隐私条款模板\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 隐私条款字数统计\r\n    privacyWordCount() {\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 去除HTML标签后计算字数\r\n        const text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.yinsi_text.length;\r\n      }\r\n    },\r\n    // 关于我们字数统计\r\n    aboutWordCount() {\r\n      if (this.aboutEditMode === 'rich') {\r\n        const text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.about_text.length;\r\n      }\r\n    },\r\n    // 团队介绍字数统计\r\n    teamWordCount() {\r\n      if (this.teamEditMode === 'rich') {\r\n        const text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.team_text.length;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n    // 初始化纯文本内容\r\n    this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    // 隐私条款相关方法\r\n    useTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>${new Date().toLocaleDateString()}</p>\r\n<p><strong>最后更新：</strong>${new Date().toLocaleDateString()}</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n<li>设备信息和使用数据</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供和改善法律服务</li>\r\n<li>处理您的请求和查询</li>\r\n<li>发送服务相关通知</li>\r\n<li>遵守法律义务</li>\r\n</ul>\r\n\r\n<h3>3. 信息共享</h3>\r\n<p>我们不会向第三方出售、交易或转让您的个人信息，除非：</p>\r\n<ul>\r\n<li>获得您的明确同意</li>\r\n<li>法律要求或政府部门要求</li>\r\n<li>为保护我们的权利和安全</li>\r\n</ul>\r\n\r\n<h3>4. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息：</p>\r\n<ul>\r\n<li>数据加密传输和存储</li>\r\n<li>访问权限控制</li>\r\n<li>定期安全审计</li>\r\n<li>员工隐私培训</li>\r\n</ul>\r\n\r\n<h3>5. 您的权利</h3>\r\n<p>您有权：</p>\r\n<ul>\r\n<li>访问和更新您的个人信息</li>\r\n<li>删除您的个人信息</li>\r\n<li>限制信息处理</li>\r\n<li>数据可携带性</li>\r\n</ul>\r\n\r\n<h3>6. Cookie使用</h3>\r\n<p>我们使用Cookie来改善用户体验，您可以通过浏览器设置管理Cookie偏好。</p>\r\n\r\n<h3>7. 政策更新</h3>\r\n<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。</p>\r\n\r\n<h3>8. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>公司名称：${this.ruleForm.company_name}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}<br>\r\n地址：${this.ruleForm.site_address}</p>`;\r\n\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = template;\r\n        } else {\r\n          this.ruleForm.yinsi_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewPrivacy() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n\r\n    resetPrivacy() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = '';\r\n        } else {\r\n          this.ruleForm.yinsi_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onPrivacyChange(val) {\r\n      this.ruleForm.yinsi = val;\r\n      // 同步更新纯文本版本\r\n      this.ruleForm.yinsi_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onPrivacyTextChange(val) {\r\n      this.ruleForm.yinsi_text = val;\r\n      // 同步更新富文本版本（简单的换行转换）\r\n      this.ruleForm.yinsi = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '邮箱地址':\r\n          insertValue = this.ruleForm.email || '[邮箱地址]';\r\n          break;\r\n        case '生效日期':\r\n          insertValue = new Date().toLocaleDateString();\r\n          break;\r\n      }\r\n\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 富文本模式下插入\r\n        this.ruleForm.yinsi += insertValue;\r\n      } else {\r\n        // 纯文本模式下插入\r\n        this.ruleForm.yinsi_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 关于我们相关方法\r\n    useAboutTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>${this.ruleForm.company_name}成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n<li>刑事辩护代理</li>\r\n<li>房地产法务</li>\r\n<li>金融法务</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n<li><strong>全程跟踪：</strong>专人负责，全程跟踪服务</li>\r\n</ul>\r\n\r\n<h3>服务承诺</h3>\r\n<p>我们承诺为每一位客户提供：</p>\r\n<ul>\r\n<li>专业的法律咨询服务</li>\r\n<li>及时的案件进展反馈</li>\r\n<li>透明的收费标准</li>\r\n<li>保密的客户信息</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：${this.ruleForm.site_address}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}</p>`;\r\n\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = template;\r\n        } else {\r\n          this.ruleForm.about_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewAbout() {\r\n      this.aboutPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetAbout() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = '';\r\n        } else {\r\n          this.ruleForm.about_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onAboutChange(val) {\r\n      this.ruleForm.index_about_content = val;\r\n      this.ruleForm.about_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onAboutTextChange(val) {\r\n      this.ruleForm.about_text = val;\r\n      this.ruleForm.index_about_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertAboutText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '成立时间':\r\n          insertValue = '2020年';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '公司地址':\r\n          insertValue = this.ruleForm.site_address || '[公司地址]';\r\n          break;\r\n      }\r\n\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.index_about_content += insertValue;\r\n      } else {\r\n        this.ruleForm.about_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 团队介绍相关方法\r\n    useTeamTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由30余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 5名</li>\r\n<li>合伙人律师 8名</li>\r\n<li>资深律师 12名</li>\r\n<li>执业律师 10名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组、公司治理</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理、违约责任</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护、侵权诉讼</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁、人事争议</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理、仲裁程序</li>\r\n<li><strong>刑事辩护：</strong>刑事案件辩护、取保候审、缓刑申请</li>\r\n<li><strong>房地产法务：</strong>房产交易、物业纠纷、拆迁补偿</li>\r\n<li><strong>金融法务：</strong>银行业务、证券投资、保险理赔</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。我们相信，只有真正理解客户的需求，才能提供最有价值的法律服务。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过12年</li>\r\n<li>成功处理案件超过2000起</li>\r\n<li>客户满意度达到99%以上</li>\r\n<li>多名律师获得省市级荣誉</li>\r\n<li>团队协作，资源共享</li>\r\n</ul>\r\n\r\n<h3>持续发展</h3>\r\n<p>我们注重团队的持续发展和专业提升，定期组织内部培训、学术交流和案例研讨，确保团队始终保持专业领先地位。</p>`;\r\n\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = template;\r\n        } else {\r\n          this.ruleForm.team_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewTeam() {\r\n      this.teamPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetTeam() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = '';\r\n        } else {\r\n          this.ruleForm.team_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onTeamChange(val) {\r\n      this.ruleForm.index_team_content = val;\r\n      this.ruleForm.team_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onTeamTextChange(val) {\r\n      this.ruleForm.team_text = val;\r\n      this.ruleForm.index_team_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertTeamText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '团队规模':\r\n          insertValue = '30余名专业律师';\r\n          break;\r\n        case '专业领域':\r\n          insertValue = '公司法务、合同纠纷、知识产权、劳动法务';\r\n          break;\r\n        case '服务理念':\r\n          insertValue = '客户至上、专业至上';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n      }\r\n\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.index_team_content += insertValue;\r\n      } else {\r\n        this.ruleForm.team_text += insertValue;\r\n      }\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 保存前同步所有内容的两种格式\r\n      // 隐私条款\r\n      if (this.privacyEditMode === 'rich') {\r\n        this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.yinsi = this.ruleForm.yinsi_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 关于我们\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_about_content = this.ruleForm.about_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 团队介绍\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_team_content = this.ruleForm.team_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tab-container {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.form-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 提交按钮容器 */\r\n.submit-container {\r\n  text-align: center;\r\n  padding: 24px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-input, .el-select, .el-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.el-textarea .el-textarea__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 标签页样式 */\r\n.el-tabs--card > .el-tabs__header .el-tabs__item {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\r\n  background-color: #fff;\r\n  border-bottom-color: #fff;\r\n}\r\n\r\n/* 上传按钮样式 */\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n/* 隐私条款、关于我们、团队介绍相关样式 */\r\n.privacy-container,\r\n.about-container,\r\n.team-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.privacy-toolbar,\r\n.about-toolbar,\r\n.team-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: #fff;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  border: 1px solid #ddd;\r\n}\r\n\r\n.edit-mode-switch {\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.rich-editor-container {\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.text-editor-container {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.privacy-textarea,\r\n.about-textarea,\r\n.team-textarea {\r\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-textarea ::v-deep .el-textarea__inner,\r\n.about-textarea ::v-deep .el-textarea__inner,\r\n.team-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 16px;\r\n  resize: vertical;\r\n}\r\n\r\n.quick-insert {\r\n  background: #f0f9ff;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 6px;\r\n  padding: 12px 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.quick-insert-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #0369a1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.quick-insert-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text {\r\n  color: #0369a1;\r\n  background: #e0f2fe;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 4px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text:hover {\r\n  background: #0369a1;\r\n  color: #fff;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.privacy-preview-dialog .preview-content,\r\n.about-preview-dialog .preview-content,\r\n.team-preview-dialog .preview-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 6px;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h2,\r\n.about-preview-dialog .preview-content h2,\r\n.team-preview-dialog .preview-content h2 {\r\n  color: #2c3e50;\r\n  border-bottom: 2px solid #3498db;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h3,\r\n.about-preview-dialog .preview-content h3,\r\n.team-preview-dialog .preview-content h3 {\r\n  color: #34495e;\r\n  margin-top: 25px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content p,\r\n.about-preview-dialog .preview-content p,\r\n.team-preview-dialog .preview-content p {\r\n  margin-bottom: 12px;\r\n  color: #555;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content ul,\r\n.about-preview-dialog .preview-content ul,\r\n.team-preview-dialog .preview-content ul {\r\n  margin-bottom: 15px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content li,\r\n.about-preview-dialog .preview-content li,\r\n.team-preview-dialog .preview-content li {\r\n  margin-bottom: 8px;\r\n  color: #666;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content strong,\r\n.about-preview-dialog .preview-content strong,\r\n.team-preview-dialog .preview-content strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-wrapper {\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .upload-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .upload-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-toolbar {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    justify-content: center;\r\n  }\r\n\r\n  .toolbar-right {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-insert-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-preview-dialog,\r\n  .about-preview-dialog,\r\n  .team-preview-dialog {\r\n    width: 95% !important;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./configs.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./configs.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./configs.vue?vue&type=template&id=7a1f1a6e&scoped=true\"\nimport script from \"./configs.vue?vue&type=script&lang=js\"\nexport * from \"./configs.vue?vue&type=script&lang=js\"\nimport style0 from \"./configs.vue?vue&type=style&index=0&id=7a1f1a6e&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a1f1a6e\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}