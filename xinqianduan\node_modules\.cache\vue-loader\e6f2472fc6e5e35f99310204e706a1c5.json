{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue?vue&type=template&id=6879a05a&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue", "mtime": 1748483892216}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}