{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue", "mtime": 1748540171932}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zhiwei.vue"], "names": [], "mappings": ";AAi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file": "zhiwei.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\r\n  <div class=\"position-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-postcard\"></i>\r\n            职位管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统职位信息和权限配置</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增职位\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">职位搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入职位名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限级别</label>\r\n              <el-select\r\n                v-model=\"search.permission_level\"\r\n                placeholder=\"请选择权限级别\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"超级管理员\" value=\"super\"></el-option>\r\n                <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n                <el-option label=\"普通用户\" value=\"user\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-postcard\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总职位数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon admin\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ adminCount }}</div>\r\n              <div class=\"stat-label\">管理职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ userCount }}</div>\r\n              <div class=\"stat-label\">普通职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <i class=\"el-icon-circle-check\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeCount }}</div>\r\n              <div class=\"stat-label\">启用职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 职位列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            职位列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"position-table\"\r\n            stripe\r\n          >\r\n            <el-table-column prop=\"title\" label=\"职位名称\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"position-title-cell\">\r\n                  <div class=\"position-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"position-level\" v-if=\"scope.row.level\">{{ scope.row.level }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"desc\" label=\"职位描述\" min-width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"权限配置\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-tags\">\r\n                  <el-tag\r\n                    v-for=\"(permission, index) in getPermissionLabels(scope.row.quanxian)\"\r\n                    :key=\"index\"\r\n                    size=\"mini\"\r\n                    :type=\"getPermissionTagType(permission)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ permission }}\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"position in list\" :key=\"position.id\" class=\"position-card-col\">\r\n              <div class=\"position-card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-title\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    {{ position.title }}\r\n                  </div>\r\n                  <div class=\"card-status\">\r\n                    <el-switch\r\n                      v-model=\"position.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      @change=\"changeStatus(position)\"\r\n                      size=\"small\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-desc\">{{ position.desc }}</div>\r\n\r\n                  <div class=\"card-permissions\">\r\n                    <div class=\"permission-title\">权限配置：</div>\r\n                    <div class=\"permission-tags\">\r\n                      <el-tag\r\n                        v-for=\"(permission, index) in getPermissionLabels(position.quanxian)\"\r\n                        :key=\"index\"\r\n                        size=\"mini\"\r\n                        :type=\"getPermissionTagType(permission)\"\r\n                        style=\"margin: 2px;\"\r\n                      >\r\n                        {{ permission }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ position.create_time }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(position.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(position), position.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"权限\" :label-width=\"formLabelWidth\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.quanxian\"\r\n            :options=\"options\"\r\n            :props=\"props\"\r\n            clearable\r\n          ></el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      viewMode: 'table', // table | card\r\n      props: { multiple: true },\r\n      options: {},\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 5,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      },\r\n      loading: true,\r\n      url: \"/zhiwei/\",\r\n      title: \"职位\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        quanxian: [],\r\n        status: 1\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        desc: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位描述\",\r\n            trigger: \"blur\",\r\n          },\r\n        ]\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 管理员职位数量\r\n    adminCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && (item.quanxian.includes(1) || item.quanxian.includes(13))\r\n      ).length;\r\n    },\r\n    // 普通用户职位数量\r\n    userCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && !item.quanxian.includes(1) && !item.quanxian.includes(13)\r\n      ).length;\r\n    },\r\n    // 启用职位数量\r\n    activeCount() {\r\n      return this.list.filter(item => item.status === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限标签\r\n    getPermissionLabels(permissions) {\r\n      if (!permissions || !Array.isArray(permissions)) return [];\r\n\r\n      const permissionMap = {\r\n        1: \"系统管理\",\r\n        2: \"业务管理\",\r\n        3: \"文书管理\",\r\n        4: \"财务管理\",\r\n        11: \"用户管理\",\r\n        12: \"角色管理\",\r\n        13: \"权限管理\",\r\n        21: \"订单管理\",\r\n        22: \"客户管理\",\r\n        31: \"合同管理\",\r\n        32: \"律师函管理\",\r\n        33: \"课程管理\",\r\n        41: \"支付管理\"\r\n      };\r\n\r\n      return permissions.map(id => permissionMap[id] || `权限${id}`).filter(Boolean);\r\n    },\r\n\r\n    // 获取权限标签类型\r\n    getPermissionTagType(permission) {\r\n      if (permission.includes('系统') || permission.includes('权限')) return 'danger';\r\n      if (permission.includes('业务') || permission.includes('订单')) return 'primary';\r\n      if (permission.includes('文书') || permission.includes('合同')) return 'success';\r\n      if (permission.includes('财务') || permission.includes('支付')) return 'warning';\r\n      return 'info';\r\n    },\r\n\r\n    // 状态切换\r\n    changeStatus(row) {\r\n      this.$message.success(`职位\"${row.title}\"状态已${row.status ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    change() {},\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getQuanxians();\r\n    },\r\n    getQuanxians() {\r\n      // 从权限管理页面获取权限数据\r\n      this.getPermissionsFromManagement();\r\n    },\r\n\r\n    // 从权限管理获取权限数据\r\n    getPermissionsFromManagement() {\r\n      // 模拟从权限管理页面获取的权限数据\r\n      const permissionData = [\r\n        {\r\n          value: 1,\r\n          label: \"系统管理\",\r\n          children: [\r\n            {\r\n              value: 11,\r\n              label: \"用户管理\",\r\n              children: [\r\n                { value: 111, label: \"查看用户\" },\r\n                { value: 112, label: \"新增用户\" },\r\n                { value: 113, label: \"编辑用户\" },\r\n                { value: 114, label: \"删除用户\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 12,\r\n              label: \"角色管理\",\r\n              children: [\r\n                { value: 121, label: \"查看角色\" },\r\n                { value: 122, label: \"新增角色\" },\r\n                { value: 123, label: \"编辑角色\" },\r\n                { value: 124, label: \"删除角色\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 13,\r\n              label: \"权限管理\",\r\n              children: [\r\n                { value: 131, label: \"查看权限\" },\r\n                { value: 132, label: \"新增权限\" },\r\n                { value: 133, label: \"编辑权限\" },\r\n                { value: 134, label: \"删除权限\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 2,\r\n          label: \"业务管理\",\r\n          children: [\r\n            {\r\n              value: 21,\r\n              label: \"订单管理\",\r\n              children: [\r\n                { value: 211, label: \"查看订单\" },\r\n                { value: 212, label: \"新增订单\" },\r\n                { value: 213, label: \"编辑订单\" },\r\n                { value: 214, label: \"删除订单\" },\r\n                { value: 215, label: \"导出订单\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 22,\r\n              label: \"客户管理\",\r\n              children: [\r\n                { value: 221, label: \"查看客户\" },\r\n                { value: 222, label: \"新增客户\" },\r\n                { value: 223, label: \"编辑客户\" },\r\n                { value: 224, label: \"删除客户\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 3,\r\n          label: \"文书管理\",\r\n          children: [\r\n            {\r\n              value: 31,\r\n              label: \"合同管理\",\r\n              children: [\r\n                { value: 311, label: \"查看合同\" },\r\n                { value: 312, label: \"新增合同\" },\r\n                { value: 313, label: \"编辑合同\" },\r\n                { value: 314, label: \"删除合同\" },\r\n                { value: 315, label: \"审核合同\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 32,\r\n              label: \"律师函管理\",\r\n              children: [\r\n                { value: 321, label: \"查看律师函\" },\r\n                { value: 322, label: \"发送律师函\" },\r\n                { value: 323, label: \"编辑律师函\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 33,\r\n              label: \"课程管理\",\r\n              children: [\r\n                { value: 331, label: \"查看课程\" },\r\n                { value: 332, label: \"新增课程\" },\r\n                { value: 333, label: \"编辑课程\" },\r\n                { value: 334, label: \"删除课程\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 4,\r\n          label: \"财务管理\",\r\n          children: [\r\n            {\r\n              value: 41,\r\n              label: \"支付管理\",\r\n              children: [\r\n                { value: 411, label: \"查看支付记录\" },\r\n                { value: 412, label: \"处理退款\" },\r\n                { value: 413, label: \"导出财务报表\" }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      this.options = permissionData;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"系统管理员\",\r\n            desc: \"拥有系统所有权限，负责系统维护和用户管理\",\r\n            quanxian: [1, 2, 3, 4], // 拥有所有模块权限\r\n            status: 1,\r\n            level: \"超级管理员\",\r\n            create_time: \"2024-01-01 10:00:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"业务经理\",\r\n            desc: \"负责业务流程管理，客户关系维护\",\r\n            quanxian: [2, 3], // 拥有业务管理和文书管理权限\r\n            status: 1,\r\n            level: \"管理员\",\r\n            create_time: \"2024-01-02 14:30:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"法务专员\",\r\n            desc: \"负责合同审核、律师函处理等法务工作\",\r\n            quanxian: [3], // 只有文书管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-03 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"财务专员\",\r\n            desc: \"负责财务管理、支付处理、报表统计\",\r\n            quanxian: [4], // 只有财务管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-04 11:20:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"客服专员\",\r\n            desc: \"负责客户咨询、问题处理、售后服务\",\r\n            quanxian: [22], // 只有客户管理权限\r\n            status: 0,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-05 16:45:00\"\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.position-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section .page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n}\r\n\r\n.search-form {\r\n  padding: 20px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.admin {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.user {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.active {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格样式 */\r\n.position-table {\r\n  margin: 0;\r\n}\r\n\r\n.position-title-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.position-title {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.position-level {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  background: #f0f2f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n  width: fit-content;\r\n}\r\n\r\n.permission-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n  justify-content: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #606266;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  padding: 20px 0;\r\n}\r\n\r\n.position-card-col {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.position-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-desc {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-permissions {\r\n  margin-bottom: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.permission-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-footer {\r\n  margin-top: auto;\r\n}\r\n\r\n.card-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.card-actions {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .position-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .position-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"]}]}