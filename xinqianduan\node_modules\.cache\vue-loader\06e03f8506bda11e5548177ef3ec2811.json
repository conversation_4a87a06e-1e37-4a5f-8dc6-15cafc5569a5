{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748606652788}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings": ";AAkbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\" @click=\"handleChatMainClick\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button \r\n                type=\"text\" \r\n                icon=\"el-icon-more\" \r\n                @click=\"quanyuan\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 群成员侧边栏 -->\r\n        <div class=\"member-sidebar\" :class=\"{ 'show': la }\" @click.stop>\r\n          <div class=\"member-header\">\r\n            <h3>群成员</h3>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"closeMemberPanel\"\r\n              class=\"close-btn\"\r\n            ></el-button>\r\n          </div>\r\n          <div class=\"member-content\">\r\n            <!-- 用户列表 -->\r\n            <div class=\"member-section\" v-if=\"userss.length\">\r\n              <div class=\"section-title\">用户</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-item\" v-for=\"(item, index) in userss\" :key=\"'user-' + index\">\r\n                  <div v-for=\"(value, key) in item.list\" :key=\"'user-item-' + key\" class=\"member-card\">\r\n                    <img :src=\"value.headimg\" class=\"member-avatar\"/>\r\n                    <div class=\"member-name\">{{ value.nickname }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 员工列表 -->\r\n            <div class=\"member-section\" v-for=\"(item, index) in yuangongss\" :key=\"'staff-' + index\">\r\n              <div class=\"section-title\">{{ item.zhiwei }}</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-card\" v-for=\"(value, key) in item.list\" :key=\"'staff-item-' + key\">\r\n                  <img :src=\"value.pic_path\" class=\"member-avatar\"/>\r\n                  <div class=\"member-name\">{{ value.title }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n      la: false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    quanyuan() {\r\n      console.log('点击群成员按钮');\r\n      _this.la = !_this.la;\r\n      // 使用测试数据，不调用API\r\n      if (_this.la) {\r\n        _this.loadTestMembers();\r\n      }\r\n    },\r\n    // 关闭群成员面板\r\n    closeMemberPanel() {\r\n      _this.la = false;\r\n    },\r\n    // 点击聊天主区域时关闭群成员面板\r\n    handleChatMainClick() {\r\n      if (_this.la) {\r\n        _this.la = false;\r\n      }\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      _this.isEmji = !_this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      _this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      _this.imgUlr = img;\r\n      _this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      console.log('点击私聊:', index);\r\n      _this.selectId = index;\r\n      _this.quliaoIndex = -1;\r\n      _this.la = false;\r\n      _this.loadTestMessages();\r\n    },\r\n    changeQun(index) {\r\n      console.log('点击群聊:', index);\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.la = false;\r\n      _this.loadTestMessages();\r\n    },\r\n    getEmoji(item) {\r\n      _this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (_this.search) _this.isShowSeach = true;\r\n      else _this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      _this.search = \"\";\r\n      _this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (_this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 群聊测试数据\r\n      _this.quns = [\r\n        {\r\n          id: 1,\r\n          title: \"法务团队群\",\r\n          desc: \"最新消息：合同审核已完成\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          create_time: \"09:30\",\r\n          count: 3,\r\n          uid: \"1,2,3\",\r\n          lvshi_id: \"1,2\",\r\n          yuangong_id: \"1,2,3\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"客户服务群\",\r\n          desc: \"张三：请问合同什么时候能完成？\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          create_time: \"昨天\",\r\n          count: 1,\r\n          uid: \"4,5\",\r\n          lvshi_id: \"3\",\r\n          yuangong_id: \"4,5\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"紧急处理群\",\r\n          desc: \"李四：这个案件需要加急处理\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          create_time: \"15:20\",\r\n          count: 5,\r\n          uid: \"1,3,5\",\r\n          lvshi_id: \"1\",\r\n          yuangong_id: \"1,3\",\r\n          is_daiban: 1\r\n        }\r\n      ];\r\n\r\n      // 私聊用户测试数据\r\n      _this.users = [\r\n        {\r\n          id: 1,\r\n          title: \"张三（客户）\",\r\n          content: \"您好，请问我的合同审核进度如何？\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          time: \"10:30\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"李四（律师）\",\r\n          content: \"合同已经审核完毕，请查收\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          time: \"09:15\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"王五（调解员）\",\r\n          content: \"调解会议安排在明天下午2点\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          time: \"昨天\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"赵六（客户）\",\r\n          content: \"谢谢您的帮助！\",\r\n          pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n          time: \"16:45\"\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据用于搜索\r\n      _this.yuanshiquns = [..._this.quns];\r\n      _this.yuanshiusers = [..._this.users];\r\n\r\n      // 设置默认选中第一个群聊\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = 0;\r\n\r\n      // 加载第一个群聊的消息\r\n      setTimeout(() => {\r\n        _this.loadTestMessages();\r\n      }, 500);\r\n    },\r\n    // 加载测试消息数据\r\n    loadTestMessages() {\r\n      console.log('加载测试消息, selectId:', _this.selectId, 'quliaoIndex:', _this.quliaoIndex);\r\n      // 设置当前聊天标题\r\n      if (_this.selectId !== -1) {\r\n        _this.title = _this.users[_this.selectId].title;\r\n        // 加载私聊消息\r\n        _this.loadPrivateMessages();\r\n      } else {\r\n        const qun = _this.quns[_this.quliaoIndex];\r\n        const count = qun.uid.split(',').length + qun.lvshi_id.split(',').length + qun.yuangong_id.split(',').length;\r\n        _this.title = qun.title + \"(\" + count + \")\";\r\n        // 加载群聊消息\r\n        _this.loadGroupMessages();\r\n      }\r\n\r\n      // 滚动到底部\r\n      _this.$nextTick(() => {\r\n        if (_this.$refs.list) {\r\n          _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n        }\r\n      });\r\n    },\r\n    // 加载群聊消息\r\n    loadGroupMessages() {\r\n      const groupMessages = {\r\n        0: [ // 法务团队群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:00:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"大家好，今天我们讨论一下最新的合同审核流程\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:05:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张专员\",\r\n            type: \"text\",\r\n            content: \"好的，我这边已经准备好相关材料了\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 09:10:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"请大家查看一下这份合同模板\"\r\n          },\r\n          {\r\n            id: 4,\r\n            create_time: \"2024-01-22 09:12:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"image\",\r\n            content: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          },\r\n          {\r\n            id: 5,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"这个模板看起来不错，我们可以在此基础上进行修改\"\r\n          }\r\n        ],\r\n        1: [ // 客户服务群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:00:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"请问合同什么时候能完成？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，合同预计明天下午可以完成审核\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:10:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢！\"\r\n          }\r\n        ],\r\n        2: [ // 紧急处理群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 15:00:00\",\r\n            yuangong_id: 5,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李四\",\r\n            type: \"text\",\r\n            content: \"这个案件需要加急处理\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 15:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，我立即安排处理\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 15:10:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"我这边也会配合加急处理\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = groupMessages[_this.quliaoIndex] || [];\r\n      console.log('群聊消息加载完成:', _this.list.length);\r\n    },\r\n    // 加载私聊消息\r\n    loadPrivateMessages() {\r\n      const privateMessages = {\r\n        0: [ // 张三（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:30:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"您好，请问我的合同审核进度如何？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:35:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，您的合同正在审核中，预计今天下午可以完成\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:40:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢您！\"\r\n          }\r\n        ],\r\n        1: [ // 李四（律师）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"合同已经审核完毕，请查收\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:20:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，辛苦了！\"\r\n          }\r\n        ],\r\n        2: [ // 王五（调解员）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-21 16:00:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"王调解员\",\r\n            type: \"text\",\r\n            content: \"调解会议安排在明天下午2点\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-21 16:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"好的，我会准时参加\"\r\n          }\r\n        ],\r\n        3: [ // 赵六（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 16:45:00\",\r\n            yuangong_id: 6,\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n            title: \"赵六\",\r\n            type: \"text\",\r\n            content: \"谢谢您的帮助！\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 16:50:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"不客气，有问题随时联系我\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = privateMessages[_this.selectId] || [];\r\n      console.log('私聊消息加载完成:', _this.list.length);\r\n    },\r\n\r\n    // 加载测试群成员数据\r\n    loadTestMembers() {\r\n      // 用户列表\r\n      _this.userss = [\r\n        {\r\n          list: [\r\n            {\r\n              id: 1,\r\n              nickname: \"张三\",\r\n              headimg: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n            },\r\n            {\r\n              id: 2,\r\n              nickname: \"李四\",\r\n              headimg: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            },\r\n            {\r\n              id: 3,\r\n              nickname: \"王五\",\r\n              headimg: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 员工列表\r\n      _this.yuangongss = [\r\n        {\r\n          zhiwei: \"律师\",\r\n          list: [\r\n            {\r\n              id: 1,\r\n              title: \"李律师\",\r\n              pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            },\r\n            {\r\n              id: 2,\r\n              title: \"陈律师\",\r\n              pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          zhiwei: \"调解员\",\r\n          list: [\r\n            {\r\n              id: 3,\r\n              title: \"王调解员\",\r\n              pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          zhiwei: \"法务专员\",\r\n          list: [\r\n            {\r\n              id: 4,\r\n              title: \"张专员\",\r\n              pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n            },\r\n            {\r\n              id: 5,\r\n              title: \"赵专员\",\r\n              pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 工单测试数据\r\n      _this.gridData = [\r\n        {\r\n          id: 1,\r\n          create_time: \"2024-01-20\",\r\n          title: \"合同审核\",\r\n          desc: \"需要审核一份购销合同\",\r\n          type_title: \"法律咨询\",\r\n          is_deal_title: \"处理中\"\r\n        },\r\n        {\r\n          id: 2,\r\n          create_time: \"2024-01-21\",\r\n          title: \"债务追讨\",\r\n          desc: \"客户需要追讨欠款\",\r\n          type_title: \"债务处理\",\r\n          is_deal_title: \"已完成\"\r\n        },\r\n        {\r\n          id: 3,\r\n          create_time: \"2024-01-22\",\r\n          title: \"律师函起草\",\r\n          desc: \"起草催款律师函\",\r\n          type_title: \"文书制作\",\r\n          is_deal_title: \"待处理\"\r\n        }\r\n      ];\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    // 使用测试数据替代API调用\r\n    _this.loadTestData();\r\n    _this.yon_id = 1; // 设置当前用户ID\r\n    // 注释掉定时器，避免不必要的API调用\r\n    // _this.timer = setInterval(() => {\r\n    //   _this.getMoreList();\r\n    // }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 群成员侧边栏 */\r\n.member-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -300px;\r\n  width: 300px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.member-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n</style>\r\n"]}]}