{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue", "mtime": 1748617691749}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["taocan.vue"], "names": [], "mappings": ";AA+ZA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "taocan.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n  <div class=\"package-management-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-box\"></i>\r\n          套餐类型管理\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务套餐产品和价格配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"getData\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-box\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">套餐总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon price-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ averagePrice }}</div>\r\n              <div class=\"stat-label\">平均价格</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon premium-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ premiumPackages }}</div>\r\n              <div class=\"stat-label\">高端套餐</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon duration-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ averageYear }}年</div>\r\n              <div class=\"stat-label\">平均年限</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 稳定\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新增套餐\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入套餐名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"价格范围\">\r\n            <el-input-number \r\n              v-model=\"search.minPrice\" \r\n              placeholder=\"最低价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n            <span style=\"margin: 0 8px\">-</span>\r\n            <el-input-number \r\n              v-model=\"search.maxPrice\" \r\n              placeholder=\"最高价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 套餐展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"package-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          套餐列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"package-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"pkg in filteredPackages\" \r\n          :key=\"pkg.id\"\r\n          class=\"package-item\"\r\n        >\r\n          <div class=\"package-header\">\r\n            <div class=\"package-title\">{{ pkg.title }}</div>\r\n            <div class=\"package-price\">¥{{ pkg.price }}</div>\r\n          </div>\r\n          \r\n          <div class=\"package-content\">\r\n            <div class=\"package-info\">\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ pkg.year }}年服务</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-sort\"></i>\r\n                <span>排序: {{ pkg.sort }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"package-desc\">\r\n              {{ pkg.desc || '暂无描述' }}\r\n            </div>\r\n            \r\n            <div class=\"package-features\" v-if=\"pkg.services && pkg.services.length > 0\">\r\n              <div class=\"feature-title\">包含服务:</div>\r\n              <div class=\"feature-list\">\r\n                <el-tag \r\n                  v-for=\"service in pkg.services.slice(0, 3)\" \r\n                  :key=\"service.id\"\r\n                  size=\"mini\"\r\n                  class=\"feature-tag\"\r\n                >\r\n                  {{ service.name }}\r\n                </el-tag>\r\n                <span v-if=\"pkg.services.length > 3\" class=\"more-services\">\r\n                  +{{ pkg.services.length - 3 }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"package-footer\">\r\n            <div class=\"package-meta\">\r\n              <span class=\"create-time\">{{ formatDate(pkg.create_time) }}</span>\r\n            </div>\r\n            <div class=\"package-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(pkg.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, pkg.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n          :data=\"filteredPackages\" \r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"套餐信息\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-package-info\">\r\n                <div class=\"table-package-title\">{{ scope.row.title }}</div>\r\n                <div class=\"table-package-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"价格\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"price-display\">¥{{ scope.row.price }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"year\" label=\"年限\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"info\" size=\"small\">{{ scope.row.year }}年</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" />\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐名称\" prop=\"title\">\r\n              <el-input \r\n                v-model=\"ruleForm.title\" \r\n                placeholder=\"请输入套餐名称\"\r\n                autocomplete=\"off\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐价格\" prop=\"price\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.price\"\r\n                :min=\"0\"\r\n                :max=\"999999\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入价格\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务年限\" prop=\"year\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.year\"\r\n                :min=\"1\"\r\n                :max=\"10\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入年限\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number\r\n                v-model=\"ruleForm.sort\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"数字越小排序越靠前\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-form-item label=\"套餐内容\" prop=\"good\">\r\n          <div class=\"service-selection\">\r\n            <div class=\"service-title\">选择包含的服务类型:</div>\r\n            <div class=\"service-list\">\r\n              <div \r\n              v-for=\"(item, index) in types\"\r\n              :key=\"index\"\r\n                class=\"service-item\"\r\n            >\r\n                <div class=\"service-checkbox\">\r\n                  <el-checkbox v-model=\"item.checked\" :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n                </div>\r\n                <div class=\"service-input\" v-if=\"item.is_num == 1 && item.checked\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                    size=\"small\"\r\n                    placeholder=\"次数\"\r\n                  />\r\n                  <span class=\"input-suffix\">次</span>\r\n                </div>\r\n                <div class=\"service-unlimited\" v-else-if=\"item.checked\">\r\n                  <el-tag size=\"small\" type=\"success\">不限次数</el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"套餐描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入套餐详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"PackageManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写套餐名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"120px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    averagePrice() {\r\n      if (this.tableData.length === 0) return '0';\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\r\n      return Math.round(total / this.tableData.length).toLocaleString();\r\n    },\r\n    premiumPackages() {\r\n      return this.tableData.filter(item => parseFloat(item.price) > 10000).length;\r\n    },\r\n    averageYear() {\r\n      if (this.tableData.length === 0) return 1;\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseInt(item.year) || 1), 0);\r\n      return Math.round(total / this.tableData.length);\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑套餐' : '新增套餐';\r\n    },\r\n    filteredPackages() {\r\n      let filtered = this.tableData;\r\n      \r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filtered = filtered.filter(item => \r\n          (item.title && item.title.toLowerCase().includes(keyword)) ||\r\n          (item.desc && item.desc.toLowerCase().includes(keyword))\r\n        );\r\n      }\r\n      \r\n      // 价格范围筛选\r\n      if (this.search.minPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) >= this.search.minPrice);\r\n      }\r\n      \r\n      if (this.search.maxPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) <= this.search.maxPrice);\r\n      }\r\n      \r\n      return filtered;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n          // 处理服务类型选择\r\n          this.ruleForm.good = this.types\r\n            .filter(type => type.checked)\r\n            .map(type => type.id);\r\n          \r\n          // 模拟保存操作\r\n          setTimeout(() => {\r\n            this.saveLoading = false;\r\n            this.dialogFormVisible = false;\r\n            this.$message.success('保存成功');\r\n            this.getData();\r\n          }, 1000);\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.package-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.price-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.premium-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.duration-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .package-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 套餐网格视图 */\r\n.package-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.package-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.package-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.package-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.package-price {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #ffd04b;\r\n}\r\n\r\n.package-content {\r\n  padding: 20px;\r\n}\r\n\r\n.package-info {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.package-desc {\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  min-height: 40px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.package-features {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.feature-title {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.feature-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  align-items: center;\r\n}\r\n\r\n.feature-tag {\r\n  background: #f0f9ff;\r\n  color: #0369a1;\r\n  border: 1px solid #e0f2fe;\r\n}\r\n\r\n.more-services {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  font-style: italic;\r\n}\r\n\r\n.package-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.package-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.package-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-package-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-package-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.price-display {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 服务选择区域 */\r\n.service-selection {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background: #fafafa;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-item:hover {\r\n  border-color: #409EFF;\r\n  background: #f8f9ff;\r\n}\r\n\r\n.service-checkbox {\r\n  flex: 1;\r\n}\r\n\r\n.service-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.input-suffix {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.service-unlimited {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .package-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n  width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .package-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .package-header {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-input {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}