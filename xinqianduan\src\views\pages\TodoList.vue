<template>
  <div class="todo-container">
    <div class="page-header">
      <h2>待办事项管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <i class="el-icon-plus"></i> 新增待办事项
      </el-button>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="未完成" value="0"></el-option>
            <el-option label="已完成" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="filterForm.priority" placeholder="请选择优先级" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="高" value="high"></el-option>
            <el-option label="中" value="medium"></el-option>
            <el-option label="低" value="low"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filterForm.type" placeholder="请选择类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="债务处理" value="debt"></el-option>
            <el-option label="订单处理" value="order"></el-option>
            <el-option label="用户管理" value="user"></el-option>
            <el-option label="系统任务" value="system"></el-option>
            <el-option label="一般任务" value="general"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadTodos">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 待办事项列表 -->
    <el-card class="list-card">
      <el-table :data="todoList" v-loading="loading" stripe>
        <el-table-column prop="title" label="标题" min-width="200">
          <template slot-scope="scope">
            <div class="todo-title">
              <el-checkbox 
                v-model="scope.row.completed" 
                @change="handleStatusChange(scope.row)"
                :disabled="scope.row.status === 2"
              ></el-checkbox>
              <span :class="{ 'completed': scope.row.completed }">{{ scope.row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="type_text" label="类型" width="100"></el-table-column>
        <el-table-column prop="priority_text" label="优先级" width="80">
          <template slot-scope="scope">
            <el-tag 
              :type="getPriorityType(scope.row.priority)" 
              size="small"
            >
              {{ scope.row.priority_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="due_date" label="截止时间" width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.due_date" :class="{ 'overdue': isOverdue(scope.row.due_date) }">
              {{ scope.row.due_date }}
            </span>
            <span v-else class="no-due-date">无</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.completed ? 'success' : 'info'" 
              size="small"
            >
              {{ scope.row.completed ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editTodo(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteTodo(scope.row)" style="color: #f56c6c;">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      :title="editingTodo.id ? '编辑待办事项' : '新增待办事项'" 
      :visible.sync="showAddDialog"
      width="600px"
    >
      <el-form :model="editingTodo" :rules="todoRules" ref="todoForm" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="editingTodo.title" placeholder="请输入待办事项标题"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            type="textarea" 
            v-model="editingTodo.description" 
            placeholder="请输入详细描述"
            :rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="editingTodo.type" placeholder="请选择类型">
            <el-option label="债务处理" value="debt"></el-option>
            <el-option label="订单处理" value="order"></el-option>
            <el-option label="用户管理" value="user"></el-option>
            <el-option label="系统任务" value="system"></el-option>
            <el-option label="一般任务" value="general"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="editingTodo.priority" placeholder="请选择优先级">
            <el-option label="高" value="high"></el-option>
            <el-option label="中" value="medium"></el-option>
            <el-option label="低" value="low"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="editingTodo.due_date"
            type="datetime"
            placeholder="选择截止时间"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTodo">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRequest, postRequest, putRequest, deleteRequest } from '@/utils/api'

export default {
  name: 'TodoList',
  mixins: [{ methods: { getRequest, postRequest, putRequest, deleteRequest } }],
  data() {
    return {
      loading: false,
      showAddDialog: false,
      todoList: [],
      filterForm: {
        status: '',
        priority: '',
        type: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      editingTodo: {
        id: null,
        title: '',
        description: '',
        type: 'general',
        priority: 'medium',
        due_date: null
      },
      todoRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        priority: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.loadTodos()
  },
  methods: {
    async loadTodos() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.filterForm
        }
        const response = await this.getRequest('/todo/list', params)
        if (response.code === 200) {
          this.todoList = response.data.list || []
          this.pagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载待办事项失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    async handleStatusChange(todo) {
      try {
        const response = await this.postRequest('/dashboard/updateTodo', {
          id: todo.id,
          completed: todo.completed
        })
        if (response.code === 200) {
          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')
        }
      } catch (error) {
        console.error('更新状态失败:', error)
        todo.completed = !todo.completed // 回滚
        this.$message.error('更新失败')
      }
    },

    editTodo(todo) {
      this.editingTodo = { ...todo }
      this.showAddDialog = true
    },

    async deleteTodo(todo) {
      try {
        await this.$confirm('确定要删除这个待办事项吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.deleteRequest('/todo/delete', { id: todo.id })
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadTodos()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    async saveTodo() {
      try {
        await this.$refs.todoForm.validate()
        
        const isEdit = !!this.editingTodo.id
        const url = isEdit ? '/todo/update' : '/todo/create'
        const response = await this.postRequest(url, this.editingTodo)
        
        if (response.code === 200) {
          this.$message.success(isEdit ? '更新成功' : '创建成功')
          this.showAddDialog = false
          this.resetForm()
          this.loadTodos()
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      }
    },

    resetForm() {
      this.editingTodo = {
        id: null,
        title: '',
        description: '',
        type: 'general',
        priority: 'medium',
        due_date: null
      }
      this.$refs.todoForm && this.$refs.todoForm.resetFields()
    },

    resetFilter() {
      this.filterForm = {
        status: '',
        priority: '',
        type: ''
      }
      this.pagination.page = 1
      this.loadTodos()
    },

    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadTodos()
    },

    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadTodos()
    },

    getPriorityType(priority) {
      const map = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return map[priority] || 'info'
    },

    isOverdue(dueDate) {
      if (!dueDate) return false
      return new Date(dueDate) < new Date()
    }
  }
}
</script>

<style scoped>
.todo-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin-bottom: 0;
}

.list-card {
  margin-bottom: 20px;
}

.todo-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-title .completed {
  text-decoration: line-through;
  color: #909399;
}

.overdue {
  color: #f56c6c;
  font-weight: bold;
}

.no-due-date {
  color: #c0c4cc;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
