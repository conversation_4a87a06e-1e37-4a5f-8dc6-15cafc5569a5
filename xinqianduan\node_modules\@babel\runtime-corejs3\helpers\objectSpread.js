var _Object$keys = require("core-js-pure/features/object/keys.js");
var _Object$getOwnPropertySymbols = require("core-js-pure/features/object/get-own-property-symbols.js");
var _pushInstanceProperty = require("core-js-pure/features/instance/push.js");
var _filterInstanceProperty = require("core-js-pure/features/instance/filter.js");
var _Object$getOwnPropertyDescriptor = require("core-js-pure/features/object/get-own-property-descriptor.js");
var _forEachInstanceProperty = require("core-js-pure/features/instance/for-each.js");
var defineProperty = require("./defineProperty.js");
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var _context;
    var t = null != arguments[r] ? Object(arguments[r]) : {},
      o = _Object$keys(t);
    "function" == typeof _Object$getOwnPropertySymbols && _pushInstanceProperty(o).apply(o, _filterInstanceProperty(_context = _Object$getOwnPropertySymbols(t)).call(_context, function (e) {
      return _Object$getOwnPropertyDescriptor(t, e).enumerable;
    })), _forEachInstanceProperty(o).call(o, function (r) {
      defineProperty(e, r, t[r]);
    });
  }
  return e;
}
module.exports = _objectSpread, module.exports.__esModule = true, module.exports["default"] = module.exports;