{"version": 3, "names": ["_core", "require", "_helperEnvironmentVisitor", "findBareSupers", "traverse", "visitors", "merge", "Super", "path", "node", "parentPath", "isCallExpression", "callee", "push", "environmentVisitor", "referenceVisitor", "TSTypeAnnotation|TypeAnnotation", "skip", "ReferencedIdentifier", "scope", "hasOwnBinding", "name", "rename", "handleClassTDZ", "state", "classBinding", "getBinding", "classNameTDZError", "file", "addHelper", "throwNode", "t", "callExpression", "stringLiteral", "replaceWith", "sequenceExpression", "classFieldDefinitionEvaluationTDZVisitor", "injectInitialization", "constructor", "nodes", "renamer", "lastReturnsThis", "length", "isDerived", "superClass", "newConstructor", "classMethod", "identifier", "blockStatement", "params", "restElement", "body", "template", "statement", "ast", "get", "unshiftContainer", "bareSupers", "<PERSON><PERSON><PERSON><PERSON>", "bareSuper", "map", "n", "cloneNode", "isExpressionStatement", "allNodes", "toExpression", "thisExpression", "insertAfter", "memoiseComputedKey", "keyNode", "hint", "isUidReference", "isIdentifier", "hasUid", "isMemoiseAssignment", "isAssignmentExpression", "operator", "left", "ident", "id", "kind", "assignmentExpression", "extractComputedKeys", "computedPaths", "declarations", "computedPath", "computedKey", "isReferencedIdentifier", "computedNode", "isConstantExpression", "assignment", "generateUidBasedOnNode", "expressionStatement", "key"], "sources": ["../src/misc.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File, NodePath, Scope, Visitor } from \"@babel/core\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nconst findBareSupers = traverse.visitors.merge<NodePath<t.CallExpression>[]>([\n  {\n    Super(path) {\n      const { node, parentPath } = path;\n      if (parentPath.isCallExpression({ callee: node })) {\n        this.push(parentPath);\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst referenceVisitor: Visitor<{ scope: Scope }> = {\n  \"TSTypeAnnotation|TypeAnnotation\"(\n    path: NodePath<t.TSTypeAnnotation | t.TypeAnnotation>,\n  ) {\n    path.skip();\n  },\n\n  ReferencedIdentifier(path: NodePath<t.Identifier>, { scope }) {\n    if (scope.hasOwnBinding(path.node.name)) {\n      scope.rename(path.node.name);\n      path.skip();\n    }\n  },\n};\n\ntype HandleClassTDZState = {\n  classBinding: Scope.Binding;\n  file: File;\n};\n\nfunction handleClassTDZ(\n  path: NodePath<t.Identifier>,\n  state: HandleClassTDZState,\n) {\n  if (\n    state.classBinding &&\n    state.classBinding === path.scope.getBinding(path.node.name)\n  ) {\n    const classNameTDZError = state.file.addHelper(\"classNameTDZError\");\n    const throwNode = t.callExpression(classNameTDZError, [\n      t.stringLiteral(path.node.name),\n    ]);\n\n    path.replaceWith(t.sequenceExpression([throwNode, path.node]));\n    path.skip();\n  }\n}\n\nconst classFieldDefinitionEvaluationTDZVisitor: Visitor<HandleClassTDZState> = {\n  ReferencedIdentifier: handleClassTDZ,\n};\n\ninterface RenamerState {\n  scope: Scope;\n}\n\nexport function injectInitialization(\n  path: NodePath<t.Class>,\n  constructor: NodePath<t.ClassMethod> | undefined,\n  nodes: t.ExpressionStatement[],\n  renamer?: (visitor: Visitor<RenamerState>, state: RenamerState) => void,\n  lastReturnsThis?: boolean,\n) {\n  if (!nodes.length) return;\n\n  const isDerived = !!path.node.superClass;\n\n  if (!constructor) {\n    const newConstructor = t.classMethod(\n      \"constructor\",\n      t.identifier(\"constructor\"),\n      [],\n      t.blockStatement([]),\n    );\n\n    if (isDerived) {\n      newConstructor.params = [t.restElement(t.identifier(\"args\"))];\n      newConstructor.body.body.push(template.statement.ast`super(...args)`);\n    }\n\n    [constructor] = path\n      .get(\"body\")\n      .unshiftContainer(\"body\", newConstructor) as NodePath<t.ClassMethod>[];\n  }\n\n  if (renamer) {\n    renamer(referenceVisitor, { scope: constructor.scope });\n  }\n\n  if (isDerived) {\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    constructor.traverse(findBareSupers, bareSupers);\n    let isFirst = true;\n    for (const bareSuper of bareSupers) {\n      if (isFirst) {\n        isFirst = false;\n      } else {\n        nodes = nodes.map(n => t.cloneNode(n));\n      }\n      if (!bareSuper.parentPath.isExpressionStatement()) {\n        const allNodes: t.Expression[] = [\n          bareSuper.node,\n          ...nodes.map(n => t.toExpression(n)),\n        ];\n        if (!lastReturnsThis) allNodes.push(t.thisExpression());\n        bareSuper.replaceWith(t.sequenceExpression(allNodes));\n      } else {\n        bareSuper.insertAfter(nodes);\n      }\n    }\n  } else {\n    constructor.get(\"body\").unshiftContainer(\"body\", nodes);\n  }\n}\n\ntype ComputedKeyAssignmentExpression = t.AssignmentExpression & {\n  left: t.Identifier;\n};\n\n/**\n * Try to memoise a computed key.\n * It returns undefined when the computed key is an uid reference, otherwise\n * an assignment expression `memoiserId = computed key`\n * @export\n * @param {t.Expression} keyNode Computed key\n * @param {Scope} scope The scope where memoiser id should be registered\n * @param {string} hint The memoiser id hint\n * @returns {(ComputedKeyAssignmentExpression | undefined)}\n */\nexport function memoiseComputedKey(\n  keyNode: t.Expression,\n  scope: Scope,\n  hint: string,\n): ComputedKeyAssignmentExpression | undefined {\n  const isUidReference = t.isIdentifier(keyNode) && scope.hasUid(keyNode.name);\n  if (isUidReference) {\n    return;\n  }\n  const isMemoiseAssignment =\n    t.isAssignmentExpression(keyNode, { operator: \"=\" }) &&\n    t.isIdentifier(keyNode.left) &&\n    scope.hasUid(keyNode.left.name);\n  if (isMemoiseAssignment) {\n    return t.cloneNode(keyNode as ComputedKeyAssignmentExpression);\n  } else {\n    const ident = t.identifier(hint);\n    // Declaring in the same block scope\n    // Ref: https://github.com/babel/babel/pull/10029/files#diff-fbbdd83e7a9c998721c1484529c2ce92\n    scope.push({\n      id: ident,\n      kind: \"let\",\n    });\n    return t.assignmentExpression(\n      \"=\",\n      t.cloneNode(ident),\n      keyNode,\n    ) as ComputedKeyAssignmentExpression;\n  }\n}\n\nexport function extractComputedKeys(\n  path: NodePath<t.Class>,\n  computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[],\n  file: File,\n) {\n  const { scope } = path;\n  const declarations: t.ExpressionStatement[] = [];\n  const state = {\n    classBinding: path.node.id && scope.getBinding(path.node.id.name),\n    file,\n  };\n  for (const computedPath of computedPaths) {\n    const computedKey = computedPath.get(\"key\");\n    if (computedKey.isReferencedIdentifier()) {\n      handleClassTDZ(computedKey, state);\n    } else {\n      computedKey.traverse(classFieldDefinitionEvaluationTDZVisitor, state);\n    }\n\n    const computedNode = computedPath.node;\n    // Make sure computed property names are only evaluated once (upon class definition)\n    // and in the right order in combination with static properties\n    if (!computedKey.isConstantExpression()) {\n      const assignment = memoiseComputedKey(\n        computedKey.node,\n        scope,\n        scope.generateUidBasedOnNode(computedKey.node),\n      );\n      if (assignment) {\n        declarations.push(t.expressionStatement(assignment));\n        computedNode.key = t.cloneNode(assignment.left);\n      }\n    }\n  }\n\n  return declarations;\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,yBAAA,GAAAD,OAAA;AAEA,MAAME,cAAc,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAA+B,CAC3E;EACEC,KAAKA,CAACC,IAAI,EAAE;IACV,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGF,IAAI;IACjC,IAAIE,UAAU,CAACC,gBAAgB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;MACjD,IAAI,CAACI,IAAI,CAACH,UAAU,CAAC;IACvB;EACF;AACF,CAAC,EACDI,iCAAkB,CACnB,CAAC;AAEF,MAAMC,gBAA2C,GAAG;EAClD,iCAAiCC,CAC/BR,IAAqD,EACrD;IACAA,IAAI,CAACS,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,oBAAoBA,CAACV,IAA4B,EAAE;IAAEW;EAAM,CAAC,EAAE;IAC5D,IAAIA,KAAK,CAACC,aAAa,CAACZ,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAAE;MACvCF,KAAK,CAACG,MAAM,CAACd,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC;MAC5Bb,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAOD,SAASM,cAAcA,CACrBf,IAA4B,EAC5BgB,KAA0B,EAC1B;EACA,IACEA,KAAK,CAACC,YAAY,IAClBD,KAAK,CAACC,YAAY,KAAKjB,IAAI,CAACW,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAC5D;IACA,MAAMM,iBAAiB,GAAGH,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC,mBAAmB,CAAC;IACnE,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACL,iBAAiB,EAAE,CACpDI,WAAC,CAACE,aAAa,CAACzB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,CAChC,CAAC;IAEFb,IAAI,CAAC0B,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC,CAACL,SAAS,EAAEtB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9DD,IAAI,CAACS,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMmB,wCAAsE,GAAG;EAC7ElB,oBAAoB,EAAEK;AACxB,CAAC;AAMM,SAASc,oBAAoBA,CAClC7B,IAAuB,EACvB8B,WAAgD,EAChDC,KAA8B,EAC9BC,OAAuE,EACvEC,eAAyB,EACzB;EACA,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;EAEnB,MAAMC,SAAS,GAAG,CAAC,CAACnC,IAAI,CAACC,IAAI,CAACmC,UAAU;EAExC,IAAI,CAACN,WAAW,EAAE;IAChB,MAAMO,cAAc,GAAGd,WAAC,CAACe,WAAW,CAClC,aAAa,EACbf,WAAC,CAACgB,UAAU,CAAC,aAAa,CAAC,EAC3B,EAAE,EACFhB,WAAC,CAACiB,cAAc,CAAC,EAAE,CACrB,CAAC;IAED,IAAIL,SAAS,EAAE;MACbE,cAAc,CAACI,MAAM,GAAG,CAAClB,WAAC,CAACmB,WAAW,CAACnB,WAAC,CAACgB,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;MAC7DF,cAAc,CAACM,IAAI,CAACA,IAAI,CAACtC,IAAI,CAACuC,cAAQ,CAACC,SAAS,CAACC,GAAI,gBAAe,CAAC;IACvE;IAEA,CAAChB,WAAW,CAAC,GAAG9B,IAAI,CACjB+C,GAAG,CAAC,MAAM,CAAC,CACXC,gBAAgB,CAAC,MAAM,EAAEX,cAAc,CAA8B;EAC1E;EAEA,IAAIL,OAAO,EAAE;IACXA,OAAO,CAACzB,gBAAgB,EAAE;MAAEI,KAAK,EAAEmB,WAAW,CAACnB;IAAM,CAAC,CAAC;EACzD;EAEA,IAAIwB,SAAS,EAAE;IACb,MAAMc,UAAwC,GAAG,EAAE;IACnDnB,WAAW,CAAClC,QAAQ,CAACD,cAAc,EAAEsD,UAAU,CAAC;IAChD,IAAIC,OAAO,GAAG,IAAI;IAClB,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;MAClC,IAAIC,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLnB,KAAK,GAAGA,KAAK,CAACqB,GAAG,CAACC,CAAC,IAAI9B,WAAC,CAAC+B,SAAS,CAACD,CAAC,CAAC,CAAC;MACxC;MACA,IAAI,CAACF,SAAS,CAACjD,UAAU,CAACqD,qBAAqB,CAAC,CAAC,EAAE;QACjD,MAAMC,QAAwB,GAAG,CAC/BL,SAAS,CAAClD,IAAI,EACd,GAAG8B,KAAK,CAACqB,GAAG,CAACC,CAAC,IAAI9B,WAAC,CAACkC,YAAY,CAACJ,CAAC,CAAC,CAAC,CACrC;QACD,IAAI,CAACpB,eAAe,EAAEuB,QAAQ,CAACnD,IAAI,CAACkB,WAAC,CAACmC,cAAc,CAAC,CAAC,CAAC;QACvDP,SAAS,CAACzB,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC6B,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACLL,SAAS,CAACQ,WAAW,CAAC5B,KAAK,CAAC;MAC9B;IACF;EACF,CAAC,MAAM;IACLD,WAAW,CAACiB,GAAG,CAAC,MAAM,CAAC,CAACC,gBAAgB,CAAC,MAAM,EAAEjB,KAAK,CAAC;EACzD;AACF;AAgBO,SAAS6B,kBAAkBA,CAChCC,OAAqB,EACrBlD,KAAY,EACZmD,IAAY,EACiC;EAC7C,MAAMC,cAAc,GAAGxC,WAAC,CAACyC,YAAY,CAACH,OAAO,CAAC,IAAIlD,KAAK,CAACsD,MAAM,CAACJ,OAAO,CAAChD,IAAI,CAAC;EAC5E,IAAIkD,cAAc,EAAE;IAClB;EACF;EACA,MAAMG,mBAAmB,GACvB3C,WAAC,CAAC4C,sBAAsB,CAACN,OAAO,EAAE;IAAEO,QAAQ,EAAE;EAAI,CAAC,CAAC,IACpD7C,WAAC,CAACyC,YAAY,CAACH,OAAO,CAACQ,IAAI,CAAC,IAC5B1D,KAAK,CAACsD,MAAM,CAACJ,OAAO,CAACQ,IAAI,CAACxD,IAAI,CAAC;EACjC,IAAIqD,mBAAmB,EAAE;IACvB,OAAO3C,WAAC,CAAC+B,SAAS,CAACO,OAA0C,CAAC;EAChE,CAAC,MAAM;IACL,MAAMS,KAAK,GAAG/C,WAAC,CAACgB,UAAU,CAACuB,IAAI,CAAC;IAGhCnD,KAAK,CAACN,IAAI,CAAC;MACTkE,EAAE,EAAED,KAAK;MACTE,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOjD,WAAC,CAACkD,oBAAoB,CAC3B,GAAG,EACHlD,WAAC,CAAC+B,SAAS,CAACgB,KAAK,CAAC,EAClBT,OACF,CAAC;EACH;AACF;AAEO,SAASa,mBAAmBA,CACjC1E,IAAuB,EACvB2E,aAA0D,EAC1DvD,IAAU,EACV;EACA,MAAM;IAAET;EAAM,CAAC,GAAGX,IAAI;EACtB,MAAM4E,YAAqC,GAAG,EAAE;EAChD,MAAM5D,KAAK,GAAG;IACZC,YAAY,EAAEjB,IAAI,CAACC,IAAI,CAACsE,EAAE,IAAI5D,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAACsE,EAAE,CAAC1D,IAAI,CAAC;IACjEO;EACF,CAAC;EACD,KAAK,MAAMyD,YAAY,IAAIF,aAAa,EAAE;IACxC,MAAMG,WAAW,GAAGD,YAAY,CAAC9B,GAAG,CAAC,KAAK,CAAC;IAC3C,IAAI+B,WAAW,CAACC,sBAAsB,CAAC,CAAC,EAAE;MACxChE,cAAc,CAAC+D,WAAW,EAAE9D,KAAK,CAAC;IACpC,CAAC,MAAM;MACL8D,WAAW,CAAClF,QAAQ,CAACgC,wCAAwC,EAAEZ,KAAK,CAAC;IACvE;IAEA,MAAMgE,YAAY,GAAGH,YAAY,CAAC5E,IAAI;IAGtC,IAAI,CAAC6E,WAAW,CAACG,oBAAoB,CAAC,CAAC,EAAE;MACvC,MAAMC,UAAU,GAAGtB,kBAAkB,CACnCkB,WAAW,CAAC7E,IAAI,EAChBU,KAAK,EACLA,KAAK,CAACwE,sBAAsB,CAACL,WAAW,CAAC7E,IAAI,CAC/C,CAAC;MACD,IAAIiF,UAAU,EAAE;QACdN,YAAY,CAACvE,IAAI,CAACkB,WAAC,CAAC6D,mBAAmB,CAACF,UAAU,CAAC,CAAC;QACpDF,YAAY,CAACK,GAAG,GAAG9D,WAAC,CAAC+B,SAAS,CAAC4B,UAAU,CAACb,IAAI,CAAC;MACjD;IACF;EACF;EAEA,OAAOO,YAAY;AACrB", "ignoreList": []}