{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\Home.vue", "mtime": 1748281481455}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <el-submenu v-for=\"(item, index) in menus\" :key=\"index\" :index=\"item.path\">\r\n            <template slot=\"title\">{{ item.name }}</template>\r\n            <el-menu-item\r\n              v-for=\"(child, indexj) in item.children\"\r\n              :key=\"indexj\"\r\n              :index=\"child.path\"\r\n            >\r\n              {{ child.name }}\r\n            </el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">管理员</span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item\r\n              ><div @click=\"menuClick('/changePwd')\">\r\n                修改密码\r\n              </div></el-dropdown-item\r\n            >\r\n            <el-dropdown-item>\r\n              <div @click=\"logout()\">退出登录</div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{\r\n            this.$router.currentRoute.name\r\n          }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <el-row :gutter=\"12\" v-if=\"this.$router.currentRoute.path == '/'\">\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\"> 访问量 {{ visit_count }}</el-card>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\">\r\n              <span @click=\"showQrcode\">查看二维码</span></el-card\r\n            >\r\n          </el-col>\r\n        </el-row>\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户列表\" },\r\n          { path: \"/order\", name: \"支付列表\" },\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/debt\",\r\n        name: \"债权管理\",\r\n        children: [\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/shipin\",\r\n        name: \"视频管理\",\r\n        children: [\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"律师管理\",\r\n        children: [\r\n          { path: \"/lvshi\", name: \"律师列表\" },\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 150px;\r\n  min-width: 150px;\r\n  text-align: right;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n}\r\n\r\n.user-info:hover {\r\n  color: #ffd04b;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background-color: #f5f5f5;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-breadcrumb {\r\n  line-height: 50px;\r\n}\r\n\r\n/* 主内容区域样式 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f0f2f5;\r\n  padding: 20px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .top-header {\r\n    flex-direction: column;\r\n    height: auto;\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-center {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .top-menu {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}