{"name": "guzzlehttp/promises", "description": "Guzzle promises library", "keywords": ["promise"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Promise\\Tests\\": "tests/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true}, "preferred-install": "dist", "sort-packages": true}}