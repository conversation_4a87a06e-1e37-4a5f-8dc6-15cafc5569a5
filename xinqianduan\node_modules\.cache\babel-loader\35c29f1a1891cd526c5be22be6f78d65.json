{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\lawyer.vue", "mtime": 1748454232524}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "is_pay", "is_deal", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "showDebtorPanel", "activeTab", "currentDebtor", "aiGenerating", "aiGeneratedContent", "contractTypes", "processMethod", "fileGenerating", "aiGeneratedFile", "ruleForm", "is_num", "rules", "required", "message", "trigger", "file_path", "form<PERSON>abe<PERSON><PERSON>", "options", "id", "options1", "mounted", "getData", "getContractTypes", "document", "addEventListener", "handleKeyDown", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "getStatusType", "status", "statusMap", "getStatusText", "searchData", "clearSearch", "handleDialogClose", "$refs", "resetFields", "type_title", "desc", "content", "cancelDialog", "setTimeout", "template_file", "template_name", "template_size", "template_content", "generateLawyerLetter", "$message", "warning", "matchedTemplate", "findMatchingTemplate", "debtorInfo", "getDebtorInfo", "userInfo", "getUserInfo", "simulateAiGeneration", "success", "generateAiFile", "error", "console", "toLowerCase", "typeTitle", "matchRules", "keywords", "templateId", "rule", "some", "includes", "find", "type", "Promise", "resolve", "dt_name", "id_card", "phone", "address", "debt_amount", "debt_type", "debt_date", "repay_date", "uid", "register_time", "template", "replace", "Date", "toLocaleDateString", "trim", "useAiContent", "regenerateContent", "onProcessMethodChange", "method", "fileName", "getTime", "path", "changeFile", "filed", "log", "clearData", "editData", "_this", "getInfo", "testData", "order_sn", "create_time", "foundData", "item", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "then", "deleteRequest", "resp", "code", "msg", "catch", "delData", "index", "splice", "refulsh", "$router", "go", "event", "keyCode", "closeDebtorPanel", "showDebtorDetail", "row", "generateIdCard", "generatePhone", "generateAddress", "generateDebtAmount", "generateDebtType", "user_name", "generateUserName", "user_phone", "user_register_time", "generateRegisterTime", "user_status", "files", "generateFiles", "history", "generateHistory", "getFileIcon", "fileType", "iconMap", "previewFile", "file", "downloadFile", "prefixes", "prefix", "Math", "floor", "random", "length", "year", "month", "String", "padStart", "day", "suffix", "addresses", "amounts", "types", "surnames", "names", "surname", "debtor<PERSON>ame", "upload_time", "action", "description", "time", "filteredData", "filter", "startIndex", "endIndex", "pageData", "slice", "saveData", "validate", "valid", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "beforeUpload", "isTypeTrue", "test", "delImage", "getRequest"], "sources": ["src/views/pages/yonghu/lawyer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"lawyer-letter-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            发律师函管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和处理律师函制作工单</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">关键词搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入工单号/标题/用户手机号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">处理状态</label>\r\n              <el-select\r\n                v-model=\"search.is_deal\"\r\n                placeholder=\"请选择处理状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in options1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.title\"\r\n                  :value=\"item.id\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <h3>\r\n              <i class=\"el-icon-tickets\"></i>\r\n              律师函工单列表\r\n            </h3>\r\n            <span class=\"table-count\">共 {{ total }} 条记录</span>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"lawyer-table\"\r\n          stripe\r\n          border\r\n          empty-text=\"暂无律师函工单数据\"\r\n        >\r\n          <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"order-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"order-text\">{{ scope.row.order_sn }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"type\" label=\"工单类型\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ scope.row.type || '律师函' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <span class=\"title-text\" :title=\"scope.row.title\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"desc-cell\">\r\n                <span class=\"desc-text\" :title=\"scope.row.desc\">\r\n                  {{ scope.row.desc ? (scope.row.desc.length > 50 ? scope.row.desc.substring(0, 50) + '...' : scope.row.desc) : '-' }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getStatusType(scope.row.is_deal)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getStatusText(scope.row.is_deal) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"uid\" label=\"用户手机\" width=\"130\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"phone-cell\">\r\n                <i class=\"el-icon-phone\"></i>\r\n                <span>{{ scope.row.uid || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"dt_name\" label=\"债务人\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"debtor-cell clickable\" @click=\"showDebtorDetail(scope.row)\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"debtor-name\">{{ scope.row.dt_name || '-' }}</span>\r\n                <i class=\"el-icon-arrow-right arrow-icon\"></i>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-check\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                  :disabled=\"scope.row.is_deal === 2\"\r\n                >\r\n                  {{ scope.row.is_deal === 2 ? '已完成' : '完成制作' }}\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-close\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  取消\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <!-- 律师函处理对话框 -->\r\n    <el-dialog\r\n      title=\"律师函制作处理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"process-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <div class=\"dialog-content\">\r\n        <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-position=\"top\">\r\n          <!-- 工单基本信息 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-info\"></i>\r\n              工单基本信息\r\n            </h4>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单类型\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.type_title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-folder\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单标题\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"工单描述\">\r\n              <el-input\r\n                v-model=\"ruleForm.desc\"\r\n                readonly\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                class=\"readonly-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 处理状态设置 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              处理状态设置\r\n            </h4>\r\n            <el-form-item label=\"制作状态\">\r\n              <el-radio-group v-model=\"ruleForm.is_deal\" class=\"status-radio-group\">\r\n                <el-radio :label=\"1\" class=\"status-radio\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  处理中\r\n                </el-radio>\r\n                <el-radio :label=\"2\" class=\"status-radio\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  已完成\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- AI生成律师函 -->\r\n          <div class=\"form-section ai-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-cpu\"></i>\r\n              AI智能生成\r\n            </h4>\r\n            <div class=\"ai-generation-content\">\r\n              <div class=\"ai-description\">\r\n                <p>\r\n                  <i class=\"el-icon-info\"></i>\r\n                  AI将根据工单信息、债务人详情和合同模板自动生成律师函内容\r\n                </p>\r\n              </div>\r\n              <div class=\"ai-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-cpu\"\r\n                  @click=\"generateLawyerLetter\"\r\n                  :loading=\"aiGenerating\"\r\n                  class=\"ai-generate-btn\"\r\n                >\r\n                  {{ aiGenerating ? 'AI生成中...' : 'AI生成律师函' }}\r\n                </el-button>\r\n              </div>\r\n              <!-- AI生成结果展示 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-result\">\r\n                <h5 class=\"result-title\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  AI生成结果\r\n                </h5>\r\n                <div class=\"generated-content\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"8\"\r\n                    placeholder=\"AI生成的律师函内容将显示在这里...\"\r\n                    class=\"ai-content-textarea\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"ai-result-actions\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-check\"\r\n                    @click=\"useAiContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    使用此内容\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"regenerateContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    重新生成\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 完成处理区域 -->\r\n          <div v-if=\"ruleForm.is_deal == 2\" class=\"form-section completion-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-upload\"></i>\r\n              完成处理\r\n            </h4>\r\n\r\n            <!-- 处理方式选择 -->\r\n            <el-form-item label=\"处理方式\" class=\"process-method-item\">\r\n              <el-radio-group v-model=\"processMethod\" @change=\"onProcessMethodChange\" class=\"method-radio-group\">\r\n                <el-radio label=\"ai\" class=\"method-radio\">\r\n                  <i class=\"el-icon-cpu\"></i>\r\n                  AI智能生成\r\n                </el-radio>\r\n                <el-radio label=\"upload\" class=\"method-radio\">\r\n                  <i class=\"el-icon-upload\"></i>\r\n                  手动上传文件\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <!-- AI生成方式 -->\r\n            <div v-if=\"processMethod === 'ai'\" class=\"ai-process-section\">\r\n              <div class=\"ai-process-info\">\r\n                <el-alert\r\n                  title=\"AI生成模式\"\r\n                  description=\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\"\r\n                  type=\"info\"\r\n                  :closable=\"false\"\r\n                  show-icon\r\n                  class=\"ai-mode-alert\"\r\n                ></el-alert>\r\n              </div>\r\n\r\n              <!-- AI生成的内容预览 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-content-preview\">\r\n                <el-form-item label=\"生成内容预览\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"6\"\r\n                    placeholder=\"AI生成的律师函内容...\"\r\n                    class=\"ai-preview-textarea\"\r\n                    readonly\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <el-form-item label=\"文件生成\">\r\n                <div class=\"ai-file-generation\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"generateAiFile\"\r\n                    :loading=\"fileGenerating\"\r\n                    class=\"generate-file-btn\"\r\n                  >\r\n                    {{ fileGenerating ? '生成文件中...' : '生成律师函文件' }}\r\n                  </el-button>\r\n                  <div v-if=\"aiGeneratedFile\" class=\"generated-file-info\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <span>{{ aiGeneratedFile.name }}</span>\r\n                    <el-tag type=\"success\" size=\"mini\">已生成</el-tag>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <!-- 手动上传方式 -->\r\n            <div v-if=\"processMethod === 'upload'\" class=\"upload-process-section\">\r\n              <el-form-item\r\n                label=\"律师函文件\"\r\n                prop=\"file_path\"\r\n                class=\"file-upload-item\"\r\n              >\r\n                <div class=\"upload-area\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.file_path\"\r\n                    placeholder=\"请上传律师函文件\"\r\n                    readonly\r\n                    class=\"file-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                  <div class=\"upload-buttons\">\r\n                    <el-button @click=\"changeFile('file_path')\" type=\"primary\" icon=\"el-icon-upload\">\r\n                      <el-upload\r\n                        action=\"/admin/Upload/uploadFile\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleSuccess\"\r\n                        style=\"display: inline-block;\"\r\n                      >\r\n                        上传文件\r\n                      </el-upload>\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"ruleForm.file_path\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n                    >\r\n                      删除文件\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <el-form-item label=\"处理说明\">\r\n              <el-input\r\n                v-model=\"ruleForm.content\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入处理说明或备注信息...\"\r\n                class=\"content-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\" icon=\"el-icon-close\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" icon=\"el-icon-check\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 债务人详情右侧滑出面板 -->\r\n    <div class=\"debtor-detail-panel\" :class=\"{ 'panel-open': showDebtorPanel }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDebtorPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"header-info\">\r\n            <h3 class=\"panel-title\">\r\n              <i class=\"el-icon-user\"></i>\r\n              债务人详情\r\n            </h3>\r\n            <p class=\"panel-subtitle\">{{ currentDebtor.dt_name }}</p>\r\n          </div>\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"closeDebtorPanel\"\r\n            class=\"close-btn\"\r\n          ></el-button>\r\n        </div>\r\n\r\n        <!-- 左侧菜单 -->\r\n        <div class=\"panel-body\">\r\n          <div class=\"sidebar-menu\">\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'basic' }\"\r\n                 @click=\"activeTab = 'basic'\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n              <span>基本信息</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'user' }\"\r\n                 @click=\"activeTab = 'user'\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>关联用户</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'files' }\"\r\n                 @click=\"activeTab = 'files'\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              <span>相关文件</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'history' }\"\r\n                 @click=\"activeTab = 'history'\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>历史记录</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"content-area\">\r\n            <!-- 基本信息 -->\r\n            <div v-if=\"activeTab === 'basic'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-info\"></i>\r\n                  债务人基本信息\r\n                </h4>\r\n                <div class=\"info-grid\">\r\n                  <div class=\"info-item\">\r\n                    <label>姓名：</label>\r\n                    <span>{{ currentDebtor.dt_name }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentDebtor.id_card || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>联系电话：</label>\r\n                    <span>{{ currentDebtor.phone || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>地址：</label>\r\n                    <span>{{ currentDebtor.address || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务金额：</label>\r\n                    <span class=\"debt-amount\">¥{{ currentDebtor.debt_amount || '0.00' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务类型：</label>\r\n                    <span>{{ currentDebtor.debt_type || '未分类' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 关联用户信息 -->\r\n            <div v-if=\"activeTab === 'user'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-user\"></i>\r\n                  关联用户信息\r\n                </h4>\r\n                <div class=\"user-card\">\r\n                  <div class=\"user-avatar\">\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </div>\r\n                  <div class=\"user-info\">\r\n                    <h5>{{ currentDebtor.user_name }}</h5>\r\n                    <p>手机号：{{ currentDebtor.user_phone }}</p>\r\n                    <p>注册时间：{{ currentDebtor.user_register_time }}</p>\r\n                    <p>用户状态：\r\n                      <el-tag :type=\"currentDebtor.user_status === 'active' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ currentDebtor.user_status === 'active' ? '正常' : '异常' }}\r\n                      </el-tag>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 相关文件 -->\r\n            <div v-if=\"activeTab === 'files'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-folder\"></i>\r\n                  相关文件\r\n                </h4>\r\n                <div class=\"file-list\">\r\n                  <div v-for=\"file in currentDebtor.files\" :key=\"file.id\" class=\"file-item\">\r\n                    <div class=\"file-icon\">\r\n                      <i :class=\"getFileIcon(file.type)\"></i>\r\n                    </div>\r\n                    <div class=\"file-info\">\r\n                      <h6>{{ file.name }}</h6>\r\n                      <p>{{ file.upload_time }}</p>\r\n                      <p class=\"file-size\">{{ file.size }}</p>\r\n                    </div>\r\n                    <div class=\"file-actions\">\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"previewFile(file)\">\r\n                        <i class=\"el-icon-view\"></i>\r\n                        预览\r\n                      </el-button>\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"downloadFile(file)\">\r\n                        <i class=\"el-icon-download\"></i>\r\n                        下载\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.files || currentDebtor.files.length === 0\" class=\"empty-files\">\r\n                    <i class=\"el-icon-folder-opened\"></i>\r\n                    <p>暂无相关文件</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 历史记录 -->\r\n            <div v-if=\"activeTab === 'history'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  历史记录\r\n                </h4>\r\n                <div class=\"history-timeline\">\r\n                  <div v-for=\"record in currentDebtor.history\" :key=\"record.id\" class=\"timeline-item\">\r\n                    <div class=\"timeline-dot\"></div>\r\n                    <div class=\"timeline-content\">\r\n                      <h6>{{ record.action }}</h6>\r\n                      <p>{{ record.description }}</p>\r\n                      <span class=\"timeline-time\">{{ record.time }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.history || currentDebtor.history.length === 0\" class=\"empty-history\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    <p>暂无历史记录</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      // 债务人详情面板相关\r\n      showDebtorPanel: false,\r\n      activeTab: 'basic',\r\n      currentDebtor: {},\r\n      // AI生成相关\r\n      aiGenerating: false,\r\n      aiGeneratedContent: '',\r\n      contractTypes: [], // 合同类型列表\r\n      // 处理方式相关\r\n      processMethod: 'upload', // 默认为手动上传，可选值：'ai' | 'upload'\r\n      fileGenerating: false, // 文件生成状态\r\n      aiGeneratedFile: null, // AI生成的文件信息\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n  methods: {\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        0: 'warning',  // 待处理\r\n        1: 'primary',  // 处理中\r\n        2: 'success'   // 已处理\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待处理',\r\n        1: '处理中',\r\n        2: '已处理'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_deal: -1,\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        is_deal: 1,\r\n        type_title: \"\",\r\n        desc: \"\",\r\n        file_path: \"\",\r\n        content: \"\"\r\n      };\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      this.handleDialogClose();\r\n      this.dialogFormVisible = false;\r\n      // 重置AI生成内容\r\n      this.aiGeneratedContent = '';\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"债务催收\",\r\n            template_file: \"/uploads/templates/debt_collection_template.docx\",\r\n            template_name: \"债务催收律师函模板.docx\",\r\n            template_size: 245760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您欠付我方当事人的债务事宜，特致函如下：\r\n\r\n一、债务事实\r\n根据相关证据材料显示，您于{{debt_date}}向我方当事人借款人民币{{debt_amount}}元，约定还款期限为{{repay_date}}。但截至目前，您仍未履行还款义务，已构成违约。\r\n\r\n二、法律后果\r\n您的上述行为已构成违约，根据《中华人民共和国民法典》相关规定，您应当承担相应的法律责任。\r\n\r\n三、催告要求\r\n现特函告知，请您在收到本函后7日内，将所欠款项{{debt_amount}}元及相应利息一次性支付给我方当事人。\r\n\r\n四、法律警告\r\n如您在上述期限内仍不履行还款义务，我方将依法采取包括但不限于向人民法院提起诉讼等法律手段维护我方当事人的合法权益，由此产生的一切法律后果由您承担。\r\n\r\n特此函告！\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n联系电话：{{contact_phone}}\r\n地址：{{law_firm_address}}\r\n            `\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"合同违约\",\r\n            template_file: \"/uploads/templates/contract_breach_template.pdf\",\r\n            template_name: \"合同违约律师函模板.pdf\",\r\n            template_size: 512000,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您违反合同约定的事宜，特致函如下：\r\n\r\n一、合同事实\r\n您与我方当事人于{{contract_date}}签订了《{{contract_title}}》，约定了双方的权利义务。\r\n\r\n二、违约事实\r\n根据合同约定及相关证据，您存在以下违约行为：\r\n{{breach_details}}\r\n\r\n三、法律后果\r\n您的违约行为已给我方当事人造成了经济损失，根据合同约定及法律规定，您应当承担违约责任。\r\n\r\n四、要求\r\n请您在收到本函后7日内：\r\n1. 立即停止违约行为\r\n2. 履行合同义务\r\n3. 赔偿相应损失\r\n\r\n如您拒不履行，我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"知识产权侵权\",\r\n            template_file: \"/uploads/templates/ip_infringement_template.doc\",\r\n            template_name: \"知识产权侵权律师函模板.doc\",\r\n            template_size: 327680,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您侵犯我方当事人知识产权的事宜，特致函如下：\r\n\r\n一、权利基础\r\n我方当事人依法享有{{ip_type}}的专有权利，该权利受法律保护。\r\n\r\n二、侵权事实\r\n经调查发现，您未经我方当事人许可，擅自{{infringement_details}}，侵犯了我方当事人的合法权益。\r\n\r\n三、法律后果\r\n您的行为构成侵权，应当承担停止侵害、赔偿损失等法律责任。\r\n\r\n四、要求\r\n请您在收到本函后立即：\r\n1. 停止一切侵权行为\r\n2. 销毁侵权产品\r\n3. 赔偿经济损失\r\n\r\n否则我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"劳动争议\",\r\n            template_file: \"/uploads/templates/labor_dispute_template.docx\",\r\n            template_name: \"劳动争议律师函模板.docx\",\r\n            template_size: 298760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就劳动争议事宜，特致函如下：\r\n\r\n一、劳动关系\r\n我方当事人与您存在劳动关系，期间为{{employment_period}}。\r\n\r\n二、争议事实\r\n{{dispute_details}}\r\n\r\n三、法律依据\r\n根据《劳动法》、《劳动合同法》等相关法律法规，您应当履行相应义务。\r\n\r\n四、要求\r\n请您在收到本函后7日内妥善处理相关事宜，否则我方将通过法律途径解决。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"房屋租赁\",\r\n            template_file: \"/uploads/templates/lease_dispute_template.pdf\",\r\n            template_name: \"房屋租赁纠纷律师函模板.pdf\",\r\n            template_size: 445760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就房屋租赁纠纷事宜，特致函如下：\r\n\r\n一、租赁关系\r\n您与我方当事人签订了房屋租赁合同，租赁期限为{{lease_period}}。\r\n\r\n二、违约事实\r\n{{lease_breach_details}}\r\n\r\n三、要求\r\n请您在收到本函后立即：\r\n1. {{specific_requirements}}\r\n2. 支付相关费用\r\n3. 配合解决纠纷\r\n\r\n如不配合，我方将依法维权。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成律师函\r\n    async generateLawyerLetter() {\r\n      if (!this.ruleForm.title || !this.ruleForm.type_title) {\r\n        this.$message.warning('请先确保工单信息完整');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n\r\n      try {\r\n        // 1. 根据工单标题和类型找到对应的合同模板\r\n        const matchedTemplate = this.findMatchingTemplate();\r\n\r\n        if (!matchedTemplate) {\r\n          this.$message.warning('未找到匹配的律师函模板，请先在合同类型管理中上传相应模板');\r\n          this.aiGenerating = false;\r\n          return;\r\n        }\r\n\r\n        // 2. 获取债务人详情信息\r\n        const debtorInfo = await this.getDebtorInfo();\r\n\r\n        // 3. 获取关联用户信息\r\n        const userInfo = await this.getUserInfo();\r\n\r\n        // 4. 模拟AI生成过程\r\n        await this.simulateAiGeneration(matchedTemplate, debtorInfo, userInfo);\r\n\r\n        this.$message.success('AI律师函生成完成！');\r\n\r\n        // 如果是AI模式，自动生成文件\r\n        if (this.processMethod === 'ai') {\r\n          setTimeout(() => {\r\n            this.generateAiFile();\r\n          }, 500);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI生成失败:', error);\r\n        this.$message.error('AI生成失败，请重试');\r\n      } finally {\r\n        this.aiGenerating = false;\r\n      }\r\n    },\r\n\r\n    // 查找匹配的模板\r\n    findMatchingTemplate() {\r\n      // 根据工单标题和类型匹配模板\r\n      const title = this.ruleForm.title.toLowerCase();\r\n      const typeTitle = this.ruleForm.type_title.toLowerCase();\r\n\r\n      // 匹配规则\r\n      const matchRules = [\r\n        { keywords: ['债务', '催收', '欠款', '借款'], templateId: 1 },\r\n        { keywords: ['合同', '违约', '违反'], templateId: 2 },\r\n        { keywords: ['知识产权', '侵权', '商标', '专利'], templateId: 3 },\r\n        { keywords: ['劳动', '工资', '员工'], templateId: 4 },\r\n        { keywords: ['租赁', '房屋', '租金'], templateId: 5 }\r\n      ];\r\n\r\n      for (const rule of matchRules) {\r\n        if (rule.keywords.some(keyword =>\r\n          title.includes(keyword) || typeTitle.includes(keyword)\r\n        )) {\r\n          return this.contractTypes.find(type => type.id === rule.templateId);\r\n        }\r\n      }\r\n\r\n      // 默认返回债务催收模板\r\n      return this.contractTypes.find(type => type.id === 1);\r\n    },\r\n\r\n    // 获取债务人信息\r\n    async getDebtorInfo() {\r\n      // 模拟获取债务人详细信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: this.ruleForm.dt_name || '张三',\r\n            id_card: '110101199001011234',\r\n            phone: '13800138001',\r\n            address: '北京市朝阳区建国门外大街1号',\r\n            debt_amount: '100000.00',\r\n            debt_type: '借款纠纷',\r\n            debt_date: '2023-06-15',\r\n            repay_date: '2023-12-15'\r\n          });\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      // 模拟获取关联用户信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: '李明',\r\n            phone: this.ruleForm.uid || '13900139001',\r\n            register_time: '2023-01-15',\r\n            status: 'active'\r\n          });\r\n        }, 300);\r\n      });\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    async simulateAiGeneration(template, debtorInfo, userInfo) {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          // 替换模板中的变量\r\n          let content = template.template_content;\r\n\r\n          // 替换债务人信息\r\n          content = content.replace(/\\{\\{debtor_name\\}\\}/g, debtorInfo.name);\r\n          content = content.replace(/\\{\\{debt_amount\\}\\}/g, debtorInfo.debt_amount);\r\n          content = content.replace(/\\{\\{debt_date\\}\\}/g, debtorInfo.debt_date);\r\n          content = content.replace(/\\{\\{repay_date\\}\\}/g, debtorInfo.repay_date);\r\n\r\n          // 替换用户信息\r\n          content = content.replace(/\\{\\{user_name\\}\\}/g, userInfo.name);\r\n\r\n          // 替换其他信息\r\n          content = content.replace(/\\{\\{current_date\\}\\}/g, new Date().toLocaleDateString('zh-CN'));\r\n          content = content.replace(/\\{\\{law_firm_name\\}\\}/g, '北京市XX律师事务所');\r\n          content = content.replace(/\\{\\{contact_phone\\}\\}/g, '010-12345678');\r\n          content = content.replace(/\\{\\{law_firm_address\\}\\}/g, '北京市朝阳区XX大厦XX层');\r\n\r\n          // 根据工单描述添加具体内容\r\n          if (this.ruleForm.desc) {\r\n            content = content.replace(/\\{\\{breach_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{dispute_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{infringement_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{lease_breach_details\\}\\}/g, this.ruleForm.desc);\r\n          }\r\n\r\n          // 清理未替换的变量\r\n          content = content.replace(/\\{\\{[^}]+\\}\\}/g, '[待填写]');\r\n\r\n          this.aiGeneratedContent = content.trim();\r\n          resolve();\r\n        }, 2000); // 模拟2秒的AI生成时间\r\n      });\r\n    },\r\n\r\n    // 使用AI生成的内容\r\n    useAiContent() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('没有可用的AI生成内容');\r\n        return;\r\n      }\r\n\r\n      // 将AI生成的内容设置到处理说明中\r\n      this.ruleForm.content = this.aiGeneratedContent;\r\n\r\n      // 自动设置为已完成状态\r\n      this.ruleForm.is_deal = 2;\r\n\r\n      this.$message.success('已应用AI生成的律师函内容');\r\n    },\r\n\r\n    // 重新生成内容\r\n    regenerateContent() {\r\n      this.aiGeneratedContent = '';\r\n      this.generateLawyerLetter();\r\n    },\r\n\r\n    // 处理方式切换\r\n    onProcessMethodChange(method) {\r\n      // 清空之前的数据\r\n      if (method === 'ai') {\r\n        // 切换到AI模式，清空上传的文件\r\n        this.ruleForm.file_path = '';\r\n        this.aiGeneratedFile = null;\r\n      } else {\r\n        // 切换到上传模式，清空AI生成的内容\r\n        this.aiGeneratedContent = '';\r\n        this.aiGeneratedFile = null;\r\n      }\r\n    },\r\n\r\n    // AI生成文件\r\n    async generateAiFile() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('请先生成律师函内容');\r\n        return;\r\n      }\r\n\r\n      this.fileGenerating = true;\r\n      try {\r\n        // 模拟文件生成过程\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n        // 这里应该调用实际的文件生成接口\r\n        // 将AI生成的内容转换为文件\r\n        const fileName = `律师函_${this.ruleForm.dt_name || '债务人'}_${new Date().getTime()}.docx`;\r\n\r\n        this.aiGeneratedFile = {\r\n          name: fileName,\r\n          path: `/uploads/lawyer_letters/${fileName}`,\r\n          size: '25KB'\r\n        };\r\n\r\n        // 将生成的文件路径设置到表单中\r\n        this.ruleForm.file_path = this.aiGeneratedFile.path;\r\n\r\n        this.$message.success('文件生成成功');\r\n      } catch (error) {\r\n        console.error('文件生成失败:', error);\r\n        this.$message.error('文件生成失败，请重试');\r\n      } finally {\r\n        this.fileGenerating = false;\r\n      }\r\n    },\r\n\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n\r\n      // 使用模拟数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        }\r\n      ];\r\n\r\n      // 查找对应的数据\r\n      const foundData = testData.find(item => item.id === id);\r\n\r\n      if (foundData) {\r\n        _this.ruleForm = { ...foundData };\r\n        _this.dialogFormVisible = true;\r\n        console.log('加载律师函详情数据:', _this.ruleForm);\r\n      } else {\r\n        _this.$message({\r\n          type: \"error\",\r\n          message: \"未找到对应的律师函数据\",\r\n        });\r\n      }\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27) {\r\n        if (this.dialogFormVisible) {\r\n          this.cancelDialog();\r\n        }\r\n        if (this.showDebtorPanel) {\r\n          this.closeDebtorPanel();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 显示债务人详情\r\n    showDebtorDetail(row) {\r\n      console.log('显示债务人详情:', row);\r\n\r\n      // 模拟获取债务人详细信息\r\n      this.currentDebtor = {\r\n        ...row,\r\n        // 基本信息\r\n        id_card: this.generateIdCard(),\r\n        phone: this.generatePhone(),\r\n        address: this.generateAddress(),\r\n        debt_amount: this.generateDebtAmount(),\r\n        debt_type: this.generateDebtType(),\r\n\r\n        // 关联用户信息\r\n        user_name: this.generateUserName(row.uid),\r\n        user_phone: row.uid,\r\n        user_register_time: this.generateRegisterTime(),\r\n        user_status: 'active',\r\n\r\n        // 相关文件\r\n        files: this.generateFiles(row.dt_name),\r\n\r\n        // 历史记录\r\n        history: this.generateHistory(row.dt_name)\r\n      };\r\n\r\n      this.activeTab = 'basic';\r\n      this.showDebtorPanel = true;\r\n    },\r\n\r\n    // 关闭债务人详情面板\r\n    closeDebtorPanel() {\r\n      this.showDebtorPanel = false;\r\n      this.currentDebtor = {};\r\n    },\r\n\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'zip': 'el-icon-folder-opened',\r\n        'rar': 'el-icon-folder-opened'\r\n      };\r\n      return iconMap[fileType] || 'el-icon-document';\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      console.log('预览文件:', file);\r\n      this.$message.info('文件预览功能开发中...');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      console.log('下载文件:', file);\r\n      this.$message.success('开始下载文件: ' + file.name);\r\n    },\r\n\r\n    // 生成模拟数据的辅助方法\r\n    generateIdCard() {\r\n      const prefixes = ['110101', '310101', '440101', '500101'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const year = 1980 + Math.floor(Math.random() * 30);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      const suffix = String(Math.floor(Math.random() * 9999)).padStart(4, '0');\r\n      return `${prefix}${year}${month}${day}${suffix}`;\r\n    },\r\n\r\n    generatePhone() {\r\n      const prefixes = ['138', '139', '150', '151', '188', '189'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const suffix = String(Math.floor(Math.random() * 100000000)).padStart(8, '0');\r\n      return `${prefix}${suffix}`;\r\n    },\r\n\r\n    generateAddress() {\r\n      const addresses = [\r\n        '北京市朝阳区建国门外大街1号',\r\n        '上海市浦东新区陆家嘴环路1000号',\r\n        '广州市天河区珠江新城花城大道85号',\r\n        '深圳市南山区深南大道10000号',\r\n        '杭州市西湖区文三路90号'\r\n      ];\r\n      return addresses[Math.floor(Math.random() * addresses.length)];\r\n    },\r\n\r\n    generateDebtAmount() {\r\n      const amounts = ['50000.00', '100000.00', '200000.00', '500000.00', '1000000.00'];\r\n      return amounts[Math.floor(Math.random() * amounts.length)];\r\n    },\r\n\r\n    generateDebtType() {\r\n      const types = ['借款纠纷', '合同违约', '房屋租赁', '劳动争议', '知识产权'];\r\n      return types[Math.floor(Math.random() * types.length)];\r\n    },\r\n\r\n    generateUserName(phone) {\r\n      const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴'];\r\n      const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];\r\n      const surname = surnames[Math.floor(Math.random() * surnames.length)];\r\n      const name = names[Math.floor(Math.random() * names.length)];\r\n      return `${surname}${name}`;\r\n    },\r\n\r\n    generateRegisterTime() {\r\n      const year = 2020 + Math.floor(Math.random() * 4);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      return `${year}-${month}-${day} 10:30:00`;\r\n    },\r\n\r\n    generateFiles(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: `${debtorName}_身份证.jpg`,\r\n          type: 'jpg',\r\n          size: '2.5MB',\r\n          upload_time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: `${debtorName}_借款合同.pdf`,\r\n          type: 'pdf',\r\n          size: '1.2MB',\r\n          upload_time: '2024-01-16 09:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: `${debtorName}_银行流水.pdf`,\r\n          type: 'pdf',\r\n          size: '3.8MB',\r\n          upload_time: '2024-01-17 16:45:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    generateHistory(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          action: '创建债务人档案',\r\n          description: `创建了${debtorName}的债务人档案`,\r\n          time: '2024-01-15 09:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          action: '上传相关文件',\r\n          description: '上传了身份证、合同等相关文件',\r\n          time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          action: '发起律师函',\r\n          description: '针对债务纠纷发起律师函制作申请',\r\n          time: '2024-01-16 10:20:00'\r\n        },\r\n        {\r\n          id: 4,\r\n          action: '更新债务信息',\r\n          description: '更新了债务金额和联系方式',\r\n          time: '2024-01-17 15:10:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载律师函测试数据...');\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.order_sn.toLowerCase().includes(keyword) ||\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.uid.includes(keyword) ||\r\n              item.dt_name.includes(keyword)\r\n            );\r\n          }\r\n\r\n          // 状态筛选\r\n          if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n            filteredData = filteredData.filter(item => item.is_deal === _this.search.is_deal);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('律师函数据加载完成:', _this.list);\r\n          console.log('总数:', _this.total);\r\n        } catch (error) {\r\n          console.error('加载律师函测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 300);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存操作\r\n          console.log('保存律师函数据:', this.ruleForm);\r\n\r\n          // 模拟API延迟\r\n          setTimeout(() => {\r\n            _this.$message({\r\n              type: \"success\",\r\n              message: \"律师函处理状态更新成功！\",\r\n            });\r\n            this.getData();\r\n            _this.dialogFormVisible = false;\r\n          }, 500);\r\n\r\n          // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n          /*\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n          */\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.lawyer-letter-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section h2.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  align-items: flex-end;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input,\r\n.search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  border: none;\r\n  padding: 10px 24px;\r\n}\r\n\r\n.reset-btn {\r\n  padding: 10px 24px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.table-header {\r\n  padding: 20px 24px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.table-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.lawyer-table {\r\n  margin: 0 24px 20px;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.order-cell,\r\n.title-cell,\r\n.desc-cell,\r\n.phone-cell,\r\n.debtor-cell,\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.order-text,\r\n.title-text,\r\n.desc-text {\r\n  font-weight: 500;\r\n}\r\n\r\n.desc-text {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 债务人单元格可点击样式 */\r\n.debtor-cell.clickable {\r\n  cursor: pointer;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n}\r\n\r\n.debtor-cell.clickable:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.debtor-name {\r\n  font-weight: 500;\r\n  color: #409eff;\r\n}\r\n\r\n.arrow-icon {\r\n  font-size: 12px;\r\n  opacity: 0.6;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.debtor-cell.clickable:hover .arrow-icon {\r\n  opacity: 1;\r\n  transform: translateX(2px);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.process-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.dialog-content {\r\n  padding: 0 8px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 32px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.section-title {\r\n  margin: 0 0 20px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.readonly-input,\r\n.readonly-textarea {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.status-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.status-radio {\r\n  padding: 12px 20px;\r\n  border: 2px solid #dcdfe6;\r\n  border-radius: 8px;\r\n  background-color: white;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.status-radio:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.status-radio.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.completion-section {\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.file-input {\r\n  flex: 1;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-textarea {\r\n  margin-top: 16px;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  padding: 10px 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .lawyer-letter-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 债务人详情右侧滑出面板 */\r\n.debtor-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debtor-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 800px;\r\n  height: 100%;\r\n  background-color: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-width: 90vw;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n/* 面板头部 */\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.header-info h3.panel-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-subtitle {\r\n  margin: 4px 0 0 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.close-btn {\r\n  color: white !important;\r\n  font-size: 18px;\r\n}\r\n\r\n.close-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1) !important;\r\n}\r\n\r\n/* 面板主体 */\r\n.panel-body {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧菜单 */\r\n.sidebar-menu {\r\n  width: 200px;\r\n  background-color: #f8f9fa;\r\n  border-right: 1px solid #ebeef5;\r\n  padding: 16px 0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.menu-item.active {\r\n  background-color: #409eff;\r\n  color: white;\r\n  position: relative;\r\n}\r\n\r\n.menu-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 20px;\r\n  background-color: white;\r\n}\r\n\r\n.menu-item i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 信息区块 */\r\n.info-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.info-section .section-title {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  min-height: 60px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n  padding-top: 2px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n  line-height: 1.5;\r\n}\r\n\r\n.debt-amount {\r\n  color: #f56c6c !important;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.user-avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.user-info h5 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  color: #303133;\r\n}\r\n\r\n.user-info p {\r\n  margin: 4px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表 */\r\n.file-list {\r\n  space-y: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.file-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 6px;\r\n  background-color: #409eff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-info h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-info p {\r\n  margin: 2px 0;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.file-size {\r\n  color: #409eff !important;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.empty-files,\r\n.empty-history {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-files i,\r\n.empty-history i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n/* 历史时间线 */\r\n.history-timeline {\r\n  position: relative;\r\n  padding-left: 20px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.timeline-item:not(:last-child)::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -15px;\r\n  top: 20px;\r\n  width: 2px;\r\n  height: calc(100% - 10px);\r\n  background-color: #e4e7ed;\r\n}\r\n\r\n.timeline-dot {\r\n  position: absolute;\r\n  left: -20px;\r\n  top: 5px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #409eff;\r\n  border: 2px solid white;\r\n  box-shadow: 0 0 0 2px #409eff;\r\n}\r\n\r\n.timeline-content {\r\n  background-color: #fafbfc;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.timeline-content h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.timeline-content p {\r\n  margin: 0 0 8px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .panel-content {\r\n    width: 700px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n    max-width: 100vw;\r\n  }\r\n\r\n  .panel-body {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    width: 100%;\r\n    display: flex;\r\n    overflow-x: auto;\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .menu-item {\r\n    white-space: nowrap;\r\n    min-width: 120px;\r\n    justify-content: center;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n/* AI生成功能样式 */\r\n.ai-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.ai-section .section-title {\r\n  color: white;\r\n  border-bottom-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.ai-generation-content {\r\n  padding: 16px 0;\r\n}\r\n\r\n.ai-description {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.ai-description p {\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.ai-actions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-generate-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  font-weight: 600;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ai-generate-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.ai-result {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.result-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: white;\r\n}\r\n\r\n.generated-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ai-content-textarea {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 6px;\r\n}\r\n\r\n.ai-content-textarea .el-textarea__inner {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: #333;\r\n  font-family: 'Microsoft YaHei', sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.ai-result-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.ai-result-actions .el-button {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.ai-result-actions .el-button:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 处理方式选择样式 */\r\n.process-method-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.method-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.method-radio {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border: 2px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  background: #fff;\r\n}\r\n\r\n.method-radio:hover {\r\n  border-color: #409eff;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.method-radio.is-checked {\r\n  border-color: #409eff;\r\n  background: #e6f7ff;\r\n  color: #409eff;\r\n}\r\n\r\n.method-radio i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* AI处理区域样式 */\r\n.ai-process-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-mode-alert {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-content-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-preview-textarea .el-textarea__inner {\r\n  background: #fff;\r\n  border: 1px solid #dcdfe6;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.ai-file-generation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.generate-file-btn {\r\n  border-radius: 6px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.generated-file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  color: #409eff;\r\n}\r\n\r\n.generated-file-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 手动上传区域样式 */\r\n.upload-process-section {\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n</style>\r\n"], "mappings": "AA2pBA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACA;MACAC,eAAA;MACAC,SAAA;MACAC,aAAA;MACA;MACAC,YAAA;MACAC,kBAAA;MACAC,aAAA;MAAA;MACA;MACAC,aAAA;MAAA;MACAC,cAAA;MAAA;MACAC,eAAA;MAAA;MACAC,QAAA;QACAd,KAAA;QACAe,MAAA;MACA;MAEAC,KAAA;QACAhB,KAAA,GACA;UACAiB,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,SAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,cAAA;MACAC,OAAA,GACA;QACAC,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA,EACA;MACAwB,QAAA,GACA;QACAD,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA,GACA;QACAuB,EAAA;QACAvB,KAAA;MACA;IAEA;EACA;EACAyB,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,gBAAA;IACA;IACAC,QAAA,CAAAC,gBAAA,iBAAAC,aAAA;EACA;EAEAC,cAAA;IACA;IACAH,QAAA,CAAAI,mBAAA,iBAAAF,aAAA;EACA;EACAG,OAAA;IACA;IACAC,cAAAC,MAAA;MACA,MAAAC,SAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,cAAAF,MAAA;MACA,MAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAG,WAAA;MACA,KAAA9C,IAAA;MACA,KAAAC,IAAA;MACA,KAAAiC,OAAA;IACA;IAEA;IACAa,YAAA;MACA,KAAA7C,MAAA;QACAC,OAAA;QACAE,OAAA;MACA;MACA,KAAAyC,UAAA;IACA;IAEA;IACAE,kBAAA;MACA;MACA,SAAAC,KAAA,CAAA3B,QAAA;QACA,KAAA2B,KAAA,CAAA3B,QAAA,CAAA4B,WAAA;MACA;MACA,KAAA5B,QAAA;QACAd,KAAA;QACAe,MAAA;QACAlB,OAAA;QACA8C,UAAA;QACAC,IAAA;QACAxB,SAAA;QACAyB,OAAA;MACA;IACA;IAEA;IACAC,aAAA;MACA,KAAAN,iBAAA;MACA,KAAAtC,iBAAA;MACA;MACA,KAAAO,kBAAA;IACA;IAEA;IACAkB,iBAAA;MACA;MACAoB,UAAA;QACA,KAAArC,aAAA,IACA;UACAa,EAAA;UACAvB,KAAA;UACAgD,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,gBAAA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;QACA,GACA;UACA5B,EAAA;UACAvB,KAAA;UACAgD,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,gBAAA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;QACA,GACA;UACA5B,EAAA;UACAvB,KAAA;UACAgD,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,gBAAA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;QACA,GACA;UACA5B,EAAA;UACAvB,KAAA;UACAgD,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,gBAAA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;QACA,GACA;UACA5B,EAAA;UACAvB,KAAA;UACAgD,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,gBAAA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;QACA,EACA;MACA;IACA;IAEA;IACA,MAAAC,qBAAA;MACA,UAAAtC,QAAA,CAAAd,KAAA,UAAAc,QAAA,CAAA6B,UAAA;QACA,KAAAU,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA9C,YAAA;MAEA;QACA;QACA,MAAA+C,eAAA,QAAAC,oBAAA;QAEA,KAAAD,eAAA;UACA,KAAAF,QAAA,CAAAC,OAAA;UACA,KAAA9C,YAAA;UACA;QACA;;QAEA;QACA,MAAAiD,UAAA,cAAAC,aAAA;;QAEA;QACA,MAAAC,QAAA,cAAAC,WAAA;;QAEA;QACA,WAAAC,oBAAA,CAAAN,eAAA,EAAAE,UAAA,EAAAE,QAAA;QAEA,KAAAN,QAAA,CAAAS,OAAA;;QAEA;QACA,SAAAnD,aAAA;UACAoC,UAAA;YACA,KAAAgB,cAAA;UACA;QACA;MAEA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAX,QAAA,CAAAW,KAAA;MACA;QACA,KAAAxD,YAAA;MACA;IACA;IAEA;IACAgD,qBAAA;MACA;MACA,MAAAxD,KAAA,QAAAc,QAAA,CAAAd,KAAA,CAAAkE,WAAA;MACA,MAAAC,SAAA,QAAArD,QAAA,CAAA6B,UAAA,CAAAuB,WAAA;;MAEA;MACA,MAAAE,UAAA,IACA;QAAAC,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,EACA;MAEA,WAAAC,IAAA,IAAAH,UAAA;QACA,IAAAG,IAAA,CAAAF,QAAA,CAAAG,IAAA,CAAA7E,OAAA,IACAK,KAAA,CAAAyE,QAAA,CAAA9E,OAAA,KAAAwE,SAAA,CAAAM,QAAA,CAAA9E,OAAA,CACA;UACA,YAAAe,aAAA,CAAAgE,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAApD,EAAA,KAAAgD,IAAA,CAAAD,UAAA;QACA;MACA;;MAEA;MACA,YAAA5D,aAAA,CAAAgE,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAApD,EAAA;IACA;IAEA;IACA,MAAAmC,cAAA;MACA;MACA,WAAAkB,OAAA,CAAAC,OAAA;QACA9B,UAAA;UACA8B,OAAA;YACA3F,IAAA,OAAA4B,QAAA,CAAAgE,OAAA;YACAC,OAAA;YACAC,KAAA;YACAC,OAAA;YACAC,WAAA;YACAC,SAAA;YACAC,SAAA;YACAC,UAAA;UACA;QACA;MACA;IACA;IAEA;IACA,MAAAzB,YAAA;MACA;MACA,WAAAgB,OAAA,CAAAC,OAAA;QACA9B,UAAA;UACA8B,OAAA;YACA3F,IAAA;YACA8F,KAAA,OAAAlE,QAAA,CAAAwE,GAAA;YACAC,aAAA;YACApD,MAAA;UACA;QACA;MACA;IACA;IAEA;IACA,MAAA0B,qBAAA2B,QAAA,EAAA/B,UAAA,EAAAE,QAAA;MACA,WAAAiB,OAAA,CAAAC,OAAA;QACA9B,UAAA;UACA;UACA,IAAAF,OAAA,GAAA2C,QAAA,CAAArC,gBAAA;;UAEA;UACAN,OAAA,GAAAA,OAAA,CAAA4C,OAAA,yBAAAhC,UAAA,CAAAvE,IAAA;UACA2D,OAAA,GAAAA,OAAA,CAAA4C,OAAA,yBAAAhC,UAAA,CAAAyB,WAAA;UACArC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,uBAAAhC,UAAA,CAAA2B,SAAA;UACAvC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,wBAAAhC,UAAA,CAAA4B,UAAA;;UAEA;UACAxC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,uBAAA9B,QAAA,CAAAzE,IAAA;;UAEA;UACA2D,OAAA,GAAAA,OAAA,CAAA4C,OAAA,8BAAAC,IAAA,GAAAC,kBAAA;UACA9C,OAAA,GAAAA,OAAA,CAAA4C,OAAA;UACA5C,OAAA,GAAAA,OAAA,CAAA4C,OAAA;UACA5C,OAAA,GAAAA,OAAA,CAAA4C,OAAA;;UAEA;UACA,SAAA3E,QAAA,CAAA8B,IAAA;YACAC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,iCAAA3E,QAAA,CAAA8B,IAAA;YACAC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,kCAAA3E,QAAA,CAAA8B,IAAA;YACAC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,uCAAA3E,QAAA,CAAA8B,IAAA;YACAC,OAAA,GAAAA,OAAA,CAAA4C,OAAA,uCAAA3E,QAAA,CAAA8B,IAAA;UACA;;UAEA;UACAC,OAAA,GAAAA,OAAA,CAAA4C,OAAA;UAEA,KAAAhF,kBAAA,GAAAoC,OAAA,CAAA+C,IAAA;UACAf,OAAA;QACA;MACA;IACA;IAEA;IACAgB,aAAA;MACA,UAAApF,kBAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxC,QAAA,CAAA+B,OAAA,QAAApC,kBAAA;;MAEA;MACA,KAAAK,QAAA,CAAAjB,OAAA;MAEA,KAAAwD,QAAA,CAAAS,OAAA;IACA;IAEA;IACAgC,kBAAA;MACA,KAAArF,kBAAA;MACA,KAAA2C,oBAAA;IACA;IAEA;IACA2C,sBAAAC,MAAA;MACA;MACA,IAAAA,MAAA;QACA;QACA,KAAAlF,QAAA,CAAAM,SAAA;QACA,KAAAP,eAAA;MACA;QACA;QACA,KAAAJ,kBAAA;QACA,KAAAI,eAAA;MACA;IACA;IAEA;IACA,MAAAkD,eAAA;MACA,UAAAtD,kBAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA1C,cAAA;MACA;QACA;QACA,UAAAgE,OAAA,CAAAC,OAAA,IAAA9B,UAAA,CAAA8B,OAAA;;QAEA;QACA;QACA,MAAAoB,QAAA,eAAAnF,QAAA,CAAAgE,OAAA,iBAAAY,IAAA,GAAAQ,OAAA;QAEA,KAAArF,eAAA;UACA3B,IAAA,EAAA+G,QAAA;UACAE,IAAA,6BAAAF,QAAA;UACAxG,IAAA;QACA;;QAEA;QACA,KAAAqB,QAAA,CAAAM,SAAA,QAAAP,eAAA,CAAAsF,IAAA;QAEA,KAAA9C,QAAA,CAAAS,OAAA;MACA,SAAAE,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAX,QAAA,CAAAW,KAAA;MACA;QACA,KAAApD,cAAA;MACA;IACA;IAEAwF,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;MACApC,OAAA,CAAAqC,GAAA,MAAAD,KAAA;IACA;IACAE,UAAA;MACA,KAAA7G,MAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAA8B,OAAA;IACA;IACA8E,SAAAjF,EAAA;MACA,IAAAkF,KAAA;MACA,IAAAlF,EAAA;QACA,KAAAmF,OAAA,CAAAnF,EAAA;MACA;QACA,KAAAT,QAAA;UACAd,KAAA;UACA4C,IAAA;QACA;MACA;IACA;IACA8D,QAAAnF,EAAA;MACA,IAAAkF,KAAA;;MAEA;MACA,MAAAE,QAAA,IACA;QACApF,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,GACA;QACAtB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,GACA;QACAtB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,GACA;QACAtB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,GACA;QACAtB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,EACA;;MAEA;MACA,MAAAiE,SAAA,GAAAH,QAAA,CAAAjC,IAAA,CAAAqC,IAAA,IAAAA,IAAA,CAAAxF,EAAA,KAAAA,EAAA;MAEA,IAAAuF,SAAA;QACAL,KAAA,CAAA3F,QAAA;UAAA,GAAAgG;QAAA;QACAL,KAAA,CAAAvG,iBAAA;QACA+D,OAAA,CAAAqC,GAAA,eAAAG,KAAA,CAAA3F,QAAA;MACA;QACA2F,KAAA,CAAApD,QAAA;UACAsB,IAAA;UACAzD,OAAA;QACA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA8F,QAAAzF,EAAA;MACA,KAAA0F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAxC,IAAA;MACA,GACAyC,IAAA;QACA,KAAAC,aAAA,MAAAtH,GAAA,mBAAAwB,EAAA,EAAA6F,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAlE,QAAA;cACAsB,IAAA;cACAzD,OAAA,EAAAoG,IAAA,CAAAE;YACA;UACA;YACA,KAAAnE,QAAA;cACAsB,IAAA;cACAzD,OAAA,EAAAoG,IAAA,CAAAE;YACA;UACA;QACA;MACA,GACAC,KAAA;QACA,KAAApE,QAAA;UACAsB,IAAA;UACAzD,OAAA;QACA;MACA;IACA;IACAwG,QAAAC,KAAA,EAAApG,EAAA;MACA,KAAA0F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAxC,IAAA;MACA,GACAyC,IAAA;QACA,KAAAC,aAAA,MAAAtH,GAAA,kBAAAwB,EAAA,EAAA6F,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAlE,QAAA;cACAsB,IAAA;cACAzD,OAAA;YACA;YACA,KAAA5B,IAAA,CAAAsI,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAApE,QAAA;UACAsB,IAAA;UACAzD,OAAA;QACA;MACA;IACA;IACA2G,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAzF,WAAA;MACA,KAAA9C,IAAA;MACA,KAAAC,IAAA;MACA,KAAAiC,OAAA;IACA;IAEA;IACAI,cAAAkG,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,OAAA;QACA,SAAA/H,iBAAA;UACA,KAAA4C,YAAA;QACA;QACA,SAAAzC,eAAA;UACA,KAAA6H,gBAAA;QACA;MACA;IACA;IAEA;IACAC,iBAAAC,GAAA;MACAnE,OAAA,CAAAqC,GAAA,aAAA8B,GAAA;;MAEA;MACA,KAAA7H,aAAA;QACA,GAAA6H,GAAA;QACA;QACArD,OAAA,OAAAsD,cAAA;QACArD,KAAA,OAAAsD,aAAA;QACArD,OAAA,OAAAsD,eAAA;QACArD,WAAA,OAAAsD,kBAAA;QACArD,SAAA,OAAAsD,gBAAA;QAEA;QACAC,SAAA,OAAAC,gBAAA,CAAAP,GAAA,CAAA9C,GAAA;QACAsD,UAAA,EAAAR,GAAA,CAAA9C,GAAA;QACAuD,kBAAA,OAAAC,oBAAA;QACAC,WAAA;QAEA;QACAC,KAAA,OAAAC,aAAA,CAAAb,GAAA,CAAAtD,OAAA;QAEA;QACAoE,OAAA,OAAAC,eAAA,CAAAf,GAAA,CAAAtD,OAAA;MACA;MAEA,KAAAxE,SAAA;MACA,KAAAD,eAAA;IACA;IAEA;IACA6H,iBAAA;MACA,KAAA7H,eAAA;MACA,KAAAE,aAAA;IACA;IAEA;IACA6I,YAAAC,QAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,QAAA;IACA;IAEA;IACAE,YAAAC,IAAA;MACAvF,OAAA,CAAAqC,GAAA,UAAAkD,IAAA;MACA,KAAAnG,QAAA,CAAApD,IAAA;IACA;IAEA;IACAwJ,aAAAD,IAAA;MACAvF,OAAA,CAAAqC,GAAA,UAAAkD,IAAA;MACA,KAAAnG,QAAA,CAAAS,OAAA,cAAA0F,IAAA,CAAAtK,IAAA;IACA;IAEA;IACAmJ,eAAA;MACA,MAAAqB,QAAA;MACA,MAAAC,MAAA,GAAAD,QAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAJ,QAAA,CAAAK,MAAA;MACA,MAAAC,IAAA,UAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA,MAAAG,KAAA,GAAAC,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,cAAAK,QAAA;MACA,MAAAC,GAAA,GAAAF,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,cAAAK,QAAA;MACA,MAAAE,MAAA,GAAAH,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,YAAAK,QAAA;MACA,UAAAR,MAAA,GAAAK,IAAA,GAAAC,KAAA,GAAAG,GAAA,GAAAC,MAAA;IACA;IAEA/B,cAAA;MACA,MAAAoB,QAAA;MACA,MAAAC,MAAA,GAAAD,QAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAJ,QAAA,CAAAK,MAAA;MACA,MAAAM,MAAA,GAAAH,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,iBAAAK,QAAA;MACA,UAAAR,MAAA,GAAAU,MAAA;IACA;IAEA9B,gBAAA;MACA,MAAA+B,SAAA,IACA,kBACA,qBACA,qBACA,oBACA,eACA;MACA,OAAAA,SAAA,CAAAV,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAQ,SAAA,CAAAP,MAAA;IACA;IAEAvB,mBAAA;MACA,MAAA+B,OAAA;MACA,OAAAA,OAAA,CAAAX,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAS,OAAA,CAAAR,MAAA;IACA;IAEAtB,iBAAA;MACA,MAAA+B,KAAA;MACA,OAAAA,KAAA,CAAAZ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAU,KAAA,CAAAT,MAAA;IACA;IAEApB,iBAAA3D,KAAA;MACA,MAAAyF,QAAA;MACA,MAAAC,KAAA;MACA,MAAAC,OAAA,GAAAF,QAAA,CAAAb,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAW,QAAA,CAAAV,MAAA;MACA,MAAA7K,IAAA,GAAAwL,KAAA,CAAAd,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAY,KAAA,CAAAX,MAAA;MACA,UAAAY,OAAA,GAAAzL,IAAA;IACA;IAEA4J,qBAAA;MACA,MAAAkB,IAAA,UAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA,MAAAG,KAAA,GAAAC,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,cAAAK,QAAA;MACA,MAAAC,GAAA,GAAAF,MAAA,CAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,cAAAK,QAAA;MACA,UAAAH,IAAA,IAAAC,KAAA,IAAAG,GAAA;IACA;IAEAnB,cAAA2B,UAAA;MACA,QACA;QACArJ,EAAA;QACArC,IAAA,KAAA0L,UAAA;QACAjG,IAAA;QACAlF,IAAA;QACAoL,WAAA;MACA,GACA;QACAtJ,EAAA;QACArC,IAAA,KAAA0L,UAAA;QACAjG,IAAA;QACAlF,IAAA;QACAoL,WAAA;MACA,GACA;QACAtJ,EAAA;QACArC,IAAA,KAAA0L,UAAA;QACAjG,IAAA;QACAlF,IAAA;QACAoL,WAAA;MACA,EACA;IACA;IAEA1B,gBAAAyB,UAAA;MACA,QACA;QACArJ,EAAA;QACAuJ,MAAA;QACAC,WAAA,QAAAH,UAAA;QACAI,IAAA;MACA,GACA;QACAzJ,EAAA;QACAuJ,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAzJ,EAAA;QACAuJ,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,GACA;QACAzJ,EAAA;QACAuJ,MAAA;QACAC,WAAA;QACAC,IAAA;MACA,EACA;IACA;IAEAtJ,QAAA;MACA,IAAA+E,KAAA;MAEAA,KAAA,CAAA3G,OAAA;;MAEA;MACA,MAAA6G,QAAA,IACA;QACApF,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;MACA,GACA;QACApB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;MACA,GACA;QACApB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;QACAvB,SAAA;QACAyB,OAAA;MACA,GACA;QACAtB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;MACA,GACA;QACApB,EAAA;QACAqF,QAAA;QACAjC,IAAA;QACA3E,KAAA;QACA4C,IAAA;QACA/C,OAAA;QACAyF,GAAA;QACAR,OAAA;QACA+B,WAAA;QACAlE,UAAA;MACA,EACA;;MAEA;MACAI,UAAA;QACA;UACAkB,OAAA,CAAAqC,GAAA;;UAEA;UACA,IAAA2E,YAAA,GAAAtE,QAAA;UACA,IAAAF,KAAA,CAAA/G,MAAA,CAAAC,OAAA,IAAA8G,KAAA,CAAA/G,MAAA,CAAAC,OAAA,CAAAiG,IAAA;YACA,MAAAjG,OAAA,GAAA8G,KAAA,CAAA/G,MAAA,CAAAC,OAAA,CAAAiG,IAAA,GAAA1B,WAAA;YACA+G,YAAA,GAAAtE,QAAA,CAAAuE,MAAA,CAAAnE,IAAA,IACAA,IAAA,CAAAH,QAAA,CAAA1C,WAAA,GAAAO,QAAA,CAAA9E,OAAA,KACAoH,IAAA,CAAA/G,KAAA,CAAAkE,WAAA,GAAAO,QAAA,CAAA9E,OAAA,KACAoH,IAAA,CAAAzB,GAAA,CAAAb,QAAA,CAAA9E,OAAA,KACAoH,IAAA,CAAAjC,OAAA,CAAAL,QAAA,CAAA9E,OAAA,CACA;UACA;;UAEA;UACA,IAAA8G,KAAA,CAAA/G,MAAA,CAAAG,OAAA,WAAA4G,KAAA,CAAA/G,MAAA,CAAAG,OAAA;YACAoL,YAAA,GAAAA,YAAA,CAAAC,MAAA,CAAAnE,IAAA,IAAAA,IAAA,CAAAlH,OAAA,KAAA4G,KAAA,CAAA/G,MAAA,CAAAG,OAAA;UACA;;UAEA;UACA,MAAAsL,UAAA,IAAA1E,KAAA,CAAAjH,IAAA,QAAAiH,KAAA,CAAAhH,IAAA;UACA,MAAA2L,QAAA,GAAAD,UAAA,GAAA1E,KAAA,CAAAhH,IAAA;UACA,MAAA4L,QAAA,GAAAJ,YAAA,CAAAK,KAAA,CAAAH,UAAA,EAAAC,QAAA;UAEA3E,KAAA,CAAAnH,IAAA,GAAA+L,QAAA;UACA5E,KAAA,CAAAlH,KAAA,GAAA0L,YAAA,CAAAlB,MAAA;UACAtD,KAAA,CAAA3G,OAAA;UAEAmE,OAAA,CAAAqC,GAAA,eAAAG,KAAA,CAAAnH,IAAA;UACA2E,OAAA,CAAAqC,GAAA,QAAAG,KAAA,CAAAlH,KAAA;QACA,SAAAyE,KAAA;UACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;UACAyC,KAAA,CAAAnH,IAAA;UACAmH,KAAA,CAAAlH,KAAA;UACAkH,KAAA,CAAA3G,OAAA;QACA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAyL,SAAA;MACA,IAAA9E,KAAA;MACA,KAAAhE,KAAA,aAAA+I,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAxH,OAAA,CAAAqC,GAAA,kBAAAxF,QAAA;;UAEA;UACAiC,UAAA;YACA0D,KAAA,CAAApD,QAAA;cACAsB,IAAA;cACAzD,OAAA;YACA;YACA,KAAAQ,OAAA;YACA+E,KAAA,CAAAvG,iBAAA;UACA;;UAEA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;UACA;QACA;MACA;IACA;IACAwL,iBAAAC,GAAA;MACA,KAAAlM,IAAA,GAAAkM,GAAA;MAEA,KAAAjK,OAAA;IACA;IACAkK,oBAAAD,GAAA;MACA,KAAAnM,IAAA,GAAAmM,GAAA;MACA,KAAAjK,OAAA;IACA;IACAmK,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAAvE,IAAA;QACA,KAAAlE,QAAA,CAAAS,OAAA;QACA,KAAAhD,QAAA,MAAAuF,KAAA,IAAAyF,GAAA,CAAA1M,IAAA,CAAAW,GAAA;MACA;QACA,KAAAsD,QAAA,CAAAW,KAAA,CAAA8H,GAAA,CAAAtE,GAAA;MACA;IACA;IAEAuE,UAAAvC,IAAA;MACA,KAAArJ,UAAA,GAAAqJ,IAAA;MACA,KAAApJ,aAAA;IACA;IACA4L,aAAAxC,IAAA;MACA,MAAAyC,UAAA,6BAAAC,IAAA,CAAA1C,IAAA,CAAA7E,IAAA;MACA,KAAAsH,UAAA;QACA,KAAA5I,QAAA,CAAAW,KAAA;QACA;MACA;IACA;IACAmI,SAAA3C,IAAA,EAAAvD,QAAA;MACA,IAAAQ,KAAA;MACAA,KAAA,CAAA2F,UAAA,gCAAA5C,IAAA,EAAApC,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAd,KAAA,CAAA3F,QAAA,CAAAmF,QAAA;UAEAQ,KAAA,CAAApD,QAAA,CAAAS,OAAA;QACA;UACA2C,KAAA,CAAApD,QAAA,CAAAW,KAAA,CAAAsD,IAAA,CAAAE,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}