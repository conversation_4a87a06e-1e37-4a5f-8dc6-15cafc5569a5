(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a3ccb5d0"],{"0760":function(e,t,a){},"5c89":function(e,t,a){"use strict";a("0760")},ca27:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",[t("el-col",{attrs:{span:4}},[t("el-input",{attrs:{placeholder:"请输入用户姓名，债务人的名字，手机号",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-col",{attrs:{span:3}},[t("el-select",{attrs:{placeholder:"请选择",size:e.allSize},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},e._l(e.options,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v("搜索")])],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.clearData()}}},[e._v("重置")])],1)],1),t("el-row",{staticClass:"page-top"},[t("el-button",{attrs:{type:"primary",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v("新增")]),t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-top"},on:{click:e.exportsDebtList}},[e._v(" 导出列表 ")]),t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-bottom"},on:{click:e.openUploadDebts}},[e._v("导入债务人 ")]),t("a",{staticStyle:{"text-decoration":"none",color:"#4397fd","font-weight":"800","margin-left":"10px"},attrs:{href:"/import_templete/debt_person.xls"}},[e._v("下载导入模板")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"},on:{"sort-change":e.handleSortChange}},[t("el-table-column",{attrs:{prop:"nickname",label:"用户姓名"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"clickable-text",on:{click:function(t){return e.viewUserData(a.row.uid)}}},[e._v(e._s(a.row.users.nickname))])]}}])}),t("el-table-column",{attrs:{prop:"name",label:"债务人姓名"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"clickable-text",on:{click:function(t){return e.viewDebtData(a.row.id)}}},[e._v(e._s(a.row.name))])]}}])}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话"}}),t("el-table-column",{attrs:{prop:"money",label:"债务金额（元）"}}),t("el-table-column",{attrs:{prop:"status",label:"状态"}}),t("el-table-column",{attrs:{prop:"back_money",label:"合计回款（元）"}}),t("el-table-column",{attrs:{prop:"un_money",label:"未回款（元）"}}),t("el-table-column",{attrs:{prop:"ctime",label:"提交时间",sortable:""}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editDebttransData(a.row.id)}}},[e._v("跟进")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.delDataDebt(a.$indexs,a.row.id)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-drawer",{attrs:{title:"债务人管理",visible:e.dialogFormVisible,direction:"rtl",size:"60%","before-close":e.handleDrawerClose},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("div",{staticClass:"drawer-content-wrapper"},[t("div",{staticClass:"drawer-sidebar"},[t("el-menu",{staticClass:"drawer-menu",attrs:{"default-active":e.activeDebtTab},on:{select:e.handleDebtTabSelect}},[t("el-menu-item",{attrs:{index:"details"}},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("债务人详情")])]),t("el-submenu",{attrs:{index:"evidence"}},[t("template",{slot:"title"},[t("i",{staticClass:"el-icon-folder"}),t("span",[e._v("证据")])]),t("el-menu-item",{attrs:{index:"evidence-all"}},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("全部")])]),t("el-menu-item",{attrs:{index:"evidence-video"}},[t("i",{staticClass:"el-icon-video-camera"}),t("span",[e._v("视频")])]),t("el-menu-item",{attrs:{index:"evidence-image"}},[t("i",{staticClass:"el-icon-picture"}),t("span",[e._v("图片")])]),t("el-menu-item",{attrs:{index:"evidence-audio"}},[t("i",{staticClass:"el-icon-microphone"}),t("span",[e._v("语音")])]),t("el-menu-item",{attrs:{index:"evidence-document"}},[t("i",{staticClass:"el-icon-document-copy"}),t("span",[e._v("文档")])])],2)],1)],1),t("div",{staticClass:"drawer-content"},["details"===e.activeDebtTab?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-user"}),e._v(" 债务人详情 ")]),1==e.ruleForm.is_user?t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-top"},on:{click:e.exports}},[e._v("导出跟进记录")])],1):e._e(),1==e.ruleForm.is_user?t("el-descriptions",{staticStyle:{"margin-top":"20px"},attrs:{title:"债务信息"}},[t("el-descriptions-item",{attrs:{label:"用户姓名"}},[e._v(e._s(e.ruleForm.nickname))]),t("el-descriptions-item",{attrs:{label:"债务人姓名"}},[e._v(e._s(e.ruleForm.name))]),t("el-descriptions-item",{attrs:{label:"债务人电话"}},[e._v(e._s(e.ruleForm.tel))]),t("el-descriptions-item",{attrs:{label:"债务人地址"}},[e._v(e._s(e.ruleForm.address))]),t("el-descriptions-item",{attrs:{label:"债务金额"}},[e._v(e._s(e.ruleForm.money))]),t("el-descriptions-item",{attrs:{label:"合计回款"}},[e._v(e._s(e.ruleForm.back_money))]),t("el-descriptions-item",{attrs:{label:"未回款"}},[e._v(e._s(e.ruleForm.un_money))]),t("el-descriptions-item",{attrs:{label:"提交时间"}},[e._v(e._s(e.ruleForm.ctime))]),t("el-descriptions-item",{attrs:{label:"最后一次修改时间"}},[e._v(e._s(e.ruleForm.utime))])],1):e._e(),t("el-form",{ref:"ruleForm",staticStyle:{"margin-top":"20px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[1!=e.ruleForm.is_user?t("el-form-item",{attrs:{label:"选择用户"},nativeOn:{click:function(t){return e.showUserList()}}},[t("el-button",{attrs:{type:"primary",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v("选择用户")])],1):e._e()],1),t("el-col",{attrs:{span:12}},[e.ruleForm.utel?t("el-form-item",{attrs:{label:"用户信息"}},[e._v(" "+e._s(e.ruleForm.uname)),t("div",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(e.ruleForm.utel))])]):e._e()],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"债务人姓名"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"债务人电话"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.tel,callback:function(t){e.$set(e.ruleForm,"tel",t)},expression:"ruleForm.tel"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"身份证号码"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.idcard_no,callback:function(t){e.$set(e.ruleForm,"idcard_no",t)},expression:"ruleForm.idcard_no"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"债务金额"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.money,callback:function(t){e.$set(e.ruleForm,"money",t)},expression:"ruleForm.money"}})],1)],1)],1),t("el-form-item",{attrs:{label:"债务人地址"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.address,callback:function(t){e.$set(e.ruleForm,"address",t)},expression:"ruleForm.address"}})],1),t("el-form-item",{attrs:{label:"案由描述"}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.case_des,callback:function(t){e.$set(e.ruleForm,"case_des",t)},expression:"ruleForm.case_des"}})],1)],1),1==e.ruleForm.is_user?t("el-descriptions",{staticStyle:{"margin-top":"30px"},attrs:{title:"跟进记录",colon:!1}},[t("el-descriptions-item",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.ruleForm.debttrans,size:"mini"}},[t("el-table-column",{attrs:{prop:"day",label:"跟进日期"}}),t("el-table-column",{attrs:{prop:"status_name",label:"跟进状态"}}),t("el-table-column",{attrs:{prop:"type_name",label:"跟进类型"}}),t("el-table-column",{attrs:{prop:"back_money",label:"回款金额（元）"}}),t("el-table-column",{attrs:{prop:"desc",label:"进度描述"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(a.$index,a.row.id)}}},[e._v("移除")])]}}],null,!1,1963948310)})],1)],1)],1):e._e(),t("div",{staticClass:"drawer-footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确定")])],1)],1)]):e._e(),e.activeDebtTab.startsWith("evidence")?t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"el-icon-folder"}),e._v(" "+e._s(e.getEvidenceTitle())+" "),t("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"mini"},on:{click:e.uploadEvidence}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 上传证据 ")])],1),t("div",{staticClass:"evidence-container"},["evidence-all"===e.activeDebtTab||"evidence-image"===e.activeDebtTab?t("div",[t("div",{staticClass:"evidence-section"},[t("h4",[e._v("身份证照片")]),t("el-button-group",{staticStyle:{"margin-bottom":"10px"}},[t("el-button",{on:{click:function(t){return e.changeFile("cards")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传身份证 ")])],1)],1),e.ruleForm.cards&&e.ruleForm.cards.length>0?t("div",{staticClass:"evidence-grid"},e._l(e.ruleForm.cards,(function(a,s){return t("div",{key:s,staticClass:"evidence-item"},[t("div",{staticClass:"evidence-preview"},[t("img",{staticClass:"evidence-image",attrs:{src:a},on:{click:function(t){return e.showImage(a)}}})]),t("div",{staticClass:"evidence-actions"},[t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.delImage(a,"cards",s)}}},[e._v("删除")])],1)])})),0):e._e()],1)]):e._e(),"evidence-all"===e.activeDebtTab||"evidence-image"===e.activeDebtTab?t("div",[t("div",{staticClass:"evidence-section"},[t("h4",[e._v("证据图片")]),t("el-button-group",{staticStyle:{"margin-bottom":"10px"}},[t("el-button",{on:{click:function(t){return e.changeFile("images")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传图片 ")])],1)],1),e.ruleForm.images&&e.ruleForm.images.length>0?t("div",{staticClass:"evidence-grid"},e._l(e.ruleForm.images,(function(a,s){return t("div",{key:s,staticClass:"evidence-item"},[t("div",{staticClass:"evidence-preview"},[t("el-image",{staticStyle:{width:"100%",height:"150px"},attrs:{src:a,"preview-src-list":e.ruleForm.images,fit:"cover"}})],1),t("div",{staticClass:"evidence-actions"},[t("el-button",{attrs:{type:"primary",size:"mini"}},[t("a",{staticStyle:{color:"white","text-decoration":"none"},attrs:{href:a,target:"_blank",download:"evidence."+a.split(".")[1]}},[e._v("下载")])]),t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.delImage(a,"images",s)}}},[e._v("删除")])],1)])})),0):e._e(),e.ruleForm.del_images&&e.ruleForm.del_images.length>0?t("div",{staticStyle:{"margin-top":"20px"}},[t("h5",[e._v("已删除的图片")]),t("div",{staticClass:"evidence-grid"},e._l(e.ruleForm.del_images,(function(a,s){return t("div",{key:s,staticClass:"evidence-item"},[t("div",{staticClass:"evidence-preview"},[t("el-image",{staticStyle:{width:"100%",height:"150px"},attrs:{src:a,"preview-src-list":e.ruleForm.del_images,fit:"cover"}})],1),t("div",{staticClass:"evidence-actions"},[t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.delImage(a,"del_images",s)}}},[e._v("删除")])],1)])})),0)]):e._e()],1)]):e._e(),"evidence-all"===e.activeDebtTab||"evidence-document"===e.activeDebtTab?t("div",[t("div",{staticClass:"evidence-section"},[t("h4",[e._v("证据文件")]),t("el-button-group",{staticStyle:{"margin-bottom":"10px"}},[t("el-button",{on:{click:function(t){return e.changeFile("attach_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传文件 ")])],1)],1),e.ruleForm.attach_path&&e.ruleForm.attach_path.length>0?t("div",{staticClass:"file-list"},e._l(e.ruleForm.attach_path,(function(a,s){return a?t("div",{key:s,staticClass:"file-item"},[t("div",{staticClass:"file-icon"},[t("i",{staticClass:"el-icon-document file-type-icon"})]),t("div",{staticClass:"file-info"},[t("div",{staticClass:"file-name"},[e._v("文件"+e._s(s+1))])]),t("div",{staticClass:"file-actions"},[t("el-button",{attrs:{type:"primary",size:"mini"}},[t("a",{staticStyle:{color:"white","text-decoration":"none"},attrs:{href:a,target:"_blank"}},[e._v("查看")])]),t("el-button",{attrs:{type:"success",size:"mini"}},[t("a",{staticStyle:{color:"white","text-decoration":"none"},attrs:{href:a,target:"_blank"}},[e._v("下载")])]),t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.delImage(a,"attach_path",s)}}},[e._v("移除")])],1)]):e._e()})),0):e._e(),e.ruleForm.del_attach_path&&e.ruleForm.del_attach_path.length>0?t("div",{staticStyle:{"margin-top":"20px"}},[t("h5",[e._v("已删除的文件")]),t("div",{staticClass:"file-list"},e._l(e.ruleForm.del_attach_path,(function(a,s){return a?t("div",{key:s,staticClass:"file-item"},[t("div",{staticClass:"file-icon"},[t("i",{staticClass:"el-icon-document file-type-icon"})]),t("div",{staticClass:"file-info"},[t("div",{staticClass:"file-name"},[e._v("文件"+e._s(s+1))])]),t("div",{staticClass:"file-actions"},[t("el-button",{attrs:{type:"primary",size:"mini"}},[t("a",{staticStyle:{color:"white","text-decoration":"none"},attrs:{href:a,target:"_blank"}},[e._v("查看")])]),t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.delImage(a,"del_attach_path",s)}}},[e._v("移除")])],1)]):e._e()})),0)]):e._e()],1)]):e._e(),e.hasEvidence()?e._e():t("div",{staticClass:"no-evidence"},[t("i",{staticClass:"el-icon-folder-opened"}),t("span",[e._v("暂无"+e._s(e.getEvidenceTypeText())+"证据")]),t("br"),t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.uploadEvidence}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 上传第一个证据 ")])],1)])])]):e._e()])])]),t("el-dialog",{attrs:{title:"用户列表",visible:e.dialogUserFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogUserFormVisible=t}}},[t("el-row",{staticStyle:{width:"300px"}},[t("el-input",{attrs:{placeholder:"请输入内容",size:"mini"},model:{value:e.searchUser.keyword,callback:function(t){e.$set(e.searchUser,"keyword",t)},expression:"searchUser.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.searchUserData()}},slot:"append"})],1)],1),t("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.listUser,size:"mini"},on:{"current-change":e.selUserData}},[t("el-table-column",{attrs:{label:"选择"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-radio",{attrs:{label:a.$index},model:{value:e.ruleForm.user_id,callback:function(t){e.$set(e.ruleForm,"user_id",t)},expression:"ruleForm.user_id"}},[e._v("  ")])]}}])}),t("el-table-column",{attrs:{prop:"phone",label:"注册手机号码"}}),t("el-table-column",{attrs:{prop:"nickname",label:"名称"}}),t("el-table-column",{attrs:{prop:"",label:"头像"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("div",[""==e.row.headimg?t("el-row"):t("el-row",[t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.row.headimg}})])],1)]}}])}),t("el-table-column",{attrs:{prop:"linkman",label:"联系人"}}),t("el-table-column",{attrs:{prop:"linkphone",label:"联系号码"}}),t("el-table-column",{attrs:{prop:"yuangong_id",label:"用户来源"}}),t("el-table-column",{attrs:{prop:"end_time",label:"到期时间"}}),t("el-table-column",{attrs:{prop:"create_time",label:"录入时间"}})],1)],1),t("el-dialog",{attrs:{title:"跟进",visible:e.dialogDebttransFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogDebttransFormVisible=t}}},[t("el-form",{ref:"ruleFormDebttrans",attrs:{model:e.ruleFormDebttrans,rules:e.rulesDebttrans}},[t("el-form-item",{attrs:{label:"跟进日期","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleFormDebttrans.day,callback:function(t){e.$set(e.ruleFormDebttrans,"day",t)},expression:"ruleFormDebttrans.day"}})],1),t("el-form-item",{attrs:{label:"跟进状态","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1},nativeOn:{click:function(t){return e.debtStatusClick("2")}},model:{value:e.ruleFormDebttrans.status,callback:function(t){e.$set(e.ruleFormDebttrans,"status",t)},expression:"ruleFormDebttrans.status"}},[e._v("待处理")]),t("el-radio",{attrs:{label:2},nativeOn:{click:function(t){return e.debtStatusClick("2")}},model:{value:e.ruleFormDebttrans.status,callback:function(t){e.$set(e.ruleFormDebttrans,"status",t)},expression:"ruleFormDebttrans.status"}},[e._v("调节中")]),t("el-radio",{attrs:{label:3},nativeOn:{click:function(t){return e.debtStatusClick("1")}},model:{value:e.ruleFormDebttrans.status,callback:function(t){e.$set(e.ruleFormDebttrans,"status",t)},expression:"ruleFormDebttrans.status"}},[e._v("转诉讼")]),t("el-radio",{attrs:{label:4},nativeOn:{click:function(t){return e.debtStatusClick("2")}},model:{value:e.ruleFormDebttrans.status,callback:function(t){e.$set(e.ruleFormDebttrans,"status",t)},expression:"ruleFormDebttrans.status"}},[e._v("已结案")]),t("el-radio",{attrs:{label:5},nativeOn:{click:function(t){return e.debtStatusClick("2")}},model:{value:e.ruleFormDebttrans.status,callback:function(t){e.$set(e.ruleFormDebttrans,"status",t)},expression:"ruleFormDebttrans.status"}},[e._v("已取消")])],1)]),t("el-form-item",{attrs:{label:"跟进类型","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1},nativeOn:{click:function(t){return e.typeClick("1")}},model:{value:e.ruleFormDebttrans.type,callback:function(t){e.$set(e.ruleFormDebttrans,"type",t)},expression:"ruleFormDebttrans.type"}},[e._v("日常")]),t("el-radio",{attrs:{label:2},nativeOn:{click:function(t){return e.typeClick("2")}},model:{value:e.ruleFormDebttrans.type,callback:function(t){e.$set(e.ruleFormDebttrans,"type",t)},expression:"ruleFormDebttrans.type"}},[e._v("回款")])],1)]),t("el-form-item",{attrs:{label:"支付费用","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1},nativeOn:{click:function(t){return e.payTypeClick("1")}},model:{value:e.ruleFormDebttrans.pay_type,callback:function(t){e.$set(e.ruleFormDebttrans,"pay_type",t)},expression:"ruleFormDebttrans.pay_type"}},[e._v("无需支付")]),t("el-radio",{attrs:{label:2},nativeOn:{click:function(t){return e.payTypeClick("2")}},model:{value:e.ruleFormDebttrans.pay_type,callback:function(t){e.$set(e.ruleFormDebttrans,"pay_type",t)},expression:"ruleFormDebttrans.pay_type"}},[e._v("待支付")]),t("el-radio",{attrs:{label:3},nativeOn:{click:function(t){return e.payTypeClick("3")}},model:{value:e.ruleFormDebttrans.pay_type,callback:function(t){e.$set(e.ruleFormDebttrans,"pay_type",t)},expression:"ruleFormDebttrans.pay_type"}},[e._v("已支付")])],1)]),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogRichangVisible,expression:"dialogRichangVisible"}],attrs:{label:"费用金额","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleFormDebttrans.total_price,callback:function(t){e.$set(e.ruleFormDebttrans,"total_price",t)},expression:"ruleFormDebttrans.total_price"}}),e._v("元 ")],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogRichangVisible,expression:"dialogRichangVisible"}],attrs:{label:"费用内容","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleFormDebttrans.content,callback:function(t){e.$set(e.ruleFormDebttrans,"content",t)},expression:"ruleFormDebttrans.content"}})],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogHuikuanVisible,expression:"dialogHuikuanVisible"}],attrs:{label:"回款日期","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleFormDebttrans.back_day,callback:function(t){e.$set(e.ruleFormDebttrans,"back_day",t)},expression:"ruleFormDebttrans.back_day"}})],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogHuikuanVisible,expression:"dialogHuikuanVisible"}],attrs:{label:"回款金额","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},on:{input:function(t){return e.editRateMoney()}},model:{value:e.ruleFormDebttrans.back_money,callback:function(t){e.$set(e.ruleFormDebttrans,"back_money",t)},expression:"ruleFormDebttrans.back_money"}}),e._v("元 ")],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogHuikuanVisible,expression:"dialogHuikuanVisible"}],attrs:{label:"手续费金额","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},on:{input:function(t){return e.editRateMoney()}},model:{value:e.ruleFormDebttrans.rate,callback:function(t){e.$set(e.ruleFormDebttrans,"rate",t)},expression:"ruleFormDebttrans.rate"}}),e._v("% "),t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleFormDebttrans.rate_money,callback:function(t){e.$set(e.ruleFormDebttrans,"rate_money",t)},expression:"ruleFormDebttrans.rate_money"}}),e._v("元 ")],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dialogZfrqVisible,expression:"dialogZfrqVisible"}],attrs:{label:"支付日期","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleFormDebttrans.pay_time,callback:function(t){e.$set(e.ruleFormDebttrans,"pay_time",t)},expression:"ruleFormDebttrans.pay_time"}})],1),t("el-form-item",{attrs:{label:"进度描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleFormDebttrans.desc,callback:function(t){e.$set(e.ruleFormDebttrans,"desc",t)},expression:"ruleFormDebttrans.desc"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogDebttransFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveDebttransData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-drawer",{attrs:{visible:e.drawerViewDebtDetail,direction:"rtl",size:"70%","before-close":e.handleDebtDetailDrawerClose,"custom-class":"modern-drawer"},on:{"update:visible":function(t){e.drawerViewDebtDetail=t}}},[t("div",{staticClass:"drawer-title",attrs:{slot:"title"},slot:"title"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("债务人详情")])]),t("div",{staticClass:"drawer-content-wrapper"},[t("div",{staticClass:"drawer-sidebar"},[t("el-menu",{staticClass:"drawer-menu",attrs:{"default-active":e.activeDebtDetailTab},on:{select:e.handleDebtDetailTabSelect}},[t("el-menu-item",{attrs:{index:"details"}},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("债务详情")])]),t("el-menu-item",{attrs:{index:"progress"}},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v("跟进记录")])]),t("el-menu-item",{attrs:{index:"evidence"}},[t("i",{staticClass:"el-icon-folder"}),t("span",[e._v("证据材料")])]),t("el-menu-item",{attrs:{index:"documents"}},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("相关文档")])])],1)],1),t("div",{staticClass:"drawer-content"},[t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card",staticStyle:{"overflow-x":"auto","max-width":"100%"}},["details"===e.activeDebtDetailTab?t("div",[t("debt-detail",{attrs:{id:e.currentDebtId}})],1):"progress"===e.activeDebtDetailTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("跟进记录")]),t("el-timeline",[t("el-timeline-item",{attrs:{timestamp:"2024-01-15 10:30",placement:"top"}},[t("el-card",[t("h4",[e._v("电话联系")]),t("p",[e._v("已与债务人取得联系，对方表示将在本月底前还款")])])],1),t("el-timeline-item",{attrs:{timestamp:"2024-01-10 14:20",placement:"top"}},[t("el-card",[t("h4",[e._v("发送催款函")]),t("p",[e._v("向债务人发送正式催款函，要求在15日内还款")])])],1),t("el-timeline-item",{attrs:{timestamp:"2024-01-05 09:15",placement:"top"}},[t("el-card",[t("h4",[e._v("案件受理")]),t("p",[e._v("案件正式受理，开始债务追讨程序")])])],1)],1)],1):"evidence"===e.activeDebtDetailTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("证据材料")]),t("div",{staticClass:"evidence-grid"},[t("div",{staticClass:"evidence-item"},[t("i",{staticClass:"el-icon-picture"}),t("span",[e._v("借条照片")]),t("el-button",{attrs:{type:"text"}},[e._v("查看")])],1),t("div",{staticClass:"evidence-item"},[t("i",{staticClass:"el-icon-chat-line-square"}),t("span",[e._v("聊天记录")]),t("el-button",{attrs:{type:"text"}},[e._v("查看")])],1),t("div",{staticClass:"evidence-item"},[t("i",{staticClass:"el-icon-bank-card"}),t("span",[e._v("转账记录")]),t("el-button",{attrs:{type:"text"}},[e._v("查看")])],1)])]):"documents"===e.activeDebtDetailTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("相关文档")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.debtDocuments}},[t("el-table-column",{attrs:{prop:"name",label:"文档名称"}}),t("el-table-column",{attrs:{prop:"type",label:"文档类型"}}),t("el-table-column",{attrs:{prop:"uploadTime",label:"上传时间"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"}},[e._v("下载")]),t("el-button",{attrs:{type:"text"}},[e._v("预览")])]}}])})],1)],1):e._e()])])])])]),t("el-dialog",{attrs:{title:"导入跟进记录",visible:e.uploadVisible,width:"30%"},on:{"update:visible":function(t){e.uploadVisible=t},close:e.closeUploadDialog}},[t("el-form",{ref:"uploadForm",attrs:{"label-position":"right","label-width":"110px"}},[t("el-form-item",{attrs:{label:"选择文件:"}},[t("el-upload",{ref:"upload",attrs:{"auto-upload":!1,action:e.uploadAction,data:e.uploadData,"on-success":e.uploadSuccess,"before-upload":e.checkFile,accept:".xls,.xlsx",limit:"1",multiple:"false"}},[t("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选择文件")])],1)],1),t("div",{staticStyle:{"text-align":"right"}},[t("el-button",{attrs:{type:"primary",size:"small",loading:e.submitOrderLoading2},on:{click:e.submitUpload}},[e._v("提交")]),t("el-button",{attrs:{size:"small"},on:{click:e.closeDialog}},[e._v("取消")])],1)],1)],1),t("el-dialog",{attrs:{title:"导入债权人",visible:e.uploadDebtsVisible,width:"30%"},on:{"update:visible":function(t){e.uploadDebtsVisible=t},close:e.closeUploadDebtsDialog}},[t("el-form",{ref:"uploadForm",attrs:{"label-position":"right","label-width":"110px"}},[t("el-form-item",{attrs:{label:"选择文件:"}},[t("el-upload",{ref:"upload",attrs:{"auto-upload":!1,action:e.uploadDebtsAction,data:e.uploadDebtsData,"on-success":e.uploadSuccess,"before-upload":e.checkFile,accept:".xls,.xlsx",limit:"1",multiple:"false"}},[t("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选择文件")])],1)],1),t("div",{staticStyle:{"text-align":"right"}},[t("el-button",{attrs:{type:"primary",size:"small",loading:e.submitOrderLoading3},on:{click:e.submitUploadDebts}},[e._v("提交")]),t("el-button",{attrs:{size:"small"},on:{click:e.closeUploadDebtsDialog}},[e._v("取消")])],1)],1)],1),t("el-drawer",{attrs:{visible:e.drawerViewUserDetail,direction:"rtl",size:"70%","before-close":e.handleUserDetailDrawerClose,"custom-class":"modern-drawer"},on:{"update:visible":function(t){e.drawerViewUserDetail=t}}},[t("div",{staticClass:"drawer-title",attrs:{slot:"title"},slot:"title"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",[e._v("用户详情")])]),t("div",{staticClass:"drawer-content-wrapper"},[t("div",{staticClass:"drawer-sidebar"},[t("el-menu",{staticClass:"drawer-menu",attrs:{"default-active":e.activeUserTab},on:{select:e.handleUserTabSelect}},[t("el-menu-item",{attrs:{index:"customer"}},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("客户信息")])]),t("el-menu-item",{attrs:{index:"member"}},[t("i",{staticClass:"el-icon-medal"}),t("span",[e._v("会员信息")])]),t("el-menu-item",{attrs:{index:"debts"}},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("债务人信息")])]),t("el-menu-item",{attrs:{index:"attachments"}},[t("i",{staticClass:"el-icon-folder-opened"}),t("span",[e._v("附件信息")])])],1)],1),t("div",{staticClass:"drawer-content"},[t("div",{staticClass:"tab-content"},[t("div",{staticClass:"card"},["customer"===e.activeUserTab?t("div",[t("user-detail",{attrs:{id:e.currentId}})],1):"member"===e.activeUserTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("会员信息")]),t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"会员等级"}},[e._v("普通会员")]),t("el-descriptions-item",{attrs:{label:"会员状态"}},[e._v("正常")]),t("el-descriptions-item",{attrs:{label:"注册时间"}},[e._v("2024-01-01")]),t("el-descriptions-item",{attrs:{label:"最后登录"}},[e._v("2024-01-15")]),t("el-descriptions-item",{attrs:{label:"积分余额"}},[e._v("1000")]),t("el-descriptions-item",{attrs:{label:"会员权益"}},[e._v("基础服务")])],1)],1):"debts"===e.activeUserTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("关联债务人信息")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userDebtsList}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名"}}),t("el-table-column",{attrs:{prop:"phone",label:"联系电话"}}),t("el-table-column",{attrs:{prop:"amount",label:"债务金额"}}),t("el-table-column",{attrs:{prop:"status",label:"状态"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.viewDebtData(a.row.id)}}},[e._v("查看详情")])]}}])})],1)],1):"attachments"===e.activeUserTab?t("div",[t("h3",{staticClass:"section-title"},[e._v("相关附件")]),t("div",{staticClass:"attachment-grid"},[t("div",{staticClass:"attachment-item"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("身份证正面")]),t("el-button",{attrs:{type:"text"}},[e._v("下载")])],1),t("div",{staticClass:"attachment-item"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("身份证反面")]),t("el-button",{attrs:{type:"text"}},[e._v("下载")])],1),t("div",{staticClass:"attachment-item"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("营业执照")]),t("el-button",{attrs:{type:"text"}},[e._v("下载")])],1)])]):e._e()])])])])])],1)},i=[],l=a("d522"),r=a("26b2"),o=a("4360"),n={name:"list",components:{UserDetail:l["a"],DebtDetail:r["a"]},data(){return{uploadAction:"",uploadDebtsAction:"/admin/debt/importDebts?token="+this.$store.getters.GET_TOKEN,uploadVisible:!1,uploadDebtsVisible:!1,submitOrderLoading2:!1,submitOrderLoading3:!1,uploadData:{review:!1},uploadDebtsData:{review:!1},allSize:"mini",listUser:[],list:[{id:1,uid:1001,name:"张三",tel:"13800138001",money:"50000",status:"待处理",back_money:"0",un_money:"50000",ctime:"2024-01-15 10:30:00",address:"北京市朝阳区建国路88号",idcard_no:"110101199001011234",case_des:"借款纠纷，借款人未按约定时间还款",users:{nickname:"李四"}},{id:2,uid:1002,name:"王五",tel:"13900139002",money:"120000",status:"调节中",back_money:"30000",un_money:"90000",ctime:"2024-01-10 14:20:00",address:"上海市浦东新区陆家嘴金融区",idcard_no:"310101199205155678",case_des:"合同纠纷，未按合同约定支付货款",users:{nickname:"赵六"}},{id:3,uid:1003,name:"陈七",tel:"13700137003",money:"80000",status:"诉讼中",back_money:"20000",un_money:"60000",ctime:"2024-01-05 09:15:00",address:"广州市天河区珠江新城",idcard_no:"******************",case_des:"服务费纠纷，拒绝支付约定的服务费用",users:{nickname:"孙八"}},{id:4,uid:1004,name:"刘九",tel:"13600136004",money:"200000",status:"已结案",back_money:"200000",un_money:"0",ctime:"2023-12-20 16:45:00",address:"深圳市南山区科技园",idcard_no:"******************",case_des:"投资纠纷，已通过调解达成一致",users:{nickname:"周十"}},{id:5,uid:1005,name:"吴十一",tel:"13500135005",money:"75000",status:"待处理",back_money:"0",un_money:"75000",ctime:"2024-01-18 11:30:00",address:"杭州市西湖区文三路",idcard_no:"330101199406067890",case_des:"租赁纠纷，拖欠房租及违约金",users:{nickname:"郑十二"}},{id:6,uid:1006,name:"马十三",tel:"13400134006",money:"150000",status:"调节中",back_money:"50000",un_money:"100000",ctime:"2024-01-12 13:20:00",address:"成都市锦江区春熙路",idcard_no:"510101199009091234",case_des:"买卖合同纠纷，货物质量问题导致损失",users:{nickname:"冯十四"}}],total:6,page:1,currentId:0,currentDebtId:0,pageUser:1,sizeUser:20,searchUser:{keyword:""},size:20,search:{keyword:"",status:-1,prop:"",order:""},loading:!0,url:"/debt/",urlUser:"/user/",title:"债务",info:{images:[],attach_path:[],cards:[],debttrans:[]},dialogUserFormVisible:!1,dialogViewUserDetail:!1,drawerViewUserDetail:!1,drawerViewDebtDetail:!1,dialogZfrqVisible:!1,dialogRichangVisible:!1,dialogHuikuanVisible:!1,dialogDebttransFormVisible:!1,dialogFormVisible:!1,viewFormVisible:!1,dialogViewDebtDetail:!1,show_image:"",dialogVisible:!1,ruleFormDebttrans:{title:""},ruleForm:{images:[],del_images:[],attach_path:[],del_attach_path:[],cards:[],debttrans:[]},rulesDebttrans:{day:[{required:!0,message:"请选择跟进日期",trigger:"blur"}],status:[{required:!0,message:"请选择跟进状态",trigger:"blur"}]},rules:{uid:[{required:!0,message:"请选择用户",trigger:"blur"}],name:[{required:!0,message:"请填写债务人姓名",trigger:"blur"}],money:[{required:!0,message:"请填写债务金额",trigger:"blur"}],case_des:[{required:!0,message:"请填写案由",trigger:"blur"}]},formLabelWidth:"140px",options:[{id:-1,title:"请选择"},{id:1,title:"待处理"},{id:2,title:"调节中"},{id:3,title:"诉讼中"},{id:4,title:"已结案"}],activeDebtTab:"details",activeUserTab:"customer",activeDebtDetailTab:"details",userDebtsList:[{id:1,name:"债务人A",phone:"13900139001",amount:"50000",status:"处理中"},{id:2,name:"债务人B",phone:"13900139002",amount:"30000",status:"已完成"}],debtDocuments:[{name:"借款合同.pdf",type:"合同文件",uploadTime:"2024-01-10"},{name:"催款函.doc",type:"法律文书",uploadTime:"2024-01-12"},{name:"还款计划.xlsx",type:"财务文件",uploadTime:"2024-01-15"}]}},mounted(){this.getData()},methods:{changeFile(e){this.filed=e},searchUserData(){this.pageUser=1,this.sizeUser=20,this.getUserData(this.ruleForm)},getUserData(e){let t=this;t.ruleForm=e,t.postRequest(t.urlUser+"index?page="+t.pageUser+"&size="+t.sizeUser,t.searchUser).then(e=>{200==e.code&&(t.dialogFormVisible=!1,t.listUser=e.data)})},typeClick(e){this.$set(this.ruleFormDebttrans,"total_price",""),this.$set(this.ruleFormDebttrans,"back_money",""),this.$set(this.ruleFormDebttrans,"content",""),this.$set(this.ruleFormDebttrans,"rate",""),1==e?(this.dialogHuikuanVisible=!1,this.dialogZfrqVisible=!1,1==this.ruleFormDebttrans["pay_type"]?this.dialogRichangVisible=!1:this.dialogRichangVisible=!0):(this.dialogRichangVisible=!1,this.dialogHuikuanVisible=!0,3!=this.ruleFormDebttrans["pay_type"]?this.dialogZfrqVisible=!1:this.dialogZfrqVisible=!0)},editRateMoney(){this.ruleFormDebttrans["rate"]>0&&this.ruleFormDebttrans["back_money"]>0&&this.$set(this.ruleFormDebttrans,"rate_money",this.ruleFormDebttrans["rate"]*this.ruleFormDebttrans["back_money"]/100)},selUserData(e){e&&(this.$set(this.ruleForm,"uid",e.id),e.phone&&this.$set(this.ruleForm,"utel",e.phone),e.nickname&&this.$set(this.ruleForm,"uname",e.nickname),this.dialogFormVisible=!0,this.dialogUserFormVisible=!1)},payTypeClick(e){2!=e&&3!=e||(1==this.ruleFormDebttrans["type"]?this.dialogRichangVisible=!0:this.dialogRichangVisible=!1),3==e&&(2==this.ruleFormDebttrans["type"]?this.dialogZfrqVisible=!0:this.dialogZfrqVisible=!1),1==e&&(this.dialogZfrqVisible=!1,this.dialogRichangVisible=!1,2==this.ruleFormDebttrans["type"]?this.dialogHuikuanVisible=!0:this.dialogHuikuanVisible=!1)},clearData(){this.search={keyword:"",status:"",prop:"",order:""},this.getData()},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={images:[],del_images:[],attach_path:[],del_attach_path:[],cards:[],debttrans:[]},t.activeDebtTab="details",t.dialogFormVisible=!0},viewUserData(e){let t=this;0!=e&&(this.currentId=e),t.drawerViewUserDetail=!0},viewDebtData(e){let t=this;0!=e&&(this.currentDebtId=e),t.drawerViewDebtDetail=!0},editDebttransData(e){0!=e?this.getDebttransInfo(e):this.ruleFormDebttrans={name:""}},viewData(e){0!=e?this.getView(e):this.ruleForm={title:"",desc:""}},getView(e){let t=this;t.getRequest(t.url+"view?id="+e).then(a=>{200==a.code?(t.info=a.data,t.viewFormVisible=!0,t.uploadAction="/admin/user/import?id="+e+"&token="+this.$store.getters.GET_TOKEN):t.$message({type:"error",message:a.msg})})},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{200==e.code?(t.ruleForm=e.data,console.log(e.data)):t.$message({type:"error",message:e.msg})})},getDebttransInfo(e){let t=this;t.getRequest(t.url+"debttransRead?id="+e).then(e=>{200==e.code?(t.ruleFormDebttrans=e.data,t.dialogZfrqVisible=!1,t.dialogRichangVisible=!1,t.dialogHuikuanVisible=!1,t.dialogDebttransFormVisible=!0):t.$message({type:"error",message:e.msg})})},tuikuan(e){this.$confirm("是否申请退款?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"tuikuan?id="+e).then(e=>{200==e.code?this.$message({type:"success",message:e.msg}):this.$message({type:"error",message:e.msg})})}).catch(()=>{this.$message({type:"error",message:"取消退款!"})})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.getData(),this.info.debttrans.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},delDataDebt(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"deleteDebt?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.getData(),this.info.debttrans.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0;const t="localhost"===window.location.hostname;t?setTimeout(()=>{e.loading=!1},500):e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},saveDebttransData(){let e=this;this.$refs["ruleFormDebttrans"].validate(t=>{if(!t)return!1;this.ruleFormDebttrans["token"]=o["a"].getters.GET_TOKEN,this.postRequest(e.url+"saveDebttrans",this.ruleFormDebttrans).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogZfrqVisible=!1,e.dialogRichangVisible=!1,e.dialogHuikuanVisible=!1,e.dialogDebttransFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){if(200==e.code){this.$message.success("上传成功");this.ruleForm[this.filed];this.ruleForm[this.filed].splice(1,0,e.data.url)}else this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},showUserList(){this.searchUserData(),this.dialogUserFormVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t,a){let s=this;s.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(s.ruleForm[t].splice(a,1),s.$message.success("删除成功!")):s.$message.error(e.msg)})},handleSortChange({column:e,prop:t,order:a}){this.search.prop=t,this.search.order=a,this.getData()},exports:function(){let e=this;location.href="/admin/debt/view?token="+e.$store.getters.GET_TOKEN+"&export=1&id="+e.ruleForm.id},exportsDebtList:function(){let e=this;location.href="/admin/debt/exportList?token="+e.$store.getters.GET_TOKEN+"&keyword="+e.search.keyword},closeUploadDialog(){this.uploadVisible=!1,this.$refs.upload.clearFiles(),this.uploadData.review=!1},closeUploadDebtsDialog(){this.uploadDebtsVisible=!1,this.$refs.upload.clearFiles(),this.uploadDebtsData.review=!1},uploadSuccess(e){200===e.code?(this.$message({type:"success",message:e.msg}),this.uploadVisible=!1,this.getData(),console.log(e)):this.$message({type:"warning",message:e.msg}),this.submitOrderLoading2=!1,this.$refs.upload.clearFiles()},uploadDebtsSuccess(e){200===e.code?(this.$message({type:"success",message:e.msg}),this.uploadDebtsVisible=!1,this.getData(),console.log(e)):this.$message({type:"warning",message:e.msg}),this.submitOrderLoading3=!1,this.$refs.upload.clearFiles()},checkFile(e){let t=["xls","xlsx"],a=e.name.split(".").slice(-1)[0].toLowerCase();return!!t.includes(a)||(this.$message({type:"warning",message:"文件格式错误仅支持 xls xlxs 文件"}),!1)},submitUpload(){this.submitOrderLoading2=!0,this.$refs.upload.submit()},submitUploadDebts(){this.submitOrderLoading3=!0,this.$refs.upload.submit()},closeDialog(){this.addVisible=!1,this.uploadVisible=!1,this.form={id:"",nickname:"",mobile:"",school_id:0,grade_id:"",class_id:"",sex:"",is_poor:"",is_display:"",number:"",remark:"",is_remark_option:0,remark_option:[],mobile_checked:!1},this.$refs.form.resetFields()},openUpload(){this.uploadVisible=!0},openUploadDebts(){this.uploadDebtsVisible=!0},handleDrawerClose(){this.dialogFormVisible=!1},handleUserDetailDrawerClose(){this.drawerViewUserDetail=!1,this.activeUserTab="customer"},handleDebtDetailDrawerClose(){this.drawerViewDebtDetail=!1,this.activeDebtDetailTab="details"},handleUserTabSelect(e){this.activeUserTab=e},handleDebtDetailTabSelect(e){this.activeDebtDetailTab=e},handleDebtTabSelect(e){this.activeDebtTab=e},getEvidenceTitle(){const e=this.activeDebtTab;switch(e){case"evidence-all":return"全部证据";case"evidence-video":return"视频证据";case"evidence-image":return"图片证据";case"evidence-audio":return"语音证据";case"evidence-document":return"文档证据";default:return"债务人详情"}},getEvidenceTypeText(){const e=this.activeDebtTab;switch(e){case"evidence-all":return"全部";case"evidence-video":return"视频";case"evidence-image":return"图片";case"evidence-audio":return"语音";case"evidence-document":return"文档";default:return"债务人详情"}},hasEvidence(){const e=this.activeDebtTab;switch(e){case"evidence-all":return this.ruleForm.cards.length>0||this.ruleForm.images.length>0||this.ruleForm.attach_path.length>0;case"evidence-video":return this.ruleForm.images.length>0;case"evidence-image":return this.ruleForm.images.length>0;case"evidence-audio":return this.ruleForm.attach_path.length>0;case"evidence-document":return this.ruleForm.attach_path.length>0;default:return!1}},uploadEvidence(){}}},c=n,d=(a("5c89"),a("2877")),u=Object(d["a"])(c,s,i,!1,null,"0bf8986a",null);t["default"]=u.exports}}]);
//# sourceMappingURL=chunk-a3ccb5d0.5f599820.js.map