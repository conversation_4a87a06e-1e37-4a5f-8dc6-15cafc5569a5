{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\order.vue?vue&type=style&index=0&id=27cc1d20&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\order.vue", "mtime": 1748540171927}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovKiDkuLvlrrnlmaggKi8NCi5wYXltZW50LW1hbmFnZW1lbnQgew0KICBwYWRkaW5nOiAwOw0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCn0NCg0KLyog6aG16Z2i5aS06YOoICovDQoucGFnZS1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBjb2xvcjogd2hpdGU7DQogIHBhZGRpbmc6IDI0cHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5oZWFkZXItY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWF4LXdpZHRoOiAxMjAwcHg7DQogIG1hcmdpbjogMCBhdXRvOw0KfQ0KDQouaGVhZGVyLWxlZnQgew0KICBmbGV4OiAxOw0KfQ0KDQoucGFnZS10aXRsZSB7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEycHg7DQp9DQoNCi5wYWdlLXRpdGxlIGkgew0KICBmb250LXNpemU6IDMycHg7DQp9DQoNCi5wYWdlLXN1YnRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBvcGFjaXR5OiAwLjk7DQogIGZvbnQtd2VpZ2h0OiA0MDA7DQp9DQoNCi5oZWFkZXItYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTJweDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnJlZnJlc2gtYnRuIHsNCiAgcGFkZGluZzogMTJweCAyNHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnJlZnJlc2gtYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOw0KICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KTsNCiAgY29sb3I6IHdoaXRlOw0KfQ0KDQovKiDkuLvljaHniYcgKi8NCi5tYWluLWNhcmQgew0KICBtYXJnaW46IDAgMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi8qIOe7n+iuoeWNoeeJhyAqLw0KLnN0YXRzLWNhcmRzIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSk7DQogIGdhcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnN0YXQtY2FyZCB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA1KTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnN0YXQtY2FyZDpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5zdGF0LWljb24gew0KICB3aWR0aDogNTBweDsNCiAgaGVpZ2h0OiA1MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5wYXltZW50LWljb24gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KfQ0KDQoub3JkZXItaWNvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIgMCUsICNmNTU3NmMgMTAwJSk7DQp9DQoNCi5zdWNjZXNzLWljb24gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNGZhY2ZlIDAlLCAjMDBmMmZlIDEwMCUpOw0KfQ0KDQoucGVuZGluZy1pY29uIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQzZTk3YiAwJSwgIzM4ZjlkNyAxMDAlKTsNCn0NCg0KLnN0YXQtY29udGVudCB7DQogIGZsZXg6IDE7DQp9DQoNCi5zdGF0LXZhbHVlIHsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KfQ0KDQouc3RhdC1sYWJlbCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOaQnOe0ouWMuuWfnyAqLw0KLnNlYXJjaC1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnNlYXJjaC1mb3JtIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5zZWFyY2gtbWFpbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kOw0KICBnYXA6IDI0cHg7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouc2VhcmNoLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMjBweDsNCiAgZmxleDogMTsNCiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kOw0KfQ0KDQouc2VhcmNoLXJpZ2h0IHsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5zZWFyY2gtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGdhcDogOHB4Ow0KfQ0KDQouc2VhcmNoLWl0ZW0gbGFiZWwgew0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQouc2VhcmNoLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEycHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi8qIOihqOagvOWMuuWfnyAqLw0KLnRhYmxlLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoucGF5bWVudC10YWJsZSB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLyog56m654q25oCBICovDQouZW1wdHktc3RhdGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgcGFkZGluZzogODBweCAyMHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KfQ0KDQouZW1wdHktaWNvbiB7DQogIHdpZHRoOiA4MHB4Ow0KICBoZWlnaHQ6IDgwcHg7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5lbXB0eS1pY29uIGkgew0KICBmb250LXNpemU6IDQwcHg7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmVtcHR5LXRleHQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5lbXB0eS10ZXh0IGgzIHsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5lbXB0eS10ZXh0IHAgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW46IDA7DQp9DQoNCi8qIOihqOagvOWNleWFg+agvOagt+W8jyAqLw0KLm9yZGVyLWluZm8tY2VsbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGdhcDogNHB4Ow0KfQ0KDQoub3JkZXItbnVtYmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQoub3JkZXItbnVtYmVyIGkgew0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnBhY2thZ2UtbmFtZSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5hbW91bnQtY2VsbCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmFtb3VudCB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgY29sb3I6ICNlNzRjM2M7DQp9DQoNCi5zdGF0dXMtdGFnIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnR5cGUtY2VsbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQoudHlwZS1jZWxsIGkgew0KICBjb2xvcjogIzliNTliNjsNCn0NCg0KLnVzZXItaW5mby1jZWxsIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBhZGRpbmc6IDRweDsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoudXNlci1pbmZvLWNlbGw6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KfQ0KDQoudXNlci1hdmF0YXIgew0KICB3aWR0aDogMzJweDsNCiAgaGVpZ2h0OiAzMnB4Ow0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi51c2VyLWRldGFpbHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDJweDsNCiAgbWluLXdpZHRoOiAwOw0KICBmbGV4OiAxOw0KfQ0KDQoudXNlci1uYW1lIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQp9DQoNCi51c2VyLXBob25lIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQp9DQoNCi5jbGlja2FibGUgew0KICBjb2xvcjogIzM0OThkYjsNCiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7DQp9DQoNCi5jbGlja2FibGU6aG92ZXIgew0KICBjb2xvcjogIzI5ODBiOTsNCn0NCg0KLnRpbWUtaW5mbyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi50aW1lLWluZm8gaSB7DQogIGNvbG9yOiAjOTVhNWE2Ow0KfQ0KDQovKiDmk43kvZzmjInpkq4gKi8NCi5hY3Rpb24tYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNHB4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouYWN0aW9uLWJ1dHRvbnMgLmVsLWJ1dHRvbiB7DQogIG1hcmdpbjogMDsNCn0NCg0KLyog5YiG6aG1ICovDQoucGFnaW5hdGlvbi13cmFwcGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHggMDsNCn0NCg0KLnBhZ2luYXRpb24gew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxMnB4IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KfQ0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsNCiAgLnNlYXJjaC1sZWZ0IHsNCiAgICBnYXA6IDE2cHg7DQogIH0NCg0KICAuc2VhcmNoLWl0ZW0gaW5wdXQsDQogIC5zZWFyY2gtaXRlbSAuZWwtc2VsZWN0LA0KICAuc2VhcmNoLWl0ZW0gLmVsLWRhdGUtZWRpdG9yIHsNCiAgICB3aWR0aDogMjAwcHggIWltcG9ydGFudDsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHsNCiAgLnNlYXJjaC1tYWluIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOw0KICAgIGdhcDogMjBweDsNCiAgfQ0KDQogIC5zZWFyY2gtbGVmdCB7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KICB9DQoNCiAgLnNlYXJjaC1yaWdodCB7DQogICAgYWxpZ24tc2VsZjogZmxleC1lbmQ7DQogIH0NCg0KICAuc2VhcmNoLWFjdGlvbnMgew0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5oZWFkZXItY29udGVudCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDE2cHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQoNCiAgLnN0YXRzLWNhcmRzIHsNCiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsNCiAgfQ0KDQogIC5zZWFyY2gtbWFpbiB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsNCiAgICBnYXA6IDIwcHg7DQogIH0NCg0KICAuc2VhcmNoLWxlZnQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxNnB4Ow0KICB9DQoNCiAgLnNlYXJjaC1pdGVtIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KDQogIC5zZWFyY2gtaXRlbSBpbnB1dCwNCiAgLnNlYXJjaC1pdGVtIC5lbC1zZWxlY3QsDQogIC5zZWFyY2gtaXRlbSAuZWwtZGF0ZS1lZGl0b3Igew0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuc2VhcmNoLWFjdGlvbnMgew0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgfQ0KfQ0KDQovKiDnlKjmiLfor6bmg4Xmu5Hlh7rpnaLmnb8gKi8NCi51c2VyLWRldGFpbC1kcmF3ZXIgew0KICB6LWluZGV4OiAzMDAwOw0KfQ0KDQoudXNlci1kZXRhaWwtZHJhd2VyIC5lbC1kcmF3ZXJfX2JvZHkgew0KICBwYWRkaW5nOiAwOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBoZWlnaHQ6IDEwMHZoOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQoudXNlci1kZXRhaWwtZHJhd2VyIC5lbC1kcmF3ZXJfX2hlYWRlciB7DQogIGRpc3BsYXk6IG5vbmU7DQp9DQoNCi8qIOiHquWumuS5ieWktOmDqCAqLw0KLmRyYXdlci1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGNvbG9yOiB3aGl0ZTsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5oZWFkZXItY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTZweDsNCn0NCg0KLnVzZXItYXZhdGFyLWxhcmdlIHsNCiAgd2lkdGg6IDYwcHg7DQogIGhlaWdodDogNjBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiB3aGl0ZTsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi51c2VyLWluZm8gaDMgew0KICBtYXJnaW46IDAgMCA0cHggMDsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi51c2VyLWluZm8gcCB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KfQ0KDQouY2xvc2UtYnRuIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDIwcHg7DQogIHJpZ2h0OiAyMHB4Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBwYWRkaW5nOiA4cHg7DQp9DQoNCi5jbG9zZS1idG46aG92ZXIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQovKiDpnaLmnb/kuLvkvZMgKi8NCi5kcmF3ZXItYm9keSB7DQogIHBhZGRpbmc6IDI0cHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGZsZXg6IDE7DQogIG1pbi1oZWlnaHQ6IDA7DQp9DQoNCi5pbmZvLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouaW5mby1zZWN0aW9uIGg0IHsNCiAgbWFyZ2luOiAwIDAgMTZweCAwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmluZm8tc2VjdGlvbiBoNCBpIHsNCiAgY29sb3I6ICM2NjdlZWE7DQp9DQoNCi8qIOS/oeaBr+e9keagvCAqLw0KLmluZm8tZ3JpZCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouaW5mby1pdGVtIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7DQp9DQoNCi5pbmZvLWl0ZW0gbGFiZWwgew0KICBkaXNwbGF5OiBibG9jazsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouaW5mby1pdGVtIHNwYW4gew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQovKiDnu5/orqHnvZHmoLwgKi8NCi5zdGF0cy1ncmlkIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTsNCiAgZ2FwOiAxMHB4Ow0KfQ0KDQouc3RhdC1pdGVtIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDIwcHggMTZweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KfQ0KDQouc3RhdC1udW1iZXIgew0KICBmb250LXNpemU6IDIwcHg7DQogIGZvbnQtd2VpZ2h0OiA3MDA7DQogIGNvbG9yOiAjNjY3ZWVhOw0KICBtYXJnaW4tYm90dG9tOiA0cHg7DQp9DQoNCi5zdGF0LWxhYmVsIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLyog5YC65Yqh5Lq65YiX6KGoICovDQouZGVidG9ycy1saXN0IHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7DQp9DQoNCi5kZWJ0b3ItaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTZweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzOw0KfQ0KDQouZGVidG9yLWl0ZW06aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOw0KfQ0KDQouZGVidG9yLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5kZWJ0b3ItaW5mbyB7DQogIGZsZXg6IDE7DQogIG1pbi13aWR0aDogMDsNCn0NCg0KLmRlYnRvci1uYW1lIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KfQ0KDQouZGVidG9yLXBob25lIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzdmOGM4ZDsNCn0NCg0KLmRlYnRvci1vcmRlcnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCiAgY29sb3I6ICM2NjdlZWE7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLm9yZGVyLWNvdW50IHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLyog5pyA6L+R6K6i5Y2VICovDQoucmVjZW50LW9yZGVycyB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KfQ0KDQoub3JkZXItaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTZweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5vcmRlci1pdGVtOmxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQoub3JkZXItaW5mbyB7DQogIGZsZXg6IDE7DQogIG1pbi13aWR0aDogMDsNCn0NCg0KLm9yZGVyLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCn0NCg0KLm9yZGVyLW1ldGEgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEycHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5vcmRlci1zdGF0dXMgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5vcmRlci1hbW91bnQgew0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjZTc0YzNjOw0KICBtYXJnaW4tYm90dG9tOiA0cHg7DQp9DQoNCi8qIOaXoOaVsOaNrueKtuaAgSAqLw0KLm5vLWRhdGEgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDMycHggMTZweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLyog5pON5L2c5Yy65Z+fICovDQouYWN0aW9uLXNlY3Rpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEycHg7DQogIHBhZGRpbmctdG9wOiAxNnB4Ow0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2VlZTsNCn0NCg0KLmFjdGlvbi1zZWN0aW9uIC5lbC1idXR0b24gew0KICBmbGV4OiAxOw0KfQ0KDQovKiDmu5Hlh7rliqjnlLvkvJjljJYgKi8NCi51c2VyLWRldGFpbC1kcmF3ZXIgLmVsLWRyYXdlciB7DQogIGJveC1zaGFkb3c6IC0ycHggMCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLnVzZXItZGV0YWlsLWRyYXdlciAuZWwtZHJhd2VyX19jb250YWluZXIgew0KICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMnB4KTsNCiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjMpOw0KfQ0KDQovKiDlhbzlrrnml6fmoLflvI8gKi8NCi5wYWdlLXRvcCB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQp9DQoNCi5lbF9pbnB1dCB7DQogIHdpZHRoOiA0NzVweDsNCn0NCg0KLmVsLWNvbCB7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5lbC1wYWdpbmF0aW9uLWNvdW50IHsNCiAgZm9udC13ZWlnaHQ6IDgwMDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["order.vue"], "names": [], "mappings": ";AAqpCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "order.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"payment-management\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <div class=\"page-title\">\r\n            <i class=\"el-icon-money\"></i>\r\n            <span>支付列表管理</span>\r\n          </div>\r\n          <div class=\"page-subtitle\">管理和查看所有支付订单信息</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            class=\"refresh-btn\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"main-card\" shadow=\"never\">\r\n      <!-- 统计卡片 -->\r\n      <div class=\"stats-cards\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon payment-icon\">\r\n            <i class=\"el-icon-coin\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ money }}元</div>\r\n            <div class=\"stat-label\">总支付金额</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon order-icon\">\r\n            <i class=\"el-icon-document\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ total }}</div>\r\n            <div class=\"stat-label\">订单总数</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon success-icon\">\r\n            <i class=\"el-icon-success\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ paidCount }}</div>\r\n            <div class=\"stat-label\">已支付订单</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon pending-icon\">\r\n            <i class=\"el-icon-time\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-value\">{{ pendingCount }}</div>\r\n            <div class=\"stat-label\">待处理订单</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索区域 -->\r\n      <div class=\"search-section\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-main\">\r\n            <div class=\"search-left\">\r\n              <div class=\"search-item\">\r\n                <label>关键词搜索</label>\r\n                <el-input\r\n                  v-model=\"search.keyword\"\r\n                  placeholder=\"请输入订单号/套餐名称\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  clearable\r\n                  style=\"width: 280px;\"\r\n                />\r\n              </div>\r\n              <div class=\"search-item\">\r\n                <label>支付状态</label>\r\n                <el-select\r\n                  v-model=\"search.is_pay\"\r\n                  placeholder=\"请选择支付状态\"\r\n                  clearable\r\n                  style=\"width: 150px;\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.title\"\r\n                    :value=\"item.id\"\r\n                  />\r\n                </el-select>\r\n              </div>\r\n              <div class=\"search-item\">\r\n                <label>处理状态</label>\r\n                <el-select\r\n                  v-model=\"search.is_deal\"\r\n                  placeholder=\"请选择处理状态\"\r\n                  clearable\r\n                  style=\"width: 150px;\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in options1\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.title\"\r\n                    :value=\"item.id\"\r\n                  />\r\n                </el-select>\r\n              </div>\r\n              <div class=\"search-item\">\r\n                <label>支付时间</label>\r\n                <el-date-picker\r\n                  v-model=\"search.pay_time\"\r\n                  type=\"daterange\"\r\n                  unlink-panels\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  :default-time=\"['00:00:00', '23:59:59']\"\r\n                  style=\"width: 300px;\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div class=\"search-right\">\r\n              <div class=\"search-actions\">\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getData()\">\r\n                  搜索\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh-left\" @click=\"clearData()\">\r\n                  重置\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportData()\">\r\n                  导出\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-section\">\r\n        <!-- 空状态 -->\r\n        <div v-if=\"!loading && list.length === 0\" class=\"empty-state\">\r\n          <div class=\"empty-icon\">\r\n            <i class=\"el-icon-document-remove\"></i>\r\n          </div>\r\n          <div class=\"empty-text\">\r\n            <h3>暂无支付订单</h3>\r\n            <p>当前没有找到任何支付订单数据</p>\r\n          </div>\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-refresh\">\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table\r\n          v-else\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"payment-table\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n\r\n          <el-table-column label=\"订单信息\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"order-info-cell\">\r\n                <div class=\"order-number\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>{{ scope.row.order_sn }}</span>\r\n                </div>\r\n                <div class=\"package-name\">{{ scope.row.title || '暂无套餐' }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"支付金额\" prop=\"total_price\" width=\"120\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"amount-cell\">\r\n                <span class=\"amount\">¥{{ scope.row.total_price || '0.00' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"支付状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getPayStatusType(scope.row.is_pay)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ scope.row.is_pay }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"处理状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getDealStatusType(scope.row.is_deal)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ scope.row.is_deal }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"购买类型\" prop=\"body\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"type-cell\">\r\n                <i class=\"el-icon-shopping-bag-1\"></i>\r\n                <span>{{ scope.row.body || '暂无' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"用户信息\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"user-info-cell\" @click=\"viewUserData(scope.row.uid)\">\r\n                <div class=\"user-avatar\">\r\n                  <i class=\"el-icon-user\"></i>\r\n                </div>\r\n                <div class=\"user-details\">\r\n                  <div class=\"user-name clickable\">{{ scope.row.user_name || '未知用户' }}</div>\r\n                  <div class=\"user-phone\">{{ scope.row.phone || '暂无手机号' }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"支付时间\" prop=\"refund_time\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ formatDate(scope.row.refund_time) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"创建时间\" prop=\"create_time\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                <i class=\"el-icon-date\"></i>\r\n                <span>{{ formatDate(scope.row.create_time) }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  v-if=\"scope.row.is_pay == '未支付'\"\r\n                  type=\"warning\"\r\n                  size=\"mini\"\r\n                  @click=\"free(scope.row.id)\"\r\n                  icon=\"el-icon-coin\"\r\n                  title=\"免支付\"\r\n                >\r\n                  免支付\r\n                </el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"viewUserData(scope.row.uid)\"\r\n                  icon=\"el-icon-view\"\r\n                  title=\"查看用户详情\"\r\n                >\r\n                  查看\r\n                </el-button>\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-check\"\r\n                  title=\"完成制作\"\r\n                >\r\n                  制作\r\n                </el-button>\r\n                <el-dropdown trigger=\"click\">\r\n                  <el-button size=\"mini\" type=\"info\" icon=\"el-icon-more\">\r\n                    更多\r\n                  </el-button>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item @click.native=\"tuikuan(scope.row.id)\">\r\n                      <i class=\"el-icon-refresh-left\"></i>\r\n                      退款\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"delData(scope.$index, scope.row.id)\">\r\n                      <i class=\"el-icon-delete\"></i>\r\n                      取消订单\r\n                    </el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          class=\"pagination\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\t\t<el-dialog title=\"订单查看\" :visible.sync=\"viewFormVisible\" :close-on-click-modal=\"false\">\r\n\t\t\t\t<el-descriptions title=\"订单信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"订单号\">{{info.order_sn}}</el-descriptions-item>\r\n\r\n\t\t\t\t\t<el-descriptions-item label=\"购买类型\">{{info.body}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"支付金额\">{{info.total_price}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"支付状态\">{{info.is_pay_name}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"支付时间\">{{info.pay_time}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"支付方式\">微信支付</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"退款时间\">{{info.refund_time}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"免支付操作人\">{{info.free_operator}}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"服务信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"服务信息\">{{info.body}}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"用户信息\">\r\n                  <el-descriptions-item label=\"用户姓名\"><div @click=\"viewUserData(info.uid)\">{{info.linkman}}</div></el-descriptions-item>\r\n                  <el-descriptions-item label=\"用户电话\"><div @click=\"viewUserData(info.uid)\">{{info.linkphone}}</div></el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"债务人信息\">\r\n                  <el-descriptions-item label=\"债务人姓名\"><div @click=\"viewDebtData(info.dt_id)\">{{info.debts_name}}</div></el-descriptions-item>\r\n                  <el-descriptions-item label=\"债务人电话\"><div @click=\"viewDebtData(info.dt_id)\">{{info.debts_tel}}</div></el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"制作信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"制作状态\">{{info.is_deal_name}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"制作文件\">文件<a :href=\"info.file_path\" target=\"_blank\">查看</a><a :href=\"info.file_path\">下载</a></el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"viewFormVisible = false\">取 消</el-button>\r\n            </div>\r\n\t\t</el-dialog>\r\n        <!-- 用户详情右侧滑出面板 -->\r\n        <el-drawer\r\n          :visible.sync=\"userDetailDrawerVisible\"\r\n          direction=\"rtl\"\r\n          size=\"650px\"\r\n          :close-on-click-modal=\"true\"\r\n          :wrapperClosable=\"true\"\r\n          :show-close=\"false\"\r\n          class=\"user-detail-drawer\"\r\n          @close=\"closeUserDetailDrawer\"\r\n        >\r\n          <div class=\"drawer-header\">\r\n            <div class=\"header-content\">\r\n              <div class=\"user-avatar-large\">\r\n                <i class=\"el-icon-user\"></i>\r\n              </div>\r\n              <div class=\"user-info\">\r\n                <h3>{{ getCurrentUserName() }}</h3>\r\n                <p>{{ getCurrentUserPhone() }}</p>\r\n              </div>\r\n            </div>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              class=\"close-btn\"\r\n              @click=\"closeUserDetailDrawer\"\r\n            ></el-button>\r\n          </div>\r\n\r\n          <div class=\"drawer-body\">\r\n            <div class=\"info-section\">\r\n              <h4><i class=\"el-icon-user\"></i> 基本信息</h4>\r\n              <div class=\"info-grid\">\r\n                <div class=\"info-item\">\r\n                  <label>用户姓名</label>\r\n                  <span>{{ getCurrentUserName() }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <label>手机号码</label>\r\n                  <span>{{ getCurrentUserPhone() }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <label>用户ID</label>\r\n                  <span>{{ currentId }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <label>注册时间</label>\r\n                  <span>2024-01-10 10:30:00</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"info-section\">\r\n              <h4><i class=\"el-icon-shopping-cart-2\"></i> 订单统计</h4>\r\n              <div class=\"stats-grid\">\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-number\">{{ getUserOrderCount() }}</div>\r\n                  <div class=\"stat-label\">总订单数</div>\r\n                </div>\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-number\">{{ getUserOrderAmount() }}</div>\r\n                  <div class=\"stat-label\">总消费金额</div>\r\n                </div>\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-number\">{{ getUserPaidCount() }}</div>\r\n                  <div class=\"stat-label\">已支付订单</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"info-section\">\r\n              <h4><i class=\"el-icon-user-solid\"></i> 关联债务人</h4>\r\n              <div class=\"debtors-list\">\r\n                <div\r\n                  v-for=\"debtor in getUserDebtors()\"\r\n                  :key=\"debtor.dt_id\"\r\n                  class=\"debtor-item\"\r\n                  @click=\"viewDebtData(debtor.dt_id)\"\r\n                >\r\n                  <div class=\"debtor-info\">\r\n                    <div class=\"debtor-name\">{{ debtor.debts_name }}</div>\r\n                    <div class=\"debtor-phone\">{{ debtor.debts_tel }}</div>\r\n                  </div>\r\n                  <div class=\"debtor-orders\">\r\n                    <span class=\"order-count\">{{ getDebtorOrderCount(debtor.dt_id) }}个订单</span>\r\n                    <i class=\"el-icon-arrow-right\"></i>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"getUserDebtors().length === 0\" class=\"no-data\">\r\n                  暂无关联债务人\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"info-section\">\r\n              <h4><i class=\"el-icon-document\"></i> 最近订单</h4>\r\n              <div class=\"recent-orders\">\r\n                <div\r\n                  v-for=\"order in getUserRecentOrders()\"\r\n                  :key=\"order.id\"\r\n                  class=\"order-item\"\r\n                >\r\n                  <div class=\"order-info\">\r\n                    <div class=\"order-title\">{{ order.title }}</div>\r\n                    <div class=\"order-meta\">\r\n                      <span class=\"order-sn\">{{ order.order_sn }}</span>\r\n                      <span class=\"order-time\">{{ formatDate(order.create_time) }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"order-status\">\r\n                    <div class=\"order-amount\">¥{{ order.total_price }}</div>\r\n                    <el-tag\r\n                      :type=\"getPayStatusType(order.is_pay)\"\r\n                      size=\"mini\"\r\n                    >\r\n                      {{ order.is_pay }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"getUserRecentOrders().length === 0\" class=\"no-data\">\r\n                  暂无订单记录\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"action-section\">\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"small\"\r\n                icon=\"el-icon-document\"\r\n                @click=\"viewAllOrdersForUser\"\r\n                style=\"width: 100%;\"\r\n              >\r\n                查看该用户所有订单\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"债务查看\" :visible.sync=\"dialogViewDebtDetail\" :close-on-click-modal=\"false\" width=\"80%\">\r\n\r\n          <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"dialogViewDebtDetail = false\">取 消</el-button>\r\n          </div>\r\n        </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails,DebtDetail },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      money: 0,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n        pay_time: null\r\n      },\r\n      loading: true,\r\n      url: \"/order/\",\r\n      title: \"订单\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      userDetailDrawerVisible: false,\r\n      orderDetailDrawerVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      currentOrderId: 0,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      selectedOrders: [], // 选中的订单\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"支付状态\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"处理状态\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 已支付订单数量\r\n    paidCount() {\r\n      return this.list.filter(order => order.is_pay === '已支付').length;\r\n    },\r\n    // 待处理订单数量\r\n    pendingCount() {\r\n      return this.list.filter(order => order.is_deal === '待处理').length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    // 设置一个超时，确保不会无限加载\r\n    setTimeout(() => {\r\n      if (this.loading) {\r\n        this.loading = false;\r\n        this.$message.warning('数据加载超时，请刷新重试');\r\n      }\r\n    }, 10000);\r\n  },\r\n  methods: {\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '暂无';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour12: false });\r\n    },\r\n\r\n    // 获取支付状态类型\r\n    getPayStatusType(status) {\r\n      const statusMap = {\r\n        '已支付': 'success',\r\n        '未支付': 'warning',\r\n        '退款': 'info'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取处理状态类型\r\n    getDealStatusType(status) {\r\n      const statusMap = {\r\n        '已处理': 'success',\r\n        '待处理': 'warning',\r\n        '处理中': 'primary'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedOrders = selection;\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      if (this.selectedOrders.length > 0) {\r\n        this.$message.success(`导出选中的 ${this.selectedOrders.length} 条订单数据`);\r\n      } else {\r\n        this.$message.success('导出全部订单数据');\r\n      }\r\n    },\r\n\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n        pay_time: null\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.userDetailDrawerVisible = true;\r\n    },\r\n\r\n    // 关闭用户详情面板\r\n    closeUserDetailDrawer() {\r\n      this.userDetailDrawerVisible = false;\r\n      this.currentId = 0;\r\n    },\r\n\r\n    // 获取当前用户姓名\r\n    getCurrentUserName() {\r\n      const user = this.list.find(item => item.uid === this.currentId);\r\n      return user ? user.user_name : '未知用户';\r\n    },\r\n\r\n    // 获取当前用户手机号\r\n    getCurrentUserPhone() {\r\n      const user = this.list.find(item => item.uid === this.currentId);\r\n      return user ? user.phone : '暂无手机号';\r\n    },\r\n\r\n    // 获取用户订单数量\r\n    getUserOrderCount() {\r\n      return this.list.filter(order => order.uid === this.currentId).length;\r\n    },\r\n\r\n    // 获取用户总消费金额\r\n    getUserOrderAmount() {\r\n      const userOrders = this.list.filter(order => order.uid === this.currentId);\r\n      const total = userOrders.reduce((sum, order) => {\r\n        return sum + parseFloat(order.total_price || 0);\r\n      }, 0);\r\n      return '¥' + total.toFixed(2);\r\n    },\r\n\r\n    // 获取用户已支付订单数\r\n    getUserPaidCount() {\r\n      return this.list.filter(order =>\r\n        order.uid === this.currentId && order.is_pay === '已支付'\r\n      ).length;\r\n    },\r\n\r\n    // 获取用户最近订单（最多3个）\r\n    getUserRecentOrders() {\r\n      return this.list\r\n        .filter(order => order.uid === this.currentId)\r\n        .sort((a, b) => new Date(b.create_time) - new Date(a.create_time))\r\n        .slice(0, 3);\r\n    },\r\n\r\n    // 获取用户关联的债务人\r\n    getUserDebtors() {\r\n      const userOrders = this.list.filter(order => order.uid === this.currentId);\r\n      const debtorsMap = new Map();\r\n\r\n      userOrders.forEach(order => {\r\n        if (order.dt_id && order.debts_name) {\r\n          debtorsMap.set(order.dt_id, {\r\n            dt_id: order.dt_id,\r\n            debts_name: order.debts_name,\r\n            debts_tel: order.debts_tel || '暂无电话'\r\n          });\r\n        }\r\n      });\r\n\r\n      return Array.from(debtorsMap.values());\r\n    },\r\n\r\n    // 获取某个债务人的订单数量\r\n    getDebtorOrderCount(debtorId) {\r\n      return this.list.filter(order =>\r\n        order.uid === this.currentId && order.dt_id === debtorId\r\n      ).length;\r\n    },\r\n\r\n    // 查看该用户所有订单\r\n    viewAllOrdersForUser() {\r\n      // 关闭用户详情面板\r\n      this.closeUserDetailDrawer();\r\n\r\n      // 设置搜索条件为当前用户\r\n      const currentUser = this.list.find(item => item.uid === this.currentId);\r\n      if (currentUser) {\r\n        this.search.keyword = currentUser.user_name || currentUser.phone;\r\n        this.getData();\r\n        this.$message.success(`已筛选显示用户\"${currentUser.user_name || currentUser.phone}\"的所有订单`);\r\n      }\r\n    },\r\n    viewDebtData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentDebtId = id;\r\n      }\r\n\r\n      _this.dialogViewDebtDetail = true;\r\n    },\r\n    editData(id) {\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n      free(id) {\r\n        var _this = this;\r\n      this.$confirm(\"是否设定此订单为免支付?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.postRequest(\"/dingdan/free?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"修改成功!\",\r\n              });\r\n                _this.getData();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            order_sn: 'ORD202401001',\r\n            title: '法律咨询套餐A',\r\n            total_price: '299.00',\r\n            is_pay: '已支付',\r\n            is_deal: '已处理',\r\n            body: '法律咨询',\r\n            phone: '13800138001',\r\n            user_name: '张明华',\r\n            uid: 1,\r\n            refund_time: '2024-01-15 14:30:00',\r\n            create_time: '2024-01-15 10:00:00'\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: 'ORD202401002',\r\n            title: '合同审查套餐B',\r\n            total_price: '599.00',\r\n            is_pay: '未支付',\r\n            is_deal: '待处理',\r\n            body: '合同审查',\r\n            phone: '13800138002',\r\n            user_name: '李晓雯',\r\n            uid: 2,\r\n            refund_time: null,\r\n            create_time: '2024-01-16 09:15:00'\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: 'ORD202401003',\r\n            title: '律师函服务',\r\n            total_price: '899.00',\r\n            is_pay: '已支付',\r\n            is_deal: '处理中',\r\n            body: '律师函',\r\n            phone: '13800138003',\r\n            user_name: '王建国',\r\n            uid: 3,\r\n            refund_time: '2024-01-16 16:45:00',\r\n            create_time: '2024-01-16 15:20:00'\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: 'ORD202401004',\r\n            title: '诉讼代理服务',\r\n            total_price: '1999.00',\r\n            is_pay: '退款',\r\n            is_deal: '已处理',\r\n            body: '诉讼代理',\r\n            phone: '13800138004',\r\n            user_name: '陈美玲',\r\n            uid: 4,\r\n            refund_time: '2024-01-17 11:20:00',\r\n            create_time: '2024-01-17 08:30:00'\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: 'ORD202401005',\r\n            title: '企业法务顾问',\r\n            total_price: '3999.00',\r\n            is_pay: '已支付',\r\n            is_deal: '已处理',\r\n            body: '法务顾问',\r\n            phone: '13800138005',\r\n            user_name: '刘志强',\r\n            uid: 5,\r\n            refund_time: '2024-01-18 13:10:00',\r\n            create_time: '2024-01-18 10:45:00'\r\n          },\r\n          {\r\n            id: 6,\r\n            order_sn: 'ORD202401006',\r\n            title: '知识产权保护',\r\n            total_price: '1299.00',\r\n            is_pay: '未支付',\r\n            is_deal: '待处理',\r\n            body: '知识产权',\r\n            phone: '13800138006',\r\n            user_name: '赵雅琴',\r\n            uid: 6,\r\n            refund_time: null,\r\n            create_time: '2024-01-19 14:25:00'\r\n          },\r\n          {\r\n            id: 7,\r\n            order_sn: 'ORD202401007',\r\n            title: '劳动纠纷处理',\r\n            total_price: '799.00',\r\n            is_pay: '已支付',\r\n            is_deal: '处理中',\r\n            body: '劳动纠纷',\r\n            phone: '13800138007',\r\n            user_name: '孙文博',\r\n            uid: 7,\r\n            refund_time: '2024-01-20 10:15:00',\r\n            create_time: '2024-01-20 09:00:00'\r\n          },\r\n          {\r\n            id: 8,\r\n            order_sn: 'ORD202401008',\r\n            title: '房产交易法务',\r\n            total_price: '1599.00',\r\n            is_pay: '已支付',\r\n            is_deal: '已处理',\r\n            body: '房产法务',\r\n            phone: '13800138008',\r\n            user_name: '周慧敏',\r\n            uid: 8,\r\n            refund_time: '2024-01-21 15:40:00',\r\n            create_time: '2024-01-21 12:30:00'\r\n          }\r\n        ];\r\n        _this.total = _this.list.length;\r\n        _this.money = _this.list.reduce((sum, item) => {\r\n          return sum + parseFloat(item.total_price || 0);\r\n        }, 0).toFixed(2);\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原始API调用（作为备用）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count.count;\r\n            _this.money = resp.count.money;\r\n          } else {\r\n            // 如果API失败，使用上面的测试数据\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // API错误时也使用测试数据\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 主容器 */\r\n.payment-management {\r\n  padding: 0;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.refresh-btn {\r\n  padding: 12px 24px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  color: white;\r\n}\r\n\r\n/* 主卡片 */\r\n.main-card {\r\n  margin: 0 24px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: none;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n  border: 1px solid #f0f0f0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.payment-icon {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.order-icon {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.success-icon {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.pending-icon {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-form {\r\n  width: 100%;\r\n}\r\n\r\n.search-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  gap: 24px;\r\n  width: 100%;\r\n}\r\n\r\n.search-left {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  flex: 1;\r\n  align-items: flex-end;\r\n}\r\n\r\n.search-right {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-item label {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  white-space: nowrap;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.empty-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.empty-icon i {\r\n  font-size: 40px;\r\n  color: white;\r\n}\r\n\r\n.empty-text {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.empty-text h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 8px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.empty-text p {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.order-info-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.order-number {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.order-number i {\r\n  color: #3498db;\r\n}\r\n\r\n.package-name {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.amount-cell {\r\n  text-align: center;\r\n}\r\n\r\n.amount {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 600;\r\n}\r\n\r\n.type-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.type-cell i {\r\n  color: #9b59b6;\r\n}\r\n\r\n.user-info-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-info-cell:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.user-avatar {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.user-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n  min-width: 0;\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.user-phone {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.clickable {\r\n  color: #3498db;\r\n  text-decoration: underline;\r\n}\r\n\r\n.clickable:hover {\r\n  color: #2980b9;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.time-info i {\r\n  color: #95a5a6;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 4px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 0;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 20px 0;\r\n}\r\n\r\n.pagination {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 12px 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .search-left {\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item input,\r\n  .search-item .el-select,\r\n  .search-item .el-date-editor {\r\n    width: 200px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .search-main {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 20px;\r\n  }\r\n\r\n  .search-left {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .search-right {\r\n    align-self: flex-end;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .stats-cards {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .search-main {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 20px;\r\n  }\r\n\r\n  .search-left {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-item input,\r\n  .search-item .el-select,\r\n  .search-item .el-date-editor {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n}\r\n\r\n/* 用户详情滑出面板 */\r\n.user-detail-drawer {\r\n  z-index: 3000;\r\n}\r\n\r\n.user-detail-drawer .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.user-detail-drawer .el-drawer__header {\r\n  display: none;\r\n}\r\n\r\n/* 自定义头部 */\r\n.drawer-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px;\r\n  position: relative;\r\n  color: white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.user-avatar-large {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.user-info h3 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: white;\r\n}\r\n\r\n.user-info p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.close-btn {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  color: white !important;\r\n  font-size: 18px;\r\n  padding: 8px;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 面板主体 */\r\n.drawer-body {\r\n  padding: 24px;\r\n  overflow-y: auto;\r\n  flex: 1;\r\n  min-height: 0;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.info-section h4 {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-section h4 i {\r\n  color: #667eea;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.info-item {\r\n  background: white;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.info-item label {\r\n  display: block;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 4px;\r\n  font-weight: 500;\r\n}\r\n\r\n.info-item span {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 统计网格 */\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 10px;\r\n}\r\n\r\n.stat-item {\r\n  background: white;\r\n  padding: 20px 16px;\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stat-number {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  color: #667eea;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 债务人列表 */\r\n.debtors-list {\r\n  background: white;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.debtor-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.debtor-item:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.debtor-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.debtor-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.debtor-name {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.debtor-phone {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.debtor-orders {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #667eea;\r\n  font-size: 12px;\r\n}\r\n\r\n.order-count {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 最近订单 */\r\n.recent-orders {\r\n  background: white;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.order-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.order-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.order-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.order-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.order-meta {\r\n  display: flex;\r\n  gap: 12px;\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.order-status {\r\n  text-align: right;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.order-amount {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-data {\r\n  text-align: center;\r\n  padding: 32px 16px;\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  background: white;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-section {\r\n  display: flex;\r\n  gap: 12px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.action-section .el-button {\r\n  flex: 1;\r\n}\r\n\r\n/* 滑出动画优化 */\r\n.user-detail-drawer .el-drawer {\r\n  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-detail-drawer .el-drawer__container {\r\n  backdrop-filter: blur(2px);\r\n  background: rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.el-col {\r\n  overflow: hidden;\r\n}\r\n\r\n.el-pagination-count {\r\n  font-weight: 800;\r\n  color: #606266;\r\n  line-height: 30px;\r\n}\r\n</style>\r\n"]}]}