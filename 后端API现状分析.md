# 法律服务管理系统 - 后端API现状分析

## 项目概述

### 技术架构
- **后端框架**: ThinkPHP 5.1
- **数据库**: MySQL
- **认证方式**: JWT Token
- **文件上传**: 支持图片、文档等多种格式
- **导入导出**: 支持Excel格式

### 目录结构
```
fdbadmin/
├── application/admin/controller/    # 控制器
├── extend/models/                   # 数据模型
├── extend/untils/                   # 工具类
├── config/                          # 配置文件
└── public/                          # 公共资源
```

## 现有API控制器分析

### 1. 用户管理 (User.php)

**现有功能**:
- ✅ `index()` - 用户列表查询 (支持keyword搜索)
- ✅ `save()` - 用户保存/更新
- ✅ `read()` - 用户详情查询 (包含债务人信息)
- ✅ `delete()` - 用户删除
- ✅ `export()` - 用户导出
- ✅ `import()` - 用户导入

**需要扩展**:
- 🔄 `index()` - 支持多字段分别搜索 (nickname, phone, company等)
- 🔄 `read()` - 返回附件信息
- ➕ 批量删除API
- ➕ 附件管理API (上传/删除)
- ➕ 债务人管理API (增删改)

### 2. 债务人管理 (Debt.php)

**现有功能**:
- ✅ `index()` - 债务人列表查询
- ✅ `save()` - 债务人保存/更新
- ✅ `read()` - 债务人详情查询 (包含跟进记录)
- ✅ `delete()` - 删除跟进记录
- ✅ `saveDebttrans()` - 添加跟进记录
- ✅ `export()` - 债务人导出
- ✅ `import()` - 债务人导入

**功能完善度**: 90% (基本满足新前端需求)

### 3. 订单管理 (Dingdan.php)

**现有功能**:
- ✅ `index()` - 订单列表查询
- ✅ `save()` - 订单保存/更新
- ✅ `read()` - 订单详情查询
- ✅ `delete()` - 订单删除
- ✅ `changeStatus()` - 订单审核

**需要扩展**:
- ➕ 批量审核API
- ➕ 订单统计API
- ➕ 高级搜索功能

### 4. 聊天管理 (Chat.php)

**现有功能**:
- ✅ `sendMessage()` - 发送私聊消息
- ✅ `sendQunMessage()` - 发送群聊消息
- ✅ `chatList()` - 获取聊天记录
- ✅ `qunliaoList()` - 获取群聊记录
- ✅ `getQun()` - 获取群组列表

**功能完善度**: 85% (基本满足需求)

### 5. 其他控制器

**已实现**:
- ✅ `Banner.php` - 轮播图管理
- ✅ `Config.php` - 系统配置
- ✅ `Gonggao.php` - 公告管理
- ✅ `Taocan.php` - 套餐管理
- ✅ `Yuangong.php` - 员工管理
- ✅ `Upload.php` - 文件上传

## 数据库表结构

### 核心表分析

#### web_users (用户表)
```sql
- id: 主键
- nickname: 用户名称
- phone: 手机号
- company: 公司名称
- linkman: 联系人
- linkphone: 联系号码
- headimg: 头像
- license: 营业执照
- yuangong_id: 用户来源
- tiaojie_id: 调解员ID
- fawu_id: 法务专员ID
- lian_id: 立案专员ID
- ls_id: 律师ID
- ywy_id: 业务员ID
- htsczy_id: 合同专员ID
```

#### web_debts (债务表)
```sql
- id: 主键
- uid: 用户ID
- name: 债务人姓名
- tel: 债务人电话
- money: 债务金额
- back_money: 已回款金额
- status: 债务状态
- cards: 身份证图片
- images: 证据图片
- attach_path: 附件路径
```

#### web_orders (订单表)
```sql
- id: 主键
- uid: 用户ID
- total_price: 总金额
- order_sn: 订单号
- is_pay: 支付状态
- type: 订单类型
```

## 需要新增的API接口

### 1. 仪表板统计API (新增)
```php
// DashboardController.php
- stats() - 统计数据
- activities() - 最近活动
- todos() - 待办事项
```

### 2. 用户管理扩展API
```php
// User.php 扩展
- batchDelete() - 批量删除
- uploadAttachment() - 上传附件
- deleteAttachment() - 删除附件
- addDebt() - 添加债务人
- updateDebt() - 更新债务人
- deleteDebt() - 删除债务人
```

### 3. 订单管理扩展API
```php
// Dingdan.php 扩展
- batchAudit() - 批量审核
- getStats() - 订单统计
- advancedSearch() - 高级搜索
```

## API响应格式规范

### 统一响应格式
```json
{
    "code": 200,
    "msg": "success",
    "data": {},
    "count": 0
}
```

### 错误处理
- 使用 `JsonService::fail()` 返回错误
- 使用 `JsonService::successful()` 返回成功

## 认证和权限

### JWT认证
- 基类 `Base.php` 已实现JWT验证
- 通过 `$this->userid` 获取当前用户ID
- 支持权限分级控制

## 文件上传处理

### 现有上传功能
- 支持图片上传 (头像、证件照等)
- 支持文档上传 (合同、证据等)
- 支持Excel导入导出

## 开发建议

### 1. 渐进式改造策略
1. **保持现有API不变** - 确保小程序等客户端正常运行
2. **新增扩展API** - 为新前端提供专门接口
3. **逐步迁移** - 测试稳定后替换旧接口

### 2. 优先级排序
**高优先级**:
- 用户管理API扩展 (多字段搜索、附件管理)
- 仪表板统计API
- 批量操作API

**中优先级**:
- 订单管理扩展
- 聊天功能优化

**低优先级**:
- 其他模块的细节优化

### 3. 开发计划
1. **第一阶段** (1-2周): 用户管理API扩展
2. **第二阶段** (1周): 仪表板和统计API
3. **第三阶段** (1周): 订单管理扩展
4. **第四阶段** (1周): 测试和优化

## 总结

现有后端代码结构良好，基础功能完善，约70%的API已经满足新前端需求。主要需要：

1. **扩展现有API** - 支持更丰富的搜索条件和数据返回
2. **新增批量操作** - 提高管理效率
3. **添加统计功能** - 支持仪表板展示
4. **完善附件管理** - 支持多类型文件管理

整体改造工作量适中，预计4-5周可以完成全部API适配工作。

## 具体API接口规范

### 用户管理API扩展

#### 1. 用户列表查询扩展
```php
// POST /admin/user/index
// 扩展搜索参数
$search = [
    'nickname' => '用户名称',
    'phone' => '手机号码',
    'company' => '公司名称',
    'linkman' => '联系人',
    'linkphone' => '联系号码',
    'yuangong_id' => '用户来源',
    'dateRange' => ['开始时间', '结束时间'],
    'prop' => '排序字段',
    'order' => '排序方向'
];

// 返回数据需包含债务人统计
$response = [
    'code' => 200,
    'data' => [
        [
            'id' => 1,
            'nickname' => '用户名称',
            'phone' => '手机号',
            // ... 其他字段
            'debt_count' => 3,  // 债务人数量
            'debt_total' => '150000.00', // 债务总金额
            'debt_back' => '50000.00'    // 已回款金额
        ]
    ],
    'count' => 100
];
```

#### 2. 附件管理API (新增)
```php
// POST /admin/user/attachment/upload
// 上传用户附件
public function uploadAttachment(Request $request) {
    $user_id = $request->post('user_id');
    $type = $request->post('type'); // idCard|license|other
    $file = $request->file('file');

    // 处理文件上传逻辑
    // 保存到附件表
    // 返回文件信息
}

// POST /admin/user/attachment/delete
// 删除用户附件
public function deleteAttachment(Request $request) {
    $id = $request->post('id');
    // 删除文件和数据库记录
}

// GET /admin/user/attachment/list
// 获取用户附件列表
public function getAttachments($user_id, $type = '') {
    // 返回指定用户的附件列表
}
```

#### 3. 债务人管理API (新增)
```php
// POST /admin/user/debt/add
// 添加债务人
public function addUserDebt(Request $request) {
    $data = [
        'user_id' => $request->post('user_id'),
        'name' => $request->post('name'),
        'tel' => $request->post('tel'),
        'money' => $request->post('money'),
        'status' => $request->post('status')
    ];
    // 保存到债务表
}

// POST /admin/user/debt/update
// 更新债务人
public function updateUserDebt(Request $request) {
    // 更新债务人信息
}

// POST /admin/user/debt/delete
// 删除债务人
public function deleteUserDebt(Request $request) {
    // 删除债务人记录
}
```

#### 4. 批量操作API (新增)
```php
// POST /admin/user/batchDelete
// 批量删除用户
public function batchDelete(Request $request) {
    $ids = $request->post('ids'); // [1,2,3]
    // 批量删除逻辑
}

// GET /admin/user/batchExport
// 批量导出用户
public function batchExport(Request $request) {
    $ids = $request->get('ids'); // "1,2,3" 或空(导出全部)
    // 导出Excel文件
}
```

### 仪表板API (新增)

#### 1. 统计数据API
```php
// GET /admin/dashboard/stats
public function getStats() {
    return JsonService::successful('成功', [
        'totalUsers' => Users::count(),
        'totalDebts' => Debts::count(),
        'totalOrders' => Orders::count(),
        'totalRevenue' => Orders::where('is_pay', 2)->sum('total_price'),
        'todayUsers' => Users::whereTime('create_time', 'today')->count(),
        'todayOrders' => Orders::whereTime('create_time', 'today')->count(),
        'pendingDebts' => Debts::where('status', 1)->count(),
        'processingDebts' => Debts::where('status', 2)->count()
    ]);
}
```

#### 2. 最近活动API
```php
// GET /admin/dashboard/activities
public function getActivities() {
    // 获取最近的用户注册、订单创建、债务提交等活动
    $activities = [];

    // 最近用户注册
    $recentUsers = Users::order('create_time desc')->limit(5)->select();
    foreach($recentUsers as $user) {
        $activities[] = [
            'type' => 'user_register',
            'title' => '新用户注册',
            'description' => $user->nickname . '注册了新账户',
            'time' => time_ago($user->create_time),
            'icon' => 'el-icon-user-solid',
            'color' => '#409EFF'
        ];
    }

    // 最近订单
    $recentOrders = Orders::order('create_time desc')->limit(5)->select();
    // ... 类似处理

    return JsonService::successful('成功', $activities);
}
```

### 订单管理API扩展

#### 1. 批量审核API
```php
// POST /admin/order/batchAudit
public function batchAudit(Request $request) {
    $ids = $request->post('ids');
    $status = $request->post('status');
    $status_msg = $request->post('status_msg');

    foreach($ids as $id) {
        $order = Orders::find($id);
        $order->status = $status;
        $order->status_msg = $status_msg;
        $order->save();

        // 如果审核通过，更新用户VIP状态
        if($status == 2) {
            // 更新用户会员信息
        }
    }

    return JsonService::successful('批量审核成功');
}
```

#### 2. 订单统计API
```php
// GET /admin/order/stats
public function getOrderStats() {
    return JsonService::successful('成功', [
        'total' => Orders::count(),
        'pending' => Orders::where('status', 1)->count(),
        'approved' => Orders::where('status', 2)->count(),
        'rejected' => Orders::where('status', 3)->count(),
        'totalRevenue' => Orders::where('is_pay', 2)->sum('total_price'),
        'monthlyRevenue' => Orders::where('is_pay', 2)
            ->whereTime('create_time', 'month')->sum('total_price'),
        'expiringOrders' => $this->getExpiringOrders(),
        'highValueOrders' => $this->getHighValueOrders()
    ]);
}
```

## 数据库表扩展建议

### 1. 新增附件表
```sql
CREATE TABLE `web_user_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '附件类型:idCard,license,other',
  `name` varchar(255) NOT NULL COMMENT '文件名',
  `path` varchar(500) NOT NULL COMMENT '文件路径',
  `size` int(11) DEFAULT 0 COMMENT '文件大小',
  `upload_time` int(11) NOT NULL COMMENT '上传时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户附件表';
```

### 2. 新增活动日志表
```sql
CREATE TABLE `web_activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '活动类型',
  `title` varchar(255) NOT NULL COMMENT '活动标题',
  `description` text COMMENT '活动描述',
  `user_id` int(11) DEFAULT 0 COMMENT '相关用户ID',
  `admin_id` int(11) DEFAULT 0 COMMENT '操作管理员ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动日志表';
```

## 实施步骤

### 第一阶段：用户管理API扩展 (1-2周)
1. 扩展User控制器的index方法，支持多字段搜索
2. 创建附件管理相关方法
3. 创建债务人管理相关方法
4. 添加批量操作方法
5. 创建附件表并实现相关功能

### 第二阶段：仪表板API开发 (1周)
1. 创建Dashboard控制器
2. 实现统计数据API
3. 实现活动日志API
4. 创建活动日志表
5. 完善数据统计逻辑

### 第三阶段：订单管理扩展 (1周)
1. 扩展订单控制器
2. 实现批量审核功能
3. 实现订单统计API
4. 优化订单搜索功能

### 第四阶段：测试和优化 (1周)
1. 接口测试
2. 性能优化
3. 错误处理完善
4. 文档更新

这样的改造计划既保证了现有功能的稳定性，又能满足新前端的所有需求。
