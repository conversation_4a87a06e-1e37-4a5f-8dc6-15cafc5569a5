{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=style&index=0&id=64ff9702&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748540171931}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["quanxian.vue"], "names": [], "mappings": ";AA0rCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "quanxian.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\r\n  <div class=\"permission-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-key\"></i>\r\n            权限管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增权限\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入权限名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n            \r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限类型</label>\r\n              <el-select\r\n                v-model=\"search.type\"\r\n                placeholder=\"请选择权限类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 权限树形结构 -->\r\n    <div class=\"tree-section\">\r\n      <el-card shadow=\"never\" class=\"tree-card\">\r\n        <div class=\"tree-header\">\r\n          <div class=\"tree-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            权限树形结构\r\n          </div>\r\n          <div class=\"tree-tools\">\r\n            <el-button-group>\r\n              <el-button \r\n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'tree'\"\r\n                size=\"small\"\r\n              >\r\n                树形视图\r\n              </el-button>\r\n              <el-button \r\n                :type=\"viewMode === 'table' ? 'primary' : ''\" \r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 树形视图 -->\r\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\r\n          <el-tree\r\n            :data=\"treeData\"\r\n            :props=\"treeProps\"\r\n            :default-expand-all=\"true\"\r\n            node-key=\"id\"\r\n            class=\"permission-tree\"\r\n          >\r\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\r\n              <div class=\"node-content\">\r\n                <div class=\"node-info\">\r\n                  <i :class=\"getNodeIcon(data.type)\"></i>\r\n                  <span class=\"node-label\">{{ data.label }}</span>\r\n                  <el-tag \r\n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \r\n                    size=\"mini\"\r\n                    class=\"node-status\"\r\n                  >\r\n                    {{ data.status === 1 ? '启用' : '禁用' }}\r\n                  </el-tag>\r\n                </div>\r\n                <div class=\"node-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(data.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click=\"addChild(data)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(data.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </span>\r\n          </el-tree>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"tableData\"\r\n            v-loading=\"loading\"\r\n            class=\"permission-table\"\r\n            stripe\r\n            row-key=\"id\"\r\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n            :default-expand-all=\"false\"\r\n          >\r\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\r\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\r\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  :type=\"getTypeColor(scope.row.type)\"\r\n                  size=\"small\"\r\n                >\r\n                  {{ getTypeLabel(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"addChild(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                    v-if=\"scope.row.type === 'menu'\"\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"delData(scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 编辑权限对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"60%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限名称\" prop=\"label\">\r\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限代码\" prop=\"code\">\r\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限类型\" prop=\"type\">\r\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"父级权限\">\r\n              <el-cascader\r\n                v-model=\"ruleForm.parent_id\"\r\n                :options=\"parentOptions\"\r\n                :props=\"cascaderProps\"\r\n                placeholder=\"请选择父级权限\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              ></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-switch\r\n                v-model=\"ruleForm.status\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"启用\"\r\n                inactive-text=\"禁用\"\r\n              >\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"权限描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.description\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入权限描述\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\r\n            <template slot=\"prepend\">\r\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\r\n            </template>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PermissionManagement\",\r\n  data() {\r\n    return {\r\n      viewMode: 'tree', // tree | table\r\n      loading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      },\r\n      treeData: [],\r\n      originalData: [], // 保存原始数据\r\n      dialogFormVisible: false,\r\n      dialogTitle: \"新增权限\",\r\n      parentOptions: [],\r\n      ruleForm: {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"menu\",\r\n        parent_id: null,\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      },\r\n      rules: {\r\n        label: [\r\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\r\n        ],\r\n        code: [\r\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\r\n        ],\r\n        type: [\r\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      cascaderProps: {\r\n        value: 'id',\r\n        label: 'label',\r\n        children: 'children',\r\n        checkStrictly: true\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 表格数据 - 将树形数据扁平化但保持层级关系\r\n    tableData() {\r\n      return this.flattenTreeForTable(this.treeData);\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限数据\r\n    getData() {\r\n      this.loading = true;\r\n      \r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        this.loading = false;\r\n        \r\n        const mockData = [\r\n          {\r\n            id: 1,\r\n            label: \"系统管理\",\r\n            code: \"system\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 1,\r\n            status: 1,\r\n            description: \"系统管理模块\",\r\n            path: \"/system\",\r\n            icon: \"el-icon-setting\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 11,\r\n                label: \"用户管理\",\r\n                code: \"system:user\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"用户管理功能\",\r\n                path: \"/user\",\r\n                icon: \"el-icon-user\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 111,\r\n                    label: \"查看用户\",\r\n                    code: \"system:user:view\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看用户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 112,\r\n                    label: \"新增用户\",\r\n                    code: \"system:user:add\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 113,\r\n                    label: \"编辑用户\",\r\n                    code: \"system:user:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑用户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 114,\r\n                    label: \"删除用户\",\r\n                    code: \"system:user:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 12,\r\n                label: \"职位管理\",\r\n                code: \"system:position\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"职位管理功能\",\r\n                path: \"/zhiwei\",\r\n                icon: \"el-icon-postcard\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 121,\r\n                    label: \"查看职位\",\r\n                    code: \"system:position:view\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看职位列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 122,\r\n                    label: \"新增职位\",\r\n                    code: \"system:position:add\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 123,\r\n                    label: \"编辑职位\",\r\n                    code: \"system:position:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑职位信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 124,\r\n                    label: \"删除职位\",\r\n                    code: \"system:position:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 13,\r\n                label: \"权限管理\",\r\n                code: \"system:permission\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"权限管理功能\",\r\n                path: \"/quanxian\",\r\n                icon: \"el-icon-key\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 131,\r\n                    label: \"查看权限\",\r\n                    code: \"system:permission:view\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看权限列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 132,\r\n                    label: \"新增权限\",\r\n                    code: \"system:permission:add\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 133,\r\n                    label: \"编辑权限\",\r\n                    code: \"system:permission:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑权限信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 134,\r\n                    label: \"删除权限\",\r\n                    code: \"system:permission:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            label: \"业务管理\",\r\n            code: \"business\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 2,\r\n            status: 1,\r\n            description: \"业务管理模块\",\r\n            path: \"/business\",\r\n            icon: \"el-icon-suitcase\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 21,\r\n                label: \"订单管理\",\r\n                code: \"business:order\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"订单管理功能\",\r\n                path: \"/dingdan\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 211,\r\n                    label: \"查看订单\",\r\n                    code: \"business:order:view\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看订单列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 212,\r\n                    label: \"新增订单\",\r\n                    code: \"business:order:add\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 213,\r\n                    label: \"编辑订单\",\r\n                    code: \"business:order:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑订单信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 214,\r\n                    label: \"删除订单\",\r\n                    code: \"business:order:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 215,\r\n                    label: \"导出订单\",\r\n                    code: \"business:order:export\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"导出订单数据\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 22,\r\n                label: \"客户管理\",\r\n                code: \"business:customer\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"客户管理功能\",\r\n                path: \"/customer\",\r\n                icon: \"el-icon-user-solid\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 221,\r\n                    label: \"查看客户\",\r\n                    code: \"business:customer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看客户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 222,\r\n                    label: \"新增客户\",\r\n                    code: \"business:customer:add\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 223,\r\n                    label: \"编辑客户\",\r\n                    code: \"business:customer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑客户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 224,\r\n                    label: \"删除客户\",\r\n                    code: \"business:customer:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            label: \"文书管理\",\r\n            code: \"document\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 3,\r\n            status: 1,\r\n            description: \"文书管理模块\",\r\n            path: \"/document\",\r\n            icon: \"el-icon-document-copy\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 31,\r\n                label: \"合同管理\",\r\n                code: \"document:contract\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"合同管理功能\",\r\n                path: \"/hetong\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 311,\r\n                    label: \"查看合同\",\r\n                    code: \"document:contract:view\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看合同列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 312,\r\n                    label: \"新增合同\",\r\n                    code: \"document:contract:add\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 313,\r\n                    label: \"编辑合同\",\r\n                    code: \"document:contract:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑合同信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 314,\r\n                    label: \"删除合同\",\r\n                    code: \"document:contract:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 315,\r\n                    label: \"审核合同\",\r\n                    code: \"document:contract:audit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"审核合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 32,\r\n                label: \"律师函管理\",\r\n                code: \"document:lawyer\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"律师函管理功能\",\r\n                path: \"/lawyer\",\r\n                icon: \"el-icon-message\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 321,\r\n                    label: \"查看律师函\",\r\n                    code: \"document:lawyer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看律师函列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 322,\r\n                    label: \"发送律师函\",\r\n                    code: \"document:lawyer:send\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"发送律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 323,\r\n                    label: \"编辑律师函\",\r\n                    code: \"document:lawyer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 33,\r\n                label: \"课程管理\",\r\n                code: \"document:course\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"课程管理功能\",\r\n                path: \"/kecheng\",\r\n                icon: \"el-icon-video-play\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 331,\r\n                    label: \"查看课程\",\r\n                    code: \"document:course:view\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看课程列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 332,\r\n                    label: \"新增课程\",\r\n                    code: \"document:course:add\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 333,\r\n                    label: \"编辑课程\",\r\n                    code: \"document:course:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 334,\r\n                    label: \"删除课程\",\r\n                    code: \"document:course:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            label: \"财务管理\",\r\n            code: \"finance\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 4,\r\n            status: 1,\r\n            description: \"财务管理模块\",\r\n            path: \"/finance\",\r\n            icon: \"el-icon-coin\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 41,\r\n                label: \"支付管理\",\r\n                code: \"finance:payment\",\r\n                type: \"menu\",\r\n                parent_id: 4,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"支付管理功能\",\r\n                path: \"/order\",\r\n                icon: \"el-icon-money\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 411,\r\n                    label: \"查看支付记录\",\r\n                    code: \"finance:payment:view\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看支付记录\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 412,\r\n                    label: \"处理退款\",\r\n                    code: \"finance:payment:refund\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"处理退款申请\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 413,\r\n                    label: \"导出财务报表\",\r\n                    code: \"finance:payment:export\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"导出财务报表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          }\r\n        ];\r\n        \r\n        this.treeData = mockData;\r\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\r\n        this.updateParentOptions();\r\n      }, 500);\r\n    },\r\n\r\n    // 将树形数据扁平化用于表格显示（保持层级结构）\r\n    flattenTreeForTable(tree, level = 0, result = []) {\r\n      tree.forEach(node => {\r\n        const flatNode = {\r\n          ...node,\r\n          level: level,\r\n          hasChildren: node.children && node.children.length > 0\r\n        };\r\n        // 移除children属性避免表格渲染问题\r\n        delete flatNode.children;\r\n        result.push(flatNode);\r\n\r\n        // 递归处理子节点\r\n        if (node.children && node.children.length > 0) {\r\n          this.flattenTreeForTable(node.children, level + 1, result);\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n\r\n    // 更新父级权限选项\r\n    updateParentOptions() {\r\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\r\n    },\r\n\r\n    // 构建级联选择器选项\r\n    buildCascaderOptions(tree) {\r\n      return tree.map(node => ({\r\n        id: node.id,\r\n        label: node.label,\r\n        children: node.children ? this.buildCascaderOptions(node.children) : []\r\n      }));\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 刷新页面\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n\r\n    // 编辑权限\r\n    editData(id) {\r\n      if (id === 0) {\r\n        this.dialogTitle = \"新增权限\";\r\n        this.ruleForm = {\r\n          id: null,\r\n          label: \"\",\r\n          code: \"\",\r\n          type: \"menu\",\r\n          parent_id: null,\r\n          sort: 0,\r\n          status: 1,\r\n          description: \"\",\r\n          path: \"\",\r\n          icon: \"\"\r\n        };\r\n      } else {\r\n        this.dialogTitle = \"编辑权限\";\r\n        const permission = this.findPermissionById(id);\r\n        if (permission) {\r\n          this.ruleForm = { ...permission };\r\n        }\r\n      }\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 添加子权限\r\n    addChild(parentData) {\r\n      this.dialogTitle = \"新增子权限\";\r\n      this.ruleForm = {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"action\",\r\n        parent_id: [parentData.id],\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      };\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 根据ID查找权限（在原始数据中查找）\r\n    findPermissionById(id, tree = this.originalData) {\r\n      for (let node of tree) {\r\n        if (node.id === id) {\r\n          return node;\r\n        }\r\n        if (node.children) {\r\n          const found = this.findPermissionById(id, node.children);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 删除权限\r\n    delData(id) {\r\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        // 模拟删除\r\n        this.$message.success(\"删除成功！\");\r\n        this.getData();\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n\r\n    // 保存权限\r\n    saveData() {\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存\r\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\r\n          this.dialogFormVisible = false;\r\n          this.getData();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 改变状态\r\n    changeStatus(row) {\r\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 获取节点图标\r\n    getNodeIcon(type) {\r\n      const iconMap = {\r\n        menu: 'el-icon-menu',\r\n        action: 'el-icon-setting',\r\n        data: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || 'el-icon-menu';\r\n    },\r\n\r\n    // 获取类型颜色\r\n    getTypeColor(type) {\r\n      const colorMap = {\r\n        menu: 'primary',\r\n        action: 'success',\r\n        data: 'warning'\r\n      };\r\n      return colorMap[type] || 'primary';\r\n    },\r\n\r\n    // 获取类型标签\r\n    getTypeLabel(type) {\r\n      const labelMap = {\r\n        menu: '菜单权限',\r\n        action: '操作权限',\r\n        data: '数据权限'\r\n      };\r\n      return labelMap[type] || '菜单权限';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 权限管理容器 */\r\n.permission-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 树形结构区域 */\r\n.tree-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tree-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tree-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tree-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.tree-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 树形视图样式 */\r\n.tree-view {\r\n  padding: 24px;\r\n}\r\n\r\n.permission-tree {\r\n  background: transparent;\r\n}\r\n\r\n.tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.node-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n}\r\n\r\n.node-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.node-label {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.node-status {\r\n  margin-left: 8px;\r\n}\r\n\r\n.node-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.permission-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.permission-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.permission-name {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 4px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 2px;\r\n}\r\n\r\n/* 表格行层级样式 */\r\n.permission-table .el-table__row[data-level=\"0\"] {\r\n  background-color: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"1\"] {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"2\"] {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 对话框样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .permission-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .tree-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .node-content {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n\r\n  .node-actions {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}