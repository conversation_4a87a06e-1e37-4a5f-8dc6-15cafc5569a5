var _sliceInstanceProperty = require("core-js-pure/features/instance/slice.js");
var _Object$freeze = require("core-js-pure/features/object/freeze.js");
var _Object$defineProperties = require("core-js-pure/features/object/define-properties.js");
function _taggedTemplateLiteral(e, t) {
  return t || (t = _sliceInstanceProperty(e).call(e, 0)), _Object$freeze(_Object$defineProperties(e, {
    raw: {
      value: _Object$freeze(t)
    }
  }));
}
module.exports = _taggedTemplateLiteral, module.exports.__esModule = true, module.exports["default"] = module.exports;