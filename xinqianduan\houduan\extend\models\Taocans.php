<?php
namespace models;
use think\Model;
class Taocans extends Base{
	public function setNumAttr($value){
		if(!empty($value)) return serialize($value);
		else return '';
	}
	public function getNumAttr($value){
		if(!empty($value)) return unserialize($value);
		else return [];
	}
	public function setGoodAttr($value){
		if(!empty($value)) return serialize($value);
		else return '';
	}
	public function getGoodAttr($value){
		if(!empty($value)) return unserialize($value);
		else return [];
	}
}