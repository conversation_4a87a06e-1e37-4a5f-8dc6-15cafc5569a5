# CHANGELOG

## 1.5.32 - 2022-12-08

- Support PHP versions: From 5.5 up to 8.1

## 1.5.31 - 2021-05-13

- Deprecate `\GuzzleHttp\Psr7\parse_query` method

## 1.5.30 - 2021-03-22
- Fixed incompatibility in PHP 5.6 version.

## 1.5.29 - 2020-08-03
- Fixed RPC Signature.

## 1.5.28 - 2020-08-03
- Updated `endpoints`.

## 1.5.27 - 2020-07-17
- Fixed composer error config.

## 1.5.26 - 2020-07-17
- Validate RegionID/EndpointSuffix/Network.

## 1.5.25 - 2020-07-04
- Fixed ROA signature.
- Deprecated `LogFormatter`.

## 1.5.24 - 2020-06-04
- Fixed Resolve Host.

## 1.5.23 - 2020-05-22
- Optimized global product support.

## 1.5.22 - 2020-05-12
- Updated Endpoints.

## 1.5.21 - 2020-02-26
- Improved Nonce.
- Updated Endpoints.

## 1.5.20 - 2019-12-30
- Improved Docs.
- Updated Endpoints.

## 1.5.19 - 2019-12-17
- Updated Endpoints.

## 1.5.18 - 2019-10-11
- Updated Request link.
- Updated Endpoints data.

## 1.5.17 - 2019-09-15
- Improved Host Finder.
- Updated Endpoints Data.

## 1.5.16 - 2019-08-21
- Updated Endpoints Data.

## 1.5.15 - 2019-08-14
- Improved Client.


## 1.5.14 - 2019-07-25
- Improved Credential Filter.


## 1.5.13 - 2019-07-18
- Improved API Resolver.


## 1.5.12 - 2019-06-20
- Fixed Signature for ROA.


## 1.5.11 - 2019-06-14
- Added endpoint rules.


## 1.5.10 - 2019-06-13
- Improved `Resovler`.
- Updated `endpoints`.


## 1.5.9 - 2019-06-04
- Improved `UUID`.


## 1.5.8 - 2019-05-30
- Improved `Arrays`.


## 1.5.7 - 2019-05-29
- Improved `uuid`.


## 1.5.6 - 2019-05-29
- Fixed `uuid` version lock.


## 1.5.5 - 2019-05-23
- Improved `Signature`.


## 1.5.4 - 2019-05-22
- Updated `Endpoints`.
- Fixed `Content-Type` in header.


## 1.5.3 - 2019-05-13
- Improved `Endpoint` tips.
- Improved `Endpoints` for `STS`.


## 1.5.2 - 2019-05-10
- Improved `Result` object.


## 1.5.1 - 2019-05-09
- Supported `Resolver` for Third-party dependencies.


## 1.5.0 - 2019-05-07
- Improved `Resolver` for products.


## 1.4.0 - 2019-05-06
- Support `Retry` and `Asynchronous` for Request.


## 1.3.1 - 2019-04-30
- Allow timeouts to be set in microseconds.


## 1.3.0 - 2019-04-18
- Improved parameters methods.
- Optimized the logic for body encode.


## 1.2.1 - 2019-04-11
- Improve exception code and message for `Region ID`.


## 1.2.0 - 2019-04-11
- Improve exception message for `Region ID`.


## 1.1.1 - 2019-04-02
- Added endpoints for `batchcomputenew`, `privatelink`.
- Improve Region ID tips.


## 1.1.0 - 2019-04-01
- Updated `composer.json`.


## 1.0.27 - 2019-03-31
- Support `Policy` for `ramRoleArnClient`.


## 1.0.26 - 2019-03-27
- Support `pid`, `cost`, `start_time` for Log.


## 1.0.25 - 2019-03-27
- Updated default log format.
- Add endpoints for `dbs`.


## 1.0.24 - 2019-03-26
- Support Log.


## 1.0.23 - 2019-03-23
- Remove SVG.


## 1.0.22 - 2019-03-20
- Add endpoint `cn-hangzhou` for `idaas` .


## 1.0.21 - 2019-03-19
- Installing by Using the ZIP file.
- Update Docs.


## 1.0.20 - 2019-03-13
- Improve Tests.
- Update Docs.


## 1.0.19 - 2019-03-12
- Add SSL Verify Option `verify()`.


## 1.0.18 - 2019-03-11
- Add endpoints for `acr`.
- Add endpoints for `faas`.
- Add endpoints for `ehs`.
- SSL certificates are not validated by default.


## 1.0.17 - 2019-03-08
- Support Mock for Test.


## 1.0.16 - 2019-03-07
- Support Credential Provider Chain.
- Support `CCC`.
- Add `ap-south-1` for `cas`.
- Add `ap-southeast-1` for `waf`.
- Update Docs.


## 1.0.15 - 2019-02-27
- Add endpoints for `Chatbot`.
- Change endpoints for `drdspost` and `drdspre`.


## 1.0.14 - 2019-02-21
- Enable debug mode by set environment variable `DEBUG=sdk`.


## 1.0.13 - 2019-02-18
- Support Release Script `composer release`.
- Add endpoints for apigateway in `drdspre` in `cn-qingdao`.
- Add endpoints for apigateway in `drdspre` in `cn-beijing`.
- Add endpoints for apigateway in `drdspre` in `cn-hangzhou`.
- Add endpoints for apigateway in `drdspre` in `cn-shanghai`.
- Add endpoints for apigateway in `drdspre` in `cn-shenzhen`.
- Add endpoints for apigateway in `drdspre` in `cn-hongkong`.
- Add endpoints for apigateway in `drdspost` in `ap-southeast-1`.
- Add endpoints for apigateway in `drdspost` in `cn-shanghai`.
- Add endpoints for apigateway in `drdspost` in `cn-hongkong`.
- Add endpoints for apigateway in `vod` in `ap-southeast-1`.
- Add endpoints for apigateway in `vod` in `eu-central-1`.


## 1.0.12 - 2019-02-16
- Support `open_basedir`.


## 1.0.11 - 2019-02-13
- Improve User Agent.


## 1.0.10 - 2019-02-12
- `userAgentAppend` is renamed to `appendUserAgent`.


## 1.0.9 - 2019-02-12
- `userAgent` is renamed to `userAgentAppend`.


## 1.0.8 - 2019-02-11
- `userAgent` - Support DIY User Agent.
- Add endpoints for apigateway in Zhangjiakou.
- Add endpoints for apigateway in Hu He Hao Te.
- Add endpoints for vod in Hu He Hao Te.
- Add endpoints for hsm in Zhangjiakou.
- Add endpoints for luban in Germany.
- Add endpoints for linkwan in Hangzhou.
- Add endpoints for drdspost in Singapore.


## 1.0.7 - 2019-01-28
- Add endpoints for gpdb in Tokyo.
- Add endpoints for elasticsearch in Beijing.


## 1.0.6 - 2019-01-23
- Add endpoints for dysmsapi in Singapore.
- Add endpoints for dybaseapi.
- Add endpoints for dyiotapi.
- Add endpoints for dycdpapi.
- Add endpoints for dyplsapi.
- Add endpoints for dypnsapi.
- Add endpoints for dyvmsapi.
- Add endpoints for snsuapi.


## 1.0.5 - 2019-01-21
- Add endpoints for ApiGateway in Silicon Valley, Virginia.
- Add endpoints for Image Search in Shanghai.


## 1.0.4 - 2019-01-17
- Support fixer all.
- Add Endpoints.


## 1.0.3 - 2019-01-15
- Update Endpoints.
- Update README.md.
- Update Return Result Message.


## 1.0.2 - 2019-01-15
- Optimize the documentation.
- Adjust the CI configuration.


## 1.0.1 - 2019-01-09
- Distinguish credential error.
- Add endpoints for NLS.
- Add not found product tip.


## 1.0.0 - 2019-01-07
- Initial release of the Alibaba Cloud Client for PHP Version 1.0.0 on Packagist See <https://github.com/aliyun/openapi-sdk-php-client> for more information.
