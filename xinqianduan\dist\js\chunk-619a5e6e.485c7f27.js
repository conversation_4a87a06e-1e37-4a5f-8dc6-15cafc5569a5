(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-619a5e6e"],{"13d5":function(t,e,s){"use strict";var a=s("23e7"),i=s("d58f").left,l=s("a640"),r=s("2d00"),o=s("605d"),c=!o&&r>79&&r<83,n=c||!l("reduce");a({target:"Array",proto:!0,forced:n},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},1558:function(t,e,s){},"49f6":function(t,e,s){"use strict";s("1558")},"605d":function(t,e,s){"use strict";var a=s("da84"),i=s("c6b6");t.exports="process"===i(a.process)},a640:function(t,e,s){"use strict";var a=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&a((function(){s.call(null,e||function(){return 1},1)}))}},be3e:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page-wrapper"},[e("div",{staticClass:"page-container"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-left"},[e("h2",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-service"}),t._v(" "+t._s(this.$router.currentRoute.name)+" ")]),e("div",{staticClass:"page-subtitle"},[t._v("管理法律服务产品和套餐")])]),e("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.refulsh}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-section"},[e("div",{staticClass:"stat-card"},[t._m(0),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.total))]),e("div",{staticClass:"stat-label"},[t._v("服务产品")])])]),e("div",{staticClass:"stat-card"},[t._m(1),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.averagePrice))]),e("div",{staticClass:"stat-label"},[t._v("平均价格")])])]),e("div",{staticClass:"stat-card"},[t._m(2),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.premiumServices))]),e("div",{staticClass:"stat-label"},[t._v("高端服务")])])])]),e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-controls"},[e("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入服务名称进行搜索",clearable:""},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.searchData()}},slot:"append"})],1)],1),e("div",{staticClass:"action-controls"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.editData(0)}}},[t._v(" 新增服务 ")])],1)]),e("div",{staticClass:"table-section"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"data-table",attrs:{data:t.list,stripe:""},on:{"sort-change":t.handleSortChange}},[e("el-table-column",{attrs:{prop:"title",label:"服务名称","min-width":"200","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"service-title-cell"},[e("div",{staticClass:"service-icon"},[e("i",{staticClass:"el-icon-service"})]),e("div",{staticClass:"service-info"},[e("div",{staticClass:"service-title"},[t._v(t._s(s.row.title))]),s.row.desc?e("div",{staticClass:"service-desc"},[t._v(t._s(s.row.desc))]):t._e()])])]}}])}),e("el-table-column",{attrs:{prop:"pic_path",label:"封面",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.pic_path?e("div",{staticClass:"service-cover"},[e("img",{staticClass:"cover-image",attrs:{src:s.row.pic_path,alt:s.row.title},on:{click:function(e){return t.showImage(s.row.pic_path)}}})]):e("span",{staticClass:"no-cover"},[t._v("暂无封面")])]}}])}),e("el-table-column",{attrs:{label:"价格信息","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"price-cell"},[e("div",{staticClass:"current-price"},[e("span",{staticClass:"price-label"},[t._v("现价：")]),e("span",{staticClass:"price-value"},[t._v("¥"+t._s(s.row.price))])]),s.row.shop_price?e("div",{staticClass:"market-price"},[e("span",{staticClass:"price-label"},[t._v("市场价：")]),e("span",{staticClass:"price-value original"},[t._v("¥"+t._s(s.row.shop_price))])]):t._e()])]}}])}),e("el-table-column",{attrs:{prop:"day",label:"有效期",width:"100",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"validity-cell"},[e("i",{staticClass:"el-icon-time"}),e("span",[t._v(t._s(s.row.day)+"年")])])]}}])}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-cell"},[e("i",{staticClass:"el-icon-calendar"}),e("span",[t._v(t._s(s.row.create_time))])])]}}])}),e("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{staticClass:"status-tag",attrs:{type:t.getServiceStatusType(s.row),size:"small"}},[t._v(" "+t._s(t.getServiceStatusText(s.row))+" ")])]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"action-buttons"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.editData(s.row.id)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small",icon:"el-icon-delete"},nativeOn:{click:function(e){return e.preventDefault(),t.delData(s.$index,s.row.id)}}},[t._v(" 删除 ")])],1)]}}])})],1)],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{attrs:{title:t.title+"内容",visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules}},[e("el-form-item",{attrs:{label:t.title+"标题","label-width":t.formLabelWidth,prop:"title"}},[e("el-input",{attrs:{autocomplete:"off"},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1),e("el-form-item",{attrs:{label:"有效期","label-width":t.formLabelWidth,prop:"day"}},[e("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:t.ruleForm.day,callback:function(e){t.$set(t.ruleForm,"day",e)},expression:"ruleForm.day"}},[e("template",{slot:"append"},[t._v("年")])],2)],1),e("el-form-item",{attrs:{label:"价格","label-width":t.formLabelWidth,prop:"price"}},[e("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:t.ruleForm.price,callback:function(e){t.$set(t.ruleForm,"price",e)},expression:"ruleForm.price"}})],1),e("el-form-item",{attrs:{label:"市场价格","label-width":t.formLabelWidth,prop:"shop_price"}},[e("el-input",{attrs:{autocomplete:"off"},model:{value:t.ruleForm.shop_price,callback:function(e){t.$set(t.ruleForm,"shop_price",e)},expression:"ruleForm.shop_price"}})],1),e("el-form-item",{attrs:{label:"封面","label-width":t.formLabelWidth}},[e("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:t.ruleForm.pic_path,callback:function(e){t.$set(t.ruleForm,"pic_path",e)},expression:"ruleForm.pic_path"}},[e("template",{slot:"append"},[t._v("330rpx*300rpx")])],2),e("el-button-group",[e("el-button",[e("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":t.handleSuccess,"before-upload":t.beforeUpload}},[t._v(" 上传 ")])],1),t.ruleForm.pic_path?e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.showImage(t.ruleForm.pic_path)}}},[t._v("查看 ")]):t._e(),t.ruleForm.pic_path?e("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delImage(t.ruleForm.pic_path,"pic_path")}}},[t._v("删除")]):t._e()],1)],1),e("el-form-item",{attrs:{label:"描述","label-width":t.formLabelWidth}},[e("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1),e("el-form-item",{attrs:{label:"内容","label-width":t.formLabelWidth}},[e("editor-bar",{attrs:{isClear:t.isClear},on:{change:t.change},model:{value:t.ruleForm.content,callback:function(e){t.$set(t.ruleForm,"content",e)},expression:"ruleForm.content"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("确 定")])],1)],1),e("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("el-image",{attrs:{src:t.show_image}})],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-service"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-icon active"},[e("i",{staticClass:"el-icon-money"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-icon premium"},[e("i",{staticClass:"el-icon-star-on"})])}],l=(s("13d5"),s("0c98")),r={name:"list",components:{EditorBar:l["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/server/",title:"服务",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},isClear:!1,rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],price:[{required:!0,message:"请填写价格",trigger:"blur"}],day:[{required:!0,message:"请填写有效期",trigger:"blur"}],shop_price:[{required:!0,message:"请填写市场价格",trigger:"blur"}]},formLabelWidth:"120px"}},computed:{averagePrice(){if(0===this.list.length)return"0";const t=this.list.reduce((t,e)=>t+parseFloat(e.price||0),0);return Math.round(t/this.list.length)},premiumServices(){return this.list.filter(t=>parseFloat(t.price||0)>1e3).length}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):this.ruleForm={title:"",desc:"",price:"",day:0,shop_price:"",pic_path:"",content:""},e.dialogFormVisible=!0},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{t&&(e.ruleForm=t.data)})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(t,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let t=this;t.loading=!0,t.postRequest(t.url+"index?page="+t.page+"&size="+t.size,t.search).then(e=>{e&&200==e.code?(t.list=Array.isArray(e.data)?e.data:[],t.total=e.count||0):(t.list=[],t.total=0),t.loading=!1}).catch(e=>{console.error("获取数据失败:",e),t.list=[],t.total=0,t.loading=!1})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest(t.url+"save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},handleSortChange(t){console.log("排序变化:",t)},change(t){this.ruleForm.content=t},getServiceStatusType(t){const e=parseFloat(t.price||0);return e>1e3?"success":e>500?"warning":"info"},getServiceStatusText(t){const e=parseFloat(t.price||0);return e>1e3?"高端":e>500?"标准":"基础"},handleSuccess(t){this.ruleForm.pic_path=t.data.url},showImage(t){this.show_image=t,this.dialogVisible=!0},beforeUpload(t){const e=/^image\/(jpeg|png|jpg)$/.test(t.type);e||this.$message.error("上传图片格式不对!")},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})}}},o=r,c=(s("49f6"),s("2877")),n=Object(c["a"])(o,a,i,!1,null,"530e6540",null);e["default"]=n.exports},d58f:function(t,e,s){"use strict";var a=s("59ed"),i=s("7b0b"),l=s("44ad"),r=s("07fa"),o=TypeError,c="Reduce of empty array with no initial value",n=function(t){return function(e,s,n,u){var d=i(e),p=l(d),m=r(d);if(a(s),0===m&&n<2)throw new o(c);var h=t?m-1:0,v=t?-1:1;if(n<2)while(1){if(h in p){u=p[h],h+=v;break}if(h+=v,t?h<0:m<=h)throw new o(c)}for(;t?h>=0:m>h;h+=v)h in p&&(u=s(u,p[h],h,d));return u}};t.exports={left:n(!1),right:n(!0)}}}]);
//# sourceMappingURL=chunk-619a5e6e.485c7f27.js.map