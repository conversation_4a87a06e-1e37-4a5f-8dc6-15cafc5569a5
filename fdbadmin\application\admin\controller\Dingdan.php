<?php
namespace app\admin\controller;
use models\Debts;
use models\Orders;
use models\Yuangongs;
use think\Request;
use untils\JsonService;
use models\{Dingdans,Taocans,Users};

class Dingdan extends Base
{
    protected $model;
    public function __construct(Dingdans $model){
        parent::__construct();
        $this->model=$model;

    }
    public function index(Request $request,$page=1,$size=20){
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['order_sn',"like","%".$search['keyword']."%"];
            $titleArray = Clients::where('title|name|phone','like','%'.$search['keyword'].'%')->column('id');
            if(!empty($titleArray)){
                $str = implode(',',$titleArray);
                $where[]=['client_id','in',$str];
            }
        }
        $res = $this->model::with(['taocan','client'])
        ->withAttr('pay_age',function($value,$data){
             $money=0;
            if($data['pay_type']==1){
                $money = $data['pay_price'];
            }else{
                foreach(unserialize($data['fenqi']) as $key => $v) {
                    if(!empty($v['pay_path'])){
                        $money+=$v['price']*1;
                    }
                }
            }
            return $money;
        })->withAttr('is_end',function($value,$data){

            if(!empty($data['end_time'])){
                if(strpos($data['end_time'], "-")==true){

                    $data['end_time']=strtotime($data['end_time']);
                }
                if($data['end_time']*1<time()*1){
                $count =$this->model
                ->where('end_time','>',time())
                ->where(['client_id'=>$data['client_id']])
                ->count();
                if(!empty($count)){
                    return "warning";
                }else{
                    return "danger";
                }
                }else{
                    return "";
                }
            }else{
                 return "";
            }

        })->withAttr('is_hezuo',function($value,$data){
            if(!empty($data['end_time'])){
                 if($data['end_time']*1<time()*1){
                $count =$this->model
                ->where('end_time','>',time())
                ->where(['client_id'=>$data['client_id']])
                ->count();
                if(!empty($count)){
                    return "已续费";
                }else{
                    return "已到期";
                }
            }else{
                return "已合作";
            }
            }else{
                 return "已到期";
            }

        })->withAttr('end_sort',function($value,$data){
            if(!empty($dasta['end_time'])){
                if($data['end_time']*1<time()*1){
                $count =$this->model
                ->where('end_time','>',time())
                ->where(['client_id'=>$data['client_id']])
                ->count();
                if(!empty($count)){
                    return 3;
                }else{
                    return 1;
                }
            }else{
                return 2;
            }
            }else{
                return 2;
            }

            })->withAttr('member_id', function ($value, $data) {
                if (empty($value)) {
                    return '公司订单';
                } else {
                    return Members::where(['id' => $value])->value('title');
                }
            })->withAttr('end_time', function ($v, $d) {
                return date('Y-m-d H:i:s', $v);
            })->withAttr('is_bd', function ($value, $data) {
                $res = Users::where(['client_id' => $data['client_id']])->count();
                if (!empty($res)) return true;
                else false;
            })
            ->whereOr($where)
            ->where(['is_delete' => 0])
            ->where(['member_id' => $this->userid])
            ->order(['end_time' => 'asc'])
            ->append(['pay_age', 'is_end', 'is_hezuo', 'end_sort', 'is_bd'])
            ->limit($size)
            ->page($page)
            ->select()->toArray();

        $end_sorts = array_column($res, 'end_sort');
        array_multisort($end_sorts, SORT_ASC, $res);
        $count = $this->model->where($where)->count();
        if (empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功', $res, $count);
    }

    public function index1(Request $request, $page = 1, $size = 20)
    {
        $where = [];
        $search = $request->post();
        $userid = $this->userid;//登录者id
        if (!empty($search['keyword'])) {
            $where[] = ['order_sn', "like", "%" . $search['keyword'] . "%"];
            $titleArray = Clients::where('title|name|phone', 'like', '%' . $search['keyword'] . '%')->column('id');
            if (!empty($titleArray)) {
                $str = implode(',', $titleArray);
                $where[] = ['client_id', 'in', $str];
            }
        }
        $res = $this->model::with(['taocan', 'client', 'member'])
            ->alias('t1')->leftJoin("users t2","t1.client_id = t2.id")
            ->field('t1.*')
            ->withAttr('pay_age', function ($value, $data) {
                $money = 0;
                if ($data['pay_type'] == 1) {
                    $money = $data['pay_price'];
                } else {
                    foreach (unserialize($data['fenqi']) as $key => $v) {
                        if (!empty($v['pay_path'])) {
                            $money += $v['price'] * 1;
                        }
                    }
                }
                return $money;
            })->withAttr('is_end', function ($value, $data) {
                if (!empty($data['end_time'])) {
                    if (strpos($data['end_time'], "-") == true) {
                        $data['end_time'] = strtotime($data['end_time']);
                    }
                    if ($data['end_time'] * 1 < time() * 1) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }

            })
            ->withAttr('t1.end_time', function ($v, $d) {
                return date('Y-m-d H:i:s', $v);
            })
            ->whereOr($where)
            ->where("{$userid}=1 or tiaojie_id={$userid} or fawu_id={$userid} or lian_id={$userid} or ls_id={$userid} or ywy_id={$userid} or htsczy_id={$userid} or (tiaojie_id=0 and fawu_id=0 and lian_id=0 and ls_id=0 and ywy_id=0 and htsczy_id=0)")
            ->where(['is_delete' => 0])
            ->where(['member_id' => $this->userid])
            ->order(['t1.id' => 'desc'])
            ->append(['pay_age', 'is_end'])
            ->limit($size)
            ->page($page)
            ->select();
        $count = $this->model->alias('t1')->leftJoin("users t2","t1.client_id = t2.id")->where($where)
            ->where("{$userid}=1 or tiaojie_id={$userid} or fawu_id={$userid} or lian_id={$userid} or ls_id={$userid} or ywy_id={$userid} or htsczy_id={$userid} or (tiaojie_id=0 and fawu_id=0 and lian_id=0 and ls_id=0 and ywy_id=0 and htsczy_id=0)")
            ->where(['is_delete' => 0])
            ->where(['member_id' => $this->userid])
            ->count();
        if (empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功', $res, $count);
    }

    public function save(Request $request)
    {
        if (!$request->isPost()) return JsonService::fail('非法请求2');
        $form = $request->post();
        if (empty($form)) return JsonService::fail('未接收到参数');
        if (empty($form['id'])) {
            $form['order_sn'] = get_order_num();
        }
        if (!empty($form['end_time'])) {
            $form['end_time'] = strtotime($form['end_time']);

        } else {
            if (!empty($form['taocan_type'])) {
                $day = $form['taocan_type'] == 1 ? Taocans::where(['id' => $form['taocan_id']])->value('year') : $form['taocan_year'];
                $time = 24 * 60 * 60 * $day * 365;
                $form['end_time'] = time() * 1 + $time * 1;
            }

        }
        $form['member_id'] = $this->userid;
        $res = $this->model->saveData($form);
        $errorMsg = $this->model::getErrorInfo();
        if (!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id = 0)
    {
        if (empty($id)) return JsonService::fail('未接收到参数');
        $res = $this->model::with(['taocan', 'client'])
            ->withAttr('pay_age', function ($value, $data) {
                $money = 0;
                if ($data['pay_type'] == 1) {
                    $money = $data['pay_price'];
                } else {
                    foreach (unserialize($data['fenqi']) as $key => $v) {
                        if (!empty($v['pay_path'])) {
                            $money += $v['price'] * 1;
                        }
                    }
                }
                return $money;
            })->withAttr('end_time', function ($v, $d) {
                return date('Y-m-d H:i:s', $v);
            })
            ->append(['pay_age'])
            ->find($id);
        $res->hidden(['create_time', 'update_time']);
        if(!empty($res['client_id'])){
            $where=[];
            $where[]=['uid','=',$res['client_id']];
            $debts = Debts::where($where)->withAttr('status',function($v,$d){
                switch ($v) {
                    case 1:
                        return '待处理';
                        break;
                    case 2:
                        return '调节中';
                        break;
                    case 3:
                        return '诉讼中';
                        break;
                    case 4:
                        return '已结案';
                        break;
                }
            })
                ->field('name,tel,money,status')
                ->order(['ctime'=>'desc'])
                ->select();
            $res['debts'] = $debts;
        }
        if(!empty($res['client']['tiaojie_id'])){
            $res['client']['tiaojie_id'] = Yuangongs::where('id', $res['client']['tiaojie_id'])->value('title');
        }else{
            $res['client']['tiaojie_id'] = '';
        }
        if(!empty($res['client']['fawu_id'])){
            $res['client']['fawu_id'] = Yuangongs::where('id', $res['client']['fawu_id'])->value('title');
        }else{
            $res['client']['fawu_id'] = '';
        }
        if(!empty($res['client']['lian_id'])){
            $res['client']['lian_id'] = Yuangongs::where('id', $res['client']['lian_id'])->value('title');
        }else{
            $res['client']['lian_id'] = '';
        }
        if(!empty($res['client']['ls_id'])){
            $res['client']['ls_id'] = Yuangongs::where('id', $res['client']['ls_id'])->value('title');
        }else{
            $res['client']['ls_id'] = '';
        }
        if(!empty($res['client']['ywy_id'])){
            $res['client']['ywy_id'] = Yuangongs::where('id', $res['client']['ywy_id'])->value('title');
        }else{
            $res['client']['ywy_id'] = '';
        }
        if(!empty($res['client']['htsczy_id'])){
            $res['client']['htsczy_id'] = Yuangongs::where('id', $res['client']['htsczy_id'])->value('title');
        }else{
            $res['client']['htsczy_id'] = '';
        }
        if (empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功', $res);
    }
    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $info = $this->model->where(['id'=>$id])->find();
//        if($info->status=='2'){
//            return JsonService::fail('已审核订单无法移除');
//        }
        $res = $this->model->where(['id'=>$id])->update(['is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }

    public function changeStatus(Request $request)
    {
        if (!$request->isPost()) return JsonService::fail('非法请求2');
        $form = $request->post();
        if (empty($form)) return JsonService::fail('未接收到参数');

        $info = $this->model->find($form['id']);
        if( $info->status=='2') return JsonService::fail('已审核通过,请勿重复审核');
        $info->status = $form['status'];
        $info->status_msg = $form['status_msg'];
        $info->save();

        if($form['status']=='2'){
             $year = Taocans::where(['id'=>$info['taocan_id']])->value('year');
             $start_time = time();
             $end_time =  $start_time*1 + 365*24*60*60 *$year*1;
             Users::where(['id'=>$info->client_id])->update(['year'=>$year,'start_time'=>$start_time,'end_time'=>$end_time,'vip_id'=>1]);
        }

        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function getDituList(Clients $client)
    {
        $list = $this->model->column('client_id');
        $str = implode(',', $list);
        $data = $client->field('title as name,location,id')
            ->withAttr('location', function ($value, $data) {
                return explode(',', $value);
            })->withAttr('color', function ($value, $data) {
                $end_time = $this->model->where(['client_id' => $data['id']])->value('end_time');
                if ($end_time * 1 < time() * 1) {
                    return 'red';
                } else {
                    return '#409EFF';
                }
            })
            ->where('id', 'in', $str)
            ->where('location', '<>', '')
            ->append(['color'])
            ->select();

        if (empty($data)) return JsonService::fail('失败');
        else return JsonService::successful('成功', $data);

    }

    public function updateEndTIme(Request $request)
    {
        if (!$request->isPost()) return JsonService::fail('非法请求2');
        $form = $request->post();
        if (empty($form)) return JsonService::fail('未接收到参数');

        if(empty($form['end_time']) || empty($form['id'])) return JsonService::fail('到期时间和id不能为空');
        $form['end_time'] = strtotime($form['end_time']);
        $this->model->isUpdate(true)->save($form);
        return JsonService::successful('成功');
    }

    public function free($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');

        $info = Orders::find($id);
        if(empty($info)) return JsonService::fail('数据不存在');
        if($info['is_pay'] != 1) return JsonService::fail('未支付订单才可以操作');

        $res = $info->save(['is_pay'=>2,'free_operator'=>$this->userid]);
        if(empty($res)) return JsonService::fail('修改成功');
        else return JsonService::successful('修改失败');
    }
}
