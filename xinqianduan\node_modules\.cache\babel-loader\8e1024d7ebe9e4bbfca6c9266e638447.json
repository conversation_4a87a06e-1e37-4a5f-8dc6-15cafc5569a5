{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\UserDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\UserDetail.vue", "mtime": 1748442914238}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\babel.config.js", "mtime": 1748336479385}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "id", "type", "String", "Number", "required", "data", "info", "loading", "dialogVisible", "show_image", "watch", "immediate", "handler", "newId", "console", "log", "getInfo", "methods", "_this", "setTimeout", "testUserData", "company", "phone", "nickname", "linkman", "headimg", "yuangong_id", "linkphone", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "license", "start_time", "year", "debts", "tel", "money", "status", "showImage", "imageUrl"], "sources": ["src/components/UserDetail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-descriptions title=\"客户信息\">\r\n        <el-descriptions-item label=\"公司名称\">{{\r\n          info.company\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{\r\n          info.phone\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"名称\">{{\r\n          info.nickname\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{\r\n          info.linkman\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"头像\">\r\n          <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n               :src=\"info.headimg\"\r\n               style=\"width: 50px; height: 50px;\"\r\n               @click=\"showImage(info.headimg)\"\r\n          /></el-descriptions-item>\r\n        <el-descriptions-item label=\"用户来源\">{{\r\n          info.yuangong_id\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系方式\">{{\r\n          info.linkphone\r\n          }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"调解员\">{{\r\n              info.tiaojie_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"法务专员\">{{\r\n              info.fawu_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"立案专员\">{{\r\n              info.lian_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"合同上传专用\">{{\r\n              info.htsczy_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"律师\">{{\r\n              info.ls_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务员\">{{\r\n              info.ywy_name\r\n              }}\r\n          </el-descriptions-item>\r\n        <el-descriptions-item label=\"营业执照\">\r\n          <img v-if=\"info.license !='' && info.license!=null\"\r\n               :src=\"info.license\"\r\n               style=\"width: 50px; height: 50px;\"\r\n               @click=\"showImage(info.license)\"\r\n          />\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"开始时间\">{{\r\n          info.start_time\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"会员年限\">{{\r\n          info.year\r\n          }}年</el-descriptions-item>\r\n      </el-descriptions>\r\n        <el-descriptions title=\"债务人信息\" :colon=\"false\">\r\n            <el-descriptions-item>\r\n                <el-table\r\n                        :data=\"info.debts\"\r\n                        style=\"width: 100%; margin-top: 10px\"\r\n                        v-loading=\"loading\"\r\n                        size=\"mini\"\r\n                >\r\n                    <el-table-column prop=\"name\" label=\"债务人姓名\"> </el-table-column>\r\n                    <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n                    <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n                    <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n                </el-table></el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n    </el-row>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      }\r\n    }\r\n  }\r\n</script>\r\n"], "mappings": "AA2FA;EACAA,IAAA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,IAAA;MAAA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;IACA;EACA;EACAC,KAAA;IACAV,EAAA;MACAW,SAAA;MAAA;MACAC,QAAAC,KAAA;QACA,IAAAA,KAAA,IAAAA,KAAA;UACAC,OAAA,CAAAC,GAAA,wBAAAF,KAAA;UACA,KAAAG,OAAA,CAAAH,KAAA;QACA;MACA;IACA;EACA;EACAI,OAAA;IACAD,QAAAhB,EAAA;MACA,IAAAkB,KAAA;MACAJ,OAAA,CAAAC,GAAA,iBAAAf,EAAA;MACAkB,KAAA,CAAAX,OAAA;;MAEA;MACAY,UAAA;QACA,MAAAC,YAAA;UACApB,EAAA,EAAAA,EAAA;UACAqB,OAAA;UACAC,KAAA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,YAAA;UACAC,SAAA;UACAC,SAAA;UACAC,WAAA;UACAC,OAAA;UACAC,QAAA;UACAC,OAAA;UACAC,UAAA;UACAC,IAAA;UACAC,KAAA,GACA;YACAvC,IAAA;YACAwC,GAAA;YACAC,KAAA;YACAC,MAAA;UACA,GACA;YACA1C,IAAA;YACAwC,GAAA;YACAC,KAAA;YACAC,MAAA;UACA;QAEA;QAEAtB,KAAA,CAAAZ,IAAA,GAAAc,YAAA;QACAF,KAAA,CAAAX,OAAA;QACAO,OAAA,CAAAC,GAAA,cAAAK,YAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IAEAqB,UAAAC,QAAA;MACA,KAAAjC,UAAA,GAAAiC,QAAA;MACA,KAAAlC,aAAA;IACA;EACA;AACA", "ignoreList": []}]}