{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/store/index.js", "webpack:///./src/App.vue", "webpack:///./src/App.vue?3746", "webpack:///./src/main.js", "webpack:///./src/views/Login.vue?4a7b", "webpack:///./src/utils/api.js", "webpack:///./src/views/Home.vue?65b7", "webpack:///./src/App.vue?745f", "webpack:///./src/views/Home.vue", "webpack:///src/views/Home.vue", "webpack:///./src/views/Home.vue?03e8", "webpack:///./src/views/Home.vue?86b4", "webpack:///./src/views/Login.vue", "webpack:///src/views/Login.vue", "webpack:///./src/views/Login.vue?4fe3", "webpack:///./src/views/Login.vue?b6b6", "webpack:///./src/router/index.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "jsonpScriptSrc", "p", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "code", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "error", "clearTimeout", "chunk", "errorType", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "<PERSON><PERSON>", "use", "Vuex", "Store", "state", "token", "sessionStorage", "getItem", "spbs", "title", "quanxian", "getters", "GET_TOKEN", "GET_TITLE", "GET_SPBS", "GET_QUANXIAN", "mutations", "INIT_TOKEN", "setItem", "INIT_SPBS", "INIT_QUANXIAN", "INIT_TITLE", "actions", "render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "component", "postRequest", "postKeyValueRequest", "putRequest", "deleteRequest", "getRequest", "config", "productionTip", "ElementUI", "router", "store", "h", "App", "$mount", "MOCK_MODE", "mockData", "user", "id", "role", "mockResponse", "delay", "msg", "location", "hostname", "axios", "defaults", "baseURL", "interceptors", "response", "success", "status", "Message", "replace", "base", "url", "params", "includes", "valid", "method", "transformRequest", "ret", "encodeURIComponent", "headers", "staticClass", "_v", "_s", "on", "menuClick", "_l", "menus", "item", "index", "children", "path", "slot", "child", "indexj", "$event", "logout", "currentParentMenu", "_e", "currentPageName", "dialogVisible", "show_image", "money_count", "user_count", "visit_count", "search_count", "export_count", "order_count", "gaode_count", "tengxun_count", "baidu_count", "shunqiwang_count", "computed", "$store", "currentPath", "$route", "routes", "$router", "options", "route", "menu", "watch", "to", "from", "log", "mounted", "methods", "showQrcode", "getQuanxian", "getCountAll", "commit", "$message", "_this", "directives", "rawName", "loading", "expression", "ref", "rules", "loginForm", "model", "username", "callback", "$$v", "$set", "password", "staticStyle", "nativeOn", "indexOf", "_k", "keyCode", "submitLogin", "arguments", "vcUrl", "updateVerifyCode", "checked", "required", "trigger", "<PERSON><PERSON><PERSON><PERSON>", "codes", "randomCode", "Math", "floor", "random", "btoa", "$refs", "validate", "mockResp", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "query", "redirect", "exdate", "setTime", "getTime", "cookie", "toGMTString", "arr", "split", "arr2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home", "hidden", "<PERSON><PERSON>", "meta", "requiresAuth", "beforeEach", "next"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAGnBC,EAAqB,CACxB,IAAO,GAMJjB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASa,EAAe7B,GACvB,OAAOyB,EAAoBK,EAAI,OAAS,GAAG9B,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,MAIp9B,SAASyB,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAUgC,QAGnC,IAAIC,EAASL,EAAiB5B,GAAY,CACzCK,EAAGL,EACHkC,GAAG,EACHF,QAAS,IAUV,OANAlB,EAAQd,GAAUW,KAAKsB,EAAOD,QAASC,EAAQA,EAAOD,QAASN,GAG/DO,EAAOC,GAAI,EAGJD,EAAOD,QAKfN,EAAoBS,EAAI,SAAuBlC,GAC9C,IAAImC,EAAW,GAIXC,EAAY,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,GACzlBR,EAAmB5B,GAAUmC,EAASvB,KAAKgB,EAAmB5B,IACzB,IAAhC4B,EAAmB5B,IAAkBoC,EAAUpC,IACtDmC,EAASvB,KAAKgB,EAAmB5B,GAAW,IAAIqC,SAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,QAAU,GAAGxC,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,OAC57ByC,EAAWhB,EAAoBK,EAAIU,EACnCE,EAAmBC,SAASC,qBAAqB,QAC7CxC,EAAI,EAAGA,EAAIsC,EAAiBpC,OAAQF,IAAK,CAChD,IAAIyC,EAAMH,EAAiBtC,GACvB0C,EAAWD,EAAIE,aAAa,cAAgBF,EAAIE,aAAa,QACjE,GAAe,eAAZF,EAAIG,MAAyBF,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIW,EAAoBN,SAASC,qBAAqB,SACtD,IAAQxC,EAAI,EAAGA,EAAI6C,EAAkB3C,OAAQF,IAAK,CAC7CyC,EAAMI,EAAkB7C,GACxB0C,EAAWD,EAAIE,aAAa,aAChC,GAAGD,IAAaN,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIY,EAAUP,SAASQ,cAAc,QACrCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WACfF,EAAQG,OAASf,EACjBY,EAAQI,QAAU,SAASC,GAC1B,IAAIC,EAAUD,GAASA,EAAME,QAAUF,EAAME,OAAOC,KAAOjB,EACvDkB,EAAM,IAAIC,MAAM,qBAAuB5D,EAAU,cAAgBwD,EAAU,KAC/EG,EAAIE,KAAO,wBACXF,EAAIH,QAAUA,SACP5B,EAAmB5B,GAC1BkD,EAAQY,WAAWC,YAAYb,GAC/BX,EAAOoB,IAERT,EAAQV,KAAOC,EAEf,IAAIuB,EAAOrB,SAASC,qBAAqB,QAAQ,GACjDoB,EAAKC,YAAYf,MACfgB,MAAK,WACPtC,EAAmB5B,GAAW,MAMhC,IAAImE,EAAqBxD,EAAgBX,GACzC,GAA0B,IAAvBmE,EAGF,GAAGA,EACFhC,EAASvB,KAAKuD,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAI/B,SAAQ,SAASC,EAASC,GAC3C4B,EAAqBxD,EAAgBX,GAAW,CAACsC,EAASC,MAE3DJ,EAASvB,KAAKuD,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS3B,SAASQ,cAAc,UAGpCmB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb/C,EAAoBgD,IACvBH,EAAOI,aAAa,QAASjD,EAAoBgD,IAElDH,EAAOZ,IAAM7B,EAAe7B,GAG5B,IAAI2E,EAAQ,IAAIf,MAChBS,EAAmB,SAAUd,GAE5Be,EAAOhB,QAAUgB,EAAOjB,OAAS,KACjCuB,aAAaJ,GACb,IAAIK,EAAQlE,EAAgBX,GAC5B,GAAa,IAAV6E,EAAa,CACf,GAAGA,EAAO,CACT,IAAIC,EAAYvB,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE2B,EAAUxB,GAASA,EAAME,QAAUF,EAAME,OAAOC,IACpDiB,EAAMK,QAAU,iBAAmBhF,EAAU,cAAgB8E,EAAY,KAAOC,EAAU,IAC1FJ,EAAMM,KAAO,iBACbN,EAAMvB,KAAO0B,EACbH,EAAMnB,QAAUuB,EAChBF,EAAM,GAAGF,GAEVhE,EAAgBX,QAAWkF,IAG7B,IAAIV,EAAUW,YAAW,WACxBd,EAAiB,CAAEjB,KAAM,UAAWK,OAAQa,MAC1C,MACHA,EAAOhB,QAAUgB,EAAOjB,OAASgB,EACjC1B,SAASqB,KAAKC,YAAYK,GAG5B,OAAOjC,QAAQ+C,IAAIjD,IAIpBV,EAAoB4D,EAAIxE,EAGxBY,EAAoB6D,EAAI3D,EAGxBF,EAAoB8D,EAAI,SAASxD,EAASkD,EAAMO,GAC3C/D,EAAoBgE,EAAE1D,EAASkD,IAClC1E,OAAOmF,eAAe3D,EAASkD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE/D,EAAoBoE,EAAI,SAAS9D,GACX,qBAAX+D,QAA0BA,OAAOC,aAC1CxF,OAAOmF,eAAe3D,EAAS+D,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAOmF,eAAe3D,EAAS,aAAc,CAAEiE,OAAO,KAQvDvE,EAAoBwE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQvE,EAAoBuE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAK7F,OAAO8F,OAAO,MAGvB,GAFA5E,EAAoBoE,EAAEO,GACtB7F,OAAOmF,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOvE,EAAoB8D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIR3E,EAAoB+E,EAAI,SAASxE,GAChC,IAAIwD,EAASxD,GAAUA,EAAOmE,WAC7B,WAAwB,OAAOnE,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAP,EAAoB8D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR/D,EAAoBgE,EAAI,SAASgB,EAAQC,GAAY,OAAOnG,OAAOC,UAAUC,eAAeC,KAAK+F,EAAQC,IAGzGjF,EAAoBK,EAAI,GAGxBL,EAAoBkF,GAAK,SAAShD,GAA2B,MAApBiD,QAAQjC,MAAMhB,GAAYA,GAEnE,IAAIkD,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAWjG,KAAK2F,KAAKM,GAC5CA,EAAWjG,KAAOf,EAClBgH,EAAaA,EAAWG,QACxB,IAAI,IAAI5G,EAAI,EAAGA,EAAIyG,EAAWvG,OAAQF,IAAKP,EAAqBgH,EAAWzG,IAC3E,IAAIU,EAAsBiG,EAI1B/F,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,oGC1QT,4BAGA+F,aAAIC,IAAIC,QAEO,WAAIA,OAAKC,MAAM,CAC5BC,MAAO,CACLC,MAAOR,OAAOS,eAAeC,QAAQ,UAAY,oBACpDC,KAAMX,OAAOS,eAAeC,QAAQ,QACjCE,MAAOZ,OAAOS,eAAeC,QAAQ,UAAY,WACjDG,SAAUb,OAAOS,eAAeC,QAAQ,aAAe,SAEzDI,QAAS,CACPC,UAAYR,GAAUA,EAAMC,MAC5BQ,UAAYT,GAAUA,EAAMK,MAC/BK,SAAWV,GAAUA,EAAMI,KACxBO,aAAeX,GAAUA,EAAMM,UAEjCM,UAAW,CACTC,WAAWb,EAAOC,GAChBD,EAAMC,MAAQA,EACdR,OAAOS,eAAeY,QAAQ,QAASb,IAE5Cc,UAAUf,EAAOI,GACfJ,EAAMI,KAAOA,EACbX,OAAOS,eAAeY,QAAQ,OAAQV,IAErCY,cAAchB,EAAOM,GACnBN,EAAMM,SAAWA,EACjBb,OAAOS,eAAeY,QAAQ,WAAYR,IAE5CW,WAAWjB,EAAOK,GAChBL,EAAMK,MAAQA,EACdZ,OAAOS,eAAeY,QAAQ,QAAST,KAG3Ca,QAAS,GACT1H,QAAS,M,2DCrCP2H,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,IAEjHG,EAAkB,G,wBCDlBxE,EAAS,GAMTyE,EAAY,eACdzE,EACAkE,EACAM,GACA,EACA,KACA,KACA,MAIa,EAAAC,E,iGCLf9B,aAAIzG,UAAUwI,YAAcA,OAC5B/B,aAAIzG,UAAUyI,oBAAsBA,OACpChC,aAAIzG,UAAU0I,WAAaA,OAC3BjC,aAAIzG,UAAU2I,cAAgBA,OAC9BlC,aAAIzG,UAAU4I,WAAaA,OAC3BnC,aAAIoC,OAAOC,eAAgB,EAE3BrC,aAAIC,IAAIqC,KACR,IAAItC,aAAI,CACNuC,cACAC,aACAjB,OAAQkB,GAAKA,EAAEC,KACdC,OAAO,S,oCCzBV,W,sFCAA,uOAMA,MAAMC,GAAY,EAGZC,EAAW,CACfxC,MAAO,oBACPyC,KAAM,CACJC,GAAI,EACJ/E,KAAM,MACNgF,KAAM,UAKJC,EAAeA,CAACpK,EAAMqK,EAAQ,MAC3B,IAAI9H,QAASC,IAClB6C,WAAW,KACT7C,EAAQ,CACNuB,KAAM,IACNuG,IAAK,UACLtK,KAAMA,KAEPqK,KAIP,IAAKN,EAAW,CAEO/C,OAAOuD,SAASC,SACrCC,IAAMC,SAASC,QAAU,8BAEzBF,IAAMG,aAAaC,SAASzD,IACzB0D,GACQA,EAAQ9K,KAEhB6E,IAC8B,KAAzBA,EAAMgG,SAASE,QAA0C,KAAzBlG,EAAMgG,SAASE,OACjDC,aAAQnG,MAAM,CAAEK,QAAS,kBACS,KAAzBL,EAAMgG,SAASE,OACxBC,aAAQnG,MAAM,CAAEK,QAAS,gBACS,KAAzBL,EAAMgG,SAASE,OACxBrB,OAAOuB,QAAQ,KAEXpG,EAAMgG,SAAS7K,KAAKsK,IACtBU,aAAQnG,MAAM,CAAEK,QAASL,EAAMgG,SAAS7K,KAAKsK,MAE7CU,aAAQnG,MAAM,CAAEK,QAAS,YAQnC,IAAIgG,EAAO,SAEJ,MAAM/B,EAAsBA,CAACgC,EAAKC,IACnCrB,EAEEoB,EAAIE,SAAS,qBACRjB,EAAa,CAAEkB,OAAO,IAExBlB,EAAaJ,GAGfS,IAAM,CACXc,OAAQ,OACRJ,IAAK,GAAGD,IAAOC,IACfnL,KAAMoL,EACNI,iBAAkB,CAChB,SAAUxL,GACR,IAAIyL,EAAM,GACV,IAAK,IAAInL,KAAKN,EACZyL,GACEC,mBAAmBpL,GAAK,IAAMoL,mBAAmB1L,EAAKM,IAAM,IAEhE,OAAOmL,IAGXE,QAAS,CACP,eAAgB,oCAChB,gBAAiBhC,OAAM7B,QAAQC,WAAa,MAKrCmB,EAAcA,CAACiC,EAAKC,IAC3BrB,EAEEoB,EAAIE,SAAS,qBACRjB,EAAa,CAAEkB,OAAO,IAExBlB,EAAaJ,GAGfS,IAAM,CACXc,OAAQ,OACRJ,IAAK,GAAGD,IAAOC,IACfnL,KAAMoL,EACNO,QAAS,CACP,cAAehC,OAAM7B,QAAQC,WAAa,MAKnCqB,EAAaA,CAAC+B,EAAKC,IAC1BrB,EACKK,EAAaJ,GAGfS,IAAM,CACXc,OAAQ,MACRJ,IAAK,GAAGD,IAAOC,IACfnL,KAAMoL,EACNO,QAAS,CACP,cAAehC,OAAM7B,QAAQC,WAAa,MAKnCuB,EAAaA,CAAC6B,EAAKC,IAC1BrB,EACKK,EAAaJ,GAGfS,IAAM,CACXc,OAAQ,MACRJ,IAAK,GAAGD,IAAOC,IACfC,OAAQA,EACRO,QAAS,CACP,cAAehC,OAAM7B,QAAQC,WAAa,MAKnCsB,EAAgBA,CAAC8B,EAAKC,IAC7BrB,EACKK,EAAa,CAAEU,SAAS,IAG1BL,IAAM,CACXc,OAAQ,SACRJ,IAAK,GAAGD,IAAOC,IACfC,OAAQA,EACRO,QAAS,CACP,cAAehC,OAAM7B,QAAQC,WAAa,O,oCCtJhD,W,2DCAA,W,8DCAIW,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,eAAe,CAAC+C,YAAY,QAAQ,CAAC/C,EAAG,YAAY,CAAC+C,YAAY,cAAc,CAAC/C,EAAG,MAAM,CAAC+C,YAAY,eAAe,CAAC/C,EAAG,OAAO,CAAC+C,YAAY,QAAQ,CAACjD,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIxD,WAAW0D,EAAG,MAAM,CAAC+C,YAAY,iBAAiB,CAAC/C,EAAG,UAAU,CAAC+C,YAAY,WAAW7C,MAAM,CAAC,KAAO,aAAa,mBAAmB,UAAU,aAAa,OAAO,oBAAoB,WAAWgD,GAAG,CAAC,OAASpD,EAAIqD,YAAY,CAACnD,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,MAAM,CAACJ,EAAIkD,GAAG,QAAQlD,EAAIsD,GAAItD,EAAIuD,OAAO,SAASC,EAAKC,GAAO,MAAO,CAAED,EAAKE,UAAYF,EAAKE,SAAS7L,OAAS,EAAGqI,EAAG,aAAa,CAACrC,IAAI,WAAa4F,EAAMrD,MAAM,CAAC,MAAQoD,EAAKG,KAAK,eAAe,qBAAqB,CAACzD,EAAG,WAAW,CAAC0D,KAAK,SAAS,CAAC5D,EAAIkD,GAAGlD,EAAImD,GAAGK,EAAKhH,SAASwD,EAAIsD,GAAIE,EAAKE,UAAU,SAASG,EAAMC,GAAQ,OAAO5D,EAAG,eAAe,CAACrC,IAAIiG,EAAO1D,MAAM,CAAC,MAAQyD,EAAMF,OAAO,CAAC3D,EAAIkD,GAAG,IAAIlD,EAAImD,GAAGU,EAAMrH,MAAM,WAAU,GAAG0D,EAAG,eAAe,CAACrC,IAAI,YAAc4F,EAAMrD,MAAM,CAAC,MAAQoD,EAAKE,UAAqC,IAAzBF,EAAKE,SAAS7L,OAAe2L,EAAKE,SAAS,GAAGC,KAAOH,EAAKG,OAAO,CAAC3D,EAAIkD,GAAG,IAAIlD,EAAImD,GAAGK,EAAKhH,MAAM,YAAW,IAAI,GAAG0D,EAAG,MAAM,CAAC+C,YAAY,gBAAgB,CAAC/C,EAAG,cAAc,CAACE,MAAM,CAAC,QAAU,UAAU,CAACF,EAAG,OAAO,CAAC+C,YAAY,aAAa,CAAC/C,EAAG,IAAI,CAAC+C,YAAY,iBAAiBjD,EAAIkD,GAAG,WAAWhD,EAAG,mBAAmB,CAACA,EAAG,mBAAmB,CAACA,EAAG,MAAM,CAAC+C,YAAY,gBAAgBG,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAO/D,EAAIqD,UAAU,eAAe,CAACnD,EAAG,IAAI,CAAC+C,YAAY,iBAAiBjD,EAAIkD,GAAG,cAAchD,EAAG,mBAAmB,CAACA,EAAG,MAAM,CAAC+C,YAAY,gBAAgBG,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAO/D,EAAIqD,UAAU,iBAAiB,CAACnD,EAAG,IAAI,CAAC+C,YAAY,iBAAiBjD,EAAIkD,GAAG,cAAchD,EAAG,mBAAmB,CAACE,MAAM,CAAC,QAAU,KAAK,CAACF,EAAG,MAAM,CAAC+C,YAAY,uBAAuBG,GAAG,CAAC,MAAQ,SAASW,GAAQ,OAAO/D,EAAIgE,YAAY,CAAC9D,EAAG,IAAI,CAAC+C,YAAY,0BAA0BjD,EAAIkD,GAAG,eAAe,IAAI,IAAI,KAAKhD,EAAG,eAAe,CAAC+C,YAAY,qBAAqB,CAAC/C,EAAG,YAAY,CAAC+C,YAAY,qBAAqB,CAAC/C,EAAG,gBAAgB,CAACE,MAAM,CAAC,UAAY,MAAM,CAACF,EAAG,qBAAqB,CAACE,MAAM,CAAC,GAAK,CAAEuD,KAAM,OAAQ,CAAC3D,EAAIkD,GAAG,QAASlD,EAAIiE,kBAAmB/D,EAAG,qBAAqB,CAACF,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIiE,sBAAsBjE,EAAIkE,KAAKhE,EAAG,qBAAqB,CAACF,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAImE,qBAAqB,IAAI,GAAGjE,EAAG,UAAU,CAAC+C,YAAY,gBAAgB,CAAC/C,EAAG,gBAAgB,IAAI,GAAGA,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAIoE,cAAc,MAAQ,OAAOhB,GAAG,CAAC,iBAAiB,SAASW,GAAQ/D,EAAIoE,cAAcL,KAAU,CAAC7D,EAAG,WAAW,CAACE,MAAM,CAAC,IAAMJ,EAAIqE,eAAe,IAAI,IAElgFhE,EAAkB,GC+FP,G,UAAA,CACf7D,KAAA,OACAnF,OACA,OACA+M,eAAA,EACAE,YAAA,EACAC,WAAA,EACAC,YAAA,KACAC,aAAA,EACAC,aAAA,EACAC,YAAA,EACAC,YAAA,EACAC,cAAA,EACAC,YAAA,EACAC,iBAAA,EACAV,WAAA,GACAd,MAAA,GACAf,IAAA,eAGAwC,SAAA,CACAxI,OACA,YAAAyI,OAAA9F,QAAAE,WAGA4E,oBACA,MAAAiB,EAAA,KAAAC,OAAAxB,KAGAyB,EAAA,KAAAC,QAAAC,QAAAF,OACA,QAAAG,KAAAH,EACA,GAAAG,EAAA7B,SACA,QAAAG,KAAA0B,EAAA7B,SACA,GAAAG,EAAAF,OAAAuB,EAEA,OAAAK,EAAA/I,OAAAqH,EAAArH,KACA,KAEA+I,EAAA/I,KAOA,QAAAgJ,KAAA,KAAAjC,MACA,GAAAiC,EAAA9B,SACA,QAAAG,KAAA2B,EAAA9B,SACA,GAAAG,EAAAF,OAAAuB,EAEA,OAAAM,EAAAhJ,OAAAqH,EAAArH,KACA,KAEAgJ,EAAAhJ,KAKA,aAGA2H,kBACA,YAAAgB,OAAA3I,MAAA,SAGAiJ,MAAA,CAEA,OAAAC,EAAAC,GAEAxH,QAAAyH,IAAA,QAAAD,EAAAhC,KAAA,KAAA+B,EAAA/B,QAGAkC,UAEA,KAAAtC,MAAA,CACA,CACAI,KAAA,SACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,UAAAnH,KAAA,QACA,CAAAmH,KAAA,UAAAnH,KAAA,OACA,CAAAmH,KAAA,OAAAnH,KAAA,QACA,CAAAmH,KAAA,WAAAnH,KAAA,QAGA,CACAmH,KAAA,UACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,QAAAnH,KAAA,UAGA,CACAmH,KAAA,QACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,SAAAnH,KAAA,WAGA,CACAmH,KAAA,UACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,QAAAnH,KAAA,QACA,CAAAmH,KAAA,UAAAnH,KAAA,QACA,CAAAmH,KAAA,WAAAnH,KAAA,UACA,CAAAmH,KAAA,OAAAnH,KAAA,WAGA,CACAmH,KAAA,SACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,SAAAnH,KAAA,UAGA,CACAmH,KAAA,YACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,QAAAnH,KAAA,UAGA,CACAmH,KAAA,gBACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,WAAAnH,KAAA,QACA,CAAAmH,KAAA,UAAAnH,KAAA,QACA,CAAAmH,KAAA,QAAAnH,KAAA,QACA,CAAAmH,KAAA,UAAAnH,KAAA,QACA,CAAAmH,KAAA,UAAAnH,KAAA,QACA,CAAAmH,KAAA,WAAAnH,KAAA,UAGA,CACAmH,KAAA,UACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,QAAAnH,KAAA,UAGA,CACAmH,KAAA,QACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,QAAAnH,KAAA,UAGA,CACAmH,KAAA,YACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,UAAAnH,KAAA,MACA,CAAAmH,KAAA,YAAAnH,KAAA,MACA,CAAAmH,KAAA,SAAAnH,KAAA,MACA,CAAAmH,KAAA,YAAAnH,KAAA,UAGA,CACAmH,KAAA,eACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,WAAAnH,KAAA,UAGA,CACAmH,KAAA,WACAnH,KAAA,OACAkH,SAAA,CACA,CAAAC,KAAA,gBAAAnH,KAAA,QACA,CAAAmH,KAAA,kBAAAnH,KAAA,WAKA2B,QAAAyH,IAAA,gBAAArC,QAEAuC,QAAA,CACAC,aAEA,KAAA1B,WAAA,iSACA,KAAAD,eAAA,GAEAf,UAAAI,GACA,KAAA4B,QAAAlN,KAAAsL,IAEAuC,cAEA7H,QAAAyH,IAAA,iBAEAK,cAEA9H,QAAAyH,IAAA,iBAEA5B,SACA,KAAAiB,OAAAiB,OAAA,iBACA,KAAAjB,OAAAiB,OAAA,iBACA,KAAAC,SAAA,CACAxL,KAAA,UACA4B,QAAA,SAEA,IAAA6J,EAAA,KACA1J,YAAA,WACA0J,EAAAf,QAAAlN,KAAA,YACA,UC9S6U,I,wBCQzUmI,EAAY,eACd,EACAP,EACAM,GACA,EACA,KACA,WACA,MAIa,EAAAC,E,QCnBXP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACmG,WAAW,CAAC,CAAC7J,KAAK,UAAU8J,QAAQ,YAAY/I,MAAOyC,EAAIuG,QAASC,WAAW,YAAYC,IAAI,YAAYxD,YAAY,iBAAiB7C,MAAM,CAAC,MAAQJ,EAAI0G,MAAM,uBAAuB,UAAU,0BAA0B,kBAAkB,6BAA6B,qBAAqB,MAAQ1G,EAAI2G,YAAY,CAACzG,EAAG,KAAK,CAAC+C,YAAY,cAAc,CAACjD,EAAIkD,GAAG,UAAUhD,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,aAAa,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,gBAAgB,MAAM,YAAc,UAAUwG,MAAM,CAACrJ,MAAOyC,EAAI2G,UAAUE,SAAUC,SAAS,SAAUC,GAAM/G,EAAIgH,KAAKhH,EAAI2G,UAAW,WAAYI,IAAMP,WAAW,yBAAyB,GAAGtG,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,aAAa,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,SAAS,KAAO,WAAW,gBAAgB,MAAM,YAAc,SAASwG,MAAM,CAACrJ,MAAOyC,EAAI2G,UAAUM,SAAUH,SAAS,SAAUC,GAAM/G,EAAIgH,KAAKhH,EAAI2G,UAAW,WAAYI,IAAMP,WAAW,yBAAyB,GAAGtG,EAAG,eAAe,CAACE,MAAM,CAAC,KAAO,SAAS,CAACF,EAAG,WAAW,CAACgH,YAAY,CAAC,MAAQ,SAAS9G,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,gBAAgB,MAAM,YAAc,aAAa+G,SAAS,CAAC,QAAU,SAASpD,GAAQ,OAAIA,EAAOpJ,KAAKyM,QAAQ,QAAQpH,EAAIqH,GAAGtD,EAAOuD,QAAQ,QAAQ,GAAGvD,EAAOlG,IAAI,SAAgB,KAAYmC,EAAIuH,YAAY/O,MAAM,KAAMgP,aAAaZ,MAAM,CAACrJ,MAAOyC,EAAI2G,UAAUvL,KAAM0L,SAAS,SAAUC,GAAM/G,EAAIgH,KAAKhH,EAAI2G,UAAW,OAAQI,IAAMP,WAAW,oBAAoBtG,EAAG,MAAM,CAACgH,YAAY,CAAC,OAAS,UAAU,OAAS,OAAO,MAAQ,SAAS9G,MAAM,CAAC,IAAMJ,EAAIyH,OAAOrE,GAAG,CAAC,MAAQpD,EAAI0H,qBAAqB,GAAGxH,EAAG,cAAc,CAAC+C,YAAY,gBAAgB7C,MAAM,CAAC,KAAO,UAAUwG,MAAM,CAACrJ,MAAOyC,EAAI2H,QAASb,SAAS,SAAUC,GAAM/G,EAAI2H,QAAQZ,GAAKP,WAAW,YAAY,CAACxG,EAAIkD,GAAG,UAAUhD,EAAG,YAAY,CAACgH,YAAY,CAAC,MAAQ,QAAQ9G,MAAM,CAAC,KAAO,SAAS,KAAO,WAAWgD,GAAG,CAAC,MAAQpD,EAAIuH,cAAc,CAACvH,EAAIkD,GAAG,SAAS,IAAI,IAEt7D7C,EAAkB,GC4DP,G,UAAA,CACf7D,KAAA,QACAnF,OACA,OACAkP,SAAA,EACAkB,MAAA,iNACAd,UAAA,CACAE,SAAA,QACAI,SAAA,SACA7L,KAAA,YAEAuM,SAAA,EACAjB,MAAA,CACAG,SAAA,CACA,CACAe,UAAA,EACArL,QAAA,SACAsL,QAAA,SAGAZ,SAAA,CACA,CACAW,UAAA,EACArL,QAAA,QACAsL,QAAA,SAGAzM,KAAA,CACA,CACAwM,UAAA,EACArL,QAAA,SACAsL,QAAA,YAMAhC,UACA,KAAAiC,aAEAhC,QAAA,CACA4B,mBAEA,MAAAK,EAAA,6CACAC,EAAAD,EAAAE,KAAAC,MAAAD,KAAAE,SAAAJ,EAAAlQ,SACA,KAAA4P,MAAA,6BAAAW,KAAA,qHAAAJ,kBACA,KAAArB,UAAAvL,KAAA4M,GAGAT,cACA,IAAAnB,EAAA,KACAA,EAAAiC,MAAA1B,UAAA2B,SAAA3F,IACA,IAAAA,EAkDA,SAjDAyD,EAAAG,SAAA,EAGA7J,WAAA,KAIA,GAHA0J,EAAAG,SAAA,EAGA,UAAAH,EAAAO,UAAAE,UAAA,WAAAT,EAAAO,UAAAM,SAAA,CAEA,MAAAsB,EAAA,CACAnN,KAAA,IACA/D,KAAA,CACAwH,MAAA,cAAA2J,KAAAC,MACAzJ,KAAA,YACAC,MAAA,WACAC,SAAA,UAIAkH,EAAAnB,OAAAiB,OAAA,aAAAqC,EAAAlR,KAAAwH,OACAuH,EAAAnB,OAAAiB,OAAA,YAAAqC,EAAAlR,KAAA2H,MACAoH,EAAAnB,OAAAiB,OAAA,aAAAqC,EAAAlR,KAAA4H,OACAmH,EAAAnB,OAAAiB,OAAA,gBAAAqC,EAAAlR,KAAA6H,UAEAkH,EAAAuB,SACAvB,EAAAsC,UACAtC,EAAAO,UAAAE,SACAT,EAAAO,UAAAM,UAIAb,EAAAD,SAAA,CACAxL,KAAA,UACA4B,QAAA,UAGA,IAAAoH,EAAAyC,EAAAjB,OAAAwD,MAAAC,SACAxC,EAAAf,QAAA/C,QACA,KAAAqB,QAAAlH,GAAAkH,EAAA,IAAAA,QAGAyC,EAAAD,SAAA,CACAxL,KAAA,QACA4B,QAAA,+BAEA6J,EAAAsB,oBAEA,QAMAgB,UAAA7B,EAAAI,GACA,IAAA4B,EAAA,IAAAL,KACAK,EAAAC,QAAAD,EAAAE,UAAA,MAAAF,GACAxK,OAAAnE,SAAA8O,OACA,YAAAnC,EAAA,mBAAAgC,EAAAI,cACA5K,OAAAnE,SAAA8O,OACA,YAAA/B,EAAA,mBAAA4B,EAAAI,eAEAnB,YACA,GAAA5N,SAAA8O,OAAAnR,OAAA,EAEA,IADA,IAAAqR,EAAAhP,SAAA8O,OAAAG,MAAA,MACAxR,EAAA,EAAAA,EAAAuR,EAAArR,OAAAF,IAAA,CACA,IAAAyR,EAAAF,EAAAvR,GAAAwR,MAAA,KAEA,YAAAC,EAAA,GACA,KAAAzC,UAAAE,SAAAuC,EAAA,GACA,YAAAA,EAAA,KACA,KAAAzC,UAAAM,SAAAmC,EAAA,SCzL8U,ICQ1U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,OAIa,I,0CCZf5K,aAAIC,IAAI4K,QAER,MAAMjE,EAAS,CACb,CACEzB,KAAM,IACNnH,KAAM,GACN8D,UAAWgJ,EACXC,QAAQ,EACR7F,SAAU,CACR,CACEC,KAAM,IACNnH,KAAM,KACN8D,UAAWA,IAAM,mDAIvB,CACEqD,KAAM,SACNnH,KAAM,QACN8D,UAAWkJ,EACXD,QAAQ,EACRE,KAAM,CACJC,cAAc,IAGlB,CACE/F,KAAM,SACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,UACNnH,KAAM,OACN8D,UAAWA,IAAM,sFAEnB,CACEqD,KAAM,UACNnH,KAAM,MACN8D,UAAWA,IAAM,sFAEnB,CACEqD,KAAM,OACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,WACNnH,KAAM,KACN8D,UAAWA,IAAM,wFASvB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,QACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAOnB,CACEqD,KAAM,WACNnH,KAAM,SACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,OACNnH,KAAM,QACN8D,UAAWA,IAAM,mDAIvB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,QACNnH,KAAM,KACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,SACNnH,KAAM,QACN8D,UAAWA,IAAM,wFAIvB,CACEqD,KAAM,SACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,SACNnH,KAAM,OACN8D,UAAWA,IAAM,wFAIvB,CACEqD,KAAM,YACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,QACNnH,KAAM,OACN8D,UAAWA,IAAM,mDAIvB,CACEqD,KAAM,gBACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,WACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,QACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWA,IAAM,sFAEnB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWA,IAAM,sFAEnB,CACEqD,KAAM,WACNnH,KAAM,OACN8D,UAAWA,IAAM,wFAIvB,CACEqD,KAAM,YACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,UACNnH,KAAM,KACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,YACNnH,KAAM,KACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,SACNnH,KAAM,KACN8D,UAAWA,IAAM,sFAEnB,CACEqD,KAAM,YACNnH,KAAM,OACN8D,UAAWA,IAAM,mDAIvB,CACEqD,KAAM,QACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,QACNnH,KAAM,OACN8D,UAAWA,IAAM,wFAIvB,CACEqD,KAAM,UACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,QACNnH,KAAM,OACN8D,UAAWA,IAAM,wFAIvB,CACEqD,KAAM,eACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,WACNnH,KAAM,OACN8D,UAAWA,IAAM,mDAKvB,CACEqD,KAAM,WACNnH,KAAM,OACN8D,UAAWA,IAAM,gDACjBiJ,QAAQ,GAEV,CACE5F,KAAM,aACNnH,KAAM,OACN8D,UAAWA,IAAM,gDACjBiJ,QAAQ,GAEV,CACE5F,KAAM,WACNnH,KAAM,OACN8D,UAAWgJ,EACX5F,SAAU,CACR,CACEC,KAAM,gBACNnH,KAAM,OACN8D,UAAWA,IAAM,iDAEnB,CACEqD,KAAM,kBACNnH,KAAM,OACN8D,UAAWA,IAAM,oDAMnBS,EAAS,IAAIsI,OAAU,CAC3BjE,WAEFrE,EAAO4I,WAAW,CAACjE,EAAIC,EAAMiE,KAE3B,GAAe,UAAXlE,EAAG/B,KAAkB,CAEvB,MAAM9E,EAAQmC,OAAM7B,QAAQC,UACvBP,EAKH+K,IAJAA,EAAK,CACHjG,KAAM,gBAMViG,MAGW7I,U", "file": "js/app.9aaabf68.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"app\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-0b5221ca\":\"f8376412\",\"chunk-0ffa028e\":\"214153fe\",\"chunk-100ed178\":\"f0e33fac\",\"chunk-35aba040\":\"c78b2e46\",\"chunk-3f0d8c31\":\"6a413f3e\",\"chunk-3fc9fc40\":\"5434a351\",\"chunk-40102a1f\":\"4dfafaff\",\"chunk-41971978\":\"a94e8bab\",\"chunk-461f593c\":\"d747781c\",\"chunk-462ad2b9\":\"f07bb2c8\",\"chunk-4ebce2ce\":\"6c500886\",\"chunk-5f4caf1e\":\"756f9b60\",\"chunk-5f5b3be8\":\"79a9d640\",\"chunk-67559f37\":\"13d8c247\",\"chunk-73ef59f7\":\"3fa288b6\",\"chunk-77d38154\":\"7ec4020f\",\"chunk-7963da7c\":\"21bd86b0\",\"chunk-79add12c\":\"7754989c\",\"chunk-02f4a32f\":\"96d0be89\",\"chunk-1ca8c59e\":\"f755cc21\",\"chunk-2a386260\":\"cf8493dc\",\"chunk-2bd81553\":\"c640f899\",\"chunk-2ee02d1a\":\"20ecaf89\",\"chunk-2ee5e81a\":\"46db9ebd\",\"chunk-4b21821e\":\"df0ce2eb\",\"chunk-619a5e6e\":\"485c7f27\",\"chunk-68f5b2a4\":\"a20d5dc5\",\"chunk-ad1c66d2\":\"57c13503\",\"chunk-c89f9eac\":\"df8d38e4\",\"chunk-f716f9b0\":\"5426e98d\",\"chunk-98b5b2ba\":\"2018e052\",\"chunk-a3ccb5d0\":\"5f599820\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-0b5221ca\":1,\"chunk-0ffa028e\":1,\"chunk-100ed178\":1,\"chunk-35aba040\":1,\"chunk-3f0d8c31\":1,\"chunk-3fc9fc40\":1,\"chunk-40102a1f\":1,\"chunk-41971978\":1,\"chunk-461f593c\":1,\"chunk-462ad2b9\":1,\"chunk-4ebce2ce\":1,\"chunk-5f4caf1e\":1,\"chunk-5f5b3be8\":1,\"chunk-67559f37\":1,\"chunk-73ef59f7\":1,\"chunk-77d38154\":1,\"chunk-7963da7c\":1,\"chunk-02f4a32f\":1,\"chunk-1ca8c59e\":1,\"chunk-2a386260\":1,\"chunk-2bd81553\":1,\"chunk-2ee02d1a\":1,\"chunk-2ee5e81a\":1,\"chunk-4b21821e\":1,\"chunk-619a5e6e\":1,\"chunk-68f5b2a4\":1,\"chunk-ad1c66d2\":1,\"chunk-c89f9eac\":1,\"chunk-f716f9b0\":1,\"chunk-98b5b2ba\":1,\"chunk-a3ccb5d0\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-0b5221ca\":\"1c81cbbe\",\"chunk-0ffa028e\":\"761947d9\",\"chunk-100ed178\":\"85e0cdd9\",\"chunk-35aba040\":\"00f79c2c\",\"chunk-3f0d8c31\":\"e594416d\",\"chunk-3fc9fc40\":\"596e452e\",\"chunk-40102a1f\":\"94277f3e\",\"chunk-41971978\":\"f58e8b37\",\"chunk-461f593c\":\"23f92990\",\"chunk-462ad2b9\":\"138fb05b\",\"chunk-4ebce2ce\":\"91d73337\",\"chunk-5f4caf1e\":\"0a81fce0\",\"chunk-5f5b3be8\":\"f39ce437\",\"chunk-67559f37\":\"9f5e573b\",\"chunk-73ef59f7\":\"ce483a3f\",\"chunk-77d38154\":\"e4e8654c\",\"chunk-7963da7c\":\"c7695355\",\"chunk-79add12c\":\"31d6cfe0\",\"chunk-02f4a32f\":\"4e2f32ad\",\"chunk-1ca8c59e\":\"8dd60ead\",\"chunk-2a386260\":\"2f028794\",\"chunk-2bd81553\":\"42a8eb32\",\"chunk-2ee02d1a\":\"861c4800\",\"chunk-2ee5e81a\":\"af5d50c5\",\"chunk-4b21821e\":\"d0361f74\",\"chunk-619a5e6e\":\"dfc925eb\",\"chunk-68f5b2a4\":\"a4666ee5\",\"chunk-ad1c66d2\":\"ba69f378\",\"chunk-c89f9eac\":\"1529e1c5\",\"chunk-f716f9b0\":\"326a22f0\",\"chunk-98b5b2ba\":\"a7aab748\",\"chunk-a3ccb5d0\":\"491acfff\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import Vue from \"vue\";\r\nimport Vuex from \"vuex\";\r\n\r\nVue.use(Vuex);\r\n\r\nexport default new Vuex.Store({\r\n  state: {\r\n    token: window.sessionStorage.getItem(\"token\") || \"demo-token-123456\",\r\n\tspbs: window.sessionStorage.getItem(\"spbs\"),\r\n    title: window.sessionStorage.getItem(\"title\") || \"法律服务管理系统\",\r\n    quanxian: window.sessionStorage.getItem(\"quanxian\") || \"admin\"\r\n  },\r\n  getters: {\r\n    GET_TOKEN: (state) => state.token,\r\n    GET_TITLE: (state) => state.title,\r\n\tGET_SPBS: (state) => state.spbs,\r\n    GET_QUANXIAN: (state) => state.quanxian,\r\n  },\r\n  mutations: {\r\n    INIT_TOKEN(state, token) {\r\n      state.token = token;\r\n      window.sessionStorage.setItem(\"token\", token);\r\n    },\r\n\tINIT_SPBS(state, spbs) {\r\n\t  state.spbs = spbs;\r\n\t  window.sessionStorage.setItem(\"spbs\", spbs);\r\n\t},\r\n    INIT_QUANXIAN(state, quanxian) {\r\n      state.quanxian = quanxian;\r\n      window.sessionStorage.setItem(\"quanxian\", quanxian);\r\n    },\r\n    INIT_TITLE(state, title) {\r\n      state.title = title;\r\n      window.sessionStorage.setItem(\"title\", title);\r\n    },\r\n  },\r\n  actions: {},\r\n  modules: {},\r\n});\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=1dfd14a7\"\nvar script = {}\nimport style0 from \"./App.vue?vue&type=style&index=0&id=1dfd14a7&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport ElementUI from 'element-ui';\r\nimport 'element-ui/lib/theme-chalk/index.css';\r\nimport './assets/css/common-ui.css';\r\nimport './assets/css/mobile.css';\r\nimport {postRequest} from \"./utils/api\";\r\nimport {postKeyValueRequest} from \"./utils/api\";\r\nimport {putRequest} from \"./utils/api\";\r\nimport {deleteRequest} from \"./utils/api\";\r\nimport {getRequest} from \"./utils/api\";\r\nVue.prototype.postRequest = postRequest;\r\nVue.prototype.postKeyValueRequest = postKeyValueRequest;\r\nVue.prototype.putRequest = putRequest;\r\nVue.prototype.deleteRequest = deleteRequest;\r\nVue.prototype.getRequest = getRequest;\r\nVue.config.productionTip = false\r\n\r\nVue.use(ElementUI);\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=style&index=0&id=69ca2c0c&prod&lang=css\"", "import axios from \"axios\";\r\nimport { Message } from \"element-ui\";\r\nimport router from \"../router\";\r\nimport store from \"../store\";\r\n\r\n// 纯前端模式 - 模拟API响应\r\nconst MOCK_MODE = false;\r\n\r\n// 模拟数据\r\nconst mockData = {\r\n  token: \"mock-token-123456\",\r\n  user: {\r\n    id: 1,\r\n    name: \"管理员\",\r\n    role: \"admin\"\r\n  }\r\n};\r\n\r\n// 模拟API响应函数\r\nconst mockResponse = (data, delay = 300) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve({\r\n        code: 200,\r\n        msg: \"success\",\r\n        data: data\r\n      });\r\n    }, delay);\r\n  });\r\n};\r\n\r\nif (!MOCK_MODE) {\r\n  // 根据当前环境自动设置API地址\r\n  const isProduction = window.location.hostname !== 'localhost';\r\n  axios.defaults.baseURL = \"http://*************/public\";\r\n\r\n  axios.interceptors.response.use(\r\n    (success) => {\r\n      return success.data;\r\n    },\r\n    (error) => {\r\n      if (error.response.status == 504 || error.response.status == 404) {\r\n        Message.error({ message: \"服务器被吃了( ╯□╰ )\" });\r\n      } else if (error.response.status == 403) {\r\n        Message.error({ message: \"权限不足，请联系管理员\" });\r\n      } else if (error.response.status == 401) {\r\n        router.replace(\"/\");\r\n      } else {\r\n        if (error.response.data.msg) {\r\n          Message.error({ message: error.response.data.msg });\r\n        } else {\r\n          Message.error({ message: \"未知错误!\" });\r\n        }\r\n      }\r\n      return;\r\n    }\r\n  );\r\n}\r\n\r\nlet base = \"/admin\";\r\n\r\nexport const postKeyValueRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    // 模拟不同的API响应\r\n    if (url.includes('/Login/checkToken')) {\r\n      return mockResponse({ valid: true });\r\n    }\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"post\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    transformRequest: [\r\n      function (data) {\r\n        let ret = \"\";\r\n        for (let i in data) {\r\n          ret +=\r\n            encodeURIComponent(i) + \"=\" + encodeURIComponent(data[i]) + \"&\";\r\n        }\r\n        return ret;\r\n      },\r\n    ],\r\n    headers: {\r\n      \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n      \"Request-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const postRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    // 模拟不同的API响应\r\n    if (url.includes('/Login/checkToken')) {\r\n      return mockResponse({ valid: true });\r\n    }\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"post\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const putRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"put\",\r\n    url: `${base}${url}`,\r\n    data: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const getRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse(mockData);\r\n  }\r\n\r\n  return axios({\r\n    method: \"get\",\r\n    url: `${base}${url}`,\r\n    params: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n\r\nexport const deleteRequest = (url, params) => {\r\n  if (MOCK_MODE) {\r\n    return mockResponse({ success: true });\r\n  }\r\n\r\n  return axios({\r\n    method: \"delete\",\r\n    url: `${base}${url}`,\r\n    params: params,\r\n    headers: {\r\n      \"Login-Token\": store.getters.GET_TOKEN || \"\",\r\n    },\r\n  });\r\n};\r\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&id=0593784a&prod&scoped=true&lang=css\"", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=1dfd14a7&prod&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-container',{staticClass:\"cont\"},[_c('el-header',{staticClass:\"top-header\"},[_c('div',{staticClass:\"header-left\"},[_c('span',{staticClass:\"logo\"},[_vm._v(_vm._s(_vm.name))])]),_c('div',{staticClass:\"header-center\"},[_c('el-menu',{staticClass:\"top-menu\",attrs:{\"mode\":\"horizontal\",\"background-color\":\"#001529\",\"text-color\":\"#fff\",\"active-text-color\":\"#ffd04b\"},on:{\"select\":_vm.menuClick}},[_c('el-menu-item',{attrs:{\"index\":\"/\"}},[_vm._v(\"首页\")]),_vm._l((_vm.menus),function(item,index){return [(item.children && item.children.length > 1)?_c('el-submenu',{key:'submenu-' + index,attrs:{\"index\":item.path,\"popper-class\":\"vertical-submenu\"}},[_c('template',{slot:\"title\"},[_vm._v(_vm._s(item.name))]),_vm._l((item.children),function(child,indexj){return _c('el-menu-item',{key:indexj,attrs:{\"index\":child.path}},[_vm._v(\" \"+_vm._s(child.name)+\" \")])})],2):_c('el-menu-item',{key:'menuitem-' + index,attrs:{\"index\":item.children && item.children.length === 1 ? item.children[0].path : item.path}},[_vm._v(\" \"+_vm._s(item.name)+\" \")])]})],2)],1),_c('div',{staticClass:\"header-right\"},[_c('el-dropdown',{attrs:{\"trigger\":\"click\"}},[_c('span',{staticClass:\"user-info\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 管理员 \")]),_c('el-dropdown-menu',[_c('el-dropdown-item',[_c('div',{staticClass:\"dropdown-item\",on:{\"click\":function($event){return _vm.menuClick('/profile')}}},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 个人信息 \")])]),_c('el-dropdown-item',[_c('div',{staticClass:\"dropdown-item\",on:{\"click\":function($event){return _vm.menuClick('/changePwd')}}},[_c('i',{staticClass:\"el-icon-lock\"}),_vm._v(\" 修改密码 \")])]),_c('el-dropdown-item',{attrs:{\"divided\":\"\"}},[_c('div',{staticClass:\"dropdown-item logout\",on:{\"click\":function($event){return _vm.logout()}}},[_c('i',{staticClass:\"el-icon-switch-button\"}),_vm._v(\" 退出登录 \")])])],1)],1)],1)]),_c('el-container',{staticClass:\"content-container\"},[_c('el-header',{staticClass:\"breadcrumb-header\"},[_c('el-breadcrumb',{attrs:{\"separator\":\"/\"}},[_c('el-breadcrumb-item',{attrs:{\"to\":{ path: '/' }}},[_vm._v(\"首页\")]),(_vm.currentParentMenu)?_c('el-breadcrumb-item',[_vm._v(_vm._s(_vm.currentParentMenu))]):_vm._e(),_c('el-breadcrumb-item',[_vm._v(_vm._s(_vm.currentPageName))])],1)],1),_c('el-main',{staticClass:\"main-content\"},[_c('router-view')],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"25%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <!-- 按顺序渲染菜单项 -->\r\n          <template v-for=\"(item, index) in menus\">\r\n            <!-- 如果有多个子菜单，显示为下拉菜单 -->\r\n            <el-submenu\r\n              v-if=\"item.children && item.children.length > 1\"\r\n              :key=\"'submenu-' + index\"\r\n              :index=\"item.path\"\r\n              popper-class=\"vertical-submenu\"\r\n            >\r\n              <template slot=\"title\">{{ item.name }}</template>\r\n              <el-menu-item\r\n                v-for=\"(child, indexj) in item.children\"\r\n                :key=\"indexj\"\r\n                :index=\"child.path\"\r\n              >\r\n                {{ child.name }}\r\n              </el-menu-item>\r\n            </el-submenu>\r\n            <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\r\n            <el-menu-item\r\n              v-else\r\n              :key=\"'menuitem-' + index\"\r\n              :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\r\n            >\r\n              {{ item.name }}\r\n            </el-menu-item>\r\n          </template>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">\r\n            <i class=\"el-icon-user\"></i>\r\n            管理员\r\n          </span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item>\r\n              <div @click=\"menuClick('/profile')\" class=\"dropdown-item\">\r\n                <i class=\"el-icon-user\"></i>\r\n                个人信息\r\n              </div>\r\n            </el-dropdown-item>\r\n            <el-dropdown-item>\r\n              <div @click=\"menuClick('/changePwd')\" class=\"dropdown-item\">\r\n                <i class=\"el-icon-lock\"></i>\r\n                修改密码\r\n              </div>\r\n            </el-dropdown-item>\r\n            <el-dropdown-item divided>\r\n              <div @click=\"logout()\" class=\"dropdown-item logout\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                退出登录\r\n              </div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item v-if=\"currentParentMenu\">{{ currentParentMenu }}</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{ currentPageName }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n    // 获取当前页面的父级菜单名称\r\n    currentParentMenu() {\r\n      const currentPath = this.$route.path;\r\n\r\n      // 先从路由配置中查找父级路由\r\n      const routes = this.$router.options.routes;\r\n      for (let route of routes) {\r\n        if (route.children) {\r\n          for (let child of route.children) {\r\n            if (child.path === currentPath) {\r\n              // 特殊处理：如果父路由名称和子路由名称相同，说明这是一个独立的主菜单项\r\n              if (route.name === child.name) {\r\n                return null; // 不显示父级菜单，因为它们是同一个\r\n              }\r\n              return route.name;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 如果路由配置中没找到，再从菜单数据中查找\r\n      for (let menu of this.menus) {\r\n        if (menu.children) {\r\n          for (let child of menu.children) {\r\n            if (child.path === currentPath) {\r\n              // 同样的特殊处理\r\n              if (menu.name === child.name) {\r\n                return null;\r\n              }\r\n              return menu.name;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n    // 获取当前页面名称\r\n    currentPageName() {\r\n      return this.$route.name || '未知页面';\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听路由变化，确保面包屑及时更新\r\n    '$route'(to, from) {\r\n      // 路由变化时，计算属性会自动重新计算\r\n      console.log('路由变化:', from.path, '->', to.path);\r\n    }\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/debt\",\r\n        name: \"债权管理\",\r\n        children: [\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/zhifu\",\r\n        name: \"支付列表\",\r\n        children: [\r\n          { path: \"/order\", name: \"支付列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/liaotian\",\r\n        name: \"聊天列表\",\r\n        children: [\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" },\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" },\r\n          { path: \"/lvshi\", name: \"律师\" },\r\n          { path: \"/quanxian\", name: \"权限管理\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"专业管理\",\r\n        children: [\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/archive\",\r\n        name: \"归档管理\",\r\n        children: [\r\n          { path: \"/archive/file\", name: \"文件归档\" },\r\n          { path: \"/archive/search\", name: \"档案检索\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n  min-width: 1400px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 140px;\r\n  min-width: 140px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n  min-width: 0;\r\n  max-width: calc(100% - 300px);\r\n  margin-right: 20px;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  flex-wrap: nowrap !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n  flex-wrap: nowrap !important;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 8px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n  font-size: 13px;\r\n  min-width: auto;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 8px;\r\n  border-bottom: none !important;\r\n  font-size: 13px;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-submenu__drop-down .el-menu .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* 强制下拉菜单容器为垂直布局 */\r\n.el-submenu__drop-down,\r\n.el-menu--horizontal .el-submenu__drop-down,\r\n.el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 确保子菜单内的ul也是垂直的 */\r\n.el-submenu__drop-down ul,\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  list-style: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制覆盖任何可能的inline样式 */\r\n.el-submenu__drop-down .el-menu-item[style] {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  position: relative !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24px;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb {\r\n  font-size: 14px;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item {\r\n  color: #666;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item:last-child .el-breadcrumb__inner {\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item .el-breadcrumb__inner.is-link {\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.breadcrumb-header .el-breadcrumb__item .el-breadcrumb__inner.is-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 使用更高的CSS优先级 */\r\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对自定义popper-class的样式 - 美化版本 */\r\n.vertical-submenu {\r\n  display: block !important;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\r\n  overflow: hidden !important;\r\n  min-width: 180px !important;\r\n  padding: 8px 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 44px !important;\r\n  line-height: 44px !important;\r\n  padding: 0 20px !important;\r\n  margin: 2px 8px !important;\r\n  background-color: rgba(255, 255, 255, 0.95) !important;\r\n  color: #2c3e50 !important;\r\n  text-align: left !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  font-weight: 500 !important;\r\n  font-size: 14px !important;\r\n  transition: all 0.3s ease !important;\r\n  position: relative !important;\r\n  width: calc(100% - 16px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  color: #fff !important;\r\n  transform: translateX(4px) !important;\r\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:active {\r\n  transform: translateX(2px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:last-child {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n/* 添加一些动画效果 */\r\n.vertical-submenu .el-menu-item::before {\r\n  content: '' !important;\r\n  position: absolute !important;\r\n  left: 0 !important;\r\n  top: 50% !important;\r\n  transform: translateY(-50%) !important;\r\n  width: 3px !important;\r\n  height: 0 !important;\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border-radius: 0 2px 2px 0 !important;\r\n  transition: height 0.3s ease !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover::before {\r\n  height: 20px !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 100px;\r\n  min-width: 100px;\r\n  text-align: right;\r\n  position: relative;\r\n  z-index: 1001;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 10px;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  transition: background-color 0.3s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 0;\r\n  color: #333;\r\n  transition: color 0.3s;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.dropdown-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.dropdown-item i {\r\n  font-size: 14px;\r\n  width: 16px;\r\n}\r\n\r\n.dropdown-item.logout {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.dropdown-item.logout:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 下拉菜单容器样式 */\r\n.el-dropdown-menu {\r\n  min-width: 140px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item {\r\n  padding: 0 16px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 主内容区域样式 - 新UI风格 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f5f5f5;\r\n  padding: 16px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 页面内容容器 */\r\n.main-content .page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 标签页导航样式 */\r\n.tab-navigation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tab-navigation .el-tabs__header {\r\n  margin: 0;\r\n}\r\n\r\n.tab-navigation .el-tabs__nav-wrap::after {\r\n  height: 1px;\r\n  background-color: #e8e8e8;\r\n}\r\n\r\n/* 搜索筛选区域样式 */\r\n.search-section {\r\n  background: #fafafa;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-left: auto;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table {\r\n  margin-top: 16px;\r\n}\r\n\r\n.data-table .el-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n}\r\n\r\n.data-table .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.data-table .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding: 16px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .cont {\r\n    height: 100vh;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .top-header {\r\n    height: 50px;\r\n    padding: 0 10px;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .header-left {\r\n    flex: 0 0 auto;\r\n    min-width: auto;\r\n  }\r\n\r\n  .logo {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .header-center {\r\n    flex: 1;\r\n    margin: 0 10px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .top-menu {\r\n    overflow-x: auto;\r\n    overflow-y: hidden;\r\n    white-space: nowrap;\r\n    scrollbar-width: none;\r\n    -ms-overflow-style: none;\r\n  }\r\n\r\n  .top-menu::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n\r\n  .top-menu >>> .el-menu-item,\r\n  .top-menu >>> .el-submenu {\r\n    padding: 0 10px;\r\n    font-size: 14px;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .header-right {\r\n    flex: 0 0 auto;\r\n    min-width: auto;\r\n  }\r\n\r\n  .user-info {\r\n    padding: 0 8px;\r\n    font-size: 14px;\r\n    height: 50px;\r\n    line-height: 50px;\r\n  }\r\n\r\n  .breadcrumb-header {\r\n    height: 40px;\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .breadcrumb-header .el-breadcrumb {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .main-content {\r\n    height: calc(100vh - 90px);\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  /* 移动端下拉菜单优化 */\r\n  .vertical-submenu {\r\n    min-width: 150px !important;\r\n    padding: 5px 0 !important;\r\n  }\r\n\r\n  .vertical-submenu .el-menu-item {\r\n    height: 40px !important;\r\n    line-height: 40px !important;\r\n    padding: 0 15px !important;\r\n    margin: 1px 5px !important;\r\n    font-size: 13px !important;\r\n    width: calc(100% - 10px) !important;\r\n  }\r\n\r\n  /* 搜索表单移动端适配 */\r\n  .search-form {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .action-buttons {\r\n    width: 100%;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  /* 表格移动端适配 */\r\n  .data-table .el-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .data-table .el-table th,\r\n  .data-table .el-table td {\r\n    padding: 8px 5px;\r\n  }\r\n\r\n  /* 分页移动端适配 */\r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .el-pagination {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-pagination .el-pager li {\r\n    min-width: 28px;\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n\r\n  .el-pagination .btn-prev,\r\n  .el-pagination .btn-next {\r\n    min-width: 28px;\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n\r\n/* 超小屏幕适配 (小于480px) */\r\n@media (max-width: 480px) {\r\n  .top-header {\r\n    height: 45px;\r\n    padding: 0 8px;\r\n  }\r\n\r\n  .logo {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .header-center {\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .top-menu >>> .el-menu-item,\r\n  .top-menu >>> .el-submenu {\r\n    padding: 0 8px;\r\n    font-size: 12px;\r\n    height: 45px;\r\n    line-height: 45px;\r\n    min-width: 70px;\r\n  }\r\n\r\n  .user-info {\r\n    padding: 0 5px;\r\n    font-size: 12px;\r\n    height: 45px;\r\n    line-height: 45px;\r\n  }\r\n\r\n  .breadcrumb-header {\r\n    height: 35px;\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .breadcrumb-header .el-breadcrumb {\r\n    font-size: 11px;\r\n  }\r\n\r\n  .main-content {\r\n    height: calc(100vh - 80px);\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 16px;\r\n    margin-bottom: 12px;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .vertical-submenu {\r\n    min-width: 130px !important;\r\n  }\r\n\r\n  .vertical-submenu .el-menu-item {\r\n    height: 36px !important;\r\n    line-height: 36px !important;\r\n    padding: 0 12px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .data-table .el-table {\r\n    font-size: 11px;\r\n  }\r\n\r\n  .data-table .el-table th,\r\n  .data-table .el-table td {\r\n    padding: 6px 3px;\r\n  }\r\n\r\n  .el-pagination {\r\n    font-size: 11px;\r\n  }\r\n\r\n  .el-pagination .el-pager li {\r\n    min-width: 24px;\r\n    height: 24px;\r\n    line-height: 24px;\r\n    font-size: 11px;\r\n  }\r\n\r\n  .el-pagination .btn-prev,\r\n  .el-pagination .btn-next {\r\n    min-width: 24px;\r\n    height: 24px;\r\n    line-height: 24px;\r\n    font-size: 11px;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n\r\n/* 用户信息和下拉菜单样式 */\r\n.header-right {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  transition: background-color 0.3s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.dropdown-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 0;\r\n  color: #333;\r\n  transition: color 0.3s;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.dropdown-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.dropdown-item i {\r\n  font-size: 14px;\r\n  width: 16px;\r\n}\r\n\r\n.dropdown-item.logout {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.dropdown-item.logout:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 下拉菜单容器样式 */\r\n.el-dropdown-menu {\r\n  min-width: 140px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item {\r\n  padding: 0 16px;\r\n}\r\n\r\n.el-dropdown-menu .el-dropdown-menu__item:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Home.vue?vue&type=template&id=0593784a&scoped=true\"\nimport script from \"./Home.vue?vue&type=script&lang=js\"\nexport * from \"./Home.vue?vue&type=script&lang=js\"\nimport style0 from \"./Home.vue?vue&type=style&index=0&id=0593784a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0593784a\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-form',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"loginForm\",staticClass:\"loginContainer\",attrs:{\"rules\":_vm.rules,\"element-loading-text\":\"正在登录...\",\"element-loading-spinner\":\"el-icon-loading\",\"element-loading-background\":\"rgba(0, 0, 0, 0.8)\",\"model\":_vm.loginForm}},[_c('h3',{staticClass:\"loginTitle\"},[_vm._v(\"系统登录\")]),_c('el-form-item',{attrs:{\"prop\":\"username\"}},[_c('el-input',{attrs:{\"size\":\"normal\",\"type\":\"text\",\"auto-complete\":\"off\",\"placeholder\":\"请输入用户名\"},model:{value:(_vm.loginForm.username),callback:function ($$v) {_vm.$set(_vm.loginForm, \"username\", $$v)},expression:\"loginForm.username\"}})],1),_c('el-form-item',{attrs:{\"prop\":\"password\"}},[_c('el-input',{attrs:{\"size\":\"normal\",\"type\":\"password\",\"auto-complete\":\"off\",\"placeholder\":\"请输入密码\"},model:{value:(_vm.loginForm.password),callback:function ($$v) {_vm.$set(_vm.loginForm, \"password\", $$v)},expression:\"loginForm.password\"}})],1),_c('el-form-item',{attrs:{\"prop\":\"code\"}},[_c('el-input',{staticStyle:{\"width\":\"250px\"},attrs:{\"size\":\"normal\",\"type\":\"text\",\"auto-complete\":\"off\",\"placeholder\":\"点击图片更换验证码\"},nativeOn:{\"keydown\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.submitLogin.apply(null, arguments)}},model:{value:(_vm.loginForm.code),callback:function ($$v) {_vm.$set(_vm.loginForm, \"code\", $$v)},expression:\"loginForm.code\"}}),_c('img',{staticStyle:{\"cursor\":\"pointer\",\"height\":\"40px\",\"width\":\"200px\"},attrs:{\"src\":_vm.vcUrl},on:{\"click\":_vm.updateVerifyCode}})],1),_c('el-checkbox',{staticClass:\"loginRemember\",attrs:{\"size\":\"normal\"},model:{value:(_vm.checked),callback:function ($$v) {_vm.checked=$$v},expression:\"checked\"}},[_vm._v(\"记住密码\")]),_c('el-button',{staticStyle:{\"width\":\"100%\"},attrs:{\"size\":\"normal\",\"type\":\"primary\"},on:{\"click\":_vm.submitLogin}},[_vm._v(\"登录\")])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <el-form\r\n      :rules=\"rules\"\r\n      ref=\"loginForm\"\r\n      v-loading=\"loading\"\r\n      element-loading-text=\"正在登录...\"\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-background=\"rgba(0, 0, 0, 0.8)\"\r\n      :model=\"loginForm\"\r\n      class=\"loginContainer\"\r\n    >\r\n      <h3 class=\"loginTitle\">系统登录</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入用户名\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"password\"\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入密码\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"点击图片更换验证码\"\r\n          @keydown.enter.native=\"submitLogin\"\r\n          style=\"width: 250px\"\r\n        ></el-input>\r\n        <img\r\n          :src=\"vcUrl\"\r\n          @click=\"updateVerifyCode\"\r\n          style=\"cursor: pointer; height: 40px; width: 200px\"\r\n        />\r\n      </el-form-item>\r\n      <el-checkbox size=\"normal\" class=\"loginRemember\" v-model=\"checked\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-button\r\n        size=\"normal\"\r\n        type=\"primary\"\r\n        style=\"width: 100%\"\r\n        @click=\"submitLogin\"\r\n        >登录</el-button\r\n      >\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      vcUrl: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjQwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjEwIiB5PSIyNSIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzMzMzMzMyI+REVNT0NPREU8L3RleHQ+PC9zdmc+\", // 演示验证码\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"123456\",\r\n        code: \"DEMOCODE\",\r\n      },\r\n      checked: true,\r\n      rules: {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入用户名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"请输入密码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [\r\n          {\r\n            required: true,\r\n            message: \"请输入验证码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    updateVerifyCode() {\r\n      // 纯前端模式 - 生成新的演示验证码\r\n      const codes = ['DEMOCODE', 'TESTCODE', 'FRONTEND', 'MOCKAPI'];\r\n      const randomCode = codes[Math.floor(Math.random() * codes.length)];\r\n      this.vcUrl = `data:image/svg+xml;base64,${btoa(`<svg width=\"200\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\"><text x=\"10\" y=\"25\" font-size=\"16\" fill=\"#333333\">${randomCode}</text></svg>`)}`;\r\n      this.loginForm.code = randomCode;\r\n    },\r\n\r\n    submitLogin() {\r\n      let _this = this;\r\n      _this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          _this.loading = true;\r\n\r\n          // 纯前端模式 - 模拟登录验证\r\n          setTimeout(() => {\r\n            _this.loading = false;\r\n\r\n            // 简单的演示登录验证\r\n            if (_this.loginForm.username === 'admin' && _this.loginForm.password === '123456') {\r\n              // 模拟成功响应\r\n              const mockResp = {\r\n                code: 200,\r\n                data: {\r\n                  token: \"demo-token-\" + Date.now(),\r\n                  spbs: \"demo-spbs\",\r\n                  title: \"法律服务管理系统\",\r\n                  quanxian: \"admin\"\r\n                }\r\n              };\r\n\r\n              _this.$store.commit(\"INIT_TOKEN\", mockResp.data.token);\r\n              _this.$store.commit(\"INIT_SPBS\", mockResp.data.spbs);\r\n              _this.$store.commit(\"INIT_TITLE\", mockResp.data.title);\r\n              _this.$store.commit(\"INIT_QUANXIAN\", mockResp.data.quanxian);\r\n\r\n              if (_this.checked) {\r\n                _this.setCookie(\r\n                  _this.loginForm.username,\r\n                  _this.loginForm.password\r\n                );\r\n              }\r\n\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: \"登录成功！\"\r\n              });\r\n\r\n              let path = _this.$route.query.redirect;\r\n              _this.$router.replace(\r\n                path == \"/\" || path == undefined ? \"/\" : path\r\n              );\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: \"用户名或密码错误！演示账号：admin/123456\"\r\n              });\r\n              _this.updateVerifyCode();\r\n            }\r\n          }, 1000); // 模拟网络延迟\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    setCookie(username, password) {\r\n      var exdate = new Date(); //获取时间\r\n      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdate); //保存的天数\r\n      window.document.cookie =\r\n        \"username\" + \"=\" + username + \";path=/;expires=\" + exdate.toGMTString();\r\n      window.document.cookie =\r\n        \"password\" + \"=\" + password + \";path=/;expires=\" + exdate.toGMTString();\r\n    },\r\n    getCookie() {\r\n      if (document.cookie.length > 0) {\r\n        var arr = document.cookie.split(\"; \"); //这里显示的格式需要切割一下自己可输出看下\r\n        for (var i = 0; i < arr.length; i++) {\r\n          var arr2 = arr[i].split(\"=\"); //再次切割\r\n          //判断查找相对应的值\r\n          if (arr2[0] == \"username\") {\r\n            this.loginForm.username = arr2[1]; //保存到保存数据的地方\r\n          } else if (arr2[0] == \"password\") {\r\n            this.loginForm.password = arr2[1];\r\n          }\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.loginContainer {\r\n  border-radius: 15px;\r\n  background-clip: padding-box;\r\n  margin: 180px auto;\r\n  width: 350px;\r\n  padding: 15px 35px 15px 35px;\r\n  background: #fff;\r\n  border: 1px solid #eaeaea;\r\n  box-shadow: 0 0 25px #cac6c6;\r\n}\r\n\r\n.loginTitle {\r\n  margin: 15px auto 20px auto;\r\n  text-align: center;\r\n  color: #505458;\r\n}\r\n\r\n.loginRemember {\r\n  text-align: left;\r\n  margin: 0px 0px 15px 0px;\r\n}\r\n\r\n.el-form-item__content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Login.vue?vue&type=template&id=69ca2c0c\"\nimport script from \"./Login.vue?vue&type=script&lang=js\"\nexport * from \"./Login.vue?vue&type=script&lang=js\"\nimport style0 from \"./Login.vue?vue&type=style&index=0&id=69ca2c0c&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from \"vue\";\r\nimport VueRouter from \"vue-router\";\r\nimport Home from \"../views/Home.vue\";\r\nimport Login from \"../views/Login.vue\";\r\nimport axios from \"axios\";\r\nimport store from \"../store\";\r\nimport { Message } from \"element-ui\";\r\nVue.use(VueRouter);\r\n\r\nconst routes = [\r\n  {\r\n    path: \"/\",\r\n    name: \"\",\r\n    component: Home,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: \"/\",\r\n        name: \"首页\",\r\n        component: () => import(\"../views/pages/Dashboard.vue\"),\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: \"/login\",\r\n    name: \"Login\",\r\n    component: Login,\r\n    hidden: true,\r\n    meta: {\r\n      requiresAuth: false,\r\n    },\r\n  },\r\n  {\r\n    path: \"/jichu\",\r\n    name: \"基础管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/config\",\r\n        name: \"基础设置\",\r\n        component: () => import(\"../views/pages/data/configs.vue\"),\r\n      },\r\n      {\r\n        path: \"/banner\",\r\n        name: \"轮播图\",\r\n        component: () => import(\"../views/pages/data/banner.vue\"),\r\n      },\r\n      {\r\n        path: \"/nav\",\r\n        name: \"首页导航\",\r\n        component: () => import(\"../views/pages/data/nav.vue\"),\r\n      },\r\n      {\r\n        path: \"/gonggao\",\r\n        name: \"公告\",\r\n        component: () => import(\"../views/pages/data/gonggao.vue\"),\r\n      },\r\n      // {\r\n      //   path: \"/vip\",\r\n      //   name: \"会员\",\r\n      //   component: () => import(\"../views/pages/data/vip.vue\"),\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/xiadan\",\r\n    name: \"订单管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/type\",\r\n        name: \"服务类型\",\r\n        component: () => import(\"../views/pages/taocan/type.vue\"),\r\n      },\r\n      {\r\n        path: \"/taocan\",\r\n        name: \"套餐类型\",\r\n        component: () => import(\"../views/pages/taocan/taocan.vue\"),\r\n      },\r\n      // {\r\n      //   path: \"/yonghu\",\r\n      //   name: \"用户列表\",\r\n      //   component: () => import(\"../views/pages/taocan/user.vue\"),\r\n      // },\r\n      {\r\n        path: \"/dingdan\",\r\n        name: \"签约用户列表\",\r\n        component: () => import(\"../views/pages/taocan/dingdan.vue\"),\r\n      },\r\n      {\r\n        path: \"/qun\",\r\n        name: \"签约客户群\",\r\n        component: () => import(\"../views/pages/yonghu/qun.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/yonghu\",\r\n    name: \"用户管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/user\",\r\n        name: \"用户\",\r\n        component: () => import(\"../views/pages/yonghu/user.vue\"),\r\n      },\r\n      {\r\n        path: \"/debts\",\r\n        name: \"债务人列表\",\r\n        component: () => import(\"../views/pages/debt/debts.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/zhifu\",\r\n    name: \"支付列表\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/order\",\r\n        name: \"支付列表\",\r\n        component: () => import(\"../views/pages/yonghu/order.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/liaotian\",\r\n    name: \"聊天列表\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/chat\",\r\n        name: \"聊天列表\",\r\n        component: () => import(\"../views/pages/yonghu/chat.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/wenshuguanli\",\r\n    name: \"文书管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/dingzhi\",\r\n        name: \"合同定制\",\r\n        component: () => import(\"../views/pages/wenshu/dingzhi.vue\"),\r\n      },\r\n      {\r\n        path: \"/shenhe\",\r\n        name: \"合同审核\",\r\n        component: () => import(\"../views/pages/wenshu/shenhe.vue\"),\r\n      },\r\n      {\r\n        path: \"/cate\",\r\n        name: \"合同类型\",\r\n        component: () => import(\"../views/pages/wenshu/cate.vue\"),\r\n      },\r\n      {\r\n        path: \"/hetong\",\r\n        name: \"合同列表\",\r\n        component: () => import(\"../views/pages/wenshu/index.vue\"),\r\n      },\r\n      {\r\n        path: \"/lawyer\",\r\n        name: \"发律师函\",\r\n        component: () => import(\"../views/pages/yonghu/lawyer.vue\"),\r\n      },\r\n      {\r\n        path: \"/kecheng\",\r\n        name: \"课程列表\",\r\n        component: () => import(\"../views/pages/shipin/kecheng.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/yuangong\",\r\n    name: \"员工管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/zhiwei\",\r\n        name: \"职位\",\r\n        component: () => import(\"../views/pages/yuangong/zhiwei.vue\"),\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工\",\r\n        component: () => import(\"../views/pages/yuangong/index.vue\"),\r\n      },\r\n      {\r\n        path: \"/lvshi\",\r\n        name: \"律师\",\r\n        component: () => import(\"../views/pages/lvshi/lvshi.vue\"),\r\n      },\r\n      {\r\n        path: \"/quanxian\",\r\n        name: \"权限管理\",\r\n        component: () => import(\"../views/pages/yuangong/quanxian.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    path: \"/fuwu\",\r\n    name: \"服务管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务列表\",\r\n        component: () => import(\"../views/pages/fuwu/index.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/xinwen\",\r\n    name: \"案例管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/anli\",\r\n        name: \"案例列表\",\r\n        component: () => import(\"../views/pages/xinwen/xinwen.vue\"),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/lvshiguanli\",\r\n    name: \"专业管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/zhuanye\",\r\n        name: \"专业列表\",\r\n        component: () => import(\"../views/pages/lvshi/zhuanye.vue\"),\r\n      }\r\n    ],\r\n  },\r\n  // 个人信息和设置页面\r\n  {\r\n    path: \"/profile\",\r\n    name: \"个人信息\",\r\n    component: () => import(\"../views/pages/profile/index.vue\"),\r\n    hidden: true,\r\n  },\r\n  {\r\n    path: \"/changePwd\",\r\n    name: \"修改密码\",\r\n    component: () => import(\"../views/pages/changePwd.vue\"),\r\n    hidden: true,\r\n  },\r\n  {\r\n    path: \"/archive\",\r\n    name: \"归档管理\",\r\n    component: Home,\r\n    children: [\r\n      {\r\n        path: \"/archive/file\",\r\n        name: \"文件归档\",\r\n        component: () => import(\"../views/pages/archive/File.vue\"),\r\n      },\r\n      {\r\n        path: \"/archive/search\",\r\n        name: \"档案检索\",\r\n        component: () => import(\"../views/pages/archive/Search.vue\"),\r\n      }\r\n    ]\r\n  },\r\n];\r\n\r\nconst router = new VueRouter({\r\n  routes,\r\n});\r\nrouter.beforeEach((to, from, next) => {\r\n  // 纯前端模式 - 简化路由守卫\r\n  if (to.path != \"/login\") {\r\n    // 检查是否有token，如果没有则跳转到登录页\r\n    const token = store.getters.GET_TOKEN;\r\n    if (!token) {\r\n      next({\r\n        path: \"/login\",\r\n      });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n    next();\r\n  }\r\n});\r\nexport default router;\r\n"], "sourceRoot": ""}