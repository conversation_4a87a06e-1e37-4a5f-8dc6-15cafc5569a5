<?php
namespace app\admin\controller;

use think\Request;
use untils\JsonService;
use models\Notifications;

/**
 * 通知管理控制器
 */
class Notification extends Base
{
    /**
     * 获取通知列表
     * @param Request $request
     * @return \think\response\Json
     */
    public function getList(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 20);
            $is_read = $request->get('is_read', '');
            $type = $request->get('type', '');
            $level = $request->get('level', '');

            // 使用与Dashboard相同的方法获取数据
            $allNotifications = Notifications::getNotificationList($this->userid, 1000); // 获取所有数据用于筛选

            $filteredNotifications = [];
            foreach ($allNotifications as $notification) {
                $shouldInclude = true;

                // 应用筛选条件
                if ($is_read !== '') {
                    $isRead = $notification['is_read'] == 1;
                    if ($is_read == '1' && !$isRead) {
                        $shouldInclude = false;
                    } elseif ($is_read == '0' && $isRead) {
                        $shouldInclude = false;
                    }
                }

                if ($type !== '' && $notification['type'] !== $type) {
                    $shouldInclude = false;
                }

                if ($level !== '' && $notification['level'] !== $level) {
                    $shouldInclude = false;
                }

                if ($shouldInclude) {
                    // 格式化数据，保持与Dashboard一致
                    $filteredNotifications[] = [
                        'id' => $notification['id'],
                        'title' => $notification['title'],
                        'content' => $notification['content'],
                        'time' => date('Y-m-d H:i', $notification['create_time']),
                        'read' => $notification['is_read'] == 1,
                        'is_read' => $notification['is_read'],
                        'type' => $notification['type'],
                        'level' => $notification['level'],
                        'create_time' => $notification['create_time'],
                        'create_time_text' => date('Y-m-d H:i:s', $notification['create_time'])
                    ];
                }
            }

            $total = count($filteredNotifications);

            // 分页处理
            $offset = ($page - 1) * $size;
            $list = array_slice($filteredNotifications, $offset, $size);

            $data = [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'size' => $size
            ];

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 创建通知
     * @param Request $request
     * @return \think\response\Json
     */
    public function create(Request $request)
    {
        try {
            $data = [
                'title' => $request->post('title'),
                'content' => $request->post('content', ''),
                'type' => $request->post('type', 'system'),
                'level' => $request->post('level', 'info'),
                'target_type' => $request->post('target_type', 'all'),
                'created_by' => $this->userid
            ];

            // 验证必填字段
            if (empty($data['title'])) {
                return JsonService::fail('标题不能为空');
            }

            if (empty($data['content'])) {
                return JsonService::fail('内容不能为空');
            }

            $result = Notifications::createNotification($data);

            if ($result) {
                return JsonService::successful('发布成功');
            } else {
                return JsonService::fail('发布失败');
            }

        } catch (\Exception $e) {
            return JsonService::fail('发布失败：' . $e->getMessage());
        }
    }

    /**
     * 删除通知
     * @param Request $request
     * @return \think\response\Json
     */
    public function delete(Request $request)
    {
        try {
            $id = $request->param('id');

            $where = [
                ['id', '=', $id],
                ['status', '=', 1]
            ];

            $result = Notifications::where($where)->update(['status' => 0]);

            if ($result) {
                return JsonService::successful('删除成功');
            } else {
                return JsonService::fail('删除失败或通知不存在');
            }

        } catch (\Exception $e) {
            return JsonService::fail('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量标记为已读
     * @return \think\response\Json
     */
    public function markAllRead()
    {
        try {
            $where = [
                ['status', '=', 1],
                ['is_read', '=', 0],
                ['target_type', 'in', ['all', 'admin']]
            ];

            // 检查是否过期
            $where[] = function($query) {
                $query->whereNull('expire_time')->whereOr('expire_time', '>', time());
            };

            $data = [
                'is_read' => 1,
                'read_time' => time()
            ];

            $result = Notifications::where($where)->update($data);

            return JsonService::successful('操作成功', ['updated' => $result]);

        } catch (\Exception $e) {
            return JsonService::fail('操作失败：' . $e->getMessage());
        }
    }

    /**
     * 获取统计信息
     * @return \think\response\Json
     */
    public function getStats()
    {
        try {
            // 使用与Dashboard相同的方法获取数据
            $allNotifications = Notifications::getNotificationList($this->userid, 1000);

            $total = count($allNotifications);
            $unread = 0;

            foreach ($allNotifications as $notification) {
                if ($notification['is_read'] == 0) {
                    $unread++;
                }
            }

            $data = [
                'total' => $total,
                'unread' => $unread,
                'read' => $total - $unread
            ];

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取通知详情
     * @param Request $request
     * @return \think\response\Json
     */
    public function detail(Request $request)
    {
        try {
            $id = $request->param('id');

            $where = [
                ['id', '=', $id],
                ['status', '=', 1],
                ['target_type', 'in', ['all', 'admin']]
            ];

            $notification = Notifications::where($where)->find();

            if (!$notification) {
                return JsonService::fail('通知不存在或无权限');
            }

            $data = $notification->toArray();
            $data['read'] = $data['is_read'] == 1;
            $data['time'] = date('Y-m-d H:i', $data['create_time']);
            $data['create_time_text'] = date('Y-m-d H:i:s', $data['create_time']);

            // 如果是未读通知，标记为已读
            if ($data['is_read'] == 0) {
                Notifications::markAsRead($id, $this->userid);
                $data['read'] = true;
                $data['is_read'] = 1;
                $data['read_time'] = time();
            }

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取最新通知（用于顶部通知栏）
     * @param Request $request
     * @return \think\response\Json
     */
    public function getLatest(Request $request)
    {
        try {
            $limit = $request->get('limit', 5);

            $where = [
                ['status', '=', 1],
                ['target_type', 'in', ['all', 'admin']],
                ['is_read', '=', 0]
            ];

            // 检查是否过期
            $where[] = function($query) {
                $query->whereNull('expire_time')->whereOr('expire_time', '>', time());
            };

            $list = Notifications::where($where)
                ->order('create_time desc')
                ->limit($limit)
                ->select()
                ->toArray();

            // 格式化数据
            foreach ($list as &$item) {
                $item['time'] = date('Y-m-d H:i', $item['create_time']);
            }

            return JsonService::successful('获取成功', $list);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }
}
