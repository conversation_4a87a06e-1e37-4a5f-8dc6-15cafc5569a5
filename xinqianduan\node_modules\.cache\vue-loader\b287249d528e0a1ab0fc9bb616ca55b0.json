{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=template&id=fae5bece&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748542316945}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}