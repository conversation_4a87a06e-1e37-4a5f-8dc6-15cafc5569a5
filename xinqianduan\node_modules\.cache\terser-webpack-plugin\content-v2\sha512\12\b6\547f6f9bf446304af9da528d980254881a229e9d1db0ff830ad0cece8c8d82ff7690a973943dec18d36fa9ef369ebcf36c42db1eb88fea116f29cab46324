{"map": "{\"version\":3,\"sources\":[\"js/chunk-5f4caf1e.ad5dc2bb.js\"],\"names\":[\"window\",\"push\",\"4a94\",\"module\",\"exports\",\"__webpack_require__\",\"c798\",\"__webpack_exports__\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"_m\",\"attrs\",\"type\",\"icon\",\"on\",\"click\",\"$event\",\"editData\",\"_v\",\"refulsh\",\"shadow\",\"placeholder\",\"clearable\",\"nativeOn\",\"keyup\",\"indexOf\",\"_k\",\"keyCode\",\"key\",\"searchData\",\"apply\",\"arguments\",\"model\",\"value\",\"search\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"slot\",\"permission_level\",\"label\",\"status\",\"clearSearch\",\"gutter\",\"span\",\"_s\",\"total\",\"adminCount\",\"userCount\",\"activeCount\",\"viewMode\",\"size\",\"directives\",\"name\",\"rawName\",\"loading\",\"data\",\"list\",\"stripe\",\"prop\",\"min-width\",\"scopedSlots\",\"_u\",\"fn\",\"scope\",\"row\",\"title\",\"level\",\"_e\",\"show-overflow-tooltip\",\"width\",\"align\",\"_l\",\"getPermissionLabels\",\"quanxian\",\"permission\",\"index\",\"staticStyle\",\"margin\",\"getPermissionTagType\",\"active-value\",\"inactive-value\",\"change\",\"changeStatus\",\"create_time\",\"fixed\",\"plain\",\"id\",\"delData\",\"$index\",\"position\",\"desc\",\"page-sizes\",\"page-size\",\"layout\",\"size-change\",\"handleSizeChange\",\"current-change\",\"handleCurrentChange\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"ref\",\"ruleForm\",\"rules\",\"label-width\",\"formLabelWidth\",\"autocomplete\",\"rows\",\"options\",\"props\",\"saveData\",\"dialogVisible\",\"src\",\"show_image\",\"staticRenderFns\",\"zhiweivue_type_script_lang_js\",\"components\",\"[object Object]\",\"multiple\",\"allSize\",\"page\",\"url\",\"info\",\"required\",\"message\",\"trigger\",\"computed\",\"filter\",\"item\",\"includes\",\"length\",\"getData\",\"methods\",\"permissions\",\"Array\",\"isArray\",\"permissionMap\",\"1\",\"2\",\"3\",\"4\",\"11\",\"12\",\"13\",\"21\",\"22\",\"31\",\"32\",\"33\",\"41\",\"map\",\"Boolean\",\"$message\",\"success\",\"_this\",\"getInfo\",\"getQuanxians\",\"getPermissionsFromManagement\",\"permissionData\",\"children\",\"getRequest\",\"then\",\"resp\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"deleteRequest\",\"code\",\"splice\",\"msg\",\"catch\",\"$router\",\"go\",\"setTimeout\",\"$refs\",\"validate\",\"valid\",\"postRequest\",\"val\",\"res\",\"pic_path\",\"file\",\"isTypeTrue\",\"test\",\"error\",\"fileName\",\"yuangong_zhiweivue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\",\"fd24\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,KAM3BC,KACA,SAAUH,EAAQI,EAAqBF,GAE7C,aAEAA,EAAoBG,EAAED,GAGtB,IAAIE,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,sBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,UACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,gBAEVC,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIY,SAAS,MAGvB,CAACZ,EAAIa,GAAG,YAAaX,EAAG,YAAa,CACtCE,YAAa,cACbE,MAAO,CACLE,KAAQ,mBAEVC,GAAI,CACFC,MAASV,EAAIc,UAEd,CAACd,EAAIa,GAAG,WAAY,OAAQX,EAAG,MAAO,CACvCE,YAAa,kBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,cACbE,MAAO,CACLS,OAAU,UAEX,CAACb,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,UAAWX,EAAG,WAAY,CACnCE,YAAa,eACbE,MAAO,CACLU,YAAe,aACfC,UAAa,IAEfC,SAAU,CACRC,MAAS,SAAUR,GACjB,OAAKA,EAAOJ,KAAKa,QAAQ,QAAUpB,EAAIqB,GAAGV,EAAOW,QAAS,QAAS,GAAIX,EAAOY,IAAK,SAAiB,KAC7FvB,EAAIwB,WAAWC,MAAM,KAAMC,aAGtCC,MAAO,CACLC,MAAO5B,EAAI6B,OAAOC,QAClBC,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,UAAWG,IAElCE,WAAY,mBAEb,CAAChC,EAAG,IAAK,CACVE,YAAa,gCACbE,MAAO,CACL6B,KAAQ,UAEVA,KAAM,cACD,GAAIjC,EAAG,MAAO,CACnBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCE,YAAa,gBACbE,MAAO,CACLU,YAAe,UACfC,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAI6B,OAAOO,iBAClBL,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,mBAAoBG,IAE3CE,WAAY,4BAEb,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACL+B,MAAS,QACTT,MAAS,WAET1B,EAAG,YAAa,CAClBI,MAAO,CACL+B,MAAS,MACTT,MAAS,WAET1B,EAAG,YAAa,CAClBI,MAAO,CACL+B,MAAS,OACTT,MAAS,WAER,IAAK,GAAI1B,EAAG,MAAO,CACtBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,QAASX,EAAG,YAAa,CAClCE,YAAa,gBACbE,MAAO,CACLU,YAAe,QACfC,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAI6B,OAAOS,OAClBP,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,SAAUG,IAEjCE,WAAY,kBAEb,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACL+B,MAAS,KACTT,MAAS,KAET1B,EAAG,YAAa,CAClBI,MAAO,CACL+B,MAAS,KACTT,MAAS,MAER,IAAK,KAAM1B,EAAG,MAAO,CACxBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRC,KAAQ,kBAEVC,GAAI,CACFC,MAASV,EAAIwB,aAEd,CAACxB,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCI,MAAO,CACLE,KAAQ,wBAEVC,GAAI,CACFC,MAASV,EAAIuC,cAEd,CAACvC,EAAIa,GAAG,WAAY,QAAS,GAAIX,EAAG,MAAO,CAC5CE,YAAa,iBACZ,CAACF,EAAG,SAAU,CACfI,MAAO,CACLkC,OAAU,KAEX,CAACtC,EAAG,SAAU,CACfI,MAAO,CACLmC,KAAQ,IAET,CAACvC,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,mBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,uBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAG1C,EAAI2C,UAAWzC,EAAG,MAAO,CACzCE,YAAa,cACZ,CAACJ,EAAIa,GAAG,gBAAiBX,EAAG,SAAU,CACvCI,MAAO,CACLmC,KAAQ,IAET,CAACvC,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,mBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,yBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAG1C,EAAI4C,eAAgB1C,EAAG,MAAO,CAC9CE,YAAa,cACZ,CAACJ,EAAIa,GAAG,gBAAiBX,EAAG,SAAU,CACvCI,MAAO,CACLmC,KAAQ,IAET,CAACvC,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,mBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAG1C,EAAI6C,cAAe3C,EAAG,MAAO,CAC7CE,YAAa,cACZ,CAACJ,EAAIa,GAAG,gBAAiBX,EAAG,SAAU,CACvCI,MAAO,CACLmC,KAAQ,IAET,CAACvC,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,oBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,2BACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAG1C,EAAI8C,gBAAiB5C,EAAG,MAAO,CAC/CE,YAAa,cACZ,CAACJ,EAAIa,GAAG,iBAAkB,IAAK,GAAIX,EAAG,MAAO,CAC9CE,YAAa,iBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,aACbE,MAAO,CACLS,OAAU,UAEX,CAACb,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIa,GAAG,YAAaX,EAAG,MAAO,CAChCE,YAAa,eACZ,CAACF,EAAG,kBAAmB,CAACA,EAAG,YAAa,CACzCI,MAAO,CACLC,KAAyB,UAAjBP,EAAI+C,SAAuB,UAAY,GAC/CvC,KAAQ,eACRwC,KAAQ,SAEVvC,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAI+C,SAAW,WAGlB,CAAC/C,EAAIa,GAAG,YAAaX,EAAG,YAAa,CACtCI,MAAO,CACLC,KAAyB,SAAjBP,EAAI+C,SAAsB,UAAY,GAC9CvC,KAAQ,iBACRwC,KAAQ,SAEVvC,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAI+C,SAAW,UAGlB,CAAC/C,EAAIa,GAAG,aAAc,IAAK,KAAuB,UAAjBb,EAAI+C,SAAuB7C,EAAG,MAAO,CACvEE,YAAa,cACZ,CAACF,EAAG,WAAY,CACjB+C,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTvB,MAAO5B,EAAIoD,QACXlB,WAAY,YAEd9B,YAAa,iBACbE,MAAO,CACL+C,KAAQrD,EAAIsD,KACZC,OAAU,KAEX,CAACrD,EAAG,kBAAmB,CACxBI,MAAO,CACLkD,KAAQ,QACRnB,MAAS,OACToB,YAAa,OAEfC,YAAa1D,EAAI2D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC3D,EAAG,MAAO,CAChBE,YAAa,uBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAGmB,EAAMC,IAAIC,UAAWF,EAAMC,IAAIE,MAAQ9D,EAAG,MAAO,CACjEE,YAAa,kBACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAGmB,EAAMC,IAAIE,UAAYhE,EAAIiE,WAE5C,MAAM,EAAO,aACf/D,EAAG,kBAAmB,CACxBI,MAAO,CACLkD,KAAQ,OACRnB,MAAS,OACToB,YAAa,MACbS,wBAAyB,MAEzBhE,EAAG,kBAAmB,CACxBI,MAAO,CACL+B,MAAS,OACT8B,MAAS,MACTC,MAAS,UAEXV,YAAa1D,EAAI2D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC3D,EAAG,MAAO,CAChBE,YAAa,mBACZJ,EAAIqE,GAAGrE,EAAIsE,oBAAoBT,EAAMC,IAAIS,WAAW,SAAUC,EAAYC,GAC3E,OAAOvE,EAAG,SAAU,CAClBqB,IAAKkD,EACLC,YAAa,CACXC,OAAU,OAEZrE,MAAO,CACL0C,KAAQ,OACRzC,KAAQP,EAAI4E,qBAAqBJ,KAElC,CAACxE,EAAIa,GAAG,IAAMb,EAAI0C,GAAG8B,GAAc,UACpC,OAEJ,MAAM,EAAO,aACftE,EAAG,kBAAmB,CACxBI,MAAO,CACL+B,MAAS,KACT8B,MAAS,MACTC,MAAS,UAEXV,YAAa1D,EAAI2D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC3D,EAAG,YAAa,CACtBI,MAAO,CACLuE,eAAgB,EAChBC,iBAAkB,GAEpBrE,GAAI,CACFsE,OAAU,SAAUpE,GAClB,OAAOX,EAAIgF,aAAanB,EAAMC,OAGlCnC,MAAO,CACLC,MAAOiC,EAAMC,IAAIxB,OACjBP,SAAU,SAAUC,GAClBhC,EAAIiC,KAAK4B,EAAMC,IAAK,SAAU9B,IAEhCE,WAAY,0BAIhB,MAAM,EAAO,cACfhC,EAAG,kBAAmB,CACxBI,MAAO,CACLkD,KAAQ,cACRnB,MAAS,OACT8B,MAAS,MACTC,MAAS,UAEXV,YAAa1D,EAAI2D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC3D,EAAG,MAAO,CAChBE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIa,GAAG,IAAMb,EAAI0C,GAAGmB,EAAMC,IAAImB,aAAe,WAEjD,MAAM,EAAO,cACf/E,EAAG,kBAAmB,CACxBI,MAAO,CACL4E,MAAS,QACT7C,MAAS,KACT8B,MAAS,MACTC,MAAS,UAEXV,YAAa1D,EAAI2D,GAAG,CAAC,CACnBpC,IAAK,UACLqC,GAAI,SAAUC,GACZ,MAAO,CAAC3D,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRyC,KAAQ,OACRxC,KAAQ,eACR2E,MAAS,IAEX1E,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIY,SAASiD,EAAMC,IAAIsB,OAGjC,CAACpF,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCI,MAAO,CACLC,KAAQ,SACRyC,KAAQ,OACRxC,KAAQ,iBACR2E,MAAS,IAEX1E,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIqF,QAAQxB,EAAMyB,OAAQzB,EAAMC,IAAIsB,OAG9C,CAACpF,EAAIa,GAAG,WAAY,OAEvB,MAAM,EAAO,eACd,IAAK,GAAKb,EAAIiE,KAAuB,SAAjBjE,EAAI+C,SAAsB7C,EAAG,MAAO,CAC3D+C,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTvB,MAAO5B,EAAIoD,QACXlB,WAAY,YAEd9B,YAAa,aACZ,CAACF,EAAG,SAAU,CACfI,MAAO,CACLkC,OAAU,KAEXxC,EAAIqE,GAAGrE,EAAIsD,MAAM,SAAUiC,GAC5B,OAAOrF,EAAG,SAAU,CAClBqB,IAAKgE,EAASH,GACdhF,YAAa,oBACbE,MAAO,CACLmC,KAAQ,IAET,CAACvC,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIa,GAAG,IAAMb,EAAI0C,GAAG6C,EAASxB,OAAS,OAAQ7D,EAAG,MAAO,CAC1DE,YAAa,eACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLuE,eAAgB,EAChBC,iBAAkB,EAClB9B,KAAQ,SAEVvC,GAAI,CACFsE,OAAU,SAAUpE,GAClB,OAAOX,EAAIgF,aAAaO,KAG5B5D,MAAO,CACLC,MAAO2D,EAASjD,OAChBP,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKsD,EAAU,SAAUvD,IAE/BE,WAAY,sBAEX,KAAMhC,EAAG,MAAO,CACnBE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACJ,EAAIa,GAAGb,EAAI0C,GAAG6C,EAASC,SAAUtF,EAAG,MAAO,CAC7CE,YAAa,oBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,oBACZ,CAACJ,EAAIa,GAAG,WAAYX,EAAG,MAAO,CAC/BE,YAAa,mBACZJ,EAAIqE,GAAGrE,EAAIsE,oBAAoBiB,EAAShB,WAAW,SAAUC,EAAYC,GAC1E,OAAOvE,EAAG,SAAU,CAClBqB,IAAKkD,EACLC,YAAa,CACXC,OAAU,OAEZrE,MAAO,CACL0C,KAAQ,OACRzC,KAAQP,EAAI4E,qBAAqBJ,KAElC,CAACxE,EAAIa,GAAG,IAAMb,EAAI0C,GAAG8B,GAAc,UACpC,KAAMtE,EAAG,MAAO,CAClBE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIa,GAAG,IAAMb,EAAI0C,GAAG6C,EAASN,aAAe,WAAY/E,EAAG,MAAO,CACpEE,YAAa,gBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRyC,KAAQ,QACRxC,KAAQ,eACR2E,MAAS,IAEX1E,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIY,SAAS2E,EAASH,OAGhC,CAACpF,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCI,MAAO,CACLC,KAAQ,SACRyC,KAAQ,QACRxC,KAAQ,iBACR2E,MAAS,IAEX1E,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAIqF,QAAQrF,EAAIsD,KAAKlC,QAAQmE,GAAWA,EAASH,OAGpD,CAACpF,EAAIa,GAAG,WAAY,UACrB,IAAK,GAAKb,EAAIiE,KAAM/D,EAAG,MAAO,CAChCE,YAAa,wBACZ,CAACF,EAAG,gBAAiB,CACtBE,YAAa,aACbE,MAAO,CACLmF,aAAc,CAAC,GAAI,GAAI,GAAI,IAC3BC,YAAa1F,EAAIgD,KACjB2C,OAAU,0CACVhD,MAAS3C,EAAI2C,OAEflC,GAAI,CACFmF,cAAe5F,EAAI6F,iBACnBC,iBAAkB9F,EAAI+F,wBAErB,MAAO,GAAI7F,EAAG,YAAa,CAC9BI,MAAO,CACLyD,MAAS/D,EAAI+D,MAAQ,KACrBiC,QAAWhG,EAAIiG,kBACfC,wBAAwB,EACxB/B,MAAS,OAEX1D,GAAI,CACF0F,iBAAkB,SAAUxF,GAC1BX,EAAIiG,kBAAoBtF,KAG3B,CAACT,EAAG,UAAW,CAChBkG,IAAK,WACL9F,MAAO,CACLqB,MAAS3B,EAAIqG,SACbC,MAAStG,EAAIsG,QAEd,CAACpG,EAAG,eAAgB,CACrBI,MAAO,CACL+B,MAASrC,EAAI+D,MAAQ,KACrBwC,cAAevG,EAAIwG,eACnBhD,KAAQ,UAET,CAACtD,EAAG,WAAY,CACjBI,MAAO,CACLmG,aAAgB,OAElB9E,MAAO,CACLC,MAAO5B,EAAIqG,SAAStC,MACpBhC,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIqG,SAAU,QAASrE,IAElCE,WAAY,qBAEX,GAAIhC,EAAG,eAAgB,CAC1BI,MAAO,CACL+B,MAAS,KACTkE,cAAevG,EAAIwG,iBAEpB,CAACtG,EAAG,WAAY,CACjBI,MAAO,CACLmG,aAAgB,MAChBlG,KAAQ,WACRmG,KAAQ,GAEV/E,MAAO,CACLC,MAAO5B,EAAIqG,SAASb,KACpBzD,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIqG,SAAU,OAAQrE,IAEjCE,WAAY,oBAEX,GAAIhC,EAAG,eAAgB,CAC1BI,MAAO,CACL+B,MAAS,KACTkE,cAAevG,EAAIwG,iBAEpB,CAACtG,EAAG,cAAe,CACpBI,MAAO,CACLqG,QAAW3G,EAAI2G,QACfC,MAAS5G,EAAI4G,MACb3F,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAIqG,SAAS9B,SACpBxC,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIqG,SAAU,WAAYrE,IAErCE,WAAY,wBAEX,IAAK,GAAIhC,EAAG,MAAO,CACtBE,YAAa,gBACbE,MAAO,CACL6B,KAAQ,UAEVA,KAAM,UACL,CAACjC,EAAG,YAAa,CAClBO,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAIiG,mBAAoB,KAG3B,CAACjG,EAAIa,GAAG,SAAUX,EAAG,YAAa,CACnCI,MAAO,CACLC,KAAQ,WAEVE,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAI6G,cAGd,CAAC7G,EAAIa,GAAG,UAAW,IAAK,GAAIX,EAAG,YAAa,CAC7CI,MAAO,CACLyD,MAAS,OACTiC,QAAWhG,EAAI8G,cACf3C,MAAS,OAEX1D,GAAI,CACF0F,iBAAkB,SAAUxF,GAC1BX,EAAI8G,cAAgBnG,KAGvB,CAACT,EAAG,WAAY,CACjBI,MAAO,CACLyG,IAAO/G,EAAIgH,eAEV,IAAK,IAERC,EAAkB,CAAC,WACrB,IAAIjH,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,iBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIa,GAAG,YAAaX,EAAG,IAAK,CAC9BE,YAAa,iBACZ,CAACJ,EAAIa,GAAG,uBAQoBqG,EAAgC,CAC/DhE,KAAM,OACNiE,WAAY,GACZC,OACE,MAAO,CACLrE,SAAU,QAEV6D,MAAO,CACLS,UAAU,GAEZV,QAAS,GACTW,QAAS,OACThE,KAAM,GACNX,MAAO,EACP4E,KAAM,EACNvE,KAAM,GACNnB,OAAQ,CACNC,QAAS,GACTM,iBAAkB,GAClBE,OAAQ,IAEVc,SAAS,EACToE,IAAK,WACLzD,MAAO,KACP0D,KAAM,GACNxB,mBAAmB,EACnBe,WAAY,GACZF,eAAe,EACfT,SAAU,CACRtC,MAAO,GACPyB,KAAM,GACNjB,SAAU,GACVjC,OAAQ,GAEVgE,MAAO,CACLvC,MAAO,CAAC,CACN2D,UAAU,EACVC,QAAS,UACTC,QAAS,SAEXpC,KAAM,CAAC,CACLkC,UAAU,EACVC,QAAS,UACTC,QAAS,UAGbpB,eAAgB,UAGpBqB,SAAU,CAERT,aACE,OAAOnH,KAAKqD,KAAKwE,OAAOC,GAAQA,EAAKxD,WAAawD,EAAKxD,SAASyD,SAAS,IAAMD,EAAKxD,SAASyD,SAAS,MAAMC,QAG9Gb,YACE,OAAOnH,KAAKqD,KAAKwE,OAAOC,GAAQA,EAAKxD,WAAawD,EAAKxD,SAASyD,SAAS,KAAOD,EAAKxD,SAASyD,SAAS,KAAKC,QAG9Gb,cACE,OAAOnH,KAAKqD,KAAKwE,OAAOC,GAAwB,IAAhBA,EAAKzF,QAAc2F,SAGvDb,UACEnH,KAAKiI,WAEPC,QAAS,CAEPf,oBAAoBgB,GAClB,IAAKA,IAAgBC,MAAMC,QAAQF,GAAc,MAAO,GACxD,MAAMG,EAAgB,CACpBC,EAAG,OACHC,EAAG,OACHC,EAAG,OACHC,EAAG,OACHC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,QACJC,GAAI,OACJC,GAAI,QAEN,OAAOhB,EAAYiB,IAAIjE,GAAMmD,EAAcnD,IAAO,KAAKA,GAAM0C,OAAOwB,UAGtElC,qBAAqB5C,GACnB,OAAIA,EAAWwD,SAAS,OAASxD,EAAWwD,SAAS,MAAc,SAC/DxD,EAAWwD,SAAS,OAASxD,EAAWwD,SAAS,MAAc,UAC/DxD,EAAWwD,SAAS,OAASxD,EAAWwD,SAAS,MAAc,UAC/DxD,EAAWwD,SAAS,OAASxD,EAAWwD,SAAS,MAAc,UAC5D,QAGTZ,aAAatD,GACX7D,KAAKsJ,SAASC,QAAQ,MAAM1F,EAAIC,YAAYD,EAAIxB,OAAS,KAAO,SAGlE8E,cACEnH,KAAK4B,OAAS,CACZC,QAAS,GACTM,iBAAkB,GAClBE,OAAQ,IAEVrC,KAAKuB,cAEP4F,WACAA,SAAShC,GACP,IAAIqE,EAAQxJ,KACF,GAANmF,EACFnF,KAAKyJ,QAAQtE,GAEbnF,KAAKoG,SAAW,CACdtC,MAAO,GACPyB,KAAM,IAGViE,EAAMxD,mBAAoB,EAC1BwD,EAAME,gBAERvC,eAEEnH,KAAK2J,gCAGPxC,+BAEE,MAAMyC,EAAiB,CAAC,CACtBjI,MAAO,EACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,UAER,CACDT,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,UAER,CACDT,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,YAGV,CACDT,MAAO,EACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,UAER,CACDT,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,YAGV,CACDT,MAAO,EACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,UAER,CACDT,MAAO,GACPS,MAAO,QACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,SACN,CACDT,MAAO,IACPS,MAAO,SACN,CACDT,MAAO,IACPS,MAAO,WAER,CACDT,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,YAGV,CACDT,MAAO,EACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,GACPS,MAAO,OACPyH,SAAU,CAAC,CACTlI,MAAO,IACPS,MAAO,UACN,CACDT,MAAO,IACPS,MAAO,QACN,CACDT,MAAO,IACPS,MAAO,eAIbpC,KAAK0G,QAAUkD,GAEjBzC,QAAQhC,GACN,IAAIqE,EAAQxJ,KACZwJ,EAAMM,WAAWN,EAAMjC,IAAM,WAAapC,GAAI4E,KAAKC,IAC7CA,IACFR,EAAMpD,SAAW4D,EAAK5G,SAI5B+D,QAAQ3C,EAAOW,GACbnF,KAAKiK,SAAS,WAAY,KAAM,CAC9BC,kBAAmB,KACnBC,iBAAkB,KAClB7J,KAAM,YACLyJ,KAAK,KACN/J,KAAKoK,cAAcpK,KAAKuH,IAAM,aAAepC,GAAI4E,KAAKC,IACnC,KAAbA,EAAKK,MACPrK,KAAKsJ,SAAS,CACZhJ,KAAM,UACNoH,QAAS,UAEX1H,KAAKqD,KAAKiH,OAAO9F,EAAO,IAExBxE,KAAKsJ,SAAS,CACZhJ,KAAM,QACNoH,QAASsC,EAAKO,UAInBC,MAAM,KACPxK,KAAKsJ,SAAS,CACZhJ,KAAM,QACNoH,QAAS,aAIfP,UACEnH,KAAKyK,QAAQC,GAAG,IAElBvD,aACEnH,KAAKsH,KAAO,EACZtH,KAAK+C,KAAO,GACZ/C,KAAKiI,WAEPd,UACE,IAAIqC,EAAQxJ,KACZwJ,EAAMrG,SAAU,EAGhBwH,WAAW,KACTnB,EAAMrG,SAAU,EAChBqG,EAAMnG,KAAO,CAAC,CACZ8B,GAAI,EACJrB,MAAO,QACPyB,KAAM,uBACNjB,SAAU,CAAC,EAAG,EAAG,EAAG,GAEpBjC,OAAQ,EACR0B,MAAO,QACPiB,YAAa,uBACZ,CACDG,GAAI,EACJrB,MAAO,OACPyB,KAAM,kBACNjB,SAAU,CAAC,EAAG,GAEdjC,OAAQ,EACR0B,MAAO,MACPiB,YAAa,uBACZ,CACDG,GAAI,EACJrB,MAAO,OACPyB,KAAM,oBACNjB,SAAU,CAAC,GAEXjC,OAAQ,EACR0B,MAAO,OACPiB,YAAa,uBACZ,CACDG,GAAI,EACJrB,MAAO,OACPyB,KAAM,mBACNjB,SAAU,CAAC,GAEXjC,OAAQ,EACR0B,MAAO,OACPiB,YAAa,uBACZ,CACDG,GAAI,EACJrB,MAAO,OACPyB,KAAM,mBACNjB,SAAU,CAAC,IAEXjC,OAAQ,EACR0B,MAAO,OACPiB,YAAa,wBAEfwE,EAAM9G,MAAQ,GACb,MAkBLyE,WACE,IAAIqC,EAAQxJ,KACZA,KAAK4K,MAAM,YAAYC,SAASC,IAC9B,IAAIA,EAiBF,OAAO,EAhBP9K,KAAK+K,YAAYvB,EAAMjC,IAAM,OAAQvH,KAAKoG,UAAU2D,KAAKC,IACtC,KAAbA,EAAKK,MACPb,EAAMF,SAAS,CACbhJ,KAAM,UACNoH,QAASsC,EAAKO,MAEhBvK,KAAKiI,UACLuB,EAAMxD,mBAAoB,GAE1BwD,EAAMF,SAAS,CACbhJ,KAAM,QACNoH,QAASsC,EAAKO,WAS1BpD,iBAAiB6D,GACfhL,KAAK+C,KAAOiI,EACZhL,KAAKiI,WAEPd,oBAAoB6D,GAClBhL,KAAKsH,KAAO0D,EACZhL,KAAKiI,WAEPd,cAAc8D,GACZjL,KAAKoG,SAAS8E,SAAWD,EAAI7H,KAAKmE,KAEpCJ,UAAUgE,GACRnL,KAAK+G,WAAaoE,EAClBnL,KAAK6G,eAAgB,GAEvBM,aAAagE,GACX,MAAMC,EAAa,0BAA0BC,KAAKF,EAAK7K,MAClD8K,GACHpL,KAAKsJ,SAASgC,MAAM,cAIxBnE,SAASgE,EAAMI,GACb,IAAI/B,EAAQxJ,KACZwJ,EAAMM,WAAW,6BAA+BqB,GAAMpB,KAAKC,IACxC,KAAbA,EAAKK,MACPb,EAAMpD,SAASmF,GAAY,GAC3B/B,EAAMF,SAASC,QAAQ,UAEvBC,EAAMF,SAASgC,MAAMtB,EAAKO,UAOFiB,EAAyC,EAKvEC,GAHqE/L,EAAoB,QAGnEA,EAAoB,SAW1CgM,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACA1L,EACAkH,GACA,EACA,KACA,WACA,MAIwCpH,EAAoB,WAAc8L,EAAiB,SAIvFE,KACA,SAAUpM,EAAQI,EAAqBF,GAE7C,aAC+cA,EAAoB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-5f4caf1e\"],{\"4a94\":function(e,t,a){},c798:function(e,t,a){\"use strict\";a.r(t);var s=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"position-container\"},[t(\"div\",{staticClass:\"page-header\"},[t(\"div\",{staticClass:\"header-content\"},[e._m(0),t(\"div\",{staticClass:\"header-actions\"},[t(\"el-button\",{staticClass:\"add-btn\",attrs:{type:\"primary\",icon:\"el-icon-plus\"},on:{click:function(t){return e.editData(0)}}},[e._v(\" 新增职位 \")]),t(\"el-button\",{staticClass:\"refresh-btn\",attrs:{icon:\"el-icon-refresh\"},on:{click:e.refulsh}},[e._v(\" 刷新 \")])],1)])]),t(\"div\",{staticClass:\"search-section\"},[t(\"el-card\",{staticClass:\"search-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"search-form\"},[t(\"div\",{staticClass:\"search-row\"},[t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"职位搜索\")]),t(\"el-input\",{staticClass:\"search-input\",attrs:{placeholder:\"请输入职位名称或描述\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,\"keyword\",t)},expression:\"search.keyword\"}},[t(\"i\",{staticClass:\"el-input__icon el-icon-search\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"权限级别\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择权限级别\",clearable:\"\"},model:{value:e.search.permission_level,callback:function(t){e.$set(e.search,\"permission_level\",t)},expression:\"search.permission_level\"}},[t(\"el-option\",{attrs:{label:\"超级管理员\",value:\"super\"}}),t(\"el-option\",{attrs:{label:\"管理员\",value:\"admin\"}}),t(\"el-option\",{attrs:{label:\"普通用户\",value:\"user\"}})],1)],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"状态\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.search.status,callback:function(t){e.$set(e.search,\"status\",t)},expression:\"search.status\"}},[t(\"el-option\",{attrs:{label:\"启用\",value:1}}),t(\"el-option\",{attrs:{label:\"禁用\",value:0}})],1)],1)]),t(\"div\",{staticClass:\"search-actions\"},[t(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\"},on:{click:e.searchData}},[e._v(\" 搜索 \")]),t(\"el-button\",{attrs:{icon:\"el-icon-refresh-left\"},on:{click:e.clearSearch}},[e._v(\" 重置 \")])],1)])])],1),t(\"div\",{staticClass:\"stats-section\"},[t(\"el-row\",{attrs:{gutter:20}},[t(\"el-col\",{attrs:{span:6}},[t(\"div\",{staticClass:\"stat-card\"},[t(\"div\",{staticClass:\"stat-icon total\"},[t(\"i\",{staticClass:\"el-icon-postcard\"})]),t(\"div\",{staticClass:\"stat-content\"},[t(\"div\",{staticClass:\"stat-number\"},[e._v(e._s(e.total))]),t(\"div\",{staticClass:\"stat-label\"},[e._v(\"总职位数\")])])])]),t(\"el-col\",{attrs:{span:6}},[t(\"div\",{staticClass:\"stat-card\"},[t(\"div\",{staticClass:\"stat-icon admin\"},[t(\"i\",{staticClass:\"el-icon-user-solid\"})]),t(\"div\",{staticClass:\"stat-content\"},[t(\"div\",{staticClass:\"stat-number\"},[e._v(e._s(e.adminCount))]),t(\"div\",{staticClass:\"stat-label\"},[e._v(\"管理职位\")])])])]),t(\"el-col\",{attrs:{span:6}},[t(\"div\",{staticClass:\"stat-card\"},[t(\"div\",{staticClass:\"stat-icon user\"},[t(\"i\",{staticClass:\"el-icon-user\"})]),t(\"div\",{staticClass:\"stat-content\"},[t(\"div\",{staticClass:\"stat-number\"},[e._v(e._s(e.userCount))]),t(\"div\",{staticClass:\"stat-label\"},[e._v(\"普通职位\")])])])]),t(\"el-col\",{attrs:{span:6}},[t(\"div\",{staticClass:\"stat-card\"},[t(\"div\",{staticClass:\"stat-icon active\"},[t(\"i\",{staticClass:\"el-icon-circle-check\"})]),t(\"div\",{staticClass:\"stat-content\"},[t(\"div\",{staticClass:\"stat-number\"},[e._v(e._s(e.activeCount))]),t(\"div\",{staticClass:\"stat-label\"},[e._v(\"启用职位\")])])])])],1)],1),t(\"div\",{staticClass:\"table-section\"},[t(\"el-card\",{staticClass:\"table-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"table-header\"},[t(\"div\",{staticClass:\"table-title\"},[t(\"i\",{staticClass:\"el-icon-menu\"}),e._v(\" 职位列表 \")]),t(\"div\",{staticClass:\"table-tools\"},[t(\"el-button-group\",[t(\"el-button\",{attrs:{type:\"table\"===e.viewMode?\"primary\":\"\",icon:\"el-icon-menu\",size:\"small\"},on:{click:function(t){e.viewMode=\"table\"}}},[e._v(\" 列表视图 \")]),t(\"el-button\",{attrs:{type:\"card\"===e.viewMode?\"primary\":\"\",icon:\"el-icon-s-grid\",size:\"small\"},on:{click:function(t){e.viewMode=\"card\"}}},[e._v(\" 卡片视图 \")])],1)],1)]),\"table\"===e.viewMode?t(\"div\",{staticClass:\"table-view\"},[t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"position-table\",attrs:{data:e.list,stripe:\"\"}},[t(\"el-table-column\",{attrs:{prop:\"title\",label:\"职位名称\",\"min-width\":\"150\"},scopedSlots:e._u([{key:\"default\",fn:function(a){return[t(\"div\",{staticClass:\"position-title-cell\"},[t(\"div\",{staticClass:\"position-title\"},[e._v(e._s(a.row.title))]),a.row.level?t(\"div\",{staticClass:\"position-level\"},[e._v(e._s(a.row.level))]):e._e()])]}}],null,!1,414999880)}),t(\"el-table-column\",{attrs:{prop:\"desc\",label:\"职位描述\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"}}),t(\"el-table-column\",{attrs:{label:\"权限配置\",width:\"200\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(a){return[t(\"div\",{staticClass:\"permission-tags\"},e._l(e.getPermissionLabels(a.row.quanxian),(function(a,s){return t(\"el-tag\",{key:s,staticStyle:{margin:\"2px\"},attrs:{size:\"mini\",type:e.getPermissionTagType(a)}},[e._v(\" \"+e._s(a)+\" \")])})),1)]}}],null,!1,110400083)}),t(\"el-table-column\",{attrs:{label:\"状态\",width:\"100\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(a){return[t(\"el-switch\",{attrs:{\"active-value\":1,\"inactive-value\":0},on:{change:function(t){return e.changeStatus(a.row)}},model:{value:a.row.status,callback:function(t){e.$set(a.row,\"status\",t)},expression:\"scope.row.status\"}})]}}],null,!1,2880962836)}),t(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"创建时间\",width:\"160\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(a){return[t(\"div\",{staticClass:\"time-cell\"},[t(\"i\",{staticClass:\"el-icon-time\"}),e._v(\" \"+e._s(a.row.create_time)+\" \")])]}}],null,!1,3001843918)}),t(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\",width:\"180\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(a){return[t(\"div\",{staticClass:\"action-buttons\"},[t(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-edit\",plain:\"\"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v(\" 编辑 \")]),t(\"el-button\",{attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-delete\",plain:\"\"},on:{click:function(t){return e.delData(a.$index,a.row.id)}}},[e._v(\" 删除 \")])],1)]}}],null,!1,2809669383)})],1)],1):e._e(),\"card\"===e.viewMode?t(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"card-view\"},[t(\"el-row\",{attrs:{gutter:20}},e._l(e.list,(function(a){return t(\"el-col\",{key:a.id,staticClass:\"position-card-col\",attrs:{span:8}},[t(\"div\",{staticClass:\"position-card\"},[t(\"div\",{staticClass:\"card-header\"},[t(\"div\",{staticClass:\"card-title\"},[t(\"i\",{staticClass:\"el-icon-postcard\"}),e._v(\" \"+e._s(a.title)+\" \")]),t(\"div\",{staticClass:\"card-status\"},[t(\"el-switch\",{attrs:{\"active-value\":1,\"inactive-value\":0,size:\"small\"},on:{change:function(t){return e.changeStatus(a)}},model:{value:a.status,callback:function(t){e.$set(a,\"status\",t)},expression:\"position.status\"}})],1)]),t(\"div\",{staticClass:\"card-content\"},[t(\"div\",{staticClass:\"card-desc\"},[e._v(e._s(a.desc))]),t(\"div\",{staticClass:\"card-permissions\"},[t(\"div\",{staticClass:\"permission-title\"},[e._v(\"权限配置：\")]),t(\"div\",{staticClass:\"permission-tags\"},e._l(e.getPermissionLabels(a.quanxian),(function(a,s){return t(\"el-tag\",{key:s,staticStyle:{margin:\"2px\"},attrs:{size:\"mini\",type:e.getPermissionTagType(a)}},[e._v(\" \"+e._s(a)+\" \")])})),1)]),t(\"div\",{staticClass:\"card-footer\"},[t(\"div\",{staticClass:\"card-time\"},[t(\"i\",{staticClass:\"el-icon-time\"}),e._v(\" \"+e._s(a.create_time)+\" \")])])]),t(\"div\",{staticClass:\"card-actions\"},[t(\"el-button\",{attrs:{type:\"primary\",size:\"small\",icon:\"el-icon-edit\",plain:\"\"},on:{click:function(t){return e.editData(a.id)}}},[e._v(\" 编辑 \")]),t(\"el-button\",{attrs:{type:\"danger\",size:\"small\",icon:\"el-icon-delete\",plain:\"\"},on:{click:function(t){e.delData(e.list.indexOf(a),a.id)}}},[e._v(\" 删除 \")])],1)])])})),1)],1):e._e(),t(\"div\",{staticClass:\"pagination-container\"},[t(\"el-pagination\",{staticClass:\"pagination\",attrs:{\"page-sizes\":[12,24,48,96],\"page-size\":e.size,layout:\"total, sizes, prev, pager, next, jumper\",total:e.total},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handleCurrentChange}})],1)])],1),t(\"el-dialog\",{attrs:{title:e.title+\"内容\",visible:e.dialogFormVisible,\"close-on-click-modal\":!1,width:\"70%\"},on:{\"update:visible\":function(t){e.dialogFormVisible=t}}},[t(\"el-form\",{ref:\"ruleForm\",attrs:{model:e.ruleForm,rules:e.rules}},[t(\"el-form-item\",{attrs:{label:e.title+\"标题\",\"label-width\":e.formLabelWidth,prop:\"title\"}},[t(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,\"title\",t)},expression:\"ruleForm.title\"}})],1),t(\"el-form-item\",{attrs:{label:\"描述\",\"label-width\":e.formLabelWidth}},[t(\"el-input\",{attrs:{autocomplete:\"off\",type:\"textarea\",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,\"desc\",t)},expression:\"ruleForm.desc\"}})],1),t(\"el-form-item\",{attrs:{label:\"权限\",\"label-width\":e.formLabelWidth}},[t(\"el-cascader\",{attrs:{options:e.options,props:e.props,clearable:\"\"},model:{value:e.ruleForm.quanxian,callback:function(t){e.$set(e.ruleForm,\"quanxian\",t)},expression:\"ruleForm.quanxian\"}})],1)],1),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(\"取 消\")]),t(\"el-button\",{attrs:{type:\"primary\"},on:{click:function(t){return e.saveData()}}},[e._v(\"确 定\")])],1)],1),t(\"el-dialog\",{attrs:{title:\"图片查看\",visible:e.dialogVisible,width:\"30%\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[t(\"el-image\",{attrs:{src:e.show_image}})],1)],1)},l=[function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"title-section\"},[t(\"h2\",{staticClass:\"page-title\"},[t(\"i\",{staticClass:\"el-icon-postcard\"}),e._v(\" 职位管理 \")]),t(\"p\",{staticClass:\"page-subtitle\"},[e._v(\"管理系统职位信息和权限配置\")])])}],i={name:\"list\",components:{},data(){return{viewMode:\"table\",props:{multiple:!0},options:{},allSize:\"mini\",list:[],total:5,page:1,size:12,search:{keyword:\"\",permission_level:\"\",status:\"\"},loading:!0,url:\"/zhiwei/\",title:\"职位\",info:{},dialogFormVisible:!1,show_image:\"\",dialogVisible:!1,ruleForm:{title:\"\",desc:\"\",quanxian:[],status:1},rules:{title:[{required:!0,message:\"请填写职位名称\",trigger:\"blur\"}],desc:[{required:!0,message:\"请填写职位描述\",trigger:\"blur\"}]},formLabelWidth:\"120px\"}},computed:{adminCount(){return this.list.filter(e=>e.quanxian&&(e.quanxian.includes(1)||e.quanxian.includes(13))).length},userCount(){return this.list.filter(e=>e.quanxian&&!e.quanxian.includes(1)&&!e.quanxian.includes(13)).length},activeCount(){return this.list.filter(e=>1===e.status).length}},mounted(){this.getData()},methods:{getPermissionLabels(e){if(!e||!Array.isArray(e))return[];const t={1:\"系统管理\",2:\"业务管理\",3:\"文书管理\",4:\"财务管理\",11:\"用户管理\",12:\"角色管理\",13:\"权限管理\",21:\"订单管理\",22:\"客户管理\",31:\"合同管理\",32:\"律师函管理\",33:\"课程管理\",41:\"支付管理\"};return e.map(e=>t[e]||\"权限\"+e).filter(Boolean)},getPermissionTagType(e){return e.includes(\"系统\")||e.includes(\"权限\")?\"danger\":e.includes(\"业务\")||e.includes(\"订单\")?\"primary\":e.includes(\"文书\")||e.includes(\"合同\")?\"success\":e.includes(\"财务\")||e.includes(\"支付\")?\"warning\":\"info\"},changeStatus(e){this.$message.success(`职位\"${e.title}\"状态已${e.status?\"启用\":\"禁用\"}`)},clearSearch(){this.search={keyword:\"\",permission_level:\"\",status:\"\"},this.searchData()},change(){},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:\"\",desc:\"\"},t.dialogFormVisible=!0,t.getQuanxians()},getQuanxians(){this.getPermissionsFromManagement()},getPermissionsFromManagement(){const e=[{value:1,label:\"系统管理\",children:[{value:11,label:\"用户管理\",children:[{value:111,label:\"查看用户\"},{value:112,label:\"新增用户\"},{value:113,label:\"编辑用户\"},{value:114,label:\"删除用户\"}]},{value:12,label:\"角色管理\",children:[{value:121,label:\"查看角色\"},{value:122,label:\"新增角色\"},{value:123,label:\"编辑角色\"},{value:124,label:\"删除角色\"}]},{value:13,label:\"权限管理\",children:[{value:131,label:\"查看权限\"},{value:132,label:\"新增权限\"},{value:133,label:\"编辑权限\"},{value:134,label:\"删除权限\"}]}]},{value:2,label:\"业务管理\",children:[{value:21,label:\"订单管理\",children:[{value:211,label:\"查看订单\"},{value:212,label:\"新增订单\"},{value:213,label:\"编辑订单\"},{value:214,label:\"删除订单\"},{value:215,label:\"导出订单\"}]},{value:22,label:\"客户管理\",children:[{value:221,label:\"查看客户\"},{value:222,label:\"新增客户\"},{value:223,label:\"编辑客户\"},{value:224,label:\"删除客户\"}]}]},{value:3,label:\"文书管理\",children:[{value:31,label:\"合同管理\",children:[{value:311,label:\"查看合同\"},{value:312,label:\"新增合同\"},{value:313,label:\"编辑合同\"},{value:314,label:\"删除合同\"},{value:315,label:\"审核合同\"}]},{value:32,label:\"律师函管理\",children:[{value:321,label:\"查看律师函\"},{value:322,label:\"发送律师函\"},{value:323,label:\"编辑律师函\"}]},{value:33,label:\"课程管理\",children:[{value:331,label:\"查看课程\"},{value:332,label:\"新增课程\"},{value:333,label:\"编辑课程\"},{value:334,label:\"删除课程\"}]}]},{value:4,label:\"财务管理\",children:[{value:41,label:\"支付管理\",children:[{value:411,label:\"查看支付记录\"},{value:412,label:\"处理退款\"},{value:413,label:\"导出财务报表\"}]}]}];this.options=e},getInfo(e){let t=this;t.getRequest(t.url+\"read?id=\"+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm(\"是否删除该信息?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"delete?id=\"+t).then(t=>{200==t.code?(this.$message({type:\"success\",message:\"删除成功!\"}),this.list.splice(e,1)):this.$message({type:\"error\",message:t.msg})})}).catch(()=>{this.$message({type:\"error\",message:\"取消删除!\"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,setTimeout(()=>{e.loading=!1,e.list=[{id:1,title:\"系统管理员\",desc:\"拥有系统所有权限，负责系统维护和用户管理\",quanxian:[1,2,3,4],status:1,level:\"超级管理员\",create_time:\"2024-01-01 10:00:00\"},{id:2,title:\"业务经理\",desc:\"负责业务流程管理，客户关系维护\",quanxian:[2,3],status:1,level:\"管理员\",create_time:\"2024-01-02 14:30:00\"},{id:3,title:\"法务专员\",desc:\"负责合同审核、律师函处理等法务工作\",quanxian:[3],status:1,level:\"普通用户\",create_time:\"2024-01-03 09:15:00\"},{id:4,title:\"财务专员\",desc:\"负责财务管理、支付处理、报表统计\",quanxian:[4],status:1,level:\"普通用户\",create_time:\"2024-01-04 11:20:00\"},{id:5,title:\"客服专员\",desc:\"负责客户咨询、问题处理、售后服务\",quanxian:[22],status:0,level:\"普通用户\",create_time:\"2024-01-05 16:45:00\"}],e.total=5},800)},saveData(){let e=this;this.$refs[\"ruleForm\"].validate(t=>{if(!t)return!1;this.postRequest(e.url+\"save\",this.ruleForm).then(t=>{200==t.code?(e.$message({type:\"success\",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:\"error\",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error(\"上传图片格式不对!\")},delImage(e,t){let a=this;a.getRequest(\"/Upload/delImage?fileName=\"+e).then(e=>{200==e.code?(a.ruleForm[t]=\"\",a.$message.success(\"删除成功!\")):a.$message.error(e.msg)})}}},n=i,r=(a(\"fd24\"),a(\"2877\")),c=Object(r[\"a\"])(n,s,l,!1,null,\"feecce72\",null);t[\"default\"]=c.exports},fd24:function(e,t,a){\"use strict\";a(\"4a94\")}}]);", "extractedComments": []}