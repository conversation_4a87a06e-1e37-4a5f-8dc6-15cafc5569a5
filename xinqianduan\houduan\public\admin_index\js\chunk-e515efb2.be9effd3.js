(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e515efb2"],{"23df":function(e,t,l){"use strict";l.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",[t("el-col",{attrs:{span:4}},[t("el-input",{attrs:{placeholder:"请输入订单号/购买人/套餐",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-col",{attrs:{span:3}},[t("el-select",{attrs:{placeholder:"请选择",size:e.allSize},model:{value:e.search.is_deal,callback:function(t){e.$set(e.search,"is_deal",t)},expression:"search.is_deal"}},e._l(e.options1,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v("搜索")])],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.clearData()}}},[e._v("重置")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"}},[t("el-table-column",{attrs:{prop:"order_sn",label:"工单号"}}),t("el-table-column",{attrs:{prop:"type",label:"工单类型"}}),t("el-table-column",{attrs:{prop:"title",label:"工单标题"}}),t("el-table-column",{attrs:{prop:"desc",label:"工单内容"}}),t("el-table-column",{attrs:{prop:"is_deal",label:"处理状态"}}),t("el-table-column",{attrs:{prop:"nickname",label:"用户名"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("div",{on:{click:function(t){return e.viewUserData(l.row.uid)}}},[e._v(e._s(l.row.nickname))])]}}])}),t("el-table-column",{attrs:{prop:"phone",label:"用户手机"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("div",{on:{click:function(t){return e.viewUserData(l.row.uid)}}},[e._v(e._s(l.row.phone))])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"发起时间"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(l.row.id)}}},[e._v("完成制作")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(l.$index,l.row.id)}}},[e._v(" 取消 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"合同标题","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",readonly:""},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"合同要求","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",readonly:"",type:"textarea",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"制作状态","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:2},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,"is_deal",t)},expression:"ruleForm.is_deal"}},[e._v("已完成")]),t("el-radio",{attrs:{label:1},model:{value:e.ruleForm.is_deal,callback:function(t){e.$set(e.ruleForm,"is_deal",t)},expression:"ruleForm.is_deal"}},[e._v("处理中")])],1)]),2==e.ruleForm.is_deal&&2==e.ruleForm.type?t("el-form-item",{attrs:{label:"请上传文件","label-width":e.formLabelWidth,prop:"file_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,"file_path",t)},expression:"ruleForm.file_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFile("file_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传 ")])],1),e.ruleForm.file_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,"file_path")}}},[e._v("删除")]):e._e()],1)],1):e._e(),2==e.ruleForm.is_deal&&2!=e.ruleForm.type?t("el-form-item",{attrs:{label:"内容回复","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,"content",t)},expression:"ruleForm.content"}})],1):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-dialog",{attrs:{title:"用户详情",visible:e.dialogViewUserDetail,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogViewUserDetail=t}}},[t("user-details",{attrs:{id:e.currentId}})],1)],1)},a=[],s=l("d522"),r={name:"list",components:{UserDetails:s["a"]},data(){return{allSize:"mini",list:[],total:1,currentId:0,page:1,size:20,search:{keyword:"",is_pay:-1,is_deal:-1},loading:!0,url:"/dingzhi/",title:"合同定制",info:{},dialogFormVisible:!1,dialogViewUserDetail:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],file_path:[{required:!0,message:"请上传文件",trigger:"blur"}]},formLabelWidth:"120px",options:[{id:-1,title:"请选择"},{id:1,title:"未支付"},{id:2,title:"已支付"},{id:3,title:"退款"}],options1:[{id:-1,title:"请选择"},{id:0,title:"待处理"},{id:1,title:"处理中"},{id:2,title:"已处理"}]}},mounted(){this.getData()},methods:{changeFile(e){this.filed=e,console.log(this.filed)},clearData(){this.search={keyword:"",is_pay:""},this.getData()},viewUserData(e){let t=this;0!=e&&(this.currentId=e),t.dialogViewUserDetail=!0},editData(e){0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""}},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{200==e.code?(t.ruleForm=e.data,t.dialogFormVisible=!0):t.$message({type:"error",message:e.msg})})},tuikuan(e){this.$confirm("是否申请退款?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"tuikuan?id="+e).then(e=>{200==e.code?this.$message({type:"success",message:e.msg}):this.$message({type:"error",message:e.msg})})}).catch(()=>{this.$message({type:"error",message:"取消退款!"})})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success("上传成功"),this.ruleForm[this.filed]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let l=this;l.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(l.ruleForm[t]="",l.$message.success("删除成功!")):l.$message.error(e.msg)})}}},o=r,n=(l("f44b"),l("2877")),c=Object(n["a"])(o,i,a,!1,null,"17b1ce6e",null);t["default"]=c.exports},"4a3e":function(e,t,l){},d522:function(e,t,l){"use strict";var i=function(){var e=this,t=e._self._c;return t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company))]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone))]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname))]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman))]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.info.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id))]),t("el-descriptions-item",{attrs:{label:"联系方式"}},[e._v(e._s(e.info.linkphone))]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"营业执照"}},[""!=e.info.license&&null!=e.info.license?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.license},on:{click:function(t){return e.showImage(e.info.license)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(e._s(e.info.start_time))]),t("el-descriptions-item",{attrs:{label:"会员年限"}},[e._v(e._s(e.info.year)+"年")])],1)],1)},a=[],s={name:"UserDetails",props:{id:{type:String,required:!0}},data(){return{info:[]}},watch:{id:{immediate:!0,handler(e){this.getInfo(e)}}},methods:{getInfo(e){let t=this;t.getRequest("/user/read?id="+e).then(e=>{e&&(t.info=e.data)})}}},r=s,o=l("2877"),n=Object(o["a"])(r,i,a,!1,null,null,null);t["a"]=n.exports},f44b:function(e,t,l){"use strict";l("4a3e")}}]);
//# sourceMappingURL=chunk-e515efb2.be9effd3.js.map