<?php
namespace app\admin\controller;
use think\Request;
use untils\JsonService;
use models\{<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>,Q<PERSON>,Qunliaos,Yuangongs,Gongdans,Notes,Zhiweis};

class Chat extends Base
{
    protected $model;
    //protected $url ="https://fdb.zhongfabang.cn";
	protected $url ="https://web.faduobang.com";
    public function __construct(Chats $model){
        parent::__construct();
        $this->model=$model;
        
    }


    public function sendMessage(Chats $Chats,Request $request){
		$post = $request->post();
	    	    if($post['type']=='text' || $post['type']=='voice'){
	        $post['content']=emoji_to_html($post['content']);
	    }
	  
	   if($post['type']=='file'){
		    $post['flies']=serialize($post['flies']);
		}
		$res = $Chats->save($post);
		if(!empty($res)) return JsonService::successful("1");
		else return JsonService::fail("没有数据");
	}
    public function sendQunMessage(Quns $quns,Qunliaos $Chats,Request $request,Notes $notes){
        $post = $request->post();
        $post['yuangong_id']=$this->userid;
      //  var_dump($post);die;
        if($post['type']=='file'){
		      $post['files']=serialize($post['files']);
		}
		    if($post['type']=='text' || $post['type']=='voice'){
	        $post['content']=emoji_to_html($post['content']);
	    }
		$res = $Chats->save($post);
		
	    $my_id = $Chats->id;
	    
	     $qus =$quns
	    ->where(['id'=>$post['qun_id']])
	    ->find();
        $user = $qus['uid'];
        
        $newList = [];
        foreach ($user as $k=>$vv){
            $newList[$k]['uid']=$vv[0];
            $newList[$k]['mag_id']=$my_id;
            $newList[$k]['qun_id']=$post['qun_id'];   
        }
          $notes->saveAll($newList);
  
        $yuangong = $qus['yuangong_id'];
        $yongList = [];
        foreach ($yuangong as $k=>$vv){
               if($vv[1] != $post['yuangong_id']){
            $yongList[$k]['yuangong_id']=$vv[1];
            $yongList[$k]['mag_id']=$my_id;
            $yongList[$k]['qun_id']=$post['qun_id'];  
               }   
        }
        $notes->saveAll($yongList);
		if(!empty($res)) return JsonService::successful("1");
		else return JsonService::fail("没有数据");
    }
    public function getMoreList(Chats $Chats,Request $request){
	    $post = $request->post();
	    $where[]=['orther_id','=',$this->userid];
	    $where[]=['id','>',$post['id']];
	    $where[]=['uid','=',$post['uid']];
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('headimg');
			}else{
				return $this->url.Lvshis::where('id',$d['orther_id'])->value('pic_path');
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time'])
		->where($where)
		->find();
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
	public function chatList(Chats $Chats,Request $request){
		$post = $request->post();
		$orther_id = Lvshis::where('id',$this->userid)->value('yuangong_id');
		if(empty($orther_id)) return JsonService::fail('fail');
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
				return $this->url.Lvshis::where('id',$d['orther_id'])->value('pic_path');
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
			    if($d['type']=='file'){
			        return $this->url.$v;
			    }
				return $v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('flies',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time'])
		->where(['orther_id'=>$orther_id])
		->where($post)
		->select();
		 $Chats::where($post)
		->where(['orther_id'=>$orther_id])
	    ->update(['is_read'=>1]);
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
	public function getMoreQunList(Request $request,Qunliaos $Chats,Notes $notes){
        $post = $request->post();
    
        $where[]=['qun_id','=',$post['qun_id']];
        $where[]=['id','>',$post['id']];
     
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return  $this->url.Users::where('id',$d['uid'])->value('headimg');
			}else{
			    if(!empty($d['lvshi_id'])) return $this->url.Lvshis::where('id',$d['lvshi_id'])->value('pic_path');
			    else return $this->url.Yuangongs::where('id',$d['yuangong_id'])->value('pic_path');
				
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
				return  $this->url.$v;
			}else{
				return preg_emoji($v);
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time'])
		->where($where)
	    ->order(['id'=>'desc'])
		->find();
		
		  $eres[]=['qun_id','=',$post['qun_id']];
	        $eres[]=['yuangong_id','=',$this->userid];
	     $note =  $notes->where($eres)->select();
	      
	        foreach ($note as $k=>$v){
	            $notes->destroy($v['id']); 
	        }
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
    }
    
    
    
      	public function getQunMoreInfo(Request $request,Quns $Quns){
	    $id = $request->post('id');
	    
	    $info = $Quns
	    ->where(['id'=>$id])
	  
	    ->find();
	   // $users = Users::where(['id'=>$info['uid']])->select();
	    $lvshis = Lvshis::withAttr('pic_path',function($value,$data){
			return $this->url.$value;
		})->where('id','in',$info['lvshi_id'])->select();
		$needDeal = [];
		foreach ($info['yuangong_id'] as $k=>$v){
		   	$needDeal[$v[0]][]=$v[1];
		}
		$userDeal = [];
			foreach ($info['uid'] as $k=>$v){
		   	$userDeal[$v[0]][]=$v[0];
		}

        $users = [];
	    foreach ($userDeal as $k=>$v){
	     //   $zhiwei = Zhiweis::where(['id'=>$k])->value('title');
	        $users[]=[
	           'list'=>Users::withAttr('headimg',function($value,$data){
        			return $this->url.$value;
        		})->where('id','in',$v)->select()
	        ];
	    }
	    //先获职位
	    $yuangongs = [];
	    foreach ($needDeal as $k=>$v){
	        $zhiwei = Zhiweis::where(['id'=>$k])->value('title');
	        $yuangongs[]=[
	           'zhiwei'=>$zhiwei,
	           'list'=>Yuangongs::withAttr('pic_path',function($value,$data){
        			return $this->url.$value;
        		})->where('id','in',$v)->select()
	        ];
	    }
	    if(empty($info)) return JsonService::fail('工作群组不存在');
	    return JsonService::successful('成功',compact('users','info','lvshis','yuangongs'));
	}
    
    
    
	public function qunliaoList(Qunliaos $Chats,Request $request ,Notes $notes){
		$post = $request->post();
		
		$res = $Chats::withAttr('avatar',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('headimg');
			}else{
			    if(!empty($d['lvshi_id'])) return Lvshis::where('id',$d['lvshi_id'])->value('pic_path');
			    else return Yuangongs::where('id',$d['yuangong_id'])->value('pic_path');
				
			}
		})->withAttr('title',function($v,$d){
			if($d['direction']=='right'){
				return Users::where('id',$d['uid'])->value('nickname');
			}else{
			    if(!empty($d['lvshi_id'])) return Lvshis::where('id',$d['lvshi_id'])->value('title');
			    else return Yuangongs::where('id',$d['yuangong_id'])->value('title');
				
			}
		})->withAttr('content',function($v,$d){
			if($d['type']!='text'){
			    if($d['type']=='file'){
			        return $this->url.$v;
			    }
				return $v;
			}else{
				return preg_emoji($v);;
			}
		})->withAttr('time',function($v,$d){
			return date('Y-m-d',$d['create_time']);
		})->withAttr('files',function($v,$d){
		     if(!empty($v)) return unserialize($v);
		})
		->append(['time','title'])
		->where($post)
		->select();
		$Chats::where($post);
		 $eres[]=['qun_id','=',$post['qun_id']];
	     $eres[]=['yuangong_id','=',$this->userid];
	     $note =  $notes->where($eres)->select();
	        foreach ($note as $k=>$v){
	            $notes->destroy($v['id']); 
	        }
	          
		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("没有数据");
	}
	public function chatAllList(Chats $Chats,Request $request){
		$post = $request->post();
		
		$orther_id = Lvshis::where('id',$this->userid)->value('yuangong_id');
		if(empty($orther_id)) return JsonService::fail('fail');
		$res = $Chats
		->distinct(true)
		->field('uid')
	    ->where(['orther_id'=>$orther_id])
		->select();
		$newList  = [];
		foreach ($res as $key => $value) {
		    $info = Users::where('id',$value['uid'])->find();
		    if(!empty($info)){
		        $newList[]= [
    			    'id'=>$info['id'],
    				'pic_path'=>$info['headimg'],
    				
    				'title'=>$info['nickname'],
    				'content'=>$Chats->where(['uid'=>$value['uid']])->order(['id'=>'desc'])->value('content'),
    				'time'=>date('Y-m-d',$Chats->where(['uid'=>$value['uid']])->order(['id'=>'desc'])->value('create_time')),
			
			    ];
		    }
		
		}
		$list = [];
		foreach($newList as $value){
		    $list[]=$value;
		}
		if(!empty($res)) return JsonService::successful("1",$list);
		else return JsonService::fail("没有数据");

	}
	
	public function getQun(Request $request){
	    $where =$request->post();
	  
	    $res = Quns::withAttr('count',function($v,$d){
    	         return Notes::where(['yuangong_id'=>$this->userid,'qun_id'=>$d['id']])->count();
		})->append(['count'])->where($where)->select();

	    $isres = false;
	    $newList = [];
	    foreach ($res as $kk=>$vv){
	        $yuangong = $vv['yuangong_id'];
	        foreach ($yuangong as $k=>$v){
    	        if($v[1]==$this->userid){
                    $vv['count'] = Chats::where(['orther_id'=>$vv,'is_read'=>0])->count();
    	            $newList[]= $vv;
    	        }
	        }
	    }
	    
	    
	    
	    
	    if(!empty($res)) return JsonService::successful("1",$newList);
		else return JsonService::fail("没有数据");
	}
	
	public function daiban(Request $request,Quns $model){
	   $id =  $request->post('id');
	   $is_daiban = $request->post('is_daiban');
	   if(empty($id)) return JsonService::fail('未查询到id');
	  
	   try {
	       $info = $model->find($id);
    	   $info->is_daiban = $is_daiban;
    	   $info->save();
    	   return JsonService::successful('备注成功');
	   } catch (\Exception $e) {
	      	return JsonService::fail("系统报错:".$e->getMessage());
	   }
	}
	
	public function gongdanList(Gongdans $model,Request $request){
	    $post = $request->post();
	    
	        $ps = [];
	       foreach ($post['uid'] as $k=>$vv){
            $ps[$k]=$vv[0];
        }  
   
        $pss  = implode(',',$ps);
        $int = intval($pss);
	    $map['uid'] = array($int,'or');
	    
	 //   var_dump($map['uid']);
	    if(empty($post['uid'])) return JsonService::fail('无数据');
		$res = $model::withAttr('file_path',function($value,$data){
			if(!empty($value)) return $this->url.$value;
			else return "";
		})->withAttr('type_title',function($v,$d){
			switch ($d['type']) {
				case '1':
					return '合同审核';
					break;
				
				case '2':
					return '合同定制';
					break;
				case '3':
					return '发律师函';
					break;
			}
		})->withAttr('is_deal_title',function($v,$d){
			if($d['is_deal']==1){
				return '处理中';
			}else{
				return '已处理';
			}
		})->append(['is_deal_title','type_title'])
		->where(['is_delete'=>0])
		->where($map)
		->select();

		if(!empty($res)) return JsonService::successful("1",$res);
		else return JsonService::fail("2");
	}
}