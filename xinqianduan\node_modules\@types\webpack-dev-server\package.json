{"name": "@types/webpack-dev-server", "version": "3.11.6", "description": "TypeScript definitions for webpack-dev-server", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-dev-server", "license": "MIT", "contributors": [{"name": "ma<PERSON><PERSON>h", "url": "https://github.com/maestroh", "githubUsername": "ma<PERSON><PERSON>h"}, {"name": "<PERSON>", "url": "https://github.com/daveparslow", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Zheyang<PERSON>ong", "githubUsername": "ZheyangSong"}, {"name": "<PERSON>", "url": "https://github.com/alan-agius4", "githubUsername": "alan-agius4"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/arturovt", "githubUsername": "arturovt"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dobogo", "githubUsername": "dobogo"}, {"name": "<PERSON>", "url": "https://github.com/billy-le", "githubUsername": "billy-le"}, {"name": "<PERSON>", "url": "https://github.com/chrispaterson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wwmoraes", "githubUsername": "ww<PERSON><PERSON>s"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-dev-server"}, "scripts": {}, "dependencies": {"@types/connect-history-api-fallback": "*", "@types/express": "*", "@types/serve-static": "*", "@types/webpack": "^4", "http-proxy-middleware": "^1.0.0"}, "typesPublisherContentHash": "b26879d2ad462235fdbf98cc2065b6df144275c80abeaee0a09d4409d5c110b2", "typeScriptVersion": "3.8"}