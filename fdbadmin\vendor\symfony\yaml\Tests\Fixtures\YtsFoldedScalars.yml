--- %YAML:1.0
test: Single ending newline
brief: >
    A pipe character, followed by an indented
    block of text is treated as a literal
    block, in which newlines are preserved
    throughout the block, including the final
    newline.
yaml: |
    ---
    this: |
        Foo
        Bar
php: |
    array('this' => "Foo\nBar\n")
---
test: The '+' indicator
brief: >
    The '+' indicator says to keep newlines at the end of text
    blocks.
yaml: |
    normal: |
      extra new lines not kept

    preserving: |+
      extra new lines are kept


    dummy: value
php: |
    array(
        'normal' => "extra new lines not kept\n",
        'preserving' => "extra new lines are kept\n\n\n",
        'dummy' => 'value'
    )
---
test: Three trailing newlines in literals
brief: >
    To give you more control over how space
    is preserved in text blocks, YAML has
    the keep '+' and chomp '-' indicators.
    The keep indicator will preserve all
    ending newlines, while the chomp indicator
    will strip all ending newlines.
yaml: |
    clipped: |
        This has one newline.



    same as "clipped" above: "This has one newline.\n"

    stripped: |-
        This has no newline.



    same as "stripped" above: "This has no newline."

    kept: |+
        This has four newlines.



    same as "kept" above: "This has four newlines.\n\n\n\n"
php: |
    array(
      'clipped' => "This has one newline.\n",
      'same as "clipped" above' => "This has one newline.\n",
      'stripped' => 'This has no newline.',
      'same as "stripped" above' => 'This has no newline.',
      'kept' => "This has four newlines.\n\n\n\n",
      'same as "kept" above' => "This has four newlines.\n\n\n\n"
    )
---
test: Extra trailing newlines with spaces
todo: true
brief: >
    Normally, only a single newline is kept
    from the end of a literal block, unless the
    keep '+' character is used in combination
    with the pipe.  The following example
    will preserve all ending whitespace
    since the last line of both literal blocks
    contains spaces which extend past the indentation
    level.
yaml: |
    ---
    this: |
        Foo


    kept: |+
        Foo


php: |
    array('this' => "Foo\n\n  \n",
      'kept' => "Foo\n\n  \n" )

---
test: Folded Block in a Sequence
brief: >
    A greater-then character, followed by an indented
    block of text is treated as a folded block, in
    which lines of text separated by a single newline
    are concatenated as a single line.
yaml: |
    ---
    - apple
    - banana
    - >
        can't you see
        the beauty of yaml?
        hmm
    - dog
php: |
    array(
        'apple',
        'banana',
        "can't you see the beauty of yaml? hmm\n",
        'dog'
    )
---
test: Folded Block as a Mapping Value
brief: >
    Both literal and folded blocks can be
    used in collections, as values in a
    sequence or a mapping.
yaml: |
    ---
    quote: >
        Mark McGwire's
        year was crippled
        by a knee injury.
    source: espn
php: |
    array(
        'quote' => "Mark McGwire's year was crippled by a knee injury.\n",
        'source' => 'espn'
    )
---
test: Three trailing newlines in folded blocks
brief: >
    The keep and chomp indicators can also
    be applied to folded blocks.
yaml: |
    clipped: >
        This has one newline.



    same as "clipped" above: "This has one newline.\n"

    stripped: >-
        This has no newline.



    same as "stripped" above: "This has no newline."

    kept: >+
        This has four newlines.



    same as "kept" above: "This has four newlines.\n\n\n\n"
php: |
    array(
      'clipped' => "This has one newline.\n",
      'same as "clipped" above' => "This has one newline.\n",
      'stripped' => 'This has no newline.',
      'same as "stripped" above' => 'This has no newline.',
      'kept' => "This has four newlines.\n\n\n\n",
      'same as "kept" above' => "This has four newlines.\n\n\n\n"
    )
