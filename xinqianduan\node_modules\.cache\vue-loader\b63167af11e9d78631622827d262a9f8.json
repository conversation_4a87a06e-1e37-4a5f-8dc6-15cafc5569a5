{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1748608840004}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["user.vue"], "names": [], "mappings": ";AA40CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "user.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n    <div class=\"page-wrapper\">\r\n        <div class=\"page-container\">\r\n            <!-- 页面标题 -->\r\n            <div class=\"page-title\">\r\n                {{ this.$router.currentRoute.name }}\r\n                <el-button\r\n                        style=\"float: right\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                        icon=\"el-icon-refresh\"\r\n                >刷新\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 搜索筛选区域 -->\r\n            <div class=\"search-container\">\r\n                <el-form :model=\"search\" class=\"search-form\" label-width=\"80px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户名称\">\r\n                                <el-input\r\n                                    v-model=\"search.nickname\"\r\n                                    placeholder=\"请输入用户名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"手机号码\">\r\n                                <el-input\r\n                                    v-model=\"search.phone\"\r\n                                    placeholder=\"请输入手机号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"公司名称\">\r\n                                <el-input\r\n                                    v-model=\"search.company\"\r\n                                    placeholder=\"请输入公司名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户来源\">\r\n                                <el-select\r\n                                    v-model=\"search.yuangong_id\"\r\n                                    placeholder=\"请选择来源\"\r\n                                    clearable\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                    <el-option label=\"小程序注册\" value=\"小程序注册\"></el-option>\r\n                                    <el-option label=\"后台创建\" value=\"后台创建\"></el-option>\r\n                                    <el-option label=\"直接注册\" value=\"直接注册\"></el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系人\">\r\n                                <el-input\r\n                                    v-model=\"search.linkman\"\r\n                                    placeholder=\"请输入联系人\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系号码\">\r\n                                <el-input\r\n                                    v-model=\"search.linkphone\"\r\n                                    placeholder=\"请输入联系号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                            <el-form-item label=\"注册时间\">\r\n                                <el-date-picker\r\n                                    v-model=\"search.dateRange\"\r\n                                    type=\"daterange\"\r\n                                    range-separator=\"至\"\r\n                                    start-placeholder=\"开始日期\"\r\n                                    end-placeholder=\"结束日期\"\r\n                                    format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                            <el-form-item>\r\n                                <div class=\"search-buttons\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        icon=\"el-icon-search\"\r\n                                        @click=\"searchData()\"\r\n                                        size=\"small\">\r\n                                        搜索\r\n                                    </el-button>\r\n                                    <el-button\r\n                                        icon=\"el-icon-refresh\"\r\n                                        @click=\"resetSearch()\"\r\n                                        size=\"small\">\r\n                                        重置\r\n                                    </el-button>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n\r\n                <!-- 操作按钮区域 -->\r\n                <div class=\"action-buttons\">\r\n                    <el-button\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        icon=\"el-icon-download\"\r\n                        @click=\"exportSelectedData\">\r\n                        {{ selectedUsers.length > 0 ? `导出选中数据 (${selectedUsers.length})` : '导出全部数据' }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\" @click=\"openUpload\">\r\n                        导入用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addUser\">\r\n                        添加用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 数据表格 -->\r\n            <div class=\"data-table\">\r\n                <el-table\r\n                        :data=\"list\"\r\n                        style=\"width: 100%\"\r\n                        v-loading=\"loading\"\r\n                        @sort-change=\"handleSortChange\"\r\n                        @selection-change=\"handleSelectionChange\"\r\n                        :border=\"true\"\r\n                        :header-cell-style=\"{background:'#fafafa',color:'#606266'}\"\r\n                        ref=\"userTable\"\r\n                >\r\n                    <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n                    <el-table-column prop=\"\" label=\"头像\" width=\"80\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"avatar-container\">\r\n                                <div v-if=\"scope.row.headimg && scope.row.headimg !== ''\" class=\"avatar-wrapper\">\r\n                                    <img\r\n                                        class=\"user-avatar\"\r\n                                        :src=\"scope.row.headimg\"\r\n                                        @click=\"showImage(scope.row.headimg)\"\r\n                                    />\r\n                                </div>\r\n                                <div v-else class=\"no-avatar\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nickname\" label=\"用户名称\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"user-info\">\r\n                                <div class=\"user-name clickable\" @click=\"viewData(scope.row.id)\">{{ scope.row.nickname || '未设置' }}</div>\r\n                                <div class=\"user-phone\">{{ scope.row.phone }}</div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"company\" label=\"公司名称\" sortable min-width=\"150\"></el-table-column>\r\n                    <el-table-column prop=\"linkman\" label=\"联系人\" sortable min-width=\"100\"></el-table-column>\r\n                    <el-table-column prop=\"linkphone\" label=\"联系号码\" sortable min-width=\"120\"></el-table-column>\r\n                    <el-table-column prop=\"yuangong_id\" label=\"用户来源\" min-width=\"100\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ scope.row.yuangong_id || '直接注册' }}</el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务人数量\" min-width=\"100\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" :type=\"getDebtCountType(scope.row.debts)\">\r\n                                {{ getDebtCount(scope.row.debts) }}人\r\n                            </el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务总金额\" min-width=\"120\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"debt-amount\" :class=\"{ 'has-debt': getTotalDebtAmount(scope.row.debts) > 0 }\">\r\n                                ¥{{ formatAmount(getTotalDebtAmount(scope.row.debts)) }}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"end_time\" label=\"到期时间\" min-width=\"120\" sortable></el-table-column>\r\n                    <el-table-column prop=\"create_time\" label=\"注册时间\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.create_time }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最后登录\" min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.last_login_time || '未登录' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"action-buttons-table\">\r\n                                <el-button type=\"primary\" size=\"mini\" @click=\"viewData(scope.row.id)\">\r\n                                    查看\r\n                                </el-button>\r\n                                <el-button type=\"success\" size=\"mini\" @click=\"editData(scope.row.id)\">\r\n                                    编辑\r\n                                </el-button>\r\n                                <el-dropdown trigger=\"click\">\r\n                                    <el-button size=\"mini\">\r\n                                        更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu slot=\"dropdown\">\r\n                                        <el-dropdown-item @click.native=\"order(scope.row)\">制作订单</el-dropdown-item>\r\n                                        <el-dropdown-item v-if=\"is_del\" @click.native=\"delData(scope.$index, scope.row.id)\" style=\"color: #f56c6c;\">移除用户</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n                <div class=\"pagination-info\">\r\n                    <span>共 {{ total }} 条</span>\r\n                    <span>第 {{ page }} 页</span>\r\n                </div>\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"size\"\r\n                        :current-page=\"page\"\r\n                        layout=\"sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                        background\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 用户详情抽屉 -->\r\n        <el-drawer\r\n            title=\"用户详情\"\r\n            :visible.sync=\"drawerViewVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleTabSelect\">\r\n                        <el-menu-item index=\"customer\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>客户信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"member\">\r\n                            <i class=\"el-icon-medal\"></i>\r\n                            <span>会员信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"debts\">\r\n                            <i class=\"el-icon-document\"></i>\r\n                            <span>债务人信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"attachments\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>附件信息</span>\r\n                        </el-menu-item>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 编辑模式切换按钮 -->\r\n                    <div class=\"edit-mode-toggle\" v-if=\"activeTab === 'customer'\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            :icon=\"isEditMode ? 'el-icon-view' : 'el-icon-edit'\"\r\n                            @click=\"toggleEditMode\">\r\n                            {{ isEditMode ? '查看模式' : '编辑模式' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"success\"\r\n                            icon=\"el-icon-check\"\r\n                            @click=\"saveUserData\">\r\n                            保存\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"info\"\r\n                            icon=\"el-icon-close\"\r\n                            @click=\"cancelEdit\">\r\n                            取消\r\n                        </el-button>\r\n                    </div>\r\n\r\n                    <!-- 客户信息标签页 -->\r\n                    <div v-if=\"activeTab === 'customer'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                客户信息\r\n                            </div>\r\n                    <!-- 查看模式 -->\r\n                    <el-descriptions v-if=\"!isEditMode\" :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            currentUserInfo.company || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            currentUserInfo.phone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户名称\">{{\r\n                            currentUserInfo.nickname || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            currentUserInfo.linkman || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系电话\">{{\r\n                            currentUserInfo.linkphone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ currentUserInfo.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"立案专员\">{{\r\n                            currentUserInfo.lian_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"调解员\">{{\r\n                            currentUserInfo.tiaojie_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"法务专员\">{{\r\n                            currentUserInfo.fawu_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"合同上传专员\">{{\r\n                            currentUserInfo.htsczy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"律师\">{{\r\n                            currentUserInfo.ls_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"业务员\">{{\r\n                            currentUserInfo.ywy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"currentUserInfo.headimg && currentUserInfo.headimg !== ''\"\r\n                                     :src=\"currentUserInfo.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(currentUserInfo.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"注册时间\" :span=\"2\">{{\r\n                            currentUserInfo.create_time || '未知'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"最后登录\" :span=\"2\">{{\r\n                            currentUserInfo.last_login_time || '从未登录'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"会员到期\" :span=\"2\">{{\r\n                            currentUserInfo.end_time || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n\r\n                    <!-- 编辑模式 -->\r\n                    <el-form v-if=\"isEditMode\" :model=\"editForm\" :rules=\"rules\" ref=\"editForm\" label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"公司名称\" prop=\"company\">\r\n                                    <el-input v-model=\"editForm.company\" placeholder=\"请输入公司名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"手机号\" prop=\"phone\">\r\n                                    <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号\" disabled></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"用户名称\" prop=\"nickname\">\r\n                                    <el-input v-model=\"editForm.nickname\" placeholder=\"请输入用户名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                                    <el-input v-model=\"editForm.linkman\" placeholder=\"请输入联系人\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"linkphone\">\r\n                                    <el-input v-model=\"editForm.linkphone\" placeholder=\"请输入联系电话\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"登录密码\" prop=\"password\">\r\n                                    <el-input v-model=\"editForm.password\" placeholder=\"请输入新密码（留空不修改）\" type=\"password\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"调解员\" prop=\"tiaojie_id\">\r\n                                    <el-select v-model=\"editForm.tiaojie_id\" placeholder=\"请选择调解员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in tiaojies\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"法务专员\" prop=\"fawu_id\">\r\n                                    <el-select v-model=\"editForm.fawu_id\" placeholder=\"请选择法务专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in fawus\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"立案专员\" prop=\"lian_id\">\r\n                                    <el-select v-model=\"editForm.lian_id\" placeholder=\"请选择立案专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in lians\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\">\r\n                                    <el-select v-model=\"editForm.htsczy_id\" placeholder=\"请选择合同上传专用\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in htsczy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"律师\" prop=\"ls_id\">\r\n                                    <el-select v-model=\"editForm.ls_id\" placeholder=\"请选择律师\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ls\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"业务员\" prop=\"ywy_id\">\r\n                                    <el-select v-model=\"editForm.ywy_id\" placeholder=\"请选择业务员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ywy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"开始时间\" prop=\"start_time\">\r\n                                    <el-date-picker\r\n                                        v-model=\"editForm.start_time\"\r\n                                        type=\"date\"\r\n                                        format=\"yyyy-MM-dd\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        placeholder=\"选择开始时间\">\r\n                                    </el-date-picker>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"会员年限\" prop=\"year\">\r\n                                    <el-input-number\r\n                                        v-model=\"editForm.year\"\r\n                                        :min=\"0\"\r\n                                        :max=\"99\"\r\n                                        placeholder=\"请输入会员年限\">\r\n                                    </el-input-number>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-form-item label=\"头像\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"editForm.headimg && editForm.headimg !== ''\"\r\n                                     :src=\"editForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(editForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 会员信息标签页 -->\r\n                    <div v-if=\"activeTab === 'member'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-medal\"></i>\r\n                                会员信息\r\n                            </div>\r\n                            <el-descriptions :column=\"2\" border>\r\n                                <el-descriptions-item label=\"开始时间\">{{\r\n                                    currentUserInfo.start_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员年限\">{{\r\n                                    currentUserInfo.year || 0\r\n                                    }}年\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"注册时间\">{{\r\n                                    currentUserInfo.create_time || '未知'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"最后登录\">{{\r\n                                    currentUserInfo.last_login_time || '从未登录'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员到期\">{{\r\n                                    currentUserInfo.end_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 债务人信息标签页 -->\r\n                    <div v-if=\"activeTab === 'debts'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                债务人信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addDebt\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加债务人\r\n                                </el-button>\r\n                            </div>\r\n                            <el-table\r\n                                :data=\"currentUserInfo.debts || []\"\r\n                                style=\"width: 100%\"\r\n                                size=\"small\"\r\n                                v-if=\"currentUserInfo.debts && currentUserInfo.debts.length > 0\">\r\n                                <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"130\"></el-table-column>\r\n                                <el-table-column prop=\"money\" label=\"债务金额（元）\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-tag :type=\"scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info'\" size=\"small\">\r\n                                            {{ scope.row.status }}\r\n                                        </el-tag>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column label=\"操作\" width=\"150\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" size=\"mini\" @click=\"editDebt(scope.row, scope.$index)\">编辑</el-button>\r\n                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteDebt(scope.$index)\">删除</el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                            <div v-else class=\"no-data\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>暂无债务人信息</span>\r\n                                <br>\r\n                                <el-button type=\"primary\" size=\"small\" @click=\"addDebt\" style=\"margin-top: 10px;\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加第一个债务人\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 附件信息标签页 -->\r\n                    <div v-if=\"activeTab === 'attachments'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder-opened\"></i>\r\n                                附件信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addAttachment\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传附件\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"attachment-grid\">\r\n                                <!-- 身份证照片 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">身份证照片</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.idCard && currentUserInfo.attachments.idCard.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.idCard\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('idCard', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-picture\"></i>\r\n                                            <span>暂无身份证照片</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadIdCard\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传身份证\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 营业执照 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">营业执照</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.license && currentUserInfo.attachments.license.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.license\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('license', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-document\"></i>\r\n                                            <span>暂无营业执照</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadLicense\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传营业执照\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 其他附件 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">其他附件</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.others && currentUserInfo.attachments.others.length > 0\" class=\"file-list\">\r\n                                            <div v-for=\"(file, index) in currentUserInfo.attachments.others\" :key=\"index\" class=\"file-item\">\r\n                                                <div class=\"file-icon\">\r\n                                                    <i :class=\"getFileIcon(file.type)\" class=\"file-type-icon\"></i>\r\n                                                </div>\r\n                                                <div class=\"file-info\">\r\n                                                    <div class=\"file-name\">{{ file.name }}</div>\r\n                                                    <div class=\"file-meta\">\r\n                                                        <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                                                        <span class=\"upload-time\">{{ file.uploadTime }}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div class=\"file-actions\">\r\n                                                    <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(file)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                    <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('others', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-folder\"></i>\r\n                                            <span>暂无其他附件</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadOthers\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传其他附件\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n\r\n        <!-- 编辑用户抽屉 -->\r\n        <el-drawer\r\n            title=\"编辑用户\"\r\n            :visible.sync=\"drawerEditVisible\"\r\n            direction=\"rtl\"\r\n            size=\"50%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content\">\r\n                <!-- 客户信息展示 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                        客户信息\r\n                    </div>\r\n                    <el-descriptions :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            ruleForm.company\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            ruleForm.phone\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"名称\">{{\r\n                            ruleForm.nickname\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            ruleForm.linkman\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"ruleForm.headimg && ruleForm.headimg !== ''\"\r\n                                     :src=\"ruleForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(ruleForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\" :span=\"2\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ ruleForm.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 信息编辑表单 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-edit\"></i>\r\n                        信息编辑\r\n                    </div>\r\n                    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n                    </el-form>\r\n\r\n                    <!-- 保存按钮 -->\r\n                    <div class=\"drawer-footer\">\r\n                        <el-button @click=\"drawerEditVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveData()\">保 存</el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog :visible.sync=\"dialogVisible\" width=\"50%\" center>\r\n            <img :src=\"show_image\" style=\"width: 100%; height: auto;\" />\r\n        </el-dialog>\r\n\r\n        <!-- 债务人表单对话框 -->\r\n        <el-dialog :title=\"debtDialogTitle\" :visible.sync=\"debtDialogVisible\" width=\"500px\" @close=\"closeDebtDialog\">\r\n            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"100px\">\r\n                <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                    <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                    <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务金额\" prop=\"money\">\r\n                    <el-input-number\r\n                        v-model=\"debtForm.money\"\r\n                        :min=\"0\"\r\n                        :precision=\"2\"\r\n                        placeholder=\"请输入债务金额\"\r\n                        style=\"width: 100%\">\r\n                    </el-input-number>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                        <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                        <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                        <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeDebtDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveDebt\">确定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 导入用户对话框 -->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"600px\" @close=\"closeUploadDialog\">\r\n            <div class=\"upload-container\">\r\n                <div class=\"upload-tips\">\r\n                    <el-alert\r\n                        title=\"导入说明\"\r\n                        type=\"info\"\r\n                        :closable=\"false\"\r\n                        show-icon>\r\n                        <div slot=\"description\">\r\n                            <p>1. 请先下载导入模板，按照模板格式填写用户信息</p>\r\n                            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n                            <p>3. 单次最多导入1000条用户数据</p>\r\n                            <p>4. 手机号码为必填项，且不能重复</p>\r\n                        </div>\r\n                    </el-alert>\r\n                </div>\r\n\r\n                <div class=\"upload-actions\">\r\n                    <el-button type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n\r\n                <div class=\"upload-area\">\r\n                    <el-upload\r\n                        ref=\"upload\"\r\n                        :action=\"uploadAction\"\r\n                        :data=\"uploadData\"\r\n                        :on-success=\"handleUploadSuccess\"\r\n                        :on-error=\"handleUploadError\"\r\n                        :before-upload=\"beforeUpload\"\r\n                        :auto-upload=\"false\"\r\n                        :limit=\"1\"\r\n                        :file-list=\"fileList\"\r\n                        accept=\".xls,.xlsx\"\r\n                        drag>\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n                        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xls/xlsx文件，且不超过10MB</div>\r\n                    </el-upload>\r\n                </div>\r\n\r\n                <div class=\"upload-options\">\r\n                    <el-checkbox v-model=\"uploadData.review\">导入前预览数据</el-checkbox>\r\n                </div>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeUploadDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">\r\n                    {{ submitOrderLoading2 ? '导入中...' : '开始导入' }}\r\n                </el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                fileList: [], // 上传文件列表\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                currentUserInfo: {},\r\n                search: {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                selectedUsers: [], // 选中的用户列表\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                drawerViewVisible: false,\r\n                drawerEditVisible: false,\r\n                isEditMode: false,\r\n                editForm: {},\r\n                originalUserInfo: {},\r\n                activeTab: 'customer',\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                // 债务人表单相关\r\n                debtDialogVisible: false,\r\n                debtDialogTitle: '添加债务人',\r\n                isEditingDebt: false,\r\n                editingDebtIndex: -1,\r\n                debtForm: {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                },\r\n                debtRules: {\r\n                    name: [\r\n                        { required: true, message: '请输入债务人姓名', trigger: 'blur' }\r\n                    ],\r\n                    tel: [\r\n                        { required: true, message: '请输入债务人电话', trigger: 'blur' },\r\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n                    ],\r\n                    money: [\r\n                        { required: true, message: '请输入债务金额', trigger: 'blur' }\r\n                    ],\r\n                    status: [\r\n                        { required: true, message: '请选择状态', trigger: 'change' }\r\n                    ]\r\n                },\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n            };\r\n        },\r\n        mounted() {\r\n            // 使用测试数据，注释掉API调用\r\n            // this.getData();\r\n            // 添加测试数据\r\n            this.addTestData();\r\n        },\r\n        methods: {\r\n            // 获取原始测试数据\r\n            getOriginalTestData() {\r\n                return [\r\n                    {\r\n                        id: 1,\r\n                        phone: '13800138001',\r\n                        nickname: '张三',\r\n                        company: '北京科技有限公司',\r\n                        linkman: '张三',\r\n                        linkphone: '13800138001',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-12-31',\r\n                        create_time: '2023-01-15 10:30:00',\r\n                        last_login_time: '2024-01-20 15:45:00',\r\n                        headimg: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n                        license: '',\r\n                        start_time: '2023-01-15',\r\n                        year: 2,\r\n                        // 专员信息\r\n                        lian_name: '陈专员',\r\n                        tiaojie_name: '朱调解',\r\n                        fawu_name: '严法务',\r\n                        htsczy_name: '合同专员',\r\n                        ls_name: '王律师',\r\n                        ywy_name: '业务员张三',\r\n                        debts: [\r\n                            {\r\n                                name: '王某某',\r\n                                tel: '13912345678',\r\n                                money: '50000',\r\n                                status: '处理中'\r\n                            },\r\n                            {\r\n                                name: '李某某',\r\n                                tel: '13987654321',\r\n                                money: '30000',\r\n                                status: '已完成'\r\n                            }\r\n                        ],\r\n                        attachments: {\r\n                            idCard: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '身份证正面.jpg'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '身份证反面.jpg'\r\n                                }\r\n                            ],\r\n                            license: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                                    name: '营业执照.jpg'\r\n                                }\r\n                            ],\r\n                            others: [\r\n                                {\r\n                                    name: '合同文件.pdf',\r\n                                    url: '/files/contract.pdf',\r\n                                    type: 'pdf'\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        phone: '13900139002',\r\n                        nickname: '李四',\r\n                        company: '上海贸易公司',\r\n                        linkman: '李四',\r\n                        linkphone: '13900139002',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-06-30',\r\n                        create_time: '2023-02-20 14:20:00',\r\n                        last_login_time: '2024-01-18 09:15:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-02-20',\r\n                        year: 1,\r\n                        // 专员信息\r\n                        lian_name: '李专员',\r\n                        tiaojie_name: '调解员王五',\r\n                        fawu_name: '法务李四',\r\n                        htsczy_name: '合同专员B',\r\n                        ls_name: '律师张三',\r\n                        ywy_name: '业务员李四',\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13811112222',\r\n                                money: '80000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        phone: '13700137003',\r\n                        nickname: '王五',\r\n                        company: '深圳创新科技',\r\n                        linkman: '王五',\r\n                        linkphone: '13700137003',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2025-03-15',\r\n                        create_time: '2023-03-10 16:40:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                        license: '',\r\n                        start_time: '2023-03-10',\r\n                        year: 2,\r\n                        debts: [\r\n                            {\r\n                                name: '陈某某',\r\n                                tel: '13765432109',\r\n                                money: '80000',\r\n                                status: '待处理'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 4,\r\n                        phone: '13600136004',\r\n                        nickname: '赵六',\r\n                        company: '广州物流集团',\r\n                        linkman: '赵六',\r\n                        linkphone: '13600136004',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-09-20',\r\n                        create_time: '2023-04-05 11:30:00',\r\n                        last_login_time: '2024-01-19 14:22:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-04-05',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 5,\r\n                        phone: '13500135005',\r\n                        nickname: '孙七',\r\n                        company: '杭州电商有限公司',\r\n                        linkman: '孙七',\r\n                        linkphone: '13500135005',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-11-10',\r\n                        create_time: '2023-05-12 09:15:00',\r\n                        last_login_time: '2024-01-21 16:30:00',\r\n                        headimg: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                        license: '',\r\n                        start_time: '2023-05-12',\r\n                        year: 1,\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13654321098',\r\n                                money: '25000',\r\n                                status: '已完成'\r\n                            },\r\n                            {\r\n                                name: '钱某某',\r\n                                tel: '13543210987',\r\n                                money: '15000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 6,\r\n                        phone: '13400134006',\r\n                        nickname: '周八',\r\n                        company: '成都软件开发',\r\n                        linkman: '周八',\r\n                        linkphone: '13400134006',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-08-15',\r\n                        create_time: '2023-06-18 13:25:00',\r\n                        last_login_time: '2024-01-22 10:12:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-06-18',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 7,\r\n                        phone: '13300133007',\r\n                        nickname: '吴九',\r\n                        company: '武汉贸易有限公司',\r\n                        linkman: '吴九',\r\n                        linkphone: '13300133007',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-10-30',\r\n                        create_time: '2023-07-22 15:45:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                        license: '',\r\n                        start_time: '2023-07-22',\r\n                        year: 1,\r\n                        debts: []\r\n                    }\r\n                ];\r\n            },\r\n            addTestData() {\r\n                // 添加测试数据\r\n                this.list = this.getOriginalTestData();\r\n                this.total = this.list.length;\r\n                this.loading = false;\r\n            },\r\n            // 过滤测试数据（模拟搜索功能）\r\n            filterTestData() {\r\n                this.loading = true;\r\n\r\n                // 获取原始测试数据\r\n                const originalData = this.getOriginalTestData();\r\n                let filteredData = [...originalData];\r\n\r\n                // 根据搜索条件过滤数据\r\n                if (this.search.nickname) {\r\n                    const nickname = this.search.nickname.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.nickname && user.nickname.toLowerCase().includes(nickname)\r\n                    );\r\n                }\r\n\r\n                if (this.search.phone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.phone && user.phone.includes(this.search.phone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkman) {\r\n                    const linkman = this.search.linkman.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkman && user.linkman.toLowerCase().includes(linkman)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkphone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkphone && user.linkphone.includes(this.search.linkphone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.company) {\r\n                    const company = this.search.company.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.company && user.company.toLowerCase().includes(company)\r\n                    );\r\n                }\r\n\r\n                if (this.search.yuangong_id) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.yuangong_id === this.search.yuangong_id\r\n                    );\r\n                }\r\n\r\n                // 注册时间范围过滤\r\n                if (this.search.dateRange && this.search.dateRange.length === 2) {\r\n                    const startDate = new Date(this.search.dateRange[0]);\r\n                    const endDate = new Date(this.search.dateRange[1]);\r\n                    filteredData = filteredData.filter(user => {\r\n                        if (user.create_time) {\r\n                            const createDate = new Date(user.create_time.split(' ')[0]);\r\n                            return createDate >= startDate && createDate <= endDate;\r\n                        }\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                // 更新列表和总数\r\n                this.list = filteredData;\r\n                this.total = filteredData.length;\r\n                this.loading = false;\r\n\r\n                // 显示搜索结果提示\r\n                const hasSearchCondition = this.search.nickname || this.search.phone || this.search.linkman ||\r\n                                         this.search.linkphone || this.search.company || this.search.yuangong_id ||\r\n                                         (this.search.dateRange && this.search.dateRange.length === 2);\r\n\r\n                if (hasSearchCondition) {\r\n                    this.$message.success(`搜索完成，找到 ${filteredData.length} 条匹配记录`);\r\n                }\r\n            },\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                console.log('viewData called with id:', id);\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                    // 从测试数据中找到对应的用户信息\r\n                    this.currentUserInfo = this.list.find(user => user.id === id) || {};\r\n                    console.log('Found user info:', this.currentUserInfo);\r\n                    // 重置编辑模式\r\n                    this.isEditMode = false;\r\n                    this.editForm = {};\r\n                    this.originalUserInfo = {};\r\n                    // 重置到客户信息标签页\r\n                    this.activeTab = 'customer';\r\n                }\r\n                _this.drawerViewVisible = true;\r\n                console.log('Drawer should be visible:', _this.drawerViewVisible);\r\n            },\r\n            handleTabSelect(key) {\r\n                this.activeTab = key;\r\n                // 如果切换到其他标签页，退出编辑模式\r\n                if (key !== 'customer') {\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            toggleEditMode() {\r\n                if (!this.isEditMode) {\r\n                    // 进入编辑模式\r\n                    this.isEditMode = true;\r\n                    // 保存原始数据用于取消时恢复\r\n                    this.originalUserInfo = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 复制当前用户信息到编辑表单\r\n                    this.editForm = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 获取员工数据用于下拉选择\r\n                    this.getYuangongs();\r\n                } else {\r\n                    // 退出编辑模式\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            cancelEdit() {\r\n                // 恢复原始数据\r\n                this.currentUserInfo = JSON.parse(JSON.stringify(this.originalUserInfo));\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.$message.info('已取消编辑');\r\n            },\r\n            saveUserData() {\r\n                // 验证表单\r\n                this.$refs.editForm.validate((valid) => {\r\n                    if (valid) {\r\n                        // 更新当前用户信息\r\n                        this.currentUserInfo = JSON.parse(JSON.stringify(this.editForm));\r\n\r\n                        // 更新列表中的数据\r\n                        const index = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1, this.currentUserInfo);\r\n                        }\r\n\r\n                        // 退出编辑模式\r\n                        this.isEditMode = false;\r\n                        this.editForm = {};\r\n\r\n                        this.$message.success('保存成功！');\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n                _this.drawerEditVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            handleDrawerClose() {\r\n                this.drawerViewVisible = false;\r\n                this.drawerEditVisible = false;\r\n                // 重置编辑模式\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.originalUserInfo = {};\r\n                // 重置标签页\r\n                this.activeTab = 'customer';\r\n            },\r\n            // 债务人管理方法\r\n            addDebt() {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.isEditingDebt = false;\r\n            },\r\n            editDebt(debt, index) {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: debt.name,\r\n                    tel: debt.tel,\r\n                    money: parseFloat(debt.money),\r\n                    status: debt.status\r\n                };\r\n                this.debtDialogTitle = '编辑债务人';\r\n                this.isEditingDebt = true;\r\n                this.editingDebtIndex = index;\r\n            },\r\n            saveDebt() {\r\n                this.$refs.debtForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const debtData = {\r\n                            name: this.debtForm.name,\r\n                            tel: this.debtForm.tel,\r\n                            money: this.debtForm.money.toString(),\r\n                            status: this.debtForm.status\r\n                        };\r\n\r\n                        if (this.isEditingDebt) {\r\n                            // 编辑模式\r\n                            this.currentUserInfo.debts[this.editingDebtIndex] = debtData;\r\n                            this.$message.success('债务人信息修改成功！');\r\n                        } else {\r\n                            // 添加模式\r\n                            if (!this.currentUserInfo.debts) {\r\n                                this.currentUserInfo.debts = [];\r\n                            }\r\n                            this.currentUserInfo.debts.push(debtData);\r\n                            this.$message.success('债务人添加成功！');\r\n                        }\r\n\r\n                        // 更新主列表中的数据\r\n                        const userIndex = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (userIndex !== -1) {\r\n                            this.list[userIndex].debts = [...this.currentUserInfo.debts];\r\n                        }\r\n\r\n                        this.closeDebtDialog();\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            closeDebtDialog() {\r\n                this.debtDialogVisible = false;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.isEditingDebt = false;\r\n                this.editingDebtIndex = -1;\r\n                this.debtDialogTitle = '添加债务人';\r\n                // 清除表单验证\r\n                this.$nextTick(() => {\r\n                    if (this.$refs.debtForm) {\r\n                        this.$refs.debtForm.clearValidate();\r\n                    }\r\n                });\r\n            },\r\n            deleteDebt(index) {\r\n                this.$confirm('确定要删除这个债务人吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.currentUserInfo.debts.splice(index, 1);\r\n                    this.$message.success('删除成功！');\r\n                });\r\n            },\r\n            // 附件管理方法\r\n            addAttachment() {\r\n                this.$message.info('请选择具体的附件类型进行上传');\r\n            },\r\n            uploadIdCard() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'idCard', '身份证照片');\r\n                });\r\n            },\r\n            uploadLicense() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'license', '营业执照');\r\n                });\r\n            },\r\n            uploadOthers() {\r\n                this.createFileInput('*', (files) => {\r\n                    this.handleFileUpload(files, 'others', '其他附件');\r\n                });\r\n            },\r\n            // 创建文件选择器\r\n            createFileInput(accept, callback) {\r\n                const input = document.createElement('input');\r\n                input.type = 'file';\r\n                input.accept = accept;\r\n                input.multiple = true;\r\n                input.style.display = 'none';\r\n\r\n                input.onchange = (e) => {\r\n                    const files = Array.from(e.target.files);\r\n                    if (files.length > 0) {\r\n                        callback(files);\r\n                    }\r\n                    document.body.removeChild(input);\r\n                };\r\n\r\n                document.body.appendChild(input);\r\n                input.click();\r\n            },\r\n            // 处理文件上传\r\n            handleFileUpload(files, type, typeName) {\r\n                if (!files || files.length === 0) {\r\n                    this.$message.warning('请选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                // 验证文件\r\n                for (let file of files) {\r\n                    if (type !== 'others' && !file.type.startsWith('image/')) {\r\n                        this.$message.error(`${typeName}只能上传图片文件`);\r\n                        return;\r\n                    }\r\n                    if (file.size > 10 * 1024 * 1024) { // 10MB限制\r\n                        this.$message.error(`文件 ${file.name} 大小超过10MB限制`);\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 初始化附件数据结构\r\n                if (!this.currentUserInfo.attachments) {\r\n                    this.currentUserInfo.attachments = {};\r\n                }\r\n                if (!this.currentUserInfo.attachments[type]) {\r\n                    this.currentUserInfo.attachments[type] = [];\r\n                }\r\n\r\n                // 模拟上传过程\r\n                this.$message.info(`正在上传 ${files.length} 个文件...`);\r\n\r\n                files.forEach((file, index) => {\r\n                    // 创建文件预览URL\r\n                    const fileUrl = URL.createObjectURL(file);\r\n\r\n                    // 模拟上传延迟\r\n                    setTimeout(() => {\r\n                        const fileData = {\r\n                            name: file.name,\r\n                            url: fileUrl,\r\n                            size: file.size,\r\n                            type: file.type,\r\n                            uploadTime: new Date().toLocaleString()\r\n                        };\r\n\r\n                        this.currentUserInfo.attachments[type].push(fileData);\r\n\r\n                        if (index === files.length - 1) {\r\n                            this.$message.success(`${typeName}上传完成！共上传 ${files.length} 个文件`);\r\n                        }\r\n                    }, (index + 1) * 500); // 模拟上传时间\r\n                });\r\n            },\r\n            deleteAttachment(type, index) {\r\n                this.$confirm('确定要删除这个附件吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    if (this.currentUserInfo.attachments[type]) {\r\n                        // 释放预览URL\r\n                        const file = this.currentUserInfo.attachments[type][index];\r\n                        if (file && file.url && file.url.startsWith('blob:')) {\r\n                            URL.revokeObjectURL(file.url);\r\n                        }\r\n\r\n                        this.currentUserInfo.attachments[type].splice(index, 1);\r\n                        this.$message.success('删除成功！');\r\n                    }\r\n                });\r\n            },\r\n            downloadFile(file) {\r\n                if (!file || !file.url) {\r\n                    this.$message.error('文件链接无效');\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    // 创建下载链接\r\n                    const link = document.createElement('a');\r\n                    link.href = file.url;\r\n                    link.download = file.name || '附件';\r\n                    link.style.display = 'none';\r\n\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n\r\n                    this.$message.success(`开始下载: ${file.name}`);\r\n                } catch (error) {\r\n                    this.$message.error('下载失败，请重试');\r\n                    console.error('下载错误:', error);\r\n                }\r\n            },\r\n            // 文件相关辅助方法\r\n            getFileIcon(fileType) {\r\n                if (!fileType) return 'el-icon-document';\r\n\r\n                if (fileType.startsWith('image/')) return 'el-icon-picture';\r\n                if (fileType.includes('pdf')) return 'el-icon-document';\r\n                if (fileType.includes('word') || fileType.includes('doc')) return 'el-icon-document';\r\n                if (fileType.includes('excel') || fileType.includes('sheet')) return 'el-icon-s-grid';\r\n                if (fileType.includes('zip') || fileType.includes('rar')) return 'el-icon-folder-opened';\r\n                if (fileType.includes('video')) return 'el-icon-video-camera';\r\n                if (fileType.includes('audio')) return 'el-icon-headset';\r\n\r\n                return 'el-icon-document';\r\n            },\r\n            formatFileSize(bytes) {\r\n                if (bytes === 0) return '0 B';\r\n\r\n                const k = 1024;\r\n                const sizes = ['B', 'KB', 'MB', 'GB'];\r\n                const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n            },\r\n            // 债务人相关计算方法\r\n            getDebtCount(debts) {\r\n                return debts && debts.length ? debts.length : 0;\r\n            },\r\n            getDebtCountType(debts) {\r\n                const count = this.getDebtCount(debts);\r\n                if (count === 0) return 'info';\r\n                if (count <= 2) return 'success';\r\n                if (count <= 5) return 'warning';\r\n                return 'danger';\r\n            },\r\n            getTotalDebtAmount(debts) {\r\n                if (!debts || !debts.length) return 0;\r\n                return debts.reduce((total, debt) => {\r\n                    return total + (parseFloat(debt.money) || 0);\r\n                }, 0);\r\n            },\r\n            formatAmount(amount) {\r\n                if (amount === 0) return '0';\r\n                return amount.toLocaleString('zh-CN', {\r\n                    minimumFractionDigits: 0,\r\n                    maximumFractionDigits: 2\r\n                });\r\n            },\r\n            getInfo(id) {\r\n                let _this = this;\r\n                _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n                    if (resp) {\r\n                        resp.data.tiaojie_id = resp.data.tiaojie_id == 0 ? '' : resp.data.tiaojie_id;\r\n                        resp.data.fawu_id = resp.data.fawu_id == 0 ? '' : resp.data.fawu_id;\r\n                        resp.data.lian_id = resp.data.lian_id == 0 ? '' : resp.data.lian_id;\r\n                        resp.data.ywy_id = resp.data.ywy_id == 0 ? '' : resp.data.ywy_id;\r\n                        resp.data.htsczy_id = resp.data.htsczy_id == 0 ? '' : resp.data.htsczy_id;\r\n                        resp.data.ls_id = resp.data.ls_id == 0 ? '' : resp.data.ls_id;\r\n                        _this.ruleForm = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            delData(index, id) {\r\n                this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\",\r\n                })\r\n                    .then(() => {\r\n                        this.postRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                this.$message({\r\n                                    type: \"success\",\r\n                                    message: \"删除成功!\",\r\n                                });\r\n                                this.list.splice(index, 1);\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {\r\n                        this.$message({\r\n                            type: \"error\",\r\n                            message: \"取消删除!\",\r\n                        });\r\n                    });\r\n            },\r\n            refulsh() {\r\n                this.$router.go(0);\r\n            },\r\n            searchData() {\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.filterTestData();\r\n            },\r\n            resetSearch() {\r\n                this.search = {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                };\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n\r\n            getData() {\r\n                let _this = this;\r\n\r\n                _this.loading = true;\r\n                _this\r\n                    .postRequest(\r\n                        _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n                        _this.search\r\n                    )\r\n                    .then((resp) => {\r\n                        if (resp.code == 200) {\r\n                            _this.list = resp.data;\r\n                            _this.total = resp.count;\r\n\r\n                            if (resp.msg == '超级管理员') {\r\n                                _this.is_del = true;\r\n                            }\r\n                        }\r\n                        _this.loading = false;\r\n                    });\r\n            },\r\n            saveData() {\r\n                let _this = this;\r\n                console.log(this.ruleForm);\r\n                this.$refs[\"ruleForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // 使用测试数据，注释掉API调用\r\n                                // this.getData();\r\n                                this.addTestData();\r\n                                _this.dialogFormVisible = false;\r\n                                _this.dialogAddUser = false;\r\n                                _this.drawerEditVisible = false;\r\n                            } else {\r\n                                _this.$message({\r\n                                    type: \"error\",\r\n                                    message: resp.msg,\r\n                                });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleSizeChange(val) {\r\n                this.size = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleSuccess(res) {\r\n                this.ruleForm.pic_path = res.data.url;\r\n            },\r\n\r\n            showImage(file) {\r\n                this.show_image = file;\r\n                this.dialogVisible = true;\r\n            },\r\n            beforeUpload(file) {\r\n                const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n                if (!isTypeTrue) {\r\n                    this.$message.error(\"上传图片格式不对!\");\r\n                    return;\r\n                }\r\n            },\r\n            delImage(file, fileName) {\r\n                let _this = this;\r\n                _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.ruleForm[fileName] = \"\";\r\n\r\n                        _this.$message.success(\"删除成功!\");\r\n                    } else {\r\n                        _this.$message.error(resp.msg);\r\n                    }\r\n                });\r\n            },\r\n            handleSortChange({column, prop, order}) {\r\n                this.search.prop = prop;\r\n                this.search.order = order;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n                // 根据 column, prop, order 来更新你的数据排序\r\n                // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n            },\r\n            exports: function () { //导出表格\r\n                let _this = this;\r\n                location.href = \"/admin/user/export2?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n                // _this.postRequest(\r\n                //                 _this.url + \"export\",\r\n                //                 _this.search\r\n                //         )\r\n                //         .then((resp) => {\r\n                //           if (resp.code == 200) {\r\n                //\r\n                //           }\r\n                //         });\r\n            },\r\n            closeUploadDialog() { //关闭窗口\r\n                this.uploadVisible = false;\r\n                this.$refs.upload.clearFiles();\r\n                this.uploadData.review = false;\r\n            },\r\n            uploadSuccess(response) { //导入完成回调\r\n                if (response.code === 200) {\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: response.msg\r\n                    });\r\n                    this.uploadVisible = false;\r\n                    // 使用测试数据，注释掉API调用\r\n                    // this.getData();\r\n                    this.addTestData();\r\n                    console.log(response);\r\n                } else {\r\n                    this.$message({\r\n                        type: 'warning',\r\n                        message: response.msg\r\n                    });\r\n                }\r\n\r\n                this.submitOrderLoading2 = false;\r\n                this.$refs.upload.clearFiles();\r\n            },\r\n            checkFile(file) { //导入前校验文件后缀\r\n                let fileType = ['xls', 'xlsx'];\r\n                let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n                if (!fileType.includes(type)) {\r\n                    this.$message({\r\n                        type: \"warning\",\r\n                        message: \"文件格式错误仅支持 xls xlxs 文件\"\r\n                    });\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            submitUpload() { //导入提交\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            closeDialog() { //关闭窗口\r\n                this.addVisible = false;\r\n                this.uploadVisible = false;\r\n                this.form = {\r\n                    id: '',\r\n                    nickname: \"\",\r\n                    mobile: \"\",\r\n                    school_id: 0,\r\n                    grade_id: '',\r\n                    class_id: '',\r\n                    sex: '',\r\n                    is_poor: '',\r\n                    is_display: '',\r\n                    number: '',\r\n                    remark: '',\r\n                    is_remark_option: 0,\r\n                    remark_option: [],\r\n                    mobile_checked: false,\r\n                };\r\n                this.$refs.form.resetFields();\r\n            },\r\n            openUpload() { //打开导入弹窗\r\n                this.uploadVisible = true;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n            },\r\n            // 关闭导入对话框\r\n            closeUploadDialog() {\r\n                this.uploadVisible = false;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n                if (this.$refs.upload) {\r\n                    this.$refs.upload.clearFiles();\r\n                }\r\n            },\r\n            // 上传前验证\r\n            beforeUpload(file) {\r\n                const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                               file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n                const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n                if (!isExcel) {\r\n                    this.$message.error('只能上传 Excel 文件!');\r\n                    return false;\r\n                }\r\n                if (!isLt10M) {\r\n                    this.$message.error('上传文件大小不能超过 10MB!');\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            // 提交上传\r\n            submitUpload() {\r\n                if (this.fileList.length === 0) {\r\n                    this.$message.warning('请先选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            // 上传成功回调\r\n            handleUploadSuccess(response, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                if (response.code === 200) {\r\n                    this.$message.success(`导入成功！共导入 ${response.count || 0} 条用户数据`);\r\n                    this.closeUploadDialog();\r\n                    // 重新加载数据\r\n                    this.addTestData(); // 使用测试数据，实际应该调用 this.getData();\r\n                } else {\r\n                    this.$message.error(response.msg || '导入失败');\r\n                }\r\n            },\r\n            // 上传失败回调\r\n            handleUploadError(err, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                this.$message.error('文件上传失败，请重试');\r\n                console.error('Upload error:', err);\r\n            },\r\n            addUser() {\r\n                this.dialogAddUser = true;\r\n                this.ruleForm = {};\r\n                this.getYuangongs();\r\n            },\r\n            // 下载导入模板\r\n            downloadTemplate() {\r\n                const templateUrl = '/import_templete/user.xls';\r\n                const link = document.createElement('a');\r\n                link.href = templateUrl;\r\n                link.download = '用户导入模板.xls';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.$message.success('模板下载中...');\r\n            },\r\n            // 处理表格选择变化\r\n            handleSelectionChange(selection) {\r\n                this.selectedUsers = selection;\r\n            },\r\n            // 导出用户数据（选中或全部）\r\n            exportSelectedData() {\r\n                let exportUrl;\r\n                let message;\r\n\r\n                if (this.selectedUsers.length > 0) {\r\n                    // 导出选中的用户数据\r\n                    const userIds = this.selectedUsers.map(user => user.id).join(',');\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&ids=${userIds}`;\r\n                    message = `正在导出 ${this.selectedUsers.length} 个用户的数据`;\r\n                } else {\r\n                    // 导出全部用户数据\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&keyword=${this.search.keyword || ''}`;\r\n                    message = '正在导出全部用户数据';\r\n                }\r\n\r\n                // 执行导出\r\n                location.href = exportUrl;\r\n                this.$message.success(message);\r\n            },\r\n            // 批量删除用户\r\n            batchDeleteUsers() {\r\n                if (this.selectedUsers.length === 0) {\r\n                    this.$message.warning('请先选择要删除的用户');\r\n                    return;\r\n                }\r\n\r\n                this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`, '批量删除确认', {\r\n                    confirmButtonText: '确定删除',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning',\r\n                    dangerouslyUseHTMLString: true\r\n                }).then(() => {\r\n                    // 获取选中用户的ID列表\r\n                    const userIds = this.selectedUsers.map(user => user.id);\r\n\r\n                    // 这里应该调用批量删除API\r\n                    // this.postRequest(this.url + \"batchDelete\", { ids: userIds }).then((resp) => {\r\n                    //     if (resp.code == 200) {\r\n                    //         this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n                    //         this.getData(); // 重新加载数据\r\n                    //         this.selectedUsers = []; // 清空选择\r\n                    //     }\r\n                    // });\r\n\r\n                    // 临时使用本地删除模拟\r\n                    userIds.forEach(id => {\r\n                        const index = this.list.findIndex(user => user.id === id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1);\r\n                        }\r\n                    });\r\n\r\n                    this.total = this.list.length;\r\n                    this.selectedUsers = []; // 清空选择\r\n                    this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n\r\n                    // 清空表格选择\r\n                    this.$refs.userTable.clearSelection();\r\n                }).catch(() => {\r\n                    this.$message.info('已取消删除操作');\r\n                });\r\n            }\r\n\r\n        },\r\n    };\r\n</script>\r\n<style scoped>\r\n/* 页面特定样式 */\r\n.page-wrapper {\r\n    background-color: #f5f5f5;\r\n    min-height: calc(100vh - 110px);\r\n    padding: 16px;\r\n}\r\n\r\n.page-container {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    padding: 24px;\r\n}\r\n\r\n.page-title {\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-container {\r\n    background: #fff;\r\n    padding: 24px;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #e8e8e8;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-form {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.search-form .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.search-form .el-input__inner,\r\n.search-form .el-select .el-input__inner {\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.search-form .el-input__inner:focus,\r\n.search-form .el-select .el-input__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.search-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n}\r\n\r\n.search-buttons .el-button {\r\n    min-width: 80px;\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-wrap: wrap;\r\n    margin-top: 20px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-buttons .el-button {\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.data-table {\r\n    margin-top: 20px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table .el-table {\r\n    border-radius: 8px;\r\n}\r\n\r\n.data-table .el-table th {\r\n    background-color: #fafafa !important;\r\n    color: #606266 !important;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table .el-table td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 头像样式 */\r\n.avatar-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    border: 2px solid #e8e8e8;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.user-avatar {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.no-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-name.clickable {\r\n    cursor: pointer;\r\n    color: #1890ff;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.user-name.clickable:hover {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n.user-phone {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons-table {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.action-buttons-table .el-button {\r\n    padding: 5px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 16px 0;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    color: #8c8c8c;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 编辑模式切换按钮样式 */\r\n.edit-mode-toggle {\r\n    display: flex;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.edit-mode-toggle .el-button {\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.custom-dialog .el-dialog__body {\r\n    padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.card {\r\n    background: #fff;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.card-header {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.el-input, .el-select {\r\n    width: 100%;\r\n}\r\n\r\n/* 搜索表单特殊样式 */\r\n.search-form .el-input,\r\n.search-form .el-select,\r\n.search-form .el-date-picker {\r\n    width: 100%;\r\n}\r\n\r\n.search-form .el-date-picker {\r\n    width: 100% !important;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.data-table .el-table tbody tr:hover {\r\n    background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n    .search-form .el-col {\r\n        margin-bottom: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .search-form .el-col {\r\n        width: 100% !important;\r\n        flex: 0 0 100% !important;\r\n        max-width: 100% !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n    }\r\n\r\n    .action-buttons {\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 16px;\r\n        margin: 8px;\r\n    }\r\n\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: center;\r\n    }\r\n\r\n    .pagination-info {\r\n        order: 2;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.drawer-content-wrapper {\r\n    display: flex;\r\n    height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n    width: 200px;\r\n    border-right: 1px solid #e6e6e6;\r\n    background-color: #fafafa;\r\n}\r\n\r\n.drawer-menu {\r\n    border-right: none;\r\n    background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.drawer-content {\r\n    flex: 1;\r\n    padding: 20px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n    height: 100%;\r\n}\r\n\r\n.drawer-content .card {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.drawer-content .card-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.drawer-content .card-header i {\r\n    color: #1890ff;\r\n    font-size: 18px;\r\n}\r\n\r\n.drawer-footer {\r\n    margin-top: 24px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n    text-align: right;\r\n}\r\n\r\n.drawer-footer .el-button {\r\n    margin-left: 12px;\r\n}\r\n\r\n/* 头像显示样式 */\r\n.avatar-display {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.detail-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    border: 2px solid #e8e8e8;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.detail-avatar:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.no-avatar-large {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 12px;\r\n    border: 2px solid #e8e8e8;\r\n}\r\n\r\n.no-avatar-large i {\r\n    font-size: 24px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n/* 抽屉内表单样式 */\r\n.drawer-content .el-form-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.drawer-content .el-descriptions-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n}\r\n\r\n/* 无数据显示样式 */\r\n.no-data {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #ccc;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-data i {\r\n    font-size: 48px;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.attachment-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n}\r\n\r\n.attachment-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n}\r\n\r\n.attachment-title {\r\n    background: #f5f5f5;\r\n    padding: 12px 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.attachment-content {\r\n    padding: 16px;\r\n}\r\n\r\n.image-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: box-shadow 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n.attachment-image {\r\n    width: 100%;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.attachment-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\r\n    color: white;\r\n    padding: 12px;\r\n    transform: translateY(100%);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover .image-overlay {\r\n    transform: translateY(0);\r\n}\r\n\r\n.image-info {\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-info .file-name {\r\n    display: block;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.image-info .upload-time {\r\n    font-size: 11px;\r\n    opacity: 0.8;\r\n}\r\n\r\n.image-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n}\r\n\r\n.image-actions .el-button {\r\n    flex: 1;\r\n    font-size: 11px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.file-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    background: #fafafa;\r\n    transition: background-color 0.2s;\r\n}\r\n\r\n.file-item:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n.file-icon {\r\n    margin-right: 12px;\r\n}\r\n\r\n.file-type-icon {\r\n    font-size: 24px;\r\n    color: #1890ff;\r\n}\r\n\r\n.file-info {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.file-info .file-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.file-meta {\r\n    display: flex;\r\n    gap: 12px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.file-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    margin-left: 12px;\r\n}\r\n\r\n.no-attachment {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #999;\r\n}\r\n\r\n.no-attachment i {\r\n    font-size: 48px;\r\n    color: #d9d9d9;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n}\r\n\r\n.no-attachment span {\r\n    display: block;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 导入用户对话框样式 */\r\n.upload-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.upload-tips {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-tips .el-alert__description p {\r\n    margin: 5px 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-actions {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-area .el-upload-dragger {\r\n    width: 100%;\r\n    height: 180px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: border-color 0.2s;\r\n}\r\n\r\n.upload-area .el-upload-dragger:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-area .el-upload-dragger .el-icon-upload {\r\n    font-size: 67px;\r\n    color: #c0c4cc;\r\n    margin: 40px 0 16px;\r\n    line-height: 50px;\r\n}\r\n\r\n.upload-area .el-upload__text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area .el-upload__text em {\r\n    color: #409eff;\r\n    font-style: normal;\r\n}\r\n\r\n.upload-area .el-upload__tip {\r\n    font-size: 12px;\r\n    color: #606266;\r\n    margin-top: 7px;\r\n}\r\n\r\n.upload-options {\r\n    text-align: center;\r\n}\r\n\r\n.upload-options .el-checkbox {\r\n    color: #606266;\r\n}\r\n\r\n/* 债务金额样式 */\r\n.debt-amount {\r\n    font-weight: 500;\r\n    color: #8c8c8c;\r\n}\r\n\r\n.debt-amount.has-debt {\r\n    color: #f56c6c;\r\n    font-weight: 600;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n    .page-wrapper {\r\n        padding: 8px;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .page-title {\r\n        font-size: 16px;\r\n        margin-bottom: 15px;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 10px;\r\n    }\r\n\r\n    .page-title .el-button {\r\n        align-self: flex-end;\r\n        margin-top: -30px;\r\n    }\r\n\r\n    .search-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .search-form .el-row {\r\n        margin: 0;\r\n    }\r\n\r\n    .search-form .el-col {\r\n        padding: 0 5px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-form .el-form-item {\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-form .el-form-item__label {\r\n        font-size: 12px;\r\n        line-height: 32px;\r\n        width: 70px !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        display: flex;\r\n        gap: 8px;\r\n        justify-content: center;\r\n    }\r\n\r\n    .action-buttons {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n        margin-top: 15px;\r\n    }\r\n\r\n    .action-buttons .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n\r\n    /* 表格移动端适配 */\r\n    .data-table {\r\n        overflow-x: auto;\r\n    }\r\n\r\n    .data-table .el-table {\r\n        min-width: 800px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .data-table .el-table th,\r\n    .data-table .el-table td {\r\n        padding: 6px 4px;\r\n    }\r\n\r\n    .data-table .el-table .cell {\r\n        padding: 0 4px;\r\n        line-height: 1.2;\r\n    }\r\n\r\n    .user-avatar {\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n\r\n    .no-avatar {\r\n        width: 30px;\r\n        height: 30px;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .user-info .user-name {\r\n        font-size: 12px;\r\n        margin-bottom: 2px;\r\n    }\r\n\r\n    .user-info .user-phone {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    /* 分页移动端适配 */\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 10px;\r\n        align-items: center;\r\n        padding: 15px 0;\r\n    }\r\n\r\n    .pagination-info {\r\n        display: flex;\r\n        gap: 15px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .el-pagination {\r\n        justify-content: center;\r\n    }\r\n\r\n    /* 抽屉移动端适配 */\r\n    .el-drawer {\r\n        width: 100% !important;\r\n    }\r\n\r\n    .drawer-content-wrapper {\r\n        flex-direction: column;\r\n        height: 100%;\r\n    }\r\n\r\n    .drawer-sidebar {\r\n        width: 100%;\r\n        height: auto;\r\n        border-right: none;\r\n        border-bottom: 1px solid #e8e8e8;\r\n    }\r\n\r\n    .drawer-menu {\r\n        display: flex;\r\n        flex-direction: row;\r\n        overflow-x: auto;\r\n        border: none;\r\n    }\r\n\r\n    .drawer-menu .el-menu-item {\r\n        min-width: 100px;\r\n        flex-shrink: 0;\r\n        text-align: center;\r\n        border-bottom: none;\r\n        border-right: 1px solid #e8e8e8;\r\n    }\r\n\r\n    .drawer-menu .el-menu-item:last-child {\r\n        border-right: none;\r\n    }\r\n\r\n    .drawer-content {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n    }\r\n\r\n    .edit-mode-toggle {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .edit-mode-toggle .el-button {\r\n        width: 100%;\r\n    }\r\n\r\n    .card {\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .card-header {\r\n        font-size: 14px;\r\n        padding: 10px 15px;\r\n    }\r\n\r\n    .el-descriptions {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .el-descriptions-item__label {\r\n        font-size: 12px !important;\r\n        width: 80px !important;\r\n    }\r\n\r\n    .el-descriptions-item__content {\r\n        font-size: 12px !important;\r\n    }\r\n\r\n    .detail-avatar {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .no-avatar-large {\r\n        width: 60px;\r\n        height: 60px;\r\n        font-size: 20px;\r\n    }\r\n\r\n    /* 表单移动端适配 */\r\n    .el-form .el-row {\r\n        margin: 0;\r\n    }\r\n\r\n    .el-form .el-col {\r\n        padding: 0 5px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n        font-size: 12px !important;\r\n        line-height: 32px !important;\r\n        width: 80px !important;\r\n    }\r\n\r\n    .el-input__inner,\r\n    .el-select .el-input__inner,\r\n    .el-textarea__inner {\r\n        font-size: 12px;\r\n        height: 32px;\r\n        line-height: 32px;\r\n    }\r\n\r\n    .el-textarea__inner {\r\n        height: auto;\r\n        min-height: 60px;\r\n    }\r\n\r\n    /* 债务人列表移动端适配 */\r\n    .debt-list {\r\n        gap: 10px;\r\n    }\r\n\r\n    .debt-item {\r\n        padding: 10px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .debt-header {\r\n        font-size: 13px;\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .debt-info {\r\n        font-size: 11px;\r\n        gap: 8px;\r\n    }\r\n\r\n    /* 附件列表移动端适配 */\r\n    .attachment-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 10px;\r\n    }\r\n\r\n    .attachment-item {\r\n        padding: 10px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .attachment-name {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .attachment-info {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .attachment-actions .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    /* 上传对话框移动端适配 */\r\n    .upload-dialog .el-dialog {\r\n        width: 95% !important;\r\n        margin: 0 auto !important;\r\n    }\r\n\r\n    .upload-area .el-upload-dragger {\r\n        height: 120px;\r\n    }\r\n\r\n    .upload-area .el-upload-dragger .el-icon-upload {\r\n        font-size: 40px;\r\n        margin: 20px 0 10px;\r\n    }\r\n\r\n    .upload-area .el-upload__text {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .upload-area .el-upload__tip {\r\n        font-size: 11px;\r\n    }\r\n}\r\n\r\n/* 超小屏幕适配 */\r\n@media (max-width: 480px) {\r\n    .page-wrapper {\r\n        padding: 5px;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 8px;\r\n    }\r\n\r\n    .page-title {\r\n        font-size: 14px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-container {\r\n        padding: 8px;\r\n    }\r\n\r\n    .search-form .el-form-item__label {\r\n        width: 60px !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .search-buttons .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .data-table .el-table {\r\n        min-width: 600px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .data-table .el-table th,\r\n    .data-table .el-table td {\r\n        padding: 4px 2px;\r\n    }\r\n\r\n    .user-avatar {\r\n        width: 25px;\r\n        height: 25px;\r\n    }\r\n\r\n    .no-avatar {\r\n        width: 25px;\r\n        height: 25px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .user-info .user-name {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .user-info .user-phone {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        padding: 2px 6px;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .drawer-content {\r\n        padding: 10px;\r\n    }\r\n\r\n    .card-header {\r\n        font-size: 12px;\r\n        padding: 8px 10px;\r\n    }\r\n\r\n    .el-descriptions {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .el-descriptions-item__label {\r\n        font-size: 11px !important;\r\n        width: 70px !important;\r\n    }\r\n\r\n    .el-descriptions-item__content {\r\n        font-size: 11px !important;\r\n    }\r\n\r\n    .detail-avatar {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n\r\n    .no-avatar-large {\r\n        width: 50px;\r\n        height: 50px;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n        width: 70px !important;\r\n        font-size: 11px !important;\r\n    }\r\n\r\n    .el-input__inner,\r\n    .el-select .el-input__inner,\r\n    .el-textarea__inner {\r\n        font-size: 11px;\r\n        height: 28px;\r\n        line-height: 28px;\r\n    }\r\n\r\n    .el-textarea__inner {\r\n        height: auto;\r\n        min-height: 50px;\r\n    }\r\n}\r\n</style>\r\n"]}]}