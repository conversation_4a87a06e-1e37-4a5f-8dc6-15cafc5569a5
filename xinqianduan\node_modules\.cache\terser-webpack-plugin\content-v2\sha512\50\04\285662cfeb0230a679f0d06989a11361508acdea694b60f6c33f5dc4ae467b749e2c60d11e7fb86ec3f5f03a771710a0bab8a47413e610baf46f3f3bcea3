{"map": "{\"version\":3,\"sources\":[\"js/chunk-4ebce2ce.6c19dbf9.js\"],\"names\":[\"window\",\"push\",\"093c\",\"module\",\"exports\",\"__webpack_require__\",\"3b39\",\"__webpack_exports__\",\"formatFileSize\",\"size\",\"toFixed\",\"d\",\"4ffb\",\"f6e7\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"attrs\",\"type\",\"on\",\"click\",\"handleUpload\",\"_v\",\"disabled\",\"selectedFiles\",\"length\",\"handleBatchArchive\",\"handleBatchDownload\",\"handleBatchDelete\",\"staticStyle\",\"width\",\"data\",\"fileList\",\"selection-change\",\"handleSelectionChange\",\"prop\",\"label\",\"min-width\",\"scopedSlots\",\"_u\",\"key\",\"fn\",\"scope\",\"class\",\"getFileIcon\",\"row\",\"fileType\",\"_s\",\"fileName\",\"align\",\"$event\",\"handlePreview\",\"handleDownload\",\"handleDelete\",\"title\",\"visible\",\"uploadDialogVisible\",\"update:visible\",\"drag\",\"multiple\",\"action\",\"uploadUrl\",\"before-upload\",\"beforeUpload\",\"on-progress\",\"handleProgress\",\"on-success\",\"handleUploadSuccess\",\"on-error\",\"handleUploadError\",\"file-list\",\"uploadFileList\",\"slot\",\"previewDialogVisible\",\"fullscreen\",\"isImage\",\"src\",\"previewUrl\",\"alt\",\"isPdf\",\"height\",\"isOffice\",\"staticRenderFns\",\"vuex_esm\",\"fileUtils\",\"api\",\"Filevue_type_script_lang_js\",\"name\",\"[object Object]\",\"currentFile\",\"fileTypeMap\",\"image\",\"pdf\",\"office\",\"text\",\"archive\",\"computed\",\"Object\",\"includes\",\"fetchFileList\",\"methods\",\"mockFiles\",\"id\",\"category\",\"uploadTime\",\"$message\",\"success\",\"error\",\"file\",\"isLt500M\",\"event\",\"console\",\"log\",\"percentage\",\"response\",\"generatePreviewUrl\",\"_response$data\",\"link\",\"document\",\"createElement\",\"href\",\"btoa\",\"download\",\"$confirm\",\"selection\",\"fileIds\",\"map\",\"forEach\",\"iconMap\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"archive_Filevue_type_script_lang_js\",\"componentNormalizer\",\"component\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,KAM3BC,OACA,SAAUH,EAAQI,EAAqBF,GAE7C,aAWA,SAASG,EAAeC,GACtB,OAAIA,EAAO,KACFA,EAAO,KACLA,EAAO,SACRA,EAAO,MAAMC,QAAQ,GAAK,MACzBD,EAAO,YACRA,EAAO,SAAeC,QAAQ,GAAK,OAEnCD,EAAO,YAAsBC,QAAQ,GAAK,MAlBvBL,EAAoBM,EAAEJ,EAAqB,KAAK,WAAa,OAAOC,MAsI7FI,OACA,SAAUT,EAAQI,EAAqBF,GAE7C,aACmgBA,EAAoB,SAOjhBQ,KACA,SAAUV,EAAQI,EAAqBF,GAE7C,aAEAA,EAAoBS,EAAEP,GAGtB,IAAIQ,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,qBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,kBAAmB,CAACA,EAAG,YAAa,CACzCG,MAAO,CACLC,KAAQ,WAEVC,GAAI,CACFC,MAASR,EAAIS,eAEd,CAACP,EAAG,IAAK,CACVE,YAAa,mBACXJ,EAAIU,GAAG,YAAaR,EAAG,YAAa,CACtCG,MAAO,CACLC,KAAQ,UACRK,UAAaX,EAAIY,cAAcC,QAEjCN,GAAI,CACFC,MAASR,EAAIc,qBAEd,CAACZ,EAAG,IAAK,CACVE,YAAa,uBACXJ,EAAIU,GAAG,YAAaR,EAAG,YAAa,CACtCG,MAAO,CACLC,KAAQ,UACRK,UAAaX,EAAIY,cAAcC,QAEjCN,GAAI,CACFC,MAASR,EAAIe,sBAEd,CAACb,EAAG,IAAK,CACVE,YAAa,qBACXJ,EAAIU,GAAG,YAAaR,EAAG,YAAa,CACtCG,MAAO,CACLC,KAAQ,SACRK,UAAaX,EAAIY,cAAcC,QAEjCN,GAAI,CACFC,MAASR,EAAIgB,oBAEd,CAACd,EAAG,IAAK,CACVE,YAAa,mBACXJ,EAAIU,GAAG,aAAc,IAAK,GAAIR,EAAG,MAAO,CAC1CE,YAAa,uBACZ,CAACF,EAAG,WAAY,CACjBe,YAAa,CACXC,MAAS,QAEXb,MAAO,CACLc,KAAQnB,EAAIoB,UAEdb,GAAI,CACFc,mBAAoBrB,EAAIsB,wBAEzB,CAACpB,EAAG,kBAAmB,CACxBG,MAAO,CACLC,KAAQ,YACRY,MAAS,QAEThB,EAAG,kBAAmB,CACxBG,MAAO,CACLkB,KAAQ,WACRC,MAAS,MACTC,YAAa,OAEfC,YAAa1B,EAAI2B,GAAG,CAAC,CACnBC,IAAK,UACLC,GAAI,SAAUC,GACZ,MAAO,CAAC5B,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,IAAK,CACV6B,MAAO/B,EAAIgC,YAAYF,EAAMG,IAAIC,YAC/BhC,EAAG,OAAQ,CAACF,EAAIU,GAAGV,EAAImC,GAAGL,EAAMG,IAAIG,sBAG1ClC,EAAG,kBAAmB,CACxBG,MAAO,CACLkB,KAAQ,WACRC,MAAS,KACTN,MAAS,SAEThB,EAAG,kBAAmB,CACxBG,MAAO,CACLkB,KAAQ,WACRC,MAAS,KACTN,MAAS,SAEThB,EAAG,kBAAmB,CACxBG,MAAO,CACLkB,KAAQ,OACRC,MAAS,KACTN,MAAS,OAEXQ,YAAa1B,EAAI2B,GAAG,CAAC,CACnBC,IAAK,UACLC,GAAI,SAAUC,GACZ,MAAO,CAAC9B,EAAIU,GAAG,IAAMV,EAAImC,GAAGnC,EAAIR,eAAesC,EAAMG,IAAIxC,OAAS,YAGpES,EAAG,kBAAmB,CACxBG,MAAO,CACLkB,KAAQ,aACRC,MAAS,OACTN,MAAS,SAEThB,EAAG,kBAAmB,CACxBG,MAAO,CACLmB,MAAS,KACTN,MAAS,MACTmB,MAAS,UAEXX,YAAa1B,EAAI2B,GAAG,CAAC,CACnBC,IAAK,UACLC,GAAI,SAAUC,GACZ,MAAO,CAAC5B,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBG,MAAO,CACLZ,KAAQ,OACRa,KAAQ,WAEVC,GAAI,CACFC,MAAS,SAAU8B,GACjB,OAAOtC,EAAIuC,cAAcT,EAAMG,QAGlC,CAACjC,EAAIU,GAAG,UAAWR,EAAG,YAAa,CACpCG,MAAO,CACLZ,KAAQ,OACRa,KAAQ,WAEVC,GAAI,CACFC,MAAS,SAAU8B,GACjB,OAAOtC,EAAIwC,eAAeV,EAAMG,QAGnC,CAACjC,EAAIU,GAAG,UAAWR,EAAG,YAAa,CACpCG,MAAO,CACLZ,KAAQ,OACRa,KAAQ,UAEVC,GAAI,CACFC,MAAS,SAAU8B,GACjB,OAAOtC,EAAIyC,aAAaX,EAAMG,QAGjC,CAACjC,EAAIU,GAAG,WAAY,WAGxB,IAAK,GAAIR,EAAG,YAAa,CAC5BG,MAAO,CACLqC,MAAS,OACTC,QAAW3C,EAAI4C,oBACf1B,MAAS,SAEXX,GAAI,CACFsC,iBAAkB,SAAUP,GAC1BtC,EAAI4C,oBAAsBN,KAG7B,CAACpC,EAAG,YAAa,CAClBE,YAAa,cACbC,MAAO,CACLyC,KAAQ,GACRC,SAAY,GACZC,OAAUhD,EAAIiD,UACdC,gBAAiBlD,EAAImD,aACrBC,cAAepD,EAAIqD,eACnBC,aAActD,EAAIuD,oBAClBC,WAAYxD,EAAIyD,kBAChBC,YAAa1D,EAAI2D,iBAElB,CAACzD,EAAG,IAAK,CACVE,YAAa,mBACXF,EAAG,MAAO,CACZE,YAAa,mBACZ,CAACJ,EAAIU,GAAG,aAAcR,EAAG,KAAM,CAACF,EAAIU,GAAG,YAAaR,EAAG,MAAO,CAC/DE,YAAa,iBACbC,MAAO,CACLuD,KAAQ,OAEVA,KAAM,OACL,CAAC5D,EAAIU,GAAG,gCAAiC,GAAIR,EAAG,YAAa,CAC9DG,MAAO,CACLqC,MAAS,OACTC,QAAW3C,EAAI6D,qBACf3C,MAAS,MACT4C,YAAc,GAEhBvD,GAAI,CACFsC,iBAAkB,SAAUP,GAC1BtC,EAAI6D,qBAAuBvB,KAG9B,CAACpC,EAAG,MAAO,CACZE,YAAa,qBACZ,CAACJ,EAAI+D,QAAU7D,EAAG,MAAO,CAC1BE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZG,MAAO,CACL2D,IAAOhE,EAAIiE,WACXC,IAAO,YAEJlE,EAAImE,MAAQjE,EAAG,MAAO,CAC3BE,YAAa,eACZ,CAACF,EAAG,SAAU,CACfG,MAAO,CACL2D,IAAOhE,EAAIiE,WACX/C,MAAS,OACTkD,OAAU,aAEPpE,EAAIqE,SAAWnE,EAAG,MAAO,CAC9BE,YAAa,kBACZ,CAACF,EAAG,SAAU,CACfG,MAAO,CACL2D,IAAOhE,EAAIiE,WACX/C,MAAS,OACTkD,OAAU,aAEPlE,EAAG,MAAO,CACfE,YAAa,iBACZ,CAACF,EAAG,IAAK,CAACF,EAAIU,GAAG,+BAAgC,IAElD4D,EAAkB,GAQlBC,GAH0BlF,EAAoB,QAGnCA,EAAoB,SAG/BmF,EAAYnF,EAAoB,QAGhCoF,EAAMpF,EAAoB,QAOGqF,EAA8B,CAC7DC,KAAM,cACNC,OACE,MAAO,CACLxD,SAAU,GACVR,cAAe,GACfgC,qBAAqB,EACrBiB,sBAAsB,EACtBF,eAAgB,GAChBM,WAAY,GACZY,YAAa,KACb5B,UAAW,kBAEX6B,YAAa,CACXC,MAAS,CAAC,MAAO,OAAQ,MAAO,MAAO,OACvCC,IAAO,CAAC,OACRC,OAAU,CAAC,MAAO,OAAQ,MAAO,OAAQ,MAAO,QAChDC,KAAQ,CAAC,MAAO,MAChBC,QAAW,CAAC,MAAO,MAAO,SAIhCC,SAAU,IACLC,OAAOd,EAAS,KAAhBc,CAAuC,CAAC,aAC3CT,UACE,OAAO3E,KAAK4E,aAAe5E,KAAK6E,YAAYC,MAAMO,SAASrF,KAAK4E,YAAY3C,WAE9E0C,QACE,OAAO3E,KAAK4E,aAAe5E,KAAK6E,YAAYE,IAAIM,SAASrF,KAAK4E,YAAY3C,WAE5E0C,WACE,OAAO3E,KAAK4E,aAAe5E,KAAK6E,YAAYG,OAAOK,SAASrF,KAAK4E,YAAY3C,YAGjF0C,UACE3E,KAAKsF,iBAEPC,QAAS,CAEPZ,sBACE,IAEE,MAAMa,EAAY,CAAC,CACjBC,GAAI,EACJtD,SAAU,WACVF,SAAU,MACVyD,SAAU,OACVlG,KAAM,OACNmG,WAAY,uBACX,CACDF,GAAI,EACJtD,SAAU,YACVF,SAAU,OACVyD,SAAU,OACVlG,KAAM,MACNmG,WAAY,uBACX,CACDF,GAAI,EACJtD,SAAU,WACVF,SAAU,MACVyD,SAAU,OACVlG,KAAM,KACNmG,WAAY,uBACX,CACDF,GAAI,EACJtD,SAAU,WACVF,SAAU,MACVyD,SAAU,OACVlG,KAAM,OACNmG,WAAY,uBACX,CACDF,GAAI,EACJtD,SAAU,aACVF,SAAU,OACVyD,SAAU,OACVlG,KAAM,MACNmG,WAAY,wBAEd3F,KAAKmB,SAAWqE,EAChBxF,KAAK4F,SAASC,QAAQ,YACtB,MAAOC,GACP9F,KAAK4F,SAASE,MAAM,cAIxBnB,eACE3E,KAAK2C,qBAAsB,GAG7BgC,aAAaoB,GACX,MAAMC,EAAWD,EAAKvG,KAAO,KAAO,KAAO,IAC3C,QAAKwG,IACHhG,KAAK4F,SAASE,MAAM,oBACb,IAKXnB,eAAesB,EAAOF,GACpBG,QAAQC,IAAI,QAASJ,EAAKK,aAG5BzB,oBAAoB0B,EAAUN,GAC5B/F,KAAK4F,SAASC,QAAQ,UACtB7F,KAAK2C,qBAAsB,EAC3B3C,KAAKsF,iBAGPX,oBACE3E,KAAK4F,SAASE,MAAM,WAGtBnB,oBAAoBoB,GAClB/F,KAAK4E,YAAcmB,EACnB/F,KAAK4D,sBAAuB,EAE5B5D,KAAKgE,iBAAmBhE,KAAKsG,mBAAmBP,IAGlDpB,yBAAyBoB,GACvB,IACE,IAAIQ,EACJ,MAAMF,QAAiBjB,OAAOZ,EAAI,KAAXY,CAAkC,oBAAoBW,EAAKN,IAClF,OAA6C,QAApCc,EAAiBF,EAASnF,YAAqC,IAAnBqF,OAA4B,EAASA,EAAevC,aAAe,sDACxH,MAAO8B,GACP,MAAO,wDAIXnB,qBAAqBoB,GACnB,IAEE,MAAMS,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAO,wCAAwCC,KAAKb,EAAK5D,UAC9DqE,EAAKK,SAAWd,EAAK5D,SACrBqE,EAAKjG,QACLP,KAAK4F,SAASC,QAAQ,QACtB,MAAOC,GACP9F,KAAK4F,SAASE,MAAM,YAIxBnB,mBAAmBoB,GACjB,UACQ/F,KAAK8G,SAAS,YAAa,KAAM,CACrCzG,KAAM,kBAEF+E,OAAOZ,EAAI,KAAXY,CAAqC,kBAAkBW,EAAKN,IAClEzF,KAAK4F,SAASC,QAAQ,QACtB7F,KAAKsF,gBACL,MAAOQ,GACO,WAAVA,GACF9F,KAAK4F,SAASE,MAAM,UAK1BnB,sBAAsBoC,GACpB/G,KAAKW,cAAgBoG,GAGvBpC,2BACE,GAAK3E,KAAKW,cAAcC,OACxB,UACQZ,KAAK8G,SAAS,cAAe,KAAM,CACvCzG,KAAM,YAER,MAAM2G,EAAUhH,KAAKW,cAAcsG,IAAIlB,GAAQA,EAAKN,UAC9CL,OAAOZ,EAAI,KAAXY,CAAmC,+BAAgC,CACvE4B,QAAAA,IAEFhH,KAAK4F,SAASC,QAAQ,QACtB7F,KAAKsF,gBACL,MAAOQ,GACO,WAAVA,GACF9F,KAAK4F,SAASE,MAAM,UAK1BnB,4BACE,GAAK3E,KAAKW,cAAcC,OACxB,IACEZ,KAAK4F,SAASC,QAAQ,UAEtB7F,KAAKW,cAAcuG,QAAQnB,IACzB,MAAMS,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAO,wCAAwCC,KAAKb,EAAK5D,UAC9DqE,EAAKK,SAAWd,EAAK5D,SACrBqE,EAAKjG,UAEP,MAAOuF,GACP9F,KAAK4F,SAASE,MAAM,UAIxBnB,0BACE,GAAK3E,KAAKW,cAAcC,OACxB,UACQZ,KAAK8G,SAAS,cAAe,KAAM,CACvCzG,KAAM,YAER,MAAM2G,EAAUhH,KAAKW,cAAcsG,IAAIlB,GAAQA,EAAKN,UAC9CL,OAAOZ,EAAI,KAAXY,CAAqC,uBAAwB,CACjE4B,QAAAA,IAEFhH,KAAK4F,SAASC,QAAQ,QACtB7F,KAAKsF,gBACL,MAAOQ,GACO,WAAVA,GACF9F,KAAK4F,SAASE,MAAM,UAK1BnB,YAAY1C,GACV,MAAMkF,EAAU,CACdpC,IAAO,mBACPqC,IAAO,mBACPC,KAAQ,mBACRC,IAAO,iBACPC,KAAQ,iBACRC,IAAO,mBACPC,KAAQ,mBACRC,IAAO,mBACP5C,MAAS,kBACTI,QAAW,kBAEb,OAAOiC,EAAQlF,IAAa,oBAG9B0C,eAAenF,GACb,OAAO4F,OAAOb,EAAU,KAAjBa,CAA4C5F,MAKvBmI,EAAsC,EAKpEC,GAHoExI,EAAoB,QAGlEA,EAAoB,SAW1CyI,EAAYzC,OAAOwC,EAAoB,KAA3BxC,CACduC,EACA7H,EACAuE,GACA,EACA,KACA,WACA,MAIsC/E,EAAoB,WAAcuI,EAAiB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-4ebce2ce\"],{\"093c\":function(e,t,i){},\"3b39\":function(e,t,i){\"use strict\";function l(e){return e<1024?e+\" B\":e<1048576?(e/1024).toFixed(2)+\" KB\":e<1073741824?(e/1048576).toFixed(2)+\" MB\":(e/1073741824).toFixed(2)+\" GB\"}i.d(t,\"a\",(function(){return l}))},\"4ffb\":function(e,t,i){\"use strict\";i(\"093c\")},f6e7:function(e,t,i){\"use strict\";i.r(t);var l=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"archive-container\"},[t(\"div\",{staticClass:\"operation-bar\"},[t(\"el-button-group\",[t(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleUpload}},[t(\"i\",{staticClass:\"el-icon-upload\"}),e._v(\" 上传文件 \")]),t(\"el-button\",{attrs:{type:\"success\",disabled:!e.selectedFiles.length},on:{click:e.handleBatchArchive}},[t(\"i\",{staticClass:\"el-icon-folder-add\"}),e._v(\" 批量归档 \")]),t(\"el-button\",{attrs:{type:\"warning\",disabled:!e.selectedFiles.length},on:{click:e.handleBatchDownload}},[t(\"i\",{staticClass:\"el-icon-download\"}),e._v(\" 批量下载 \")]),t(\"el-button\",{attrs:{type:\"danger\",disabled:!e.selectedFiles.length},on:{click:e.handleBatchDelete}},[t(\"i\",{staticClass:\"el-icon-delete\"}),e._v(\" 批量删除 \")])],1)],1),t(\"div\",{staticClass:\"file-list-container\"},[t(\"el-table\",{staticStyle:{width:\"100%\"},attrs:{data:e.fileList},on:{\"selection-change\":e.handleSelectionChange}},[t(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\"}}),t(\"el-table-column\",{attrs:{prop:\"fileName\",label:\"文件名\",\"min-width\":\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"div\",{staticClass:\"file-name-cell\"},[t(\"i\",{class:e.getFileIcon(i.row.fileType)}),t(\"span\",[e._v(e._s(i.row.fileName))])])]}}])}),t(\"el-table-column\",{attrs:{prop:\"fileType\",label:\"类型\",width:\"100\"}}),t(\"el-table-column\",{attrs:{prop:\"category\",label:\"分类\",width:\"120\"}}),t(\"el-table-column\",{attrs:{prop:\"size\",label:\"大小\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.formatFileSize(t.row.size))+\" \")]}}])}),t(\"el-table-column\",{attrs:{prop:\"uploadTime\",label:\"上传时间\",width:\"180\"}}),t(\"el-table-column\",{attrs:{label:\"操作\",width:\"240\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"div\",{staticClass:\"action-buttons\"},[t(\"el-button\",{attrs:{size:\"mini\",type:\"primary\"},on:{click:function(t){return e.handlePreview(i.row)}}},[e._v(\" 预览 \")]),t(\"el-button\",{attrs:{size:\"mini\",type:\"success\"},on:{click:function(t){return e.handleDownload(i.row)}}},[e._v(\" 下载 \")]),t(\"el-button\",{attrs:{size:\"mini\",type:\"danger\"},on:{click:function(t){return e.handleDelete(i.row)}}},[e._v(\" 删除 \")])],1)]}}])})],1)],1),t(\"el-dialog\",{attrs:{title:\"文件上传\",visible:e.uploadDialogVisible,width:\"500px\"},on:{\"update:visible\":function(t){e.uploadDialogVisible=t}}},[t(\"el-upload\",{staticClass:\"upload-demo\",attrs:{drag:\"\",multiple:\"\",action:e.uploadUrl,\"before-upload\":e.beforeUpload,\"on-progress\":e.handleProgress,\"on-success\":e.handleUploadSuccess,\"on-error\":e.handleUploadError,\"file-list\":e.uploadFileList}},[t(\"i\",{staticClass:\"el-icon-upload\"}),t(\"div\",{staticClass:\"el-upload__text\"},[e._v(\"将文件拖到此处，或\"),t(\"em\",[e._v(\"点击上传\")])]),t(\"div\",{staticClass:\"el-upload__tip\",attrs:{slot:\"tip\"},slot:\"tip\"},[e._v(\" 支持任意格式文件，单个文件不超过500MB \")])])],1),t(\"el-dialog\",{attrs:{title:\"文件预览\",visible:e.previewDialogVisible,width:\"80%\",fullscreen:!0},on:{\"update:visible\":function(t){e.previewDialogVisible=t}}},[t(\"div\",{staticClass:\"preview-container\"},[e.isImage?t(\"div\",{staticClass:\"image-preview\"},[t(\"img\",{attrs:{src:e.previewUrl,alt:\"预览图片\"}})]):e.isPdf?t(\"div\",{staticClass:\"pdf-preview\"},[t(\"iframe\",{attrs:{src:e.previewUrl,width:\"100%\",height:\"600px\"}})]):e.isOffice?t(\"div\",{staticClass:\"office-preview\"},[t(\"iframe\",{attrs:{src:e.previewUrl,width:\"100%\",height:\"600px\"}})]):t(\"div\",{staticClass:\"other-preview\"},[t(\"p\",[e._v(\"该文件类型暂不支持预览，请下载后查看\")])])])])],1)},s=[],a=(i(\"b7ef\"),i(\"2f62\")),c=i(\"3b39\"),r=i(\"7c15\"),o={name:\"ArchiveFile\",data(){return{fileList:[],selectedFiles:[],uploadDialogVisible:!1,previewDialogVisible:!1,uploadFileList:[],previewUrl:\"\",currentFile:null,uploadUrl:\"/archive/upload\",fileTypeMap:{image:[\"jpg\",\"jpeg\",\"png\",\"gif\",\"bmp\"],pdf:[\"pdf\"],office:[\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\"],text:[\"txt\",\"md\"],archive:[\"zip\",\"rar\",\"7z\"]}}},computed:{...Object(a[\"b\"])([\"userRole\"]),isImage(){return this.currentFile&&this.fileTypeMap.image.includes(this.currentFile.fileType)},isPdf(){return this.currentFile&&this.fileTypeMap.pdf.includes(this.currentFile.fileType)},isOffice(){return this.currentFile&&this.fileTypeMap.office.includes(this.currentFile.fileType)}},created(){this.fetchFileList()},methods:{async fetchFileList(){try{const e=[{id:1,fileName:\"合同模板.pdf\",fileType:\"pdf\",category:\"合同文件\",size:1024e3,uploadTime:\"2024-01-15 10:30:00\"},{id:2,fileName:\"案件资料.docx\",fileType:\"docx\",category:\"案件文书\",size:512e3,uploadTime:\"2024-01-14 14:20:00\"},{id:3,fileName:\"咨询记录.txt\",fileType:\"txt\",category:\"咨询记录\",size:8192,uploadTime:\"2024-01-13 16:45:00\"},{id:4,fileName:\"证据材料.jpg\",fileType:\"jpg\",category:\"案件文书\",size:2048e3,uploadTime:\"2024-01-12 09:15:00\"},{id:5,fileName:\"法律意见书.docx\",fileType:\"docx\",category:\"案件文书\",size:768e3,uploadTime:\"2024-01-11 16:30:00\"}];this.fileList=e,this.$message.success(\"文件列表加载成功\")}catch(e){this.$message.error(\"获取文件列表失败\")}},handleUpload(){this.uploadDialogVisible=!0},beforeUpload(e){const t=e.size/1024/1024<500;return!!t||(this.$message.error(\"文件大小不能超过 500MB!\"),!1)},handleProgress(e,t){console.log(\"上传进度：\",t.percentage)},handleUploadSuccess(e,t){this.$message.success(\"文件上传成功\"),this.uploadDialogVisible=!1,this.fetchFileList()},handleUploadError(){this.$message.error(\"文件上传失败\")},async handlePreview(e){this.currentFile=e,this.previewDialogVisible=!0,this.previewUrl=await this.generatePreviewUrl(e)},async generatePreviewUrl(e){try{var t;const i=await Object(r[\"b\"])(\"/archive/preview/\"+e.id);return(null===(t=i.data)||void 0===t?void 0:t.previewUrl)||\"data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit\"}catch(i){return\"data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit\"}},async handleDownload(e){try{const t=document.createElement(\"a\");t.href=\"data:application/octet-stream;base64,\"+btoa(e.fileName),t.download=e.fileName,t.click(),this.$message.success(\"开始下载\")}catch(t){this.$message.error(\"文件下载失败\")}},async handleDelete(e){try{await this.$confirm(\"确认删除该文件吗？\",\"提示\",{type:\"warning\"}),await Object(r[\"a\"])(\"/archive/files/\"+e.id),this.$message.success(\"删除成功\"),this.fetchFileList()}catch(t){\"cancel\"!==t&&this.$message.error(\"删除失败\")}},handleSelectionChange(e){this.selectedFiles=e},async handleBatchArchive(){if(this.selectedFiles.length)try{await this.$confirm(\"确认归档选中的文件吗？\",\"提示\",{type:\"warning\"});const e=this.selectedFiles.map(e=>e.id);await Object(r[\"d\"])(\"/archive/files/batch/archive\",{fileIds:e}),this.$message.success(\"归档成功\"),this.fetchFileList()}catch(e){\"cancel\"!==e&&this.$message.error(\"归档失败\")}},async handleBatchDownload(){if(this.selectedFiles.length)try{this.$message.success(\"开始批量下载\"),this.selectedFiles.forEach(e=>{const t=document.createElement(\"a\");t.href=\"data:application/octet-stream;base64,\"+btoa(e.fileName),t.download=e.fileName,t.click()})}catch(e){this.$message.error(\"下载失败\")}},async handleBatchDelete(){if(this.selectedFiles.length)try{await this.$confirm(\"确认删除选中的文件吗？\",\"提示\",{type:\"warning\"});const e=this.selectedFiles.map(e=>e.id);await Object(r[\"a\"])(\"/archive/files/batch\",{fileIds:e}),this.$message.success(\"删除成功\"),this.fetchFileList()}catch(e){\"cancel\"!==e&&this.$message.error(\"删除失败\")}},getFileIcon(e){const t={pdf:\"el-icon-document\",doc:\"el-icon-document\",docx:\"el-icon-document\",xls:\"el-icon-s-grid\",xlsx:\"el-icon-s-grid\",ppt:\"el-icon-document\",pptx:\"el-icon-document\",txt:\"el-icon-document\",image:\"el-icon-picture\",archive:\"el-icon-folder\"};return t[e]||\"el-icon-document\"},formatFileSize(e){return Object(c[\"a\"])(e)}}},n=o,d=(i(\"4ffb\"),i(\"2877\")),p=Object(d[\"a\"])(n,l,s,!1,null,\"228d4396\",null);t[\"default\"]=p.exports}}]);", "extractedComments": []}