{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue", "mtime": 1748540171920}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "desc", "is_num", "template_file", "template_name", "template_size", "rules", "required", "message", "trigger", "templateRules", "form<PERSON>abe<PERSON><PERSON>", "uploadAction", "mounted", "getData", "methods", "editData", "id", "_this", "getInfo", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "setTimeout", "allData", "create_time", "filter", "item", "includes", "length", "saveData", "$refs", "validate", "valid", "postRequest", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "beforeTemplateUpload", "isValidType", "isLt10M", "handleTemplateSuccess", "response", "validateField", "handleTemplateError", "err", "console", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "previewTemplate", "downloadCurrentTemplate", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "replaceTemplate", "replaceUpload", "$el", "querySelector", "removeTemplate", "downloadTemplate", "row", "warning"], "sources": ["src/views/pages/wenshu/cate.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-type-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 操作区域 -->\r\n    <div class=\"action-section\">\r\n      <div class=\"search-area\">\r\n        <el-input\r\n          placeholder=\"请输入合同类型名称\"\r\n          v-model=\"search.keyword\"\r\n          class=\"search-input\"\r\n          clearable\r\n        >\r\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n            type=\"primary\"\r\n          >\r\n            搜索\r\n          </el-button>\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"button-area\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"editData(0)\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-btn\"\r\n        >\r\n          新增合同类型\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同类型数据\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"合同类型名称\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-name\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"template_status\" label=\"合同模板\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"template-status\">\r\n              <el-tag\r\n                :type=\"scope.row.template_file ? 'success' : 'warning'\"\r\n                size=\"mini\"\r\n                :icon=\"scope.row.template_file ? 'el-icon-document' : 'el-icon-warning'\"\r\n              >\r\n                {{ scope.row.template_file ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-edit\"\r\n                class=\"action-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.template_file\"\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"downloadTemplate(scope.row)\"\r\n                icon=\"el-icon-download\"\r\n                class=\"action-btn\"\r\n              >\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"合同模板\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"template_file\"\r\n          :rules=\"templateRules\"\r\n        >\r\n          <div class=\"template-upload-area\">\r\n            <!-- 文件上传区域 -->\r\n            <div v-if=\"!ruleForm.template_file\" class=\"upload-section\">\r\n              <el-upload\r\n                ref=\"templateUpload\"\r\n                :action=\"uploadAction\"\r\n                :before-upload=\"beforeTemplateUpload\"\r\n                :on-success=\"handleTemplateSuccess\"\r\n                :on-error=\"handleTemplateError\"\r\n                :show-file-list=\"false\"\r\n                accept=\".doc,.docx,.pdf\"\r\n                :auto-upload=\"true\"\r\n                class=\"template-uploader\"\r\n              >\r\n                <el-button type=\"primary\" icon=\"el-icon-upload\">\r\n                  <span>上传合同模板</span>\r\n                </el-button>\r\n              </el-upload>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 已上传文件显示区域 -->\r\n            <div v-else class=\"uploaded-file\">\r\n              <div class=\"file-info\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ ruleForm.template_name || '合同模板文件' }}</span>\r\n                <span class=\"file-size\">{{ formatFileSize(ruleForm.template_size) }}</span>\r\n              </div>\r\n              <div class=\"file-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"previewTemplate\"\r\n                  icon=\"el-icon-view\"\r\n                  size=\"mini\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"downloadCurrentTemplate\"\r\n                  icon=\"el-icon-download\"\r\n                  size=\"mini\"\r\n                >\r\n                  下载\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"replaceTemplate\"\r\n                  icon=\"el-icon-refresh\"\r\n                  size=\"mini\"\r\n                >\r\n                  替换\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"removeTemplate\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  class=\"danger-text\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 隐藏的替换上传组件 -->\r\n            <el-upload\r\n              v-show=\"false\"\r\n              ref=\"replaceUpload\"\r\n              :action=\"uploadAction\"\r\n              :before-upload=\"beforeTemplateUpload\"\r\n              :on-success=\"handleTemplateSuccess\"\r\n              :on-error=\"handleTemplateError\"\r\n              :show-file-list=\"false\"\r\n              accept=\".doc,.docx,.pdf\"\r\n              :auto-upload=\"true\"\r\n            >\r\n            </el-upload>\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshucate/\",\r\n      title: \"文书类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n        template_file: \"\",\r\n        template_name: \"\",\r\n        template_size: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n\r\n      // 合同模板验证规则\r\n      templateRules: [\r\n        {\r\n          required: true,\r\n          message: \"请上传合同模板\",\r\n          trigger: \"change\",\r\n        },\r\n      ],\r\n\r\n      formLabelWidth: \"120px\",\r\n      uploadAction: \"/admin/Upload/uploadFile\", // 文件上传接口\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          template_file: \"\",\r\n          template_name: \"\",\r\n          template_size: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 模拟搜索测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n\r\n        if (_this.search.keyword) {\r\n          _this.list = allData.filter(item =>\r\n            item.title.includes(_this.search.keyword)\r\n          );\r\n        } else {\r\n          _this.list = allData;\r\n        }\r\n\r\n        _this.total = _this.list.length;\r\n        _this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 合同模板相关方法\r\n    beforeTemplateUpload(file) {\r\n      const isValidType = /\\.(doc|docx|pdf)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('合同模板只能是 .doc、.docx、.pdf 格式!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('合同模板文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传合同模板...');\r\n      return true;\r\n    },\r\n\r\n    handleTemplateSuccess(response, file) {\r\n      if (response.code === 200) {\r\n        this.ruleForm.template_file = response.data.url;\r\n        this.ruleForm.template_name = file.name;\r\n        this.ruleForm.template_size = file.size;\r\n        this.$message.success('合同模板上传成功!');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      } else {\r\n        this.$message.error(response.msg || '合同模板上传失败!');\r\n      }\r\n    },\r\n\r\n    handleTemplateError(err, file) {\r\n      this.$message.error('合同模板上传失败，请重试!');\r\n      console.error('Template upload error:', err);\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    },\r\n\r\n    // 预览模板\r\n    previewTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        // 这里可以实现文件预览功能\r\n        this.$message.info('预览功能开发中...');\r\n        // window.open(this.ruleForm.template_file, '_blank');\r\n      }\r\n    },\r\n\r\n    // 下载当前模板\r\n    downloadCurrentTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = this.ruleForm.template_file;\r\n        link.download = this.ruleForm.template_name || '合同模板';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success('开始下载合同模板');\r\n      }\r\n    },\r\n\r\n    // 替换模板\r\n    replaceTemplate() {\r\n      this.$refs.replaceUpload.$el.querySelector('input').click();\r\n    },\r\n\r\n    // 删除模板\r\n    removeTemplate() {\r\n      this.$confirm('确定要删除当前合同模板吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.ruleForm.template_file = '';\r\n        this.ruleForm.template_name = '';\r\n        this.ruleForm.template_size = 0;\r\n        this.$message.success('合同模板已删除');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 下载表格中的模板\r\n    downloadTemplate(row) {\r\n      if (row.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = row.template_file;\r\n        link.download = row.template_name || `${row.title}模板`;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success(`开始下载 ${row.title} 模板`);\r\n      } else {\r\n        this.$message.warning('该合同类型暂无模板文件');\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-type-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.search-area {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.button-area {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn {\r\n  font-weight: 500;\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.type-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.type-name i {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-name span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-type-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .action-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-area {\r\n    max-width: none;\r\n  }\r\n\r\n  .button-area {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 小屏幕下操作按钮调整 */\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n}\r\n\r\n/* 操作按钮横向布局 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n}\r\n\r\n.el-button--success:hover {\r\n  background: #85ce61;\r\n  border-color: #85ce61;\r\n}\r\n\r\n.el-button--danger:hover {\r\n  background: #f78989;\r\n  border-color: #f78989;\r\n}\r\n\r\n/* 合同模板状态样式 */\r\n.template-status {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n/* 合同模板上传区域样式 */\r\n.template-upload-area {\r\n  width: 100%;\r\n}\r\n\r\n.upload-section {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-section:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.template-uploader {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tip i {\r\n  color: #409eff;\r\n}\r\n\r\n/* 已上传文件显示样式 */\r\n.uploaded-file {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.file-info i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.file-size {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-actions .el-button--text {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n}\r\n\r\n.danger-text {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n.danger-text:hover {\r\n  color: #f78989 !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .file-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .file-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAuQA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,aAAA;QACAC,aAAA;MACA;MAEAC,KAAA;QACAX,KAAA,GACA;UACAY,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MAEA;MACAC,aAAA,GACA;QACAH,QAAA;QACAC,OAAA;QACAC,OAAA;MACA,EACA;MAEAE,cAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAAjB,QAAA;UACAL,KAAA;UACAM,IAAA;UACAE,aAAA;UACAC,aAAA;UACAC,aAAA;QACA;MACA;MAEAa,KAAA,CAAArB,iBAAA;IACA;IACAsB,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAE,UAAA,CAAAF,KAAA,CAAAxB,GAAA,gBAAAuB,EAAA,EAAAI,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAJ,KAAA,CAAAlB,QAAA,GAAAsB,IAAA,CAAArC,IAAA;QACA;MACA;IACA;IACAsC,QAAAC,KAAA,EAAAP,EAAA;MACA,KAAAQ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAnC,GAAA,kBAAAuB,EAAA,EAAAI,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACApB,OAAA;YACA;YACA,KAAArB,IAAA,CAAA6C,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACApB,OAAA;QACA;MACA;IACA;IACA0B,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAAhD,IAAA;MACA,KAAAC,IAAA;MAEA,IAAA4B,KAAA;MACAA,KAAA,CAAAzB,OAAA;;MAEA;MACA6C,UAAA;QACA,IAAAC,OAAA,IACA;UACAtB,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,EACA;QAEA,IAAAa,KAAA,CAAA3B,MAAA,CAAAC,OAAA;UACA0B,KAAA,CAAA/B,IAAA,GAAAoD,OAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAA/C,KAAA,CAAAgD,QAAA,CAAAzB,KAAA,CAAA3B,MAAA,CAAAC,OAAA,CACA;QACA;UACA0B,KAAA,CAAA/B,IAAA,GAAAoD,OAAA;QACA;QAEArB,KAAA,CAAA9B,KAAA,GAAA8B,KAAA,CAAA/B,IAAA,CAAAyD,MAAA;QACA1B,KAAA,CAAAzB,OAAA;MACA;IACA;IAEAqB,QAAA;MACA,IAAAI,KAAA;MAEAA,KAAA,CAAAzB,OAAA;;MAEA;MACA6C,UAAA;QACApB,KAAA,CAAA/B,IAAA,IACA;UACA8B,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAY,EAAA;UACAtB,KAAA;UACA6C,WAAA;UACArC,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,EACA;QACAa,KAAA,CAAA9B,KAAA;QACA8B,KAAA,CAAAzB,OAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAoD,SAAA;MACA,IAAA3B,KAAA;MACA,KAAA4B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAA/B,KAAA,CAAAxB,GAAA,gBAAAM,QAAA,EAAAqB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAZ,KAAA,CAAAa,QAAA;gBACAH,IAAA;gBACApB,OAAA,EAAAc,IAAA,CAAA4B;cACA;cACA,KAAApC,OAAA;cACAI,KAAA,CAAArB,iBAAA;YACA;cACAqB,KAAA,CAAAa,QAAA;gBACAH,IAAA;gBACApB,OAAA,EAAAc,IAAA,CAAA4B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA9D,IAAA,GAAA8D,GAAA;MAEA,KAAAtC,OAAA;IACA;IACAuC,oBAAAD,GAAA;MACA,KAAA/D,IAAA,GAAA+D,GAAA;MACA,KAAAtC,OAAA;IACA;IACAwC,cAAAC,GAAA;MACA,KAAAvD,QAAA,CAAAwD,QAAA,GAAAD,GAAA,CAAAtE,IAAA,CAAAS,GAAA;IACA;IAEA+D,UAAAC,IAAA;MACA,KAAA5D,UAAA,GAAA4D,IAAA;MACA,KAAA3D,aAAA;IACA;IACA4D,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA9B,IAAA;MACA,KAAAgC,UAAA;QACA,KAAA7B,QAAA,CAAA+B,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAA9C,KAAA;MACAA,KAAA,CAAAE,UAAA,gCAAAsC,IAAA,EAAArC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAZ,KAAA,CAAAlB,QAAA,CAAAgE,QAAA;UAEA9C,KAAA,CAAAa,QAAA,CAAAkC,OAAA;QACA;UACA/C,KAAA,CAAAa,QAAA,CAAA+B,KAAA,CAAAxC,IAAA,CAAA4B,GAAA;QACA;MACA;IACA;IAEA;IACAgB,qBAAAR,IAAA;MACA,MAAAS,WAAA,wBAAAN,IAAA,CAAAH,IAAA,CAAA3E,IAAA;MACA,MAAAqF,OAAA,GAAAV,IAAA,CAAApE,IAAA;MAEA,KAAA6E,WAAA;QACA,KAAApC,QAAA,CAAA+B,KAAA;QACA;MACA;MACA,KAAAM,OAAA;QACA,KAAArC,QAAA,CAAA+B,KAAA;QACA;MACA;MAEA,KAAA/B,QAAA,CAAAnC,IAAA;MACA;IACA;IAEAyE,sBAAAC,QAAA,EAAAZ,IAAA;MACA,IAAAY,QAAA,CAAAxC,IAAA;QACA,KAAA9B,QAAA,CAAAG,aAAA,GAAAmE,QAAA,CAAArF,IAAA,CAAAS,GAAA;QACA,KAAAM,QAAA,CAAAI,aAAA,GAAAsD,IAAA,CAAA3E,IAAA;QACA,KAAAiB,QAAA,CAAAK,aAAA,GAAAqD,IAAA,CAAApE,IAAA;QACA,KAAAyC,QAAA,CAAAkC,OAAA;;QAEA;QACA,KAAAnB,KAAA,CAAA9C,QAAA,CAAAuE,aAAA;MACA;QACA,KAAAxC,QAAA,CAAA+B,KAAA,CAAAQ,QAAA,CAAApB,GAAA;MACA;IACA;IAEAsB,oBAAAC,GAAA,EAAAf,IAAA;MACA,KAAA3B,QAAA,CAAA+B,KAAA;MACAY,OAAA,CAAAZ,KAAA,2BAAAW,GAAA;IACA;IAEA;IACAE,eAAAC,KAAA;MACA,IAAAA,KAAA;MACA,MAAAC,CAAA;MACA,MAAAC,KAAA;MACA,MAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,GAAA,CAAAN,KAAA,IAAAI,IAAA,CAAAE,GAAA,CAAAL,CAAA;MACA,OAAAM,UAAA,EAAAP,KAAA,GAAAI,IAAA,CAAAI,GAAA,CAAAP,CAAA,EAAAE,CAAA,GAAAM,OAAA,aAAAP,KAAA,CAAAC,CAAA;IACA;IAEA;IACAO,gBAAA;MACA,SAAAtF,QAAA,CAAAG,aAAA;QACA;QACA,KAAA4B,QAAA,CAAAnC,IAAA;QACA;MACA;IACA;IAEA;IACA2F,wBAAA;MACA,SAAAvF,QAAA,CAAAG,aAAA;QACA,MAAAqF,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,QAAA3F,QAAA,CAAAG,aAAA;QACAqF,IAAA,CAAAI,QAAA,QAAA5F,QAAA,CAAAI,aAAA;QACAqF,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;QACAA,IAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;QACA,KAAAzD,QAAA,CAAAkC,OAAA;MACA;IACA;IAEA;IACAgC,gBAAA;MACA,KAAAnD,KAAA,CAAAoD,aAAA,CAAAC,GAAA,CAAAC,aAAA,UAAAL,KAAA;IACA;IAEA;IACAM,eAAA;MACA,KAAA5E,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,KAAArB,QAAA,CAAAG,aAAA;QACA,KAAAH,QAAA,CAAAI,aAAA;QACA,KAAAJ,QAAA,CAAAK,aAAA;QACA,KAAA0B,QAAA,CAAAkC,OAAA;;QAEA;QACA,KAAAnB,KAAA,CAAA9C,QAAA,CAAAuE,aAAA;MACA,GAAAtC,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAqE,iBAAAC,GAAA;MACA,IAAAA,GAAA,CAAApG,aAAA;QACA,MAAAqF,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAY,GAAA,CAAApG,aAAA;QACAqF,IAAA,CAAAI,QAAA,GAAAW,GAAA,CAAAnG,aAAA,OAAAmG,GAAA,CAAA5G,KAAA;QACA8F,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;QACAA,IAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;QACA,KAAAzD,QAAA,CAAAkC,OAAA,SAAAsC,GAAA,CAAA5G,KAAA;MACA;QACA,KAAAoC,QAAA,CAAAyE,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}