(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4e97bb1e"],{"2bcb":function(e,t,l){},3520:function(e,t,l){"use strict";l("2bcb")},bfef:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新 ")])],1),t("el-row",{staticStyle:{width:"600px"}},[t("el-input",{attrs:{placeholder:"请输入名称/手机号/公司名称",size:"mini"},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.searchData()}},slot:"append"})],1),t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-top"},on:{click:e.exports}},[e._v(" 导出列表 ")]),t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-bottom"},on:{click:e.openUpload}},[e._v("导入用户 ")]),t("el-button",{staticStyle:{"margin-top":"5px"},attrs:{size:"small",type:"primary"},on:{click:e.addUser}},[e._v("添加用户")]),t("a",{staticStyle:{"text-decoration":"none",color:"#4397fd","font-weight":"800","margin-left":"10px"},attrs:{href:"/import_templete/user.xls"}},[e._v("下载导入模板")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"},on:{"sort-change":e.handleSortChange}},[t("el-table-column",{attrs:{prop:"phone",label:"注册手机号码"}}),t("el-table-column",{attrs:{prop:"company",label:"公司名称",sortable:""}}),t("el-table-column",{attrs:{prop:"nickname",label:"名称",sortable:""}}),t("el-table-column",{attrs:{prop:"",label:"头像"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("div",[""==e.row.headimg?t("el-row"):t("el-row",[t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.row.headimg}})])],1)]}}])}),t("el-table-column",{attrs:{prop:"linkman",label:"联系人",sortable:""}}),t("el-table-column",{attrs:{prop:"linkphone",label:"联系号码",sortable:""}}),t("el-table-column",{attrs:{prop:"yuangong_id",label:"用户来源"}}),t("el-table-column",{attrs:{prop:"end_time",label:"到期时间"}}),t("el-table-column",{attrs:{prop:"create_time",label:"录入时间",sortable:""}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewData(l.row.id)}}},[e._v("查看详情 ")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(l.row.id)}}},[e._v("编辑资料 ")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.order(l.row)}}},[e._v("制作订单 ")]),e.is_del?t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(l.$index,l.row.id)}}},[e._v(" 移除 ")]):e._e()]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.ruleForm.company)+" ")]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.ruleForm.phone)+" ")]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.ruleForm.nickname)+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.ruleForm.linkman)+" ")]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.info.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id)+" ")])],1)],1),t("el-descriptions",{attrs:{title:"信息编辑"}}),t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"公司名称","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.company,callback:function(t){e.$set(e.ruleForm,"company",t)},expression:"ruleForm.company"}})],1),t("el-form-item",{attrs:{label:"联系人","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkman,callback:function(t){e.$set(e.ruleForm,"linkman",t)},expression:"ruleForm.linkman"}})],1),t("el-form-item",{attrs:{label:"联系方式","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkphone,callback:function(t){e.$set(e.ruleForm,"linkphone",t)},expression:"ruleForm.linkphone"}})],1),t("el-form-item",{attrs:{label:"登录密码","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),t("el-form-item",{attrs:{label:"调解员",prop:"tiaojie_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.tiaojie_id,callback:function(t){e.$set(e.ruleForm,"tiaojie_id",t)},expression:"ruleForm.tiaojie_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.tiaojies,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"法务专员",prop:"fawu_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.fawu_id,callback:function(t){e.$set(e.ruleForm,"fawu_id",t)},expression:"ruleForm.fawu_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.fawus,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"立案专员",prop:"lian_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.lian_id,callback:function(t){e.$set(e.ruleForm,"lian_id",t)},expression:"ruleForm.lian_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lians,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"合同上传专用",prop:"htsczy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.htsczy_id,callback:function(t){e.$set(e.ruleForm,"htsczy_id",t)},expression:"ruleForm.htsczy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.htsczy,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"律师",prop:"ls_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ls_id,callback:function(t){e.$set(e.ruleForm,"ls_id",t)},expression:"ruleForm.ls_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ls,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"业务员",prop:"ywy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ywy_id,callback:function(t){e.$set(e.ruleForm,"ywy_id",t)},expression:"ruleForm.ywy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ywy,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{"label-width":e.formLabelWidth,label:"营业执照"}},[""!=e.ruleForm.license&&null!=e.ruleForm.license?t("img",{staticStyle:{width:"400px",height:"400px"},attrs:{src:e.ruleForm.license},on:{click:function(t){return e.showImage(e.ruleForm.license)}}}):e._e()]),t("el-form-item",{attrs:{label:"开始时间","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleForm.start_time,callback:function(t){e.$set(e.ruleForm,"start_time",t)},expression:"ruleForm.start_time"}})],1),t("el-form-item",{attrs:{label:"会员年限","label-width":e.formLabelWidth}},[t("el-input-number",{attrs:{min:0,max:99,label:"请输入年份"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,"year",t)},expression:"ruleForm.year"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-dialog",{attrs:{title:"制作订单",visible:e.dialogFormOrder,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogFormOrder=t}}},[t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company)+" ")]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone)+" ")]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname)+" ")]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman)+" ")]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.ruleForm.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id)+" ")]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")])],1)],1),t("el-descriptions",{attrs:{title:"下单内容"}}),t("el-form",{ref:"orderForm",attrs:{model:e.orderForm,rules:e.rules2,"label-width":"80px",mode:"left"}},[t("el-form-item",{attrs:{label:"套餐",prop:"taocan_id"}},[t("el-select",{attrs:{placeholder:"请选择"},on:{change:e.changeTaocan},model:{value:e.orderForm.taocan_id,callback:function(t){e.$set(e.orderForm,"taocan_id",t)},expression:"orderForm.taocan_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.taocans,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"总金额"}},[t("el-input",{staticClass:"el_input2",attrs:{type:"number",placeholder:"请输入内容"},model:{value:e.orderForm.total_price,callback:function(t){e.$set(e.orderForm,"total_price",t)},expression:"orderForm.total_price"}})],1),t("el-form-item",{attrs:{label:"实际支付",prop:"pay_price"}},[t("el-input",{staticClass:"el_input2",attrs:{placeholder:"请输入内容"},model:{value:e.orderForm.pay_price,callback:function(t){e.$set(e.orderForm,"pay_price",t)},expression:"orderForm.pay_price"}})],1),t("el-form-item",{attrs:{label:"客户描述"}},[t("el-input",{staticClass:"el_input2",attrs:{type:"textarea",rows:3,placeholder:"请输入内容"},model:{value:e.orderForm.desc,callback:function(t){e.$set(e.orderForm,"desc",t)},expression:"orderForm.desc"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormOrder=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData2()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"导入用户",visible:e.uploadVisible,width:"30%"},on:{"update:visible":function(t){e.uploadVisible=t},close:e.closeUploadDialog}},[t("el-form",{ref:"uploadForm",attrs:{"label-position":"right","label-width":"110px"}},[t("el-form-item",{attrs:{label:"选择文件:"}},[t("el-upload",{ref:"upload",attrs:{"auto-upload":!1,action:e.uploadAction,data:e.uploadData,"on-success":e.uploadSuccess,"before-upload":e.checkFile,accept:".xls,.xlsx",limit:"1",multiple:"false"}},[t("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选择文件")])],1)],1),t("div",{staticStyle:{"text-align":"right"}},[t("el-button",{attrs:{type:"primary",size:"small",loading:e.submitOrderLoading2},on:{click:e.submitUpload}},[e._v("提交 ")]),t("el-button",{attrs:{size:"small"},on:{click:e.closeDialog}},[e._v("取消")])],1)],1)],1),t("el-dialog",{attrs:{title:"用户详情",visible:e.dialogViewUserDetail,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogViewUserDetail=t}}},[t("user-details",{attrs:{id:e.currentId}})],1),t("el-dialog",{attrs:{title:"新增用户",visible:e.dialogAddUser,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogAddUser=t}}},[t("el-descriptions",{attrs:{title:"信息添加"}}),t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"手机账号","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),t("el-form-item",{attrs:{label:"公司名称","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.company,callback:function(t){e.$set(e.ruleForm,"company",t)},expression:"ruleForm.company"}})],1),t("el-form-item",{attrs:{label:"联系人","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkman,callback:function(t){e.$set(e.ruleForm,"linkman",t)},expression:"ruleForm.linkman"}})],1),t("el-form-item",{attrs:{label:"联系方式","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.linkphone,callback:function(t){e.$set(e.ruleForm,"linkphone",t)},expression:"ruleForm.linkphone"}})],1),t("el-form-item",{attrs:{label:"登录密码","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),t("el-form-item",{attrs:{label:"调解员",prop:"tiaojie_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.tiaojie_id,callback:function(t){e.$set(e.ruleForm,"tiaojie_id",t)},expression:"ruleForm.tiaojie_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.tiaojies,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"法务专员",prop:"fawu_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.fawu_id,callback:function(t){e.$set(e.ruleForm,"fawu_id",t)},expression:"ruleForm.fawu_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.fawus,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"立案专员",prop:"lian_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.lian_id,callback:function(t){e.$set(e.ruleForm,"lian_id",t)},expression:"ruleForm.lian_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lians,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"合同上传专用",prop:"htsczy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.htsczy_id,callback:function(t){e.$set(e.ruleForm,"htsczy_id",t)},expression:"ruleForm.htsczy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.htsczy,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"律师",prop:"ls_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ls_id,callback:function(t){e.$set(e.ruleForm,"ls_id",t)},expression:"ruleForm.ls_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ls,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"业务员",prop:"ywy_id","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.ywy_id,callback:function(t){e.$set(e.ruleForm,"ywy_id",t)},expression:"ruleForm.ywy_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.ywy,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{"label-width":e.formLabelWidth,label:"营业执照"}},[""!=e.ruleForm.license&&null!=e.ruleForm.license?t("img",{staticStyle:{width:"400px",height:"400px"},attrs:{src:e.ruleForm.license},on:{click:function(t){return e.showImage(e.ruleForm.license)}}}):e._e()]),t("el-form-item",{attrs:{label:"开始时间","label-width":e.formLabelWidth,prop:"day"}},[t("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.ruleForm.start_time,callback:function(t){e.$set(e.ruleForm,"start_time",t)},expression:"ruleForm.start_time"}})],1),t("el-form-item",{attrs:{label:"会员年限","label-width":e.formLabelWidth}},[t("el-input-number",{attrs:{min:0,max:99,label:"请输入年份"},model:{value:e.ruleForm.year,callback:function(t){e.$set(e.ruleForm,"year",t)},expression:"ruleForm.year"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogAddUser=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1)],1)},i=[],r=l("d522"),o={name:"list",components:{UserDetails:r["a"]},data(){return{uploadAction:"/admin/user/import?token="+this.$store.getters.GET_TOKEN,uploadVisible:!1,submitOrderLoading2:!1,uploadData:{review:!1},allSize:"mini",list:[],total:1,page:1,size:20,currentId:0,search:{keyword:"",prop:"",order:""},is_del:!1,loading:!0,url:"/user/",title:"用户",info:{},dialogFormVisible:!1,dialogViewUserDetail:!1,dialogAddUser:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}]},formLabelWidth:"120px",dialogFormOrder:!1,taocans:[],tiaojies:[],fawus:[],lians:[],htsczy:[],ls:[],ywy:[],orderForm:{client_id:"",taocan_id:"",tiaojie_id:"",fawu_id:"",lian_id:"",htsczy_id:"",ls_id:"",ywy_id:"",total_price:"",pay_price:0,pay_path:"",desc:"",pay_type:1,qishu:2,taocan_year:"",taocan_content:[],taocan_type:1,fenqi:[{date:"",price:"",pay_path:""},{date:"",price:"",pay_path:""}]},rules2:{taocan_id:[{required:!0,message:"请选择套餐",trigger:"blur"}],pay_path:[{required:!0,message:"请上传凭证",trigger:"blur"}],taocan_year:[{required:!0,message:"请填写年份",trigger:"blur"}],pay_price:[{required:!0,message:"请填写支付金额",trigger:"blur"}],desc:[{required:!0,message:"请填写内容",trigger:"blur"}]}}},mounted(){this.getData()},methods:{order(e){this.dialogFormOrder=!0,this.info=e,this.orderForm={client_id:e.id,taocan_id:"",total_price:"",pay_price:0,pay_path:"",desc:"",pay_type:1},this.$nextTick(()=>{this.getTaocans()})},saveData2(){let e=this;this.$refs["orderForm"].validate(t=>{if(!t)return!1;this.postRequest("/dingdan/save",this.orderForm).then(t=>{200==t.code&&(e.$message({type:"success",message:t.msg}),e.dialogFormOrder=!1)})})},changeTaocan(e){this.orderForm.taocan_content=[],this.orderForm.taocan_type=1,this.getRequest("/taocan/read?id="+e).then(e=>{200==e.code&&(this.orderForm.total_price=e.data.price,this.orderForm.pay_price=e.data.price)})},getTaocans(){this.postRequest("/taocan/getList",{}).then(e=>{200==e.code&&(this.taocans=e.data)})},getYuangongs(){let e=this;this.postRequest("/yuangong/getList",{}).then(t=>{200==t.code&&(e.tiaojies=t.data.filter(e=>6==e.zhiwei_id),e.fawus=t.data.filter(e=>5==e.zhiwei_id),e.lians=t.data.filter(e=>12==e.zhiwei_id),e.ywy=t.data.filter(e=>3==e.zhiwei_id),e.ls=t.data.filter(e=>4==e.zhiwei_id),e.htsczy=t.data.filter(e=>9==e.zhiwei_id))})},viewData(e){let t=this;0!=e&&(this.currentId=e),t.dialogViewUserDetail=!0},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""},t.dialogFormVisible=!0,t.getYuangongs()},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(e.data.tiaojie_id=0==e.data.tiaojie_id?"":e.data.tiaojie_id,e.data.fawu_id=0==e.data.fawu_id?"":e.data.fawu_id,e.data.lian_id=0==e.data.lian_id?"":e.data.lian_id,e.data.ywy_id=0==e.data.ywy_id?"":e.data.ywy_id,e.data.htsczy_id=0==e.data.htsczy_id?"":e.data.htsczy_id,e.data.ls_id=0==e.data.ls_id?"":e.data.ls_id,t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.postRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count,"超级管理员"==t.msg&&(e.is_del=!0)),e.loading=!1})},saveData(){let e=this;console.log(this.ruleForm),this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1,e.dialogAddUser=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let l=this;l.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(l.ruleForm[t]="",l.$message.success("删除成功!")):l.$message.error(e.msg)})},handleSortChange({column:e,prop:t,order:l}){this.search.prop=t,this.search.order=l,this.getData()},exports:function(){let e=this;location.href="/admin/user/export2?token="+e.$store.getters.GET_TOKEN+"&keyword="+e.search.keyword},closeUploadDialog(){this.uploadVisible=!1,this.$refs.upload.clearFiles(),this.uploadData.review=!1},uploadSuccess(e){200===e.code?(this.$message({type:"success",message:e.msg}),this.uploadVisible=!1,this.getData(),console.log(e)):this.$message({type:"warning",message:e.msg}),this.submitOrderLoading2=!1,this.$refs.upload.clearFiles()},checkFile(e){let t=["xls","xlsx"],l=e.name.split(".").slice(-1)[0].toLowerCase();return!!t.includes(l)||(this.$message({type:"warning",message:"文件格式错误仅支持 xls xlxs 文件"}),!1)},submitUpload(){this.submitOrderLoading2=!0,this.$refs.upload.submit()},closeDialog(){this.addVisible=!1,this.uploadVisible=!1,this.form={id:"",nickname:"",mobile:"",school_id:0,grade_id:"",class_id:"",sex:"",is_poor:"",is_display:"",number:"",remark:"",is_remark_option:0,remark_option:[],mobile_checked:!1},this.$refs.form.resetFields()},openUpload(){this.uploadVisible=!0},addUser(){this.dialogAddUser=!0,this.ruleForm={},this.getYuangongs()}}},s=o,n=(l("3520"),l("2877")),d=Object(n["a"])(s,a,i,!1,null,"260363d9",null);t["default"]=d.exports},d522:function(e,t,l){"use strict";var a=function(){var e=this,t=e._self._c;return t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company))]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone))]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname))]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman))]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.info.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id))]),t("el-descriptions-item",{attrs:{label:"联系方式"}},[e._v(e._s(e.info.linkphone))]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"营业执照"}},[""!=e.info.license&&null!=e.info.license?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.license},on:{click:function(t){return e.showImage(e.info.license)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(e._s(e.info.start_time))]),t("el-descriptions-item",{attrs:{label:"会员年限"}},[e._v(e._s(e.info.year)+"年")])],1)],1)},i=[],r={name:"UserDetails",props:{id:{type:String,required:!0}},data(){return{info:[]}},watch:{id:{immediate:!0,handler(e){this.getInfo(e)}}},methods:{getInfo(e){let t=this;t.getRequest("/user/read?id="+e).then(e=>{e&&(t.info=e.data)})}}},o=r,s=l("2877"),n=Object(s["a"])(o,a,i,!1,null,null,null);t["a"]=n.exports}}]);
//# sourceMappingURL=chunk-4e97bb1e.55c21a90.js.map