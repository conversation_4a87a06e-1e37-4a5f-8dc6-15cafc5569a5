<?php
namespace app\index\validate;

use think\Validate;

class User extends Validate
{
    protected $rule =   [

    	'phone'=>'checkPhone:请输入正确的手机号码',
    	'password'=>'length:3,30|alphaDash',
       
    ];
    
    protected $message  =   [
      	// 'password.length' => '密码长度为3到30位字符',
    ];

    protected $field = [
        'phone'=>'手机号',
        'password'=>'密码'
    ];

  protected  function checkPhone($value,$rule)
	{
	
		if(!preg_match("/^1[345789]\d{9}$/", $value)) return $rule;
		else return true;
			
	}
}