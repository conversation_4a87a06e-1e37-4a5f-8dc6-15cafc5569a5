(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2eb0cf1e"],{4166:function(e,t,l){"use strict";l("5aa9")},"5aa9":function(e,t,l){},c22b:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"always"}},[t("div",{staticStyle:{"margin-top":"20px"}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"label-width":"140px",size:"mini"}},[t("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"基础管理",name:"first"}},[t("el-form-item",{attrs:{label:"网站名称"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.site_name,callback:function(t){e.$set(e.ruleForm,"site_name",t)},expression:"ruleForm.site_name"}})],1),t("el-form-item",{attrs:{label:"公司名称"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.company_name,callback:function(t){e.$set(e.ruleForm,"company_name",t)},expression:"ruleForm.company_name"}})],1),t("el-form-item",{attrs:{label:"联系方式"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.site_tel,callback:function(t){e.$set(e.ruleForm,"site_tel",t)},expression:"ruleForm.site_tel"}})],1),t("el-form-item",{attrs:{label:"邮箱"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.email,callback:function(t){e.$set(e.ruleForm,"email",t)},expression:"ruleForm.email"}})],1),t("el-form-item",{attrs:{label:"地址"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.site_address,callback:function(t){e.$set(e.ruleForm,"site_address",t)},expression:"ruleForm.site_address"}})],1),t("el-form-item",{attrs:{label:"icp备案号"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.site_icp,callback:function(t){e.$set(e.ruleForm,"site_icp",t)},expression:"ruleForm.site_icp"}})],1),t("el-form-item",{attrs:{label:"icp备案连接"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.site_icp_url,callback:function(t){e.$set(e.ruleForm,"site_icp_url",t)},expression:"ruleForm.site_icp_url"}})],1),t("el-form-item",{attrs:{label:"logo","label-width":e.formLabelWidth}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.site_logo,callback:function(t){e.$set(e.ruleForm,"site_logo",t)},expression:"ruleForm.site_logo"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFiled("site_logo")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.site_logo?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.site_logo)}}},[e._v("查看 ")]):e._e(),e.ruleForm.site_logo?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.site_logo,"site_logo")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"我的详情推广律师","label-width":e.formLabelWidth}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.lvshi,callback:function(t){e.$set(e.ruleForm,"lvshi",t)},expression:"ruleForm.lvshi"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.lvshi,(function(e,l){return t("el-option",{key:l,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:"我的详情推广标题"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.my_title,callback:function(t){e.$set(e.ruleForm,"my_title",t)},expression:"ruleForm.my_title"}})],1),t("el-form-item",{attrs:{label:"我的详情推广语"}},[t("el-input",{staticClass:"el_input",model:{value:e.ruleForm.my_desc,callback:function(t){e.$set(e.ruleForm,"my_desc",t)},expression:"ruleForm.my_desc"}})],1),t("el-form-item",{attrs:{label:"我的详情推广图","label-width":e.formLabelWidth}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.about_path,callback:function(t){e.$set(e.ruleForm,"about_path",t)},expression:"ruleForm.about_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeFiled("about_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.about_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.about_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.about_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.about_path,"about_path")}}},[e._v("删除")]):e._e()],1)],1)],1),t("el-tab-pane",{attrs:{label:"隐私条款",name:"yinsi"}},[t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:12},model:{value:e.ruleForm.yinsi,callback:function(t){e.$set(e.ruleForm,"yinsi",t)},expression:"ruleForm.yinsi"}})],1)],1),t("el-tab-pane",{attrs:{label:"关于我们",name:"about"}},[t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("editor-bar",{attrs:{isClear:e.isClear},on:{change:e.change},model:{value:e.ruleForm.index_about_content,callback:function(t){e.$set(e.ruleForm,"index_about_content",t)},expression:"ruleForm.index_about_content"}})],1)],1),t("el-tab-pane",{attrs:{label:"团队介绍",name:"team"}},[t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("editor-bar",{attrs:{isClear:e.isClear},on:{change:e.change},model:{value:e.ruleForm.index_team_content,callback:function(t){e.$set(e.ruleForm,"index_team_content",t)},expression:"ruleForm.index_team_content"}})],1)],1)],1),t("el-form-item",[t("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{type:"primary"},on:{click:e.saveData}},[e._v("提交 ")])],1)],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},s=[],o=l("0c98"),i={name:"edit",components:{EditorBar:o["a"]},data(){return{ruleForm:{},activeName:"first",url:"/Config/",fullscreenLoading:!1,show_image:"",dialogVisible:!1,filedName:"",isClear:!0,lvshi:[]}},mounted(){this.getAllData(),this.getList()},methods:{getList(){this.postRequest("/lvshi/getList",{}).then(e=>{200==e.code&&(this.lvshi=e.data)})},changeFiled(e){this.filedName=e},change(){},getAllData(){let e=this;e.getRequest(e.url+"index").then(t=>{t&&(e.ruleForm=t.data)})},handleSuccess(e){this.ruleForm[this.filedName]=e.data.url,this.saveData()},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let l=this;l.getRequest("/unit.Upload/delImage?fileName="+e).then(e=>{200==e.code?(l.ruleForm[t]="",l.saveData(),l.$message.success("删除成功!")):l.$message.error(e.msg)})},showImage(e){this.show_image=e,this.dialogVisible=!0},handleClick(){},saveData(){let e=this;e.fullscreenLoading=!0,this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code&&e.$message({type:"success",message:t.msg}),e.fullscreenLoading=!1})}}},r=i,n=(l("4166"),l("2877")),u=Object(n["a"])(r,a,s,!1,null,null,null);t["default"]=u.exports}}]);
//# sourceMappingURL=chunk-2eb0cf1e.39c2dae3.js.map