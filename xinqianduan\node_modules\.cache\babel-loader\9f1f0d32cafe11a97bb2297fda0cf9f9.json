{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\dingzhi.vue", "mtime": 1748439252432}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetails", "name", "components", "data", "allSize", "list", "total", "currentId", "page", "size", "search", "keyword", "is_pay", "is_deal", "loading", "url", "title", "info", "dialogFormVisible", "dialogViewUserDetail", "dialogAIGenerate", "show_image", "dialogVisible", "ruleForm", "is_num", "currentOrder", "currentUserInfo", "matchedContractType", "contractTypes", "aiGenerating", "aiProgress", "aiProgressText", "generatedContract", "rules", "required", "message", "trigger", "file_path", "form<PERSON>abe<PERSON><PERSON>", "options", "id", "options1", "mounted", "getData", "getContractTypes", "methods", "changeFile", "filed", "console", "log", "clearData", "viewUserData", "_this", "editData", "getInfo", "desc", "setTimeout", "item", "find", "type", "content", "$message", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "then", "deleteRequest", "resp", "code", "msg", "catch", "delData", "index", "splice", "refulsh", "$router", "go", "searchData", "allData", "order_sn", "nickname", "phone", "uid", "create_time", "filteredData", "filter", "includes", "length", "saveData", "$refs", "validate", "valid", "postRequest", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "getRequest", "template_file", "template_name", "template_size", "generateAIContract", "orderData", "matchContractType", "getUserInfo", "orderTitle", "keywords", "matchedType", "userInfoMap", "id_card", "address", "startAIGeneration", "simulateAIGeneration", "steps", "progress", "text", "currentStep", "updateProgress", "generateContractContent", "contractTemplate", "Date", "toLocaleString", "saveGeneratedContract", "warning", "orderIndex", "findIndex", "submitForReview", "row", "review_status", "submit_time", "review_step"], "sources": ["src/views/pages/wenshu/dingzhi.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-custom-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">{{ this.$router.currentRoute.name }}</h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">处理状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"150\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"200\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.is_deal == 2 ? 'success' : scope.row.is_deal == 1 ? 'warning' : 'info'\"\r\n              size=\"small\"\r\n            >\r\n              {{ scope.row.is_deal == 2 ? '已处理' : scope.row.is_deal == 1 ? '处理中' : '待处理' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"160\">\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- AI生成按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.type === '合同定制'\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"generateAIContract(scope.row)\"\r\n                icon=\"el-icon-magic-stick\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                AI生成\r\n              </el-button>\r\n\r\n              <!-- 完成制作按钮 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-check\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                完成制作\r\n              </el-button>\r\n\r\n              <!-- 提交审核按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.is_deal === 2\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitForReview(scope.row)\"\r\n                icon=\"el-icon-upload\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                提交审核\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-close\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同要求\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- AI生成合同对话框 -->\r\n    <el-dialog\r\n      title=\"AI生成合同\"\r\n      :visible.sync=\"dialogAIGenerate\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"ai-generate-dialog\"\r\n    >\r\n      <div class=\"ai-generate-content\">\r\n        <!-- 工单信息展示 -->\r\n        <div class=\"order-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            工单信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>工单标题：</label>\r\n              <span>{{ currentOrder.title }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>工单内容：</label>\r\n              <span>{{ currentOrder.desc }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>合同类型：</label>\r\n              <span>{{ matchedContractType.title || '未匹配到合同类型' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            用户信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>用户姓名：</label>\r\n              <span>{{ currentUserInfo.nickname }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>联系电话：</label>\r\n              <span>{{ currentUserInfo.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>身份证号：</label>\r\n              <span>{{ currentUserInfo.id_card || '未填写' }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>地址：</label>\r\n              <span>{{ currentUserInfo.address || '未填写' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息展示 -->\r\n        <div class=\"template-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document-copy\"></i>\r\n            合同模板\r\n          </h3>\r\n          <div v-if=\"matchedContractType.template_file\" class=\"template-info\">\r\n            <div class=\"template-file\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ matchedContractType.template_name }}</span>\r\n              <el-tag type=\"success\" size=\"mini\">已找到模板</el-tag>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"no-template\">\r\n            <el-alert\r\n              title=\"未找到对应的合同模板\"\r\n              type=\"warning\"\r\n              description=\"请先在合同类型管理中为该类型上传模板文件\"\r\n              show-icon\r\n              :closable=\"false\">\r\n            </el-alert>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- AI生成进度 -->\r\n        <div v-if=\"aiGenerating\" class=\"ai-progress-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            AI生成中...\r\n          </h3>\r\n          <el-progress\r\n            :percentage=\"aiProgress\"\r\n            :status=\"aiProgress === 100 ? 'success' : null\"\r\n            :stroke-width=\"8\"\r\n          >\r\n          </el-progress>\r\n          <p class=\"progress-text\">{{ aiProgressText }}</p>\r\n        </div>\r\n\r\n        <!-- 生成结果 -->\r\n        <div v-if=\"generatedContract\" class=\"result-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-check\"></i>\r\n            生成结果\r\n          </h3>\r\n          <div class=\"contract-preview\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"generatedContract\"\r\n              :rows=\"15\"\r\n              placeholder=\"AI生成的合同内容将显示在这里...\"\r\n              class=\"contract-content\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogAIGenerate = false\">取消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"startAIGeneration\"\r\n          :loading=\"aiGenerating\"\r\n          :disabled=\"!matchedContractType.template_file\"\r\n        >\r\n          {{ aiGenerating ? 'AI生成中...' : '开始AI生成' }}\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"generatedContract\"\r\n          type=\"success\"\r\n          @click=\"saveGeneratedContract\"\r\n        >\r\n          保存合同\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/dingzhi/\",\r\n      title: \"合同定制\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogAIGenerate: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      // AI生成相关数据\r\n      currentOrder: {},\r\n      currentUserInfo: {},\r\n      matchedContractType: {},\r\n      contractTypes: [], // 合同类型列表\r\n      aiGenerating: false,\r\n      aiProgress: 0,\r\n      aiProgressText: '',\r\n      generatedContract: '',\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      console.log('viewUserData 被调用，传入的 ID:', id);\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n        console.log('设置 currentId 为:', this.currentId);\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n      console.log('打开用户详情抽屉');\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      console.log('getInfo 被调用，ID:', id);\r\n\r\n      // 使用测试数据，因为API可能不可用\r\n      setTimeout(() => {\r\n        // 从列表数据中找到对应的项目\r\n        const item = _this.list.find(item => item.id === id);\r\n        if (item) {\r\n          _this.ruleForm = {\r\n            id: item.id,\r\n            title: item.title,\r\n            desc: item.desc,\r\n            is_deal: item.is_deal,\r\n            type: item.type === \"合同定制\" ? 1 : 2,\r\n            content: \"\",\r\n            file_path: \"\"\r\n          };\r\n          console.log('设置表单数据:', _this.ruleForm);\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: \"未找到对应的数据\",\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n      // 原始API调用（注释掉）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"WD202403001\",\r\n            type: \"合同定制\",\r\n            title: \"劳动合同定制\",\r\n            desc: \"需要定制一份标准的劳动合同模板，包含薪资、工作时间、福利待遇等条款\",\r\n            is_deal: 0,\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"WD202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"请帮忙审核房屋租赁合同，检查条款是否合理，有无法律风险\",\r\n            is_deal: 1,\r\n            nickname: \"李四\",\r\n            phone: \"13800138002\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"WD202403003\",\r\n            type: \"合同定制\",\r\n            title: \"买卖合同定制\",\r\n            desc: \"需要定制商品买卖合同，涉及货物交付、付款方式、违约责任等\",\r\n            is_deal: 2,\r\n            nickname: \"王五\",\r\n            phone: \"13800138003\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"WD202403004\",\r\n            type: \"法律咨询\",\r\n            title: \"服务合同咨询\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            is_deal: 1,\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"WD202403005\",\r\n            type: \"合同定制\",\r\n            title: \"借款合同定制\",\r\n            desc: \"需要定制个人借款合同，明确借款金额、利率、还款方式等条款\",\r\n            is_deal: 0,\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成合同\r\n    generateAIContract(orderData) {\r\n      console.log('开始AI生成合同，工单数据:', orderData);\r\n\r\n      // 设置当前工单数据\r\n      this.currentOrder = orderData;\r\n\r\n      // 根据工单标题匹配合同类型\r\n      this.matchContractType(orderData.title);\r\n\r\n      // 获取用户详情信息\r\n      this.getUserInfo(orderData.uid);\r\n\r\n      // 重置AI生成状态\r\n      this.aiGenerating = false;\r\n      this.aiProgress = 0;\r\n      this.aiProgressText = '';\r\n      this.generatedContract = '';\r\n\r\n      // 打开AI生成对话框\r\n      this.dialogAIGenerate = true;\r\n    },\r\n\r\n    // 匹配合同类型\r\n    matchContractType(orderTitle) {\r\n      // 根据工单标题关键词匹配合同类型\r\n      const keywords = {\r\n        '劳动': '劳动合同',\r\n        '租赁': '租赁合同',\r\n        '买卖': '买卖合同',\r\n        '服务': '服务合同',\r\n        '借款': '借款合同'\r\n      };\r\n\r\n      let matchedType = null;\r\n      for (let keyword in keywords) {\r\n        if (orderTitle.includes(keyword)) {\r\n          matchedType = this.contractTypes.find(type => type.title === keywords[keyword]);\r\n          break;\r\n        }\r\n      }\r\n\r\n      this.matchedContractType = matchedType || {};\r\n      console.log('匹配到的合同类型:', this.matchedContractType);\r\n    },\r\n\r\n    // 获取用户信息\r\n    getUserInfo(uid) {\r\n      // 模拟获取用户详情信息\r\n      const userInfoMap = {\r\n        1: {\r\n          nickname: \"张三\",\r\n          phone: \"13800138001\",\r\n          id_card: \"110101199001011234\",\r\n          address: \"北京市朝阳区某某街道123号\"\r\n        },\r\n        2: {\r\n          nickname: \"李四\",\r\n          phone: \"13800138002\",\r\n          id_card: \"110101199002022345\",\r\n          address: \"上海市浦东新区某某路456号\"\r\n        },\r\n        3: {\r\n          nickname: \"王五\",\r\n          phone: \"13800138003\",\r\n          id_card: \"110101199003033456\",\r\n          address: \"广州市天河区某某大道789号\"\r\n        },\r\n        4: {\r\n          nickname: \"赵六\",\r\n          phone: \"13800138004\",\r\n          id_card: \"110101199004044567\",\r\n          address: \"深圳市南山区某某街101号\"\r\n        },\r\n        5: {\r\n          nickname: \"孙七\",\r\n          phone: \"13800138005\",\r\n          id_card: \"110101199005055678\",\r\n          address: \"杭州市西湖区某某路202号\"\r\n        }\r\n      };\r\n\r\n      this.currentUserInfo = userInfoMap[uid] || {\r\n        nickname: \"未知用户\",\r\n        phone: \"\",\r\n        id_card: \"\",\r\n        address: \"\"\r\n      };\r\n\r\n      console.log('获取到的用户信息:', this.currentUserInfo);\r\n    },\r\n\r\n    // 开始AI生成\r\n    startAIGeneration() {\r\n      if (!this.matchedContractType.template_file) {\r\n        this.$message.error('未找到对应的合同模板，无法进行AI生成');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n      this.aiProgress = 0;\r\n      this.generatedContract = '';\r\n\r\n      // 模拟AI生成过程\r\n      this.simulateAIGeneration();\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    simulateAIGeneration() {\r\n      const steps = [\r\n        { progress: 20, text: '正在分析工单内容...' },\r\n        { progress: 40, text: '正在解析合同模板...' },\r\n        { progress: 60, text: '正在整合用户信息...' },\r\n        { progress: 80, text: '正在生成合同条款...' },\r\n        { progress: 100, text: 'AI生成完成！' }\r\n      ];\r\n\r\n      let currentStep = 0;\r\n\r\n      const updateProgress = () => {\r\n        if (currentStep < steps.length) {\r\n          this.aiProgress = steps[currentStep].progress;\r\n          this.aiProgressText = steps[currentStep].text;\r\n          currentStep++;\r\n\r\n          setTimeout(updateProgress, 1000);\r\n        } else {\r\n          // 生成完成，显示模拟的合同内容\r\n          this.generateContractContent();\r\n          this.aiGenerating = false;\r\n        }\r\n      };\r\n\r\n      updateProgress();\r\n    },\r\n\r\n    // 生成合同内容\r\n    generateContractContent() {\r\n      const contractTemplate = `${this.matchedContractType.title}\r\n\r\n甲方（委托方）：${this.currentUserInfo.nickname}\r\n身份证号：${this.currentUserInfo.id_card}\r\n联系电话：${this.currentUserInfo.phone}\r\n地址：${this.currentUserInfo.address}\r\n\r\n乙方（受托方）：[待填写]\r\n\r\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就以下事项达成一致，签订本合同：\r\n\r\n一、合同内容\r\n${this.currentOrder.desc}\r\n\r\n二、合同条款\r\n[根据AI分析生成的具体条款内容]\r\n\r\n1. 权利义务\r\n   甲方权利：[根据合同类型和用户需求生成]\r\n   甲方义务：[根据合同类型和用户需求生成]\r\n   乙方权利：[根据合同类型和用户需求生成]\r\n   乙方义务：[根据合同类型和用户需求生成]\r\n\r\n2. 履行期限\r\n   [根据工单内容分析生成具体期限]\r\n\r\n3. 违约责任\r\n   [根据合同类型生成标准违约条款]\r\n\r\n4. 争议解决\r\n   因履行本合同发生的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉。\r\n\r\n5. 其他约定\r\n   [根据具体需求生成其他条款]\r\n\r\n三、合同生效\r\n本合同自双方签字（盖章）之日起生效。\r\n\r\n甲方签字：_________________ 日期：_________________\r\n\r\n乙方签字：_________________ 日期：_________________\r\n\r\n---\r\n本合同由AI智能生成，请仔细核对内容后使用。\r\n生成时间：${new Date().toLocaleString()}\r\n工单号：${this.currentOrder.order_sn}`;\r\n\r\n      this.generatedContract = contractTemplate;\r\n      this.$message.success('AI合同生成完成！');\r\n    },\r\n\r\n    // 保存生成的合同\r\n    saveGeneratedContract() {\r\n      if (!this.generatedContract) {\r\n        this.$message.warning('没有可保存的合同内容');\r\n        return;\r\n      }\r\n\r\n      // 这里可以调用API保存合同内容\r\n      // 模拟保存过程\r\n      this.$message.success('合同保存成功！');\r\n\r\n      // 更新工单状态为处理中\r\n      const orderIndex = this.list.findIndex(item => item.id === this.currentOrder.id);\r\n      if (orderIndex !== -1) {\r\n        this.list[orderIndex].is_deal = 1; // 设置为处理中\r\n      }\r\n\r\n      // 关闭对话框\r\n      this.dialogAIGenerate = false;\r\n    },\r\n\r\n    // 提交审核\r\n    submitForReview(row) {\r\n      console.log('提交审核:', row);\r\n\r\n      this.$confirm('确认将此合同提交审核？提交后将进入审核流程。', '提示', {\r\n        confirmButtonText: '确定提交',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交审核过程\r\n        setTimeout(() => {\r\n          // 更新工单状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            // 添加审核相关字段\r\n            this.list[index] = {\r\n              ...this.list[index],\r\n              review_status: 'submitted', // 已提交审核\r\n              submit_time: new Date().toLocaleString(),\r\n              review_step: 'mediator_review' // 下一步：调解员审核\r\n            };\r\n          }\r\n\r\n          this.$message.success('合同已成功提交审核！将进入审核流程。');\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-custom-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 抽屉动画优化 */\r\n.user-detail-drawer >>> .el-drawer__container {\r\n  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\r\n}\r\n\r\n/* 操作按钮纵向排列 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  width: 80px;\r\n  margin: 0 !important;\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-custom-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* AI生成对话框样式 */\r\n.ai-generate-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.ai-generate-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.order-info-section,\r\n.user-info-section,\r\n.template-info-section,\r\n.ai-progress-section,\r\n.result-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.template-info {\r\n  padding: 16px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.template-file {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.template-file i {\r\n  color: #67c23a;\r\n  font-size: 20px;\r\n}\r\n\r\n.template-file span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.no-template {\r\n  padding: 16px;\r\n}\r\n\r\n.ai-progress-section {\r\n  border-left-color: #e6a23c;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  margin-top: 12px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-section {\r\n  border-left-color: #67c23a;\r\n}\r\n\r\n.contract-preview {\r\n  background: white;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.contract-content >>> .el-textarea__inner {\r\n  font-family: 'Courier New', monospace;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .ai-generate-content {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .info-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA0ZA;AACA,OAAAA,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,SAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAP,KAAA;QACAQ,MAAA;MACA;MAEA;MACAC,YAAA;MACAC,eAAA;MACAC,mBAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,cAAA;MACAC,iBAAA;MAEAC,KAAA;QACAjB,KAAA,GACA;UACAkB,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,SAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,cAAA;MACAC,OAAA,GACA;QACAC,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA,EACA;MACAyB,QAAA,GACA;QACAD,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA,GACA;QACAwB,EAAA;QACAxB,KAAA;MACA;IAEA;EACA;EACA0B,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACAC,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;MACAC,OAAA,CAAAC,GAAA,MAAAF,KAAA;IACA;IACAG,UAAA;MACA,KAAAxC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,KAAAL,IAAA;MACA,KAAAmC,OAAA;IACA;IACAQ,aAAAX,EAAA;MACA,IAAAY,KAAA;MACAJ,OAAA,CAAAC,GAAA,6BAAAT,EAAA;MACA,IAAAA,EAAA;QACA,KAAAjC,SAAA,GAAAiC,EAAA;QACAQ,OAAA,CAAAC,GAAA,yBAAA1C,SAAA;MACA;MAEA6C,KAAA,CAAAjC,oBAAA;MACA6B,OAAA,CAAAC,GAAA;IACA;IACAI,SAAAb,EAAA;MACA,IAAAY,KAAA;MACA,IAAAZ,EAAA;QACA,KAAAc,OAAA,CAAAd,EAAA;MACA;QACA,KAAAjB,QAAA;UACAP,KAAA;UACAuC,IAAA;QACA;MACA;IACA;IACAD,QAAAd,EAAA;MACA,IAAAY,KAAA;MACAJ,OAAA,CAAAC,GAAA,oBAAAT,EAAA;;MAEA;MACAgB,UAAA;QACA;QACA,MAAAC,IAAA,GAAAL,KAAA,CAAA/C,IAAA,CAAAqD,IAAA,CAAAD,IAAA,IAAAA,IAAA,CAAAjB,EAAA,KAAAA,EAAA;QACA,IAAAiB,IAAA;UACAL,KAAA,CAAA7B,QAAA;YACAiB,EAAA,EAAAiB,IAAA,CAAAjB,EAAA;YACAxB,KAAA,EAAAyC,IAAA,CAAAzC,KAAA;YACAuC,IAAA,EAAAE,IAAA,CAAAF,IAAA;YACA1C,OAAA,EAAA4C,IAAA,CAAA5C,OAAA;YACA8C,IAAA,EAAAF,IAAA,CAAAE,IAAA;YACAC,OAAA;YACAvB,SAAA;UACA;UACAW,OAAA,CAAAC,GAAA,YAAAG,KAAA,CAAA7B,QAAA;UACA6B,KAAA,CAAAlC,iBAAA;QACA;UACAkC,KAAA,CAAAS,QAAA;YACAF,IAAA;YACAxB,OAAA;UACA;QACA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA2B,QAAAtB,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAN,IAAA;MACA,GACAO,IAAA;QACA,KAAAC,aAAA,MAAApD,GAAA,mBAAAyB,EAAA,EAAA0B,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAR,QAAA;cACAF,IAAA;cACAxB,OAAA,EAAAiC,IAAA,CAAAE;YACA;UACA;YACA,KAAAT,QAAA;cACAF,IAAA;cACAxB,OAAA,EAAAiC,IAAA,CAAAE;YACA;UACA;QACA;MACA,GACAC,KAAA;QACA,KAAAV,QAAA;UACAF,IAAA;UACAxB,OAAA;QACA;MACA;IACA;IACAqC,QAAAC,KAAA,EAAAjC,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAN,IAAA;MACA,GACAO,IAAA;QACA,KAAAC,aAAA,MAAApD,GAAA,kBAAAyB,EAAA,EAAA0B,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAR,QAAA;cACAF,IAAA;cACAxB,OAAA;YACA;YACA,KAAA9B,IAAA,CAAAqE,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAV,QAAA;UACAF,IAAA;UACAxB,OAAA;QACA;MACA;IACA;IACAwC,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAAtE,IAAA;MACA,KAAAC,IAAA;MACA,KAAAkC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAS,KAAA;MAEAA,KAAA,CAAAtC,OAAA;;MAEA;MACA0C,UAAA;QACA,IAAAuB,OAAA,IACA;UACAvC,EAAA;UACAwC,QAAA;UACArB,IAAA;UACA3C,KAAA;UACAuC,IAAA;UACA1C,OAAA;UACAoE,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;QACA,GACA;UACA5C,EAAA;UACAwC,QAAA;UACArB,IAAA;UACA3C,KAAA;UACAuC,IAAA;UACA1C,OAAA;UACAoE,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;QACA,GACA;UACA5C,EAAA;UACAwC,QAAA;UACArB,IAAA;UACA3C,KAAA;UACAuC,IAAA;UACA1C,OAAA;UACAoE,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;QACA,GACA;UACA5C,EAAA;UACAwC,QAAA;UACArB,IAAA;UACA3C,KAAA;UACAuC,IAAA;UACA1C,OAAA;UACAoE,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;QACA,GACA;UACA5C,EAAA;UACAwC,QAAA;UACArB,IAAA;UACA3C,KAAA;UACAuC,IAAA;UACA1C,OAAA;UACAoE,QAAA;UACAC,KAAA;UACAC,GAAA;UACAC,WAAA;QACA,EACA;;QAEA;QACA,IAAAC,YAAA,GAAAN,OAAA;QACA,IAAA3B,KAAA,CAAA1C,MAAA,CAAAC,OAAA;UACA0E,YAAA,GAAAN,OAAA,CAAAO,MAAA,CAAA7B,IAAA,IACAA,IAAA,CAAAuB,QAAA,CAAAO,QAAA,CAAAnC,KAAA,CAAA1C,MAAA,CAAAC,OAAA,KACA8C,IAAA,CAAAzC,KAAA,CAAAuE,QAAA,CAAAnC,KAAA,CAAA1C,MAAA,CAAAC,OAAA,KACA8C,IAAA,CAAAwB,QAAA,CAAAM,QAAA,CAAAnC,KAAA,CAAA1C,MAAA,CAAAC,OAAA,KACA8C,IAAA,CAAAyB,KAAA,CAAAK,QAAA,CAAAnC,KAAA,CAAA1C,MAAA,CAAAC,OAAA,CACA;QACA;QAEA,IAAAyC,KAAA,CAAA1C,MAAA,CAAAG,OAAA,WAAAuC,KAAA,CAAA1C,MAAA,CAAAG,OAAA;UACAwE,YAAA,GAAAA,YAAA,CAAAC,MAAA,CAAA7B,IAAA,IACAA,IAAA,CAAA5C,OAAA,IAAAuC,KAAA,CAAA1C,MAAA,CAAAG,OACA;QACA;QAEAuC,KAAA,CAAA/C,IAAA,GAAAgF,YAAA;QACAjC,KAAA,CAAA9C,KAAA,GAAA+E,YAAA,CAAAG,MAAA;QACApC,KAAA,CAAAtC,OAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA2E,SAAA;MACA,IAAArC,KAAA;MACA,KAAAsC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAAzC,KAAA,CAAArC,GAAA,gBAAAQ,QAAA,EAAA2C,IAAA,CAAAE,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAjB,KAAA,CAAAS,QAAA;gBACAF,IAAA;gBACAxB,OAAA,EAAAiC,IAAA,CAAAE;cACA;cACA,KAAA3B,OAAA;cACAS,KAAA,CAAAlC,iBAAA;YACA;cACAkC,KAAA,CAAAS,QAAA;gBACAF,IAAA;gBACAxB,OAAA,EAAAiC,IAAA,CAAAE;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAwB,iBAAAC,GAAA;MACA,KAAAtF,IAAA,GAAAsF,GAAA;MAEA,KAAApD,OAAA;IACA;IACAqD,oBAAAD,GAAA;MACA,KAAAvF,IAAA,GAAAuF,GAAA;MACA,KAAApD,OAAA;IACA;IACAsD,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAA7B,IAAA;QACA,KAAAR,QAAA,CAAAsC,OAAA;QACA,KAAA5E,QAAA,MAAAwB,KAAA,IAAAmD,GAAA,CAAA/F,IAAA,CAAAY,GAAA;MACA;QACA,KAAA8C,QAAA,CAAAuC,KAAA,CAAAF,GAAA,CAAA5B,GAAA;MACA;IACA;IAEA+B,UAAAC,IAAA;MACA,KAAAjF,UAAA,GAAAiF,IAAA;MACA,KAAAhF,aAAA;IACA;IACAiF,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA3C,IAAA;MACA,KAAA6C,UAAA;QACA,KAAA3C,QAAA,CAAAuC,KAAA;QACA;MACA;IACA;IACAM,SAAAJ,IAAA,EAAAK,QAAA;MACA,IAAAvD,KAAA;MACAA,KAAA,CAAAwD,UAAA,gCAAAN,IAAA,EAAApC,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAjB,KAAA,CAAA7B,QAAA,CAAAoF,QAAA;UAEAvD,KAAA,CAAAS,QAAA,CAAAsC,OAAA;QACA;UACA/C,KAAA,CAAAS,QAAA,CAAAuC,KAAA,CAAAhC,IAAA,CAAAE,GAAA;QACA;MACA;IACA;IAEA;IACA1B,iBAAA;MACA;MACAY,UAAA;QACA,KAAA5B,aAAA,IACA;UACAY,EAAA;UACAxB,KAAA;UACA6F,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAvE,EAAA;UACAxB,KAAA;UACA6F,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAvE,EAAA;UACAxB,KAAA;UACA6F,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAvE,EAAA;UACAxB,KAAA;UACA6F,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,GACA;UACAvE,EAAA;UACAxB,KAAA;UACA6F,aAAA;UACAC,aAAA;UACAC,aAAA;QACA,EACA;MACA;IACA;IAEA;IACAC,mBAAAC,SAAA;MACAjE,OAAA,CAAAC,GAAA,mBAAAgE,SAAA;;MAEA;MACA,KAAAxF,YAAA,GAAAwF,SAAA;;MAEA;MACA,KAAAC,iBAAA,CAAAD,SAAA,CAAAjG,KAAA;;MAEA;MACA,KAAAmG,WAAA,CAAAF,SAAA,CAAA9B,GAAA;;MAEA;MACA,KAAAtD,YAAA;MACA,KAAAC,UAAA;MACA,KAAAC,cAAA;MACA,KAAAC,iBAAA;;MAEA;MACA,KAAAZ,gBAAA;IACA;IAEA;IACA8F,kBAAAE,UAAA;MACA;MACA,MAAAC,QAAA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,WAAA;MACA,SAAA3G,OAAA,IAAA0G,QAAA;QACA,IAAAD,UAAA,CAAA7B,QAAA,CAAA5E,OAAA;UACA2G,WAAA,QAAA1F,aAAA,CAAA8B,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3C,KAAA,KAAAqG,QAAA,CAAA1G,OAAA;UACA;QACA;MACA;MAEA,KAAAgB,mBAAA,GAAA2F,WAAA;MACAtE,OAAA,CAAAC,GAAA,mBAAAtB,mBAAA;IACA;IAEA;IACAwF,YAAAhC,GAAA;MACA;MACA,MAAAoC,WAAA;QACA;UACAtC,QAAA;UACAC,KAAA;UACAsC,OAAA;UACAC,OAAA;QACA;QACA;UACAxC,QAAA;UACAC,KAAA;UACAsC,OAAA;UACAC,OAAA;QACA;QACA;UACAxC,QAAA;UACAC,KAAA;UACAsC,OAAA;UACAC,OAAA;QACA;QACA;UACAxC,QAAA;UACAC,KAAA;UACAsC,OAAA;UACAC,OAAA;QACA;QACA;UACAxC,QAAA;UACAC,KAAA;UACAsC,OAAA;UACAC,OAAA;QACA;MACA;MAEA,KAAA/F,eAAA,GAAA6F,WAAA,CAAApC,GAAA;QACAF,QAAA;QACAC,KAAA;QACAsC,OAAA;QACAC,OAAA;MACA;MAEAzE,OAAA,CAAAC,GAAA,mBAAAvB,eAAA;IACA;IAEA;IACAgG,kBAAA;MACA,UAAA/F,mBAAA,CAAAkF,aAAA;QACA,KAAAhD,QAAA,CAAAuC,KAAA;QACA;MACA;MAEA,KAAAvE,YAAA;MACA,KAAAC,UAAA;MACA,KAAAE,iBAAA;;MAEA;MACA,KAAA2F,oBAAA;IACA;IAEA;IACAA,qBAAA;MACA,MAAAC,KAAA,IACA;QAAAC,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,IAAA;MAAA,EACA;MAEA,IAAAC,WAAA;MAEA,MAAAC,cAAA,GAAAA,CAAA;QACA,IAAAD,WAAA,GAAAH,KAAA,CAAApC,MAAA;UACA,KAAA1D,UAAA,GAAA8F,KAAA,CAAAG,WAAA,EAAAF,QAAA;UACA,KAAA9F,cAAA,GAAA6F,KAAA,CAAAG,WAAA,EAAAD,IAAA;UACAC,WAAA;UAEAvE,UAAA,CAAAwE,cAAA;QACA;UACA;UACA,KAAAC,uBAAA;UACA,KAAApG,YAAA;QACA;MACA;MAEAmG,cAAA;IACA;IAEA;IACAC,wBAAA;MACA,MAAAC,gBAAA,WAAAvG,mBAAA,CAAAX,KAAA;;AAEA,eAAAU,eAAA,CAAAuD,QAAA;AACA,YAAAvD,eAAA,CAAA8F,OAAA;AACA,YAAA9F,eAAA,CAAAwD,KAAA;AACA,UAAAxD,eAAA,CAAA+F,OAAA;;AAEA;;AAEA;;AAEA;AACA,OAAAhG,YAAA,CAAA8B,IAAA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,WAAA4E,IAAA,GAAAC,cAAA;AACA,WAAA3G,YAAA,CAAAuD,QAAA;MAEA,KAAAhD,iBAAA,GAAAkG,gBAAA;MACA,KAAArE,QAAA,CAAAsC,OAAA;IACA;IAEA;IACAkC,sBAAA;MACA,UAAArG,iBAAA;QACA,KAAA6B,QAAA,CAAAyE,OAAA;QACA;MACA;;MAEA;MACA;MACA,KAAAzE,QAAA,CAAAsC,OAAA;;MAEA;MACA,MAAAoC,UAAA,QAAAlI,IAAA,CAAAmI,SAAA,CAAA/E,IAAA,IAAAA,IAAA,CAAAjB,EAAA,UAAAf,YAAA,CAAAe,EAAA;MACA,IAAA+F,UAAA;QACA,KAAAlI,IAAA,CAAAkI,UAAA,EAAA1H,OAAA;MACA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAqH,gBAAAC,GAAA;MACA1F,OAAA,CAAAC,GAAA,UAAAyF,GAAA;MAEA,KAAA3E,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAN,IAAA;MACA,GAAAO,IAAA;QACA;QACAV,UAAA;UACA;UACA,MAAAiB,KAAA,QAAApE,IAAA,CAAAmI,SAAA,CAAA/E,IAAA,IAAAA,IAAA,CAAAjB,EAAA,KAAAkG,GAAA,CAAAlG,EAAA;UACA,IAAAiC,KAAA;YACA;YACA,KAAApE,IAAA,CAAAoE,KAAA;cACA,QAAApE,IAAA,CAAAoE,KAAA;cACAkE,aAAA;cAAA;cACAC,WAAA,MAAAT,IAAA,GAAAC,cAAA;cACAS,WAAA;YACA;UACA;UAEA,KAAAhF,QAAA,CAAAsC,OAAA;QACA;MACA,GAAA5B,KAAA;QACA,KAAAV,QAAA,CAAA5C,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}