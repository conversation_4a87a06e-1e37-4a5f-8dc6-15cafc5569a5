{"name": "@types/uglify-js", "version": "3.17.5", "description": "TypeScript definitions for uglify-js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/uglify-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "alan-agius4", "url": "https://github.com/alan-agius4"}, {"name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uglify-js"}, "scripts": {}, "dependencies": {"source-map": "^0.6.1"}, "typesPublisherContentHash": "44c17a72d4848ee079e26ea14d82becfc914e72dd78763c8776d0d6d841ae2b4", "typeScriptVersion": "4.6"}