<?php
namespace app\api\controller;
use think\Request;
use untils\JsonService;
use models\{Navs,<PERSON>s,Cates,Tupians,Navtupians};
use think\facade\Config;
class Index{
	private $url = "https://ymsj.itans.top";
    private $upload_server ="server";
    public function __construct(Banners $model){
        //parent::__construct();
        $this->upload_server=Config::get("")['easyupload']['upload_server'];
        
        
    }
	public function getIndexAll(){
	   
		$banners =Banners::withAttr('pic_path',function($value,$data){
		    if($this->upload_server=="server"){
		        return $this->url.str_replace("\\","/",$value);
		    }else{
		        return $value;
		    }
			
		})->order(['sort'=>'asc'])->select();
		$cates =[];
		$navs =Navs::order(['sort'=>'asc'])->select()->toArray();
		$first = current($navs);
		$type = 1;
		$list = [];
	    $id = -1;
		if(!empty($first)){
	    	$arrays = Cates::where(['nav_id'=>$first['id']])->column('id');
	    	
        	$where[]=['cate_id','in',$arrays];
    	    $list = Tupians::withAttr('pic_path',function($value,$data){
    		    if($this->upload_server=="server"){
    		        return $this->url.str_replace("\\","/",$value);
    		    }else{
    		        return $value;
    		    }
    		})->withAttr('is_like',function($value,$data){
    			return false;
    		})
    		->order(['sort'=>'asc'])
    		->where($where)
    		->append(["is_like"])
    		->select()->toArray();
    		$cates = Cates::where(['nav_id'=>$first['id']])->order(['sort'=>'asc'])->select()->toArray();
    		$type = $first['type'];
    		$id =  $first['id'];
		}
	
		//array_unshift($navs,['id'=>-1,'title'=>'全部']);
	
		JsonService::successful('获取成功',compact("cates","navs","banners","list","type","id"));

	}
	public function getTupian(Tupians $model,Request $request){
		$post = $request->post();
		$where=[];
		if(!empty($post['cate_id'])&&$post['cate_id']!=-1){
			$where[]=['cate_id','=',$post['cate_id']];
		}
		if(!empty($post['nav_id'])&&$post['nav_id']!=-1){
		    $arrays = Cates::where(['nav_id'=>$post['nav_id']])->column('id');
		    //$str = implode(",",$arrays);
			$where[]=['cate_id','in',$arrays];
		}
	    
		$res = $model::withAttr('pic_path',function($value,$data){
		    if($this->upload_server=="server"){
		        return $this->url.str_replace("\\","/",$value);
		    }else{
		        return $value;
		    }
		})->withAttr('is_like',function($value,$data){
			return false;
		})
		->order(['sort'=>'asc'])
		->where($where)
		->append(["is_like"])
		->select()->toArray();
	    
		if(empty($res)) return JsonService::fail('暂无数据');
		else return JsonService::successful('获取成功',$res);
	}
	public function getCate(Cates $model,Request $request){
	    $post = $request->post();
		$where=[];
		if(!empty($post['nav_id'])&&$post['nav_id']!=-1){
			$where[]=['nav_id','=',$post['nav_id']];
		}
		$res = $model->where($where)
		->order(['sort'=>'asc'])
		->select()->toArray();
	
		if(empty($res)) return JsonService::fail('暂无数据');
		else return JsonService::successful('获取成功',$res);
		
	}
	public function getNavTupian(Navtupians $model,Request $request){
		$post = $request->post();
		$where=[];
		if(!empty($post['nav_id'])&&$post['nav_id']!=-1){
			$where[]=['nav_id','=',$post['nav_id']];
		}
		$res = $model::withAttr('pic_path',function($value,$data){
		    if($this->upload_server=="server"){
		        return $this->url.str_replace("\\","/",$value);
		    }else{
		        return $value;
		    }
		})->withAttr('is_like',function($value,$data){
			return false;
		})
		->order(['sort'=>'asc'])
		->where($where)
		->append(["is_like"])
		->select()->toArray();
		if(empty($res)) return JsonService::fail('暂无数据');
		else return JsonService::successful('获取成功',$res);
	}


}