<?php
/**
 * Created by <PERSON>
 * Date: 2021-02-01
 * Time: 16:06
 * Description:
 */
namespace untils;



use app\Request;
use think\facade\Config;
use think\facade\Log;
use models\WxSign;
class WxApi
{
    /**
     * 微信开发者模式获取
     */
    protected $appid = "";
    protected $appsecret ="";
    protected $token = "";

    //初始值
    public function __construct()
    {
       
        //$this->appid = 'wxb2fc940e0dfbc084';
        //$this->appsecret = 'c3a38952ec64e27552ce936b14afb10f';
        //福建法多邦法务公众号
        $this->appId = 'wx82309797155b7b16';
        $this->appSecret = '8348621479a22b3c39dc929fcbe89548';
       
    }

    /**
     * Notes:验证消息的确来自微信服务器
     * @param  string $echostr 返回值，
     */
    public function _valid(Request $request){
        //微信服务器将发送GET请求到填写服务器地址URL上，GET请求携带参数如下表示：
        //Log::write($request->param());
        $data['signature'] = $request->param('signature');
        $data['echostr'] = $request->param('echostr');
        $data['timestamp'] = $request->param('timestamp');
        $data['nonce'] = $request->param('nonce');
        $data['echostr'] = $request->param('echostr');
        $echostr = $this->_checkSignature($data);
        if( $echostr == true && $echostr == $data['echostr']){
            //第一次接入weixin api接口的时候
            return  $echostr;
        }
    }

    /**
     * 检查消息签名是否相同
     * @param object $getObj
     * @return boolean 成功返回true，失败返回false
     */
    public function _checkSignature($data)
    {
        $signature = $data['signature'];
        $timestamp = $data['timestamp'];
        $nonce = $data['nonce'];
        $echostr = $data['echostr'];
        $token = $this->token;
        $tmpArr = array($token, $timestamp, $nonce);
        //1）将token、timestamp、nonce三个参数进行字典序排序
        sort($tmpArr, SORT_STRING);
        // 2）将三个参数字符串拼接成一个字符串进行sha1加密
        $tmpStr = implode($tmpArr );
        $tmpStr = sha1($tmpStr );
        //3）开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
      if( $tmpStr == $signature ){
            echo  $echostr;
            exit;
        }else{
            return false;
        }

    }

    /**
     * 获取access_token（全局）
     *  有效时间2小时
     */
    public function getAccessToken(){
        $sign = new WxSign(); 
        //1、查询数据库的access_token
        $data = $sign->where(['type'=>'access_token'])->find();
        //2、判断access_token的有效时间是否过期
        if($data['expire_time']< time()){
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appsecret}";
            $res = $this->http_url($url);
            $access_token = $res['access_token'];
            if($access_token){
                $sign->where(['type'=>'access_token'])->update(['value'=>$access_token,'expire_time'=>time() + 7000]);
            }
        }else{
            $access_token = $data['value'];
        }
        return $access_token;
    }


    /*********************************网页授权***************************************************/
    //1 第一步：用户同意授权，获取code(前端发起)
    //2 第二步：通过code换取网页授权access_token
    //3 第三步：刷新access_token（如果需要）
    //4 第四步：拉取用户信息(需scope为 snsapi_userinfo)
    /**
     * 获取access_token （网页）
     * @param code 前端返回code
     * @param appid  微信公众号 appid
     * @param secret 微信公众号 secret
     * @return {"access_token":"ACCESS_TOKEN","expires_in":7200,"refresh_token":"REFRESH_TOKEN","openid":"OPENID", "scope":"SCOPE" }
     */
    public function _access_token($code){
        $url ="https://api.weixin.qq.com/sns/oauth2/access_token?appid={$this->appid}&secret={$this->appsecret}&code={$code}&grant_type=authorization_code";
        $result = $this->http_url($url);
        return $result;
    }

    /*
    *拉取用户信息(默认snsapi_userinfo)
    *@param openid 用户唯一标识
    *@param access_token 网页授权接口调用凭证
    *return openid,nickname,sex,province,province,country,headimgurl,privilege,unionid
    */
    public function get_userinfo($access_token,$openid){
        $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}&lang=zh_CN";
        $result = $this->http_url($url);
        return $result;
    }

    /**
     *  
     */
    /*********************************创建菜单***************************************************/
    /**
     * Notes:创建自定义菜单
     * @param $data 数据（数据库）
     * @return bool
     */
    public function createMenu($data){
        $access_token = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token={$access_token}";
        $result = $this->http_url($url,$data);
        if($result['errcode']==0 && $result['errmsg']=="ok"){
            return true;
        }else{
            return false;
        }
    }
    public function sendTemplete($data){
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token='.$this->getAccessToken();
        $data = $this->_httpsRequest($url,$data);
        return json_decode($data,true);
    }
    
    protected function _httpsRequest($url, $data = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)){
            //处理php版本问题开始
//            if (class_exists('\CURLFile')) {
//                $data['media'] = new \CURLFile(realpath($data['media']));
//            } else {
//                if (defined('CURLOPT_SAFE_UPLOAD')) {
//                    curl_setopt($curl, CURLOPT_SAFE_UPLOAD, FALSE);
//                }
//            }
            $postData = json_encode($data);

            //处理php版本问题结束
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }
    /*********************************支付JSDK签名***************************************************/

    /**
     * Notes:获取发票签名临时票据
     * @param WxSign $sign
     * @return mixed
     */
    public function getJsApiTicket(WxSign $sign){
        $sign =  new WxSign();
        //1、查询数据库的jsapi_ticket
        $data = $sign->where(['type'=>'jsapi_ticket'])->find();
        //2、判断jsapi_ticket的有效时间是否过期
        if($data['expire_time'] < time()){
            $accessToken = $this->getAccessToken();
            $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={$accessToken}&&type=jsapi";
            $res = $this->http_url($url);
            $ticket = $res['ticket'];
            if($ticket){
                $sign->where(['type'=>'jsapi_ticket'])->update(['value'=>$ticket,'expire_time'=>time() + 7000]);
            }
        }else{
            $ticket = $data['value'];
        }
        return $ticket;
    }

    /**
     * 创建随机字符串
    */
    public function createNoncestr($length = 16) {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for($i = 0; $i < $length; $i ++) {
            $str .= substr ( $chars, mt_rand ( 0, strlen ( $chars ) - 1 ), 1 );
        }
        return $str;
    }

    /**
     * Notes: 微信提交API方法，返回微信指定JSON
     * @param $url
     * @param null $data
     * @return mixed
     *
     *curl_init()函数将初始化一个新的会话
     *curl_setopt()函数是php中一个重要的函数，它可以模仿用户的一些行为，如模仿用户登录，注册等等一些用户可操作的行为
     *curl_exec()执行给定的cURL会话，抓取URL并把它传递给浏览器。
     *curl_close()关闭cURL资源，并且释放系统资源
     */
    public function http_url($url,$data=null){
        $ch = curl_init();
        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,FALSE);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,0);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,TRUE);
        if(!empty($data)){
            curl_setopt($ch,CURLOPT_POST,1);
            curl_setopt($ch,CURLOPT_POSTFIELDS,$data);
        }
        $output = curl_exec($ch);
        if(curl_errno($ch)){
            echo "error:".curl_error($ch);
            exit;
        }
        curl_close($ch);
        return json_decode($output,true);
    }

/*********************************接收消息***************************************************/

    /**
     * Notes:接收消息
     */
    public function responseMsg(){
        $poststr = file_get_contents('php://input');
        Log::write("微信传递的数据".$poststr);
        //如果推送消息 或者推送事件存在,进行处理
        if(!empty($poststr)){
            $postObj = simplexml_load_string($poststr, 'SimpleXMLElement', LIBXML_NOCDATA);
            $msgType  = trim($postObj->MsgType);
            switch ($msgType) {
                //当回复公众号时
                case 'text':       //文本消息
                    $resultStr =  $this->receiveText($postObj);
                    break;
                case 'image':      //图片消息
                    $resultStr =  $this->receiveImage($postObj);
                    break;
                case 'voice':       //语音消息
                    $resultStr =  $this->receiveVoice($postObj);
                    break;
                case 'video':       //视频消息
                    $resultStr =  $this->receiveVideo($postObj);
                    break;
                case 'shortvideo':  //小视频
                    $resultStr =  $this->receiveShortVideo($postObj);
                    break;
                case 'location':   //位置消息
                    $resultStr =  $this->receiveLocation($postObj);
                    break;
                case 'link':        //链接
                    $resultStr =  $this->receiveLink($postObj);
                    break;
                case 'event':        //事件
                    $resultStr =  $this->receiveEvent($postObj);
                    break;
                default:
                    $resultStr =  "Unknow msg type: ".$msgType;
                    break;
            }
            return $resultStr;

        }else{
            echo "";
            exit;
        }
    }

    /*
     * 接收文本消息
     */
    private function receiveText($object)
    {
        $content = "你发送的是文本，内容为：".$object->Content;
        $result = $this->transmitText($object, $content);
        return $result;
    }

    /*
     * 接收图片消息
     */
    private function receiveImage($object)
    {
        $content = array("MediaId"=>$object->MediaId);
        $result = $this->transmitImage($object, $content);
        return $result;
    }

    /*
     * 接收语音消息
     */
    private function receiveVoice($object)
    {
        if (isset($object->Recognition) && !empty($object->Recognition)){
            $content = "你刚才说的是：".$object->Recognition;
            $result = $this->transmitText($object, $content);
        }else{
            $content = array("MediaId"=>$object->MediaId);
            $result = $this->transmitVoice($object, $content);
        }
        return $result;
    }

    /*
     * 接收视频消息
     */
    private function receiveVideo($object)
    {
        $content = array("MediaId"=>$object->MediaId, "ThumbMediaId"=>$object->ThumbMediaId, "Title"=>"", "Description"=>"");
        $result = $this->transmitVideo($object, $content);
        return $result;
    }

    /*
    * 接收小视频
    */
    private function receiveShortVideo($object){
        $content = array("MediaId"=>$object->MediaId, "ThumbMediaId"=>$object->ThumbMediaId, "Title"=>"", "Description"=>"");
        $result = $this->transmitVideo($object, $content);
        return $result;
    }

    /*
     * 接收位置消息
     */
    private function receiveLocation($object)
    {
        $content = "你发送的是位置，纬度为：".$object->Location_X."；经度为：".$object->Location_Y."；缩放级别为：".$object->Scale."；位置为：".$object->Label;
        $result = $this->transmitText($object, $content);
        return $result;
    }

    /*
     * 接收链接消息
     */
    private function receiveLink($object)
    {
        $content = "你发送的是链接，标题为：".$object->Title."；内容为：".$object->Description."；链接地址为：".$object->Url;
        $result = $this->transmitText($object, $content);
        return $result;
    }

    /*
     * 接收事件消息
     */
    private function receiveEvent($object){
        $content = "";
        switch ($object->Event){
            case"subscribe"://关注时的时间推送
                $content = "欢迎关注!";
                $content .= (!empty($object->EventKey)) ? ("\n来自二维码场景".str_replace("qrscene_","",$object->EventKey)) : "";
                break;
            case "unsubscribe"://取消关注事件
                $content = "取消关注";
                break;
            case "CLICK":
                switch ($object->EventKey)
                {
                    case "COMPANY":
                        $content = array();
                        $content[] = array("Title"=>"公众号", "Description"=>"", "PicUrl"=>"", "Url" =>"");
                        break;
                    default:
                        $content = "点击菜单：".$object->EventKey;
                        break;
                }
                break;
            case "VIEW":// 点击菜单跳转链接时的事件推送
                $content = "跳转链接 ".$object->EventKey;
                break;
            case"SCAN"; // 扫描带参数二维码场景，用户已关注时的事件推送
                $content = "扫描场景 ".$object->EventKey;
                break;
            case "LOCATION": // 上报地理位置事件
                $content = "上传位置：纬度 ".$object->Latitude.";经度 ".$object->Longitude;
                break;
        }
        if(is_array($content)){
            if (isset($content[0]['PicUrl'])){
                $result = $this->transmitNews($object, $content);
            }
        }else{
            $result = $this->transmitText($object, $content);
        }
        return $result;
    }



    /*********************************回复消息***************************************************/

    /*
   * 回复文本消息
   */
    private function transmitText($object, $content)
    {
        $textTpl = "<xml>
                    <ToUserName><![CDATA[%s]]></ToUserName>
                    <FromUserName><![CDATA[%s]]></FromUserName>
                    <CreateTime>%s</CreateTime>
                    <MsgType><![CDATA[text]]></MsgType>
                    <Content><![CDATA[%s]]></Content>
                    </xml>";
        $result = sprintf($textTpl, $object->FromUserName, $object->ToUserName, time(), $content);
        return $result;
    }

    /**
     * 回复图片消息
     */
    private function transmitImage($object, $imageArray)
    {
        $itemTpl = "<Image>
            <MediaId><![CDATA[%s]]></MediaId>
          </Image>";
        $item_str = sprintf($itemTpl, $imageArray['MediaId']);
        $xmlTpl =  "<xml>
            <ToUserName><![CDATA[%s]]></ToUserName>
            <FromUserName><![CDATA[%s]]></FromUserName>
            <CreateTime>%s</CreateTime>
            <MsgType><![CDATA[image]]></MsgType>
            $item_str
          </xml>";
        $result = sprintf($xmlTpl, $object->FromUserName, $object->ToUserName, time());
        return $result;
    }

    /**
     * 回复语音消息
     */
    private function transmitVoice($object, $voiceArray)
    {
        $itemTpl = "<Voice>
            <MediaId><![CDATA[%s]]></MediaId>
          </Voice>";
        $item_str = sprintf($itemTpl, $voiceArray['MediaId']);
        $xmlTpl =  "<xml>
            <ToUserName><![CDATA[%s]]></ToUserName>
            <FromUserName><![CDATA[%s]]></FromUserName>
            <CreateTime>%s</CreateTime>
            <MsgType><![CDATA[voice]]></MsgType>
            $item_str
          </xml>";
        $result = sprintf($xmlTpl, $object->FromUserName, $object->ToUserName, time());
        return $result;
    }

    /**
     * 回复视频消息
     */
    private function transmitVideo($object, $videoArray)
    {
        $itemTpl = "<Video>
            <MediaId><![CDATA[%s]]></MediaId>
            <ThumbMediaId><![CDATA[%s]]></ThumbMediaId>
            <Title><![CDATA[%s]]></Title>
            <Description><![CDATA[%s]]></Description>
          </Video>";
        $item_str = sprintf($itemTpl, $videoArray['MediaId'], $videoArray['ThumbMediaId'], $videoArray['Title'], $videoArray['Description']);
        $xmlTpl =  "<xml>
            <ToUserName><![CDATA[%s]]></ToUserName>
            <FromUserName><![CDATA[%s]]></FromUserName>
            <CreateTime>%s</CreateTime>
            <MsgType><![CDATA[video]]></MsgType>
            $item_str
          </xml>";
        $result = sprintf($xmlTpl, $object->FromUserName, $object->ToUserName, time());
        return $result;
    }

    /**
     * 回复图文消息
     */
    private function transmitNews($object, $newsArray)
    {
        if(!is_array($newsArray)){
            return "";
        }
        $itemTpl = "<item>
            <Title><![CDATA[%s]]></Title>
            <Description><![CDATA[%s]]></Description>
            <PicUrl><![CDATA[%s]]></PicUrl>
            <Url><![CDATA[%s]]></Url>
          </item>";
        $item_str = "";
        foreach ($newsArray as $item){
            $item_str .= sprintf($itemTpl, $item['Title'], $item['Description'], $item['PicUrl'], $item['Url']);
        }
        $xmlTpl =  "<xml>
            <ToUserName><![CDATA[%s]]></ToUserName>
            <FromUserName><![CDATA[%s]]></FromUserName>
            <CreateTime>%s</CreateTime>
            <MsgType><![CDATA[news]]></MsgType>
            <ArticleCount>%s</ArticleCount>
            <Articles>$item_str</Articles>
          </xml>";
        $result = sprintf($xmlTpl, $object->FromUserName, $object->ToUserName, time(), count($newsArray));
        return $result;
    }



}