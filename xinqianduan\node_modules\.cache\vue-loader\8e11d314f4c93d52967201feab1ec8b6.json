{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\data\\configs.vue?vue&type=style&index=0&id=163793d6&prod&lang=css", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\data\\configs.vue", "mtime": 1748279529143}, {"path": "H:\\fdbfront\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748278548153}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748278552176}, {"path": "H:\\fdbfront\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748278549571}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYXZhdGFyLXVwbG9hZGVyIC5lbC11cGxvYWQgew0KICBib3JkZXI6IDFweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmVsX2lucHV0IHsNCiAgd2lkdGg6IDYwMHB4Ow0KfQ0KDQouYXZhdGFyLXVwbG9hZGVyIC5lbC11cGxvYWQ6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQp9DQoNCi5hdmF0YXItdXBsb2FkZXItaWNvbiB7DQogIGZvbnQtc2l6ZTogMjhweDsNCiAgY29sb3I6ICM4YzkzOWQ7DQogIHdpZHRoOiAxMjBweDsNCiAgaGVpZ2h0OiAxMjBweDsNCiAgbGluZS1oZWlnaHQ6IDEyMHB4Ow0KfQ0KDQouYXZhdGFyIHsNCiAgd2lkdGg6IDEyMHB4Ow0KICBoZWlnaHQ6IDEyMHB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCg=="}, {"version": 3, "sources": ["configs.vue"], "names": [], "mappings": ";AAkSA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "configs.vue", "sourceRoot": "src/views/pages/data", "sourcesContent": ["<template>\r\n  <el-card shadow=\"always\">\r\n    <div style=\"margin-top: 20px\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\" size=\"mini\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <el-form-item label=\"网站名称\">\r\n              <el-input\r\n                v-model=\"ruleForm.site_name\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"公司名称\">\r\n              <el-input\r\n                v-model=\"ruleForm.company_name\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"联系方式\">\r\n              <el-input v-model=\"ruleForm.site_tel\" class=\"el_input\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"邮箱\">\r\n              <el-input v-model=\"ruleForm.email\" class=\"el_input\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"地址\">\r\n              <el-input\r\n                v-model=\"ruleForm.site_address\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"icp备案号\">\r\n              <el-input v-model=\"ruleForm.site_icp\" class=\"el_input\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"icp备案连接\">\r\n              <el-input\r\n                v-model=\"ruleForm.site_icp_url\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"logo\" :label-width=\"formLabelWidth\">\r\n              <el-input\r\n                v-model=\"ruleForm.site_logo\"\r\n                :disabled=\"true\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n              <el-button-group>\r\n                <el-button @click=\"changeFiled('site_logo')\">\r\n                  <el-upload\r\n                    action=\"/admin/Upload/uploadImage\"\r\n                    :show-file-list=\"false\"\r\n                    :on-success=\"handleSuccess\"\r\n                    :before-upload=\"beforeUpload\"\r\n                  >\r\n                    上传\r\n                  </el-upload>\r\n                </el-button>\r\n                <el-button\r\n                  type=\"success\"\r\n                  v-if=\"ruleForm.site_logo\"\r\n                  @click=\"showImage(ruleForm.site_logo)\"\r\n                  >查看\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  v-if=\"ruleForm.site_logo\"\r\n                  @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                  >删除</el-button\r\n                >\r\n              </el-button-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              label=\"我的详情推广律师\"\r\n              :label-width=\"formLabelWidth\"\r\n            >\r\n              <el-select\r\n                v-model=\"ruleForm.lvshi\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n              >\r\n                <el-option value=\"\">请选择</el-option>\r\n                <el-option\r\n                  v-for=\"(item, index) in lvshi\"\r\n                  :key=\"index\"\r\n                  :label=\"item.title\"\r\n                  :value=\"item.id\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"我的详情推广标题\">\r\n              <el-input v-model=\"ruleForm.my_title\" class=\"el_input\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"我的详情推广语\">\r\n              <el-input v-model=\"ruleForm.my_desc\" class=\"el_input\"></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"我的详情推广图\" :label-width=\"formLabelWidth\">\r\n              <el-input\r\n                v-model=\"ruleForm.about_path\"\r\n                :disabled=\"true\"\r\n                class=\"el_input\"\r\n              ></el-input>\r\n              <el-button-group>\r\n                <el-button @click=\"changeFiled('about_path')\">\r\n                  <el-upload\r\n                    action=\"/admin/Upload/uploadImage\"\r\n                    :show-file-list=\"false\"\r\n                    :on-success=\"handleSuccess\"\r\n                    :before-upload=\"beforeUpload\"\r\n                  >\r\n                    上传\r\n                  </el-upload>\r\n                </el-button>\r\n                <el-button\r\n                  type=\"success\"\r\n                  v-if=\"ruleForm.about_path\"\r\n                  @click=\"showImage(ruleForm.about_path)\"\r\n                  >查看\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  v-if=\"ruleForm.about_path\"\r\n                  @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                  >删除</el-button\r\n                >\r\n              </el-button-group>\r\n            </el-form-item>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n              <el-input\r\n                v-model=\"ruleForm.yinsi\"\r\n                autocomplete=\"off\"\r\n                type=\"textarea\"\r\n                :rows=\"12\"\r\n              ></el-input> </el-form-item\r\n          ></el-tab-pane>\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n              <editor-bar\r\n                v-model=\"ruleForm.index_about_content\"\r\n                :isClear=\"isClear\"\r\n                @change=\"change\"\r\n              ></editor-bar> </el-form-item\r\n          ></el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n              <editor-bar\r\n                v-model=\"ruleForm.index_team_content\"\r\n                :isClear=\"isClear\"\r\n                @change=\"change\"\r\n              ></editor-bar>\r\n            </el-form-item>\r\n          </el-tab-pane>\r\n          <!-- <el-tab-pane label=\"员工培训\" name=\"peixun\">\r\n            <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n              <editor-bar\r\n                v-model=\"ruleForm.index_peixun_content\"\r\n                :isClear=\"isClear\"\r\n                @change=\"change\"\r\n              ></editor-bar> </el-form-item\r\n          ></el-tab-pane> -->\r\n          <!-- <el-tab-pane label=\"案例学习\" name=\"xuexi\">\r\n            <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n              <editor-bar\r\n                v-model=\"ruleForm.index_xuexi_content\"\r\n                :isClear=\"isClear\"\r\n                @change=\"change\"\r\n              ></editor-bar>\r\n            </el-form-item>\r\n          </el-tab-pane> -->\r\n        </el-tabs>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"saveData\"\r\n            v-loading.fullscreen.lock=\"fullscreenLoading\"\r\n            >提交\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: \"这是隐私条款的演示内容...\",\r\n        index_about_content: \"<p>这是关于我们的演示内容...</p>\",\r\n        index_team_content: \"<p>这是团队介绍的演示内容...</p>\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.el_input {\r\n  width: 600px;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 120px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n}\r\n\r\n.avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}