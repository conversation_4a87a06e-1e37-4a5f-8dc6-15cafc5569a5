{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue", "mtime": 1748450855160}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "field", "title", "info", "dialogFormVisible", "dialogPreview", "previewData", "show_image", "dialogVisible", "isClear", "ruleForm", "is_num", "rules", "required", "message", "trigger", "cate_id", "file_path", "form<PERSON>abe<PERSON><PERSON>", "cates", "expireTimeOption", "disabledDate", "date", "getTime", "Date", "now", "mounted", "console", "log", "getData", "getLvshi", "document", "addEventListener", "handleKeyDown", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "watch", "newVal", "oldVal", "handleDialogClose", "methods", "change", "changefield", "editData", "id", "_this", "getInfo", "desc", "testCategories", "setTimeout", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "clearSearch", "getCategoryName", "cateId", "Array", "isArray", "warn", "category", "find", "item", "previewContract", "row", "downloadFile", "fileUrl", "link", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "success", "$refs", "resetFields", "price", "content", "cancelDialog", "event", "keyCode", "testData", "create_time", "update_time", "filteredData", "trim", "toLowerCase", "filter", "includes", "startIndex", "endIndex", "pageData", "slice", "length", "error", "saveData", "validate", "valid", "postRequest", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "file", "beforeUpload", "filed", "isTypeTrue", "test", "delImage", "fileName"], "sources": ["src/views/pages/wenshu/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-list-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            合同列表管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统中的所有合同模板和文书</p>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增合同\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索和筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-content\">\r\n          <div class=\"search-left\">\r\n            <el-input\r\n              placeholder=\"搜索合同标题、类型...\"\r\n              v-model=\"search.keyword\"\r\n              class=\"search-input\"\r\n              clearable\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch()\"\r\n              class=\"clear-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"contract-table\"\r\n          stripe\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"文书标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"title-text\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"cate_id\" label=\"文书类型\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ getCategoryName(scope.row.cate_id) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"price\" label=\"价格\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"price-text\">\r\n                <i class=\"el-icon-money\"></i>\r\n                ¥{{ scope.row.price || '0.00' }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"file_path\" label=\"文件状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.file_path ? 'success' : 'warning'\"\r\n                size=\"small\"\r\n              >\r\n                {{ scope.row.file_path ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"录入时间\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-edit\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.file_path\"\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"previewContract(scope.row)\"\r\n                  icon=\"el-icon-view\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-delete\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"form-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"文书类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cate_id\"\r\n        >\r\n          <el-select v-model=\"ruleForm.cate_id\" placeholder=\"请选择\" filterable>\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in cates\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"文件上传\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changefield('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 文书预览对话框 -->\r\n    <el-dialog\r\n      title=\"文书预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              类型：{{ getCategoryName(previewData.cate_id) }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-money\"></i>\r\n              价格：¥{{ previewData.price || '0.00' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div v-if=\"previewData.file_path\" class=\"file-preview\">\r\n            <div class=\"file-info\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ previewData.file_path.split('/').pop() }}</span>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"downloadFile(previewData.file_path)\"\r\n                icon=\"el-icon-download\"\r\n              >\r\n                下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"previewData.content\" class=\"content-preview\">\r\n            <h4>文书内容：</h4>\r\n            <div class=\"content-html\" v-html=\"previewData.content\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshu/\",\r\n      field: \"\",\r\n      title: \"文书\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogPreview: false,\r\n      previewData: {},\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      isClear: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        cate_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择文书类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      cates: [],\r\n      expireTimeOption: {\r\n        disabledDate(date) {\r\n          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean\r\n          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    console.log('页面挂载完成，开始加载数据...');\r\n    this.getData();\r\n    this.getLvshi(); // 获取分类数据\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n\r\n    // 添加调试信息\r\n    this.$nextTick(() => {\r\n      console.log('页面渲染完成');\r\n      console.log('当前list数据:', this.list);\r\n      console.log('当前loading状态:', this.loading);\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  watch: {\r\n    dialogFormVisible(newVal, oldVal) {\r\n      console.log('对话框可见性变化:', newVal, oldVal);\r\n      if (!newVal && oldVal) {\r\n        // 对话框关闭时重置表单\r\n        this.handleDialogClose();\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    change() {},\r\n    changefield(field) {\r\n      this.field = field;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n      _this.getLvshi();\r\n      _this.dialogFormVisible = true;\r\n    },\r\n\r\n    getLvshi() {\r\n      // 添加测试分类数据\r\n      const testCategories = [\r\n        { id: 1, title: \"民事诉讼\", desc: \"民事纠纷相关文书\" },\r\n        { id: 2, title: \"商事诉讼\", desc: \"商业纠纷相关文书\" },\r\n        { id: 3, title: \"侵权诉讼\", desc: \"侵权纠纷相关文书\" },\r\n        { id: 4, title: \"婚姻家庭\", desc: \"婚姻家庭纠纷文书\" },\r\n        { id: 5, title: \"知识产权\", desc: \"知识产权纠纷文书\" },\r\n        { id: 6, title: \"劳动争议\", desc: \"劳动关系纠纷文书\" },\r\n        { id: 7, title: \"行政诉讼\", desc: \"行政纠纷相关文书\" },\r\n        { id: 8, title: \"刑事辩护\", desc: \"刑事案件相关文书\" }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        this.cates = testCategories;\r\n        console.log('加载测试分类数据:', this.cates);\r\n      }, 50);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      this.postRequest(\"/wenshucate/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          // 确保返回的数据是数组\r\n          this.cates = Array.isArray(resp.data) ? resp.data : [];\r\n          console.log('获取到的分类数据:', this.cates);\r\n        } else {\r\n          console.error('获取分类失败:', resp);\r\n          this.cates = [];\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取分类出错:', error);\r\n        this.cates = [];\r\n      });\r\n      */\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search.keyword = '';\r\n      this.searchData();\r\n    },\r\n\r\n    // 获取分类名称\r\n    getCategoryName(cateId) {\r\n      // 确保 cates 是数组\r\n      if (!Array.isArray(this.cates)) {\r\n        console.warn('cates is not an array:', this.cates);\r\n        return '未分类';\r\n      }\r\n      const category = this.cates.find(item => item.id === cateId);\r\n      return category ? category.title : '未分类';\r\n    },\r\n\r\n    // 预览文书\r\n    previewContract(row) {\r\n      console.log('预览文书:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      console.log('对话框关闭事件触发');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      console.log('取消按钮点击');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n      this.dialogFormVisible = false;\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27 && this.dialogFormVisible) {\r\n        this.cancelDialog();\r\n      }\r\n    },\r\n\r\n\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          title: \"民事起诉状模板\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/civil_complaint_template.docx\",\r\n          price: \"500.00\",\r\n          content: \"<p>这是一份标准的民事起诉状模板，适用于一般民事纠纷案件。包含完整的格式要求和必要条款。</p><p>主要内容包括：</p><ul><li>当事人基本信息</li><li>诉讼请求</li><li>事实与理由</li><li>证据清单</li></ul>\",\r\n          create_time: \"2024-01-15 10:30:00\",\r\n          update_time: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"劳动合同纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/labor_dispute_complaint.pdf\",\r\n          price: \"800.00\",\r\n          content: \"<p>专门针对劳动合同纠纷的起诉书模板，涵盖工资拖欠、违法解除等常见情形。</p><p>适用范围：</p><ul><li>工资拖欠纠纷</li><li>违法解除劳动合同</li><li>加班费争议</li><li>经济补偿金纠纷</li></ul>\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          update_time: \"2024-01-16 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"房屋买卖合同纠纷诉状\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/property_sale_dispute.docx\",\r\n          price: \"1200.00\",\r\n          content: \"<p>房屋买卖合同纠纷专用诉讼文书，包含房产交易中的各种争议处理。</p><p>涵盖问题：</p><ul><li>房屋质量问题</li><li>逾期交房</li><li>产权过户纠纷</li><li>定金违约</li></ul>\",\r\n          create_time: \"2024-01-17 09:15:00\",\r\n          update_time: \"2024-01-17 09:15:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"交通事故赔偿起诉书\",\r\n          cate_id: 3,\r\n          file_path: \"/uploads/documents/traffic_accident_claim.pdf\",\r\n          price: \"600.00\",\r\n          content: \"<p>交通事故人身损害赔偿起诉书模板，适用于各类交通事故赔偿案件。</p><p>赔偿项目：</p><ul><li>医疗费</li><li>误工费</li><li>护理费</li><li>精神损害抚慰金</li></ul>\",\r\n          create_time: \"2024-01-18 16:45:00\",\r\n          update_time: \"2024-01-18 16:45:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"借款合同纠纷起诉状\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/loan_dispute_complaint.docx\",\r\n          price: \"400.00\",\r\n          content: \"<p>民间借贷纠纷起诉状模板，适用于个人借款、企业借贷等各类借款纠纷。</p><p>主要条款：</p><ul><li>借款本金确认</li><li>利息计算标准</li><li>违约责任</li><li>担保责任</li></ul>\",\r\n          create_time: \"2024-01-19 11:30:00\",\r\n          update_time: \"2024-01-19 11:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"离婚纠纷起诉书\",\r\n          cate_id: 4,\r\n          file_path: \"/uploads/documents/divorce_complaint.pdf\",\r\n          price: \"900.00\",\r\n          content: \"<p>离婚纠纷起诉书模板，包含财产分割、子女抚养等完整内容。</p><p>主要内容：</p><ul><li>夫妻感情破裂事实</li><li>财产分割方案</li><li>子女抚养安排</li><li>债务承担</li></ul>\",\r\n          create_time: \"2024-01-20 13:20:00\",\r\n          update_time: \"2024-01-20 13:20:00\"\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"知识产权侵权起诉状\",\r\n          cate_id: 5,\r\n          file_path: \"/uploads/documents/ip_infringement_complaint.docx\",\r\n          price: \"1500.00\",\r\n          content: \"<p>知识产权侵权起诉状模板，适用于商标、专利、著作权等侵权案件。</p><p>保护范围：</p><ul><li>商标权侵权</li><li>专利权侵权</li><li>著作权侵权</li><li>商业秘密侵权</li></ul>\",\r\n          create_time: \"2024-01-21 15:10:00\",\r\n          update_time: \"2024-01-21 15:10:00\"\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"公司股权纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/equity_dispute_complaint.pdf\",\r\n          price: \"2000.00\",\r\n          content: \"<p>公司股权纠纷起诉书模板，处理股东权益、公司治理等复杂商事纠纷。</p><p>争议类型：</p><ul><li>股权转让纠纷</li><li>股东知情权</li><li>利润分配争议</li><li>公司决议效力</li></ul>\",\r\n          create_time: \"2024-01-22 10:00:00\",\r\n          update_time: \"2024-01-22 10:00:00\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载测试数据...');\r\n          console.log('原始测试数据:', testData);\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.content.toLowerCase().includes(keyword)\r\n            );\r\n            console.log('搜索关键词:', keyword);\r\n            console.log('搜索结果:', filteredData);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('当前页:', _this.page);\r\n          console.log('每页大小:', _this.size);\r\n          console.log('分页后的数据:', pageData);\r\n          console.log('设置到list的数据:', _this.list);\r\n          console.log('总数:', _this.total);\r\n          console.log('加载状态:', _this.loading);\r\n        } catch (error) {\r\n          console.error('加载测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 100); // 减少延迟到100ms\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            // 确保返回的数据是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n            console.log('获取到的列表数据:', _this.list);\r\n          } else {\r\n            console.error('获取数据失败:', resp);\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.field] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-list-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left .page-title i {\r\n  font-size: 28px;\r\n}\r\n\r\n.header-left .page-subtitle {\r\n  margin: 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.add-btn:hover, .refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e4e7ed;\r\n  padding-left: 40px;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn {\r\n  border: 2px solid #e4e7ed;\r\n  color: #606266;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn:hover {\r\n  border-color: #c0c4cc;\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格区域样式 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.contract-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.contract-table >>> .el-table__row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.title-cell i {\r\n  color: #667eea;\r\n  font-size: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.time-cell i {\r\n  color: #909399;\r\n}\r\n\r\n.price-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #e6a23c;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-text i {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n  border-width: 1px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #667eea;\r\n}\r\n\r\n.preview-body {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #667eea;\r\n}\r\n\r\n.file-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  background: white;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-info i {\r\n  color: #667eea;\r\n  font-size: 18px;\r\n}\r\n\r\n.content-preview h4 {\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.content-html {\r\n  background: white;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-list-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-left {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表单对话框样式 */\r\n.form-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__close {\r\n  color: white !important;\r\n  font-size: 20px !important;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__footer {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": "AAyTA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;MACAC,OAAA;MACAC,QAAA;QACAR,KAAA;QACAS,MAAA;MACA;MAEAC,KAAA;QACAV,KAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,OAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,SAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAG,cAAA;MACAC,KAAA;MACAC,gBAAA;QACAC,aAAAC,IAAA;UACA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA;IACA;IACAC,QAAA,CAAAC,gBAAA,iBAAAC,aAAA;;IAEA;IACA,KAAAC,SAAA;MACAP,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,mBAAAnC,IAAA;MACAkC,OAAA,CAAAC,GAAA,sBAAA7B,OAAA;IACA;EACA;EAEAoC,cAAA;IACA;IACAJ,QAAA,CAAAK,mBAAA,iBAAAH,aAAA;EACA;EAEAI,KAAA;IACAjC,kBAAAkC,MAAA,EAAAC,MAAA;MACAZ,OAAA,CAAAC,GAAA,cAAAU,MAAA,EAAAC,MAAA;MACA,KAAAD,MAAA,IAAAC,MAAA;QACA;QACA,KAAAC,iBAAA;MACA;IACA;EACA;EAEAC,OAAA;IACAC,OAAA;IACAC,YAAA1C,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACA2C,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAAnC,QAAA;UACAR,KAAA;UACA8C,IAAA;QACA;MACA;MACAF,KAAA,CAAAhB,QAAA;MACAgB,KAAA,CAAA1C,iBAAA;IACA;IAEA0B,SAAA;MACA;MACA,MAAAmB,cAAA,IACA;QAAAJ,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,GACA;QAAAH,EAAA;QAAA3C,KAAA;QAAA8C,IAAA;MAAA,EACA;;MAEA;MACAE,UAAA;QACA,KAAA/B,KAAA,GAAA8B,cAAA;QACAtB,OAAA,CAAAC,GAAA,mBAAAT,KAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA4B,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAK,UAAA,CAAAL,KAAA,CAAA9C,GAAA,gBAAA6C,EAAA,EAAAO,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAP,KAAA,CAAApC,QAAA,GAAA2C,IAAA,CAAA9D,IAAA;QACA;MACA;IACA;IACA+D,QAAAC,KAAA,EAAAV,EAAA;MACA,KAAAW,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAA5D,GAAA,kBAAA6C,EAAA,EAAAO,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACA7C,OAAA;YACA;YACA,KAAArB,IAAA,CAAAsE,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACA7C,OAAA;QACA;MACA;IACA;IACAmD,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAAzE,IAAA;MACA,KAAAC,IAAA;MACA,KAAAiC,OAAA;IACA;IAEA;IACAwC,YAAA;MACA,KAAAxE,MAAA,CAAAC,OAAA;MACA,KAAAsE,UAAA;IACA;IAEA;IACAE,gBAAAC,MAAA;MACA;MACA,KAAAC,KAAA,CAAAC,OAAA,MAAAtD,KAAA;QACAQ,OAAA,CAAA+C,IAAA,gCAAAvD,KAAA;QACA;MACA;MACA,MAAAwD,QAAA,QAAAxD,KAAA,CAAAyD,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAhC,EAAA,KAAA0B,MAAA;MACA,OAAAI,QAAA,GAAAA,QAAA,CAAAzE,KAAA;IACA;IAEA;IACA4E,gBAAAC,GAAA;MACApD,OAAA,CAAAC,GAAA,UAAAmD,GAAA;MACA,KAAAzE,WAAA,GAAAyE,GAAA;MACA,KAAA1E,aAAA;IACA;IAEA;IACA2E,aAAAC,OAAA;MACA,MAAAC,IAAA,GAAAnD,QAAA,CAAAoD,aAAA;MACAD,IAAA,CAAAE,IAAA,GAAAH,OAAA;MACAC,IAAA,CAAAG,QAAA,GAAAJ,OAAA,CAAAK,KAAA,MAAAC,GAAA;MACAxD,QAAA,CAAAyD,IAAA,CAAAC,WAAA,CAAAP,IAAA;MACAA,IAAA,CAAAQ,KAAA;MACA3D,QAAA,CAAAyD,IAAA,CAAAG,WAAA,CAAAT,IAAA;MACA,KAAApB,QAAA,CAAA8B,OAAA;IACA;IAEA;IACApD,kBAAA;MACAb,OAAA,CAAAC,GAAA;MACA;MACA,SAAAiE,KAAA,CAAAnF,QAAA;QACA,KAAAmF,KAAA,CAAAnF,QAAA,CAAAoF,WAAA;MACA;MACA,KAAApF,QAAA;QACAR,KAAA;QACAS,MAAA;QACAK,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;MACA;MACA,KAAAvF,OAAA;IACA;IAEA;IACAwF,aAAA;MACAtE,OAAA,CAAAC,GAAA;MACA;MACA,SAAAiE,KAAA,CAAAnF,QAAA;QACA,KAAAmF,KAAA,CAAAnF,QAAA,CAAAoF,WAAA;MACA;MACA,KAAApF,QAAA;QACAR,KAAA;QACAS,MAAA;QACAK,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;MACA;MACA,KAAAvF,OAAA;MACA,KAAAL,iBAAA;IACA;IAEA;IACA6B,cAAAiE,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,OAAA,gBAAA/F,iBAAA;QACA,KAAA6F,YAAA;MACA;IACA;IAIApE,QAAA;MACA,IAAAiB,KAAA;MAEAA,KAAA,CAAA/C,OAAA;;MAEA;MACA,MAAAqG,QAAA,IACA;QACAvD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,GACA;QACAzD,EAAA;QACA3C,KAAA;QACAc,OAAA;QACAC,SAAA;QACA8E,KAAA;QACAC,OAAA;QACAK,WAAA;QACAC,WAAA;MACA,EACA;;MAEA;MACApD,UAAA;QACA;UACAvB,OAAA,CAAAC,GAAA;UACAD,OAAA,CAAAC,GAAA,YAAAwE,QAAA;;UAEA;UACA,IAAAG,YAAA,GAAAH,QAAA;UACA,IAAAtD,KAAA,CAAAjD,MAAA,CAAAC,OAAA,IAAAgD,KAAA,CAAAjD,MAAA,CAAAC,OAAA,CAAA0G,IAAA;YACA,MAAA1G,OAAA,GAAAgD,KAAA,CAAAjD,MAAA,CAAAC,OAAA,CAAA0G,IAAA,GAAAC,WAAA;YACAF,YAAA,GAAAH,QAAA,CAAAM,MAAA,CAAA7B,IAAA,IACAA,IAAA,CAAA3E,KAAA,CAAAuG,WAAA,GAAAE,QAAA,CAAA7G,OAAA,KACA+E,IAAA,CAAAmB,OAAA,CAAAS,WAAA,GAAAE,QAAA,CAAA7G,OAAA,CACA;YACA6B,OAAA,CAAAC,GAAA,WAAA9B,OAAA;YACA6B,OAAA,CAAAC,GAAA,UAAA2E,YAAA;UACA;;UAEA;UACA,MAAAK,UAAA,IAAA9D,KAAA,CAAAnD,IAAA,QAAAmD,KAAA,CAAAlD,IAAA;UACA,MAAAiH,QAAA,GAAAD,UAAA,GAAA9D,KAAA,CAAAlD,IAAA;UACA,MAAAkH,QAAA,GAAAP,YAAA,CAAAQ,KAAA,CAAAH,UAAA,EAAAC,QAAA;UAEA/D,KAAA,CAAArD,IAAA,GAAAqH,QAAA;UACAhE,KAAA,CAAApD,KAAA,GAAA6G,YAAA,CAAAS,MAAA;UACAlE,KAAA,CAAA/C,OAAA;UAEA4B,OAAA,CAAAC,GAAA,SAAAkB,KAAA,CAAAnD,IAAA;UACAgC,OAAA,CAAAC,GAAA,UAAAkB,KAAA,CAAAlD,IAAA;UACA+B,OAAA,CAAAC,GAAA,YAAAkF,QAAA;UACAnF,OAAA,CAAAC,GAAA,gBAAAkB,KAAA,CAAArD,IAAA;UACAkC,OAAA,CAAAC,GAAA,QAAAkB,KAAA,CAAApD,KAAA;UACAiC,OAAA,CAAAC,GAAA,UAAAkB,KAAA,CAAA/C,OAAA;QACA,SAAAkH,KAAA;UACAtF,OAAA,CAAAsF,KAAA,cAAAA,KAAA;UACAnE,KAAA,CAAArD,IAAA;UACAqD,KAAA,CAAApD,KAAA;UACAoD,KAAA,CAAA/C,OAAA;QACA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAmH,SAAA;MACA,IAAApE,KAAA;MACA,KAAA+C,KAAA,aAAAsB,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAAvE,KAAA,CAAA9C,GAAA,gBAAAU,QAAA,EAAA0C,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAf,KAAA,CAAAgB,QAAA;gBACAH,IAAA;gBACA7C,OAAA,EAAAuC,IAAA,CAAAiE;cACA;cACA,KAAAzF,OAAA;cACAiB,KAAA,CAAA1C,iBAAA;YACA;cACA0C,KAAA,CAAAgB,QAAA;gBACAH,IAAA;gBACA7C,OAAA,EAAAuC,IAAA,CAAAiE;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA5H,IAAA,GAAA4H,GAAA;MAEA,KAAA3F,OAAA;IACA;IACA4F,oBAAAD,GAAA;MACA,KAAA7H,IAAA,GAAA6H,GAAA;MACA,KAAA3F,OAAA;IACA;IACA6F,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAA9D,IAAA;QACA,KAAAC,QAAA,CAAA8B,OAAA;QACA,KAAAlF,QAAA,MAAAT,KAAA,IAAA0H,GAAA,CAAApI,IAAA,CAAAS,GAAA;MACA;QACA,KAAA8D,QAAA,CAAAmD,KAAA,CAAAU,GAAA,CAAAL,GAAA;MACA;IACA;IAEAM,UAAAC,IAAA;MACA,KAAAtH,UAAA,GAAAsH,IAAA;MACA,KAAArH,aAAA;IACA;IACAsH,aAAAD,IAAA;MACA,SAAAE,KAAA;QACA,MAAAC,UAAA,6BAAAC,IAAA,CAAAtE,IAAA;QACA,KAAAqE,UAAA;UACA,KAAAlE,QAAA,CAAAmD,KAAA;UACA;QACA;MACA;IACA;IACAiB,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAArF,KAAA;MACAA,KAAA,CAAAK,UAAA,gCAAA0E,IAAA,EAAAzE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAf,KAAA,CAAApC,QAAA,CAAAyH,QAAA;UAEArF,KAAA,CAAAgB,QAAA,CAAA8B,OAAA;QACA;UACA9C,KAAA,CAAAgB,QAAA,CAAAmD,KAAA,CAAA5D,IAAA,CAAAiE,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}