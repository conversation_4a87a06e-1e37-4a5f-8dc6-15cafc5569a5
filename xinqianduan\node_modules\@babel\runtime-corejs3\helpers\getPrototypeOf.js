var _Object$setPrototypeOf = require("core-js-pure/features/object/set-prototype-of.js");
var _bindInstanceProperty = require("core-js-pure/features/instance/bind.js");
var _Object$getPrototypeOf = require("core-js-pure/features/object/get-prototype-of.js");
function _getPrototypeOf(t) {
  var _context;
  return (module.exports = _getPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$getPrototypeOf).call(_context) : function (t) {
    return t.__proto__ || _Object$getPrototypeOf(t);
  }, module.exports.__esModule = true, module.exports["default"] = module.exports), _getPrototypeOf(t);
}
module.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;