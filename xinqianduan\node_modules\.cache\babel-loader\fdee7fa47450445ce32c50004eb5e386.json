{"remainingRequest": "H:\\fdbfront\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\src\\components\\js\\AlertMenu.js", "dependencies": [{"path": "H:\\fdbfront\\src\\components\\js\\AlertMenu.js", "mtime": 1732626900070}, {"path": "H:\\fdbfront\\babel.config.js", "mtime": 1732626900032}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748278547513}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["E", "axios", "$", "BtnMenu", "DropListMenu", "PanelMenu", "DropList", "Panel", "<PERSON><PERSON><PERSON>", "_this", "Alert<PERSON>enu", "constructor", "editor", "$elem", "clickHandler", "tryChangeActive", "active"], "sources": ["H:/fdbfront/src/components/js/AlertMenu.js"], "sourcesContent": ["import E from 'wangeditor' // npm 安装\r\nimport axios from \"axios\";\r\nconst { $, BtnMenu, DropListMenu, PanelMenu, DropList, Panel, Tooltip } = E\r\nvar _this = null\r\nexport  default class AlertMenu extends BtnMenu {\r\n  constructor(editor) {\r\n    // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述\r\n    _this = editor\r\n    const $elem = E.$(\r\n      `<div class=\"w-e-menu\" data-title=\"上传文件\">\r\n      <div style=\"width: 18px;height: 18px;display: flex;\"><img src=\"./file.png\"/>\r\n      <input type=\"file\" style=\"opacity: 0;width: 16px;height: 16px;position: absolute;\" onchange=\"handleFileChange(this)\"/></div>\r\n      </div>`\r\n\r\n    )\r\n    super($elem, editor)\r\n  }\r\n  // 菜单点击事件\r\n  clickHandler() {\r\n    // 做任何你想做的事情\r\n    // 可参考【常用 API】文档，来操作编辑器\r\n   \r\n    // _this.cmd.do('insertHTML', '<h1>selectionText</h1>')\r\n\r\n  }\r\n  // 菜单是否被激活（如果不需要，这个函数可以空着）\r\n  // 1. 激活是什么？光标放在一段加粗、下划线的文本时，菜单栏里的 B 和 U 被激活，如下图\r\n  // 2. 什么时候执行这个函数？每次编辑器区域的选区变化（如鼠标操作、键盘操作等），都会触发各个菜单的 tryChangeActive 函数，重新计算菜单的激活状态\r\n  tryChangeActive() {\r\n    // 激活菜单\r\n    // 1. 菜单 DOM 节点会增加一个 .w-e-active 的 css class\r\n    // 2. this.this.isActive === true\r\n    this.active()\r\n\r\n    // // 取消激活菜单\r\n    // // 1. 菜单 DOM 节点会删掉 .w-e-active\r\n    // // 2. this.this.isActive === false\r\n    // this.unActive()\r\n  }\r\n\r\n\r\n\r\n}\r\n"], "mappings": "AAAA,OAAOA,CAAC,MAAM,YAAY,EAAC;AAC3B,OAAOC,KAAK,MAAM,OAAO;AACzB,MAAM;EAAEC,CAAC;EAAEC,OAAO;EAAEC,YAAY;EAAEC,SAAS;EAAEC,QAAQ;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGR,CAAC;AAC3E,IAAIS,KAAK,GAAG,IAAI;AAChB,eAAgB,MAAMC,SAAS,SAASP,OAAO,CAAC;EAC9CQ,WAAWA,CAACC,MAAM,EAAE;IAClB;IACAH,KAAK,GAAGG,MAAM;IACd,MAAMC,KAAK,GAAGb,CAAC,CAACE,CAAC,CACf;AACN;AACA;AACA,aAEI,CAAC;IACD,KAAK,CAACW,KAAK,EAAED,MAAM,CAAC;EACtB;EACA;EACAE,YAAYA,CAAA,EAAG;IACb;IACA;;IAEA;EAAA;EAGF;EACA;EACA;EACAC,eAAeA,CAAA,EAAG;IAChB;IACA;IACA;IACA,IAAI,CAACC,MAAM,CAAC,CAAC;;IAEb;IACA;IACA;IACA;EACF;AAIF", "ignoreList": []}]}