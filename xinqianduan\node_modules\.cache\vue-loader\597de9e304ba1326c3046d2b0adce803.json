{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748443889839}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748336490492}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748336507382}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748336500812}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAm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file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <!-- 如果有子菜单，显示为下拉菜单 -->\r\n          <el-submenu\r\n            v-for=\"(item, index) in menus\"\r\n            v-if=\"item.children && item.children.length > 1\"\r\n            :key=\"'submenu-' + index\"\r\n            :index=\"item.path\"\r\n            popper-class=\"vertical-submenu\"\r\n          >\r\n            <template slot=\"title\">{{ item.name }}</template>\r\n            <el-menu-item\r\n              v-for=\"(child, indexj) in item.children\"\r\n              :key=\"indexj\"\r\n              :index=\"child.path\"\r\n            >\r\n              {{ child.name }}\r\n            </el-menu-item>\r\n          </el-submenu>\r\n          <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\r\n          <el-menu-item\r\n            v-for=\"(item, index) in menus\"\r\n            v-if=\"!item.children || item.children.length <= 1\"\r\n            :key=\"'menuitem-' + index\"\r\n            :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\r\n          >\r\n            {{ item.name }}\r\n          </el-menu-item>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">管理员</span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item\r\n              ><div @click=\"menuClick('/changePwd')\">\r\n                修改密码\r\n              </div></el-dropdown-item\r\n            >\r\n            <el-dropdown-item>\r\n              <div @click=\"logout()\">退出登录</div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{\r\n            this.$router.currentRoute.name\r\n          }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <el-row :gutter=\"12\" v-if=\"this.$router.currentRoute.path == '/'\">\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\"> 访问量 {{ visit_count }}</el-card>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\">\r\n              <span @click=\"showQrcode\">查看二维码</span></el-card\r\n            >\r\n          </el-col>\r\n        </el-row>\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户\" },\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/zhifu\",\r\n        name: \"支付列表\",\r\n        children: [\r\n          { path: \"/order\", name: \"支付列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/liaotian\",\r\n        name: \"聊天列表\",\r\n        children: [\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" },\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" },\r\n          { path: \"/lvshi\", name: \"律师\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"专业管理\",\r\n        children: [\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-submenu__drop-down .el-menu .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* 强制下拉菜单容器为垂直布局 */\r\n.el-submenu__drop-down,\r\n.el-menu--horizontal .el-submenu__drop-down,\r\n.el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 确保子菜单内的ul也是垂直的 */\r\n.el-submenu__drop-down ul,\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  list-style: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制覆盖任何可能的inline样式 */\r\n.el-submenu__drop-down .el-menu-item[style] {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n}\r\n\r\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  border-right: none !important;\r\n  border-left: none !important;\r\n  border-top: none !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  position: relative !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down {\r\n  display: block !important;\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n/* 使用更高的CSS优先级 */\r\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对自定义popper-class的样式 - 美化版本 */\r\n.vertical-submenu {\r\n  display: block !important;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\r\n  overflow: hidden !important;\r\n  min-width: 180px !important;\r\n  padding: 8px 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu {\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n  background: transparent !important;\r\n  border: none !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  height: 44px !important;\r\n  line-height: 44px !important;\r\n  padding: 0 20px !important;\r\n  margin: 2px 8px !important;\r\n  background-color: rgba(255, 255, 255, 0.95) !important;\r\n  color: #2c3e50 !important;\r\n  text-align: left !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  box-sizing: border-box !important;\r\n  clear: both !important;\r\n  font-weight: 500 !important;\r\n  font-size: 14px !important;\r\n  transition: all 0.3s ease !important;\r\n  position: relative !important;\r\n  width: calc(100% - 16px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  color: #fff !important;\r\n  transform: translateX(4px) !important;\r\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:active {\r\n  transform: translateX(2px) !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:last-child {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n/* 添加一些动画效果 */\r\n.vertical-submenu .el-menu-item::before {\r\n  content: '' !important;\r\n  position: absolute !important;\r\n  left: 0 !important;\r\n  top: 50% !important;\r\n  transform: translateY(-50%) !important;\r\n  width: 3px !important;\r\n  height: 0 !important;\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\r\n  border-radius: 0 2px 2px 0 !important;\r\n  transition: height 0.3s ease !important;\r\n}\r\n\r\n.vertical-submenu .el-menu-item:hover::before {\r\n  height: 20px !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 150px;\r\n  min-width: 150px;\r\n  text-align: right;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n}\r\n\r\n.user-info:hover {\r\n  color: #ffd04b;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background-color: #f5f5f5;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-breadcrumb {\r\n  line-height: 50px;\r\n}\r\n\r\n/* 主内容区域样式 - 新UI风格 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f5f5f5;\r\n  padding: 16px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 页面内容容器 */\r\n.main-content .page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面标题样式 */\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 标签页导航样式 */\r\n.tab-navigation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tab-navigation .el-tabs__header {\r\n  margin: 0;\r\n}\r\n\r\n.tab-navigation .el-tabs__nav-wrap::after {\r\n  height: 1px;\r\n  background-color: #e8e8e8;\r\n}\r\n\r\n/* 搜索筛选区域样式 */\r\n.search-section {\r\n  background: #fafafa;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 操作按钮区域 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-left: auto;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table {\r\n  margin-top: 16px;\r\n}\r\n\r\n.data-table .el-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n}\r\n\r\n.data-table .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.data-table .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding: 16px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .top-header {\r\n    flex-direction: column;\r\n    height: auto;\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-center {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .top-menu {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}