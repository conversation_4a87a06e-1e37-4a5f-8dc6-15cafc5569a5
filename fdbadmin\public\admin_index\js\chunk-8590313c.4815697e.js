(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8590313c"],{2044:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",{staticStyle:{width:"600px"}},[t("el-input",{attrs:{placeholder:"请输入名称/律所/证号",size:"mini"},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.searchData()}},slot:"append"})],1)],1),t("el-row",{staticClass:"page-top"},[t("el-button",{attrs:{type:"primary",size:e.allSize},on:{click:function(t){return e.editData(0)}}},[e._v("新增")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"}},[t("el-table-column",{attrs:{prop:"title",label:"标题"}}),t("el-table-column",{attrs:{prop:"lvsuo",label:"律所"}}),t("el-table-column",{attrs:{prop:"zhuanyes",label:"专业"}}),t("el-table-column",{attrs:{prop:"phone",label:"联系方式"}}),t("el-table-column",{attrs:{prop:"pic_path",label:"头像"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("img",{staticStyle:{width:"160px",height:"80px"},attrs:{src:a.row.pic_path},on:{click:function(t){return e.showImage(a.row.pic_path)}}})]}}])}),t("el-table-column",{attrs:{prop:"card_path",label:"证书"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("img",{staticStyle:{width:"160px",height:"80px"},attrs:{src:a.row.card_path},on:{click:function(t){return e.showImage(a.row.card_path)}}})]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"注册时间"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(a.$index,a.row.id)}}},[e._v(" 移除 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:e.title+"姓名","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"绑定员工","label-width":e.formLabelWidth,prop:"yuangong_id"}},[t("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:e.ruleForm.yuangong_id,callback:function(t){e.$set(e.ruleForm,"yuangong_id",t)},expression:"ruleForm.yuangong_id"}},e._l(e.yuangongs,(function(a){return t("el-option-group",{key:a.label,attrs:{label:a.label}},e._l(a.options,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)})),1)],1),t("el-form-item",{attrs:{label:"专业","label-width":e.formLabelWidth,prop:"zhuanyes"}},[t("el-select",{attrs:{multiple:"",placeholder:"请选择"},model:{value:e.ruleForm.zhuanyes,callback:function(t){e.$set(e.ruleForm,"zhuanyes",t)},expression:"ruleForm.zhuanyes"}},e._l(e.zhuanyes,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"律所","label-width":e.formLabelWidth,prop:"lvsuo"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.lvsuo,callback:function(t){e.$set(e.ruleForm,"lvsuo",t)},expression:"ruleForm.lvsuo"}})],1),t("el-form-item",{attrs:{label:"职业年薪","label-width":e.formLabelWidth,prop:"age"}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.age,callback:function(t){e.$set(e.ruleForm,"age",t)},expression:"ruleForm.age"}})],1),t("el-form-item",{attrs:{label:"联系方式","label-width":e.formLabelWidth,prop:"phone"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),t("el-form-item",{attrs:{label:"证件号","label-width":e.formLabelWidth,prop:"laywer_card"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.laywer_card,callback:function(t){e.$set(e.ruleForm,"laywer_card",t)},expression:"ruleForm.laywer_card"}})],1),t("el-form-item",{attrs:{label:"封面","label-width":e.formLabelWidth,prop:"pic_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.pic_path,callback:function(t){e.$set(e.ruleForm,"pic_path",t)},expression:"ruleForm.pic_path"}},[t("template",{slot:"append"},[e._v("330rpx*300rpx")])],2),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeField("pic_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.pic_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.pic_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.pic_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.pic_path,"pic_path")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"证书","label-width":e.formLabelWidth,prop:"card_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.card_path,callback:function(t){e.$set(e.ruleForm,"card_path",t)},expression:"ruleForm.card_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changeField("card_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.card_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.card_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.card_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.card_path,"card_path")}}},[e._v("删除")]):e._e()],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},r=[],o=a("0c98"),i={name:"list",components:{EditorBar:o["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,zhuanyes:[],url:"/lvshi/",title:"律师",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写律师姓名",trigger:"blur"}],yuangong_id:[{required:!0,message:"请绑定员工",trigger:"blur"}],zhuanyes:[{required:!0,message:"请选择专业",trigger:"blur"}],lvsuo:[{required:!0,message:"请填写律所",trigger:"blur"}],age:[{required:!0,message:"请填写职业年限",trigger:"blur"}],laywer_card:[{required:!0,message:"请填写证件号",trigger:"blur"}],phone:[{required:!0,message:"请填写律师联系方式",trigger:"blur"}],pic_path:[{required:!0,message:"请上传封面",trigger:"blur"}],card_path:[{required:!0,message:"请上传证书",trigger:"blur"}]},formLabelWidth:"120px",field:"",yuangongs:[]}},mounted(){this.getData()},methods:{changeField(e){this.field=e},getLvshi(){let e=this;e.getRequest("/yuangong/getMoreList").then(t=>{200==t.code&&(e.yuangongs=t.data)})},getZhuanyes(){let e=this;e.getRequest("/zhuanye/getList").then(t=>{t&&(e.zhuanyes=t.data)})},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",phone:"",address:"",pic_path:"",card_path:"",zhuanyes:"",age:""},t.dialogFormVisible=!0,t.getZhuanyes(),t.getLvshi()},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm[this.field]=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let a=this;a.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(a.ruleForm[t]="",a.$message.success("删除成功!")):a.$message.error(e.msg)})}}},s=i,n=(a("8716"),a("2877")),u=Object(n["a"])(s,l,r,!1,null,"0ebabf81",null);t["default"]=u.exports},8716:function(e,t,a){"use strict";a("eaed")},eaed:function(e,t,a){}}]);
//# sourceMappingURL=chunk-8590313c.4815697e.js.map