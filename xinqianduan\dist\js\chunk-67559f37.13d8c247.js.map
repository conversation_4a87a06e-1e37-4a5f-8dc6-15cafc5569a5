{"version": 3, "sources": ["webpack:///./src/views/pages/yuangong/quanxian.vue", "webpack:///src/views/pages/yuangong/quanxian.vue", "webpack:///./src/views/pages/yuangong/quanxian.vue?be99", "webpack:///./src/views/pages/yuangong/quanxian.vue?7eab", "webpack:///./src/views/pages/yuangong/quanxian.vue?8ed0"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "$event", "editData", "_v", "refulsh", "nativeOn", "type", "indexOf", "_k", "keyCode", "key", "searchData", "apply", "arguments", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "status", "clearSearch", "viewMode", "treeData", "treeProps", "scopedSlots", "_u", "fn", "node", "data", "class", "getNodeIcon", "_s", "label", "id", "<PERSON><PERSON><PERSON><PERSON>", "delData", "_e", "directives", "name", "rawName", "loading", "tableData", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scope", "style", "paddingLeft", "row", "level", "staticStyle", "getTypeColor", "getTypeLabel", "changeStatus", "stopPropagation", "dialogTitle", "dialogFormVisible", "ref", "ruleForm", "rules", "code", "parentOptions", "cascaderProps", "parent_id", "sort", "description", "path", "icon", "saveData", "staticRenderFns", "originalData", "required", "message", "trigger", "checkStrictly", "computed", "flattenTreeForTable", "mounted", "getData", "methods", "setTimeout", "mockData", "create_time", "JSON", "parse", "stringify", "updateParentOptions", "tree", "result", "for<PERSON>ach", "flatNode", "length", "push", "buildCascaderOptions", "map", "$router", "go", "permission", "findPermissionById", "parentData", "found", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$message", "success", "catch", "info", "$refs", "validate", "valid", "iconMap", "menu", "action", "colorMap", "labelMap", "component"], "mappings": "yHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,UAAUE,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAAS,MAAM,CAACT,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIW,UAAU,CAACX,EAAIU,GAAG,WAAW,OAAOR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,aAAa,UAAY,IAAIM,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAIA,EAAOK,KAAKC,QAAQ,QAAQd,EAAIe,GAAGP,EAAOQ,QAAQ,QAAQ,GAAGR,EAAOS,IAAI,SAAgB,KAAYjB,EAAIkB,WAAWC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOtB,EAAIuB,OAAOC,QAASC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAAC1B,EAAG,IAAI,CAACE,YAAY,gCAAgCE,MAAM,CAAC,KAAO,UAAUuB,KAAK,cAAc,GAAG3B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOV,KAAMY,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,OAAQG,IAAME,WAAW,gBAAgB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,YAAYJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,WAAW,IAAI,GAAGJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,QAAQR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOO,OAAQL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,SAAUG,IAAME,WAAW,kBAAkB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,KAAKJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,IAAI,KAAKJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAIkB,aAAa,CAAClB,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQP,EAAI+B,cAAc,CAAC/B,EAAIU,GAAG,WAAW,QAAQ,GAAGR,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,cAAcR,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,SAAjBN,EAAIgC,SAAsB,UAAY,GAAG,KAAO,iBAAiB,KAAO,SAASzB,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIgC,SAAW,UAAU,CAAChC,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,UAAjBN,EAAIgC,SAAuB,UAAY,GAAG,KAAO,eAAe,KAAO,SAASzB,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIgC,SAAW,WAAW,CAAChC,EAAIU,GAAG,aAAa,IAAI,KAAuB,SAAjBV,EAAIgC,SAAqB9B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,UAAU,CAACE,YAAY,kBAAkBE,MAAM,CAAC,KAAON,EAAIiC,SAAS,MAAQjC,EAAIkC,UAAU,sBAAqB,EAAK,WAAW,MAAMC,YAAYnC,EAAIoC,GAAG,CAAC,CAACnB,IAAI,UAAUoB,GAAG,UAAS,KAAEC,EAAI,KAAEC,IAAQ,OAAOrC,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACsC,MAAMxC,EAAIyC,YAAYF,EAAK1B,QAAQX,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAGV,EAAI0C,GAAGH,EAAKI,UAAUzC,EAAG,SAAS,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAuB,IAAhBiC,EAAKT,OAAe,UAAY,SAAS,KAAO,SAAS,CAAC9B,EAAIU,GAAG,IAAIV,EAAI0C,GAAmB,IAAhBH,EAAKT,OAAe,KAAO,MAAM,QAAQ,GAAG5B,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAAS8B,EAAKK,OAAO,CAAC5C,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI6C,SAASN,MAAS,CAACvC,EAAIU,GAAG,aAAaR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI8C,QAAQP,EAAKK,OAAO,CAAC5C,EAAIU,GAAG,WAAW,UAAU,MAAK,EAAM,cAAc,GAAGV,EAAI+C,KAAuB,UAAjB/C,EAAIgC,SAAsB9B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAAC8C,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAY5B,MAAOtB,EAAImD,QAASvB,WAAW,YAAYxB,YAAY,mBAAmBE,MAAM,CAAC,KAAON,EAAIoD,UAAU,OAAS,GAAG,UAAU,KAAK,aAAa,CAACC,SAAU,WAAYC,YAAa,eAAe,sBAAqB,IAAQ,CAACpD,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,OAAO6B,YAAYnC,EAAIoC,GAAG,CAAC,CAACnB,IAAI,UAAUoB,GAAG,SAASkB,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,uBAAuBoD,MAAO,CAAEC,YAAsC,IAAxBF,EAAMG,IAAIC,OAAS,GAAU,OAAS,CAACzD,EAAG,IAAI,CAACsC,MAAMxC,EAAIyC,YAAYc,EAAMG,IAAI7C,MAAM+C,YAAY,CAAC,eAAe,SAAS1D,EAAG,OAAO,CAACE,YAAY,mBAAmB,CAACJ,EAAIU,GAAGV,EAAI0C,GAAGa,EAAMG,IAAIf,gBAAgB,MAAK,EAAM,aAAazC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,MAAM,MAAQ,SAAS,wBAAwB,MAAMJ,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAU6B,YAAYnC,EAAIoC,GAAG,CAAC,CAACnB,IAAI,UAAUoB,GAAG,SAASkB,GAAO,MAAO,CAACrD,EAAG,SAAS,CAACI,MAAM,CAAC,KAAON,EAAI6D,aAAaN,EAAMG,IAAI7C,MAAM,KAAO,UAAU,CAACb,EAAIU,GAAG,IAAIV,EAAI0C,GAAG1C,EAAI8D,aAAaP,EAAMG,IAAI7C,OAAO,WAAW,MAAK,EAAM,aAAaX,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAU6B,YAAYnC,EAAIoC,GAAG,CAAC,CAACnB,IAAI,UAAUoB,GAAG,SAASkB,GAAO,MAAO,CAACrD,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,GAAGC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOR,EAAI+D,aAAaR,EAAMG,OAAOrC,MAAM,CAACC,MAAOiC,EAAMG,IAAI5B,OAAQL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK4B,EAAMG,IAAK,SAAUhC,IAAME,WAAW,0BAA0B,MAAK,EAAM,cAAc1B,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,KAAK,MAAQ,YAAYJ,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,YAAYJ,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAU6B,YAAYnC,EAAIoC,GAAG,CAAC,CAACnB,IAAI,UAAUoB,GAAG,SAASkB,GAAO,MAAO,CAACrD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwD,kBAAyBhE,EAAIS,SAAS8C,EAAMG,IAAId,OAAO,CAAC5C,EAAIU,GAAG,UAA8B,SAAnB6C,EAAMG,IAAI7C,KAAiBX,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwD,kBAAyBhE,EAAI6C,SAASU,EAAMG,QAAQ,CAAC1D,EAAIU,GAAG,aAAaV,EAAI+C,KAAK7C,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwD,kBAAyBhE,EAAI8C,QAAQS,EAAMG,IAAId,OAAO,CAAC5C,EAAIU,GAAG,WAAW,OAAO,MAAK,EAAM,eAAe,IAAI,GAAGV,EAAI+C,QAAQ,GAAG7C,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQN,EAAIiE,YAAY,QAAUjE,EAAIkE,kBAAkB,wBAAuB,EAAM,MAAQ,OAAO3D,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAIkE,kBAAkB1D,KAAU,CAACN,EAAG,UAAU,CAACiE,IAAI,WAAW7D,MAAM,CAAC,MAAQN,EAAIoE,SAAS,MAAQpE,EAAIqE,MAAM,cAAc,UAAU,CAACnE,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASzB,MAAOlB,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,QAAS1C,IAAME,WAAW,qBAAqB,IAAI,GAAG1B,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASE,KAAM7C,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,OAAQ1C,IAAME,WAAW,oBAAoB,IAAI,IAAI,GAAG1B,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACJ,EAAG,YAAY,CAAC0D,YAAY,CAAC,MAAQ,QAAQtD,MAAM,CAAC,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASvD,KAAMY,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,OAAQ1C,IAAME,WAAW,kBAAkB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,YAAYJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,MAAQ,WAAW,IAAI,IAAI,GAAGJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,cAAc,CAAC0D,YAAY,CAAC,MAAQ,QAAQtD,MAAM,CAAC,QAAUN,EAAIuE,cAAc,MAAQvE,EAAIwE,cAAc,YAAc,UAAU,UAAY,IAAInD,MAAM,CAACC,MAAOtB,EAAIoE,SAASK,UAAWhD,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,YAAa1C,IAAME,WAAW,yBAAyB,IAAI,IAAI,GAAG1B,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,kBAAkB,CAAC0D,YAAY,CAAC,MAAQ,QAAQtD,MAAM,CAAC,IAAM,EAAE,IAAM,KAAKe,MAAM,CAACC,MAAOtB,EAAIoE,SAASM,KAAMjD,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,OAAQ1C,IAAME,WAAW,oBAAoB,IAAI,GAAG1B,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,EAAE,cAAc,KAAK,gBAAgB,MAAMe,MAAM,CAACC,MAAOtB,EAAIoE,SAAStC,OAAQL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,SAAU1C,IAAME,WAAW,sBAAsB,IAAI,IAAI,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASO,YAAalD,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,cAAe1C,IAAME,WAAW,2BAA2B,GAA0B,SAAtB5B,EAAIoE,SAASvD,KAAiBX,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASQ,KAAMnD,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,OAAQ1C,IAAME,WAAW,oBAAoB,GAAG5B,EAAI+C,KAA4B,SAAtB/C,EAAIoE,SAASvD,KAAiBX,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,WAAWe,MAAM,CAACC,MAAOtB,EAAIoE,SAASS,KAAMpD,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIoE,SAAU,OAAQ1C,IAAME,WAAW,kBAAkB,CAAC1B,EAAG,WAAW,CAAC2B,KAAK,WAAW,CAAC3B,EAAG,IAAI,CAACsC,MAAMxC,EAAIoE,SAASS,MAAQ,oBAAoB,IAAI,GAAG7E,EAAI+C,MAAM,GAAG7C,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUuB,KAAK,UAAU,CAAC3B,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIkE,mBAAoB,KAAS,CAAClE,EAAIU,GAAG,SAASR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI8E,cAAc,CAAC9E,EAAIU,GAAG,UAAU,IAAI,IAAI,IAEz8UqE,EAAkB,CAAC,WAAY,IAAI/E,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBJ,EAAIU,GAAG,YAAYR,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIU,GAAG,uBCyWxO,G,UAAA,CACfuC,KAAA,uBACAV,OACA,OACAP,SAAA,OACAmB,SAAA,EACA5B,OAAA,CACAC,QAAA,GACAX,KAAA,GACAiB,OAAA,IAEAG,SAAA,GACA+C,aAAA,GACAd,mBAAA,EACAD,YAAA,OACAM,cAAA,GACAH,SAAA,CACAxB,GAAA,KACAD,MAAA,GACA2B,KAAA,GACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,GACAC,KAAA,GACAC,KAAA,IAEAR,MAAA,CACA1B,MAAA,CACA,CAAAsC,UAAA,EAAAC,QAAA,UAAAC,QAAA,SAEAb,KAAA,CACA,CAAAW,UAAA,EAAAC,QAAA,UAAAC,QAAA,SAEAtE,KAAA,CACA,CAAAoE,UAAA,EAAAC,QAAA,UAAAC,QAAA,YAGAjD,UAAA,CACAmB,SAAA,WACAV,MAAA,SAEA6B,cAAA,CACAlD,MAAA,KACAqB,MAAA,QACAU,SAAA,WACA+B,eAAA,KAIAC,SAAA,CAEAjC,YACA,YAAAkC,oBAAA,KAAArD,YAGAsD,UACA,KAAAC,WAEAC,QAAA,CAEAD,UACA,KAAArC,SAAA,EAGAuC,WAAA,KACA,KAAAvC,SAAA,EAEA,MAAAwC,EAAA,CACA,CACA/C,GAAA,EACAD,MAAA,OACA2B,KAAA,SACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,UACAC,KAAA,kBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,GACAD,MAAA,OACA2B,KAAA,cACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,QACAC,KAAA,eACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,mBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,kBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,mBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,qBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,yBAIA,CACAhD,GAAA,GACAD,MAAA,OACA2B,KAAA,kBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,UACAC,KAAA,mBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,sBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,yBAIA,CACAhD,GAAA,GACAD,MAAA,OACA2B,KAAA,oBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,YACAC,KAAA,cACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,wBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,2BACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,2BAMA,CACAhD,GAAA,EACAD,MAAA,OACA2B,KAAA,WACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,YACAC,KAAA,mBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,GACAD,MAAA,OACA2B,KAAA,iBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,WACAC,KAAA,mBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,sBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,qBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,sBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,wBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,wBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,yBAIA,CACAhD,GAAA,GACAD,MAAA,OACA2B,KAAA,oBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,YACAC,KAAA,qBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,wBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,2BACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,2BAMA,CACAhD,GAAA,EACAD,MAAA,OACA2B,KAAA,WACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,YACAC,KAAA,wBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,GACAD,MAAA,OACA2B,KAAA,oBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,UACAC,KAAA,mBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,wBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,2BACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,0BACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,yBAIA,CACAhD,GAAA,GACAD,MAAA,QACA2B,KAAA,kBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,UACAC,KAAA,UACAC,KAAA,kBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,QACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,UACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,QACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,QACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,QACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,QACAiB,YAAA,yBAIA,CACAhD,GAAA,GACAD,MAAA,OACA2B,KAAA,kBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,WACAC,KAAA,qBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,OACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,sBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,OACAiB,YAAA,2BAMA,CACAhD,GAAA,EACAD,MAAA,OACA2B,KAAA,UACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,WACAC,KAAA,eACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,GACAD,MAAA,OACA2B,KAAA,kBACAzD,KAAA,OACA4D,UAAA,EACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAC,KAAA,SACAC,KAAA,gBACAe,YAAA,sBACAvC,SAAA,CACA,CACAT,GAAA,IACAD,MAAA,SACA2B,KAAA,uBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,OACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,uBAEA,CACAhD,GAAA,IACAD,MAAA,SACA2B,KAAA,yBACAzD,KAAA,SACA4D,UAAA,GACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,SACAiB,YAAA,4BAQA,KAAA3D,SAAA0D,EACA,KAAAX,aAAAa,KAAAC,MAAAD,KAAAE,UAAAJ,IACA,KAAAK,uBACA,MAIAV,oBAAAW,EAAAtC,EAAA,EAAAuC,EAAA,IAgBA,OAfAD,EAAAE,QAAA7D,IACA,MAAA8D,EAAA,IACA9D,EACAqB,QACAL,YAAAhB,EAAAe,UAAAf,EAAAe,SAAAgD,OAAA,UAGAD,EAAA/C,SACA6C,EAAAI,KAAAF,GAGA9D,EAAAe,UAAAf,EAAAe,SAAAgD,OAAA,GACA,KAAAf,oBAAAhD,EAAAe,SAAAM,EAAA,EAAAuC,KAGAA,GAIAF,sBACA,KAAAzB,cAAA,KAAAgC,qBAAA,KAAAtE,WAIAsE,qBAAAN,GACA,OAAAA,EAAAO,IAAAlE,IAAA,CACAM,GAAAN,EAAAM,GACAD,MAAAL,EAAAK,MACAU,SAAAf,EAAAe,SAAA,KAAAkD,qBAAAjE,EAAAe,UAAA,OAKAnC,aACA,KAAAsE,WAIAzD,cACA,KAAAR,OAAA,CACAC,QAAA,GACAX,KAAA,GACAiB,OAAA,IAEA,KAAAZ,cAIAP,UACA,KAAA8F,QAAAC,GAAA,IAIAjG,SAAAmC,GACA,OAAAA,EACA,KAAAqB,YAAA,OACA,KAAAG,SAAA,CACAxB,GAAA,KACAD,MAAA,GACA2B,KAAA,GACAzD,KAAA,OACA4D,UAAA,KACAC,KAAA,EACA5C,OAAA,EACA6C,YAAA,GACAC,KAAA,GACAC,KAAA,QAEA,CACA,KAAAZ,YAAA,OACA,MAAA0C,EAAA,KAAAC,mBAAAhE,GACA+D,IACA,KAAAvC,SAAA,IAAAuC,IAGA,KAAAzC,mBAAA,GAIArB,SAAAgE,GACA,KAAA5C,YAAA,QACA,KAAAG,SAAA,CACAxB,GAAA,KACAD,MAAA,GACA2B,KAAA,GACAzD,KAAA,SACA4D,UAAA,CAAAoC,EAAAjE,IACA8B,KAAA,EACA5C,OAAA,EACA6C,YAAA,GACAC,KAAA,GACAC,KAAA,IAEA,KAAAX,mBAAA,GAIA0C,mBAAAhE,EAAAqD,EAAA,KAAAjB,cACA,QAAA1C,KAAA2D,EAAA,CACA,GAAA3D,EAAAM,OACA,OAAAN,EAEA,GAAAA,EAAAe,SAAA,CACA,MAAAyD,EAAA,KAAAF,mBAAAhE,EAAAN,EAAAe,UACA,GAAAyD,EAAA,OAAAA,GAGA,aAIAhE,QAAAF,GACA,KAAAmE,SAAA,4BACAC,kBAAA,KACAC,iBAAA,KACApG,KAAA,YACAqG,KAAA,KAEA,KAAAC,SAAAC,QAAA,SACA,KAAA5B,YACA6B,MAAA,KACA,KAAAF,SAAAG,KAAA,YAKAxC,WACA,KAAAyC,MAAA,YAAAC,SAAAC,IACAA,IAEA,KAAAN,SAAAC,QAAA,KAAAhD,SAAAxB,GAAA,iBACA,KAAAsB,mBAAA,EACA,KAAAsB,cAMAzB,aAAAL,GACA,KAAAyD,SAAAC,QAAA,aAAA1D,EAAA5B,OAAA,aAIAW,YAAA5B,GACA,MAAA6G,EAAA,CACAC,KAAA,eACAC,OAAA,kBACArF,KAAA,oBAEA,OAAAmF,EAAA7G,IAAA,gBAIAgD,aAAAhD,GACA,MAAAgH,EAAA,CACAF,KAAA,UACAC,OAAA,UACArF,KAAA,WAEA,OAAAsF,EAAAhH,IAAA,WAIAiD,aAAAjD,GACA,MAAAiH,EAAA,CACAH,KAAA,OACAC,OAAA,OACArF,KAAA,QAEA,OAAAuF,EAAAjH,IAAA,WCnrC+W,I,wBCQ3WkH,EAAY,eACd,EACAhI,EACAgF,GACA,EACA,KACA,WACA,MAIa,aAAAgD,E,oECnBf", "file": "js/chunk-67559f37.13d8c247.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"permission-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"add-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增权限 \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新 \")])],1)])]),_c('div',{staticClass:\"search-section\"},[_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-row\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"权限搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入权限名称或描述\",\"clearable\":\"\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchData.apply(null, arguments)}},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"权限类型\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择权限类型\",\"clearable\":\"\"},model:{value:(_vm.search.type),callback:function ($$v) {_vm.$set(_vm.search, \"type\", $$v)},expression:\"search.type\"}},[_c('el-option',{attrs:{\"label\":\"菜单权限\",\"value\":\"menu\"}}),_c('el-option',{attrs:{\"label\":\"操作权限\",\"value\":\"action\"}}),_c('el-option',{attrs:{\"label\":\"数据权限\",\"value\":\"data\"}})],1)],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.status),callback:function ($$v) {_vm.$set(_vm.search, \"status\", $$v)},expression:\"search.status\"}},[_c('el-option',{attrs:{\"label\":\"启用\",\"value\":1}}),_c('el-option',{attrs:{\"label\":\"禁用\",\"value\":0}})],1)],1)]),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchData}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.clearSearch}},[_vm._v(\" 重置 \")])],1)])])],1),_c('div',{staticClass:\"tree-section\"},[_c('el-card',{staticClass:\"tree-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"tree-header\"},[_c('div',{staticClass:\"tree-title\"},[_c('i',{staticClass:\"el-icon-menu\"}),_vm._v(\" 权限树形结构 \")]),_c('div',{staticClass:\"tree-tools\"},[_c('el-button-group',[_c('el-button',{attrs:{\"type\":_vm.viewMode === 'tree' ? 'primary' : '',\"icon\":\"el-icon-s-grid\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'tree'}}},[_vm._v(\" 树形视图 \")]),_c('el-button',{attrs:{\"type\":_vm.viewMode === 'table' ? 'primary' : '',\"icon\":\"el-icon-menu\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'table'}}},[_vm._v(\" 列表视图 \")])],1)],1)]),(_vm.viewMode === 'tree')?_c('div',{staticClass:\"tree-view\"},[_c('el-tree',{staticClass:\"permission-tree\",attrs:{\"data\":_vm.treeData,\"props\":_vm.treeProps,\"default-expand-all\":true,\"node-key\":\"id\"},scopedSlots:_vm._u([{key:\"default\",fn:function({ node, data }){return _c('span',{staticClass:\"tree-node\"},[_c('div',{staticClass:\"node-content\"},[_c('div',{staticClass:\"node-info\"},[_c('i',{class:_vm.getNodeIcon(data.type)}),_c('span',{staticClass:\"node-label\"},[_vm._v(_vm._s(data.label))]),_c('el-tag',{staticClass:\"node-status\",attrs:{\"type\":data.status === 1 ? 'success' : 'danger',\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(data.status === 1 ? '启用' : '禁用')+\" \")])],1),_c('div',{staticClass:\"node-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.editData(data.id)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-plus\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.addChild(data)}}},[_vm._v(\" 添加子权限 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(data.id)}}},[_vm._v(\" 删除 \")])],1)])])}}],null,false,369117358)})],1):_vm._e(),(_vm.viewMode === 'table')?_c('div',{staticClass:\"table-view\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"permission-table\",attrs:{\"data\":_vm.tableData,\"stripe\":\"\",\"row-key\":\"id\",\"tree-props\":{children: 'children', hasChildren: 'hasChildren'},\"default-expand-all\":false}},[_c('el-table-column',{attrs:{\"prop\":\"label\",\"label\":\"权限名称\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"permission-name-cell\",style:({ paddingLeft: (scope.row.level || 0) * 20 + 'px' })},[_c('i',{class:_vm.getNodeIcon(scope.row.type),staticStyle:{\"margin-right\":\"8px\"}}),_c('span',{staticClass:\"permission-name\"},[_vm._v(_vm._s(scope.row.label))])])]}}],null,false,296214969)}),_c('el-table-column',{attrs:{\"prop\":\"code\",\"label\":\"权限代码\",\"width\":\"180\",\"align\":\"center\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"权限类型\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getTypeColor(scope.row.type),\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(_vm.getTypeLabel(scope.row.type))+\" \")])]}}],null,false,417277375)}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0},on:{\"change\":function($event){return _vm.changeStatus(scope.row)}},model:{value:(scope.row.status),callback:function ($$v) {_vm.$set(scope.row, \"status\", $$v)},expression:\"scope.row.status\"}})]}}],null,false,2880962836)}),_c('el-table-column',{attrs:{\"prop\":\"sort\",\"label\":\"排序\",\"width\":\"80\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建时间\",\"width\":\"160\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"240\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.editData(scope.row.id)}}},[_vm._v(\" 编辑 \")]),(scope.row.type === 'menu')?_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-plus\",\"plain\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.addChild(scope.row)}}},[_vm._v(\" 添加子权限 \")]):_vm._e(),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.delData(scope.row.id)}}},[_vm._v(\" 删除 \")])],1)]}}],null,false,2647926959)})],1)],1):_vm._e()])],1),_c('el-dialog',{attrs:{\"title\":_vm.dialogTitle,\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"60%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"权限名称\",\"prop\":\"label\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入权限名称\"},model:{value:(_vm.ruleForm.label),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"label\", $$v)},expression:\"ruleForm.label\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"权限代码\",\"prop\":\"code\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入权限代码\"},model:{value:(_vm.ruleForm.code),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"code\", $$v)},expression:\"ruleForm.code\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"权限类型\",\"prop\":\"type\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择权限类型\"},model:{value:(_vm.ruleForm.type),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"type\", $$v)},expression:\"ruleForm.type\"}},[_c('el-option',{attrs:{\"label\":\"菜单权限\",\"value\":\"menu\"}}),_c('el-option',{attrs:{\"label\":\"操作权限\",\"value\":\"action\"}}),_c('el-option',{attrs:{\"label\":\"数据权限\",\"value\":\"data\"}})],1)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"父级权限\"}},[_c('el-cascader',{staticStyle:{\"width\":\"100%\"},attrs:{\"options\":_vm.parentOptions,\"props\":_vm.cascaderProps,\"placeholder\":\"请选择父级权限\",\"clearable\":\"\"},model:{value:(_vm.ruleForm.parent_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"parent_id\", $$v)},expression:\"ruleForm.parent_id\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":24}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"排序\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"max\":999},model:{value:(_vm.ruleForm.sort),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"sort\", $$v)},expression:\"ruleForm.sort\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"状态\"}},[_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0,\"active-text\":\"启用\",\"inactive-text\":\"禁用\"},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"权限描述\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入权限描述\"},model:{value:(_vm.ruleForm.description),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"description\", $$v)},expression:\"ruleForm.description\"}})],1),(_vm.ruleForm.type === 'menu')?_c('el-form-item',{attrs:{\"label\":\"路由路径\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入路由路径\"},model:{value:(_vm.ruleForm.path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"path\", $$v)},expression:\"ruleForm.path\"}})],1):_vm._e(),(_vm.ruleForm.type === 'menu')?_c('el-form-item',{attrs:{\"label\":\"图标\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入图标类名\"},model:{value:(_vm.ruleForm.icon),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"icon\", $$v)},expression:\"ruleForm.icon\"}},[_c('template',{slot:\"prepend\"},[_c('i',{class:_vm.ruleForm.icon || 'el-icon-menu'})])],2)],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"title-section\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-key\"}),_vm._v(\" 权限管理 \")]),_c('p',{staticClass:\"page-subtitle\"},[_vm._v(\"管理系统功能权限和访问控制\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"permission-container\">\n    <!-- 页面标题区域 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-key\"></i>\n            权限管理\n          </h2>\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\n        </div>\n        <div class=\"header-actions\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            @click=\"editData(0)\"\n            class=\"add-btn\"\n          >\n            新增权限\n          </el-button>\n          <el-button\n            icon=\"el-icon-refresh\"\n            @click=\"refulsh\"\n            class=\"refresh-btn\"\n          >\n            刷新\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <el-card shadow=\"never\" class=\"search-card\">\n        <div class=\"search-form\">\n          <div class=\"search-row\">\n            <div class=\"search-item\">\n              <label class=\"search-label\">权限搜索</label>\n              <el-input\n                v-model=\"search.keyword\"\n                placeholder=\"请输入权限名称或描述\"\n                class=\"search-input\"\n                clearable\n                @keyup.enter.native=\"searchData\"\n              >\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\n              </el-input>\n            </div>\n            \n            <div class=\"search-item\">\n              <label class=\"search-label\">权限类型</label>\n              <el-select\n                v-model=\"search.type\"\n                placeholder=\"请选择权限类型\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </div>\n\n            <div class=\"search-item\">\n              <label class=\"search-label\">状态</label>\n              <el-select\n                v-model=\"search.status\"\n                placeholder=\"请选择状态\"\n                class=\"search-select\"\n                clearable\n              >\n                <el-option label=\"启用\" :value=\"1\"></el-option>\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\n              </el-select>\n            </div>\n          </div>\n\n          <div class=\"search-actions\">\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\n              搜索\n            </el-button>\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\n              重置\n            </el-button>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 权限树形结构 -->\n    <div class=\"tree-section\">\n      <el-card shadow=\"never\" class=\"tree-card\">\n        <div class=\"tree-header\">\n          <div class=\"tree-title\">\n            <i class=\"el-icon-menu\"></i>\n            权限树形结构\n          </div>\n          <div class=\"tree-tools\">\n            <el-button-group>\n              <el-button \n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \n                icon=\"el-icon-s-grid\"\n                @click=\"viewMode = 'tree'\"\n                size=\"small\"\n              >\n                树形视图\n              </el-button>\n              <el-button \n                :type=\"viewMode === 'table' ? 'primary' : ''\" \n                icon=\"el-icon-menu\"\n                @click=\"viewMode = 'table'\"\n                size=\"small\"\n              >\n                列表视图\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n\n        <!-- 树形视图 -->\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\n          <el-tree\n            :data=\"treeData\"\n            :props=\"treeProps\"\n            :default-expand-all=\"true\"\n            node-key=\"id\"\n            class=\"permission-tree\"\n          >\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\n              <div class=\"node-content\">\n                <div class=\"node-info\">\n                  <i :class=\"getNodeIcon(data.type)\"></i>\n                  <span class=\"node-label\">{{ data.label }}</span>\n                  <el-tag \n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \n                    size=\"mini\"\n                    class=\"node-status\"\n                  >\n                    {{ data.status === 1 ? '启用' : '禁用' }}\n                  </el-tag>\n                </div>\n                <div class=\"node-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click=\"editData(data.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click=\"addChild(data)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click=\"delData(data.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </span>\n          </el-tree>\n        </div>\n\n        <!-- 表格视图 -->\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\n          <el-table\n            :data=\"tableData\"\n            v-loading=\"loading\"\n            class=\"permission-table\"\n            stripe\n            row-key=\"id\"\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n            :default-expand-all=\"false\"\n          >\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\n              <template slot-scope=\"scope\">\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\n                </div>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\n            </el-table-column>\n\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-tag\n                  :type=\"getTypeColor(scope.row.type)\"\n                  size=\"small\"\n                >\n                  {{ getTypeLabel(scope.row.type) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-switch\n                  v-model=\"scope.row.status\"\n                  :active-value=\"1\"\n                  :inactive-value=\"0\"\n                  @change=\"changeStatus(scope.row)\"\n                >\n                </el-switch>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\n            </el-table-column>\n\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <div class=\"action-buttons\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"mini\"\n                    @click.stop=\"editData(scope.row.id)\"\n                    icon=\"el-icon-edit\"\n                    plain\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    type=\"success\"\n                    size=\"mini\"\n                    @click.stop=\"addChild(scope.row)\"\n                    icon=\"el-icon-plus\"\n                    plain\n                    v-if=\"scope.row.type === 'menu'\"\n                  >\n                    添加子权限\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"mini\"\n                    @click.stop=\"delData(scope.row.id)\"\n                    icon=\"el-icon-delete\"\n                    plain\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 编辑权限对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogFormVisible\"\n      :close-on-click-modal=\"false\"\n      width=\"60%\"\n    >\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限名称\" prop=\"label\">\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限代码\" prop=\"code\">\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"权限类型\" prop=\"type\">\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"父级权限\">\n              <el-cascader\n                v-model=\"ruleForm.parent_id\"\n                :options=\"parentOptions\"\n                :props=\"cascaderProps\"\n                placeholder=\"请选择父级权限\"\n                clearable\n                style=\"width: 100%\"\n              ></el-cascader>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"24\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"排序\">\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-switch\n                v-model=\"ruleForm.status\"\n                :active-value=\"1\"\n                :inactive-value=\"0\"\n                active-text=\"启用\"\n                inactive-text=\"禁用\"\n              >\n              </el-switch>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"权限描述\">\n          <el-input\n            v-model=\"ruleForm.description\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入权限描述\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\n            <template slot=\"prepend\">\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\n            </template>\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"PermissionManagement\",\n  data() {\n    return {\n      viewMode: 'tree', // tree | table\n      loading: false,\n      search: {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      },\n      treeData: [],\n      originalData: [], // 保存原始数据\n      dialogFormVisible: false,\n      dialogTitle: \"新增权限\",\n      parentOptions: [],\n      ruleForm: {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"menu\",\n        parent_id: null,\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      },\n      rules: {\n        label: [\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\n        ],\n        code: [\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\n        ]\n      },\n      treeProps: {\n        children: 'children',\n        label: 'label'\n      },\n      cascaderProps: {\n        value: 'id',\n        label: 'label',\n        children: 'children',\n        checkStrictly: true\n      }\n    };\n  },\n  computed: {\n    // 表格数据 - 将树形数据扁平化但保持层级关系\n    tableData() {\n      return this.flattenTreeForTable(this.treeData);\n    }\n  },\n  mounted() {\n    this.getData();\n  },\n  methods: {\n    // 获取权限数据\n    getData() {\n      this.loading = true;\n      \n      // 使用测试数据\n      setTimeout(() => {\n        this.loading = false;\n        \n        const mockData = [\n          {\n            id: 1,\n            label: \"系统管理\",\n            code: \"system\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 1,\n            status: 1,\n            description: \"系统管理模块\",\n            path: \"/system\",\n            icon: \"el-icon-setting\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 11,\n                label: \"用户管理\",\n                code: \"system:user\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 1,\n                status: 1,\n                description: \"用户管理功能\",\n                path: \"/user\",\n                icon: \"el-icon-user\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 111,\n                    label: \"查看用户\",\n                    code: \"system:user:view\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看用户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 112,\n                    label: \"新增用户\",\n                    code: \"system:user:add\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 113,\n                    label: \"编辑用户\",\n                    code: \"system:user:edit\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑用户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 114,\n                    label: \"删除用户\",\n                    code: \"system:user:delete\",\n                    type: \"action\",\n                    parent_id: 11,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除用户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 12,\n                label: \"职位管理\",\n                code: \"system:position\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 2,\n                status: 1,\n                description: \"职位管理功能\",\n                path: \"/zhiwei\",\n                icon: \"el-icon-postcard\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 121,\n                    label: \"查看职位\",\n                    code: \"system:position:view\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看职位列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 122,\n                    label: \"新增职位\",\n                    code: \"system:position:add\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 123,\n                    label: \"编辑职位\",\n                    code: \"system:position:edit\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑职位信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 124,\n                    label: \"删除职位\",\n                    code: \"system:position:delete\",\n                    type: \"action\",\n                    parent_id: 12,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除职位\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 13,\n                label: \"权限管理\",\n                code: \"system:permission\",\n                type: \"menu\",\n                parent_id: 1,\n                sort: 3,\n                status: 1,\n                description: \"权限管理功能\",\n                path: \"/quanxian\",\n                icon: \"el-icon-key\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 131,\n                    label: \"查看权限\",\n                    code: \"system:permission:view\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看权限列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 132,\n                    label: \"新增权限\",\n                    code: \"system:permission:add\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 133,\n                    label: \"编辑权限\",\n                    code: \"system:permission:edit\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑权限信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 134,\n                    label: \"删除权限\",\n                    code: \"system:permission:delete\",\n                    type: \"action\",\n                    parent_id: 13,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除权限\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 2,\n            label: \"业务管理\",\n            code: \"business\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 2,\n            status: 1,\n            description: \"业务管理模块\",\n            path: \"/business\",\n            icon: \"el-icon-suitcase\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 21,\n                label: \"订单管理\",\n                code: \"business:order\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 1,\n                status: 1,\n                description: \"订单管理功能\",\n                path: \"/dingdan\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 211,\n                    label: \"查看订单\",\n                    code: \"business:order:view\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看订单列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 212,\n                    label: \"新增订单\",\n                    code: \"business:order:add\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 213,\n                    label: \"编辑订单\",\n                    code: \"business:order:edit\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑订单信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 214,\n                    label: \"删除订单\",\n                    code: \"business:order:delete\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除订单\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 215,\n                    label: \"导出订单\",\n                    code: \"business:order:export\",\n                    type: \"action\",\n                    parent_id: 21,\n                    sort: 5,\n                    status: 1,\n                    description: \"导出订单数据\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 22,\n                label: \"客户管理\",\n                code: \"business:customer\",\n                type: \"menu\",\n                parent_id: 2,\n                sort: 2,\n                status: 1,\n                description: \"客户管理功能\",\n                path: \"/customer\",\n                icon: \"el-icon-user-solid\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 221,\n                    label: \"查看客户\",\n                    code: \"business:customer:view\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看客户列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 222,\n                    label: \"新增客户\",\n                    code: \"business:customer:add\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 223,\n                    label: \"编辑客户\",\n                    code: \"business:customer:edit\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑客户信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 224,\n                    label: \"删除客户\",\n                    code: \"business:customer:delete\",\n                    type: \"action\",\n                    parent_id: 22,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除客户\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 3,\n            label: \"文书管理\",\n            code: \"document\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 3,\n            status: 1,\n            description: \"文书管理模块\",\n            path: \"/document\",\n            icon: \"el-icon-document-copy\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 31,\n                label: \"合同管理\",\n                code: \"document:contract\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 1,\n                status: 1,\n                description: \"合同管理功能\",\n                path: \"/hetong\",\n                icon: \"el-icon-document\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 311,\n                    label: \"查看合同\",\n                    code: \"document:contract:view\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看合同列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 312,\n                    label: \"新增合同\",\n                    code: \"document:contract:add\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 313,\n                    label: \"编辑合同\",\n                    code: \"document:contract:edit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑合同信息\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 314,\n                    label: \"删除合同\",\n                    code: \"document:contract:delete\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 315,\n                    label: \"审核合同\",\n                    code: \"document:contract:audit\",\n                    type: \"action\",\n                    parent_id: 31,\n                    sort: 5,\n                    status: 1,\n                    description: \"审核合同\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 32,\n                label: \"律师函管理\",\n                code: \"document:lawyer\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 2,\n                status: 1,\n                description: \"律师函管理功能\",\n                path: \"/lawyer\",\n                icon: \"el-icon-message\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 321,\n                    label: \"查看律师函\",\n                    code: \"document:lawyer:view\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看律师函列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 322,\n                    label: \"发送律师函\",\n                    code: \"document:lawyer:send\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 2,\n                    status: 1,\n                    description: \"发送律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 323,\n                    label: \"编辑律师函\",\n                    code: \"document:lawyer:edit\",\n                    type: \"action\",\n                    parent_id: 32,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑律师函\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              },\n              {\n                id: 33,\n                label: \"课程管理\",\n                code: \"document:course\",\n                type: \"menu\",\n                parent_id: 3,\n                sort: 3,\n                status: 1,\n                description: \"课程管理功能\",\n                path: \"/kecheng\",\n                icon: \"el-icon-video-play\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 331,\n                    label: \"查看课程\",\n                    code: \"document:course:view\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看课程列表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 332,\n                    label: \"新增课程\",\n                    code: \"document:course:add\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 2,\n                    status: 1,\n                    description: \"新增课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 333,\n                    label: \"编辑课程\",\n                    code: \"document:course:edit\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 3,\n                    status: 1,\n                    description: \"编辑课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 334,\n                    label: \"删除课程\",\n                    code: \"document:course:delete\",\n                    type: \"action\",\n                    parent_id: 33,\n                    sort: 4,\n                    status: 1,\n                    description: \"删除课程\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            id: 4,\n            label: \"财务管理\",\n            code: \"finance\",\n            type: \"menu\",\n            parent_id: null,\n            sort: 4,\n            status: 1,\n            description: \"财务管理模块\",\n            path: \"/finance\",\n            icon: \"el-icon-coin\",\n            create_time: \"2024-01-01 10:00:00\",\n            children: [\n              {\n                id: 41,\n                label: \"支付管理\",\n                code: \"finance:payment\",\n                type: \"menu\",\n                parent_id: 4,\n                sort: 1,\n                status: 1,\n                description: \"支付管理功能\",\n                path: \"/order\",\n                icon: \"el-icon-money\",\n                create_time: \"2024-01-01 10:00:00\",\n                children: [\n                  {\n                    id: 411,\n                    label: \"查看支付记录\",\n                    code: \"finance:payment:view\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 1,\n                    status: 1,\n                    description: \"查看支付记录\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 412,\n                    label: \"处理退款\",\n                    code: \"finance:payment:refund\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 2,\n                    status: 1,\n                    description: \"处理退款申请\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  },\n                  {\n                    id: 413,\n                    label: \"导出财务报表\",\n                    code: \"finance:payment:export\",\n                    type: \"action\",\n                    parent_id: 41,\n                    sort: 3,\n                    status: 1,\n                    description: \"导出财务报表\",\n                    create_time: \"2024-01-01 10:00:00\"\n                  }\n                ]\n              }\n            ]\n          }\n        ];\n        \n        this.treeData = mockData;\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\n        this.updateParentOptions();\n      }, 500);\n    },\n\n    // 将树形数据扁平化用于表格显示（保持层级结构）\n    flattenTreeForTable(tree, level = 0, result = []) {\n      tree.forEach(node => {\n        const flatNode = {\n          ...node,\n          level: level,\n          hasChildren: node.children && node.children.length > 0\n        };\n        // 移除children属性避免表格渲染问题\n        delete flatNode.children;\n        result.push(flatNode);\n\n        // 递归处理子节点\n        if (node.children && node.children.length > 0) {\n          this.flattenTreeForTable(node.children, level + 1, result);\n        }\n      });\n      return result;\n    },\n\n    // 更新父级权限选项\n    updateParentOptions() {\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\n    },\n\n    // 构建级联选择器选项\n    buildCascaderOptions(tree) {\n      return tree.map(node => ({\n        id: node.id,\n        label: node.label,\n        children: node.children ? this.buildCascaderOptions(node.children) : []\n      }));\n    },\n\n    // 搜索数据\n    searchData() {\n      this.getData();\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.search = {\n        keyword: \"\",\n        type: \"\",\n        status: \"\"\n      };\n      this.searchData();\n    },\n\n    // 刷新页面\n    refulsh() {\n      this.$router.go(0);\n    },\n\n    // 编辑权限\n    editData(id) {\n      if (id === 0) {\n        this.dialogTitle = \"新增权限\";\n        this.ruleForm = {\n          id: null,\n          label: \"\",\n          code: \"\",\n          type: \"menu\",\n          parent_id: null,\n          sort: 0,\n          status: 1,\n          description: \"\",\n          path: \"\",\n          icon: \"\"\n        };\n      } else {\n        this.dialogTitle = \"编辑权限\";\n        const permission = this.findPermissionById(id);\n        if (permission) {\n          this.ruleForm = { ...permission };\n        }\n      }\n      this.dialogFormVisible = true;\n    },\n\n    // 添加子权限\n    addChild(parentData) {\n      this.dialogTitle = \"新增子权限\";\n      this.ruleForm = {\n        id: null,\n        label: \"\",\n        code: \"\",\n        type: \"action\",\n        parent_id: [parentData.id],\n        sort: 0,\n        status: 1,\n        description: \"\",\n        path: \"\",\n        icon: \"\"\n      };\n      this.dialogFormVisible = true;\n    },\n\n    // 根据ID查找权限（在原始数据中查找）\n    findPermissionById(id, tree = this.originalData) {\n      for (let node of tree) {\n        if (node.id === id) {\n          return node;\n        }\n        if (node.children) {\n          const found = this.findPermissionById(id, node.children);\n          if (found) return found;\n        }\n      }\n      return null;\n    },\n\n    // 删除权限\n    delData(id) {\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        // 模拟删除\n        this.$message.success(\"删除成功！\");\n        this.getData();\n      }).catch(() => {\n        this.$message.info(\"已取消删除\");\n      });\n    },\n\n    // 保存权限\n    saveData() {\n      this.$refs[\"ruleForm\"].validate((valid) => {\n        if (valid) {\n          // 模拟保存\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\n          this.dialogFormVisible = false;\n          this.getData();\n        }\n      });\n    },\n\n    // 改变状态\n    changeStatus(row) {\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\n    },\n\n    // 获取节点图标\n    getNodeIcon(type) {\n      const iconMap = {\n        menu: 'el-icon-menu',\n        action: 'el-icon-setting',\n        data: 'el-icon-document'\n      };\n      return iconMap[type] || 'el-icon-menu';\n    },\n\n    // 获取类型颜色\n    getTypeColor(type) {\n      const colorMap = {\n        menu: 'primary',\n        action: 'success',\n        data: 'warning'\n      };\n      return colorMap[type] || 'primary';\n    },\n\n    // 获取类型标签\n    getTypeLabel(type) {\n      const labelMap = {\n        menu: '菜单权限',\n        action: '操作权限',\n        data: '数据权限'\n      };\n      return labelMap[type] || '菜单权限';\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 权限管理容器 */\n.permission-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding: 24px;\n}\n\n/* 页面标题区域 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 24px 32px;\n  border-radius: 12px;\n  color: white;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.title-section h2.page-title {\n  margin: 0;\n  font-size: 28px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.title-section .page-subtitle {\n  margin: 8px 0 0 0;\n  opacity: 0.9;\n  font-size: 14px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.add-btn, .refresh-btn {\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.add-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.add-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.refresh-btn {\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 24px;\n}\n\n.search-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form {\n  padding: 8px;\n}\n\n.search-row {\n  display: flex;\n  gap: 24px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n}\n\n.search-item {\n  display: flex;\n  flex-direction: column;\n  min-width: 200px;\n}\n\n.search-label {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 8px;\n  font-weight: 500;\n}\n\n.search-input, .search-select {\n  width: 240px;\n}\n\n.search-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n/* 树形结构区域 */\n.tree-section {\n  margin-bottom: 24px;\n}\n\n.tree-card {\n  border-radius: 12px;\n  border: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.tree-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.tree-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #262626;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.tree-tools {\n  display: flex;\n  gap: 12px;\n}\n\n/* 树形视图样式 */\n.tree-view {\n  padding: 24px;\n}\n\n.permission-tree {\n  background: transparent;\n}\n\n.tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.node-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.node-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.node-label {\n  font-weight: 500;\n  color: #262626;\n}\n\n.node-status {\n  margin-left: 8px;\n}\n\n.node-actions {\n  display: flex;\n  gap: 4px;\n}\n\n/* 表格视图样式 */\n.table-view {\n  padding: 0 24px 24px;\n}\n\n.permission-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.permission-name-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.permission-name {\n  font-weight: 500;\n  color: #262626;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.action-buttons .el-button {\n  margin: 2px;\n}\n\n/* 表格行层级样式 */\n.permission-table .el-table__row[data-level=\"0\"] {\n  background-color: #fafafa;\n  font-weight: 600;\n}\n\n.permission-table .el-table__row[data-level=\"1\"] {\n  background-color: #f5f5f5;\n}\n\n.permission-table .el-table__row[data-level=\"2\"] {\n  background-color: #ffffff;\n}\n\n/* 对话框样式 */\n.dialog-footer {\n  text-align: right;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .permission-container {\n    padding: 16px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .search-row {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .search-item {\n    min-width: auto;\n  }\n\n  .search-input, .search-select {\n    width: 100%;\n  }\n\n  .tree-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n\n  .node-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .node-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./quanxian.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./quanxian.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./quanxian.vue?vue&type=template&id=7aacc5fe&scoped=true\"\nimport script from \"./quanxian.vue?vue&type=script&lang=js\"\nexport * from \"./quanxian.vue?vue&type=script&lang=js\"\nimport style0 from \"./quanxian.vue?vue&type=style&index=0&id=7aacc5fe&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7aacc5fe\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./quanxian.vue?vue&type=style&index=0&id=7aacc5fe&prod&scoped=true&lang=css\""], "sourceRoot": ""}