{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\Search.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\Search.vue", "mtime": 1748604247133}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZm9ybWF0RmlsZVNpemUgfSBmcm9tICdAL3V0aWxzL2ZpbGVVdGlscyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXJjaGl2ZVNlYXJjaCcsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNlYXJjaEZvcm06IHsKICAgICAgICBrZXl3b3JkOiAnJywKICAgICAgICBmaWxlVHlwZTogJycsCiAgICAgICAgY2F0ZWdvcnk6ICcnLAogICAgICAgIGRhdGVSYW5nZTogW10KICAgICAgfSwKICAgICAgdmlld01vZGU6ICdsaXN0JywKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRGaWxlOiBudWxsLAogICAgICAvLyDmqKHmi5/mlofku7bmlbDmja4KICAgICAgYWxsRmlsZXM6IFt7CiAgICAgICAgaWQ6IDEsCiAgICAgICAgZmlsZU5hbWU6ICLlkIjlkIzmqKHmnb8ucGRmIiwKICAgICAgICBmaWxlVHlwZTogInBkZiIsCiAgICAgICAgY2F0ZWdvcnk6ICLlkIjlkIzmlofku7YiLAogICAgICAgIHNpemU6IDEwMjQwMDAsCiAgICAgICAgdXBsb2FkVGltZTogIjIwMjQtMDEtMTUgMTA6MzA6MDAiCiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICBmaWxlTmFtZTogIuahiOS7tui1hOaWmS5kb2N4IiwKICAgICAgICBmaWxlVHlwZTogImRvY3giLAogICAgICAgIGNhdGVnb3J5OiAi5qGI5Lu25paH5LmmIiwKICAgICAgICBzaXplOiA1MTIwMDAsCiAgICAgICAgdXBsb2FkVGltZTogIjIwMjQtMDEtMTQgMTQ6MjA6MDAiCiAgICAgIH0sIHsKICAgICAgICBpZDogMywKICAgICAgICBmaWxlTmFtZTogIuWSqOivouiusOW9lS50eHQiLAogICAgICAgIGZpbGVUeXBlOiAidHh0IiwKICAgICAgICBjYXRlZ29yeTogIuWSqOivouiusOW9lSIsCiAgICAgICAgc2l6ZTogODE5MiwKICAgICAgICB1cGxvYWRUaW1lOiAiMjAyNC0wMS0xMyAxNjo0NTowMCIKICAgICAgfSwgewogICAgICAgIGlkOiA0LAogICAgICAgIGZpbGVOYW1lOiAi5rOV5b6L5oSP6KeB5LmmLnBkZiIsCiAgICAgICAgZmlsZVR5cGU6ICJwZGYiLAogICAgICAgIGNhdGVnb3J5OiAi5rOV5b6L5oSP6KeB5LmmIiwKICAgICAgICBzaXplOiAyMDQ4MDAwLAogICAgICAgIHVwbG9hZFRpbWU6ICIyMDI0LTAxLTEyIDA5OjE1OjAwIgogICAgICB9LCB7CiAgICAgICAgaWQ6IDUsCiAgICAgICAgZmlsZU5hbWU6ICLor4Hmja7muIXljZUueGxzeCIsCiAgICAgICAgZmlsZVR5cGU6ICJ4bHN4IiwKICAgICAgICBjYXRlZ29yeTogIuahiOS7tuaWh+S5piIsCiAgICAgICAgc2l6ZTogMjU2MDAwLAogICAgICAgIHVwbG9hZFRpbWU6ICIyMDI0LTAxLTExIDE1OjMwOjAwIgogICAgICB9LCB7CiAgICAgICAgaWQ6IDYsCiAgICAgICAgZmlsZU5hbWU6ICLlp5TmiZjljY/orq4ucGRmIiwKICAgICAgICBmaWxlVHlwZTogInBkZiIsCiAgICAgICAgY2F0ZWdvcnk6ICLlkIjlkIzmlofku7YiLAogICAgICAgIHNpemU6IDc2ODAwMCwKICAgICAgICB1cGxvYWRUaW1lOiAiMjAyNC0wMS0xMCAxMToyMDowMCIKICAgICAgfV0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgZmlsdGVyZWRGaWxlcygpIHsKICAgICAgbGV0IGZpbGVzID0gdGhpcy5hbGxGaWxlczsKCiAgICAgIC8vIOWFs+mUruivjeaQnOe0ogogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLmtleXdvcmQpIHsKICAgICAgICBjb25zdCBrZXl3b3JkID0gdGhpcy5zZWFyY2hGb3JtLmtleXdvcmQudG9Mb3dlckNhc2UoKTsKICAgICAgICBmaWxlcyA9IGZpbGVzLmZpbHRlcihmaWxlID0+IGZpbGUuZmlsZU5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSB8fCBmaWxlLmNhdGVnb3J5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoa2V5d29yZCkpOwogICAgICB9CgogICAgICAvLyDmlofku7bnsbvlnovnrZvpgIkKICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5maWxlVHlwZSkgewogICAgICAgIGZpbGVzID0gZmlsZXMuZmlsdGVyKGZpbGUgPT4gewogICAgICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5maWxlVHlwZSA9PT0gJ2RvYycpIHsKICAgICAgICAgICAgcmV0dXJuIFsnZG9jJywgJ2RvY3gnXS5pbmNsdWRlcyhmaWxlLmZpbGVUeXBlKTsKICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWFyY2hGb3JtLmZpbGVUeXBlID09PSAneGxzJykgewogICAgICAgICAgICByZXR1cm4gWyd4bHMnLCAneGxzeCddLmluY2x1ZGVzKGZpbGUuZmlsZVR5cGUpOwogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLnNlYXJjaEZvcm0uZmlsZVR5cGUgPT09ICdpbWFnZScpIHsKICAgICAgICAgICAgcmV0dXJuIFsnanBnJywgJ2pwZWcnLCAncG5nJywgJ2dpZicsICdibXAnXS5pbmNsdWRlcyhmaWxlLmZpbGVUeXBlKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHJldHVybiBmaWxlLmZpbGVUeXBlID09PSB0aGlzLnNlYXJjaEZvcm0uZmlsZVR5cGU7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIOWIhuexu+etm+mAiQogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLmNhdGVnb3J5KSB7CiAgICAgICAgZmlsZXMgPSBmaWxlcy5maWx0ZXIoZmlsZSA9PiBmaWxlLmNhdGVnb3J5ID09PSB0aGlzLnNlYXJjaEZvcm0uY2F0ZWdvcnkpOwogICAgICB9CgogICAgICAvLyDml7bpl7TojIPlm7TnrZvpgIkKICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5kYXRlUmFuZ2UgJiYgdGhpcy5zZWFyY2hGb3JtLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICBjb25zdCBbc3RhcnREYXRlLCBlbmREYXRlXSA9IHRoaXMuc2VhcmNoRm9ybS5kYXRlUmFuZ2U7CiAgICAgICAgZmlsZXMgPSBmaWxlcy5maWx0ZXIoZmlsZSA9PiB7CiAgICAgICAgICBjb25zdCBmaWxlRGF0ZSA9IGZpbGUudXBsb2FkVGltZS5zcGxpdCgnICcpWzBdOwogICAgICAgICAgcmV0dXJuIGZpbGVEYXRlID49IHN0YXJ0RGF0ZSAmJiBmaWxlRGF0ZSA8PSBlbmREYXRlOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIHJldHVybiBmaWxlczsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGZvcm1hdEZpbGVTaXplLAogICAgZ2V0RmlsZUljb24oZmlsZVR5cGUpIHsKICAgICAgY29uc3QgaWNvbk1hcCA9IHsKICAgICAgICAncGRmJzogJ2VsLWljb24tZG9jdW1lbnQnLAogICAgICAgICdkb2MnOiAnZWwtaWNvbi1kb2N1bWVudCcsCiAgICAgICAgJ2RvY3gnOiAnZWwtaWNvbi1kb2N1bWVudCcsCiAgICAgICAgJ3hscyc6ICdlbC1pY29uLXMtZ3JpZCcsCiAgICAgICAgJ3hsc3gnOiAnZWwtaWNvbi1zLWdyaWQnLAogICAgICAgICd0eHQnOiAnZWwtaWNvbi1kb2N1bWVudC1jb3B5JywKICAgICAgICAnanBnJzogJ2VsLWljb24tcGljdHVyZScsCiAgICAgICAgJ2pwZWcnOiAnZWwtaWNvbi1waWN0dXJlJywKICAgICAgICAncG5nJzogJ2VsLWljb24tcGljdHVyZScsCiAgICAgICAgJ2dpZic6ICdlbC1pY29uLXBpY3R1cmUnCiAgICAgIH07CiAgICAgIHJldHVybiBpY29uTWFwW2ZpbGVUeXBlXSB8fCAnZWwtaWNvbi1kb2N1bWVudCc7CiAgICB9LAogICAgaGFuZGxlU2VhcmNoKCkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmkJzntKLlrozmiJAnKTsKICAgIH0sCiAgICBoYW5kbGVSZXNldCgpIHsKICAgICAgdGhpcy5zZWFyY2hGb3JtID0gewogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIGZpbGVUeXBlOiAnJywKICAgICAgICBjYXRlZ29yeTogJycsCiAgICAgICAgZGF0ZVJhbmdlOiBbXQogICAgICB9OwogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmkJzntKLmnaHku7blt7Lph43nva4nKTsKICAgIH0sCiAgICBoYW5kbGVQcmV2aWV3KGZpbGUpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKGDpooTop4jmlofku7Y6ICR7ZmlsZS5maWxlTmFtZX1gKTsKICAgICAgLy8g6L+Z6YeM5Y+v5Lul5a6e546w5paH5Lu26aKE6KeI5Yqf6IO9CiAgICB9LAogICAgaGFuZGxlRG93bmxvYWQoZmlsZSkgewogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW8gOWni+S4i+i9vTogJHtmaWxlLmZpbGVOYW1lfWApOwogICAgICAvLyDov5nph4zlj6/ku6Xlrp7njrDmlofku7bkuIvovb3lip/og70KICAgIH0sCiAgICBoYW5kbGVWaWV3RGV0YWlscyhmaWxlKSB7CiAgICAgIHRoaXMuY3VycmVudEZpbGUgPSBmaWxlOwogICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZVNpemVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZVNpemUgPSB2YWw7CiAgICB9LAogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IHZhbDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["formatFileSize", "name", "data", "searchForm", "keyword", "fileType", "category", "date<PERSON><PERSON><PERSON>", "viewMode", "currentPage", "pageSize", "detailDialogVisible", "currentFile", "allFiles", "id", "fileName", "size", "uploadTime", "computed", "filteredFiles", "files", "toLowerCase", "filter", "file", "includes", "length", "startDate", "endDate", "fileDate", "split", "methods", "getFileIcon", "iconMap", "handleSearch", "$message", "success", "handleReset", "info", "handlePreview", "handleDownload", "handleViewDetails", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/pages/archive/Search.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-search-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"关键词\">\r\n          <el-input\r\n            v-model=\"searchForm.keyword\"\r\n            placeholder=\"请输入文件名或内容关键词\"\r\n            style=\"width: 200px\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\">\r\n          <el-select v-model=\"searchForm.fileType\" placeholder=\"请选择文件类型\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"PDF文档\" value=\"pdf\" />\r\n            <el-option label=\"Word文档\" value=\"doc\" />\r\n            <el-option label=\"Excel表格\" value=\"xls\" />\r\n            <el-option label=\"图片\" value=\"image\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"分类\">\r\n          <el-select v-model=\"searchForm.category\" placeholder=\"请选择分类\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"案件文书\" value=\"案件文书\" />\r\n            <el-option label=\"合同文件\" value=\"合同文件\" />\r\n            <el-option label=\"咨询记录\" value=\"咨询记录\" />\r\n            <el-option label=\"法律意见书\" value=\"法律意见书\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"时间范围\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">\r\n            <i class=\"el-icon-search\"></i> 搜索\r\n          </el-button>\r\n          <el-button @click=\"handleReset\">\r\n            <i class=\"el-icon-refresh\"></i> 重置\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 搜索结果 -->\r\n    <div class=\"search-results\">\r\n      <div class=\"result-header\">\r\n        <span class=\"result-count\">共找到 {{ filteredFiles.length }} 个文件</span>\r\n        <div class=\"view-mode\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"list\">列表视图</el-radio-button>\r\n            <el-radio-button label=\"grid\">网格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 列表视图 -->\r\n      <div v-if=\"viewMode === 'list'\" class=\"list-view\">\r\n        <el-table :data=\"filteredFiles\" style=\"width: 100%\">\r\n          <el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"getFileIcon(scope.row.fileType)\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.fileName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"category\" label=\"分类\" width=\"120\" />\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatFileSize(scope.row.size) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"160\" />\r\n          <el-table-column label=\"操作\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" @click=\"handlePreview(scope.row)\">预览</el-button>\r\n              <el-button size=\"mini\" type=\"success\" @click=\"handleDownload(scope.row)\">下载</el-button>\r\n              <el-button size=\"mini\" type=\"info\" @click=\"handleViewDetails(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"grid-view\">\r\n        <div class=\"file-grid\">\r\n          <div\r\n            v-for=\"file in filteredFiles\"\r\n            :key=\"file.id\"\r\n            class=\"file-card\"\r\n            @click=\"handlePreview(file)\"\r\n          >\r\n            <div class=\"file-thumbnail\">\r\n              <i :class=\"getFileIcon(file.fileType)\" class=\"file-icon-large\"></i>\r\n            </div>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\" :title=\"file.fileName\">{{ file.fileName }}</div>\r\n              <div class=\"file-meta\">\r\n                <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                <span class=\"file-date\">{{ file.uploadTime.split(' ')[0] }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"filteredFiles.length\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文件详情对话框 -->\r\n    <el-dialog title=\"文件详情\" :visible.sync=\"detailDialogVisible\" width=\"600px\">\r\n      <div v-if=\"currentFile\" class=\"file-details\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"文件名\">{{ currentFile.fileName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件类型\">{{ currentFile.fileType.toUpperCase() }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件大小\">{{ formatFileSize(currentFile.size) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"分类\">{{ currentFile.category }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"上传时间\">{{ currentFile.uploadTime }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件路径\">{{ currentFile.filePath || '/archive/files/' + currentFile.fileName }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownload(currentFile)\">下载文件</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { formatFileSize } from '@/utils/fileUtils'\r\n\r\nexport default {\r\n  name: 'ArchiveSearch',\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      },\r\n      viewMode: 'list',\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      detailDialogVisible: false,\r\n      currentFile: null,\r\n      // 模拟文件数据\r\n      allFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: \"合同模板.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 1024000,\r\n          uploadTime: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: \"案件资料.docx\",\r\n          fileType: \"docx\",\r\n          category: \"案件文书\",\r\n          size: 512000,\r\n          uploadTime: \"2024-01-14 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: \"咨询记录.txt\",\r\n          fileType: \"txt\",\r\n          category: \"咨询记录\",\r\n          size: 8192,\r\n          uploadTime: \"2024-01-13 16:45:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: \"法律意见书.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"法律意见书\",\r\n          size: 2048000,\r\n          uploadTime: \"2024-01-12 09:15:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: \"证据清单.xlsx\",\r\n          fileType: \"xlsx\",\r\n          category: \"案件文书\",\r\n          size: 256000,\r\n          uploadTime: \"2024-01-11 15:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: \"委托协议.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 768000,\r\n          uploadTime: \"2024-01-10 11:20:00\"\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredFiles() {\r\n      let files = this.allFiles\r\n\r\n      // 关键词搜索\r\n      if (this.searchForm.keyword) {\r\n        const keyword = this.searchForm.keyword.toLowerCase()\r\n        files = files.filter(file => \r\n          file.fileName.toLowerCase().includes(keyword) ||\r\n          file.category.toLowerCase().includes(keyword)\r\n        )\r\n      }\r\n\r\n      // 文件类型筛选\r\n      if (this.searchForm.fileType) {\r\n        files = files.filter(file => {\r\n          if (this.searchForm.fileType === 'doc') {\r\n            return ['doc', 'docx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'xls') {\r\n            return ['xls', 'xlsx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'image') {\r\n            return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(file.fileType)\r\n          } else {\r\n            return file.fileType === this.searchForm.fileType\r\n          }\r\n        })\r\n      }\r\n\r\n      // 分类筛选\r\n      if (this.searchForm.category) {\r\n        files = files.filter(file => file.category === this.searchForm.category)\r\n      }\r\n\r\n      // 时间范围筛选\r\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\r\n        const [startDate, endDate] = this.searchForm.dateRange\r\n        files = files.filter(file => {\r\n          const fileDate = file.uploadTime.split(' ')[0]\r\n          return fileDate >= startDate && fileDate <= endDate\r\n        })\r\n      }\r\n\r\n      return files\r\n    }\r\n  },\r\n  methods: {\r\n    formatFileSize,\r\n    \r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'txt': 'el-icon-document-copy',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'gif': 'el-icon-picture'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n\r\n    handleSearch() {\r\n      this.currentPage = 1\r\n      this.$message.success('搜索完成')\r\n    },\r\n\r\n    handleReset() {\r\n      this.searchForm = {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      }\r\n      this.currentPage = 1\r\n      this.$message.info('搜索条件已重置')\r\n    },\r\n\r\n    handlePreview(file) {\r\n      this.$message.info(`预览文件: ${file.fileName}`)\r\n      // 这里可以实现文件预览功能\r\n    },\r\n\r\n    handleDownload(file) {\r\n      this.$message.success(`开始下载: ${file.fileName}`)\r\n      // 这里可以实现文件下载功能\r\n    },\r\n\r\n    handleViewDetails(file) {\r\n      this.currentFile = file\r\n      this.detailDialogVisible = true\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.pageSize = val\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.archive-search-container {\r\n  padding: 20px;\r\n}\r\n\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-results {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.result-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 网格视图样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.file-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-card:hover {\r\n  border-color: #409EFF;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.file-thumbnail {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-icon-large {\r\n  font-size: 48px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-card .file-name {\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.file-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n.file-details {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .result-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .file-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 15px;\r\n  }\r\n}\r\n</style> "], "mappings": "AAwJA,SAAAA,cAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,QAAA;MACAC,mBAAA;MACAC,WAAA;MACA;MACAC,QAAA,GACA;QACAC,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAV,QAAA;QACAC,QAAA;QACAU,IAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,cAAA;MACA,IAAAC,KAAA,QAAAP,QAAA;;MAEA;MACA,SAAAV,UAAA,CAAAC,OAAA;QACA,MAAAA,OAAA,QAAAD,UAAA,CAAAC,OAAA,CAAAiB,WAAA;QACAD,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAR,QAAA,CAAAM,WAAA,GAAAG,QAAA,CAAApB,OAAA,KACAmB,IAAA,CAAAjB,QAAA,CAAAe,WAAA,GAAAG,QAAA,CAAApB,OAAA,CACA;MACA;;MAEA;MACA,SAAAD,UAAA,CAAAE,QAAA;QACAe,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA;UACA,SAAApB,UAAA,CAAAE,QAAA;YACA,uBAAAmB,QAAA,CAAAD,IAAA,CAAAlB,QAAA;UACA,gBAAAF,UAAA,CAAAE,QAAA;YACA,uBAAAmB,QAAA,CAAAD,IAAA,CAAAlB,QAAA;UACA,gBAAAF,UAAA,CAAAE,QAAA;YACA,4CAAAmB,QAAA,CAAAD,IAAA,CAAAlB,QAAA;UACA;YACA,OAAAkB,IAAA,CAAAlB,QAAA,UAAAF,UAAA,CAAAE,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAF,UAAA,CAAAG,QAAA;QACAc,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAjB,QAAA,UAAAH,UAAA,CAAAG,QAAA;MACA;;MAEA;MACA,SAAAH,UAAA,CAAAI,SAAA,SAAAJ,UAAA,CAAAI,SAAA,CAAAkB,MAAA;QACA,OAAAC,SAAA,EAAAC,OAAA,SAAAxB,UAAA,CAAAI,SAAA;QACAa,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA;UACA,MAAAK,QAAA,GAAAL,IAAA,CAAAN,UAAA,CAAAY,KAAA;UACA,OAAAD,QAAA,IAAAF,SAAA,IAAAE,QAAA,IAAAD,OAAA;QACA;MACA;MAEA,OAAAP,KAAA;IACA;EACA;EACAU,OAAA;IACA9B,cAAA;IAEA+B,YAAA1B,QAAA;MACA,MAAA2B,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA3B,QAAA;IACA;IAEA4B,aAAA;MACA,KAAAxB,WAAA;MACA,KAAAyB,QAAA,CAAAC,OAAA;IACA;IAEAC,YAAA;MACA,KAAAjC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA,KAAAE,WAAA;MACA,KAAAyB,QAAA,CAAAG,IAAA;IACA;IAEAC,cAAAf,IAAA;MACA,KAAAW,QAAA,CAAAG,IAAA,UAAAd,IAAA,CAAAR,QAAA;MACA;IACA;IAEAwB,eAAAhB,IAAA;MACA,KAAAW,QAAA,CAAAC,OAAA,UAAAZ,IAAA,CAAAR,QAAA;MACA;IACA;IAEAyB,kBAAAjB,IAAA;MACA,KAAAX,WAAA,GAAAW,IAAA;MACA,KAAAZ,mBAAA;IACA;IAEA8B,iBAAAC,GAAA;MACA,KAAAhC,QAAA,GAAAgC,GAAA;IACA;IAEAC,oBAAAD,GAAA;MACA,KAAAjC,WAAA,GAAAiC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}