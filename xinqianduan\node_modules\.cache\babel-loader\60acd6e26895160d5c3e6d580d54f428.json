{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\src\\main.js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\main.js", "mtime": 1748425644022}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnLi9yb3V0ZXInOwppbXBvcnQgc3RvcmUgZnJvbSAnLi9zdG9yZSc7CmltcG9ydCBFbGVtZW50VUkgZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCAnZWxlbWVudC11aS9saWIvdGhlbWUtY2hhbGsvaW5kZXguY3NzJzsKaW1wb3J0ICcuL2Fzc2V0cy9jc3MvY29tbW9uLXVpLmNzcyc7CmltcG9ydCB7IHBvc3RSZXF1ZXN0IH0gZnJvbSAiLi91dGlscy9hcGkiOwppbXBvcnQgeyBwb3N0S2V5VmFsdWVSZXF1ZXN0IH0gZnJvbSAiLi91dGlscy9hcGkiOwppbXBvcnQgeyBwdXRSZXF1ZXN0IH0gZnJvbSAiLi91dGlscy9hcGkiOwppbXBvcnQgeyBkZWxldGVSZXF1ZXN0IH0gZnJvbSAiLi91dGlscy9hcGkiOwppbXBvcnQgeyBnZXRSZXF1ZXN0IH0gZnJvbSAiLi91dGlscy9hcGkiOwpWdWUucHJvdG90eXBlLnBvc3RSZXF1ZXN0ID0gcG9zdFJlcXVlc3Q7ClZ1ZS5wcm90b3R5cGUucG9zdEtleVZhbHVlUmVxdWVzdCA9IHBvc3RLZXlWYWx1ZVJlcXVlc3Q7ClZ1ZS5wcm90b3R5cGUucHV0UmVxdWVzdCA9IHB1dFJlcXVlc3Q7ClZ1ZS5wcm90b3R5cGUuZGVsZXRlUmVxdWVzdCA9IGRlbGV0ZVJlcXVlc3Q7ClZ1ZS5wcm90b3R5cGUuZ2V0UmVxdWVzdCA9IGdldFJlcXVlc3Q7ClZ1ZS5jb25maWcucHJvZHVjdGlvblRpcCA9IGZhbHNlOwpWdWUudXNlKEVsZW1lbnRVSSk7Cm5ldyBWdWUoewogIHJvdXRlciwKICBzdG9yZSwKICByZW5kZXI6IGggPT4gaChBcHApCn0pLiRtb3VudCgnI2FwcCcpOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "postRequest", "postKeyValueRequest", "putRequest", "deleteRequest", "getRequest", "prototype", "config", "productionTip", "use", "render", "h", "$mount"], "sources": ["D:/Gitee/xinqianduan/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport ElementUI from 'element-ui';\r\nimport 'element-ui/lib/theme-chalk/index.css';\r\nimport './assets/css/common-ui.css';\r\nimport {postRequest} from \"./utils/api\";\r\nimport {postKeyValueRequest} from \"./utils/api\";\r\nimport {putRequest} from \"./utils/api\";\r\nimport {deleteRequest} from \"./utils/api\";\r\nimport {getRequest} from \"./utils/api\";\r\nVue.prototype.postRequest = postRequest;\r\nVue.prototype.postKeyValueRequest = postKeyValueRequest;\r\nVue.prototype.putRequest = putRequest;\r\nVue.prototype.deleteRequest = deleteRequest;\r\nVue.prototype.getRequest = getRequest;\r\nVue.config.productionTip = false\r\n\r\nVue.use(ElementUI);\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAO,4BAA4B;AACnC,SAAQC,WAAW,QAAO,aAAa;AACvC,SAAQC,mBAAmB,QAAO,aAAa;AAC/C,SAAQC,UAAU,QAAO,aAAa;AACtC,SAAQC,aAAa,QAAO,aAAa;AACzC,SAAQC,UAAU,QAAO,aAAa;AACtCT,GAAG,CAACU,SAAS,CAACL,WAAW,GAAGA,WAAW;AACvCL,GAAG,CAACU,SAAS,CAACJ,mBAAmB,GAAGA,mBAAmB;AACvDN,GAAG,CAACU,SAAS,CAACH,UAAU,GAAGA,UAAU;AACrCP,GAAG,CAACU,SAAS,CAACF,aAAa,GAAGA,aAAa;AAC3CR,GAAG,CAACU,SAAS,CAACD,UAAU,GAAGA,UAAU;AACrCT,GAAG,CAACW,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCZ,GAAG,CAACa,GAAG,CAACT,SAAS,CAAC;AAClB,IAAIJ,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLW,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACd,GAAG;AACpB,CAAC,CAAC,CAACe,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}