'use strict';

const Bench = require('bench');
const Address = require('../');
const Isemail = require('isemail');


const tests = [
    ['', false],
    ['\r', false],
    ['test', false],
    ['@', false],
    ['test@', false],
    ['test@io', false],
    ['test@io', true, { minDomainSegments: 1 }],
    ['@io', false],
    ['@iana.org', false],
    ['<EMAIL>', true],
    ['<EMAIL>', true],
    ['<EMAIL>', true],
    ['<EMAIL>', true],
    ['ê**************', true],
    ['ñoñó*************', true],
    ['ñoñó******************', true],
    ['伊昭傑@郵件.商務', true],
    ['\ud801\udc37\ud852\<EMAIL>', true],
    ['<EMAIL>', true],
    ['.<EMAIL>', false],
    ['<EMAIL>', false],
    ['test..iana.org', false],
    ['test_exa-mple.com', false],
    ['!#$%&`*+/=?^`{|}~@iana.org', true],
    ['test\\@<EMAIL>', false],
    ['<EMAIL>', true],
    ['<EMAIL>', true],
    ['test@iana.123', false],
    ['test@***************', false],
    ['<EMAIL>', true],
    ['<EMAIL>', false],
    ['\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\<EMAIL>', false],
    ['test@abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghiklm', false],
    ['test@\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06.org', true],
    ['test@abcdefghijklmnopqrstuvwxyzabcdefghijklmno\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06.org', false],
    ['<EMAIL>', false],
    ['<EMAIL>', true],
    ['<EMAIL>', false],
    ['<EMAIL>', false],
    ['<EMAIL>', false],
    ['<EMAIL>.', false],
    ['<EMAIL>', false],
    ['<EMAIL>', false],
    ['<EMAIL>.\ud83d\ude06\ud83d\ude06\ud83d\ude06\ud83d\ude06', false],
    ['<EMAIL>', false],
    ['<EMAIL>\ud83d\ude06', false],
    ['<EMAIL>\ud83d\ude06', false],
    ['<EMAIL>', false],
    ['<EMAIL>.\ud83d\ude06', false],
    ['\"\r', false],
    ['\"test\"@iana.org', false],
    ['\"\"@iana.org', false],
    ['\"\"\"@iana.org', false],
    ['\"\\a\"@iana.org', false],
    ['\"\\\"\"@iana.org', false],
    ['\"\\\"@iana.org', false],
    ['\"\\\\\"@iana.org', false],
    ['test\"@iana.org', false],
    ['\"<EMAIL>', false],
    ['\"test\"<EMAIL>', false],
    ['test\"text\"@iana.org', false],
    ['\"test\"\"test\"@iana.org', false],
    ['\"test\".\"test\"@iana.org', false],
    ['\"test\\ test\"@iana.org', false],
    ['\"test\".<EMAIL>', false],
    ['\"test\u0000\"@iana.org', false],
    ['\"test\\\u0000\"@iana.org', false],
    ['\"test\r\n test\"@iana.org', false],
    ['\"abcdefghijklmnopqrstuvwxyz abcdefghijklmnopqrstuvwxyz abcdefghj\"@iana.org', false],
    ['\"abcdefghijklmnopqrstuvwxyz abcdefghijklmnopqrstuvwxyz abcdefg\\h\"@iana.org', false],
    ['test@[***************]', false],
    ['test@a[***************]', false],
    ['test@[255.255.255]', false],
    ['test@[***************.255]', false],
    ['test@[255.255.255.256]', false],
    ['test@[1111:2222:3333:4444:5555:6666:7777:8888]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888:9999]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:888G]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666::8888]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555::8888]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666::7777:8888]', false],
    ['test@[IPv6::3333:4444:5555:6666:7777:8888]', false],
    ['test@[IPv6:::3333:4444:5555:6666:7777:8888]', false],
    ['test@[IPv6:1111::4444:5555::8888]', false],
    ['test@[IPv6:::]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:***************]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:***************]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666:7777:***************]', false],
    ['test@[IPv6:1111:2222:3333:4444::***************]', false],
    ['test@[IPv6:1111:2222:3333:4444:5555:6666::***************]', false],
    ['test@[IPv6:1111:2222:3333:4444:::***************]', false],
    ['test@[IPv6::***************]', false],
    ['test@[***************].local', false],
    ['test@local.[***************]', false],
    ['test@local.[***************].local', false],
    ['test@local.(comment)[***************].local', false],
    ['test@local. [***************].local', false],
    ['test@local.[***************](comment).local', false],
    ['test@local.[***************] .local', false],
    [' test @iana.org', false],
    ['test@ iana .com', false],
    ['test . <EMAIL>', false],
    ['\r\n <EMAIL>', false],
    ['\r\n \r\n <EMAIL>', false],
    ['(\r', false],
    ['(comment)<EMAIL>', false],
    ['((comment)<EMAIL>', false],
    ['(comment(comment))<EMAIL>', false],
    ['test@(comment)iana.org', false],
    ['test(comment)@iana.org', false],
    ['test(comment)<EMAIL>', false],
    ['test@(comment)[***************]', false],
    ['(comment)<EMAIL>', false],
    ['test@(comment)abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefg.com', false],
    ['(comment)<EMAIL>', false],
    ['<EMAIL>\n', false],
    ['<EMAIL>', true],
    ['<EMAIL>-', false],
    ['\"<EMAIL>', false],
    ['(<EMAIL>', false],
    ['test@(iana.org', false],
    ['test@[1.2.3.4', false],
    ['\"test\\\"@iana.org', false],
    ['(comment\\)<EMAIL>', false],
    ['<EMAIL>(comment\\)', false],
    ['<EMAIL>(comment\\', false],
    ['test@[RFC-5322-domain-literal]', false],
    ['test@[RFC-5322-郵件ñó-domain-literal]', false],
    ['test@[RFC-5322]-domain-literal]', false],
    ['test@[RFC-5322].domain-literal]', false],
    ['test@[RFC-5322-[domain-literal]', false],
    ['test@[', false],
    ['test@[\u0007]', false],
    ['test@[RFC-5322-\\\u0007-domain-literal]', false],
    ['test@[RFC-5322-\\\t-domain-literal]', false],
    ['test@[RFC-5322-\\]-domain-literal]', false],
    ['test@[RFC-5322-\\郵-no-domain-literal]', false],
    ['test@[RFC-5322--domain-literal]', false],
    ['test@[RFC-5322-domain-literal\\]', false],
    ['test@[RFC-5322-domain-literal\\', false],
    ['test@[RFC 5322 domain literal]', false],
    ['test@[RFC-5322-domain-literal] (comment)', false],
    ['@iana.org', false],
    ['test@.org', false],
    ['\"\"@iana.org', false],
    ['\"\"@iana.org', false],
    ['\"\\\"@iana.org', false],
    ['()<EMAIL>', false],
    ['()<EMAIL>', false],
    ['<EMAIL>\r', false],
    ['\<EMAIL>', false],
    ['\"\rtest\"@iana.org', false],
    ['(\r)<EMAIL>', false],
    ['<EMAIL>(\r)', false],
    ['test@<iana>.org', false],
    ['\<EMAIL>', false],
    ['\"\n\"@iana.org', false],
    ['\"\\\n\"@iana.org', false],
    ['(\n)<EMAIL>', false],
    ['\<EMAIL>', false],
    ['test@\u0007.org', false],
    ['\"\u0007\"@iana.org', false],
    ['\"\\\u0007\"@iana.org', false],
    ['(\u0007)<EMAIL>', false],
    ['\r\<EMAIL>', false],
    ['\r\n \r\<EMAIL>', false],
    [' \r\<EMAIL>', false],
    [' \r\n <EMAIL>', false],
    [' \r\n \r\<EMAIL>', false],
    [' \r\n\r\<EMAIL>', false],
    [' \r\n\r\n <EMAIL>', false],
    ['<EMAIL>\r\n ', false],
    ['<EMAIL>\r\n \r\n ', false],
    ['<EMAIL>\r\n', false],
    ['<EMAIL> \r', false],
    ['<EMAIL>\r\n \r\n', false],
    ['<EMAIL> \r\n', false],
    ['<EMAIL> \r\n ', false],
    ['<EMAIL> \r\n \r\n', false],
    ['<EMAIL> \r\n\r\n', false],
    ['<EMAIL> \r\n\r\n ', false],
    ['test@iana. org', false],
    ['test@[\r', false],
    ['test@[\r\n', false],
    [' <EMAIL>', false],
    ['<EMAIL> ', false],
    ['test@[IPv6:1::2:]', false],
    ['\"test\\\u0094\"@iana.org', false],
    ['test@iana/icann.org', false],
    ['test@iana!icann.org', false],
    ['test@iana?icann.org', false],
    ['test@iana^icann.org', false],
    ['test@iana{icann}.org', false],
    ['test.(comment)<EMAIL>', false],
    ['test@iana.(comment)org', false],
    ['test@iana(comment)iana.org', false],
    ['(comment\r\n comment)<EMAIL>', false],
    ['test@org', true, { minDomainSegments: 1 }],
    ['test\ud800@invalid', false],
    ['\"\ud800\"@invalid', false],
    ['\"\\\ud800\"@invalid', false],
    ['(\ud800)thing@invalid', false],
    ['\"\\\ud800\"@invalid', false],
    ['test@\ud800\udfffñoñó郵件ñoñó郵件.郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.noñó郵件.商務', true],
    ['test@\ud800\udfffñoñó郵件ñoñó郵件.郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.noñó郵件ñoñó郵.商務', false],
    ['test@\ud800\udfffñoñó郵件ñoñó郵件.郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.ñoñó郵件ñoñó郵件.oñó郵件ñoñó郵件ñoñó郵件.商務', false],
    ['test@ñoñoñó郵件\ud83d\ude06ñoñ.oñó郵件\uc138ñoñ.oñó郵件\u0644\u4eec\u010dñoñoñó郵件\u05dcño.ñoñó郵件\u092f\u672cñoñoñó郵件\uc138añoñ.oñó郵件\ud83d\ude06bc\uc138郵\ud83d\ude06ño.ñoñó郵件ñoñoñó郵件\ud83d\ude06ñoñoñó郵件\uc138ñoñ.oñó郵件\u0644\u4eecñoñoñó.郵件\ud83d\ude06ñoñoñó郵件郵\uc138ñoñoñó郵件\u0644\u4eecñoñoñó郵件.\ud83d\ude06ñoñoñó郵件郵\uc138\u0644\u4eec.郵件\ud83d\ude06ñoñoñó郵.件郵\uc138\u4eec\ud83d\ude06ñoñoñó件郵\uc138ñoñoñó郵件', false],
    ['test@ñoñó郵件ñoñó郵件ñoñó郵件ñoñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件ñoñó郵件.商務', false],
    ['\ud83d\ude06ñoñó郵件ñoñó郵件ñoñó\ud83d\ude06郵件ñoñoñó郵@\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.郵件ñoñó郵件ñoñó\ud83d\ude06.郵件ñoñó郵件ñoñó.郵件ñoñó郵件.ñoñó郵件ñoñó.郵件ñoñó郵件.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06郵件ñoñó郵件ñoñó.\ud83d\ude06商務.郵件ñoñó郵件ñoñó郵件.\ud83d\ude06商務.\ud83d\ude06商務.\ud83d\ude06商務', false]
];

exports.compare = {
    address: function () {

        for (const test of tests) {
            Address.email.isValid(test[0]);
        }
    },
    isemail: function () {

        for (const test of tests) {
            Isemail.validate(test[0]);
        }
    }
};


Bench.runMain();
