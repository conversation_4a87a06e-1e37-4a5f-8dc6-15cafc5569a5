{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\Login.vue", "mtime": 1748279302926}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJMb2dpbiIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdmNVcmw6ICJkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNakF3SWlCb1pXbG5hSFE5SWpRd0lpQjRiV3h1Y3owaWFIUjBjRG92TDNkM2R5NTNNeTV2Y21jdk1qQXdNQzl6ZG1jaVBqeDBaWGgwSUhnOUlqRXdJaUI1UFNJeU5TSWdabTl1ZEMxemFYcGxQU0l4TmlJZ1ptbHNiRDBpSXpNek16TXpNeUkrUkVWTlQwTlBSRVU4TDNSbGVIUStQQzl6ZG1jKyIsIC8vIOa8lOekuumqjOivgeeggQ0KICAgICAgbG9naW5Gb3JtOiB7DQogICAgICAgIHVzZXJuYW1lOiAiYWRtaW4iLA0KICAgICAgICBwYXNzd29yZDogIjEyMzQ1NiIsDQogICAgICAgIGNvZGU6ICJERU1PQ09ERSIsDQogICAgICB9LA0KICAgICAgY2hlY2tlZDogdHJ1ZSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVzZXJuYW1lOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl55So5oi35ZCNIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwYXNzd29yZDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeWvhueggSIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29kZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpemqjOivgeeggSIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldENvb2tpZSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgdXBkYXRlVmVyaWZ5Q29kZSgpIHsNCiAgICAgIC8vIOe6r+W<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :rules=\"rules\"\r\n      ref=\"loginForm\"\r\n      v-loading=\"loading\"\r\n      element-loading-text=\"正在登录...\"\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-background=\"rgba(0, 0, 0, 0.8)\"\r\n      :model=\"loginForm\"\r\n      class=\"loginContainer\"\r\n    >\r\n      <h3 class=\"loginTitle\">系统登录</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入用户名\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"password\"\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入密码\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"点击图片更换验证码\"\r\n          @keydown.enter.native=\"submitLogin\"\r\n          style=\"width: 250px\"\r\n        ></el-input>\r\n        <img\r\n          :src=\"vcUrl\"\r\n          @click=\"updateVerifyCode\"\r\n          style=\"cursor: pointer; height: 40px; width: 200px\"\r\n        />\r\n      </el-form-item>\r\n      <el-checkbox size=\"normal\" class=\"loginRemember\" v-model=\"checked\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-button\r\n        size=\"normal\"\r\n        type=\"primary\"\r\n        style=\"width: 100%\"\r\n        @click=\"submitLogin\"\r\n        >登录</el-button\r\n      >\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      vcUrl: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjQwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjEwIiB5PSIyNSIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzMzMzMzMyI+REVNT0NPREU8L3RleHQ+PC9zdmc+\", // 演示验证码\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"123456\",\r\n        code: \"DEMOCODE\",\r\n      },\r\n      checked: true,\r\n      rules: {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入用户名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"请输入密码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [\r\n          {\r\n            required: true,\r\n            message: \"请输入验证码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    updateVerifyCode() {\r\n      // 纯前端模式 - 生成新的演示验证码\r\n      const codes = ['DEMOCODE', 'TESTCODE', 'FRONTEND', 'MOCKAPI'];\r\n      const randomCode = codes[Math.floor(Math.random() * codes.length)];\r\n      this.vcUrl = `data:image/svg+xml;base64,${btoa(`<svg width=\"200\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\"><text x=\"10\" y=\"25\" font-size=\"16\" fill=\"#333333\">${randomCode}</text></svg>`)}`;\r\n      this.loginForm.code = randomCode;\r\n    },\r\n\r\n    submitLogin() {\r\n      let _this = this;\r\n      _this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          _this.loading = true;\r\n\r\n          // 纯前端模式 - 模拟登录验证\r\n          setTimeout(() => {\r\n            _this.loading = false;\r\n\r\n            // 简单的演示登录验证\r\n            if (_this.loginForm.username === 'admin' && _this.loginForm.password === '123456') {\r\n              // 模拟成功响应\r\n              const mockResp = {\r\n                code: 200,\r\n                data: {\r\n                  token: \"demo-token-\" + Date.now(),\r\n                  spbs: \"demo-spbs\",\r\n                  title: \"法律服务管理系统\",\r\n                  quanxian: \"admin\"\r\n                }\r\n              };\r\n\r\n              _this.$store.commit(\"INIT_TOKEN\", mockResp.data.token);\r\n              _this.$store.commit(\"INIT_SPBS\", mockResp.data.spbs);\r\n              _this.$store.commit(\"INIT_TITLE\", mockResp.data.title);\r\n              _this.$store.commit(\"INIT_QUANXIAN\", mockResp.data.quanxian);\r\n\r\n              if (_this.checked) {\r\n                _this.setCookie(\r\n                  _this.loginForm.username,\r\n                  _this.loginForm.password\r\n                );\r\n              }\r\n\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: \"登录成功！\"\r\n              });\r\n\r\n              let path = _this.$route.query.redirect;\r\n              _this.$router.replace(\r\n                path == \"/\" || path == undefined ? \"/\" : path\r\n              );\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: \"用户名或密码错误！演示账号：admin/123456\"\r\n              });\r\n              _this.updateVerifyCode();\r\n            }\r\n          }, 1000); // 模拟网络延迟\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    setCookie(username, password) {\r\n      var exdate = new Date(); //获取时间\r\n      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdate); //保存的天数\r\n      window.document.cookie =\r\n        \"username\" + \"=\" + username + \";path=/;expires=\" + exdate.toGMTString();\r\n      window.document.cookie =\r\n        \"password\" + \"=\" + password + \";path=/;expires=\" + exdate.toGMTString();\r\n    },\r\n    getCookie() {\r\n      if (document.cookie.length > 0) {\r\n        var arr = document.cookie.split(\"; \"); //这里显示的格式需要切割一下自己可输出看下\r\n        for (var i = 0; i < arr.length; i++) {\r\n          var arr2 = arr[i].split(\"=\"); //再次切割\r\n          //判断查找相对应的值\r\n          if (arr2[0] == \"username\") {\r\n            this.loginForm.username = arr2[1]; //保存到保存数据的地方\r\n          } else if (arr2[0] == \"password\") {\r\n            this.loginForm.password = arr2[1];\r\n          }\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.loginContainer {\r\n  border-radius: 15px;\r\n  background-clip: padding-box;\r\n  margin: 180px auto;\r\n  width: 350px;\r\n  padding: 15px 35px 15px 35px;\r\n  background: #fff;\r\n  border: 1px solid #eaeaea;\r\n  box-shadow: 0 0 25px #cac6c6;\r\n}\r\n\r\n.loginTitle {\r\n  margin: 15px auto 20px auto;\r\n  text-align: center;\r\n  color: #505458;\r\n}\r\n\r\n.loginRemember {\r\n  text-align: left;\r\n  margin: 0px 0px 15px 0px;\r\n}\r\n\r\n.el-form-item__content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style>\r\n"]}]}