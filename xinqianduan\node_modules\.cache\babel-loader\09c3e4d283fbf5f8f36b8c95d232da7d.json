{"remainingRequest": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\Login.vue", "mtime": 1748279302926}, {"path": "H:\\fdbfront\\babel.config.js", "mtime": 1732626900032}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>+S4iwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYXJyLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICB2YXIgYXJyMiA9IGFycltpXS5zcGxpdCgiPSIpOyAvL+WGjeasoeWIh+WJsgogICAgICAgICAgLy/liKTmlq3mn6Xmib7nm7jlr7nlupTnmoTlgLwKICAgICAgICAgIGlmIChhcnIyWzBdID09ICJ1c2VybmFtZSIpIHsKICAgICAgICAgICAgdGhpcy5sb2dpbkZvcm0udXNlcm5hbWUgPSBhcnIyWzFdOyAvL+S/neWtmOWIsOS/neWtmOaVsOaNrueahOWcsOaWuQogICAgICAgICAgfSBlbHNlIGlmIChhcnIyWzBdID09ICJwYXNzd29yZCIpIHsKICAgICAgICAgICAgdGhpcy5sb2dpbkZvcm0ucGFzc3dvcmQgPSBhcnIyWzFdOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "loading", "vcUrl", "loginForm", "username", "password", "code", "checked", "rules", "required", "message", "trigger", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateVerifyCode", "codes", "randomCode", "Math", "floor", "random", "length", "btoa", "submitLogin", "_this", "$refs", "validate", "valid", "setTimeout", "mockResp", "token", "Date", "now", "spbs", "title", "quanxian", "$store", "commit", "<PERSON><PERSON><PERSON><PERSON>", "$message", "type", "path", "$route", "query", "redirect", "$router", "replace", "undefined", "exdate", "setTime", "getTime", "window", "document", "cookie", "toGMTString", "arr", "split", "i", "arr2"], "sources": ["src/views/Login.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :rules=\"rules\"\r\n      ref=\"loginForm\"\r\n      v-loading=\"loading\"\r\n      element-loading-text=\"正在登录...\"\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-background=\"rgba(0, 0, 0, 0.8)\"\r\n      :model=\"loginForm\"\r\n      class=\"loginContainer\"\r\n    >\r\n      <h3 class=\"loginTitle\">系统登录</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入用户名\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"password\"\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"请输入密码\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\">\r\n        <el-input\r\n          size=\"normal\"\r\n          type=\"text\"\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"点击图片更换验证码\"\r\n          @keydown.enter.native=\"submitLogin\"\r\n          style=\"width: 250px\"\r\n        ></el-input>\r\n        <img\r\n          :src=\"vcUrl\"\r\n          @click=\"updateVerifyCode\"\r\n          style=\"cursor: pointer; height: 40px; width: 200px\"\r\n        />\r\n      </el-form-item>\r\n      <el-checkbox size=\"normal\" class=\"loginRemember\" v-model=\"checked\"\r\n        >记住密码</el-checkbox\r\n      >\r\n      <el-button\r\n        size=\"normal\"\r\n        type=\"primary\"\r\n        style=\"width: 100%\"\r\n        @click=\"submitLogin\"\r\n        >登录</el-button\r\n      >\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      vcUrl: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjQwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjEwIiB5PSIyNSIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzMzMzMzMyI+REVNT0NPREU8L3RleHQ+PC9zdmc+\", // 演示验证码\r\n      loginForm: {\r\n        username: \"admin\",\r\n        password: \"123456\",\r\n        code: \"DEMOCODE\",\r\n      },\r\n      checked: true,\r\n      rules: {\r\n        username: [\r\n          {\r\n            required: true,\r\n            message: \"请输入用户名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"请输入密码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        code: [\r\n          {\r\n            required: true,\r\n            message: \"请输入验证码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getCookie();\r\n  },\r\n  methods: {\r\n    updateVerifyCode() {\r\n      // 纯前端模式 - 生成新的演示验证码\r\n      const codes = ['DEMOCODE', 'TESTCODE', 'FRONTEND', 'MOCKAPI'];\r\n      const randomCode = codes[Math.floor(Math.random() * codes.length)];\r\n      this.vcUrl = `data:image/svg+xml;base64,${btoa(`<svg width=\"200\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\"><text x=\"10\" y=\"25\" font-size=\"16\" fill=\"#333333\">${randomCode}</text></svg>`)}`;\r\n      this.loginForm.code = randomCode;\r\n    },\r\n\r\n    submitLogin() {\r\n      let _this = this;\r\n      _this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          _this.loading = true;\r\n\r\n          // 纯前端模式 - 模拟登录验证\r\n          setTimeout(() => {\r\n            _this.loading = false;\r\n\r\n            // 简单的演示登录验证\r\n            if (_this.loginForm.username === 'admin' && _this.loginForm.password === '123456') {\r\n              // 模拟成功响应\r\n              const mockResp = {\r\n                code: 200,\r\n                data: {\r\n                  token: \"demo-token-\" + Date.now(),\r\n                  spbs: \"demo-spbs\",\r\n                  title: \"法律服务管理系统\",\r\n                  quanxian: \"admin\"\r\n                }\r\n              };\r\n\r\n              _this.$store.commit(\"INIT_TOKEN\", mockResp.data.token);\r\n              _this.$store.commit(\"INIT_SPBS\", mockResp.data.spbs);\r\n              _this.$store.commit(\"INIT_TITLE\", mockResp.data.title);\r\n              _this.$store.commit(\"INIT_QUANXIAN\", mockResp.data.quanxian);\r\n\r\n              if (_this.checked) {\r\n                _this.setCookie(\r\n                  _this.loginForm.username,\r\n                  _this.loginForm.password\r\n                );\r\n              }\r\n\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: \"登录成功！\"\r\n              });\r\n\r\n              let path = _this.$route.query.redirect;\r\n              _this.$router.replace(\r\n                path == \"/\" || path == undefined ? \"/\" : path\r\n              );\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: \"用户名或密码错误！演示账号：admin/123456\"\r\n              });\r\n              _this.updateVerifyCode();\r\n            }\r\n          }, 1000); // 模拟网络延迟\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    setCookie(username, password) {\r\n      var exdate = new Date(); //获取时间\r\n      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdate); //保存的天数\r\n      window.document.cookie =\r\n        \"username\" + \"=\" + username + \";path=/;expires=\" + exdate.toGMTString();\r\n      window.document.cookie =\r\n        \"password\" + \"=\" + password + \";path=/;expires=\" + exdate.toGMTString();\r\n    },\r\n    getCookie() {\r\n      if (document.cookie.length > 0) {\r\n        var arr = document.cookie.split(\"; \"); //这里显示的格式需要切割一下自己可输出看下\r\n        for (var i = 0; i < arr.length; i++) {\r\n          var arr2 = arr[i].split(\"=\"); //再次切割\r\n          //判断查找相对应的值\r\n          if (arr2[0] == \"username\") {\r\n            this.loginForm.username = arr2[1]; //保存到保存数据的地方\r\n          } else if (arr2[0] == \"password\") {\r\n            this.loginForm.password = arr2[1];\r\n          }\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.loginContainer {\r\n  border-radius: 15px;\r\n  background-clip: padding-box;\r\n  margin: 180px auto;\r\n  width: 350px;\r\n  padding: 15px 35px 15px 35px;\r\n  background: #fff;\r\n  border: 1px solid #eaeaea;\r\n  box-shadow: 0 0 25px #cac6c6;\r\n}\r\n\r\n.loginTitle {\r\n  margin: 15px auto 20px auto;\r\n  text-align: center;\r\n  color: #505458;\r\n}\r\n\r\n.loginRemember {\r\n  text-align: left;\r\n  margin: 0px 0px 15px 0px;\r\n}\r\n\r\n.el-form-item__content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style>\r\n"], "mappings": ";AA8DA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACAC,OAAA;MACAC,KAAA;QACAJ,QAAA,GACA;UACAK,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAN,QAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAL,IAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,iBAAA;MACA;MACA,MAAAC,KAAA;MACA,MAAAC,UAAA,GAAAD,KAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAJ,KAAA,CAAAK,MAAA;MACA,KAAAnB,KAAA,gCAAAoB,IAAA,sHAAAL,UAAA;MACA,KAAAd,SAAA,CAAAG,IAAA,GAAAW,UAAA;IACA;IAEAM,YAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAC,KAAA,CAAAtB,SAAA,CAAAuB,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAAvB,OAAA;;UAEA;UACA2B,UAAA;YACAJ,KAAA,CAAAvB,OAAA;;YAEA;YACA,IAAAuB,KAAA,CAAArB,SAAA,CAAAC,QAAA,gBAAAoB,KAAA,CAAArB,SAAA,CAAAE,QAAA;cACA;cACA,MAAAwB,QAAA;gBACAvB,IAAA;gBACAN,IAAA;kBACA8B,KAAA,kBAAAC,IAAA,CAAAC,GAAA;kBACAC,IAAA;kBACAC,KAAA;kBACAC,QAAA;gBACA;cACA;cAEAX,KAAA,CAAAY,MAAA,CAAAC,MAAA,eAAAR,QAAA,CAAA7B,IAAA,CAAA8B,KAAA;cACAN,KAAA,CAAAY,MAAA,CAAAC,MAAA,cAAAR,QAAA,CAAA7B,IAAA,CAAAiC,IAAA;cACAT,KAAA,CAAAY,MAAA,CAAAC,MAAA,eAAAR,QAAA,CAAA7B,IAAA,CAAAkC,KAAA;cACAV,KAAA,CAAAY,MAAA,CAAAC,MAAA,kBAAAR,QAAA,CAAA7B,IAAA,CAAAmC,QAAA;cAEA,IAAAX,KAAA,CAAAjB,OAAA;gBACAiB,KAAA,CAAAc,SAAA,CACAd,KAAA,CAAArB,SAAA,CAAAC,QAAA,EACAoB,KAAA,CAAArB,SAAA,CAAAE,QACA;cACA;cAEAmB,KAAA,CAAAe,QAAA;gBACAC,IAAA;gBACA9B,OAAA;cACA;cAEA,IAAA+B,IAAA,GAAAjB,KAAA,CAAAkB,MAAA,CAAAC,KAAA,CAAAC,QAAA;cACApB,KAAA,CAAAqB,OAAA,CAAAC,OAAA,CACAL,IAAA,WAAAA,IAAA,IAAAM,SAAA,SAAAN,IACA;YACA;cACAjB,KAAA,CAAAe,QAAA;gBACAC,IAAA;gBACA9B,OAAA;cACA;cACAc,KAAA,CAAAT,gBAAA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAuB,UAAAlC,QAAA,EAAAC,QAAA;MACA,IAAA2C,MAAA,OAAAjB,IAAA;MACAiB,MAAA,CAAAC,OAAA,CAAAD,MAAA,CAAAE,OAAA,2BAAAF,MAAA;MACAG,MAAA,CAAAC,QAAA,CAAAC,MAAA,GACA,mBAAAjD,QAAA,wBAAA4C,MAAA,CAAAM,WAAA;MACAH,MAAA,CAAAC,QAAA,CAAAC,MAAA,GACA,mBAAAhD,QAAA,wBAAA2C,MAAA,CAAAM,WAAA;IACA;IACAzC,UAAA;MACA,IAAAuC,QAAA,CAAAC,MAAA,CAAAhC,MAAA;QACA,IAAAkC,GAAA,GAAAH,QAAA,CAAAC,MAAA,CAAAG,KAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAlC,MAAA,EAAAoC,CAAA;UACA,IAAAC,IAAA,GAAAH,GAAA,CAAAE,CAAA,EAAAD,KAAA;UACA;UACA,IAAAE,IAAA;YACA,KAAAvD,SAAA,CAAAC,QAAA,GAAAsD,IAAA;UACA,WAAAA,IAAA;YACA,KAAAvD,SAAA,CAAAE,QAAA,GAAAqD,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}