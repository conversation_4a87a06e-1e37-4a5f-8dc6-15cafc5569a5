{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748525288783}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSG9tZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBtb25leV9jb3VudDogMCwKICAgICAgdXNlcl9jb3VudDogMCwKICAgICAgdmlzaXRfY291bnQ6IDEyMzQsCiAgICAgIC8vIOa8lOekuuaVsOaNrgogICAgICBzZWFyY2hfY291bnQ6IDAsCiAgICAgIGV4cG9ydF9jb3VudDogMCwKICAgICAgb3JkZXJfY291bnQ6IDAsCiAgICAgIGdhb2RlX2NvdW50OiAwLAogICAgICB0ZW5neHVuX2NvdW50OiAwLAogICAgICBiYWlkdV9jb3VudDogMCwKICAgICAgc2h1bnFpd2FuZ19jb3VudDogMCwKICAgICAgc2hvd19pbWFnZTogIiIsCiAgICAgIG1lbnVzOiBbXSwKICAgICAgdXJsOiAiL1l1YW5nb25nLyIKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgbmFtZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RJVExFOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8vIOe6r+<PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "names": ["name", "data", "dialogVisible", "money_count", "user_count", "visit_count", "search_count", "export_count", "order_count", "gaode_count", "tengxun_count", "baidu_count", "shunqiwang_count", "show_image", "menus", "url", "computed", "$store", "getters", "GET_TITLE", "mounted", "path", "children", "console", "log", "methods", "showQrcode", "menuClick", "index", "$router", "push", "getQuanxian", "getCountAll", "logout", "commit", "$message", "type", "message", "_this", "setTimeout"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <el-container class=\"cont\">\n    <el-header class=\"top-header\">\n      <!-- 顶部导航栏 -->\n      <div class=\"header-left\">\n        <span class=\"logo\">{{ name }}</span>\n      </div>\n      <div class=\"header-center\">\n        <el-menu\n          class=\"top-menu\"\n          @select=\"menuClick\"\n          mode=\"horizontal\"\n          background-color=\"#001529\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/\">首页</el-menu-item>\n          <!-- 如果有子菜单，显示为下拉菜单 -->\n          <el-submenu\n            v-for=\"(item, index) in menus\"\n            v-if=\"item.children && item.children.length > 1\"\n            :key=\"'submenu-' + index\"\n            :index=\"item.path\"\n            popper-class=\"vertical-submenu\"\n          >\n            <template slot=\"title\">{{ item.name }}</template>\n            <el-menu-item\n              v-for=\"(child, indexj) in item.children\"\n              :key=\"indexj\"\n              :index=\"child.path\"\n            >\n              {{ child.name }}\n            </el-menu-item>\n          </el-submenu>\n          <!-- 如果只有一个子菜单或没有子菜单，直接显示为菜单项 -->\n          <el-menu-item\n            v-for=\"(item, index) in menus\"\n            v-if=\"!item.children || item.children.length <= 1\"\n            :key=\"'menuitem-' + index\"\n            :index=\"item.children && item.children.length === 1 ? item.children[0].path : item.path\"\n          >\n            {{ item.name }}\n          </el-menu-item>\n        </el-menu>\n      </div>\n      <div class=\"header-right\">\n        <el-dropdown trigger=\"click\">\n          <span class=\"user-info\">管理员</span>\n          <el-dropdown-menu>\n            <el-dropdown-item\n              ><div @click=\"menuClick('/changePwd')\">\n                修改密码\n              </div></el-dropdown-item\n            >\n            <el-dropdown-item>\n              <div @click=\"logout()\">退出登录</div>\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </div>\n    </el-header>\n\n    <el-container class=\"content-container\">\n      <el-header class=\"breadcrumb-header\">\n        <el-breadcrumb separator=\"/\">\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n          <el-breadcrumb-item>{{\n            this.$router.currentRoute.name\n          }}</el-breadcrumb-item>\n        </el-breadcrumb>\n      </el-header>\n\n      <el-main class=\"main-content\">\n        <router-view></router-view>\n      </el-main>\n    </el-container>\n\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\n      <el-image :src=\"show_image\"></el-image>\n    </el-dialog>\n  </el-container>\n</template>\n\n<script>\nexport default {\n  name: \"Home\",\n  data() {\n    return {\n      dialogVisible: false,\n      money_count: 0,\n      user_count: 0,\n      visit_count: 1234, // 演示数据\n      search_count: 0,\n      export_count: 0,\n      order_count: 0,\n      gaode_count: 0,\n      tengxun_count: 0,\n      baidu_count: 0,\n      shunqiwang_count: 0,\n      show_image: \"\",\n      menus: [],\n      url: \"/Yuangong/\",\n    };\n  },\n  computed: {\n    name() {\n      return this.$store.getters.GET_TITLE;\n    },\n  },\n  mounted() {\n    // 纯前端模式 - 直接提供菜单数据\n    this.menus = [\n      {\n        path: \"/jichu\",\n        name: \"基础管理\",\n        children: [\n          { path: \"/config\", name: \"基础设置\" },\n          { path: \"/banner\", name: \"轮播图\" },\n          { path: \"/nav\", name: \"首页导航\" },\n          { path: \"/gonggao\", name: \"公告\" }\n        ]\n      },\n      {\n        path: \"/xiadan\",\n        name: \"订单管理\",\n        children: [\n          { path: \"/type\", name: \"服务类型\" },\n          { path: \"/taocan\", name: \"套餐类型\" },\n          { path: \"/dingdan\", name: \"签约用户列表\" },\n          { path: \"/qun\", name: \"签约客户群\" }\n        ]\n      },\n      {\n        path: \"/yonghu\",\n        name: \"用户管理\",\n        children: [\n          { path: \"/user\", name: \"用户列表\" }\n        ]\n      },\n      {\n        path: \"/zhifu\",\n        name: \"支付列表\",\n        children: [\n          { path: \"/order\", name: \"支付列表\" }\n        ]\n      },\n      {\n        path: \"/liaotian\",\n        name: \"聊天列表\",\n        children: [\n          { path: \"/chat\", name: \"聊天列表\" }\n        ]\n      },\n      {\n        path: \"/debt\",\n        name: \"债权管理\",\n        children: [\n          { path: \"/debts\", name: \"债务人列表\" }\n        ]\n      },\n      {\n        path: \"/wenshuguanli\",\n        name: \"文书管理\",\n        children: [\n          { path: \"/dingzhi\", name: \"合同定制\" },\n          { path: \"/shenhe\", name: \"合同审核\" },\n          { path: \"/cate\", name: \"合同类型\" },\n          { path: \"/hetong\", name: \"合同列表\" },\n          { path: \"/lawyer\", name: \"发律师函\" },\n          { path: \"/kecheng\", name: \"课程列表\" }\n        ]\n      },\n      {\n        path: \"/yuangong\",\n        name: \"员工管理\",\n        children: [\n          { path: \"/zhiwei\", name: \"职位\" },\n          { path: \"/yuangong\", name: \"员工\" },\n          { path: \"/lvshi\", name: \"律师\" }\n        ]\n      },\n      {\n        path: \"/fuwu\",\n        name: \"服务管理\",\n        children: [\n          { path: \"/fuwu\", name: \"服务列表\" }\n        ]\n      },\n      {\n        path: \"/xinwen\",\n        name: \"案例管理\",\n        children: [\n          { path: \"/anli\", name: \"案例列表\" }\n        ]\n      },\n      {\n        path: \"/lvshiguanli\",\n        name: \"专业管理\",\n        children: [\n          { path: \"/zhuanye\", name: \"专业列表\" }\n        ]\n      },\n      {\n        path: \"/archive\",\n        name: \"归档管理\",\n        children: [\n          { path: \"/archive/file\", name: \"文件归档\" },\n          { path: \"/archive/search\", name: \"档案检索\" }\n        ]\n      }\n    ];\n\n    console.log(\"菜单数据已加载:\", this.menus);\n  },\n  methods: {\n    showQrcode() {\n      // 纯前端模式 - 显示演示二维码\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\n      this.dialogVisible = true;\n    },\n    menuClick(index) {\n      this.$router.push(index);\n    },\n    getQuanxian() {\n      // 纯前端模式 - 不需要后端权限验证\n      console.log(\"纯前端模式，跳过权限验证\");\n    },\n    getCountAll() {\n      // 纯前端模式 - 使用演示数据\n      console.log(\"纯前端模式，使用演示数据\");\n    },\n    logout() {\n      this.$store.commit(\"INIT_TOKEN\", \"\");\n      this.$store.commit(\"INIT_TITLE\", \"\");\n      this.$message({\n        type: \"success\",\n        message: \"退出成功\",\n      });\n      let _this = this;\n      setTimeout(function () {\n        _this.$router.push(\"/login\");\n      }, 1500);\n    },\n  },\n};\n</script>\n<style scoped>\n.cont {\n  height: 100vh;\n  overflow: hidden;\n  flex-direction: column;\n}\n\n.content-container {\n  flex: 1;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* 顶部导航栏样式 */\n.top-header {\n  height: 60px;\n  background-color: #001529;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: relative;\n  z-index: 1000;\n}\n\n.header-left {\n  flex: 0 0 200px;\n  min-width: 200px;\n}\n\n.logo {\n  color: #fff;\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.header-center {\n  flex: 1;\n  display: flex;\n  justify-content: flex-start;\n  overflow: hidden;\n}\n\n.top-menu {\n  border: none !important;\n  background-color: transparent !important;\n  width: 100%;\n  min-width: 0;\n}\n\n/* 强制水平排列 */\n.top-menu {\n  display: flex !important;\n  flex-direction: row !important;\n}\n\n.top-menu >>> .el-menu {\n  display: flex !important;\n  flex-direction: row !important;\n  width: 100%;\n}\n\n.top-menu >>> .el-menu-item,\n.top-menu >>> .el-submenu {\n  border-bottom: none !important;\n  height: 60px;\n  line-height: 60px;\n  padding: 0 15px;\n  white-space: nowrap;\n  flex: 0 0 auto;\n  display: inline-flex !important;\n  align-items: center;\n  float: none !important;\n}\n\n/* 确保Element UI的默认样式被覆盖 */\n.el-menu--horizontal {\n  display: flex !important;\n  flex-direction: row !important;\n}\n\n.el-menu--horizontal .el-menu-item,\n.el-menu--horizontal .el-submenu {\n  float: none !important;\n  display: inline-flex !important;\n}\n\n.top-menu .el-submenu__title {\n  height: 60px;\n  line-height: 60px;\n  padding: 0 15px;\n  border-bottom: none !important;\n}\n\n/* 强制子菜单垂直排列 - 最高优先级 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  margin: 0 !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 使用更高优先级的选择器 */\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 针对所有可能的子菜单容器 */\n.el-submenu__drop-down .el-menu-item,\n.el-submenu .el-submenu__drop-down .el-menu-item,\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n}\n\n/* 覆盖任何可能的水平布局 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n.el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n}\n\n/* 最强制的垂直排列规则 - 覆盖所有可能的水平布局 */\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item,\n.el-submenu__drop-down .el-menu .el-menu-item,\n.el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  box-sizing: border-box !important;\n}\n\n/* 强制下拉菜单容器为垂直布局 */\n.el-submenu__drop-down,\n.el-menu--horizontal .el-submenu__drop-down,\n.el-submenu .el-submenu__drop-down {\n  display: block !important;\n  flex-direction: column !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n/* 确保子菜单内的ul也是垂直的 */\n.el-submenu__drop-down ul,\n.el-submenu__drop-down .el-menu {\n  display: block !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  list-style: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* 针对Element UI水平菜单的特殊处理 - 最强制的规则 */\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down {\n  display: block !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  border-right: none !important;\n  border-left: none !important;\n  border-top: none !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu .el-menu-item:last-child {\n  border-bottom: none !important;\n}\n\n/* 强制覆盖任何可能的inline样式 */\n.el-submenu__drop-down .el-menu-item[style] {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n}\n\n/* 最终解决方案 - 直接覆盖Element UI的默认行为 */\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 40px !important;\n  line-height: 40px !important;\n  padding: 0 20px !important;\n  margin: 0 !important;\n  background-color: #fff !important;\n  color: #333 !important;\n  text-align: left !important;\n  border-bottom: 1px solid #f0f0f0 !important;\n  border-right: none !important;\n  border-left: none !important;\n  border-top: none !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n  position: relative !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down .el-menu-item:hover {\n  background-color: #f5f5f5 !important;\n  color: #409EFF !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down {\n  display: block !important;\n  position: absolute !important;\n  top: 100% !important;\n  left: 0 !important;\n  z-index: 1000 !important;\n  min-width: 160px !important;\n  background: #fff !important;\n  border: 1px solid #e4e7ed !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\n}\n\n.top-menu .el-submenu .el-submenu__drop-down .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* 使用更高的CSS优先级 */\n.el-menu.el-menu--horizontal.top-menu .el-submenu .el-submenu__drop-down .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  clear: both !important;\n}\n\n/* 针对自定义popper-class的样式 - 美化版本 */\n.vertical-submenu {\n  display: block !important;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\n  overflow: hidden !important;\n  min-width: 180px !important;\n  padding: 8px 0 !important;\n}\n\n.vertical-submenu .el-menu {\n  display: flex !important;\n  flex-direction: column !important;\n  width: 100% !important;\n  background: transparent !important;\n  border: none !important;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n.vertical-submenu .el-menu-item {\n  display: block !important;\n  float: none !important;\n  width: 100% !important;\n  height: 44px !important;\n  line-height: 44px !important;\n  padding: 0 20px !important;\n  margin: 2px 8px !important;\n  background-color: rgba(255, 255, 255, 0.95) !important;\n  color: #2c3e50 !important;\n  text-align: left !important;\n  border: none !important;\n  border-radius: 6px !important;\n  box-sizing: border-box !important;\n  clear: both !important;\n  font-weight: 500 !important;\n  font-size: 14px !important;\n  transition: all 0.3s ease !important;\n  position: relative !important;\n  width: calc(100% - 16px) !important;\n}\n\n.vertical-submenu .el-menu-item:hover {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\n  color: #fff !important;\n  transform: translateX(4px) !important;\n  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3) !important;\n}\n\n.vertical-submenu .el-menu-item:active {\n  transform: translateX(2px) !important;\n}\n\n.vertical-submenu .el-menu-item:last-child {\n  margin-bottom: 0 !important;\n}\n\n/* 添加一些动画效果 */\n.vertical-submenu .el-menu-item::before {\n  content: '' !important;\n  position: absolute !important;\n  left: 0 !important;\n  top: 50% !important;\n  transform: translateY(-50%) !important;\n  width: 3px !important;\n  height: 0 !important;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;\n  border-radius: 0 2px 2px 0 !important;\n  transition: height 0.3s ease !important;\n}\n\n.vertical-submenu .el-menu-item:hover::before {\n  height: 20px !important;\n}\n\n.header-right {\n  flex: 0 0 150px;\n  min-width: 150px;\n  text-align: right;\n}\n\n.user-info {\n  color: #fff;\n  cursor: pointer;\n  padding: 0 15px;\n}\n\n.user-info:hover {\n  color: #ffd04b;\n}\n\n/* 面包屑导航样式 */\n.breadcrumb-header {\n  height: 50px;\n  background-color: #f5f5f5;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n  flex-shrink: 0;\n}\n\n.el-breadcrumb {\n  line-height: 50px;\n}\n\n/* 主内容区域样式 - 新UI风格 */\n.main-content {\n  flex: 1;\n  overflow: auto;\n  background-color: #f5f5f5;\n  padding: 16px;\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\n}\n\n/* 页面内容容器 */\n.main-content .page-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  padding: 24px;\n  margin-bottom: 16px;\n}\n\n/* 页面标题样式 */\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 标签页导航样式 */\n.tab-navigation {\n  margin-bottom: 24px;\n}\n\n.tab-navigation .el-tabs__header {\n  margin: 0;\n}\n\n.tab-navigation .el-tabs__nav-wrap::after {\n  height: 1px;\n  background-color: #e8e8e8;\n}\n\n/* 搜索筛选区域样式 */\n.search-section {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.search-form {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  align-items: center;\n}\n\n.search-form .el-form-item {\n  margin-bottom: 0;\n}\n\n/* 操作按钮区域 */\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  margin-left: auto;\n}\n\n/* 表格样式优化 */\n.data-table {\n  margin-top: 16px;\n}\n\n.data-table .el-table {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n}\n\n.data-table .el-table th {\n  background-color: #fafafa;\n  color: #262626;\n  font-weight: 500;\n}\n\n.data-table .el-table td {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 分页样式 */\n.pagination-wrapper {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 16px;\n  padding: 16px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .top-header {\n    flex-direction: column;\n    height: auto;\n    padding: 10px;\n  }\n\n  .header-center {\n    width: 100%;\n    justify-content: flex-start;\n    margin: 10px 0;\n  }\n\n  .top-menu {\n    width: 100%;\n  }\n}\n\n/* 移除原有的侧边栏样式 */\n.size {\n  width: 100%;\n  height: 100%;\n}\n\n.homeRouterView {\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";AAoFA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MAAA;MACAC,YAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACAhB,KAAA;MACA,YAAAiB,MAAA,CAAAC,OAAA,CAAAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAN,KAAA,IACA;MACAO,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA;IAEA,GACA;MACAqB,IAAA;MACArB,IAAA;MACAsB,QAAA,GACA;QAAAD,IAAA;QAAArB,IAAA;MAAA,GACA;QAAAqB,IAAA;QAAArB,IAAA;MAAA;IAEA,EACA;IAEAuB,OAAA,CAAAC,GAAA,kBAAAV,KAAA;EACA;EACAW,OAAA;IACAC,WAAA;MACA;MACA,KAAAb,UAAA;MACA,KAAAX,aAAA;IACA;IACAyB,UAAAC,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,KAAA;IACA;IACAG,YAAA;MACA;MACAR,OAAA,CAAAC,GAAA;IACA;IACAQ,YAAA;MACA;MACAT,OAAA,CAAAC,GAAA;IACA;IACAS,OAAA;MACA,KAAAhB,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,IAAAC,KAAA;MACAC,UAAA;QACAD,KAAA,CAAAT,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}