(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ffa028e"],{"0b1c":function(t,e,s){"use strict";s("506a")},"506a":function(t,e,s){},9402:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat-container",on:{click:function(e){t.isEmji=!1}}},[e("div",{staticClass:"chat-content"},[e("div",{staticClass:"contact-sidebar"},[e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-input-wrapper"},[e("i",{staticClass:"el-icon-search search-icon"}),e("input",{directives:[{name:"model",rawName:"v-model",value:t.search,expression:"search"}],staticClass:"search-input",attrs:{type:"text",placeholder:"搜索联系人或群聊"},domProps:{value:t.search},on:{input:[function(e){e.target.composing||(t.search=e.target.value)},t.changeKeyword]}}),t.isShowSeach?e("el-tooltip",{attrs:{content:"清除搜索",placement:"top",effect:"dark"}},[e("i",{staticClass:"el-icon-close clear-icon",on:{click:t.del}})]):t._e()],1)]),e("div",{staticClass:"tab-section"},[e("el-button",{class:{"active-tab":"group"===t.currentTab},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showDaiban("2")}}},[e("i",{staticClass:"el-icon-s-custom"}),t._v(" 群聊 ")]),e("el-button",{class:{"active-tab":"todo"===t.currentTab},attrs:{type:"success",size:"small"},on:{click:function(e){return t.showDaiban("1")}}},[e("i",{staticClass:"el-icon-s-order"}),t._v(" 代办 ")])],1),e("div",{staticClass:"contact-list"},[t._l(t.quns,(function(s,a){return e("div",{key:"qun"+a,staticClass:"contact-item",class:{active:a===t.quliaoIndex&&-1===t.selectId},on:{click:function(e){return t.changeQun(a)}}},[e("div",{staticClass:"avatar-wrapper"},[e("img",{staticClass:"avatar",attrs:{src:s.pic_path}}),s.count>0?e("span",{staticClass:"unread-badge"},[t._v(t._s(s.count))]):t._e()]),e("div",{staticClass:"contact-info"},[e("div",{staticClass:"contact-header"},[e("h4",{staticClass:"contact-name"},[t._v(t._s(s.title))]),e("span",{staticClass:"contact-time"},[t._v(t._s(s.create_time))])]),e("p",{staticClass:"last-message"},[t._v(t._s(s.desc))])])])})),t._l(t.users,(function(s,a){return e("div",{key:"user"+a,staticClass:"contact-item",class:{active:a===t.selectId&&-1===t.quliaoIndex},on:{click:function(e){return t.redSession(a)}}},[e("div",{staticClass:"avatar-wrapper"},[e("img",{staticClass:"avatar",attrs:{src:s.pic_path}}),e("div",{staticClass:"online-status"})]),e("div",{staticClass:"contact-info"},[e("div",{staticClass:"contact-header"},[e("h4",{staticClass:"contact-name"},[t._v(t._s(s.title))]),e("span",{staticClass:"contact-time"},[t._v(t._s(s.time))])]),e("p",{staticClass:"last-message"},[t._v(t._s(s.content))])])])}))],2)]),e("div",{staticClass:"chat-main",on:{click:function(e){t.showMemberPanel=!1}}},[e("div",{staticClass:"chat-header"},[e("div",{staticClass:"chat-title"},[e("h3",[t._v(t._s(t.title))])]),e("div",{staticClass:"chat-actions"},[e("el-tooltip",{attrs:{content:"用户详情",placement:"bottom",effect:"dark"}},[e("el-button",{staticClass:"user-detail-btn",class:{active:t.showUserDetail},attrs:{type:"text",icon:"el-icon-user"},on:{click:function(e){e.stopPropagation(),t.showUserDetail=!t.showUserDetail}}})],1),e("el-tooltip",{attrs:{content:"查看群成员",placement:"bottom",effect:"dark"}},[e("el-button",{staticClass:"more-btn",attrs:{type:"text",icon:"el-icon-more"},on:{click:function(e){return e.stopPropagation(),t.toggleMemberPanel.apply(null,arguments)}}})],1)],1)]),e("div",{ref:"list",staticClass:"message-list",on:{scroll:function(e){return t.handleScroll()}}},t._l(t.list,(function(s,a){return e("div",{key:a,staticClass:"message-item"},[e("div",{staticClass:"time-divider"},[e("span",{staticClass:"time-text"},[t._v(t._s(s.create_time))])]),e("div",{staticClass:"message-wrapper",class:{"own-message":s.yuangong_id==t.yon_id}},[e("div",{staticClass:"message-avatar"},[e("img",{attrs:{src:s.avatar}})]),e("div",{staticClass:"message-content"},[e("div",{staticClass:"sender-name"},[t._v(t._s(s.title))]),e("div",{staticClass:"message-bubble"},["image"==s.type?e("div",{staticClass:"image-message"},[e("img",{attrs:{src:s.content},on:{click:function(e){return t.openImg(s.content)}}})]):t._e(),"text"==s.type?e("div",{staticClass:"text-message"},[t._v(" "+t._s(s.content)+" ")]):t._e(),"voice"==s.type?e("div",{staticClass:"voice-message"},[e("div",{staticClass:"voice-content"},[e("audioplay",{attrs:{recordFile:s.content}}),e("span",{staticClass:"voice-duration"},[t._v(t._s(s.datas))])],1)]):t._e(),"file"==s.type?e("div",{staticClass:"file-message"},[e("div",{staticClass:"file-content",on:{click:function(e){return t.openFile(s.content)}}},[t._m(0,!0),e("div",{staticClass:"file-info"},[e("div",{staticClass:"file-name"},[t._v(t._s(s.files.name))]),e("div",{staticClass:"file-size"},[t._v(t._s(s.files.size))])])])]):t._e()])])])])})),0),e("div",{staticClass:"user-detail-sidebar",class:{show:t.showUserDetail},on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"detail-header"},[e("h3",[t._v("用户详情")]),e("el-button",{staticClass:"close-btn",attrs:{type:"text",icon:"el-icon-close"},on:{click:function(e){t.showUserDetail=!1}}})],1),e("div",{staticClass:"detail-content"},[e("div",{staticClass:"detail-menu"},[e("div",{staticClass:"menu-item",class:{active:"info"===t.activeDetailTab},on:{click:function(e){t.activeDetailTab="info"}}},[e("i",{staticClass:"el-icon-user"}),e("span",[t._v("基本信息")])]),e("div",{staticClass:"menu-item",class:{active:"debtors"===t.activeDetailTab},on:{click:function(e){t.activeDetailTab="debtors"}}},[e("i",{staticClass:"el-icon-s-custom"}),e("span",[t._v("关联债务人")])]),e("div",{staticClass:"menu-item",class:{active:"documents"===t.activeDetailTab},on:{click:function(e){t.activeDetailTab="documents"}}},[e("i",{staticClass:"el-icon-folder"}),e("span",[t._v("相关文档")])]),e("div",{staticClass:"menu-item",class:{active:"orders"===t.activeDetailTab},on:{click:function(e){t.activeDetailTab="orders"}}},[e("i",{staticClass:"el-icon-tickets"}),e("span",[t._v("工单记录")])]),e("div",{staticClass:"menu-item",class:{active:"payments"===t.activeDetailTab},on:{click:function(e){t.activeDetailTab="payments"}}},[e("i",{staticClass:"el-icon-money"}),e("span",[t._v("支付记录")])])]),e("div",{staticClass:"detail-main"},["info"===t.activeDetailTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"user-profile"},[e("div",{staticClass:"profile-avatar"},[e("img",{attrs:{src:t.currentUserDetail.avatar,alt:"用户头像"}})]),e("div",{staticClass:"profile-info"},[e("h4",[t._v(t._s(t.currentUserDetail.name))]),e("p",{staticClass:"user-type"},[t._v(t._s(t.currentUserDetail.type))])])]),e("div",{staticClass:"info-section"},[e("div",{staticClass:"info-item"},[e("label",[t._v("手机号码：")]),e("span",[t._v(t._s(t.currentUserDetail.phone))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("身份证号：")]),e("span",[t._v(t._s(t.currentUserDetail.idCard))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("注册时间：")]),e("span",[t._v(t._s(t.currentUserDetail.registerTime))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("最后登录：")]),e("span",[t._v(t._s(t.currentUserDetail.lastLogin))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("用户状态：")]),e("el-tag",{attrs:{type:"正常"===t.currentUserDetail.status?"success":"danger"}},[t._v(" "+t._s(t.currentUserDetail.status)+" ")])],1)])]):t._e(),"debtors"===t.activeDetailTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"section-header"},[e("h4",[t._v("关联债务人列表")]),e("span",{staticClass:"count-badge"},[t._v(t._s(t.currentUserDetail.debtors.length)+"人")])]),e("div",{staticClass:"debtors-list"},t._l(t.currentUserDetail.debtors,(function(s){return e("div",{key:s.id,staticClass:"debtor-card"},[e("div",{staticClass:"debtor-info"},[e("div",{staticClass:"debtor-name"},[t._v(t._s(s.name))]),e("div",{staticClass:"debtor-details"},[e("span",{staticClass:"debt-amount"},[t._v("欠款金额：¥"+t._s(s.amount))]),e("span",{staticClass:"debt-status",class:s.status},[t._v(t._s(s.statusText))])])]),e("div",{staticClass:"debtor-actions"},[e("el-button",{attrs:{type:"text",size:"small"}},[t._v("查看详情")])],1)])})),0)]):t._e(),"documents"===t.activeDetailTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"section-header"},[e("h4",[t._v("相关文档")]),e("span",{staticClass:"count-badge"},[t._v(t._s(t.currentUserDetail.documents.length)+"个")])]),e("div",{staticClass:"documents-list"},t._l(t.currentUserDetail.documents,(function(s){return e("div",{key:s.id,staticClass:"document-item"},[e("div",{staticClass:"doc-icon"},[e("i",{class:t.getDocIcon(s.type)})]),e("div",{staticClass:"doc-info"},[e("div",{staticClass:"doc-name"},[t._v(t._s(s.name))]),e("div",{staticClass:"doc-meta"},[e("span",[t._v(t._s(s.size))]),e("span",[t._v(t._s(s.uploadTime))])])]),e("div",{staticClass:"doc-actions"},[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.downloadDoc(s)}}},[t._v("下载")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.previewDoc(s)}}},[t._v("预览")])],1)])})),0)]):t._e(),"orders"===t.activeDetailTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"section-header"},[e("h4",[t._v("工单记录")]),e("span",{staticClass:"count-badge"},[t._v(t._s(t.currentUserDetail.orders.length)+"个")])]),e("div",{staticClass:"orders-list"},t._l(t.currentUserDetail.orders,(function(s){return e("div",{key:s.id,staticClass:"order-item"},[e("div",{staticClass:"order-info"},[e("div",{staticClass:"order-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"order-meta"},[e("span",{staticClass:"order-type"},[t._v(t._s(s.type))]),e("span",{staticClass:"order-time"},[t._v(t._s(s.createTime))])])]),e("div",{staticClass:"order-status"},[e("el-tag",{attrs:{type:t.getOrderStatusType(s.status)}},[t._v(t._s(s.status))])],1)])})),0)]):t._e(),"payments"===t.activeDetailTab?e("div",{staticClass:"tab-content"},[e("div",{staticClass:"section-header"},[e("h4",[t._v("支付记录")]),e("span",{staticClass:"count-badge"},[t._v(t._s(t.currentUserDetail.payments.length)+"笔")])]),e("div",{staticClass:"payments-list"},t._l(t.currentUserDetail.payments,(function(s){return e("div",{key:s.id,staticClass:"payment-item"},[e("div",{staticClass:"payment-info"},[e("div",{staticClass:"payment-desc"},[t._v(t._s(s.description))]),e("div",{staticClass:"payment-time"},[t._v(t._s(s.time))])]),e("div",{staticClass:"payment-amount"},[e("span",{staticClass:"amount"},[t._v("¥"+t._s(s.amount))]),e("el-tag",{attrs:{type:"已支付"===s.status?"success":"warning",size:"mini"}},[t._v(" "+t._s(s.status)+" ")])],1)])})),0)]):t._e()])])]),e("div",{staticClass:"input-section"},[e("div",{staticClass:"toolbar"},[e("div",{staticClass:"tool-item emoji-tool"},[e("el-tooltip",{attrs:{content:"发送表情",placement:"top",effect:"dark"}},[e("el-button",{staticClass:"tool-btn",attrs:{type:"text",icon:"el-icon-sunny"},on:{click:function(e){return e.stopPropagation(),t.openEmji.apply(null,arguments)}}})],1),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isEmji,expression:"isEmji"}],staticClass:"emoji-panel"},[e("div",{staticClass:"emoji-grid"},t._l(t.emojiData,(function(s,a){return e("div",{key:a,staticClass:"emoji-item",on:{click:function(e){return t.getEmoji(s)}}},[t._v(" "+t._s(s)+" ")])})),0)])],1),e("div",{staticClass:"tool-item"},[e("el-tooltip",{attrs:{content:"发送图片",placement:"top",effect:"dark"}},[e("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":t.handleSuccess}},[e("el-button",{staticClass:"tool-btn",attrs:{type:"text",icon:"el-icon-picture"}})],1)],1)],1),e("div",{staticClass:"tool-item"},[e("el-tooltip",{attrs:{content:"发送文件",placement:"top",effect:"dark"}},[e("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":t.handleSuccess1,"before-upload":t.beforeUpload}},[e("el-button",{staticClass:"tool-btn",attrs:{type:"text",icon:"el-icon-folder"}})],1)],1)],1),e("div",{staticClass:"tool-item"},[e("el-tooltip",{attrs:{content:"标记代办",placement:"top",effect:"dark"}},[e("el-button",{staticClass:"tool-btn",attrs:{type:"text",icon:"el-icon-s-order"},on:{click:t.daiban}})],1)],1),e("div",{staticClass:"tool-item"},[e("el-tooltip",{attrs:{content:"查看工单",placement:"top",effect:"dark"}},[e("el-button",{staticClass:"tool-btn",attrs:{type:"text",icon:"el-icon-tickets"},on:{click:t.showgongdan}})],1)],1)]),e("div",{staticClass:"input-wrapper"},[e("el-input",{staticClass:"message-input",attrs:{type:"textarea",rows:3,placeholder:"输入消息...",resize:"none"},model:{value:t.textContent,callback:function(e){t.textContent=e},expression:"textContent"}})],1),e("div",{staticClass:"send-section"},[e("el-tooltip",{attrs:{content:"发送消息 (Enter)",placement:"top",effect:"dark"}},[e("el-button",{staticClass:"send-btn",attrs:{type:"primary",disabled:!t.textContent.trim()},on:{click:t.send}},[e("i",{staticClass:"el-icon-position"}),t._v(" 发送 ")])],1)],1)])])]),e("el-dialog",{attrs:{title:"图片预览",visible:t.isShowPopup,width:"60%",center:""},on:{"update:visible":function(e){t.isShowPopup=e}}},[e("div",{staticClass:"image-preview"},[e("img",{attrs:{src:t.imgUlr,alt:"预览图片"}})])]),e("el-drawer",{attrs:{title:"客户工单",visible:t.table,direction:"rtl",size:"40%"},on:{"update:visible":function(e){t.table=e}}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.gridData}},[e("el-table-column",{attrs:{property:"create_time",label:"下单日期",width:"150"}}),e("el-table-column",{attrs:{property:"title",label:"需求标题"}}),e("el-table-column",{attrs:{property:"desc",label:"需求描述"}}),e("el-table-column",{attrs:{property:"type_title",label:"下单类型"}}),e("el-table-column",{attrs:{property:"is_deal_title",label:"状态"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.editData(s.row.id)}}},[t._v("完成制作")])]}}])})],1)],1),e("el-dialog",{attrs:{title:"工单详情",visible:t.dialogFormVisible,width:"50%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm}},[e("el-form-item",{attrs:{label:"工单类型"}},[e("el-input",{attrs:{autocomplete:"off",readonly:""},model:{value:t.ruleForm.type_title,callback:function(e){t.$set(t.ruleForm,"type_title",e)},expression:"ruleForm.type_title"}})],1),e("el-form-item",{attrs:{label:"工单标题"}},[e("el-input",{attrs:{autocomplete:"off",readonly:""},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1),e("el-form-item",{attrs:{label:"工单描述"}},[e("el-input",{attrs:{autocomplete:"off",readonly:"",type:"textarea",rows:4},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1),e("el-form-item",{attrs:{label:"制作状态"}},[e("div",[e("el-radio",{attrs:{label:2},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("已完成")]),e("el-radio",{attrs:{label:1},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("处理中")])],1)]),2==t.ruleForm.is_deal&&2==t.ruleForm.type?e("el-form-item",{attrs:{label:"请上传文件",prop:"file_path"}},[e("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:t.ruleForm.file_path,callback:function(e){t.$set(t.ruleForm,"file_path",e)},expression:"ruleForm.file_path"}}),e("el-button-group",[e("el-button",[e("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":t.handleSuccess1}},[t._v(" 上传 ")])],1),t.ruleForm.file_path?e("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delImage(t.ruleForm.file_path,"file_path")}}},[t._v("删除")]):t._e()],1)],1):t._e(),2==t.ruleForm.is_deal&&2!=t.ruleForm.type?e("el-form-item",{attrs:{label:"内容回复"}},[e("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:t.ruleForm.content,callback:function(e){t.$set(t.ruleForm,"content",e)},expression:"ruleForm.content"}})],1):t._e()],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"file-icon"},[e("i",{staticClass:"el-icon-document"})])}];s("14d9");const l=["😀","😃","😄","😆","😅","🤣","🙂","😇","🤩","🤩","😘","😗","😚","😋","😛","🤪","🙁","😴","😷","🤮","🥵","🥵","🤢","🤢","😦","😰","😥","😱","😈","💀","🤡","👺","👿","😠","😡","😤","🥱","👻","👽","😺","😸","😹","😻","😼","😽","🙀","😿","🙈","🙉","🙊","💋","💌"];var n=l,c=function(){var t=this,e=t._self._c;return e("i",{class:0==t.isPlay?"el-icon-video-play":"el-icon-video-pause",staticStyle:{cursor:"pointer","margin-right":"10px","margin-top":"3px","font-size":"25px"},style:0==t.isPlay?"":"color: red;",attrs:{slot:"reference"},on:{click:t.autoPlay},slot:"reference"})},o=[],r={props:{recordFile:{type:String}},name:"audioplay",data(){return{isPlay:!1,myAuto:new Audio(this.recordFile)}},methods:{autoPlay(){this.isPlay=!this.isPlay,this.isPlay?(this.myAuto.play(),this.palyEnd()):(this.myAuto.pause(),this.palyEnd())},palyEnd(){this.myAuto.addEventListener("ended",()=>{this.isPlay=!1})}}},d=r,u=s("2877"),p=Object(u["a"])(d,c,o,!1,null,"51f04c58",null),m=p.exports;let g;var h={name:"chat",components:{audioplay:m},data(){return{userss:[],lvshiss:[],yuangongss:[],table:!1,gridData:"",ruleForm:"",dialogFormVisible:!1,emojiData:n,currentTab:"group",selectId:1,activeName:"first",search:"",active:!1,imgUlr:"",yon_id:0,id:0,isShowSeach:!1,type:"",lists:[],Names:"",isShowPopup:!1,textContent:"",selectId:0,lvshiid:"4",pic_path:"",file_path:"",list:[],timer:"",users:[],quns:[],quliaoIndex:0,quliaos:[],isEmji:!1,title:"",yuanshiquns:[],yuanshiusers:[],showMemberPanel:!1,memberData:{users:[],lawyers:[],staff:[]},showUserDetail:!0,activeDetailTab:"info",currentUserDetail:{name:"",type:"",avatar:"",phone:"",idCard:"",registerTime:"",lastLogin:"",status:"",debtors:[],documents:[],orders:[],payments:[]}}},methods:{editData(t){0!=t?this.getInfo(t):this.ruleForm={title:"",desc:""}},handleSucces1s(t){200==t.code?(this.$message.success("上传成功"),this.ruleForm["file_path"]=t.data.url):this.$message.error(t.msg)},getInfo(t){let e=this;this.getRequest("/gongdan/read?id="+t).then(t=>{200==t.code?(e.ruleForm=t.data,e.dialogFormVisible=!0):e.$message({type:"error",message:t.msg})})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest("/gongdan/save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},showgongdan(){let t=this.quns[this.quliaoIndex]["uid"];g.table=!0,g.postRequest("/chat/gongdanList",{uid:t}).then(t=>{200==t.code&&(g.gridData=t.data)})},showDaiban(t){this.currentTab="2"===t?"group":"todo",g.postRequest("/chat/getQun",{is_daiban:t}).then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1,g.getList())})},changeKeyword(t){let e=g.yuanshiquns,s=g.yuanshiusers,a=t.target.value;this.isShowSeach=a.length>0,g.quns=e.filter(t=>t.title.toLowerCase().includes(a.toLowerCase())),g.users=s.filter(t=>t.title.toLowerCase().includes(a.toLowerCase()))},daiban(){let t=this.quns[this.quliaoIndex]["id"],e=1==this.quns[this.quliaoIndex]["is_daiban"]?2:1;g.postRequest("/chat/daiban",{id:t,is_daiban:e}).then(t=>{200==t.code?(g.quns[this.quliaoIndex]["is_daiban"]=e,g.$message.success(t.msg)):g.$message.error(t.msg)})},openEmji(){g.isEmji=!g.isEmji,console.log("----------------------ww2w")},changeFile(t){g.type=t},openFile(t){window.open(t,"_blank")},openImg(t){g.imgUlr=t,g.isShowPopup=!0,console.log("----------",t)},beforeUpload(t){let e=t.type;if(console.log(e,"type"),"doc"==!t.type.split("/")[1]||"docx"==!t.type.split("/")[1]||"xls"==!t.type.split("/")[1]||"ppt"==!t.type.split("/")[1]||"pdf"==!t.type.split("/")[1]||"xlsx"==!t.type.split("/")[1]||"pptx"==!t.type.split("/")[1])return g.$message({showClose:!0,message:"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件",type:"error"}),!1},handleSuccess(t){let e=this;console.log(t),200==t.code?e.sendImg(t.data.url):e.$message.error(t.msg)},handleSuccess1(t,e){200==t.code?g.sendFile(t.data.url,e):g.$message({showClose:!0,message:"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件",type:"error"})},redSession(t){console.log("点击私聊:",t),g.selectId=t,g.quliaoIndex=-1,g.showMemberPanel=!1,g.loadTestMessages(),g.loadUserDetailData()},changeQun(t){console.log("点击群聊:",t),g.selectId=-1,g.quliaoIndex=t,g.quns[t].count=0,g.showMemberPanel=!1,g.loadTestMessages(),g.loadUserDetailData()},getEmoji(t){g.textContent+=t},change(t){g.search?g.isShowSeach=!0:g.isShowSeach=!1},del(){g.search="",g.isShowSeach=!1},handleScroll(t){0==g.$refs.list.scrollTop&&console.log("这里处理加载更多")},send(){g.sendMessage(g.textContent),g.textContent=""},getList(){if(-1!=g.selectId){let t=g.users[g.selectId].id;g.title=g.users[g.selectId].title,g.postRequest("/chat/chatList",{uid:t}).then(t=>{200==t.code&&t.data.length>0&&(g.list=t.data,g.$refs.list.scrollTop=g.$refs.list.scrollHeight),g.loading=!1})}else{let t=g.quns[g.quliaoIndex].id,e=1*g.quns[g.quliaoIndex].uid.length+1*g.quns[g.quliaoIndex].lvshi_id.length+1*g.quns[g.quliaoIndex].yuangong_id.length;g.id=t,console.log(g.id),g.title=g.quns[g.quliaoIndex].title+"("+e+")",g.postRequest("/chat/qunliaoList",{qun_id:t}).then(t=>{200==t.code&&(t.data.length>0?(g.list=t.data,g.$refs.list.scrollTop=g.$refs.list.scrollHeight):g.list=[],setTimeout(()=>this.$refs.list.scrollTop=this.$refs.list.scrollHeight,0)),g.loading=!1})}},getMoreList(){if(-1!=g.selectId){let t=g.users[g.selectId].id;g.title=g.users[g.selectId].title;let e=0;g.list.length>0&&(e=g.list[g.list.length-1].id,g.postRequest("/chat/getMoreQunList",{uid:t,id:e}).then(t=>{g.getQun1(),200==t.code&&t.data.length>0&&(g.list.push(t.data),setTimeout(()=>this.$refs.list.scrollTop=this.$refs.list.scrollHeight,1e3)),g.loading=!1}))}else{let t=g.quns[g.quliaoIndex].id,e=1*g.quns[g.quliaoIndex].lvshi_id.length+1*g.quns[g.quliaoIndex].yuangong_id.length+1;g.title=g.quns[g.quliaoIndex].title+"("+e+")";let s=0;g.list.length>0?(s=g.list[g.list.length-1].id,g.postRequest("/chat/getMoreQunList",{qun_id:t,id:s}).then(t=>{g.getQun1(),200==t.code&&(g.list.push(t.data),setTimeout(()=>g.$refs.list.scrollTop=g.$refs.list.scrollHeight,1e3)),g.loading=!1})):(s=1,g.postRequest("/chat/getMoreQunList",{qun_id:t,id:s}).then(t=>{g.getQun1(),200==t.code&&(g.list.push(t.data),setTimeout(()=>g.$refs.list.scrollTop=g.$refs.list.scrollHeight,1e3)),g.loading=!1}))}},sendMessage(t){if(-1!=g.selectId){let e=g.users[g.selectId].id,s=3;g.postRequest("/chat/sendMessage",{uid:e,direction:"left",type:"text",content:t,orther_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{g.quns[g.quliaoIndex].uid;let e=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{direction:"left",type:"text",content:t,qun_id:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},sendFile(t,e){if(-1!=g.selectId){let s=3;g.postRequest("/chat/sendMessage",{direction:"left",type:"file",content:t,orther_id:s,files:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{let s=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{direction:"left",type:"file",content:t,qun_id:s,files:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},sendImg(t){if(-1!=g.selectId){let e=g.users[g.selectId].id,s=3;g.postRequest("/chat/sendMessage",{uid:e,direction:"left",type:"image",content:t,orther_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{let e=g.quns[g.quliaoIndex].uid,s=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{uid:e,direction:"left",type:"image",content:t,qun_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},chatAllList(){g.postRequest("/chat/chatAllList").then(t=>{200==t.code&&(g.users=t.data,g.yuanshiusers=t.data)})},getQun(){g.postRequest("/chat/getQun").then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1,setTimeout(()=>{g.getList()},1500))})},getQun1(){g.postRequest("/chat/getQun").then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1)})},keyupSubmit(){let t=this;document.onkeydown=e=>{let s=window.event.keyCode;13===s&&t.send()}},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})},loadTestData(){g.quns=[{id:1,title:"法务团队群",desc:"最新消息：合同审核已完成",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",create_time:"09:30",count:3,uid:"1,2,3",lvshi_id:"1,2",yuangong_id:"1,2,3",is_daiban:2},{id:2,title:"客户服务群",desc:"张三：请问合同什么时候能完成？",pic_path:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",create_time:"昨天",count:1,uid:"4,5",lvshi_id:"3",yuangong_id:"4,5",is_daiban:2},{id:3,title:"紧急处理群",desc:"李四：这个案件需要加急处理",pic_path:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",create_time:"15:20",count:5,uid:"1,3,5",lvshi_id:"1",yuangong_id:"1,3",is_daiban:1}],g.users=[{id:1,title:"张三（客户）",content:"您好，请问我的合同审核进度如何？",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",time:"10:30"},{id:2,title:"李四（律师）",content:"合同已经审核完毕，请查收",pic_path:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",time:"09:15"},{id:3,title:"王五（调解员）",content:"调解会议安排在明天下午2点",pic_path:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",time:"昨天"},{id:4,title:"赵六（客户）",content:"谢谢您的帮助！",pic_path:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",time:"16:45"}],g.yuanshiquns=[...g.quns],g.yuanshiusers=[...g.users],g.selectId=-1,g.quliaoIndex=0,setTimeout(()=>{g.loadTestMessages()},500)},loadTestMessages(){if(console.log("加载测试消息, selectId:",g.selectId,"quliaoIndex:",g.quliaoIndex),-1!==g.selectId)g.title=g.users[g.selectId].title,g.loadPrivateMessages();else{const t=g.quns[g.quliaoIndex],e=t.uid.split(",").length+t.lvshi_id.split(",").length+t.yuangong_id.split(",").length;g.title=t.title+"("+e+")",g.loadGroupMessages()}g.$nextTick(()=>{g.$refs.list&&(g.$refs.list.scrollTop=g.$refs.list.scrollHeight)})},loadGroupMessages(){const t={0:[{id:1,create_time:"2024-01-22 09:00:00",yuangong_id:2,avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",title:"李律师",type:"text",content:"大家好，今天我们讨论一下最新的合同审核流程"},{id:2,create_time:"2024-01-22 09:05:00",yuangong_id:3,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",title:"张专员",type:"text",content:"好的，我这边已经准备好相关材料了"},{id:3,create_time:"2024-01-22 09:10:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"请大家查看一下这份合同模板"},{id:4,create_time:"2024-01-22 09:12:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"image",content:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"},{id:5,create_time:"2024-01-22 09:15:00",yuangong_id:2,avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",title:"李律师",type:"text",content:"这个模板看起来不错，我们可以在此基础上进行修改"}],1:[{id:1,create_time:"2024-01-22 10:00:00",yuangong_id:4,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",title:"张三",type:"text",content:"请问合同什么时候能完成？"},{id:2,create_time:"2024-01-22 10:05:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"您好，合同预计明天下午可以完成审核"},{id:3,create_time:"2024-01-22 10:10:00",yuangong_id:4,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",title:"张三",type:"text",content:"好的，谢谢！"}],2:[{id:1,create_time:"2024-01-22 15:00:00",yuangong_id:5,avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",title:"李四",type:"text",content:"这个案件需要加急处理"},{id:2,create_time:"2024-01-22 15:05:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"收到，我立即安排处理"},{id:3,create_time:"2024-01-22 15:10:00",yuangong_id:2,avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",title:"李律师",type:"text",content:"我这边也会配合加急处理"}]};g.list=t[g.quliaoIndex]||[],console.log("群聊消息加载完成:",g.list.length)},loadPrivateMessages(){const t={0:[{id:1,create_time:"2024-01-22 10:30:00",yuangong_id:4,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",title:"张三",type:"text",content:"您好，请问我的合同审核进度如何？"},{id:2,create_time:"2024-01-22 10:35:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"您好，您的合同正在审核中，预计今天下午可以完成"},{id:3,create_time:"2024-01-22 10:40:00",yuangong_id:4,avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",title:"张三",type:"text",content:"好的，谢谢您！"}],1:[{id:1,create_time:"2024-01-22 09:15:00",yuangong_id:2,avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",title:"李律师",type:"text",content:"合同已经审核完毕，请查收"},{id:2,create_time:"2024-01-22 09:20:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"收到，辛苦了！"}],2:[{id:1,create_time:"2024-01-21 16:00:00",yuangong_id:3,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"王调解员",type:"text",content:"调解会议安排在明天下午2点"},{id:2,create_time:"2024-01-21 16:05:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"好的，我会准时参加"}],3:[{id:1,create_time:"2024-01-22 16:45:00",yuangong_id:6,avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",title:"赵六",type:"text",content:"谢谢您的帮助！"},{id:2,create_time:"2024-01-22 16:50:00",yuangong_id:1,avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",title:"我",type:"text",content:"不客气，有问题随时联系我"}]};g.list=t[g.selectId]||[],console.log("私聊消息加载完成:",g.list.length)},toggleMemberPanel(){console.log("切换群成员面板"),g.showMemberPanel=!g.showMemberPanel,g.showMemberPanel&&g.loadMemberData()},loadMemberData(){console.log("加载群成员数据"),g.memberData={users:[{id:1,name:"张三",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:2,name:"李四",avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg"},{id:3,name:"王五",avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}],lawyers:[{id:4,name:"李律师",avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg"},{id:5,name:"陈律师",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}],staff:[{id:6,name:"王调解员",avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"},{id:7,name:"张专员",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:8,name:"赵专员",avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg"}]}},loadUserDetailData(){const t=g.quns[g.quliaoIndex];t&&(g.currentUserDetail={name:t.title.replace(/群$/,"")+"用户",type:"客户",avatar:t.pic_path,phone:"138****8888",idCard:"320***********1234",registerTime:"2024-01-15 10:30:00",lastLogin:"2024-01-22 09:15:00",status:"正常",debtors:[{id:1,name:"张三",amount:"50000.00",status:"overdue",statusText:"逾期"},{id:2,name:"李四",amount:"30000.00",status:"normal",statusText:"正常"},{id:3,name:"王五",amount:"80000.00",status:"settled",statusText:"已结清"}],documents:[{id:1,name:"身份证正面.jpg",type:"image",size:"2.5MB",uploadTime:"2024-01-15"},{id:2,name:"营业执照.pdf",type:"pdf",size:"1.8MB",uploadTime:"2024-01-16"},{id:3,name:"合同文件.docx",type:"word",size:"856KB",uploadTime:"2024-01-18"}],orders:[{id:1,title:"合同审核申请",type:"法律咨询",status:"已完成",createTime:"2024-01-20"},{id:2,title:"债务追讨服务",type:"债务处理",status:"处理中",createTime:"2024-01-21"},{id:3,title:"法律文书起草",type:"文书服务",status:"待处理",createTime:"2024-01-22"}],payments:[{id:1,description:"法律咨询费用",amount:"500.00",status:"已支付",time:"2024-01-20 14:30:00"},{id:2,description:"合同审核费用",amount:"800.00",status:"已支付",time:"2024-01-21 10:15:00"},{id:3,description:"债务处理服务费",amount:"1200.00",status:"待支付",time:"2024-01-22 09:00:00"}]})},getDocIcon(t){const e={image:"el-icon-picture",pdf:"el-icon-document",word:"el-icon-document",excel:"el-icon-s-grid",default:"el-icon-document"};return e[t]||e.default},getOrderStatusType(t){const e={"已完成":"success","处理中":"warning","待处理":"info","已取消":"danger"};return e[t]||"info"},downloadDoc(t){console.log("下载文档:",t.name),g.$message.success("开始下载: "+t.name)},previewDoc(t){console.log("预览文档:",t.name),g.$message.info("预览功能开发中...")}},beforeDestroy(){console.log("离开乐"),clearInterval(this.timer)},mounted(){g=this,g.loadTestData(),g.loadUserDetailData(),g.yon_id=1,g.keyupSubmit()}},v=h,b=(s("0b1c"),Object(u["a"])(v,a,i,!1,null,"72349a68",null));e["default"]=b.exports}}]);
//# sourceMappingURL=chunk-0ffa028e.214153fe.js.map