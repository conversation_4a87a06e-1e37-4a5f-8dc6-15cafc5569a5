{"version": 3, "sources": ["webpack:///./src/utils/fileUtils.js", "webpack:///./src/views/pages/archive/File.vue?795b", "webpack:///./src/views/pages/archive/File.vue", "webpack:///src/views/pages/archive/File.vue", "webpack:///./src/views/pages/archive/File.vue?df4f", "webpack:///./src/views/pages/archive/File.vue?5e9f"], "names": ["formatFileSize", "size", "toFixed", "render", "_vm", "this", "_c", "_self", "staticClass", "attrs", "on", "handleUpload", "_v", "selectedFiles", "length", "handleBatchArchive", "handleBatchDownload", "handleBatchDelete", "staticStyle", "fileList", "handleSelectionChange", "scopedSlots", "_u", "key", "fn", "scope", "class", "getFileIcon", "row", "fileType", "_s", "fileName", "$event", "handlePreview", "handleDownload", "handleDelete", "uploadDialogVisible", "uploadUrl", "beforeUpload", "handleProgress", "handleUploadSuccess", "handleUploadError", "uploadFileList", "slot", "previewDialogVisible", "isImage", "previewUrl", "isPdf", "isOffice", "staticRenderFns", "name", "data", "currentFile", "fileTypeMap", "computed", "mapGetters", "image", "includes", "pdf", "office", "created", "fetchFileList", "methods", "mockFiles", "id", "category", "uploadTime", "$message", "success", "error", "file", "isLt500M", "event", "console", "log", "percentage", "response", "generatePreviewUrl", "_response$data", "getRequest", "link", "document", "createElement", "href", "btoa", "download", "click", "$confirm", "type", "deleteRequest", "selection", "fileIds", "map", "postRequest", "for<PERSON>ach", "iconMap", "component"], "mappings": "2IAKO,SAASA,EAAeC,GAC7B,OAAIA,EAAO,KACFA,EAAO,KACLA,EAAO,SACRA,EAAO,MAAMC,QAAQ,GAAK,MACzBD,EAAO,YACRA,EAAO,SAAeC,QAAQ,GAAK,OAEnCD,EAAO,YAAsBC,QAAQ,GAAK,MAbtD,mC,oCCAA,W,yCCAA,IAAIC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQN,EAAIO,eAAe,CAACL,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIQ,GAAG,YAAYN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAU,UAAYL,EAAIS,cAAcC,QAAQJ,GAAG,CAAC,MAAQN,EAAIW,qBAAqB,CAACT,EAAG,IAAI,CAACE,YAAY,uBAAuBJ,EAAIQ,GAAG,YAAYN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAU,UAAYL,EAAIS,cAAcC,QAAQJ,GAAG,CAAC,MAAQN,EAAIY,sBAAsB,CAACV,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIQ,GAAG,YAAYN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,SAAS,UAAYL,EAAIS,cAAcC,QAAQJ,GAAG,CAAC,MAAQN,EAAIa,oBAAoB,CAACX,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIQ,GAAG,aAAa,IAAI,GAAGN,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,WAAW,CAACY,YAAY,CAAC,MAAQ,QAAQT,MAAM,CAAC,KAAOL,EAAIe,UAAUT,GAAG,CAAC,mBAAmBN,EAAIgB,wBAAwB,CAACd,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,YAAY,MAAQ,QAAQH,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,YAAY,OAAOY,YAAYjB,EAAIkB,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACnB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACoB,MAAMtB,EAAIuB,YAAYF,EAAMG,IAAIC,YAAYvB,EAAG,OAAO,CAACF,EAAIQ,GAAGR,EAAI0B,GAAGL,EAAMG,IAAIG,sBAAsBzB,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,KAAK,MAAQ,SAASH,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,KAAK,MAAQ,SAASH,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,OAAOY,YAAYjB,EAAIkB,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACrB,EAAIQ,GAAG,IAAIR,EAAI0B,GAAG1B,EAAIJ,eAAeyB,EAAMG,IAAI3B,OAAO,YAAYK,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,MAAQ,SAASH,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUY,YAAYjB,EAAIkB,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACnB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASsB,GAAQ,OAAO5B,EAAI6B,cAAcR,EAAMG,QAAQ,CAACxB,EAAIQ,GAAG,UAAUN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASsB,GAAQ,OAAO5B,EAAI8B,eAAeT,EAAMG,QAAQ,CAACxB,EAAIQ,GAAG,UAAUN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASsB,GAAQ,OAAO5B,EAAI+B,aAAaV,EAAMG,QAAQ,CAACxB,EAAIQ,GAAG,WAAW,WAAW,IAAI,GAAGN,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIgC,oBAAoB,MAAQ,SAAS1B,GAAG,CAAC,iBAAiB,SAASsB,GAAQ5B,EAAIgC,oBAAoBJ,KAAU,CAAC1B,EAAG,YAAY,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,GAAG,SAAW,GAAG,OAASL,EAAIiC,UAAU,gBAAgBjC,EAAIkC,aAAa,cAAclC,EAAImC,eAAe,aAAanC,EAAIoC,oBAAoB,WAAWpC,EAAIqC,kBAAkB,YAAYrC,EAAIsC,iBAAiB,CAACpC,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACJ,EAAIQ,GAAG,aAAaN,EAAG,KAAK,CAACF,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACE,YAAY,iBAAiBC,MAAM,CAAC,KAAO,OAAOkC,KAAK,OAAO,CAACvC,EAAIQ,GAAG,gCAAgC,GAAGN,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIwC,qBAAqB,MAAQ,MAAM,YAAa,GAAMlC,GAAG,CAAC,iBAAiB,SAASsB,GAAQ5B,EAAIwC,qBAAqBZ,KAAU,CAAC1B,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAAEJ,EAAIyC,QAASvC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACG,MAAM,CAAC,IAAML,EAAI0C,WAAW,IAAM,YAAa1C,EAAI2C,MAAOzC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACG,MAAM,CAAC,IAAML,EAAI0C,WAAW,MAAQ,OAAO,OAAS,aAAc1C,EAAI4C,SAAU1C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,SAAS,CAACG,MAAM,CAAC,IAAML,EAAI0C,WAAW,MAAQ,OAAO,OAAS,aAAaxC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACF,EAAIQ,GAAG,+BAA+B,IAEvrHqC,EAAkB,G,gDCoJP,GACfC,KAAA,cACAC,OACA,OACAhC,SAAA,GACAN,cAAA,GACAuB,qBAAA,EACAQ,sBAAA,EACAF,eAAA,GACAI,WAAA,GACAM,YAAA,KACAf,UAAA,kBAEAgB,YAAA,CACA,uCACA,YACA,gDACA,kBACA,8BAIAC,SAAA,IACAC,eAAA,CACA,aAEAV,UACA,YAAAO,aAAA,KAAAC,YAAAG,MAAAC,SAAA,KAAAL,YAAAvB,WAEAkB,QACA,YAAAK,aAAA,KAAAC,YAAAK,IAAAD,SAAA,KAAAL,YAAAvB,WAEAmB,WACA,YAAAI,aAAA,KAAAC,YAAAM,OAAAF,SAAA,KAAAL,YAAAvB,YAGA+B,UACA,KAAAC,iBAEAC,QAAA,CAEA,sBACA,IAEA,MAAAC,EAAA,CACA,CACAC,GAAA,EACAjC,SAAA,WACAF,SAAA,MACAoC,SAAA,OACAhE,KAAA,OACAiE,WAAA,uBAEA,CACAF,GAAA,EACAjC,SAAA,YACAF,SAAA,OACAoC,SAAA,OACAhE,KAAA,MACAiE,WAAA,uBAEA,CACAF,GAAA,EACAjC,SAAA,WACAF,SAAA,MACAoC,SAAA,OACAhE,KAAA,KACAiE,WAAA,uBAEA,CACAF,GAAA,EACAjC,SAAA,WACAF,SAAA,MACAoC,SAAA,OACAhE,KAAA,OACAiE,WAAA,uBAEA,CACAF,GAAA,EACAjC,SAAA,aACAF,SAAA,OACAoC,SAAA,OACAhE,KAAA,MACAiE,WAAA,wBAIA,KAAA/C,SAAA4C,EACA,KAAAI,SAAAC,QAAA,YACA,MAAAC,GACA,KAAAF,SAAAE,MAAA,cAIA1D,eACA,KAAAyB,qBAAA,GAGAE,aAAAgC,GACA,MAAAC,EAAAD,EAAArE,KAAA,cACA,QAAAsE,IACA,KAAAJ,SAAAE,MAAA,oBACA,IAKA9B,eAAAiC,EAAAF,GACAG,QAAAC,IAAA,QAAAJ,EAAAK,aAGAnC,oBAAAoC,EAAAN,GACA,KAAAH,SAAAC,QAAA,UACA,KAAAhC,qBAAA,EACA,KAAAyB,iBAGApB,oBACA,KAAA0B,SAAAE,MAAA,WAGA,oBAAAC,GACA,KAAAlB,YAAAkB,EACA,KAAA1B,sBAAA,EAEA,KAAAE,iBAAA,KAAA+B,mBAAAP,IAGA,yBAAAA,GACA,QAAAQ,EACA,MAAAF,QAAAG,eAAA,oBAAAT,EAAAN,IACA,eAAAc,EAAAF,EAAAzB,YAAA,IAAA2B,OAAA,EAAAA,EAAAhC,aAAA,sDACA,MAAAuB,GACA,8DAIA,qBAAAC,GACA,IAEA,MAAAU,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA,wCAAAC,KAAAd,EAAAvC,UACAiD,EAAAK,SAAAf,EAAAvC,SACAiD,EAAAM,QACA,KAAAnB,SAAAC,QAAA,QACA,MAAAC,GACA,KAAAF,SAAAE,MAAA,YAIA,mBAAAC,GACA,UACA,KAAAiB,SAAA,kBACAC,KAAA,kBAEAC,eAAA,kBAAAnB,EAAAN,IACA,KAAAG,SAAAC,QAAA,QACA,KAAAP,gBACA,MAAAQ,GACA,WAAAA,GACA,KAAAF,SAAAE,MAAA,UAKAjD,sBAAAsE,GACA,KAAA7E,cAAA6E,GAGA,2BACA,QAAA7E,cAAAC,OACA,UACA,KAAAyE,SAAA,oBACAC,KAAA,YAEA,MAAAG,EAAA,KAAA9E,cAAA+E,IAAAtB,KAAAN,UACA6B,eAAA,gCAAAF,YACA,KAAAxB,SAAAC,QAAA,QACA,KAAAP,gBACA,MAAAQ,GACA,WAAAA,GACA,KAAAF,SAAAE,MAAA,UAKA,4BACA,QAAAxD,cAAAC,OACA,IACA,KAAAqD,SAAAC,QAAA,UAEA,KAAAvD,cAAAiF,QAAAxB,IACA,MAAAU,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA,wCAAAC,KAAAd,EAAAvC,UACAiD,EAAAK,SAAAf,EAAAvC,SACAiD,EAAAM,UAEA,MAAAjB,GACA,KAAAF,SAAAE,MAAA,UAIA,0BACA,QAAAxD,cAAAC,OACA,UACA,KAAAyE,SAAA,oBACAC,KAAA,YAEA,MAAAG,EAAA,KAAA9E,cAAA+E,IAAAtB,KAAAN,UACAyB,eAAA,wBAAAE,YACA,KAAAxB,SAAAC,QAAA,QACA,KAAAP,gBACA,MAAAQ,GACA,WAAAA,GACA,KAAAF,SAAAE,MAAA,UAKA1C,YAAAE,GACA,MAAAkE,EAAA,CACA,uBACA,uBACA,wBACA,qBACA,sBACA,uBACA,wBACA,uBACA,wBACA,0BAEA,OAAAA,EAAAlE,IAAA,oBAGA7B,eAAAC,GACA,OAAAD,eAAAC,MClY2W,I,wBCQvW+F,EAAY,eACd,EACA7F,EACA8C,GACA,EACA,KACA,WACA,MAIa,aAAA+C,E", "file": "js/chunk-4ebce2ce.6c500886.js", "sourcesContent": ["/**\r\n * 格式化文件大小\r\n * @param {number} size 文件大小（字节）\r\n * @returns {string} 格式化后的文件大小\r\n */\r\nexport function formatFileSize(size) {\r\n  if (size < 1024) {\r\n    return size + ' B'\r\n  } else if (size < 1024 * 1024) {\r\n    return (size / 1024).toFixed(2) + ' KB'\r\n  } else if (size < 1024 * 1024 * 1024) {\r\n    return (size / (1024 * 1024)).toFixed(2) + ' MB'\r\n  } else {\r\n    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'\r\n  }\r\n}\r\n\r\n/**\r\n * 获取文件类型\r\n * @param {string} fileName 文件名\r\n * @returns {string} 文件类型\r\n */\r\nexport function getFileType(fileName) {\r\n  const extension = fileName.split('.').pop().toLowerCase()\r\n  const typeMap = {\r\n    // 图片\r\n    'jpg': 'image',\r\n    'jpeg': 'image',\r\n    'png': 'image',\r\n    'gif': 'image',\r\n    'bmp': 'image',\r\n    // 文档\r\n    'pdf': 'pdf',\r\n    'doc': 'doc',\r\n    'docx': 'doc',\r\n    'xls': 'xls',\r\n    'xlsx': 'xls',\r\n    'ppt': 'ppt',\r\n    'pptx': 'ppt',\r\n    // 文本\r\n    'txt': 'text',\r\n    'md': 'text',\r\n    // 压缩文件\r\n    'zip': 'archive',\r\n    'rar': 'archive',\r\n    '7z': 'archive'\r\n  }\r\n  return typeMap[extension] || 'other'\r\n}\r\n\r\n/**\r\n * 获取文件分类\r\n * @param {string} fileName 文件名\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 文件分类\r\n */\r\nexport function getFileCategory(fileName, fileType) {\r\n  // 根据文件名和类型智能分类\r\n  const name = fileName.toLowerCase()\r\n  \r\n  // 案件文书\r\n  if (name.includes('案件') || name.includes('诉讼') || name.includes('判决')) {\r\n    return '案件文书'\r\n  }\r\n  \r\n  // 合同文件\r\n  if (name.includes('合同') || name.includes('协议') || name.includes('契约')) {\r\n    return '合同文件'\r\n  }\r\n  \r\n  // 咨询记录\r\n  if (name.includes('咨询') || name.includes('记录') || name.includes('纪要')) {\r\n    return '咨询记录'\r\n  }\r\n  \r\n  // 根据文件类型分类\r\n  switch (fileType) {\r\n    case 'image':\r\n      return '图片文件'\r\n    case 'pdf':\r\n      return 'PDF文档'\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'Word文档'\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'Excel文档'\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'PPT文档'\r\n    case 'text':\r\n      return '文本文件'\r\n    case 'archive':\r\n      return '压缩文件'\r\n    default:\r\n      return '其他文件'\r\n  }\r\n}\r\n\r\n/**\r\n * 检查文件是否可预览\r\n * @param {string} fileType 文件类型\r\n * @returns {boolean} 是否可预览\r\n */\r\nexport function isPreviewable(fileType) {\r\n  const previewableTypes = ['image', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']\r\n  return previewableTypes.includes(fileType)\r\n}\r\n\r\n/**\r\n * 生成文件预览URL\r\n * @param {string} fileId 文件ID\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 预览URL\r\n */\r\nexport function generatePreviewUrl(fileId, fileType) {\r\n  const baseUrl = process.env.VUE_APP_BASE_API\r\n  if (fileType === 'image') {\r\n    return `${baseUrl}/archive/preview/image/${fileId}`\r\n  } else if (fileType === 'pdf') {\r\n    return `${baseUrl}/archive/preview/pdf/${fileId}`\r\n  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileType)) {\r\n    return `${baseUrl}/archive/preview/office/${fileId}`\r\n  }\r\n  return ''\r\n} ", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./File.vue?vue&type=style&index=0&id=228d4396&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"archive-container\"},[_c('div',{staticClass:\"operation-bar\"},[_c('el-button-group',[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleUpload}},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 上传文件 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"disabled\":!_vm.selectedFiles.length},on:{\"click\":_vm.handleBatchArchive}},[_c('i',{staticClass:\"el-icon-folder-add\"}),_vm._v(\" 批量归档 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"disabled\":!_vm.selectedFiles.length},on:{\"click\":_vm.handleBatchDownload}},[_c('i',{staticClass:\"el-icon-download\"}),_vm._v(\" 批量下载 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"disabled\":!_vm.selectedFiles.length},on:{\"click\":_vm.handleBatchDelete}},[_c('i',{staticClass:\"el-icon-delete\"}),_vm._v(\" 批量删除 \")])],1)],1),_c('div',{staticClass:\"file-list-container\"},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.fileList},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_c('el-table-column',{attrs:{\"prop\":\"fileName\",\"label\":\"文件名\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"file-name-cell\"},[_c('i',{class:_vm.getFileIcon(scope.row.fileType)}),_c('span',[_vm._v(_vm._s(scope.row.fileName))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"fileType\",\"label\":\"类型\",\"width\":\"100\"}}),_c('el-table-column',{attrs:{\"prop\":\"category\",\"label\":\"分类\",\"width\":\"120\"}}),_c('el-table-column',{attrs:{\"prop\":\"size\",\"label\":\"大小\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(_vm.formatFileSize(scope.row.size))+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"uploadTime\",\"label\":\"上传时间\",\"width\":\"180\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"240\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.handlePreview(scope.row)}}},[_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"success\"},on:{\"click\":function($event){return _vm.handleDownload(scope.row)}}},[_vm._v(\" 下载 \")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"danger\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row)}}},[_vm._v(\" 删除 \")])],1)]}}])})],1)],1),_c('el-dialog',{attrs:{\"title\":\"文件上传\",\"visible\":_vm.uploadDialogVisible,\"width\":\"500px\"},on:{\"update:visible\":function($event){_vm.uploadDialogVisible=$event}}},[_c('el-upload',{staticClass:\"upload-demo\",attrs:{\"drag\":\"\",\"multiple\":\"\",\"action\":_vm.uploadUrl,\"before-upload\":_vm.beforeUpload,\"on-progress\":_vm.handleProgress,\"on-success\":_vm.handleUploadSuccess,\"on-error\":_vm.handleUploadError,\"file-list\":_vm.uploadFileList}},[_c('i',{staticClass:\"el-icon-upload\"}),_c('div',{staticClass:\"el-upload__text\"},[_vm._v(\"将文件拖到此处，或\"),_c('em',[_vm._v(\"点击上传\")])]),_c('div',{staticClass:\"el-upload__tip\",attrs:{\"slot\":\"tip\"},slot:\"tip\"},[_vm._v(\" 支持任意格式文件，单个文件不超过500MB \")])])],1),_c('el-dialog',{attrs:{\"title\":\"文件预览\",\"visible\":_vm.previewDialogVisible,\"width\":\"80%\",\"fullscreen\":true},on:{\"update:visible\":function($event){_vm.previewDialogVisible=$event}}},[_c('div',{staticClass:\"preview-container\"},[(_vm.isImage)?_c('div',{staticClass:\"image-preview\"},[_c('img',{attrs:{\"src\":_vm.previewUrl,\"alt\":\"预览图片\"}})]):(_vm.isPdf)?_c('div',{staticClass:\"pdf-preview\"},[_c('iframe',{attrs:{\"src\":_vm.previewUrl,\"width\":\"100%\",\"height\":\"600px\"}})]):(_vm.isOffice)?_c('div',{staticClass:\"office-preview\"},[_c('iframe',{attrs:{\"src\":_vm.previewUrl,\"width\":\"100%\",\"height\":\"600px\"}})]):_c('div',{staticClass:\"other-preview\"},[_c('p',[_vm._v(\"该文件类型暂不支持预览，请下载后查看\")])])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"archive-container\">\r\n    <!-- 顶部操作栏 -->\r\n    <div class=\"operation-bar\">\r\n      <el-button-group>\r\n        <el-button type=\"primary\" @click=\"handleUpload\">\r\n          <i class=\"el-icon-upload\"></i> 上传文件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleBatchArchive\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-folder-add\"></i> 批量归档\r\n        </el-button>\r\n        <el-button type=\"warning\" @click=\"handleBatchDownload\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-download\"></i> 批量下载\r\n        </el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchDelete\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-delete\"></i> 批量删除\r\n        </el-button>\r\n      </el-button-group>\r\n    </div>\r\n\r\n    <!-- 文件列表区域 -->\r\n    <div class=\"file-list-container\">\r\n      <el-table\r\n        :data=\"fileList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\">\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"55\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileName\"\r\n          label=\"文件名\"\r\n          min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"file-name-cell\">\r\n              <i :class=\"getFileIcon(scope.row.fileType)\"></i>\r\n              <span>{{ scope.row.fileName }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileType\"\r\n          label=\"类型\"\r\n          width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"category\"\r\n          label=\"分类\"\r\n          width=\"120\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"size\"\r\n          label=\"大小\"\r\n          width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatFileSize(scope.row.size) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"uploadTime\"\r\n          label=\"上传时间\"\r\n          width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          width=\"240\"\r\n          align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handlePreview(scope.row)\">\r\n                预览\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleDownload(scope.row)\">\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\">\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 文件上传对话框 -->\r\n    <el-dialog\r\n      title=\"文件上传\"\r\n      :visible.sync=\"uploadDialogVisible\"\r\n      width=\"500px\">\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        multiple\r\n        :action=\"uploadUrl\"\r\n        :before-upload=\"beforeUpload\"\r\n        :on-progress=\"handleProgress\"\r\n        :on-success=\"handleUploadSuccess\"\r\n        :on-error=\"handleUploadError\"\r\n        :file-list=\"uploadFileList\">\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          支持任意格式文件，单个文件不超过500MB\r\n        </div>\r\n      </el-upload>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"80%\"\r\n      :fullscreen=\"true\">\r\n      <div class=\"preview-container\">\r\n        <!-- 图片预览 -->\r\n        <div v-if=\"isImage\" class=\"image-preview\">\r\n          <img :src=\"previewUrl\" alt=\"预览图片\">\r\n        </div>\r\n        <!-- PDF预览 -->\r\n        <div v-else-if=\"isPdf\" class=\"pdf-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- Office文档预览 -->\r\n        <div v-else-if=\"isOffice\" class=\"office-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- 其他文件类型 -->\r\n        <div v-else class=\"other-preview\">\r\n          <p>该文件类型暂不支持预览，请下载后查看</p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { formatFileSize, getFileType } from '@/utils/fileUtils'\r\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ArchiveFile',\r\n  data() {\r\n    return {\r\n      fileList: [],\r\n      selectedFiles: [],\r\n      uploadDialogVisible: false,\r\n      previewDialogVisible: false,\r\n      uploadFileList: [],\r\n      previewUrl: '',\r\n      currentFile: null,\r\n      uploadUrl: '/archive/upload',\r\n      // 文件类型映射\r\n      fileTypeMap: {\r\n        'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp'],\r\n        'pdf': ['pdf'],\r\n        'office': ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],\r\n        'text': ['txt', 'md'],\r\n        'archive': ['zip', 'rar', '7z']\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'userRole'\r\n    ]),\r\n    isImage() {\r\n      return this.currentFile && this.fileTypeMap.image.includes(this.currentFile.fileType)\r\n    },\r\n    isPdf() {\r\n      return this.currentFile && this.fileTypeMap.pdf.includes(this.currentFile.fileType)\r\n    },\r\n    isOffice() {\r\n      return this.currentFile && this.fileTypeMap.office.includes(this.currentFile.fileType)\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchFileList()\r\n  },\r\n  methods: {\r\n    // 获取文件列表\r\n    async fetchFileList() {\r\n      try {\r\n        // 模拟归档文件数据\r\n        const mockFiles = [\r\n          {\r\n            id: 1,\r\n            fileName: \"合同模板.pdf\",\r\n            fileType: \"pdf\",\r\n            category: \"合同文件\",\r\n            size: 1024000,\r\n            uploadTime: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            fileName: \"案件资料.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 512000,\r\n            uploadTime: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            fileName: \"咨询记录.txt\",\r\n            fileType: \"txt\",\r\n            category: \"咨询记录\",\r\n            size: 8192,\r\n            uploadTime: \"2024-01-13 16:45:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            fileName: \"证据材料.jpg\",\r\n            fileType: \"jpg\",\r\n            category: \"案件文书\",\r\n            size: 2048000,\r\n            uploadTime: \"2024-01-12 09:15:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            fileName: \"法律意见书.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 768000,\r\n            uploadTime: \"2024-01-11 16:30:00\"\r\n          }\r\n        ];\r\n        \r\n        this.fileList = mockFiles;\r\n        this.$message.success('文件列表加载成功');\r\n      } catch (error) {\r\n        this.$message.error('获取文件列表失败');\r\n      }\r\n    },\r\n    // 显示上传对话框\r\n    handleUpload() {\r\n      this.uploadDialogVisible = true\r\n    },\r\n    // 文件上传前检查\r\n    beforeUpload(file) {\r\n      const isLt500M = file.size / 1024 / 1024 < 500\r\n      if (!isLt500M) {\r\n        this.$message.error('文件大小不能超过 500MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    // 处理上传进度\r\n    handleProgress(event, file) {\r\n      console.log('上传进度：', file.percentage)\r\n    },\r\n    // 处理上传成功\r\n    handleUploadSuccess(response, file) {\r\n      this.$message.success('文件上传成功')\r\n      this.uploadDialogVisible = false\r\n      this.fetchFileList()\r\n    },\r\n    // 处理上传失败\r\n    handleUploadError() {\r\n      this.$message.error('文件上传失败')\r\n    },\r\n    // 处理文件预览\r\n    async handlePreview(file) {\r\n      this.currentFile = file\r\n      this.previewDialogVisible = true\r\n      // 根据文件类型生成预览URL\r\n      this.previewUrl = await this.generatePreviewUrl(file)\r\n    },\r\n    // 生成预览URL\r\n    async generatePreviewUrl(file) {\r\n      try {\r\n        const response = await getRequest(`/archive/preview/${file.id}`)\r\n        return response.data?.previewUrl || 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      } catch (error) {\r\n        return 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      }\r\n    },\r\n    // 处理文件下载\r\n    async handleDownload(file) {\r\n      try {\r\n        // 创建一个虚拟的下载链接\r\n        const link = document.createElement('a')\r\n        link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n        link.download = file.fileName\r\n        link.click()\r\n        this.$message.success('开始下载')\r\n      } catch (error) {\r\n        this.$message.error('文件下载失败')\r\n      }\r\n    },\r\n    // 处理文件删除\r\n    async handleDelete(file) {\r\n      try {\r\n        await this.$confirm('确认删除该文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        await deleteRequest(`/archive/files/${file.id}`)\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 处理批量操作\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n    },\r\n    // 批量归档\r\n    async handleBatchArchive() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认归档选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await postRequest('/archive/files/batch/archive', { fileIds })\r\n        this.$message.success('归档成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('归档失败')\r\n        }\r\n      }\r\n    },\r\n    // 批量下载\r\n    async handleBatchDownload() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        this.$message.success('开始批量下载')\r\n        // 模拟批量下载\r\n        this.selectedFiles.forEach(file => {\r\n          const link = document.createElement('a')\r\n          link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n          link.download = file.fileName\r\n          link.click()\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('下载失败')\r\n      }\r\n    },\r\n    // 批量删除\r\n    async handleBatchDelete() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认删除选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await deleteRequest('/archive/files/batch', { fileIds })\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'ppt': 'el-icon-document',\r\n        'pptx': 'el-icon-document',\r\n        'txt': 'el-icon-document',\r\n        'image': 'el-icon-picture',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n    // 格式化文件大小\r\n    formatFileSize(size) {\r\n      return formatFileSize(size)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-container {\r\n  padding: 20px;\r\n\r\n  .operation-bar {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .file-list-container {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n    \r\n    .el-table {\r\n      border: 1px solid #ebeef5;\r\n      border-radius: 4px;\r\n      \r\n      .el-table__header-wrapper {\r\n        .el-table__header {\r\n          th {\r\n            background-color: #fafafa;\r\n            color: #606266;\r\n            font-weight: 600;\r\n            border-bottom: 1px solid #ebeef5;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .el-table__body-wrapper {\r\n        .el-table__body {\r\n          tr {\r\n            &:hover {\r\n              background-color: #f5f7fa;\r\n            }\r\n            \r\n            td {\r\n              border-bottom: 1px solid #f0f0f0;\r\n              padding: 12px 0;\r\n              vertical-align: middle;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .file-name-cell {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    \r\n    .el-button {\r\n      margin: 0;\r\n      min-width: 60px;\r\n      height: 28px;\r\n      font-size: 12px;\r\n      padding: 5px 12px;\r\n      border-radius: 4px;\r\n      \r\n      &.el-button--mini {\r\n        padding: 5px 12px;\r\n      }\r\n    }\r\n    \r\n    .el-button + .el-button {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n\r\n  .preview-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 400px;\r\n\r\n    .image-preview {\r\n      img {\r\n        max-width: 100%;\r\n        max-height: 600px;\r\n      }\r\n    }\r\n\r\n    .pdf-preview, .office-preview {\r\n      width: 100%;\r\n      height: 600px;\r\n    }\r\n\r\n    .other-preview {\r\n      text-align: center;\r\n      color: #909399;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./File.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./File.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./File.vue?vue&type=template&id=228d4396&scoped=true\"\nimport script from \"./File.vue?vue&type=script&lang=js\"\nexport * from \"./File.vue?vue&type=script&lang=js\"\nimport style0 from \"./File.vue?vue&type=style&index=0&id=228d4396&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"228d4396\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}