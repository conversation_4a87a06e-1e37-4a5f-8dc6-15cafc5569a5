{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\audioplay.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\audioplay.vue", "mtime": 1748336508318}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\babel.config.js", "mtime": 1748336479385}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICByZWNvcmRGaWxlOiB7CiAgICAgIHR5cGU6IFN0cmluZwogICAgfQogIH0sCiAgbmFtZTogImF1ZGlvcGxheSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzUGxheTogZmFsc2UsCiAgICAgIG15QXV0bzogbmV3IEF1ZGlvKHRoaXMucmVjb3JkRmlsZSkKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBhdXRvUGxheSgpIHsKICAgICAgdGhpcy5pc1BsYXkgPSAhdGhpcy5pc1BsYXk7CiAgICAgIGlmICh0aGlzLmlzUGxheSkgewogICAgICAgIHRoaXMubXlBdXRvLnBsYXkoKTsKICAgICAgICB0aGlzLnBhbHlFbmQoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm15QXV0by5wYXVzZSgpOwogICAgICAgIHRoaXMucGFseUVuZCgpOwogICAgICB9CiAgICB9LAogICAgcGFseUVuZCgpIHsKICAgICAgdGhpcy5teUF1dG8uYWRkRXZlbnRMaXN0ZW5lcigiZW5kZWQiLCAoKSA9PiB7CiAgICAgICAgdGhpcy5pc1BsYXkgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["props", "recordFile", "type", "String", "name", "data", "isPlay", "myAuto", "Audio", "methods", "autoPlay", "play", "palyEnd", "pause", "addEventListener"], "sources": ["src/components/audioplay.vue"], "sourcesContent": ["<template>\r\n  <i\r\n    slot=\"reference\"\r\n    :style=\"isPlay == false ? '' : 'color: red;'\"\r\n    :class=\"isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause'\"\r\n    @click=\"autoPlay\"\r\n    style=\"\r\n      cursor: pointer;\r\n      margin-right: 10px;\r\n      margin-top: 3px;\r\n      font-size: 25px;\r\n    \"\r\n  ></i>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    recordFile: {\r\n      type: String,\r\n    },\r\n  },\r\n  name: \"audioplay\",\r\n  data() {\r\n    return {\r\n      isPlay: false,\r\n      myAuto: new Audio(this.recordFile),\r\n    };\r\n  },\r\n  methods: {\r\n    autoPlay() {\r\n      this.isPlay = !this.isPlay;\r\n      if (this.isPlay) {\r\n        this.myAuto.play();\r\n        this.palyEnd();\r\n      } else {\r\n        this.myAuto.pause();\r\n        this.palyEnd();\r\n      }\r\n    },\r\n    palyEnd() {\r\n      this.myAuto.addEventListener(\"ended\", () => {\r\n        this.isPlay = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped></style>\r\n"], "mappings": "AAgBA;EACAA,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,MAAA,MAAAC,KAAA,MAAAP,UAAA;IACA;EACA;EACAQ,OAAA;IACAC,SAAA;MACA,KAAAJ,MAAA,SAAAA,MAAA;MACA,SAAAA,MAAA;QACA,KAAAC,MAAA,CAAAI,IAAA;QACA,KAAAC,OAAA;MACA;QACA,KAAAL,MAAA,CAAAM,KAAA;QACA,KAAAD,OAAA;MACA;IACA;IACAA,QAAA;MACA,KAAAL,MAAA,CAAAO,gBAAA;QACA,KAAAR,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}