{"version": 3, "sources": ["webpack:///./src/components/UserDetail.vue?89c4", "webpack:///./src/components/DebtDetail.vue", "webpack:///src/components/DebtDetail.vue", "webpack:///./src/components/DebtDetail.vue?c3c7", "webpack:///./src/components/DebtDetail.vue?f065", "webpack:///./src/components/DebtDetail.vue?d2a9", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "attrs", "on", "exports", "_v", "slot", "getDebtStatusType", "_s", "getDebtStatusText", "info", "nickname", "name", "tel", "address", "formatMoney", "money", "back_money", "un_money", "ctime", "utime", "cards", "length", "_l", "card", "index", "key", "$event", "showImage", "_e", "case_des", "images", "downloadFiles", "images_download", "image", "downloadSingleFile", "attach_path", "file", "class", "getFileIcon", "getFileExtension", "viewFile", "debttrans", "directives", "rawName", "value", "loading", "expression", "staticStyle", "background", "color", "scopedSlots", "_u", "fn", "scope", "row", "day", "au_id", "getProgressType", "type", "total_price", "content", "pay_type", "pay_time", "pay_order_type", "desc", "nativeOn", "preventDefault", "delData", "$index", "id", "dialogVisible", "show_image", "staticRenderFns", "props", "String", "Number", "required", "data", "watch", "immediate", "handler", "newId", "getInfo", "methods", "_this", "console", "log", "setTimeout", "testDebtData", "path", "rate", "imgs", "for<PERSON>ach", "link", "document", "createElement", "href", "download", "click", "location", "$store", "getters", "GET_TOKEN", "ruleForm", "imageUrl", "url", "filename", "window", "open", "ext", "toLowerCase", "iconMap", "split", "pop", "amount", "parseFloat", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "unMoney", "typeMap", "$confirm", "confirmButtonText", "cancelButtonText", "then", "splice", "$message", "success", "catch", "component", "company", "phone", "linkman", "linkphone", "yuangong_id", "start_time", "year", "headimg", "license", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "debts", "status", "viewDebtDetail", "testUserData", "debt"], "mappings": "kHAAA,W,oCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,SAAS,KAAO,UAAU,KAAO,oBAAoBC,GAAG,CAAC,MAAQN,EAAIO,UAAU,CAACP,EAAIQ,GAAG,eAAe,GAAGN,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACG,MAAM,CAAC,KAAOL,EAAIU,oBAAoB,KAAO,WAAW,CAACV,EAAIQ,GAAG,IAAIR,EAAIW,GAAGX,EAAIY,qBAAqB,QAAQ,KAAKV,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKC,UAAY,cAAcZ,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKE,MAAQ,cAAcb,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKG,KAAO,eAAe,GAAGd,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,KAAK,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKI,SAAW,eAAe,GAAGf,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAGX,EAAIkB,YAAYlB,EAAIa,KAAKM,eAAejB,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAGX,EAAIkB,YAAYlB,EAAIa,KAAKO,oBAAoBlB,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,WAAWN,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAGX,EAAIkB,YAAYlB,EAAIa,KAAKQ,mBAAmB,GAAGnB,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,KAAK,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKS,OAAS,cAAcpB,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,KAAK,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKU,OAAS,eAAe,IAAI,GAAIvB,EAAIa,KAAKW,OAASxB,EAAIa,KAAKW,MAAMC,OAAS,EAAGvB,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,eAAeN,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI0B,GAAI1B,EAAIa,KAAKW,OAAO,SAASG,EAAKC,GAAO,OAAO1B,EAAG,MAAM,CAAC2B,IAAID,EAAMxB,YAAY,eAAeE,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAI+B,UAAUJ,MAAS,CAACzB,EAAG,WAAW,CAACE,YAAY,gBAAgBC,MAAM,CAAC,IAAMsB,EAAK,IAAM,UAAU,CAACzB,EAAG,MAAM,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,SAASI,KAAK,SAAS,CAACP,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAa,IAAViB,EAAc,QAAU,SAAS,QAAQ,MAAK,KAAK5B,EAAIgC,KAAK9B,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACF,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKoB,UAAY,iBAAkBjC,EAAIa,KAAKqB,QAAUlC,EAAIa,KAAKqB,OAAOT,OAAS,EAAGvB,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,YAAY,CAACE,YAAY,gBAAgBC,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAImC,cAAcnC,EAAIa,KAAKuB,oBAAoB,CAACpC,EAAIQ,GAAG,aAAa,GAAGN,EAAG,MAAM,CAACE,YAAY,wBAAwBJ,EAAI0B,GAAI1B,EAAIa,KAAKqB,QAAQ,SAASG,EAAMT,GAAO,OAAO1B,EAAG,MAAM,CAAC2B,IAAID,EAAMxB,YAAY,uBAAuB,CAACF,EAAG,WAAW,CAACE,YAAY,iBAAiBC,MAAM,CAAC,IAAMgC,EAAM,mBAAmBrC,EAAIa,KAAKqB,OAAO,IAAM,UAAU,CAAChC,EAAG,MAAM,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,SAASI,KAAK,SAAS,CAACP,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAIsC,mBAAmBD,EAAO,aAAYT,EAAQ,OAAQ,CAAC5B,EAAIQ,GAAG,WAAW,IAAI,MAAK,KAAKR,EAAIgC,KAAMhC,EAAIa,KAAK0B,aAAevC,EAAIa,KAAK0B,YAAYd,OAAS,EAAGvB,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,YAAYN,EAAG,MAAM,CAACE,YAAY,uBAAuBJ,EAAI0B,GAAI1B,EAAIa,KAAK0B,aAAa,SAASC,EAAKZ,GAAO,OAAQY,EAAMtC,EAAG,MAAM,CAAC2B,IAAID,EAAMxB,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,YAAYqC,MAAMzC,EAAI0C,YAAYF,KAAQtC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,KAAKR,EAAIW,GAAGiB,EAAQ,MAAM1B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAI2C,iBAAiBH,WAActC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQ,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAI4C,SAASJ,MAAS,CAACxC,EAAIQ,GAAG,UAAUN,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAIsC,mBAAmBE,EAAM,SAAQZ,EAAQ,OAAQ,CAAC5B,EAAIQ,GAAG,WAAW,KAAKR,EAAIgC,QAAO,KAAKhC,EAAIgC,KAAK9B,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,MAAMR,EAAIW,GAAGX,EAAIa,KAAKgC,UAAY7C,EAAIa,KAAKgC,UAAUpB,OAAS,GAAG,aAAavB,EAAG,WAAW,CAAC4C,WAAW,CAAC,CAAC/B,KAAK,UAAUgC,QAAQ,YAAYC,MAAOhD,EAAIiD,QAASC,WAAW,YAAY9C,YAAY,kBAAkB+C,YAAY,CAAC,MAAQ,QAAQ9C,MAAM,CAAC,KAAOL,EAAIa,KAAKgC,UAAU,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACO,WAAW,UAAUC,MAAM,aAAa,CAACnD,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,MAAM,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIC,KAAK,YAAYzD,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIpC,OAAO,YAAYpB,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,UAAU,CAACL,EAAIQ,GAAGR,EAAIW,GAAG8C,EAAMC,IAAIE,iBAAiB1D,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,SAAS,CAACG,MAAM,CAAC,KAAOL,EAAI6D,gBAAgBJ,EAAMC,IAAII,MAAM,KAAO,UAAU,CAAC9D,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAII,MAAM,cAAc5D,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAAEA,EAAMC,IAAIK,aAAyC,MAA1BN,EAAMC,IAAIK,YAAqB7D,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,KAAKR,EAAIW,GAAG8C,EAAMC,IAAIK,aAAa,OAAO7D,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIQ,GAAG,cAAcN,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,UAAU,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACzD,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIM,SAAW,KAAK,YAAY9D,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAAEA,EAAMC,IAAItC,YAAuC,MAAzBqC,EAAMC,IAAItC,WAAoBlB,EAAG,OAAO,CAACE,YAAY,sBAAsB,CAACJ,EAAIQ,GAAG,KAAKR,EAAIW,GAAG8C,EAAMC,IAAItC,YAAY,OAAOlB,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIQ,GAAG,cAAcN,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,SAAS,CAACG,MAAM,CAAC,KAA8B,QAAvBoD,EAAMC,IAAIO,SAAqB,UAAY,UAAU,KAAO,UAAU,CAACjE,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIO,UAAY,OAAO,cAAc/D,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACzD,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIQ,UAAY,KAAK,YAAYhE,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,iBAAiB,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACzD,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIS,gBAAkB,KAAK,YAAYjE,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,YAAY,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAGR,EAAIW,GAAG8C,EAAMC,IAAIU,gBAAgBlE,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAMiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,OAAO,KAAO,SAASgE,SAAS,CAAC,MAAQ,SAASvC,GAAgC,OAAxBA,EAAOwC,iBAAwBtE,EAAIuE,QAAQd,EAAMe,OAAQf,EAAMC,IAAIe,OAAO,CAACvE,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIQ,GAAG,kBAAkB,GAAKR,EAAIa,KAAKgC,WAA2C,IAA9B7C,EAAIa,KAAKgC,UAAUpB,OAAyHzB,EAAIgC,KAA/G9B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACF,EAAIQ,GAAG,eAAwB,GAAGN,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAI0E,cAAc,MAAQ,OAAOpE,GAAG,CAAC,iBAAiB,SAASwB,GAAQ9B,EAAI0E,cAAc5C,KAAU,CAAC5B,EAAG,WAAW,CAACiD,YAAY,CAAC,MAAQ,QAAQ9C,MAAM,CAAC,IAAML,EAAI2E,eAAe,IAAI,IAEtsUC,EAAkB,GCgUtB,GACA7D,KAAA,aACA8D,MAAA,CACAJ,GAAA,CACAX,KAAA,CAAAgB,OAAAC,QACAC,UAAA,IAGAC,OACA,OACApE,KAAA,CACAC,SAAA,GACAC,KAAA,GACAC,IAAA,GACAC,QAAA,GACAE,MAAA,GACAC,WAAA,GACAC,SAAA,GACAC,MAAA,GACAC,MAAA,GACAU,SAAA,GACAT,MAAA,GACAU,OAAA,GACAE,gBAAA,GACAG,YAAA,GACAM,UAAA,IAEAI,SAAA,EACAyB,eAAA,EACAC,WAAA,KAGAO,MAAA,CACAT,GAAA,CACAU,WAAA,EACAC,QAAAC,GACA,KAAAC,QAAAD,MAIAE,QAAA,CACAD,QAAAb,GACA,IAAAe,EAAA,KACAC,QAAAC,IAAA,eAAAjB,GACAe,EAAAvC,SAAA,EAGA0C,WAAA,KACA,MAAAC,EAAA,CACAnB,KACA3D,SAAA,KACAC,KAAA,QACAC,IAAA,cACAC,QAAA,iBACAE,MAAA,QACAC,WAAA,QACAC,SAAA,QACAC,MAAA,sBACAC,MAAA,sBACAU,SAAA,+BACAT,MAAA,CACA,mCACA,mCAEAU,OAAA,CACA,+BACA,+BACA,gCAEAE,gBAAA,CACA,CAAArB,KAAA,UAAA8E,KAAA,gCACA,CAAA9E,KAAA,UAAA8E,KAAA,iCAEAtD,YAAA,CACA,6BACA,kCAEAM,UAAA,CACA,CACA4B,GAAA,EACAd,IAAA,aACArC,MAAA,sBACAsC,MAAA,QACAE,KAAA,OACAC,YAAA,IACAC,QAAA,OACA8B,KAAA,IACA1E,WAAA,IACA6C,SAAA,MACAC,SAAA,GACAC,eAAA,GACAC,KAAA,0BAEA,CACAK,GAAA,EACAd,IAAA,aACArC,MAAA,sBACAsC,MAAA,OACAE,KAAA,QACAC,YAAA,MACAC,QAAA,QACA8B,KAAA,IACA1E,WAAA,IACA6C,SAAA,MACAC,SAAA,sBACAC,eAAA,OACAC,KAAA,2BAKAoB,EAAA3E,KAAA+E,EACAJ,EAAAvC,SAAA,EACAwC,QAAAC,IAAA,cAAAE,IACA,MAsBAzD,cAAA4D,GACAA,EAAAC,QAAAxD,IACA,MAAAyD,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA5D,EAAAqD,KACAI,EAAAI,SAAA7D,EAAAzB,KACAkF,EAAAK,WAGA/F,QAAA,WACA,IAAAiF,EAAA,KACAe,SAAAH,KAAA,0BAAAZ,EAAAgB,OAAAC,QAAAC,UAAA,gBAAAlB,EAAAmB,SAAAlC,IAIA1C,UAAA6E,GACA,KAAAjC,WAAAiC,EACA,KAAAlC,eAAA,GAIApC,mBAAAuE,EAAAC,GACA,MAAAb,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAAS,EACAZ,EAAAI,SAAAS,EACAb,EAAAK,SAIA1D,SAAAiE,GACAE,OAAAC,KAAAH,EAAA,WAIAnE,YAAAoE,GACA,MAAAG,EAAA,KAAAtE,iBAAAmE,GAAAI,cACAC,EAAA,CACA,uBACA,uBACA,wBACA,qBACA,sBACA,uBACA,qBACA,sBAEA,OAAAA,EAAAF,IAAA,oBAIAtE,iBAAAmE,GACA,OAAAA,EAAAM,MAAA,KAAAC,OAAA,IAIAnG,YAAAoG,GACA,OAAAA,GAAA,MAAAA,EACAC,WAAAD,GAAAE,eAAA,SACAC,sBAAA,EACAC,sBAAA,IAHA,QAQAhH,oBACA,MAAAiH,EAAAJ,WAAA,KAAA1G,KAAAQ,UAAA,GACA,WAAAsG,EAAA,UACAA,EAAA,YACA,QAIA/G,oBACA,MAAA+G,EAAAJ,WAAA,KAAA1G,KAAAQ,UAAA,GACA,WAAAsG,EAAA,MACAA,EAAA,QACA,OAIA9D,gBAAAC,GACA,MAAA8D,EAAA,CACA,iBACA,kBACA,gBACA,iBACA,kBAEA,OAAAA,EAAA9D,IAAA,QAIAS,QAAA3C,EAAA6C,GACA,KAAAoD,SAAA,sBACAC,kBAAA,KACAC,iBAAA,KACAjE,KAAA,YACAkE,KAAA,KAEA,KAAAnH,KAAAgC,UAAAoF,OAAArG,EAAA,GACA,KAAAsG,SAAAC,QAAA,UACAC,MAAA,KACA,KAAAF,SAAArH,KAAA,cC/iBmV,I,wBCQ/UwH,EAAY,eACd,EACAtI,EACA6E,GACA,EACA,KACA,WACA,MAIa,OAAAyD,E,6CCnBf,W,oFCAA,IAAItI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,cAAcN,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKyH,SAAW,cAAcpI,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK0H,OAAS,cAAcrI,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKC,UAAY,eAAe,GAAGZ,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK2H,SAAW,cAActI,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK4H,WAAa,cAAcvI,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK6H,aAAe,eAAe,GAAGxI,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK8H,YAAc,cAAczI,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAK+H,KAAO5I,EAAIa,KAAK+H,KAAO,IAAM,cAAc1I,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,QAAQN,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAIa,KAAKgI,SAAgC,KAArB7I,EAAIa,KAAKgI,QAAgB3I,EAAG,YAAY,CAACiD,YAAY,CAAC,OAAS,WAAW9C,MAAM,CAAC,IAAML,EAAIa,KAAKgI,QAAQ,KAAO,IAAIxE,SAAS,CAAC,MAAQ,SAASvC,GAAQ,OAAO9B,EAAI+B,UAAU/B,EAAIa,KAAKgI,aAAa3I,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIQ,GAAG,UAAU,QAAQ,GAAGN,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,KAAK,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAIa,KAAKiI,SAAgC,KAArB9I,EAAIa,KAAKiI,QAAgB5I,EAAG,WAAW,CAACiD,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,OAAS,WAAW9C,MAAM,CAAC,IAAML,EAAIa,KAAKiI,QAAQ,IAAM,SAASxI,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAI+B,UAAU/B,EAAIa,KAAKiI,YAAY,CAAC5I,EAAG,MAAM,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,SAASI,KAAK,SAAS,CAACP,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIQ,GAAG,UAAU,QAAQ,IAAI,GAAGN,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,YAAYN,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKkI,cAAgB,cAAc7I,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKmI,WAAa,cAAc9I,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKoI,WAAa,eAAe,GAAG/I,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKqI,aAAe,cAAchJ,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,QAAQN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKsI,SAAW,cAAcjJ,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIQ,GAAGR,EAAIW,GAAGX,EAAIa,KAAKuI,UAAY,eAAe,IAAI,GAAGlJ,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUI,KAAK,UAAU,CAACP,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIQ,GAAG,aAAaN,EAAG,WAAW,CAAC4C,WAAW,CAAC,CAAC/B,KAAK,UAAUgC,QAAQ,YAAYC,MAAOhD,EAAIiD,QAASC,WAAW,YAAYC,YAAY,CAAC,MAAQ,QAAQ9C,MAAM,CAAC,KAAOL,EAAIa,KAAKwI,MAAM,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACjG,WAAW,UAAUC,MAAM,aAAa,CAACnD,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACL,EAAIQ,GAAGR,EAAIW,GAAG8C,EAAMC,IAAI3C,gBAAgBb,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAGR,EAAIW,GAAG8C,EAAMC,IAAI1C,eAAed,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAIvC,iBAAiBjB,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,SAAS,CAACG,MAAM,CAAC,KAA4B,QAArBoD,EAAMC,IAAI4F,OAAmB,UAAY,UAAU,KAAO,UAAU,CAACtJ,EAAIQ,GAAG,IAAIR,EAAIW,GAAG8C,EAAMC,IAAI4F,QAAQ,cAAcpJ,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOiD,YAAYtD,EAAIuD,GAAG,CAAC,CAAC1B,IAAI,UAAU2B,GAAG,SAASC,GAAO,MAAO,CAACvD,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAO9B,EAAIuJ,eAAe9F,EAAMC,QAAQ,CAACxD,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIQ,GAAG,kBAAkB,GAAKR,EAAIa,KAAKwI,OAAmC,IAA1BrJ,EAAIa,KAAKwI,MAAM5H,OAA0HzB,EAAIgC,KAAhH9B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACF,EAAIQ,GAAG,gBAAyB,GAAGN,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAI0E,cAAc,MAAQ,OAAOpE,GAAG,CAAC,iBAAiB,SAASwB,GAAQ9B,EAAI0E,cAAc5C,KAAU,CAAC5B,EAAG,WAAW,CAACG,MAAM,CAAC,IAAML,EAAI2E,eAAe,IAAI,IAEjuNC,EAAkB,GCqNtB,GACA7D,KAAA,cACA8D,MAAA,CACAJ,GAAA,CACAX,KAAA,CAAAgB,OAAAC,QACAC,UAAA,IAGAC,OACA,OACApE,KAAA,GACAoC,SAAA,EACAyB,eAAA,EACAC,WAAA,KAGAO,MAAA,CACAT,GAAA,CACAU,WAAA,EACAC,QAAAC,GACAA,GAAA,GAAAA,IACAI,QAAAC,IAAA,sBAAAL,GACA,KAAAC,QAAAD,OAKAE,QAAA,CACAD,QAAAb,GACA,IAAAe,EAAA,KACAC,QAAAC,IAAA,eAAAjB,GACAe,EAAAvC,SAAA,EAGA0C,WAAA,KACA,MAAA6D,EAAA,CACA/E,KACA6D,QAAA,WACAC,MAAA,cACAzH,SAAA,KACA0H,QAAA,KACAK,QAAA,GACAH,YAAA,QACAD,UAAA,cACAM,aAAA,OACAC,UAAA,MACAC,UAAA,OACAC,YAAA,OACAC,QAAA,MACAC,SAAA,OACAN,QAAA,GACAH,WAAA,aACAC,KAAA,EACAS,MAAA,CACA,CACAtI,KAAA,OACAC,IAAA,cACAG,MAAA,QACAmI,OAAA,OAEA,CACAvI,KAAA,OACAC,IAAA,cACAG,MAAA,QACAmI,OAAA,SAKA9D,EAAA3E,KAAA2I,EACAhE,EAAAvC,SAAA,EACAwC,QAAAC,IAAA,YAAA8D,IACA,MAmBAzH,UAAA6E,GACA,KAAAjC,WAAAiC,EACA,KAAAlC,eAAA,GAGA6E,eAAAE,GACAhE,QAAAC,IAAA,WAAA+D,GAEA,KAAAvB,SAAArH,KAAA,iBC1TmV,I,wBCQ/UwH,EAAY,eACd,EACAtI,EACA6E,GACA,EACA,KACA,WACA,MAIa,OAAAyD,E", "file": "js/chunk-f716f9b0.5426e98d.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"debt-detail-container\"},[_c('div',{staticClass:\"action-bar\"},[_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"primary\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exports}},[_vm._v(\" 导出跟进记录 \")])],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务基本信息\")]),_c('div',{staticClass:\"debt-status\"},[_c('el-tag',{attrs:{\"type\":_vm.getDebtStatusType(),\"size\":\"medium\"}},[_vm._v(\" \"+_vm._s(_vm.getDebtStatusText())+\" \")])],1)]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"委托人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"债务人姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.name || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"债务人电话\")]),_c('div',{staticClass:\"info-value phone-number\"},[_vm._v(_vm._s(_vm.info.tel || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"债务人地址\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.address || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"amount-card debt-amount\"},[_c('div',{staticClass:\"amount-label\"},[_vm._v(\"债务总金额\")]),_c('div',{staticClass:\"amount-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatMoney(_vm.info.money)))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"amount-card back-amount\"},[_c('div',{staticClass:\"amount-label\"},[_vm._v(\"已回款金额\")]),_c('div',{staticClass:\"amount-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatMoney(_vm.info.back_money)))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"amount-card remaining-amount\"},[_c('div',{staticClass:\"amount-label\"},[_vm._v(\"未回款金额\")]),_c('div',{staticClass:\"amount-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatMoney(_vm.info.un_money)))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"提交时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.ctime || '未填写'))])])]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"最后修改时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.utime || '未填写'))])])])],1)],1),(_vm.info.cards && _vm.info.cards.length > 0)?_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-postcard\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人身份信息\")])]),_c('div',{staticClass:\"id-cards-grid\"},_vm._l((_vm.info.cards),function(card,index){return _c('div',{key:index,staticClass:\"id-card-item\",on:{\"click\":function($event){return _vm.showImage(card)}}},[_c('el-image',{staticClass:\"id-card-image\",attrs:{\"src\":card,\"fit\":\"cover\"}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]),_c('div',{staticClass:\"id-card-label\"},[_vm._v(\" \"+_vm._s(index === 0 ? '身份证正面' : '身份证反面')+\" \")])],1)}),0)]):_vm._e(),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"案由描述\")])]),_c('div',{staticClass:\"case-description\"},[_c('p',[_vm._v(_vm._s(_vm.info.case_des || '暂无案由描述'))])])]),(_vm.info.images && _vm.info.images.length > 0)?_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-picture\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"证据图片\")]),_c('el-button',{staticClass:\"header-action\",attrs:{\"size\":\"small\",\"type\":\"primary\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadFiles(_vm.info.images_download)}}},[_vm._v(\" 全部下载 \")])],1),_c('div',{staticClass:\"evidence-images-grid\"},_vm._l((_vm.info.images),function(image,index){return _c('div',{key:index,staticClass:\"evidence-image-item\"},[_c('el-image',{staticClass:\"evidence-image\",attrs:{\"src\":image,\"preview-src-list\":_vm.info.images,\"fit\":\"cover\"}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]),_c('div',{staticClass:\"evidence-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadSingleFile(image, `evidence_${index + 1}`)}}},[_vm._v(\" 下载 \")])],1)],1)}),0)]):_vm._e(),(_vm.info.attach_path && _vm.info.attach_path.length > 0)?_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-folder\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"证据文件\")])]),_c('div',{staticClass:\"evidence-files-list\"},_vm._l((_vm.info.attach_path),function(file,index){return (file)?_c('div',{key:index,staticClass:\"file-item\"},[_c('div',{staticClass:\"file-info\"},[_c('i',{staticClass:\"file-icon\",class:_vm.getFileIcon(file)}),_c('div',{staticClass:\"file-details\"},[_c('div',{staticClass:\"file-name\"},[_vm._v(\"文件\"+_vm._s(index + 1))]),_c('div',{staticClass:\"file-type\"},[_vm._v(_vm._s(_vm.getFileExtension(file)))])])]),_c('div',{staticClass:\"file-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\",\"icon\":\"el-icon-view\"},on:{\"click\":function($event){return _vm.viewFile(file)}}},[_vm._v(\" 查看 \")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadSingleFile(file, `file_${index + 1}`)}}},[_vm._v(\" 下载 \")])],1)]):_vm._e()}),0)]):_vm._e(),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"跟进记录\")]),_c('div',{staticClass:\"record-count\"},[_vm._v(\" 共 \"+_vm._s(_vm.info.debttrans ? _vm.info.debttrans.length : 0)+\" 条记录 \")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"follow-up-table\",staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debttrans,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"day\",\"label\":\"跟进日期\",\"width\":\"110\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('i',{staticClass:\"el-icon-date\"}),_vm._v(\" \"+_vm._s(scope.row.day)+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"ctime\",\"label\":\"提交时间\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" \"+_vm._s(scope.row.ctime)+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"au_id\",\"label\":\"操作人员\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"info\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.au_id))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"进度类型\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getProgressType(scope.row.type),\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.type)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"total_price\",\"label\":\"费用金额\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.total_price && scope.row.total_price !== '0')?_c('span',{staticClass:\"money-text\"},[_vm._v(\" ¥\"+_vm._s(scope.row.total_price)+\" \")]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"-\")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"content\",\"label\":\"费用内容\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(scope.row.content || '-')+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"back_money\",\"label\":\"回款金额\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.back_money && scope.row.back_money !== '0')?_c('span',{staticClass:\"money-text success\"},[_vm._v(\" ¥\"+_vm._s(scope.row.back_money)+\" \")]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"-\")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"pay_type\",\"label\":\"支付状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.pay_type === '已支付' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.pay_type || '未支付')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"pay_time\",\"label\":\"支付时间\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(scope.row.pay_time || '-')+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"pay_order_type\",\"label\":\"支付方式\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(scope.row.pay_order_type || '-')+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"进度描述\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"desc-content\"},[_vm._v(_vm._s(scope.row.desc))])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{staticClass:\"danger-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.delData(scope.$index, scope.row.id)}}},[_c('i',{staticClass:\"el-icon-delete\"}),_vm._v(\" 移除 \")])]}}])})],1),(!_vm.info.debttrans || _vm.info.debttrans.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无跟进记录\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"debt-detail-container\">\r\n    <!-- 操作按钮区域 -->\r\n    <div class=\"action-bar\">\r\n      <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-download\" @click=\"exports\">\r\n        导出跟进记录\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 债务基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务基本信息</span>\r\n        <div class=\"debt-status\">\r\n          <el-tag :type=\"getDebtStatusType()\" size=\"medium\">\r\n            {{ getDebtStatusText() }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">委托人</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人姓名</div>\r\n            <div class=\"info-value\">{{ info.name || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人电话</div>\r\n            <div class=\"info-value phone-number\">{{ info.tel || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人地址</div>\r\n            <div class=\"info-value\">{{ info.address || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card debt-amount\">\r\n            <div class=\"amount-label\">债务总金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card back-amount\">\r\n            <div class=\"amount-label\">已回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.back_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card remaining-amount\">\r\n            <div class=\"amount-label\">未回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.un_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">提交时间</div>\r\n            <div class=\"info-value\">{{ info.ctime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">最后修改时间</div>\r\n            <div class=\"info-value\">{{ info.utime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n    <!-- 债务人身份信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.cards && info.cards.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-postcard\"></i>\r\n        <span class=\"card-title\">债务人身份信息</span>\r\n      </div>\r\n\r\n      <div class=\"id-cards-grid\">\r\n        <div\r\n          v-for=\"(card, index) in info.cards\"\r\n          :key=\"index\"\r\n          class=\"id-card-item\"\r\n          @click=\"showImage(card)\">\r\n          <el-image\r\n            :src=\"card\"\r\n            fit=\"cover\"\r\n            class=\"id-card-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"id-card-label\">\r\n            {{ index === 0 ? '身份证正面' : '身份证反面' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 案由信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span class=\"card-title\">案由描述</span>\r\n      </div>\r\n\r\n      <div class=\"case-description\">\r\n        <p>{{ info.case_des || '暂无案由描述' }}</p>\r\n      </div>\r\n    </el-card>\r\n    <!-- 证据图片卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.images && info.images.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-picture\"></i>\r\n        <span class=\"card-title\">证据图片</span>\r\n        <el-button\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"downloadFiles(info.images_download)\"\r\n          class=\"header-action\">\r\n          全部下载\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"evidence-images-grid\">\r\n        <div\r\n          v-for=\"(image, index) in info.images\"\r\n          :key=\"index\"\r\n          class=\"evidence-image-item\">\r\n          <el-image\r\n            :src=\"image\"\r\n            :preview-src-list=\"info.images\"\r\n            fit=\"cover\"\r\n            class=\"evidence-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"evidence-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(image, `evidence_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 证据文件卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.attach_path && info.attach_path.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-folder\"></i>\r\n        <span class=\"card-title\">证据文件</span>\r\n      </div>\r\n\r\n      <div class=\"evidence-files-list\">\r\n        <div\r\n          v-for=\"(file, index) in info.attach_path\"\r\n          :key=\"index\"\r\n          class=\"file-item\"\r\n          v-if=\"file\">\r\n          <div class=\"file-info\">\r\n            <i :class=\"getFileIcon(file)\" class=\"file-icon\"></i>\r\n            <div class=\"file-details\">\r\n              <div class=\"file-name\">文件{{ index + 1 }}</div>\r\n              <div class=\"file-type\">{{ getFileExtension(file) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"file-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"viewFile(file)\">\r\n              查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(file, `file_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n    <!-- 跟进记录卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-time\"></i>\r\n        <span class=\"card-title\">跟进记录</span>\r\n        <div class=\"record-count\">\r\n          共 {{ info.debttrans ? info.debttrans.length : 0 }} 条记录\r\n        </div>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debttrans\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n        class=\"follow-up-table\">\r\n        <el-table-column prop=\"day\" label=\"跟进日期\" width=\"110\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-date\"></i>\r\n            {{ scope.row.day }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-time\"></i>\r\n            {{ scope.row.ctime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"au_id\" label=\"操作人员\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"info\" size=\"small\">{{ scope.row.au_id }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"进度类型\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getProgressType(scope.row.type)\" size=\"small\">\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"total_price\" label=\"费用金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text\" v-if=\"scope.row.total_price && scope.row.total_price !== '0'\">\r\n              ¥{{ scope.row.total_price }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"content\" label=\"费用内容\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.content || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"回款金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text success\" v-if=\"scope.row.back_money && scope.row.back_money !== '0'\">\r\n              ¥{{ scope.row.back_money }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_type\" label=\"支付状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.pay_type === '已支付' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.pay_type || '未支付' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_time\" label=\"支付时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_time || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_order_type\" label=\"支付方式\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_order_type || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"进度描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"desc-content\">{{ scope.row.desc }}</div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n              class=\"danger-btn\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debttrans || info.debttrans.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无跟进记录</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%;\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'DebtDetail',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {\r\n            nickname: '',\r\n            name: '',\r\n            tel: '',\r\n            address: '',\r\n            money: '',\r\n            back_money: '',\r\n            un_money: '',\r\n            ctime: '',\r\n            utime: '',\r\n            case_des: '',\r\n            cards: [],\r\n            images: [],\r\n            images_download: [],\r\n            attach_path: [],\r\n            debttrans: []\r\n          }, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取债务详情，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testDebtData = {\r\n            id: id,\r\n            nickname: \"张三\",\r\n            name: \"债务人李四\",\r\n            tel: \"13900139001\",\r\n            address: \"北京市朝阳区测试街道123号\",\r\n            money: \"50000\",\r\n            back_money: \"10000\",\r\n            un_money: \"40000\",\r\n            ctime: \"2024-01-01 10:00:00\",\r\n            utime: \"2024-01-15 15:30:00\",\r\n            case_des: \"借款纠纷，借款人未按约定时间还款，现申请追讨欠款及利息。\",\r\n            cards: [\r\n              \"/static/images/id_card_front.jpg\",\r\n              \"/static/images/id_card_back.jpg\"\r\n            ],\r\n            images: [\r\n              \"/static/images/evidence1.jpg\",\r\n              \"/static/images/evidence2.jpg\",\r\n              \"/static/images/evidence3.jpg\"\r\n            ],\r\n            images_download: [\r\n              { name: \"证据1.jpg\", path: \"/static/images/evidence1.jpg\" },\r\n              { name: \"证据2.jpg\", path: \"/static/images/evidence2.jpg\" }\r\n            ],\r\n            attach_path: [\r\n              \"/static/files/contract.pdf\",\r\n              \"/static/files/bank_record.xlsx\"\r\n            ],\r\n            debttrans: [\r\n              {\r\n                id: 1,\r\n                day: \"2024-01-15\",\r\n                ctime: \"2024-01-15 10:30:00\",\r\n                au_id: \"调解员王五\",\r\n                type: \"电话联系\",\r\n                total_price: \"0\",\r\n                content: \"联系费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"未支付\",\r\n                pay_time: \"\",\r\n                pay_order_type: \"\",\r\n                desc: \"已与债务人取得联系，对方表示将在本月底前还款\"\r\n              },\r\n              {\r\n                id: 2,\r\n                day: \"2024-01-10\",\r\n                ctime: \"2024-01-10 14:20:00\",\r\n                au_id: \"法务赵六\",\r\n                type: \"发送催款函\",\r\n                total_price: \"200\",\r\n                content: \"律师函费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"已支付\",\r\n                pay_time: \"2024-01-10 14:25:00\",\r\n                pay_order_type: \"微信支付\",\r\n                desc: \"向债务人发送正式催款函，要求在15日内还款\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testDebtData;\r\n          _this.loading = false;\r\n          console.log('债务详情数据加载完成:', testDebtData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/debt/view?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取债务详情失败:', resp);\r\n            _this.$message({\r\n              type: \"error\",\r\n              message: resp.msg || \"获取数据失败\",\r\n            });\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n        downloadFiles(imgs) {\r\n            imgs.forEach((file) => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = file.path;\r\n                link.download = file.name;\r\n                link.click();\r\n            });\r\n        },\r\n        exports:function () { //导出表格\r\n            let _this = this;\r\n            location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n        },\r\n\r\n        // 显示图片\r\n        showImage(imageUrl) {\r\n          this.show_image = imageUrl;\r\n          this.dialogVisible = true;\r\n        },\r\n\r\n        // 下载单个文件\r\n        downloadSingleFile(url, filename) {\r\n          const link = document.createElement(\"a\");\r\n          link.href = url;\r\n          link.download = filename;\r\n          link.click();\r\n        },\r\n\r\n        // 查看文件\r\n        viewFile(url) {\r\n          window.open(url, '_blank');\r\n        },\r\n\r\n        // 获取文件图标\r\n        getFileIcon(filename) {\r\n          const ext = this.getFileExtension(filename).toLowerCase();\r\n          const iconMap = {\r\n            'pdf': 'el-icon-document',\r\n            'doc': 'el-icon-document',\r\n            'docx': 'el-icon-document',\r\n            'xls': 'el-icon-s-grid',\r\n            'xlsx': 'el-icon-s-grid',\r\n            'txt': 'el-icon-document',\r\n            'zip': 'el-icon-folder',\r\n            'rar': 'el-icon-folder'\r\n          };\r\n          return iconMap[ext] || 'el-icon-document';\r\n        },\r\n\r\n        // 获取文件扩展名\r\n        getFileExtension(filename) {\r\n          return filename.split('.').pop() || '';\r\n        },\r\n\r\n        // 格式化金额\r\n        formatMoney(amount) {\r\n          if (!amount || amount === '0') return '0.00';\r\n          return parseFloat(amount).toLocaleString('zh-CN', {\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2\r\n          });\r\n        },\r\n\r\n        // 获取债务状态类型\r\n        getDebtStatusType() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return 'success';\r\n          if (unMoney > 0) return 'warning';\r\n          return 'info';\r\n        },\r\n\r\n        // 获取债务状态文本\r\n        getDebtStatusText() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return '已结清';\r\n          if (unMoney > 0) return '未结清';\r\n          return '处理中';\r\n        },\r\n\r\n        // 获取进度类型\r\n        getProgressType(type) {\r\n          const typeMap = {\r\n            '电话联系': 'primary',\r\n            '发送催款函': 'warning',\r\n            '法院起诉': 'danger',\r\n            '调解成功': 'success',\r\n            '回款确认': 'success'\r\n          };\r\n          return typeMap[type] || 'info';\r\n        },\r\n\r\n        // 删除数据\r\n        delData(index, id) {\r\n          this.$confirm('确定要移除这条跟进记录吗？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 这里应该调用删除API\r\n            this.info.debttrans.splice(index, 1);\r\n            this.$message.success('删除成功');\r\n          }).catch(() => {\r\n            this.$message.info('已取消删除');\r\n          });\r\n        }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.debt-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.action-bar {\r\n  margin-bottom: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n.debt-status {\r\n  margin-left: auto;\r\n}\r\n\r\n.header-action {\r\n  margin-left: auto;\r\n}\r\n\r\n.record-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.amount-card {\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.amount-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.debt-amount {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.back-amount {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.remaining-amount {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.amount-label {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.amount-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n}\r\n\r\n.id-cards-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.id-card-item {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.id-card-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.id-card-image {\r\n  width: 200px;\r\n  height: 120px;\r\n  border-radius: 8px;\r\n  border: 2px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.id-card-item:hover .id-card-image {\r\n  border-color: #409eff;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.id-card-label {\r\n  margin-top: 10px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.case-description {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.case-description p {\r\n  margin: 0;\r\n  line-height: 1.8;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.evidence-images-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-image-item {\r\n  text-align: center;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.evidence-image-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 120px;\r\n  border-radius: 6px;\r\n}\r\n\r\n.evidence-actions {\r\n  margin-top: 10px;\r\n}\r\n\r\n.evidence-files-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n  margin-bottom: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  color: #409eff;\r\n  margin-right: 15px;\r\n}\r\n\r\n.file-details {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.file-type {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.follow-up-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.money-text {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n}\r\n\r\n.money-text.success {\r\n  color: #67c23a;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.desc-content {\r\n  max-width: 200px;\r\n  word-break: break-word;\r\n  line-height: 1.4;\r\n}\r\n\r\n.danger-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.danger-btn:hover {\r\n  color: #f78989;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .debt-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .amount-card {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .amount-value {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .id-cards-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .evidence-images-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DebtDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DebtDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DebtDetail.vue?vue&type=template&id=d8466e1a&scoped=true\"\nimport script from \"./DebtDetail.vue?vue&type=script&lang=js\"\nexport * from \"./DebtDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./DebtDetail.vue?vue&type=style&index=0&id=d8466e1a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d8466e1a\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DebtDetail.vue?vue&type=style&index=0&id=d8466e1a&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-detail-container\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"客户基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"公司名称\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.company || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"手机号\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.phone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"客户姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkman || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkphone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"用户来源\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.yuangong_id || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"开始时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.start_time || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"会员年限\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.year ? _vm.info.year + '年' : '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"头像\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.headimg && _vm.info.headimg !== '')?_c('el-avatar',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.headimg,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.license && _vm.info.license !== '')?_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.license,\"fit\":\"cover\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"服务团队\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"调解员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.tiaojie_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"法务专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.fawu_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"立案专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.lian_name || '未分配'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"合同专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.htsczy_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"律师\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ls_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"业务员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ywy_name || '未分配'))])])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人信息\")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debts,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.name))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"phone-number\"},[_vm._v(_vm._s(scope.row.tel))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDebtDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 详情 \")])]}}])})],1),(!_vm.info.debts || _vm.info.debts.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无债务人信息\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=4468717a&scoped=true\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4468717a\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}