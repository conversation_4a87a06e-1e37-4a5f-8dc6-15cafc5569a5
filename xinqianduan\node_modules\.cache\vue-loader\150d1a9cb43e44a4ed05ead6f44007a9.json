{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue?vue&type=template&id=794dfcc4&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue", "mtime": 1748428274747}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ0ZXN0LXBhZ2UiCiAgfSwgW19jKCJoMSIsIFtfdm0uX3YoIuW9kuaho+euoeeQhua1i+ivlemhtemdoiIpXSksIF9jKCJwIiwgW192bS5fdigi5aaC5p6c5L2g6IO955yL5Yiw6L+Z5Liq6aG16Z2i77yM6K+05piO5b2S5qGj566h55CG6Lev55Sx6YWN572u5q2j56Gu44CCIildKSwgX2MoImVsLWJ1dHRvbiIsIHsKICAgIGF0dHJzOiB7CiAgICAgIHR5cGU6ICJwcmltYXJ5IgogICAgfSwKICAgIG9uOiB7CiAgICAgIGNsaWNrOiBfdm0uZ29Ub0ZpbGVBcmNoaXZlCiAgICB9CiAgfSwgW192bS5fdigi5YmN5b6A5paH5Lu25b2S5qGjIildKV0sIDEpOwp9Owp2YXIgc3RhdGljUmVuZGVyRm5zID0gW107CnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZTsKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfTs="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "type", "on", "click", "goToFileArchive", "staticRenderFns", "_withStripped"], "sources": ["D:/Gitee/xinqianduan/src/views/pages/archive/test.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"test-page\" },\n    [\n      _c(\"h1\", [_vm._v(\"归档管理测试页面\")]),\n      _c(\"p\", [_vm._v(\"如果你能看到这个页面，说明归档管理路由配置正确。\")]),\n      _c(\n        \"el-button\",\n        { attrs: { type: \"primary\" }, on: { click: _vm.goToFileArchive } },\n        [_vm._v(\"前往文件归档\")]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC9BH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAC7CH,EAAE,CACA,WAAW,EACX;IAAEI,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAER,GAAG,CAACS;IAAgB;EAAE,CAAC,EAClE,CAACT,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIM,eAAe,GAAG,EAAE;AACxBX,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEW,eAAe", "ignoreList": []}]}