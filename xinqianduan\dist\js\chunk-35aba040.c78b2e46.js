(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-35aba040"],{b221:function(e,t,s){"use strict";s("df46")},c69d:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"service-type-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-left"},[t("h2",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-menu"}),e._v(" "+e._s(this.$router.currentRoute.name)+" ")]),t("div",{staticClass:"page-subtitle"},[e._v("管理法律服务分类和类型配置")])]),t("div",{staticClass:"header-actions"},[t("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新数据 ")])],1)]),t("div",{staticClass:"stats-section"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{xs:12,sm:8,md:8,lg:8,xl:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon total-icon"},[t("i",{staticClass:"el-icon-menu"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.total))]),t("div",{staticClass:"stat-label"},[e._v("服务类型")]),t("div",{staticClass:"stat-change positive"},[t("i",{staticClass:"el-icon-arrow-up"}),e._v(" +5% ")])])])]),t("el-col",{attrs:{xs:12,sm:8,md:8,lg:8,xl:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon active-icon"},[t("i",{staticClass:"el-icon-star-on"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.activeTypes))]),t("div",{staticClass:"stat-label"},[e._v("活跃类型")]),t("div",{staticClass:"stat-change positive"},[t("i",{staticClass:"el-icon-check"}),e._v(" 正常 ")])])])]),t("el-col",{attrs:{xs:12,sm:8,md:8,lg:8,xl:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon usage-icon"},[t("i",{staticClass:"el-icon-data-analysis"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v("85%")]),t("div",{staticClass:"stat-label"},[e._v("使用率")]),t("div",{staticClass:"stat-change positive"},[t("i",{staticClass:"el-icon-arrow-up"}),e._v(" +3% ")])])])])],1)],1),t("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("div",{staticClass:"header-left"},[t("span",{staticClass:"card-title"},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索与筛选 ")]),t("div",{staticClass:"card-subtitle"},[e._v("快速查找和管理服务类型")])]),t("div",{staticClass:"header-actions"},[t("el-button-group",{staticClass:"action-group"},[t("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出 ")]),t("el-button",{attrs:{size:"small",icon:"el-icon-refresh"},on:{click:e.refreshData}},[e._v(" 刷新 ")])],1),t("el-button",{staticClass:"primary-action",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增类型 ")])],1)]),t("div",{staticClass:"search-section"},[t("el-form",{staticClass:"search-form",attrs:{model:e.search,inline:!0}},[t("div",{staticClass:"search-row"},[t("el-form-item",{staticClass:"search-item-main",attrs:{label:"关键词搜索"}},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入类型名称或描述关键词...",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchData()}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-form-item",{staticClass:"search-item",attrs:{label:"服务类型"}},[t("el-select",{staticClass:"search-select",attrs:{placeholder:"选择服务类型",clearable:""},model:{value:e.search.serviceType,callback:function(t){e.$set(e.search,"serviceType",t)},expression:"search.serviceType"}},[t("el-option",{attrs:{label:"全部类型",value:""}}),t("el-option",{attrs:{label:"计次服务",value:"1"}}),t("el-option",{attrs:{label:"不限次数",value:"0"}})],1)],1),t("el-form-item",{staticClass:"search-item",attrs:{label:"状态筛选"}},[t("el-select",{staticClass:"search-select",attrs:{placeholder:"选择状态",clearable:""},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},[t("el-option",{attrs:{label:"全部状态",value:""}}),t("el-option",{attrs:{label:"正常",value:"1"}}),t("el-option",{attrs:{label:"待完善",value:"0"}})],1)],1),t("el-form-item",{staticClass:"search-actions-item"},[t("div",{staticClass:"search-actions"},[t("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchData}},[e._v(" 搜索 ")]),t("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh-left"},on:{click:e.resetSearch}},[e._v(" 重置 ")]),t("el-button",{staticClass:"toggle-btn",attrs:{type:"text"},on:{click:e.toggleAdvanced}},[t("i",{class:e.showAdvanced?"el-icon-arrow-up":"el-icon-arrow-down"}),e._v(" "+e._s(e.showAdvanced?"收起":"高级筛选")+" ")])],1)])],1),t("transition",{attrs:{name:"slide-fade"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.showAdvanced,expression:"showAdvanced"}],staticClass:"advanced-search"},[t("el-divider",{attrs:{"content-position":"left"}},[t("i",{staticClass:"el-icon-setting"}),e._v(" 高级筛选选项 ")]),t("div",{staticClass:"advanced-content"},[t("div",{staticClass:"advanced-row"},[t("el-form-item",{staticClass:"advanced-item",attrs:{label:"创建时间范围"}},[t("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.search.dateRange,callback:function(t){e.$set(e.search,"dateRange",t)},expression:"search.dateRange"}})],1),t("el-form-item",{staticClass:"advanced-item",attrs:{label:"排序方式"}},[t("el-select",{staticClass:"sort-select",attrs:{placeholder:"选择排序"},model:{value:e.search.sortBy,callback:function(t){e.$set(e.search,"sortBy",t)},expression:"search.sortBy"}},[t("el-option",{attrs:{label:"创建时间",value:"create_time"}}),t("el-option",{attrs:{label:"名称字母",value:"title"}}),t("el-option",{attrs:{label:"使用频率",value:"usage"}}),t("el-option",{attrs:{label:"更新时间",value:"update_time"}})],1)],1),t("el-form-item",{staticClass:"advanced-item",attrs:{label:"排序顺序"}},[t("el-radio-group",{staticClass:"sort-order",attrs:{size:"small"},model:{value:e.search.sortOrder,callback:function(t){e.$set(e.search,"sortOrder",t)},expression:"search.sortOrder"}},[t("el-radio-button",{attrs:{label:"desc"}},[t("i",{staticClass:"el-icon-sort-down"}),e._v(" 降序 ")]),t("el-radio-button",{attrs:{label:"asc"}},[t("i",{staticClass:"el-icon-sort-up"}),e._v(" 升序 ")])],1)],1)],1),t("div",{staticClass:"advanced-row"},[t("el-form-item",{staticClass:"advanced-item",attrs:{label:"使用频率"}},[t("el-select",{staticClass:"usage-select",attrs:{placeholder:"选择使用频率"},model:{value:e.search.usageLevel,callback:function(t){e.$set(e.search,"usageLevel",t)},expression:"search.usageLevel"}},[t("el-option",{attrs:{label:"全部频率",value:""}}),t("el-option",{attrs:{label:"高频使用",value:"high"}}),t("el-option",{attrs:{label:"中频使用",value:"medium"}}),t("el-option",{attrs:{label:"低频使用",value:"low"}}),t("el-option",{attrs:{label:"未使用",value:"none"}})],1)],1),t("el-form-item",{staticClass:"advanced-item",attrs:{label:"类型特性"}},[t("el-checkbox-group",{staticClass:"feature-checkboxes",model:{value:e.search.features,callback:function(t){e.$set(e.search,"features",t)},expression:"search.features"}},[t("el-checkbox",{attrs:{label:"popular"}},[e._v("热门类型")]),t("el-checkbox",{attrs:{label:"new"}},[e._v("新增类型")]),t("el-checkbox",{attrs:{label:"recommended"}},[e._v("推荐类型")])],1)],1),t("el-form-item",{staticClass:"advanced-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-check"},on:{click:e.applyAdvancedSearch}},[e._v(" 应用筛选 ")]),t("el-button",{attrs:{size:"small",icon:"el-icon-close"},on:{click:e.clearAdvancedSearch}},[e._v(" 清空高级选项 ")])],1)],1)])],1)])],1)],1)]),t("el-card",{staticClass:"table-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"card-title"},[t("i",{staticClass:"el-icon-tickets"}),e._v(" 类型列表 ")]),t("div",{staticClass:"table-actions"},[t("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出数据 ")]),t("el-button",{attrs:{size:"small",icon:"el-icon-delete"},on:{click:e.batchDelete}},[e._v(" 批量删除 ")])],1)]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:e.list},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{label:"类型信息","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",{staticClass:"type-info"},[t("div",{staticClass:"type-header"},[t("div",{staticClass:"type-icon"},[t("i",{staticClass:"el-icon-star-on"})]),t("div",{staticClass:"type-details"},[t("div",{staticClass:"type-title"},[e._v(e._s(s.row.title))]),s.row.desc?t("div",{staticClass:"type-desc"},[e._v(" "+e._s(s.row.desc)+" ")]):e._e(),t("div",{staticClass:"type-features"},[t("el-tag",{attrs:{size:"mini",type:1==s.row.is_num?"success":"info"}},[e._v(" "+e._s(1==s.row.is_num?"计次服务":"不限次数")+" ")])],1)])])])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",{staticClass:"time-info"},[t("i",{staticClass:"el-icon-time"}),e._v(" "+e._s(e.formatDate(s.row.create_time))+" ")])]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row),effect:"dark"}},[e._v(" "+e._s(e.getStatusText(s.row))+" ")])]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",{staticClass:"action-buttons"},[t("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(s.row.id)}}},[t("i",{staticClass:"el-icon-edit"}),e._v(" 编辑 ")]),t("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.delData(s.$index,s.row.id)}}},[t("i",{staticClass:"el-icon-delete"}),e._v(" 删除 ")])],1)]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{staticClass:"edit-dialog",attrs:{title:e.dialogTitle,visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"类型名称",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入服务类型名称",autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"计次设置"}},[t("el-radio-group",{model:{value:e.ruleForm.is_num,callback:function(t){e.$set(e.ruleForm,"is_num",t)},expression:"ruleForm.is_num"}},[t("el-radio",{attrs:{label:1}},[e._v("计次服务")]),t("el-radio",{attrs:{label:0}},[e._v("不限次数")])],1),t("div",{staticClass:"form-tip"},[t("i",{staticClass:"el-icon-info"}),e._v(" 计次服务将限制使用次数，不限次数则可无限使用 ")])],1),t("el-form-item",{attrs:{label:"类型描述"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入服务类型的详细描述...",autocomplete:"off"},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:function(t){return e.saveData()}}},[e._v(" 保存 ")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"image-viewer"},[t("el-image",{attrs:{src:e.show_image,fit:"contain"}})],1)])],1)},l=[],i={name:"list",components:{},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,showAdvanced:!1,search:{keyword:"",serviceType:"",status:"",dateRange:[],sortBy:"create_time",sortOrder:"desc",usageLevel:"",features:[]},loading:!0,url:"/type/",title:"服务类型",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,saveLoading:!1,selectedRows:[],ruleForm:{title:"",desc:"",is_num:0},rules:{title:[{required:!0,message:"请填写类型名称",trigger:"blur"}]},formLabelWidth:"120px"}},computed:{activeTypes(){return Array.isArray(this.list)?this.list.filter(e=>e.title&&""!==e.title.trim()).length:0},dialogTitle(){return this.ruleForm.id?"编辑服务类型":"新增服务类型"}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:"",is_num:0},t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let s=this;s.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(s.ruleForm[t]="",s.$message.success("删除成功!")):s.$message.error(e.msg)})},getStatusType(e){return e.title&&""!==e.title.trim()?"success":"info"},getStatusText(e){return e.title&&""!==e.title.trim()?"正常":"待完善"},formatDate(e){return e?new Date(e).toLocaleDateString("zh-CN"):"未设置"},resetSearch(){this.search={keyword:"",serviceType:"",status:"",dateRange:[],sortBy:"create_time",sortOrder:"desc",usageLevel:"",features:[]},this.showAdvanced=!1,this.page=1,this.getData()},toggleAdvanced(){this.showAdvanced=!this.showAdvanced},refreshData(){this.getData(),this.$message.success("数据已刷新")},handleSelectionChange(e){this.selectedRows=e},exportData(){this.$message.success("数据导出功能开发中...")},batchDelete(){0!==this.selectedRows.length?this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条数据吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$message.success("批量删除功能开发中...")}).catch(()=>{this.$message.info("已取消删除")}):this.$message.warning("请先选择要删除的数据")},applyAdvancedSearch(){this.page=1,this.getData(),this.$message.success("高级筛选已应用")},clearAdvancedSearch(){this.search.dateRange=[],this.search.sortBy="create_time",this.search.sortOrder="desc",this.search.usageLevel="",this.search.features=[],this.$message.info("高级筛选选项已清空")}}},r=i,c=(s("b221"),s("2877")),o=Object(c["a"])(r,a,l,!1,null,"0f8e2eec",null);t["default"]=o.exports},df46:function(e,t,s){}}]);
//# sourceMappingURL=chunk-35aba040.c78b2e46.js.map