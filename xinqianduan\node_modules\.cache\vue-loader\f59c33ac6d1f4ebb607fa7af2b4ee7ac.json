{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue?vue&type=template&id=6879a05a&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\xinwen\\xinwen.vue", "mtime": 1748425644040}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}