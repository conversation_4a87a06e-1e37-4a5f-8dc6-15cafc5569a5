{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue", "mtime": 1748608848793}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetails", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "salesman", "refund_time", "status", "payType", "minAmount", "maxAmount", "packageType", "sortBy", "loading", "url", "title", "info", "currentId", "dialogFormVisible", "show_image", "dialogVisible", "dialogViewUserDetail", "is_info", "upload_index", "dialogStatus", "dialogEndTime", "ruleForm", "status_msg", "end_time", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "showAdvanced", "selectedRows", "showRevenueDialog", "showExportDialog", "exportForm", "format", "fields", "range", "date<PERSON><PERSON><PERSON>", "exportLoading", "fullPaymentCount", "installmentPaymentCount", "showNotifications", "computed", "pendingOrders", "filter", "item", "length", "approvedOrders", "rejectedOrders", "totalRevenue", "reduce", "sum", "pay_age", "toLocaleString", "pay_type", "expiringOrders", "today", "Date", "sevenDaysLater", "getTime", "endDate", "expiredOrders", "highValueOrders", "hasImportantNotifications", "mounted", "getData", "methods", "editData", "id", "_this", "getInfo", "viewUserData", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "msg", "catch", "updateEndTIme", "postRequest", "refulsh", "$router", "go", "count", "saveData", "$refs", "validate", "valid", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "fenqi", "pay_path", "beforeUpload", "file", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "showImage", "changePinzhen", "showStatus", "row", "showEndTime", "changeEndTime", "changeStatus", "getStatusType", "statusMap", "getStatusText", "getDebtStatusType", "formatDate", "dateStr", "toLocaleDateString", "tableRowClassName", "rowIndex", "resetSearch", "exportData", "downloadOrder", "toggleAdvanced", "applyAdvancedSearch", "clearAdvancedSearch", "refreshData", "batch<PERSON><PERSON><PERSON>", "handleSelectionChange", "selection", "filterByStatus", "showRevenueChart", "executeExport", "setTimeout", "formatText", "rangeText", "blob", "generateExportData", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "exportList", "map", "includes", "_item$client", "_item$client2", "_item$client3", "client", "company", "linkman", "phone", "_item$taocan", "_item$taocan2", "_item$taocan3", "taocan", "price", "year", "total_price", "create_time", "_item$member", "member", "csv<PERSON><PERSON>nt", "convertToCSV", "Blob", "headers", "Object", "keys", "csvRows", "join", "values", "header", "value", "push", "batchApprove", "warning", "pendingRows", "promises", "Promise", "all", "responses", "successCount", "batchReject", "$prompt", "inputPlaceholder", "inputValidator", "trim", "quickApprove", "_row$client", "quickReject", "_row$client2", "getRemainingDays", "remainingDays", "Math", "ceil", "abs", "getRemainingDaysClass", "dismissNotifications", "showExpiringOrders", "showExpiredOrders", "showHighValueOrders"], "sources": ["src/views/pages/taocan/dingdan.vue"], "sourcesContent": ["<template>\r\n\t<div class=\"order-management-container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<div class=\"page-header\">\r\n\t\t\t<div class=\"header-left\">\r\n\t\t\t\t<h2 class=\"page-title\">\r\n\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t{{ this.$router.currentRoute.name }}\r\n\t\t\t\t</h2>\r\n\t\t\t\t<div class=\"page-subtitle\">管理客户签约订单和合同信息</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t<el-button\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\ticon=\"el-icon-refresh\"\r\n\t\t\t\t\t\t@click=\"refulsh\"\r\n\t\t\t\t\t\tclass=\"refresh-btn\"\r\n\t\t\t\t>\r\n\t\t\t\t\t刷新数据\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 重要通知和提醒区域 -->\r\n\t\t<div class=\"notifications-section\" v-if=\"hasImportantNotifications\">\r\n\t\t\t<el-card shadow=\"hover\" class=\"notification-card\">\r\n\t\t\t\t<div class=\"notification-header\">\r\n\t\t\t\t\t<i class=\"el-icon-warning-outline notification-icon\"></i>\r\n\t\t\t\t\t<span class=\"notification-title\">重要提醒</span>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tsize=\"mini\" \r\n\t\t\t\t\t\t@click=\"dismissNotifications\"\r\n\t\t\t\t\t\tclass=\"dismiss-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"notification-content\">\r\n\t\t\t\t\t<div class=\"notification-list\">\r\n\t\t\t\t\t\t<!-- 待审核订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"pendingOrders > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item urgent\"\r\n\t\t\t\t\t\t\t@click=\"filterByStatus('1')\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ pendingOrders }}</strong> 个订单待审核，请及时处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"warning\">立即处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 即将到期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiringOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item warning\"\r\n\t\t\t\t\t\t\t@click=\"showExpiringOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiringOrders.length }}</strong> 个订单即将到期，请提醒客户续费\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\">查看详情</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 已过期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiredOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item error\"\r\n\t\t\t\t\t\t\t@click=\"showExpiredOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiredOrders.length }}</strong> 个订单已过期，需要联系客户处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\">紧急处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 高价值订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"highValueOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item info\"\r\n\t\t\t\t\t\t\t@click=\"showHighValueOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ highValueOrders.length }}</strong> 个高价值订单需要重点关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"success\">查看订单</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\r\n\t\t<!-- 统计卡片区域 -->\r\n\t\t<div class=\"stats-section\">\r\n\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon total-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ total }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总订单数</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +8%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('1')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon pending-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">待审核</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change warning\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i> 需关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('2')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon approved-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-circle-check\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">已通过</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +12%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"showRevenueChart\">\r\n\t\t\t\t\t\t<div class=\"stat-icon revenue-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-money\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">¥{{ totalRevenue }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总收入</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +15%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t</div>\r\n\r\n\t\t<!-- 搜索和筛选区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"search-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-search\"></i>\r\n\t\t\t\t\t\t搜索与筛选\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"card-subtitle\">快速查找和管理订单信息</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t\t<el-button-group class=\"action-group\">\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t\t导出\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n\t\t\t\t\t\t\t刷新\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</el-button-group>\r\n\t\t\t\t\t<el-button type=\"primary\" @click=\"batchAudit\" icon=\"el-icon-check\" class=\"primary-action\">\r\n\t\t\t\t\t\t批量审核\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"search-section\">\r\n\t\t\t\t<el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n\t\t\t\t\t<div class=\"search-row\">\r\n\t\t\t\t\t\t<el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tprefix-icon=\"el-icon-search\"\r\n\t\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t\t\********************=\"getData()\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"业务员\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.salesman\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tclass=\"search-select\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"审核状态\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-select v-model=\"search.status\" placeholder=\"选择状态\" clearable class=\"search-select\">\r\n\t\t\t\t\t\t\t\t<el-option label=\"全部状态\" value=\"\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未审核\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"已通过\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未通过\" value=\"3\" />\r\n\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item class=\"search-actions-item\">\r\n\t\t\t\t\t\t\t<div class=\"search-actions\">\r\n\t\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\" class=\"search-btn\">\r\n\t\t\t\t\t\t\t\t\t搜索\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n\t\t\t\t\t\t\t\t\t重置\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n\t\t\t\t\t\t\t\t\t<i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ showAdvanced ? '收起' : '高级筛选' }}\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 高级筛选区域 -->\r\n\t\t\t\t\t<transition name=\"slide-fade\">\r\n\t\t\t\t\t\t<div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n\t\t\t\t\t\t\t<el-divider content-position=\"left\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-setting\"></i>\r\n\t\t\t\t\t\t\t\t高级筛选选项\r\n\t\t\t\t\t\t\t</el-divider>\r\n\t\t\t\t\t\t\t<div class=\"advanced-content\">\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付时间\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"date-picker\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.payType\" placeholder=\"选择支付方式\" class=\"pay-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部方式\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全款支付\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"分期付款\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"金额范围\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"amount-range\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.minAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最小金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"range-separator\">-</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.maxAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最大金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"套餐类型\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.packageType\" placeholder=\"选择套餐\" clearable class=\"package-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部套餐\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"基础套餐\" value=\"basic\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"高级套餐\" value=\"advanced\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"专业套餐\" value=\"professional\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.sortBy\" placeholder=\"排序字段\" class=\"sort-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"创建时间\" value=\"create_time\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"支付金额\" value=\"pay_age\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"订单状态\" value=\"status\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"到期时间\" value=\"end_time\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item class=\"advanced-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n\t\t\t\t\t\t\t\t\t\t\t应用筛选\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n\t\t\t\t\t\t\t\t\t\t\t清空选项\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</transition>\r\n\t\t\t\t</el-form>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 数据表格区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"table-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-tickets\"></i>\r\n\t\t\t\t\t\t订单列表\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"selected-info\" v-if=\"selectedRows.length > 0\">\r\n\t\t\t\t\t\t已选择 {{ selectedRows.length }} 项\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"table-actions\">\r\n\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t导出数据\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchApprove\" \r\n\t\t\t\t\t\ticon=\"el-icon-check\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量通过\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchReject\" \r\n\t\t\t\t\t\ticon=\"el-icon-close\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"danger\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量拒绝\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<el-table \r\n\t\t\t\t\t:data=\"list\" \r\n\t\t\t\t\tv-loading=\"loading\" \r\n\t\t\t\t\tclass=\"modern-table\"\r\n\t\t\t\t\t:row-class-name=\"tableRowClassName\"\r\n\t\t\t\t\t@selection-change=\"handleSelectionChange\"\r\n\t\t\t>\r\n\t\t\t\t<el-table-column type=\"selection\" width=\"55\" />\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"客户信息\" min-width=\"200\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"client-info\" @click=\"viewUserData(scope.row.client?.id)\">\r\n\t\t\t\t\t\t\t<div class=\"client-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"client-avatar\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-office-building\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"client-details\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"company-name\">\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.company || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-info\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"contact-name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-phone\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-phone\"></i>\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"套餐内容\" min-width=\"180\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"package-info\">\r\n\t\t\t\t\t\t\t<div class=\"package-name\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.taocan?.title || '未选择套餐' }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-price\">\r\n\t\t\t\t\t\t\t\t<span class=\"price-label\">价格：</span>\r\n\t\t\t\t\t\t\t\t<span class=\"price-value\">¥{{ scope.row.taocan?.price || 0 }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-duration\">\r\n\t\t\t\t\t\t\t\t<el-tag size=\"small\" type=\"info\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.taocan?.year || 0 }}年服务\r\n\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"支付情况\" min-width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"payment-info\">\r\n\t\t\t\t\t\t\t<div class=\"payment-type\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-wallet\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.pay_type == 1 ? \"全款\" : `分期/${scope.row.qishu}期` }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-amount\">\r\n\t\t\t\t\t\t\t\t<span class=\"paid\">已付：¥{{ scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-amount\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<span class=\"remaining\">余款：¥{{ scope.row.total_price - scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-progress\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<el-progress \r\n\t\t\t\t\t\t\t\t\t:percentage=\"Math.round((scope.row.pay_age / scope.row.total_price) * 100)\" \r\n\t\t\t\t\t\t\t\t\t:stroke-width=\"6\"\r\n\t\t\t\t\t\t\t\t\t:show-text=\"false\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"审核状态\" width=\"120\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"status-info\" @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t:type=\"getStatusType(scope.row.status)\" \r\n\t\t\t\t\t\t\t\t\tclass=\"status-tag\"\r\n\t\t\t\t\t\t\t\t\t:effect=\"scope.row.status == 1 ? 'plain' : 'dark'\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{{ getStatusText(scope.row.status) }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t<div v-if=\"scope.row.status == 3\" class=\"status-reason\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.status_msg }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\" width=\"100\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"member-info\">\r\n\t\t\t\t\t\t\t<el-tag type=\"info\" size=\"small\">\r\n\t\t\t\t\t\t\t\t{{ scope.row.member?.title || '未分配' }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"时间信息\" min-width=\"140\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"time-info\">\r\n\t\t\t\t\t\t\t<div class=\"create-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t\t\t创建：{{ formatDate(scope.row.create_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"end-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-date\"></i>\r\n\t\t\t\t\t\t\t\t到期：{{ formatDate(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-days\" :class=\"getRemainingDaysClass(scope.row.end_time)\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t\t{{ getRemainingDays(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\" width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"editData(scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"view-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-view\"></i>\r\n\t\t\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickApprove(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"approve-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-check\"></i>\r\n\t\t\t\t\t\t\t\t\t通过\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickReject(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"reject-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t\t\t\t\t拒绝\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"delData(scope.$index, scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"delete-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-delete\"></i>\r\n\t\t\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t\r\n\t\t\t<!-- 分页 -->\r\n\t\t\t<div class=\"pagination-wrapper\">\r\n\t\t\t\t<el-pagination\r\n\t\t\t\t\t\t@size-change=\"handleSizeChange\"\r\n\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t\t:page-sizes=\"[20, 50, 100, 200]\"\r\n\t\t\t\t\t\t:page-size=\"size\"\r\n\t\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\"\r\n\t\t\t\t\t\t:total=\"total\"\r\n\t\t\t\t\t\tbackground\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 订单详情对话框 -->\r\n\t\t<el-dialog \r\n\t\t\t\ttitle=\"订单详情\" \r\n\t\t\t\t:visible.sync=\"dialogFormVisible\" \r\n\t\t\t\t:close-on-click-modal=\"false\" \r\n\t\t\t\twidth=\"85%\"\r\n\t\t\t\tclass=\"order-detail-dialog\"\r\n\t\t>\r\n\t\t\t\t<div v-if=\"is_info\" class=\"order-detail-content\">\r\n\t\t\t\t\t\t<!-- 客户信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t客户信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"公司名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"info\">{{ info.client?.company || '未填写' }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系人\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系方式\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"showImage(info.client?.pic_path)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client?.pic_path\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t查看执照\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag v-else type=\"info\">暂无</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"调解员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.tiaojie_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"法务专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.fawu_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"立案专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.lian_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"合同专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.htsczy_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"指定律师\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.ls_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 债务人信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.debts && info.debts.length > 0\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user-solid\"></i>\r\n\t\t\t\t\t\t\t\t\t\t债务人信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-table :data=\"info.debts\" size=\"medium\" class=\"debt-table\">\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"name\" label=\"债务人姓名\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"tel\" label=\"联系电话\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"money\" label=\"债务金额（元）\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"status\" label=\"状态\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag :type=\"getDebtStatusType(scope.row.status)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.status }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t</el-table>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 套餐信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.taocan\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t\t\t套餐信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"primary\">{{ info.taocan.title }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-highlight\">¥{{ info.taocan.price }}</span>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"服务年限\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"success\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t\t\t<el-button @click=\"dialogFormVisible = false\">关闭</el-button>\r\n\t\t\t\t\t\t<el-button type=\"primary\" @click=\"downloadOrder\">下载订单</el-button>\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 图片查看对话框 -->\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n\t\t\t\t<div class=\"image-viewer\">\r\n\t\t\t\t\t\t<el-image :src=\"show_image\" fit=\"contain\" />\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 收入统计图表对话框 -->\r\n\t\t<el-dialog title=\"收入统计分析\" :visible.sync=\"showRevenueDialog\" width=\"80%\" class=\"revenue-dialog\">\r\n\t\t\t<div class=\"revenue-stats\">\r\n\t\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>月度收入趋势</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-data-line chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>月度收入趋势图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"mock-chart-data\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 60%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 80%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 45%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 70%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 90%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 65%\"></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>支付方式分布</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-pie-chart chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>支付方式比例图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"payment-stats\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot full-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t全款支付: {{ fullPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot installment-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t分期付款: {{ installmentPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t\t<el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n\t\t\t\t\t<el-col :span=\"24\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>订单状态统计</h4>\r\n\t\t\t\t\t\t\t<div class=\"status-overview\">\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle pending-circle\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>待审核</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle approved-circle\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已通过</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle rejected-circle\">{{ rejectedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已拒绝</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle total-circle\">{{ total }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>总计</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 数据导出对话框 -->\r\n\t\t<el-dialog title=\"数据导出\" :visible.sync=\"showExportDialog\" width=\"600px\" class=\"export-dialog\">\r\n\t\t\t<el-form :model=\"exportForm\" label-width=\"120px\">\r\n\t\t\t\t<el-form-item label=\"导出格式\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.format\">\r\n\t\t\t\t\t\t<el-radio label=\"excel\">Excel (.xlsx)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"csv\">CSV (.csv)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"pdf\">PDF (.pdf)</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"导出内容\">\r\n\t\t\t\t\t<el-checkbox-group v-model=\"exportForm.fields\">\r\n\t\t\t\t\t\t<el-checkbox label=\"client\">客户信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"package\">套餐信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"payment\">支付情况</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"status\">审核状态</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"time\">时间信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"member\">业务员信息</el-checkbox>\r\n\t\t\t\t\t</el-checkbox-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"数据范围\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.range\">\r\n\t\t\t\t\t\t<el-radio label=\"all\">全部数据</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"current\">当前页面</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"selected\">选中项目</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"filtered\">筛选结果</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"时间范围\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tv-model=\"exportForm.dateRange\"\r\n\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tstyle=\"width: 100%\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"showExportDialog = false\">取消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"executeExport\" :loading=\"exportLoading\">\r\n\t\t\t\t\t<i class=\"el-icon-download\"></i>\r\n\t\t\t\t\t开始导出\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t\tallSize: \"mini\",\r\n\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\ttotal: 1,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tsize: 20,\r\n\t\t\t\t\t\tsearch: {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tloading: true,\r\n\t\t\t\t\t\turl: \"/dingdan/\",\r\n\t\t\t\t\t\ttitle: \"签约用户\",\r\n\t\t\t\t\t\tinfo: {},\r\n\t\t\t\t\t\tcurrentId:0,\r\n\t\t\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\t\t\tshow_image: \"\",\r\n\t\t\t\t\t\tdialogVisible: false,\r\n\t\t\t\t\t\tdialogViewUserDetail:false,\r\n\t\t\t\t\t\tis_info: false,\r\n\t\t\t\t\t\tupload_index: \"\",\r\n\t\t\t\t\t\tdialogStatus: false,\r\n\t\t\t\t\t\tdialogEndTime: false,\r\n\t\t\t\t\t\truleForm: {\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tstatus_msg: \"\",\r\n\t\t\t\t\t\t\t\tend_time: \"\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\trules: {\r\n\t\t\t\t\t\t\t\tstatus_msg: [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\t\t\tshowAdvanced: false,\r\n\t\t\t\t\t\tselectedRows: [],\r\n\t\t\t\t\t\tshowRevenueDialog: false,\r\n\t\t\t\t\t\tshowExportDialog: false,\r\n\t\t\t\t\t\texportForm: {\r\n\t\t\t\t\t\t\t\tformat: \"excel\",\r\n\t\t\t\t\t\t\t\tfields: [\"client\", \"package\", \"payment\", \"status\", \"time\", \"member\"],\r\n\t\t\t\t\t\t\t\trange: \"all\",\r\n\t\t\t\t\t\t\t\tdateRange: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\texportLoading: false,\r\n\t\t\t\t\t\tfullPaymentCount: 0,\r\n\t\t\t\t\t\tinstallmentPaymentCount: 0,\r\n\t\t\t\t\t\tshowNotifications: true\r\n\t\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t\t// 统计数据计算\r\n\t\t\t\tpendingOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tapprovedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 2).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\trejectedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 3).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\ttotalRevenue() {\r\n\t\t\t\t\t\treturn this.list.reduce((sum, item) => sum + (item.pay_age || 0), 0).toLocaleString();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tfullPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tinstallmentPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type !== 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t// 即将到期的订单（7天内）\r\n\t\t\t\texpiringOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\tconst sevenDaysLater = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate >= today && endDate <= sevenDaysLater;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 已过期的订单\r\n\t\t\t\texpiredOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate < today;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 高价值订单（金额超过5000）\r\n\t\t\t\thighValueOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => (item.pay_age || 0) > 5000);\r\n\t\t\t\t},\r\n\t\t\t\t// 是否有重要通知\r\n\t\t\t\thasImportantNotifications() {\r\n\t\t\t\t\t\treturn this.showNotifications && (\r\n\t\t\t\t\t\t\t\tthis.pendingOrders > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiringOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiredOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.highValueOrders.length > 0\r\n\t\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\teditData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tviewUserData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t\t},\r\n\t\t\t\tgetInfo(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tdelData(index, id) {\r\n\t\t\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\r\n\t\t\t\tupdateEndTIme() {\r\n\t\t\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\trefulsh() {\r\n\t\t\t\t\t\tthis.$router.go(0);\r\n\t\t\t\t},\r\n\t\t\t\tgetData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.loading = true;\r\n\t\t\t\t\t\t_this\r\n\t\t\t\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tsaveData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\thandleSizeChange(val) {\r\n\t\t\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleCurrentChange(val) {\r\n\t\t\t\t\t\tthis.page = val;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleSuccess(res) {\r\n\t\t\t\t\t\tlet _this = this\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t},\r\n\t\t\t\tbeforeUpload(file) {\r\n\t\t\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdelImage(file, fileName) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tshowImage(file) {\r\n\t\t\t\t\t\tthis.show_image = file;\r\n\t\t\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tchangePinzhen(index) {\r\n\t\t\t\t\t\tthis.index = index\r\n\t\t\t\t},\r\n\t\t\t\tshowStatus(row) {\r\n\t\t\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tshowEndTime(row) {\r\n\t\t\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tchangeEndTime() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusType(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: 'warning',\r\n\t\t\t\t\t\t\t\t2: 'success', \r\n\t\t\t\t\t\t\t\t3: 'danger'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || 'info';\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusText(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: '未审核',\r\n\t\t\t\t\t\t\t\t2: '已通过',\r\n\t\t\t\t\t\t\t\t3: '未通过'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || '未知';\r\n\t\t\t\t},\r\n\t\t\t\tgetDebtStatusType(status) {\r\n\t\t\t\t\t\t// 债务状态类型映射\r\n\t\t\t\t\t\tif (status === '已解决') return 'success';\r\n\t\t\t\t\t\tif (status === '处理中') return 'warning';\r\n\t\t\t\t\t\treturn 'info';\r\n\t\t\t\t},\r\n\t\t\t\tformatDate(dateStr) {\r\n\t\t\t\t\t\tif (!dateStr) return '未设置';\r\n\t\t\t\t\t\treturn new Date(dateStr).toLocaleDateString('zh-CN');\r\n\t\t\t\t},\r\n\t\t\t\ttableRowClassName({row, rowIndex}) {\r\n\t\t\t\t\t\tif (row.status === 1) return 'warning-row';\r\n\t\t\t\t\t\tif (row.status === 3) return 'danger-row';\r\n\t\t\t\t\t\treturn '';\r\n\t\t\t\t},\r\n\t\t\t\tresetSearch() {\r\n\t\t\t\t\t\tthis.search = {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\texportData() {\r\n\t\t\t\t\tthis.showExportDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\tdownloadOrder() {\r\n\t\t\t\t\t\tthis.$message.success('订单下载功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\ttoggleAdvanced() {\r\n\t\t\t\t\t\tthis.showAdvanced = !this.showAdvanced;\r\n\t\t\t\t},\r\n\t\t\t\tapplyAdvancedSearch() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tclearAdvancedSearch() {\r\n\t\t\t\t\t\tthis.resetSearch();\r\n\t\t\t\t},\r\n\t\t\t\trefreshData() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tbatchAudit() {\r\n\t\t\t\t\t\tthis.$message.success('批量审核功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\thandleSelectionChange(selection) {\r\n\t\t\t\t\t\tthis.selectedRows = selection;\r\n\t\t\t\t},\r\n\t\t\t\tfilterByStatus(status) {\r\n\t\t\t\t\tthis.search.status = status;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t\tthis.$message.success(`已筛选${this.getStatusText(status) || '全部'}订单`);\r\n\t\t\t\t},\r\n\t\t\t\tshowRevenueChart() {\r\n\t\t\t\t\tthis.showRevenueDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\texecuteExport() {\r\n\t\t\t\t\tthis.exportLoading = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟导出过程\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst formatText = {\r\n\t\t\t\t\t\t\t'excel': 'Excel',\r\n\t\t\t\t\t\t\t'csv': 'CSV', \r\n\t\t\t\t\t\t\t'pdf': 'PDF'\r\n\t\t\t\t\t\t}[this.exportForm.format];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst rangeText = {\r\n\t\t\t\t\t\t\t'all': '全部数据',\r\n\t\t\t\t\t\t\t'current': '当前页面',\r\n\t\t\t\t\t\t\t'selected': '选中项目',\r\n\t\t\t\t\t\t\t'filtered': '筛选结果'\r\n\t\t\t\t\t\t}[this.exportForm.range];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 这里可以集成真实的导出库，如 xlsx、jsPDF等\r\n\t\t\t\t\t\tconst blob = this.generateExportData();\r\n\t\t\t\t\t\tconst url = URL.createObjectURL(blob);\r\n\t\t\t\t\t\tconst link = document.createElement('a');\r\n\t\t\t\t\t\tlink.href = url;\r\n\t\t\t\t\t\tlink.download = `订单数据_${new Date().toISOString().split('T')[0]}.${this.exportForm.format}`;\r\n\t\t\t\t\t\tlink.click();\r\n\t\t\t\t\t\tURL.revokeObjectURL(url);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.exportLoading = false;\r\n\t\t\t\t\t\tthis.showExportDialog = false;\r\n\t\t\t\t\t\tthis.$message.success(`${formatText}格式的${rangeText}导出成功！`);\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t},\r\n\t\t\t\tgenerateExportData() {\r\n\t\t\t\t\t// 根据选择的范围获取数据\r\n\t\t\t\t\tlet exportList = [];\r\n\t\t\t\t\tswitch(this.exportForm.range) {\r\n\t\t\t\t\t\tcase 'all':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'current':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'selected':\r\n\t\t\t\t\t\t\texportList = this.selectedRows;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'filtered':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据选择的字段生成数据\r\n\t\t\t\t\tconst data = exportList.map(item => {\r\n\t\t\t\t\t\tconst row = {};\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('client')) {\r\n\t\t\t\t\t\t\trow['公司名称'] = item.client?.company || '';\r\n\t\t\t\t\t\t\trow['联系人'] = item.client?.linkman || '';\r\n\t\t\t\t\t\t\trow['联系电话'] = item.client?.phone || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('package')) {\r\n\t\t\t\t\t\t\trow['套餐名称'] = item.taocan?.title || '';\r\n\t\t\t\t\t\t\trow['套餐价格'] = item.taocan?.price || 0;\r\n\t\t\t\t\t\t\trow['服务年限'] = item.taocan?.year || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('payment')) {\r\n\t\t\t\t\t\t\trow['支付方式'] = item.pay_type == 1 ? '全款' : '分期';\r\n\t\t\t\t\t\t\trow['已付金额'] = item.pay_age || 0;\r\n\t\t\t\t\t\t\trow['总金额'] = item.total_price || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('status')) {\r\n\t\t\t\t\t\t\trow['审核状态'] = this.getStatusText(item.status);\r\n\t\t\t\t\t\t\trow['状态说明'] = item.status_msg || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('time')) {\r\n\t\t\t\t\t\t\trow['创建时间'] = item.create_time || '';\r\n\t\t\t\t\t\t\trow['到期时间'] = item.end_time || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('member')) {\r\n\t\t\t\t\t\t\trow['业务员'] = item.member?.title || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn row;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 生成CSV格式的Blob\r\n\t\t\t\t\tconst csvContent = this.convertToCSV(data);\r\n\t\t\t\t\treturn new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\t\t\t\t},\r\n\t\t\t\tconvertToCSV(data) {\r\n\t\t\t\t\tif (!data || data.length === 0) return '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst headers = Object.keys(data[0]);\r\n\t\t\t\t\tconst csvRows = [headers.join(',')];\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor (const row of data) {\r\n\t\t\t\t\t\tconst values = headers.map(header => {\r\n\t\t\t\t\t\t\tconst value = row[header];\r\n\t\t\t\t\t\t\treturn typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tcsvRows.push(values.join(','));\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn csvRows.join('\\n');\r\n\t\t\t\t},\r\n\t\t\t\tbatchApprove() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量通过的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$confirm(`确认批量通过选中的 ${pendingRows.length} 个待审核订单吗？`, '批量审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t\t'status_msg': '批量审核通过'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量审核完成，成功通过 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量审核过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量审核');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tbatchReject() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量拒绝的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$prompt('请输入批量拒绝理由', `批量拒绝 ${pendingRows.length} 个订单`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量拒绝完成，成功拒绝 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量拒绝过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量拒绝');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickApprove(row) {\r\n\t\t\t\t\tthis.$confirm(`确认通过 ${row.client?.company || '该客户'} 的订单吗？`, '快速审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\t// 调用API通过订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t'status_msg': '快速审核通过'\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('审核通过成功');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickReject(row) {\r\n\t\t\t\t\tthis.$prompt('请输入拒绝理由', `拒绝订单 - ${row.client?.company || '客户'}`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\t// 调用API拒绝订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('订单已拒绝');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDays(end_time) {\r\n\t\t\t\t\tif (!end_time) return '未设置';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return `已过期${Math.abs(remainingDays)}天`;\r\n\t\t\t\t\tif (remainingDays === 0) return '今天到期';\r\n\t\t\t\t\tif (remainingDays <= 7) return `${remainingDays}天后到期`;\r\n\t\t\t\t\tif (remainingDays <= 30) return `${remainingDays}天后到期`;\r\n\t\t\t\t\treturn `${remainingDays}天后到期`;\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDaysClass(end_time) {\r\n\t\t\t\t\tif (!end_time) return '';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return 'expired';\r\n\t\t\t\t\tif (remainingDays <= 3) return 'urgent';\r\n\t\t\t\t\tif (remainingDays <= 7) return 'warning';\r\n\t\t\t\t\treturn 'normal';\r\n\t\t\t\t},\r\n\t\t\t\t// 通知系统相关方法\r\n\t\t\t\tdismissNotifications() {\r\n\t\t\t\t\tthis.showNotifications = false;\r\n\t\t\t\t\tthis.$message.success('已暂时隐藏提醒通知');\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiringOrders() {\r\n\t\t\t\t\t// 显示即将到期的订单\r\n\t\t\t\t\tthis.$message.info(`查看${this.expiringOrders.length}个即将到期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiredOrders() {\r\n\t\t\t\t\t// 显示已过期的订单\r\n\t\t\t\t\tthis.$message.warning(`查看${this.expiredOrders.length}个已过期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowHighValueOrders() {\r\n\t\t\t\t\t// 显示高价值订单\r\n\t\t\t\t\tthis.$message.success(`查看${this.highValueOrders.length}个高价值订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n.order-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card:active {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.pending-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.revenue-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n.stat-change.warning {\r\n  color: #f39c12;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker,\r\n.pay-select,\r\n.package-select,\r\n.sort-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner,\r\n.pay-select .el-input__inner,\r\n.package-select .el-input__inner,\r\n.sort-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus,\r\n.pay-select .el-input__inner:focus,\r\n.package-select .el-input__inner:focus,\r\n.sort-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.amount-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.amount-range .el-input-number {\r\n  flex: 1;\r\n}\r\n\r\n.range-separator {\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n  padding: 0 4px;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 选择信息样式 */\r\n.selected-info {\r\n  font-size: 13px;\r\n  color: #667eea;\r\n  margin-top: 4px;\r\n  font-weight: 500;\r\n  background: rgba(102, 126, 234, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 表格行样式 */\r\n.modern-table .warning-row {\r\n  background-color: #fff7e6 !important;\r\n}\r\n\r\n.modern-table .danger-row {\r\n  background-color: #fff2f0 !important;\r\n}\r\n\r\n/* 客户信息样式 */\r\n.client-info {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.client-info:hover {\r\n  background-color: #f8f9ff;\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n  margin: -8px;\r\n}\r\n\r\n.client-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.client-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.client-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.company-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.contact-info, .contact-phone {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 2px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 套餐信息样式 */\r\n.package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.package-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.package-price {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.price-label {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.package-duration {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 支付信息样式 */\r\n.payment-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-type {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.payment-amount, .remaining-amount {\r\n  font-size: 13px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.paid {\r\n  color: #27ae60;\r\n  font-weight: 500;\r\n}\r\n\r\n.remaining {\r\n  color: #e74c3c;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-progress {\r\n  margin-top: 8px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__outer {\r\n  background-color: #f0f0f0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__inner {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 业务员信息样式 */\r\n.member-info {\r\n  text-align: center;\r\n}\r\n\r\n.member-info .el-tag {\r\n  font-size: 12px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 状态信息样式 */\r\n.status-info {\r\n  cursor: pointer;\r\n  text-align: center;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-reason {\r\n  font-size: 12px;\r\n  color: #e74c3c;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n}\r\n\r\n.create-time, .end-time {\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.remaining-days {\r\n  margin-top: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  text-align: center;\r\n}\r\n\r\n.remaining-days.normal {\r\n  background-color: #f0f9ff;\r\n  color: #1e40af;\r\n  border: 1px solid #dbeafe;\r\n}\r\n\r\n.remaining-days.warning {\r\n  background-color: #fef3c7;\r\n  color: #b45309;\r\n  border: 1px solid #fde68a;\r\n}\r\n\r\n.remaining-days.urgent {\r\n  background-color: #fee2e2;\r\n  color: #dc2626;\r\n  border: 1px solid #fecaca;\r\n}\r\n\r\n.remaining-days.expired {\r\n  background-color: #f3f4f6;\r\n  color: #6b7280;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.view-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.approve-btn {\r\n  color: #67C23A !important;\r\n}\r\n\r\n.reject-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.view-btn:hover, .approve-btn:hover, .reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n.approve-btn:hover {\r\n  background-color: rgba(103, 194, 58, 0.1) !important;\r\n}\r\n\r\n.reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(245, 108, 108, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-detail-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.order-detail-content {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border: none;\r\n}\r\n\r\n.detail-header {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.clickable-tag {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-tag:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-highlight {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.money-amount {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.debt-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .order-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-input-number {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .amount-range {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .client-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .selected-info {\r\n    margin-top: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n  \r\n  .remaining-days {\r\n    font-size: 11px;\r\n    padding: 2px 6px;\r\n  }\r\n  \r\n  .payment-progress {\r\n    margin-top: 6px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .action-buttons .el-button {\r\n    font-size: 12px;\r\n    padding: 4px 8px;\r\n  }\r\n  \r\n  .table-actions .el-button {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 收入统计图表样式 */\r\n.revenue-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.revenue-stats {\r\n  min-height: 400px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #f0f0f0;\r\n  height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-card h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 12px;\r\n  color: #667eea;\r\n}\r\n\r\n.chart-placeholder p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.mock-chart-data {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 8px;\r\n  height: 100px;\r\n  width: 200px;\r\n}\r\n\r\n.chart-bar {\r\n  flex: 1;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 4px 4px 0 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chart-bar:hover {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.payment-stats {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.payment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.payment-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.full-payment {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.installment-payment {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.status-overview {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.status-circle {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.status-circle:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.pending-circle {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-circle {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.rejected-circle {\r\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\r\n}\r\n\r\n.total-circle {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.status-item span {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据导出对话框样式 */\r\n.export-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.export-dialog .el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.export-dialog .el-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox-group {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox {\r\n  margin: 0;\r\n}\r\n\r\n.export-dialog .el-form-item__label {\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-dialog .el-radio {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.export-dialog .dialog-footer {\r\n  padding: 16px 0 0 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button {\r\n  margin-left: 12px;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 响应式图表样式 */\r\n@media (max-width: 768px) {\r\n  .revenue-stats .el-col {\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .chart-card {\r\n    height: auto;\r\n    min-height: 250px;\r\n  }\r\n  \r\n  .status-overview {\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .status-circle {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .export-dialog .el-checkbox-group {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 通知系统样式 */\r\n.notifications-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 12px;\r\n  border: 1px solid #ffd93d;\r\n  background: linear-gradient(135deg, #fff9c4 0%, #ffffff 100%);\r\n  box-shadow: 0 4px 20px rgba(255, 217, 61, 0.3);\r\n}\r\n\r\n.notification-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px 20px 12px 20px;\r\n  border-bottom: 1px solid rgba(255, 217, 61, 0.2);\r\n}\r\n\r\n.notification-icon {\r\n  font-size: 20px;\r\n  color: #f39c12;\r\n}\r\n\r\n.notification-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.dismiss-btn {\r\n  color: #7f8c8d !important;\r\n  padding: 4px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.dismiss-btn:hover {\r\n  color: #e74c3c !important;\r\n  background: rgba(231, 76, 60, 0.1) !important;\r\n}\r\n\r\n.notification-content {\r\n  padding: 16px 20px 20px 20px;\r\n}\r\n\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.notification-item:hover {\r\n  transform: translateX(4px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.notification-item.urgent {\r\n  background: linear-gradient(135deg, #fee2e2 0%, #fef7f7 100%);\r\n  border-color: rgba(239, 68, 68, 0.2);\r\n}\r\n\r\n.notification-item.urgent:hover {\r\n  background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%);\r\n}\r\n\r\n.notification-item.warning {\r\n  background: linear-gradient(135deg, #fef3c7 0%, #fefdf8 100%);\r\n  border-color: rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.notification-item.warning:hover {\r\n  background: linear-gradient(135deg, #fde68a 0%, #fef3c7 100%);\r\n}\r\n\r\n.notification-item.error {\r\n  background: linear-gradient(135deg, #ffebee 0%, #fafafa 100%);\r\n  border-color: rgba(244, 67, 54, 0.2);\r\n}\r\n\r\n.notification-item.error:hover {\r\n  background: linear-gradient(135deg, #ffcdd2 0%, #ffebee 100%);\r\n}\r\n\r\n.notification-item.info {\r\n  background: linear-gradient(135deg, #e3f2fd 0%, #fafafa 100%);\r\n  border-color: rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.notification-item.info:hover {\r\n  background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 100%);\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.notification-item.urgent .notification-dot {\r\n  background: #ef4444;\r\n}\r\n\r\n.notification-item.warning .notification-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.notification-item.error .notification-dot {\r\n  background: #f44336;\r\n}\r\n\r\n.notification-item.info .notification-dot {\r\n  background: #2196f3;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    opacity: 0.7;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.notification-text {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  line-height: 1.5;\r\n}\r\n\r\n.notification-text strong {\r\n  color: #e74c3c;\r\n  font-weight: 600;\r\n}\r\n\r\n.notification-action {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.notification-action .el-button {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.notification-action .el-button:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.export-dialog .el-checkbox-group {\r\n  grid-template-columns: 1fr;\r\n}\r\n\r\n.notifications-section {\r\n  margin: 0 -8px 16px -8px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 8px;\r\n  margin: 0 8px;\r\n}\r\n\r\n.notification-header {\r\n  padding: 12px 16px 8px 16px;\r\n}\r\n\r\n.notification-content {\r\n  padding: 12px 16px 16px 16px;\r\n}\r\n\r\n.notification-item {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.notification-item .notification-dot {\r\n  order: -1;\r\n  align-self: flex-start;\r\n}\r\n\r\n.notification-text {\r\n  order: 0;\r\n  width: 100%;\r\n}\r\n\r\n.notification-action {\r\n  order: 1;\r\n  align-self: flex-end;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AA4xBA;AACA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,oBAAA;MACAC,OAAA;MACAC,YAAA;MACAC,YAAA;MACAC,aAAA;MACAC,QAAA;QACAnB,MAAA;QACAoB,UAAA;QACAC,QAAA;MACA;MACAC,KAAA;QACAF,UAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;MACAC,YAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,aAAA;MACAC,gBAAA;MACAC,uBAAA;MACAC,iBAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,cAAA;MACA,YAAAjD,IAAA,CAAAkD,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3C,MAAA,QAAA4C,MAAA;IACA;IAEAC,eAAA;MACA,YAAArD,IAAA,CAAAkD,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3C,MAAA,QAAA4C,MAAA;IACA;IAEAE,eAAA;MACA,YAAAtD,IAAA,CAAAkD,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3C,MAAA,QAAA4C,MAAA;IACA;IAEAG,aAAA;MACA,YAAAvD,IAAA,CAAAwD,MAAA,EAAAC,GAAA,EAAAN,IAAA,KAAAM,GAAA,IAAAN,IAAA,CAAAO,OAAA,WAAAC,cAAA;IACA;IAEAd,iBAAA;MACA,YAAA7C,IAAA,CAAAkD,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAS,QAAA,QAAAR,MAAA;IACA;IAEAN,wBAAA;MACA,YAAA9C,IAAA,CAAAkD,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAS,QAAA,QAAAR,MAAA;IACA;IACA;IACAS,eAAA;MACA,MAAAC,KAAA,OAAAC,IAAA;MACA,MAAAC,cAAA,OAAAD,IAAA,CAAAD,KAAA,CAAAG,OAAA;MACA,YAAAjE,IAAA,CAAAkD,MAAA,CAAAC,IAAA;QACA,KAAAA,IAAA,CAAAtB,QAAA;QACA,MAAAqC,OAAA,OAAAH,IAAA,CAAAZ,IAAA,CAAAtB,QAAA;QACA,OAAAqC,OAAA,IAAAJ,KAAA,IAAAI,OAAA,IAAAF,cAAA;MACA;IACA;IACA;IACAG,cAAA;MACA,MAAAL,KAAA,OAAAC,IAAA;MACA,YAAA/D,IAAA,CAAAkD,MAAA,CAAAC,IAAA;QACA,KAAAA,IAAA,CAAAtB,QAAA;QACA,MAAAqC,OAAA,OAAAH,IAAA,CAAAZ,IAAA,CAAAtB,QAAA;QACA,OAAAqC,OAAA,GAAAJ,KAAA;MACA;IACA;IACA;IACAM,gBAAA;MACA,YAAApE,IAAA,CAAAkD,MAAA,CAAAC,IAAA,KAAAA,IAAA,CAAAO,OAAA;IACA;IACA;IACAW,0BAAA;MACA,YAAAtB,iBAAA,KACA,KAAAE,aAAA,QACA,KAAAY,cAAA,CAAAT,MAAA,QACA,KAAAe,aAAA,CAAAf,MAAA,QACA,KAAAgB,eAAA,CAAAhB,MAAA,KACA;IACA;EACA;EACAkB,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAA/C,QAAA;UACAX,KAAA;QACA;MACA;MACA2D,KAAA,CAAAxD,iBAAA;IACA;IACA0D,aAAAH,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAxD,SAAA,GAAAwD,EAAA;MACA;MAEAC,KAAA,CAAArD,oBAAA;IACA;IACAsD,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAG,UAAA,CAAAH,KAAA,CAAA5D,GAAA,gBAAA2D,EAAA,EAAAK,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAL,KAAA,CAAA1D,IAAA,GAAA+D,IAAA,CAAAlF,IAAA;UACA6E,KAAA,CAAApD,OAAA;QACA;MACA;IACA;IACA0D,QAAAC,KAAA,EAAAR,EAAA;MACA,KAAAS,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAxE,GAAA,kBAAA2D,EAAA,EAAAK,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACAtD,OAAA;YACA;YACA,KAAAhC,IAAA,CAAA0F,MAAA,CAAAR,KAAA;UACA;YACAP,KAAA,CAAAc,QAAA;cACAH,IAAA;cACAtD,OAAA,EAAAgD,IAAA,CAAAW;YACA;UACA;QACA;MACA,GACAC,KAAA;QACA,KAAAH,QAAA;UACAH,IAAA;UACAtD,OAAA;QACA;MACA;IACA;IAEA6D,cAAA;MACA,KAAAV,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,IAAAjF,IAAA;UAAA,WAAAmB,IAAA,CAAAyD,EAAA;UAAA,iBAAAzD,IAAA,CAAAY;QAAA;QACA,KAAAiE,WAAA,MAAA/E,GAAA,oBAAAjB,IAAA,EACAiF,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACAtD,OAAA;YACA;UACA;YACA2C,KAAA,CAAAc,QAAA;cACAH,IAAA;cACAtD,OAAA,EAAAgD,IAAA,CAAAW;YACA;UACA;QACA;MACA,GACAC,KAAA;QACA,KAAAH,QAAA;UACAH,IAAA;UACAtD,OAAA;QACA;MACA;IACA;IACA+D,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACA1B,QAAA;MACA,IAAAI,KAAA;MACAA,KAAA,CAAA7D,OAAA;MACA6D,KAAA,CACAmB,WAAA,CACAnB,KAAA,CAAA5D,GAAA,oBAAA4D,KAAA,CAAAzE,IAAA,cAAAyE,KAAA,CAAAxE,IAAA,EACAwE,KAAA,CAAAvE,MACA,EACA2E,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAA3E,IAAA,GAAAgF,IAAA,CAAAlF,IAAA;UACA6E,KAAA,CAAA1E,KAAA,GAAA+E,IAAA,CAAAkB,KAAA;QACA;QACAvB,KAAA,CAAA7D,OAAA;MACA;IACA;IACAqF,SAAA;MACA,IAAAxB,KAAA;MACA,KAAAyB,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAR,WAAA,CAAAnB,KAAA,CAAA5D,GAAA,gBAAAY,QAAA,EAAAoD,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAb,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA,EAAAgD,IAAA,CAAAW;cACA;cACAhB,KAAA,CAAAxD,iBAAA;YACA;cACAwD,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA,EAAAgD,IAAA,CAAAW;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAY,iBAAAC,GAAA;MACA,KAAArG,IAAA,GAAAqG,GAAA;MAEA,KAAAjC,OAAA;IACA;IACAkC,oBAAAD,GAAA;MACA,KAAAtG,IAAA,GAAAsG,GAAA;MACA,KAAAjC,OAAA;IACA;IACAmC,cAAAC,GAAA;MACA,IAAAhC,KAAA;MACA,IAAAgC,GAAA,CAAAnB,IAAA;QACAb,KAAA,CAAA1D,IAAA,CAAA2F,KAAA,CAAAjC,KAAA,CAAAO,KAAA,EAAA2B,QAAA,GAAAF,GAAA,CAAA7G,IAAA,CAAAiB,GAAA;QACA4D,KAAA,CAAAmB,WAAA,CAAAnB,KAAA,CAAA5D,GAAA;UACA,MAAA4D,KAAA,CAAA1D,IAAA,CAAAyD,EAAA;UACA,SAAAC,KAAA,CAAA1D,IAAA,CAAA2F;QACA,GAAA7B,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACAb,KAAA,CAAAc,QAAA;cACAH,IAAA;cACAtD,OAAA;YACA;UAEA;YACA2C,KAAA,CAAAc,QAAA;cACAH,IAAA;cACAtD,OAAA;YACA;UACA;QACA;MACA;IAGA;IACA8E,aAAAC,IAAA;MACA,MAAAC,UAAA,6BAAAC,IAAA,CAAAF,IAAA,CAAAzB,IAAA;MACA,KAAA0B,UAAA;QACA,KAAAvB,QAAA,CAAAyB,KAAA;QACA;MACA;IACA;IACAC,SAAAJ,IAAA,EAAAK,QAAA;MACA,IAAAzC,KAAA;MACAA,KAAA,CAAAG,UAAA,gCAAAiC,IAAA,EAAAhC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAAhD,QAAA,CAAAyF,QAAA;UAEAzC,KAAA,CAAAc,QAAA,CAAA4B,OAAA;QACA;UACA1C,KAAA,CAAAc,QAAA,CAAAyB,KAAA,CAAAlC,IAAA,CAAAW,GAAA;QACA;MACA;IACA;IACA2B,UAAAP,IAAA;MACA,KAAA3F,UAAA,GAAA2F,IAAA;MACA,KAAA1F,aAAA;IACA;IACAkG,cAAArC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACAsC,WAAAC,GAAA;MACA,KAAAhG,YAAA;MACA,KAAAE,QAAA,GAAA8F,GAAA;IACA;IACAC,YAAAD,GAAA;MACA,KAAA/F,aAAA;MACA,KAAAC,QAAA,GAAA8F,GAAA;IACA;IACAE,cAAA;MACA,IAAAhD,KAAA;MACA,KAAAyB,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA3B,KAAA,CAAAmB,WAAA,CAAAnB,KAAA,CAAA5D,GAAA;YACA,MAAA4D,KAAA,CAAAhD,QAAA,CAAA+C,EAAA;YACA,YAAAC,KAAA,CAAAhD,QAAA,CAAAE;UAEA,GAAAkD,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAb,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA;cACA;cACA2C,KAAA,CAAAlD,YAAA;YACA;cACAkD,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA,EAAAgD,IAAA,CAAAW;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAiC,aAAA;MAGA,IAAAjD,KAAA;MACA,KAAAyB,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA3B,KAAA,CAAAmB,WAAA,CAAAnB,KAAA,CAAA5D,GAAA;YACA,MAAA4D,KAAA,CAAAhD,QAAA,CAAA+C,EAAA;YACA,UAAAC,KAAA,CAAAhD,QAAA,CAAAnB,MAAA;YACA,cAAAmE,KAAA,CAAAhD,QAAA,CAAAC;UACA,GAAAmD,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAb,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA;cACA;cACA2C,KAAA,CAAAlD,YAAA;YACA;cACAkD,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAtD,OAAA,EAAAgD,IAAA,CAAAW;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IAEA;IACAkC,cAAArH,MAAA;MACA,MAAAsH,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAtH,MAAA;IACA;IACAuH,cAAAvH,MAAA;MACA,MAAAsH,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAtH,MAAA;IACA;IACAwH,kBAAAxH,MAAA;MACA;MACA,IAAAA,MAAA;MACA,IAAAA,MAAA;MACA;IACA;IACAyH,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,WAAAnE,IAAA,CAAAmE,OAAA,EAAAC,kBAAA;IACA;IACAC,kBAAA;MAAAX,GAAA;MAAAY;IAAA;MACA,IAAAZ,GAAA,CAAAjH,MAAA;MACA,IAAAiH,GAAA,CAAAjH,MAAA;MACA;IACA;IACA8H,YAAA;MACA,KAAAlI,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACA,KAAAX,IAAA;MACA,KAAAqE,OAAA;IACA;IACAgE,WAAA;MACA,KAAAjG,gBAAA;IACA;IACAkG,cAAA;MACA,KAAA/C,QAAA,CAAA4B,OAAA;IACA;IACAoB,eAAA;MACA,KAAAtG,YAAA,SAAAA,YAAA;IACA;IACAuG,oBAAA;MACA,KAAAnE,OAAA;IACA;IACAoE,oBAAA;MACA,KAAAL,WAAA;IACA;IACAM,YAAA;MACA,KAAArE,OAAA;IACA;IACAsE,WAAA;MACA,KAAApD,QAAA,CAAA4B,OAAA;IACA;IACAyB,sBAAAC,SAAA;MACA,KAAA3G,YAAA,GAAA2G,SAAA;IACA;IACAC,eAAAxI,MAAA;MACA,KAAAJ,MAAA,CAAAI,MAAA,GAAAA,MAAA;MACA,KAAAN,IAAA;MACA,KAAAqE,OAAA;MACA,KAAAkB,QAAA,CAAA4B,OAAA,YAAAU,aAAA,CAAAvH,MAAA;IACA;IACAyI,iBAAA;MACA,KAAA5G,iBAAA;IACA;IACA6G,cAAA;MACA,KAAAtG,aAAA;;MAEA;MACAuG,UAAA;QACA,MAAAC,UAAA;UACA;UACA;UACA;QACA,OAAA7G,UAAA,CAAAC,MAAA;QAEA,MAAA6G,SAAA;UACA;UACA;UACA;UACA;QACA,OAAA9G,UAAA,CAAAG,KAAA;;QAEA;QACA,MAAA4G,IAAA,QAAAC,kBAAA;QACA,MAAAxI,GAAA,GAAAyI,GAAA,CAAAC,eAAA,CAAAH,IAAA;QACA,MAAAI,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAA9I,GAAA;QACA2I,IAAA,CAAAI,QAAA,eAAA/F,IAAA,GAAAgG,WAAA,GAAAC,KAAA,iBAAAzH,UAAA,CAAAC,MAAA;QACAkH,IAAA,CAAAO,KAAA;QACAT,GAAA,CAAAU,eAAA,CAAAnJ,GAAA;QAEA,KAAA6B,aAAA;QACA,KAAAN,gBAAA;QACA,KAAAmD,QAAA,CAAA4B,OAAA,IAAA+B,UAAA,MAAAC,SAAA;MACA;IACA;IACAE,mBAAA;MACA;MACA,IAAAY,UAAA;MACA,aAAA5H,UAAA,CAAAG,KAAA;QACA;UACAyH,UAAA,QAAAnK,IAAA;UACA;QACA;UACAmK,UAAA,QAAAnK,IAAA;UACA;QACA;UACAmK,UAAA,QAAA/H,YAAA;UACA;QACA;UACA+H,UAAA,QAAAnK,IAAA;UACA;MACA;;MAEA;MACA,MAAAF,IAAA,GAAAqK,UAAA,CAAAC,GAAA,CAAAjH,IAAA;QACA,MAAAsE,GAAA;QACA,SAAAlF,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;UACA/C,GAAA,aAAA6C,YAAA,GAAAnH,IAAA,CAAAsH,MAAA,cAAAH,YAAA,uBAAAA,YAAA,CAAAI,OAAA;UACAjD,GAAA,YAAA8C,aAAA,GAAApH,IAAA,CAAAsH,MAAA,cAAAF,aAAA,uBAAAA,aAAA,CAAAI,OAAA;UACAlD,GAAA,aAAA+C,aAAA,GAAArH,IAAA,CAAAsH,MAAA,cAAAD,aAAA,uBAAAA,aAAA,CAAAI,KAAA;QACA;QACA,SAAArI,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UAAA,IAAAQ,YAAA,EAAAC,aAAA,EAAAC,aAAA;UACAtD,GAAA,aAAAoD,YAAA,GAAA1H,IAAA,CAAA6H,MAAA,cAAAH,YAAA,uBAAAA,YAAA,CAAA7J,KAAA;UACAyG,GAAA,aAAAqD,aAAA,GAAA3H,IAAA,CAAA6H,MAAA,cAAAF,aAAA,uBAAAA,aAAA,CAAAG,KAAA;UACAxD,GAAA,aAAAsD,aAAA,GAAA5H,IAAA,CAAA6H,MAAA,cAAAD,aAAA,uBAAAA,aAAA,CAAAG,IAAA;QACA;QACA,SAAA3I,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UACA5C,GAAA,WAAAtE,IAAA,CAAAS,QAAA;UACA6D,GAAA,WAAAtE,IAAA,CAAAO,OAAA;UACA+D,GAAA,UAAAtE,IAAA,CAAAgI,WAAA;QACA;QACA,SAAA5I,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UACA5C,GAAA,gBAAAM,aAAA,CAAA5E,IAAA,CAAA3C,MAAA;UACAiH,GAAA,WAAAtE,IAAA,CAAAvB,UAAA;QACA;QACA,SAAAW,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UACA5C,GAAA,WAAAtE,IAAA,CAAAiI,WAAA;UACA3D,GAAA,WAAAtE,IAAA,CAAAtB,QAAA;QACA;QACA,SAAAU,UAAA,CAAAE,MAAA,CAAA4H,QAAA;UAAA,IAAAgB,YAAA;UACA5D,GAAA,YAAA4D,YAAA,GAAAlI,IAAA,CAAAmI,MAAA,cAAAD,YAAA,uBAAAA,YAAA,CAAArK,KAAA;QACA;QACA,OAAAyG,GAAA;MACA;;MAEA;MACA,MAAA8D,UAAA,QAAAC,YAAA,CAAA1L,IAAA;MACA,WAAA2L,IAAA,EAAAF,UAAA;QAAAjG,IAAA;MAAA;IACA;IACAkG,aAAA1L,IAAA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAsD,MAAA;MAEA,MAAAsI,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAA9L,IAAA;MACA,MAAA+L,OAAA,IAAAH,OAAA,CAAAI,IAAA;MAEA,WAAArE,GAAA,IAAA3H,IAAA;QACA,MAAAiM,MAAA,GAAAL,OAAA,CAAAtB,GAAA,CAAA4B,MAAA;UACA,MAAAC,KAAA,GAAAxE,GAAA,CAAAuE,MAAA;UACA,cAAAC,KAAA,iBAAAA,KAAA,CAAA5B,QAAA,YAAA4B,KAAA,MAAAA,KAAA;QACA;QACAJ,OAAA,CAAAK,IAAA,CAAAH,MAAA,CAAAD,IAAA;MACA;MAEA,OAAAD,OAAA,CAAAC,IAAA;IACA;IACAK,aAAA;MACA,SAAA/J,YAAA,CAAAgB,MAAA;QACA,KAAAqC,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,MAAAC,WAAA,QAAAjK,YAAA,CAAAc,MAAA,CAAAuE,GAAA,IAAAA,GAAA,CAAAjH,MAAA;MACA,IAAA6L,WAAA,CAAAjJ,MAAA;QACA,KAAAqC,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,KAAAjH,QAAA,cAAAkH,WAAA,CAAAjJ,MAAA;QACAgC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA,MAAAuH,QAAA,GAAAD,WAAA,CAAAjC,GAAA,CAAA3C,GAAA;UACA,YAAA3B,WAAA,MAAA/E,GAAA;YACA,MAAA0G,GAAA,CAAA/C,EAAA;YACA;YACA;UACA;QACA;QAEA6H,OAAA,CAAAC,GAAA,CAAAF,QAAA,EAAAvH,IAAA,CAAA0H,SAAA;UACA,MAAAC,YAAA,GAAAD,SAAA,CAAAvJ,MAAA,CAAA8B,IAAA,IAAAA,IAAA,CAAAQ,IAAA,UAAApC,MAAA;UACA,KAAAqC,QAAA,CAAA4B,OAAA,gBAAAqF,YAAA;UACA,KAAAnI,OAAA;UACA,KAAAnC,YAAA;QACA,GAAAwD,KAAA;UACA,KAAAH,QAAA,CAAAyB,KAAA;UACA,KAAA3C,OAAA;QACA;MACA,GAAAqB,KAAA;QACA,KAAAH,QAAA,CAAAxE,IAAA;MACA;IACA;IACA0L,YAAA;MACA,SAAAvK,YAAA,CAAAgB,MAAA;QACA,KAAAqC,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,MAAAC,WAAA,QAAAjK,YAAA,CAAAc,MAAA,CAAAuE,GAAA,IAAAA,GAAA,CAAAjH,MAAA;MACA,IAAA6L,WAAA,CAAAjJ,MAAA;QACA,KAAAqC,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,KAAAQ,OAAA,sBAAAP,WAAA,CAAAjJ,MAAA;QACAgC,iBAAA;QACAC,gBAAA;QACAwH,gBAAA;QACAC,cAAA,EAAAb,KAAA;UACA,KAAAA,KAAA,IAAAA,KAAA,CAAAc,IAAA;YACA;UACA;UACA,IAAAd,KAAA,CAAA7I,MAAA;YACA;UACA;UACA;QACA;MACA,GAAA2B,IAAA;QAAAkH;MAAA;QACA,MAAAK,QAAA,GAAAD,WAAA,CAAAjC,GAAA,CAAA3C,GAAA;UACA,YAAA3B,WAAA,MAAA/E,GAAA;YACA,MAAA0G,GAAA,CAAA/C,EAAA;YACA;YACA,cAAAuH;UACA;QACA;QAEAM,OAAA,CAAAC,GAAA,CAAAF,QAAA,EAAAvH,IAAA,CAAA0H,SAAA;UACA,MAAAC,YAAA,GAAAD,SAAA,CAAAvJ,MAAA,CAAA8B,IAAA,IAAAA,IAAA,CAAAQ,IAAA,UAAApC,MAAA;UACA,KAAAqC,QAAA,CAAA4B,OAAA,gBAAAqF,YAAA;UACA,KAAAnI,OAAA;UACA,KAAAnC,YAAA;QACA,GAAAwD,KAAA;UACA,KAAAH,QAAA,CAAAyB,KAAA;UACA,KAAA3C,OAAA;QACA;MACA,GAAAqB,KAAA;QACA,KAAAH,QAAA,CAAAxE,IAAA;MACA;IACA;IACA+L,aAAAvF,GAAA;MAAA,IAAAwF,WAAA;MACA,KAAA9H,QAAA,WAAA8H,WAAA,GAAAxF,GAAA,CAAAgD,MAAA,cAAAwC,WAAA,uBAAAA,WAAA,CAAAvC,OAAA;QACAtF,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA;QACA,KAAAe,WAAA,MAAA/E,GAAA;UACA,MAAA0G,GAAA,CAAA/C,EAAA;UACA;UACA;QACA,GAAAK,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA,CAAA4B,OAAA;YACA,KAAA9C,OAAA;UACA;YACA,KAAAkB,QAAA,CAAAyB,KAAA,CAAAlC,IAAA,CAAAW,GAAA;UACA;QACA;MACA,GAAAC,KAAA;QACA,KAAAH,QAAA,CAAAxE,IAAA;MACA;IACA;IACAiM,YAAAzF,GAAA;MAAA,IAAA0F,YAAA;MACA,KAAAP,OAAA,wBAAAO,YAAA,GAAA1F,GAAA,CAAAgD,MAAA,cAAA0C,YAAA,uBAAAA,YAAA,CAAAzC,OAAA;QACAtF,iBAAA;QACAC,gBAAA;QACAwH,gBAAA;QACAC,cAAA,EAAAb,KAAA;UACA,KAAAA,KAAA,IAAAA,KAAA,CAAAc,IAAA;YACA;UACA;UACA,IAAAd,KAAA,CAAA7I,MAAA;YACA;UACA;UACA;QACA;MACA,GAAA2B,IAAA;QAAAkH;MAAA;QACA;QACA,KAAAnG,WAAA,MAAA/E,GAAA;UACA,MAAA0G,GAAA,CAAA/C,EAAA;UACA;UACA,cAAAuH;QACA,GAAAlH,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA,CAAA4B,OAAA;YACA,KAAA9C,OAAA;UACA;YACA,KAAAkB,QAAA,CAAAyB,KAAA,CAAAlC,IAAA,CAAAW,GAAA;UACA;QACA;MACA,GAAAC,KAAA;QACA,KAAAH,QAAA,CAAAxE,IAAA;MACA;IACA;IACAmM,iBAAAvL,QAAA;MACA,KAAAA,QAAA;MACA,MAAAiC,KAAA,OAAAC,IAAA;MACA,MAAAG,OAAA,OAAAH,IAAA,CAAAlC,QAAA;MACA,MAAAwL,aAAA,GAAAC,IAAA,CAAAC,IAAA,EAAArJ,OAAA,GAAAJ,KAAA;MAEA,IAAAuJ,aAAA,mBAAAC,IAAA,CAAAE,GAAA,CAAAH,aAAA;MACA,IAAAA,aAAA;MACA,IAAAA,aAAA,iBAAAA,aAAA;MACA,IAAAA,aAAA,kBAAAA,aAAA;MACA,UAAAA,aAAA;IACA;IACAI,sBAAA5L,QAAA;MACA,KAAAA,QAAA;MACA,MAAAiC,KAAA,OAAAC,IAAA;MACA,MAAAG,OAAA,OAAAH,IAAA,CAAAlC,QAAA;MACA,MAAAwL,aAAA,GAAAC,IAAA,CAAAC,IAAA,EAAArJ,OAAA,GAAAJ,KAAA;MAEA,IAAAuJ,aAAA;MACA,IAAAA,aAAA;MACA,IAAAA,aAAA;MACA;IACA;IACA;IACAK,qBAAA;MACA,KAAA3K,iBAAA;MACA,KAAA0C,QAAA,CAAA4B,OAAA;IACA;IACAsG,mBAAA;MACA;MACA,KAAAlI,QAAA,CAAAxE,IAAA,WAAA4C,cAAA,CAAAT,MAAA;MACA;IACA;IACAwK,kBAAA;MACA;MACA,KAAAnI,QAAA,CAAA2G,OAAA,WAAAjI,aAAA,CAAAf,MAAA;MACA;IACA;IACAyK,oBAAA;MACA;MACA,KAAApI,QAAA,CAAA4B,OAAA,WAAAjD,eAAA,CAAAhB,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}