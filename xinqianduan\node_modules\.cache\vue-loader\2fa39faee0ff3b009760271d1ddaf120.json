{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\qun.vue?vue&type=style&index=0&id=da698b76&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\qun.vue", "mtime": 1748617978532}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["qun.vue"], "names": [], "mappings": ";AA+pBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "qun.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"client-group-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-s-custom\"></i>\r\n          {{ this.$router.currentRoute.name }}\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理签约客户群组和工作协作</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">客户群组</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +6%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active-icon\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeGroups }}</div>\r\n              <div class=\"stat-label\">活跃群组</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +10%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon member-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ totalMembers }}</div>\r\n              <div class=\"stat-label\">总成员数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon efficiency-icon\">\r\n              <i class=\"el-icon-data-analysis\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">92%</div>\r\n              <div class=\"stat-label\">协作效率</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +3%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新建群组\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入群组名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n            >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 群组展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"group-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          群组列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"group-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"group in list\" \r\n          :key=\"group.id\"\r\n          class=\"group-item\"\r\n        >\r\n          <div class=\"group-header\">\r\n            <div class=\"group-avatar\">\r\n              <img v-if=\"group.pic_path\" :src=\"group.pic_path\" alt=\"群组头像\" />\r\n              <i v-else class=\"el-icon-s-custom default-avatar\"></i>\r\n            </div>\r\n            <div class=\"group-info\">\r\n              <div class=\"group-title\">{{ group.title }}</div>\r\n              <div class=\"group-desc\">{{ group.desc || '暂无描述' }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"group-content\">\r\n            <div class=\"group-stats\">\r\n              <div class=\"stat-item\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ getGroupMemberCount(group) }}人</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ formatDate(group.create_time) }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"group-members\" v-if=\"group.members && group.members.length > 0\">\r\n              <div class=\"member-avatars\">\r\n                <div \r\n                  v-for=\"(member, index) in group.members.slice(0, 5)\" \r\n                  :key=\"index\"\r\n                  class=\"member-avatar\"\r\n                  :title=\"member.name\"\r\n                >\r\n                  <i class=\"el-icon-user\"></i>\r\n                </div>\r\n                <div v-if=\"group.members.length > 5\" class=\"more-members\">\r\n                  +{{ group.members.length - 5 }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"group-footer\">\r\n            <div class=\"group-status\">\r\n              <el-tag :type=\"getGroupStatusType(group)\" size=\"small\">\r\n                {{ getGroupStatusText(group) }}\r\n              </el-tag>\r\n            </div>\r\n            <div class=\"group-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(group.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, group.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"群组信息\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-group-info\">\r\n                <div class=\"table-group-header\">\r\n                  <div class=\"table-group-avatar\">\r\n                    <img v-if=\"scope.row.pic_path\" :src=\"scope.row.pic_path\" alt=\"群组头像\" />\r\n                    <i v-else class=\"el-icon-s-custom\"></i>\r\n                  </div>\r\n                  <div class=\"table-group-details\">\r\n                    <div class=\"table-group-title\">{{ scope.row.title }}</div>\r\n                    <div class=\"table-group-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"成员\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"member-count\">\r\n                <i class=\"el-icon-user\"></i>\r\n                {{ getGroupMemberCount(scope.row) }}人\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getGroupStatusType(scope.row)\" size=\"small\">\r\n                {{ getGroupStatusText(scope.row) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"650px\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-form-item label=\"群组名称\" prop=\"title\">\r\n          <el-input \r\n            v-model=\"ruleForm.title\" \r\n            placeholder=\"请输入群组名称\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"群组头像\" prop=\"pic_path\">\r\n          <div class=\"avatar-upload\">\r\n            <div class=\"avatar-preview\" v-if=\"ruleForm.pic_path\">\r\n              <img :src=\"ruleForm.pic_path\" alt=\"群组头像\" />\r\n              <div class=\"avatar-actions\">\r\n                <el-button size=\"mini\" @click=\"showImage(ruleForm.pic_path)\">查看</el-button>\r\n                <el-button size=\"mini\" type=\"danger\" @click=\"delImage(ruleForm.pic_path, 'pic_path')\">删除</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"avatar-upload-area\" v-else>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n                class=\"avatar-uploader\"\r\n              >\r\n                <div class=\"upload-placeholder\">\r\n                  <i class=\"el-icon-plus\"></i>\r\n                  <div>上传头像</div>\r\n                </div>\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-tip\">建议尺寸: 96×96像素</div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"负责员工\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            :options=\"yuangongs\"\r\n            :props=\"props\"\r\n            placeholder=\"请选择负责员工\"\r\n            filterable\r\n            clearable\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"群组成员\">\r\n\t\t  <el-cascader\r\n\t\t    v-model=\"ruleForm.uid\"\r\n\t\t    :options=\"users\"\r\n\t\t    :props=\"props\"\r\n            placeholder=\"请选择群组成员\"\r\n\t\t    filterable\r\n            clearable\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"群组描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入群组详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <div class=\"image-viewer\">\r\n        <el-image :src=\"show_image\" fit=\"contain\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"ClientGroupManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      props: { multiple: true },\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/qun/\",\r\n      title: \"工作群\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        pic_path: \"\",\r\n        desc: \"\",\r\n        yuangong_id: [],\r\n        uid: []\r\n      },\r\n\t  uid:[],\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写群组名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      users: [],\r\n      lvshis: [],\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    activeGroups() {\r\n      return Array.isArray(this.list) ? this.list.filter(group => group.title && group.title.trim() !== '').length : 0;\r\n    },\r\n    totalMembers() {\r\n      return Array.isArray(this.list) ? this.list.reduce((sum, group) => sum + this.getGroupMemberCount(group), 0) : 0;\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑群组' : '新建群组';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n\t\t  uid:\"\",\r\n          pic_path: \"\",\r\n          yuangong_id: \"\",\r\n          lvshi_id: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getLvshi();\r\n      _this.getYuaong();\r\n      _this.getUser();\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getLvshi\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.lvshis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getYuaong() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getYuangong\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getUser() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getKehu\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          let users = resp.data;\r\n\t\t  users.forEach((item,key) => {\r\n\t\t\t\titem.label  =  item.nickname\r\n\t\t\t\titem.value\t= item.id\r\n\t\t  \t});\r\n\t\t\t_this.users = users\r\n        }\r\n      });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\"\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    getGroupMemberCount(group) {\r\n      // 模拟计算群组成员数量\r\n      if (group.uid && Array.isArray(group.uid)) {\r\n        return group.uid.length;\r\n      }\r\n      if (group.members && Array.isArray(group.members)) {\r\n        return group.members.length;\r\n      }\r\n      return Math.floor(Math.random() * 20) + 3; // 模拟3-22人\r\n    },\r\n    getGroupStatusType(group) {\r\n      // 根据群组数据判断状态类型\r\n      if (group.title && group.title.trim() !== '') {\r\n        return 'success';\r\n      }\r\n      return 'info';\r\n    },\r\n    getGroupStatusText(group) {\r\n      // 根据群组数据判断状态文本\r\n      if (group.title && group.title.trim() !== '') {\r\n        return '正常';\r\n      }\r\n      return '待完善';\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.client-group-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.member-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.efficiency-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .group-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 群组网格视图 */\r\n.group-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.group-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.group-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.group-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.group-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.group-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.default-avatar {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.group-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.group-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.group-desc {\r\n  font-size: 13px;\r\n  opacity: 0.9;\r\n  line-height: 1.4;\r\n  max-height: 35px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.group-content {\r\n  padding: 20px;\r\n}\r\n\r\n.group-stats {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.group-members {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.member-avatars {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.member-avatar {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 14px;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.more-members {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  background: #f0f0f0;\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.group-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.group-status {\r\n  flex: 1;\r\n}\r\n\r\n.group-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-group-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-group-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.table-group-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.table-group-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.table-group-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.table-group-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-group-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.member-count {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 头像上传样式 */\r\n.avatar-upload {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.avatar-preview {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 16px;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  background: #fafafa;\r\n}\r\n\r\n.avatar-preview img {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  object-fit: cover;\r\n}\r\n\r\n.avatar-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.avatar-upload-area {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-uploader {\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-uploader:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-placeholder {\r\n  text-align: center;\r\n  color: #8c939d;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 28px;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  text-align: center;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .client-group-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .group-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .group-header {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .table-group-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .avatar-preview {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}