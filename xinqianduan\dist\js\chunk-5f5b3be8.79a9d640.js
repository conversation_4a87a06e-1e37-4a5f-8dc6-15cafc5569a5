(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f5b3be8"],{"101b":function(e,t,i){},"53b6":function(e,t,i){"use strict";i("101b")},6962:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"employee-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增员工 ")]),t("el-button",{staticClass:"refresh-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新 ")])],1)])]),t("div",{staticClass:"search-section"},[t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("div",{staticClass:"search-form"},[t("div",{staticClass:"search-row"},[t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("员工搜索")]),t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入员工姓名、手机号或账号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("职位筛选")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择职位",clearable:""},model:{value:e.search.zhiwei_id,callback:function(t){e.$set(e.search,"zhiwei_id",t)},expression:"search.zhiwei_id"}},e._l(e.zhiweis,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("状态")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},[t("el-option",{attrs:{label:"正常",value:1}}),t("el-option",{attrs:{label:"禁用",value:0}})],1)],1)]),t("div",{staticClass:"search-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchData}},[e._v(" 搜索 ")]),t("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:e.clearSearch}},[e._v(" 重置 ")]),t("el-button",{attrs:{icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出 ")])],1)])])],1),t("div",{staticClass:"stats-section"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon total"},[t("i",{staticClass:"el-icon-user"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.total))]),t("div",{staticClass:"stat-label"},[e._v("总员工数")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon admin"},[t("i",{staticClass:"el-icon-user-solid"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.adminCount))]),t("div",{staticClass:"stat-label"},[e._v("管理员")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon active"},[t("i",{staticClass:"el-icon-circle-check"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.activeCount))]),t("div",{staticClass:"stat-label"},[e._v("在职员工")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon new"},[t("i",{staticClass:"el-icon-plus"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.newCount))]),t("div",{staticClass:"stat-label"},[e._v("本月新增")])])])])],1)],1),t("div",{staticClass:"table-section"},[t("el-card",{staticClass:"table-card",attrs:{shadow:"never"}},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[t("i",{staticClass:"el-icon-menu"}),e._v(" 员工列表 ")]),t("div",{staticClass:"table-tools"},[t("el-button-group",[t("el-button",{attrs:{type:"table"===e.viewMode?"primary":"",icon:"el-icon-menu",size:"small"},on:{click:function(t){e.viewMode="table"}}},[e._v(" 列表视图 ")]),t("el-button",{attrs:{type:"card"===e.viewMode?"primary":"",icon:"el-icon-s-grid",size:"small"},on:{click:function(t){e.viewMode="card"}}},[e._v(" 卡片视图 ")])],1)],1)]),"table"===e.viewMode?t("div",{staticClass:"table-view"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"employee-table",attrs:{data:e.list,stripe:""}},[t("el-table-column",{attrs:{label:"头像",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"avatar-cell"},[t("el-avatar",{staticClass:"employee-avatar",attrs:{src:i.row.pic_path,size:50},nativeOn:{click:function(t){return e.showImage(i.row.pic_path)}}},[t("i",{staticClass:"el-icon-user-solid"})])],1)]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"title",label:"员工姓名","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"employee-name-cell"},[t("div",{staticClass:"employee-name clickable",on:{click:function(t){return e.showEmployeeDetail(i.row)}}},[e._v(e._s(i.row.title))]),t("div",{staticClass:"employee-account"},[e._v(e._s(i.row.account))])])]}}],null,!1,**********)}),t("el-table-column",{attrs:{label:"职位",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-tag",{attrs:{type:e.getPositionTagType(i.row.zhiwei_title),size:"small"}},[e._v(" "+e._s(i.row.zhiwei_title||"未分配")+" ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"phone",label:"手机号码",width:"130",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"phone-cell"},[t("i",{staticClass:"el-icon-phone"}),e._v(" "+e._s(i.row.phone)+" ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(t){return e.changeStatus(i.row)}},model:{value:i.row.status,callback:function(t){e.$set(i.row,"status",t)},expression:"scope.row.status"}})]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"create_time",label:"入职时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"time-cell"},[t("i",{staticClass:"el-icon-time"}),e._v(" "+e._s(i.row.create_time)+" ")])]}}],null,!1,3001843918)}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"action-buttons"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.showEmployeeEdit(i.row)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"warning",size:"mini",icon:"el-icon-key",plain:""},on:{click:function(t){return e.chongzhi(i.row.id)}}},[e._v(" 重置密码 ")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",plain:""},on:{click:function(t){return e.delData(i.$index,i.row.id)}}},[e._v(" 删除 ")])],1)]}}],null,!1,1304503458)})],1)],1):e._e(),"card"===e.viewMode?t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-view"},[t("el-row",{attrs:{gutter:20}},e._l(e.list,(function(i){return t("el-col",{key:i.id,staticClass:"employee-card-col",attrs:{span:8}},[t("div",{staticClass:"employee-card"},[t("div",{staticClass:"card-header"},[t("div",{staticClass:"card-avatar"},[t("el-avatar",{attrs:{src:i.pic_path,size:60},nativeOn:{click:function(t){return e.showImage(i.pic_path)}}},[t("i",{staticClass:"el-icon-user-solid"})])],1),t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-name clickable",on:{click:function(t){return e.showEmployeeDetail(i)}}},[e._v(e._s(i.title))]),t("div",{staticClass:"card-position"},[t("el-tag",{attrs:{type:e.getPositionTagType(i.zhiwei_title),size:"mini"}},[e._v(" "+e._s(i.zhiwei_title||"未分配")+" ")])],1)]),t("div",{staticClass:"card-status"},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"small"},on:{change:function(t){return e.changeStatus(i)}},model:{value:i.status,callback:function(t){e.$set(i,"status",t)},expression:"employee.status"}})],1)]),t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-detail"},[t("div",{staticClass:"detail-item"},[t("i",{staticClass:"el-icon-phone"}),t("span",[e._v(e._s(i.phone))])]),t("div",{staticClass:"detail-item"},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v(e._s(i.account))])]),t("div",{staticClass:"detail-item"},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v(e._s(i.create_time))])])])]),t("div",{staticClass:"card-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.showEmployeeEdit(i)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"warning",size:"small",icon:"el-icon-key",plain:""},on:{click:function(t){return e.chongzhi(i.id)}}},[e._v(" 重置密码 ")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete",plain:""},on:{click:function(t){e.delData(e.list.indexOf(i),i.id)}}},[e._v(" 删除 ")])],1)])])})),1)],1):e._e(),t("div",{staticClass:"pagination-container"},[t("el-pagination",{staticClass:"pagination",attrs:{"page-sizes":[12,24,48,96],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)])],1),t("div",{staticClass:"employee-detail-panel",class:{"panel-open":e.detailPanelVisible}},[t("div",{staticClass:"panel-overlay",on:{click:e.closeDetailPanel}}),t("div",{staticClass:"panel-content"},[t("div",{staticClass:"panel-header"},[t("div",{staticClass:"panel-title"},[t("i",{staticClass:"el-icon-user"}),e.currentEmployee.id?e.isViewMode?t("span",[e._v("员工详情")]):t("span",[e._v("编辑员工")]):t("span",[e._v("新增员工")])]),t("div",{staticClass:"panel-actions"},[e.isViewMode&&e.currentEmployee.id?t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:e.switchToEditMode}},[e._v(" 编辑 ")]):e._e(),e.isViewMode?e._e():t("el-button",{attrs:{type:"primary",size:"small",loading:e.saving,icon:"el-icon-check"},on:{click:e.saveEmployeeData}},[e._v(" 保存 ")]),!e.isViewMode&&e.currentEmployee.id?t("el-button",{attrs:{size:"small",icon:"el-icon-refresh-left"},on:{click:e.cancelEdit}},[e._v(" 取消编辑 ")]):e._e(),t("el-button",{attrs:{size:"small",icon:"el-icon-close"},on:{click:e.closeDetailPanel}},[e._v(" 关闭 ")])],1)]),t("div",{staticClass:"panel-body"},[t("el-form",{ref:"detailForm",staticClass:"employee-form",attrs:{model:e.currentEmployee,rules:e.detailRules,"label-width":"100px"}},[t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-title"},[t("i",{staticClass:"el-icon-user"}),e._v(" 基本信息 ")]),t("div",{staticClass:"form-row"},[t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"员工姓名",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入员工姓名",readonly:e.isViewMode,clearable:""},model:{value:e.currentEmployee.title,callback:function(t){e.$set(e.currentEmployee,"title",t)},expression:"currentEmployee.title"}})],1)],1),t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"手机号码",prop:"phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号码",readonly:e.isViewMode,clearable:""},model:{value:e.currentEmployee.phone,callback:function(t){e.$set(e.currentEmployee,"phone",t)},expression:"currentEmployee.phone"}})],1)],1)]),t("div",{staticClass:"form-row"},[t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"登录账号",prop:"account"}},[t("el-input",{attrs:{placeholder:"请输入登录账号",readonly:e.isViewMode,clearable:""},model:{value:e.currentEmployee.account,callback:function(t){e.$set(e.currentEmployee,"account",t)},expression:"currentEmployee.account"}},[e.currentEmployee.id||e.isViewMode?e._e():t("template",{slot:"append"},[e._v("默认密码888888")])],2)],1)],1),t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"员工状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,disabled:e.isViewMode,"active-text":"正常","inactive-text":"禁用"},model:{value:e.currentEmployee.status,callback:function(t){e.$set(e.currentEmployee,"status",t)},expression:"currentEmployee.status"}})],1)],1)])]),t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-title"},[t("i",{staticClass:"el-icon-postcard"}),e._v(" 职位信息 ")]),t("div",{staticClass:"form-row"},[t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"所属职位",prop:"zhiwei_id"}},[e.isViewMode?t("el-input",{staticStyle:{width:"100%"},attrs:{value:e.currentEmployee.zhiwei_title||"未分配",readonly:""}}):t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择职位",filterable:"",clearable:""},model:{value:e.currentEmployee.zhiwei_id,callback:function(t){e.$set(e.currentEmployee,"zhiwei_id",t)},expression:"currentEmployee.zhiwei_id"}},e._l(e.zhiweis,(function(e){return t("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1)],1)],1),t("div",{staticClass:"form-col"},[t("el-form-item",{attrs:{label:"入职时间"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择入职时间",readonly:e.isViewMode,disabled:e.isViewMode,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.currentEmployee.join_date,callback:function(t){e.$set(e.currentEmployee,"join_date",t)},expression:"currentEmployee.join_date"}})],1)],1)])]),t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-title"},[t("i",{staticClass:"el-icon-picture"}),e._v(" 头像信息 ")]),t("div",{staticClass:"avatar-upload-section"},[t("div",{staticClass:"current-avatar"},[t("el-avatar",{staticClass:"preview-avatar",attrs:{src:e.currentEmployee.pic_path,size:100},nativeOn:{click:function(t){return e.showImage(e.currentEmployee.pic_path)}}},[t("i",{staticClass:"el-icon-user-solid"})])],1),t("div",{staticClass:"upload-controls"},[e.isViewMode?e._e():t("el-form-item",{attrs:{label:"头像",prop:"pic_path"}},[t("el-input",{staticClass:"avatar-input",attrs:{placeholder:"头像URL",readonly:""},model:{value:e.currentEmployee.pic_path,callback:function(t){e.$set(e.currentEmployee,"pic_path",t)},expression:"currentEmployee.pic_path"}})],1),e.isViewMode?t("div",{staticClass:"view-buttons"},[e.currentEmployee.pic_path?t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-view"},on:{click:function(t){return e.showImage(e.currentEmployee.pic_path)}}},[e._v(" 查看头像 ")]):t("span",{staticClass:"no-avatar-text"},[e._v("暂无头像")])],1):t("div",{staticClass:"upload-buttons"},[t("el-upload",{staticClass:"avatar-uploader",attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeUpload}},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload"}},[e._v(" 上传头像 ")])],1),e.currentEmployee.pic_path?t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-view"},on:{click:function(t){return e.showImage(e.currentEmployee.pic_path)}}},[e._v(" 查看 ")]):e._e(),e.currentEmployee.pic_path?t("el-button",{attrs:{size:"small",type:"danger",icon:"el-icon-delete"},on:{click:e.removeAvatar}},[e._v(" 删除 ")]):e._e()],1),e.isViewMode?e._e():t("div",{staticClass:"upload-tip"},[e._v("建议尺寸：330px × 300px")])],1)])]),e.currentEmployee.id?t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-title"},[t("i",{staticClass:"el-icon-time"}),e._v(" 操作记录 ")]),t("div",{staticClass:"operation-record"},[t("div",{staticClass:"record-item"},[t("span",{staticClass:"record-label"},[e._v("创建时间：")]),t("span",{staticClass:"record-value"},[e._v(e._s(e.currentEmployee.create_time))])]),t("div",{staticClass:"record-item"},[t("span",{staticClass:"record-label"},[e._v("最后更新：")]),t("span",{staticClass:"record-value"},[e._v(e._s(e.currentEmployee.update_time||"暂无"))])]),t("div",{staticClass:"record-item"},[t("span",{staticClass:"record-label"},[e._v("员工ID：")]),t("span",{staticClass:"record-value"},[e._v(e._s(e.currentEmployee.id))])])]),e.isViewMode?e._e():t("div",{staticClass:"quick-actions"},[t("el-button",{attrs:{type:"warning",size:"small",icon:"el-icon-key"},on:{click:e.resetPassword}},[e._v(" 重置密码 ")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:e.deleteEmployee}},[e._v(" 删除员工 ")])],1)]):e._e()])],1)])]),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"职位类型","label-width":e.formLabelWidth,prop:"zhiwei_id"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.zhiwei_id,callback:function(t){e.$set(e.ruleForm,"zhiwei_id",t)},expression:"ruleForm.zhiwei_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.zhiweis,(function(e,i){return t("el-option",{key:i,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:e.title+"名称","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:e.title+"手机","label-width":e.formLabelWidth,prop:"phone"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),t("el-form-item",{attrs:{label:e.title+"账号","label-width":e.formLabelWidth,prop:"account"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.account,callback:function(t){e.$set(e.ruleForm,"account",t)},expression:"ruleForm.account"}},[t("template",{slot:"append"},[e._v("默认密码888888")])],2)],1),t("el-form-item",{attrs:{label:"头像","label-width":e.formLabelWidth,prop:"pic_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.pic_path,callback:function(t){e.$set(e.ruleForm,"pic_path",t)},expression:"ruleForm.pic_path"}}),t("el-button-group",[t("el-button",[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.pic_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.pic_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.pic_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.pic_path,"pic_path")}}},[e._v("删除")]):e._e()],1),t("div",{staticClass:"el-upload__tip"},[e._v("330rpx*300rpx")])],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"title-section"},[t("h2",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-user"}),e._v(" 员工管理 ")]),t("p",{staticClass:"page-subtitle"},[e._v("管理系统员工信息和职位分配")])])}],l={name:"list",components:{},data(){return{viewMode:"table",allSize:"mini",list:[],total:0,page:1,size:12,search:{keyword:"",zhiwei_id:"",status:""},loading:!0,url:"/Yuangong/",title:"员工",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",pic_path:"",account:"",phone:"",zhiwei_id:"",status:1},field:"",zhiweis:[],detailPanelVisible:!1,saving:!1,isViewMode:!0,originalEmployee:{},currentEmployee:{id:null,title:"",account:"",phone:"",pic_path:"",zhiwei_id:"",zhiwei_title:"",status:1,join_date:"",create_time:"",update_time:""},detailRules:{title:[{required:!0,message:"请填写员工姓名",trigger:"blur"}],account:[{required:!0,message:"请填写登录账号",trigger:"blur"}],phone:[{required:!0,message:"请填写手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],zhiwei_id:[{required:!0,message:"请选择职位",trigger:"change"}]},rules:{title:[{required:!0,message:"请填写员工姓名",trigger:"blur"}],account:[{required:!0,message:"请填写账号",trigger:"blur"}],phone:[{required:!0,message:"请填写手机号",trigger:"blur"}],pic_path:[{required:!0,message:"请上传头像",trigger:"blur"}],zhiwei_id:[{required:!0,message:"请选择职位类型",trigger:"blur"}]},formLabelWidth:"120px"}},computed:{adminCount(){return this.list.filter(e=>e.zhiwei_title&&(e.zhiwei_title.includes("管理员")||e.zhiwei_title.includes("经理")||e.zhiwei_title.includes("主管"))).length},activeCount(){return this.list.filter(e=>1===e.status).length},newCount(){const e=(new Date).getMonth()+1;return this.list.filter(t=>{if(!t.create_time)return!1;const i=new Date(t.create_time).getMonth()+1;return i===e}).length}},mounted(){this.getData()},methods:{getPositionTagType(e){return e?e.includes("管理员")||e.includes("经理")?"danger":e.includes("主管")||e.includes("专员")?"warning":e.includes("助理")||e.includes("客服")?"success":"primary":"info"},changeStatus(e){this.$message.success(`员工"${e.title}"状态已${e.status?"启用":"禁用"}`)},clearSearch(){this.search={keyword:"",zhiwei_id:"",status:""},this.searchData()},exportData(){this.$message.success("导出功能开发中...")},showEmployeeDetail(e){var t;this.currentEmployee={...e,join_date:e.join_date||(null===(t=e.create_time)||void 0===t?void 0:t.split(" ")[0])||""},this.originalEmployee={...this.currentEmployee},this.isViewMode=!0,this.detailPanelVisible=!0,0===this.zhiweis.length&&this.getZhiwei()},showEmployeeEdit(e){var t;this.currentEmployee={...e,join_date:e.join_date||(null===(t=e.create_time)||void 0===t?void 0:t.split(" ")[0])||""},this.originalEmployee={...this.currentEmployee},this.isViewMode=!1,this.detailPanelVisible=!0,0===this.zhiweis.length&&this.getZhiwei()},switchToEditMode(){this.isViewMode=!1},cancelEdit(){this.currentEmployee={...this.originalEmployee},this.isViewMode=!0},closeDetailPanel(){this.detailPanelVisible=!1,this.saving=!1,this.$nextTick(()=>{this.$refs.detailForm&&this.$refs.detailForm.resetFields()})},saveEmployeeData(){this.$refs.detailForm.validate(e=>{e&&(this.saving=!0,setTimeout(()=>{if(this.saving=!1,this.$message.success("保存成功！"),this.currentEmployee.id){const e=this.list.findIndex(e=>e.id===this.currentEmployee.id);if(-1!==e){const t=this.zhiweis.find(e=>e.id===this.currentEmployee.zhiwei_id);this.currentEmployee.zhiwei_title=t?t.title:"",this.$set(this.list,e,{...this.currentEmployee})}}else{const e={...this.currentEmployee,id:Date.now(),create_time:(new Date).toLocaleString()},t=this.zhiweis.find(t=>t.id===e.zhiwei_id);e.zhiwei_title=t?t.title:"",this.list.unshift(e),this.total++}this.closeDetailPanel()},1e3))})},handleAvatarSuccess(e){this.currentEmployee.pic_path=e.data.url,this.$message.success("头像上传成功！")},removeAvatar(){this.$confirm("确定要删除头像吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.currentEmployee.pic_path="",this.$message.success("头像已删除！")})},resetPassword(){this.$confirm("确定要重置该员工的密码为888888吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$message.success("密码重置成功！")})},deleteEmployee(){this.$confirm("确定要删除该员工吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=this.list.findIndex(e=>e.id===this.currentEmployee.id);-1!==e&&(this.list.splice(e,1),this.total--,this.$message.success("员工删除成功！"),this.closeDetailPanel())})},changeField(e){this.field=e},chongzhi(e){this.$confirm("重置密码888888?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.postRequest("/yuangong/chongzhi",{id:e}).then(e=>{200==e.code?this.$message({type:"success",message:"重置成功!"}):this.$message({type:"error",message:"重置失败!"})})}).catch(()=>{this.$message({type:"error",message:"取消重置!"})})},getZhiwei(){this.postRequest("/zhiwei/getList",{}).then(e=>{200==e.code&&(this.zhiweis=e.data)})},editData(e){if(0!=e){const t=this.list.find(t=>t.id===e);t&&this.showEmployeeEdit(t)}else this.currentEmployee={id:null,title:"",account:"",phone:"",pic_path:"",zhiwei_id:"",zhiwei_title:"",status:1,join_date:"",create_time:"",update_time:""},this.originalEmployee={...this.currentEmployee},this.isViewMode=!1,this.detailPanelVisible=!0,0===this.zhiweis.length&&this.getZhiwei()},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,setTimeout(()=>{e.loading=!1,e.list=[{id:1,title:"张三",account:"zhangsan",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:1,zhiwei_title:"系统管理员",status:1,create_time:"2024-01-01 09:00:00"},{id:2,title:"李四",account:"lisi",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:2,zhiwei_title:"业务经理",status:1,create_time:"2024-01-02 10:30:00"},{id:3,title:"王五",account:"wangwu",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:3,zhiwei_title:"法务专员",status:1,create_time:"2024-01-03 14:15:00"},{id:4,title:"赵六",account:"zhaoliu",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:4,zhiwei_title:"财务专员",status:1,create_time:"2024-01-04 16:45:00"},{id:5,title:"孙七",account:"sunqi",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:5,zhiwei_title:"客服专员",status:0,create_time:"2024-01-05 11:20:00"},{id:6,title:"周八",account:"zhouba",phone:"***********",pic_path:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",zhiwei_id:2,zhiwei_title:"业务专员",status:1,create_time:"2024-01-06 08:30:00"}],e.total=6,e.zhiweis=[{id:1,title:"系统管理员"},{id:2,title:"业务经理"},{id:3,title:"法务专员"},{id:4,title:"财务专员"},{id:5,title:"客服专员"}]},800)},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let i=this;i.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(i.ruleForm[t]="",i.$message.success("删除成功!")):i.$message.error(e.msg)})}}},c=l,o=(i("53b6"),i("2877")),n=Object(o["a"])(c,s,a,!1,null,"04fb7389",null);t["default"]=n.exports}}]);
//# sourceMappingURL=chunk-5f5b3be8.79a9d640.js.map