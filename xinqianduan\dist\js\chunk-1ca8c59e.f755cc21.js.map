{"version": 3, "sources": ["webpack:///./src/views/pages/yonghu/lawyer.vue?c113", "webpack:///./src/views/pages/yonghu/lawyer.vue", "webpack:///src/views/pages/yonghu/lawyer.vue", "webpack:///./src/views/pages/yonghu/lawyer.vue?13c0", "webpack:///./src/views/pages/yonghu/lawyer.vue?a490"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "refulsh", "_v", "nativeOn", "$event", "type", "indexOf", "_k", "keyCode", "key", "searchData", "apply", "arguments", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "is_deal", "_l", "options1", "item", "id", "title", "clearSearch", "_s", "total", "directives", "name", "rawName", "loading", "list", "scopedSlots", "_u", "fn", "scope", "row", "order_sn", "desc", "length", "substring", "getStatusType", "getStatusText", "uid", "showDebtorDetail", "dt_name", "create_time", "editData", "delData", "$index", "size", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "handleDialogClose", "ref", "ruleForm", "rules", "type_title", "aiGenerating", "generateLawyerLetter", "aiGeneratedContent", "useAiContent", "regenerateContent", "_e", "onProcessMethodChange", "processMethod", "fileGenerating", "generateAiFile", "aiGeneratedFile", "file_path", "changeFile", "staticStyle", "handleSuccess", "delImage", "content", "cancelDialog", "saveData", "dialogVisible", "show_image", "class", "showDebtorPanel", "closeDebtorPanel", "currentDebtor", "active", "activeTab", "id_card", "phone", "address", "debt_amount", "debt_type", "user_name", "user_phone", "user_register_time", "user_status", "files", "file", "getFileIcon", "upload_time", "previewFile", "downloadFile", "history", "record", "action", "description", "time", "staticRenderFns", "components", "EditorBar", "data", "allSize", "page", "is_pay", "url", "info", "contractTypes", "is_num", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "options", "mounted", "getData", "getContractTypes", "document", "addEventListener", "handleKeyDown", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "status", "statusMap", "$refs", "resetFields", "setTimeout", "template_file", "template_name", "template_size", "template_content", "matchedTemplate", "findMatchingTemplate", "$message", "warning", "debtorInfo", "getDebtorInfo", "userInfo", "getUserInfo", "simulateAiGeneration", "success", "error", "console", "toLowerCase", "typeTitle", "matchRules", "keywords", "templateId", "rule", "some", "includes", "find", "Promise", "resolve", "debt_date", "repay_date", "register_time", "template", "replace", "Date", "toLocaleDateString", "trim", "method", "fileName", "getTime", "path", "filed", "log", "clearData", "getInfo", "_this", "testData", "foundData", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "then", "deleteRequest", "resp", "code", "msg", "catch", "index", "splice", "$router", "go", "event", "generateIdCard", "generatePhone", "generateAddress", "generateDebtAmount", "generateDebtType", "generateUserName", "generateRegisterTime", "generateFiles", "generateHistory", "fileType", "iconMap", "prefixes", "prefix", "Math", "floor", "random", "year", "month", "String", "padStart", "day", "suffix", "addresses", "amounts", "types", "surnames", "names", "surname", "debtor<PERSON>ame", "filteredData", "filter", "startIndex", "endIndex", "pageData", "slice", "validate", "valid", "val", "res", "showImage", "beforeUpload", "isTypeTrue", "test", "getRequest", "component"], "mappings": "2IAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,UAAU,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIQ,UAAU,CAACR,EAAIS,GAAG,aAAa,OAAOP,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIS,GAAG,WAAWP,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,kBAAkB,UAAY,IAAII,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQb,EAAIc,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAgB,KAAYhB,EAAIiB,WAAWC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOrB,EAAIsB,OAAOC,QAASC,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAIsB,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAACzB,EAAG,IAAI,CAACE,YAAY,gCAAgCE,MAAM,CAAC,KAAO,UAAUsB,KAAK,cAAc,GAAG1B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIS,GAAG,UAAUP,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIc,MAAM,CAACC,MAAOrB,EAAIsB,OAAOO,QAASL,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAIsB,OAAQ,UAAWG,IAAME,WAAW,mBAAmB3B,EAAI8B,GAAI9B,EAAI+B,UAAU,SAASC,GAAM,OAAO9B,EAAG,YAAY,CAACc,IAAIgB,EAAKC,GAAG3B,MAAM,CAAC,MAAQ0B,EAAKE,MAAM,MAAQF,EAAKC,SAAQ,IAAI,KAAK/B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAIiB,aAAa,CAACjB,EAAIS,GAAG,UAAUP,EAAG,YAAY,CAACE,YAAY,YAAYE,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQP,EAAImC,cAAc,CAACnC,EAAIS,GAAG,WAAW,QAAQ,GAAGP,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACE,YAAY,aAAaE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACA,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIS,GAAG,eAAeP,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIS,GAAG,KAAKT,EAAIoC,GAAGpC,EAAIqC,OAAO,cAAcnC,EAAG,WAAW,CAACoC,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYnB,MAAOrB,EAAIyC,QAASd,WAAW,YAAYvB,YAAY,eAAeE,MAAM,CAAC,KAAON,EAAI0C,KAAK,OAAS,GAAG,OAAS,GAAG,aAAa,cAAc,CAACxC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIS,GAAGT,EAAIoC,GAAGU,EAAMC,IAAIC,sBAAsB9C,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACN,EAAIS,GAAG,IAAIT,EAAIoC,GAAGU,EAAMC,IAAInC,MAAQ,OAAO,cAAcV,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,OAAOqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,aAAaE,MAAM,CAAC,MAAQwC,EAAMC,IAAIb,QAAQ,CAAClC,EAAIS,GAAGT,EAAIoC,GAAGU,EAAMC,IAAIb,mBAAmBhC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,YAAY,OAAOqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,YAAYE,MAAM,CAAC,MAAQwC,EAAMC,IAAIE,OAAO,CAACjD,EAAIS,GAAG,IAAIT,EAAIoC,GAAGU,EAAMC,IAAIE,KAAQH,EAAMC,IAAIE,KAAKC,OAAS,GAAKJ,EAAMC,IAAIE,KAAKE,UAAU,EAAG,IAAM,MAAQL,EAAMC,IAAIE,KAAQ,KAAK,gBAAgB/C,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,UAAU,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAON,EAAIoD,cAAcN,EAAMC,IAAIlB,SAAS,KAAO,UAAU,CAAC7B,EAAIS,GAAG,IAAIT,EAAIoC,GAAGpC,EAAIqD,cAAcP,EAAMC,IAAIlB,UAAU,cAAc3B,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,MAAM,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGU,EAAMC,IAAIO,KAAO,iBAAiBpD,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,UAAU,MAAQ,MAAM,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,wBAAwBG,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAIuD,iBAAiBT,EAAMC,QAAQ,CAAC7C,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIS,GAAGT,EAAIoC,GAAGU,EAAMC,IAAIS,SAAW,QAAQtD,EAAG,IAAI,CAACE,YAAY,4CAA4CF,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGU,EAAMC,IAAIU,yBAAyBvD,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUqC,YAAY3C,EAAI4C,GAAG,CAAC,CAAC5B,IAAI,UAAU6B,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,gBAAgB,MAAQ,GAAG,SAAiC,IAAtBwC,EAAMC,IAAIlB,SAAetB,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAI0D,SAASZ,EAAMC,IAAId,OAAO,CAACjC,EAAIS,GAAG,IAAIT,EAAIoC,GAAyB,IAAtBU,EAAMC,IAAIlB,QAAgB,MAAQ,QAAQ,OAAO3B,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,gBAAgB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAI2D,QAAQb,EAAMc,OAAQd,EAAMC,IAAId,OAAO,CAACjC,EAAIS,GAAG,WAAW,WAAW,GAAGP,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACI,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,IAAK,KAAK,YAAYN,EAAI6D,KAAK,OAAS,0CAA0C,MAAQ7D,EAAIqC,MAAM,WAAa,IAAI9B,GAAG,CAAC,cAAcP,EAAI8D,iBAAiB,iBAAiB9D,EAAI+D,wBAAwB,IAAI,IAAI,GAAG7D,EAAG,YAAY,CAACE,YAAY,iBAAiBE,MAAM,CAAC,MAAQ,UAAU,QAAUN,EAAIgE,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOzD,GAAG,CAAC,iBAAiB,SAASI,GAAQX,EAAIgE,kBAAkBrD,GAAQ,MAAQX,EAAIiE,oBAAoB,CAAC/D,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACgE,IAAI,WAAW5D,MAAM,CAAC,MAAQN,EAAImE,SAAS,MAAQnE,EAAIoE,MAAM,iBAAiB,QAAQ,CAAClE,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,cAAcP,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACE,YAAY,iBAAiBE,MAAM,CAAC,SAAW,IAAIc,MAAM,CAACC,MAAOrB,EAAImE,SAASE,WAAY7C,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,aAAc1C,IAAME,WAAW,wBAAwB,CAACzB,EAAG,IAAI,CAACE,YAAY,iBAAiBE,MAAM,CAAC,KAAO,UAAUsB,KAAK,cAAc,IAAI,GAAG1B,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACE,YAAY,iBAAiBE,MAAM,CAAC,SAAW,IAAIc,MAAM,CAACC,MAAOrB,EAAImE,SAASjC,MAAOV,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,QAAS1C,IAAME,WAAW,mBAAmB,CAACzB,EAAG,IAAI,CAACE,YAAY,mBAAmBE,MAAM,CAAC,KAAO,UAAUsB,KAAK,cAAc,IAAI,IAAI,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACE,YAAY,oBAAoBE,MAAM,CAAC,SAAW,GAAG,KAAO,WAAW,KAAO,GAAGc,MAAM,CAACC,MAAOrB,EAAImE,SAASlB,KAAMzB,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,OAAQ1C,IAAME,WAAW,oBAAoB,IAAI,GAAGzB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIS,GAAG,cAAcP,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,iBAAiB,CAACE,YAAY,qBAAqBgB,MAAM,CAACC,MAAOrB,EAAImE,SAAStC,QAASL,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,UAAW1C,IAAME,WAAW,qBAAqB,CAACzB,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIS,GAAG,WAAWP,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIS,GAAG,YAAY,IAAI,IAAI,GAAGP,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBJ,EAAIS,GAAG,cAAcP,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACA,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,uCAAuCP,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,YAAY,CAACE,YAAY,kBAAkBE,MAAM,CAAC,KAAO,UAAU,KAAO,cAAc,QAAUN,EAAIsE,cAAc/D,GAAG,CAAC,MAAQP,EAAIuE,uBAAuB,CAACvE,EAAIS,GAAG,IAAIT,EAAIoC,GAAGpC,EAAIsE,aAAe,WAAa,WAAW,QAAQ,GAAItE,EAAIwE,mBAAoBtE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIS,GAAG,cAAcP,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,WAAW,CAACE,YAAY,sBAAsBE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,uBAAuBc,MAAM,CAACC,MAAOrB,EAAIwE,mBAAoBhD,SAAS,SAAUC,GAAMzB,EAAIwE,mBAAmB/C,GAAKE,WAAW,yBAAyB,GAAGzB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgB,KAAO,SAASC,GAAG,CAAC,MAAQP,EAAIyE,eAAe,CAACzE,EAAIS,GAAG,aAAaP,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkB,KAAO,SAASC,GAAG,CAAC,MAAQP,EAAI0E,oBAAoB,CAAC1E,EAAIS,GAAG,aAAa,KAAKT,EAAI2E,SAAkC,GAAxB3E,EAAImE,SAAStC,QAAc3B,EAAG,MAAM,CAACE,YAAY,mCAAmC,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIS,GAAG,YAAYP,EAAG,eAAe,CAACE,YAAY,sBAAsBE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,iBAAiB,CAACE,YAAY,qBAAqBG,GAAG,CAAC,OAASP,EAAI4E,uBAAuBxD,MAAM,CAACC,MAAOrB,EAAI6E,cAAerD,SAAS,SAAUC,GAAMzB,EAAI6E,cAAcpD,GAAKE,WAAW,kBAAkB,CAACzB,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAG,IAAI,CAACE,YAAY,gBAAgBJ,EAAIS,GAAG,cAAcP,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIS,GAAG,eAAe,IAAI,GAA0B,OAAtBT,EAAI6E,cAAwB3E,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,WAAW,CAACE,YAAY,gBAAgBE,MAAM,CAAC,MAAQ,SAAS,YAAc,kCAAkC,KAAO,OAAO,UAAW,EAAM,YAAY,OAAO,GAAIN,EAAIwE,mBAAoBtE,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAG,WAAW,CAACE,YAAY,sBAAsBE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,gBAAgB,SAAW,IAAIc,MAAM,CAACC,MAAOrB,EAAIwE,mBAAoBhD,SAAS,SAAUC,GAAMzB,EAAIwE,mBAAmB/C,GAAKE,WAAW,yBAAyB,IAAI,GAAG3B,EAAI2E,KAAKzE,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,YAAY,CAACE,YAAY,oBAAoBE,MAAM,CAAC,KAAO,UAAU,KAAO,mBAAmB,QAAUN,EAAI8E,gBAAgBvE,GAAG,CAAC,MAAQP,EAAI+E,iBAAiB,CAAC/E,EAAIS,GAAG,IAAIT,EAAIoC,GAAGpC,EAAI8E,eAAiB,WAAa,WAAW,OAAQ9E,EAAIgF,gBAAiB9E,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAIgF,gBAAgBzC,SAASrC,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,SAAS,CAACN,EAAIS,GAAG,UAAU,GAAGT,EAAI2E,MAAM,MAAM,GAAG3E,EAAI2E,KAA4B,WAAtB3E,EAAI6E,cAA4B3E,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,eAAe,CAACE,YAAY,mBAAmBE,MAAM,CAAC,MAAQ,QAAQ,KAAO,cAAc,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACE,YAAY,aAAaE,MAAM,CAAC,YAAc,WAAW,SAAW,IAAIc,MAAM,CAACC,MAAOrB,EAAImE,SAASc,UAAWzD,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,YAAa1C,IAAME,WAAW,uBAAuB,CAACzB,EAAG,IAAI,CAACE,YAAY,mBAAmBE,MAAM,CAAC,KAAO,UAAUsB,KAAK,aAAa1B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAIkF,WAAW,gBAAgB,CAAChF,EAAG,YAAY,CAACiF,YAAY,CAAC,QAAU,gBAAgB7E,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAaN,EAAIoF,gBAAgB,CAACpF,EAAIS,GAAG,aAAa,GAAIT,EAAImE,SAASc,UAAW/E,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAIqF,SAASrF,EAAImE,SAASc,UAAW,gBAAgB,CAACjF,EAAIS,GAAG,YAAYT,EAAI2E,MAAM,IAAI,MAAM,GAAG3E,EAAI2E,KAAKzE,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,WAAW,CAACE,YAAY,mBAAmBE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,mBAAmBc,MAAM,CAACC,MAAOrB,EAAImE,SAASmB,QAAS9D,SAAS,SAAUC,GAAMzB,EAAI0B,KAAK1B,EAAImE,SAAU,UAAW1C,IAAME,WAAW,uBAAuB,IAAI,GAAG3B,EAAI2E,QAAQ,GAAGzE,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUsB,KAAK,UAAU,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,iBAAiBC,GAAG,CAAC,MAAQP,EAAIuF,eAAe,CAACvF,EAAIS,GAAG,SAASP,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,iBAAiBC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAIwF,cAAc,CAACxF,EAAIS,GAAG,UAAU,KAAKP,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAIyF,cAAc,MAAQ,OAAOlF,GAAG,CAAC,iBAAiB,SAASI,GAAQX,EAAIyF,cAAc9E,KAAU,CAACT,EAAG,WAAW,CAACI,MAAM,CAAC,IAAMN,EAAI0F,eAAe,GAAGxF,EAAG,MAAM,CAACE,YAAY,sBAAsBuF,MAAM,CAAE,aAAc3F,EAAI4F,kBAAmB,CAAC1F,EAAG,MAAM,CAACE,YAAY,gBAAgBG,GAAG,CAAC,MAAQP,EAAI6F,oBAAoB3F,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,GAAGH,EAAG,IAAI,CAACE,YAAY,kBAAkB,CAACJ,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAActC,cAActD,EAAG,YAAY,CAACE,YAAY,YAAYE,MAAM,CAAC,KAAO,OAAO,KAAO,iBAAiBC,GAAG,CAAC,MAAQP,EAAI6F,qBAAqB,GAAG3F,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYuF,MAAM,CAAEI,OAA0B,UAAlB/F,EAAIgG,WAAwBzF,GAAG,CAAC,MAAQ,SAASI,GAAQX,EAAIgG,UAAY,WAAW,CAAC9F,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,OAAO,CAACF,EAAIS,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,YAAYuF,MAAM,CAAEI,OAA0B,SAAlB/F,EAAIgG,WAAuBzF,GAAG,CAAC,MAAQ,SAASI,GAAQX,EAAIgG,UAAY,UAAU,CAAC9F,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIS,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,YAAYuF,MAAM,CAAEI,OAA0B,UAAlB/F,EAAIgG,WAAwBzF,GAAG,CAAC,MAAQ,SAASI,GAAQX,EAAIgG,UAAY,WAAW,CAAC9F,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,OAAO,CAACF,EAAIS,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,YAAYuF,MAAM,CAAEI,OAA0B,YAAlB/F,EAAIgG,WAA0BzF,GAAG,CAAC,MAAQ,SAASI,GAAQX,EAAIgG,UAAY,aAAa,CAAC9F,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIS,GAAG,cAAcP,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAoB,UAAlBJ,EAAIgG,UAAuB9F,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,SAASP,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAActC,cAActD,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,WAAWP,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAAcG,SAAW,YAAY/F,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,WAAWP,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAAcI,OAAS,YAAYhG,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,SAASP,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAAcK,SAAW,YAAYjG,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,WAAWP,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIS,GAAG,IAAIT,EAAIoC,GAAGpC,EAAI8F,cAAcM,aAAe,aAAalG,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIS,GAAG,WAAWP,EAAG,OAAO,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAAcO,WAAa,kBAAkBrG,EAAI2E,KAAwB,SAAlB3E,EAAIgG,UAAsB9F,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,KAAK,CAACF,EAAIS,GAAGT,EAAIoC,GAAGpC,EAAI8F,cAAcQ,cAAcpG,EAAG,IAAI,CAACF,EAAIS,GAAG,OAAOT,EAAIoC,GAAGpC,EAAI8F,cAAcS,eAAerG,EAAG,IAAI,CAACF,EAAIS,GAAG,QAAQT,EAAIoC,GAAGpC,EAAI8F,cAAcU,uBAAuBtG,EAAG,IAAI,CAACF,EAAIS,GAAG,UAAUP,EAAG,SAAS,CAACI,MAAM,CAAC,KAAyC,WAAlCN,EAAI8F,cAAcW,YAA2B,UAAY,UAAU,KAAO,SAAS,CAACzG,EAAIS,GAAG,IAAIT,EAAIoC,GAAqC,WAAlCpC,EAAI8F,cAAcW,YAA2B,KAAO,MAAM,QAAQ,WAAWzG,EAAI2E,KAAwB,UAAlB3E,EAAIgG,UAAuB9F,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAI8B,GAAI9B,EAAI8F,cAAcY,OAAO,SAASC,GAAM,OAAOzG,EAAG,MAAM,CAACc,IAAI2F,EAAK1E,GAAG7B,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACyF,MAAM3F,EAAI4G,YAAYD,EAAK/F,UAAUV,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,KAAK,CAACF,EAAIS,GAAGT,EAAIoC,GAAGuE,EAAKpE,SAASrC,EAAG,IAAI,CAACF,EAAIS,GAAGT,EAAIoC,GAAGuE,EAAKE,gBAAgB3G,EAAG,IAAI,CAACE,YAAY,aAAa,CAACJ,EAAIS,GAAGT,EAAIoC,GAAGuE,EAAK9C,WAAW3D,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAI8G,YAAYH,MAAS,CAACzG,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,UAAUP,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOX,EAAI+G,aAAaJ,MAAS,CAACzG,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIS,GAAG,WAAW,QAAST,EAAI8F,cAAcY,OAA4C,IAAnC1G,EAAI8F,cAAcY,MAAMxD,OAA+HlD,EAAI2E,KAArHzE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,IAAI,CAACF,EAAIS,GAAG,eAAwB,OAAOT,EAAI2E,KAAwB,YAAlB3E,EAAIgG,UAAyB9F,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACJ,EAAI8B,GAAI9B,EAAI8F,cAAckB,SAAS,SAASC,GAAQ,OAAO/G,EAAG,MAAM,CAACc,IAAIiG,EAAOhF,GAAG7B,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiBF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,KAAK,CAACF,EAAIS,GAAGT,EAAIoC,GAAG6E,EAAOC,WAAWhH,EAAG,IAAI,CAACF,EAAIS,GAAGT,EAAIoC,GAAG6E,EAAOE,gBAAgBjH,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIS,GAAGT,EAAIoC,GAAG6E,EAAOG,gBAAiBpH,EAAI8F,cAAckB,SAAgD,IAArChH,EAAI8F,cAAckB,QAAQ9D,OAAwHlD,EAAI2E,KAA9GzE,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,IAAI,CAACF,EAAIS,GAAG,eAAwB,OAAOT,EAAI2E,cAAc,IAEzikB0C,EAAkB,CAAC,WAAY,IAAIrH,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIS,GAAG,cAAcP,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIS,GAAG,qBAC5P,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,cACjI,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,gBACnI,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,eACnI,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,0BAC1G,WAAY,IAAIJ,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIS,GAAG,aACrI,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,c,YCqpBtH,GACf8B,KAAA,OACA+E,WAAA,CAAAC,kBACAC,OACA,OACAC,QAAA,OACA/E,KAAA,GACAL,MAAA,EACAqF,KAAA,EACA7D,KAAA,GACAvC,OAAA,CACAC,QAAA,GACAoG,QAAA,EACA9F,SAAA,GAEAY,SAAA,EACAmF,IAAA,WACA1F,MAAA,MACA2F,KAAA,GACA7D,mBAAA,EACA0B,WAAA,GACAD,eAAA,EAEAG,iBAAA,EACAI,UAAA,QACAF,cAAA,GAEAxB,cAAA,EACAE,mBAAA,GACAsD,cAAA,GAEAjD,cAAA,SACAC,gBAAA,EACAE,gBAAA,KACAb,SAAA,CACAjC,MAAA,GACA6F,OAAA,GAGA3D,MAAA,CACAlC,MAAA,CACA,CACA8F,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAjD,UAAA,CACA,CACA+C,UAAA,EACAC,QAAA,QACAC,QAAA,UAIAC,eAAA,QACAC,QAAA,CACA,CACAnG,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAGAH,SAAA,CACA,CACAE,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,UAKAmG,UACA,KAAAC,UACA,KAAAC,mBAEAC,SAAAC,iBAAA,eAAAC,gBAGAC,gBAEAH,SAAAI,oBAAA,eAAAF,gBAEAG,QAAA,CAEAzF,cAAA0F,GACA,MAAAC,EAAA,CACA,YACA,YACA,aAEA,OAAAA,EAAAD,IAAA,QAIAzF,cAAAyF,GACA,MAAAC,EAAA,CACA,QACA,QACA,SAEA,OAAAA,EAAAD,IAAA,MAIA7H,aACA,KAAAyG,KAAA,EACA,KAAA7D,KAAA,GACA,KAAAyE,WAIAnG,cACA,KAAAb,OAAA,CACAC,QAAA,GACAM,SAAA,GAEA,KAAAZ,cAIAgD,oBAEA,KAAA+E,MAAA7E,UACA,KAAA6E,MAAA7E,SAAA8E,cAEA,KAAA9E,SAAA,CACAjC,MAAA,GACA6F,OAAA,EACAlG,QAAA,EACAwC,WAAA,GACApB,KAAA,GACAgC,UAAA,GACAK,QAAA,KAKAC,eACA,KAAAtB,oBACA,KAAAD,mBAAA,EAEA,KAAAQ,mBAAA,IAIA+D,mBAEAW,WAAA,KACA,KAAApB,cAAA,CACA,CACA7F,GAAA,EACAC,MAAA,OACAiH,cAAA,mDACAC,cAAA,iBACAC,cAAA,OACAC,iBAAA,6fAyBA,CACArH,GAAA,EACAC,MAAA,OACAiH,cAAA,kDACAC,cAAA,gBACAC,cAAA,MACAC,iBAAA,wYA2BA,CACArH,GAAA,EACAC,MAAA,SACAiH,cAAA,kDACAC,cAAA,kBACAC,cAAA,OACAC,iBAAA,+WA0BA,CACArH,GAAA,EACAC,MAAA,OACAiH,cAAA,iDACAC,cAAA,iBACAC,cAAA,OACAC,iBAAA,wSAqBA,CACArH,GAAA,EACAC,MAAA,OACAiH,cAAA,gDACAC,cAAA,kBACAC,cAAA,OACAC,iBAAA,kTAwBA,MAIA,6BACA,QAAAnF,SAAAjC,OAAA,KAAAiC,SAAAE,WAAA,CAKA,KAAAC,cAAA,EAEA,IAEA,MAAAiF,EAAA,KAAAC,uBAEA,IAAAD,EAGA,OAFA,KAAAE,SAAAC,QAAA,qCACA,KAAApF,cAAA,GAKA,MAAAqF,QAAA,KAAAC,gBAGAC,QAAA,KAAAC,oBAGA,KAAAC,qBAAAR,EAAAI,EAAAE,GAEA,KAAAJ,SAAAO,QAAA,cAGA,YAAAnF,eACAqE,WAAA,KACA,KAAAnE,kBACA,KAGA,MAAAkF,GACAC,QAAAD,MAAA,UAAAA,GACA,KAAAR,SAAAQ,MAAA,cACA,QACA,KAAA3F,cAAA,QAtCA,KAAAmF,SAAAC,QAAA,eA2CAF,uBAEA,MAAAtH,EAAA,KAAAiC,SAAAjC,MAAAiI,cACAC,EAAA,KAAAjG,SAAAE,WAAA8F,cAGAE,EAAA,CACA,CAAAC,SAAA,sBAAAC,WAAA,GACA,CAAAD,SAAA,iBAAAC,WAAA,GACA,CAAAD,SAAA,wBAAAC,WAAA,GACA,CAAAD,SAAA,iBAAAC,WAAA,GACA,CAAAD,SAAA,iBAAAC,WAAA,IAGA,UAAAC,KAAAH,EACA,GAAAG,EAAAF,SAAAG,KAAAlJ,GACAW,EAAAwI,SAAAnJ,IAAA6I,EAAAM,SAAAnJ,IAEA,YAAAuG,cAAA6C,KAAA/J,KAAAqB,KAAAuI,EAAAD,YAKA,YAAAzC,cAAA6C,KAAA/J,GAAA,IAAAA,EAAAqB,KAIA,sBAEA,WAAA2I,QAAAC,IACA3B,WAAA,KACA2B,EAAA,CACAtI,KAAA,KAAA4B,SAAAX,SAAA,KACAyC,QAAA,qBACAC,MAAA,cACAC,QAAA,iBACAC,YAAA,YACAC,UAAA,OACAyE,UAAA,aACAC,WAAA,gBAEA,QAKA,oBAEA,WAAAH,QAAAC,IACA3B,WAAA,KACA2B,EAAA,CACAtI,KAAA,KACA2D,MAAA,KAAA/B,SAAAb,KAAA,cACA0H,cAAA,aACAlC,OAAA,YAEA,QAKA,2BAAAmC,EAAAtB,EAAAE,GACA,WAAAe,QAAAC,IACA3B,WAAA,KAEA,IAAA5D,EAAA2F,EAAA3B,iBAGAhE,IAAA4F,QAAA,uBAAAvB,EAAApH,MACA+C,IAAA4F,QAAA,uBAAAvB,EAAAvD,aACAd,IAAA4F,QAAA,qBAAAvB,EAAAmB,WACAxF,IAAA4F,QAAA,sBAAAvB,EAAAoB,YAGAzF,IAAA4F,QAAA,qBAAArB,EAAAtH,MAGA+C,IAAA4F,QAAA,6BAAAC,MAAAC,mBAAA,UACA9F,IAAA4F,QAAA,uCACA5F,IAAA4F,QAAA,yCACA5F,IAAA4F,QAAA,6CAGA,KAAA/G,SAAAlB,OACAqC,IAAA4F,QAAA,+BAAA/G,SAAAlB,MACAqC,IAAA4F,QAAA,gCAAA/G,SAAAlB,MACAqC,IAAA4F,QAAA,qCAAA/G,SAAAlB,MACAqC,IAAA4F,QAAA,qCAAA/G,SAAAlB,OAIAqC,IAAA4F,QAAA,0BAEA,KAAA1G,mBAAAc,EAAA+F,OACAR,KACA,QAKApG,eACA,KAAAD,oBAMA,KAAAL,SAAAmB,QAAA,KAAAd,mBAGA,KAAAL,SAAAtC,QAAA,EAEA,KAAA4H,SAAAO,QAAA,kBAVA,KAAAP,SAAAC,QAAA,gBAcAhF,oBACA,KAAAF,mBAAA,GACA,KAAAD,wBAIAK,sBAAA0G,GAEA,OAAAA,GAEA,KAAAnH,SAAAc,UAAA,GACA,KAAAD,gBAAA,OAGA,KAAAR,mBAAA,GACA,KAAAQ,gBAAA,OAKA,uBACA,QAAAR,mBAAA,CAKA,KAAAM,gBAAA,EACA,UAEA,IAAA8F,QAAAC,GAAA3B,WAAA2B,EAAA,OAIA,MAAAU,EAAA,YAAApH,SAAAX,SAAA,cAAA2H,MAAAK,iBAEA,KAAAxG,gBAAA,CACAzC,KAAAgJ,EACAE,KAAA,2BAAAF,EACA1H,KAAA,QAIA,KAAAM,SAAAc,UAAA,KAAAD,gBAAAyG,KAEA,KAAAhC,SAAAO,QAAA,UACA,MAAAC,GACAC,QAAAD,MAAA,UAAAA,GACA,KAAAR,SAAAQ,MAAA,cACA,QACA,KAAAnF,gBAAA,QA3BA,KAAA2E,SAAAC,QAAA,cA+BAxE,WAAAwG,GACA,KAAAA,QACAxB,QAAAyB,IAAA,KAAAD,QAEAE,YACA,KAAAtK,OAAA,CACAC,QAAA,GACAoG,OAAA,IAEA,KAAAW,WAEA5E,SAAAzB,GAEA,GAAAA,EACA,KAAA4J,QAAA5J,GAEA,KAAAkC,SAAA,CACAjC,MAAA,GACAe,KAAA,KAIA4I,QAAA5J,GACA,IAAA6J,EAAA,KAGA,MAAAC,EAAA,CACA,CACA9J,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,mDACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,UACAY,UAAA,GACAK,QAAA,IAEA,CACArD,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,gCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,UACAY,UAAA,GACAK,QAAA,IAEA,CACArD,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,YACAe,KAAA,uCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,YACAY,UAAA,0CACAK,QAAA,qBAEA,CACArD,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,iCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,UACAY,UAAA,GACAK,QAAA,IAEA,CACArD,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,YACAe,KAAA,mCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,YACAY,UAAA,GACAK,QAAA,KAKA0G,EAAAD,EAAApB,KAAA3I,KAAAC,QAEA+J,GACAF,EAAA3H,SAAA,IAAA6H,GACAF,EAAA9H,mBAAA,EACAkG,QAAAyB,IAAA,aAAAG,EAAA3H,WAEA2H,EAAArC,SAAA,CACA7I,KAAA,QACAqH,QAAA,iBAmBAgE,QAAAhK,GACA,KAAAiK,SAAA,gBACAC,kBAAA,KACAC,iBAAA,KACAxL,KAAA,YAEAyL,KAAA,KACA,KAAAC,cAAA,KAAA1E,IAAA,cAAA3F,GAAAoK,KAAAE,IACA,KAAAA,EAAAC,KACA,KAAA/C,SAAA,CACA7I,KAAA,UACAqH,QAAAsE,EAAAE,MAGA,KAAAhD,SAAA,CACA7I,KAAA,QACAqH,QAAAsE,EAAAE,UAKAC,MAAA,KACA,KAAAjD,SAAA,CACA7I,KAAA,QACAqH,QAAA,aAIAtE,QAAAgJ,EAAA1K,GACA,KAAAiK,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAxL,KAAA,YAEAyL,KAAA,KACA,KAAAC,cAAA,KAAA1E,IAAA,aAAA3F,GAAAoK,KAAAE,IACA,KAAAA,EAAAC,OACA,KAAA/C,SAAA,CACA7I,KAAA,UACAqH,QAAA,UAEA,KAAAvF,KAAAkK,OAAAD,EAAA,QAIAD,MAAA,KACA,KAAAjD,SAAA,CACA7I,KAAA,QACAqH,QAAA,aAIAzH,UACA,KAAAqM,QAAAC,GAAA,IAEA7L,aACA,KAAAyG,KAAA,EACA,KAAA7D,KAAA,GACA,KAAAyE,WAIAI,cAAAqE,GAEA,KAAAA,EAAAhM,UACA,KAAAiD,mBACA,KAAAuB,eAEA,KAAAK,iBACA,KAAAC,qBAMAtC,iBAAAR,GACAmH,QAAAyB,IAAA,WAAA5I,GAGA,KAAA+C,cAAA,IACA/C,EAEAkD,QAAA,KAAA+G,iBACA9G,MAAA,KAAA+G,gBACA9G,QAAA,KAAA+G,kBACA9G,YAAA,KAAA+G,qBACA9G,UAAA,KAAA+G,mBAGA9G,UAAA,KAAA+G,iBAAAtK,EAAAO,KACAiD,WAAAxD,EAAAO,IACAkD,mBAAA,KAAA8G,uBACA7G,YAAA,SAGAC,MAAA,KAAA6G,cAAAxK,EAAAS,SAGAwD,QAAA,KAAAwG,gBAAAzK,EAAAS,UAGA,KAAAwC,UAAA,QACA,KAAAJ,iBAAA,GAIAC,mBACA,KAAAD,iBAAA,EACA,KAAAE,cAAA,IAIAc,YAAA6G,GACA,MAAAC,EAAA,CACA,uBACA,uBACA,wBACA,sBACA,uBACA,sBACA,4BACA,6BAEA,OAAAA,EAAAD,IAAA,oBAIA3G,YAAAH,GACAuD,QAAAyB,IAAA,QAAAhF,GACA,KAAA8C,SAAA5B,KAAA,iBAIAd,aAAAJ,GACAuD,QAAAyB,IAAA,QAAAhF,GACA,KAAA8C,SAAAO,QAAA,WAAArD,EAAApE,OAIAyK,iBACA,MAAAW,EAAA,sCACAC,EAAAD,EAAAE,KAAAC,MAAAD,KAAAE,SAAAJ,EAAAzK,SACA8K,EAAA,KAAAH,KAAAC,MAAA,GAAAD,KAAAE,UACAE,EAAAC,OAAAL,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GAAAI,SAAA,OACAC,EAAAF,OAAAL,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GAAAI,SAAA,OACAE,EAAAH,OAAAL,KAAAC,MAAA,KAAAD,KAAAE,WAAAI,SAAA,OACA,SAAAP,IAAAI,IAAAC,IAAAG,IAAAC,KAGApB,gBACA,MAAAU,EAAA,sCACAC,EAAAD,EAAAE,KAAAC,MAAAD,KAAAE,SAAAJ,EAAAzK,SACAmL,EAAAH,OAAAL,KAAAC,MAAA,IAAAD,KAAAE,WAAAI,SAAA,OACA,SAAAP,IAAAS,KAGAnB,kBACA,MAAAoB,EAAA,CACA,iBACA,oBACA,oBACA,mBACA,gBAEA,OAAAA,EAAAT,KAAAC,MAAAD,KAAAE,SAAAO,EAAApL,UAGAiK,qBACA,MAAAoB,EAAA,8DACA,OAAAA,EAAAV,KAAAC,MAAAD,KAAAE,SAAAQ,EAAArL,UAGAkK,mBACA,MAAAoB,EAAA,qCACA,OAAAA,EAAAX,KAAAC,MAAAD,KAAAE,SAAAS,EAAAtL,UAGAmK,iBAAAnH,GACA,MAAAuI,EAAA,0CACAC,EAAA,0CACAC,EAAAF,EAAAZ,KAAAC,MAAAD,KAAAE,SAAAU,EAAAvL,SACAX,EAAAmM,EAAAb,KAAAC,MAAAD,KAAAE,SAAAW,EAAAxL,SACA,SAAAyL,IAAApM,KAGA+K,uBACA,MAAAU,EAAA,KAAAH,KAAAC,MAAA,EAAAD,KAAAE,UACAE,EAAAC,OAAAL,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GAAAI,SAAA,OACAC,EAAAF,OAAAL,KAAAC,MAAA,GAAAD,KAAAE,UAAA,GAAAI,SAAA,OACA,SAAAH,KAAAC,KAAAG,cAGAb,cAAAqB,GACA,OACA,CACA3M,GAAA,EACAM,KAAAqM,EAAA,WACAhO,KAAA,MACAiD,KAAA,QACAgD,YAAA,uBAEA,CACA5E,GAAA,EACAM,KAAAqM,EAAA,YACAhO,KAAA,MACAiD,KAAA,QACAgD,YAAA,uBAEA,CACA5E,GAAA,EACAM,KAAAqM,EAAA,YACAhO,KAAA,MACAiD,KAAA,QACAgD,YAAA,yBAKA2G,gBAAAoB,GACA,OACA,CACA3M,GAAA,EACAiF,OAAA,UACAC,YAAA,MAAAyH,UACAxH,KAAA,uBAEA,CACAnF,GAAA,EACAiF,OAAA,SACAC,YAAA,iBACAC,KAAA,uBAEA,CACAnF,GAAA,EACAiF,OAAA,QACAC,YAAA,kBACAC,KAAA,uBAEA,CACAnF,GAAA,EACAiF,OAAA,SACAC,YAAA,eACAC,KAAA,yBAKAkB,UACA,IAAAwD,EAAA,KAEAA,EAAArJ,SAAA,EAGA,MAAAsJ,EAAA,CACA,CACA9J,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,mDACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,WAEA,CACApC,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,gCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,WAEA,CACApC,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,YACAe,KAAA,uCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,YACAY,UAAA,0CACAK,QAAA,qBAEA,CACArD,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,UACAe,KAAA,iCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,WAEA,CACApC,GAAA,EACAe,SAAA,cACApC,KAAA,MACAsB,MAAA,YACAe,KAAA,mCACApB,QAAA,EACAyB,IAAA,cACAE,QAAA,KACAC,YAAA,sBACAY,WAAA,cAKA6E,WAAA,KACA,IACAgB,QAAAyB,IAAA,kBAGA,IAAAkD,EAAA9C,EACA,GAAAD,EAAAxK,OAAAC,SAAAuK,EAAAxK,OAAAC,QAAA8J,OAAA,CACA,MAAA9J,EAAAuK,EAAAxK,OAAAC,QAAA8J,OAAAlB,cACA0E,EAAA9C,EAAA+C,OAAA9M,GACAA,EAAAgB,SAAAmH,cAAAO,SAAAnJ,IACAS,EAAAE,MAAAiI,cAAAO,SAAAnJ,IACAS,EAAAsB,IAAAoH,SAAAnJ,IACAS,EAAAwB,QAAAkH,SAAAnJ,KAKA,IAAAuK,EAAAxK,OAAAO,SAAA,KAAAiK,EAAAxK,OAAAO,UACAgN,IAAAC,OAAA9M,KAAAH,UAAAiK,EAAAxK,OAAAO,UAIA,MAAAkN,GAAAjD,EAAApE,KAAA,GAAAoE,EAAAjI,KACAmL,EAAAD,EAAAjD,EAAAjI,KACAoL,EAAAJ,EAAAK,MAAAH,EAAAC,GAEAlD,EAAApJ,KAAAuM,EACAnD,EAAAzJ,MAAAwM,EAAA3L,OACA4I,EAAArJ,SAAA,EAEAyH,QAAAyB,IAAA,aAAAG,EAAApJ,MACAwH,QAAAyB,IAAA,MAAAG,EAAAzJ,OACA,MAAA4H,GACAC,QAAAD,MAAA,eAAAA,GACA6B,EAAApJ,KAAA,GACAoJ,EAAAzJ,MAAA,EACAyJ,EAAArJ,SAAA,IAEA,MAkBA+C,WACA,IAAAsG,EAAA,KACA,KAAA9C,MAAA,YAAAmG,SAAAC,IACA,IAAAA,EAiCA,SA/BAlF,QAAAyB,IAAA,gBAAAxH,UAGA+E,WAAA,KACA4C,EAAArC,SAAA,CACA7I,KAAA,UACAqH,QAAA,iBAEA,KAAAK,UACAwD,EAAA9H,mBAAA,GACA,QAyBAF,iBAAAuL,GACA,KAAAxL,KAAAwL,EAEA,KAAA/G,WAEAvE,oBAAAsL,GACA,KAAA3H,KAAA2H,EACA,KAAA/G,WAEAlD,cAAAkK,GACA,KAAAA,EAAA9C,MACA,KAAA/C,SAAAO,QAAA,QACA,KAAA7F,SAAA,KAAAuH,OAAA4D,EAAA9H,KAAAI,KAEA,KAAA6B,SAAAQ,MAAAqF,EAAA7C,MAIA8C,UAAA5I,GACA,KAAAjB,WAAAiB,EACA,KAAAlB,eAAA,GAEA+J,aAAA7I,GACA,MAAA8I,EAAA,0BAAAC,KAAA/I,EAAA/F,MACA6O,GACA,KAAAhG,SAAAQ,MAAA,cAIA5E,SAAAsB,EAAA4E,GACA,IAAAO,EAAA,KACAA,EAAA6D,WAAA,6BAAAhJ,GAAA0F,KAAAE,IACA,KAAAA,EAAAC,MACAV,EAAA3H,SAAAoH,GAAA,GAEAO,EAAArC,SAAAO,QAAA,UAEA8B,EAAArC,SAAAQ,MAAAsC,EAAAE,UChwD6W,I,wBCQzWmD,EAAY,eACd,EACA7P,EACAsH,GACA,EACA,KACA,WACA,MAIa,aAAAuI,E", "file": "js/chunk-1ca8c59e.f755cc21.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=style&index=0&id=4a81e5cf&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"lawyer-letter-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新数据 \")])],1)])]),_c('div',{staticClass:\"search-section\"},[_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-row\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"关键词搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入工单号/标题/用户手机号\",\"clearable\":\"\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchData.apply(null, arguments)}},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"处理状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择处理状态\",\"clearable\":\"\"},model:{value:(_vm.search.is_deal),callback:function ($$v) {_vm.$set(_vm.search, \"is_deal\", $$v)},expression:\"search.is_deal\"}},_vm._l((_vm.options1),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.title,\"value\":item.id}})}),1)],1)]),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{staticClass:\"search-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchData}},[_vm._v(\" 搜索 \")]),_c('el-button',{staticClass:\"reset-btn\",attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.clearSearch}},[_vm._v(\" 重置 \")])],1)])])],1),_c('div',{staticClass:\"table-section\"},[_c('el-card',{staticClass:\"table-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"table-header\"},[_c('div',{staticClass:\"table-title\"},[_c('h3',[_c('i',{staticClass:\"el-icon-tickets\"}),_vm._v(\" 律师函工单列表 \")]),_c('span',{staticClass:\"table-count\"},[_vm._v(\"共 \"+_vm._s(_vm.total)+\" 条记录\")])])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"lawyer-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\",\"border\":\"\",\"empty-text\":\"暂无律师函工单数据\"}},[_c('el-table-column',{attrs:{\"prop\":\"order_sn\",\"label\":\"工单号\",\"width\":\"140\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"order-cell\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_c('span',{staticClass:\"order-text\"},[_vm._v(_vm._s(scope.row.order_sn))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"工单类型\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.type || '律师函')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"工单标题\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"title-cell\"},[_c('span',{staticClass:\"title-text\",attrs:{\"title\":scope.row.title}},[_vm._v(_vm._s(scope.row.title))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"工单内容\",\"min-width\":\"250\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"desc-cell\"},[_c('span',{staticClass:\"desc-text\",attrs:{\"title\":scope.row.desc}},[_vm._v(\" \"+_vm._s(scope.row.desc ? (scope.row.desc.length > 50 ? scope.row.desc.substring(0, 50) + '...' : scope.row.desc) : '-')+\" \")])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"is_deal\",\"label\":\"处理状态\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{staticClass:\"status-tag\",attrs:{\"type\":_vm.getStatusType(scope.row.is_deal),\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(scope.row.is_deal))+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"uid\",\"label\":\"用户手机\",\"width\":\"130\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"phone-cell\"},[_c('i',{staticClass:\"el-icon-phone\"}),_c('span',[_vm._v(_vm._s(scope.row.uid || '-'))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"dt_name\",\"label\":\"债务人\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"debtor-cell clickable\",on:{\"click\":function($event){return _vm.showDebtorDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"debtor-name\"},[_vm._v(_vm._s(scope.row.dt_name || '-'))]),_c('i',{staticClass:\"el-icon-arrow-right arrow-icon\"})])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"发起时间\",\"width\":\"180\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-cell\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(scope.row.create_time))])])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-check\",\"plain\":\"\",\"disabled\":scope.row.is_deal === 2},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" \"+_vm._s(scope.row.is_deal === 2 ? '已完成' : '完成制作')+\" \")]),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-close\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 取消 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1)],1),_c('el-dialog',{staticClass:\"process-dialog\",attrs:{\"title\":\"律师函制作处理\",\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event},\"close\":_vm.handleDialogClose}},[_c('div',{staticClass:\"dialog-content\"},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-position\":\"top\"}},[_c('div',{staticClass:\"form-section\"},[_c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-info\"}),_vm._v(\" 工单基本信息 \")]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"工单类型\"}},[_c('el-input',{staticClass:\"readonly-input\",attrs:{\"readonly\":\"\"},model:{value:(_vm.ruleForm.type_title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"type_title\", $$v)},expression:\"ruleForm.type_title\"}},[_c('i',{staticClass:\"el-icon-folder\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"工单标题\"}},[_c('el-input',{staticClass:\"readonly-input\",attrs:{\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}},[_c('i',{staticClass:\"el-icon-document\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"工单描述\"}},[_c('el-input',{staticClass:\"readonly-textarea\",attrs:{\"readonly\":\"\",\"type\":\"textarea\",\"rows\":3},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1)],1),_c('div',{staticClass:\"form-section\"},[_c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-setting\"}),_vm._v(\" 处理状态设置 \")]),_c('el-form-item',{attrs:{\"label\":\"制作状态\"}},[_c('el-radio-group',{staticClass:\"status-radio-group\",model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_c('el-radio',{staticClass:\"status-radio\",attrs:{\"label\":1}},[_c('i',{staticClass:\"el-icon-loading\"}),_vm._v(\" 处理中 \")]),_c('el-radio',{staticClass:\"status-radio\",attrs:{\"label\":2}},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" 已完成 \")])],1)],1)],1),_c('div',{staticClass:\"form-section ai-section\"},[_c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-cpu\"}),_vm._v(\" AI智能生成 \")]),_c('div',{staticClass:\"ai-generation-content\"},[_c('div',{staticClass:\"ai-description\"},[_c('p',[_c('i',{staticClass:\"el-icon-info\"}),_vm._v(\" AI将根据工单信息、债务人详情和合同模板自动生成律师函内容 \")])]),_c('div',{staticClass:\"ai-actions\"},[_c('el-button',{staticClass:\"ai-generate-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-cpu\",\"loading\":_vm.aiGenerating},on:{\"click\":_vm.generateLawyerLetter}},[_vm._v(\" \"+_vm._s(_vm.aiGenerating ? 'AI生成中...' : 'AI生成律师函')+\" \")])],1),(_vm.aiGeneratedContent)?_c('div',{staticClass:\"ai-result\"},[_c('h5',{staticClass:\"result-title\"},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" AI生成结果 \")]),_c('div',{staticClass:\"generated-content\"},[_c('el-input',{staticClass:\"ai-content-textarea\",attrs:{\"type\":\"textarea\",\"rows\":8,\"placeholder\":\"AI生成的律师函内容将显示在这里...\"},model:{value:(_vm.aiGeneratedContent),callback:function ($$v) {_vm.aiGeneratedContent=$$v},expression:\"aiGeneratedContent\"}})],1),_c('div',{staticClass:\"ai-result-actions\"},[_c('el-button',{attrs:{\"type\":\"success\",\"icon\":\"el-icon-check\",\"size\":\"small\"},on:{\"click\":_vm.useAiContent}},[_vm._v(\" 使用此内容 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"icon\":\"el-icon-refresh\",\"size\":\"small\"},on:{\"click\":_vm.regenerateContent}},[_vm._v(\" 重新生成 \")])],1)]):_vm._e()])]),(_vm.ruleForm.is_deal == 2)?_c('div',{staticClass:\"form-section completion-section\"},[_c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 完成处理 \")]),_c('el-form-item',{staticClass:\"process-method-item\",attrs:{\"label\":\"处理方式\"}},[_c('el-radio-group',{staticClass:\"method-radio-group\",on:{\"change\":_vm.onProcessMethodChange},model:{value:(_vm.processMethod),callback:function ($$v) {_vm.processMethod=$$v},expression:\"processMethod\"}},[_c('el-radio',{staticClass:\"method-radio\",attrs:{\"label\":\"ai\"}},[_c('i',{staticClass:\"el-icon-cpu\"}),_vm._v(\" AI智能生成 \")]),_c('el-radio',{staticClass:\"method-radio\",attrs:{\"label\":\"upload\"}},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 手动上传文件 \")])],1)],1),(_vm.processMethod === 'ai')?_c('div',{staticClass:\"ai-process-section\"},[_c('div',{staticClass:\"ai-process-info\"},[_c('el-alert',{staticClass:\"ai-mode-alert\",attrs:{\"title\":\"AI生成模式\",\"description\":\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\",\"type\":\"info\",\"closable\":false,\"show-icon\":\"\"}})],1),(_vm.aiGeneratedContent)?_c('div',{staticClass:\"ai-content-preview\"},[_c('el-form-item',{attrs:{\"label\":\"生成内容预览\"}},[_c('el-input',{staticClass:\"ai-preview-textarea\",attrs:{\"type\":\"textarea\",\"rows\":6,\"placeholder\":\"AI生成的律师函内容...\",\"readonly\":\"\"},model:{value:(_vm.aiGeneratedContent),callback:function ($$v) {_vm.aiGeneratedContent=$$v},expression:\"aiGeneratedContent\"}})],1)],1):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"文件生成\"}},[_c('div',{staticClass:\"ai-file-generation\"},[_c('el-button',{staticClass:\"generate-file-btn\",attrs:{\"type\":\"success\",\"icon\":\"el-icon-document\",\"loading\":_vm.fileGenerating},on:{\"click\":_vm.generateAiFile}},[_vm._v(\" \"+_vm._s(_vm.fileGenerating ? '生成文件中...' : '生成律师函文件')+\" \")]),(_vm.aiGeneratedFile)?_c('div',{staticClass:\"generated-file-info\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(_vm._s(_vm.aiGeneratedFile.name))]),_c('el-tag',{attrs:{\"type\":\"success\",\"size\":\"mini\"}},[_vm._v(\"已生成\")])],1):_vm._e()],1)])],1):_vm._e(),(_vm.processMethod === 'upload')?_c('div',{staticClass:\"upload-process-section\"},[_c('el-form-item',{staticClass:\"file-upload-item\",attrs:{\"label\":\"律师函文件\",\"prop\":\"file_path\"}},[_c('div',{staticClass:\"upload-area\"},[_c('el-input',{staticClass:\"file-input\",attrs:{\"placeholder\":\"请上传律师函文件\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}},[_c('i',{staticClass:\"el-icon-document\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})]),_c('div',{staticClass:\"upload-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-upload\"},on:{\"click\":function($event){return _vm.changeFile('file_path')}}},[_c('el-upload',{staticStyle:{\"display\":\"inline-block\"},attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_vm._v(\" 上传文件 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\" 删除文件 \")]):_vm._e()],1)],1)])],1):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"处理说明\"}},[_c('el-input',{staticClass:\"content-textarea\",attrs:{\"type\":\"textarea\",\"rows\":4,\"placeholder\":\"请输入处理说明或备注信息...\"},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1)],1):_vm._e()])],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"icon\":\"el-icon-close\"},on:{\"click\":_vm.cancelDialog}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-check\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('div',{staticClass:\"debtor-detail-panel\",class:{ 'panel-open': _vm.showDebtorPanel }},[_c('div',{staticClass:\"panel-overlay\",on:{\"click\":_vm.closeDebtorPanel}}),_c('div',{staticClass:\"panel-content\"},[_c('div',{staticClass:\"panel-header\"},[_c('div',{staticClass:\"header-info\"},[_vm._m(1),_c('p',{staticClass:\"panel-subtitle\"},[_vm._v(_vm._s(_vm.currentDebtor.dt_name))])]),_c('el-button',{staticClass:\"close-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-close\"},on:{\"click\":_vm.closeDebtorPanel}})],1),_c('div',{staticClass:\"panel-body\"},[_c('div',{staticClass:\"sidebar-menu\"},[_c('div',{staticClass:\"menu-item\",class:{ active: _vm.activeTab === 'basic' },on:{\"click\":function($event){_vm.activeTab = 'basic'}}},[_c('i',{staticClass:\"el-icon-user-solid\"}),_c('span',[_vm._v(\"基本信息\")])]),_c('div',{staticClass:\"menu-item\",class:{ active: _vm.activeTab === 'user' },on:{\"click\":function($event){_vm.activeTab = 'user'}}},[_c('i',{staticClass:\"el-icon-phone\"}),_c('span',[_vm._v(\"关联用户\")])]),_c('div',{staticClass:\"menu-item\",class:{ active: _vm.activeTab === 'files' },on:{\"click\":function($event){_vm.activeTab = 'files'}}},[_c('i',{staticClass:\"el-icon-folder\"}),_c('span',[_vm._v(\"相关文件\")])]),_c('div',{staticClass:\"menu-item\",class:{ active: _vm.activeTab === 'history' },on:{\"click\":function($event){_vm.activeTab = 'history'}}},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(\"历史记录\")])])]),_c('div',{staticClass:\"content-area\"},[(_vm.activeTab === 'basic')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"info-section\"},[_vm._m(2),_c('div',{staticClass:\"info-grid\"},[_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"姓名：\")]),_c('span',[_vm._v(_vm._s(_vm.currentDebtor.dt_name))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"身份证号：\")]),_c('span',[_vm._v(_vm._s(_vm.currentDebtor.id_card || '未提供'))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"联系电话：\")]),_c('span',[_vm._v(_vm._s(_vm.currentDebtor.phone || '未提供'))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"地址：\")]),_c('span',[_vm._v(_vm._s(_vm.currentDebtor.address || '未提供'))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"债务金额：\")]),_c('span',{staticClass:\"debt-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.currentDebtor.debt_amount || '0.00'))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"债务类型：\")]),_c('span',[_vm._v(_vm._s(_vm.currentDebtor.debt_type || '未分类'))])])])])]):_vm._e(),(_vm.activeTab === 'user')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"info-section\"},[_vm._m(3),_c('div',{staticClass:\"user-card\"},[_vm._m(4),_c('div',{staticClass:\"user-info\"},[_c('h5',[_vm._v(_vm._s(_vm.currentDebtor.user_name))]),_c('p',[_vm._v(\"手机号：\"+_vm._s(_vm.currentDebtor.user_phone))]),_c('p',[_vm._v(\"注册时间：\"+_vm._s(_vm.currentDebtor.user_register_time))]),_c('p',[_vm._v(\"用户状态： \"),_c('el-tag',{attrs:{\"type\":_vm.currentDebtor.user_status === 'active' ? 'success' : 'warning',\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(_vm.currentDebtor.user_status === 'active' ? '正常' : '异常')+\" \")])],1)])])])]):_vm._e(),(_vm.activeTab === 'files')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"info-section\"},[_vm._m(5),_c('div',{staticClass:\"file-list\"},[_vm._l((_vm.currentDebtor.files),function(file){return _c('div',{key:file.id,staticClass:\"file-item\"},[_c('div',{staticClass:\"file-icon\"},[_c('i',{class:_vm.getFileIcon(file.type)})]),_c('div',{staticClass:\"file-info\"},[_c('h6',[_vm._v(_vm._s(file.name))]),_c('p',[_vm._v(_vm._s(file.upload_time))]),_c('p',{staticClass:\"file-size\"},[_vm._v(_vm._s(file.size))])]),_c('div',{staticClass:\"file-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.previewFile(file)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 预览 \")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.downloadFile(file)}}},[_c('i',{staticClass:\"el-icon-download\"}),_vm._v(\" 下载 \")])],1)])}),(!_vm.currentDebtor.files || _vm.currentDebtor.files.length === 0)?_c('div',{staticClass:\"empty-files\"},[_c('i',{staticClass:\"el-icon-folder-opened\"}),_c('p',[_vm._v(\"暂无相关文件\")])]):_vm._e()],2)])]):_vm._e(),(_vm.activeTab === 'history')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"info-section\"},[_vm._m(6),_c('div',{staticClass:\"history-timeline\"},[_vm._l((_vm.currentDebtor.history),function(record){return _c('div',{key:record.id,staticClass:\"timeline-item\"},[_c('div',{staticClass:\"timeline-dot\"}),_c('div',{staticClass:\"timeline-content\"},[_c('h6',[_vm._v(_vm._s(record.action))]),_c('p',[_vm._v(_vm._s(record.description))]),_c('span',{staticClass:\"timeline-time\"},[_vm._v(_vm._s(record.time))])])])}),(!_vm.currentDebtor.history || _vm.currentDebtor.history.length === 0)?_c('div',{staticClass:\"empty-history\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('p',[_vm._v(\"暂无历史记录\")])]):_vm._e()],2)])]):_vm._e()])])])])],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"title-section\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 发律师函管理 \")]),_c('p',{staticClass:\"page-subtitle\"},[_vm._v(\"管理和处理律师函制作工单\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('h3',{staticClass:\"panel-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 债务人详情 \")])\n},function (){var _vm=this,_c=_vm._self._c;return _c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-info\"}),_vm._v(\" 债务人基本信息 \")])\n},function (){var _vm=this,_c=_vm._self._c;return _c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 关联用户信息 \")])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-avatar\"},[_c('i',{staticClass:\"el-icon-user-solid\"})])\n},function (){var _vm=this,_c=_vm._self._c;return _c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-folder\"}),_vm._v(\" 相关文件 \")])\n},function (){var _vm=this,_c=_vm._self._c;return _c('h4',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" 历史记录 \")])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"lawyer-letter-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            发律师函管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和处理律师函制作工单</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新数据\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">关键词搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入工单号/标题/用户手机号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">处理状态</label>\r\n              <el-select\r\n                v-model=\"search.is_deal\"\r\n                placeholder=\"请选择处理状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in options1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.title\"\r\n                  :value=\"item.id\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <h3>\r\n              <i class=\"el-icon-tickets\"></i>\r\n              律师函工单列表\r\n            </h3>\r\n            <span class=\"table-count\">共 {{ total }} 条记录</span>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"lawyer-table\"\r\n          stripe\r\n          border\r\n          empty-text=\"暂无律师函工单数据\"\r\n        >\r\n          <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"order-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"order-text\">{{ scope.row.order_sn }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"type\" label=\"工单类型\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ scope.row.type || '律师函' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <span class=\"title-text\" :title=\"scope.row.title\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"desc-cell\">\r\n                <span class=\"desc-text\" :title=\"scope.row.desc\">\r\n                  {{ scope.row.desc ? (scope.row.desc.length > 50 ? scope.row.desc.substring(0, 50) + '...' : scope.row.desc) : '-' }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getStatusType(scope.row.is_deal)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getStatusText(scope.row.is_deal) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"uid\" label=\"用户手机\" width=\"130\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"phone-cell\">\r\n                <i class=\"el-icon-phone\"></i>\r\n                <span>{{ scope.row.uid || '-' }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"dt_name\" label=\"债务人\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"debtor-cell clickable\" @click=\"showDebtorDetail(scope.row)\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span class=\"debtor-name\">{{ scope.row.dt_name || '-' }}</span>\r\n                <i class=\"el-icon-arrow-right arrow-icon\"></i>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-check\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                  :disabled=\"scope.row.is_deal === 2\"\r\n                >\r\n                  {{ scope.row.is_deal === 2 ? '已完成' : '完成制作' }}\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-close\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  取消\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <!-- 律师函处理对话框 -->\r\n    <el-dialog\r\n      title=\"律师函制作处理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"process-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <div class=\"dialog-content\">\r\n        <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-position=\"top\">\r\n          <!-- 工单基本信息 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-info\"></i>\r\n              工单基本信息\r\n            </h4>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单类型\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.type_title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-folder\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"工单标题\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.title\"\r\n                    readonly\r\n                    class=\"readonly-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"工单描述\">\r\n              <el-input\r\n                v-model=\"ruleForm.desc\"\r\n                readonly\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                class=\"readonly-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 处理状态设置 -->\r\n          <div class=\"form-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-setting\"></i>\r\n              处理状态设置\r\n            </h4>\r\n            <el-form-item label=\"制作状态\">\r\n              <el-radio-group v-model=\"ruleForm.is_deal\" class=\"status-radio-group\">\r\n                <el-radio :label=\"1\" class=\"status-radio\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  处理中\r\n                </el-radio>\r\n                <el-radio :label=\"2\" class=\"status-radio\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  已完成\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- AI生成律师函 -->\r\n          <div class=\"form-section ai-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-cpu\"></i>\r\n              AI智能生成\r\n            </h4>\r\n            <div class=\"ai-generation-content\">\r\n              <div class=\"ai-description\">\r\n                <p>\r\n                  <i class=\"el-icon-info\"></i>\r\n                  AI将根据工单信息、债务人详情和合同模板自动生成律师函内容\r\n                </p>\r\n              </div>\r\n              <div class=\"ai-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-cpu\"\r\n                  @click=\"generateLawyerLetter\"\r\n                  :loading=\"aiGenerating\"\r\n                  class=\"ai-generate-btn\"\r\n                >\r\n                  {{ aiGenerating ? 'AI生成中...' : 'AI生成律师函' }}\r\n                </el-button>\r\n              </div>\r\n              <!-- AI生成结果展示 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-result\">\r\n                <h5 class=\"result-title\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                  AI生成结果\r\n                </h5>\r\n                <div class=\"generated-content\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"8\"\r\n                    placeholder=\"AI生成的律师函内容将显示在这里...\"\r\n                    class=\"ai-content-textarea\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"ai-result-actions\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-check\"\r\n                    @click=\"useAiContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    使用此内容\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"regenerateContent\"\r\n                    size=\"small\"\r\n                  >\r\n                    重新生成\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 完成处理区域 -->\r\n          <div v-if=\"ruleForm.is_deal == 2\" class=\"form-section completion-section\">\r\n            <h4 class=\"section-title\">\r\n              <i class=\"el-icon-upload\"></i>\r\n              完成处理\r\n            </h4>\r\n\r\n            <!-- 处理方式选择 -->\r\n            <el-form-item label=\"处理方式\" class=\"process-method-item\">\r\n              <el-radio-group v-model=\"processMethod\" @change=\"onProcessMethodChange\" class=\"method-radio-group\">\r\n                <el-radio label=\"ai\" class=\"method-radio\">\r\n                  <i class=\"el-icon-cpu\"></i>\r\n                  AI智能生成\r\n                </el-radio>\r\n                <el-radio label=\"upload\" class=\"method-radio\">\r\n                  <i class=\"el-icon-upload\"></i>\r\n                  手动上传文件\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <!-- AI生成方式 -->\r\n            <div v-if=\"processMethod === 'ai'\" class=\"ai-process-section\">\r\n              <div class=\"ai-process-info\">\r\n                <el-alert\r\n                  title=\"AI生成模式\"\r\n                  description=\"系统将根据工单信息和债务人详情自动生成律师函文件，无需手动上传\"\r\n                  type=\"info\"\r\n                  :closable=\"false\"\r\n                  show-icon\r\n                  class=\"ai-mode-alert\"\r\n                ></el-alert>\r\n              </div>\r\n\r\n              <!-- AI生成的内容预览 -->\r\n              <div v-if=\"aiGeneratedContent\" class=\"ai-content-preview\">\r\n                <el-form-item label=\"生成内容预览\">\r\n                  <el-input\r\n                    v-model=\"aiGeneratedContent\"\r\n                    type=\"textarea\"\r\n                    :rows=\"6\"\r\n                    placeholder=\"AI生成的律师函内容...\"\r\n                    class=\"ai-preview-textarea\"\r\n                    readonly\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <el-form-item label=\"文件生成\">\r\n                <div class=\"ai-file-generation\">\r\n                  <el-button\r\n                    type=\"success\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"generateAiFile\"\r\n                    :loading=\"fileGenerating\"\r\n                    class=\"generate-file-btn\"\r\n                  >\r\n                    {{ fileGenerating ? '生成文件中...' : '生成律师函文件' }}\r\n                  </el-button>\r\n                  <div v-if=\"aiGeneratedFile\" class=\"generated-file-info\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <span>{{ aiGeneratedFile.name }}</span>\r\n                    <el-tag type=\"success\" size=\"mini\">已生成</el-tag>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <!-- 手动上传方式 -->\r\n            <div v-if=\"processMethod === 'upload'\" class=\"upload-process-section\">\r\n              <el-form-item\r\n                label=\"律师函文件\"\r\n                prop=\"file_path\"\r\n                class=\"file-upload-item\"\r\n              >\r\n                <div class=\"upload-area\">\r\n                  <el-input\r\n                    v-model=\"ruleForm.file_path\"\r\n                    placeholder=\"请上传律师函文件\"\r\n                    readonly\r\n                    class=\"file-input\"\r\n                  >\r\n                    <i slot=\"prefix\" class=\"el-icon-document\"></i>\r\n                  </el-input>\r\n                  <div class=\"upload-buttons\">\r\n                    <el-button @click=\"changeFile('file_path')\" type=\"primary\" icon=\"el-icon-upload\">\r\n                      <el-upload\r\n                        action=\"/admin/Upload/uploadFile\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleSuccess\"\r\n                        style=\"display: inline-block;\"\r\n                      >\r\n                        上传文件\r\n                      </el-upload>\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"ruleForm.file_path\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n                    >\r\n                      删除文件\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n\r\n            <el-form-item label=\"处理说明\">\r\n              <el-input\r\n                v-model=\"ruleForm.content\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入处理说明或备注信息...\"\r\n                class=\"content-textarea\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\" icon=\"el-icon-close\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" icon=\"el-icon-check\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 债务人详情右侧滑出面板 -->\r\n    <div class=\"debtor-detail-panel\" :class=\"{ 'panel-open': showDebtorPanel }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDebtorPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"header-info\">\r\n            <h3 class=\"panel-title\">\r\n              <i class=\"el-icon-user\"></i>\r\n              债务人详情\r\n            </h3>\r\n            <p class=\"panel-subtitle\">{{ currentDebtor.dt_name }}</p>\r\n          </div>\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"closeDebtorPanel\"\r\n            class=\"close-btn\"\r\n          ></el-button>\r\n        </div>\r\n\r\n        <!-- 左侧菜单 -->\r\n        <div class=\"panel-body\">\r\n          <div class=\"sidebar-menu\">\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'basic' }\"\r\n                 @click=\"activeTab = 'basic'\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n              <span>基本信息</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'user' }\"\r\n                 @click=\"activeTab = 'user'\">\r\n              <i class=\"el-icon-phone\"></i>\r\n              <span>关联用户</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'files' }\"\r\n                 @click=\"activeTab = 'files'\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              <span>相关文件</span>\r\n            </div>\r\n            <div class=\"menu-item\"\r\n                 :class=\"{ active: activeTab === 'history' }\"\r\n                 @click=\"activeTab = 'history'\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>历史记录</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"content-area\">\r\n            <!-- 基本信息 -->\r\n            <div v-if=\"activeTab === 'basic'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-info\"></i>\r\n                  债务人基本信息\r\n                </h4>\r\n                <div class=\"info-grid\">\r\n                  <div class=\"info-item\">\r\n                    <label>姓名：</label>\r\n                    <span>{{ currentDebtor.dt_name }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentDebtor.id_card || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>联系电话：</label>\r\n                    <span>{{ currentDebtor.phone || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>地址：</label>\r\n                    <span>{{ currentDebtor.address || '未提供' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务金额：</label>\r\n                    <span class=\"debt-amount\">¥{{ currentDebtor.debt_amount || '0.00' }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>债务类型：</label>\r\n                    <span>{{ currentDebtor.debt_type || '未分类' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 关联用户信息 -->\r\n            <div v-if=\"activeTab === 'user'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-user\"></i>\r\n                  关联用户信息\r\n                </h4>\r\n                <div class=\"user-card\">\r\n                  <div class=\"user-avatar\">\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </div>\r\n                  <div class=\"user-info\">\r\n                    <h5>{{ currentDebtor.user_name }}</h5>\r\n                    <p>手机号：{{ currentDebtor.user_phone }}</p>\r\n                    <p>注册时间：{{ currentDebtor.user_register_time }}</p>\r\n                    <p>用户状态：\r\n                      <el-tag :type=\"currentDebtor.user_status === 'active' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ currentDebtor.user_status === 'active' ? '正常' : '异常' }}\r\n                      </el-tag>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 相关文件 -->\r\n            <div v-if=\"activeTab === 'files'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-folder\"></i>\r\n                  相关文件\r\n                </h4>\r\n                <div class=\"file-list\">\r\n                  <div v-for=\"file in currentDebtor.files\" :key=\"file.id\" class=\"file-item\">\r\n                    <div class=\"file-icon\">\r\n                      <i :class=\"getFileIcon(file.type)\"></i>\r\n                    </div>\r\n                    <div class=\"file-info\">\r\n                      <h6>{{ file.name }}</h6>\r\n                      <p>{{ file.upload_time }}</p>\r\n                      <p class=\"file-size\">{{ file.size }}</p>\r\n                    </div>\r\n                    <div class=\"file-actions\">\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"previewFile(file)\">\r\n                        <i class=\"el-icon-view\"></i>\r\n                        预览\r\n                      </el-button>\r\n                      <el-button type=\"text\" size=\"mini\" @click=\"downloadFile(file)\">\r\n                        <i class=\"el-icon-download\"></i>\r\n                        下载\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.files || currentDebtor.files.length === 0\" class=\"empty-files\">\r\n                    <i class=\"el-icon-folder-opened\"></i>\r\n                    <p>暂无相关文件</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 历史记录 -->\r\n            <div v-if=\"activeTab === 'history'\" class=\"tab-content\">\r\n              <div class=\"info-section\">\r\n                <h4 class=\"section-title\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  历史记录\r\n                </h4>\r\n                <div class=\"history-timeline\">\r\n                  <div v-for=\"record in currentDebtor.history\" :key=\"record.id\" class=\"timeline-item\">\r\n                    <div class=\"timeline-dot\"></div>\r\n                    <div class=\"timeline-content\">\r\n                      <h6>{{ record.action }}</h6>\r\n                      <p>{{ record.description }}</p>\r\n                      <span class=\"timeline-time\">{{ record.time }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"!currentDebtor.history || currentDebtor.history.length === 0\" class=\"empty-history\">\r\n                    <i class=\"el-icon-time\"></i>\r\n                    <p>暂无历史记录</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      // 债务人详情面板相关\r\n      showDebtorPanel: false,\r\n      activeTab: 'basic',\r\n      currentDebtor: {},\r\n      // AI生成相关\r\n      aiGenerating: false,\r\n      aiGeneratedContent: '',\r\n      contractTypes: [], // 合同类型列表\r\n      // 处理方式相关\r\n      processMethod: 'upload', // 默认为手动上传，可选值：'ai' | 'upload'\r\n      fileGenerating: false, // 文件生成状态\r\n      aiGeneratedFile: null, // AI生成的文件信息\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n  methods: {\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        0: 'warning',  // 待处理\r\n        1: 'primary',  // 处理中\r\n        2: 'success'   // 已处理\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待处理',\r\n        1: '处理中',\r\n        2: '已处理'\r\n      };\r\n      return statusMap[status] || '未知';\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_deal: -1,\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        is_deal: 1,\r\n        type_title: \"\",\r\n        desc: \"\",\r\n        file_path: \"\",\r\n        content: \"\"\r\n      };\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      this.handleDialogClose();\r\n      this.dialogFormVisible = false;\r\n      // 重置AI生成内容\r\n      this.aiGeneratedContent = '';\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"债务催收\",\r\n            template_file: \"/uploads/templates/debt_collection_template.docx\",\r\n            template_name: \"债务催收律师函模板.docx\",\r\n            template_size: 245760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您欠付我方当事人的债务事宜，特致函如下：\r\n\r\n一、债务事实\r\n根据相关证据材料显示，您于{{debt_date}}向我方当事人借款人民币{{debt_amount}}元，约定还款期限为{{repay_date}}。但截至目前，您仍未履行还款义务，已构成违约。\r\n\r\n二、法律后果\r\n您的上述行为已构成违约，根据《中华人民共和国民法典》相关规定，您应当承担相应的法律责任。\r\n\r\n三、催告要求\r\n现特函告知，请您在收到本函后7日内，将所欠款项{{debt_amount}}元及相应利息一次性支付给我方当事人。\r\n\r\n四、法律警告\r\n如您在上述期限内仍不履行还款义务，我方将依法采取包括但不限于向人民法院提起诉讼等法律手段维护我方当事人的合法权益，由此产生的一切法律后果由您承担。\r\n\r\n特此函告！\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n联系电话：{{contact_phone}}\r\n地址：{{law_firm_address}}\r\n            `\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"合同违约\",\r\n            template_file: \"/uploads/templates/contract_breach_template.pdf\",\r\n            template_name: \"合同违约律师函模板.pdf\",\r\n            template_size: 512000,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您违反合同约定的事宜，特致函如下：\r\n\r\n一、合同事实\r\n您与我方当事人于{{contract_date}}签订了《{{contract_title}}》，约定了双方的权利义务。\r\n\r\n二、违约事实\r\n根据合同约定及相关证据，您存在以下违约行为：\r\n{{breach_details}}\r\n\r\n三、法律后果\r\n您的违约行为已给我方当事人造成了经济损失，根据合同约定及法律规定，您应当承担违约责任。\r\n\r\n四、要求\r\n请您在收到本函后7日内：\r\n1. 立即停止违约行为\r\n2. 履行合同义务\r\n3. 赔偿相应损失\r\n\r\n如您拒不履行，我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"知识产权侵权\",\r\n            template_file: \"/uploads/templates/ip_infringement_template.doc\",\r\n            template_name: \"知识产权侵权律师函模板.doc\",\r\n            template_size: 327680,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就您侵犯我方当事人知识产权的事宜，特致函如下：\r\n\r\n一、权利基础\r\n我方当事人依法享有{{ip_type}}的专有权利，该权利受法律保护。\r\n\r\n二、侵权事实\r\n经调查发现，您未经我方当事人许可，擅自{{infringement_details}}，侵犯了我方当事人的合法权益。\r\n\r\n三、法律后果\r\n您的行为构成侵权，应当承担停止侵害、赔偿损失等法律责任。\r\n\r\n四、要求\r\n请您在收到本函后立即：\r\n1. 停止一切侵权行为\r\n2. 销毁侵权产品\r\n3. 赔偿经济损失\r\n\r\n否则我方将依法追究您的法律责任。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"劳动争议\",\r\n            template_file: \"/uploads/templates/labor_dispute_template.docx\",\r\n            template_name: \"劳动争议律师函模板.docx\",\r\n            template_size: 298760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就劳动争议事宜，特致函如下：\r\n\r\n一、劳动关系\r\n我方当事人与您存在劳动关系，期间为{{employment_period}}。\r\n\r\n二、争议事实\r\n{{dispute_details}}\r\n\r\n三、法律依据\r\n根据《劳动法》、《劳动合同法》等相关法律法规，您应当履行相应义务。\r\n\r\n四、要求\r\n请您在收到本函后7日内妥善处理相关事宜，否则我方将通过法律途径解决。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"房屋租赁\",\r\n            template_file: \"/uploads/templates/lease_dispute_template.pdf\",\r\n            template_name: \"房屋租赁纠纷律师函模板.pdf\",\r\n            template_size: 445760,\r\n            template_content: `\r\n尊敬的{{debtor_name}}先生/女士：\r\n\r\n我们是{{user_name}}的法律代理人。现就房屋租赁纠纷事宜，特致函如下：\r\n\r\n一、租赁关系\r\n您与我方当事人签订了房屋租赁合同，租赁期限为{{lease_period}}。\r\n\r\n二、违约事实\r\n{{lease_breach_details}}\r\n\r\n三、要求\r\n请您在收到本函后立即：\r\n1. {{specific_requirements}}\r\n2. 支付相关费用\r\n3. 配合解决纠纷\r\n\r\n如不配合，我方将依法维权。\r\n\r\n{{law_firm_name}}\r\n{{current_date}}\r\n            `\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成律师函\r\n    async generateLawyerLetter() {\r\n      if (!this.ruleForm.title || !this.ruleForm.type_title) {\r\n        this.$message.warning('请先确保工单信息完整');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n\r\n      try {\r\n        // 1. 根据工单标题和类型找到对应的合同模板\r\n        const matchedTemplate = this.findMatchingTemplate();\r\n\r\n        if (!matchedTemplate) {\r\n          this.$message.warning('未找到匹配的律师函模板，请先在合同类型管理中上传相应模板');\r\n          this.aiGenerating = false;\r\n          return;\r\n        }\r\n\r\n        // 2. 获取债务人详情信息\r\n        const debtorInfo = await this.getDebtorInfo();\r\n\r\n        // 3. 获取关联用户信息\r\n        const userInfo = await this.getUserInfo();\r\n\r\n        // 4. 模拟AI生成过程\r\n        await this.simulateAiGeneration(matchedTemplate, debtorInfo, userInfo);\r\n\r\n        this.$message.success('AI律师函生成完成！');\r\n\r\n        // 如果是AI模式，自动生成文件\r\n        if (this.processMethod === 'ai') {\r\n          setTimeout(() => {\r\n            this.generateAiFile();\r\n          }, 500);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('AI生成失败:', error);\r\n        this.$message.error('AI生成失败，请重试');\r\n      } finally {\r\n        this.aiGenerating = false;\r\n      }\r\n    },\r\n\r\n    // 查找匹配的模板\r\n    findMatchingTemplate() {\r\n      // 根据工单标题和类型匹配模板\r\n      const title = this.ruleForm.title.toLowerCase();\r\n      const typeTitle = this.ruleForm.type_title.toLowerCase();\r\n\r\n      // 匹配规则\r\n      const matchRules = [\r\n        { keywords: ['债务', '催收', '欠款', '借款'], templateId: 1 },\r\n        { keywords: ['合同', '违约', '违反'], templateId: 2 },\r\n        { keywords: ['知识产权', '侵权', '商标', '专利'], templateId: 3 },\r\n        { keywords: ['劳动', '工资', '员工'], templateId: 4 },\r\n        { keywords: ['租赁', '房屋', '租金'], templateId: 5 }\r\n      ];\r\n\r\n      for (const rule of matchRules) {\r\n        if (rule.keywords.some(keyword =>\r\n          title.includes(keyword) || typeTitle.includes(keyword)\r\n        )) {\r\n          return this.contractTypes.find(type => type.id === rule.templateId);\r\n        }\r\n      }\r\n\r\n      // 默认返回债务催收模板\r\n      return this.contractTypes.find(type => type.id === 1);\r\n    },\r\n\r\n    // 获取债务人信息\r\n    async getDebtorInfo() {\r\n      // 模拟获取债务人详细信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: this.ruleForm.dt_name || '张三',\r\n            id_card: '110101199001011234',\r\n            phone: '13800138001',\r\n            address: '北京市朝阳区建国门外大街1号',\r\n            debt_amount: '100000.00',\r\n            debt_type: '借款纠纷',\r\n            debt_date: '2023-06-15',\r\n            repay_date: '2023-12-15'\r\n          });\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      // 模拟获取关联用户信息\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            name: '李明',\r\n            phone: this.ruleForm.uid || '13900139001',\r\n            register_time: '2023-01-15',\r\n            status: 'active'\r\n          });\r\n        }, 300);\r\n      });\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    async simulateAiGeneration(template, debtorInfo, userInfo) {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          // 替换模板中的变量\r\n          let content = template.template_content;\r\n\r\n          // 替换债务人信息\r\n          content = content.replace(/\\{\\{debtor_name\\}\\}/g, debtorInfo.name);\r\n          content = content.replace(/\\{\\{debt_amount\\}\\}/g, debtorInfo.debt_amount);\r\n          content = content.replace(/\\{\\{debt_date\\}\\}/g, debtorInfo.debt_date);\r\n          content = content.replace(/\\{\\{repay_date\\}\\}/g, debtorInfo.repay_date);\r\n\r\n          // 替换用户信息\r\n          content = content.replace(/\\{\\{user_name\\}\\}/g, userInfo.name);\r\n\r\n          // 替换其他信息\r\n          content = content.replace(/\\{\\{current_date\\}\\}/g, new Date().toLocaleDateString('zh-CN'));\r\n          content = content.replace(/\\{\\{law_firm_name\\}\\}/g, '北京市XX律师事务所');\r\n          content = content.replace(/\\{\\{contact_phone\\}\\}/g, '010-12345678');\r\n          content = content.replace(/\\{\\{law_firm_address\\}\\}/g, '北京市朝阳区XX大厦XX层');\r\n\r\n          // 根据工单描述添加具体内容\r\n          if (this.ruleForm.desc) {\r\n            content = content.replace(/\\{\\{breach_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{dispute_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{infringement_details\\}\\}/g, this.ruleForm.desc);\r\n            content = content.replace(/\\{\\{lease_breach_details\\}\\}/g, this.ruleForm.desc);\r\n          }\r\n\r\n          // 清理未替换的变量\r\n          content = content.replace(/\\{\\{[^}]+\\}\\}/g, '[待填写]');\r\n\r\n          this.aiGeneratedContent = content.trim();\r\n          resolve();\r\n        }, 2000); // 模拟2秒的AI生成时间\r\n      });\r\n    },\r\n\r\n    // 使用AI生成的内容\r\n    useAiContent() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('没有可用的AI生成内容');\r\n        return;\r\n      }\r\n\r\n      // 将AI生成的内容设置到处理说明中\r\n      this.ruleForm.content = this.aiGeneratedContent;\r\n\r\n      // 自动设置为已完成状态\r\n      this.ruleForm.is_deal = 2;\r\n\r\n      this.$message.success('已应用AI生成的律师函内容');\r\n    },\r\n\r\n    // 重新生成内容\r\n    regenerateContent() {\r\n      this.aiGeneratedContent = '';\r\n      this.generateLawyerLetter();\r\n    },\r\n\r\n    // 处理方式切换\r\n    onProcessMethodChange(method) {\r\n      // 清空之前的数据\r\n      if (method === 'ai') {\r\n        // 切换到AI模式，清空上传的文件\r\n        this.ruleForm.file_path = '';\r\n        this.aiGeneratedFile = null;\r\n      } else {\r\n        // 切换到上传模式，清空AI生成的内容\r\n        this.aiGeneratedContent = '';\r\n        this.aiGeneratedFile = null;\r\n      }\r\n    },\r\n\r\n    // AI生成文件\r\n    async generateAiFile() {\r\n      if (!this.aiGeneratedContent) {\r\n        this.$message.warning('请先生成律师函内容');\r\n        return;\r\n      }\r\n\r\n      this.fileGenerating = true;\r\n      try {\r\n        // 模拟文件生成过程\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n        // 这里应该调用实际的文件生成接口\r\n        // 将AI生成的内容转换为文件\r\n        const fileName = `律师函_${this.ruleForm.dt_name || '债务人'}_${new Date().getTime()}.docx`;\r\n\r\n        this.aiGeneratedFile = {\r\n          name: fileName,\r\n          path: `/uploads/lawyer_letters/${fileName}`,\r\n          size: '25KB'\r\n        };\r\n\r\n        // 将生成的文件路径设置到表单中\r\n        this.ruleForm.file_path = this.aiGeneratedFile.path;\r\n\r\n        this.$message.success('文件生成成功');\r\n      } catch (error) {\r\n        console.error('文件生成失败:', error);\r\n        this.$message.error('文件生成失败，请重试');\r\n      } finally {\r\n        this.fileGenerating = false;\r\n      }\r\n    },\r\n\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n\r\n      // 使用模拟数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\",\r\n          file_path: \"\",\r\n          content: \"\"\r\n        }\r\n      ];\r\n\r\n      // 查找对应的数据\r\n      const foundData = testData.find(item => item.id === id);\r\n\r\n      if (foundData) {\r\n        _this.ruleForm = { ...foundData };\r\n        _this.dialogFormVisible = true;\r\n        console.log('加载律师函详情数据:', _this.ruleForm);\r\n      } else {\r\n        _this.$message({\r\n          type: \"error\",\r\n          message: \"未找到对应的律师函数据\",\r\n        });\r\n      }\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27) {\r\n        if (this.dialogFormVisible) {\r\n          this.cancelDialog();\r\n        }\r\n        if (this.showDebtorPanel) {\r\n          this.closeDebtorPanel();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 显示债务人详情\r\n    showDebtorDetail(row) {\r\n      console.log('显示债务人详情:', row);\r\n\r\n      // 模拟获取债务人详细信息\r\n      this.currentDebtor = {\r\n        ...row,\r\n        // 基本信息\r\n        id_card: this.generateIdCard(),\r\n        phone: this.generatePhone(),\r\n        address: this.generateAddress(),\r\n        debt_amount: this.generateDebtAmount(),\r\n        debt_type: this.generateDebtType(),\r\n\r\n        // 关联用户信息\r\n        user_name: this.generateUserName(row.uid),\r\n        user_phone: row.uid,\r\n        user_register_time: this.generateRegisterTime(),\r\n        user_status: 'active',\r\n\r\n        // 相关文件\r\n        files: this.generateFiles(row.dt_name),\r\n\r\n        // 历史记录\r\n        history: this.generateHistory(row.dt_name)\r\n      };\r\n\r\n      this.activeTab = 'basic';\r\n      this.showDebtorPanel = true;\r\n    },\r\n\r\n    // 关闭债务人详情面板\r\n    closeDebtorPanel() {\r\n      this.showDebtorPanel = false;\r\n      this.currentDebtor = {};\r\n    },\r\n\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'zip': 'el-icon-folder-opened',\r\n        'rar': 'el-icon-folder-opened'\r\n      };\r\n      return iconMap[fileType] || 'el-icon-document';\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      console.log('预览文件:', file);\r\n      this.$message.info('文件预览功能开发中...');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      console.log('下载文件:', file);\r\n      this.$message.success('开始下载文件: ' + file.name);\r\n    },\r\n\r\n    // 生成模拟数据的辅助方法\r\n    generateIdCard() {\r\n      const prefixes = ['110101', '310101', '440101', '500101'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const year = 1980 + Math.floor(Math.random() * 30);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      const suffix = String(Math.floor(Math.random() * 9999)).padStart(4, '0');\r\n      return `${prefix}${year}${month}${day}${suffix}`;\r\n    },\r\n\r\n    generatePhone() {\r\n      const prefixes = ['138', '139', '150', '151', '188', '189'];\r\n      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];\r\n      const suffix = String(Math.floor(Math.random() * 100000000)).padStart(8, '0');\r\n      return `${prefix}${suffix}`;\r\n    },\r\n\r\n    generateAddress() {\r\n      const addresses = [\r\n        '北京市朝阳区建国门外大街1号',\r\n        '上海市浦东新区陆家嘴环路1000号',\r\n        '广州市天河区珠江新城花城大道85号',\r\n        '深圳市南山区深南大道10000号',\r\n        '杭州市西湖区文三路90号'\r\n      ];\r\n      return addresses[Math.floor(Math.random() * addresses.length)];\r\n    },\r\n\r\n    generateDebtAmount() {\r\n      const amounts = ['50000.00', '100000.00', '200000.00', '500000.00', '1000000.00'];\r\n      return amounts[Math.floor(Math.random() * amounts.length)];\r\n    },\r\n\r\n    generateDebtType() {\r\n      const types = ['借款纠纷', '合同违约', '房屋租赁', '劳动争议', '知识产权'];\r\n      return types[Math.floor(Math.random() * types.length)];\r\n    },\r\n\r\n    generateUserName(phone) {\r\n      const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴'];\r\n      const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];\r\n      const surname = surnames[Math.floor(Math.random() * surnames.length)];\r\n      const name = names[Math.floor(Math.random() * names.length)];\r\n      return `${surname}${name}`;\r\n    },\r\n\r\n    generateRegisterTime() {\r\n      const year = 2020 + Math.floor(Math.random() * 4);\r\n      const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0');\r\n      const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0');\r\n      return `${year}-${month}-${day} 10:30:00`;\r\n    },\r\n\r\n    generateFiles(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: `${debtorName}_身份证.jpg`,\r\n          type: 'jpg',\r\n          size: '2.5MB',\r\n          upload_time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: `${debtorName}_借款合同.pdf`,\r\n          type: 'pdf',\r\n          size: '1.2MB',\r\n          upload_time: '2024-01-16 09:15:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: `${debtorName}_银行流水.pdf`,\r\n          type: 'pdf',\r\n          size: '3.8MB',\r\n          upload_time: '2024-01-17 16:45:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    generateHistory(debtorName) {\r\n      return [\r\n        {\r\n          id: 1,\r\n          action: '创建债务人档案',\r\n          description: `创建了${debtorName}的债务人档案`,\r\n          time: '2024-01-15 09:30:00'\r\n        },\r\n        {\r\n          id: 2,\r\n          action: '上传相关文件',\r\n          description: '上传了身份证、合同等相关文件',\r\n          time: '2024-01-15 14:30:00'\r\n        },\r\n        {\r\n          id: 3,\r\n          action: '发起律师函',\r\n          description: '针对债务纠纷发起律师函制作申请',\r\n          time: '2024-01-16 10:20:00'\r\n        },\r\n        {\r\n          id: 4,\r\n          action: '更新债务信息',\r\n          description: '更新了债务金额和联系方式',\r\n          time: '2024-01-17 15:10:00'\r\n        }\r\n      ];\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          order_sn: \"LF202401001\",\r\n          type: \"律师函\",\r\n          title: \"债务催收律师函\",\r\n          desc: \"针对张三欠款10万元未还的情况，要求其在收到律师函后7日内归还全部欠款，否则将采取法律手段追讨。\",\r\n          is_deal: 0,\r\n          uid: \"13800138001\",\r\n          dt_name: \"张三\",\r\n          create_time: \"2024-01-15 09:30:00\",\r\n          type_title: \"债务催收律师函\"\r\n        },\r\n        {\r\n          id: 2,\r\n          order_sn: \"LF202401002\",\r\n          type: \"律师函\",\r\n          title: \"合同违约律师函\",\r\n          desc: \"李四违反购房合同约定，未按时支付房款，要求其履行合同义务。\",\r\n          is_deal: 1,\r\n          uid: \"13900139002\",\r\n          dt_name: \"李四\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          type_title: \"合同违约律师函\"\r\n        },\r\n        {\r\n          id: 3,\r\n          order_sn: \"LF202401003\",\r\n          type: \"律师函\",\r\n          title: \"知识产权侵权律师函\",\r\n          desc: \"王五未经授权使用我方商标，构成商标侵权，要求立即停止侵权行为并赔偿损失。\",\r\n          is_deal: 2,\r\n          uid: \"13700137003\",\r\n          dt_name: \"王五\",\r\n          create_time: \"2024-01-17 11:45:00\",\r\n          type_title: \"知识产权侵权律师函\",\r\n          file_path: \"/uploads/lawyer_letters/LF202401003.pdf\",\r\n          content: \"已完成律师函制作，已发送给当事人。\"\r\n        },\r\n        {\r\n          id: 4,\r\n          order_sn: \"LF202401004\",\r\n          type: \"律师函\",\r\n          title: \"劳动争议律师函\",\r\n          desc: \"赵六公司拖欠员工工资3个月，要求立即支付拖欠工资及相应补偿。\",\r\n          is_deal: 0,\r\n          uid: \"13600136004\",\r\n          dt_name: \"赵六\",\r\n          create_time: \"2024-01-18 16:10:00\",\r\n          type_title: \"劳动争议律师函\"\r\n        },\r\n        {\r\n          id: 5,\r\n          order_sn: \"LF202401005\",\r\n          type: \"律师函\",\r\n          title: \"房屋租赁纠纷律师函\",\r\n          desc: \"田七拒不搬离租赁房屋，已逾期3个月，要求立即搬离并支付逾期租金。\",\r\n          is_deal: 1,\r\n          uid: \"13500135005\",\r\n          dt_name: \"田七\",\r\n          create_time: \"2024-01-19 10:25:00\",\r\n          type_title: \"房屋租赁纠纷律师函\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载律师函测试数据...');\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.order_sn.toLowerCase().includes(keyword) ||\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.uid.includes(keyword) ||\r\n              item.dt_name.includes(keyword)\r\n            );\r\n          }\r\n\r\n          // 状态筛选\r\n          if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n            filteredData = filteredData.filter(item => item.is_deal === _this.search.is_deal);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('律师函数据加载完成:', _this.list);\r\n          console.log('总数:', _this.total);\r\n        } catch (error) {\r\n          console.error('加载律师函测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 300);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存操作\r\n          console.log('保存律师函数据:', this.ruleForm);\r\n\r\n          // 模拟API延迟\r\n          setTimeout(() => {\r\n            _this.$message({\r\n              type: \"success\",\r\n              message: \"律师函处理状态更新成功！\",\r\n            });\r\n            this.getData();\r\n            _this.dialogFormVisible = false;\r\n          }, 500);\r\n\r\n          // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n          /*\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n          */\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.lawyer-letter-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section h2.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  align-items: flex-end;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input,\r\n.search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  border: none;\r\n  padding: 10px 24px;\r\n}\r\n\r\n.reset-btn {\r\n  padding: 10px 24px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.table-header {\r\n  padding: 20px 24px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.table-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.lawyer-table {\r\n  margin: 0 24px 20px;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.order-cell,\r\n.title-cell,\r\n.desc-cell,\r\n.phone-cell,\r\n.debtor-cell,\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.order-text,\r\n.title-text,\r\n.desc-text {\r\n  font-weight: 500;\r\n}\r\n\r\n.desc-text {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 债务人单元格可点击样式 */\r\n.debtor-cell.clickable {\r\n  cursor: pointer;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n  position: relative;\r\n}\r\n\r\n.debtor-cell.clickable:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.debtor-name {\r\n  font-weight: 500;\r\n  color: #409eff;\r\n}\r\n\r\n.arrow-icon {\r\n  font-size: 12px;\r\n  opacity: 0.6;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.debtor-cell.clickable:hover .arrow-icon {\r\n  opacity: 1;\r\n  transform: translateX(2px);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.process-dialog .el-dialog {\r\n  border-radius: 12px;\r\n}\r\n\r\n.dialog-content {\r\n  padding: 0 8px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 32px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.section-title {\r\n  margin: 0 0 20px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.readonly-input,\r\n.readonly-textarea {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.status-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.status-radio {\r\n  padding: 12px 20px;\r\n  border: 2px solid #dcdfe6;\r\n  border-radius: 8px;\r\n  background-color: white;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.status-radio:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.status-radio.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.completion-section {\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.file-input {\r\n  flex: 1;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-textarea {\r\n  margin-top: 16px;\r\n}\r\n\r\n.dialog-footer {\r\n  padding: 20px 24px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fafafa;\r\n  text-align: right;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  padding: 10px 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .lawyer-letter-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 债务人详情右侧滑出面板 */\r\n.debtor-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.debtor-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 800px;\r\n  height: 100%;\r\n  background-color: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-width: 90vw;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n/* 面板头部 */\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.header-info h3.panel-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-subtitle {\r\n  margin: 4px 0 0 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.close-btn {\r\n  color: white !important;\r\n  font-size: 18px;\r\n}\r\n\r\n.close-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1) !important;\r\n}\r\n\r\n/* 面板主体 */\r\n.panel-body {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧菜单 */\r\n.sidebar-menu {\r\n  width: 200px;\r\n  background-color: #f8f9fa;\r\n  border-right: 1px solid #ebeef5;\r\n  padding: 16px 0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.menu-item.active {\r\n  background-color: #409eff;\r\n  color: white;\r\n  position: relative;\r\n}\r\n\r\n.menu-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 3px;\r\n  height: 20px;\r\n  background-color: white;\r\n}\r\n\r\n.menu-item i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  min-width: 0;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 信息区块 */\r\n.info-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.info-section .section-title {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n  gap: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  min-height: 60px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 100px;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n  padding-top: 2px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n  flex: 1;\r\n  word-break: break-all;\r\n  line-height: 1.5;\r\n}\r\n\r\n.debt-amount {\r\n  color: #f56c6c !important;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 20px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.user-avatar {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #409eff, #36a3f7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.user-info h5 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  color: #303133;\r\n}\r\n\r\n.user-info p {\r\n  margin: 4px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件列表 */\r\n.file-list {\r\n  space-y: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px;\r\n  background-color: #fafbfc;\r\n  border-radius: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.file-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 6px;\r\n  background-color: #409eff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-info h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-info p {\r\n  margin: 2px 0;\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.file-size {\r\n  color: #409eff !important;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.empty-files,\r\n.empty-history {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-files i,\r\n.empty-history i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n/* 历史时间线 */\r\n.history-timeline {\r\n  position: relative;\r\n  padding-left: 20px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.timeline-item:not(:last-child)::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -15px;\r\n  top: 20px;\r\n  width: 2px;\r\n  height: calc(100% - 10px);\r\n  background-color: #e4e7ed;\r\n}\r\n\r\n.timeline-dot {\r\n  position: absolute;\r\n  left: -20px;\r\n  top: 5px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #409eff;\r\n  border: 2px solid white;\r\n  box-shadow: 0 0 0 2px #409eff;\r\n}\r\n\r\n.timeline-content {\r\n  background-color: #fafbfc;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.timeline-content h6 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.timeline-content p {\r\n  margin: 0 0 8px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .panel-content {\r\n    width: 700px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n    max-width: 100vw;\r\n  }\r\n\r\n  .panel-body {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    width: 100%;\r\n    display: flex;\r\n    overflow-x: auto;\r\n    padding: 8px 0;\r\n  }\r\n\r\n  .menu-item {\r\n    white-space: nowrap;\r\n    min-width: 120px;\r\n    justify-content: center;\r\n  }\r\n\r\n  .content-area {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: 80px;\r\n  }\r\n}\r\n\r\n/* AI生成功能样式 */\r\n.ai-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.ai-section .section-title {\r\n  color: white;\r\n  border-bottom-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.ai-generation-content {\r\n  padding: 16px 0;\r\n}\r\n\r\n.ai-description {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.ai-description p {\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.ai-actions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-generate-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  font-weight: 600;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ai-generate-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.ai-result {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.result-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: white;\r\n}\r\n\r\n.generated-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ai-content-textarea {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 6px;\r\n}\r\n\r\n.ai-content-textarea .el-textarea__inner {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: #333;\r\n  font-family: 'Microsoft YaHei', sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.ai-result-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.ai-result-actions .el-button {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.ai-result-actions .el-button:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 处理方式选择样式 */\r\n.process-method-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.method-radio-group {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.method-radio {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border: 2px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  background: #fff;\r\n}\r\n\r\n.method-radio:hover {\r\n  border-color: #409eff;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.method-radio.is-checked {\r\n  border-color: #409eff;\r\n  background: #e6f7ff;\r\n  color: #409eff;\r\n}\r\n\r\n.method-radio i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* AI处理区域样式 */\r\n.ai-process-section {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-mode-alert {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-content-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.ai-preview-textarea .el-textarea__inner {\r\n  background: #fff;\r\n  border: 1px solid #dcdfe6;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.ai-file-generation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.generate-file-btn {\r\n  border-radius: 6px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.generated-file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f0f9ff;\r\n  border: 1px solid #b3d8ff;\r\n  border-radius: 6px;\r\n  color: #409eff;\r\n}\r\n\r\n.generated-file-info i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 手动上传区域样式 */\r\n.upload-process-section {\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./lawyer.vue?vue&type=template&id=4a81e5cf&scoped=true\"\nimport script from \"./lawyer.vue?vue&type=script&lang=js\"\nexport * from \"./lawyer.vue?vue&type=script&lang=js\"\nimport style0 from \"./lawyer.vue?vue&type=style&index=0&id=4a81e5cf&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a81e5cf\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}