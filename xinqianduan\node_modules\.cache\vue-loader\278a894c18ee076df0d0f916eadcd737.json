{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\changePwd.vue?vue&type=style&index=0&id=38f72e90&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\changePwd.vue", "mtime": 1748484434485}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changePwd.vue"], "names": [], "mappings": ";AAgOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "changePwd.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\n  <div class=\"page-wrapper\">\n    <div class=\"page-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-lock\"></i>\n            修改密码\n          </h2>\n          <div class=\"page-subtitle\">为了您的账户安全，请定期更换密码</div>\n        </div>\n        <el-button \n          type=\"text\" \n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          class=\"back-btn\"\n        >\n          返回\n        </el-button>\n      </div>\n\n      <!-- 修改密码表单 -->\n      <div class=\"form-section\">\n        <div class=\"form-card\">\n          <div class=\"security-tips\">\n            <div class=\"tips-header\">\n              <i class=\"el-icon-warning\"></i>\n              <span>密码安全提示</span>\n            </div>\n            <ul class=\"tips-list\">\n              <li>密码长度至少8位，包含字母、数字</li>\n              <li>不要使用过于简单的密码</li>\n              <li>建议定期更换密码</li>\n              <li>不要在多个平台使用相同密码</li>\n            </ul>\n          </div>\n\n          <el-form \n            :model=\"passwordForm\" \n            :rules=\"rules\" \n            ref=\"passwordForm\" \n            label-width=\"120px\"\n            class=\"password-form\"\n          >\n            <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n              <el-input \n                v-model=\"passwordForm.oldPassword\" \n                type=\"password\"\n                placeholder=\"请输入当前密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"新密码\" prop=\"newPassword\">\n              <el-input \n                v-model=\"passwordForm.newPassword\" \n                type=\"password\"\n                placeholder=\"请输入新密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"确认新密码\" prop=\"confirmPassword\">\n              <el-input \n                v-model=\"passwordForm.confirmPassword\" \n                type=\"password\"\n                placeholder=\"请再次输入新密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <!-- 密码强度指示器 -->\n            <div class=\"password-strength\" v-if=\"passwordForm.newPassword\">\n              <div class=\"strength-label\">密码强度：</div>\n              <div class=\"strength-bar\">\n                <div \n                  class=\"strength-fill\" \n                  :class=\"passwordStrengthClass\"\n                  :style=\"{ width: passwordStrengthWidth }\"\n                ></div>\n              </div>\n              <div class=\"strength-text\" :class=\"passwordStrengthClass\">\n                {{ passwordStrengthText }}\n              </div>\n            </div>\n\n            <!-- 操作按钮 -->\n            <div class=\"action-buttons\">\n              <el-button \n                type=\"primary\" \n                @click=\"changePassword\"\n                :loading=\"loading\"\n                size=\"medium\"\n              >\n                确认修改\n              </el-button>\n              <el-button \n                @click=\"resetForm\"\n                size=\"medium\"\n              >\n                重置\n              </el-button>\n            </div>\n          </el-form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"ChangePwd\",\n  data() {\n    // 确认密码验证\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入新密码'));\n      } else if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入密码不一致'));\n      } else {\n        callback();\n      }\n    };\n\n    return {\n      loading: false,\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      rules: {\n        oldPassword: [\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\n        ],\n        newPassword: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 8, message: '密码长度至少8位', trigger: 'blur' },\n          { \n            pattern: /^(?=.*[a-zA-Z])(?=.*\\d).+$/, \n            message: '密码必须包含字母和数字', \n            trigger: 'blur' \n          }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      }\n    };\n  },\n  computed: {\n    // 密码强度计算\n    passwordStrength() {\n      const password = this.passwordForm.newPassword;\n      if (!password) return 0;\n      \n      let strength = 0;\n      \n      // 长度检查\n      if (password.length >= 8) strength += 1;\n      if (password.length >= 12) strength += 1;\n      \n      // 字符类型检查\n      if (/[a-z]/.test(password)) strength += 1;\n      if (/[A-Z]/.test(password)) strength += 1;\n      if (/\\d/.test(password)) strength += 1;\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) strength += 1;\n      \n      return Math.min(strength, 4);\n    },\n    passwordStrengthWidth() {\n      return (this.passwordStrength / 4) * 100 + '%';\n    },\n    passwordStrengthClass() {\n      const classes = ['weak', 'fair', 'good', 'strong'];\n      return classes[Math.max(0, this.passwordStrength - 1)] || 'weak';\n    },\n    passwordStrengthText() {\n      const texts = ['弱', '一般', '良好', '强'];\n      return texts[Math.max(0, this.passwordStrength - 1)] || '弱';\n    }\n  },\n  methods: {\n    changePassword() {\n      this.$refs.passwordForm.validate((valid) => {\n        if (valid) {\n          this.loading = true;\n          \n          // 模拟密码修改过程\n          setTimeout(() => {\n            this.loading = false;\n            this.$message.success('密码修改成功！');\n            this.resetForm();\n            \n            // 可以选择跳转回个人信息页面或者首页\n            setTimeout(() => {\n              this.$router.push('/profile');\n            }, 1500);\n          }, 1000);\n        }\n      });\n    },\n    resetForm() {\n      this.$refs.passwordForm.resetFields();\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      };\n    },\n    goBack() {\n      this.$router.go(-1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 页面布局样式 */\n.page-wrapper {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 16px;\n}\n\n.page-container {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n/* 页面头部 */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24px;\n  padding: 24px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.page-title i {\n  color: #1890ff;\n  font-size: 22px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #8c8c8c;\n  margin: 0;\n}\n\n.back-btn {\n  color: #1890ff;\n}\n\n.back-btn:hover {\n  color: #40a9ff;\n}\n\n/* 表单区域 */\n.form-section {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.form-card {\n  padding: 32px;\n}\n\n/* 安全提示 */\n.security-tips {\n  background: #f6ffed;\n  border: 1px solid #b7eb8f;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 32px;\n}\n\n.tips-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #52c41a;\n  margin-bottom: 12px;\n}\n\n.tips-list {\n  margin: 0;\n  padding-left: 20px;\n  color: #52c41a;\n}\n\n.tips-list li {\n  margin-bottom: 4px;\n  font-size: 14px;\n}\n\n/* 表单样式 */\n.password-form {\n  max-width: 500px;\n}\n\n/* 密码强度指示器 */\n.password-strength {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 24px;\n  padding: 0 0 0 120px;\n}\n\n.strength-label {\n  font-size: 14px;\n  color: #666;\n  white-space: nowrap;\n}\n\n.strength-bar {\n  flex: 1;\n  height: 6px;\n  background: #f0f0f0;\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.strength-fill {\n  height: 100%;\n  transition: all 0.3s;\n  border-radius: 3px;\n}\n\n.strength-fill.weak {\n  background: #ff4d4f;\n}\n\n.strength-fill.fair {\n  background: #faad14;\n}\n\n.strength-fill.good {\n  background: #1890ff;\n}\n\n.strength-fill.strong {\n  background: #52c41a;\n}\n\n.strength-text {\n  font-size: 12px;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.strength-text.weak {\n  color: #ff4d4f;\n}\n\n.strength-text.fair {\n  color: #faad14;\n}\n\n.strength-text.good {\n  color: #1890ff;\n}\n\n.strength-text.strong {\n  color: #52c41a;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  gap: 12px;\n}\n\n/* 表单样式优化 */\n.password-form ::v-deep .el-form-item__label {\n  color: #262626;\n  font-weight: 500;\n}\n\n.password-form ::v-deep .el-input__inner {\n  border-radius: 6px;\n}\n\n.password-form ::v-deep .el-input__inner:focus {\n  border-color: #1890ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n  \n  .form-card {\n    padding: 24px 16px;\n  }\n  \n  .password-strength {\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0;\n    gap: 8px;\n  }\n  \n  .strength-bar {\n    width: 100%;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n  }\n}\n</style>\n"]}]}