{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue?vue&type=template&id=204d0746&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\type.vue", "mtime": 1748617691749}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}