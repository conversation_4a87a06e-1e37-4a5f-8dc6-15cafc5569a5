{"version": 3, "names": ["_t", "require", "isClassDeclaration", "isExportDefaultSpecifier", "isExportNamespaceSpecifier", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isStatement", "ImportSpecifier", "node", "importKind", "word", "space", "print", "imported", "local", "name", "ImportDefaultSpecifier", "ExportDefaultSpecifier", "exported", "ExportSpecifier", "exportKind", "ExportNamespaceSpecifier", "token", "warningShown", "_printAttributes", "importAttributesKeyword", "format", "attributes", "assertions", "console", "warn", "useAssertKeyword", "printList", "ExportAllDeclaration", "_node$attributes", "_node$assertions", "length", "source", "semicolon", "maybePrintDecoratorsBeforeExport", "printer", "declaration", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "ExportNamedDeclaration", "declar", "specifiers", "slice", "hasSpecial", "first", "shift", "_node$attributes2", "_node$assertions2", "ExportDefaultDeclaration", "noIndentInnerCommentsHere", "ImportDeclaration", "_node$attributes3", "_node$assertions3", "isTypeKind", "module", "phase", "hasSpecifiers", "ImportAttribute", "key", "value", "ImportNamespaceSpecifier", "ImportExpression", "options"], "sources": ["../../src/generators/modules.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isClassDeclaration,\n  isExportDefaultSpecifier,\n  isExportNamespaceSpecifier,\n  isImportDefaultSpecifier,\n  isImportNamespaceSpecifier,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport function ImportSpecifier(this: Printer, node: t.ImportSpecifier) {\n  if (node.importKind === \"type\" || node.importKind === \"typeof\") {\n    this.word(node.importKind);\n    this.space();\n  }\n\n  this.print(node.imported, node);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.local && node.local.name !== node.imported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.local, node);\n  }\n}\n\nexport function ImportDefaultSpecifier(\n  this: Printer,\n  node: t.ImportDefaultSpecifier,\n) {\n  this.print(node.local, node);\n}\n\nexport function ExportDefaultSpecifier(\n  this: Printer,\n  node: t.ExportDefaultSpecifier,\n) {\n  this.print(node.exported, node);\n}\n\nexport function ExportSpecifier(this: Printer, node: t.ExportSpecifier) {\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n\n  this.print(node.local, node);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.exported && node.local.name !== node.exported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.exported, node);\n  }\n}\n\nexport function ExportNamespaceSpecifier(\n  this: Printer,\n  node: t.ExportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.exported, node);\n}\n\nlet warningShown = false;\n\nexport function _printAttributes(\n  this: Printer,\n  node: Extract<t.Node, { attributes?: t.ImportAttribute[] }>,\n) {\n  const { importAttributesKeyword } = this.format;\n  const { attributes, assertions } = node;\n\n  if (\n    attributes &&\n    !importAttributesKeyword &&\n    // In the production build only show the warning once.\n    // We want to show it per-usage locally for tests.\n    (!process.env.IS_PUBLISH || !warningShown)\n  ) {\n    warningShown = true;\n    console.warn(`\\\nYou are using import attributes, without specifying the desired output syntax.\nPlease specify the \"importAttributesKeyword\" generator option, whose value can be one of:\n - \"with\"        : \\`import { a } from \"b\" with { type: \"json\" };\\`\n - \"assert\"      : \\`import { a } from \"b\" assert { type: \"json\" };\\`\n - \"with-legacy\" : \\`import { a } from \"b\" with type: \"json\";\\`\n`);\n  }\n\n  const useAssertKeyword =\n    importAttributesKeyword === \"assert\" ||\n    (!importAttributesKeyword && assertions);\n\n  this.word(useAssertKeyword ? \"assert\" : \"with\");\n  this.space();\n\n  if (!useAssertKeyword && importAttributesKeyword !== \"with\") {\n    // with-legacy\n    this.printList(attributes || assertions, node);\n    return;\n  }\n\n  this.token(\"{\");\n  this.space();\n  this.printList(attributes || assertions, node);\n  this.space();\n  this.token(\"}\");\n}\n\nexport function ExportAllDeclaration(\n  this: Printer,\n  node: t.ExportAllDeclaration | t.DeclareExportAllDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n  this.token(\"*\");\n  this.space();\n  this.word(\"from\");\n  this.space();\n  // @ts-expect-error Fixme: attributes is not defined in DeclareExportAllDeclaration\n  if (node.attributes?.length || node.assertions?.length) {\n    this.print(node.source, node, true);\n    this.space();\n    // @ts-expect-error Fixme: attributes is not defined in DeclareExportAllDeclaration\n    this._printAttributes(node);\n  } else {\n    this.print(node.source, node);\n  }\n\n  this.semicolon();\n}\n\nfunction maybePrintDecoratorsBeforeExport(\n  printer: Printer,\n  node: t.ExportNamedDeclaration | t.ExportDefaultDeclaration,\n) {\n  if (\n    isClassDeclaration(node.declaration) &&\n    printer._shouldPrintDecoratorsBeforeExport(\n      node as t.ExportNamedDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    printer.printJoin(node.declaration.decorators, node);\n  }\n}\n\nexport function ExportNamedDeclaration(\n  this: Printer,\n  node: t.ExportNamedDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.space();\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar, node);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    if (node.exportKind === \"type\") {\n      this.word(\"type\");\n      this.space();\n    }\n\n    const specifiers = node.specifiers.slice(0);\n\n    // print \"special\" specifiers first\n    let hasSpecial = false;\n    for (;;) {\n      const first = specifiers[0];\n      if (\n        isExportDefaultSpecifier(first) ||\n        isExportNamespaceSpecifier(first)\n      ) {\n        hasSpecial = true;\n        this.print(specifiers.shift(), node);\n        if (specifiers.length) {\n          this.token(\",\");\n          this.space();\n        }\n      } else {\n        break;\n      }\n    }\n\n    if (specifiers.length || (!specifiers.length && !hasSpecial)) {\n      this.token(\"{\");\n      if (specifiers.length) {\n        this.space();\n        this.printList(specifiers, node);\n        this.space();\n      }\n      this.token(\"}\");\n    }\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      if (node.attributes?.length || node.assertions?.length) {\n        this.print(node.source, node, true);\n        this.space();\n        this._printAttributes(node);\n      } else {\n        this.print(node.source, node);\n      }\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExportDefaultDeclaration(\n  this: Printer,\n  node: t.ExportDefaultDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.noIndentInnerCommentsHere();\n  this.space();\n  this.word(\"default\");\n  this.space();\n  const declar = node.declaration;\n  this.print(declar, node);\n  if (!isStatement(declar)) this.semicolon();\n}\n\nexport function ImportDeclaration(this: Printer, node: t.ImportDeclaration) {\n  this.word(\"import\");\n  this.space();\n\n  const isTypeKind = node.importKind === \"type\" || node.importKind === \"typeof\";\n  if (isTypeKind) {\n    this.noIndentInnerCommentsHere();\n    this.word(node.importKind);\n    this.space();\n  } else if (node.module) {\n    this.noIndentInnerCommentsHere();\n    this.word(\"module\");\n    this.space();\n  } else if (node.phase) {\n    this.noIndentInnerCommentsHere();\n    this.word(node.phase);\n    this.space();\n  }\n\n  const specifiers = node.specifiers.slice(0);\n  const hasSpecifiers = !!specifiers.length;\n  // print \"special\" specifiers first. The loop condition is constant,\n  // but there is a \"break\" in the body.\n  while (hasSpecifiers) {\n    const first = specifiers[0];\n    if (isImportDefaultSpecifier(first) || isImportNamespaceSpecifier(first)) {\n      this.print(specifiers.shift(), node);\n      if (specifiers.length) {\n        this.token(\",\");\n        this.space();\n      }\n    } else {\n      break;\n    }\n  }\n\n  if (specifiers.length) {\n    this.token(\"{\");\n    this.space();\n    this.printList(specifiers, node);\n    this.space();\n    this.token(\"}\");\n  } else if (isTypeKind && !hasSpecifiers) {\n    this.token(\"{\");\n    this.token(\"}\");\n  }\n\n  if (hasSpecifiers || isTypeKind) {\n    this.space();\n    this.word(\"from\");\n    this.space();\n  }\n\n  if (node.attributes?.length || node.assertions?.length) {\n    this.print(node.source, node, true);\n    this.space();\n    this._printAttributes(node);\n  } else {\n    this.print(node.source, node);\n  }\n\n  this.semicolon();\n}\n\nexport function ImportAttribute(this: Printer, node: t.ImportAttribute) {\n  this.print(node.key);\n  this.token(\":\");\n  this.space();\n  this.print(node.value);\n}\n\nexport function ImportNamespaceSpecifier(\n  this: Printer,\n  node: t.ImportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.local, node);\n}\n\nexport function ImportExpression(this: Printer, node: t.ImportExpression) {\n  this.word(\"import\");\n  if (node.phase) {\n    this.token(\".\");\n    this.word(node.phase);\n  }\n  this.token(\"(\");\n  this.print(node.source, node);\n  if (node.options != null) {\n    this.token(\",\");\n    this.space();\n    this.print(node.options, node);\n  }\n  this.token(\")\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAOsB;EANpBC,kBAAkB;EAClBC,wBAAwB;EACxBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,0BAA0B;EAC1BC;AAAW,IAAAP,EAAA;AAIN,SAASQ,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC9D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,QAAQ,EAAEL,IAAI,CAAC;EAE/B,IAAIA,IAAI,CAACM,KAAK,IAAIN,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACK,QAAQ,CAACE,IAAI,EAAE;IACxD,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;EAC9B;AACF;AAEO,SAASQ,sBAAsBA,CAEpCR,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAASS,sBAAsBA,CAEpCT,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;AACjC;AAEO,SAASW,eAAeA,CAAgBX,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;EAE5B,IAAIA,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACU,QAAQ,CAACH,IAAI,EAAE;IAC3D,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;EACjC;AACF;AAEO,SAASa,wBAAwBA,CAEtCb,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;AACjC;AAEA,IAAIe,YAAY,GAAG,KAAK;AAEjB,SAASC,gBAAgBA,CAE9BhB,IAA2D,EAC3D;EACA,MAAM;IAAEiB;EAAwB,CAAC,GAAG,IAAI,CAACC,MAAM;EAC/C,MAAM;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGpB,IAAI;EAEvC,IACEmB,UAAU,IACV,CAACF,uBAAuB,IAGI,CAACF,YAAY,EACzC;IACAA,YAAY,GAAG,IAAI;IACnBM,OAAO,CAACC,IAAI,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;EACA;EAEA,MAAMC,gBAAgB,GACpBN,uBAAuB,KAAK,QAAQ,IACnC,CAACA,uBAAuB,IAAIG,UAAW;EAE1C,IAAI,CAAClB,IAAI,CAACqB,gBAAgB,GAAG,QAAQ,GAAG,MAAM,CAAC;EAC/C,IAAI,CAACpB,KAAK,CAAC,CAAC;EAEZ,IAAI,CAACoB,gBAAgB,IAAIN,uBAAuB,KAAK,MAAM,EAAE;IAE3D,IAAI,CAACO,SAAS,CAACL,UAAU,IAAIC,UAAU,EAAEpB,IAAI,CAAC;IAC9C;EACF;EAEA,IAAI,CAACc,SAAK,IAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACqB,SAAS,CAACL,UAAU,IAAIC,UAAU,EAAEpB,IAAI,CAAC;EAC9C,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACW,SAAK,IAAI,CAAC;AACjB;AAEO,SAASW,oBAAoBA,CAElCzB,IAA4D,EAC5D;EAAA,IAAA0B,gBAAA,EAAAC,gBAAA;EACA,IAAI,CAACzB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACW,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,IAAI,CAAAuB,gBAAA,GAAA1B,IAAI,CAACmB,UAAU,aAAfO,gBAAA,CAAiBE,MAAM,KAAAD,gBAAA,GAAI3B,IAAI,CAACoB,UAAU,aAAfO,gBAAA,CAAiBC,MAAM,EAAE;IACtD,IAAI,CAACxB,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,EAAE,IAAI,CAAC;IACnC,IAAI,CAACG,KAAK,CAAC,CAAC;IAEZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,CAAC;EAC7B,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAAC;EAC/B;EAEA,IAAI,CAAC8B,SAAS,CAAC,CAAC;AAClB;AAEA,SAASC,gCAAgCA,CACvCC,OAAgB,EAChBhC,IAA2D,EAC3D;EACA,IACEP,kBAAkB,CAACO,IAAI,CAACiC,WAAW,CAAC,IACpCD,OAAO,CAACE,kCAAkC,CACxClC,IACF,CAAC,EACD;IACAgC,OAAO,CAACG,SAAS,CAACnC,IAAI,CAACiC,WAAW,CAACG,UAAU,EAAEpC,IAAI,CAAC;EACtD;AACF;AAEO,SAASqC,sBAAsBA,CAEpCrC,IAA8B,EAC9B;EACA+B,gCAAgC,CAAC,IAAI,EAAE/B,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACiC,WAAW,EAAE;IACpB,MAAMK,MAAM,GAAGtC,IAAI,CAACiC,WAAW;IAC/B,IAAI,CAAC7B,KAAK,CAACkC,MAAM,EAAEtC,IAAI,CAAC;IACxB,IAAI,CAACF,WAAW,CAACwC,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC,CAAC;EAC5C,CAAC,MAAM;IACL,IAAI9B,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;MAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACd;IAEA,MAAMoC,UAAU,GAAGvC,IAAI,CAACuC,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;IAG3C,IAAIC,UAAU,GAAG,KAAK;IACtB,SAAS;MACP,MAAMC,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;MAC3B,IACE7C,wBAAwB,CAACgD,KAAK,CAAC,IAC/B/C,0BAA0B,CAAC+C,KAAK,CAAC,EACjC;QACAD,UAAU,GAAG,IAAI;QACjB,IAAI,CAACrC,KAAK,CAACmC,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE3C,IAAI,CAAC;QACpC,IAAIuC,UAAU,CAACX,MAAM,EAAE;UACrB,IAAI,CAACd,SAAK,GAAI,CAAC;UACf,IAAI,CAACX,KAAK,CAAC,CAAC;QACd;MACF,CAAC,MAAM;QACL;MACF;IACF;IAEA,IAAIoC,UAAU,CAACX,MAAM,IAAK,CAACW,UAAU,CAACX,MAAM,IAAI,CAACa,UAAW,EAAE;MAC5D,IAAI,CAAC3B,SAAK,IAAI,CAAC;MACf,IAAIyB,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAACzB,KAAK,CAAC,CAAC;QACZ,IAAI,CAACqB,SAAS,CAACe,UAAU,EAAEvC,IAAI,CAAC;QAChC,IAAI,CAACG,KAAK,CAAC,CAAC;MACd;MACA,IAAI,CAACW,SAAK,IAAI,CAAC;IACjB;IAEA,IAAId,IAAI,CAAC6B,MAAM,EAAE;MAAA,IAAAe,iBAAA,EAAAC,iBAAA;MACf,IAAI,CAAC1C,KAAK,CAAC,CAAC;MACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ,IAAI,CAAAyC,iBAAA,GAAA5C,IAAI,CAACmB,UAAU,aAAfyB,iBAAA,CAAiBhB,MAAM,KAAAiB,iBAAA,GAAI7C,IAAI,CAACoB,UAAU,aAAfyB,iBAAA,CAAiBjB,MAAM,EAAE;QACtD,IAAI,CAACxB,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,EAAE,IAAI,CAAC;QACnC,IAAI,CAACG,KAAK,CAAC,CAAC;QACZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAAC8B,SAAS,CAAC,CAAC;EAClB;AACF;AAEO,SAASgB,wBAAwBA,CAEtC9C,IAAgC,EAChC;EACA+B,gCAAgC,CAAC,IAAI,EAAE/B,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAAC6C,yBAAyB,CAAC,CAAC;EAChC,IAAI,CAAC5C,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,MAAMmC,MAAM,GAAGtC,IAAI,CAACiC,WAAW;EAC/B,IAAI,CAAC7B,KAAK,CAACkC,MAAM,EAAEtC,IAAI,CAAC;EACxB,IAAI,CAACF,WAAW,CAACwC,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC,CAAC;AAC5C;AAEO,SAASkB,iBAAiBA,CAAgBhD,IAAyB,EAAE;EAAA,IAAAiD,iBAAA,EAAAC,iBAAA;EAC1E,IAAI,CAAChD,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,MAAMgD,UAAU,GAAGnD,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ;EAC7E,IAAIkD,UAAU,EAAE;IACd,IAAI,CAACJ,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC7C,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,CAAC,CAAC;EACd,CAAC,MAAM,IAAIH,IAAI,CAACoD,MAAM,EAAE;IACtB,IAAI,CAACL,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC7C,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd,CAAC,MAAM,IAAIH,IAAI,CAACqD,KAAK,EAAE;IACrB,IAAI,CAACN,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC7C,IAAI,CAACF,IAAI,CAACqD,KAAK,CAAC;IACrB,IAAI,CAAClD,KAAK,CAAC,CAAC;EACd;EAEA,MAAMoC,UAAU,GAAGvC,IAAI,CAACuC,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C,MAAMc,aAAa,GAAG,CAAC,CAACf,UAAU,CAACX,MAAM;EAGzC,OAAO0B,aAAa,EAAE;IACpB,MAAMZ,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAI3C,wBAAwB,CAAC8C,KAAK,CAAC,IAAI7C,0BAA0B,CAAC6C,KAAK,CAAC,EAAE;MACxE,IAAI,CAACtC,KAAK,CAACmC,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE3C,IAAI,CAAC;MACpC,IAAIuC,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAACd,SAAK,GAAI,CAAC;QACf,IAAI,CAACX,KAAK,CAAC,CAAC;MACd;IACF,CAAC,MAAM;MACL;IACF;EACF;EAEA,IAAIoC,UAAU,CAACX,MAAM,EAAE;IACrB,IAAI,CAACd,SAAK,IAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACqB,SAAS,CAACe,UAAU,EAAEvC,IAAI,CAAC;IAChC,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM,IAAIqC,UAAU,IAAI,CAACG,aAAa,EAAE;IACvC,IAAI,CAACxC,SAAK,IAAI,CAAC;IACf,IAAI,CAACA,SAAK,IAAI,CAAC;EACjB;EAEA,IAAIwC,aAAa,IAAIH,UAAU,EAAE;IAC/B,IAAI,CAAChD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAAA8C,iBAAA,GAAAjD,IAAI,CAACmB,UAAU,aAAf8B,iBAAA,CAAiBrB,MAAM,KAAAsB,iBAAA,GAAIlD,IAAI,CAACoB,UAAU,aAAf8B,iBAAA,CAAiBtB,MAAM,EAAE;IACtD,IAAI,CAACxB,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,EAAE,IAAI,CAAC;IACnC,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,CAAC;EAC7B,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAAC;EAC/B;EAEA,IAAI,CAAC8B,SAAS,CAAC,CAAC;AAClB;AAEO,SAASyB,eAAeA,CAAgBvD,IAAuB,EAAE;EACtE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACwD,GAAG,CAAC;EACpB,IAAI,CAAC1C,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACyD,KAAK,CAAC;AACxB;AAEO,SAASC,wBAAwBA,CAEtC1D,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAAS2D,gBAAgBA,CAAgB3D,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAIF,IAAI,CAACqD,KAAK,EAAE;IACd,IAAI,CAACvC,SAAK,GAAI,CAAC;IACf,IAAI,CAACZ,IAAI,CAACF,IAAI,CAACqD,KAAK,CAAC;EACvB;EACA,IAAI,CAACvC,SAAK,GAAI,CAAC;EACf,IAAI,CAACV,KAAK,CAACJ,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAAC;EAC7B,IAAIA,IAAI,CAAC4D,OAAO,IAAI,IAAI,EAAE;IACxB,IAAI,CAAC9C,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC4D,OAAO,EAAE5D,IAAI,CAAC;EAChC;EACA,IAAI,CAACc,SAAK,GAAI,CAAC;AACjB", "ignoreList": []}