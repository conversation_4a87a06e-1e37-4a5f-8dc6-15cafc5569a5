"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var syntaxJsx=_interopDefault(require("@babel/plugin-syntax-jsx"));const autoImportGetCurrentInstance=(e,t,n)=>{const i=t.get("body").filter(e=>e.isImportDeclaration()).map(e=>e.node);const r=i.filter(e=>e.source.value===n);const o=r.some(t=>t.specifiers.some(t=>e.isImportSpecifier(t)&&"getCurrentInstance"===t.local.name));if(!o){const i=e.importSpecifier(e.identifier("getCurrentInstance"),e.identifier("getCurrentInstance"));r.length>0?r[0].specifiers.push(i):t.unshiftContainer("body",e.importDeclaration([i],e.stringLiteral(n)))}},injectInstanceId="__currentInstance";var index=({types:t},{importSource:importSource="@vue/composition-api"}={})=>({inherits:syntaxJsx,visitor:{Program(e){e.traverse({"ObjectMethod|ObjectProperty"(n){if("setup"===n.node.key.name){let i=!1;n.traverse({JSXAttribute(r){const o=r.get("name");["v-on","on-input","on-change","model"].includes(o.node.name)&&r.traverse({MemberExpression(r){const o=r.get("object"),s=r.get("property");t.isThisExpression(o)&&t.isIdentifier(s)&&["$","_"].includes(s.node.name[0])&&(autoImportGetCurrentInstance(t,e,importSource),i||(n.node.value.body.body.unshift(t.variableDeclaration("const",[t.variableDeclarator(t.identifier(injectInstanceId),t.callExpression(t.identifier("getCurrentInstance"),[]))])),i=!0),o.replaceWith(t.identifier(injectInstanceId)))}})}})}}})}}});module.exports=index;
