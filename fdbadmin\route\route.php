<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\Route;

Route::get('think', function () {
    return 'hello,ThinkPHP5!';
});

Route::get('hello/:name', 'index/hello');

// Admin模块路由
Route::group('admin', function () {
    // Dashboard路由
    Route::get('dashboard/getStats', 'admin/Dashboard/getStats');
    Route::get('dashboard/getActivities', 'admin/Dashboard/getActivities');
    Route::get('dashboard/getTodos', 'admin/Dashboard/getTodos');
    Route::get('dashboard/getQuickActions', 'admin/Dashboard/getQuickActions');
    Route::get('dashboard/getChartData', 'admin/Dashboard/getChartData');
    Route::post('dashboard/updateTodo', 'admin/Dashboard/updateTodo');
});

return [

];
