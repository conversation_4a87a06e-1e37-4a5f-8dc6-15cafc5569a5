{"version": 3, "sources": ["webpack:///./src/views/pages/changePwd.vue", "webpack:///src/views/pages/changePwd.vue", "webpack:///./src/views/pages/changePwd.vue?469d", "webpack:///./src/views/pages/changePwd.vue?60a9", "webpack:///./node_modules/core-js/internals/function-apply.js", "webpack:///./src/views/pages/changePwd.vue?2878", "webpack:///./node_modules/core-js/internals/error-stack-install.js", "webpack:///./node_modules/core-js/internals/install-error-cause.js", "webpack:///./node_modules/core-js/internals/proxy-accessor.js", "webpack:///./node_modules/core-js/internals/error-stack-installable.js", "webpack:///./node_modules/core-js/modules/es.error.cause.js", "webpack:///./node_modules/core-js/internals/wrap-error-constructor-with-cause.js"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "goBack", "_v", "ref", "passwordForm", "rules", "model", "value", "oldPassword", "callback", "$$v", "$set", "expression", "newPassword", "confirmPassword", "class", "passwordStrengthClass", "style", "width", "passwordStrengthWidth", "_s", "passwordStrengthText", "_e", "loading", "changePassword", "resetForm", "staticRenderFns", "name", "data", "validateConfirmPassword", "rule", "Error", "required", "message", "trigger", "min", "pattern", "validator", "computed", "passwordStrength", "password", "strength", "length", "test", "Math", "classes", "max", "texts", "methods", "$refs", "validate", "valid", "setTimeout", "$message", "success", "$router", "push", "resetFields", "go", "component", "NATIVE_BIND", "FunctionPrototype", "Function", "prototype", "apply", "call", "module", "exports", "Reflect", "bind", "arguments", "createNonEnumerableProperty", "clearErrorStack", "ERROR_STACK_INSTALLABLE", "captureStackTrace", "error", "C", "stack", "dropEntries", "isObject", "O", "options", "cause", "defineProperty", "f", "Target", "Source", "key", "configurable", "get", "set", "it", "fails", "createPropertyDescriptor", "Object", "$", "global", "wrapErrorConstructorWithCause", "WEB_ASSEMBLY", "WebAssembly", "FORCED", "exportGlobalErrorCauseWrapper", "ERROR_NAME", "wrapper", "constructor", "arity", "forced", "exportWebAssemblyErrorCauseWrapper", "target", "stat", "init", "getBuiltIn", "hasOwn", "isPrototypeOf", "setPrototypeOf", "copyConstructorProperties", "proxyAccessor", "inheritIfRequired", "normalizeStringArgument", "installErrorCause", "installErrorStack", "DESCRIPTORS", "IS_PURE", "FULL_NAME", "IS_AGGREGATE_ERROR", "STACK_TRACE_LIMIT", "OPTIONS_POSITION", "path", "split", "OriginalError", "OriginalErrorPrototype", "BaseError", "WrappedError", "a", "b", "undefined", "result"], "mappings": "yHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIK,GAAG,GAAGH,EAAG,YAAY,CAACE,YAAY,WAAWE,MAAM,CAAC,KAAO,OAAO,KAAO,gBAAgBC,GAAG,CAAC,MAAQP,EAAIQ,SAAS,CAACR,EAAIS,GAAG,WAAW,GAAGP,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,GAAGH,EAAG,UAAU,CAACQ,IAAI,eAAeN,YAAY,gBAAgBE,MAAM,CAAC,MAAQN,EAAIW,aAAa,MAAQX,EAAIY,MAAM,cAAc,UAAU,CAACV,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,gBAAgB,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,YAAc,UAAU,gBAAgB,GAAG,aAAe,OAAOO,MAAM,CAACC,MAAOd,EAAIW,aAAaI,YAAaC,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIW,aAAc,cAAeM,IAAME,WAAW,+BAA+B,GAAGjB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,MAAM,KAAO,gBAAgB,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,YAAc,SAAS,gBAAgB,GAAG,aAAe,OAAOO,MAAM,CAACC,MAAOd,EAAIW,aAAaS,YAAaJ,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIW,aAAc,cAAeM,IAAME,WAAW,+BAA+B,GAAGjB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,QAAQ,KAAO,oBAAoB,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,WAAW,YAAc,WAAW,gBAAgB,GAAG,aAAe,OAAOO,MAAM,CAACC,MAAOd,EAAIW,aAAaU,gBAAiBL,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIW,aAAc,kBAAmBM,IAAME,WAAW,mCAAmC,GAAInB,EAAIW,aAAaS,YAAalB,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIS,GAAG,WAAWP,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBkB,MAAMtB,EAAIuB,sBAAsBC,MAAO,CAAEC,MAAOzB,EAAI0B,2BAA6BxB,EAAG,MAAM,CAACE,YAAY,gBAAgBkB,MAAMtB,EAAIuB,uBAAuB,CAACvB,EAAIS,GAAG,IAAIT,EAAI2B,GAAG3B,EAAI4B,sBAAsB,SAAS5B,EAAI6B,KAAK3B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,QAAUN,EAAI8B,QAAQ,KAAO,UAAUvB,GAAG,CAAC,MAAQP,EAAI+B,iBAAiB,CAAC/B,EAAIS,GAAG,YAAYP,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQP,EAAIgC,YAAY,CAAChC,EAAIS,GAAG,WAAW,IAAI,IAAI,UAExsEwB,EAAkB,CAAC,WAAY,IAAIjC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIS,GAAG,YAAYP,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACJ,EAAIS,GAAG,yBACtP,WAAY,IAAIT,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,OAAO,CAACF,EAAIS,GAAG,cAAcP,EAAG,KAAK,CAACE,YAAY,aAAa,CAACF,EAAG,KAAK,CAACF,EAAIS,GAAG,sBAAsBP,EAAG,KAAK,CAACF,EAAIS,GAAG,iBAAiBP,EAAG,KAAK,CAACF,EAAIS,GAAG,cAAcP,EAAG,KAAK,CAACF,EAAIS,GAAG,yBCgHjV,G,oBAAA,CACfyB,KAAA,YACAC,OAEA,MAAAC,GAAAC,EAAAvB,EAAAE,KACA,KAAAF,EACAE,EAAA,IAAAsB,MAAA,aACAxB,IAAA,KAAAH,aAAAS,YACAJ,EAAA,IAAAsB,MAAA,cAEAtB,KAIA,OACAc,SAAA,EACAnB,aAAA,CACAI,YAAA,GACAK,YAAA,GACAC,gBAAA,IAEAT,MAAA,CACAG,YAAA,CACA,CAAAwB,UAAA,EAAAC,QAAA,UAAAC,QAAA,SAEArB,YAAA,CACA,CAAAmB,UAAA,EAAAC,QAAA,SAAAC,QAAA,QACA,CAAAC,IAAA,EAAAF,QAAA,WAAAC,QAAA,QACA,CACAE,QAAA,6BACAH,QAAA,cACAC,QAAA,SAGApB,gBAAA,CACA,CAAAkB,UAAA,EAAAC,QAAA,SAAAC,QAAA,QACA,CAAAG,UAAAR,EAAAK,QAAA,YAKAI,SAAA,CAEAC,mBACA,MAAAC,EAAA,KAAApC,aAAAS,YACA,IAAA2B,EAAA,SAEA,IAAAC,EAAA,EAYA,OATAD,EAAAE,QAAA,IAAAD,GAAA,GACAD,EAAAE,QAAA,KAAAD,GAAA,GAGA,QAAAE,KAAAH,KAAAC,GAAA,GACA,QAAAE,KAAAH,KAAAC,GAAA,GACA,KAAAE,KAAAH,KAAAC,GAAA,GACA,yBAAAE,KAAAH,KAAAC,GAAA,GAEAG,KAAAT,IAAAM,EAAA,IAEAtB,wBACA,YAAAoB,iBAAA,WAEAvB,wBACA,MAAA6B,EAAA,gCACA,OAAAA,EAAAD,KAAAE,IAAA,OAAAP,iBAAA,aAEAlB,uBACA,MAAA0B,EAAA,oBACA,OAAAA,EAAAH,KAAAE,IAAA,OAAAP,iBAAA,WAGAS,QAAA,CACAxB,iBACA,KAAAyB,MAAA7C,aAAA8C,SAAAC,IACAA,IACA,KAAA5B,SAAA,EAGA6B,WAAA,KACA,KAAA7B,SAAA,EACA,KAAA8B,SAAAC,QAAA,WACA,KAAA7B,YAGA2B,WAAA,KACA,KAAAG,QAAAC,KAAA,aACA,OACA,SAIA/B,YACA,KAAAwB,MAAA7C,aAAAqD,cACA,KAAArD,aAAA,CACAI,YAAA,GACAK,YAAA,GACAC,gBAAA,KAGAb,SACA,KAAAsD,QAAAG,IAAA,OCzNiW,I,wBCQ7VC,EAAY,eACd,EACAnE,EACAkC,GACA,EACA,KACA,WACA,MAIa,aAAAiC,E,6CClBf,IAAIC,EAAc,EAAQ,QAEtBC,EAAoBC,SAASC,UAC7BC,EAAQH,EAAkBG,MAC1BC,EAAOJ,EAAkBI,KAG7BC,EAAOC,QAA4B,iBAAXC,SAAuBA,QAAQJ,QAAUJ,EAAcK,EAAKI,KAAKL,GAAS,WAChG,OAAOC,EAAKD,MAAMA,EAAOM,c,oCCT3B,W,oCCCA,IAAIC,EAA8B,EAAQ,QACtCC,EAAkB,EAAQ,QAC1BC,EAA0B,EAAQ,QAGlCC,EAAoB3C,MAAM2C,kBAE9BR,EAAOC,QAAU,SAAUQ,EAAOC,EAAGC,EAAOC,GACtCL,IACEC,EAAmBA,EAAkBC,EAAOC,GAC3CL,EAA4BI,EAAO,QAASH,EAAgBK,EAAOC,O,kCCV5E,IAAIC,EAAW,EAAQ,QACnBR,EAA8B,EAAQ,QAI1CL,EAAOC,QAAU,SAAUa,EAAGC,GACxBF,EAASE,IAAY,UAAWA,GAClCV,EAA4BS,EAAG,QAASC,EAAQC,S,kCCPpD,IAAIC,EAAiB,EAAQ,QAAuCC,EAEpElB,EAAOC,QAAU,SAAUkB,EAAQC,EAAQC,GACzCA,KAAOF,GAAUF,EAAeE,EAAQE,EAAK,CAC3CC,cAAc,EACdC,IAAK,WAAc,OAAOH,EAAOC,IACjCG,IAAK,SAAUC,GAAML,EAAOC,GAAOI,O,kCCNvC,IAAIC,EAAQ,EAAQ,QAChBC,EAA2B,EAAQ,QAEvC3B,EAAOC,SAAWyB,GAAM,WACtB,IAAIjB,EAAQ,IAAI5C,MAAM,KACtB,QAAM,UAAW4C,KAEjBmB,OAAOX,eAAeR,EAAO,QAASkB,EAAyB,EAAG,IAC3C,IAAhBlB,EAAME,W,yDCPf,IAAIkB,EAAI,EAAQ,QACZC,EAAS,EAAQ,QACjBhC,EAAQ,EAAQ,QAChBiC,EAAgC,EAAQ,QAExCC,EAAe,cACfC,EAAcH,EAAOE,GAGrBE,EAAgD,IAAvC,IAAIrE,MAAM,IAAK,CAAEmD,MAAO,IAAKA,MAEtCmB,EAAgC,SAAUC,EAAYC,GACxD,IAAIvB,EAAI,GACRA,EAAEsB,GAAcL,EAA8BK,EAAYC,EAASH,GACnEL,EAAE,CAAEC,QAAQ,EAAMQ,aAAa,EAAMC,MAAO,EAAGC,OAAQN,GAAUpB,IAG/D2B,EAAqC,SAAUL,EAAYC,GAC7D,GAAIJ,GAAeA,EAAYG,GAAa,CAC1C,IAAItB,EAAI,GACRA,EAAEsB,GAAcL,EAA8BC,EAAe,IAAMI,EAAYC,EAASH,GACxFL,EAAE,CAAEa,OAAQV,EAAcW,MAAM,EAAML,aAAa,EAAMC,MAAO,EAAGC,OAAQN,GAAUpB,KAKzFqB,EAA8B,SAAS,SAAUS,GAC/C,OAAO,SAAe7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAE5D+B,EAA8B,aAAa,SAAUS,GACnD,OAAO,SAAmB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAEhE+B,EAA8B,cAAc,SAAUS,GACpD,OAAO,SAAoB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAEjE+B,EAA8B,kBAAkB,SAAUS,GACxD,OAAO,SAAwB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAErE+B,EAA8B,eAAe,SAAUS,GACrD,OAAO,SAAqB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAElE+B,EAA8B,aAAa,SAAUS,GACnD,OAAO,SAAmB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAEhE+B,EAA8B,YAAY,SAAUS,GAClD,OAAO,SAAkB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAE/DqC,EAAmC,gBAAgB,SAAUG,GAC3D,OAAO,SAAsB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAEnEqC,EAAmC,aAAa,SAAUG,GACxD,OAAO,SAAmB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,eAEhEqC,EAAmC,gBAAgB,SAAUG,GAC3D,OAAO,SAAsB7E,GAAW,OAAO+B,EAAM8C,EAAMpH,KAAM4E,gB,kCCvDnE,IAAIyC,EAAa,EAAQ,QACrBC,EAAS,EAAQ,QACjBzC,EAA8B,EAAQ,QACtC0C,EAAgB,EAAQ,QACxBC,EAAiB,EAAQ,QACzBC,EAA4B,EAAQ,QACpCC,EAAgB,EAAQ,QACxBC,EAAoB,EAAQ,QAC5BC,EAA0B,EAAQ,QAClCC,EAAoB,EAAQ,QAC5BC,EAAoB,EAAQ,QAC5BC,EAAc,EAAQ,QACtBC,EAAU,EAAQ,QAEtBxD,EAAOC,QAAU,SAAUwD,EAAWpB,EAASH,EAAQwB,GACrD,IAAIC,EAAoB,kBACpBC,EAAmBF,EAAqB,EAAI,EAC5CG,EAAOJ,EAAUK,MAAM,KACvB1B,EAAayB,EAAKA,EAAKrF,OAAS,GAChCuF,EAAgBlB,EAAW/C,MAAM,KAAM+D,GAE3C,GAAKE,EAAL,CAEA,IAAIC,EAAyBD,EAAclE,UAK3C,IAFK2D,GAAWV,EAAOkB,EAAwB,iBAAiBA,EAAuBhD,OAElFkB,EAAQ,OAAO6B,EAEpB,IAAIE,EAAYpB,EAAW,SAEvBqB,EAAe7B,GAAQ,SAAU8B,EAAGC,GACtC,IAAIrG,EAAUqF,EAAwBM,EAAqBU,EAAID,OAAGE,GAC9DC,EAASZ,EAAqB,IAAIK,EAAcI,GAAK,IAAIJ,EAK7D,YAJgBM,IAAZtG,GAAuBsC,EAA4BiE,EAAQ,UAAWvG,GAC1EuF,EAAkBgB,EAAQJ,EAAcI,EAAO3D,MAAO,GAClDnF,MAAQuH,EAAciB,EAAwBxI,OAAO2H,EAAkBmB,EAAQ9I,KAAM0I,GACrF9D,UAAU5B,OAASoF,GAAkBP,EAAkBiB,EAAQlE,UAAUwD,IACtEU,KAeT,GAZAJ,EAAarE,UAAYmE,EAEN,UAAf5B,EACEY,EAAgBA,EAAekB,EAAcD,GAC5ChB,EAA0BiB,EAAcD,EAAW,CAAExG,MAAM,IACvD8F,GAAeI,KAAqBI,IAC7Cb,EAAcgB,EAAcH,EAAeJ,GAC3CT,EAAcgB,EAAcH,EAAe,sBAG7Cd,EAA0BiB,EAAcH,IAEnCP,EAAS,IAERQ,EAAuBvG,OAAS2E,GAClC/B,EAA4B2D,EAAwB,OAAQ5B,GAE9D4B,EAAuB1B,YAAc4B,EACrC,MAAOzD,IAET,OAAOyD", "file": "js/chunk-100ed178.f0e33fac.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"page-wrapper\"},[_c('div',{staticClass:\"page-container\"},[_c('div',{staticClass:\"page-header\"},[_vm._m(0),_c('el-button',{staticClass:\"back-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-back\"},on:{\"click\":_vm.goBack}},[_vm._v(\" 返回 \")])],1),_c('div',{staticClass:\"form-section\"},[_c('div',{staticClass:\"form-card\"},[_vm._m(1),_c('el-form',{ref:\"passwordForm\",staticClass:\"password-form\",attrs:{\"model\":_vm.passwordForm,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('el-form-item',{attrs:{\"label\":\"当前密码\",\"prop\":\"oldPassword\"}},[_c('el-input',{attrs:{\"type\":\"password\",\"placeholder\":\"请输入当前密码\",\"show-password\":\"\",\"autocomplete\":\"off\"},model:{value:(_vm.passwordForm.oldPassword),callback:function ($$v) {_vm.$set(_vm.passwordForm, \"oldPassword\", $$v)},expression:\"passwordForm.oldPassword\"}})],1),_c('el-form-item',{attrs:{\"label\":\"新密码\",\"prop\":\"newPassword\"}},[_c('el-input',{attrs:{\"type\":\"password\",\"placeholder\":\"请输入新密码\",\"show-password\":\"\",\"autocomplete\":\"off\"},model:{value:(_vm.passwordForm.newPassword),callback:function ($$v) {_vm.$set(_vm.passwordForm, \"newPassword\", $$v)},expression:\"passwordForm.newPassword\"}})],1),_c('el-form-item',{attrs:{\"label\":\"确认新密码\",\"prop\":\"confirmPassword\"}},[_c('el-input',{attrs:{\"type\":\"password\",\"placeholder\":\"请再次输入新密码\",\"show-password\":\"\",\"autocomplete\":\"off\"},model:{value:(_vm.passwordForm.confirmPassword),callback:function ($$v) {_vm.$set(_vm.passwordForm, \"confirmPassword\", $$v)},expression:\"passwordForm.confirmPassword\"}})],1),(_vm.passwordForm.newPassword)?_c('div',{staticClass:\"password-strength\"},[_c('div',{staticClass:\"strength-label\"},[_vm._v(\"密码强度：\")]),_c('div',{staticClass:\"strength-bar\"},[_c('div',{staticClass:\"strength-fill\",class:_vm.passwordStrengthClass,style:({ width: _vm.passwordStrengthWidth })})]),_c('div',{staticClass:\"strength-text\",class:_vm.passwordStrengthClass},[_vm._v(\" \"+_vm._s(_vm.passwordStrengthText)+\" \")])]):_vm._e(),_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.loading,\"size\":\"medium\"},on:{\"click\":_vm.changePassword}},[_vm._v(\" 确认修改 \")]),_c('el-button',{attrs:{\"size\":\"medium\"},on:{\"click\":_vm.resetForm}},[_vm._v(\" 重置 \")])],1)],1)],1)])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-lock\"}),_vm._v(\" 修改密码 \")]),_c('div',{staticClass:\"page-subtitle\"},[_vm._v(\"为了您的账户安全，请定期更换密码\")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"security-tips\"},[_c('div',{staticClass:\"tips-header\"},[_c('i',{staticClass:\"el-icon-warning\"}),_c('span',[_vm._v(\"密码安全提示\")])]),_c('ul',{staticClass:\"tips-list\"},[_c('li',[_vm._v(\"密码长度至少8位，包含字母、数字\")]),_c('li',[_vm._v(\"不要使用过于简单的密码\")]),_c('li',[_vm._v(\"建议定期更换密码\")]),_c('li',[_vm._v(\"不要在多个平台使用相同密码\")])])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"page-wrapper\">\n    <div class=\"page-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-lock\"></i>\n            修改密码\n          </h2>\n          <div class=\"page-subtitle\">为了您的账户安全，请定期更换密码</div>\n        </div>\n        <el-button \n          type=\"text\" \n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          class=\"back-btn\"\n        >\n          返回\n        </el-button>\n      </div>\n\n      <!-- 修改密码表单 -->\n      <div class=\"form-section\">\n        <div class=\"form-card\">\n          <div class=\"security-tips\">\n            <div class=\"tips-header\">\n              <i class=\"el-icon-warning\"></i>\n              <span>密码安全提示</span>\n            </div>\n            <ul class=\"tips-list\">\n              <li>密码长度至少8位，包含字母、数字</li>\n              <li>不要使用过于简单的密码</li>\n              <li>建议定期更换密码</li>\n              <li>不要在多个平台使用相同密码</li>\n            </ul>\n          </div>\n\n          <el-form \n            :model=\"passwordForm\" \n            :rules=\"rules\" \n            ref=\"passwordForm\" \n            label-width=\"120px\"\n            class=\"password-form\"\n          >\n            <el-form-item label=\"当前密码\" prop=\"oldPassword\">\n              <el-input \n                v-model=\"passwordForm.oldPassword\" \n                type=\"password\"\n                placeholder=\"请输入当前密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"新密码\" prop=\"newPassword\">\n              <el-input \n                v-model=\"passwordForm.newPassword\" \n                type=\"password\"\n                placeholder=\"请输入新密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <el-form-item label=\"确认新密码\" prop=\"confirmPassword\">\n              <el-input \n                v-model=\"passwordForm.confirmPassword\" \n                type=\"password\"\n                placeholder=\"请再次输入新密码\"\n                show-password\n                autocomplete=\"off\"\n              ></el-input>\n            </el-form-item>\n\n            <!-- 密码强度指示器 -->\n            <div class=\"password-strength\" v-if=\"passwordForm.newPassword\">\n              <div class=\"strength-label\">密码强度：</div>\n              <div class=\"strength-bar\">\n                <div \n                  class=\"strength-fill\" \n                  :class=\"passwordStrengthClass\"\n                  :style=\"{ width: passwordStrengthWidth }\"\n                ></div>\n              </div>\n              <div class=\"strength-text\" :class=\"passwordStrengthClass\">\n                {{ passwordStrengthText }}\n              </div>\n            </div>\n\n            <!-- 操作按钮 -->\n            <div class=\"action-buttons\">\n              <el-button \n                type=\"primary\" \n                @click=\"changePassword\"\n                :loading=\"loading\"\n                size=\"medium\"\n              >\n                确认修改\n              </el-button>\n              <el-button \n                @click=\"resetForm\"\n                size=\"medium\"\n              >\n                重置\n              </el-button>\n            </div>\n          </el-form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"ChangePwd\",\n  data() {\n    // 确认密码验证\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入新密码'));\n      } else if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入密码不一致'));\n      } else {\n        callback();\n      }\n    };\n\n    return {\n      loading: false,\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      rules: {\n        oldPassword: [\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\n        ],\n        newPassword: [\n          { required: true, message: '请输入新密码', trigger: 'blur' },\n          { min: 8, message: '密码长度至少8位', trigger: 'blur' },\n          { \n            pattern: /^(?=.*[a-zA-Z])(?=.*\\d).+$/, \n            message: '密码必须包含字母和数字', \n            trigger: 'blur' \n          }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认新密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      }\n    };\n  },\n  computed: {\n    // 密码强度计算\n    passwordStrength() {\n      const password = this.passwordForm.newPassword;\n      if (!password) return 0;\n      \n      let strength = 0;\n      \n      // 长度检查\n      if (password.length >= 8) strength += 1;\n      if (password.length >= 12) strength += 1;\n      \n      // 字符类型检查\n      if (/[a-z]/.test(password)) strength += 1;\n      if (/[A-Z]/.test(password)) strength += 1;\n      if (/\\d/.test(password)) strength += 1;\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) strength += 1;\n      \n      return Math.min(strength, 4);\n    },\n    passwordStrengthWidth() {\n      return (this.passwordStrength / 4) * 100 + '%';\n    },\n    passwordStrengthClass() {\n      const classes = ['weak', 'fair', 'good', 'strong'];\n      return classes[Math.max(0, this.passwordStrength - 1)] || 'weak';\n    },\n    passwordStrengthText() {\n      const texts = ['弱', '一般', '良好', '强'];\n      return texts[Math.max(0, this.passwordStrength - 1)] || '弱';\n    }\n  },\n  methods: {\n    changePassword() {\n      this.$refs.passwordForm.validate((valid) => {\n        if (valid) {\n          this.loading = true;\n          \n          // 模拟密码修改过程\n          setTimeout(() => {\n            this.loading = false;\n            this.$message.success('密码修改成功！');\n            this.resetForm();\n            \n            // 可以选择跳转回个人信息页面或者首页\n            setTimeout(() => {\n              this.$router.push('/profile');\n            }, 1500);\n          }, 1000);\n        }\n      });\n    },\n    resetForm() {\n      this.$refs.passwordForm.resetFields();\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      };\n    },\n    goBack() {\n      this.$router.go(-1);\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 页面布局样式 */\n.page-wrapper {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 16px;\n}\n\n.page-container {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n/* 页面头部 */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24px;\n  padding: 24px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.page-title i {\n  color: #1890ff;\n  font-size: 22px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #8c8c8c;\n  margin: 0;\n}\n\n.back-btn {\n  color: #1890ff;\n}\n\n.back-btn:hover {\n  color: #40a9ff;\n}\n\n/* 表单区域 */\n.form-section {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.form-card {\n  padding: 32px;\n}\n\n/* 安全提示 */\n.security-tips {\n  background: #f6ffed;\n  border: 1px solid #b7eb8f;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 32px;\n}\n\n.tips-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #52c41a;\n  margin-bottom: 12px;\n}\n\n.tips-list {\n  margin: 0;\n  padding-left: 20px;\n  color: #52c41a;\n}\n\n.tips-list li {\n  margin-bottom: 4px;\n  font-size: 14px;\n}\n\n/* 表单样式 */\n.password-form {\n  max-width: 500px;\n}\n\n/* 密码强度指示器 */\n.password-strength {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 24px;\n  padding: 0 0 0 120px;\n}\n\n.strength-label {\n  font-size: 14px;\n  color: #666;\n  white-space: nowrap;\n}\n\n.strength-bar {\n  flex: 1;\n  height: 6px;\n  background: #f0f0f0;\n  border-radius: 3px;\n  overflow: hidden;\n}\n\n.strength-fill {\n  height: 100%;\n  transition: all 0.3s;\n  border-radius: 3px;\n}\n\n.strength-fill.weak {\n  background: #ff4d4f;\n}\n\n.strength-fill.fair {\n  background: #faad14;\n}\n\n.strength-fill.good {\n  background: #1890ff;\n}\n\n.strength-fill.strong {\n  background: #52c41a;\n}\n\n.strength-text {\n  font-size: 12px;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.strength-text.weak {\n  color: #ff4d4f;\n}\n\n.strength-text.fair {\n  color: #faad14;\n}\n\n.strength-text.good {\n  color: #1890ff;\n}\n\n.strength-text.strong {\n  color: #52c41a;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  gap: 12px;\n}\n\n/* 表单样式优化 */\n.password-form ::v-deep .el-form-item__label {\n  color: #262626;\n  font-weight: 500;\n}\n\n.password-form ::v-deep .el-input__inner {\n  border-radius: 6px;\n}\n\n.password-form ::v-deep .el-input__inner:focus {\n  border-color: #1890ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n  \n  .form-card {\n    padding: 24px 16px;\n  }\n  \n  .password-strength {\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0;\n    gap: 8px;\n  }\n  \n  .strength-bar {\n    width: 100%;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./changePwd.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./changePwd.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./changePwd.vue?vue&type=template&id=a6c71daa&scoped=true\"\nimport script from \"./changePwd.vue?vue&type=script&lang=js\"\nexport * from \"./changePwd.vue?vue&type=script&lang=js\"\nimport style0 from \"./changePwd.vue?vue&type=style&index=0&id=a6c71daa&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a6c71daa\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\r\nvar NATIVE_BIND = require('../internals/function-bind-native');\r\n\r\nvar FunctionPrototype = Function.prototype;\r\nvar apply = FunctionPrototype.apply;\r\nvar call = FunctionPrototype.call;\r\n\r\n// eslint-disable-next-line es/no-reflect -- safe\r\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\r\n  return call.apply(apply, arguments);\r\n});\r\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./changePwd.vue?vue&type=style&index=0&id=a6c71daa&prod&scoped=true&lang=css\"", "'use strict';\r\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\r\nvar clearErrorStack = require('../internals/error-stack-clear');\r\nvar ERROR_STACK_INSTALLABLE = require('../internals/error-stack-installable');\r\n\r\n// non-standard V8\r\nvar captureStackTrace = Error.captureStackTrace;\r\n\r\nmodule.exports = function (error, C, stack, dropEntries) {\r\n  if (ERROR_STACK_INSTALLABLE) {\r\n    if (captureStackTrace) captureStackTrace(error, C);\r\n    else createNonEnumerableProperty(error, 'stack', clearErrorStack(stack, dropEntries));\r\n  }\r\n};\r\n", "'use strict';\r\nvar isObject = require('../internals/is-object');\r\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\r\n\r\n// `InstallErrorCause` abstract operation\r\n// https://tc39.es/proposal-error-cause/#sec-errorobjects-install-error-cause\r\nmodule.exports = function (O, options) {\r\n  if (isObject(options) && 'cause' in options) {\r\n    createNonEnumerableProperty(O, 'cause', options.cause);\r\n  }\r\n};\r\n", "'use strict';\r\nvar defineProperty = require('../internals/object-define-property').f;\r\n\r\nmodule.exports = function (Target, Source, key) {\r\n  key in Target || defineProperty(Target, key, {\r\n    configurable: true,\r\n    get: function () { return Source[key]; },\r\n    set: function (it) { Source[key] = it; }\r\n  });\r\n};\r\n", "'use strict';\r\nvar fails = require('../internals/fails');\r\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\r\n\r\nmodule.exports = !fails(function () {\r\n  var error = new Error('a');\r\n  if (!('stack' in error)) return true;\r\n  // eslint-disable-next-line es/no-object-defineproperty -- safe\r\n  Object.defineProperty(error, 'stack', createPropertyDescriptor(1, 7));\r\n  return error.stack !== 7;\r\n});\r\n", "'use strict';\r\n/* eslint-disable no-unused-vars -- required for functions `.length` */\r\nvar $ = require('../internals/export');\r\nvar global = require('../internals/global');\r\nvar apply = require('../internals/function-apply');\r\nvar wrapErrorConstructorWithCause = require('../internals/wrap-error-constructor-with-cause');\r\n\r\nvar WEB_ASSEMBLY = 'WebAssembly';\r\nvar WebAssembly = global[WEB_ASSEMBLY];\r\n\r\n// eslint-disable-next-line es/no-error-cause -- feature detection\r\nvar FORCED = new Error('e', { cause: 7 }).cause !== 7;\r\n\r\nvar exportGlobalErrorCauseWrapper = function (ERROR_NAME, wrapper) {\r\n  var O = {};\r\n  O[ERROR_NAME] = wrapErrorConstructorWithCause(ERROR_NAME, wrapper, FORCED);\r\n  $({ global: true, constructor: true, arity: 1, forced: FORCED }, O);\r\n};\r\n\r\nvar exportWebAssemblyErrorCauseWrapper = function (ERROR_NAME, wrapper) {\r\n  if (WebAssembly && WebAssembly[ERROR_NAME]) {\r\n    var O = {};\r\n    O[ERROR_NAME] = wrapErrorConstructorWithCause(WEB_ASSEMBLY + '.' + ERROR_NAME, wrapper, FORCED);\r\n    $({ target: WEB_ASSEMBLY, stat: true, constructor: true, arity: 1, forced: FORCED }, O);\r\n  }\r\n};\r\n\r\n// https://tc39.es/ecma262/#sec-nativeerror\r\nexportGlobalErrorCauseWrapper('Error', function (init) {\r\n  return function Error(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('EvalError', function (init) {\r\n  return function EvalError(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('RangeError', function (init) {\r\n  return function RangeError(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('ReferenceError', function (init) {\r\n  return function ReferenceError(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('SyntaxError', function (init) {\r\n  return function SyntaxError(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('TypeError', function (init) {\r\n  return function TypeError(message) { return apply(init, this, arguments); };\r\n});\r\nexportGlobalErrorCauseWrapper('URIError', function (init) {\r\n  return function URIError(message) { return apply(init, this, arguments); };\r\n});\r\nexportWebAssemblyErrorCauseWrapper('CompileError', function (init) {\r\n  return function CompileError(message) { return apply(init, this, arguments); };\r\n});\r\nexportWebAssemblyErrorCauseWrapper('LinkError', function (init) {\r\n  return function LinkError(message) { return apply(init, this, arguments); };\r\n});\r\nexportWebAssemblyErrorCauseWrapper('RuntimeError', function (init) {\r\n  return function RuntimeError(message) { return apply(init, this, arguments); };\r\n});\r\n", "'use strict';\r\nvar getBuiltIn = require('../internals/get-built-in');\r\nvar hasOwn = require('../internals/has-own-property');\r\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\r\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\r\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\r\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\r\nvar proxyAccessor = require('../internals/proxy-accessor');\r\nvar inheritIfRequired = require('../internals/inherit-if-required');\r\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\r\nvar installErrorCause = require('../internals/install-error-cause');\r\nvar installErrorStack = require('../internals/error-stack-install');\r\nvar DESCRIPTORS = require('../internals/descriptors');\r\nvar IS_PURE = require('../internals/is-pure');\r\n\r\nmodule.exports = function (FULL_NAME, wrapper, FORCED, IS_AGGREGATE_ERROR) {\r\n  var STACK_TRACE_LIMIT = 'stackTraceLimit';\r\n  var OPTIONS_POSITION = IS_AGGREGATE_ERROR ? 2 : 1;\r\n  var path = FULL_NAME.split('.');\r\n  var ERROR_NAME = path[path.length - 1];\r\n  var OriginalError = getBuiltIn.apply(null, path);\r\n\r\n  if (!OriginalError) return;\r\n\r\n  var OriginalErrorPrototype = OriginalError.prototype;\r\n\r\n  // V8 9.3- bug https://bugs.chromium.org/p/v8/issues/detail?id=12006\r\n  if (!IS_PURE && hasOwn(OriginalErrorPrototype, 'cause')) delete OriginalErrorPrototype.cause;\r\n\r\n  if (!FORCED) return OriginalError;\r\n\r\n  var BaseError = getBuiltIn('Error');\r\n\r\n  var WrappedError = wrapper(function (a, b) {\r\n    var message = normalizeStringArgument(IS_AGGREGATE_ERROR ? b : a, undefined);\r\n    var result = IS_AGGREGATE_ERROR ? new OriginalError(a) : new OriginalError();\r\n    if (message !== undefined) createNonEnumerableProperty(result, 'message', message);\r\n    installErrorStack(result, WrappedError, result.stack, 2);\r\n    if (this && isPrototypeOf(OriginalErrorPrototype, this)) inheritIfRequired(result, this, WrappedError);\r\n    if (arguments.length > OPTIONS_POSITION) installErrorCause(result, arguments[OPTIONS_POSITION]);\r\n    return result;\r\n  });\r\n\r\n  WrappedError.prototype = OriginalErrorPrototype;\r\n\r\n  if (ERROR_NAME !== 'Error') {\r\n    if (setPrototypeOf) setPrototypeOf(WrappedError, BaseError);\r\n    else copyConstructorProperties(WrappedError, BaseError, { name: true });\r\n  } else if (DESCRIPTORS && STACK_TRACE_LIMIT in OriginalError) {\r\n    proxyAccessor(WrappedError, OriginalError, STACK_TRACE_LIMIT);\r\n    proxyAccessor(WrappedError, OriginalError, 'prepareStackTrace');\r\n  }\r\n\r\n  copyConstructorProperties(WrappedError, OriginalError);\r\n\r\n  if (!IS_PURE) try {\r\n    // Safari 13- bug: WebAssembly errors does not have a proper `.name`\r\n    if (OriginalErrorPrototype.name !== ERROR_NAME) {\r\n      createNonEnumerableProperty(OriginalErrorPrototype, 'name', ERROR_NAME);\r\n    }\r\n    OriginalErrorPrototype.constructor = WrappedError;\r\n  } catch (error) { /* empty */ }\r\n\r\n  return WrappedError;\r\n};\r\n"], "sourceRoot": ""}