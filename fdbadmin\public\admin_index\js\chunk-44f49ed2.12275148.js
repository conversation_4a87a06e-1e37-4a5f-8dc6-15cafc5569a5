(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44f49ed2"],{"4c8c":function(e,t,s){},5384:function(e,t,s){"use strict";s("4c8c")},a578:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{attrs:{shadow:"always"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refulsh}},[e._v("刷新")])],1),t("el-row",[t("el-col",{attrs:{span:4}},[t("el-input",{attrs:{placeholder:"请输入订单号/购买人/套餐/手机号",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-col",{attrs:{span:3}},[t("el-input",{attrs:{placeholder:"请输入业务员姓名",size:e.allSize},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),t("el-col",{attrs:{span:8}},[t("el-date-picker",{attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":"支付开始日期","end-placeholder":"支付结束日期",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},model:{value:e.search.refund_time,callback:function(t){e.$set(e.search,"refund_time",t)},expression:"search.refund_time"}})],1),t("el-col",{attrs:{span:1}},[t("el-button",{attrs:{size:e.allSize},on:{click:function(t){return e.getData()}}},[e._v("搜索")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,size:"mini"}},[t("el-table-column",{attrs:{prop:"title",label:"客户信息"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-row",[e._v("公司名称:"),t("el-tag",{attrs:{size:"small"}},[e._v(e._s(null==s.row.client?"":s.row.client.company))])],1),t("el-row",[e._v("联系人:"+e._s(null==s.row.client?"":s.row.client.linkman))]),e._v(" 用户内容 "),t("el-row",[e._v("联系方式:"),t("el-tag",{attrs:{size:"small"}},[e._v(e._s(null==s.row.client?"":s.row.client.phone))])],1)]}}])}),t("el-table-column",{attrs:{prop:"title",label:"套餐内容"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-row",[e._v("套餐名称:"+e._s(s.row.taocan?s.row.taocan.title:""))]),t("el-row",[e._v("套餐价格:"+e._s(s.row.taocan?s.row.taocan.price:"")+"元")]),t("el-row",[e._v("套餐年份:"),t("el-tag",{attrs:{size:"small"}},[e._v(e._s(s.row.taocan?s.row.taocan.year:"")+"年")])],1)]}}])}),t("el-table-column",{attrs:{prop:"title",label:"支付情况"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-row",[e._v("支付类型:"+e._s(1==s.row.pay_type?"全款":"分期/"+s.row.qishu+"期"))]),t("el-row",[e._v("已付款:"+e._s(s.row.pay_age)+"元")]),t("el-row",[e._v("剩余尾款:"+e._s(1==s.row.pay_type?0:s.row.total_price-s.row.pay_age)+"元")])]}}])}),t("el-table-column",{attrs:{label:"审核状态"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",{on:{click:function(t){return e.showStatus(s.row)}}},[1==s.row.status?t("el-row",[t("span",{staticStyle:{cursor:"pointer",color:"#409EFF"}},[e._v("未审核")])]):3==s.row.status?t("el-row",[t("span",{staticStyle:{cursor:"pointer",color:"#F56C6C"}},[e._v("审核未通过")])]):2==s.row.status?t("el-row",[t("span",{staticStyle:{cursor:"pointer",color:"#67C23A"}},[e._v("已通过")])]):e._e(),3==s.row.status?t("el-row",[t("span",[e._v("原因:"+e._s(s.row.status_msg))])]):e._e()],1)]}}])}),t("el-table-column",{attrs:{prop:"member.title",label:"业务员"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间"}}),t("el-table-column",{attrs:{prop:"end_time",label:"到期时间"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editData(s.row.id)}}},[e._v("查看")]),t("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(s.$index,s.row.id)}}},[e._v(" 移除 ")])]}}])})],1),t("div",{staticClass:"page-top"},[t("el-pagination",{attrs:{"page-sizes":[20,100,200,300,400],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:"订单信息",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[e.is_info?t("div",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(null==e.info.client?"":e.info.client.company))]),t("el-descriptions-item",{attrs:{label:"联系人"}},[null!=e.info.client?t("el-tag",{attrs:{size:"small"},on:{click:function(t){return e.viewUserData(e.info.client.id)}}},[e._v(e._s(null==e.info.client?"":e.info.client.linkman))]):e._e()],1),t("el-descriptions-item",{attrs:{label:"联系方式"}},[null!=e.info.client?t("el-tag",{attrs:{size:"small"},on:{click:function(t){return e.viewUserData(e.info.client.id)}}},[e._v(e._s(null==e.info.client?"":e.info.client.phone))]):e._e()],1),t("el-descriptions-item",{attrs:{label:"营业执照"}},[null!=e.info.client?t("el-tag",{attrs:{size:"small"},on:{click:function(t){return e.showImage(e.info.client.pic_path)}}},[e._v("查看 ")]):t("el-tag",{attrs:{size:"small"}},[e._v("暂无")])],1),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(null==e.info.client?"":e.info.client.tiaojie_id))]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(null==e.info.client?"":e.info.client.fawu_id))]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(null==e.info.client?"":e.info.client.lian_id))]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(null==e.info.client?"":e.info.client.htsczy_id))]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(null==e.info.client?"":e.info.client.ls_id))]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(null==e.info.client?"":e.info.client.ywy_id))])],1),t("el-descriptions",{attrs:{title:"债务人信息",colon:!1}},[t("el-descriptions-item",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.info.debts,size:"mini"}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名"}}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话"}}),t("el-table-column",{attrs:{prop:"money",label:"债务金额（元）"}}),t("el-table-column",{attrs:{prop:"status",label:"状态"}})],1)],1)],1),t("el-descriptions",{attrs:{title:"套餐内容"}},[t("el-descriptions-item",{attrs:{label:"套餐名称"}},[e._v(e._s(e.info.taocan.title))]),t("el-descriptions-item",{attrs:{label:"套餐价格"}},[e._v(e._s(e.info.taocan.price))]),t("el-descriptions-item",{attrs:{label:"套餐年份"}},[t("el-tag",{attrs:{size:"small"}},[e._v(e._s(e.info.taocan.year)+"年")])],1),t("el-descriptions-item",{attrs:{label:"到期时间"}},[t("el-date-picker",{attrs:{type:"datetime",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期",size:"mini"},model:{value:e.info.end_time,callback:function(t){e.$set(e.info,"end_time",t)},expression:"info.end_time"}}),t("el-tag",{staticStyle:{cursor:"pointer"},attrs:{size:"small"},on:{click:function(t){return e.updateEndTIme()}}},[e._v("修改")])],1)],1),t("el-descriptions",{attrs:{title:"套餐详情"}},e._l(e.info.taocan.num,(function(s,i){return t("el-descriptions-item",{key:i,attrs:{label:s.title}},[e._v(e._s(1==s.is_num?s.value+"次":"不限"))])})),1),t("el-descriptions",{attrs:{title:"款项信息"}},[t("el-descriptions-item",{attrs:{label:"付款类型"}},[e._v(e._s(1==e.info.pay_type?"全款":"分期/"+e.info.qishu+"期"))]),t("el-descriptions-item",{attrs:{label:"已付款"}},[t("el-tag",{attrs:{size:"small"}},[e._v(e._s(e.info.pay_age)+"元")])],1),t("el-descriptions-item",{attrs:{label:"剩余款"}},[t("el-tag",{attrs:{size:"small"}},[e._v(e._s(e.info.total_price-e.info.pay_age)+"元")])],1)],1),2==e.info.pay_type?t("el-descriptions",{attrs:{title:"期数"}}):e._e(),e._l(e.info.fenqi,(function(s,i){return 2==e.info.pay_type?t("el-descriptions",{key:i},[t("el-descriptions-item",{attrs:{label:"第"+(1*i+1)+"期"}},[e._v(" "+e._s(s.price)+" ")]),t("el-descriptions-item",{attrs:{label:"第"+(1*i+1)+"还款期"}},[e._v(" "+e._s(s.date)+" ")]),t("el-descriptions-item",{attrs:{label:"第"+(1*i+1)+"期凭证"}},[s.pay_path?t("el-tag",{attrs:{size:"small"},on:{click:function(t){return e.showImage(s.pay_path)}}},[e._v("查看")]):t("el-tag",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.changePinzhen(i)}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage",accept:".jpg, .jpeg, .png","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传凭证 ")])],1)],1)],1):e._e()})),t("el-descriptions",{attrs:{title:"备注信息"}},[t("el-descriptions-item",{attrs:{label:"具体内容"}},[e._v(e._s(e.info.desc))])],1)],2):e._e()]),t("el-dialog",{attrs:{title:"审核内容",visible:e.dialogStatus,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogStatus=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"审核","label-width":e.formLabelWidth}},[t("div",[t("el-radio",{attrs:{label:1,disabled:2==e.ruleForm.status},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[e._v("未审核 ")]),t("el-radio",{attrs:{label:2},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[e._v("审核通过")]),t("el-radio",{attrs:{label:3},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[e._v("审核不通过 ")])],1)]),3==e.ruleForm.status?t("el-form-item",{attrs:{label:"不通过原因","label-width":e.formLabelWidth,prop:"status_msg"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入内容"},model:{value:e.ruleForm.status_msg,callback:function(t){e.$set(e.ruleForm,"status_msg",t)},expression:"ruleForm.status_msg"}})],1):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogStatus=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.changeStatus()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"设置到期时间",visible:e.dialogEndTime,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogEndTime=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-date-picker",{attrs:{type:"date",format:"Y-m-d",placeholder:"选择日期"},model:{value:e.ruleForm.end_time,callback:function(t){e.$set(e.ruleForm,"end_time",t)},expression:"ruleForm.end_time"}})],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogEndTime=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.changeEndTime()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"60%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1),t("el-dialog",{attrs:{title:e.用户详情,visible:e.dialogViewUserDetail,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogViewUserDetail=t}}},[t("user-details",{attrs:{id:e.currentId}})],1)],1)},l=[],a=s("d522"),r={name:"list",components:{UserDetails:a["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,currentId:0,search:{keyword:""},loading:!0,url:"/Dingdan/",info:{},is_info:!1,dialogFormVisible:!1,dialogVisible:!1,dialogViewUserDetail:!1,dialogStatus:!1,dialogEndTime:!1,dialogAddOrder:!1,ruleForm:{status:1,status_msg:"",id:""},rules:{status_msg:[{required:!0,message:"请填写不通过原因",trigger:"blur"}],end_time:[{required:!0,message:"请填写时间",trigger:"blur"}]},formLabelWidth:"120px",show_image:"",index:0}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:""},t.dialogFormVisible=!0},viewUserData(e){let t=this;0!=e&&(this.currentId=e),t.dialogViewUserDetail=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.info=e.data,t.is_info=!0)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code?(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1)):_this.$message({type:"error",message:t.msg})})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},updateEndTIme(){this.$confirm("确认修改到期时间?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var e={id:this.info.id,end_time:this.info.end_time};this.postRequest(this.url+"updateEndTIme",e).then(e=>{200==e.code?this.$message({type:"success",message:"修改成功!"}):_this.$message({type:"error",message:e.msg})})}).catch(()=>{this.$message({type:"error",message:"取消修改!"})})},refulsh(){this.$router.go(0)},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index1?page="+e.page+"&size="+e.size,e.search).then(t=>{200==t.code&&(e.list=t.data,e.total=t.count),e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){let t=this;200==e.code&&(t.info.fenqi[t.index].pay_path=e.data.url,t.postRequest(t.url+"save",{id:t.info.id,fenqi:t.info.fenqi}).then(e=>{200==e.code?t.$message({type:"success",message:"上传成功"}):t.$message({type:"error",message:"上传失败"})}))},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let s=this;s.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(s.ruleForm[t]="",s.$message.success("删除成功!")):s.$message.error(e.msg)})},showImage(e){this.show_image=e,this.dialogVisible=!0},changePinzhen(e){this.index=e},showStatus(e){this.dialogStatus=!0,this.ruleForm=e},showEndTime(e){this.dialogEndTime=!0,this.ruleForm=e},changeEndTime(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;e.postRequest(e.url+"save",{id:e.ruleForm.id,end_time:e.ruleForm.end_time}).then(t=>{200==t.code?(e.$message({type:"success",message:"审核成功"}),e.dialogStatus=!1):e.$message({type:"error",message:t.msg})})})},changeStatus(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;e.postRequest(e.url+"changeStatus",{id:e.ruleForm.id,status:e.ruleForm.status,status_msg:e.ruleForm.status_msg}).then(t=>{200==t.code?(e.$message({type:"success",message:"审核成功"}),e.dialogStatus=!1):e.$message({type:"error",message:t.msg})})})}}},o=r,n=(s("5384"),s("2877")),c=Object(n["a"])(o,i,l,!1,null,"0bd6432c",null);t["default"]=c.exports},d522:function(e,t,s){"use strict";var i=function(){var e=this,t=e._self._c;return t("el-row",[t("el-descriptions",{attrs:{title:"客户信息"}},[t("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.info.company))]),t("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.info.phone))]),t("el-descriptions-item",{attrs:{label:"名称"}},[e._v(e._s(e.info.nickname))]),t("el-descriptions-item",{attrs:{label:"联系人"}},[e._v(e._s(e.info.linkman))]),t("el-descriptions-item",{attrs:{label:"头像"}},[""!=e.info.headimg&&null!=e.info.headimg?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.headimg},on:{click:function(t){return e.showImage(e.info.headimg)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"用户来源"}},[e._v(e._s(e.info.yuangong_id))]),t("el-descriptions-item",{attrs:{label:"联系方式"}},[e._v(e._s(e.info.linkphone))]),t("el-descriptions-item",{attrs:{label:"调解员"}},[e._v(e._s(e.info.tiaojie_name)+" ")]),t("el-descriptions-item",{attrs:{label:"法务专员"}},[e._v(e._s(e.info.fawu_name)+" ")]),t("el-descriptions-item",{attrs:{label:"立案专员"}},[e._v(e._s(e.info.lian_name)+" ")]),t("el-descriptions-item",{attrs:{label:"合同上传专用"}},[e._v(e._s(e.info.htsczy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"律师"}},[e._v(e._s(e.info.ls_name)+" ")]),t("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(e._s(e.info.ywy_name)+" ")]),t("el-descriptions-item",{attrs:{label:"营业执照"}},[""!=e.info.license&&null!=e.info.license?t("img",{staticStyle:{width:"50px",height:"50px"},attrs:{src:e.info.license},on:{click:function(t){return e.showImage(e.info.license)}}}):e._e()]),t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(e._s(e.info.start_time))]),t("el-descriptions-item",{attrs:{label:"会员年限"}},[e._v(e._s(e.info.year)+"年")])],1),t("el-descriptions",{attrs:{title:"债务人信息",colon:!1}},[t("el-descriptions-item",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.info.debts,size:"mini"}},[t("el-table-column",{attrs:{prop:"name",label:"债务人姓名"}}),t("el-table-column",{attrs:{prop:"tel",label:"债务人电话"}}),t("el-table-column",{attrs:{prop:"money",label:"债务金额（元）"}}),t("el-table-column",{attrs:{prop:"status",label:"状态"}})],1)],1)],1)],1)},l=[],a={name:"UserDetails",props:{id:{type:String,required:!0}},data(){return{info:[]}},watch:{id:{immediate:!0,handler(e){this.getInfo(e)}}},methods:{getInfo(e){let t=this;t.getRequest("/user/read?id="+e).then(e=>{e&&(t.info=e.data)})}}},r=a,o=s("2877"),n=Object(o["a"])(r,i,l,!1,null,null,null);t["a"]=n.exports}}]);
//# sourceMappingURL=chunk-44f49ed2.12275148.js.map