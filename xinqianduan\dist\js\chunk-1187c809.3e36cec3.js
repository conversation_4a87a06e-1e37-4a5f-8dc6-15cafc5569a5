(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1187c809"],{"0a1c":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"notification-container"},[e("div",{staticClass:"page-header"},[e("h2",[t._v("系统通知")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{disabled:0===t.unreadCount},on:{click:t.markAllAsRead}},[e("i",{staticClass:"el-icon-check"}),t._v(" 全部标记为已读 ")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showAddDialog=!0}}},[e("i",{staticClass:"el-icon-plus"}),t._v(" 发布通知 ")])],1)]),e("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.totalCount))]),e("div",{staticClass:"stat-label"},[t._v("总通知数")])]),e("i",{staticClass:"el-icon-bell stat-icon"})])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number unread"},[t._v(t._s(t.unreadCount))]),e("div",{staticClass:"stat-label"},[t._v("未读通知")])]),e("i",{staticClass:"el-icon-message stat-icon"})])],1)],1),e("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[e("el-form",{staticClass:"filter-form",attrs:{inline:!0,model:t.filterForm}},[e("el-form-item",{attrs:{label:"状态"}},[e("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:t.filterForm.is_read,callback:function(e){t.$set(t.filterForm,"is_read",e)},expression:"filterForm.is_read"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"未读",value:"0"}}),e("el-option",{attrs:{label:"已读",value:"1"}})],1)],1),e("el-form-item",{attrs:{label:"类型"}},[e("el-select",{attrs:{placeholder:"请选择类型",clearable:""},model:{value:t.filterForm.type,callback:function(e){t.$set(t.filterForm,"type",e)},expression:"filterForm.type"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"系统通知",value:"system"}}),e("el-option",{attrs:{label:"更新通知",value:"update"}}),e("el-option",{attrs:{label:"备份通知",value:"backup"}}),e("el-option",{attrs:{label:"警告通知",value:"warning"}})],1)],1),e("el-form-item",{attrs:{label:"级别"}},[e("el-select",{attrs:{placeholder:"请选择级别",clearable:""},model:{value:t.filterForm.level,callback:function(e){t.$set(t.filterForm,"level",e)},expression:"filterForm.level"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"信息",value:"info"}}),e("el-option",{attrs:{label:"警告",value:"warning"}}),e("el-option",{attrs:{label:"错误",value:"error"}}),e("el-option",{attrs:{label:"成功",value:"success"}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.loadNotifications}},[t._v("查询")]),e("el-button",{on:{click:t.resetFilter}},[t._v("重置")])],1)],1)],1),e("el-card",{staticClass:"list-card"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"notification-list"},[t._l(t.notificationList,(function(a){return e("div",{key:a.id,staticClass:"notification-item",class:{unread:!a.read}},[e("div",{staticClass:"notification-header"},[e("div",{staticClass:"notification-meta"},[e("el-tag",{staticClass:"level-tag",attrs:{type:t.getLevelType(a.level),size:"small"}},[t._v(" "+t._s(t.getLevelText(a.level))+" ")]),e("el-tag",{staticClass:"type-tag",attrs:{type:t.getTypeColor(a.type),size:"small"}},[t._v(" "+t._s(t.getTypeText(a.type))+" ")]),e("span",{staticClass:"notification-time"},[t._v(t._s(a.time))])],1),e("div",{staticClass:"notification-actions"},[a.read?t._e():e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.markAsRead(a)}}},[t._v(" 标记已读 ")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteNotification(a)}}},[t._v(" 删除 ")])],1)]),e("div",{staticClass:"notification-content"},[e("h4",{staticClass:"notification-title"},[t._v(t._s(a.title))]),e("p",{staticClass:"notification-desc"},[t._v(t._s(a.content))])])])})),0===t.notificationList.length?e("div",{staticClass:"empty-state"},[e("i",{staticClass:"el-icon-bell"}),e("p",[t._v("暂无通知")])]):t._e()],2),e("div",{staticClass:"pagination-wrapper"},[e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50],"page-size":t.pagination.size,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{attrs:{title:"发布通知",visible:t.showAddDialog,width:"600px"},on:{"update:visible":function(e){t.showAddDialog=e}}},[e("el-form",{ref:"notificationForm",attrs:{model:t.newNotification,rules:t.notificationRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"标题",prop:"title"}},[e("el-input",{attrs:{placeholder:"请输入通知标题"},model:{value:t.newNotification.title,callback:function(e){t.$set(t.newNotification,"title",e)},expression:"newNotification.title"}})],1),e("el-form-item",{attrs:{label:"内容",prop:"content"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入通知内容",rows:4},model:{value:t.newNotification.content,callback:function(e){t.$set(t.newNotification,"content",e)},expression:"newNotification.content"}})],1),e("el-form-item",{attrs:{label:"类型",prop:"type"}},[e("el-select",{attrs:{placeholder:"请选择类型"},model:{value:t.newNotification.type,callback:function(e){t.$set(t.newNotification,"type",e)},expression:"newNotification.type"}},[e("el-option",{attrs:{label:"系统通知",value:"system"}}),e("el-option",{attrs:{label:"更新通知",value:"update"}}),e("el-option",{attrs:{label:"备份通知",value:"backup"}}),e("el-option",{attrs:{label:"警告通知",value:"warning"}})],1)],1),e("el-form-item",{attrs:{label:"级别",prop:"level"}},[e("el-select",{attrs:{placeholder:"请选择级别"},model:{value:t.newNotification.level,callback:function(e){t.$set(t.newNotification,"level",e)},expression:"newNotification.level"}},[e("el-option",{attrs:{label:"信息",value:"info"}}),e("el-option",{attrs:{label:"警告",value:"warning"}}),e("el-option",{attrs:{label:"错误",value:"error"}}),e("el-option",{attrs:{label:"成功",value:"success"}})],1)],1),e("el-form-item",{attrs:{label:"目标用户"}},[e("el-select",{attrs:{placeholder:"请选择目标用户"},model:{value:t.newNotification.target_type,callback:function(e){t.$set(t.newNotification,"target_type",e)},expression:"newNotification.target_type"}},[e("el-option",{attrs:{label:"所有用户",value:"all"}}),e("el-option",{attrs:{label:"管理员",value:"admin"}}),e("el-option",{attrs:{label:"普通用户",value:"user"}})],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showAddDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.publishNotification}},[t._v("发布")])],1)],1)],1)},s=[],o=a("7c15"),l={name:"NotificationList",mixins:[{methods:{getRequest:o["b"],postRequest:o["d"],deleteRequest:o["a"]}}],data(){return{loading:!1,showAddDialog:!1,notificationList:[],totalCount:0,unreadCount:0,filterForm:{is_read:"",type:"",level:""},pagination:{page:1,size:20,total:0},newNotification:{title:"",content:"",type:"system",level:"info",target_type:"all"},notificationRules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"change"}],level:[{required:!0,message:"请选择级别",trigger:"change"}]}}},mounted(){this.loadNotifications(),this.loadStats()},methods:{async loadNotifications(){this.loading=!0;try{const t={page:this.pagination.page,size:this.pagination.size,...this.filterForm},e=await this.getRequest("/notification/list",t);200===e.code&&(this.notificationList=e.data.list||[],this.pagination.total=e.data.total||0)}catch(t){console.error("加载通知失败:",t),this.$message.error("加载数据失败")}finally{this.loading=!1}},async loadStats(){try{const t=await this.getRequest("/notification/stats");200===t.code&&(this.totalCount=t.data.total||0,this.unreadCount=t.data.unread||0)}catch(t){console.error("加载统计失败:",t)}},async markAsRead(t){try{const e=await this.postRequest("/dashboard/markNotificationRead",{id:t.id});200===e.code&&(t.read=!0,this.unreadCount=Math.max(0,this.unreadCount-1),this.$message.success("标记成功"))}catch(e){console.error("标记失败:",e),this.$message.error("操作失败")}},async markAllAsRead(){try{await this.$confirm("确定要将所有未读通知标记为已读吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.postRequest("/notification/markAllRead");200===t.code&&(this.notificationList.forEach(t=>t.read=!0),this.unreadCount=0,this.$message.success("操作成功"))}catch(t){"cancel"!==t&&(console.error("操作失败:",t),this.$message.error("操作失败"))}},async deleteNotification(t){try{await this.$confirm("确定要删除这条通知吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.deleteRequest("/notification/delete",{id:t.id});200===e.code&&(this.$message.success("删除成功"),this.loadNotifications(),this.loadStats())}catch(e){"cancel"!==e&&(console.error("删除失败:",e),this.$message.error("删除失败"))}},async publishNotification(){try{await this.$refs.notificationForm.validate();const t=await this.postRequest("/notification/create",this.newNotification);200===t.code&&(this.$message.success("发布成功"),this.showAddDialog=!1,this.resetForm(),this.loadNotifications(),this.loadStats())}catch(t){console.error("发布失败:",t),this.$message.error("发布失败")}},resetForm(){this.newNotification={title:"",content:"",type:"system",level:"info",target_type:"all"},this.$refs.notificationForm&&this.$refs.notificationForm.resetFields()},resetFilter(){this.filterForm={is_read:"",type:"",level:""},this.pagination.page=1,this.loadNotifications()},handleSizeChange(t){this.pagination.size=t,this.pagination.page=1,this.loadNotifications()},handleCurrentChange(t){this.pagination.page=t,this.loadNotifications()},getLevelType(t){const e={info:"info",warning:"warning",error:"danger",success:"success"};return e[t]||"info"},getLevelText(t){const e={info:"信息",warning:"警告",error:"错误",success:"成功"};return e[t]||"信息"},getTypeColor(t){const e={system:"",update:"success",backup:"info",warning:"warning"};return e[t]||""},getTypeText(t){const e={system:"系统通知",update:"更新通知",backup:"备份通知",warning:"警告通知"};return e[t]||"系统通知"}}},n=l,r=(a("d768"),a("2877")),c=Object(r["a"])(n,i,s,!1,null,"8f6e51f2",null);e["default"]=c.exports},"0e58":function(t,e,a){},d768:function(t,e,a){"use strict";a("0e58")}}]);
//# sourceMappingURL=chunk-1187c809.3e36cec3.js.map