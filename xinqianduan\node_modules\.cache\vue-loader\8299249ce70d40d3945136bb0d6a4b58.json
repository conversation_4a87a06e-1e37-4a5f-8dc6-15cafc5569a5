{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue", "mtime": 1748442914242}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["cate.vue"], "names": [], "mappings": ";AAuQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "cate.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-type-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 操作区域 -->\r\n    <div class=\"action-section\">\r\n      <div class=\"search-area\">\r\n        <el-input\r\n          placeholder=\"请输入合同类型名称\"\r\n          v-model=\"search.keyword\"\r\n          class=\"search-input\"\r\n          clearable\r\n        >\r\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n            type=\"primary\"\r\n          >\r\n            搜索\r\n          </el-button>\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"button-area\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"editData(0)\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-btn\"\r\n        >\r\n          新增合同类型\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同类型数据\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"合同类型名称\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-name\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"template_status\" label=\"合同模板\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"template-status\">\r\n              <el-tag\r\n                :type=\"scope.row.template_file ? 'success' : 'warning'\"\r\n                size=\"mini\"\r\n                :icon=\"scope.row.template_file ? 'el-icon-document' : 'el-icon-warning'\"\r\n              >\r\n                {{ scope.row.template_file ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-edit\"\r\n                class=\"action-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.template_file\"\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"downloadTemplate(scope.row)\"\r\n                icon=\"el-icon-download\"\r\n                class=\"action-btn\"\r\n              >\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"合同模板\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"template_file\"\r\n          :rules=\"templateRules\"\r\n        >\r\n          <div class=\"template-upload-area\">\r\n            <!-- 文件上传区域 -->\r\n            <div v-if=\"!ruleForm.template_file\" class=\"upload-section\">\r\n              <el-upload\r\n                ref=\"templateUpload\"\r\n                :action=\"uploadAction\"\r\n                :before-upload=\"beforeTemplateUpload\"\r\n                :on-success=\"handleTemplateSuccess\"\r\n                :on-error=\"handleTemplateError\"\r\n                :show-file-list=\"false\"\r\n                accept=\".doc,.docx,.pdf\"\r\n                :auto-upload=\"true\"\r\n                class=\"template-uploader\"\r\n              >\r\n                <el-button type=\"primary\" icon=\"el-icon-upload\">\r\n                  <span>上传合同模板</span>\r\n                </el-button>\r\n              </el-upload>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 已上传文件显示区域 -->\r\n            <div v-else class=\"uploaded-file\">\r\n              <div class=\"file-info\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ ruleForm.template_name || '合同模板文件' }}</span>\r\n                <span class=\"file-size\">{{ formatFileSize(ruleForm.template_size) }}</span>\r\n              </div>\r\n              <div class=\"file-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"previewTemplate\"\r\n                  icon=\"el-icon-view\"\r\n                  size=\"mini\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"downloadCurrentTemplate\"\r\n                  icon=\"el-icon-download\"\r\n                  size=\"mini\"\r\n                >\r\n                  下载\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"replaceTemplate\"\r\n                  icon=\"el-icon-refresh\"\r\n                  size=\"mini\"\r\n                >\r\n                  替换\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"removeTemplate\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  class=\"danger-text\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 隐藏的替换上传组件 -->\r\n            <el-upload\r\n              v-show=\"false\"\r\n              ref=\"replaceUpload\"\r\n              :action=\"uploadAction\"\r\n              :before-upload=\"beforeTemplateUpload\"\r\n              :on-success=\"handleTemplateSuccess\"\r\n              :on-error=\"handleTemplateError\"\r\n              :show-file-list=\"false\"\r\n              accept=\".doc,.docx,.pdf\"\r\n              :auto-upload=\"true\"\r\n            >\r\n            </el-upload>\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshucate/\",\r\n      title: \"文书类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n        template_file: \"\",\r\n        template_name: \"\",\r\n        template_size: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n\r\n      // 合同模板验证规则\r\n      templateRules: [\r\n        {\r\n          required: true,\r\n          message: \"请上传合同模板\",\r\n          trigger: \"change\",\r\n        },\r\n      ],\r\n\r\n      formLabelWidth: \"120px\",\r\n      uploadAction: \"/admin/Upload/uploadFile\", // 文件上传接口\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          template_file: \"\",\r\n          template_name: \"\",\r\n          template_size: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 模拟搜索测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n\r\n        if (_this.search.keyword) {\r\n          _this.list = allData.filter(item =>\r\n            item.title.includes(_this.search.keyword)\r\n          );\r\n        } else {\r\n          _this.list = allData;\r\n        }\r\n\r\n        _this.total = _this.list.length;\r\n        _this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 合同模板相关方法\r\n    beforeTemplateUpload(file) {\r\n      const isValidType = /\\.(doc|docx|pdf)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('合同模板只能是 .doc、.docx、.pdf 格式!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('合同模板文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传合同模板...');\r\n      return true;\r\n    },\r\n\r\n    handleTemplateSuccess(response, file) {\r\n      if (response.code === 200) {\r\n        this.ruleForm.template_file = response.data.url;\r\n        this.ruleForm.template_name = file.name;\r\n        this.ruleForm.template_size = file.size;\r\n        this.$message.success('合同模板上传成功!');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      } else {\r\n        this.$message.error(response.msg || '合同模板上传失败!');\r\n      }\r\n    },\r\n\r\n    handleTemplateError(err, file) {\r\n      this.$message.error('合同模板上传失败，请重试!');\r\n      console.error('Template upload error:', err);\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    },\r\n\r\n    // 预览模板\r\n    previewTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        // 这里可以实现文件预览功能\r\n        this.$message.info('预览功能开发中...');\r\n        // window.open(this.ruleForm.template_file, '_blank');\r\n      }\r\n    },\r\n\r\n    // 下载当前模板\r\n    downloadCurrentTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = this.ruleForm.template_file;\r\n        link.download = this.ruleForm.template_name || '合同模板';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success('开始下载合同模板');\r\n      }\r\n    },\r\n\r\n    // 替换模板\r\n    replaceTemplate() {\r\n      this.$refs.replaceUpload.$el.querySelector('input').click();\r\n    },\r\n\r\n    // 删除模板\r\n    removeTemplate() {\r\n      this.$confirm('确定要删除当前合同模板吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.ruleForm.template_file = '';\r\n        this.ruleForm.template_name = '';\r\n        this.ruleForm.template_size = 0;\r\n        this.$message.success('合同模板已删除');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 下载表格中的模板\r\n    downloadTemplate(row) {\r\n      if (row.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = row.template_file;\r\n        link.download = row.template_name || `${row.title}模板`;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success(`开始下载 ${row.title} 模板`);\r\n      } else {\r\n        this.$message.warning('该合同类型暂无模板文件');\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-type-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.search-area {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.button-area {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn {\r\n  font-weight: 500;\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.type-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.type-name i {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-name span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-type-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .action-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-area {\r\n    max-width: none;\r\n  }\r\n\r\n  .button-area {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 小屏幕下操作按钮调整 */\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n}\r\n\r\n/* 操作按钮横向布局 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n}\r\n\r\n.el-button--success:hover {\r\n  background: #85ce61;\r\n  border-color: #85ce61;\r\n}\r\n\r\n.el-button--danger:hover {\r\n  background: #f78989;\r\n  border-color: #f78989;\r\n}\r\n\r\n/* 合同模板状态样式 */\r\n.template-status {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n/* 合同模板上传区域样式 */\r\n.template-upload-area {\r\n  width: 100%;\r\n}\r\n\r\n.upload-section {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-section:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.template-uploader {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tip i {\r\n  color: #409eff;\r\n}\r\n\r\n/* 已上传文件显示样式 */\r\n.uploaded-file {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.file-info i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.file-size {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-actions .el-button--text {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n}\r\n\r\n.danger-text {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n.danger-text:hover {\r\n  color: #f78989 !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .file-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .file-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"]}]}