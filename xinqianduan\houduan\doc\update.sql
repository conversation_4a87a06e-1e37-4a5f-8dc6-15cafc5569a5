/*240713 新增密码字段  yzh*/
ALTER TABLE `fdb`.`web_users`
ADD COLUMN `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '密码' AFTER `yuangong_id`;

/*240717  yzh*/
ALTER TABLE `fdb`.`web_debts`
ADD COLUMN `del_images` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '删除的图片' AFTER `utime`;

ALTER TABLE `fdb`.`web_debts`
ADD COLUMN `del_attach_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '删除的文件' AFTER `del_images`;

/*240722  yzh*/
ALTER TABLE `fdb`.`web_orders`
ADD COLUMN `free_operator` int(11) NULL COMMENT '免支付的操作人（员工id或者是admin）' AFTER `pay_time`;

/*240802  yzh*/
ALTER TABLE `fdb`.`web_users`
ADD COLUMN `tiaojie_id` int(11) NULL DEFAULT 0 COMMENT '调解员id' AFTER `yuangong_id`,
ADD COLUMN `fawu_id` int(11) NULL DEFAULT 0 COMMENT '法务专员id' AFTER `tiaojie_id`,
ADD COLUMN `lian_id` int(11) NULL DEFAULT 0 COMMENT '立案专员id' AFTER `fawu_id`;

/*240807  yzh*/
ALTER TABLE `fdb`.`web_debts`
ADD COLUMN `lvshi_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '律师涵' AFTER `del_attach_path`,
ADD COLUMN `del_lvshi_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '删除的律师函' AFTER `lvshi_path`,
ADD COLUMN `wenshu_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '法律文书' AFTER `del_lvshi_path`,
ADD COLUMN `del_wenshu_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '删除的法律文书' AFTER `wenshu_path`;

/*240819  yzh*/
ALTER TABLE `fdb`.`web_chats`
MODIFY COLUMN `orther_id` int(11) NULL DEFAULT 0 COMMENT '律师id或者是业务员id' AFTER `uid`,
ADD COLUMN `source` varchar(255) NULL DEFAULT 1 COMMENT '1与律师对话  2与业务员对话' AFTER `datas`;


/*240830  ljl*/

ALTER TABLE `web_users`
ADD COLUMN `ls_id`  int(11) NULL DEFAULT 0 COMMENT '律师ID' AFTER `lian_id`,
ADD COLUMN `ywy_id`  int(11) NULL DEFAULT 0 COMMENT '业务员ID' AFTER `ls_id`,
ADD COLUMN `htsczy_id`  int(11) NULL DEFAULT 0 COMMENT '合同上传专用id' AFTER `ywy_id`;


