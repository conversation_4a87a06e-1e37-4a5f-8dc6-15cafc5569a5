{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\components\\UserDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\components\\UserDetail.vue", "mtime": 1732626900069}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIGV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAnVXNlckRldGFpbHMnLA0KICAgIHByb3BzOiB7DQogICAgICBpZDogew0KICAgICAgICB0eXBlOiBTdHJpbmcsDQogICAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBkYXRhKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBpbmZvOiBbXSAvLyDnlKjkuo7lrZjlgqjmjqXlj6Pov5Tlm57nmoTmlbDmja4NCiAgICAgIH07DQogICAgfSwNCiAgICB3YXRjaDogew0KICAgICAgaWQ6IHsNCiAgICAgICAgICBpbW1lZGlhdGU6IHRydWUsIC8vIOe7hOS7tuWIm+W7uuaXtueri+WNs+inpuWPkQ0KICAgICAgICAgIGhhbmRsZXIobmV3SWQpIHsNCiAgICAgICAgICAgICAgdGhpcy5nZXRJbmZvKG5ld0lkKTsNCiAgICAgICAgICB9DQogICAgICB9DQogICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgZ2V0SW5mbyhpZCkgew0KICAgICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgICBfdGhpcy5nZXRSZXF1ZXN0KCIvdXNlci9yZWFkP2lkPSIgKyBpZCkudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgIGlmIChyZXNwKSB7DQogICAgICAgICAgICBfdGhpcy5pbmZvID0gcmVzcC5kYXRhOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["UserDetail.vue"], "names": [], "mappings": ";AAoFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UserDetail.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <el-row>\r\n    <el-descriptions title=\"客户信息\">\r\n      <el-descriptions-item label=\"公司名称\">{{\r\n        info.company\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"手机号\">{{\r\n        info.phone\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"名称\">{{\r\n        info.nickname\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系人\">{{\r\n        info.linkman\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"头像\">\r\n        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n             :src=\"info.headimg\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.headimg)\"\r\n        /></el-descriptions-item>\r\n      <el-descriptions-item label=\"用户来源\">{{\r\n        info.yuangong_id\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系方式\">{{\r\n        info.linkphone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"调解员\">{{\r\n            info.tiaojie_name\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"法务专员\">{{\r\n            info.fawu_name\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"立案专员\">{{\r\n            info.lian_name\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"合同上传专用\">{{\r\n            info.htsczy_name\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"律师\">{{\r\n            info.ls_name\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"业务员\">{{\r\n            info.ywy_name\r\n            }}\r\n        </el-descriptions-item>\r\n      <el-descriptions-item label=\"营业执照\">\r\n        <img v-if=\"info.license !='' && info.license!=null\"\r\n             :src=\"info.license\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.license)\"\r\n        />\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"开始时间\">{{\r\n        info.start_time\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"会员年限\">{{\r\n        info.year\r\n        }}年</el-descriptions-item>\r\n    </el-descriptions>\r\n      <el-descriptions title=\"债务人信息\" :colon=\"false\">\r\n          <el-descriptions-item>\r\n              <el-table\r\n                      :data=\"info.debts\"\r\n                      style=\"width: 100%; margin-top: 10px\"\r\n                      v-loading=\"loading\"\r\n                      size=\"mini\"\r\n              >\r\n                  <el-table-column prop=\"name\" label=\"债务人姓名\"> </el-table-column>\r\n                  <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n                  <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n                  <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n              </el-table></el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n  </el-row>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: String,\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: [] // 用于存储接口返回的数据\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          if (resp) {\r\n            _this.info = resp.data;\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n"]}]}