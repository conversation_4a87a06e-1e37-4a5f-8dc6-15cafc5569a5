<?php
// +----------------------------------------------------------------------
// | websocket
// +----------------------------------------------------------------------
// | Author: myh
// +----------------------------------------------------------------------
 
namespace app\worker;
 
use think\worker\Server;
use models\{Zhubos,Users,Comments,Lines};
use untils\{JsonService};
class Worker extends Server
{
    protected $socket = 'http://0.0.0.0:2346';
 
   	public function onMessage($connection,$message)
	{
		
		$data = explode('|',$message);
		$res = '';
		if(count($data)==3){
			$type = $data[0];
			$action=$data[1];
			$id = $data[2];
			if($type=='line'){
				if($action=='list'){
					$model = new Lines();
					$count = $model
			        ->where(['zhubo_id'=>$id])
			        ->where('is_line','=',1)
			        ->count();
			        $list = $model->where(['zhubo_id'=>$id])
			        ->where('update_time','>=',time())
			        ->select();
			        
					$rand = rand(99,9999);
					$res = json_encode(['code'=>200, 'msg'=>'成功','data'=>['count'=>$count,'list'=>$list]]);

				}
			}else{
				if($action=='list'){
					$model = new Comments();
					$info = $model
			        ->where(['zhubo_id'=>$id])
			        ->where('comment_time','>',time()*1-1.9)
			        ->order(['id'=>'asc'])
			        ->find();

			        if(!empty($info)) {
		
			            $res = json_encode(['code'=>200, 'msg'=>'获取成功','data'=>$info]);           
			        }else{
			            $res = json_encode(['code'=>400, 'msg'=>'暂无评论']);
			        }
				}
			}
		}else{
			$res = json_encode(['code'=>400, 'msg'=>'失败']);
		}
		$connection->send($res);
	}
 	
  
}