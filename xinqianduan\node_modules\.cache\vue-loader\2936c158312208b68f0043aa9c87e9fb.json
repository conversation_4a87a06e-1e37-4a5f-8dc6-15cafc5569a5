{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\lvshi.vue", "mtime": 1748469881447}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lvshi.vue"], "names": [], "mappings": ";AA8j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file": "lvshi.vue", "sourceRoot": "src/views/pages/lvshi", "sourcesContent": ["<template>\r\n  <div class=\"lawyer-management\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <div class=\"page-title\">\r\n            <i class=\"el-icon-user-solid\"></i>\r\n            <span>律师管理</span>\r\n          </div>\r\n          <div class=\"page-subtitle\">管理系统中的律师信息和专业资质</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增律师\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            circle\r\n            class=\"refresh-btn\"\r\n          ></el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div class=\"stats-cards\">\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon lawyer-icon\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ total }}</div>\r\n          <div class=\"stat-label\">律师总数</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon active-icon\">\r\n          <i class=\"el-icon-check\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ activeCount }}</div>\r\n          <div class=\"stat-label\">在职律师</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon specialty-icon\">\r\n          <i class=\"el-icon-medal\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ specialtyCount }}</div>\r\n          <div class=\"stat-label\">专业领域</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"stat-card\">\r\n        <div class=\"stat-icon firm-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\">{{ firmCount }}</div>\r\n          <div class=\"stat-label\">合作律所</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <el-card shadow=\"never\" class=\"content-card\">\r\n        <!-- 搜索和筛选区域 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-left\">\r\n            <div class=\"search-input-group\">\r\n              <el-input\r\n                placeholder=\"搜索律师姓名、律所、证号...\"\r\n                v-model=\"search.keyword\"\r\n                class=\"search-input\"\r\n                clearable\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  icon=\"el-icon-search\"\r\n                  @click=\"searchData()\"\r\n                ></el-button>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-select\r\n              v-model=\"search.specialty\"\r\n              placeholder=\"专业领域\"\r\n              clearable\r\n              class=\"filter-select\"\r\n              @change=\"searchData\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in zhuanyes\"\r\n                :key=\"item.id\"\r\n                :label=\"item.title\"\r\n                :value=\"item.id\"\r\n              ></el-option>\r\n            </el-select>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"resetSearch\"\r\n              class=\"reset-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视图切换 -->\r\n        <div class=\"view-controls\">\r\n          <div class=\"view-tabs\">\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'table' }\"\r\n              @click=\"switchView('table')\"\r\n            >\r\n              <i class=\"el-icon-menu\"></i>\r\n              <span>列表视图</span>\r\n            </div>\r\n            <div\r\n              class=\"view-tab\"\r\n              :class=\"{ active: viewMode === 'card' }\"\r\n              @click=\"switchView('card')\"\r\n            >\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              <span>卡片视图</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"view-actions\">\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-download\"\r\n              size=\"small\"\r\n              @click=\"exportData\"\r\n            >\r\n              导出\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <!-- 列表视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"lawyer-table\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n\r\n            <el-table-column label=\"律师信息\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"lawyer-info-cell\">\r\n                  <div class=\"lawyer-avatar\">\r\n                    <el-avatar\r\n                      :src=\"scope.row.pic_path\"\r\n                      :size=\"50\"\r\n                      @click.native=\"showImage(scope.row.pic_path)\"\r\n                      class=\"clickable-avatar\"\r\n                    >\r\n                      <i class=\"el-icon-user-solid\"></i>\r\n                    </el-avatar>\r\n                  </div>\r\n                  <div class=\"lawyer-details\">\r\n                    <div class=\"lawyer-name\">{{ scope.row.title }}</div>\r\n                    <div class=\"lawyer-card\">证号：{{ scope.row.laywer_card || '暂无' }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"律所\" prop=\"lvsuo\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"firm-info\">\r\n                  <i class=\"el-icon-office-building\"></i>\r\n                  <span>{{ scope.row.lvsuo || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"专业领域\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"specialties\">\r\n                  <el-tag\r\n                    v-for=\"specialty in getSpecialtyNames(scope.row.zhuanyes)\"\r\n                    :key=\"specialty\"\r\n                    size=\"mini\"\r\n                    class=\"specialty-tag\"\r\n                  >\r\n                    {{ specialty }}\r\n                  </el-tag>\r\n                  <span v-if=\"!scope.row.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"联系方式\" prop=\"phone\" min-width=\"130\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"contact-info\">\r\n                  <i class=\"el-icon-phone\"></i>\r\n                  <span>{{ scope.row.phone || '暂无' }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"证书\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.card_path\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"showImage(scope.row.card_path)\"\r\n                  class=\"view-cert-btn\"\r\n                >\r\n                  查看\r\n                </el-button>\r\n                <span v-else class=\"no-data\">暂无</span>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"注册时间\" prop=\"create_time\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-info\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(scope.row.create_time) }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    circle\r\n                    title=\"编辑\"\r\n                  ></el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    circle\r\n                    title=\"删除\"\r\n                  ></el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-else class=\"card-view\">\r\n          <div class=\"lawyer-cards\" v-loading=\"loading\">\r\n            <div\r\n              v-for=\"lawyer in list\"\r\n              :key=\"lawyer.id\"\r\n              class=\"lawyer-card\"\r\n            >\r\n              <div class=\"card-header\">\r\n                <div class=\"lawyer-avatar-large\">\r\n                  <el-avatar\r\n                    :src=\"lawyer.pic_path\"\r\n                    :size=\"80\"\r\n                    @click.native=\"showImage(lawyer.pic_path)\"\r\n                    class=\"clickable-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"lawyer-basic-info\">\r\n                  <div class=\"lawyer-name-large\">{{ lawyer.title }}</div>\r\n                  <div class=\"lawyer-firm\">\r\n                    <i class=\"el-icon-office-building\"></i>\r\n                    <span>{{ lawyer.lvsuo || '暂无律所' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-content\">\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-phone\"></i>\r\n                    联系方式\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.phone || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    证件号码\r\n                  </div>\r\n                  <div class=\"info-value\">{{ lawyer.laywer_card || '暂无' }}</div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-collection-tag\"></i>\r\n                    专业领域\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-tag\r\n                      v-for=\"specialty in getSpecialtyNames(lawyer.zhuanyes)\"\r\n                      :key=\"specialty\"\r\n                      size=\"mini\"\r\n                      class=\"specialty-tag\"\r\n                    >\r\n                      {{ specialty }}\r\n                    </el-tag>\r\n                    <span v-if=\"!lawyer.zhuanyes\" class=\"no-data\">暂无专业</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-row\">\r\n                  <div class=\"info-label\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    执业证书\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    <el-button\r\n                      v-if=\"lawyer.card_path\"\r\n                      type=\"text\"\r\n                      size=\"mini\"\r\n                      @click=\"showImage(lawyer.card_path)\"\r\n                      class=\"view-cert-btn\"\r\n                    >\r\n                      查看证书\r\n                    </el-button>\r\n                    <span v-else class=\"no-data\">暂无证书</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-footer\">\r\n                <div class=\"register-time\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  <span>{{ formatDate(lawyer.create_time) }}</span>\r\n                </div>\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(lawyer.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(lawyer), lawyer.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '姓名'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"绑定员工\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"yuangong_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option-group\r\n              v-for=\"group in yuangongs\"\r\n              :key=\"group.label\"\r\n              :label=\"group.label\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in group.options\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-option-group>\r\n          </el-select>\r\n          <!-- <el-select\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            filterable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in yuangongs\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select> -->\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"专业\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhuanyes\"\r\n        >\r\n          <el-select v-model=\"ruleForm.zhuanyes\" multiple placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in zhuanyes\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"律所\" :label-width=\"formLabelWidth\" prop=\"lvsuo\">\r\n          <el-input v-model=\"ruleForm.lvsuo\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"职业年薪\" :label-width=\"formLabelWidth\" prop=\"age\">\r\n          <el-input\r\n            v-model=\"ruleForm.age\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"联系方式\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证件号\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"laywer_card\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.laywer_card\"\r\n            autocomplete=\"off\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          >\r\n            <template slot=\"append\">330rpx*300rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"证书\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"card_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.card_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeField('card_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"showImage(ruleForm.card_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.card_path\"\r\n              @click=\"delImage(ruleForm.card_path, 'card_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12, // 改为12，适合卡片视图\r\n      search: {\r\n        keyword: \"\",\r\n        specialty: \"\", // 新增专业筛选\r\n      },\r\n      loading: true,\r\n      zhuanyes: [],\r\n      url: \"/lvshi/\",\r\n      title: \"律师\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table 或 card\r\n      selectedLawyers: [], // 选中的律师\r\n      originalList: [], // 原始数据，用于搜索\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        yuangong_id: [\r\n          {\r\n            required: true,\r\n            message: \"请绑定员工\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhuanyes: [\r\n          {\r\n            required: true,\r\n            message: \"请选择专业\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lvsuo: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律所\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        age: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职业年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        laywer_card: [\r\n          {\r\n            required: true,\r\n            message: \"请填写证件号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写律师联系方式\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        card_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传证书\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      field: \"\",\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 在职律师数量\r\n    activeCount() {\r\n      return this.list.filter(lawyer => lawyer.status === 1).length;\r\n    },\r\n    // 专业领域数量\r\n    specialtyCount() {\r\n      const specialties = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.zhuanyes && Array.isArray(lawyer.zhuanyes)) {\r\n          lawyer.zhuanyes.forEach(specialty => specialties.add(specialty));\r\n        }\r\n      });\r\n      return specialties.size;\r\n    },\r\n    // 合作律所数量\r\n    firmCount() {\r\n      const firms = new Set();\r\n      this.list.forEach(lawyer => {\r\n        if (lawyer.lvsuo) {\r\n          firms.add(lawyer.lvsuo);\r\n        }\r\n      });\r\n      return firms.size;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTestData(); // 先加载测试数据\r\n    this.getZhuanyes(); // 加载专业数据用于筛选\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 切换视图模式\r\n    switchView(mode) {\r\n      this.viewMode = mode;\r\n      this.$message.success(`已切换到${mode === 'table' ? '列表' : '卡片'}视图`);\r\n    },\r\n\r\n    // 获取专业名称\r\n    getSpecialtyNames(specialtyIds) {\r\n      if (!specialtyIds || !Array.isArray(specialtyIds)) return [];\r\n      if (!this.zhuanyes || !Array.isArray(this.zhuanyes)) return [];\r\n      return specialtyIds.map(id => {\r\n        const specialty = this.zhuanyes.find(z => z.id === id);\r\n        return specialty ? specialty.title : '未知专业';\r\n      });\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return '暂无';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('zh-CN');\r\n    },\r\n\r\n    // 重置搜索\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        specialty: \"\"\r\n      };\r\n      // 重新加载完整的测试数据\r\n      this.loadTestData();\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      if (this.selectedLawyers.length > 0) {\r\n        this.$message.success(`导出选中的 ${this.selectedLawyers.length} 条律师数据`);\r\n      } else {\r\n        this.$message.success('导出全部律师数据');\r\n      }\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedLawyers = selection;\r\n    },\r\n\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 测试专业数据\r\n      this.zhuanyes = [\r\n        { id: 1, title: '民事诉讼' },\r\n        { id: 2, title: '刑事辩护' },\r\n        { id: 3, title: '商事仲裁' },\r\n        { id: 4, title: '知识产权' },\r\n        { id: 5, title: '劳动争议' },\r\n        { id: 6, title: '房产纠纷' },\r\n        { id: 7, title: '合同纠纷' },\r\n        { id: 8, title: '公司法务' }\r\n      ];\r\n\r\n      // 测试律师数据\r\n      this.list = [\r\n        {\r\n          id: 1,\r\n          title: '张明华',\r\n          lvsuo: '北京德恒律师事务所',\r\n          zhuanyes: [1, 3, 7],\r\n          phone: '13800138001',\r\n          laywer_card: '*********',\r\n          age: 8,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-03-15 09:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '李晓雯',\r\n          lvsuo: '上海锦天城律师事务所',\r\n          zhuanyes: [2, 4],\r\n          phone: '13800138002',\r\n          laywer_card: 'A20210002',\r\n          age: 12,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-08-22 14:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '王建国',\r\n          lvsuo: '广东广和律师事务所',\r\n          zhuanyes: [5, 6, 8],\r\n          phone: '13800138003',\r\n          laywer_card: 'A20210003',\r\n          age: 15,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-12-10 11:45:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '陈美玲',\r\n          lvsuo: '深圳君合律师事务所',\r\n          zhuanyes: [1, 4, 7],\r\n          phone: '13800138004',\r\n          laywer_card: 'A20210004',\r\n          age: 6,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-01-18 16:10:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '刘志强',\r\n          lvsuo: '北京金杜律师事务所',\r\n          zhuanyes: [2, 3, 8],\r\n          phone: '13800138005',\r\n          laywer_card: 'A20210005',\r\n          age: 20,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2018-05-30 10:25:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '赵雅琴',\r\n          lvsuo: '上海方达律师事务所',\r\n          zhuanyes: [4, 5, 6],\r\n          phone: '13800138006',\r\n          laywer_card: 'A20210006',\r\n          age: 9,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-07-12 13:55:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 7,\r\n          title: '孙文博',\r\n          lvsuo: '广州广信君达律师事务所',\r\n          zhuanyes: [1, 2, 7],\r\n          phone: '13800138007',\r\n          laywer_card: 'A20210007',\r\n          age: 11,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-11-08 15:40:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 8,\r\n          title: '周慧敏',\r\n          lvsuo: '深圳市律师协会',\r\n          zhuanyes: [3, 6, 8],\r\n          phone: '13800138008',\r\n          laywer_card: 'A20210008',\r\n          age: 7,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-09-25 12:15:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 9,\r\n          title: '吴国强',\r\n          lvsuo: '北京市中伦律师事务所',\r\n          zhuanyes: [1, 5, 7],\r\n          phone: '13800138009',\r\n          laywer_card: 'A20210009',\r\n          age: 13,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2020-04-14 09:20:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 10,\r\n          title: '郑小红',\r\n          lvsuo: '上海市汇业律师事务所',\r\n          zhuanyes: [2, 4, 6],\r\n          phone: '13800138010',\r\n          laywer_card: 'A20210010',\r\n          age: 5,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2022-06-03 14:30:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 11,\r\n          title: '马云飞',\r\n          lvsuo: '广东信达律师事务所',\r\n          zhuanyes: [3, 7, 8],\r\n          phone: '13800138011',\r\n          laywer_card: 'A20210011',\r\n          age: 16,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2019-02-28 11:05:00',\r\n          status: 1\r\n        },\r\n        {\r\n          id: 12,\r\n          title: '林静怡',\r\n          lvsuo: '深圳市盈科律师事务所',\r\n          zhuanyes: [1, 4, 5],\r\n          phone: '13800138012',\r\n          laywer_card: 'A20210012',\r\n          age: 10,\r\n          pic_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          card_path: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n          create_time: '2021-01-20 16:45:00',\r\n          status: 1\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据\r\n      this.originalList = [...this.list];\r\n      // 设置总数\r\n      this.total = this.list.length;\r\n      this.loading = false;\r\n    },\r\n\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(\"/yuangong/getMoreList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getZhuanyes() {\r\n      let _this = this;\r\n      _this.getRequest(\"/zhuanye/getList\").then((resp) => {\r\n        if (resp) {\r\n          _this.zhuanyes = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          phone: \"\",\r\n          address: \"\",\r\n          pic_path: \"\",\r\n          card_path: \"\",\r\n          zhuanyes: \"\",\r\n          age: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getZhuanyes();\r\n      _this.getLvshi();\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n\r\n      // 如果有原始测试数据，在本地进行搜索\r\n      if (this.originalList.length > 0) {\r\n        this.filterTestData();\r\n      } else {\r\n        this.size = 20;\r\n        this.getData();\r\n      }\r\n    },\r\n\r\n    // 在测试数据中进行筛选\r\n    filterTestData() {\r\n      let filteredList = [...this.originalList];\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.title.toLowerCase().includes(keyword) ||\r\n          lawyer.lvsuo.toLowerCase().includes(keyword) ||\r\n          lawyer.laywer_card.toLowerCase().includes(keyword) ||\r\n          lawyer.phone.includes(keyword)\r\n        );\r\n      }\r\n\r\n      // 专业筛选\r\n      if (this.search.specialty) {\r\n        filteredList = filteredList.filter(lawyer =>\r\n          lawyer.zhuanyes && lawyer.zhuanyes.includes(this.search.specialty)\r\n        );\r\n      }\r\n\r\n      // 这里可以添加分页逻辑，但为了演示简单，直接显示所有结果\r\n      this.total = filteredList.length;\r\n\r\n      // 模拟搜索延迟\r\n      this.loading = true;\r\n      setTimeout(() => {\r\n        this.list = filteredList;\r\n        this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      // 如果已经有测试数据，直接使用\r\n      if (_this.list.length > 0) {\r\n        return;\r\n      }\r\n\r\n      _this.loading = true;\r\n\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          } else {\r\n            // 如果接口失败，使用测试数据\r\n            console.log('使用测试数据');\r\n            _this.loadTestData();\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 接口错误时也使用测试数据\r\n          console.log('接口错误，使用测试数据');\r\n          _this.loadTestData();\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm[this.field] = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 主容器 */\r\n.lawyer-management {\r\n  padding: 0;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.add-btn {\r\n  padding: 12px 24px;\r\n  font-size: 16px;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  padding: 0 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.lawyer-icon {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.specialty-icon {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.firm-icon {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  line-height: 1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  padding: 0 24px;\r\n}\r\n\r\n.content-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 24px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n}\r\n\r\n.search-input-group {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-input {\r\n  min-width: 300px;\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.filter-select {\r\n  width: 150px;\r\n}\r\n\r\n.search-btn, .reset-btn {\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.view-tabs {\r\n  display: flex;\r\n  background: #f5f7fa;\r\n  border-radius: 8px;\r\n  padding: 4px;\r\n}\r\n\r\n.view-tab {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.view-tab.active {\r\n  background: white;\r\n  color: #409eff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.view-tab:hover:not(.active) {\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格视图 */\r\n.table-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.lawyer-info-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.lawyer-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.clickable-avatar {\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.clickable-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.lawyer-details {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.lawyer-card {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.firm-info, .contact-info, .time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.specialties {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n}\r\n\r\n.specialty-tag {\r\n  background: #ecf5ff;\r\n  color: #409eff;\r\n  border: 1px solid #d9ecff;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n  font-size: 12px;\r\n}\r\n\r\n.view-cert-btn {\r\n  color: #67c23a;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 6px;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 0;\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n.action-buttons .el-button.is-circle {\r\n  border-radius: 50%;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.lawyer-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.lawyer-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.lawyer-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  display: flex;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.lawyer-avatar-large {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.lawyer-basic-info {\r\n  flex: 1;\r\n}\r\n\r\n.lawyer-name-large {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.lawyer-firm {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #f5f7fa;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  text-align: right;\r\n  color: #303133;\r\n}\r\n\r\n.card-footer {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.register-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 24px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.pagination {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 12px 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-right {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .view-controls {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .stats-cards {\r\n    grid-template-columns: 1fr;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .lawyer-cards {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .card-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .info-row {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-value {\r\n    text-align: left;\r\n  }\r\n\r\n  .card-footer {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n/* 原有样式保留 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}