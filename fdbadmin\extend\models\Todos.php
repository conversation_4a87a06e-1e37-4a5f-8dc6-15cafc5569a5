<?php
namespace models;

use think\Model;

/**
 * 待办事项模型
 */
class Todos extends Model
{
    // 设置表名
    protected $table = 'todos';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'title'        => 'string',
        'description'  => 'string',
        'type'         => 'string',
        'priority'     => 'string',
        'status'       => 'int',
        'assigned_to'  => 'int',
        'related_id'   => 'int',
        'related_type' => 'string',
        'due_date'     => 'int',
        'completed_at' => 'int',
        'created_by'   => 'int',
        'create_time'  => 'int',
        'update_time'  => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 获取待办事项列表
     * @param int $userId 用户ID
     * @param int $limit 限制数量
     * @param int $status 状态筛选：null-所有,0-未完成,1-已完成
     * @return array
     */
    public static function getTodoList($userId = null, $limit = 10, $status = null)
    {
        $where = [];
        
        if ($userId) {
            $where[] = ['assigned_to', '=', $userId];
        }
        
        if ($status !== null) {
            $where[] = ['status', '=', $status];
        }
        
        return self::where($where)
            ->order('priority desc, due_date asc, create_time desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取未完成待办事项数量
     * @param int $userId 用户ID
     * @return int
     */
    public static function getPendingCount($userId = null)
    {
        $where = [
            ['status', '=', 0]
        ];
        
        if ($userId) {
            $where[] = ['assigned_to', '=', $userId];
        }
        
        return self::where($where)->count();
    }

    /**
     * 获取即将到期的待办事项
     * @param int $userId 用户ID
     * @param int $days 天数，默认3天内
     * @return array
     */
    public static function getDueSoon($userId = null, $days = 3)
    {
        $where = [
            ['status', '=', 0],
            ['due_date', '>', 0],
            ['due_date', '<=', time() + ($days * 86400)]
        ];
        
        if ($userId) {
            $where[] = ['assigned_to', '=', $userId];
        }
        
        return self::where($where)
            ->order('due_date asc')
            ->select()
            ->toArray();
    }

    /**
     * 完成待办事项
     * @param int $id 待办事项ID
     * @param int $userId 用户ID
     * @return bool
     */
    public static function completeTodo($id, $userId = null)
    {
        $where = [
            ['id', '=', $id],
            ['status', '=', 0]
        ];
        
        if ($userId) {
            $where[] = ['assigned_to', '=', $userId];
        }
        
        $data = [
            'status' => 1,
            'completed_at' => time()
        ];
        
        return self::where($where)->update($data);
    }

    /**
     * 重新激活待办事项
     * @param int $id 待办事项ID
     * @param int $userId 用户ID
     * @return bool
     */
    public static function reactivateTodo($id, $userId = null)
    {
        $where = [
            ['id', '=', $id],
            ['status', '=', 1]
        ];
        
        if ($userId) {
            $where[] = ['assigned_to', '=', $userId];
        }
        
        $data = [
            'status' => 0,
            'completed_at' => null
        ];
        
        return self::where($where)->update($data);
    }

    /**
     * 创建待办事项
     * @param array $data 待办事项数据
     * @return bool|int
     */
    public static function createTodo($data)
    {
        $todo = new self();
        $todo->title = $data['title'];
        $todo->description = $data['description'] ?? '';
        $todo->type = $data['type'] ?? 'general';
        $todo->priority = $data['priority'] ?? 'medium';
        $todo->assigned_to = $data['assigned_to'] ?? null;
        $todo->related_id = $data['related_id'] ?? null;
        $todo->related_type = $data['related_type'] ?? null;
        $todo->due_date = $data['due_date'] ?? null;
        $todo->created_by = $data['created_by'] ?? null;
        
        return $todo->save();
    }

    /**
     * 获取优先级文本
     * @param string $priority
     * @return string
     */
    public static function getPriorityText($priority)
    {
        $map = [
            'high' => '高',
            'medium' => '中',
            'low' => '低'
        ];
        
        return $map[$priority] ?? '中';
    }

    /**
     * 获取类型文本
     * @param string $type
     * @return string
     */
    public static function getTypeText($type)
    {
        $map = [
            'debt' => '债务处理',
            'order' => '订单处理',
            'user' => '用户管理',
            'system' => '系统任务',
            'general' => '一般任务'
        ];
        
        return $map[$type] ?? '一般任务';
    }
}
