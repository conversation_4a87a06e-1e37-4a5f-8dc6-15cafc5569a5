# Installation
> `npm install --save @types/webpack-dev-server`

# Summary
This package contains type definitions for webpack-dev-server (https://github.com/webpack/webpack-dev-server).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-dev-server/v3.

### Additional Details
 * Last updated: <PERSON><PERSON>, 24 Aug 2021 12:01:24 GMT
 * Dependencies: [@types/webpack](https://npmjs.com/package/@types/webpack), [@types/http-proxy-middleware](https://npmjs.com/package/@types/http-proxy-middleware), [@types/express](https://npmjs.com/package/@types/express), [@types/serve-static](https://npmjs.com/package/@types/serve-static), [@types/connect-history-api-fallback](https://npmjs.com/package/@types/connect-history-api-fallback)
 * Global values: none

# Credits
These definitions were written by [maest<PERSON><PERSON>](https://github.com/maestroh), [<PERSON>](https://github.com/daveparslow), [<PERSON><PERSON><PERSON>](https://github.com/ZheyangSong), [<PERSON> Agius](https://github.com/alan-agius4), [Artur Androsovych](https://github.com/arturovt), [Dave Cardwell](https://github.com/davecardwell), [Katsuya Hino](https://github.com/dobogo), [Billy Le](https://github.com/billy-le), [Chris Paterson](https://github.com/chrispaterson), [Piotr Błażejewicz](https://github.com/peterblazejewicz), and [William Artero](https://github.com/wwmoraes).
