{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\Dashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\Dashboard.vue", "mtime": 1749567608929}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SystemMonitor", "name", "components", "data", "chartPeriod", "loading", "stats", "totalUsers", "totalCases", "totalContracts", "totalRevenue", "recentActivities", "quickActions", "id", "icon", "color", "title", "description", "action", "todoList", "completed", "priority", "notifications", "time", "read", "mounted", "loadDashboardData", "methods", "Promise", "all", "loadStats", "loadActivities", "loadTodos", "loadQuickActions", "loadNotifications", "error", "console", "$message", "response", "getRequest", "code", "totalDebts", "totalOrders", "actions", "map", "count", "url", "getCurrentTime", "now", "Date", "options", "year", "month", "day", "weekday", "toLocaleDateString", "handleQuickAction", "startsWith", "$router", "push", "info", "viewAllActivities", "viewAllTodos", "viewAllNotifications", "handleTodoChange", "todo", "postRequest", "success", "mark<PERSON><PERSON><PERSON>", "notification", "getPriorityText", "high", "medium", "low", "refreshData"], "sources": ["src/views/pages/Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- 欢迎区域 -->\r\n    <div class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <h1 class=\"welcome-title\">欢迎使用法律服务管理系统</h1>\r\n        <p class=\"welcome-subtitle\">{{ getCurrentTime() }} | 管理员，您好！</p>\r\n      </div>\r\n      <div class=\"welcome-actions\">\r\n        <el-button type=\"primary\" @click=\"handleQuickAction('new-case')\">\r\n          <i class=\"el-icon-plus\"></i> 新建案件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleQuickAction('new-contract')\">\r\n          <i class=\"el-icon-document-add\"></i> 新建合同\r\n        </el-button>\r\n        <el-button type=\"info\" @click=\"refreshData\" :loading=\"loading\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <div class=\"stats-section\" v-loading=\"loading\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalUsers }}</div>\r\n              <div class=\"stat-label\">总用户数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon case-icon\">\r\n              <i class=\"el-icon-folder\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalCases }}</div>\r\n              <div class=\"stat-label\">案件总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon contract-icon\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalContracts }}</div>\r\n              <div class=\"stat-label\">合同数量</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon revenue-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ stats.totalRevenue }}</div>\r\n              <div class=\"stat-label\">总收入</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +22%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 左侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"16\" :lg=\"16\" :xl=\"16\">\r\n        <!-- 图表区域 -->\r\n        <div class=\"chart-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">业务数据趋势</span>\r\n              <div class=\"chart-controls\">\r\n                <el-radio-group v-model=\"chartPeriod\" size=\"small\">\r\n                  <el-radio-button label=\"week\">本周</el-radio-button>\r\n                  <el-radio-button label=\"month\">本月</el-radio-button>\r\n                  <el-radio-button label=\"year\">本年</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"chart-container\">\r\n              <div class=\"chart-placeholder\">\r\n                <i class=\"el-icon-data-line chart-icon\"></i>\r\n                <p>数据图表区域</p>\r\n                <p class=\"chart-desc\">这里可以集成 ECharts 或其他图表库显示业务数据趋势</p>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 最近活动 -->\r\n        <div class=\"activity-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">最近活动</span>\r\n              <el-button type=\"text\" @click=\"viewAllActivities\">查看全部</el-button>\r\n            </div>\r\n            <div class=\"activity-list\">\r\n              <div\r\n                v-for=\"activity in recentActivities\"\r\n                :key=\"activity.id\"\r\n                class=\"activity-item\"\r\n              >\r\n                <div class=\"activity-avatar\">\r\n                  <i :class=\"activity.icon\" :style=\"{ color: activity.color }\"></i>\r\n                </div>\r\n                <div class=\"activity-content\">\r\n                  <div class=\"activity-title\">{{ activity.title }}</div>\r\n                  <div class=\"activity-desc\">{{ activity.description }}</div>\r\n                  <div class=\"activity-time\">{{ activity.time }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 右侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n        <!-- 快捷操作 -->\r\n        <div class=\"quick-actions-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">快捷操作</span>\r\n            </div>\r\n            <div class=\"quick-actions\">\r\n              <div\r\n                v-for=\"action in quickActions\"\r\n                :key=\"action.id\"\r\n                class=\"quick-action-item\"\r\n                @click=\"handleQuickAction(action.action)\"\r\n              >\r\n                <div class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\r\n                  <i :class=\"action.icon\"></i>\r\n                </div>\r\n                <div class=\"action-content\">\r\n                  <div class=\"action-title\">{{ action.title }}</div>\r\n                  <div class=\"action-desc\">{{ action.description }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 待办事项 -->\r\n        <div class=\"todo-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">待办事项</span>\r\n              <el-badge :value=\"todoList.filter(item => !item.completed).length\" class=\"todo-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllTodos\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"todo-list\">\r\n              <div\r\n                v-for=\"todo in todoList.slice(0, 5)\"\r\n                :key=\"todo.id\"\r\n                class=\"todo-item\"\r\n                :class=\"{ completed: todo.completed }\"\r\n              >\r\n                <el-checkbox\r\n                  v-model=\"todo.completed\"\r\n                  @change=\"handleTodoChange(todo)\"\r\n                >\r\n                  {{ todo.title }}\r\n                </el-checkbox>\r\n                <div class=\"todo-priority\" :class=\"todo.priority\">\r\n                  {{ getPriorityText(todo.priority) }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 系统通知 -->\r\n        <div class=\"notification-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统通知</span>\r\n              <el-badge :value=\"notifications.filter(item => !item.read).length\" class=\"notification-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllNotifications\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"notification-list\">\r\n              <div\r\n                v-for=\"notification in notifications.slice(0, 3)\"\r\n                :key=\"notification.id\"\r\n                class=\"notification-item\"\r\n                :class=\"{ unread: !notification.read }\"\r\n                @click=\"markAsRead(notification)\"\r\n              >\r\n                <div class=\"notification-content\">\r\n                  <div class=\"notification-title\">{{ notification.title }}</div>\r\n                  <div class=\"notification-time\">{{ notification.time }}</div>\r\n                </div>\r\n                <div v-if=\"!notification.read\" class=\"notification-dot\"></div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- System Monitor -->\r\n        <div class=\"system-monitor-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统监控</span>\r\n            </div>\r\n            <div class=\"system-monitor-content\">\r\n              <system-monitor></system-monitor>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport SystemMonitor from '@/components/SystemMonitor.vue'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: {\r\n    SystemMonitor\r\n  },\r\n  data() {\r\n    return {\r\n      chartPeriod: 'month',\r\n      loading: false,\r\n      stats: {\r\n        totalUsers: 0,\r\n        totalCases: 0,\r\n        totalContracts: 0,\r\n        totalRevenue: '0'\r\n      },\r\n      recentActivities: [],\r\n      quickActions: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-plus',\r\n          color: '#409EFF',\r\n          title: '新建案件',\r\n          description: '创建新的法律案件',\r\n          action: 'new-case'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document-add',\r\n          color: '#67C23A',\r\n          title: '新建合同',\r\n          description: '创建新的合同文档',\r\n          action: 'new-contract'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#E6A23C',\r\n          title: '添加客户',\r\n          description: '添加新的客户信息',\r\n          action: 'new-client'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-upload',\r\n          color: '#F56C6C',\r\n          title: '文件归档',\r\n          description: '上传并归档文件',\r\n          action: 'upload-file'\r\n        }\r\n      ],\r\n      todoList: [\r\n        {\r\n          id: 1,\r\n          title: '审核张三的合同申请',\r\n          completed: false,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '回复李四的法律咨询',\r\n          completed: false,\r\n          priority: 'medium'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '准备明天的庭审材料',\r\n          completed: true,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '更新客户联系信息',\r\n          completed: false,\r\n          priority: 'low'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '整理本月财务报表',\r\n          completed: false,\r\n          priority: 'medium'\r\n        }\r\n      ],\r\n      notifications: [\r\n        {\r\n          id: 1,\r\n          title: '系统维护通知',\r\n          time: '今天 14:30',\r\n          read: false\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '新版本更新',\r\n          time: '昨天 16:20',\r\n          read: false\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '数据备份完成',\r\n          time: '昨天 09:15',\r\n          read: true\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDashboardData()\r\n  },\r\n  methods: {\r\n    // 加载仪表板数据\r\n    async loadDashboardData() {\r\n      this.loading = true\r\n      try {\r\n        await Promise.all([\r\n          this.loadStats(),\r\n          this.loadActivities(),\r\n          this.loadTodos(),\r\n          this.loadQuickActions(),\r\n          this.loadNotifications()\r\n        ])\r\n      } catch (error) {\r\n        console.error('加载仪表板数据失败:', error)\r\n        this.$message.error('加载数据失败，请刷新页面重试')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStats() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getStats')\r\n        if (response.code === 200) {\r\n          const data = response.data\r\n          this.stats = {\r\n            totalUsers: data.totalUsers || 0,\r\n            totalCases: data.totalDebts || 0, // 债务数作为案件数\r\n            totalContracts: data.totalOrders || 0, // 订单数作为合同数\r\n            totalRevenue: data.totalRevenue || '0'\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载最近活动\r\n    async loadActivities() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getActivities')\r\n        if (response.code === 200) {\r\n          this.recentActivities = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载活动数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载待办事项\r\n    async loadTodos() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getTodos')\r\n        if (response.code === 200) {\r\n          this.todoList = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载待办事项失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载快捷操作\r\n    async loadQuickActions() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getQuickActions')\r\n        if (response.code === 200) {\r\n          const actions = response.data || []\r\n          // 转换为前端需要的格式\r\n          this.quickActions = actions.map(action => ({\r\n            id: action.title,\r\n            icon: action.icon,\r\n            color: action.color,\r\n            title: action.title,\r\n            description: `当前数量: ${action.count}`,\r\n            action: action.url\r\n          }))\r\n        }\r\n      } catch (error) {\r\n        console.error('加载快捷操作失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载系统通知\r\n    async loadNotifications() {\r\n      try {\r\n        const response = await this.getRequest('/dashboard/getNotifications')\r\n        if (response.code === 200) {\r\n          this.notifications = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载通知失败:', error)\r\n      }\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date()\r\n      const options = {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric',\r\n        weekday: 'long'\r\n      }\r\n      return now.toLocaleDateString('zh-CN', options)\r\n    },\r\n\r\n    handleQuickAction(action) {\r\n      // 如果action是URL路径，直接跳转\r\n      if (action.startsWith('/')) {\r\n        this.$router.push(action)\r\n        return\r\n      }\r\n\r\n      // 兼容原有的action处理\r\n      switch (action) {\r\n        case 'new-case':\r\n          this.$router.push('/debts')\r\n          break\r\n        case 'new-contract':\r\n          this.$router.push('/dingdan')\r\n          break\r\n        case 'new-client':\r\n          this.$router.push('/user')\r\n          break\r\n        case 'upload-file':\r\n          this.$router.push('/archive/file')\r\n          break\r\n        default:\r\n          this.$message.info(`执行操作: ${action}`)\r\n      }\r\n    },\r\n\r\n    viewAllActivities() {\r\n      // 跳转到活动日志页面（如果有的话）\r\n      this.$message.info('活动日志功能开发中')\r\n    },\r\n\r\n    viewAllTodos() {\r\n      // 可以创建一个专门的待办事项管理页面，或者跳转到相关管理页面\r\n      this.$router.push('/todo/list')\r\n    },\r\n\r\n    viewAllNotifications() {\r\n      // 可以创建一个专门的通知管理页面\r\n      this.$router.push('/notifications')\r\n    },\r\n\r\n    async handleTodoChange(todo) {\r\n      try {\r\n        const response = await this.postRequest('/dashboard/updateTodo', {\r\n          id: todo.id,\r\n          completed: todo.completed\r\n        })\r\n        if (response.code === 200) {\r\n          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\r\n        }\r\n      } catch (error) {\r\n        console.error('更新待办事项失败:', error)\r\n        // 回滚状态\r\n        todo.completed = !todo.completed\r\n        this.$message.error('更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    async markAsRead(notification) {\r\n      try {\r\n        const response = await this.postRequest('/dashboard/markNotificationRead', {\r\n          id: notification.id\r\n        })\r\n        if (response.code === 200) {\r\n          notification.read = true\r\n          this.$message.success('通知已标记为已读')\r\n        }\r\n      } catch (error) {\r\n        console.error('标记通知失败:', error)\r\n        this.$message.error('操作失败，请重试')\r\n      }\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const map = {\r\n        high: '高',\r\n        medium: '中',\r\n        low: '低'\r\n      }\r\n      return map[priority] || '中'\r\n    },\r\n\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.loadDashboardData()\r\n      this.$message.success('数据已刷新')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 欢迎区域 */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  padding: 30px;\r\n  margin-bottom: 20px;\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n.case-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n.contract-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n.revenue-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.chart-section,\r\n.activity-section,\r\n.quick-actions-section,\r\n.todo-section,\r\n.notification-section,\r\n.system-monitor-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 图表区域 */\r\n.chart-container {\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  text-align: center;\r\n  color: #95a5a6;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.chart-desc {\r\n  margin: 8px 0 0 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 活动列表 */\r\n.activity-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.activity-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.activity-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.activity-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.activity-content {\r\n  flex: 1;\r\n}\r\n\r\n.activity-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-time {\r\n  color: #bdc3c7;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 快捷操作 */\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-action-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  background-color: #f8f9fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quick-action-item:hover {\r\n  background-color: #e9ecef;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.action-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.action-desc {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 待办事项 */\r\n.todo-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.todo-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.todo-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.todo-item.completed {\r\n  opacity: 0.6;\r\n}\r\n\r\n.todo-priority {\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.todo-priority.high {\r\n  background-color: #fee;\r\n  color: #e74c3c;\r\n}\r\n\r\n.todo-priority.medium {\r\n  background-color: #fff3cd;\r\n  color: #f39c12;\r\n}\r\n\r\n.todo-priority.low {\r\n  background-color: #d4edda;\r\n  color: #27ae60;\r\n}\r\n\r\n/* 通知列表 */\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.notification-item:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.notification-item.unread {\r\n  background-color: #e3f2fd;\r\n}\r\n\r\n.notification-content {\r\n  flex: 1;\r\n}\r\n\r\n.notification-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-time {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #409EFF;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .welcome-section {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 20px;\r\n  }\r\n\r\n  .welcome-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 12px;\r\n  }\r\n}\r\n</style> "], "mappings": ";AA8OA,OAAAA,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,WAAA;MACAC,OAAA;MACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;QACAC,YAAA;MACA;MACAC,gBAAA;MACAC,YAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,MAAA;MACA,GACA;QACAL,EAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,MAAA;MACA,EACA;MACAC,QAAA,GACA;QACAN,EAAA;QACAG,KAAA;QACAI,SAAA;QACAC,QAAA;MACA,GACA;QACAR,EAAA;QACAG,KAAA;QACAI,SAAA;QACAC,QAAA;MACA,GACA;QACAR,EAAA;QACAG,KAAA;QACAI,SAAA;QACAC,QAAA;MACA,GACA;QACAR,EAAA;QACAG,KAAA;QACAI,SAAA;QACAC,QAAA;MACA,GACA;QACAR,EAAA;QACAG,KAAA;QACAI,SAAA;QACAC,QAAA;MACA,EACA;MACAC,aAAA,GACA;QACAT,EAAA;QACAG,KAAA;QACAO,IAAA;QACAC,IAAA;MACA,GACA;QACAX,EAAA;QACAG,KAAA;QACAO,IAAA;QACAC,IAAA;MACA,GACA;QACAX,EAAA;QACAG,KAAA;QACAO,IAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,kBAAA;MACA,KAAArB,OAAA;MACA;QACA,MAAAuB,OAAA,CAAAC,GAAA,EACA,KAAAC,SAAA,IACA,KAAAC,cAAA,IACA,KAAAC,SAAA,IACA,KAAAC,gBAAA,IACA,KAAAC,iBAAA,GACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAA9B,OAAA;MACA;IACA;IAEA;IACA,MAAAyB,UAAA;MACA;QACA,MAAAQ,QAAA,cAAAC,UAAA;QACA,IAAAD,QAAA,CAAAE,IAAA;UACA,MAAArC,IAAA,GAAAmC,QAAA,CAAAnC,IAAA;UACA,KAAAG,KAAA;YACAC,UAAA,EAAAJ,IAAA,CAAAI,UAAA;YACAC,UAAA,EAAAL,IAAA,CAAAsC,UAAA;YAAA;YACAhC,cAAA,EAAAN,IAAA,CAAAuC,WAAA;YAAA;YACAhC,YAAA,EAAAP,IAAA,CAAAO,YAAA;UACA;QACA;MACA,SAAAyB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAJ,eAAA;MACA;QACA,MAAAO,QAAA,cAAAC,UAAA;QACA,IAAAD,QAAA,CAAAE,IAAA;UACA,KAAA7B,gBAAA,GAAA2B,QAAA,CAAAnC,IAAA;QACA;MACA,SAAAgC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAH,UAAA;MACA;QACA,MAAAM,QAAA,cAAAC,UAAA;QACA,IAAAD,QAAA,CAAAE,IAAA;UACA,KAAArB,QAAA,GAAAmB,QAAA,CAAAnC,IAAA;QACA;MACA,SAAAgC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAF,iBAAA;MACA;QACA,MAAAK,QAAA,cAAAC,UAAA;QACA,IAAAD,QAAA,CAAAE,IAAA;UACA,MAAAG,OAAA,GAAAL,QAAA,CAAAnC,IAAA;UACA;UACA,KAAAS,YAAA,GAAA+B,OAAA,CAAAC,GAAA,CAAA1B,MAAA;YACAL,EAAA,EAAAK,MAAA,CAAAF,KAAA;YACAF,IAAA,EAAAI,MAAA,CAAAJ,IAAA;YACAC,KAAA,EAAAG,MAAA,CAAAH,KAAA;YACAC,KAAA,EAAAE,MAAA,CAAAF,KAAA;YACAC,WAAA,WAAAC,MAAA,CAAA2B,KAAA;YACA3B,MAAA,EAAAA,MAAA,CAAA4B;UACA;QACA;MACA,SAAAX,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAD,kBAAA;MACA;QACA,MAAAI,QAAA,cAAAC,UAAA;QACA,IAAAD,QAAA,CAAAE,IAAA;UACA,KAAAlB,aAAA,GAAAgB,QAAA,CAAAnC,IAAA;QACA;MACA,SAAAgC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACA;IACA;IAEAY,eAAA;MACA,MAAAC,GAAA,OAAAC,IAAA;MACA,MAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;MACA;MACA,OAAAN,GAAA,CAAAO,kBAAA,UAAAL,OAAA;IACA;IAEAM,kBAAAtC,MAAA;MACA;MACA,IAAAA,MAAA,CAAAuC,UAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAzC,MAAA;QACA;MACA;;MAEA;MACA,QAAAA,MAAA;QACA;UACA,KAAAwC,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAtB,QAAA,CAAAuB,IAAA,UAAA1C,MAAA;MACA;IACA;IAEA2C,kBAAA;MACA;MACA,KAAAxB,QAAA,CAAAuB,IAAA;IACA;IAEAE,aAAA;MACA;MACA,KAAAJ,OAAA,CAAAC,IAAA;IACA;IAEAI,qBAAA;MACA;MACA,KAAAL,OAAA,CAAAC,IAAA;IACA;IAEA,MAAAK,iBAAAC,IAAA;MACA;QACA,MAAA3B,QAAA,cAAA4B,WAAA;UACArD,EAAA,EAAAoD,IAAA,CAAApD,EAAA;UACAO,SAAA,EAAA6C,IAAA,CAAA7C;QACA;QACA,IAAAkB,QAAA,CAAAE,IAAA;UACA,KAAAH,QAAA,CAAA8B,OAAA,CAAAF,IAAA,CAAA7C,SAAA;QACA;MACA,SAAAe,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;QACA8B,IAAA,CAAA7C,SAAA,IAAA6C,IAAA,CAAA7C,SAAA;QACA,KAAAiB,QAAA,CAAAF,KAAA;MACA;IACA;IAEA,MAAAiC,WAAAC,YAAA;MACA;QACA,MAAA/B,QAAA,cAAA4B,WAAA;UACArD,EAAA,EAAAwD,YAAA,CAAAxD;QACA;QACA,IAAAyB,QAAA,CAAAE,IAAA;UACA6B,YAAA,CAAA7C,IAAA;UACA,KAAAa,QAAA,CAAA8B,OAAA;QACA;MACA,SAAAhC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEAmC,gBAAAjD,QAAA;MACA,MAAAuB,GAAA;QACA2B,IAAA;QACAC,MAAA;QACAC,GAAA;MACA;MACA,OAAA7B,GAAA,CAAAvB,QAAA;IACA;IAEA;IACAqD,YAAA;MACA,KAAAhD,iBAAA;MACA,KAAAW,QAAA,CAAA8B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}