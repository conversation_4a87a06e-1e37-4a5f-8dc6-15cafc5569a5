{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\src\\api\\archive.js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\api\\archive.js", "mtime": 1748427382967}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8qKg0KICog6I635Y+W5paH5Lu25YiX6KGoDQogKiBAcGFyYW0ge09iamVjdH0gcGFyYW1zIOafpeivouWPguaVsA0KICogQHJldHVybnMge1Byb21pc2V9IOaWh+S7tuWIl+ihqA0KICovCmV4cG9ydCBmdW5jdGlvbiBnZXRGaWxlTGlzdChwYXJhbXMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXJjaGl2ZS9maWxlcycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zCiAgfSk7Cn0KCi8qKg0KICog5LiK5Lyg5paH5Lu2DQogKiBAcGFyYW0ge0Zvcm1EYXRhfSBmb3JtRGF0YSDmlofku7bmlbDmja4NCiAqIEBwYXJhbSB7RnVuY3Rpb259IG9uUHJvZ3Jlc3Mg5LiK5Lyg6L+b5bqm5Zue6LCDDQogKiBAcmV0dXJucyB7UHJvbWlzZX0g5LiK5Lyg57uT5p6cDQogKi8KZXhwb3J0IGZ1bmN0aW9uIHVwbG9hZEZpbGUoZm9ybURhdGEsIG9uUHJvZ3Jlc3MpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXJjaGl2ZS91cGxvYWQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBmb3JtRGF0YSwKICAgIGhlYWRlcnM6IHsKICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJwogICAgfSwKICAgIG9uVXBsb2FkUHJvZ3Jlc3M6IG9uUHJvZ3Jlc3MKICB9KTsKfQoKLyoqDQogKiDliKDpmaTmlofku7YNCiAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWQg5paH5Lu2SUQNCiAqIEByZXR1cm5zIHtQcm9taXNlfSDliKDpmaTnu5PmnpwNCiAqLwpleHBvcnQgZnVuY3Rpb24gZGVsZXRlRmlsZShmaWxlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGAvYXJjaGl2ZS9maWxlcy8ke2ZpbGVJZH1gLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovKioNCiAqIOaJuemHj+WIoOmZpOaWh+S7tg0KICogQHBhcmFtIHtBcnJheX0gZmlsZUlkcyDmlofku7ZJROaVsOe7hA0KICogQHJldHVybnMge1Byb21pc2V9IOWIoOmZpOe7k+aenA0KICovCmV4cG9ydCBmdW5jdGlvbiBiYXRjaERlbGV0ZUZpbGVzKGZpbGVJZHMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXJjaGl2ZS9maWxlcy9iYXRjaCcsCiAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgZGF0YTogewogICAgICBmaWxlSWRzCiAgICB9CiAgfSk7Cn0KCi8qKg0KICog5LiL6L295paH5Lu2DQogKiBAcGFyYW0ge3N0cmluZ30gZmlsZUlkIOaWh+S7tklEDQogKiBAcmV0dXJucyB7UHJvbWlzZX0g5paH5Lu25rWBDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGRvd25sb2FkRmlsZShmaWxlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGAvYXJjaGl2ZS9maWxlcy8ke2ZpbGVJZH0vZG93bmxvYWRgLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InCiAgfSk7Cn0KCi8qKg0KICog5om56YeP5LiL6L295paH5Lu2DQogKiBAcGFyYW0ge0FycmF5fSBmaWxlSWRzIOaWh+S7tklE5pWw57uEDQogKiBAcmV0dXJucyB7UHJvbWlzZX0g5paH5Lu25rWBDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGJhdGNoRG93bmxvYWRGaWxlcyhmaWxlSWRzKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FyY2hpdmUvZmlsZXMvYmF0Y2gvZG93bmxvYWQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiB7CiAgICAgIGZpbGVJZHMKICAgIH0sCiAgICByZXNwb25zZVR5cGU6ICdibG9iJwogIH0pOwp9CgovKioNCiAqIOiOt+WPluaWh+S7tumihOiniFVSTA0KICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZCDmlofku7ZJRA0KICogQHJldHVybnMge1Byb21pc2V9IOmihOiniFVSTA0KICovCmV4cG9ydCBmdW5jdGlvbiBnZXRQcmV2aWV3VXJsKGZpbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYC9hcmNoaXZlL2ZpbGVzLyR7ZmlsZUlkfS9wcmV2aWV3YCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLyoqDQogKiDmm7TmlrDmlofku7bkv6Hmga8NCiAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWQg5paH5Lu2SUQNCiAqIEBwYXJhbSB7T2JqZWN0fSBkYXRhIOaWh+S7tuS/oeaBrw0KICogQHJldHVybnMge1Byb21pc2V9IOabtOaWsOe7k+aenA0KICovCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVGaWxlSW5mbyhmaWxlSWQsIGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGAvYXJjaGl2ZS9maWxlcy8ke2ZpbGVJZH1gLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGEKICB9KTsKfQoKLyoqDQogKiDmibnph4/lvZLmoaPmlofku7YNCiAqIEBwYXJhbSB7QXJyYXl9IGZpbGVJZHMg5paH5Lu2SUTmlbDnu4QNCiAqIEBwYXJhbSB7c3RyaW5nfSBjYXRlZ29yeSDlvZLmoaPliIbnsbsNCiAqIEByZXR1cm5zIHtQcm9taXNlfSDlvZLmoaPnu5PmnpwNCiAqLwpleHBvcnQgZnVuY3Rpb24gYmF0Y2hBcmNoaXZlRmlsZXMoZmlsZUlkcywgY2F0ZWdvcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXJjaGl2ZS9maWxlcy9iYXRjaC9hcmNoaXZlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogewogICAgICBmaWxlSWRzLAogICAgICBjYXRlZ29yeQogICAgfQogIH0pOwp9CgovKioNCiAqIOiOt+WPluaWh+S7tuWIhuexu+WIl+ihqA0KICogQHJldHVybnMge1Byb21pc2V9IOWIhuexu+WIl+ihqA0KICovCmV4cG9ydCBmdW5jdGlvbiBnZXRGaWxlQ2F0ZWdvcmllcygpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXJjaGl2ZS9jYXRlZ29yaWVzJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "getFileList", "params", "url", "method", "uploadFile", "formData", "onProgress", "data", "headers", "onUploadProgress", "deleteFile", "fileId", "batchDeleteFiles", "fileIds", "downloadFile", "responseType", "batchDownloadFiles", "getPreviewUrl", "updateFileInfo", "batchArchiveFiles", "category", "getFileCategories"], "sources": ["D:/Gitee/xinqianduan/src/api/archive.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n/**\r\n * 获取文件列表\r\n * @param {Object} params 查询参数\r\n * @returns {Promise} 文件列表\r\n */\r\nexport function getFileList(params) {\r\n  return request({\r\n    url: '/archive/files',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n/**\r\n * 上传文件\r\n * @param {FormData} formData 文件数据\r\n * @param {Function} onProgress 上传进度回调\r\n * @returns {Promise} 上传结果\r\n */\r\nexport function uploadFile(formData, onProgress) {\r\n  return request({\r\n    url: '/archive/upload',\r\n    method: 'post',\r\n    data: formData,\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data'\r\n    },\r\n    onUploadProgress: onProgress\r\n  })\r\n}\r\n\r\n/**\r\n * 删除文件\r\n * @param {string} fileId 文件ID\r\n * @returns {Promise} 删除结果\r\n */\r\nexport function deleteFile(fileId) {\r\n  return request({\r\n    url: `/archive/files/${fileId}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n/**\r\n * 批量删除文件\r\n * @param {Array} fileIds 文件ID数组\r\n * @returns {Promise} 删除结果\r\n */\r\nexport function batchDeleteFiles(fileIds) {\r\n  return request({\r\n    url: '/archive/files/batch',\r\n    method: 'delete',\r\n    data: { fileIds }\r\n  })\r\n}\r\n\r\n/**\r\n * 下载文件\r\n * @param {string} fileId 文件ID\r\n * @returns {Promise} 文件流\r\n */\r\nexport function downloadFile(fileId) {\r\n  return request({\r\n    url: `/archive/files/${fileId}/download`,\r\n    method: 'get',\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n/**\r\n * 批量下载文件\r\n * @param {Array} fileIds 文件ID数组\r\n * @returns {Promise} 文件流\r\n */\r\nexport function batchDownloadFiles(fileIds) {\r\n  return request({\r\n    url: '/archive/files/batch/download',\r\n    method: 'post',\r\n    data: { fileIds },\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n/**\r\n * 获取文件预览URL\r\n * @param {string} fileId 文件ID\r\n * @returns {Promise} 预览URL\r\n */\r\nexport function getPreviewUrl(fileId) {\r\n  return request({\r\n    url: `/archive/files/${fileId}/preview`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 更新文件信息\r\n * @param {string} fileId 文件ID\r\n * @param {Object} data 文件信息\r\n * @returns {Promise} 更新结果\r\n */\r\nexport function updateFileInfo(fileId, data) {\r\n  return request({\r\n    url: `/archive/files/${fileId}`,\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\n/**\r\n * 批量归档文件\r\n * @param {Array} fileIds 文件ID数组\r\n * @param {string} category 归档分类\r\n * @returns {Promise} 归档结果\r\n */\r\nexport function batchArchiveFiles(fileIds, category) {\r\n  return request({\r\n    url: '/archive/files/batch/archive',\r\n    method: 'post',\r\n    data: { fileIds, category }\r\n  })\r\n}\r\n\r\n/**\r\n * 获取文件分类列表\r\n * @returns {Promise} 分类列表\r\n */\r\nexport function getFileCategories() {\r\n  return request({\r\n    url: '/archive/categories',\r\n    method: 'get'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,UAAUA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC/C,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEF,QAAQ;IACdG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB,CAAC;IACDC,gBAAgB,EAAEH;EACpB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkBS,MAAM,EAAE;IAC/BR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,gBAAgBA,CAACC,OAAO,EAAE;EACxC,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,QAAQ;IAChBI,IAAI,EAAE;MAAEM;IAAQ;EAClB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACH,MAAM,EAAE;EACnC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkBS,MAAM,WAAW;IACxCR,MAAM,EAAE,KAAK;IACbY,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACH,OAAO,EAAE;EAC1C,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAE;MAAEM;IAAQ,CAAC;IACjBE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACN,MAAM,EAAE;EACpC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkBS,MAAM,UAAU;IACvCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,cAAcA,CAACP,MAAM,EAAEJ,IAAI,EAAE;EAC3C,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkBS,MAAM,EAAE;IAC/BR,MAAM,EAAE,KAAK;IACbI;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,iBAAiBA,CAACN,OAAO,EAAEO,QAAQ,EAAE;EACnD,OAAOrB,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAE;MAAEM,OAAO;MAAEO;IAAS;EAC5B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,OAAOtB,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}