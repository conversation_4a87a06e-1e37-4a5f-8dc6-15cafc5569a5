{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue", "mtime": 1748463119363}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gQCBpcyBhbiBhbGlhcyB0byAvc3JjCmltcG9ydCBFZGl0b3JCYXIgZnJvbSAiL3NyYy9jb21wb25lbnRzL3dhbmdFbmR1aXQudnVlIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJsaXN0IiwKICBjb21wb25lbnRzOiB7CiAgICBFZGl0b3JCYXIKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhbGxTaXplOiAibWluaSIsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0b3RhbDogMSwKICAgICAgcGFnZTogMSwKICAgICAgc2l6ZTogMTIsCiAgICAgIHNlYXJjaDogewogICAgICAgIGtleXdvcmQ6ICIiLAogICAgICAgIGlzX2ZyZWU6ICIiLAogICAgICAgIGlzX2hvdDogIiIKICAgICAgfSwKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgdXJsOiAiL2tlY2hlbmcvIiwKICAgICAgdGl0bGU6ICLor77nqIsiLAogICAgICBpbmZvOiB7fSwKICAgICAgZmlsZWQ6ICIiLAogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIHNob3dfaW1hZ2U6ICIiLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgdmlld01vZGU6ICd0YWJsZScsCiAgICAgIC8vIOinhuWbvuaooeW8j++8mnRhYmxlIHwgY2FyZAogICAgICBydWxlRm9ybTogewogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICBpc19udW06IDAKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICB0aXRsZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+Whq+WGmeagh+mimCIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBwaWNfcGF0aDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+S4iuS8oOWwgemdoiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBmaWxlX3BhdGg6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fkuIrkvKDop4bpopEiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgZm9ybUxhYmVsV2lkdGg6ICIxMjBweCIKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLy8g5YWN6LS56K++56iL5pWw6YePCiAgICBmcmVlQ291bnQoKSB7CiAgICAgIHJldHVybiB0aGlzLmxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc19mcmVlID09PSAxKS5sZW5ndGg7CiAgICB9LAogICAgLy8g5LuY6LS56K++56iL5pWw6YePCiAgICBwYWlkQ291bnQoKSB7CiAgICAgIHJldHVybiB0aGlzLmxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc19mcmVlID09PSAyKS5sZW5ndGg7CiAgICB9LAogICAgLy8g54Ot6Zeo6K++56iL5pWw6YePCiAgICBob3RDb3VudCgpIHsKICAgICAgcmV0dXJuIHRoaXMubGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmlzX2hvdCA9PT0gMSkubGVuZ3RoOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5riF56m65pCc57SiCiAgICBjbGVhclNlYXJjaCgpIHsKICAgICAgdGhpcy5zZWFyY2ggPSB7CiAgICAgICAga2V5d29yZDogIiIsCiAgICAgICAgaXNfZnJlZTogIiIsCiAgICAgICAgaXNfaG90OiAiIgogICAgICB9OwogICAgICB0aGlzLnNlYXJjaERhdGEoKTsKICAgIH0sCiAgICAvLyDlpITnkIbmjpLluo/lj5jljJYKICAgIGhhbmRsZVNvcnRDaGFuZ2UoewogICAgICBjb2x1bW4sCiAgICAgIHByb3AsCiAgICAgIG9yZGVyCiAgICB9KSB7CiAgICAgIGNvbnNvbGUubG9nKCfmjpLluo/lj5jljJY6JywgewogICAgICAgIGNvbHVtbiwKICAgICAgICBwcm9wLAogICAgICAgIG9yZGVyCiAgICAgIH0pOwogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDmjpLluo/pgLvovpEKICAgIH0sCiAgICBjaGFuZ2VGaWxlKGZpbGVkKSB7CiAgICAgIHRoaXMuZmlsZWQgPSBmaWxlZDsKICAgICAgY29uc29sZS5sb2codGhpcy5maWxlZCk7CiAgICB9LAogICAgZWRpdERhdGEoaWQpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgaWYgKGlkICE9IDApIHsKICAgICAgICB0aGlzLmdldEluZm8oaWQpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucnVsZUZvcm0gPSB7CiAgICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgICBkZXNjOiAiIiwKICAgICAgICAgIGlzX2ZyZWU6IDIsCiAgICAgICAgICBmaWxlX3BhdGg6ICIiLAogICAgICAgICAgcGljX3BhdGg6ICIiCiAgICAgICAgfTsKICAgICAgfQogICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgZ2V0SW5mbyhpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KF90aGlzLnVybCArICJyZWFkP2lkPSIgKyBpZCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcCkgewogICAgICAgICAgX3RoaXMucnVsZUZvcm0gPSByZXNwLmRhdGE7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBkZWxEYXRhKGluZGV4LCBpZCkgewogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbliKDpmaTor6Xkv6Hmga8/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZGVsZXRlUmVxdWVzdCh0aGlzLnVybCArICJkZWxldGU/aWQ9IiArIGlkKS50aGVuKHJlc3AgPT4gewogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5saXN0LnNwbGljZShpbmRleCwgMSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICBtZXNzYWdlOiAi5Y+W5raI5Yig6ZmkISIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgcmVmdWxzaCgpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKDApOwogICAgfSwKICAgIHNlYXJjaERhdGEoKSB7CiAgICAgIHRoaXMucGFnZSA9IDE7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIGdldERhdGEoKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmxvYWRpbmcgPSB0cnVlOwoKICAgICAgLy8g5L2/55So5rWL6K+V5pWw5o2uCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKCiAgICAgICAgLy8g5qih5ouf5pCc57Si6L+H5rukCiAgICAgICAgbGV0IGZpbHRlcmVkTGlzdCA9IFt7CiAgICAgICAgICBpZDogMSwKICAgICAgICAgIHRpdGxlOiAiVnVlLmpzIOS7juWFpemXqOWIsOeyvumAmiIsCiAgICAgICAgICBkZXNjOiAi5YWo6Z2i5a2m5LmgVnVlLmpz5qGG5p6277yM5YyF5ous57uE5Lu25byA5Y+R44CB6Lev55Sx566h55CG44CB54q25oCB566h55CG562J5qC45b+D5qaC5b+1IiwKICAgICAgICAgIHByaWNlOiAxOTksCiAgICAgICAgICBpc19mcmVlOiAyLAogICAgICAgICAgaXNfaG90OiAxLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC80Q0FGNTAvd2hpdGU/dGV4dD1WdWUuanMiLAogICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTE1IDEwOjMwOjAwIgogICAgICAgIH0sIHsKICAgICAgICAgIGlkOiAyLAogICAgICAgICAgdGl0bGU6ICJSZWFjdCDlrp7miJjlvIDlj5HmlZnnqIsiLAogICAgICAgICAgZGVzYzogIua3seWFpeWtpuS5oFJlYWN05qGG5p6277yM5o6M5o+h546w5Luj5YmN56uv5byA5Y+R5oqA6IO9IiwKICAgICAgICAgIHByaWNlOiAyOTksCiAgICAgICAgICBpc19mcmVlOiAyLAogICAgICAgICAgaXNfaG90OiAxLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC8yMTk2RjMvd2hpdGU/dGV4dD1SZWFjdCIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMTQgMTQ6MjA6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDMsCiAgICAgICAgICB0aXRsZTogIkphdmFTY3JpcHQg5Z+656GA5YWl6ZeoIiwKICAgICAgICAgIGRlc2M6ICLpm7bln7rnoYDlrabkuaBKYXZhU2NyaXB057yW56iL6K+t6KiA77yM5Li65YmN56uv5byA5Y+R5omT5LiL5Z2a5a6e5Z+656GAIiwKICAgICAgICAgIHByaWNlOiAwLAogICAgICAgICAgaXNfZnJlZTogMSwKICAgICAgICAgIGlzX2hvdDogMCwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvRkY5ODAwL3doaXRlP3RleHQ9SmF2YVNjcmlwdCIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMTMgMDk6MTU6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDQsCiAgICAgICAgICB0aXRsZTogIk5vZGUuanMg5ZCO56uv5byA5Y+RIiwKICAgICAgICAgIGRlc2M6ICLlrabkuaDkvb/nlKhOb2RlLmpz6L+b6KGM5ZCO56uv5byA5Y+R77yM5YyF5ousRXhwcmVzc+ahhuaetuWSjOaVsOaNruW6k+aTjeS9nCIsCiAgICAgICAgICBwcmljZTogMzk5LAogICAgICAgICAgaXNfZnJlZTogMiwKICAgICAgICAgIGlzX2hvdDogMCwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvNENBRjUwL3doaXRlP3RleHQ9Tm9kZS5qcyIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMTIgMTY6NDU6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDUsCiAgICAgICAgICB0aXRsZTogIkNTUzMg5Yqo55S75LiO54m55pWIIiwKICAgICAgICAgIGRlc2M6ICLmjozmj6FDU1Mz6auY57qn54m55oCn77yM5Yib5bu654Kr6YW355qE572R6aG15Yqo55S75ZKM6KeG6KeJ5pWI5p6cIiwKICAgICAgICAgIHByaWNlOiAwLAogICAgICAgICAgaXNfZnJlZTogMSwKICAgICAgICAgIGlzX2hvdDogMSwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvRTkxRTYzL3doaXRlP3RleHQ9Q1NTMyIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMTEgMTE6MzA6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDYsCiAgICAgICAgICB0aXRsZTogIlR5cGVTY3JpcHQg6L+b6Zi25oyH5Y2XIiwKICAgICAgICAgIGRlc2M6ICLmt7HlhaXlrabkuaBUeXBlU2NyaXB077yM5o+Q5Y2HSmF2YVNjcmlwdOW8gOWPkeeahOexu+Wei+WuieWFqOaApyIsCiAgICAgICAgICBwcmljZTogMjQ5LAogICAgICAgICAgaXNfZnJlZTogMiwKICAgICAgICAgIGlzX2hvdDogMCwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvM0Y1MUI1L3doaXRlP3RleHQ9VHlwZVNjcmlwdCIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMTAgMTM6MjA6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDcsCiAgICAgICAgICB0aXRsZTogIkhUTUw1IOenu+WKqOerr+W8gOWPkSIsCiAgICAgICAgICBkZXNjOiAi5a2m5LmgSFRNTDXnp7vliqjnq6/lvIDlj5HmioDmnK/vvIzliJvlu7rlk43lupTlvI/np7vliqjlupTnlKgiLAogICAgICAgICAgcHJpY2U6IDAsCiAgICAgICAgICBpc19mcmVlOiAxLAogICAgICAgICAgaXNfaG90OiAwLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC9GRjU3MjIvd2hpdGU/dGV4dD1IVE1MNSIsCiAgICAgICAgICBjcmVhdGVfdGltZTogIjIwMjQtMDEtMDkgMTU6MTA6MDAiCiAgICAgICAgfSwgewogICAgICAgICAgaWQ6IDgsCiAgICAgICAgICB0aXRsZTogIuW+ruS/oeWwj+eoi+W6j+W8gOWPkeWunuaImCIsCiAgICAgICAgICBkZXNjOiAi5LuO6Zu25byA5aeL5a2m5Lmg5b6u5L+h5bCP56iL5bqP5byA5Y+R77yM5YyF5ous57uE5Lu25L2/55So44CBQVBJ6LCD55So562JIiwKICAgICAgICAgIHByaWNlOiAxOTksCiAgICAgICAgICBpc19mcmVlOiAyLAogICAgICAgICAgaXNfaG90OiAxLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC8wMEJDRDQvd2hpdGU/dGV4dD3lsI/nqIvluo8iLAogICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTA4IDEwOjAwOjAwIgogICAgICAgIH0sIHsKICAgICAgICAgIGlkOiA5LAogICAgICAgICAgdGl0bGU6ICLliY3nq6/lt6XnqIvljJblrp7ot7UiLAogICAgICAgICAgZGVzYzogIuWtpuS5oOeOsOS7o+WJjeerr+W3peeoi+WMluW3peWFt+WSjOa1geeoi++8jOaPkOWNh+W8gOWPkeaViOeOhyIsCiAgICAgICAgICBwcmljZTogMjk5LAogICAgICAgICAgaXNfZnJlZTogMiwKICAgICAgICAgIGlzX2hvdDogMCwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvOUMyN0IwL3doaXRlP3RleHQ95bel56iL5YyWIiwKICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wNyAxNDozMDowMCIKICAgICAgICB9LCB7CiAgICAgICAgICBpZDogMTAsCiAgICAgICAgICB0aXRsZTogIldlYuWuieWFqOWfuuehgCIsCiAgICAgICAgICBkZXNjOiAi5LqG6Kej5bi46KeB55qEV2Vi5a6J5YWo5ryP5rSe5ZKM6Ziy5oqk5o6q5pa977yM5L+d6Zqc5bqU55So5a6J5YWoIiwKICAgICAgICAgIHByaWNlOiAwLAogICAgICAgICAgaXNfZnJlZTogMSwKICAgICAgICAgIGlzX2hvdDogMCwKICAgICAgICAgIHBpY19wYXRoOiAiaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvNzk1NTQ4L3doaXRlP3RleHQ95a6J5YWoIiwKICAgICAgICAgIGNyZWF0ZV90aW1lOiAiMjAyNC0wMS0wNiAwOTo0NTowMCIKICAgICAgICB9LCB7CiAgICAgICAgICBpZDogMTEsCiAgICAgICAgICB0aXRsZTogIuaVsOaNruWPr+inhuWMluaKgOacryIsCiAgICAgICAgICBkZXNjOiAi5a2m5Lmg5L2/55SoRDMuanPjgIFFQ2hhcnRz562J5bel5YW35Yib5bu65pWw5o2u5Y+v6KeG5YyW5Zu+6KGoIiwKICAgICAgICAgIHByaWNlOiAzOTksCiAgICAgICAgICBpc19mcmVlOiAyLAogICAgICAgICAgaXNfaG90OiAxLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC82MDdEOEIvd2hpdGU/dGV4dD3lj6/op4bljJYiLAogICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTA1IDEyOjE1OjAwIgogICAgICAgIH0sIHsKICAgICAgICAgIGlkOiAxMiwKICAgICAgICAgIHRpdGxlOiAiUFdBIOa4kOi/m+W8j1dlYuW6lOeUqCIsCiAgICAgICAgICBkZXNjOiAi5a2m5LmgUFdB5oqA5pyv77yM5Yib5bu657G75Ly85Y6f55Sf5bqU55So5L2T6aqM55qEV2Vi5bqU55SoIiwKICAgICAgICAgIHByaWNlOiAxOTksCiAgICAgICAgICBpc19mcmVlOiAyLAogICAgICAgICAgaXNfaG90OiAwLAogICAgICAgICAgcGljX3BhdGg6ICJodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMC84QkMzNEEvd2hpdGU/dGV4dD1QV0EiLAogICAgICAgICAgY3JlYXRlX3RpbWU6ICIyMDI0LTAxLTA0IDE2OjIwOjAwIgogICAgICAgIH1dOwoKICAgICAgICAvLyDlupTnlKjmkJzntKLov4fmu6QKICAgICAgICBpZiAoX3RoaXMuc2VhcmNoLmtleXdvcmQpIHsKICAgICAgICAgIGZpbHRlcmVkTGlzdCA9IGZpbHRlcmVkTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnRpdGxlLmluY2x1ZGVzKF90aGlzLnNlYXJjaC5rZXl3b3JkKSB8fCBpdGVtLmRlc2MuaW5jbHVkZXMoX3RoaXMuc2VhcmNoLmtleXdvcmQpKTsKICAgICAgICB9CiAgICAgICAgaWYgKF90aGlzLnNlYXJjaC5pc19mcmVlICE9PSAiIikgewogICAgICAgICAgZmlsdGVyZWRMaXN0ID0gZmlsdGVyZWRMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uaXNfZnJlZSA9PT0gX3RoaXMuc2VhcmNoLmlzX2ZyZWUpOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXMuc2VhcmNoLmlzX2hvdCAhPT0gIiIpIHsKICAgICAgICAgIGZpbHRlcmVkTGlzdCA9IGZpbHRlcmVkTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmlzX2hvdCA9PT0gX3RoaXMuc2VhcmNoLmlzX2hvdCk7CiAgICAgICAgfQoKICAgICAgICAvLyDliIbpobXlpITnkIYKICAgICAgICBjb25zdCBzdGFydEluZGV4ID0gKF90aGlzLnBhZ2UgLSAxKSAqIF90aGlzLnNpemU7CiAgICAgICAgY29uc3QgZW5kSW5kZXggPSBzdGFydEluZGV4ICsgX3RoaXMuc2l6ZTsKICAgICAgICBfdGhpcy5saXN0ID0gZmlsdGVyZWRMaXN0LnNsaWNlKHN0YXJ0SW5kZXgsIGVuZEluZGV4KTsKICAgICAgICBfdGhpcy50b3RhbCA9IGZpbHRlcmVkTGlzdC5sZW5ndGg7CiAgICAgIH0sIDgwMCk7CgogICAgICAvLyDljp/lp4tBUEnosIPnlKjku6PnoIHvvIjms6jph4rmjonvvIkKICAgICAgLyoNCiAgICAgIF90aGlzDQogICAgICAgIC5wb3N0UmVxdWVzdCgNCiAgICAgICAgICBfdGhpcy51cmwgKyAiaW5kZXg/cGFnZT0iICsgX3RoaXMucGFnZSArICImc2l6ZT0iICsgX3RoaXMuc2l6ZSwNCiAgICAgICAgICBfdGhpcy5zZWFyY2gNCiAgICAgICAgKQ0KICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICBfdGhpcy5saXN0ID0gcmVzcC5kYXRhOw0KICAgICAgICAgICAgX3RoaXMudG90YWwgPSByZXNwLmNvdW50Ow0KICAgICAgICAgIH0NCiAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgICAgKi8KICAgIH0sCiAgICBzYXZlRGF0YSgpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlIiwgdGhpcy5ydWxlRm9ybSkudGhlbihyZXNwID0+IHsKICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLnNpemUgPSB2YWw7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgaGFuZGxlU3VjY2VzcyhyZXMpIHsKICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5LiK5Lyg5oiQ5YqfIik7CiAgICAgICAgdGhpcy5ydWxlRm9ybVt0aGlzLmZpbGVkXSA9IHJlcy5kYXRhLnVybDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICB9CiAgICB9LAogICAgc2hvd0ltYWdlKGZpbGUpIHsKICAgICAgdGhpcy5zaG93X2ltYWdlID0gZmlsZTsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBsZXQgdHlwZSA9IGZpbGUudHlwZTsKICAgICAgaWYgKHRoaXMuZmlsZWQgPT0gInBpY19wYXRoIikgewogICAgICAgIGNvbnN0IGlzVHlwZVRydWUgPSAvXmltYWdlXC8oanBlZ3xwbmd8anBnKSQvLnRlc3QodHlwZSk7CiAgICAgICAgaWYgKCFpc1R5cGVUcnVlKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDlm77niYfmoLzlvI/kuI3lr7khIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGlmICghZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gIm1wNCIgfHwgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJxbHYiIHx8ICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAicXN2IiB8fCAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gIm9nYSIgfHwgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJmbHYiIHx8ICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAiYXZpIiB8fCAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gIndtdiIgfHwgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJybXZiIikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHNob3dDbG9zZTogdHJ1ZSwKICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqScubXA0LC5xbHYsLnFzdiwub2dhLC5mbHYsLmF2aSwud212LC5ybXZiJ+aWh+S7tiIsCiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGRlbEltYWdlKGZpbGUsIGZpbGVOYW1lKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmdldFJlcXVlc3QoIi9VcGxvYWQvZGVsSW1hZ2U/ZmlsZU5hbWU9IiArIGZpbGUpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzLnJ1bGVGb3JtW2ZpbGVOYW1lXSA9ICIiOwogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfISIpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "is_free", "is_hot", "loading", "url", "title", "info", "filed", "dialogFormVisible", "show_image", "dialogVisible", "viewMode", "ruleForm", "is_num", "rules", "required", "message", "trigger", "pic_path", "file_path", "form<PERSON>abe<PERSON><PERSON>", "computed", "freeCount", "filter", "item", "length", "paidCount", "hotCount", "mounted", "getData", "methods", "clearSearch", "searchData", "handleSortChange", "column", "prop", "order", "console", "log", "changeFile", "editData", "id", "_this", "getInfo", "desc", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "setTimeout", "filteredList", "price", "create_time", "includes", "startIndex", "endIndex", "slice", "saveData", "$refs", "validate", "valid", "postRequest", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "split", "showClose", "delImage", "fileName"], "sources": ["src/views/pages/shipin/kecheng.vue"], "sourcesContent": ["<template>\r\n  <div class=\"course-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-video-play\"></i>\r\n            课程列表\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和维护在线课程内容</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增课程\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入课程标题或关键词\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程类型</label>\r\n              <el-select\r\n                v-model=\"search.is_free\"\r\n                placeholder=\"请选择课程类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"免费课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"付费课程\" :value=\"2\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">热门推荐</label>\r\n              <el-select\r\n                v-model=\"search.is_hot\"\r\n                placeholder=\"请选择是否热门\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"热门课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"普通课程\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-video-play\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总课程数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon free\">\r\n              <i class=\"el-icon-present\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ freeCount }}</div>\r\n              <div class=\"stat-label\">免费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon paid\">\r\n              <i class=\"el-icon-coin\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ paidCount }}</div>\r\n              <div class=\"stat-label\">付费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon hot\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ hotCount }}</div>\r\n              <div class=\"stat-label\">热门课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 课程列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            课程列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"course-table\"\r\n            stripe\r\n            @sort-change=\"handleSortChange\"\r\n          >\r\n            <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-title-cell\">\r\n                  <div class=\"course-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"course-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-cover\">\r\n                  <img\r\n                    :src=\"scope.row.pic_path\"\r\n                    @click=\"showImage(scope.row.pic_path)\"\r\n                    class=\"cover-image\"\r\n                    :alt=\"scope.row.title\"\r\n                  />\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"price\" label=\"价格\" width=\"100\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"price-cell\">\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <span v-else class=\"price-amount\">¥{{ scope.row.price || 0 }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"status-cell\">\r\n                  <el-tag v-if=\"scope.row.is_hot === 1\" type=\"warning\" size=\"small\">\r\n                    <i class=\"el-icon-star-on\"></i>\r\n                    热门\r\n                  </el-tag>\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <el-tag v-else type=\"info\" size=\"small\">\r\n                    付费\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"course in list\" :key=\"course.id\" class=\"course-card-col\">\r\n              <div class=\"course-card\">\r\n                <div class=\"card-cover\" @click=\"showImage(course.pic_path)\">\r\n                  <img :src=\"course.pic_path\" :alt=\"course.title\" />\r\n                  <div class=\"cover-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-header\">\r\n                    <h3 class=\"card-title\" :title=\"course.title\">{{ course.title }}</h3>\r\n                    <div class=\"card-badges\">\r\n                      <el-tag v-if=\"course.is_hot === 1\" type=\"warning\" size=\"mini\">\r\n                        <i class=\"el-icon-star-on\"></i>\r\n                        热门\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-desc\" v-if=\"course.desc\">{{ course.desc }}</div>\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-price\">\r\n                      <el-tag v-if=\"course.is_free === 1\" type=\"success\" size=\"small\">\r\n                        免费课程\r\n                      </el-tag>\r\n                      <span v-else class=\"price\">¥{{ course.price || 0 }}</span>\r\n                    </div>\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ course.create_time }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-actions\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      size=\"small\"\r\n                      @click=\"editData(course.id)\"\r\n                      icon=\"el-icon-edit\"\r\n                      plain\r\n                    >\r\n                      编辑\r\n                    </el-button>\r\n                    <el-button\r\n                      type=\"danger\"\r\n                      size=\"small\"\r\n                      @click=\"delData(list.indexOf(course), course.id)\"\r\n                      icon=\"el-icon-delete\"\r\n                      plain\r\n                    >\r\n                      删除\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否免费\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"2\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"首页热门\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"0\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_free == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"课程视频\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"showImage(ruleForm.file_path)\"\r\n              >查看\r\n            </el-button> -->\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/kecheng/\",\r\n      title: \"课程\",\r\n      info: {},\r\n      filed: \"\",\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table | card\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传视频\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 免费课程数量\r\n    freeCount() {\r\n      return this.list.filter(item => item.is_free === 1).length;\r\n    },\r\n    // 付费课程数量\r\n    paidCount() {\r\n      return this.list.filter(item => item.is_free === 2).length;\r\n    },\r\n    // 热门课程数量\r\n    hotCount() {\r\n      return this.list.filter(item => item.is_hot === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理排序变化\r\n    handleSortChange({ column, prop, order }) {\r\n      console.log('排序变化:', { column, prop, order });\r\n      // 这里可以添加排序逻辑\r\n    },\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_free: 2,\r\n          file_path: \"\",\r\n          pic_path: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n\r\n        // 模拟搜索过滤\r\n        let filteredList = [\r\n          {\r\n            id: 1,\r\n            title: \"Vue.js 从入门到精通\",\r\n            desc: \"全面学习Vue.js框架，包括组件开发、路由管理、状态管理等核心概念\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Vue.js\",\r\n            create_time: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"React 实战开发教程\",\r\n            desc: \"深入学习React框架，掌握现代前端开发技能\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/2196F3/white?text=React\",\r\n            create_time: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"JavaScript 基础入门\",\r\n            desc: \"零基础学习JavaScript编程语言，为前端开发打下坚实基础\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF9800/white?text=JavaScript\",\r\n            create_time: \"2024-01-13 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"Node.js 后端开发\",\r\n            desc: \"学习使用Node.js进行后端开发，包括Express框架和数据库操作\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Node.js\",\r\n            create_time: \"2024-01-12 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"CSS3 动画与特效\",\r\n            desc: \"掌握CSS3高级特性，创建炫酷的网页动画和视觉效果\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/E91E63/white?text=CSS3\",\r\n            create_time: \"2024-01-11 11:30:00\"\r\n          },\r\n          {\r\n            id: 6,\r\n            title: \"TypeScript 进阶指南\",\r\n            desc: \"深入学习TypeScript，提升JavaScript开发的类型安全性\",\r\n            price: 249,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/3F51B5/white?text=TypeScript\",\r\n            create_time: \"2024-01-10 13:20:00\"\r\n          },\r\n          {\r\n            id: 7,\r\n            title: \"HTML5 移动端开发\",\r\n            desc: \"学习HTML5移动端开发技术，创建响应式移动应用\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF5722/white?text=HTML5\",\r\n            create_time: \"2024-01-09 15:10:00\"\r\n          },\r\n          {\r\n            id: 8,\r\n            title: \"微信小程序开发实战\",\r\n            desc: \"从零开始学习微信小程序开发，包括组件使用、API调用等\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/00BCD4/white?text=小程序\",\r\n            create_time: \"2024-01-08 10:00:00\"\r\n          },\r\n          {\r\n            id: 9,\r\n            title: \"前端工程化实践\",\r\n            desc: \"学习现代前端工程化工具和流程，提升开发效率\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/9C27B0/white?text=工程化\",\r\n            create_time: \"2024-01-07 14:30:00\"\r\n          },\r\n          {\r\n            id: 10,\r\n            title: \"Web安全基础\",\r\n            desc: \"了解常见的Web安全漏洞和防护措施，保障应用安全\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/795548/white?text=安全\",\r\n            create_time: \"2024-01-06 09:45:00\"\r\n          },\r\n          {\r\n            id: 11,\r\n            title: \"数据可视化技术\",\r\n            desc: \"学习使用D3.js、ECharts等工具创建数据可视化图表\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/607D8B/white?text=可视化\",\r\n            create_time: \"2024-01-05 12:15:00\"\r\n          },\r\n          {\r\n            id: 12,\r\n            title: \"PWA 渐进式Web应用\",\r\n            desc: \"学习PWA技术，创建类似原生应用体验的Web应用\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/8BC34A/white?text=PWA\",\r\n            create_time: \"2024-01-04 16:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 应用搜索过滤\r\n        if (_this.search.keyword) {\r\n          filteredList = filteredList.filter(item =>\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.desc.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_free !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_free === _this.search.is_free);\r\n        }\r\n\r\n        if (_this.search.is_hot !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_hot === _this.search.is_hot);\r\n        }\r\n\r\n        // 分页处理\r\n        const startIndex = (_this.page - 1) * _this.size;\r\n        const endIndex = startIndex + _this.size;\r\n        _this.list = filteredList.slice(startIndex, endIndex);\r\n        _this.total = filteredList.length;\r\n\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      } else {\r\n        if (\r\n          !file.type.split(\"/\")[1] == \"mp4\" ||\r\n          !file.type.split(\"/\")[1] == \"qlv\" ||\r\n          !file.type.split(\"/\")[1] == \"qsv\" ||\r\n          !file.type.split(\"/\")[1] == \"oga\" ||\r\n          !file.type.split(\"/\")[1] == \"flv\" ||\r\n          !file.type.split(\"/\")[1] == \"avi\" ||\r\n          !file.type.split(\"/\")[1] == \"wmv\" ||\r\n          !file.type.split(\"/\")[1] == \"rmvb\"\r\n        ) {\r\n          this.$message({\r\n            showClose: true,\r\n            message: \"请选择'.mp4,.qlv,.qsv,.oga,.flv,.avi,.wmv,.rmvb'文件\",\r\n            type: \"error\",\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 课程管理容器 */\r\n.course-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.free {\r\n  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\r\n}\r\n\r\n.stat-icon.paid {\r\n  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);\r\n}\r\n\r\n.stat-icon.hot {\r\n  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #262626;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.course-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.course-title-cell {\r\n  padding: 8px 0;\r\n}\r\n\r\n.course-title {\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.course-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  max-width: 300px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.course-cover {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.cover-image {\r\n  width: 80px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.price-amount {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 16px;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #8c8c8c;\r\n  font-size: 13px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n/* 卡片视图样式 */\r\n.card-view {\r\n  padding: 0 24px 24px;\r\n  min-height: 400px;\r\n}\r\n\r\n.course-card-col {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.course-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.course-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-cover {\r\n  position: relative;\r\n  height: 200px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.card-cover img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: all 0.3s ease;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.card-cover:hover .cover-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.card-cover:hover img {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-badges {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.card-desc {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-price .price {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 18px;\r\n}\r\n\r\n.card-time {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .course-card-col {\r\n    span: 12;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .course-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .course-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .table-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA+dA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;MAAA;MACAC,QAAA;QACAP,KAAA;QACAQ,MAAA;MACA;MAEAC,KAAA;QACAT,KAAA,GACA;UACAU,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,QAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,SAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAG,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA;MACA,YAAA3B,IAAA,CAAA4B,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAvB,OAAA,QAAAwB,MAAA;IACA;IACA;IACAC,UAAA;MACA,YAAA/B,IAAA,CAAA4B,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAvB,OAAA,QAAAwB,MAAA;IACA;IACA;IACAE,SAAA;MACA,YAAAhC,IAAA,CAAA4B,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtB,MAAA,QAAAuB,MAAA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,YAAA;MACA,KAAAhC,MAAA;QACAC,OAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAA8B,UAAA;IACA;IAEA;IACAC,iBAAA;MAAAC,MAAA;MAAAC,IAAA;MAAAC;IAAA;MACAC,OAAA,CAAAC,GAAA;QAAAJ,MAAA;QAAAC,IAAA;QAAAC;MAAA;MACA;IACA;IACAG,WAAAhC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;MACA8B,OAAA,CAAAC,GAAA,MAAA/B,KAAA;IACA;IACAiC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAA7B,QAAA;UACAP,KAAA;UACAuC,IAAA;UACA3C,OAAA;UACAkB,SAAA;UACAD,QAAA;QACA;MACA;MAEAwB,KAAA,CAAAlC,iBAAA;IACA;IACAmC,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAG,UAAA,CAAAH,KAAA,CAAAtC,GAAA,gBAAAqC,EAAA,EAAAK,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAL,KAAA,CAAA9B,QAAA,GAAAmC,IAAA,CAAAtD,IAAA;QACA;MACA;IACA;IACAuD,QAAAC,KAAA,EAAAR,EAAA;MACA,KAAAS,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAlD,GAAA,kBAAAqC,EAAA,EAAAK,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACArC,OAAA;YACA;YACA,KAAArB,IAAA,CAAA8D,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACArC,OAAA;QACA;MACA;IACA;IACA2C,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACA7B,WAAA;MACA,KAAAnC,IAAA;MACA,KAAAgC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAa,KAAA;MACAA,KAAA,CAAAvC,OAAA;;MAEA;MACA2D,UAAA;QACApB,KAAA,CAAAvC,OAAA;;QAEA;QACA,IAAA4D,YAAA,IACA;UACAtB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,GACA;UACAxB,EAAA;UACApC,KAAA;UACAuC,IAAA;UACAoB,KAAA;UACA/D,OAAA;UACAC,MAAA;UACAgB,QAAA;UACA+C,WAAA;QACA,EACA;;QAEA;QACA,IAAAvB,KAAA,CAAA3C,MAAA,CAAAC,OAAA;UACA+D,YAAA,GAAAA,YAAA,CAAAxC,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAnB,KAAA,CAAA6D,QAAA,CAAAxB,KAAA,CAAA3C,MAAA,CAAAC,OAAA,KACAwB,IAAA,CAAAoB,IAAA,CAAAsB,QAAA,CAAAxB,KAAA,CAAA3C,MAAA,CAAAC,OAAA,CACA;QACA;QAEA,IAAA0C,KAAA,CAAA3C,MAAA,CAAAE,OAAA;UACA8D,YAAA,GAAAA,YAAA,CAAAxC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAvB,OAAA,KAAAyC,KAAA,CAAA3C,MAAA,CAAAE,OAAA;QACA;QAEA,IAAAyC,KAAA,CAAA3C,MAAA,CAAAG,MAAA;UACA6D,YAAA,GAAAA,YAAA,CAAAxC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtB,MAAA,KAAAwC,KAAA,CAAA3C,MAAA,CAAAG,MAAA;QACA;;QAEA;QACA,MAAAiE,UAAA,IAAAzB,KAAA,CAAA7C,IAAA,QAAA6C,KAAA,CAAA5C,IAAA;QACA,MAAAsE,QAAA,GAAAD,UAAA,GAAAzB,KAAA,CAAA5C,IAAA;QACA4C,KAAA,CAAA/C,IAAA,GAAAoE,YAAA,CAAAM,KAAA,CAAAF,UAAA,EAAAC,QAAA;QACA1B,KAAA,CAAA9C,KAAA,GAAAmE,YAAA,CAAAtC,MAAA;MAEA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA6C,SAAA;MACA,IAAA5B,KAAA;MACA,KAAA6B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAAhC,KAAA,CAAAtC,GAAA,gBAAAQ,QAAA,EAAAkC,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAb,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACArC,OAAA,EAAA+B,IAAA,CAAA4B;cACA;cACA,KAAA9C,OAAA;cACAa,KAAA,CAAAlC,iBAAA;YACA;cACAkC,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACArC,OAAA,EAAA+B,IAAA,CAAA4B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA/E,IAAA,GAAA+E,GAAA;MAEA,KAAAhD,OAAA;IACA;IACAiD,oBAAAD,GAAA;MACA,KAAAhF,IAAA,GAAAgF,GAAA;MACA,KAAAhD,OAAA;IACA;IACAkD,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAAzB,IAAA;QACA,KAAAC,QAAA,CAAAyB,OAAA;QACA,KAAArE,QAAA,MAAAL,KAAA,IAAAyE,GAAA,CAAAvF,IAAA,CAAAW,GAAA;MACA;QACA,KAAAoD,QAAA,CAAA0B,KAAA,CAAAF,GAAA,CAAAL,GAAA;MACA;IACA;IAEAQ,UAAAC,IAAA;MACA,KAAA3E,UAAA,GAAA2E,IAAA;MACA,KAAA1E,aAAA;IACA;IACA2E,aAAAD,IAAA;MACA,IAAA/B,IAAA,GAAA+B,IAAA,CAAA/B,IAAA;MACA,SAAA9C,KAAA;QACA,MAAA+E,UAAA,6BAAAC,IAAA,CAAAlC,IAAA;QACA,KAAAiC,UAAA;UACA,KAAA9B,QAAA,CAAA0B,KAAA;UACA;QACA;MACA;QACA,IACA,CAAAE,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,qBACA,CAAAJ,IAAA,CAAA/B,IAAA,CAAAmC,KAAA,oBACA;UACA,KAAAhC,QAAA;YACAiC,SAAA;YACAzE,OAAA;YACAqC,IAAA;UACA;UACA;QACA;MACA;IACA;IACAqC,SAAAN,IAAA,EAAAO,QAAA;MACA,IAAAjD,KAAA;MACAA,KAAA,CAAAG,UAAA,gCAAAuC,IAAA,EAAAtC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAA9B,QAAA,CAAA+E,QAAA;UAEAjD,KAAA,CAAAc,QAAA,CAAAyB,OAAA;QACA;UACAvC,KAAA,CAAAc,QAAA,CAAA0B,KAAA,CAAAnC,IAAA,CAAA4B,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}