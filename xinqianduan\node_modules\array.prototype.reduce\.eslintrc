{
	"root": true,

	"extends": "@ljharb",

	"rules": {
		"array-bracket-newline": 0,
		"id-length": 0,
		"new-cap": [2, {
			"capIsNewExceptions": [
				"ArraySpeciesCreate",
				"Call",
				"CreateDataPropertyOrThrow",
				"Get",
				"HasProperty",
				"IsCallable",
				"LengthOfArrayLike",
				"RequireObjectCoercible",
				"ToObject",
				"ToString",
			],
		}],
		"no-magic-numbers": 0,
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"max-lines-per-function": 0,
				"max-params": 0,
				"no-invalid-this": 1,
				"strict": 0,
			},
		},
	],
}
