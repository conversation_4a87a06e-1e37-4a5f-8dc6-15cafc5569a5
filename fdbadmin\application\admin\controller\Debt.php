<?php
namespace app\admin\controller;
use models\Admins;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use think\Exception;
use think\exception\PDOException;
use think\Request;
use untils\{JsonService,WechatApp};
use models\{Orders,Users,Debts,Debttrans};
use untils\JwtAuth;

class Debt extends Base
{
    protected $model;
    public function __construct(Debts $model){
        parent::__construct();
        $this->model=$model;

    }
    public function index(Request $request,$page=1,$size=20){

        $where=[];
        $userid = $this->userid;//登录者id
        $search=$request->post();
        if(!empty($search['keyword'])){
            $search['keyword'] = trim($search['keyword']);
            $where[]=['debts.tel|debts.name|users.nickname',"like","%".$search['keyword']."%"];
        }
        if($search['status']!=-1){
            $where[]=['debts.status','=',$search['status']];
        }
        $order = ['debts.ctime'=>'desc'];
        if(!empty($search['prop']) && !empty($search['order'])){
            $order = ['debts.'.$search['prop']=>$search['order'] == 'descending' ? 'desc' : 'asc'];
        }
        $res = $this->model->with(['users' => function ($query) {
            return $query->withField(['nickname']);
        }])->withAttr('status',function($v,$d){
            switch ($v) {
                case 1:
                    return '待处理';
                    break;
                case 2:
                    return '调节中';
                    break;
                case 3:
                    return '诉讼中';
                    break;
                case 4:
                    return '已结案';
                    break;
            }
        })
            ->where($where)
            ->where("{$userid}=1 or tiaojie_id={$userid} or fawu_id={$userid} or lian_id={$userid} or ywy_id={$userid} or ls_id={$userid} or htsczy_id={$userid} or (tiaojie_id=0 and fawu_id=0 and lian_id=0 and ls_id=0 and ywy_id=0 and htsczy_id=0)")
            ->limit($size)
            ->order($order)
            ->page($page)
            ->select();
        $count = $this->model->with('users')->where($where)
            ->where("{$userid}=1 or tiaojie_id={$userid} or fawu_id={$userid} or lian_id={$userid} or ywy_id={$userid} or ls_id={$userid} or htsczy_id={$userid} or (tiaojie_id=0 and fawu_id=0 and lian_id=0 and ywy_id=0 and ls_id=0 and htsczy_id=0)")->count();
        if($res){
            foreach ($res as &$v){
                $v["un_money"] = sprintf("%.2f",$v["money"] - $v["back_money"]);
                if($v["ctime"]){
                    $v["ctime"] = date('Y-m-d H:i:s',$v["ctime"]);
                }
            }
        }
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $form['attach_path'] = implode(",",$form['attach_path']);
        $form['del_attach_path'] = implode(",",$form['del_attach_path']);
        $form['images'] = implode(",",$form['images']);
        $form['del_images'] = implode(",",$form['del_images']);
        $form['cards'] = implode(",",$form['cards']);
        if(empty($form['id'])){
            $form['ctime'] = time();
        }else{
            $form['utime'] = time();
        }
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['ctime','utime']);

        if(empty($res)) return JsonService::fail('获取数据失败');
        if(!empty($res["attach_path"])){
            $res['attach_path'] = explode(",",$res["attach_path"]) ;
        }else{
            $res['attach_path'] = [];
        }
        if(!empty($res["del_attach_path"])){
            $res['del_attach_path'] = explode(",",$res["del_attach_path"]) ;
        }else{
            $res['del_attach_path'] = [];
        }
        if(!empty($res["images"])){
            $res['images'] = explode(",",$res["images"]) ;
        }else{
            $res['images'] = [];
        }
        if(!empty($res["del_images"])){
            $res['del_images'] = explode(",",$res["del_images"]) ;
        }else{
            $res['del_images'] = [];
        }
        if(!empty($res["cards"])){
            $res['cards'] = explode(",",$res["cards"]) ;
        }else{
            $res['cards'] = [];
        }


        $where=[];
        $where[]=['dt_id','=',$id];
        $debttrans = Debttrans::where($where)
            ->order(['ctime'=>'desc'])
            ->select();
        if(!empty($debttrans)){
            foreach ($debttrans as&$de){
                if(!empty($de['rate'])){
                    $de['rate'] = $de['rate'].'%';
                }
                if($de["ctime"]){
                    $de["ctime"] = date('Y-m-d H:i:s',$de["ctime"]);
                }
                if($de["pay_time"]){
                    $de["pay_time"] = date('Y-m-d H:i:s',$de["pay_time"]);
                }
                $de['au_id'] = Admins::where(['id'=>$de['au_id']])->value('username');
                switch ($de['type']) {
                    case 1:
                        $de['type'] = '日常';
                        break;
                    case 2:
                        $de['type'] = '回款';
                        break;
                }
                switch ($de['pay_type']) {
                    case 1:
                        $de['pay_type'] = '无需支付';
                        break;
                    case 2:
                        $de['pay_type'] = '待支付';
                        break;
                    case 3:
                        $de['pay_type'] = '已支付';
                        break;
                }
                switch ($de['pay_order_type']) {
                    case 0:
                        $de['pay_order_type'] = '';
                        break;
                    case 1:
                        $de['pay_order_type'] = '线下支付';
                        break;
                    case 2:
                        $de['pay_order_type'] = '微信支付';
                        break;
                }
            }
        }
        $res['debttrans'] = $debttrans;


        if(!empty($res)){
            $res['is_user'] = 1;
        }
        //用户信息
        $res['uname'] = Users::where(['id'=>$res['uid']])->value('nickname');
        $res['utel'] = Users::where(['id'=>$res['uid']])->value('phone');
        return JsonService::successful('成功',$res);
    }

    public function debttransRead($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = Debts::find($id);
//        if($res['status'] == 4 || $res['status'] == 5){
//            return JsonService::fail('该债务无需再跟进了');
//        }
        $data['dt_id'] = $id;
        $data['uid'] = $res['uid'];
        $data['status'] = $res['status'];
        $data['pay_type'] = 1;
        $data['type'] = 1;
        $date = date("Y-m-d");
        $data['day'] = $date;
        $data['back_day'] = $date;
        $data['pay_time'] = $date;
        if(empty($res)) return JsonService::fail('获取数据失败');
       // if($res['is_pay']!=2) return JsonService::fail('订单未支付/已退款,不能选择制作完成');
        else return JsonService::successful('成功',$data);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $debttransModel = new Debttrans();
        $info = $debttransModel->find($id);
        if($info->pay_type == 3 && $info->order_sn){
            return JsonService::fail('费用已支付，不支持移除；');
        }
        $res = $debttransModel->delData(['id'=>$id]);
        if($info->order_sn){
            $orderModel = new Orders();
            $orderinfo =$orderModel
                ->where(['order_sn'=>$info->order_sn])
                ->find();
            $orderinfo->is_delete=1;
            $orderinfo->save();
        }

        if($info->back_money > 0 ){
            $back_money = Debts::where(['id'=>$info->dt_id])->value('back_money');
            $debts['back_money'] = $back_money - $info->back_money;
            $debts['id'] = $info->dt_id;
            $this->model->saveData($debts);
        }
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function tuikuan($id){
        if(empty($id)) return JsonService::fail('数据不存在');
        $info = $this->model->find($id);
        if($info->is_pay!=2){
            return JsonService::fail('订单未支付/已退款,不能申请退款');
        }
        $res = $this->model->saveData(['id'=>$id,'is_pay'=>3,'refund_time'=>now()]);
        if(empty($res)) return JsonService::fail('退款成功');
        else return JsonService::successful('退款失败');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }

    public function view($id=0,Request $request){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        if(!empty($res)){
            if($res["ctime"]){
                $res["ctime"] = date('Y-m-d H:i:s',$res["ctime"]);
            }
            if($res["utime"]){
                $res["utime"] = date('Y-m-d H:i:s',$res["utime"]);
            }
            $res['nickname'] = Users::where(['id'=>$res['uid']])->value('nickname');
            $res["un_money"] = sprintf("%.2f",$res["money"] - $res["back_money"]);
            if(!empty($res["attach_path"])){
                $res['attach_path'] = explode(",",$res["attach_path"]) ;
            }else{
                $res['attach_path'] = [];
            }
            if(!empty($res["cards"])){
                $res['cards'] = explode(",",$res["cards"]) ;
            }else{
                $res['cards'] = [];
            }
            if(!empty($res["images"])){
                $res['images'] = explode(",",$res["images"]) ;
                if(!empty($res['images'])){
                    $img_arr = [];
                    $i = 1;
                    foreach ($res['images'] as $v){
                        $arr['name'] = $res['name'].'证据图片'.$i;
                        $arr['path'] = '../'.$v;
                        $img_arr[] = $arr;
                        $i++;
                    }
                    $res['images_download'] = $img_arr;
                }
            }else{
                $res['images'] = [];
            }
            $where=[];
            $where[]=['dt_id','=',$id];
            $debttrans = Debttrans::where($where)
                ->order(['ctime'=>'desc'])
                ->select();
            if(!empty($debttrans)){
                foreach ($debttrans as&$de){
                    if(!empty($de['rate'])){
                        $de['rate'] = $de['rate'].'%';
                    }
                    if($de["ctime"]){
                        $de["ctime"] = date('Y-m-d H:i:s',$de["ctime"]);
                    }
                    if($de["pay_time"]){
                        $de["pay_time"] = date('Y-m-d H:i:s',$de["pay_time"]);
                    }
                    $de['au_id'] = Admins::where(['id'=>$de['au_id']])->value('username');
                    switch ($de['type']) {
                        case 1:
                            $de['type'] = '日常';
                            break;
                        case 2:
                            $de['type'] = '回款';
                            break;
                    }
                    switch ($de['pay_type']) {
                        case 1:
                            $de['pay_type'] = '无需支付';
                            break;
                        case 2:
                            $de['pay_type'] = '待支付';
                            break;
                        case 3:
                            $de['pay_type'] = '已支付';
                            break;
                    }
                    switch ($de['pay_order_type']) {
                        case 0:
                            $de['pay_order_type'] = '';
                            break;
                        case 1:
                            $de['pay_order_type'] = '线下支付';
                            break;
                        case 2:
                            $de['pay_order_type'] = '微信支付';
                            break;
                    }
                }
            }
            $res['debttrans'] = $debttrans;
        }else{
            return JsonService::fail('id为'.$id.'数据不存在');
        }
        if($request->get('export') == 1) {//导出数据
            $this->export($res);exit;
        }
        if(empty($res)) return JsonService::fail('获取数据失败');
        // if($res['is_pay']!=2) return JsonService::fail('订单未支付/已退款,不能选择制作完成');
        else return JsonService::successful('成功',$res);
    }


    public function saveDebttrans(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $status = Debts::where(['id'=>$form['dt_id']])->value('status');
        if($status == 4 || $status == 5){
            return JsonService::fail('该债务无需再跟进了');
        }
        $openid = '';
        $hk = 0;
        if($form['type'] == 2){
            if($form['back_money'] > 0) {
                if($form['rate'] >= 0){
                    $hk = sprintf("%01.2f", $form['back_money'] * $form['rate'] / 100);
                    $form['total_price'] = $hk;
                }else{
                    $form['total_price'] = $form['rate_money'];
                }
                $back_money = Debts::where(['id'=>$form['dt_id']])->value('back_money');
                $debts['back_money'] = $back_money+$form['back_money'];
            }
            $form['content'] = '回款';
        }
        $debts['status'] = $form['status'];
        $debts['id'] = $form['dt_id'];
        $this->model->saveData($debts);
        if($form['pay_type'] == 2){
            $order_sn = get_order_num();
            $form['order_sn'] = $order_sn;
            $order['order_sn'] = $order_sn;
            $order['type'] = 5;
            //跟进费用内容  回款XXX元
            if($form['type'] == 1){
                $body = $form['content'];
                $order['title'] = '日常跟进手续费';
            }else{
                $body = "回款";
                $order['title'] = '回款手续费';
            }
            $order['total_price'] = $form['total_price'];
            $order['body'] = $body;
            $order['create_time'] = time();
            $order['uid'] = $form['uid'];
            $order['dt_id'] = $form['dt_id'];
            $ordersModel = new Orders();
            $ordersModel->saveData($order);
        }
        if($form['pay_type'] == 3){
            $form['pay_order_type'] = 1;
        }else{
            unset($form['pay_time']);
        }
        if(!empty($form['pay_time'])){
            $form['pay_time'] = strtotime($form['pay_time']);
        }
        $form['ctime'] = time();
        $res = JwtAuth::getInfoByToken($form['token']);
        $form['au_id'] = $res->admin_id;
        $debttransModel = new Debttrans();
        $debttransModel->saveData($form);
        //$res = Debttrans::saveData($form);
        //日常跟进
        //发送推送
        $openid = Users::where(['id'=>$form['uid']])->value('openid');
        if($openid){
            if($form['type'] == 1){
                $phrase11 = '日常跟进';
                if($form['pay_type'] == 2){
                    $thing6 = '您有一条新的跟进费用需要支付';
                }else{
                    $thing6 = '您的债权有新进展，请点击查看';
                }
            }else{
                $phrase11 = "回款跟进";
                if($form['pay_type'] == 2){
                    $thing6 = "回款{$form['back_money']}元，请支付{$form['total_price']}元";
                }else{
                    $thing6 = "您有一条新的回款记录（{$form['back_money']}元）";
                }
            }
            $debt_name = Debts::where(['id'=>$form['dt_id']])->value('name');
            $arr['thing4'] = ['value'=>"债权进度{$debt_name}"];//订单名称
            $arr['phrase11'] = ['value'=>$phrase11];//订单状态
            $arr['time3'] = ['value'=>date('Y-m-d H:i:s',time())];//受理日期
            $arr['thing6'] = ['value'=>$thing6];//订单备注
            WechatApp::send_temp($openid,$arr);
        }
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function deleteDebt($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->delData(['id'=>$id]);
        $debttransModel = new Debttrans();
        $debttransModel->delData(['dt_id'=>$id]);
        $orderModel = new Orders();
        $orderModel->delData(['dt_id'=>$id]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }

    public function export($res){
        $data = $res['debttrans'];
        $headerData = [
            '跟进日期'=>'day',
            '提交时间'=>'ctime',
            '操作人员'=>'au_id',
            '进度类型'=>'type',
            '费用金额/手续费'=>'total_price',
            '费用内容'=>'content',
            '手续费比率'=>'rate',
            '回款金额'=>'back_money',
            '支付状态'=>'pay_type',
            '支付时间'=>'pay_time',
            '支付方式'=>'pay_order_type',
            '进度描述'=>'desc',
        ];
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->setActiveSheetIndex(0);  //读取文件中的工作表


        //导出前两行用户信息
        $info = [
            ['用户姓名', '债务人姓名', '债务人电话', '债务人地址', '债务金额', '合计回款', '未回款', '案由', '提交时间', '最后一次修改时间'],
            [$res['nickname'], $res['name'], $res['tel'], $res['address'], $res['money'], $res['back_money'], $res['un_money'], $res['case_des'], $res['ctime'], $res['utime']]
        ];
        $ro = 1;
        foreach ($info as $v) {
            foreach ($v as $k=>$v2){
                $sheet->setCellValueByColumnAndRow($k + 1, $ro, $v2);
            }
            $ro++;
        }


        //导出跟进记录
        $header = array_keys($headerData);//表头

        $header = array_merge($header);
        foreach ($header as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 5, $value);
            $sheet->getColumnDimension(
                \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($key + 1)
            )->setWidth(strlen($value) * 2);
        }
        $row = 6;
        if($data){
            foreach ($data as $key => $item) {
                $columnIndex = 1;
                foreach ($header as $he){
                    $sheet->setCellValueByColumnAndRow($columnIndex, $row, $item[$headerData[$he]]);
                    $columnIndex++;
                }
                $row++;
            }
        }

        ob_end_clean();
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . '跟进记录' . '.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
        //删除清空：
        $spreadsheet->disconnectWorksheets();exit;
    }

    /**
     * 导入
     */
    public function import(Request $request)
    {
        $file = $request->file('file');
        if (!$file) {
            return JsonService::fail('没有数据!');
        }

        $filePath = ROOT_PATH . DS . 'public' . DS . 'uploads';
        $file = $file->move($filePath);
        //实例化reader
        $ext = $file->getExtension();
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            return JsonService::fail('未知的数据格式!');
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }

        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath.DS. $file->getSaveName())) {
                return JsonService::fail('未知的数据格式!');
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }

            //获取手机号列表
            $mobileArr = [];
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string)$currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : $val;
                }

                if(empty($values[0])){
                    return JsonService::fail('手机号不能为空!');
                }

                if(in_array($values[0],$mobileArr)){
                    return JsonService::fail($values[0].'手机号重复!');
                }
                $mobileArr[] = $values[0];
            }
            $exist = $this->model->whereIn('phone', $mobileArr)->value('phone');
            if($exist) return JsonService::fail($exist.'手机号已存在!');

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string) $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : trim($val);
                }

                $password = empty($values[1])?md5($values[0]):md5($values[1]);
                $insertData = [
                    'create_time'   => time(),
                    'update_time'      => time(),
                    'nickname'      => $values[2],
                    'phone'      => $values[0],
                    'company'        =>  $values[3],
                    'linkman'       =>  $values[4],
                    'linkphone'       =>  $values[5],
                    'yuangong_id'       => $this->userid,
                    'password'       =>  $password
                ];
                $insert[] = $insertData;
            }
        } catch (Exception $exception) {
            @unlink($filePath . DS . $file->getSaveName());
            return JsonService::fail($exception->getMessage());
        }
        @unlink($filePath . DS . $file->getSaveName());
        if (!$insert) {
            return JsonService::fail('没有数据需要导入');
        }
        try {
            $this->model->saveAll($insert);
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            return JsonService::fail($msg);
        } catch (Exception $e) {
            return JsonService::fail($e->getMessage());
        }
        return JsonService::successful('导入成功');
    }

    /**
     * 导入
     */
    public function importDebts(Request $request)
    {
        $file = $request->file('file');
        if (!$file) {
            return JsonService::fail('没有数据!');
        }

        $filePath = ROOT_PATH . DS . 'public' . DS . 'uploads';
        $file = $file->move($filePath);
        //实例化reader
        $ext = $file->getExtension();
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            return JsonService::fail('未知的数据格式!');
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }

        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath . DS . $file->getSaveName())) {
                return JsonService::fail('未知的数据格式!');
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }

            //获取手机号列表
            //$mobileArr = [];
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string)$currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : $val;
                }

                if (empty($values[0])) {
                    return JsonService::fail('手机号不能为空!');
                }
                $uid = Users::where(['phone'=>$values[0]])->value('id');
                if (empty($values[0])) {
                    return JsonService::fail('手机号不存在!');
                }
            }

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = (string)$currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();//强制转成string  防止有些数据取得的是excel对象

                    $values[] = is_null($val) ? '' : trim($val);
                }
                $insertData = [
                    'ctime' => time(),
                    'utime' => time(),
                    'uid'=>$uid,
                    'name' => $values[1],
                    'idcard_no' => $values[2],
                    'tel' => $values[3],
                    'address' => $values[4],
                    'money' => $values[5],
                    'case_des' => $values[6]
                ];
                $insert[] = $insertData;
            }
        } catch (Exception $exception) {
            @unlink($filePath . DS . $file->getSaveName());
            return JsonService::fail($exception->getMessage());
        }
        @unlink($filePath . DS . $file->getSaveName());
        if (!$insert) {
            return JsonService::fail('没有数据需要导入');
        }
        try {
            $this->model->saveAll($insert);
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            return JsonService::fail($msg);
        } catch (Exception $e) {
            return JsonService::fail($e->getMessage());
        }
        return JsonService::successful('导入成功');
    }

    public function exportList(Request $request)
    {
        $where = [];
        $search = $request->post();
        if(!empty($search['keyword'])){
            $search['keyword'] = trim($search['keyword']);
            $where[]=['debts.tel|debts.name|users.nickname',"like","%".$search['keyword']."%"];
        }
//        if($search['status']!=-1){
//            $where[]=['debts.status','=',$search['status']];
//        }
//        $order = ['debts.ctime'=>'desc'];
//        if(!empty($search['prop']) && !empty($search['order'])){
//            $order = ['debts.'.$search['prop']=>$search['order'] == 'descending' ? 'desc' : 'asc'];
//        }
        $res = $this->model
            ->where($where)
            //->order($order)
            ->select();
        if($res){
            foreach ($res as &$v){
                $v["un_money"] = sprintf("%.2f",$v["money"] - $v["back_money"]);
                if($v["ctime"]){
                    $v["ctime"] = date('Y-m-d H:i:s',$v["ctime"]);
                }
                switch ($v["status"]) {
                    case 1:
                        $v["status"] = '待处理';
                        break;
                    case 2:
                        $v["status"] = '调节中';
                        break;
                    case 3:
                        $v["status"] = '诉讼中';
                        break;
                    case 4:
                        $v["status"] = '已结案';
                        break;
                }
                $v["nickname"] = Users::where(['id'=>$v['uid']])->value('nickname');;
            }
        }
        $headerData = [
            '用户姓名' => 'nickname',
            '债务人姓名' => 'name',
            '债务人电话' => 'tel',
            '债务金额（元）' => 'money',
            '状态' => 'status',
            '合计回款（元）' => 'back_money',
            '未回款（元）' => 'un_money',
            '录入时间' => 'ctime'
        ];
        exportData($res, $headerData, '债务人列表');
    }
}
