{"version": 3, "sources": ["webpack:///./src/components/UserDetail.vue?89c4", "webpack:///./src/views/pages/wenshu/dingzhi.vue", "webpack:///src/views/pages/wenshu/dingzhi.vue", "webpack:///./src/views/pages/wenshu/dingzhi.vue?5455", "webpack:///./src/views/pages/wenshu/dingzhi.vue?5286", "webpack:///./src/views/pages/wenshu/dingzhi.vue?7f56", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_v", "_s", "$router", "currentRoute", "name", "attrs", "on", "refulsh", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "is_deal", "_l", "options1", "item", "key", "id", "title", "$event", "getData", "clearData", "directives", "rawName", "loading", "list", "scopedSlots", "_u", "fn", "scope", "row", "viewUserData", "uid", "nickname", "phone", "type", "generateAIContract", "_e", "editData", "submitForReview", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "desc", "file_path", "changeFile", "handleSuccess", "delImage", "content", "saveData", "dialogVisible", "show_image", "dialogAIGenerate", "currentOrder", "matchedContractType", "currentUserInfo", "id_card", "address", "template_file", "template_name", "aiGenerating", "aiProgress", "aiProgressText", "generatedContract", "startAIGeneration", "saveGeneratedContract", "dialogViewUserDetail", "currentId", "staticRenderFns", "components", "UserDetails", "data", "allSize", "page", "is_pay", "url", "info", "is_num", "contractTypes", "required", "message", "trigger", "options", "mounted", "getContractTypes", "methods", "filed", "console", "log", "_this", "getInfo", "setTimeout", "find", "$message", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "then", "deleteRequest", "resp", "code", "msg", "catch", "index", "splice", "go", "searchData", "allData", "order_sn", "create_time", "filteredData", "filter", "includes", "length", "$refs", "validate", "valid", "postRequest", "val", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "fileName", "getRequest", "template_size", "orderData", "matchContractType", "getUserInfo", "orderTitle", "keywords", "matchedType", "userInfoMap", "simulateAIGeneration", "steps", "progress", "text", "currentStep", "updateProgress", "generateContractContent", "contractTemplate", "Date", "toLocaleString", "warning", "orderIndex", "findIndex", "review_status", "submit_time", "review_step", "component", "company", "linkman", "linkphone", "yuangong_id", "start_time", "year", "headimg", "staticStyle", "nativeOn", "license", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "debts", "background", "color", "tel", "money", "status", "viewDebtDetail", "props", "String", "Number", "watch", "immediate", "handler", "newId", "testUserData", "imageUrl", "debt"], "mappings": "kHAAA,W,2CCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGL,KAAKM,QAAQC,aAAaC,SAASP,EAAG,YAAY,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQX,EAAIY,UAAU,CAACV,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIK,GAAG,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,WAAWH,EAAG,WAAW,CAACE,YAAY,eAAeM,MAAM,CAAC,YAAc,gBAAgB,UAAY,IAAIG,MAAM,CAACC,MAAOd,EAAIe,OAAOC,QAASC,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAIe,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAAClB,EAAG,IAAI,CAACE,YAAY,gCAAgCM,MAAM,CAAC,KAAO,UAAUW,KAAK,cAAc,GAAGnB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACE,YAAY,gBAAgBM,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIG,MAAM,CAACC,MAAOd,EAAIe,OAAOO,QAASL,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAIe,OAAQ,UAAWG,IAAME,WAAW,mBAAmBpB,EAAIuB,GAAIvB,EAAIwB,UAAU,SAASC,GAAM,OAAOvB,EAAG,YAAY,CAACwB,IAAID,EAAKE,GAAGjB,MAAM,CAAC,MAAQe,EAAKG,MAAM,MAAQH,EAAKE,SAAQ,IAAI,GAAGzB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAI8B,aAAa,CAAC9B,EAAIK,GAAG,UAAUH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAI+B,eAAe,CAAC/B,EAAIK,GAAG,WAAW,OAAOH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,WAAW,CAAC8B,WAAW,CAAC,CAACvB,KAAK,UAAUwB,QAAQ,YAAYnB,MAAOd,EAAIkC,QAASd,WAAW,YAAYhB,YAAY,aAAaM,MAAM,CAAC,KAAOV,EAAImC,KAAK,OAAS,GAAG,OAAS,KAAK,CAACjC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,MAAQ,MAAM,wBAAwB,MAAMR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,MAAM,wBAAwB,MAAMR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,YAAY,MAAM,wBAAwB,MAAMR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,UAAU,MAAQ,OAAO,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,SAAS,CAACQ,MAAM,CAAC,KAA4B,GAArB6B,EAAMC,IAAIlB,QAAe,UAAiC,GAArBiB,EAAMC,IAAIlB,QAAe,UAAY,OAAO,KAAO,UAAU,CAACtB,EAAIK,GAAG,IAAIL,EAAIM,GAAwB,GAArBiC,EAAMC,IAAIlB,QAAe,MAA6B,GAArBiB,EAAMC,IAAIlB,QAAe,MAAQ,OAAO,cAAcpB,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,UAAU,CAACQ,MAAM,CAAC,KAAO,UAAU,WAAY,GAAOC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIyC,aAAaF,EAAMC,IAAIE,QAAQ,CAAC1C,EAAIK,GAAG,IAAIL,EAAIM,GAAGiC,EAAMC,IAAIG,UAAU,cAAczC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,UAAU,CAACQ,MAAM,CAAC,KAAO,UAAU,WAAY,GAAOC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIyC,aAAaF,EAAMC,IAAIE,QAAQ,CAAC1C,EAAIK,GAAG,IAAIL,EAAIM,GAAGiC,EAAMC,IAAII,OAAO,cAAc1C,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAqB,SAAnBmC,EAAMC,IAAIK,KAAiB3C,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,sBAAsB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAI8C,mBAAmBP,EAAMC,QAAQ,CAACxC,EAAIK,GAAG,YAAYL,EAAI+C,KAAK7C,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,gBAAgB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIgD,SAAST,EAAMC,IAAIb,OAAO,CAAC3B,EAAIK,GAAG,YAAmC,IAAtBkC,EAAMC,IAAIlB,QAAepB,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIiD,gBAAgBV,EAAMC,QAAQ,CAACxC,EAAIK,GAAG,YAAYL,EAAI+C,KAAK7C,EAAG,YAAY,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,gBAAgB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIkD,QAAQX,EAAMY,OAAQZ,EAAMC,IAAIb,OAAO,CAAC3B,EAAIK,GAAG,WAAW,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACQ,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,IAAK,KAAK,YAAYV,EAAIoD,KAAK,OAAS,0CAA0C,MAAQpD,EAAIqD,MAAM,WAAa,IAAI1C,GAAG,CAAC,cAAcX,EAAIsD,iBAAiB,iBAAiBtD,EAAIuD,wBAAwB,IAAI,GAAGrD,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQV,EAAI4B,MAAQ,KAAK,QAAU5B,EAAIwD,kBAAkB,wBAAuB,EAAM,MAAQ,OAAO7C,GAAG,CAAC,iBAAiB,SAASkB,GAAQ7B,EAAIwD,kBAAkB3B,KAAU,CAAC3B,EAAG,UAAU,CAACuD,IAAI,WAAW/C,MAAM,CAAC,MAAQV,EAAI0D,SAAS,MAAQ1D,EAAI2D,QAAQ,CAACzD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcV,EAAI4D,iBAAiB,CAAC1D,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,MAAM,SAAW,IAAIG,MAAM,CAACC,MAAOd,EAAI0D,SAAS9B,MAAOX,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,QAASxC,IAAME,WAAW,qBAAqB,GAAGlB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcV,EAAI4D,iBAAiB,CAAC1D,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,MAAM,SAAW,GAAG,KAAO,WAAW,KAAO,GAAGG,MAAM,CAACC,MAAOd,EAAI0D,SAASG,KAAM5C,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,OAAQxC,IAAME,WAAW,oBAAoB,GAAGlB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcV,EAAI4D,iBAAiB,CAAC1D,EAAG,MAAM,CAACA,EAAG,WAAW,CAACQ,MAAM,CAAC,MAAQ,GAAGG,MAAM,CAACC,MAAOd,EAAI0D,SAASpC,QAASL,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,UAAWxC,IAAME,WAAW,qBAAqB,CAACpB,EAAIK,GAAG,SAASH,EAAG,WAAW,CAACQ,MAAM,CAAC,MAAQ,GAAGG,MAAM,CAACC,MAAOd,EAAI0D,SAASpC,QAASL,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,UAAWxC,IAAME,WAAW,qBAAqB,CAACpB,EAAIK,GAAG,UAAU,KAA8B,GAAxBL,EAAI0D,SAASpC,SAAqC,GAArBtB,EAAI0D,SAASb,KAAW3C,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,QAAQ,cAAcV,EAAI4D,eAAe,KAAO,cAAc,CAAC1D,EAAG,WAAW,CAACE,YAAY,WAAWM,MAAM,CAAC,UAAW,GAAMG,MAAM,CAACC,MAAOd,EAAI0D,SAASI,UAAW7C,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,YAAaxC,IAAME,WAAW,wBAAwBlB,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAI+D,WAAW,gBAAgB,CAAC7D,EAAG,YAAY,CAACQ,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAaV,EAAIgE,gBAAgB,CAAChE,EAAIK,GAAG,WAAW,GAAIL,EAAI0D,SAASI,UAAW5D,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAIiE,SAASjE,EAAI0D,SAASI,UAAW,gBAAgB,CAAC9D,EAAIK,GAAG,QAAQL,EAAI+C,MAAM,IAAI,GAAG/C,EAAI+C,KAA8B,GAAxB/C,EAAI0D,SAASpC,SAAqC,GAArBtB,EAAI0D,SAASb,KAAW3C,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcV,EAAI4D,iBAAiB,CAAC1D,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAGG,MAAM,CAACC,MAAOd,EAAI0D,SAASQ,QAASjD,SAAS,SAAUC,GAAMlB,EAAImB,KAAKnB,EAAI0D,SAAU,UAAWxC,IAAME,WAAW,uBAAuB,GAAGpB,EAAI+C,MAAM,GAAG7C,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASkB,GAAQ7B,EAAIwD,mBAAoB,KAAS,CAACxD,EAAIK,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAImE,cAAc,CAACnE,EAAIK,GAAG,UAAU,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUV,EAAIoE,cAAc,MAAQ,OAAOzD,GAAG,CAAC,iBAAiB,SAASkB,GAAQ7B,EAAIoE,cAAcvC,KAAU,CAAC3B,EAAG,WAAW,CAACQ,MAAM,CAAC,IAAMV,EAAIqE,eAAe,GAAGnE,EAAG,YAAY,CAACE,YAAY,qBAAqBM,MAAM,CAAC,MAAQ,SAAS,QAAUV,EAAIsE,iBAAiB,wBAAuB,EAAM,MAAQ,OAAO3D,GAAG,CAAC,iBAAiB,SAASkB,GAAQ7B,EAAIsE,iBAAiBzC,KAAU,CAAC3B,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIuE,aAAa3C,YAAY1B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIuE,aAAaV,WAAW3D,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIwE,oBAAoB5C,OAAS,qBAAqB1B,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyE,gBAAgB9B,eAAezC,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyE,gBAAgB7B,YAAY1C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,WAAWH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyE,gBAAgBC,SAAW,YAAYxE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIK,GAAG,SAASH,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIyE,gBAAgBE,SAAW,gBAAgBzE,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BJ,EAAIK,GAAG,YAAaL,EAAIwE,oBAAoBI,cAAe1E,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIK,GAAGL,EAAIM,GAAGN,EAAIwE,oBAAoBK,kBAAkB3E,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,SAAS,CAACV,EAAIK,GAAG,YAAY,KAAKH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACQ,MAAM,CAAC,MAAQ,aAAa,KAAO,UAAU,YAAc,uBAAuB,YAAY,GAAG,UAAW,MAAU,KAAMV,EAAI8E,aAAc5E,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIK,GAAG,gBAAgBH,EAAG,cAAc,CAACQ,MAAM,CAAC,WAAaV,EAAI+E,WAAW,OAA4B,MAAnB/E,EAAI+E,WAAqB,UAAY,KAAK,eAAe,KAAK7E,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAIgF,oBAAoB,GAAGhF,EAAI+C,KAAM/C,EAAIiF,kBAAmB/E,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIK,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBM,MAAM,CAAC,KAAO,WAAW,KAAO,GAAG,YAAc,sBAAsBG,MAAM,CAACC,MAAOd,EAAIiF,kBAAmBhE,SAAS,SAAUC,GAAMlB,EAAIiF,kBAAkB/D,GAAKE,WAAW,wBAAwB,KAAKpB,EAAI+C,OAAO7C,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASkB,GAAQ7B,EAAIsE,kBAAmB,KAAS,CAACtE,EAAIK,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,QAAUV,EAAI8E,aAAa,UAAY9E,EAAIwE,oBAAoBI,eAAejE,GAAG,CAAC,MAAQX,EAAIkF,oBAAoB,CAAClF,EAAIK,GAAG,IAAIL,EAAIM,GAAGN,EAAI8E,aAAe,WAAa,UAAU,OAAQ9E,EAAIiF,kBAAmB/E,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQX,EAAImF,wBAAwB,CAACnF,EAAIK,GAAG,YAAYL,EAAI+C,MAAM,KAAK7C,EAAG,YAAY,CAACE,YAAY,qBAAqBM,MAAM,CAAC,MAAQ,OAAO,QAAUV,EAAIoF,qBAAqB,UAAY,MAAM,KAAO,MAAM,yBAAwB,EAAK,wBAAuB,GAAOzE,GAAG,CAAC,iBAAiB,SAASkB,GAAQ7B,EAAIoF,qBAAqBvD,KAAU,CAAC3B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,eAAe,CAACQ,MAAM,CAAC,GAAKV,EAAIqF,cAAc,MAAM,IAE7xWC,EAAkB,G,YC0ZP,GACf7E,KAAA,OACA8E,WAAA,CAAAC,oBACAC,OACA,OACAC,QAAA,OACAvD,KAAA,GACAkB,MAAA,EACAgC,UAAA,EACAM,KAAA,EACAvC,KAAA,GACArC,OAAA,CACAC,QAAA,GACA4E,QAAA,EACAtE,SAAA,GAEAY,SAAA,EACA2D,IAAA,YACAjE,MAAA,OACAkE,KAAA,GACAtC,mBAAA,EACA4B,sBAAA,EACAd,kBAAA,EACAD,WAAA,GACAD,eAAA,EACAV,SAAA,CACA9B,MAAA,GACAmE,OAAA,GAIAxB,aAAA,GACAE,gBAAA,GACAD,oBAAA,GACAwB,cAAA,GACAlB,cAAA,EACAC,WAAA,EACAC,eAAA,GACAC,kBAAA,GAEAtB,MAAA,CACA/B,MAAA,CACA,CACAqE,UAAA,EACAC,QAAA,QACAC,QAAA,SAGArC,UAAA,CACA,CACAmC,UAAA,EACAC,QAAA,QACAC,QAAA,UAIAvC,eAAA,QACAwC,QAAA,CACA,CACAzE,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAGAJ,SAAA,CACA,CACAG,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,UAKAyE,UACA,KAAAvE,UACA,KAAAwE,oBAEAC,QAAA,CACAxC,WAAAyC,GACA,KAAAA,QACAC,QAAAC,IAAA,KAAAF,QAEAzE,YACA,KAAAhB,OAAA,CACAC,QAAA,GACA4E,QAAA,EACAtE,SAAA,GAEA,KAAAqE,KAAA,EACA,KAAA7D,WAEAW,aAAAd,GACA,IAAAgF,EAAA,KACAF,QAAAC,IAAA,2BAAA/E,GACA,GAAAA,IACA,KAAA0D,UAAA1D,EACA8E,QAAAC,IAAA,uBAAArB,YAGAsB,EAAAvB,sBAAA,EACAqB,QAAAC,IAAA,aAEA1D,SAAArB,GAEA,GAAAA,EACA,KAAAiF,QAAAjF,GAEA,KAAA+B,SAAA,CACA9B,MAAA,GACAiC,KAAA,KAIA+C,QAAAjF,GACA,IAAAgF,EAAA,KACAF,QAAAC,IAAA,kBAAA/E,GAGAkF,WAAA,KAEA,MAAApF,EAAAkF,EAAAxE,KAAA2E,KAAArF,KAAAE,QACAF,GACAkF,EAAAjD,SAAA,CACA/B,GAAAF,EAAAE,GACAC,MAAAH,EAAAG,MACAiC,KAAApC,EAAAoC,KACAvC,QAAAG,EAAAH,QACAuB,KAAA,SAAApB,EAAAoB,KAAA,IACAqB,QAAA,GACAJ,UAAA,IAEA2C,QAAAC,IAAA,UAAAC,EAAAjD,UACAiD,EAAAnD,mBAAA,GAEAmD,EAAAI,SAAA,CACAlE,KAAA,QACAqD,QAAA,cAGA,MAiBAc,QAAArF,GACA,KAAAsF,SAAA,gBACAC,kBAAA,KACAC,iBAAA,KACAtE,KAAA,YAEAuE,KAAA,KACA,KAAAC,cAAA,KAAAxB,IAAA,cAAAlE,GAAAyF,KAAAE,IACA,KAAAA,EAAAC,KACA,KAAAR,SAAA,CACAlE,KAAA,UACAqD,QAAAoB,EAAAE,MAGA,KAAAT,SAAA,CACAlE,KAAA,QACAqD,QAAAoB,EAAAE,UAKAC,MAAA,KACA,KAAAV,SAAA,CACAlE,KAAA,QACAqD,QAAA,aAIAhD,QAAAwE,EAAA/F,GACA,KAAAsF,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAtE,KAAA,YAEAuE,KAAA,KACA,KAAAC,cAAA,KAAAxB,IAAA,aAAAlE,GAAAyF,KAAAE,IACA,KAAAA,EAAAC,OACA,KAAAR,SAAA,CACAlE,KAAA,UACAqD,QAAA,UAEA,KAAA/D,KAAAwF,OAAAD,EAAA,QAIAD,MAAA,KACA,KAAAV,SAAA,CACAlE,KAAA,QACAqD,QAAA,aAIAtF,UACA,KAAAL,QAAAqH,GAAA,IAEAC,aACA,KAAAlC,KAAA,EACA,KAAAvC,KAAA,GACA,KAAAtB,WAGAA,UACA,IAAA6E,EAAA,KAEAA,EAAAzE,SAAA,EAGA2E,WAAA,KACA,IAAAiB,EAAA,CACA,CACAnG,GAAA,EACAoG,SAAA,cACAlF,KAAA,OACAjB,MAAA,SACAiC,KAAA,oCACAvC,QAAA,EACAqB,SAAA,KACAC,MAAA,cACAF,IAAA,EACAsF,YAAA,uBAEA,CACArG,GAAA,EACAoG,SAAA,cACAlF,KAAA,OACAjB,MAAA,SACAiC,KAAA,8BACAvC,QAAA,EACAqB,SAAA,KACAC,MAAA,cACAF,IAAA,EACAsF,YAAA,uBAEA,CACArG,GAAA,EACAoG,SAAA,cACAlF,KAAA,OACAjB,MAAA,SACAiC,KAAA,+BACAvC,QAAA,EACAqB,SAAA,KACAC,MAAA,cACAF,IAAA,EACAsF,YAAA,uBAEA,CACArG,GAAA,EACAoG,SAAA,cACAlF,KAAA,OACAjB,MAAA,SACAiC,KAAA,6BACAvC,QAAA,EACAqB,SAAA,KACAC,MAAA,cACAF,IAAA,EACAsF,YAAA,uBAEA,CACArG,GAAA,EACAoG,SAAA,cACAlF,KAAA,OACAjB,MAAA,SACAiC,KAAA,+BACAvC,QAAA,EACAqB,SAAA,KACAC,MAAA,cACAF,IAAA,EACAsF,YAAA,wBAKAC,EAAAH,EACAnB,EAAA5F,OAAAC,UACAiH,EAAAH,EAAAI,OAAAzG,GACAA,EAAAsG,SAAAI,SAAAxB,EAAA5F,OAAAC,UACAS,EAAAG,MAAAuG,SAAAxB,EAAA5F,OAAAC,UACAS,EAAAkB,SAAAwF,SAAAxB,EAAA5F,OAAAC,UACAS,EAAAmB,MAAAuF,SAAAxB,EAAA5F,OAAAC,YAIA,IAAA2F,EAAA5F,OAAAO,SAAA,KAAAqF,EAAA5F,OAAAO,UACA2G,IAAAC,OAAAzG,GACAA,EAAAH,SAAAqF,EAAA5F,OAAAO,UAIAqF,EAAAxE,KAAA8F,EACAtB,EAAAtD,MAAA4E,EAAAG,OACAzB,EAAAzE,SAAA,GACA,MAkBAiC,WACA,IAAAwC,EAAA,KACA,KAAA0B,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAA7B,EAAAd,IAAA,YAAAnC,UAAA0D,KAAAE,IACA,KAAAA,EAAAC,MACAZ,EAAAI,SAAA,CACAlE,KAAA,UACAqD,QAAAoB,EAAAE,MAEA,KAAA1F,UACA6E,EAAAnD,mBAAA,GAEAmD,EAAAI,SAAA,CACAlE,KAAA,QACAqD,QAAAoB,EAAAE,WASAlE,iBAAAmF,GACA,KAAArF,KAAAqF,EAEA,KAAA3G,WAEAyB,oBAAAkF,GACA,KAAA9C,KAAA8C,EACA,KAAA3G,WAEAkC,cAAA0E,GACA,KAAAA,EAAAnB,MACA,KAAAR,SAAA4B,QAAA,QACA,KAAAjF,SAAA,KAAA8C,OAAAkC,EAAAjD,KAAAI,KAEA,KAAAkB,SAAA6B,MAAAF,EAAAlB,MAIAqB,UAAAC,GACA,KAAAzE,WAAAyE,EACA,KAAA1E,eAAA,GAEA2E,aAAAD,GACA,MAAAE,EAAA,0BAAAC,KAAAH,EAAAjG,MACAmG,GACA,KAAAjC,SAAA6B,MAAA,cAIA3E,SAAA6E,EAAAI,GACA,IAAAvC,EAAA,KACAA,EAAAwC,WAAA,6BAAAL,GAAA1B,KAAAE,IACA,KAAAA,EAAAC,MACAZ,EAAAjD,SAAAwF,GAAA,GAEAvC,EAAAI,SAAA4B,QAAA,UAEAhC,EAAAI,SAAA6B,MAAAtB,EAAAE,QAMAlB,mBAEAO,WAAA,KACA,KAAAb,cAAA,CACA,CACArE,GAAA,EACAC,MAAA,OACAgD,cAAA,kDACAC,cAAA,cACAuE,cAAA,QAEA,CACAzH,GAAA,EACAC,MAAA,OACAgD,cAAA,iDACAC,cAAA,aACAuE,cAAA,OAEA,CACAzH,GAAA,EACAC,MAAA,OACAgD,cAAA,GACAC,cAAA,GACAuE,cAAA,GAEA,CACAzH,GAAA,EACAC,MAAA,OACAgD,cAAA,mDACAC,cAAA,aACAuE,cAAA,QAEA,CACAzH,GAAA,EACAC,MAAA,OACAgD,cAAA,GACAC,cAAA,GACAuE,cAAA,KAGA,MAIAtG,mBAAAuG,GACA5C,QAAAC,IAAA,iBAAA2C,GAGA,KAAA9E,aAAA8E,EAGA,KAAAC,kBAAAD,EAAAzH,OAGA,KAAA2H,YAAAF,EAAA3G,KAGA,KAAAoC,cAAA,EACA,KAAAC,WAAA,EACA,KAAAC,eAAA,GACA,KAAAC,kBAAA,GAGA,KAAAX,kBAAA,GAIAgF,kBAAAE,GAEA,MAAAC,EAAA,CACA,YACA,YACA,YACA,YACA,aAGA,IAAAC,EAAA,KACA,QAAA1I,KAAAyI,EACA,GAAAD,EAAArB,SAAAnH,GAAA,CACA0I,EAAA,KAAA1D,cAAAc,KAAAjE,KAAAjB,QAAA6H,EAAAzI,IACA,MAIA,KAAAwD,oBAAAkF,GAAA,GACAjD,QAAAC,IAAA,iBAAAlC,sBAIA+E,YAAA7G,GAEA,MAAAiH,EAAA,CACA,GACAhH,SAAA,KACAC,MAAA,cACA8B,QAAA,qBACAC,QAAA,kBAEA,GACAhC,SAAA,KACAC,MAAA,cACA8B,QAAA,qBACAC,QAAA,kBAEA,GACAhC,SAAA,KACAC,MAAA,cACA8B,QAAA,qBACAC,QAAA,kBAEA,GACAhC,SAAA,KACAC,MAAA,cACA8B,QAAA,qBACAC,QAAA,iBAEA,GACAhC,SAAA,KACAC,MAAA,cACA8B,QAAA,qBACAC,QAAA,kBAIA,KAAAF,gBAAAkF,EAAAjH,IAAA,CACAC,SAAA,OACAC,MAAA,GACA8B,QAAA,GACAC,QAAA,IAGA8B,QAAAC,IAAA,iBAAAjC,kBAIAS,oBACA,KAAAV,oBAAAI,eAKA,KAAAE,cAAA,EACA,KAAAC,WAAA,EACA,KAAAE,kBAAA,GAGA,KAAA2E,wBATA,KAAA7C,SAAA6B,MAAA,wBAaAgB,uBACA,MAAAC,EAAA,CACA,CAAAC,SAAA,GAAAC,KAAA,eACA,CAAAD,SAAA,GAAAC,KAAA,eACA,CAAAD,SAAA,GAAAC,KAAA,eACA,CAAAD,SAAA,GAAAC,KAAA,eACA,CAAAD,SAAA,IAAAC,KAAA,YAGA,IAAAC,EAAA,EAEA,MAAAC,OACAD,EAAAH,EAAAzB,QACA,KAAArD,WAAA8E,EAAAG,GAAAF,SACA,KAAA9E,eAAA6E,EAAAG,GAAAD,KACAC,IAEAnD,WAAAoD,EAAA,OAGA,KAAAC,0BACA,KAAApF,cAAA,IAIAmF,KAIAC,0BACA,MAAAC,EAAA,QAAA3F,oBAAA5C,oBAEU3B,KAAVwE,gBAAA9B,kBACO1C,KAAPwE,gBAAAC,iBACOzE,KAAPwE,gBAAA7B,aACK3C,KAALwE,gBAAAE,wGAOE1E,KAAFsE,aAAAV,4cAgCO,IAAPuG,MAAAC,yBACMpK,KAANsE,aAAAwD,WAEA,KAAA9C,kBAAAkF,EACA,KAAApD,SAAA4B,QAAA,cAIAxD,wBACA,SAAAF,kBAEA,YADA,KAAA8B,SAAAuD,QAAA,cAMA,KAAAvD,SAAA4B,QAAA,WAGA,MAAA4B,EAAA,KAAApI,KAAAqI,UAAA/I,KAAAE,KAAA,KAAA4C,aAAA5C,KACA,IAAA4I,IACA,KAAApI,KAAAoI,GAAAjJ,QAAA,GAIA,KAAAgD,kBAAA,GAIArB,gBAAAT,GACAiE,QAAAC,IAAA,QAAAlE,GAEA,KAAAyE,SAAA,+BACAC,kBAAA,OACAC,iBAAA,KACAtE,KAAA,YACAuE,KAAA,KAEAP,WAAA,KAEA,MAAAa,EAAA,KAAAvF,KAAAqI,UAAA/I,KAAAE,KAAAa,EAAAb,KACA,IAAA+F,IAEA,KAAAvF,KAAAuF,GAAA,IACA,KAAAvF,KAAAuF,GACA+C,cAAA,YACAC,aAAA,IAAAN,MAAAC,iBACAM,YAAA,oBAIA,KAAA5D,SAAA4B,QAAA,uBACA,OACAlB,MAAA,KACA,KAAAV,SAAAjB,KAAA,cCjlC8W,I,wBCQ1W8E,EAAY,eACd,EACA7K,EACAuF,GACA,EACA,KACA,WACA,MAIa,aAAAsF,E,6CCnBf,W,kFCAA,IAAI7K,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,cAAcH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK+E,SAAW,cAAc3K,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKlD,OAAS,cAAc1C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKnD,UAAY,eAAe,GAAGzC,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKgF,SAAW,cAAc5K,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKiF,WAAa,cAAc7K,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKkF,aAAe,eAAe,GAAG9K,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKmF,YAAc,cAAc/K,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKoF,KAAOlL,EAAI8F,KAAKoF,KAAO,IAAM,cAAchL,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAI8F,KAAKqF,SAAgC,KAArBnL,EAAI8F,KAAKqF,QAAgBjL,EAAG,YAAY,CAACkL,YAAY,CAAC,OAAS,WAAW1K,MAAM,CAAC,IAAMV,EAAI8F,KAAKqF,QAAQ,KAAO,IAAIE,SAAS,CAAC,MAAQ,SAASxJ,GAAQ,OAAO7B,EAAI6I,UAAU7I,EAAI8F,KAAKqF,aAAajL,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIK,GAAG,UAAU,QAAQ,GAAGH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAI8F,KAAKwF,SAAgC,KAArBtL,EAAI8F,KAAKwF,QAAgBpL,EAAG,WAAW,CAACkL,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,OAAS,WAAW1K,MAAM,CAAC,IAAMV,EAAI8F,KAAKwF,QAAQ,IAAM,SAAS3K,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAI6I,UAAU7I,EAAI8F,KAAKwF,YAAY,CAACpL,EAAG,MAAM,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,SAASW,KAAK,SAAS,CAACnB,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIK,GAAG,UAAU,QAAQ,IAAI,GAAGH,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,YAAYH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAKyF,cAAgB,cAAcrL,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK0F,WAAa,cAActL,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK2F,WAAa,eAAe,GAAGvL,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK4F,aAAe,cAAcxL,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK6F,SAAW,cAAczL,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAGL,EAAIM,GAAGN,EAAI8F,KAAK8F,UAAY,eAAe,IAAI,GAAG1L,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUW,KAAK,UAAU,CAACnB,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIK,GAAG,aAAaH,EAAG,WAAW,CAAC8B,WAAW,CAAC,CAACvB,KAAK,UAAUwB,QAAQ,YAAYnB,MAAOd,EAAIkC,QAASd,WAAW,YAAYgK,YAAY,CAAC,MAAQ,QAAQ1K,MAAM,CAAC,KAAOV,EAAI8F,KAAK+F,MAAM,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACC,WAAW,UAAUC,MAAM,aAAa,CAAC7L,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACV,EAAIK,GAAGL,EAAIM,GAAGiC,EAAMC,IAAI/B,gBAAgBP,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAGL,EAAIM,GAAGiC,EAAMC,IAAIwJ,eAAe9L,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIK,GAAG,IAAIL,EAAIM,GAAGiC,EAAMC,IAAIyJ,iBAAiB/L,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,SAAS,CAACQ,MAAM,CAAC,KAA4B,QAArB6B,EAAMC,IAAI0J,OAAmB,UAAY,UAAU,KAAO,UAAU,CAAClM,EAAIK,GAAG,IAAIL,EAAIM,GAAGiC,EAAMC,IAAI0J,QAAQ,cAAchM,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAO0B,YAAYpC,EAAIqC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACrC,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO7B,EAAImM,eAAe5J,EAAMC,QAAQ,CAACtC,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIK,GAAG,kBAAkB,GAAKL,EAAI8F,KAAK+F,OAAmC,IAA1B7L,EAAI8F,KAAK+F,MAAMzD,OAA0HpI,EAAI+C,KAAhH7C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACF,EAAIK,GAAG,gBAAyB,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUV,EAAIoE,cAAc,MAAQ,OAAOzD,GAAG,CAAC,iBAAiB,SAASkB,GAAQ7B,EAAIoE,cAAcvC,KAAU,CAAC3B,EAAG,WAAW,CAACQ,MAAM,CAAC,IAAMV,EAAIqE,eAAe,IAAI,IAEjuNiB,EAAkB,GCqNtB,GACA7E,KAAA,cACA2L,MAAA,CACAzK,GAAA,CACAkB,KAAA,CAAAwJ,OAAAC,QACArG,UAAA,IAGAR,OACA,OACAK,KAAA,GACA5D,SAAA,EACAkC,eAAA,EACAC,WAAA,KAGAkI,MAAA,CACA5K,GAAA,CACA6K,WAAA,EACAC,QAAAC,GACAA,GAAA,GAAAA,IACAjG,QAAAC,IAAA,sBAAAgG,GACA,KAAA9F,QAAA8F,OAKAnG,QAAA,CACAK,QAAAjF,GACA,IAAAgF,EAAA,KACAF,QAAAC,IAAA,eAAA/E,GACAgF,EAAAzE,SAAA,EAGA2E,WAAA,KACA,MAAA8F,EAAA,CACAhL,KACAkJ,QAAA,WACAjI,MAAA,cACAD,SAAA,KACAmI,QAAA,KACAK,QAAA,GACAH,YAAA,QACAD,UAAA,cACAQ,aAAA,OACAC,UAAA,MACAC,UAAA,OACAC,YAAA,OACAC,QAAA,MACAC,SAAA,OACAN,QAAA,GACAL,WAAA,aACAC,KAAA,EACAW,MAAA,CACA,CACApL,KAAA,OACAuL,IAAA,cACAC,MAAA,QACAC,OAAA,OAEA,CACAzL,KAAA,OACAuL,IAAA,cACAC,MAAA,QACAC,OAAA,SAKAvF,EAAAb,KAAA6G,EACAhG,EAAAzE,SAAA,EACAuE,QAAAC,IAAA,YAAAiG,IACA,MAmBA9D,UAAA+D,GACA,KAAAvI,WAAAuI,EACA,KAAAxI,eAAA,GAGA+H,eAAAU,GACApG,QAAAC,IAAA,WAAAmG,GAEA,KAAA9F,SAAAjB,KAAA,iBC1TmV,I,wBCQ/U8E,EAAY,eACd,EACA7K,EACAuF,GACA,EACA,KACA,WACA,MAIa,OAAAsF,E", "file": "js/chunk-462ad2b9.f07bb2c8.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"contract-custom-container\"},[_c('div',{staticClass:\"page-header\"},[_c('h1',{staticClass:\"page-title\"},[_vm._v(_vm._s(this.$router.currentRoute.name))]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 刷新 \")])],1),_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"关键词搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入订单号/购买人/套餐\",\"clearable\":\"\"},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"处理状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.is_deal),callback:function ($$v) {_vm.$set(_vm.search, \"is_deal\", $$v)},expression:\"search.is_deal\"}},_vm._l((_vm.options1),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.title,\"value\":item.id}})}),1)],1),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":function($event){return _vm.clearData()}}},[_vm._v(\" 重置 \")])],1)])]),_c('div',{staticClass:\"table-section\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"data-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\",\"border\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"order_sn\",\"label\":\"工单号\",\"width\":\"120\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"工单类型\",\"width\":\"100\"}}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"工单标题\",\"min-width\":\"150\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"工单内容\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"is_deal\",\"label\":\"处理状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.is_deal == 2 ? 'success' : scope.row.is_deal == 1 ? 'warning' : 'info',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.is_deal == 2 ? '已处理' : scope.row.is_deal == 1 ? '处理中' : '待处理')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"nickname\",\"label\":\"用户名\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{attrs:{\"type\":\"primary\",\"underline\":false},on:{\"click\":function($event){return _vm.viewUserData(scope.row.uid)}}},[_vm._v(\" \"+_vm._s(scope.row.nickname)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"phone\",\"label\":\"用户手机\",\"width\":\"130\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{attrs:{\"type\":\"primary\",\"underline\":false},on:{\"click\":function($event){return _vm.viewUserData(scope.row.uid)}}},[_vm._v(\" \"+_vm._s(scope.row.phone)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"发起时间\",\"width\":\"160\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[(scope.row.type === '合同定制')?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-magic-stick\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.generateAIContract(scope.row)}}},[_vm._v(\" AI生成 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-check\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" 完成制作 \")]),(scope.row.is_deal === 2)?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"warning\",\"size\":\"mini\",\"icon\":\"el-icon-upload\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.submitForReview(scope.row)}}},[_vm._v(\" 提交审核 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-close\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 取消 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"合同标题\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"合同要求\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"制作状态\",\"label-width\":_vm.formLabelWidth}},[_c('div',[_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"已完成\")]),_c('el-radio',{attrs:{\"label\":1},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"处理中\")])],1)]),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type == 2)?_c('el-form-item',{attrs:{\"label\":\"请上传文件\",\"label-width\":_vm.formLabelWidth,\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',{on:{\"click\":function($event){return _vm.changeFile('file_path')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1):_vm._e(),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type != 2)?_c('el-form-item',{attrs:{\"label\":\"内容回复\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{staticClass:\"ai-generate-dialog\",attrs:{\"title\":\"AI生成合同\",\"visible\":_vm.dialogAIGenerate,\"close-on-click-modal\":false,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.dialogAIGenerate=$event}}},[_c('div',{staticClass:\"ai-generate-content\"},[_c('div',{staticClass:\"order-info-section\"},[_c('h3',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 工单信息 \")]),_c('div',{staticClass:\"info-grid\"},[_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"工单标题：\")]),_c('span',[_vm._v(_vm._s(_vm.currentOrder.title))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"工单内容：\")]),_c('span',[_vm._v(_vm._s(_vm.currentOrder.desc))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"合同类型：\")]),_c('span',[_vm._v(_vm._s(_vm.matchedContractType.title || '未匹配到合同类型'))])])])]),_c('div',{staticClass:\"user-info-section\"},[_c('h3',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 用户信息 \")]),_c('div',{staticClass:\"info-grid\"},[_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"用户姓名：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserInfo.nickname))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"联系电话：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserInfo.phone))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"身份证号：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserInfo.id_card || '未填写'))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"地址：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserInfo.address || '未填写'))])])])]),_c('div',{staticClass:\"template-info-section\"},[_c('h3',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_vm._v(\" 合同模板 \")]),(_vm.matchedContractType.template_file)?_c('div',{staticClass:\"template-info\"},[_c('div',{staticClass:\"template-file\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(_vm._s(_vm.matchedContractType.template_name))]),_c('el-tag',{attrs:{\"type\":\"success\",\"size\":\"mini\"}},[_vm._v(\"已找到模板\")])],1)]):_c('div',{staticClass:\"no-template\"},[_c('el-alert',{attrs:{\"title\":\"未找到对应的合同模板\",\"type\":\"warning\",\"description\":\"请先在合同类型管理中为该类型上传模板文件\",\"show-icon\":\"\",\"closable\":false}})],1)]),(_vm.aiGenerating)?_c('div',{staticClass:\"ai-progress-section\"},[_c('h3',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-loading\"}),_vm._v(\" AI生成中... \")]),_c('el-progress',{attrs:{\"percentage\":_vm.aiProgress,\"status\":_vm.aiProgress === 100 ? 'success' : null,\"stroke-width\":8}}),_c('p',{staticClass:\"progress-text\"},[_vm._v(_vm._s(_vm.aiProgressText))])],1):_vm._e(),(_vm.generatedContract)?_c('div',{staticClass:\"result-section\"},[_c('h3',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" 生成结果 \")]),_c('div',{staticClass:\"contract-preview\"},[_c('el-input',{staticClass:\"contract-content\",attrs:{\"type\":\"textarea\",\"rows\":15,\"placeholder\":\"AI生成的合同内容将显示在这里...\"},model:{value:(_vm.generatedContract),callback:function ($$v) {_vm.generatedContract=$$v},expression:\"generatedContract\"}})],1)]):_vm._e()]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogAIGenerate = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.aiGenerating,\"disabled\":!_vm.matchedContractType.template_file},on:{\"click\":_vm.startAIGeneration}},[_vm._v(\" \"+_vm._s(_vm.aiGenerating ? 'AI生成中...' : '开始AI生成')+\" \")]),(_vm.generatedContract)?_c('el-button',{attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveGeneratedContract}},[_vm._v(\" 保存合同 \")]):_vm._e()],1)]),_c('el-drawer',{staticClass:\"user-detail-drawer\",attrs:{\"title\":\"用户详情\",\"visible\":_vm.dialogViewUserDetail,\"direction\":\"rtl\",\"size\":\"60%\",\"close-on-press-escape\":true,\"modal-append-to-body\":false},on:{\"update:visible\":function($event){_vm.dialogViewUserDetail=$event}}},[_c('div',{staticClass:\"drawer-content\"},[_c('user-details',{attrs:{\"id\":_vm.currentId}})],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"contract-custom-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">{{ this.$router.currentRoute.name }}</h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">处理状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\" min-width=\"150\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\" min-width=\"200\" show-overflow-tooltip>\r\n        </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.is_deal == 2 ? 'success' : scope.row.is_deal == 1 ? 'warning' : 'info'\"\r\n              size=\"small\"\r\n            >\r\n              {{ scope.row.is_deal == 2 ? '已处理' : scope.row.is_deal == 1 ? '处理中' : '待处理' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n            >\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\" width=\"160\">\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- AI生成按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.type === '合同定制'\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"generateAIContract(scope.row)\"\r\n                icon=\"el-icon-magic-stick\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                AI生成\r\n              </el-button>\r\n\r\n              <!-- 完成制作按钮 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-check\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                完成制作\r\n              </el-button>\r\n\r\n              <!-- 提交审核按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.is_deal === 2\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitForReview(scope.row)\"\r\n                icon=\"el-icon-upload\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                提交审核\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-close\"\r\n                plain\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同要求\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- AI生成合同对话框 -->\r\n    <el-dialog\r\n      title=\"AI生成合同\"\r\n      :visible.sync=\"dialogAIGenerate\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"ai-generate-dialog\"\r\n    >\r\n      <div class=\"ai-generate-content\">\r\n        <!-- 工单信息展示 -->\r\n        <div class=\"order-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            工单信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>工单标题：</label>\r\n              <span>{{ currentOrder.title }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>工单内容：</label>\r\n              <span>{{ currentOrder.desc }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>合同类型：</label>\r\n              <span>{{ matchedContractType.title || '未匹配到合同类型' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            用户信息\r\n          </h3>\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <label>用户姓名：</label>\r\n              <span>{{ currentUserInfo.nickname }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>联系电话：</label>\r\n              <span>{{ currentUserInfo.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>身份证号：</label>\r\n              <span>{{ currentUserInfo.id_card || '未填写' }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <label>地址：</label>\r\n              <span>{{ currentUserInfo.address || '未填写' }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息展示 -->\r\n        <div class=\"template-info-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-document-copy\"></i>\r\n            合同模板\r\n          </h3>\r\n          <div v-if=\"matchedContractType.template_file\" class=\"template-info\">\r\n            <div class=\"template-file\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ matchedContractType.template_name }}</span>\r\n              <el-tag type=\"success\" size=\"mini\">已找到模板</el-tag>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"no-template\">\r\n            <el-alert\r\n              title=\"未找到对应的合同模板\"\r\n              type=\"warning\"\r\n              description=\"请先在合同类型管理中为该类型上传模板文件\"\r\n              show-icon\r\n              :closable=\"false\">\r\n            </el-alert>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- AI生成进度 -->\r\n        <div v-if=\"aiGenerating\" class=\"ai-progress-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-loading\"></i>\r\n            AI生成中...\r\n          </h3>\r\n          <el-progress\r\n            :percentage=\"aiProgress\"\r\n            :status=\"aiProgress === 100 ? 'success' : null\"\r\n            :stroke-width=\"8\"\r\n          >\r\n          </el-progress>\r\n          <p class=\"progress-text\">{{ aiProgressText }}</p>\r\n        </div>\r\n\r\n        <!-- 生成结果 -->\r\n        <div v-if=\"generatedContract\" class=\"result-section\">\r\n          <h3 class=\"section-title\">\r\n            <i class=\"el-icon-check\"></i>\r\n            生成结果\r\n          </h3>\r\n          <div class=\"contract-preview\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"generatedContract\"\r\n              :rows=\"15\"\r\n              placeholder=\"AI生成的合同内容将显示在这里...\"\r\n              class=\"contract-content\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogAIGenerate = false\">取消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"startAIGeneration\"\r\n          :loading=\"aiGenerating\"\r\n          :disabled=\"!matchedContractType.template_file\"\r\n        >\r\n          {{ aiGenerating ? 'AI生成中...' : '开始AI生成' }}\r\n        </el-button>\r\n        <el-button\r\n          v-if=\"generatedContract\"\r\n          type=\"success\"\r\n          @click=\"saveGeneratedContract\"\r\n        >\r\n          保存合同\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/dingzhi/\",\r\n      title: \"合同定制\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogAIGenerate: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      // AI生成相关数据\r\n      currentOrder: {},\r\n      currentUserInfo: {},\r\n      matchedContractType: {},\r\n      contractTypes: [], // 合同类型列表\r\n      aiGenerating: false,\r\n      aiProgress: 0,\r\n      aiProgressText: '',\r\n      generatedContract: '',\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n    this.getContractTypes();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      console.log('viewUserData 被调用，传入的 ID:', id);\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n        console.log('设置 currentId 为:', this.currentId);\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n      console.log('打开用户详情抽屉');\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      console.log('getInfo 被调用，ID:', id);\r\n\r\n      // 使用测试数据，因为API可能不可用\r\n      setTimeout(() => {\r\n        // 从列表数据中找到对应的项目\r\n        const item = _this.list.find(item => item.id === id);\r\n        if (item) {\r\n          _this.ruleForm = {\r\n            id: item.id,\r\n            title: item.title,\r\n            desc: item.desc,\r\n            is_deal: item.is_deal,\r\n            type: item.type === \"合同定制\" ? 1 : 2,\r\n            content: \"\",\r\n            file_path: \"\"\r\n          };\r\n          console.log('设置表单数据:', _this.ruleForm);\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: \"未找到对应的数据\",\r\n          });\r\n        }\r\n      }, 300);\r\n\r\n      // 原始API调用（注释掉）\r\n      /*\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n      */\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"WD202403001\",\r\n            type: \"合同定制\",\r\n            title: \"劳动合同定制\",\r\n            desc: \"需要定制一份标准的劳动合同模板，包含薪资、工作时间、福利待遇等条款\",\r\n            is_deal: 0,\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"WD202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"请帮忙审核房屋租赁合同，检查条款是否合理，有无法律风险\",\r\n            is_deal: 1,\r\n            nickname: \"李四\",\r\n            phone: \"13800138002\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"WD202403003\",\r\n            type: \"合同定制\",\r\n            title: \"买卖合同定制\",\r\n            desc: \"需要定制商品买卖合同，涉及货物交付、付款方式、违约责任等\",\r\n            is_deal: 2,\r\n            nickname: \"王五\",\r\n            phone: \"13800138003\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"WD202403004\",\r\n            type: \"法律咨询\",\r\n            title: \"服务合同咨询\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            is_deal: 1,\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"WD202403005\",\r\n            type: \"合同定制\",\r\n            title: \"借款合同定制\",\r\n            desc: \"需要定制个人借款合同，明确借款金额、利率、还款方式等条款\",\r\n            is_deal: 0,\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取合同类型列表\r\n    getContractTypes() {\r\n      // 模拟获取合同类型数据（与合同类型页面的数据保持一致）\r\n      setTimeout(() => {\r\n        this.contractTypes = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n      }, 100);\r\n    },\r\n\r\n    // AI生成合同\r\n    generateAIContract(orderData) {\r\n      console.log('开始AI生成合同，工单数据:', orderData);\r\n\r\n      // 设置当前工单数据\r\n      this.currentOrder = orderData;\r\n\r\n      // 根据工单标题匹配合同类型\r\n      this.matchContractType(orderData.title);\r\n\r\n      // 获取用户详情信息\r\n      this.getUserInfo(orderData.uid);\r\n\r\n      // 重置AI生成状态\r\n      this.aiGenerating = false;\r\n      this.aiProgress = 0;\r\n      this.aiProgressText = '';\r\n      this.generatedContract = '';\r\n\r\n      // 打开AI生成对话框\r\n      this.dialogAIGenerate = true;\r\n    },\r\n\r\n    // 匹配合同类型\r\n    matchContractType(orderTitle) {\r\n      // 根据工单标题关键词匹配合同类型\r\n      const keywords = {\r\n        '劳动': '劳动合同',\r\n        '租赁': '租赁合同',\r\n        '买卖': '买卖合同',\r\n        '服务': '服务合同',\r\n        '借款': '借款合同'\r\n      };\r\n\r\n      let matchedType = null;\r\n      for (let keyword in keywords) {\r\n        if (orderTitle.includes(keyword)) {\r\n          matchedType = this.contractTypes.find(type => type.title === keywords[keyword]);\r\n          break;\r\n        }\r\n      }\r\n\r\n      this.matchedContractType = matchedType || {};\r\n      console.log('匹配到的合同类型:', this.matchedContractType);\r\n    },\r\n\r\n    // 获取用户信息\r\n    getUserInfo(uid) {\r\n      // 模拟获取用户详情信息\r\n      const userInfoMap = {\r\n        1: {\r\n          nickname: \"张三\",\r\n          phone: \"13800138001\",\r\n          id_card: \"110101199001011234\",\r\n          address: \"北京市朝阳区某某街道123号\"\r\n        },\r\n        2: {\r\n          nickname: \"李四\",\r\n          phone: \"13800138002\",\r\n          id_card: \"110101199002022345\",\r\n          address: \"上海市浦东新区某某路456号\"\r\n        },\r\n        3: {\r\n          nickname: \"王五\",\r\n          phone: \"13800138003\",\r\n          id_card: \"110101199003033456\",\r\n          address: \"广州市天河区某某大道789号\"\r\n        },\r\n        4: {\r\n          nickname: \"赵六\",\r\n          phone: \"13800138004\",\r\n          id_card: \"110101199004044567\",\r\n          address: \"深圳市南山区某某街101号\"\r\n        },\r\n        5: {\r\n          nickname: \"孙七\",\r\n          phone: \"13800138005\",\r\n          id_card: \"110101199005055678\",\r\n          address: \"杭州市西湖区某某路202号\"\r\n        }\r\n      };\r\n\r\n      this.currentUserInfo = userInfoMap[uid] || {\r\n        nickname: \"未知用户\",\r\n        phone: \"\",\r\n        id_card: \"\",\r\n        address: \"\"\r\n      };\r\n\r\n      console.log('获取到的用户信息:', this.currentUserInfo);\r\n    },\r\n\r\n    // 开始AI生成\r\n    startAIGeneration() {\r\n      if (!this.matchedContractType.template_file) {\r\n        this.$message.error('未找到对应的合同模板，无法进行AI生成');\r\n        return;\r\n      }\r\n\r\n      this.aiGenerating = true;\r\n      this.aiProgress = 0;\r\n      this.generatedContract = '';\r\n\r\n      // 模拟AI生成过程\r\n      this.simulateAIGeneration();\r\n    },\r\n\r\n    // 模拟AI生成过程\r\n    simulateAIGeneration() {\r\n      const steps = [\r\n        { progress: 20, text: '正在分析工单内容...' },\r\n        { progress: 40, text: '正在解析合同模板...' },\r\n        { progress: 60, text: '正在整合用户信息...' },\r\n        { progress: 80, text: '正在生成合同条款...' },\r\n        { progress: 100, text: 'AI生成完成！' }\r\n      ];\r\n\r\n      let currentStep = 0;\r\n\r\n      const updateProgress = () => {\r\n        if (currentStep < steps.length) {\r\n          this.aiProgress = steps[currentStep].progress;\r\n          this.aiProgressText = steps[currentStep].text;\r\n          currentStep++;\r\n\r\n          setTimeout(updateProgress, 1000);\r\n        } else {\r\n          // 生成完成，显示模拟的合同内容\r\n          this.generateContractContent();\r\n          this.aiGenerating = false;\r\n        }\r\n      };\r\n\r\n      updateProgress();\r\n    },\r\n\r\n    // 生成合同内容\r\n    generateContractContent() {\r\n      const contractTemplate = `${this.matchedContractType.title}\r\n\r\n甲方（委托方）：${this.currentUserInfo.nickname}\r\n身份证号：${this.currentUserInfo.id_card}\r\n联系电话：${this.currentUserInfo.phone}\r\n地址：${this.currentUserInfo.address}\r\n\r\n乙方（受托方）：[待填写]\r\n\r\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就以下事项达成一致，签订本合同：\r\n\r\n一、合同内容\r\n${this.currentOrder.desc}\r\n\r\n二、合同条款\r\n[根据AI分析生成的具体条款内容]\r\n\r\n1. 权利义务\r\n   甲方权利：[根据合同类型和用户需求生成]\r\n   甲方义务：[根据合同类型和用户需求生成]\r\n   乙方权利：[根据合同类型和用户需求生成]\r\n   乙方义务：[根据合同类型和用户需求生成]\r\n\r\n2. 履行期限\r\n   [根据工单内容分析生成具体期限]\r\n\r\n3. 违约责任\r\n   [根据合同类型生成标准违约条款]\r\n\r\n4. 争议解决\r\n   因履行本合同发生的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉。\r\n\r\n5. 其他约定\r\n   [根据具体需求生成其他条款]\r\n\r\n三、合同生效\r\n本合同自双方签字（盖章）之日起生效。\r\n\r\n甲方签字：_________________ 日期：_________________\r\n\r\n乙方签字：_________________ 日期：_________________\r\n\r\n---\r\n本合同由AI智能生成，请仔细核对内容后使用。\r\n生成时间：${new Date().toLocaleString()}\r\n工单号：${this.currentOrder.order_sn}`;\r\n\r\n      this.generatedContract = contractTemplate;\r\n      this.$message.success('AI合同生成完成！');\r\n    },\r\n\r\n    // 保存生成的合同\r\n    saveGeneratedContract() {\r\n      if (!this.generatedContract) {\r\n        this.$message.warning('没有可保存的合同内容');\r\n        return;\r\n      }\r\n\r\n      // 这里可以调用API保存合同内容\r\n      // 模拟保存过程\r\n      this.$message.success('合同保存成功！');\r\n\r\n      // 更新工单状态为处理中\r\n      const orderIndex = this.list.findIndex(item => item.id === this.currentOrder.id);\r\n      if (orderIndex !== -1) {\r\n        this.list[orderIndex].is_deal = 1; // 设置为处理中\r\n      }\r\n\r\n      // 关闭对话框\r\n      this.dialogAIGenerate = false;\r\n    },\r\n\r\n    // 提交审核\r\n    submitForReview(row) {\r\n      console.log('提交审核:', row);\r\n\r\n      this.$confirm('确认将此合同提交审核？提交后将进入审核流程。', '提示', {\r\n        confirmButtonText: '确定提交',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交审核过程\r\n        setTimeout(() => {\r\n          // 更新工单状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            // 添加审核相关字段\r\n            this.list[index] = {\r\n              ...this.list[index],\r\n              review_status: 'submitted', // 已提交审核\r\n              submit_time: new Date().toLocaleString(),\r\n              review_step: 'mediator_review' // 下一步：调解员审核\r\n            };\r\n          }\r\n\r\n          this.$message.success('合同已成功提交审核！将进入审核流程。');\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-custom-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 抽屉动画优化 */\r\n.user-detail-drawer >>> .el-drawer__container {\r\n  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);\r\n}\r\n\r\n/* 操作按钮纵向排列 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  width: 80px;\r\n  margin: 0 !important;\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-custom-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* AI生成对话框样式 */\r\n.ai-generate-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.ai-generate-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.ai-generate-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.order-info-section,\r\n.user-info-section,\r\n.template-info-section,\r\n.ai-progress-section,\r\n.result-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.template-info {\r\n  padding: 16px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.template-file {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.template-file i {\r\n  color: #67c23a;\r\n  font-size: 20px;\r\n}\r\n\r\n.template-file span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.no-template {\r\n  padding: 16px;\r\n}\r\n\r\n.ai-progress-section {\r\n  border-left-color: #e6a23c;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  margin-top: 12px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.result-section {\r\n  border-left-color: #67c23a;\r\n}\r\n\r\n.contract-preview {\r\n  background: white;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.contract-content >>> .el-textarea__inner {\r\n  font-family: 'Courier New', monospace;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .ai-generate-content {\r\n    padding: 16px;\r\n  }\r\n\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .info-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .info-item label {\r\n    min-width: auto;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingzhi.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingzhi.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./dingzhi.vue?vue&type=template&id=5ead154d&scoped=true\"\nimport script from \"./dingzhi.vue?vue&type=script&lang=js\"\nexport * from \"./dingzhi.vue?vue&type=script&lang=js\"\nimport style0 from \"./dingzhi.vue?vue&type=style&index=0&id=5ead154d&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5ead154d\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingzhi.vue?vue&type=style&index=0&id=5ead154d&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-detail-container\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"客户基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"公司名称\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.company || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"手机号\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.phone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"客户姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkman || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkphone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"用户来源\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.yuangong_id || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"开始时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.start_time || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"会员年限\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.year ? _vm.info.year + '年' : '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"头像\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.headimg && _vm.info.headimg !== '')?_c('el-avatar',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.headimg,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.license && _vm.info.license !== '')?_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.license,\"fit\":\"cover\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"服务团队\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"调解员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.tiaojie_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"法务专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.fawu_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"立案专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.lian_name || '未分配'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"合同专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.htsczy_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"律师\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ls_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"业务员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ywy_name || '未分配'))])])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人信息\")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debts,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.name))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"phone-number\"},[_vm._v(_vm._s(scope.row.tel))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDebtDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 详情 \")])]}}])})],1),(!_vm.info.debts || _vm.info.debts.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无债务人信息\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=4468717a&scoped=true\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4468717a\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}