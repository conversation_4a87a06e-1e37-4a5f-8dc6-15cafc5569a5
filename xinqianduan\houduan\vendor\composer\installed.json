{"packages": [{"name": "adbario/php-dot-notation", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "time": "2022-10-14T20:31:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.5.0"}, "install-path": "../adbario/php-dot-notation"}, {"name": "alibabacloud/client", "version": "1.5.32", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "reference": "5bc6f6d660797dcee2c3aef29700ab41ee764f4d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.4.1", "clagiordano/weblibs-configmanager": "^1.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7|^6.6|^7.5|^8.5|^9.5", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "time": "2022-12-09T04:05:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"AlibabaCloud\\Client\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "support": {"issues": "https://github.com/aliyun/openapi-sdk-php-client/issues", "source": "https://github.com/aliyun/openapi-sdk-php-client"}, "install-path": "../alibabacloud/client"}, {"name": "alibabacloud/live", "version": "1.8.958", "version_normalized": "1.8.958.0", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/live.git", "reference": "2dc756e9e156cb33bc1287d28fc3fade87e4ae60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/live/zipball/2dc756e9e156cb33bc1287d28fc3fade87e4ae60", "reference": "2dc756e9e156cb33bc1287d28fc3fade87e4ae60", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/client": "^1.5", "php": ">=5.5"}, "time": "2021-04-29T09:14:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"AlibabaCloud\\Live\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Live SDK for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "cloud", "library", "live", "sdk"], "support": {"issues": "https://github.com/alibabacloud-sdk-php/live/issues", "source": "https://github.com/alibabacloud-sdk-php/live"}, "install-path": "../alibabacloud/live"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "ce5d34dae9868237a32248788ea175c7e9da14b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/ce5d34dae9868237a32248788ea175c7e9da14b1", "reference": "ce5d34dae9868237a32248788ea175c7e9da14b1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"php-coveralls/php-coveralls": "*", "phpunit/phpunit": "*"}, "time": "2024-02-28T11:22:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.7.1"}, "install-path": "../aliyuncs/oss-sdk-php"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "8802c7396d61a923c9a73e37ead062b24bb1b273"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/8802c7396d61a923c9a73e37ead062b24bb1b273", "reference": "8802c7396d61a923c9a73e37ead062b24bb1b273", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=5.4", "symfony/yaml": "^2.8"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "php-coveralls/php-coveralls": "^1.1", "phpunit/phpunit": "^4.8"}, "time": "2021-07-12T15:27:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "support": {"issues": "https://github.com/clagiordano/weblibs-configmanager/issues", "source": "https://github.com/clagiordano/weblibs-configmanager/tree/v1.5.0"}, "install-path": "../clagiordano/weblibs-configmanager"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2021-11-08T20:18:51+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.5.1"}, "install-path": "../firebase/php-jwt"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2023-12-03T20:35:24+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "time": "2023-12-03T20:19:20+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2023-12-03T20:05:35+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "markbaker/complex", "version": "1.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/c3131244e29c08d44fefb49e0dd35021e9e39dd2", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0|^5.0|^6.0|^7.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.0|^6.0|^7.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "time": "2020-08-26T19:47:57+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"], "psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/1.5.0"}, "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "1.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "44bb1ab01811116f01fe216ab37d921dccc6c10d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/44bb1ab01811116f01fe216ab37d921dccc6c10d", "reference": "44bb1ab01811116f01fe216ab37d921dccc6c10d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7|^6.0|7.0", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "time": "2021-01-26T14:36:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"], "psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/1.2.3"}, "install-path": "../markbaker/matrix"}, {"name": "mtdowling/jmespath.php", "version": "2.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/bbb69a935c2cbb0c03d7f481a238027430f6440b", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "time": "2023-08-25T10:54:48+00:00", "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.7.0"}, "install-path": "../mtdowling/jmespath.php"}, {"name": "myclabs/php-enum", "version": "1.7.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "time": "2020-11-14T18:14:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "install-path": "../myclabs/php-enum"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "time": "2020-04-27T08:12:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.12.0"}, "install-path": "../phpoffice/phpspreadsheet"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-04-10T20:10:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "install-path": "../psr/simple-cache"}, {"name": "qiniu/php-sdk", "version": "v7.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "96971af3cc6151b32e4a9d61001e126624100538"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/96971af3cc6151b32e4a9d61001e126624100538", "reference": "96971af3cc6151b32e4a9d61001e126624100538", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "~1.5.2 || ~1.6.6 || ~1.7.7 || ~1.8.4", "php": ">=5.3.3"}, "require-dev": {"paragonie/random_compat": ">=2", "phpunit/phpunit": "^4.8 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4", "squizlabs/php_codesniffer": "^2.3 || ~3.6"}, "time": "2023-12-25T08:30:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Qiniu/functions.php", "src/Qiniu/Http/Middleware/Middleware.php"], "psr-4": {"Qiniu\\": "src/<PERSON>iu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/qiniu/php-sdk/issues", "source": "https://github.com/qiniu/php-sdk/tree/v7.12.0"}, "install-path": "../qiniu/php-sdk"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "time": "2022-01-02T09:53:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/polyfill-ctype", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-01-29T20:11:03+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/yaml", "version": "v2.8.52", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/02c1859112aa779d9ab394ae4f3381911d84052b", "reference": "02c1859112aa779d9ab394ae4f3381911d84052b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "~1.8"}, "time": "2018-11-11T11:18:13+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v2.8.52"}, "install-path": "../symfony/yaml"}, {"name": "topthink/framework", "version": "v5.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "ecf1a90d397d821ce2df58f7d47e798c17eba3ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/ecf1a90d397d821ce2df58f7d47e798c17eba3ad", "reference": "ecf1a90d397d821ce2df58f7d47e798c17eba3ad", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6.0", "topthink/think-installer": "2.*"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "^5.0|^6.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "2.*"}, "time": "2022-10-25T15:04:49+00:00", "type": "think-framework", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v5.1.42"}, "install-path": "../../thinkphp"}, {"name": "topthink/think-captcha", "version": "v2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/54c8a51552f99ff9ea89ea9c272383a8f738ceee", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "5.1.*"}, "time": "2017-12-31T16:37:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/2.0"}, "install-path": "../topthink/think-captcha"}, {"name": "topthink/think-installer", "version": "v2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/38ba647706e35d6704b5d370c06f8a160b635f88", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-plugin-api": "^1.0||^2.0"}, "require-dev": {"composer/composer": "^1.0||^2.0"}, "time": "2021-01-14T12:12:14+00:00", "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-installer/issues", "source": "https://github.com/top-think/think-installer/tree/v2.0.5"}, "install-path": "../topthink/think-installer"}, {"name": "topthink/think-worker", "version": "v2.0.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-worker.git", "reference": "922d8c95e2f095e0da66d18b9e3fbbfd8de70a3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-worker/zipball/922d8c95e2f095e0da66d18b9e3fbbfd8de70a3f", "reference": "922d8c95e2f095e0da66d18b9e3fbbfd8de70a3f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "topthink/framework": "^5.1.18", "topthink/think-installer": "^2.0", "workerman/gateway-worker": "^3.0.0", "workerman/workerman": "^3.5.0"}, "time": "2019-03-08T11:22:34+00:00", "type": "think-extend", "extra": {"think-config": {"worker": "src/config/worker.php", "worker_server": "src/config/server.php", "gateway_worker": "src/config/gateway.php"}}, "installation-source": "dist", "autoload": {"files": ["src/command.php"], "psr-4": {"think\\worker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "workerman extend for thinkphp5.1", "support": {"issues": "https://github.com/top-think/think-worker/issues", "source": "https://github.com/top-think/think-worker/tree/v2.0.12"}, "install-path": "../topthink/think-worker"}, {"name": "workerman/gateway-worker", "version": "v3.0.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "a615036c482d11f68b693998575e804752ef9068"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/a615036c482d11f68b693998575e804752ef9068", "reference": "a615036c482d11f68b693998575e804752ef9068", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"workerman/workerman": ">=3.5.0"}, "time": "2021-12-23T13:13:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "support": {"issues": "https://github.com/walkor/GatewayWorker/issues", "source": "https://github.com/walkor/GatewayWorker/tree/v3.0.22"}, "funding": [{"url": "https://opencollective.com/walkor", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "install-path": "../workerman/gateway-worker"}, {"name": "workerman/workerman", "version": "v3.5.35", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/walkor/workerman.git", "reference": "3cc0adae51ba36db38b11e7996c64250d356dbe7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman/zipball/3cc0adae51ba36db38b11e7996c64250d356dbe7", "reference": "3cc0adae51ba36db38b11e7996c64250d356dbe7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3||^7.0"}, "suggest": {"ext-event": "For better performance. "}, "time": "2023-09-13T14:30:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "funding": [{"url": "https://opencollective.com/workerman", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "install-path": "../workerman/workerman"}, {"name": "yuanhang/easy-upload", "version": "v1.1.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mygithub-hang/easyUpload.git", "reference": "e051169ffd35f2021f257599beb6744c1e9ce3ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mygithub-hang/easyUpload/zipball/e051169ffd35f2021f257599beb6744c1e9ce3ed", "reference": "e051169ffd35f2021f257599beb6744c1e9ce3ed", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"aliyuncs/oss-sdk-php": ">=2.3", "php": ">=5.6.0", "qiniu/php-sdk": ">=7.3"}, "time": "2021-12-23T11:17:38+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"EasyUpload\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Voyage", "email": "<EMAIL>"}], "description": "Integrate <PERSON> and Ali OSS", "keywords": ["oss", "qiniu"], "support": {"issues": "https://github.com/mygithub-hang/easyUpload/issues", "source": "https://github.com/mygithub-hang/easyUpload/tree/v1.1.7"}, "install-path": "../yuanhang/easy-upload"}], "dev": true, "dev-package-names": []}