{"version": 3, "sources": ["webpack:///./src/views/pages/taocan/dingdan.vue", "webpack:///src/views/pages/taocan/dingdan.vue", "webpack:///./src/views/pages/taocan/dingdan.vue?61a7", "webpack:///./src/views/pages/taocan/dingdan.vue?3379", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f", "webpack:///./src/views/pages/taocan/dingdan.vue?0b10"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "slot", "_v", "_s", "$router", "currentRoute", "name", "staticStyle", "on", "refulsh", "allSize", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "refund_time", "$event", "getData", "directives", "rawName", "loading", "list", "scopedSlots", "_u", "key", "fn", "scope", "row", "client", "company", "linkman", "phone", "taocan", "title", "price", "year", "pay_type", "qishu", "pay_age", "total_price", "showStatus", "status", "_e", "status_msg", "editData", "id", "nativeOn", "preventDefault", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "is_info", "info", "viewUserData", "showImage", "pic_path", "tiaojie_id", "fawu_id", "lian_id", "htsczy_id", "ls_id", "ywy_id", "debts", "end_time", "updateEndTIme", "_l", "num", "item", "index", "is_num", "fenqi", "date", "pay_path", "changePinzhen", "handleSuccess", "beforeUpload", "desc", "dialogStatus", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "changeStatus", "dialogEndTime", "changeEndTime", "dialogVisible", "show_image", "用户详情", "dialogViewUserDetail", "currentId", "staticRenderFns", "components", "UserDetails", "data", "page", "url", "dialogAddOrder", "required", "message", "trigger", "mounted", "methods", "_this", "getInfo", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "msg", "catch", "postRequest", "go", "count", "saveData", "$refs", "validate", "valid", "val", "res", "file", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "showEndTime", "component", "nickname", "headimg", "yuangong_id", "linkphone", "license", "start_time", "props", "String", "watch", "immediate", "handler", "newId"], "mappings": "gJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,OAAS,WAAW,CAACF,EAAG,MAAM,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGP,KAAKQ,QAAQC,aAAaC,SAAST,EAAG,YAAY,CAACU,YAAY,CAAC,MAAQ,QAAQ,QAAU,SAASR,MAAM,CAAC,KAAO,QAAQS,GAAG,CAAC,MAAQb,EAAIc,UAAU,CAACd,EAAIO,GAAG,SAAS,GAAGL,EAAG,SAAS,CAACA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,oBAAoB,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOC,QAASC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,WAAW,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOC,QAASC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,YAAY,gBAAgB,GAAG,kBAAkB,IAAI,oBAAoB,SAAS,kBAAkB,SAAS,KAAO,OAAO,eAAe,sBAAsB,eAAe,CAAC,WAAY,aAAaY,MAAM,CAACC,MAAOjB,EAAIkB,OAAOM,YAAaJ,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,cAAeG,IAAME,WAAW,yBAAyB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,KAAOJ,EAAIe,SAASF,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAI0B,aAAa,CAAC1B,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,WAAW,CAACyB,WAAW,CAAC,CAAChB,KAAK,UAAUiB,QAAQ,YAAYX,MAAOjB,EAAI6B,QAASN,WAAW,YAAYX,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQR,MAAM,CAAC,KAAOJ,EAAI8B,KAAK,KAAO,SAAS,CAAC5B,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAGF,EAAMC,IAAIC,OAAOC,aAAa,GAAGpC,EAAG,SAAS,CAACF,EAAIO,GAAG,OAAOP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAIF,EAAMC,IAAIC,OAAOE,YAAYvC,EAAIO,GAAG,UAAUL,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAIF,EAAMC,IAAIC,OAAOG,WAAW,UAAUtC,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAASN,EAAMC,IAAIK,OAAOC,MAAM,OAAOxC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAAON,EAAMC,IAAIK,OAAOE,MAAM,IAAI,OAAOzC,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAAQN,EAAMC,IAAIK,OAAOG,KAAM,IAAI,QAAQ,UAAU1C,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAyB,GAAtB2B,EAAMC,IAAIS,SAAgB,KAAO,MAAaV,EAAMC,IAAIU,MAAQ,QAAQ5C,EAAG,SAAS,CAACF,EAAIO,GAAG,OAAOP,EAAIQ,GAAG2B,EAAMC,IAAIW,SAAS,OAAO7C,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAyB,GAAtB2B,EAAMC,IAAIS,SAAc,EAAEV,EAAMC,IAAIY,YAAcb,EAAMC,IAAIW,SAAS,cAAc7C,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIiD,WAAWd,EAAMC,QAAQ,CAAoB,GAAlBD,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,WAA8B,GAAlB4B,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,aAAgC,GAAlB4B,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,WAAWP,EAAImD,KAAwB,GAAlBhB,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACF,EAAIO,GAAG,MAAMP,EAAIQ,GAAG2B,EAAMC,IAAIgB,iBAAiBpD,EAAImD,MAAM,UAAUjD,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,eAAe,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,cAAc,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,WAAW,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAM2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIqD,SAASlB,EAAMC,IAAIkB,OAAO,CAACtD,EAAIO,GAAG,QAAQL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASmD,SAAS,CAAC,MAAQ,SAAS9B,GAAgC,OAAxBA,EAAO+B,iBAAwBxD,EAAIyD,QAAQtB,EAAMuB,OAAQvB,EAAMC,IAAIkB,OAAO,CAACtD,EAAIO,GAAG,kBAAkB,GAAGL,EAAG,MAAM,CAACG,YAAY,YAAY,CAACH,EAAG,gBAAgB,CAACE,MAAM,CAAC,aAAa,CAAC,GAAI,IAAK,IAAK,IAAK,KAAK,YAAYJ,EAAI2D,KAAK,OAAS,0CAA0C,MAAQ3D,EAAI4D,OAAO/C,GAAG,CAAC,cAAcb,EAAI6D,iBAAiB,iBAAiB7D,EAAI8D,wBAAwB,IAAI,GAAG5D,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI+D,kBAAkB,wBAAuB,GAAOlD,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI+D,kBAAkBtC,KAAU,CAAEzB,EAAIgE,QAAS9D,EAAG,MAAM,CAACA,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOC,YAAYpC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIkE,aAAalE,EAAIiE,KAAK5B,OAAOiB,OAAO,CAACtD,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAIrC,EAAIiE,KAAK5B,OAAOE,YAAYvC,EAAImD,MAAM,GAAGjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIkE,aAAalE,EAAIiE,KAAK5B,OAAOiB,OAAO,CAACtD,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAIrC,EAAIiE,KAAK5B,OAAOG,UAAUxC,EAAImD,MAAM,GAAGjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAK5B,OAAO+B,aAAa,CAACpE,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAG,SAAS,GAAGL,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOgC,eAAenE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOiC,YAAYpE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOkC,YAAYrE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOmC,cAActE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOoC,UAAUvE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOqC,YAAY,GAAGxE,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,OAAQ,IAAQ,CAACF,EAAG,uBAAuB,CAACA,EAAG,WAAW,CAACyB,WAAW,CAAC,CAAChB,KAAK,UAAUiB,QAAQ,YAAYX,MAAOjB,EAAI6B,QAASN,WAAW,YAAYX,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQR,MAAM,CAAC,KAAOJ,EAAIiE,KAAKU,MAAM,KAAO,SAAS,CAACzE,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAWF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,MAAM,MAAQ,WAAWF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,aAAaF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,SAAS,MAAQ,SAAS,IAAI,IAAI,GAAGF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOC,UAAUxC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOE,UAAUzC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOG,MAAM,QAAQ,GAAG1C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,WAAW,OAAS,sBAAsB,eAAe,sBAAsB,YAAc,OAAO,KAAO,QAAQY,MAAM,CAACC,MAAOjB,EAAIiE,KAAKW,SAAUxD,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIiE,KAAM,WAAY5C,IAAME,WAAW,mBAAmBrB,EAAG,SAAS,CAACU,YAAY,CAAC,OAAS,WAAWR,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAI6E,mBAAmB,CAAC7E,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAASJ,EAAI8E,GAAI9E,EAAIiE,KAAKxB,OAAOsC,KAAK,SAASC,EAAKC,GAAO,OAAO/E,EAAG,uBAAuB,CAAC+B,IAAIgD,EAAM7E,MAAM,CAAC,MAAQ4E,EAAKtC,QAAQ,CAAC1C,EAAIO,GAAGP,EAAIQ,GAAkB,GAAfwE,EAAKE,OAAcF,EAAK/D,MAAQ,IAAM,YAAW,GAAGf,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAwB,GAArBR,EAAIiE,KAAKpB,SAAgB,KAAO,MAAa7C,EAAIiE,KAAKnB,MAAQ,QAAQ5C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKlB,SAAS,QAAQ,GAAG7C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKjB,YAAchD,EAAIiE,KAAKlB,SAAS,QAAQ,IAAI,GAAuB,GAAnB/C,EAAIiE,KAAKpB,SAAa3C,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQJ,EAAImD,KAAKnD,EAAI8E,GAAI9E,EAAIiE,KAAKkB,OAAO,SAASH,EAAKC,GAAO,OAA6B,GAArBjF,EAAIiE,KAAKpB,SAAe3C,EAAG,kBAAkB,CAAC+B,IAAIgD,GAAO,CAAC/E,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAAN6E,EAAQ,GAAG,MAAM,CAACjF,EAAIO,GAAG,IAAIP,EAAIQ,GAAGwE,EAAKrC,OAAO,OAAOzC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAAN6E,EAAQ,GAAG,QAAQ,CAACjF,EAAIO,GAAG,IAAIP,EAAIQ,GAAGwE,EAAKI,MAAM,OAAOlF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAAN6E,EAAQ,GAAG,QAAQ,CAAED,EAAKK,SAAUnF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUa,EAAKK,aAAa,CAACrF,EAAIO,GAAG,QAAQL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIsF,cAAcL,MAAU,CAAC/E,EAAG,YAAY,CAACE,MAAM,CAAC,OAAS,4BAA4B,OAAS,oBAAoB,kBAAiB,EAAM,aAAaJ,EAAIuF,cAAc,gBAAgBvF,EAAIwF,eAAe,CAACxF,EAAIO,GAAG,aAAa,IAAI,IAAI,GAAGP,EAAImD,QAAOjD,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKwB,UAAU,IAAI,GAAGzF,EAAImD,OAAOjD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI0F,aAAa,wBAAuB,GAAO7E,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI0F,aAAajE,KAAU,CAACvB,EAAG,UAAU,CAACyF,IAAI,WAAWvF,MAAM,CAAC,MAAQJ,EAAI4F,SAAS,MAAQ5F,EAAI6F,QAAQ,CAAC3F,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,KAAK,cAAcJ,EAAI8F,iBAAiB,CAAC5F,EAAG,MAAM,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,EAAE,SAAgC,GAArBJ,EAAI4F,SAAS1C,QAAsBlC,MAAM,CAACC,MAAOjB,EAAI4F,SAAS1C,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAI4F,SAAU,SAAUvE,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,UAAUL,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAI4F,SAAS1C,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAI4F,SAAU,SAAUvE,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,UAAUL,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAI4F,SAAS1C,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAI4F,SAAU,SAAUvE,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,aAAa,KAA2B,GAArBP,EAAI4F,SAAS1C,OAAWhD,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,QAAQ,cAAcJ,EAAI8F,eAAe,KAAO,eAAe,CAAC5F,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,SAASY,MAAM,CAACC,MAAOjB,EAAI4F,SAASxC,WAAYhC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAI4F,SAAU,aAAcvE,IAAME,WAAW,0BAA0B,GAAGvB,EAAImD,MAAM,GAAGjD,EAAG,MAAM,CAACG,YAAY,gBAAgBD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQzB,EAAI0F,cAAe,KAAS,CAAC1F,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAI+F,kBAAkB,CAAC/F,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,SAAS,QAAUJ,EAAIgG,cAAc,wBAAuB,GAAOnF,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAIgG,cAAcvE,KAAU,CAACvB,EAAG,UAAU,CAACyF,IAAI,WAAWvF,MAAM,CAAC,MAAQJ,EAAI4F,SAAS,MAAQ5F,EAAI6F,QAAQ,CAAC3F,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,QAAQ,YAAc,QAAQY,MAAM,CAACC,MAAOjB,EAAI4F,SAAShB,SAAUxD,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAI4F,SAAU,WAAYvE,IAAME,WAAW,wBAAwB,GAAGrB,EAAG,MAAM,CAACG,YAAY,gBAAgBD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQzB,EAAIgG,eAAgB,KAAS,CAAChG,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIiG,mBAAmB,CAACjG,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAIkG,cAAc,MAAQ,OAAOrF,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAIkG,cAAczE,KAAU,CAACvB,EAAG,WAAW,CAACE,MAAM,CAAC,IAAMJ,EAAImG,eAAe,GAAGjG,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAIoG,KAAK,QAAUpG,EAAIqG,qBAAqB,wBAAuB,GAAOxF,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAIqG,qBAAqB5E,KAAU,CAACvB,EAAG,eAAe,CAACE,MAAM,CAAC,GAAKJ,EAAIsG,cAAc,IAAI,IAE1nYC,EAAkB,G,YCwTtB,GACA5F,KAAA,OACA6F,WAAA,CAAAC,oBACAC,OACA,OACA3F,QAAA,OACAe,KAAA,GACA8B,MAAA,EACA+C,KAAA,EACAhD,KAAA,GACA2C,UAAA,EACApF,OAAA,CACAC,QAAA,IAEAU,SAAA,EACA+E,IAAA,YACA3C,KAAA,GACAD,SAAA,EACAD,mBAAA,EACAmC,eAAA,EACAG,sBAAA,EACAX,cAAA,EACAM,eAAA,EACAa,gBAAA,EACAjB,SAAA,CACA1C,OAAA,EACAE,WAAA,GACAE,GAAA,IAEAuC,MAAA,CACAzC,WAAA,EACA0D,UAAA,EACAC,QAAA,WACAC,QAAA,SAEApC,SAAA,EACAkC,UAAA,EACAC,QAAA,QACAC,QAAA,UAGAlB,eAAA,QACAK,WAAA,GACAlB,MAAA,IAGAgC,UACA,KAAAvF,WAEAwF,QAAA,CACA7D,SAAAC,GACA,IAAA6D,EAAA,KACA,GAAA7D,EACA,KAAA8D,QAAA9D,GAEA,KAAAsC,SAAA,CACAlD,MAAA,IAGAyE,EAAApD,mBAAA,GAEAG,aAAAZ,GACA,IAAA6D,EAAA,KACA,GAAA7D,IACA,KAAAgD,UAAAhD,GAGA6D,EAAAd,sBAAA,GAEAe,QAAA9D,GACA,IAAA6D,EAAA,KACAA,EAAAE,WAAAF,EAAAP,IAAA,WAAAtD,GAAAgE,KAAAC,IACAA,IACAJ,EAAAlD,KAAAsD,EAAAb,KACAS,EAAAnD,SAAA,MAIAP,QAAAwB,EAAA3B,GACA,KAAAkE,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,KAAAM,cAAA,KAAAhB,IAAA,aAAAtD,GAAAgE,KAAAC,IACA,KAAAA,EAAAM,MACA,KAAAC,SAAA,CACAH,KAAA,UACAZ,QAAA,UAEA,KAAAjF,KAAAiG,OAAA9C,EAAA,IAEAkC,MAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,UAKAC,MAAA,KACA,KAAAH,SAAA,CACAH,KAAA,QACAZ,QAAA,aAKAlC,gBACA,KAAA2C,SAAA,kBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,IAAAZ,EAAA,SAAAzC,KAAAX,GAAA,cAAAW,KAAAW,UACA,KAAAsD,YAAA,KAAAtB,IAAA,gBAAAF,GACAY,KAAAC,IACA,KAAAA,EAAAM,KACA,KAAAC,SAAA,CACAH,KAAA,UACAZ,QAAA,UAGAI,MAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,UAKAC,MAAA,KACA,KAAAH,SAAA,CACAH,KAAA,QACAZ,QAAA,aAIAjG,UACA,KAAAL,QAAA0H,GAAA,IAEAzG,UACA,IAAAyF,EAAA,KACAA,EAAAtF,SAAA,EACAsF,EACAe,YACAf,EAAAP,IAAA,eAAAO,EAAAR,KAAA,SAAAQ,EAAAxD,KACAwD,EAAAjG,QAEAoG,KAAAC,IACA,KAAAA,EAAAM,OACAV,EAAArF,KAAAyF,EAAAb,KACAS,EAAAvD,MAAA2D,EAAAa,OAEAjB,EAAAtF,SAAA,KAGAwG,WACA,IAAAlB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAgBA,SAfA,KAAAN,YAAAf,EAAAP,IAAA,YAAAhB,UAAA0B,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAAQ,EAAAS,MAEAb,EAAApD,mBAAA,GAEAoD,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,WASAnE,iBAAA4E,GACA,KAAA9E,KAAA8E,EAEA,KAAA/G,WAEAoC,oBAAA2E,GACA,KAAA9B,KAAA8B,EACA,KAAA/G,WAEA6D,cAAAmD,GACA,IAAAvB,EAAA,KACA,KAAAuB,EAAAb,OACAV,EAAAlD,KAAAkB,MAAAgC,EAAAlC,OAAAI,SAAAqD,EAAAhC,KAAAE,IACAO,EAAAe,YAAAf,EAAAP,IAAA,QACA,GAAAO,EAAAlD,KAAAX,GACA,MAAA6D,EAAAlD,KAAAkB,QACAmC,KAAAC,IACA,KAAAA,EAAAM,KACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAIAI,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAA,aAQAvB,aAAAmD,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAAhB,MACAiB,GACA,KAAAd,SAAAgB,MAAA,cAIAC,SAAAJ,EAAAK,GACA,IAAA7B,EAAA,KACAA,EAAAE,WAAA,6BAAAsB,GAAArB,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAvB,SAAAoD,GAAA,GAEA7B,EAAAW,SAAAmB,QAAA,UAEA9B,EAAAW,SAAAgB,MAAAvB,EAAAS,QAIA7D,UAAAwE,GACA,KAAAxC,WAAAwC,EACA,KAAAzC,eAAA,GAEAZ,cAAAL,GACA,KAAAA,SAEAhC,WAAAb,GACA,KAAAsD,cAAA,EACA,KAAAE,SAAAxD,GAEA8G,YAAA9G,GACA,KAAA4D,eAAA,EACA,KAAAJ,SAAAxD,GAEA6D,gBACA,IAAAkB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBArB,EAAAe,YAAAf,EAAAP,IAAA,QACA,GAAAO,EAAAvB,SAAAtC,GACA,SAAA6D,EAAAvB,SAAAhB,WAEA0C,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAEAI,EAAAzB,cAAA,GAEAyB,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,WASAjC,eAGA,IAAAoB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBArB,EAAAe,YAAAf,EAAAP,IAAA,gBACA,GAAAO,EAAAvB,SAAAtC,GACA,OAAA6D,EAAAvB,SAAA1C,OACA,WAAAiE,EAAAvB,SAAAxC,aACAkE,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAEAI,EAAAzB,cAAA,GAEAyB,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,aCjmB8W,I,wBCQ1WmB,EAAY,eACd,EACApJ,EACAwG,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,2CCnBf,IAAIpJ,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK3B,YAAYpC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKzB,UAAUtC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKmF,aAAalJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK1B,YAAYrC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAAqB,IAAnBJ,EAAIiE,KAAKoF,SAAkC,MAAlBrJ,EAAIiE,KAAKoF,QAAenJ,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQR,MAAM,CAAC,IAAMJ,EAAIiE,KAAKoF,SAASxI,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAKoF,aAAarJ,EAAImD,OAAOjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKqF,gBAAgBpJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKsF,cAAcrJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKI,YAAY,OAAOnE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKK,SAAS,OAAOpE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKM,SAAS,OAAOrE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKO,WAAW,OAAOtE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKQ,OAAO,OAAOvE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKS,QAAQ,OAAOxE,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAqB,IAAnBJ,EAAIiE,KAAKuF,SAAkC,MAAlBxJ,EAAIiE,KAAKuF,QAAetJ,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQR,MAAM,CAAC,IAAMJ,EAAIiE,KAAKuF,SAAS3I,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAKuF,aAAaxJ,EAAImD,OAAOjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKwF,eAAevJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKrB,MAAM,QAAQ,IAAI,IAE33D2D,EAAkB,GCmEtB,GACA5F,KAAA,cACA+I,MAAA,CACApG,GAAA,CACAqE,KAAAgC,OACA7C,UAAA,IAGAJ,OACA,OACAzC,KAAA,KAGA2F,MAAA,CACAtG,GAAA,CACAuG,WAAA,EACAC,QAAAC,GACA,KAAA3C,QAAA2C,MAIA7C,QAAA,CACAE,QAAA9D,GACA,IAAA6D,EAAA,KACAA,EAAAE,WAAA,iBAAA/D,GAAAgE,KAAAC,IACAA,IACAJ,EAAAlD,KAAAsD,EAAAb,WC/FmV,I,YCO/UyC,EAAY,eACd,EACApJ,EACAwG,GACA,EACA,KACA,KACA,MAIa,OAAA4C,E,2CClBf", "file": "js/chunk-7bfafcfa.6f5bfe4e.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-card',{attrs:{\"shadow\":\"always\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(_vm._s(this.$router.currentRoute.name))]),_c('el-button',{staticStyle:{\"float\":\"right\",\"padding\":\"3px 0\"},attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_vm._v(\"刷新\")])],1),_c('el-row',[_c('el-col',{attrs:{\"span\":4}},[_c('el-input',{attrs:{\"placeholder\":\"请输入订单号/购买人/套餐/手机号\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-col',{attrs:{\"span\":3}},[_c('el-input',{attrs:{\"placeholder\":\"请输入业务员姓名\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-col',{attrs:{\"span\":8}},[_c('el-date-picker',{attrs:{\"type\":\"daterange\",\"unlink-panels\":\"\",\"range-separator\":\"至\",\"start-placeholder\":\"支付开始日期\",\"end-placeholder\":\"支付结束日期\",\"size\":\"mini\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"default-time\":['00:00:00', '23:59:59']},model:{value:(_vm.search.refund_time),callback:function ($$v) {_vm.$set(_vm.search, \"refund_time\", $$v)},expression:\"search.refund_time\"}})],1),_c('el-col',{attrs:{\"span\":1}},[_c('el-button',{attrs:{\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\"搜索\")])],1)],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\",\"margin-top\":\"10px\"},attrs:{\"data\":_vm.list,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"客户信息\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"公司名称:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.client==null ?'':scope.row.client.company))])],1),_c('el-row',[_vm._v(\"联系人:\"+_vm._s(scope.row.client==null ?'': scope.row.client.linkman))]),_vm._v(\" 用户内容 \"),_c('el-row',[_vm._v(\"联系方式:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.client==null ?'': scope.row.client.phone))])],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"套餐内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"套餐名称:\"+_vm._s(scope.row.taocan ? scope.row.taocan.title:''))]),_c('el-row',[_vm._v(\"套餐价格:\"+_vm._s(scope.row.taocan?scope.row.taocan.price:\"\")+\"元\")]),_c('el-row',[_vm._v(\"套餐年份:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.taocan ?scope.row.taocan.year :'')+\"年\")])],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"支付情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"支付类型:\"+_vm._s(scope.row.pay_type == 1 ? \"全款\" : \"分期\" + \"/\" + scope.row.qishu + \"期\"))]),_c('el-row',[_vm._v(\"已付款:\"+_vm._s(scope.row.pay_age)+\"元\")]),_c('el-row',[_vm._v(\"剩余尾款:\"+_vm._s(scope.row.pay_type == 1?0:scope.row.total_price - scope.row.pay_age)+\"元\")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"审核状态\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{on:{\"click\":function($event){return _vm.showStatus(scope.row)}}},[(scope.row.status==1)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#409EFF\"}},[_vm._v(\"未审核\")])]):(scope.row.status==3)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#F56C6C\"}},[_vm._v(\"审核未通过\")])]):(scope.row.status==2)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#67C23A\"}},[_vm._v(\"已通过\")])]):_vm._e(),(scope.row.status==3)?_c('el-row',[_c('span',[_vm._v(\"原因:\"+_vm._s(scope.row.status_msg))])]):_vm._e()],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"member.title\",\"label\":\"业务员\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建时间\"}}),_c('el-table-column',{attrs:{\"prop\":\"end_time\",\"label\":\"到期时间\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"查看\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 移除 \")])]}}])})],1),_c('div',{staticClass:\"page-top\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 100, 200, 300, 400],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":\"订单信息\",\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[(_vm.is_info)?_c('div',[_c('el-descriptions',{attrs:{\"title\":\"客户信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.company))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client.id)}}},[_vm._v(_vm._s(_vm.info.client==null ?'': _vm.info.client.linkman))]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"联系方式\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client.id)}}},[_vm._v(_vm._s(_vm.info.client==null ?'': _vm.info.client.phone))]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"营业执照\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.client.pic_path)}}},[_vm._v(\"查看 \")]):_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(\"暂无\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.tiaojie_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.fawu_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.lian_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"合同上传专用\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.htsczy_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"律师\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.ls_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"业务员\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.ywy_id))])],1),_c('el-descriptions',{attrs:{\"title\":\"债务人信息\",\"colon\":false}},[_c('el-descriptions-item',[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\",\"margin-top\":\"10px\"},attrs:{\"data\":_vm.info.debts,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\"}}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\"}}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额（元）\"}}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\"}})],1)],1)],1),_c('el-descriptions',{attrs:{\"title\":\"套餐内容\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"套餐名称\"}},[_vm._v(_vm._s(_vm.info.taocan.title))]),_c('el-descriptions-item',{attrs:{\"label\":\"套餐价格\"}},[_vm._v(_vm._s(_vm.info.taocan.price))]),_c('el-descriptions-item',{attrs:{\"label\":\"套餐年份\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.taocan.year)+\"年\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"到期时间\"}},[_c('el-date-picker',{attrs:{\"type\":\"datetime\",\"format\":\"yyyy-MM-dd HH:mm:ss\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择日期\",\"size\":\"mini\"},model:{value:(_vm.info.end_time),callback:function ($$v) {_vm.$set(_vm.info, \"end_time\", $$v)},expression:\"info.end_time\"}}),_c('el-tag',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.updateEndTIme()}}},[_vm._v(\"修改\")])],1)],1),_c('el-descriptions',{attrs:{\"title\":\"套餐详情\"}},_vm._l((_vm.info.taocan.num),function(item,index){return _c('el-descriptions-item',{key:index,attrs:{\"label\":item.title}},[_vm._v(_vm._s(item.is_num == 1 ? item.value + \"次\" : \"不限\"))])}),1),_c('el-descriptions',{attrs:{\"title\":\"款项信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"付款类型\"}},[_vm._v(_vm._s(_vm.info.pay_type == 1 ? \"全款\" : \"分期\" + \"/\" + _vm.info.qishu + \"期\"))]),_c('el-descriptions-item',{attrs:{\"label\":\"已付款\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.pay_age)+\"元\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"剩余款\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.total_price - _vm.info.pay_age)+\"元\")])],1)],1),(_vm.info.pay_type==2)?_c('el-descriptions',{attrs:{\"title\":\"期数\"}}):_vm._e(),_vm._l((_vm.info.fenqi),function(item,index){return (_vm.info.pay_type == 2)?_c('el-descriptions',{key:index},[_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'期'}},[_vm._v(\" \"+_vm._s(item.price)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'还款期'}},[_vm._v(\" \"+_vm._s(item.date)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'期凭证'}},[(item.pay_path)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(item.pay_path)}}},[_vm._v(\"查看\")]):_c('el-tag',{attrs:{\"type\":\"warning\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.changePinzhen(index)}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"accept\":\".jpg, .jpeg, .png\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess,\"before-upload\":_vm.beforeUpload}},[_vm._v(\" 上传凭证 \")])],1)],1)],1):_vm._e()}),_c('el-descriptions',{attrs:{\"title\":\"备注信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"具体内容\"}},[_vm._v(_vm._s(_vm.info.desc))])],1)],2):_vm._e()]),_c('el-dialog',{attrs:{\"title\":\"审核内容\",\"visible\":_vm.dialogStatus,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogStatus=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"审核\",\"label-width\":_vm.formLabelWidth}},[_c('div',[_c('el-radio',{attrs:{\"label\":1,\"disabled\":_vm.ruleForm.status==2?true:false},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"未审核 \")]),_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"审核通过\")]),_c('el-radio',{attrs:{\"label\":3},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"审核不通过 \")])],1)]),(_vm.ruleForm.status==3)?_c('el-form-item',{attrs:{\"label\":\"不通过原因\",\"label-width\":_vm.formLabelWidth,\"prop\":\"status_msg\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入内容\"},model:{value:(_vm.ruleForm.status_msg),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status_msg\", $$v)},expression:\"ruleForm.status_msg\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogStatus = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.changeStatus()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"设置到期时间\",\"visible\":_vm.dialogEndTime,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogEndTime=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"format\":\"Y-m-d\",\"placeholder\":\"选择日期\"},model:{value:(_vm.ruleForm.end_time),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"end_time\", $$v)},expression:\"ruleForm.end_time\"}})],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogEndTime = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.changeEndTime()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"60%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{attrs:{\"title\":_vm.用户详情,\"visible\":_vm.dialogViewUserDetail,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogViewUserDetail=$event}}},[_c('user-details',{attrs:{\"id\":_vm.currentId}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div>\r\n\t\t<el-card shadow=\"always\">\r\n\t\t\t<div slot=\"header\" class=\"clearfix\">\r\n\t\t\t\t<span>{{ this.$router.currentRoute.name }}</span>\r\n\t\t\t\t<el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refulsh\">刷新</el-button>\r\n\t\t\t</div>\r\n\t\t\t<el-row>\r\n\t\t\t\t<el-col :span=\"4\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t:size=\"allSize\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :span=\"3\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t:size=\"allSize\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :span=\"8\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\tunlink-panels\r\n\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\tstart-placeholder=\"支付开始日期\"\r\n\t\t\t\t\t\t\tend-placeholder=\"支付结束日期\"\r\n\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-date-picker>\r\n\t\t\t\t</el-col>\r\n\r\n\t\t\t\t<el-col :span=\"1\">\r\n\t\t\t\t\t<el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t\t<!-- <el-row class=\"page-top\">\r\n\t\t\t\t<el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">新增</el-button>\r\n\t\t\t</el-row> -->\r\n\t\t\t<el-table :data=\"list\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"客户信息\">\r\n\t\t\t\t\t<template slot-scope=\"scope\" @click=\"viewUserData(info.client.id)\">\r\n\t\t\t\t\t\t<el-row>公司名称:<el-tag size=\"small\">{{ scope.row.client==null\r\n\t\t\t\t\t  ?'':scope.row.client.company }}</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t<el-row>联系人:{{scope.row.client==null\r\n\t\t\t\t\t  ?'': scope.row.client.linkman }}</el-row>\r\n                        用户内容\r\n\t\t\t\t\t\t<el-row>联系方式:<el-tag size=\"small\">{{scope.row.client==null\r\n\t\t\t\t\t  ?'': scope.row.client.phone }}</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"套餐内容\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-row>套餐名称:{{ scope.row.taocan ? scope.row.taocan.title:''}}</el-row>\r\n\t\t\t\t\t\t<el-row>套餐价格:{{ scope.row.taocan?scope.row.taocan.price:\"\" }}元</el-row>\r\n\t\t\t\t\t\t<el-row>套餐年份:<el-tag size=\"small\">{{ scope.row.taocan ?scope.row.taocan.year :''}}年</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"支付情况\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-row>支付类型:{{\r\n                scope.row.pay_type == 1\r\n                  ? \"全款\"\r\n                  : \"分期\" + \"/\" + scope.row.qishu + \"期\"\r\n              }}</el-row>\r\n\t\t\t\t\t\t<el-row>已付款:{{ scope.row.pay_age }}元</el-row>\r\n\t\t\t\t\t\t<el-row>剩余尾款:{{\r\n                 scope.row.pay_type == 1?0:scope.row.total_price - scope.row.pay_age\r\n              }}元</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<!-- <el-table-column prop=\"title\" label=\"支付凭证\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div v-if=\"scope.row.pay_type == 1\">\r\n\t\t\t\t\t\t\t<div class=\"pictrueBox pictrue\">\r\n\t\t\t\t\t\t\t\t<img :src=\"scope.row.pay_path\" @click=\"showImage(scope.row.pay_path)\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div v-else>\r\n\t\t\t\t\t\t\t<div class=\"pictrueBox pictrue\" v-for=\"(item,index) in scope.row.fenqi\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<img :src=\"item.pay_path\" @click=\"showImage(item.pay_path)\" v-if=\"item.pay_path\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column> -->\r\n\t\t\t\t<el-table-column  label=\"审核状态\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div  @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-row v-if=\"scope.row.status==1\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#409EFF\">未审核</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-else-if=\"scope.row.status==3\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#F56C6C\">审核未通过</span>\r\n\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-else-if=\"scope.row.status==2\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#67C23A\">已通过</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-if=\"scope.row.status==3\">\r\n\r\n\t\t\t\t\t\t\t\t<span>原因:{{scope.row.status_msg}}</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\"> </el-table-column>\r\n\t\t\t\t<el-table-column prop=\"create_time\" label=\"创建时间\"> </el-table-column>\r\n\t\t\t\t<el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\">查看</el-button>\r\n\r\n\t\t\t\t\t\t<el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">\r\n\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t<div class=\"page-top\">\r\n\t\t\t\t<el-pagination @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t:page-sizes=\"[20, 100, 200, 300, 400]\" :page-size=\"size\"\r\n\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\" :total=\"total\">\r\n\t\t\t\t</el-pagination>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\t\t<el-dialog title=\"订单信息\" :visible.sync=\"dialogFormVisible\" :close-on-click-modal=\"false\">\r\n\t\t\t<div v-if=\"is_info\">\r\n\t\t\t\t<el-descriptions title=\"客户信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"公司名称\">{{info.client==null\r\n\t\t\t\t\t  ?'':info.client.company\r\n\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<el-descriptions-item label=\"联系人\"><el-tag @click=\"viewUserData(info.client.id)\" size=\"small\" v-if=\"info.client!=null\">{{info.client==null\r\n\t\t\t\t\t  ?'':\r\n\t\t\t\t\t  info.client.linkman\r\n\t\t\t\t\t}}</el-tag></el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"联系方式\"><el-tag @click=\"viewUserData(info.client.id)\" size=\"small\" v-if=\"info.client!=null\">{{info.client==null\r\n\t\t\t\t\t  ?'':\r\n\t\t\t\t\t  info.client.phone\r\n\t\t\t\t\t}}</el-tag></el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\r\n\t\t\t\t\t\t<el-tag @click=\"showImage(info.client.pic_path)\" size=\"small\" v-if=\"info.client!=null\">查看\r\n\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t<el-tag size=\"small\" v-else>暂无</el-tag>\r\n\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"调解员\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.tiaojie_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"法务专员\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.fawu_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"立案专员\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.lian_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"合同上传专用\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.htsczy_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"律师\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.ls_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"业务员\">{{info.client==null\r\n\t\t\t\t\t\t?'':info.client.ywy_id\r\n\t\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\r\n                <el-descriptions title=\"债务人信息\" :colon=\"false\">\r\n                    <el-descriptions-item>\r\n                        <el-table\r\n                                :data=\"info.debts\"\r\n                                style=\"width: 100%; margin-top: 10px\"\r\n                                v-loading=\"loading\"\r\n                                size=\"mini\"\r\n                        >\r\n                            <el-table-column prop=\"name\" label=\"债务人姓名\"> </el-table-column>\r\n                            <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n\t\t\t\t\t\t\t<el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n\t\t\t\t\t\t\t<el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n                        </el-table></el-descriptions-item>\r\n                </el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"套餐内容\">\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">{{\r\n            info.taocan.title\r\n          }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">{{\r\n            info.taocan.price\r\n          }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐年份\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"到期时间\">\r\n<!--\t\t\t\t\t\t<el-tag size=\"small\">{{ info.end_time }}</el-tag>-->\r\n\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\tv-model=\"info.end_time\"\r\n\t\t\t\t\t\t\t\ttype=\"datetime\"\r\n\t\t\t\t\t\t\t\tformat=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"选择日期\"\r\n\t\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t</el-date-picker>\r\n\t\t\t\t\t\t<el-tag @click=\"updateEndTIme()\" size=\"small\" style=\"cursor:pointer;\">修改</el-tag>\r\n\r\n\t\t\t\t\t</el-descriptions-item>\r\n\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"套餐详情\">\r\n\t\t\t\t\t<el-descriptions-item :label=\"item.title\" v-for=\"(item, index) in info.taocan.num\" :key=\"index\">{{\r\n              item.is_num == 1 ? item.value + \"次\" : \"不限\"\r\n            }}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"款项信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"付款类型\">{{\r\n                info.pay_type == 1\r\n                  ? \"全款\"\r\n                  : \"分期\" + \"/\" + info.qishu + \"期\"\r\n              }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"已付款\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{info.pay_age}}元</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"剩余款\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{info.total_price - info.pay_age}}元</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"期数\" v-if=\"info.pay_type==2\">\r\n\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions v-for=\"(item,index) in info.fenqi\" v-if=\"info.pay_type == 2\" :key=\"index\">\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'期'\">\r\n\t\t\t\t\t\t{{item.price}}\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'还款期'\">\r\n\t\t\t\t\t\t{{item.date}}\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'期凭证'\">\r\n\t\t\t\t\t\t<el-tag @click=\"showImage(item.pay_path)\" size=\"small\" v-if=\"item.pay_path\">查看</el-tag>\r\n\r\n\t\t\t\t\t\t<el-tag type=\"warning\" size=\"small\" v-else @click=\"changePinzhen(index)\">\r\n\t\t\t\t\t\t\t<el-upload action=\"/admin/Upload/uploadImage\" accept=\".jpg, .jpeg, .png\"\r\n\t\t\t\t\t\t\t\t:show-file-list=\"false\" :on-success=\"handleSuccess\" :before-upload=\"beforeUpload\">\r\n\t\t\t\t\t\t\t\t上传凭证\r\n\t\t\t\t\t\t\t</el-upload>\r\n\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"备注信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"具体内容\">{{\r\n\t\t\t\t\t  info.desc\r\n\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog title=\"审核内容\" :visible.sync=\"dialogStatus\" :close-on-click-modal=\"false\">\r\n\t\t\t<el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n\t\t\t\t<el-form-item label=\"审核\" :label-width=\"formLabelWidth\">\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"1\" :disabled=\"ruleForm.status==2?true:false\">未审核\r\n\t\t\t\t\t\t</el-radio>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"2\">审核通过</el-radio>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"3\">审核不通过\r\n\t\t\t\t\t\t</el-radio>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item label=\"不通过原因\" :label-width=\"formLabelWidth\" prop=\"status_msg\" v-if=\"ruleForm.status==3\">\r\n\t\t\t\t\t<el-input type=\"textarea\" :rows=\"3\" placeholder=\"请输入内容\" v-model=\"ruleForm.status_msg\">\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"dialogStatus = false\">取 消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"changeStatus()\">确 定</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog title=\"设置到期时间\" :visible.sync=\"dialogEndTime\" :close-on-click-modal=\"false\">\r\n\t\t\t<el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n\r\n\t\t\t\t<el-date-picker v-model=\"ruleForm.end_time\" type=\"date\" format=\"Y-m-d\" placeholder=\"选择日期\">\r\n\t\t\t\t</el-date-picker>\r\n\r\n\t\t\t</el-form>\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"dialogEndTime = false\">取 消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"changeEndTime()\">确 定</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"60%\">\r\n\t\t\t<el-image :src=\"show_image\"></el-image>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog\r\n\t\t\t\t:title=\"用户详情\"\r\n\t\t\t\t:visible.sync=\"dialogViewUserDetail\"\r\n\t\t\t\t:close-on-click-modal=\"false\"\r\n\t\t>\r\n\t\t\t<user-details :id=\"currentId\"></user-details>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tallSize: \"mini\",\r\n\t\t\t\tlist: [],\r\n\t\t\t\ttotal: 1,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tsize: 20,\r\n\t\t\t\tcurrentId:0,\r\n\t\t\t\tsearch: {\r\n\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t},\r\n\t\t\t\tloading: true,\r\n\t\t\t\turl: \"/Dingdan/\",\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tis_info: false,\r\n\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\tdialogVisible: false,\r\n\t\t\t\tdialogViewUserDetail: false,\r\n\t\t\t\tdialogStatus: false,\r\n\t\t\t\tdialogEndTime: false,\r\n\t\t\t\tdialogAddOrder: false,\r\n\t\t\t\truleForm: {\r\n\t\t\t\t\tstatus: 1,\r\n\t\t\t\t\tstatus_msg: '',\r\n\t\t\t\t\tid: '',\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\tstatus_msg: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t}, ],\r\n\t\t\t\t\tend_time: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"请填写时间\",\r\n\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t}, ],\r\n\t\t\t\t},\r\n\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\tshow_image: '',\r\n\t\t\t\tindex: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\teditData(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (id != 0) {\r\n\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t},\r\n\t\t\tviewUserData(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (id != 0) {\r\n\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t},\r\n\t\t\tgetInfo(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdelData(index, id) {\r\n\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tupdateEndTIme() {\r\n\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\trefulsh() {\r\n\t\t\t\tthis.$router.go(0);\r\n\t\t\t},\r\n\t\t\tgetData() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.loading = true;\r\n\t\t\t\t_this\r\n\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t)\r\n\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaveData() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleSizeChange(val) {\r\n\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\tthis.getData();\r\n\t\t\t},\r\n\t\t\thandleCurrentChange(val) {\r\n\t\t\t\tthis.page = val;\r\n\t\t\t\tthis.getData();\r\n\t\t\t},\r\n\t\t\thandleSuccess(res) {\r\n\t\t\t\tlet _this = this\r\n\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\tbeforeUpload(file) {\r\n\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdelImage(file, fileName) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowImage(file) {\r\n\t\t\t\tthis.show_image = file;\r\n\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t},\r\n\t\t\tchangePinzhen(index) {\r\n\t\t\t\tthis.index = index\r\n\t\t\t},\r\n\t\t\tshowStatus(row) {\r\n\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\tthis.ruleForm = row\r\n\t\t\t},\r\n\t\t\tshowEndTime(row) {\r\n\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\tthis.ruleForm = row\r\n\t\t\t},\r\n\t\t\tchangeEndTime() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n<style scoped>\r\n\t.page-top {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t.el_input {\r\n\t\twidth: 475px;\r\n\t}\r\n\r\n\t.pictrueBox {\r\n\t\tdisplay: inline-block !important;\r\n\t}\r\n\r\n\t.pictrue {\r\n\t\twidth: 60px;\r\n\t\theight: 60px;\r\n\t\tborder: 1px dotted rgba(0, 0, 0, 0.1);\r\n\t\tmargin-right: 15px;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.pictrue img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./dingdan.vue?vue&type=template&id=0ff878f8&scoped=true\"\nimport script from \"./dingdan.vue?vue&type=script&lang=js\"\nexport * from \"./dingdan.vue?vue&type=script&lang=js\"\nimport style0 from \"./dingdan.vue?vue&type=style&index=0&id=0ff878f8&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ff878f8\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-row',[_c('el-descriptions',{attrs:{\"title\":\"客户信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.info.company))]),_c('el-descriptions-item',{attrs:{\"label\":\"手机号\"}},[_vm._v(_vm._s(_vm.info.phone))]),_c('el-descriptions-item',{attrs:{\"label\":\"名称\"}},[_vm._v(_vm._s(_vm.info.nickname))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[_vm._v(_vm._s(_vm.info.linkman))]),_c('el-descriptions-item',{attrs:{\"label\":\"头像\"}},[(_vm.info.headimg !='' && _vm.info.headimg!=null)?_c('img',{staticStyle:{\"width\":\"50px\",\"height\":\"50px\"},attrs:{\"src\":_vm.info.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_vm._e()]),_c('el-descriptions-item',{attrs:{\"label\":\"用户来源\"}},[_vm._v(_vm._s(_vm.info.yuangong_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系方式\"}},[_vm._v(_vm._s(_vm.info.linkphone))]),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(_vm._s(_vm.info.tiaojie_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(_vm._s(_vm.info.fawu_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(_vm._s(_vm.info.lian_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"合同上传专用\"}},[_vm._v(_vm._s(_vm.info.htsczy_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"律师\"}},[_vm._v(_vm._s(_vm.info.ls_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"业务员\"}},[_vm._v(_vm._s(_vm.info.ywy_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"营业执照\"}},[(_vm.info.license !='' && _vm.info.license!=null)?_c('img',{staticStyle:{\"width\":\"50px\",\"height\":\"50px\"},attrs:{\"src\":_vm.info.license},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}}):_vm._e()]),_c('el-descriptions-item',{attrs:{\"label\":\"开始时间\"}},[_vm._v(_vm._s(_vm.info.start_time))]),_c('el-descriptions-item',{attrs:{\"label\":\"会员年限\"}},[_vm._v(_vm._s(_vm.info.year)+\"年\")])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <el-row>\r\n    <el-descriptions title=\"客户信息\">\r\n      <el-descriptions-item label=\"公司名称\">{{\r\n        info.company\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"手机号\">{{\r\n        info.phone\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"名称\">{{\r\n        info.nickname\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系人\">{{\r\n        info.linkman\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"头像\">\r\n        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n             :src=\"info.headimg\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.headimg)\"\r\n        /></el-descriptions-item>\r\n      <el-descriptions-item label=\"用户来源\">{{\r\n        info.yuangong_id\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系方式\">{{\r\n        info.linkphone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"调解员\">{{\r\n            info.tiaojie_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"法务专员\">{{\r\n            info.fawu_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"立案专员\">{{\r\n            info.lian_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"合同上传专用\">{{\r\n            info.htsczy_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"律师\">{{\r\n            info.ls_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"业务员\">{{\r\n            info.ywy_id\r\n            }}\r\n        </el-descriptions-item>\r\n      <el-descriptions-item label=\"营业执照\">\r\n        <img v-if=\"info.license !='' && info.license!=null\"\r\n             :src=\"info.license\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.license)\"\r\n        />\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"开始时间\">{{\r\n        info.start_time\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"会员年限\">{{\r\n        info.year\r\n        }}年</el-descriptions-item>\r\n    </el-descriptions>\r\n  </el-row>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: String,\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: [] // 用于存储接口返回的数据\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          if (resp) {\r\n            _this.info = resp.data;\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=9e80c8c2\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=style&index=0&id=0ff878f8&prod&scoped=true&lang=css\""], "sourceRoot": ""}