{"version": 3, "sources": ["webpack:///./src/components/UserDetail.vue?89c4", "webpack:///./src/views/pages/taocan/dingdan.vue?e0b7", "webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./node_modules/core-js/modules/web.url-search-params.has.js", "webpack:///./node_modules/core-js/modules/web.url-search-params.size.js", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/web.url-search-params.delete.js", "webpack:///./src/views/pages/taocan/dingdan.vue", "webpack:///src/views/pages/taocan/dingdan.vue", "webpack:///./src/views/pages/taocan/dingdan.vue?61a7", "webpack:///./src/views/pages/taocan/dingdan.vue?3379", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f", "webpack:///./node_modules/core-js/internals/array-reduce.js", "webpack:///./node_modules/core-js/internals/validate-arguments-length.js", "webpack:///./node_modules/core-js/internals/define-built-in-accessor.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "CHROME_BUG", "FORCED", "target", "proto", "forced", "reduce", "callbackfn", "length", "arguments", "this", "undefined", "defineBuiltIn", "uncurryThis", "toString", "validateArgumentsLength", "$URLSearchParams", "URLSearchParams", "URLSearchParamsPrototype", "prototype", "getAll", "$has", "has", "params", "name", "$value", "values", "value", "index", "enumerable", "unsafe", "DESCRIPTORS", "defineBuiltInAccessor", "for<PERSON>ach", "get", "count", "configurable", "global", "classof", "module", "exports", "process", "append", "$delete", "push", "entries", "v", "k", "key", "entry", "dindex", "found", "<PERSON><PERSON><PERSON><PERSON>", "render", "_vm$info$client", "_vm$info$client3", "_vm$info$client5", "_vm$info$client6", "_vm$info$client8", "_vm$info$client9", "_vm$info$client10", "_vm$info$client11", "_vm$info$client12", "_vm", "_c", "_self", "staticClass", "_v", "_s", "$router", "currentRoute", "attrs", "on", "refulsh", "hasImportantNotifications", "dismissNotifications", "pendingOrders", "$event", "filterByStatus", "_e", "expiringOrders", "showExpiringOrders", "expiredOrders", "showExpiredOrders", "highValueOrders", "showHighValueOrders", "total", "approvedOrders", "showRevenueChart", "totalRevenue", "slot", "exportData", "refreshData", "batch<PERSON><PERSON><PERSON>", "search", "nativeOn", "type", "indexOf", "_k", "keyCode", "getData", "model", "keyword", "callback", "$$v", "$set", "expression", "salesman", "status", "resetSearch", "toggleAdvanced", "class", "showAdvanced", "directives", "rawName", "refund_time", "payType", "minAmount", "maxAmount", "packageType", "sortBy", "applyAdvancedSearch", "clearAdvancedSearch", "selectedRows", "batchApprove", "batchReject", "loading", "list", "tableRowClassName", "handleSelectionChange", "scopedSlots", "_u", "fn", "scope", "_scope$row$client2", "_scope$row$client3", "_scope$row$client4", "_scope$row$client", "viewUserData", "row", "client", "id", "company", "linkman", "phone", "_scope$row$taocan", "_scope$row$taocan2", "_scope$row$taocan3", "taocan", "title", "price", "year", "pay_type", "qishu", "pay_age", "total_price", "Math", "round", "showStatus", "getStatusType", "getStatusText", "status_msg", "_scope$row$member", "member", "formatDate", "create_time", "end_time", "getRemainingDaysClass", "getRemainingDays", "editData", "quickApprove", "quickReject", "delData", "$index", "size", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "is_info", "info", "_vm$info$client2", "_vm$info$client4", "pic_path", "_vm$info$client7", "showImage", "tiaojie_id", "fawu_id", "lian_id", "htsczy_id", "ls_id", "debts", "money", "getDebtStatusType", "downloadOrder", "dialogVisible", "show_image", "showRevenueDialog", "staticStyle", "fullPaymentCount", "installmentPaymentCount", "rejectedOrders", "showExportDialog", "exportForm", "format", "fields", "range", "date<PERSON><PERSON><PERSON>", "exportLoading", "executeExport", "staticRenderFns", "components", "UserDetails", "data", "allSize", "page", "url", "currentId", "dialogViewUserDetail", "upload_index", "dialogStatus", "dialogEndTime", "ruleForm", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "showNotifications", "computed", "Array", "isArray", "filter", "item", "sum", "toLocaleString", "today", "Date", "sevenDaysLater", "getTime", "endDate", "mounted", "methods", "_this", "getInfo", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "code", "$message", "splice", "msg", "catch", "updateEndTIme", "postRequest", "go", "saveData", "$refs", "validate", "valid", "val", "handleSuccess", "res", "fenqi", "pay_path", "beforeUpload", "file", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "changePinzhen", "showEndTime", "changeEndTime", "changeStatus", "statusMap", "dateStr", "toLocaleDateString", "selection", "setTimeout", "formatText", "rangeText", "blob", "generateExportData", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "exportList", "map", "_item$client", "_item$client2", "_item$client3", "_item$taocan", "_item$taocan2", "_item$taocan3", "_item$member", "includes", "csv<PERSON><PERSON>nt", "convertToCSV", "Blob", "headers", "Object", "keys", "csvRows", "join", "header", "warning", "pendingRows", "promises", "Promise", "all", "responses", "successCount", "$prompt", "inputPlaceholder", "inputValidator", "trim", "_row$client", "_row$client2", "remainingDays", "ceil", "abs", "component", "fails", "METHOD_NAME", "argument", "method", "call", "nickname", "linkphone", "yuangong_id", "start_time", "headimg", "license", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "background", "color", "tel", "viewDebtDetail", "props", "String", "Number", "watch", "immediate", "handler", "newId", "console", "log", "testUserData", "imageUrl", "debt", "aCallable", "toObject", "IndexedObject", "lengthOfArrayLike", "$TypeError", "TypeError", "REDUCE_EMPTY", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "i", "right", "passed", "makeBuiltIn", "defineProperty", "descriptor", "getter", "set", "setter", "f"], "mappings": "kHAAA,W,kCCAA,W,oCCCA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAIlBC,GAAcD,GAAWD,EAAiB,IAAMA,EAAiB,GACjEG,EAASD,IAAeH,EAAoB,UAIhDH,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,OAAQH,GAAU,CAClDI,OAAQ,SAAgBC,GACtB,IAAIC,EAASC,UAAUD,OACvB,OAAOZ,EAAQc,KAAMH,EAAYC,EAAQA,EAAS,EAAIC,UAAU,QAAKE,O,6DChBzE,IAAIC,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmBC,gBACnBC,EAA2BF,EAAiBG,UAC5CC,EAASP,EAAYK,EAAyBE,QAC9CC,EAAOR,EAAYK,EAAyBI,KAC5CC,EAAS,IAAIP,EAAiB,QAI9BO,EAAOD,IAAI,IAAK,IAAOC,EAAOD,IAAI,SAAKX,IACzCC,EAAcM,EAA0B,OAAO,SAAaM,GAC1D,IAAIhB,EAASC,UAAUD,OACnBiB,EAASjB,EAAS,OAAIG,EAAYF,UAAU,GAChD,GAAID,QAAqBG,IAAXc,EAAsB,OAAOJ,EAAKX,KAAMc,GACtD,IAAIE,EAASN,EAAOV,KAAMc,GAC1BT,EAAwBP,EAAQ,GAChC,IAAImB,EAAQb,EAASW,GACjBG,EAAQ,EACZ,MAAOA,EAAQF,EAAOlB,OACpB,GAAIkB,EAAOE,OAAaD,EAAO,OAAO,EACtC,OAAO,IACR,CAAEE,YAAY,EAAMC,QAAQ,K,kCCzBjC,IAAIC,EAAc,EAAQ,QACtBlB,EAAc,EAAQ,QACtBmB,EAAwB,EAAQ,QAEhCd,EAA2BD,gBAAgBE,UAC3Cc,EAAUpB,EAAYK,EAAyBe,SAI/CF,KAAiB,SAAUb,IAC7Bc,EAAsBd,EAA0B,OAAQ,CACtDgB,IAAK,WACH,IAAIC,EAAQ,EAEZ,OADAF,EAAQvB,MAAM,WAAcyB,OACrBA,GAETC,cAAc,EACdP,YAAY,K,oCCjBhB,IAAIQ,EAAS,EAAQ,QACjBC,EAAU,EAAQ,QAEtBC,EAAOC,QAAsC,YAA5BF,EAAQD,EAAOI,U,6DCHhC,IAAI7B,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmBC,gBACnBC,EAA2BF,EAAiBG,UAC5CuB,EAAS7B,EAAYK,EAAyBwB,QAC9CC,EAAU9B,EAAYK,EAAyB,WAC/Ce,EAAUpB,EAAYK,EAAyBe,SAC/CW,EAAO/B,EAAY,GAAG+B,MACtBrB,EAAS,IAAIP,EAAiB,eAElCO,EAAO,UAAU,IAAK,GAGtBA,EAAO,UAAU,SAAKZ,GAElBY,EAAS,KAAO,OAClBX,EAAcM,EAA0B,UAAU,SAAUM,GAC1D,IAAIhB,EAASC,UAAUD,OACnBiB,EAASjB,EAAS,OAAIG,EAAYF,UAAU,GAChD,GAAID,QAAqBG,IAAXc,EAAsB,OAAOkB,EAAQjC,KAAMc,GACzD,IAAIqB,EAAU,GACdZ,EAAQvB,MAAM,SAAUoC,EAAGC,GACzBH,EAAKC,EAAS,CAAEG,IAAKD,EAAGpB,MAAOmB,OAEjC/B,EAAwBP,EAAQ,GAChC,IAMIyC,EANAD,EAAMlC,EAASU,GACfG,EAAQb,EAASW,GACjBG,EAAQ,EACRsB,EAAS,EACTC,GAAQ,EACRC,EAAgBP,EAAQrC,OAE5B,MAAOoB,EAAQwB,EACbH,EAAQJ,EAAQjB,KACZuB,GAASF,EAAMD,MAAQA,GACzBG,GAAQ,EACRR,EAAQjC,KAAMuC,EAAMD,MACfE,IAET,MAAOA,EAASE,EACdH,EAAQJ,EAAQK,KACVD,EAAMD,MAAQA,GAAOC,EAAMtB,QAAUA,GAAQe,EAAOhC,KAAMuC,EAAMD,IAAKC,EAAMtB,SAElF,CAAEE,YAAY,EAAMC,QAAQ,K,yCC/CjC,IAAIuB,EAAS,WAAiB,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAKC,EAAIrD,KAAKsD,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAII,GAAG,IAAIJ,EAAIK,GAAG1D,KAAK2D,QAAQC,aAAa9C,MAAM,OAAOwC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAII,GAAG,qBAAqBH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmBC,GAAG,CAAC,MAAQT,EAAIU,UAAU,CAACV,EAAII,GAAG,aAAa,KAAMJ,EAAIW,0BAA2BV,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,oBAAoBK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,8CAA8CF,EAAG,OAAO,CAACE,YAAY,sBAAsB,CAACH,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,OAAO,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAIY,uBAAuB,CAACX,EAAG,IAAI,CAACE,YAAY,qBAAqB,GAAGF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAAEH,EAAIa,cAAgB,EAAGZ,EAAG,MAAM,CAACE,YAAY,2BAA2BM,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIe,eAAe,QAAQ,CAACd,EAAG,MAAM,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACD,EAAII,GAAGJ,EAAIK,GAAGL,EAAIa,kBAAkBb,EAAII,GAAG,oBAAoBH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,YAAY,CAACR,EAAII,GAAG,WAAW,KAAKJ,EAAIgB,KAAMhB,EAAIiB,eAAexE,OAAS,EAAGwD,EAAG,MAAM,CAACE,YAAY,4BAA4BM,GAAG,CAAC,MAAQT,EAAIkB,qBAAqB,CAACjB,EAAG,MAAM,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACD,EAAII,GAAGJ,EAAIK,GAAGL,EAAIiB,eAAexE,WAAWuD,EAAII,GAAG,uBAAuBH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,YAAY,CAACR,EAAII,GAAG,WAAW,KAAKJ,EAAIgB,KAAMhB,EAAImB,cAAc1E,OAAS,EAAGwD,EAAG,MAAM,CAACE,YAAY,0BAA0BM,GAAG,CAAC,MAAQT,EAAIoB,oBAAoB,CAACnB,EAAG,MAAM,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACD,EAAII,GAAGJ,EAAIK,GAAGL,EAAImB,cAAc1E,WAAWuD,EAAII,GAAG,uBAAuBH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,WAAW,CAACR,EAAII,GAAG,WAAW,KAAKJ,EAAIgB,KAAMhB,EAAIqB,gBAAgB5E,OAAS,EAAGwD,EAAG,MAAM,CAACE,YAAY,yBAAyBM,GAAG,CAAC,MAAQT,EAAIsB,sBAAsB,CAACrB,EAAG,MAAM,CAACE,YAAY,qBAAqBF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,SAAS,CAACD,EAAII,GAAGJ,EAAIK,GAAGL,EAAIqB,gBAAgB5E,WAAWuD,EAAII,GAAG,oBAAoBH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,YAAY,CAACR,EAAII,GAAG,WAAW,KAAKJ,EAAIgB,YAAY,GAAGhB,EAAIgB,KAAKf,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,YAAYM,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIe,eAAe,OAAO,CAACd,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,sBAAsBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIuB,UAAUtB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAII,GAAG,iBAAiBH,EAAG,SAAS,CAACO,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,YAAYM,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIe,eAAe,QAAQ,CAACd,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIa,kBAAkBZ,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAII,GAAG,iBAAiBH,EAAG,SAAS,CAACO,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,YAAYM,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIe,eAAe,QAAQ,CAACd,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,IAAI,CAACE,YAAY,2BAA2BF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwB,mBAAmBvB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAII,GAAG,kBAAkBH,EAAG,SAAS,CAACO,MAAM,CAAC,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,GAAK,EAAE,GAAK,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,YAAYM,GAAG,CAAC,MAAQT,EAAIyB,mBAAmB,CAACxB,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAI0B,iBAAiBzB,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAII,GAAG,mBAAmB,IAAI,GAAGH,EAAG,UAAU,CAACE,YAAY,cAAcK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,aAAaH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAII,GAAG,mBAAmBH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,kBAAkB,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQT,EAAI4B,aAAa,CAAC5B,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,mBAAmBC,GAAG,CAAC,MAAQT,EAAI6B,cAAc,CAAC7B,EAAII,GAAG,WAAW,GAAGH,EAAG,YAAY,CAACE,YAAY,iBAAiBK,MAAM,CAAC,KAAO,UAAU,KAAO,iBAAiBC,GAAG,CAAC,MAAQT,EAAI8B,aAAa,CAAC9B,EAAII,GAAG,aAAa,KAAKH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcK,MAAM,CAAC,MAAQR,EAAI+B,OAAO,QAAS,IAAO,CAAC9B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,eAAe,CAACE,YAAY,mBAAmBK,MAAM,CAAC,MAAQ,UAAU,CAACP,EAAG,WAAW,CAACE,YAAY,eAAeK,MAAM,CAAC,YAAc,oBAAoB,UAAY,GAAG,cAAc,kBAAkBwB,SAAS,CAAC,MAAQ,SAASlB,GAAQ,OAAIA,EAAOmB,KAAKC,QAAQ,QAAQlC,EAAImC,GAAGrB,EAAOsB,QAAQ,QAAQ,GAAGtB,EAAO7B,IAAI,SAAgB,KAAYe,EAAIqC,YAAYC,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOQ,QAASC,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,UAAWU,IAAME,WAAW,qBAAqB,GAAG1C,EAAG,eAAe,CAACE,YAAY,cAAcK,MAAM,CAAC,MAAQ,QAAQ,CAACP,EAAG,WAAW,CAACE,YAAY,gBAAgBK,MAAM,CAAC,YAAc,WAAW,UAAY,IAAI8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOa,SAAUJ,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,WAAYU,IAAME,WAAW,sBAAsB,GAAG1C,EAAG,eAAe,CAACE,YAAY,cAAcK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,YAAY,CAACE,YAAY,gBAAgBK,MAAM,CAAC,YAAc,OAAO,UAAY,IAAI8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOc,OAAQL,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,SAAUU,IAAME,WAAW,kBAAkB,CAAC1C,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,MAAMP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,MAAM,MAAQ,OAAOP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,MAAM,MAAQ,OAAOP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,MAAM,MAAQ,QAAQ,IAAI,GAAGP,EAAG,eAAe,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIqC,aAAa,CAACrC,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACE,YAAY,YAAYK,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQT,EAAI8C,cAAc,CAAC9C,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAI+C,iBAAiB,CAAC9C,EAAG,IAAI,CAAC+C,MAAMhD,EAAIiD,aAAe,mBAAqB,uBAAuBjD,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIiD,aAAe,KAAO,QAAQ,QAAQ,MAAM,GAAGhD,EAAG,aAAa,CAACO,MAAM,CAAC,KAAO,eAAe,CAACP,EAAG,MAAM,CAACiD,WAAW,CAAC,CAACzF,KAAK,OAAO0F,QAAQ,SAASvF,MAAOoC,EAAIiD,aAAcN,WAAW,iBAAiBxC,YAAY,mBAAmB,CAACF,EAAG,aAAa,CAACO,MAAM,CAAC,mBAAmB,SAAS,CAACP,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAII,GAAG,cAAcH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,eAAe,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,iBAAiB,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,YAAY,kBAAkB,IAAI,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,sBAAsB,eAAe,CAAC,WAAY,aAAa8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOqB,YAAaZ,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,cAAeU,IAAME,WAAW,yBAAyB,GAAG1C,EAAG,eAAe,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,YAAY,CAACE,YAAY,aAAaK,MAAM,CAAC,YAAc,UAAU8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOsB,QAASb,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,UAAWU,IAAME,WAAW,mBAAmB,CAAC1C,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,MAAMP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,OAAOP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,QAAQ,IAAI,GAAGP,EAAG,eAAe,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,kBAAkB,CAACO,MAAM,CAAC,YAAc,OAAO,IAAM,EAAE,UAAY,EAAE,oBAAoB,QAAQ,KAAO,SAAS8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOuB,UAAWd,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,YAAaU,IAAME,WAAW,sBAAsB1C,EAAG,OAAO,CAACE,YAAY,mBAAmB,CAACH,EAAII,GAAG,OAAOH,EAAG,kBAAkB,CAACO,MAAM,CAAC,YAAc,OAAO,IAAM,EAAE,UAAY,EAAE,oBAAoB,QAAQ,KAAO,SAAS8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOwB,UAAWf,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,YAAaU,IAAME,WAAW,uBAAuB,MAAM,GAAG1C,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,eAAe,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,YAAY,CAACE,YAAY,iBAAiBK,MAAM,CAAC,YAAc,OAAO,UAAY,IAAI8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAOyB,YAAahB,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,cAAeU,IAAME,WAAW,uBAAuB,CAAC1C,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,MAAMP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,WAAWP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,cAAcP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,mBAAmB,IAAI,GAAGP,EAAG,eAAe,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,YAAY,CAACE,YAAY,cAAcK,MAAM,CAAC,YAAc,QAAQ8B,MAAM,CAAC1E,MAAOoC,EAAI+B,OAAO0B,OAAQjB,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI+B,OAAQ,SAAUU,IAAME,WAAW,kBAAkB,CAAC1C,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,iBAAiBP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,aAAaP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,YAAYP,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,eAAe,IAAI,GAAGP,EAAG,eAAe,CAACE,YAAY,oBAAoB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,iBAAiBC,GAAG,CAAC,MAAQT,EAAI0D,sBAAsB,CAAC1D,EAAII,GAAG,YAAYH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,iBAAiBC,GAAG,CAAC,MAAQT,EAAI2D,sBAAsB,CAAC3D,EAAII,GAAG,aAAa,IAAI,MAAM,MAAM,IAAI,KAAKH,EAAG,UAAU,CAACE,YAAY,aAAaK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAII,GAAG,YAAaJ,EAAI4D,aAAanH,OAAS,EAAGwD,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAII,GAAG,QAAQJ,EAAIK,GAAGL,EAAI4D,aAAanH,QAAQ,SAASuD,EAAIgB,OAAOf,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,oBAAoBC,GAAG,CAAC,MAAQT,EAAI4B,aAAa,CAAC5B,EAAII,GAAG,YAAYH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,gBAAgB,SAAuC,IAA5BR,EAAI4D,aAAanH,OAAa,KAAO,WAAWgE,GAAG,CAAC,MAAQT,EAAI6D,eAAe,CAAC7D,EAAII,GAAG,YAAYH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,gBAAgB,SAAuC,IAA5BR,EAAI4D,aAAanH,OAAa,KAAO,UAAUgE,GAAG,CAAC,MAAQT,EAAI8D,cAAc,CAAC9D,EAAII,GAAG,aAAa,KAAKH,EAAG,WAAW,CAACiD,WAAW,CAAC,CAACzF,KAAK,UAAU0F,QAAQ,YAAYvF,MAAOoC,EAAI+D,QAASpB,WAAW,YAAYxC,YAAY,eAAeK,MAAM,CAAC,KAAOR,EAAIgE,KAAK,iBAAiBhE,EAAIiE,mBAAmBxD,GAAG,CAAC,mBAAmBT,EAAIkE,wBAAwB,CAACjE,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,YAAY,MAAQ,QAAQP,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAM,IAAAC,EAAAC,EAAAC,EAAC,MAAO,CAACxE,EAAG,MAAM,CAACE,YAAY,cAAcM,GAAG,CAAC,MAAQ,SAASK,GAAO,IAAA4D,EAAC,OAAO1E,EAAI2E,aAA6B,QAAjBD,EAACJ,EAAMM,IAAIC,cAAM,IAAAH,OAAA,EAAhBA,EAAkBI,OAAO,CAAC7E,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,8BAA8BF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhBkE,EAAAD,EAAMM,IAAIC,cAAM,IAAAN,OAAA,EAAhBA,EAAkBQ,UAAW,OAAO,OAAO9E,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhBmE,EAAAF,EAAMM,IAAIC,cAAM,IAAAL,OAAA,EAAhBA,EAAkBQ,UAAW,OAAO,SAAS/E,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBH,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhBoE,EAAAH,EAAMM,IAAIC,cAAM,IAAAJ,OAAA,EAAhBA,EAAkBQ,QAAS,OAAO,oBAAoBhF,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAM,IAAAY,EAAAC,EAAAC,EAAC,MAAO,CAACnF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,gBAAgBH,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhB6E,EAAAZ,EAAMM,IAAIS,cAAM,IAAAH,OAAA,EAAhBA,EAAkBI,QAAS,SAAS,OAAOrF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAII,GAAG,SAASH,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhB8E,EAAAb,EAAMM,IAAIS,cAAM,IAAAF,OAAA,EAAhBA,EAAkBI,QAAS,QAAQtF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhB+E,EAAAd,EAAMM,IAAIS,cAAM,IAAAD,OAAA,EAAhBA,EAAkBI,OAAQ,GAAG,WAAW,YAAYvF,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,IAAIJ,EAAIK,GAAyB,GAAtBiE,EAAMM,IAAIa,SAAgB,KAAO,MAAMnB,EAAMM,IAAIc,UAAU,OAAOzF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACH,EAAII,GAAG,OAAOJ,EAAIK,GAAGiE,EAAMM,IAAIe,cAAqC,GAAtBrB,EAAMM,IAAIa,SAAexF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,OAAOJ,EAAIK,GAAGiE,EAAMM,IAAIgB,YAActB,EAAMM,IAAIe,cAAc3F,EAAIgB,KAA4B,GAAtBsD,EAAMM,IAAIa,SAAexF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,cAAc,CAACO,MAAM,CAAC,WAAaqF,KAAKC,MAAOxB,EAAMM,IAAIe,QAAUrB,EAAMM,IAAIgB,YAAe,KAAK,eAAe,EAAE,aAAY,MAAU,GAAG5F,EAAIgB,cAAcf,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,OAAO,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,MAAM,CAACE,YAAY,cAAcM,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAI+F,WAAWzB,EAAMM,QAAQ,CAAC3E,EAAG,SAAS,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAOR,EAAIgG,cAAc1B,EAAMM,IAAI/B,QAAQ,OAA6B,GAApByB,EAAMM,IAAI/B,OAAc,QAAU,SAAS,CAAC7C,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIiG,cAAc3B,EAAMM,IAAI/B,SAAS,OAA4B,GAApByB,EAAMM,IAAI/B,OAAa5C,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGiE,EAAMM,IAAIsB,YAAY,OAAOlG,EAAIgB,MAAM,UAAUf,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,eAAe,MAAQ,MAAM,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAM,IAAA6B,EAAC,MAAO,CAAClG,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,UAAU,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAmB,QAAhB8F,EAAA7B,EAAMM,IAAIwB,cAAM,IAAAD,OAAA,EAAhBA,EAAkBb,QAAS,OAAO,QAAQ,UAAUrF,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,OAAOJ,EAAIK,GAAGL,EAAIqG,WAAW/B,EAAMM,IAAI0B,cAAc,OAAOrG,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,OAAOJ,EAAIK,GAAGL,EAAIqG,WAAW/B,EAAMM,IAAI2B,WAAW,OAAOtG,EAAG,MAAM,CAACE,YAAY,iBAAiB6C,MAAMhD,EAAIwG,sBAAsBlC,EAAMM,IAAI2B,WAAW,CAACtG,EAAG,IAAI,CAACE,YAAY,oBAAoBH,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIyG,iBAAiBnC,EAAMM,IAAI2B,WAAW,gBAAgBtG,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,WAAWK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAI0G,SAASpC,EAAMM,IAAIE,OAAO,CAAC7E,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,UAAgC,IAArBkE,EAAMM,IAAI/B,OAAc5C,EAAG,YAAY,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAI2G,aAAarC,EAAMM,QAAQ,CAAC3E,EAAG,IAAI,CAACE,YAAY,kBAAkBH,EAAII,GAAG,UAAUJ,EAAIgB,KAA2B,IAArBsD,EAAMM,IAAI/B,OAAc5C,EAAG,YAAY,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAI4G,YAAYtC,EAAMM,QAAQ,CAAC3E,EAAG,IAAI,CAACE,YAAY,kBAAkBH,EAAII,GAAG,UAAUJ,EAAIgB,KAAKf,EAAG,YAAY,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAI6G,QAAQvC,EAAMwC,OAAQxC,EAAMM,IAAIE,OAAO,CAAC7E,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,WAAW,WAAW,GAAGH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACO,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,IAAK,KAAK,YAAYR,EAAI+G,KAAK,OAAS,0CAA0C,MAAQ/G,EAAIuB,MAAM,WAAa,IAAId,GAAG,CAAC,cAAcT,EAAIgH,iBAAiB,iBAAiBhH,EAAIiH,wBAAwB,IAAI,GAAGhH,EAAG,YAAY,CAACE,YAAY,sBAAsBK,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAIkH,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOzG,GAAG,CAAC,iBAAiB,SAASK,GAAQd,EAAIkH,kBAAkBpG,KAAU,CAAEd,EAAImH,QAASlH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,YAAYH,EAAG,kBAAkB,CAACO,MAAM,CAAC,OAAS,EAAE,OAAS,KAAK,CAACP,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,SAAS,CAACR,EAAII,GAAGJ,EAAIK,IAAkB,QAAfd,EAAAS,EAAIoH,KAAKvC,cAAM,IAAAtF,OAAA,EAAfA,EAAiBwF,UAAW,WAAW,GAAG9E,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,QAAQ,CAAER,EAAIoH,KAAKvC,OAAQ5E,EAAG,SAAS,CAACE,YAAY,gBAAgBM,GAAG,CAAC,MAAQ,SAASK,GAAO,IAAAuG,EAAC,OAAOrH,EAAI2E,aAA4B,QAAhB0C,EAACrH,EAAIoH,KAAKvC,cAAM,IAAAwC,OAAA,EAAfA,EAAiBvC,OAAO,CAAC9E,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfb,EAAAQ,EAAIoH,KAAKvC,cAAM,IAAArF,OAAA,EAAfA,EAAiBwF,UAAW,OAAO,OAAOhF,EAAIgB,MAAM,GAAGf,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAAER,EAAIoH,KAAKvC,OAAQ5E,EAAG,SAAS,CAACE,YAAY,gBAAgBM,GAAG,CAAC,MAAQ,SAASK,GAAO,IAAAwG,EAAC,OAAOtH,EAAI2E,aAA4B,QAAhB2C,EAACtH,EAAIoH,KAAKvC,cAAM,IAAAyC,OAAA,EAAfA,EAAiBxC,OAAO,CAAC9E,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfZ,EAAAO,EAAIoH,KAAKvC,cAAM,IAAApF,OAAA,EAAfA,EAAiBwF,QAAS,OAAO,OAAOjF,EAAIgB,MAAM,GAAGf,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAAiB,QAAhBd,EAACM,EAAIoH,KAAKvC,cAAM,IAAAnF,GAAfA,EAAiB6H,SAAUtH,EAAG,SAAS,CAACE,YAAY,gBAAgBM,GAAG,CAAC,MAAQ,SAASK,GAAO,IAAA0G,EAAC,OAAOxH,EAAIyH,UAAyB,QAAhBD,EAACxH,EAAIoH,KAAKvC,cAAM,IAAA2C,OAAA,EAAfA,EAAiBD,aAAa,CAACvH,EAAII,GAAG,YAAYH,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,SAAS,CAACR,EAAII,GAAG,SAAS,GAAGH,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfV,EAAAK,EAAIoH,KAAKvC,cAAM,IAAAlF,OAAA,EAAfA,EAAiB+H,aAAc,OAAO,OAAOzH,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfT,EAAAI,EAAIoH,KAAKvC,cAAM,IAAAjF,OAAA,EAAfA,EAAiB+H,UAAW,OAAO,OAAO1H,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfR,EAAAG,EAAIoH,KAAKvC,cAAM,IAAAhF,OAAA,EAAfA,EAAiB+H,UAAW,OAAO,OAAO3H,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfP,EAAAE,EAAIoH,KAAKvC,cAAM,IAAA/E,OAAA,EAAfA,EAAiB+H,YAAa,OAAO,OAAO5H,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAII,GAAG,IAAIJ,EAAIK,IAAkB,QAAfN,EAAAC,EAAIoH,KAAKvC,cAAM,IAAA9E,OAAA,EAAfA,EAAiB+H,QAAS,OAAO,QAAQ,IAAI,GAAI9H,EAAIoH,KAAKW,OAAS/H,EAAIoH,KAAKW,MAAMtL,OAAS,EAAGwD,EAAG,UAAU,CAACE,YAAY,cAAcK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,uBAAuBH,EAAII,GAAG,aAAaH,EAAG,WAAW,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAOR,EAAIoH,KAAKW,MAAM,KAAO,WAAW,CAAC9H,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAWP,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,MAAM,MAAQ,UAAUP,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,QAAQ,MAAQ,WAAW2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGiE,EAAMM,IAAIoD,cAAc,MAAK,EAAM,cAAc/H,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,SAAS,MAAQ,MAAM2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,SAAS,CAACO,MAAM,CAAC,KAAOR,EAAIiI,kBAAkB3D,EAAMM,IAAI/B,UAAU,CAAC7C,EAAII,GAAG,IAAIJ,EAAIK,GAAGiE,EAAMM,IAAI/B,QAAQ,WAAW,MAAK,EAAM,eAAe,IAAI,GAAG7C,EAAIgB,KAAMhB,EAAIoH,KAAK/B,OAAQpF,EAAG,UAAU,CAACE,YAAY,cAAcK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,gBAAgBH,EAAII,GAAG,YAAYH,EAAG,kBAAkB,CAACO,MAAM,CAAC,OAAS,EAAE,OAAS,KAAK,CAACP,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,YAAY,CAACR,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK/B,OAAOC,WAAW,GAAGrF,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,OAAO,CAACE,YAAY,mBAAmB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIoH,KAAK/B,OAAOE,YAAYtF,EAAG,uBAAuB,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,YAAY,CAACR,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK/B,OAAOG,MAAM,QAAQ,IAAI,IAAI,GAAGxF,EAAIgB,MAAM,GAAGhB,EAAIgB,KAAKf,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,YAAY,CAACQ,GAAG,CAAC,MAAQ,SAASK,GAAQd,EAAIkH,mBAAoB,KAAS,CAAClH,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQT,EAAIkI,gBAAgB,CAAClI,EAAII,GAAG,WAAW,KAAKH,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAImI,cAAc,MAAQ,OAAO1H,GAAG,CAAC,iBAAiB,SAASK,GAAQd,EAAImI,cAAcrH,KAAU,CAACb,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,WAAW,CAACO,MAAM,CAAC,IAAMR,EAAIoI,WAAW,IAAM,cAAc,KAAKnI,EAAG,YAAY,CAACE,YAAY,iBAAiBK,MAAM,CAAC,MAAQ,SAAS,QAAUR,EAAIqI,kBAAkB,MAAQ,OAAO5H,GAAG,CAAC,iBAAiB,SAASK,GAAQd,EAAIqI,kBAAkBvH,KAAU,CAACb,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,KAAK,CAACP,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,KAAK,CAACD,EAAII,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,IAAI,CAACE,YAAY,iCAAiCF,EAAG,IAAI,CAACD,EAAII,GAAG,aAAaH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,SAASrI,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,SAASrI,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,SAASrI,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,SAASrI,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,SAASrI,EAAG,MAAM,CAACE,YAAY,YAAYmI,YAAY,CAAC,OAAS,iBAAiBrI,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,KAAK,CAACP,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,KAAK,CAACD,EAAII,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,IAAI,CAACE,YAAY,iCAAiCF,EAAG,IAAI,CAACD,EAAII,GAAG,aAAaH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,6BAA6BH,EAAII,GAAG,UAAUJ,EAAIK,GAAGL,EAAIuI,kBAAkB,OAAOtI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,oCAAoCH,EAAII,GAAG,UAAUJ,EAAIK,GAAGL,EAAIwI,yBAAyB,gBAAgB,GAAGvI,EAAG,SAAS,CAACqI,YAAY,CAAC,aAAa,QAAQ9H,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,KAAK,CAACP,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,KAAK,CAACD,EAAII,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIa,kBAAkBZ,EAAG,OAAO,CAACD,EAAII,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iCAAiC,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwB,mBAAmBvB,EAAG,OAAO,CAACD,EAAII,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,iCAAiC,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIyI,mBAAmBxI,EAAG,OAAO,CAACD,EAAII,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIuB,UAAUtB,EAAG,OAAO,CAACD,EAAII,GAAG,iBAAiB,IAAI,KAAKH,EAAG,YAAY,CAACE,YAAY,gBAAgBK,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAI0I,iBAAiB,MAAQ,SAASjI,GAAG,CAAC,iBAAiB,SAASK,GAAQd,EAAI0I,iBAAiB5H,KAAU,CAACb,EAAG,UAAU,CAACO,MAAM,CAAC,MAAQR,EAAI2I,WAAW,cAAc,UAAU,CAAC1I,EAAG,eAAe,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,iBAAiB,CAACqC,MAAM,CAAC1E,MAAOoC,EAAI2I,WAAWC,OAAQpG,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI2I,WAAY,SAAUlG,IAAME,WAAW,sBAAsB,CAAC1C,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,UAAU,CAACR,EAAII,GAAG,mBAAmBH,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAII,GAAG,gBAAgBH,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAII,GAAG,iBAAiB,IAAI,GAAGH,EAAG,eAAe,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,oBAAoB,CAACqC,MAAM,CAAC1E,MAAOoC,EAAI2I,WAAWE,OAAQrG,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI2I,WAAY,SAAUlG,IAAME,WAAW,sBAAsB,CAAC1C,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,WAAW,CAACR,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,YAAY,CAACR,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,YAAY,CAACR,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,WAAW,CAACR,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACO,MAAM,CAAC,MAAQ,WAAW,CAACR,EAAII,GAAG,YAAY,IAAI,GAAGH,EAAG,eAAe,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,iBAAiB,CAACqC,MAAM,CAAC1E,MAAOoC,EAAI2I,WAAWG,MAAOtG,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI2I,WAAY,QAASlG,IAAME,WAAW,qBAAqB,CAAC1C,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAII,GAAG,UAAUH,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,YAAY,CAACR,EAAII,GAAG,UAAUH,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,aAAa,CAACR,EAAII,GAAG,UAAUH,EAAG,WAAW,CAACO,MAAM,CAAC,MAAQ,aAAa,CAACR,EAAII,GAAG,WAAW,IAAI,GAAGH,EAAG,eAAe,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACP,EAAG,iBAAiB,CAACqI,YAAY,CAAC,MAAQ,QAAQ9H,MAAM,CAAC,KAAO,YAAY,kBAAkB,IAAI,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,cAAc8B,MAAM,CAAC1E,MAAOoC,EAAI2I,WAAWI,UAAWvG,SAAS,SAAUC,GAAMzC,EAAI0C,KAAK1C,EAAI2I,WAAY,YAAalG,IAAME,WAAW,2BAA2B,IAAI,GAAG1C,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,YAAY,CAACQ,GAAG,CAAC,MAAQ,SAASK,GAAQd,EAAI0I,kBAAmB,KAAS,CAAC1I,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,UAAU,QAAUR,EAAIgJ,eAAevI,GAAG,CAAC,MAAQT,EAAIiJ,gBAAgB,CAAChJ,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAII,GAAG,aAAa,IAAI,IAAI,IAEzp0B8I,EAAkB,G,gEC6xBtB,GACAzL,KAAA,OACA0L,WAAA,CAAAC,oBACAC,OACA,OACAC,QAAA,OACAtF,KAAA,GACAzC,MAAA,EACAgI,KAAA,EACAxC,KAAA,GACAhF,OAAA,CACAQ,QAAA,GACAK,SAAA,GACAQ,YAAA,GACAP,OAAA,GACAQ,QAAA,GACAC,UAAA,EACAC,UAAA,EACAC,YAAA,GACAC,OAAA,IAEAM,SAAA,EACAyF,IAAA,YACAlE,MAAA,OACA8B,KAAA,GACAqC,UAAA,EACAvC,mBAAA,EACAkB,WAAA,GACAD,eAAA,EACAuB,sBAAA,EACAvC,SAAA,EACAwC,aAAA,GACAC,cAAA,EACAC,eAAA,EACAC,SAAA,CACAjH,OAAA,GACAqD,WAAA,GACAK,SAAA,IAEAwD,MAAA,CACA7D,WAAA,CACA,CACA8D,UAAA,EACAC,QAAA,WACAC,QAAA,UAIAC,eAAA,QACAlH,cAAA,EACAW,aAAA,GACAyE,mBAAA,EACAK,kBAAA,EACAC,WAAA,CACAC,OAAA,QACAC,OAAA,wDACAC,MAAA,MACAC,UAAA,IAEAC,eAAA,EACAoB,mBAAA,IAGAC,SAAA,CAEAxJ,gBACA,OAAAyJ,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,GAAA,IAAAA,EAAA5H,QAAApG,OAAA,GAGA+E,iBACA,OAAA8I,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,GAAA,IAAAA,EAAA5H,QAAApG,OAAA,GAGAgM,iBACA,OAAA6B,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,GAAA,IAAAA,EAAA5H,QAAApG,OAAA,GAGAiF,eACA,OAAA4I,MAAAC,QAAA,KAAAvG,MACA,KAAAA,KAAAzH,OAAA,CAAAmO,EAAAD,IAAAC,GAAAD,EAAA9E,SAAA,MAAAgF,iBADA,KAIApC,mBACA,OAAA+B,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,GAAA,IAAAA,EAAAhF,UAAAhJ,OAAA,GAGA+L,0BACA,OAAA8B,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,GAAA,IAAAA,EAAAhF,UAAAhJ,OAAA,GAGAwE,iBACA,IAAAqJ,MAAAC,QAAA,KAAAvG,MAAA,SACA,MAAA4G,EAAA,IAAAC,KACAC,EAAA,IAAAD,KAAAD,EAAAG,UAAA,QACA,YAAA/G,KAAAwG,OAAAC,IACA,IAAAA,EAAAlE,SAAA,SACA,MAAAyE,EAAA,IAAAH,KAAAJ,EAAAlE,UACA,OAAAyE,GAAAJ,GAAAI,GAAAF,KAIA3J,gBACA,IAAAmJ,MAAAC,QAAA,KAAAvG,MAAA,SACA,MAAA4G,EAAA,IAAAC,KACA,YAAA7G,KAAAwG,OAAAC,IACA,IAAAA,EAAAlE,SAAA,SACA,MAAAyE,EAAA,IAAAH,KAAAJ,EAAAlE,UACA,OAAAyE,EAAAJ,KAIAvJ,kBACA,OAAAiJ,MAAAC,QAAA,KAAAvG,MAAA,KAAAA,KAAAwG,OAAAC,MAAA9E,SAAA,YAGAhF,4BACA,YAAAyJ,oBACA,KAAAvJ,cAAA,GACA,KAAAI,eAAAxE,OAAA,GACA,KAAA0E,cAAA1E,OAAA,GACA,KAAA4E,gBAAA5E,OAAA,KAIAwO,UACA,KAAA5I,WAEA6I,QAAA,CACAxE,SAAA5B,GACA,IAAAqG,EAAA,KACA,GAAArG,EACA,KAAAsG,QAAAtG,GAEA,KAAAgF,SAAA,CACAxE,MAAA,IAGA6F,EAAAjE,mBAAA,GAEAvC,aAAAG,GACA,IAAAqG,EAAA,KACA,GAAArG,IACA,KAAA2E,UAAA3E,GAGAqG,EAAAzB,sBAAA,GAEA0B,QAAAtG,GACA,IAAAqG,EAAA,KACAA,EAAAE,WAAAF,EAAA3B,IAAA,WAAA1E,GAAAwG,KAAAC,IACAA,IACAJ,EAAA/D,KAAAmE,EAAAlC,KACA8B,EAAAhE,SAAA,MAIAN,QAAAhJ,EAAAiH,GACA,KAAA0G,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAzJ,KAAA,YAEAqJ,KAAA,KACA,KAAAK,cAAA,KAAAnC,IAAA,aAAA1E,GAAAwG,KAAAC,IACA,KAAAA,EAAAK,MACA,KAAAC,SAAA,CACA5J,KAAA,UACAgI,QAAA,UAEA,KAAAjG,KAAA8H,OAAAjO,EAAA,IAEAsN,MAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAAsB,EAAAQ,UAKAC,MAAA,KACA,KAAAH,SAAA,CACA5J,KAAA,QACAgI,QAAA,aAKAgC,gBACA,KAAAT,SAAA,kBACAC,kBAAA,KACAC,iBAAA,KACAzJ,KAAA,YAEAqJ,KAAA,KACA,IAAAjC,EAAA,SAAAjC,KAAAtC,GAAA,cAAAsC,KAAAb,UACA,KAAA2F,YAAA,KAAA1C,IAAA,gBAAAH,GACAiC,KAAAC,IACA,KAAAA,EAAAK,KACA,KAAAC,SAAA,CACA5J,KAAA,UACAgI,QAAA,UAGAkB,MAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAAsB,EAAAQ,UAKAC,MAAA,KACA,KAAAH,SAAA,CACA5J,KAAA,QACAgI,QAAA,aAIAvJ,UACA,KAAAJ,QAAA6L,GAAA,IAEA9J,UACA,IAAA8I,EAAA,KACAA,EAAApH,SAAA,EACAoH,EACAe,YACAf,EAAA3B,IAAA,eAAA2B,EAAA5B,KAAA,SAAA4B,EAAApE,KACAoE,EAAApJ,QAEAuJ,KAAAC,IACA,KAAAA,EAAAK,OACAT,EAAAnH,KAAAuH,EAAAlC,KACA8B,EAAA5J,MAAAgK,EAAAnN,OAEA+M,EAAApH,SAAA,KAGAqI,WACA,IAAAjB,EAAA,KACA,KAAAkB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAgBA,SAfA,KAAAL,YAAAf,EAAA3B,IAAA,YAAAM,UAAAwB,KAAAC,IACA,KAAAA,EAAAK,MACAT,EAAAU,SAAA,CACA5J,KAAA,UACAgI,QAAAsB,EAAAQ,MAEAZ,EAAAjE,mBAAA,GAEAiE,EAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAAsB,EAAAQ,WASA/E,iBAAAwF,GACA,KAAAzF,KAAAyF,EAEA,KAAAnK,WAEA4E,oBAAAuF,GACA,KAAAjD,KAAAiD,EACA,KAAAnK,WAEAoK,cAAAC,GACA,IAAAvB,EAAA,KACA,KAAAuB,EAAAd,OACAT,EAAA/D,KAAAuF,MAAAxB,EAAAtN,OAAA+O,SAAAF,EAAArD,KAAAG,IACA2B,EAAAe,YAAAf,EAAA3B,IAAA,QACA,GAAA2B,EAAA/D,KAAAtC,GACA,MAAAqG,EAAA/D,KAAAuF,QACArB,KAAAC,IACA,KAAAA,EAAAK,KACAT,EAAAU,SAAA,CACA5J,KAAA,UACAgI,QAAA,SAIAkB,EAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAA,aAQA4C,aAAAC,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAA7K,MACA8K,GACA,KAAAlB,SAAAoB,MAAA,cAIAC,SAAAJ,EAAAK,GACA,IAAAhC,EAAA,KACAA,EAAAE,WAAA,6BAAAyB,GAAAxB,KAAAC,IACA,KAAAA,EAAAK,MACAT,EAAArB,SAAAqD,GAAA,GAEAhC,EAAAU,SAAAuB,QAAA,UAEAjC,EAAAU,SAAAoB,MAAA1B,EAAAQ,QAIAtE,UAAAqF,GACA,KAAA1E,WAAA0E,EACA,KAAA3E,eAAA,GAEAkF,cAAAxP,GACA,KAAAA,SAEAkI,WAAAnB,GACA,KAAAgF,cAAA,EACA,KAAAE,SAAAlF,GAEA0I,YAAA1I,GACA,KAAAiF,eAAA,EACA,KAAAC,SAAAlF,GAEA2I,gBACA,IAAApC,EAAA,KACA,KAAAkB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBApB,EAAAe,YAAAf,EAAA3B,IAAA,QACA,GAAA2B,EAAArB,SAAAhF,GACA,SAAAqG,EAAArB,SAAAvD,WAEA+E,KAAAC,IACA,KAAAA,EAAAK,MACAT,EAAAU,SAAA,CACA5J,KAAA,UACAgI,QAAA,SAEAkB,EAAAvB,cAAA,GAEAuB,EAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAAsB,EAAAQ,WASAyB,eAGA,IAAArC,EAAA,KACA,KAAAkB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBApB,EAAAe,YAAAf,EAAA3B,IAAA,gBACA,GAAA2B,EAAArB,SAAAhF,GACA,OAAAqG,EAAArB,SAAAjH,OACA,WAAAsI,EAAArB,SAAA5D,aACAoF,KAAAC,IACA,KAAAA,EAAAK,MACAT,EAAAU,SAAA,CACA5J,KAAA,UACAgI,QAAA,SAEAkB,EAAAvB,cAAA,GAEAuB,EAAAU,SAAA,CACA5J,KAAA,QACAgI,QAAAsB,EAAAQ,WAUA/F,cAAAnD,GACA,MAAA4K,EAAA,CACA,YACA,YACA,YAEA,OAAAA,EAAA5K,IAAA,QAEAoD,cAAApD,GACA,MAAA4K,EAAA,CACA,QACA,QACA,SAEA,OAAAA,EAAA5K,IAAA,MAEAoF,kBAAApF,GAEA,cAAAA,EAAA,UACA,QAAAA,EAAA,UACA,QAEAwD,WAAAqH,GACA,OAAAA,EACA,IAAA7C,KAAA6C,GAAAC,mBAAA,SADA,OAGA1J,mBAAA,IAAAW,IACA,WAAAA,EAAA/B,OAAA,cACA,IAAA+B,EAAA/B,OAAA,aACA,IAEAC,cACA,KAAAf,OAAA,CACAQ,QAAA,GACAK,SAAA,GACAQ,YAAA,GACAP,OAAA,GACAQ,QAAA,GACAC,UAAA,EACAC,UAAA,EACAC,YAAA,GACAC,OAAA,IAEA,KAAA8F,KAAA,EACA,KAAAlH,WAEAT,aACA,KAAA8G,kBAAA,GAEAR,gBACA,KAAA2D,SAAAuB,QAAA,iBAEArK,iBACA,KAAAE,cAAA,KAAAA,cAEAS,sBACA,KAAArB,WAEAsB,sBACA,KAAAb,eAEAjB,cACA,KAAAQ,WAEAP,aACA,KAAA+J,SAAAuB,QAAA,iBAEAlJ,sBAAA0J,GACA,KAAAhK,aAAAgK,GAEA7M,eAAA8B,GACA,KAAAd,OAAAc,SACA,KAAA0G,KAAA,EACA,KAAAlH,UACA,KAAAwJ,SAAAuB,QAAA,WAAAnH,cAAApD,IAAA,WAEApB,mBACA,KAAA4G,mBAAA,GAEAY,gBACA,KAAAD,eAAA,EAGA6E,WAAA,KACA,MAAAC,EAAA,CACA,cACA,UACA,WACA,KAAAnF,WAAAC,QAEAmF,EAAA,CACA,WACA,eACA,gBACA,iBACA,KAAApF,WAAAG,OAGAkF,EAAA,KAAAC,qBACAzE,EAAA0E,IAAAC,gBAAAH,GACAI,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAA/E,EACA4E,EAAAI,SAAA,aAAA3D,MAAA4D,cAAAC,MAAA,gBAAA/F,WAAAC,SACAwF,EAAAO,QACAT,IAAAU,gBAAApF,GAEA,KAAAR,eAAA,EACA,KAAAN,kBAAA,EACA,KAAAmD,SAAAuB,QAAA,GAAAU,OAAAC,WACA,MAEAE,qBAEA,IAAAY,EAAA,GACA,YAAAlG,WAAAG,OACA,UACA+F,EAAA,KAAA7K,KACA,MACA,cACA6K,EAAA,KAAA7K,KACA,MACA,eACA6K,EAAA,KAAAjL,aACA,MACA,eACAiL,EAAA,KAAA7K,KACA,MAIA,MAAAqF,EAAAwF,EAAAC,IAAArE,IACA,MAAA7F,EAAA,GACA,IAAAmK,EAAAC,EAAAC,EAKAC,EAAAC,EAAAC,EAkBAC,EAvBA,KAAA1G,WAAAE,OAAAyG,SAAA,YACA1K,EAAA,iBAAAmK,EAAAtE,EAAA5F,cAAA,IAAAkK,OAAA,EAAAA,EAAAhK,UAAA,GACAH,EAAA,gBAAAoK,EAAAvE,EAAA5F,cAAA,IAAAmK,OAAA,EAAAA,EAAAhK,UAAA,GACAJ,EAAA,iBAAAqK,EAAAxE,EAAA5F,cAAA,IAAAoK,OAAA,EAAAA,EAAAhK,QAAA,IAEA,KAAA0D,WAAAE,OAAAyG,SAAA,aACA1K,EAAA,iBAAAsK,EAAAzE,EAAApF,cAAA,IAAA6J,OAAA,EAAAA,EAAA5J,QAAA,GACAV,EAAA,iBAAAuK,EAAA1E,EAAApF,cAAA,IAAA8J,OAAA,EAAAA,EAAA5J,QAAA,EACAX,EAAA,iBAAAwK,EAAA3E,EAAApF,cAAA,IAAA+J,OAAA,EAAAA,EAAA5J,OAAA,IAEA,KAAAmD,WAAAE,OAAAyG,SAAA,aACA1K,EAAA,WAAA6F,EAAAhF,SAAA,UACAb,EAAA,QAAA6F,EAAA9E,SAAA,EACAf,EAAA,OAAA6F,EAAA7E,aAAA,GAEA,KAAA+C,WAAAE,OAAAyG,SAAA,YACA1K,EAAA,aAAAqB,cAAAwE,EAAA5H,QACA+B,EAAA,QAAA6F,EAAAvE,YAAA,IAEA,KAAAyC,WAAAE,OAAAyG,SAAA,UACA1K,EAAA,QAAA6F,EAAAnE,aAAA,GACA1B,EAAA,QAAA6F,EAAAlE,UAAA,IAEA,KAAAoC,WAAAE,OAAAyG,SAAA,aACA1K,EAAA,gBAAAyK,EAAA5E,EAAArE,cAAA,IAAAiJ,OAAA,EAAAA,EAAA/J,QAAA,IAEA,OAAAV,IAIA2K,EAAA,KAAAC,aAAAnG,GACA,WAAAoG,KAAA,CAAAF,GAAA,CAAAtN,KAAA,6BAEAuN,aAAAnG,GACA,IAAAA,GAAA,IAAAA,EAAA5M,OAAA,SAEA,MAAAiT,EAAAC,OAAAC,KAAAvG,EAAA,IACAwG,EAAA,CAAAH,EAAAI,KAAA,MAEA,UAAAlL,KAAAyE,EAAA,CACA,MAAA1L,EAAA+R,EAAAZ,IAAAiB,IACA,MAAAnS,EAAAgH,EAAAmL,GACA,wBAAAnS,KAAA0R,SAAA,SAAA1R,SAEAiS,EAAAhR,KAAAlB,EAAAmS,KAAA,MAGA,OAAAD,EAAAC,KAAA,OAEAjM,eACA,YAAAD,aAAAnH,OAEA,YADA,KAAAoP,SAAAmE,QAAA,gBAIA,MAAAC,EAAA,KAAArM,aAAA4G,OAAA5F,GAAA,IAAAA,EAAA/B,QACA,IAAAoN,EAAAxT,OAKA,KAAA+O,SAAA,aAAAyE,EAAAxT,kBAAA,QACAgP,kBAAA,OACAC,iBAAA,KACAzJ,KAAA,YACAqJ,KAAA,KACA,MAAA4E,EAAAD,EAAAnB,IAAAlK,GACA,KAAAsH,YAAA,KAAA1C,IAAA,gBACA,GAAA5E,EAAAE,GACA,SACA,uBAIAqL,QAAAC,IAAAF,GAAA5E,KAAA+E,IACA,MAAAC,EAAAD,EAAA7F,OAAAe,GAAA,MAAAA,EAAAK,MAAAnP,OACA,KAAAoP,SAAAuB,QAAA,eAAAkD,SACA,KAAAjO,UACA,KAAAuB,aAAA,KACAoI,MAAA,KACA,KAAAH,SAAAoB,MAAA,eACA,KAAA5K,cAEA2J,MAAA,KACA,KAAAH,SAAAzE,KAAA,aA3BA,KAAAyE,SAAAmE,QAAA,mBA8BAlM,cACA,YAAAF,aAAAnH,OAEA,YADA,KAAAoP,SAAAmE,QAAA,gBAIA,MAAAC,EAAA,KAAArM,aAAA4G,OAAA5F,GAAA,IAAAA,EAAA/B,QACA,IAAAoN,EAAAxT,OAKA,KAAA8T,QAAA,oBAAAN,EAAAxT,aAAA,CACAgP,kBAAA,OACAC,iBAAA,KACA8E,iBAAA,gBACAC,eAAA7S,GACAA,GAAA,KAAAA,EAAA8S,SAGA9S,EAAAnB,OAAA,IACA,eAHA,aAOA6O,KAAA,EAAA1N,YACA,MAAAsS,EAAAD,EAAAnB,IAAAlK,GACA,KAAAsH,YAAA,KAAA1C,IAAA,gBACA,GAAA5E,EAAAE,GACA,SACA,WAAAlH,KAIAuS,QAAAC,IAAAF,GAAA5E,KAAA+E,IACA,MAAAC,EAAAD,EAAA7F,OAAAe,GAAA,MAAAA,EAAAK,MAAAnP,OACA,KAAAoP,SAAAuB,QAAA,eAAAkD,SACA,KAAAjO,UACA,KAAAuB,aAAA,KACAoI,MAAA,KACA,KAAAH,SAAAoB,MAAA,eACA,KAAA5K,cAEA2J,MAAA,KACA,KAAAH,SAAAzE,KAAA,aApCA,KAAAyE,SAAAmE,QAAA,mBAuCArJ,aAAA/B,GAAA,IAAA+L,EACA,KAAAnF,SAAA,iBAAAmF,EAAA/L,EAAAC,cAAA,IAAA8L,OAAA,EAAAA,EAAA5L,UAAA,sBACA0G,kBAAA,OACAC,iBAAA,KACAzJ,KAAA,YACAqJ,KAAA,KAEA,KAAAY,YAAA,KAAA1C,IAAA,gBACA,GAAA5E,EAAAE,GACA,SACA,sBACAwG,KAAAC,IACA,KAAAA,EAAAK,MACA,KAAAC,SAAAuB,QAAA,UACA,KAAA/K,WAEA,KAAAwJ,SAAAoB,MAAA1B,EAAAQ,SAGAC,MAAA,KACA,KAAAH,SAAAzE,KAAA,YAGAR,YAAAhC,GAAA,IAAAgM,EACA,KAAAL,QAAA,8BAAAK,EAAAhM,EAAAC,cAAA,IAAA+L,OAAA,EAAAA,EAAA7L,UAAA,OACA0G,kBAAA,OACAC,iBAAA,KACA8E,iBAAA,gBACAC,eAAA7S,GACAA,GAAA,KAAAA,EAAA8S,SAGA9S,EAAAnB,OAAA,IACA,eAHA,aAOA6O,KAAA,EAAA1N,YAEA,KAAAsO,YAAA,KAAA1C,IAAA,gBACA,GAAA5E,EAAAE,GACA,SACA,WAAAlH,IACA0N,KAAAC,IACA,KAAAA,EAAAK,MACA,KAAAC,SAAAuB,QAAA,SACA,KAAA/K,WAEA,KAAAwJ,SAAAoB,MAAA1B,EAAAQ,SAGAC,MAAA,KACA,KAAAH,SAAAzE,KAAA,YAGAX,iBAAAF,GACA,IAAAA,EAAA,YACA,MAAAqE,EAAA,IAAAC,KACAG,EAAA,IAAAH,KAAAtE,GACAsK,EAAAhL,KAAAiL,MAAA9F,EAAAJ,GAAA,OAEA,OAAAiG,EAAA,QAAAhL,KAAAkL,IAAAF,MACA,IAAAA,EAAA,OACAA,EAAA,QAIArK,sBAAAD,GACA,IAAAA,EAAA,SACA,MAAAqE,EAAA,IAAAC,KACAG,EAAA,IAAAH,KAAAtE,GACAsK,EAAAhL,KAAAiL,MAAA9F,EAAAJ,GAAA,OAEA,OAAAiG,EAAA,YACAA,GAAA,WACAA,GAAA,YACA,UAGAjQ,uBACA,KAAAwJ,mBAAA,EACA,KAAAyB,SAAAuB,QAAA,cAEAlM,qBAEA,KAAA2K,SAAAzE,KAAA,UAAAnG,eAAAxE,mBAGA2E,oBAEA,KAAAyK,SAAAmE,QAAA,UAAA7O,cAAA1E,kBAGA6E,sBAEA,KAAAuK,SAAAuB,QAAA,UAAA/L,gBAAA5E,mBCvgD8W,I,wBCQ1WuU,EAAY,eACd,EACA1R,EACA4J,GACA,EACA,KACA,WACA,MAIa,aAAA8H,E,2CClBf,IAAIC,EAAQ,EAAQ,QAEpBzS,EAAOC,QAAU,SAAUyS,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,OAAO,GAAM,Q,kCCP7D,IAAI7R,EAAS,WAAkB,IAAIU,EAAIrD,KAAKsD,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,cAAcH,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKrC,SAAW,cAAc9E,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKnC,OAAS,cAAchF,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKkK,UAAY,eAAe,GAAGrR,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKpC,SAAW,cAAc/E,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKmK,WAAa,cAActR,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKoK,aAAe,eAAe,GAAGvR,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKqK,YAAc,cAAcxR,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK5B,KAAOxF,EAAIoH,KAAK5B,KAAO,IAAM,cAAcvF,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEH,EAAIoH,KAAKsK,SAAgC,KAArB1R,EAAIoH,KAAKsK,QAAgBzR,EAAG,YAAY,CAACqI,YAAY,CAAC,OAAS,WAAW9H,MAAM,CAAC,IAAMR,EAAIoH,KAAKsK,QAAQ,KAAO,IAAI1P,SAAS,CAAC,MAAQ,SAASlB,GAAQ,OAAOd,EAAIyH,UAAUzH,EAAIoH,KAAKsK,aAAazR,EAAG,OAAO,CAACE,YAAY,WAAW,CAACH,EAAII,GAAG,UAAU,QAAQ,GAAGH,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,KAAK,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEH,EAAIoH,KAAKuK,SAAgC,KAArB3R,EAAIoH,KAAKuK,QAAgB1R,EAAG,WAAW,CAACqI,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,OAAS,WAAW9H,MAAM,CAAC,IAAMR,EAAIoH,KAAKuK,QAAQ,IAAM,SAASlR,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIyH,UAAUzH,EAAIoH,KAAKuK,YAAY,CAAC1R,EAAG,MAAM,CAACE,YAAY,aAAaK,MAAM,CAAC,KAAO,SAASmB,KAAK,SAAS,CAAC1B,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,OAAO,CAACE,YAAY,WAAW,CAACH,EAAII,GAAG,UAAU,QAAQ,IAAI,GAAGH,EAAG,UAAU,CAACE,YAAY,YAAYK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,YAAYH,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKwK,cAAgB,cAAc3R,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAKyK,WAAa,cAAc5R,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK0K,WAAa,eAAe,GAAG7R,EAAG,SAAS,CAACO,MAAM,CAAC,OAAS,KAAK,CAACP,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK2K,aAAe,cAAc9R,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK4K,SAAW,cAAc/R,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,IAAI,CAACP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIoH,KAAK6K,UAAY,eAAe,IAAI,GAAGhS,EAAG,UAAU,CAACE,YAAY,YAAYK,MAAM,CAAC,OAAS,UAAU,CAACP,EAAG,MAAM,CAACE,YAAY,cAAcK,MAAM,CAAC,KAAO,UAAUmB,KAAK,UAAU,CAAC1B,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,aAAaH,EAAG,WAAW,CAACiD,WAAW,CAAC,CAACzF,KAAK,UAAU0F,QAAQ,YAAYvF,MAAOoC,EAAI+D,QAASpB,WAAW,YAAY2F,YAAY,CAAC,MAAQ,QAAQ9H,MAAM,CAAC,KAAOR,EAAIoH,KAAKW,MAAM,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACmK,WAAW,UAAUC,MAAM,aAAa,CAAClS,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,SAAS,CAACO,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACR,EAAII,GAAGJ,EAAIK,GAAGiE,EAAMM,IAAInH,gBAAgBwC,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAGJ,EAAIK,GAAGiE,EAAMM,IAAIwN,eAAenS,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGiE,EAAMM,IAAIoD,iBAAiB/H,EAAG,kBAAkB,CAACO,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,SAAS,CAACO,MAAM,CAAC,KAA4B,QAArB8D,EAAMM,IAAI/B,OAAmB,UAAY,UAAU,KAAO,UAAU,CAAC7C,EAAII,GAAG,IAAIJ,EAAIK,GAAGiE,EAAMM,IAAI/B,QAAQ,cAAc5C,EAAG,kBAAkB,CAACO,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAO2D,YAAYnE,EAAIoE,GAAG,CAAC,CAACnF,IAAI,UAAUoF,GAAG,SAASC,GAAO,MAAO,CAACrE,EAAG,YAAY,CAACO,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAOd,EAAIqS,eAAe/N,EAAMM,QAAQ,CAAC3E,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,kBAAkB,GAAKJ,EAAIoH,KAAKW,OAAmC,IAA1B/H,EAAIoH,KAAKW,MAAMtL,OAA0HuD,EAAIgB,KAAhHf,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACD,EAAII,GAAG,gBAAyB,GAAGH,EAAG,YAAY,CAACO,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAImI,cAAc,MAAQ,OAAO1H,GAAG,CAAC,iBAAiB,SAASK,GAAQd,EAAImI,cAAcrH,KAAU,CAACb,EAAG,WAAW,CAACO,MAAM,CAAC,IAAMR,EAAIoI,eAAe,IAAI,IAEjuNc,EAAkB,GCqNtB,GACAzL,KAAA,cACA6U,MAAA,CACAxN,GAAA,CACA7C,KAAA,CAAAsQ,OAAAC,QACAxI,UAAA,IAGAX,OACA,OACAjC,KAAA,GACArD,SAAA,EACAoE,eAAA,EACAC,WAAA,KAGAqK,MAAA,CACA3N,GAAA,CACA4N,WAAA,EACAC,QAAAC,GACAA,GAAA,GAAAA,IACAC,QAAAC,IAAA,sBAAAF,GACA,KAAAxH,QAAAwH,OAKA1H,QAAA,CACAE,QAAAtG,GACA,IAAAqG,EAAA,KACA0H,QAAAC,IAAA,eAAAhO,GACAqG,EAAApH,SAAA,EAGA8J,WAAA,KACA,MAAAkF,EAAA,CACAjO,KACAC,QAAA,WACAE,MAAA,cACAqM,SAAA,KACAtM,QAAA,KACA0M,QAAA,GACAF,YAAA,QACAD,UAAA,cACAK,aAAA,OACAC,UAAA,MACAC,UAAA,OACAC,YAAA,OACAC,QAAA,MACAC,SAAA,OACAN,QAAA,GACAF,WAAA,aACAjM,KAAA,EACAuC,MAAA,CACA,CACAtK,KAAA,OACA2U,IAAA,cACApK,MAAA,QACAnF,OAAA,OAEA,CACApF,KAAA,OACA2U,IAAA,cACApK,MAAA,QACAnF,OAAA,SAKAsI,EAAA/D,KAAA2L,EACA5H,EAAApH,SAAA,EACA8O,QAAAC,IAAA,YAAAC,IACA,MAmBAtL,UAAAuL,GACA,KAAA5K,WAAA4K,EACA,KAAA7K,eAAA,GAGAkK,eAAAY,GACAJ,QAAAC,IAAA,WAAAG,GAEA,KAAApH,SAAAzE,KAAA,iBC1TmV,I,wBCQ/U4J,EAAY,eACd,EACA1R,EACA4J,GACA,EACA,KACA,WACA,MAIa,OAAA8H,E,2CClBf,IAAIkC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAoB,EAAQ,QAE5BC,EAAaC,UAEbC,EAAe,8CAGfC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMnX,EAAYoX,EAAiBC,GAClD,IAAIC,EAAIX,EAASQ,GACbI,EAAOX,EAAcU,GACrBrX,EAAS4W,EAAkBS,GAE/B,GADAZ,EAAU1W,GACK,IAAXC,GAAgBmX,EAAkB,EAAG,MAAM,IAAIN,EAAWE,GAC9D,IAAI3V,EAAQ6V,EAAWjX,EAAS,EAAI,EAChCuX,EAAIN,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAI/V,KAASkW,EAAM,CACjBF,EAAOE,EAAKlW,GACZA,GAASmW,EACT,MAGF,GADAnW,GAASmW,EACLN,EAAW7V,EAAQ,EAAIpB,GAAUoB,EACnC,MAAM,IAAIyV,EAAWE,GAGzB,KAAME,EAAW7V,GAAS,EAAIpB,EAASoB,EAAOA,GAASmW,EAAOnW,KAASkW,IACrEF,EAAOrX,EAAWqX,EAAME,EAAKlW,GAAQA,EAAOiW,IAE9C,OAAOD,IAIXrV,EAAOC,QAAU,CAGf3C,KAAM2X,GAAa,GAGnBQ,MAAOR,GAAa,K,kCC3CtB,IAAIH,EAAaC,UAEjB/U,EAAOC,QAAU,SAAUyV,EAAQlK,GACjC,GAAIkK,EAASlK,EAAU,MAAM,IAAIsJ,EAAW,wBAC5C,OAAOY,I,kCCJT,IAAIC,EAAc,EAAQ,QACtBC,EAAiB,EAAQ,QAE7B5V,EAAOC,QAAU,SAAUrC,EAAQqB,EAAM4W,GAGvC,OAFIA,EAAWlW,KAAKgW,EAAYE,EAAWlW,IAAKV,EAAM,CAAE6W,QAAQ,IAC5DD,EAAWE,KAAKJ,EAAYE,EAAWE,IAAK9W,EAAM,CAAE+W,QAAQ,IACzDJ,EAAeK,EAAErY,EAAQqB,EAAM4W", "file": "js/chunk-41971978.a94e8bab.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=style&index=0&id=ffe30e72&prod&scoped=true&lang=css\"", "'use strict';\r\nvar $ = require('../internals/export');\r\nvar $reduce = require('../internals/array-reduce').left;\r\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\r\nvar CHROME_VERSION = require('../internals/engine-v8-version');\r\nvar IS_NODE = require('../internals/engine-is-node');\r\n\r\n// Chrome 80-82 has a critical bug\r\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\r\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\r\nvar FORCED = CHROME_BUG || !arrayMethodIsStrict('reduce');\r\n\r\n// `Array.prototype.reduce` method\r\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n$({ target: 'Array', proto: true, forced: FORCED }, {\r\n  reduce: function reduce(callbackfn /* , initialValue */) {\r\n    var length = arguments.length;\r\n    return $reduce(this, callbackfn, length, length > 1 ? arguments[1] : undefined);\r\n  }\r\n});\r\n", "'use strict';\r\nvar defineBuiltIn = require('../internals/define-built-in');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar toString = require('../internals/to-string');\r\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\r\n\r\nvar $URLSearchParams = URLSearchParams;\r\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\r\nvar getAll = uncurryThis(URLSearchParamsPrototype.getAll);\r\nvar $has = uncurryThis(URLSearchParamsPrototype.has);\r\nvar params = new $URLSearchParams('a=1');\r\n\r\n// `undefined` case is a Chromium 117 bug\r\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\r\nif (params.has('a', 2) || !params.has('a', undefined)) {\r\n  defineBuiltIn(URLSearchParamsPrototype, 'has', function has(name /* , value */) {\r\n    var length = arguments.length;\r\n    var $value = length < 2 ? undefined : arguments[1];\r\n    if (length && $value === undefined) return $has(this, name);\r\n    var values = getAll(this, name); // also validates `this`\r\n    validateArgumentsLength(length, 1);\r\n    var value = toString($value);\r\n    var index = 0;\r\n    while (index < values.length) {\r\n      if (values[index++] === value) return true;\r\n    } return false;\r\n  }, { enumerable: true, unsafe: true });\r\n}\r\n", "'use strict';\r\nvar DESCRIPTORS = require('../internals/descriptors');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\r\n\r\nvar URLSearchParamsPrototype = URLSearchParams.prototype;\r\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\r\n\r\n// `URLSearchParams.prototype.size` getter\r\n// https://github.com/whatwg/url/pull/734\r\nif (DESCRIPTORS && !('size' in URLSearchParamsPrototype)) {\r\n  defineBuiltInAccessor(URLSearchParamsPrototype, 'size', {\r\n    get: function size() {\r\n      var count = 0;\r\n      forEach(this, function () { count++; });\r\n      return count;\r\n    },\r\n    configurable: true,\r\n    enumerable: true\r\n  });\r\n}\r\n", "'use strict';\r\nvar global = require('../internals/global');\r\nvar classof = require('../internals/classof-raw');\r\n\r\nmodule.exports = classof(global.process) === 'process';\r\n", "'use strict';\r\nvar defineBuiltIn = require('../internals/define-built-in');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar toString = require('../internals/to-string');\r\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\r\n\r\nvar $URLSearchParams = URLSearchParams;\r\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\r\nvar append = uncurryThis(URLSearchParamsPrototype.append);\r\nvar $delete = uncurryThis(URLSearchParamsPrototype['delete']);\r\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\r\nvar push = uncurryThis([].push);\r\nvar params = new $URLSearchParams('a=1&a=2&b=3');\r\n\r\nparams['delete']('a', 1);\r\n// `undefined` case is a Chromium 117 bug\r\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\r\nparams['delete']('b', undefined);\r\n\r\nif (params + '' !== 'a=2') {\r\n  defineBuiltIn(URLSearchParamsPrototype, 'delete', function (name /* , value */) {\r\n    var length = arguments.length;\r\n    var $value = length < 2 ? undefined : arguments[1];\r\n    if (length && $value === undefined) return $delete(this, name);\r\n    var entries = [];\r\n    forEach(this, function (v, k) { // also validates `this`\r\n      push(entries, { key: k, value: v });\r\n    });\r\n    validateArgumentsLength(length, 1);\r\n    var key = toString(name);\r\n    var value = toString($value);\r\n    var index = 0;\r\n    var dindex = 0;\r\n    var found = false;\r\n    var entriesLength = entries.length;\r\n    var entry;\r\n    while (index < entriesLength) {\r\n      entry = entries[index++];\r\n      if (found || entry.key === key) {\r\n        found = true;\r\n        $delete(this, entry.key);\r\n      } else dindex++;\r\n    }\r\n    while (dindex < entriesLength) {\r\n      entry = entries[dindex++];\r\n      if (!(entry.key === key && entry.value === value)) append(this, entry.key, entry.value);\r\n    }\r\n  }, { enumerable: true, unsafe: true });\r\n}\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"order-management-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-s-order\"}),_vm._v(\" \"+_vm._s(this.$router.currentRoute.name)+\" \")]),_c('div',{staticClass:\"page-subtitle\"},[_vm._v(\"管理客户签约订单和合同信息\")])]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新数据 \")])],1)]),(_vm.hasImportantNotifications)?_c('div',{staticClass:\"notifications-section\"},[_c('el-card',{staticClass:\"notification-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"notification-header\"},[_c('i',{staticClass:\"el-icon-warning-outline notification-icon\"}),_c('span',{staticClass:\"notification-title\"},[_vm._v(\"重要提醒\")]),_c('el-button',{staticClass:\"dismiss-btn\",attrs:{\"type\":\"text\",\"size\":\"mini\"},on:{\"click\":_vm.dismissNotifications}},[_c('i',{staticClass:\"el-icon-close\"})])],1),_c('div',{staticClass:\"notification-content\"},[_c('div',{staticClass:\"notification-list\"},[(_vm.pendingOrders > 0)?_c('div',{staticClass:\"notification-item urgent\",on:{\"click\":function($event){return _vm.filterByStatus('1')}}},[_c('div',{staticClass:\"notification-dot\"}),_c('div',{staticClass:\"notification-text\"},[_c('strong',[_vm._v(_vm._s(_vm.pendingOrders))]),_vm._v(\" 个订单待审核，请及时处理 \")]),_c('div',{staticClass:\"notification-action\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"warning\"}},[_vm._v(\"立即处理\")])],1)]):_vm._e(),(_vm.expiringOrders.length > 0)?_c('div',{staticClass:\"notification-item warning\",on:{\"click\":_vm.showExpiringOrders}},[_c('div',{staticClass:\"notification-dot\"}),_c('div',{staticClass:\"notification-text\"},[_c('strong',[_vm._v(_vm._s(_vm.expiringOrders.length))]),_vm._v(\" 个订单即将到期，请提醒客户续费 \")]),_c('div',{staticClass:\"notification-action\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"查看详情\")])],1)]):_vm._e(),(_vm.expiredOrders.length > 0)?_c('div',{staticClass:\"notification-item error\",on:{\"click\":_vm.showExpiredOrders}},[_c('div',{staticClass:\"notification-dot\"}),_c('div',{staticClass:\"notification-text\"},[_c('strong',[_vm._v(_vm._s(_vm.expiredOrders.length))]),_vm._v(\" 个订单已过期，需要联系客户处理 \")]),_c('div',{staticClass:\"notification-action\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"danger\"}},[_vm._v(\"紧急处理\")])],1)]):_vm._e(),(_vm.highValueOrders.length > 0)?_c('div',{staticClass:\"notification-item info\",on:{\"click\":_vm.showHighValueOrders}},[_c('div',{staticClass:\"notification-dot\"}),_c('div',{staticClass:\"notification-text\"},[_c('strong',[_vm._v(_vm._s(_vm.highValueOrders.length))]),_vm._v(\" 个高价值订单需要重点关注 \")]),_c('div',{staticClass:\"notification-action\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"success\"}},[_vm._v(\"查看订单\")])],1)]):_vm._e()])])])],1):_vm._e(),_c('div',{staticClass:\"stats-section\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\",on:{\"click\":function($event){return _vm.filterByStatus('')}}},[_c('div',{staticClass:\"stat-icon total-icon\"},[_c('i',{staticClass:\"el-icon-s-order\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.total))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总订单数\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +8% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\",on:{\"click\":function($event){return _vm.filterByStatus('1')}}},[_c('div',{staticClass:\"stat-icon pending-icon\"},[_c('i',{staticClass:\"el-icon-time\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.pendingOrders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"待审核\")]),_c('div',{staticClass:\"stat-change warning\"},[_c('i',{staticClass:\"el-icon-warning\"}),_vm._v(\" 需关注 \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\",on:{\"click\":function($event){return _vm.filterByStatus('2')}}},[_c('div',{staticClass:\"stat-icon approved-icon\"},[_c('i',{staticClass:\"el-icon-circle-check\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.approvedOrders))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"已通过\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +12% \")])])])]),_c('el-col',{attrs:{\"xs\":12,\"sm\":6,\"md\":6,\"lg\":6,\"xl\":6}},[_c('div',{staticClass:\"stat-card\",on:{\"click\":_vm.showRevenueChart}},[_c('div',{staticClass:\"stat-icon revenue-icon\"},[_c('i',{staticClass:\"el-icon-money\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(\"¥\"+_vm._s(_vm.totalRevenue))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总收入\")]),_c('div',{staticClass:\"stat-change positive\"},[_c('i',{staticClass:\"el-icon-arrow-up\"}),_vm._v(\" +15% \")])])])])],1)],1),_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticClass:\"header-left\"},[_c('span',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-search\"}),_vm._v(\" 搜索与筛选 \")]),_c('div',{staticClass:\"card-subtitle\"},[_vm._v(\"快速查找和管理订单信息\")])]),_c('div',{staticClass:\"header-actions\"},[_c('el-button-group',{staticClass:\"action-group\"},[_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportData}},[_vm._v(\" 导出 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refreshData}},[_vm._v(\" 刷新 \")])],1),_c('el-button',{staticClass:\"primary-action\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-check\"},on:{\"click\":_vm.batchAudit}},[_vm._v(\" 批量审核 \")])],1)]),_c('div',{staticClass:\"search-section\"},[_c('el-form',{staticClass:\"search-form\",attrs:{\"model\":_vm.search,\"inline\":true}},[_c('div',{staticClass:\"search-row\"},[_c('el-form-item',{staticClass:\"search-item-main\",attrs:{\"label\":\"关键词搜索\"}},[_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入订单号/购买人/套餐/手机号\",\"clearable\":\"\",\"prefix-icon\":\"el-icon-search\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.getData()}},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-form-item',{staticClass:\"search-item\",attrs:{\"label\":\"业务员\"}},[_c('el-input',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请输入业务员姓名\",\"clearable\":\"\"},model:{value:(_vm.search.salesman),callback:function ($$v) {_vm.$set(_vm.search, \"salesman\", $$v)},expression:\"search.salesman\"}})],1),_c('el-form-item',{staticClass:\"search-item\",attrs:{\"label\":\"审核状态\"}},[_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.status),callback:function ($$v) {_vm.$set(_vm.search, \"status\", $$v)},expression:\"search.status\"}},[_c('el-option',{attrs:{\"label\":\"全部状态\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"未审核\",\"value\":\"1\"}}),_c('el-option',{attrs:{\"label\":\"已通过\",\"value\":\"2\"}}),_c('el-option',{attrs:{\"label\":\"未通过\",\"value\":\"3\"}})],1)],1),_c('el-form-item',{staticClass:\"search-actions-item\"},[_c('div',{staticClass:\"search-actions\"},[_c('el-button',{staticClass:\"search-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\" 搜索 \")]),_c('el-button',{staticClass:\"reset-btn\",attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.resetSearch}},[_vm._v(\" 重置 \")]),_c('el-button',{staticClass:\"toggle-btn\",attrs:{\"type\":\"text\"},on:{\"click\":_vm.toggleAdvanced}},[_c('i',{class:_vm.showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}),_vm._v(\" \"+_vm._s(_vm.showAdvanced ? '收起' : '高级筛选')+\" \")])],1)])],1),_c('transition',{attrs:{\"name\":\"slide-fade\"}},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showAdvanced),expression:\"showAdvanced\"}],staticClass:\"advanced-search\"},[_c('el-divider',{attrs:{\"content-position\":\"left\"}},[_c('i',{staticClass:\"el-icon-setting\"}),_vm._v(\" 高级筛选选项 \")]),_c('div',{staticClass:\"advanced-content\"},[_c('div',{staticClass:\"advanced-row\"},[_c('el-form-item',{staticClass:\"advanced-item\",attrs:{\"label\":\"支付时间\"}},[_c('el-date-picker',{staticClass:\"date-picker\",attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"default-time\":['00:00:00', '23:59:59']},model:{value:(_vm.search.refund_time),callback:function ($$v) {_vm.$set(_vm.search, \"refund_time\", $$v)},expression:\"search.refund_time\"}})],1),_c('el-form-item',{staticClass:\"advanced-item\",attrs:{\"label\":\"支付方式\"}},[_c('el-select',{staticClass:\"pay-select\",attrs:{\"placeholder\":\"选择支付方式\"},model:{value:(_vm.search.payType),callback:function ($$v) {_vm.$set(_vm.search, \"payType\", $$v)},expression:\"search.payType\"}},[_c('el-option',{attrs:{\"label\":\"全部方式\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"全款支付\",\"value\":\"1\"}}),_c('el-option',{attrs:{\"label\":\"分期付款\",\"value\":\"2\"}})],1)],1),_c('el-form-item',{staticClass:\"advanced-item\",attrs:{\"label\":\"金额范围\"}},[_c('div',{staticClass:\"amount-range\"},[_c('el-input-number',{attrs:{\"placeholder\":\"最小金额\",\"min\":0,\"precision\":2,\"controls-position\":\"right\",\"size\":\"small\"},model:{value:(_vm.search.minAmount),callback:function ($$v) {_vm.$set(_vm.search, \"minAmount\", $$v)},expression:\"search.minAmount\"}}),_c('span',{staticClass:\"range-separator\"},[_vm._v(\"-\")]),_c('el-input-number',{attrs:{\"placeholder\":\"最大金额\",\"min\":0,\"precision\":2,\"controls-position\":\"right\",\"size\":\"small\"},model:{value:(_vm.search.maxAmount),callback:function ($$v) {_vm.$set(_vm.search, \"maxAmount\", $$v)},expression:\"search.maxAmount\"}})],1)])],1),_c('div',{staticClass:\"advanced-row\"},[_c('el-form-item',{staticClass:\"advanced-item\",attrs:{\"label\":\"套餐类型\"}},[_c('el-select',{staticClass:\"package-select\",attrs:{\"placeholder\":\"选择套餐\",\"clearable\":\"\"},model:{value:(_vm.search.packageType),callback:function ($$v) {_vm.$set(_vm.search, \"packageType\", $$v)},expression:\"search.packageType\"}},[_c('el-option',{attrs:{\"label\":\"全部套餐\",\"value\":\"\"}}),_c('el-option',{attrs:{\"label\":\"基础套餐\",\"value\":\"basic\"}}),_c('el-option',{attrs:{\"label\":\"高级套餐\",\"value\":\"advanced\"}}),_c('el-option',{attrs:{\"label\":\"专业套餐\",\"value\":\"professional\"}})],1)],1),_c('el-form-item',{staticClass:\"advanced-item\",attrs:{\"label\":\"排序方式\"}},[_c('el-select',{staticClass:\"sort-select\",attrs:{\"placeholder\":\"排序字段\"},model:{value:(_vm.search.sortBy),callback:function ($$v) {_vm.$set(_vm.search, \"sortBy\", $$v)},expression:\"search.sortBy\"}},[_c('el-option',{attrs:{\"label\":\"创建时间\",\"value\":\"create_time\"}}),_c('el-option',{attrs:{\"label\":\"支付金额\",\"value\":\"pay_age\"}}),_c('el-option',{attrs:{\"label\":\"订单状态\",\"value\":\"status\"}}),_c('el-option',{attrs:{\"label\":\"到期时间\",\"value\":\"end_time\"}})],1)],1),_c('el-form-item',{staticClass:\"advanced-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-check\"},on:{\"click\":_vm.applyAdvancedSearch}},[_vm._v(\" 应用筛选 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-close\"},on:{\"click\":_vm.clearAdvancedSearch}},[_vm._v(\" 清空选项 \")])],1)],1)])],1)])],1)],1)]),_c('el-card',{staticClass:\"table-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticClass:\"header-left\"},[_c('span',{staticClass:\"card-title\"},[_c('i',{staticClass:\"el-icon-tickets\"}),_vm._v(\" 订单列表 \")]),(_vm.selectedRows.length > 0)?_c('div',{staticClass:\"selected-info\"},[_vm._v(\" 已选择 \"+_vm._s(_vm.selectedRows.length)+\" 项 \")]):_vm._e()]),_c('div',{staticClass:\"table-actions\"},[_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportData}},[_vm._v(\" 导出数据 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-check\",\"disabled\":_vm.selectedRows.length === 0,\"type\":\"success\"},on:{\"click\":_vm.batchApprove}},[_vm._v(\" 批量通过 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-close\",\"disabled\":_vm.selectedRows.length === 0,\"type\":\"danger\"},on:{\"click\":_vm.batchReject}},[_vm._v(\" 批量拒绝 \")])],1)]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"modern-table\",attrs:{\"data\":_vm.list,\"row-class-name\":_vm.tableRowClassName},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_c('el-table-column',{attrs:{\"label\":\"客户信息\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"client-info\",on:{\"click\":function($event){return _vm.viewUserData(scope.row.client?.id)}}},[_c('div',{staticClass:\"client-header\"},[_c('div',{staticClass:\"client-avatar\"},[_c('i',{staticClass:\"el-icon-office-building\"})]),_c('div',{staticClass:\"client-details\"},[_c('div',{staticClass:\"company-name\"},[_vm._v(\" \"+_vm._s(scope.row.client?.company || '未填写')+\" \")]),_c('div',{staticClass:\"contact-info\"},[_c('span',{staticClass:\"contact-name\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" \"+_vm._s(scope.row.client?.linkman || '未填写')+\" \")])]),_c('div',{staticClass:\"contact-phone\"},[_c('i',{staticClass:\"el-icon-phone\"}),_vm._v(\" \"+_vm._s(scope.row.client?.phone || '未填写')+\" \")])])])])]}}])}),_c('el-table-column',{attrs:{\"label\":\"套餐内容\",\"min-width\":\"180\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"package-info\"},[_c('div',{staticClass:\"package-name\"},[_c('i',{staticClass:\"el-icon-box\"}),_vm._v(\" \"+_vm._s(scope.row.taocan?.title || '未选择套餐')+\" \")]),_c('div',{staticClass:\"package-price\"},[_c('span',{staticClass:\"price-label\"},[_vm._v(\"价格：\")]),_c('span',{staticClass:\"price-value\"},[_vm._v(\"¥\"+_vm._s(scope.row.taocan?.price || 0))])]),_c('div',{staticClass:\"package-duration\"},[_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(\" \"+_vm._s(scope.row.taocan?.year || 0)+\"年服务 \")])],1)])]}}])}),_c('el-table-column',{attrs:{\"label\":\"支付情况\",\"min-width\":\"160\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"payment-info\"},[_c('div',{staticClass:\"payment-type\"},[_c('i',{staticClass:\"el-icon-wallet\"}),_vm._v(\" \"+_vm._s(scope.row.pay_type == 1 ? \"全款\" : `分期/${scope.row.qishu}期`)+\" \")]),_c('div',{staticClass:\"payment-amount\"},[_c('span',{staticClass:\"paid\"},[_vm._v(\"已付：¥\"+_vm._s(scope.row.pay_age))])]),(scope.row.pay_type != 1)?_c('div',{staticClass:\"remaining-amount\"},[_c('span',{staticClass:\"remaining\"},[_vm._v(\"余款：¥\"+_vm._s(scope.row.total_price - scope.row.pay_age))])]):_vm._e(),(scope.row.pay_type != 1)?_c('div',{staticClass:\"payment-progress\"},[_c('el-progress',{attrs:{\"percentage\":Math.round((scope.row.pay_age / scope.row.total_price) * 100),\"stroke-width\":6,\"show-text\":false}})],1):_vm._e()])]}}])}),_c('el-table-column',{attrs:{\"label\":\"审核状态\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"status-info\",on:{\"click\":function($event){return _vm.showStatus(scope.row)}}},[_c('el-tag',{staticClass:\"status-tag\",attrs:{\"type\":_vm.getStatusType(scope.row.status),\"effect\":scope.row.status == 1 ? 'plain' : 'dark'}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(scope.row.status))+\" \")]),(scope.row.status == 3)?_c('div',{staticClass:\"status-reason\"},[_vm._v(\" \"+_vm._s(scope.row.status_msg)+\" \")]):_vm._e()],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"member.title\",\"label\":\"业务员\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"member-info\"},[_c('el-tag',{attrs:{\"type\":\"info\",\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.member?.title || '未分配')+\" \")])],1)]}}])}),_c('el-table-column',{attrs:{\"label\":\"时间信息\",\"min-width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-info\"},[_c('div',{staticClass:\"create-time\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" 创建：\"+_vm._s(_vm.formatDate(scope.row.create_time))+\" \")]),_c('div',{staticClass:\"end-time\"},[_c('i',{staticClass:\"el-icon-date\"}),_vm._v(\" 到期：\"+_vm._s(_vm.formatDate(scope.row.end_time))+\" \")]),_c('div',{staticClass:\"remaining-days\",class:_vm.getRemainingDaysClass(scope.row.end_time)},[_c('i',{staticClass:\"el-icon-warning\"}),_vm._v(\" \"+_vm._s(_vm.getRemainingDays(scope.row.end_time))+\" \")])])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"160\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{staticClass:\"view-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 查看 \")]),(scope.row.status === 1)?_c('el-button',{staticClass:\"approve-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.quickApprove(scope.row)}}},[_c('i',{staticClass:\"el-icon-check\"}),_vm._v(\" 通过 \")]):_vm._e(),(scope.row.status === 1)?_c('el-button',{staticClass:\"reject-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.quickReject(scope.row)}}},[_c('i',{staticClass:\"el-icon-close\"}),_vm._v(\" 拒绝 \")]):_vm._e(),_c('el-button',{staticClass:\"delete-btn\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_c('i',{staticClass:\"el-icon-delete\"}),_vm._v(\" 移除 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{staticClass:\"order-detail-dialog\",attrs:{\"title\":\"订单详情\",\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"85%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[(_vm.is_info)?_c('div',{staticClass:\"order-detail-content\"},[_c('el-card',{staticClass:\"detail-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"detail-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 客户信息 \")]),_c('el-descriptions',{attrs:{\"column\":3,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_c('el-tag',{attrs:{\"type\":\"info\"}},[_vm._v(_vm._s(_vm.info.client?.company || '未填写'))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[(_vm.info.client)?_c('el-tag',{staticClass:\"clickable-tag\",on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client?.id)}}},[_vm._v(\" \"+_vm._s(_vm.info.client?.linkman || '未填写')+\" \")]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"联系方式\"}},[(_vm.info.client)?_c('el-tag',{staticClass:\"clickable-tag\",on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client?.id)}}},[_vm._v(\" \"+_vm._s(_vm.info.client?.phone || '未填写')+\" \")]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"营业执照\"}},[(_vm.info.client?.pic_path)?_c('el-tag',{staticClass:\"clickable-tag\",on:{\"click\":function($event){return _vm.showImage(_vm.info.client?.pic_path)}}},[_vm._v(\" 查看执照 \")]):_c('el-tag',{attrs:{\"type\":\"info\"}},[_vm._v(\"暂无\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(\" \"+_vm._s(_vm.info.client?.tiaojie_id || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(\" \"+_vm._s(_vm.info.client?.fawu_id || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(\" \"+_vm._s(_vm.info.client?.lian_id || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"合同专员\"}},[_vm._v(\" \"+_vm._s(_vm.info.client?.htsczy_id || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"指定律师\"}},[_vm._v(\" \"+_vm._s(_vm.info.client?.ls_id || '未分配')+\" \")])],1)],1),(_vm.info.debts && _vm.info.debts.length > 0)?_c('el-card',{staticClass:\"detail-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"detail-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user-solid\"}),_vm._v(\" 债务人信息 \")]),_c('el-table',{staticClass:\"debt-table\",attrs:{\"data\":_vm.info.debts,\"size\":\"medium\"}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\"}}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"联系电话\"}}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额（元）\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}],null,false,1629117519)}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getDebtStatusType(scope.row.status)}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}],null,false,1325240676)})],1)],1):_vm._e(),(_vm.info.taocan)?_c('el-card',{staticClass:\"detail-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"detail-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-box\"}),_vm._v(\" 套餐信息 \")]),_c('el-descriptions',{attrs:{\"column\":3,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"套餐名称\"}},[_c('el-tag',{attrs:{\"type\":\"primary\"}},[_vm._v(_vm._s(_vm.info.taocan.title))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"套餐价格\"}},[_c('span',{staticClass:\"price-highlight\"},[_vm._v(\"¥\"+_vm._s(_vm.info.taocan.price))])]),_c('el-descriptions-item',{attrs:{\"label\":\"服务年限\"}},[_c('el-tag',{attrs:{\"type\":\"success\"}},[_vm._v(_vm._s(_vm.info.taocan.year)+\"年\")])],1)],1)],1):_vm._e()],1):_vm._e(),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"关闭\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.downloadOrder}},[_vm._v(\"下载订单\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('div',{staticClass:\"image-viewer\"},[_c('el-image',{attrs:{\"src\":_vm.show_image,\"fit\":\"contain\"}})],1)]),_c('el-dialog',{staticClass:\"revenue-dialog\",attrs:{\"title\":\"收入统计分析\",\"visible\":_vm.showRevenueDialog,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.showRevenueDialog=$event}}},[_c('div',{staticClass:\"revenue-stats\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"chart-card\"},[_c('h4',[_vm._v(\"月度收入趋势\")]),_c('div',{staticClass:\"chart-placeholder\"},[_c('i',{staticClass:\"el-icon-data-line chart-icon\"}),_c('p',[_vm._v(\"月度收入趋势图\")]),_c('div',{staticClass:\"mock-chart-data\"},[_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"60%\"}}),_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"80%\"}}),_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"45%\"}}),_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"70%\"}}),_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"90%\"}}),_c('div',{staticClass:\"chart-bar\",staticStyle:{\"height\":\"65%\"}})])])])]),_c('el-col',{attrs:{\"span\":12}},[_c('div',{staticClass:\"chart-card\"},[_c('h4',[_vm._v(\"支付方式分布\")]),_c('div',{staticClass:\"chart-placeholder\"},[_c('i',{staticClass:\"el-icon-pie-chart chart-icon\"}),_c('p',[_vm._v(\"支付方式比例图\")]),_c('div',{staticClass:\"payment-stats\"},[_c('div',{staticClass:\"payment-item\"},[_c('span',{staticClass:\"payment-dot full-payment\"}),_vm._v(\" 全款支付: \"+_vm._s(_vm.fullPaymentCount)+\" \")]),_c('div',{staticClass:\"payment-item\"},[_c('span',{staticClass:\"payment-dot installment-payment\"}),_vm._v(\" 分期付款: \"+_vm._s(_vm.installmentPaymentCount)+\" \")])])])])])],1),_c('el-row',{staticStyle:{\"margin-top\":\"20px\"},attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"chart-card\"},[_c('h4',[_vm._v(\"订单状态统计\")]),_c('div',{staticClass:\"status-overview\"},[_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-circle pending-circle\"},[_vm._v(_vm._s(_vm.pendingOrders))]),_c('span',[_vm._v(\"待审核\")])]),_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-circle approved-circle\"},[_vm._v(_vm._s(_vm.approvedOrders))]),_c('span',[_vm._v(\"已通过\")])]),_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-circle rejected-circle\"},[_vm._v(_vm._s(_vm.rejectedOrders))]),_c('span',[_vm._v(\"已拒绝\")])]),_c('div',{staticClass:\"status-item\"},[_c('div',{staticClass:\"status-circle total-circle\"},[_vm._v(_vm._s(_vm.total))]),_c('span',[_vm._v(\"总计\")])])])])])],1)],1)]),_c('el-dialog',{staticClass:\"export-dialog\",attrs:{\"title\":\"数据导出\",\"visible\":_vm.showExportDialog,\"width\":\"600px\"},on:{\"update:visible\":function($event){_vm.showExportDialog=$event}}},[_c('el-form',{attrs:{\"model\":_vm.exportForm,\"label-width\":\"120px\"}},[_c('el-form-item',{attrs:{\"label\":\"导出格式\"}},[_c('el-radio-group',{model:{value:(_vm.exportForm.format),callback:function ($$v) {_vm.$set(_vm.exportForm, \"format\", $$v)},expression:\"exportForm.format\"}},[_c('el-radio',{attrs:{\"label\":\"excel\"}},[_vm._v(\"Excel (.xlsx)\")]),_c('el-radio',{attrs:{\"label\":\"csv\"}},[_vm._v(\"CSV (.csv)\")]),_c('el-radio',{attrs:{\"label\":\"pdf\"}},[_vm._v(\"PDF (.pdf)\")])],1)],1),_c('el-form-item',{attrs:{\"label\":\"导出内容\"}},[_c('el-checkbox-group',{model:{value:(_vm.exportForm.fields),callback:function ($$v) {_vm.$set(_vm.exportForm, \"fields\", $$v)},expression:\"exportForm.fields\"}},[_c('el-checkbox',{attrs:{\"label\":\"client\"}},[_vm._v(\"客户信息\")]),_c('el-checkbox',{attrs:{\"label\":\"package\"}},[_vm._v(\"套餐信息\")]),_c('el-checkbox',{attrs:{\"label\":\"payment\"}},[_vm._v(\"支付情况\")]),_c('el-checkbox',{attrs:{\"label\":\"status\"}},[_vm._v(\"审核状态\")]),_c('el-checkbox',{attrs:{\"label\":\"time\"}},[_vm._v(\"时间信息\")]),_c('el-checkbox',{attrs:{\"label\":\"member\"}},[_vm._v(\"业务员信息\")])],1)],1),_c('el-form-item',{attrs:{\"label\":\"数据范围\"}},[_c('el-radio-group',{model:{value:(_vm.exportForm.range),callback:function ($$v) {_vm.$set(_vm.exportForm, \"range\", $$v)},expression:\"exportForm.range\"}},[_c('el-radio',{attrs:{\"label\":\"all\"}},[_vm._v(\"全部数据\")]),_c('el-radio',{attrs:{\"label\":\"current\"}},[_vm._v(\"当前页面\")]),_c('el-radio',{attrs:{\"label\":\"selected\"}},[_vm._v(\"选中项目\")]),_c('el-radio',{attrs:{\"label\":\"filtered\"}},[_vm._v(\"筛选结果\")])],1)],1),_c('el-form-item',{attrs:{\"label\":\"时间范围\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.exportForm.dateRange),callback:function ($$v) {_vm.$set(_vm.exportForm, \"dateRange\", $$v)},expression:\"exportForm.dateRange\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.showExportDialog = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.exportLoading},on:{\"click\":_vm.executeExport}},[_c('i',{staticClass:\"el-icon-download\"}),_vm._v(\" 开始导出 \")])],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"order-management-container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<div class=\"page-header\">\r\n\t\t\t<div class=\"header-left\">\r\n\t\t\t\t<h2 class=\"page-title\">\r\n\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t{{ this.$router.currentRoute.name }}\r\n\t\t\t\t</h2>\r\n\t\t\t\t<div class=\"page-subtitle\">管理客户签约订单和合同信息</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t<el-button\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\ticon=\"el-icon-refresh\"\r\n\t\t\t\t\t\t@click=\"refulsh\"\r\n\t\t\t\t\t\tclass=\"refresh-btn\"\r\n\t\t\t\t>\r\n\t\t\t\t\t刷新数据\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 重要通知和提醒区域 -->\r\n\t\t<div class=\"notifications-section\" v-if=\"hasImportantNotifications\">\r\n\t\t\t<el-card shadow=\"hover\" class=\"notification-card\">\r\n\t\t\t\t<div class=\"notification-header\">\r\n\t\t\t\t\t<i class=\"el-icon-warning-outline notification-icon\"></i>\r\n\t\t\t\t\t<span class=\"notification-title\">重要提醒</span>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tsize=\"mini\" \r\n\t\t\t\t\t\t@click=\"dismissNotifications\"\r\n\t\t\t\t\t\tclass=\"dismiss-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"notification-content\">\r\n\t\t\t\t\t<div class=\"notification-list\">\r\n\t\t\t\t\t\t<!-- 待审核订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"pendingOrders > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item urgent\"\r\n\t\t\t\t\t\t\t@click=\"filterByStatus('1')\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ pendingOrders }}</strong> 个订单待审核，请及时处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"warning\">立即处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 即将到期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiringOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item warning\"\r\n\t\t\t\t\t\t\t@click=\"showExpiringOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiringOrders.length }}</strong> 个订单即将到期，请提醒客户续费\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\">查看详情</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 已过期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiredOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item error\"\r\n\t\t\t\t\t\t\t@click=\"showExpiredOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiredOrders.length }}</strong> 个订单已过期，需要联系客户处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\">紧急处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 高价值订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"highValueOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item info\"\r\n\t\t\t\t\t\t\t@click=\"showHighValueOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ highValueOrders.length }}</strong> 个高价值订单需要重点关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"success\">查看订单</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\r\n\t\t<!-- 统计卡片区域 -->\r\n\t\t<div class=\"stats-section\">\r\n\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon total-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ total }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总订单数</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +8%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('1')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon pending-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">待审核</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change warning\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i> 需关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('2')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon approved-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-circle-check\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">已通过</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +12%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"showRevenueChart\">\r\n\t\t\t\t\t\t<div class=\"stat-icon revenue-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-money\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">¥{{ totalRevenue }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总收入</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +15%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t</div>\r\n\r\n\t\t<!-- 搜索和筛选区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"search-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-search\"></i>\r\n\t\t\t\t\t\t搜索与筛选\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"card-subtitle\">快速查找和管理订单信息</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t\t<el-button-group class=\"action-group\">\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t\t导出\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n\t\t\t\t\t\t\t刷新\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</el-button-group>\r\n\t\t\t\t\t<el-button type=\"primary\" @click=\"batchAudit\" icon=\"el-icon-check\" class=\"primary-action\">\r\n\t\t\t\t\t\t批量审核\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"search-section\">\r\n\t\t\t\t<el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n\t\t\t\t\t<div class=\"search-row\">\r\n\t\t\t\t\t\t<el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tprefix-icon=\"el-icon-search\"\r\n\t\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t\t\********************=\"getData()\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"业务员\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.salesman\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tclass=\"search-select\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"审核状态\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-select v-model=\"search.status\" placeholder=\"选择状态\" clearable class=\"search-select\">\r\n\t\t\t\t\t\t\t\t<el-option label=\"全部状态\" value=\"\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未审核\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"已通过\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未通过\" value=\"3\" />\r\n\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item class=\"search-actions-item\">\r\n\t\t\t\t\t\t\t<div class=\"search-actions\">\r\n\t\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\" class=\"search-btn\">\r\n\t\t\t\t\t\t\t\t\t搜索\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n\t\t\t\t\t\t\t\t\t重置\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n\t\t\t\t\t\t\t\t\t<i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ showAdvanced ? '收起' : '高级筛选' }}\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 高级筛选区域 -->\r\n\t\t\t\t\t<transition name=\"slide-fade\">\r\n\t\t\t\t\t\t<div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n\t\t\t\t\t\t\t<el-divider content-position=\"left\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-setting\"></i>\r\n\t\t\t\t\t\t\t\t高级筛选选项\r\n\t\t\t\t\t\t\t</el-divider>\r\n\t\t\t\t\t\t\t<div class=\"advanced-content\">\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付时间\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"date-picker\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.payType\" placeholder=\"选择支付方式\" class=\"pay-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部方式\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全款支付\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"分期付款\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"金额范围\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"amount-range\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.minAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最小金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"range-separator\">-</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.maxAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最大金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"套餐类型\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.packageType\" placeholder=\"选择套餐\" clearable class=\"package-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部套餐\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"基础套餐\" value=\"basic\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"高级套餐\" value=\"advanced\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"专业套餐\" value=\"professional\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.sortBy\" placeholder=\"排序字段\" class=\"sort-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"创建时间\" value=\"create_time\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"支付金额\" value=\"pay_age\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"订单状态\" value=\"status\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"到期时间\" value=\"end_time\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item class=\"advanced-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n\t\t\t\t\t\t\t\t\t\t\t应用筛选\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n\t\t\t\t\t\t\t\t\t\t\t清空选项\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</transition>\r\n\t\t\t\t</el-form>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 数据表格区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"table-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-tickets\"></i>\r\n\t\t\t\t\t\t订单列表\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"selected-info\" v-if=\"selectedRows.length > 0\">\r\n\t\t\t\t\t\t已选择 {{ selectedRows.length }} 项\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"table-actions\">\r\n\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t导出数据\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchApprove\" \r\n\t\t\t\t\t\ticon=\"el-icon-check\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量通过\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchReject\" \r\n\t\t\t\t\t\ticon=\"el-icon-close\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"danger\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量拒绝\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<el-table \r\n\t\t\t\t\t:data=\"list\" \r\n\t\t\t\t\tv-loading=\"loading\" \r\n\t\t\t\t\tclass=\"modern-table\"\r\n\t\t\t\t\t:row-class-name=\"tableRowClassName\"\r\n\t\t\t\t\t@selection-change=\"handleSelectionChange\"\r\n\t\t\t>\r\n\t\t\t\t<el-table-column type=\"selection\" width=\"55\" />\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"客户信息\" min-width=\"200\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"client-info\" @click=\"viewUserData(scope.row.client?.id)\">\r\n\t\t\t\t\t\t\t<div class=\"client-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"client-avatar\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-office-building\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"client-details\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"company-name\">\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.company || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-info\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"contact-name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-phone\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-phone\"></i>\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"套餐内容\" min-width=\"180\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"package-info\">\r\n\t\t\t\t\t\t\t<div class=\"package-name\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.taocan?.title || '未选择套餐' }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-price\">\r\n\t\t\t\t\t\t\t\t<span class=\"price-label\">价格：</span>\r\n\t\t\t\t\t\t\t\t<span class=\"price-value\">¥{{ scope.row.taocan?.price || 0 }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-duration\">\r\n\t\t\t\t\t\t\t\t<el-tag size=\"small\" type=\"info\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.taocan?.year || 0 }}年服务\r\n\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"支付情况\" min-width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"payment-info\">\r\n\t\t\t\t\t\t\t<div class=\"payment-type\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-wallet\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.pay_type == 1 ? \"全款\" : `分期/${scope.row.qishu}期` }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-amount\">\r\n\t\t\t\t\t\t\t\t<span class=\"paid\">已付：¥{{ scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-amount\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<span class=\"remaining\">余款：¥{{ scope.row.total_price - scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-progress\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<el-progress \r\n\t\t\t\t\t\t\t\t\t:percentage=\"Math.round((scope.row.pay_age / scope.row.total_price) * 100)\" \r\n\t\t\t\t\t\t\t\t\t:stroke-width=\"6\"\r\n\t\t\t\t\t\t\t\t\t:show-text=\"false\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"审核状态\" width=\"120\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"status-info\" @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t:type=\"getStatusType(scope.row.status)\" \r\n\t\t\t\t\t\t\t\t\tclass=\"status-tag\"\r\n\t\t\t\t\t\t\t\t\t:effect=\"scope.row.status == 1 ? 'plain' : 'dark'\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{{ getStatusText(scope.row.status) }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t<div v-if=\"scope.row.status == 3\" class=\"status-reason\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.status_msg }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\" width=\"100\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"member-info\">\r\n\t\t\t\t\t\t\t<el-tag type=\"info\" size=\"small\">\r\n\t\t\t\t\t\t\t\t{{ scope.row.member?.title || '未分配' }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"时间信息\" min-width=\"140\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"time-info\">\r\n\t\t\t\t\t\t\t<div class=\"create-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t\t\t创建：{{ formatDate(scope.row.create_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"end-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-date\"></i>\r\n\t\t\t\t\t\t\t\t到期：{{ formatDate(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-days\" :class=\"getRemainingDaysClass(scope.row.end_time)\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t\t{{ getRemainingDays(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\" width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"editData(scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"view-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-view\"></i>\r\n\t\t\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickApprove(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"approve-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-check\"></i>\r\n\t\t\t\t\t\t\t\t\t通过\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickReject(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"reject-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t\t\t\t\t拒绝\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"delData(scope.$index, scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"delete-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-delete\"></i>\r\n\t\t\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t\r\n\t\t\t<!-- 分页 -->\r\n\t\t\t<div class=\"pagination-wrapper\">\r\n\t\t\t\t<el-pagination\r\n\t\t\t\t\t\t@size-change=\"handleSizeChange\"\r\n\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t\t:page-sizes=\"[20, 50, 100, 200]\"\r\n\t\t\t\t\t\t:page-size=\"size\"\r\n\t\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\"\r\n\t\t\t\t\t\t:total=\"total\"\r\n\t\t\t\t\t\tbackground\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 订单详情对话框 -->\r\n\t\t<el-dialog \r\n\t\t\t\ttitle=\"订单详情\" \r\n\t\t\t\t:visible.sync=\"dialogFormVisible\" \r\n\t\t\t\t:close-on-click-modal=\"false\" \r\n\t\t\t\twidth=\"85%\"\r\n\t\t\t\tclass=\"order-detail-dialog\"\r\n\t\t>\r\n\t\t\t\t<div v-if=\"is_info\" class=\"order-detail-content\">\r\n\t\t\t\t\t\t<!-- 客户信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t客户信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"公司名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"info\">{{ info.client?.company || '未填写' }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系人\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系方式\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"showImage(info.client?.pic_path)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client?.pic_path\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t查看执照\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag v-else type=\"info\">暂无</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"调解员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.tiaojie_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"法务专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.fawu_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"立案专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.lian_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"合同专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.htsczy_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"指定律师\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.ls_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 债务人信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.debts && info.debts.length > 0\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user-solid\"></i>\r\n\t\t\t\t\t\t\t\t\t\t债务人信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-table :data=\"info.debts\" size=\"medium\" class=\"debt-table\">\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"name\" label=\"债务人姓名\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"tel\" label=\"联系电话\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"money\" label=\"债务金额（元）\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"status\" label=\"状态\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag :type=\"getDebtStatusType(scope.row.status)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.status }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t</el-table>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 套餐信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.taocan\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t\t\t套餐信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"primary\">{{ info.taocan.title }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-highlight\">¥{{ info.taocan.price }}</span>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"服务年限\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"success\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t\t\t<el-button @click=\"dialogFormVisible = false\">关闭</el-button>\r\n\t\t\t\t\t\t<el-button type=\"primary\" @click=\"downloadOrder\">下载订单</el-button>\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 图片查看对话框 -->\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n\t\t\t\t<div class=\"image-viewer\">\r\n\t\t\t\t\t\t<el-image :src=\"show_image\" fit=\"contain\" />\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 收入统计图表对话框 -->\r\n\t\t<el-dialog title=\"收入统计分析\" :visible.sync=\"showRevenueDialog\" width=\"80%\" class=\"revenue-dialog\">\r\n\t\t\t<div class=\"revenue-stats\">\r\n\t\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>月度收入趋势</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-data-line chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>月度收入趋势图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"mock-chart-data\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 60%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 80%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 45%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 70%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 90%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 65%\"></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>支付方式分布</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-pie-chart chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>支付方式比例图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"payment-stats\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot full-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t全款支付: {{ fullPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot installment-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t分期付款: {{ installmentPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t\t<el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n\t\t\t\t\t<el-col :span=\"24\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>订单状态统计</h4>\r\n\t\t\t\t\t\t\t<div class=\"status-overview\">\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle pending-circle\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>待审核</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle approved-circle\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已通过</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle rejected-circle\">{{ rejectedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已拒绝</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle total-circle\">{{ total }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>总计</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 数据导出对话框 -->\r\n\t\t<el-dialog title=\"数据导出\" :visible.sync=\"showExportDialog\" width=\"600px\" class=\"export-dialog\">\r\n\t\t\t<el-form :model=\"exportForm\" label-width=\"120px\">\r\n\t\t\t\t<el-form-item label=\"导出格式\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.format\">\r\n\t\t\t\t\t\t<el-radio label=\"excel\">Excel (.xlsx)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"csv\">CSV (.csv)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"pdf\">PDF (.pdf)</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"导出内容\">\r\n\t\t\t\t\t<el-checkbox-group v-model=\"exportForm.fields\">\r\n\t\t\t\t\t\t<el-checkbox label=\"client\">客户信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"package\">套餐信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"payment\">支付情况</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"status\">审核状态</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"time\">时间信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"member\">业务员信息</el-checkbox>\r\n\t\t\t\t\t</el-checkbox-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"数据范围\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.range\">\r\n\t\t\t\t\t\t<el-radio label=\"all\">全部数据</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"current\">当前页面</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"selected\">选中项目</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"filtered\">筛选结果</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"时间范围\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tv-model=\"exportForm.dateRange\"\r\n\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tstyle=\"width: 100%\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"showExportDialog = false\">取消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"executeExport\" :loading=\"exportLoading\">\r\n\t\t\t\t\t<i class=\"el-icon-download\"></i>\r\n\t\t\t\t\t开始导出\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t\tallSize: \"mini\",\r\n\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\ttotal: 1,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tsize: 20,\r\n\t\t\t\t\t\tsearch: {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tloading: true,\r\n\t\t\t\t\t\turl: \"/dingdan/\",\r\n\t\t\t\t\t\ttitle: \"签约用户\",\r\n\t\t\t\t\t\tinfo: {},\r\n\t\t\t\t\t\tcurrentId:0,\r\n\t\t\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\t\t\tshow_image: \"\",\r\n\t\t\t\t\t\tdialogVisible: false,\r\n\t\t\t\t\t\tdialogViewUserDetail:false,\r\n\t\t\t\t\t\tis_info: false,\r\n\t\t\t\t\t\tupload_index: \"\",\r\n\t\t\t\t\t\tdialogStatus: false,\r\n\t\t\t\t\t\tdialogEndTime: false,\r\n\t\t\t\t\t\truleForm: {\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tstatus_msg: \"\",\r\n\t\t\t\t\t\t\t\tend_time: \"\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\trules: {\r\n\t\t\t\t\t\t\t\tstatus_msg: [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\t\t\tshowAdvanced: false,\r\n\t\t\t\t\t\tselectedRows: [],\r\n\t\t\t\t\t\tshowRevenueDialog: false,\r\n\t\t\t\t\t\tshowExportDialog: false,\r\n\t\t\t\t\t\texportForm: {\r\n\t\t\t\t\t\t\t\tformat: \"excel\",\r\n\t\t\t\t\t\t\t\tfields: [\"client\", \"package\", \"payment\", \"status\", \"time\", \"member\"],\r\n\t\t\t\t\t\t\t\trange: \"all\",\r\n\t\t\t\t\t\t\t\tdateRange: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\texportLoading: false,\r\n\t\t\t\t\t\tshowNotifications: true\r\n\t\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t\t// 统计数据计算\r\n\t\t\t\tpendingOrders() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => item.status === 1).length : 0;\r\n\t\t\t\t},\r\n\r\n\t\t\t\tapprovedOrders() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => item.status === 2).length : 0;\r\n\t\t\t\t},\r\n\r\n\t\t\t\trejectedOrders() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => item.status === 3).length : 0;\r\n\t\t\t\t},\r\n\r\n\t\t\t\ttotalRevenue() {\r\n\t\t\t\t\t\tif (!Array.isArray(this.list)) return '0';\r\n\t\t\t\t\t\treturn this.list.reduce((sum, item) => sum + (item.pay_age || 0), 0).toLocaleString();\r\n\t\t\t\t},\r\n\r\n\t\t\t\tfullPaymentCount() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => item.pay_type === 1).length : 0;\r\n\t\t\t\t},\r\n\r\n\t\t\t\tinstallmentPaymentCount() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => item.pay_type !== 1).length : 0;\r\n\t\t\t\t},\r\n\t\t\t\t// 即将到期的订单（7天内）\r\n\t\t\t\texpiringOrders() {\r\n\t\t\t\t\t\tif (!Array.isArray(this.list)) return [];\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\tconst sevenDaysLater = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate >= today && endDate <= sevenDaysLater;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 已过期的订单\r\n\t\t\t\texpiredOrders() {\r\n\t\t\t\t\t\tif (!Array.isArray(this.list)) return [];\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate < today;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 高价值订单（金额超过5000）\r\n\t\t\t\thighValueOrders() {\r\n\t\t\t\t\t\treturn Array.isArray(this.list) ? this.list.filter(item => (item.pay_age || 0) > 5000) : [];\r\n\t\t\t\t},\r\n\t\t\t\t// 是否有重要通知\r\n\t\t\t\thasImportantNotifications() {\r\n\t\t\t\t\t\treturn this.showNotifications && (\r\n\t\t\t\t\t\t\t\tthis.pendingOrders > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiringOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiredOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.highValueOrders.length > 0\r\n\t\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\teditData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tviewUserData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t\t},\r\n\t\t\t\tgetInfo(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tdelData(index, id) {\r\n\t\t\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\r\n\t\t\t\tupdateEndTIme() {\r\n\t\t\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\trefulsh() {\r\n\t\t\t\t\t\tthis.$router.go(0);\r\n\t\t\t\t},\r\n\t\t\t\tgetData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.loading = true;\r\n\t\t\t\t\t\t_this\r\n\t\t\t\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tsaveData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\thandleSizeChange(val) {\r\n\t\t\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleCurrentChange(val) {\r\n\t\t\t\t\t\tthis.page = val;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleSuccess(res) {\r\n\t\t\t\t\t\tlet _this = this\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t},\r\n\t\t\t\tbeforeUpload(file) {\r\n\t\t\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdelImage(file, fileName) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tshowImage(file) {\r\n\t\t\t\t\t\tthis.show_image = file;\r\n\t\t\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tchangePinzhen(index) {\r\n\t\t\t\t\t\tthis.index = index\r\n\t\t\t\t},\r\n\t\t\t\tshowStatus(row) {\r\n\t\t\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tshowEndTime(row) {\r\n\t\t\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tchangeEndTime() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusType(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: 'warning',\r\n\t\t\t\t\t\t\t\t2: 'success', \r\n\t\t\t\t\t\t\t\t3: 'danger'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || 'info';\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusText(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: '未审核',\r\n\t\t\t\t\t\t\t\t2: '已通过',\r\n\t\t\t\t\t\t\t\t3: '未通过'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || '未知';\r\n\t\t\t\t},\r\n\t\t\t\tgetDebtStatusType(status) {\r\n\t\t\t\t\t\t// 债务状态类型映射\r\n\t\t\t\t\t\tif (status === '已解决') return 'success';\r\n\t\t\t\t\t\tif (status === '处理中') return 'warning';\r\n\t\t\t\t\t\treturn 'info';\r\n\t\t\t\t},\r\n\t\t\t\tformatDate(dateStr) {\r\n\t\t\t\t\t\tif (!dateStr) return '未设置';\r\n\t\t\t\t\t\treturn new Date(dateStr).toLocaleDateString('zh-CN');\r\n\t\t\t\t},\r\n\t\t\t\ttableRowClassName({row}) {\r\n\t\t\t\t\t\tif (row.status === 1) return 'warning-row';\r\n\t\t\t\t\t\tif (row.status === 3) return 'danger-row';\r\n\t\t\t\t\t\treturn '';\r\n\t\t\t\t},\r\n\t\t\t\tresetSearch() {\r\n\t\t\t\t\t\tthis.search = {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\texportData() {\r\n\t\t\t\t\tthis.showExportDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\tdownloadOrder() {\r\n\t\t\t\t\t\tthis.$message.success('订单下载功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\ttoggleAdvanced() {\r\n\t\t\t\t\t\tthis.showAdvanced = !this.showAdvanced;\r\n\t\t\t\t},\r\n\t\t\t\tapplyAdvancedSearch() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tclearAdvancedSearch() {\r\n\t\t\t\t\t\tthis.resetSearch();\r\n\t\t\t\t},\r\n\t\t\t\trefreshData() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tbatchAudit() {\r\n\t\t\t\t\t\tthis.$message.success('批量审核功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\thandleSelectionChange(selection) {\r\n\t\t\t\t\t\tthis.selectedRows = selection;\r\n\t\t\t\t},\r\n\t\t\t\tfilterByStatus(status) {\r\n\t\t\t\t\tthis.search.status = status;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t\tthis.$message.success(`已筛选${this.getStatusText(status) || '全部'}订单`);\r\n\t\t\t\t},\r\n\t\t\t\tshowRevenueChart() {\r\n\t\t\t\t\tthis.showRevenueDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\texecuteExport() {\r\n\t\t\t\t\tthis.exportLoading = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟导出过程\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst formatText = {\r\n\t\t\t\t\t\t\t'excel': 'Excel',\r\n\t\t\t\t\t\t\t'csv': 'CSV', \r\n\t\t\t\t\t\t\t'pdf': 'PDF'\r\n\t\t\t\t\t\t}[this.exportForm.format];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst rangeText = {\r\n\t\t\t\t\t\t\t'all': '全部数据',\r\n\t\t\t\t\t\t\t'current': '当前页面',\r\n\t\t\t\t\t\t\t'selected': '选中项目',\r\n\t\t\t\t\t\t\t'filtered': '筛选结果'\r\n\t\t\t\t\t\t}[this.exportForm.range];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 这里可以集成真实的导出库，如 xlsx、jsPDF等\r\n\t\t\t\t\t\tconst blob = this.generateExportData();\r\n\t\t\t\t\t\tconst url = URL.createObjectURL(blob);\r\n\t\t\t\t\t\tconst link = document.createElement('a');\r\n\t\t\t\t\t\tlink.href = url;\r\n\t\t\t\t\t\tlink.download = `订单数据_${new Date().toISOString().split('T')[0]}.${this.exportForm.format}`;\r\n\t\t\t\t\t\tlink.click();\r\n\t\t\t\t\t\tURL.revokeObjectURL(url);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.exportLoading = false;\r\n\t\t\t\t\t\tthis.showExportDialog = false;\r\n\t\t\t\t\t\tthis.$message.success(`${formatText}格式的${rangeText}导出成功！`);\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t},\r\n\t\t\t\tgenerateExportData() {\r\n\t\t\t\t\t// 根据选择的范围获取数据\r\n\t\t\t\t\tlet exportList = [];\r\n\t\t\t\t\tswitch(this.exportForm.range) {\r\n\t\t\t\t\t\tcase 'all':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'current':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'selected':\r\n\t\t\t\t\t\t\texportList = this.selectedRows;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'filtered':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据选择的字段生成数据\r\n\t\t\t\t\tconst data = exportList.map(item => {\r\n\t\t\t\t\t\tconst row = {};\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('client')) {\r\n\t\t\t\t\t\t\trow['公司名称'] = item.client?.company || '';\r\n\t\t\t\t\t\t\trow['联系人'] = item.client?.linkman || '';\r\n\t\t\t\t\t\t\trow['联系电话'] = item.client?.phone || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('package')) {\r\n\t\t\t\t\t\t\trow['套餐名称'] = item.taocan?.title || '';\r\n\t\t\t\t\t\t\trow['套餐价格'] = item.taocan?.price || 0;\r\n\t\t\t\t\t\t\trow['服务年限'] = item.taocan?.year || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('payment')) {\r\n\t\t\t\t\t\t\trow['支付方式'] = item.pay_type == 1 ? '全款' : '分期';\r\n\t\t\t\t\t\t\trow['已付金额'] = item.pay_age || 0;\r\n\t\t\t\t\t\t\trow['总金额'] = item.total_price || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('status')) {\r\n\t\t\t\t\t\t\trow['审核状态'] = this.getStatusText(item.status);\r\n\t\t\t\t\t\t\trow['状态说明'] = item.status_msg || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('time')) {\r\n\t\t\t\t\t\t\trow['创建时间'] = item.create_time || '';\r\n\t\t\t\t\t\t\trow['到期时间'] = item.end_time || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('member')) {\r\n\t\t\t\t\t\t\trow['业务员'] = item.member?.title || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn row;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 生成CSV格式的Blob\r\n\t\t\t\t\tconst csvContent = this.convertToCSV(data);\r\n\t\t\t\t\treturn new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\t\t\t\t},\r\n\t\t\t\tconvertToCSV(data) {\r\n\t\t\t\t\tif (!data || data.length === 0) return '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst headers = Object.keys(data[0]);\r\n\t\t\t\t\tconst csvRows = [headers.join(',')];\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor (const row of data) {\r\n\t\t\t\t\t\tconst values = headers.map(header => {\r\n\t\t\t\t\t\t\tconst value = row[header];\r\n\t\t\t\t\t\t\treturn typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tcsvRows.push(values.join(','));\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn csvRows.join('\\n');\r\n\t\t\t\t},\r\n\t\t\t\tbatchApprove() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量通过的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$confirm(`确认批量通过选中的 ${pendingRows.length} 个待审核订单吗？`, '批量审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t\t'status_msg': '批量审核通过'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量审核完成，成功通过 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量审核过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量审核');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tbatchReject() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量拒绝的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$prompt('请输入批量拒绝理由', `批量拒绝 ${pendingRows.length} 个订单`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量拒绝完成，成功拒绝 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量拒绝过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量拒绝');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickApprove(row) {\r\n\t\t\t\t\tthis.$confirm(`确认通过 ${row.client?.company || '该客户'} 的订单吗？`, '快速审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\t// 调用API通过订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t'status_msg': '快速审核通过'\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('审核通过成功');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickReject(row) {\r\n\t\t\t\t\tthis.$prompt('请输入拒绝理由', `拒绝订单 - ${row.client?.company || '客户'}`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\t// 调用API拒绝订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('订单已拒绝');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDays(end_time) {\r\n\t\t\t\t\tif (!end_time) return '未设置';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return `已过期${Math.abs(remainingDays)}天`;\r\n\t\t\t\t\tif (remainingDays === 0) return '今天到期';\r\n\t\t\t\t\tif (remainingDays <= 7) return `${remainingDays}天后到期`;\r\n\t\t\t\t\tif (remainingDays <= 30) return `${remainingDays}天后到期`;\r\n\t\t\t\t\treturn `${remainingDays}天后到期`;\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDaysClass(end_time) {\r\n\t\t\t\t\tif (!end_time) return '';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return 'expired';\r\n\t\t\t\t\tif (remainingDays <= 3) return 'urgent';\r\n\t\t\t\t\tif (remainingDays <= 7) return 'warning';\r\n\t\t\t\t\treturn 'normal';\r\n\t\t\t\t},\r\n\t\t\t\t// 通知系统相关方法\r\n\t\t\t\tdismissNotifications() {\r\n\t\t\t\t\tthis.showNotifications = false;\r\n\t\t\t\t\tthis.$message.success('已暂时隐藏提醒通知');\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiringOrders() {\r\n\t\t\t\t\t// 显示即将到期的订单\r\n\t\t\t\t\tthis.$message.info(`查看${this.expiringOrders.length}个即将到期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiredOrders() {\r\n\t\t\t\t\t// 显示已过期的订单\r\n\t\t\t\t\tthis.$message.warning(`查看${this.expiredOrders.length}个已过期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowHighValueOrders() {\r\n\t\t\t\t\t// 显示高价值订单\r\n\t\t\t\t\tthis.$message.success(`查看${this.highValueOrders.length}个高价值订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n.order-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card:active {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.pending-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.revenue-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n.stat-change.warning {\r\n  color: #f39c12;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker,\r\n.pay-select,\r\n.package-select,\r\n.sort-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner,\r\n.pay-select .el-input__inner,\r\n.package-select .el-input__inner,\r\n.sort-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus,\r\n.pay-select .el-input__inner:focus,\r\n.package-select .el-input__inner:focus,\r\n.sort-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.amount-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.amount-range .el-input-number {\r\n  flex: 1;\r\n}\r\n\r\n.range-separator {\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n  padding: 0 4px;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 选择信息样式 */\r\n.selected-info {\r\n  font-size: 13px;\r\n  color: #667eea;\r\n  margin-top: 4px;\r\n  font-weight: 500;\r\n  background: rgba(102, 126, 234, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 表格行样式 */\r\n.modern-table .warning-row {\r\n  background-color: #fff7e6 !important;\r\n}\r\n\r\n.modern-table .danger-row {\r\n  background-color: #fff2f0 !important;\r\n}\r\n\r\n/* 客户信息样式 */\r\n.client-info {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.client-info:hover {\r\n  background-color: #f8f9ff;\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n  margin: -8px;\r\n}\r\n\r\n.client-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.client-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.client-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.company-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.contact-info, .contact-phone {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 2px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 套餐信息样式 */\r\n.package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.package-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.package-price {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.price-label {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.package-duration {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 支付信息样式 */\r\n.payment-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-type {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.payment-amount, .remaining-amount {\r\n  font-size: 13px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.paid {\r\n  color: #27ae60;\r\n  font-weight: 500;\r\n}\r\n\r\n.remaining {\r\n  color: #e74c3c;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-progress {\r\n  margin-top: 8px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__outer {\r\n  background-color: #f0f0f0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__inner {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 业务员信息样式 */\r\n.member-info {\r\n  text-align: center;\r\n}\r\n\r\n.member-info .el-tag {\r\n  font-size: 12px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 状态信息样式 */\r\n.status-info {\r\n  cursor: pointer;\r\n  text-align: center;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-reason {\r\n  font-size: 12px;\r\n  color: #e74c3c;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n}\r\n\r\n.create-time, .end-time {\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.remaining-days {\r\n  margin-top: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  text-align: center;\r\n}\r\n\r\n.remaining-days.normal {\r\n  background-color: #f0f9ff;\r\n  color: #1e40af;\r\n  border: 1px solid #dbeafe;\r\n}\r\n\r\n.remaining-days.warning {\r\n  background-color: #fef3c7;\r\n  color: #b45309;\r\n  border: 1px solid #fde68a;\r\n}\r\n\r\n.remaining-days.urgent {\r\n  background-color: #fee2e2;\r\n  color: #dc2626;\r\n  border: 1px solid #fecaca;\r\n}\r\n\r\n.remaining-days.expired {\r\n  background-color: #f3f4f6;\r\n  color: #6b7280;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.view-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.approve-btn {\r\n  color: #67C23A !important;\r\n}\r\n\r\n.reject-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.view-btn:hover, .approve-btn:hover, .reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n.approve-btn:hover {\r\n  background-color: rgba(103, 194, 58, 0.1) !important;\r\n}\r\n\r\n.reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(245, 108, 108, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-detail-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.order-detail-content {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border: none;\r\n}\r\n\r\n.detail-header {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.clickable-tag {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-tag:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-highlight {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.money-amount {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.debt-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .order-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-input-number {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .amount-range {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .client-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .selected-info {\r\n    margin-top: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n  \r\n  .remaining-days {\r\n    font-size: 11px;\r\n    padding: 2px 6px;\r\n  }\r\n  \r\n  .payment-progress {\r\n    margin-top: 6px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .action-buttons .el-button {\r\n    font-size: 12px;\r\n    padding: 4px 8px;\r\n  }\r\n  \r\n  .table-actions .el-button {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 收入统计图表样式 */\r\n.revenue-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.revenue-stats {\r\n  min-height: 400px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #f0f0f0;\r\n  height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-card h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 12px;\r\n  color: #667eea;\r\n}\r\n\r\n.chart-placeholder p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.mock-chart-data {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 8px;\r\n  height: 100px;\r\n  width: 200px;\r\n}\r\n\r\n.chart-bar {\r\n  flex: 1;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 4px 4px 0 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chart-bar:hover {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.payment-stats {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.payment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.payment-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.full-payment {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.installment-payment {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.status-overview {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.status-circle {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.status-circle:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.pending-circle {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-circle {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.rejected-circle {\r\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\r\n}\r\n\r\n.total-circle {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.status-item span {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据导出对话框样式 */\r\n.export-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.export-dialog .el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.export-dialog .el-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox-group {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox {\r\n  margin: 0;\r\n}\r\n\r\n.export-dialog .el-form-item__label {\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-dialog .el-radio {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.export-dialog .dialog-footer {\r\n  padding: 16px 0 0 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button {\r\n  margin-left: 12px;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 响应式图表样式 */\r\n@media (max-width: 768px) {\r\n  .revenue-stats .el-col {\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .chart-card {\r\n    height: auto;\r\n    min-height: 250px;\r\n  }\r\n  \r\n  .status-overview {\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .status-circle {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .export-dialog .el-checkbox-group {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 通知系统样式 */\r\n.notifications-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 12px;\r\n  border: 1px solid #ffd93d;\r\n  background: linear-gradient(135deg, #fff9c4 0%, #ffffff 100%);\r\n  box-shadow: 0 4px 20px rgba(255, 217, 61, 0.3);\r\n}\r\n\r\n.notification-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px 20px 12px 20px;\r\n  border-bottom: 1px solid rgba(255, 217, 61, 0.2);\r\n}\r\n\r\n.notification-icon {\r\n  font-size: 20px;\r\n  color: #f39c12;\r\n}\r\n\r\n.notification-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.dismiss-btn {\r\n  color: #7f8c8d !important;\r\n  padding: 4px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.dismiss-btn:hover {\r\n  color: #e74c3c !important;\r\n  background: rgba(231, 76, 60, 0.1) !important;\r\n}\r\n\r\n.notification-content {\r\n  padding: 16px 20px 20px 20px;\r\n}\r\n\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.notification-item:hover {\r\n  transform: translateX(4px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.notification-item.urgent {\r\n  background: linear-gradient(135deg, #fee2e2 0%, #fef7f7 100%);\r\n  border-color: rgba(239, 68, 68, 0.2);\r\n}\r\n\r\n.notification-item.urgent:hover {\r\n  background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%);\r\n}\r\n\r\n.notification-item.warning {\r\n  background: linear-gradient(135deg, #fef3c7 0%, #fefdf8 100%);\r\n  border-color: rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.notification-item.warning:hover {\r\n  background: linear-gradient(135deg, #fde68a 0%, #fef3c7 100%);\r\n}\r\n\r\n.notification-item.error {\r\n  background: linear-gradient(135deg, #ffebee 0%, #fafafa 100%);\r\n  border-color: rgba(244, 67, 54, 0.2);\r\n}\r\n\r\n.notification-item.error:hover {\r\n  background: linear-gradient(135deg, #ffcdd2 0%, #ffebee 100%);\r\n}\r\n\r\n.notification-item.info {\r\n  background: linear-gradient(135deg, #e3f2fd 0%, #fafafa 100%);\r\n  border-color: rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.notification-item.info:hover {\r\n  background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 100%);\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.notification-item.urgent .notification-dot {\r\n  background: #ef4444;\r\n}\r\n\r\n.notification-item.warning .notification-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.notification-item.error .notification-dot {\r\n  background: #f44336;\r\n}\r\n\r\n.notification-item.info .notification-dot {\r\n  background: #2196f3;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    opacity: 0.7;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.notification-text {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  line-height: 1.5;\r\n}\r\n\r\n.notification-text strong {\r\n  color: #e74c3c;\r\n  font-weight: 600;\r\n}\r\n\r\n.notification-action {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.notification-action .el-button {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.notification-action .el-button:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.export-dialog .el-checkbox-group {\r\n  grid-template-columns: 1fr;\r\n}\r\n\r\n.notifications-section {\r\n  margin: 0 -8px 16px -8px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 8px;\r\n  margin: 0 8px;\r\n}\r\n\r\n.notification-header {\r\n  padding: 12px 16px 8px 16px;\r\n}\r\n\r\n.notification-content {\r\n  padding: 12px 16px 16px 16px;\r\n}\r\n\r\n.notification-item {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.notification-item .notification-dot {\r\n  order: -1;\r\n  align-self: flex-start;\r\n}\r\n\r\n.notification-text {\r\n  order: 0;\r\n  width: 100%;\r\n}\r\n\r\n.notification-action {\r\n  order: 1;\r\n  align-self: flex-end;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./dingdan.vue?vue&type=template&id=ffe30e72&scoped=true\"\nimport script from \"./dingdan.vue?vue&type=script&lang=js\"\nexport * from \"./dingdan.vue?vue&type=script&lang=js\"\nimport style0 from \"./dingdan.vue?vue&type=style&index=0&id=ffe30e72&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ffe30e72\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\r\nvar fails = require('../internals/fails');\r\n\r\nmodule.exports = function (METHOD_NAME, argument) {\r\n  var method = [][METHOD_NAME];\r\n  return !!method && fails(function () {\r\n    // eslint-disable-next-line no-useless-call -- required for testing\r\n    method.call(null, argument || function () { return 1; }, 1);\r\n  });\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-detail-container\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"客户基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"公司名称\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.company || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"手机号\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.phone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"客户姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkman || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkphone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"用户来源\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.yuangong_id || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"开始时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.start_time || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"会员年限\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.year ? _vm.info.year + '年' : '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"头像\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.headimg && _vm.info.headimg !== '')?_c('el-avatar',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.headimg,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.license && _vm.info.license !== '')?_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.license,\"fit\":\"cover\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"服务团队\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"调解员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.tiaojie_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"法务专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.fawu_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"立案专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.lian_name || '未分配'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"合同专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.htsczy_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"律师\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ls_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"业务员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ywy_name || '未分配'))])])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人信息\")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debts,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.name))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"phone-number\"},[_vm._v(_vm._s(scope.row.tel))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDebtDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 详情 \")])]}}])})],1),(!_vm.info.debts || _vm.info.debts.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无债务人信息\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=4468717a&scoped=true\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4468717a\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\r\nvar aCallable = require('../internals/a-callable');\r\nvar toObject = require('../internals/to-object');\r\nvar IndexedObject = require('../internals/indexed-object');\r\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\r\n\r\nvar $TypeError = TypeError;\r\n\r\nvar REDUCE_EMPTY = 'Reduce of empty array with no initial value';\r\n\r\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\r\nvar createMethod = function (IS_RIGHT) {\r\n  return function (that, callbackfn, argumentsLength, memo) {\r\n    var O = toObject(that);\r\n    var self = IndexedObject(O);\r\n    var length = lengthOfArrayLike(O);\r\n    aCallable(callbackfn);\r\n    if (length === 0 && argumentsLength < 2) throw new $TypeError(REDUCE_EMPTY);\r\n    var index = IS_RIGHT ? length - 1 : 0;\r\n    var i = IS_RIGHT ? -1 : 1;\r\n    if (argumentsLength < 2) while (true) {\r\n      if (index in self) {\r\n        memo = self[index];\r\n        index += i;\r\n        break;\r\n      }\r\n      index += i;\r\n      if (IS_RIGHT ? index < 0 : length <= index) {\r\n        throw new $TypeError(REDUCE_EMPTY);\r\n      }\r\n    }\r\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\r\n      memo = callbackfn(memo, self[index], index, O);\r\n    }\r\n    return memo;\r\n  };\r\n};\r\n\r\nmodule.exports = {\r\n  // `Array.prototype.reduce` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n  left: createMethod(false),\r\n  // `Array.prototype.reduceRight` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\r\n  right: createMethod(true)\r\n};\r\n", "'use strict';\r\nvar $TypeError = TypeError;\r\n\r\nmodule.exports = function (passed, required) {\r\n  if (passed < required) throw new $TypeError('Not enough arguments');\r\n  return passed;\r\n};\r\n", "'use strict';\r\nvar makeBuiltIn = require('../internals/make-built-in');\r\nvar defineProperty = require('../internals/object-define-property');\r\n\r\nmodule.exports = function (target, name, descriptor) {\r\n  if (descriptor.get) makeBuiltIn(descriptor.get, name, { getter: true });\r\n  if (descriptor.set) makeBuiltIn(descriptor.set, name, { setter: true });\r\n  return defineProperty.f(target, name, descriptor);\r\n};\r\n"], "sourceRoot": ""}