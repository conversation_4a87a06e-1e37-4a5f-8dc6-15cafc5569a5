# Changelog

## 7.12.0 (2023-12-11)
* 对象存储，支持归档直读存储
* 对象存储，批量操作支持自动查询 rs 服务域名

## 7.11.0 (2023-09-05)
* 支持代理

## 7.10.1 (2023-08-04)
* 修复部分 API 调用中间件合并失败（#417）

## 7.10.0 (2023-06-20)
* 对象存储，新增请求中间件逻辑，方便拓展请求逻辑
* 对象存储，新增备用 UC 域名用于查询区域域名
* 对象存储，修复分片上传初始化失败无法快速失败
* 对象存储，移除首尔区域

## 7.9.0 (2023-03-31)
* 对象存储，修复无法对 key 为空字符串的对象进行操作
* 修复 301 重定向无法正确获取 header 信息
* 对象存储，新增查询区域域名过期时间
* 对象存储，更新获取区域域名的接口
* 对象存储，更新查询 bucket 域名为 uc 服务
* 对象存储，新增 uc 服务可配置

## 7.8.0 (2022-10-25)
* 移除不推荐域名，并增加区域亚太-首尔和华东-浙江2
* 对象存储，修复断点上传的文件内容不正确
* 对象存储，优化分片上传 ctx 超时检测

## 7.7.0 (2022-09-02)
* 对象存储，新增支持设置文件级别生命周期 setObjectLifecycle API
* 对象存储，内置增加七牛新建存储区域域名信息
* 修复当前已知问题

## 7.6.0 (2022-06-08)
* 对象存储，管理类 API 发送请求时增加 [X-Qiniu-Date](https://developer.qiniu.com/kodo/3924/common-request-headers) （生成请求的时间） header


## 7.5.0 (2022-04-18)
* 对象存储，新增支持 [深度归档存储类型](https://developer.qiniu.com/kodo/3956/kodo-category#deep_archive)

## 7.4.3 (2022-04-01)
* 优化签名算法逻辑

## 7.4.2（2022-03-01）
* 修复已知关于请求 Header 处理不当问题，比如没有处理为大小写不敏感等问题

## 7.4.1（2021-09-24）
* 修复了 分片上传 v2 已知问题，明确给出了参数不合理情况下对应的错误提示信息

## 7.4.0 (2021-07-19)
* 【对象存储】支持 [分片上传 v2](https://developer.qiniu.com/kodo/7458/multipartupload) 和 断点续传，使用方式见 [开发者文档](https://developer.qiniu.com/kodo/1241/php#resume-upload-file)

## 7.3.0 (2020-09-24)
### 新增
* 【对象存储】增加异步抓取方法与demo
* 【融合cdn】增加查询CDN刷新记录、查询CDN预取记录方法与demo
* 【云短信】增加查询短信发送记录的方法
* 【实时音视频】增加rtc停止房间的合流转推方法
* 【内容审核】增加图片审核、视频审核方法与demo

### 修复
* 【对象存储】修复签算 token 时上传策略中的 forceSaveKey 字段不生效的问题
* 【对象存储】修复更新空间事件通知规则方法

### 优化
* 【对象存储】创建空间迁移到mkbucketv3 api
* 优化对 http2 返回头的判断
* 优化 demo 中的文档注释说明
* docs 目录下的 rtc demo 移动至 examples/rtc 目录下
* docs 目录下的 sms demo 移动至 examples/sms 目录下

## 7.2.10 (2019-10-28)
* 去除云短信类类型指定
* 修改不传文件名时存在表单上传错误的情况

## 7.2.9 (2019-07-09)
* 添加空间管理、云短信接口
* 去除无效参数

## 7.2.7 (2018-11-06)
* 添加 QVM 内网上传到 KODO 的 zone 设置

## 7.2.6 (2018-05-18)
* 修复rs，rsf在不同机房默认的https域名

## 7.2.5 (2018-05-10)
* 修复表单上传中多余的参数checkCrc导致的fname错位问题

## 7.2.4 (2018-05-09)
### 增加
* 连麦功能

## 7.2.3 (2018-01-20)
### 增加
* 新加坡机房
### 修正
* 获取域名的入口域名
* http回复头部兼容大小写

## 7.2.2 (2017-11-06)
### 增加
* Qiniu算法的鉴权方法

## 7.1.4 (2017-06-21)
### 增加
* cdn 文件/目录 刷新
* cdn 获取 流量/带宽
* cdn 获取域名的访问日志列表
* cdn 对资源链接进行时间戳防盗链签名

## 7.1.3 (2016-11-18)
### 增加
* move, copy操作增加force参数

## 7.1.2 (2016-11-12)
### 修正
* 明确抛出获取各区域域名失败时的报错

## 7.1.1 (2016-11-02)
### 修正
* 多区域配置文件存储目录从home修改到tmp目录


## 7.1.0 (2016-10-22)
### 增加
* 多存储区域的支持

## 7.0.8 (2016-07-19)
### 增加
* demo
* https url 支持
* deleteAfterDays 策略
* 添加图片处理链接统一拼接方法 by @SherlockRen

## 7.0.7 (2016-01-12)
### 修正
* PersistentFop参数pipeline和notify_url失效
* resume 模式 close file inputstream

## 7.0.6 (2015-12-05)
### 修正
* php7.0 Json 对空字符串解析单元测试报错
* 开启安全模式或者设置可操作目录树时，设置CURLOPT_FOLLOWLOCATION报错, by @twocabbages
* fetch 支持不指定key, by @sinkcup

## 7.0.5 (2015-10-29)
### 增加
* 增加上传策略最小文件大小限制 fsizeMin
* 增加常见examples

## 7.0.4 (2015-07-23)
### 修正
* 一些地方的严格比较检查
* resumeupload 备用地址失效

## 7.0.3 (2015-07-10)
### 修改
* 多zone 支持

## 7.0.2 (2015-04-18)
### 修改
* fetch 接口返回内容调整
* pfop 接口调整

###修正
* exception 类调用

## 7.0.1 (2015-03-27)
### 增加
* 增加代码注释

## 7.0.0 (2015-02-03)

### 增加
* 简化上传接口
* 自动选择断点续上传还是直传
* 重构代码，接口和内部结构更清晰
* 改变mime
* 代码覆盖度报告
* policy改为array, 便于灵活增加，并加入过期字段检查
* 文件列表支持目录形式
* 利用元编程方式支持 fop 和 pfop
