{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\components\\DebtDetail.vue?vue&type=style&index=0&id=d8466e1a&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\components\\DebtDetail.vue", "mtime": 1748616174300}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DebtDetail.vue"], "names": [], "mappings": ";AAujBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "DebtDetail.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div class=\"debt-detail-container\">\r\n    <!-- 操作按钮区域 -->\r\n    <div class=\"action-bar\">\r\n      <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-download\" @click=\"exports\">\r\n        导出跟进记录\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 债务基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务基本信息</span>\r\n        <div class=\"debt-status\">\r\n          <el-tag :type=\"getDebtStatusType()\" size=\"medium\">\r\n            {{ getDebtStatusText() }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">委托人</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人姓名</div>\r\n            <div class=\"info-value\">{{ info.name || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人电话</div>\r\n            <div class=\"info-value phone-number\">{{ info.tel || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">债务人地址</div>\r\n            <div class=\"info-value\">{{ info.address || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card debt-amount\">\r\n            <div class=\"amount-label\">债务总金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card back-amount\">\r\n            <div class=\"amount-label\">已回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.back_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"amount-card remaining-amount\">\r\n            <div class=\"amount-label\">未回款金额</div>\r\n            <div class=\"amount-value\">¥{{ formatMoney(info.un_money) }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">提交时间</div>\r\n            <div class=\"info-value\">{{ info.ctime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">最后修改时间</div>\r\n            <div class=\"info-value\">{{ info.utime || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n    <!-- 债务人身份信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.cards && info.cards.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-postcard\"></i>\r\n        <span class=\"card-title\">债务人身份信息</span>\r\n      </div>\r\n\r\n      <div class=\"id-cards-grid\">\r\n        <div\r\n          v-for=\"(card, index) in info.cards\"\r\n          :key=\"index\"\r\n          class=\"id-card-item\"\r\n          @click=\"showImage(card)\">\r\n          <el-image\r\n            :src=\"card\"\r\n            fit=\"cover\"\r\n            class=\"id-card-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"id-card-label\">\r\n            {{ index === 0 ? '身份证正面' : '身份证反面' }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 案由信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <span class=\"card-title\">案由描述</span>\r\n      </div>\r\n\r\n      <div class=\"case-description\">\r\n        <p>{{ info.case_des || '暂无案由描述' }}</p>\r\n      </div>\r\n    </el-card>\r\n    <!-- 证据图片卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.images && info.images.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-picture\"></i>\r\n        <span class=\"card-title\">证据图片</span>\r\n        <el-button\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"downloadFiles(info.images_download)\"\r\n          class=\"header-action\">\r\n          全部下载\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"evidence-images-grid\">\r\n        <div\r\n          v-for=\"(image, index) in info.images\"\r\n          :key=\"index\"\r\n          class=\"evidence-image-item\">\r\n          <el-image\r\n            :src=\"image\"\r\n            :preview-src-list=\"info.images\"\r\n            fit=\"cover\"\r\n            class=\"evidence-image\">\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n          <div class=\"evidence-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(image, `evidence_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 证据文件卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\" v-if=\"info.attach_path && info.attach_path.length > 0\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-folder\"></i>\r\n        <span class=\"card-title\">证据文件</span>\r\n      </div>\r\n\r\n      <div class=\"evidence-files-list\">\r\n        <div\r\n          v-for=\"(file, index) in info.attach_path\"\r\n          :key=\"index\"\r\n          class=\"file-item\"\r\n          v-if=\"file\">\r\n          <div class=\"file-info\">\r\n            <i :class=\"getFileIcon(file)\" class=\"file-icon\"></i>\r\n            <div class=\"file-details\">\r\n              <div class=\"file-name\">文件{{ index + 1 }}</div>\r\n              <div class=\"file-type\">{{ getFileExtension(file) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"file-actions\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"viewFile(file)\">\r\n              查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadSingleFile(file, `file_${index + 1}`)\">\r\n              下载\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n    <!-- 跟进记录卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-time\"></i>\r\n        <span class=\"card-title\">跟进记录</span>\r\n        <div class=\"record-count\">\r\n          共 {{ info.debttrans ? info.debttrans.length : 0 }} 条记录\r\n        </div>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debttrans\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n        class=\"follow-up-table\">\r\n        <el-table-column prop=\"day\" label=\"跟进日期\" width=\"110\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-date\"></i>\r\n            {{ scope.row.day }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <i class=\"el-icon-time\"></i>\r\n            {{ scope.row.ctime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"au_id\" label=\"操作人员\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"info\" size=\"small\">{{ scope.row.au_id }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"进度类型\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getProgressType(scope.row.type)\" size=\"small\">\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"total_price\" label=\"费用金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text\" v-if=\"scope.row.total_price && scope.row.total_price !== '0'\">\r\n              ¥{{ scope.row.total_price }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"content\" label=\"费用内容\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.content || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"回款金额\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-text success\" v-if=\"scope.row.back_money && scope.row.back_money !== '0'\">\r\n              ¥{{ scope.row.back_money }}\r\n            </span>\r\n            <span v-else class=\"no-data\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_type\" label=\"支付状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.pay_type === '已支付' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.pay_type || '未支付' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_time\" label=\"支付时间\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_time || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"pay_order_type\" label=\"支付方式\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.pay_order_type || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"进度描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"desc-content\">{{ scope.row.desc }}</div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n              class=\"danger-btn\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debttrans || info.debttrans.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无跟进记录</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%;\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'DebtDetail',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {\r\n            nickname: '',\r\n            name: '',\r\n            tel: '',\r\n            address: '',\r\n            money: '',\r\n            back_money: '',\r\n            un_money: '',\r\n            ctime: '',\r\n            utime: '',\r\n            case_des: '',\r\n            cards: [],\r\n            images: [],\r\n            images_download: [],\r\n            attach_path: [],\r\n            debttrans: []\r\n          }, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取债务详情，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testDebtData = {\r\n            id: id,\r\n            nickname: \"张三\",\r\n            name: \"债务人李四\",\r\n            tel: \"13900139001\",\r\n            address: \"北京市朝阳区测试街道123号\",\r\n            money: \"50000\",\r\n            back_money: \"10000\",\r\n            un_money: \"40000\",\r\n            ctime: \"2024-01-01 10:00:00\",\r\n            utime: \"2024-01-15 15:30:00\",\r\n            case_des: \"借款纠纷，借款人未按约定时间还款，现申请追讨欠款及利息。\",\r\n            cards: [\r\n              \"/static/images/id_card_front.jpg\",\r\n              \"/static/images/id_card_back.jpg\"\r\n            ],\r\n            images: [\r\n              \"/static/images/evidence1.jpg\",\r\n              \"/static/images/evidence2.jpg\",\r\n              \"/static/images/evidence3.jpg\"\r\n            ],\r\n            images_download: [\r\n              { name: \"证据1.jpg\", path: \"/static/images/evidence1.jpg\" },\r\n              { name: \"证据2.jpg\", path: \"/static/images/evidence2.jpg\" }\r\n            ],\r\n            attach_path: [\r\n              \"/static/files/contract.pdf\",\r\n              \"/static/files/bank_record.xlsx\"\r\n            ],\r\n            debttrans: [\r\n              {\r\n                id: 1,\r\n                day: \"2024-01-15\",\r\n                ctime: \"2024-01-15 10:30:00\",\r\n                au_id: \"调解员王五\",\r\n                type: \"电话联系\",\r\n                total_price: \"0\",\r\n                content: \"联系费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"未支付\",\r\n                pay_time: \"\",\r\n                pay_order_type: \"\",\r\n                desc: \"已与债务人取得联系，对方表示将在本月底前还款\"\r\n              },\r\n              {\r\n                id: 2,\r\n                day: \"2024-01-10\",\r\n                ctime: \"2024-01-10 14:20:00\",\r\n                au_id: \"法务赵六\",\r\n                type: \"发送催款函\",\r\n                total_price: \"200\",\r\n                content: \"律师函费用\",\r\n                rate: \"0\",\r\n                back_money: \"0\",\r\n                pay_type: \"已支付\",\r\n                pay_time: \"2024-01-10 14:25:00\",\r\n                pay_order_type: \"微信支付\",\r\n                desc: \"向债务人发送正式催款函，要求在15日内还款\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testDebtData;\r\n          _this.loading = false;\r\n          console.log('债务详情数据加载完成:', testDebtData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/debt/view?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取债务详情失败:', resp);\r\n            _this.$message({\r\n              type: \"error\",\r\n              message: resp.msg || \"获取数据失败\",\r\n            });\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n        downloadFiles(imgs) {\r\n            imgs.forEach((file) => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = file.path;\r\n                link.download = file.name;\r\n                link.click();\r\n            });\r\n        },\r\n        exports:function () { //导出表格\r\n            let _this = this;\r\n            location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n        },\r\n\r\n        // 显示图片\r\n        showImage(imageUrl) {\r\n          this.show_image = imageUrl;\r\n          this.dialogVisible = true;\r\n        },\r\n\r\n        // 下载单个文件\r\n        downloadSingleFile(url, filename) {\r\n          const link = document.createElement(\"a\");\r\n          link.href = url;\r\n          link.download = filename;\r\n          link.click();\r\n        },\r\n\r\n        // 查看文件\r\n        viewFile(url) {\r\n          window.open(url, '_blank');\r\n        },\r\n\r\n        // 获取文件图标\r\n        getFileIcon(filename) {\r\n          const ext = this.getFileExtension(filename).toLowerCase();\r\n          const iconMap = {\r\n            'pdf': 'el-icon-document',\r\n            'doc': 'el-icon-document',\r\n            'docx': 'el-icon-document',\r\n            'xls': 'el-icon-s-grid',\r\n            'xlsx': 'el-icon-s-grid',\r\n            'txt': 'el-icon-document',\r\n            'zip': 'el-icon-folder',\r\n            'rar': 'el-icon-folder'\r\n          };\r\n          return iconMap[ext] || 'el-icon-document';\r\n        },\r\n\r\n        // 获取文件扩展名\r\n        getFileExtension(filename) {\r\n          return filename.split('.').pop() || '';\r\n        },\r\n\r\n        // 格式化金额\r\n        formatMoney(amount) {\r\n          if (!amount || amount === '0') return '0.00';\r\n          return parseFloat(amount).toLocaleString('zh-CN', {\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2\r\n          });\r\n        },\r\n\r\n        // 获取债务状态类型\r\n        getDebtStatusType() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return 'success';\r\n          if (unMoney > 0) return 'warning';\r\n          return 'info';\r\n        },\r\n\r\n        // 获取债务状态文本\r\n        getDebtStatusText() {\r\n          const unMoney = parseFloat(this.info.un_money || 0);\r\n          if (unMoney === 0) return '已结清';\r\n          if (unMoney > 0) return '未结清';\r\n          return '处理中';\r\n        },\r\n\r\n        // 获取进度类型\r\n        getProgressType(type) {\r\n          const typeMap = {\r\n            '电话联系': 'primary',\r\n            '发送催款函': 'warning',\r\n            '法院起诉': 'danger',\r\n            '调解成功': 'success',\r\n            '回款确认': 'success'\r\n          };\r\n          return typeMap[type] || 'info';\r\n        },\r\n\r\n        // 删除数据\r\n        delData(index, id) {\r\n          this.$confirm('确定要移除这条跟进记录吗？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 这里应该调用删除API\r\n            this.info.debttrans.splice(index, 1);\r\n            this.$message.success('删除成功');\r\n          }).catch(() => {\r\n            this.$message.info('已取消删除');\r\n          });\r\n        }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.debt-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.action-bar {\r\n  margin-bottom: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n.debt-status {\r\n  margin-left: auto;\r\n}\r\n\r\n.header-action {\r\n  margin-left: auto;\r\n}\r\n\r\n.record-count {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.amount-card {\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.amount-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.debt-amount {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.back-amount {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.remaining-amount {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.amount-label {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.amount-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n}\r\n\r\n.id-cards-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.id-card-item {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.id-card-item:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.id-card-image {\r\n  width: 200px;\r\n  height: 120px;\r\n  border-radius: 8px;\r\n  border: 2px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.id-card-item:hover .id-card-image {\r\n  border-color: #409eff;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n.id-card-label {\r\n  margin-top: 10px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.case-description {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.case-description p {\r\n  margin: 0;\r\n  line-height: 1.8;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.evidence-images-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-image-item {\r\n  text-align: center;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.evidence-image-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 120px;\r\n  border-radius: 6px;\r\n}\r\n\r\n.evidence-actions {\r\n  margin-top: 10px;\r\n}\r\n\r\n.evidence-files-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n  margin-bottom: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  color: #409eff;\r\n  margin-right: 15px;\r\n}\r\n\r\n.file-details {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.file-type {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.follow-up-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.money-text {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n}\r\n\r\n.money-text.success {\r\n  color: #67c23a;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.desc-content {\r\n  max-width: 200px;\r\n  word-break: break-word;\r\n  line-height: 1.4;\r\n}\r\n\r\n.danger-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.danger-btn:hover {\r\n  color: #f78989;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .debt-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .amount-card {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .amount-value {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .id-cards-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .evidence-images-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"]}]}