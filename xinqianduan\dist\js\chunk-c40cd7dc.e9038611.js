(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c40cd7dc"],{"9a02":function(e,t,o){},a8bb:function(e,t,o){"use strict";o("9a02")},f374:function(e,t,o){"use strict";o.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"todo-container"},[t("div",{staticClass:"page-header"},[t("h2",[e._v("待办事项管理")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showAddDialog=!0}}},[t("i",{staticClass:"el-icon-plus"}),e._v(" 新增待办事项 ")])],1),t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-form",{staticClass:"filter-form",attrs:{inline:!0,model:e.filterForm}},[t("el-form-item",{attrs:{label:"状态"}},[t("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.filterForm.status,callback:function(t){e.$set(e.filterForm,"status",t)},expression:"filterForm.status"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"未完成",value:"0"}}),t("el-option",{attrs:{label:"已完成",value:"1"}})],1)],1),t("el-form-item",{attrs:{label:"优先级"}},[t("el-select",{attrs:{placeholder:"请选择优先级",clearable:""},model:{value:e.filterForm.priority,callback:function(t){e.$set(e.filterForm,"priority",t)},expression:"filterForm.priority"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"高",value:"high"}}),t("el-option",{attrs:{label:"中",value:"medium"}}),t("el-option",{attrs:{label:"低",value:"low"}})],1)],1),t("el-form-item",{attrs:{label:"类型"}},[t("el-select",{attrs:{placeholder:"请选择类型",clearable:""},model:{value:e.filterForm.type,callback:function(t){e.$set(e.filterForm,"type",t)},expression:"filterForm.type"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"债务处理",value:"debt"}}),t("el-option",{attrs:{label:"订单处理",value:"order"}}),t("el-option",{attrs:{label:"用户管理",value:"user"}}),t("el-option",{attrs:{label:"系统任务",value:"system"}}),t("el-option",{attrs:{label:"一般任务",value:"general"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTodos}},[e._v("查询")]),t("el-button",{on:{click:e.resetFilter}},[e._v("重置")])],1)],1)],1),t("el-card",{staticClass:"list-card"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.todoList,stripe:""}},[t("el-table-column",{attrs:{prop:"title",label:"标题","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("div",{staticClass:"todo-title"},[t("el-checkbox",{attrs:{disabled:2===o.row.status},on:{change:function(t){return e.handleStatusChange(o.row)}},model:{value:o.row.completed,callback:function(t){e.$set(o.row,"completed",t)},expression:"scope.row.completed"}}),t("span",{class:{completed:o.row.completed}},[e._v(e._s(o.row.title))])],1)]}}])}),t("el-table-column",{attrs:{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"type_text",label:"类型",width:"100"}}),t("el-table-column",{attrs:{prop:"priority_text",label:"优先级",width:"80"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-tag",{attrs:{type:e.getPriorityType(o.row.priority),size:"small"}},[e._v(" "+e._s(o.row.priority_text)+" ")])]}}])}),t("el-table-column",{attrs:{prop:"due_date",label:"截止时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(o){return[o.row.due_date?t("span",{class:{overdue:e.isOverdue(o.row.due_date)}},[e._v(" "+e._s(o.row.due_date)+" ")]):t("span",{staticClass:"no-due-date"},[e._v("无")])]}}])}),t("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-tag",{attrs:{type:o.row.completed?"success":"info",size:"small"}},[e._v(" "+e._s(o.row.completed?"已完成":"未完成")+" ")])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editTodo(o.row)}}},[e._v("编辑")]),t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteTodo(o.row)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.pagination.page,"page-sizes":[10,20,50,100],"page-size":e.pagination.size,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.editingTodo.id?"编辑待办事项":"新增待办事项",visible:e.showAddDialog,width:"600px"},on:{"update:visible":function(t){e.showAddDialog=t}}},[t("el-form",{ref:"todoForm",attrs:{model:e.editingTodo,rules:e.todoRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"标题",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入待办事项标题"},model:{value:e.editingTodo.title,callback:function(t){e.$set(e.editingTodo,"title",t)},expression:"editingTodo.title"}})],1),t("el-form-item",{attrs:{label:"描述",prop:"description"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入详细描述",rows:3},model:{value:e.editingTodo.description,callback:function(t){e.$set(e.editingTodo,"description",t)},expression:"editingTodo.description"}})],1),t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{attrs:{placeholder:"请选择类型"},model:{value:e.editingTodo.type,callback:function(t){e.$set(e.editingTodo,"type",t)},expression:"editingTodo.type"}},[t("el-option",{attrs:{label:"债务处理",value:"debt"}}),t("el-option",{attrs:{label:"订单处理",value:"order"}}),t("el-option",{attrs:{label:"用户管理",value:"user"}}),t("el-option",{attrs:{label:"系统任务",value:"system"}}),t("el-option",{attrs:{label:"一般任务",value:"general"}})],1)],1),t("el-form-item",{attrs:{label:"优先级",prop:"priority"}},[t("el-select",{attrs:{placeholder:"请选择优先级"},model:{value:e.editingTodo.priority,callback:function(t){e.$set(e.editingTodo,"priority",t)},expression:"editingTodo.priority"}},[t("el-option",{attrs:{label:"高",value:"high"}}),t("el-option",{attrs:{label:"中",value:"medium"}}),t("el-option",{attrs:{label:"低",value:"low"}})],1)],1),t("el-form-item",{attrs:{label:"截止时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择截止时间",format:"yyyy-MM-dd HH:mm","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.editingTodo.due_date,callback:function(t){e.$set(e.editingTodo,"due_date",t)},expression:"editingTodo.due_date"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.showAddDialog=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveTodo}},[e._v("确定")])],1)],1)],1)},a=[],i=o("7c15"),s={name:"TodoList",mixins:[{methods:{getRequest:i["b"],postRequest:i["d"],putRequest:i["e"],deleteRequest:i["a"]}}],data(){return{loading:!1,showAddDialog:!1,todoList:[],filterForm:{status:"",priority:"",type:""},pagination:{page:1,size:20,total:0},editingTodo:{id:null,title:"",description:"",type:"general",priority:"medium",due_date:null},todoRules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]}}},mounted(){this.loadTodos()},methods:{async loadTodos(){this.loading=!0;try{const e={page:this.pagination.page,size:this.pagination.size,...this.filterForm},t=await this.getRequest("/todo/list",e);200===t.code&&(this.todoList=t.data.list||[],this.pagination.total=t.data.total||0)}catch(e){console.error("加载待办事项失败:",e),this.$message.error("加载数据失败")}finally{this.loading=!1}},async handleStatusChange(e){try{const t=await this.postRequest("/dashboard/updateTodo",{id:e.id,completed:e.completed});200===t.code&&this.$message.success(e.completed?"任务已完成":"任务已重新激活")}catch(t){console.error("更新状态失败:",t),e.completed=!e.completed,this.$message.error("更新失败")}},editTodo(e){this.editingTodo={...e},this.showAddDialog=!0},async deleteTodo(e){try{await this.$confirm("确定要删除这个待办事项吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.deleteRequest("/todo/delete",{id:e.id});200===t.code&&(this.$message.success("删除成功"),this.loadTodos())}catch(t){"cancel"!==t&&(console.error("删除失败:",t),this.$message.error("删除失败"))}},async saveTodo(){try{await this.$refs.todoForm.validate();const e=!!this.editingTodo.id,t=e?"/todo/update":"/todo/create",o=await this.postRequest(t,this.editingTodo);200===o.code&&(this.$message.success(e?"更新成功":"创建成功"),this.showAddDialog=!1,this.resetForm(),this.loadTodos())}catch(e){console.error("保存失败:",e),this.$message.error("保存失败")}},resetForm(){this.editingTodo={id:null,title:"",description:"",type:"general",priority:"medium",due_date:null},this.$refs.todoForm&&this.$refs.todoForm.resetFields()},resetFilter(){this.filterForm={status:"",priority:"",type:""},this.pagination.page=1,this.loadTodos()},handleSizeChange(e){this.pagination.size=e,this.pagination.page=1,this.loadTodos()},handleCurrentChange(e){this.pagination.page=e,this.loadTodos()},getPriorityType(e){const t={high:"danger",medium:"warning",low:"info"};return t[e]||"info"},isOverdue(e){return!!e&&new Date(e)<new Date}}},r=s,n=(o("a8bb"),o("2877")),d=Object(n["a"])(r,l,a,!1,null,"19e8101f",null);t["default"]=d.exports}}]);
//# sourceMappingURL=chunk-c40cd7dc.e9038611.js.map