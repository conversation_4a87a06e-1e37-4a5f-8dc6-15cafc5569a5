(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-98b5b2ba"],{"13d5":function(t,e,s){"use strict";var i=s("23e7"),a=s("d58f").left,l=s("a640"),r=s("2d00"),n=s("605d"),o=!n&&r>79&&r<83,c=o||!l("reduce");i({target:"Array",proto:!0,forced:c},{reduce:function(t){var e=arguments.length;return a(this,t,e,e>1?arguments[1]:void 0)}})},"605d":function(t,e,s){"use strict";var i=s("da84"),a=s("c6b6");t.exports="process"===a(i.process)},"8f78":function(t,e,s){},a640:function(t,e,s){"use strict";var i=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&i((function(){s.call(null,e||function(){return 1},1)}))}},c3ba:function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"payment-management"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-content"},[t._m(0),e("div",{staticClass:"header-actions"},[e("el-button",{staticClass:"refresh-btn",attrs:{icon:"el-icon-refresh"},on:{click:t.refulsh}},[t._v(" 刷新数据 ")])],1)])]),e("el-card",{staticClass:"main-card",attrs:{shadow:"never"}},[e("div",{staticClass:"stats-cards"},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon payment-icon"},[e("i",{staticClass:"el-icon-coin"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.money)+"元")]),e("div",{staticClass:"stat-label"},[t._v("总支付金额")])])]),e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon order-icon"},[e("i",{staticClass:"el-icon-document"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.total))]),e("div",{staticClass:"stat-label"},[t._v("订单总数")])])]),e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon success-icon"},[e("i",{staticClass:"el-icon-success"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.paidCount))]),e("div",{staticClass:"stat-label"},[t._v("已支付订单")])])]),e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-icon pending-icon"},[e("i",{staticClass:"el-icon-time"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.pendingCount))]),e("div",{staticClass:"stat-label"},[t._v("待处理订单")])])])]),e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-form"},[e("div",{staticClass:"search-main"},[e("div",{staticClass:"search-left"},[e("div",{staticClass:"search-item"},[e("label",[t._v("关键词搜索")]),e("el-input",{staticStyle:{width:"280px"},attrs:{placeholder:"请输入订单号/套餐名称","prefix-icon":"el-icon-search",clearable:""},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}})],1),e("div",{staticClass:"search-item"},[e("label",[t._v("支付状态")]),e("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择支付状态",clearable:""},model:{value:t.search.is_pay,callback:function(e){t.$set(t.search,"is_pay",e)},expression:"search.is_pay"}},t._l(t.options,(function(t){return e("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})})),1)],1),e("div",{staticClass:"search-item"},[e("label",[t._v("处理状态")]),e("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择处理状态",clearable:""},model:{value:t.search.is_deal,callback:function(e){t.$set(t.search,"is_deal",e)},expression:"search.is_deal"}},t._l(t.options1,(function(t){return e("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})})),1)],1),e("div",{staticClass:"search-item"},[e("label",[t._v("支付时间")]),e("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},model:{value:t.search.pay_time,callback:function(e){t.$set(t.search,"pay_time",e)},expression:"search.pay_time"}})],1)]),e("div",{staticClass:"search-right"},[e("div",{staticClass:"search-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.getData()}}},[t._v(" 搜索 ")]),e("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:function(e){return t.clearData()}}},[t._v(" 重置 ")]),e("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:function(e){return t.exportData()}}},[t._v(" 导出 ")])],1)])])])]),e("div",{staticClass:"table-section"},[t.loading||0!==t.list.length?e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"payment-table",attrs:{data:t.list},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),e("el-table-column",{attrs:{label:"订单信息","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"order-info-cell"},[e("div",{staticClass:"order-number"},[e("i",{staticClass:"el-icon-document"}),e("span",[t._v(t._s(s.row.order_sn))])]),e("div",{staticClass:"package-name"},[t._v(t._s(s.row.title||"暂无套餐"))])])]}}])}),e("el-table-column",{attrs:{label:"支付金额",prop:"total_price",width:"120",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"amount-cell"},[e("span",{staticClass:"amount"},[t._v("¥"+t._s(s.row.total_price||"0.00"))])])]}}])}),e("el-table-column",{attrs:{label:"支付状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{staticClass:"status-tag",attrs:{type:t.getPayStatusType(s.row.is_pay),size:"small"}},[t._v(" "+t._s(s.row.is_pay)+" ")])]}}])}),e("el-table-column",{attrs:{label:"处理状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{staticClass:"status-tag",attrs:{type:t.getDealStatusType(s.row.is_deal),size:"small"}},[t._v(" "+t._s(s.row.is_deal)+" ")])]}}])}),e("el-table-column",{attrs:{label:"购买类型",prop:"body",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"type-cell"},[e("i",{staticClass:"el-icon-shopping-bag-1"}),e("span",[t._v(t._s(s.row.body||"暂无"))])])]}}])}),e("el-table-column",{attrs:{label:"用户信息",width:"160"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"user-info-cell",on:{click:function(e){return t.viewUserData(s.row.uid)}}},[e("div",{staticClass:"user-avatar"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"user-details"},[e("div",{staticClass:"user-name clickable"},[t._v(t._s(s.row.user_name||"未知用户"))]),e("div",{staticClass:"user-phone"},[t._v(t._s(s.row.phone||"暂无手机号"))])])])]}}])}),e("el-table-column",{attrs:{label:"支付时间",prop:"refund_time",width:"160",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-info"},[e("i",{staticClass:"el-icon-time"}),e("span",[t._v(t._s(t.formatDate(s.row.refund_time)))])])]}}])}),e("el-table-column",{attrs:{label:"创建时间",prop:"create_time",width:"160",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-info"},[e("i",{staticClass:"el-icon-date"}),e("span",[t._v(t._s(t.formatDate(s.row.create_time)))])])]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"action-buttons"},["未支付"==s.row.is_pay?e("el-button",{attrs:{type:"warning",size:"mini",icon:"el-icon-coin",title:"免支付"},on:{click:function(e){return t.free(s.row.id)}}},[t._v(" 免支付 ")]):t._e(),e("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-view",title:"查看用户详情"},on:{click:function(e){return t.viewUserData(s.row.uid)}}},[t._v(" 查看 ")]),e("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-check",title:"完成制作"},on:{click:function(e){return t.editData(s.row.id)}}},[t._v(" 制作 ")]),e("el-dropdown",{attrs:{trigger:"click"}},[e("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-more"}},[t._v(" 更多 ")]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{nativeOn:{click:function(e){return t.tuikuan(s.row.id)}}},[e("i",{staticClass:"el-icon-refresh-left"}),t._v(" 退款 ")]),e("el-dropdown-item",{nativeOn:{click:function(e){return t.delData(s.$index,s.row.id)}}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 取消订单 ")])],1)],1)],1)]}}])})],1):e("div",{staticClass:"empty-state"},[e("div",{staticClass:"empty-icon"},[e("i",{staticClass:"el-icon-document-remove"})]),e("div",{staticClass:"empty-text"},[e("h3",[t._v("暂无支付订单")]),e("p",[t._v("当前没有找到任何支付订单数据")])]),e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:function(e){return t.getData()}}},[t._v(" 刷新数据 ")])],1)],1),e("div",{staticClass:"pagination-wrapper"},[e("el-pagination",{staticClass:"pagination",attrs:{"page-sizes":[20,50,100,200],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{attrs:{title:t.title+"内容",visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules}},[e("el-form-item",{attrs:{label:"制作状态","label-width":t.formLabelWidth}},[e("div",[e("el-radio",{attrs:{label:2},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("已完成")]),e("el-radio",{attrs:{label:1},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("处理中")])],1)]),2==t.ruleForm.is_deal?e("el-form-item",{attrs:{label:"请上传文件","label-width":t.formLabelWidth,prop:"file_path"}},[e("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:t.ruleForm.file_path,callback:function(e){t.$set(t.ruleForm,"file_path",e)},expression:"ruleForm.file_path"}}),e("el-button-group",[e("el-button",{on:{click:function(e){return t.changeFile("file_path")}}},[e("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":t.handleSuccess}},[t._v(" 上传 ")])],1),t.ruleForm.file_path?e("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delImage(t.ruleForm.file_path,"file_path")}}},[t._v("删除")]):t._e()],1)],1):t._e()],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("确 定")])],1)],1),e("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("el-image",{attrs:{src:t.show_image}})],1),e("el-dialog",{attrs:{title:"订单查看",visible:t.viewFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.viewFormVisible=e}}},[e("el-descriptions",{attrs:{title:"订单信息"}},[e("el-descriptions-item",{attrs:{label:"订单号"}},[t._v(t._s(t.info.order_sn))]),e("el-descriptions-item",{attrs:{label:"购买类型"}},[t._v(t._s(t.info.body))]),e("el-descriptions-item",{attrs:{label:"支付金额"}},[t._v(t._s(t.info.total_price))]),e("el-descriptions-item",{attrs:{label:"支付状态"}},[t._v(t._s(t.info.is_pay_name))]),e("el-descriptions-item",{attrs:{label:"支付时间"}},[t._v(t._s(t.info.pay_time))]),e("el-descriptions-item",{attrs:{label:"支付方式"}},[t._v("微信支付")]),e("el-descriptions-item",{attrs:{label:"退款时间"}},[t._v(t._s(t.info.refund_time))]),e("el-descriptions-item",{attrs:{label:"免支付操作人"}},[t._v(t._s(t.info.free_operator))])],1),e("el-descriptions",{attrs:{title:"服务信息"}},[e("el-descriptions-item",{attrs:{label:"服务信息"}},[t._v(t._s(t.info.body))])],1),e("el-descriptions",{attrs:{title:"用户信息"}},[e("el-descriptions-item",{attrs:{label:"用户姓名"}},[e("div",{on:{click:function(e){return t.viewUserData(t.info.uid)}}},[t._v(t._s(t.info.linkman))])]),e("el-descriptions-item",{attrs:{label:"用户电话"}},[e("div",{on:{click:function(e){return t.viewUserData(t.info.uid)}}},[t._v(t._s(t.info.linkphone))])])],1),e("el-descriptions",{attrs:{title:"债务人信息"}},[e("el-descriptions-item",{attrs:{label:"债务人姓名"}},[e("div",{on:{click:function(e){return t.viewDebtData(t.info.dt_id)}}},[t._v(t._s(t.info.debts_name))])]),e("el-descriptions-item",{attrs:{label:"债务人电话"}},[e("div",{on:{click:function(e){return t.viewDebtData(t.info.dt_id)}}},[t._v(t._s(t.info.debts_tel))])])],1),e("el-descriptions",{attrs:{title:"制作信息"}},[e("el-descriptions-item",{attrs:{label:"制作状态"}},[t._v(t._s(t.info.is_deal_name))]),e("el-descriptions-item",{attrs:{label:"制作文件"}},[t._v("文件"),e("a",{attrs:{href:t.info.file_path,target:"_blank"}},[t._v("查看")]),e("a",{attrs:{href:t.info.file_path}},[t._v("下载")])])],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.viewFormVisible=!1}}},[t._v("取 消")])],1)],1),e("el-drawer",{staticClass:"user-detail-drawer",attrs:{visible:t.userDetailDrawerVisible,direction:"rtl",size:"650px","close-on-click-modal":!0,wrapperClosable:!0,"show-close":!1},on:{"update:visible":function(e){t.userDetailDrawerVisible=e},close:t.closeUserDetailDrawer}},[e("div",{staticClass:"drawer-header"},[e("div",{staticClass:"header-content"},[e("div",{staticClass:"user-avatar-large"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"user-info"},[e("h3",[t._v(t._s(t.getCurrentUserName()))]),e("p",[t._v(t._s(t.getCurrentUserPhone()))])])]),e("el-button",{staticClass:"close-btn",attrs:{type:"text",icon:"el-icon-close"},on:{click:t.closeUserDetailDrawer}})],1),e("div",{staticClass:"drawer-body"},[e("div",{staticClass:"info-section"},[e("h4",[e("i",{staticClass:"el-icon-user"}),t._v(" 基本信息")]),e("div",{staticClass:"info-grid"},[e("div",{staticClass:"info-item"},[e("label",[t._v("用户姓名")]),e("span",[t._v(t._s(t.getCurrentUserName()))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("手机号码")]),e("span",[t._v(t._s(t.getCurrentUserPhone()))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("用户ID")]),e("span",[t._v(t._s(t.currentId))])]),e("div",{staticClass:"info-item"},[e("label",[t._v("注册时间")]),e("span",[t._v("2024-01-10 10:30:00")])])])]),e("div",{staticClass:"info-section"},[e("h4",[e("i",{staticClass:"el-icon-shopping-cart-2"}),t._v(" 订单统计")]),e("div",{staticClass:"stats-grid"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.getUserOrderCount()))]),e("div",{staticClass:"stat-label"},[t._v("总订单数")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.getUserOrderAmount()))]),e("div",{staticClass:"stat-label"},[t._v("总消费金额")])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.getUserPaidCount()))]),e("div",{staticClass:"stat-label"},[t._v("已支付订单")])])])]),e("div",{staticClass:"info-section"},[e("h4",[e("i",{staticClass:"el-icon-user-solid"}),t._v(" 关联债务人")]),e("div",{staticClass:"debtors-list"},[t._l(t.getUserDebtors(),(function(s){return e("div",{key:s.dt_id,staticClass:"debtor-item",on:{click:function(e){return t.viewDebtData(s.dt_id)}}},[e("div",{staticClass:"debtor-info"},[e("div",{staticClass:"debtor-name"},[t._v(t._s(s.debts_name))]),e("div",{staticClass:"debtor-phone"},[t._v(t._s(s.debts_tel))])]),e("div",{staticClass:"debtor-orders"},[e("span",{staticClass:"order-count"},[t._v(t._s(t.getDebtorOrderCount(s.dt_id))+"个订单")]),e("i",{staticClass:"el-icon-arrow-right"})])])})),0===t.getUserDebtors().length?e("div",{staticClass:"no-data"},[t._v(" 暂无关联债务人 ")]):t._e()],2)]),e("div",{staticClass:"info-section"},[e("h4",[e("i",{staticClass:"el-icon-document"}),t._v(" 最近订单")]),e("div",{staticClass:"recent-orders"},[t._l(t.getUserRecentOrders(),(function(s){return e("div",{key:s.id,staticClass:"order-item"},[e("div",{staticClass:"order-info"},[e("div",{staticClass:"order-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"order-meta"},[e("span",{staticClass:"order-sn"},[t._v(t._s(s.order_sn))]),e("span",{staticClass:"order-time"},[t._v(t._s(t.formatDate(s.create_time)))])])]),e("div",{staticClass:"order-status"},[e("div",{staticClass:"order-amount"},[t._v("¥"+t._s(s.total_price))]),e("el-tag",{attrs:{type:t.getPayStatusType(s.is_pay),size:"mini"}},[t._v(" "+t._s(s.is_pay)+" ")])],1)])})),0===t.getUserRecentOrders().length?e("div",{staticClass:"no-data"},[t._v(" 暂无订单记录 ")]):t._e()],2)]),e("div",{staticClass:"action-section"},[e("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",size:"small",icon:"el-icon-document"},on:{click:t.viewAllOrdersForUser}},[t._v(" 查看该用户所有订单 ")])],1)])]),e("el-dialog",{attrs:{title:"债务查看",visible:t.dialogViewDebtDetail,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(e){t.dialogViewDebtDetail=e}}},[e("debt-detail",{attrs:{id:t.currentDebtId}}),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogViewDebtDetail=!1}}},[t._v("取 消")])],1)],1)],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-left"},[e("div",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-money"}),e("span",[t._v("支付列表管理")])]),e("div",{staticClass:"page-subtitle"},[t._v("管理和查看所有支付订单信息")])])}],l=(s("13d5"),s("d522")),r=s("26b2"),n={name:"list",components:{UserDetails:l["a"],DebtDetail:r["a"]},data(){return{allSize:"mini",list:[],total:1,money:0,currentId:0,currentDebtId:0,page:1,size:20,search:{keyword:"",is_pay:-1,is_deal:-1,pay_time:null},loading:!0,url:"/order/",title:"订单",info:{},dialogFormVisible:!1,userDetailDrawerVisible:!1,orderDetailDrawerVisible:!1,viewFormVisible:!1,dialogViewDebtDetail:!1,currentOrderId:0,show_image:"",dialogVisible:!1,selectedOrders:[],ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],file_path:[{required:!0,message:"请上传文件",trigger:"blur"}]},formLabelWidth:"120px",options:[{id:-1,title:"支付状态"},{id:1,title:"未支付"},{id:2,title:"已支付"},{id:3,title:"退款"}],options1:[{id:-1,title:"处理状态"},{id:1,title:"待处理"},{id:2,title:"已处理"}]}},computed:{paidCount(){return this.list.filter(t=>"已支付"===t.is_pay).length},pendingCount(){return this.list.filter(t=>"待处理"===t.is_deal).length}},mounted(){this.getData(),setTimeout(()=>{this.loading&&(this.loading=!1,this.$message.warning("数据加载超时，请刷新重试"))},1e4)},methods:{formatDate(t){if(!t)return"暂无";const e=new Date(t);return e.toLocaleDateString("zh-CN")+" "+e.toLocaleTimeString("zh-CN",{hour12:!1})},getPayStatusType(t){const e={"已支付":"success","未支付":"warning","退款":"info"};return e[t]||"info"},getDealStatusType(t){const e={"已处理":"success","待处理":"warning","处理中":"primary"};return e[t]||"info"},handleSelectionChange(t){this.selectedOrders=t},exportData(){this.selectedOrders.length>0?this.$message.success(`导出选中的 ${this.selectedOrders.length} 条订单数据`):this.$message.success("导出全部订单数据")},changeFile(t){this.filed=t,console.log(this.filed)},clearData(){this.search={keyword:"",is_pay:-1,is_deal:-1,pay_time:null},this.getData()},viewUserData(t){let e=this;0!=t&&(this.currentId=t),e.userDetailDrawerVisible=!0},closeUserDetailDrawer(){this.userDetailDrawerVisible=!1,this.currentId=0},getCurrentUserName(){const t=this.list.find(t=>t.uid===this.currentId);return t?t.user_name:"未知用户"},getCurrentUserPhone(){const t=this.list.find(t=>t.uid===this.currentId);return t?t.phone:"暂无手机号"},getUserOrderCount(){return this.list.filter(t=>t.uid===this.currentId).length},getUserOrderAmount(){const t=this.list.filter(t=>t.uid===this.currentId),e=t.reduce((t,e)=>t+parseFloat(e.total_price||0),0);return"¥"+e.toFixed(2)},getUserPaidCount(){return this.list.filter(t=>t.uid===this.currentId&&"已支付"===t.is_pay).length},getUserRecentOrders(){return this.list.filter(t=>t.uid===this.currentId).sort((t,e)=>new Date(e.create_time)-new Date(t.create_time)).slice(0,3)},getUserDebtors(){const t=this.list.filter(t=>t.uid===this.currentId),e=new Map;return t.forEach(t=>{t.dt_id&&t.debts_name&&e.set(t.dt_id,{dt_id:t.dt_id,debts_name:t.debts_name,debts_tel:t.debts_tel||"暂无电话"})}),Array.from(e.values())},getDebtorOrderCount(t){return this.list.filter(e=>e.uid===this.currentId&&e.dt_id===t).length},viewAllOrdersForUser(){this.closeUserDetailDrawer();const t=this.list.find(t=>t.uid===this.currentId);t&&(this.search.keyword=t.user_name||t.phone,this.getData(),this.$message.success(`已筛选显示用户"${t.user_name||t.phone}"的所有订单`))},viewDebtData(t){let e=this;0!=t&&(this.currentDebtId=t),e.dialogViewDebtDetail=!0},editData(t){0!=t?this.getInfo(t):this.ruleForm={title:"",desc:""}},viewData(t){0!=t?this.getView(t):this.ruleForm={title:"",desc:""}},getView(t){let e=this;e.getRequest(e.url+"view?id="+t).then(t=>{200==t.code?(e.info=t.data,e.viewFormVisible=!0):e.$message({type:"error",message:t.msg})})},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{200==t.code?(e.ruleForm=t.data,e.dialogFormVisible=!0):e.$message({type:"error",message:t.msg})})},tuikuan(t){this.$confirm("是否申请退款?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"tuikuan?id="+t).then(t=>{200==t.code?this.$message({type:"success",message:t.msg}):this.$message({type:"error",message:t.msg})})}).catch(()=>{this.$message({type:"error",message:"取消退款!"})})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(t,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},free(t){var e=this;this.$confirm("是否设定此订单为免支付?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.postRequest("/dingdan/free?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"修改成功!"}),e.getData())})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let t=this;t.loading=!0,setTimeout(()=>{t.list=[{id:1,order_sn:"ORD202401001",title:"法律咨询套餐A",total_price:"299.00",is_pay:"已支付",is_deal:"已处理",body:"法律咨询",phone:"13800138001",user_name:"张明华",uid:1,refund_time:"2024-01-15 14:30:00",create_time:"2024-01-15 10:00:00"},{id:2,order_sn:"ORD202401002",title:"合同审查套餐B",total_price:"599.00",is_pay:"未支付",is_deal:"待处理",body:"合同审查",phone:"13800138002",user_name:"李晓雯",uid:2,refund_time:null,create_time:"2024-01-16 09:15:00"},{id:3,order_sn:"ORD202401003",title:"律师函服务",total_price:"899.00",is_pay:"已支付",is_deal:"处理中",body:"律师函",phone:"13800138003",user_name:"王建国",uid:3,refund_time:"2024-01-16 16:45:00",create_time:"2024-01-16 15:20:00"},{id:4,order_sn:"ORD202401004",title:"诉讼代理服务",total_price:"1999.00",is_pay:"退款",is_deal:"已处理",body:"诉讼代理",phone:"13800138004",user_name:"陈美玲",uid:4,refund_time:"2024-01-17 11:20:00",create_time:"2024-01-17 08:30:00"},{id:5,order_sn:"ORD202401005",title:"企业法务顾问",total_price:"3999.00",is_pay:"已支付",is_deal:"已处理",body:"法务顾问",phone:"13800138005",user_name:"刘志强",uid:5,refund_time:"2024-01-18 13:10:00",create_time:"2024-01-18 10:45:00"},{id:6,order_sn:"ORD202401006",title:"知识产权保护",total_price:"1299.00",is_pay:"未支付",is_deal:"待处理",body:"知识产权",phone:"13800138006",user_name:"赵雅琴",uid:6,refund_time:null,create_time:"2024-01-19 14:25:00"},{id:7,order_sn:"ORD202401007",title:"劳动纠纷处理",total_price:"799.00",is_pay:"已支付",is_deal:"处理中",body:"劳动纠纷",phone:"13800138007",user_name:"孙文博",uid:7,refund_time:"2024-01-20 10:15:00",create_time:"2024-01-20 09:00:00"},{id:8,order_sn:"ORD202401008",title:"房产交易法务",total_price:"1599.00",is_pay:"已支付",is_deal:"已处理",body:"房产法务",phone:"13800138008",user_name:"周慧敏",uid:8,refund_time:"2024-01-21 15:40:00",create_time:"2024-01-21 12:30:00"}],t.total=t.list.length,t.money=t.list.reduce((t,e)=>t+parseFloat(e.total_price||0),0).toFixed(2),t.loading=!1},500)},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest(t.url+"save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},handleSuccess(t){200==t.code?(this.$message.success("上传成功"),this.ruleForm[this.filed]=t.data.url):this.$message.error(t.msg)},showImage(t){this.show_image=t,this.dialogVisible=!0},beforeUpload(t){const e=/^image\/(jpeg|png|jpg)$/.test(t.type);e||this.$message.error("上传图片格式不对!")},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})}}},o=n,c=(s("e146"),s("2877")),d=Object(c["a"])(o,i,a,!1,null,"5fdaf19d",null);e["default"]=d.exports},d58f:function(t,e,s){"use strict";var i=s("59ed"),a=s("7b0b"),l=s("44ad"),r=s("07fa"),n=TypeError,o="Reduce of empty array with no initial value",c=function(t){return function(e,s,c,d){var u=a(e),_=l(u),m=r(u);if(i(s),0===m&&c<2)throw new n(o);var p=t?m-1:0,v=t?-1:1;if(c<2)while(1){if(p in _){d=_[p],p+=v;break}if(p+=v,t?p<0:m<=p)throw new n(o)}for(;t?p>=0:m>p;p+=v)p in _&&(d=s(d,_[p],p,u));return d}};t.exports={left:c(!1),right:c(!0)}},e146:function(t,e,s){"use strict";s("8f78")}}]);
//# sourceMappingURL=chunk-98b5b2ba.2018e052.js.map