<?php
/**
 * 
 * 微信支付
 * Author :神台猫屎
 * Data : 2020 01 08
 * Description :微信支付
 * php vs 7.0以上
 */
namespace untils;
error_reporting(E_ALL);
ini_set('display_errors', '1');
// 定义时区
ini_set('date.timezone','Asia/Shanghai');
class WechatPay{
	//定义配置
  private $config=array(
        'APPID'              => 'wxed96286a6f9879e7', // 微信支付APPID
       'MCHID'              => '1633240259', // 微信支付MCHID 商户收款账号
       'KEY'                => 'qdBdnrNddemEKOzhumPOCGluTLAVbRfd', // 微信支付KEY
    );
	// 构造函数
    public function __construct(){
        //这里可以根据不同的框架进行支付配置的填写
        // 如果不是在thinkphp框架中使用；那么注释掉下面一行代码；直接补全 private $config 即可
        //$this->config=$config;
    }
    /**
     * 统一下单
     * @param  array $order 订单 必须包含支付所需要的参数 body(产品描述)、total_fee(订单金额)、out_trade_no(订单号)、product_id(产品id)、trade_type(类型：JSAPI，NATIVE，APP)
     */
    public function _unifiedOrder($order){
    	$wx_config=$this->config;
    	$config=array(
            'appid'=>$wx_config['APPID'],
            'mch_id'=>$wx_config['MCHID'],
            'nonce_str'=>$this->_getNonceStr(),
            'spbill_create_ip'=>$this->_getIp(),  
        );

        //合并字符串
        $data=array_merge($order,$config);
       
        // 生成签名
        $sign=$this->_makeSign($data);
        $data['sign']=$sign;
        $xml=$this->_toXml($data);
        $url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';//接收xml数据的文件
        $result = $this->_request($url,$xml);
        $result=$this->_toArray($result);
        // 显示错误信息
        if ($result['return_code']=='FAIL') {
            die($result['return_msg']);
        }
        if($result['result_code']=="FAIL"){
        	 die($result['err_code'].':'.$result['err_code_des']);
        }
        
        return $result;
    }
     /**
     * 企业支付红包
     * @param  array $order 订单 必须包含支付所需要的参数	partner_trade_no(商户订单号) openid(用户openid)
     * check_name(校验用户姓名选项) NO_CHECK：不校验真实姓名 FORCE_CHECK：强校验真实姓名
     * amount(金额) desc (企业付款备注)	

     */
    public function _macpay($order){
        $wx_config=$this->config;
    	$config=array(
            'mch_appid'=>$wx_config['APPID'],
            'mchid'=>$wx_config['MCHID'],
            'nonce_str'=>$this->_getNonceStr(),
            'spbill_create_ip'=>$this->_getIp(),  
        );
        
       
        $data=array_merge($order,$config);
       
        // 生成签名
        $sign=$this->_makeSign($data);
        $data['sign']=$sign;  
        $xml=$this->_toXml($data);
        $url = 'https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers';//接收xml数据的文件
        $result = $this->_request2($url,$xml);
        $result=$this->_toArray($result);
        return $result;
    }

	/**
	 * [_request curl请求]
	 * @param  [type]  $curl   [请求地址s]
	 * @return [type]          [返回相应数据]
	 */
	public function _request($curl,$xml){
	  	$header[] = "Content-type: text/xml";//定义content-type为xml,注意是数组
        $ch = curl_init ($curl);
        curl_setopt($ch, CURLOPT_URL, $curl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 兼容本地没有指定curl.cainfo路径的错误
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        $response = curl_exec($ch);
		return $response;
	}

	/**
     * 将xml转为array
     * @param  string $xml xml字符串
     * @return array       转换得到的数组
     */
    public function _toArray($xml){   
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $result= json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);        
        return $result;
    }


    /**
     * [_getIp 获取终端ip]
     * @return [type] [返回ip地址]
     */
    public function _getIp(){
	   	if(!empty($_SERVER['HTTP_CLIENT_IP'])){
	        $ip=$_SERVER['HTTP_CLIENT_IP'];
	    }elseif(!empty($_SERVER['HTTP_X_FORWARDED_FOR'])){
	        $ip=$_SERVER['HTTP_X_FORWARDED_FOR'];
	    }else{
	        $ip=$_SERVER['REMOTE_ADDR'];
	    }
	    return $ip;
	}
    /**
     * 输出xml字符
     * @throws WxPayException
    **/
    public function _toXml($data){
        if(!is_array($data) || count($data) <= 0){
            throw new WxPayException("数组数据异常！");
        }
        $xml = "<xml>";
        foreach ($data as $key=>$val){
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        return $xml; 
    }
    /**
     * 生成签名
     * @return 签名，本函数不覆盖sign成员变量，如要设置签名需要调用SetSign方法赋值
     */
    public function _makeSign($data){
        ksort($data);
        $config=$this->config;
        $string = $this->_toUrlParams($data);
        
        //签名步骤二：在string后加入KEY
        $string = $string . "&key=".$config['KEY'];
        //签名步骤三：MD5加密
        
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }
    
    private function _toUrlParams($data)
    {  
        $buff = "";  
        foreach ($data as $k => $v)  
        {  
            if($k != "sign" && $v != "" && !is_array($v)){  
                $buff .= $k . "=" . $v . "&";  
            }  
        }  

        $buff = trim($buff, "&");  
        return $buff;  
    }  
    /**
     * [getNonceStr 制作str]
     * @param  integer $length [description]
     * @return [type]          [description]
     */
    protected function _getNonceStr($length = 32) {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";  
        $str ="";
        for ( $i = 0; $i < $length; $i++ )  {  
            $str .= substr($chars, mt_rand(0, strlen($chars)-1), 1);  
        } 
        return $str;
    }
        /**
     * 验证
     * @return array 返回数组格式的notify数据
     */
    public function _notify(){
        // 获取xml
        $xml=file_get_contents('php://input', 'r'); 
        // 转成php数组
        $data=$this->_toArray($xml);
        // 保存原sign
        $data_sign=$data['sign'];
        // sign不参与签名
        unset($data['sign']);
        $sign=$this->_makeSign($data);
        // 判断签名是否正确  判断支付状态
        if ($sign===$data_sign && $data['return_code']=='SUCCESS' && $data['result_code']=='SUCCESS') {
            $result=$data;
        }else{
            $result=false;
        }
        // 返回状态给微信服务器
        if ($result) {
            $str='<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
        }else{
            $str='<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名失败]]></return_msg></xml>';
        }
        echo $str;
        return $result;
    }
        /**
     * 微信退款(POST)
     * @param string(28) $transaction_id    在微信支付的时候,微信服务器生成的订单流水号,在支付通知中有返回
     * @param string $out_refund_no         商户退款单号
     * @param string $total_fee             微信支付的时候支付的总金额(单位:分)
     * @param string $refund_fee            此次要退款金额(单位:分)
     * @return string                       xml格式的数据
     */
    public function _refund($transaction_id,$out_refund_no,$total_fee,$refund_fee){
    	$wx_config=$this->config;
        $refundorder = array(
            'appid'         => $wx_config['APPID'],
            'mch_id'        => $wx_config['MCHID'],
            'nonce_str'     => $this->_getNonceStr(),
            'out_trade_no' =>$out_refund_no,
            'out_refund_no' => 'tk'.$out_refund_no,
            'total_fee'     => $total_fee * 100,
            'refund_fee'    => $refund_fee * 100
        );

        $sign=$this->_makeSign($refundorder);
        $refundorder['sign'] = $sign;
        $xml=$this->_toXml($refundorder);
       
        $url = 'https://api.mch.weixin.qq.com/secapi/pay/refund';
        $result = $this->_request2($url,$xml);
        $result=$this->_toArray($result);
        file_put_contents('./log3.txt',$result,FILE_APPEND);
        // 显示错误信息
        if ($result['return_code']=='FAIL') {
            die($result['return_msg']);
        }
        if($result['result_code']=="FAIL"){
        	 die($result['err_code'].':'.$result['err_code_des']);
        }
        return $result;
    }
    /**
     * 
     * 
     * 
     * 
    */
    public function _sendMoney($money=0.01,$openid,$desc='推荐奖励',$check_name=''){
    	$wx_config=$this->config;
    	$total_fee=$money*100;
    	$data=[
        'mch_appid'=> $wx_config['APPID'],//商户账号appid
        'mchid'=>$wx_config['MCHID'],//商户号
        'nonce_str'=> $this->_getNonceStr(),//随机字符串
        'partner_trade_no'=> date('YmdHis').rand(1000, 9999),//商户订单号
        'openid'=> $openid,//用户openid
        'check_name'=>'NO_CHECK',//校验用户姓名选项,
        're_user_name'=> $check_name,//收款用户姓名
        'amount'=>$total_fee,//金额
        'desc'=> $desc,//企业付款描述信息
        'spbill_create_ip'=> $this->_getIp(),//Ip地址
        ];
        $sign=$this->_makeSign($data);
        $data['sign']=$sign;  
        $xml=$this->_toXml($data);
        $url = 'https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers';//接收xml数据的文件
        $result = $this->_request2($url,$xml);
    	
        $result=$this->_toArray($result);
        var_dump($result);die;
        // $return  = simplexml_load_string($result, 'SimpleXMLElement', LIBXML_NOCDATA);
        // echo $return= $responseObj->return_code;  
        // return $result;
    }
        
	public function _request2($url,$vars,$second = 30, $aHeader = array()) {
		$isdir =getcwd()."/wechatpay/";//证书位置
	
	    $ch = curl_init();//初始化curl
	    curl_setopt($ch, CURLOPT_TIMEOUT, $second);//设置执行最长秒数
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
	    curl_setopt($ch, CURLOPT_URL, $url);//抓取指定网页
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// 终止从服务端进行验证
	    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//
	    curl_setopt($ch, CURLOPT_SSLCERTTYPE, 'PEM');//证书类型
	    curl_setopt($ch, CURLOPT_SSLCERT, $isdir . 'apiclient_cert.pem');//证书位置
	    curl_setopt($ch, CURLOPT_SSLKEYTYPE, 'PEM');//CURLOPT_SSLKEY中规定的私钥的加密类型
	    curl_setopt($ch, CURLOPT_SSLKEY, $isdir . 'apiclient_key.pem');//证书位置
	    if (count($aHeader) >= 1) {
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $aHeader);//设置头部
	    }
	    curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
	    curl_setopt($ch, CURLOPT_POSTFIELDS, $vars);//全部数据使用HTTP协议中的"POST"操作来发送
	 
	    $data = curl_exec($ch);//执行回话
	   
	    if ($data) {
	        curl_close($ch);
	        return $data;
	    } else {
	        $error = curl_errno($ch);
	        echo "call faild, errorCode:$error\n";
	        curl_close($ch);
	        return false;
	    }
	}

	
	//小程序以及公众号支付
	public function test(){
		$order=[
			'body'=>"测试",
			'total_fee'=>1,
			'out_trade_no'=>'20150806125346',
			'trade_type'=>'JSAPI',
			'openid'=>'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o',
			'notify_url'=>'	http://www.weixin.qq.com/wxpay/pay.php'
		];
	}

}