(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ad1c66d2"],{"9dc7":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"contract-type-container"},[t("div",{staticClass:"page-header"},[t("h1",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(this.$router.currentRoute.name)+" ")]),t("el-button",{staticClass:"refresh-btn",attrs:{type:"text"},on:{click:e.refulsh}},[t("i",{staticClass:"el-icon-refresh"}),e._v(" 刷新 ")])],1),t("div",{staticClass:"action-section"},[t("div",{staticClass:"search-area"},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入合同类型名称",clearable:""},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"}),t("el-button",{attrs:{slot:"append",icon:"el-icon-search",type:"primary"},on:{click:function(t){return e.searchData()}},slot:"append"},[e._v(" 搜索 ")])],1)],1),t("div",{staticClass:"button-area"},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增合同类型 ")])],1)]),t("div",{staticClass:"table-section"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"data-table",attrs:{data:e.list,stripe:"",border:"","empty-text":"暂无合同类型数据"}},[t("el-table-column",{attrs:{prop:"title",label:"合同类型名称","min-width":"200","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"type-name"},[t("i",{staticClass:"el-icon-document-copy"}),t("span",[e._v(e._s(a.row.title))])])]}}])}),t("el-table-column",{attrs:{prop:"template_status",label:"合同模板",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"template-status"},[t("el-tag",{attrs:{type:a.row.template_file?"success":"warning",size:"mini",icon:a.row.template_file?"el-icon-document":"el-icon-warning"}},[e._v(" "+e._s(a.row.template_file?"已上传":"未上传")+" ")])],1)]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"time-info"},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v(e._s(a.row.create_time))])])]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"action-buttons"},[t("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v(" 编辑 ")]),a.row.template_file?t("el-button",{staticClass:"action-btn",attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:function(t){return e.downloadTemplate(a.row)}}},[e._v(" 下载 ")]):e._e(),t("el-button",{staticClass:"action-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.delData(a.$index,a.row.id)}}},[e._v(" 删除 ")])],1)]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"page-sizes":[10,20,50,100],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:e.title+"标题","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"合同模板","label-width":e.formLabelWidth,prop:"template_file",rules:e.templateRules}},[t("div",{staticClass:"template-upload-area"},[e.ruleForm.template_file?t("div",{staticClass:"uploaded-file"},[t("div",{staticClass:"file-info"},[t("i",{staticClass:"el-icon-document"}),t("span",{staticClass:"file-name"},[e._v(e._s(e.ruleForm.template_name||"合同模板文件"))]),t("span",{staticClass:"file-size"},[e._v(e._s(e.formatFileSize(e.ruleForm.template_size)))])]),t("div",{staticClass:"file-actions"},[t("el-button",{attrs:{type:"text",icon:"el-icon-view",size:"mini"},on:{click:e.previewTemplate}},[e._v(" 预览 ")]),t("el-button",{attrs:{type:"text",icon:"el-icon-download",size:"mini"},on:{click:e.downloadCurrentTemplate}},[e._v(" 下载 ")]),t("el-button",{attrs:{type:"text",icon:"el-icon-refresh",size:"mini"},on:{click:e.replaceTemplate}},[e._v(" 替换 ")]),t("el-button",{staticClass:"danger-text",attrs:{type:"text",icon:"el-icon-delete",size:"mini"},on:{click:e.removeTemplate}},[e._v(" 删除 ")])],1)]):t("div",{staticClass:"upload-section"},[t("el-upload",{ref:"templateUpload",staticClass:"template-uploader",attrs:{action:e.uploadAction,"before-upload":e.beforeTemplateUpload,"on-success":e.handleTemplateSuccess,"on-error":e.handleTemplateError,"show-file-list":!1,accept:".doc,.docx,.pdf","auto-upload":!0}},[t("el-button",{attrs:{type:"primary",icon:"el-icon-upload"}},[t("span",[e._v("上传合同模板")])])],1),t("div",{staticClass:"upload-tip"},[t("i",{staticClass:"el-icon-info"}),t("span",[e._v("支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB")])])],1),t("el-upload",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"replaceUpload",attrs:{action:e.uploadAction,"before-upload":e.beforeTemplateUpload,"on-success":e.handleTemplateSuccess,"on-error":e.handleTemplateError,"show-file-list":!1,accept:".doc,.docx,.pdf","auto-upload":!0}})],1)])],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},i=[],s={name:"list",components:{},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/wenshucate/",title:"文书类型",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",desc:"",is_num:0,template_file:"",template_name:"",template_size:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}]},templateRules:[{required:!0,message:"请上传合同模板",trigger:"change"}],formLabelWidth:"120px",uploadAction:"/admin/Upload/uploadFile"}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:"",template_file:"",template_name:"",template_size:0},t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20;let e=this;e.loading=!0,setTimeout(()=>{let t=[{id:1,title:"劳动合同",create_time:"2024-03-20",template_file:"/uploads/templates/labor_contract_template.docx",template_name:"劳动合同模板.docx",template_size:245760},{id:2,title:"租赁合同",create_time:"2024-03-19",template_file:"/uploads/templates/lease_contract_template.pdf",template_name:"租赁合同模板.pdf",template_size:512e3},{id:3,title:"买卖合同",create_time:"2024-03-18",template_file:"",template_name:"",template_size:0},{id:4,title:"服务合同",create_time:"2024-03-17",template_file:"/uploads/templates/service_contract_template.doc",template_name:"服务合同模板.doc",template_size:327680},{id:5,title:"借款合同",create_time:"2024-03-16",template_file:"",template_name:"",template_size:0}];e.search.keyword?e.list=t.filter(t=>t.title.includes(e.search.keyword)):e.list=t,e.total=e.list.length,e.loading=!1},300)},getData(){let e=this;e.loading=!0,setTimeout(()=>{e.list=[{id:1,title:"劳动合同",create_time:"2024-03-20",template_file:"/uploads/templates/labor_contract_template.docx",template_name:"劳动合同模板.docx",template_size:245760},{id:2,title:"租赁合同",create_time:"2024-03-19",template_file:"/uploads/templates/lease_contract_template.pdf",template_name:"租赁合同模板.pdf",template_size:512e3},{id:3,title:"买卖合同",create_time:"2024-03-18",template_file:"",template_name:"",template_size:0},{id:4,title:"服务合同",create_time:"2024-03-17",template_file:"/uploads/templates/service_contract_template.doc",template_name:"服务合同模板.doc",template_size:327680},{id:5,title:"借款合同",create_time:"2024-03-16",template_file:"",template_name:"",template_size:0}],e.total=5,e.loading=!1},500)},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let a=this;a.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(a.ruleForm[t]="",a.$message.success("删除成功!")):a.$message.error(e.msg)})},beforeTemplateUpload(e){const t=/\.(doc|docx|pdf)$/i.test(e.name),a=e.size/1024/1024<10;return t?a?(this.$message.info("正在上传合同模板..."),!0):(this.$message.error("合同模板文件大小不能超过 10MB!"),!1):(this.$message.error("合同模板只能是 .doc、.docx、.pdf 格式!"),!1)},handleTemplateSuccess(e,t){200===e.code?(this.ruleForm.template_file=e.data.url,this.ruleForm.template_name=t.name,this.ruleForm.template_size=t.size,this.$message.success("合同模板上传成功!"),this.$refs.ruleForm.validateField("template_file")):this.$message.error(e.msg||"合同模板上传失败!")},handleTemplateError(e,t){this.$message.error("合同模板上传失败，请重试!"),console.error("Template upload error:",e)},formatFileSize(e){if(0===e)return"0 B";const t=1024,a=["B","KB","MB","GB"],l=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,l)).toFixed(2))+" "+a[l]},previewTemplate(){this.ruleForm.template_file&&this.$message.info("预览功能开发中...")},downloadCurrentTemplate(){if(this.ruleForm.template_file){const e=document.createElement("a");e.href=this.ruleForm.template_file,e.download=this.ruleForm.template_name||"合同模板",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.$message.success("开始下载合同模板")}},replaceTemplate(){this.$refs.replaceUpload.$el.querySelector("input").click()},removeTemplate(){this.$confirm("确定要删除当前合同模板吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.ruleForm.template_file="",this.ruleForm.template_name="",this.ruleForm.template_size=0,this.$message.success("合同模板已删除"),this.$refs.ruleForm.validateField("template_file")}).catch(()=>{})},downloadTemplate(e){if(e.template_file){const t=document.createElement("a");t.href=e.template_file,t.download=e.template_name||e.title+"模板",document.body.appendChild(t),t.click(),document.body.removeChild(t),this.$message.success(`开始下载 ${e.title} 模板`)}else this.$message.warning("该合同类型暂无模板文件")}}},o=s,r=(a("ac58"),a("2877")),n=Object(r["a"])(o,l,i,!1,null,"031e9798",null);t["default"]=n.exports},ac58:function(e,t,a){"use strict";a("beb1")},beb1:function(e,t,a){}}]);
//# sourceMappingURL=chunk-ad1c66d2.57c13503.js.map