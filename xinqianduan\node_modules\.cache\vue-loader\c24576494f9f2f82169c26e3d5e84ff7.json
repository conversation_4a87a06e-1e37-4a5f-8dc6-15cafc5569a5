{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=template&id=72349a68&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748608591384}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}