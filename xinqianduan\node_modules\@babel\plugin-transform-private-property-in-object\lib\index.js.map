{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCreateClassFeaturesPlugin", "_helperAnnotateAsPure", "_default", "exports", "default", "declare", "api", "opt", "assertVersion", "types", "t", "template", "loose", "classWeakSets", "WeakMap", "fieldsWeakSets", "unshadow", "name", "targetScope", "scope", "hasOwnBinding", "rename", "parent", "injectToFieldInit", "fieldPath", "expr", "before", "node", "value", "get", "insertBefore", "insertAfter", "set", "unaryExpression", "injectInitialization", "classPath", "init", "firstFieldPath", "constructorPath", "el", "isClassProperty", "isClassPrivateProperty", "static", "isClassMethod", "kind", "injectConstructorInit", "expressionStatement", "getWeakSetId", "weakSets", "outerClass", "reference", "inject", "id", "generateUidIdentifier", "expression", "ast", "cloneNode", "newExpr", "newExpression", "identifier", "annotateAsPure", "inherits", "version", "undefined", "pre", "enableFeature", "file", "FEATURES", "privateIn", "visitor", "BinaryExpression", "path", "state", "operator", "isPrivateName", "left", "privateElement", "findParent", "isClass", "find", "isPrivate", "key", "parentPath", "isPattern", "replaceWith", "type", "buildCheckInRHS", "right", "_outerClass$node$id"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport {\n  enableFeature,\n  FEATURES,\n  injectInitialization as injectConstructorInit,\n  buildCheckInRHS,\n} from \"@babel/helper-create-class-features-plugin\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport type { NodePath, Scope, types as t } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\nexport default declare((api, opt: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const { types: t, template } = api;\n  const { loose } = opt;\n\n  // NOTE: When using the class fields or private methods plugins,\n  // they will also take care of '#priv in obj' checks when visiting\n  // the ClassExpression or ClassDeclaration nodes.\n  // The visitor of this plugin is only effective when not compiling\n  // private fields and methods.\n\n  const classWeakSets: WeakMap<t.Class, t.Identifier> = new WeakMap();\n  const fieldsWeakSets: WeakMap<\n    t.ClassPrivateProperty | t.ClassPrivateMethod,\n    t.Identifier\n  > = new WeakMap();\n\n  function unshadow(name: string, targetScope: Scope, scope: Scope) {\n    while (scope !== targetScope) {\n      if (scope.hasOwnBinding(name)) scope.rename(name);\n      scope = scope.parent;\n    }\n  }\n\n  function injectToFieldInit(\n    fieldPath: NodePath<t.ClassPrivateProperty | t.ClassProperty>,\n    expr: t.Expression,\n    before = false,\n  ) {\n    if (fieldPath.node.value) {\n      const value = fieldPath.get(\"value\");\n      if (before) {\n        value.insertBefore(expr);\n      } else {\n        value.insertAfter(expr);\n      }\n    } else {\n      fieldPath.set(\"value\", t.unaryExpression(\"void\", expr));\n    }\n  }\n\n  function injectInitialization(\n    classPath: NodePath<t.Class>,\n    init: t.Expression,\n  ) {\n    let firstFieldPath;\n    let constructorPath;\n\n    for (const el of classPath.get(\"body.body\")) {\n      if (\n        (el.isClassProperty() || el.isClassPrivateProperty()) &&\n        !el.node.static\n      ) {\n        firstFieldPath = el;\n        break;\n      }\n      if (!constructorPath && el.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = el;\n      }\n    }\n\n    if (firstFieldPath) {\n      injectToFieldInit(firstFieldPath, init, true);\n    } else {\n      injectConstructorInit(classPath, constructorPath, [\n        t.expressionStatement(init),\n      ]);\n    }\n  }\n\n  function getWeakSetId<Ref extends t.Node>(\n    weakSets: WeakMap<t.Node, t.Identifier>,\n    outerClass: NodePath<t.Class>,\n    reference: NodePath<Ref>,\n    name = \"\",\n    inject: (\n      reference: NodePath<Ref>,\n      expression: t.Expression,\n      before?: boolean,\n    ) => void,\n  ) {\n    let id = weakSets.get(reference.node);\n\n    if (!id) {\n      id = outerClass.scope.generateUidIdentifier(`${name || \"\"} brandCheck`);\n      weakSets.set(reference.node, id);\n\n      inject(reference, template.expression.ast`${t.cloneNode(id)}.add(this)`);\n\n      const newExpr = t.newExpression(t.identifier(\"WeakSet\"), []);\n      annotateAsPure(newExpr);\n\n      outerClass.insertBefore(template.ast`var ${id} = ${newExpr}`);\n    }\n\n    return t.cloneNode(id);\n  }\n\n  return {\n    name: \"transform-private-property-in-object\",\n    inherits:\n      USE_ESM || IS_STANDALONE || api.version[0] === \"8\"\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-private-property-in-object\").default,\n    pre() {\n      // Enable this in @babel/helper-create-class-features-plugin, so that it\n      // can be handled by the private fields and methods transform.\n      enableFeature(this.file, FEATURES.privateIn, loose);\n    },\n    visitor: {\n      BinaryExpression(path, state) {\n        const { node } = path;\n        const { file } = state;\n        if (node.operator !== \"in\") return;\n        if (!t.isPrivateName(node.left)) return;\n\n        const { name } = node.left.id;\n\n        let privateElement: NodePath<\n          t.ClassPrivateMethod | t.ClassPrivateProperty\n        >;\n        const outerClass = path.findParent(path => {\n          if (!path.isClass()) return false;\n\n          privateElement = path.get(\"body.body\").find(\n            ({ node }) =>\n              // fixme: Support class accessor property\n              t.isPrivate(node) && node.key.id.name === name,\n          ) as NodePath<t.ClassPrivateMethod | t.ClassPrivateProperty>;\n\n          return !!privateElement;\n        }) as NodePath<t.Class>;\n\n        if (outerClass.parentPath.scope.path.isPattern()) {\n          outerClass.replaceWith(\n            template.ast`(() => ${outerClass.node})()` as t.Statement,\n          );\n          // The injected class will be queued and eventually transformed when visited\n          return;\n        }\n\n        if (privateElement.node.type === \"ClassPrivateMethod\") {\n          if (privateElement.node.static) {\n            if (outerClass.node.id) {\n              unshadow(outerClass.node.id.name, outerClass.scope, path.scope);\n            } else {\n              outerClass.set(\"id\", path.scope.generateUidIdentifier(\"class\"));\n            }\n            path.replaceWith(\n              template.expression.ast`\n                ${t.cloneNode(outerClass.node.id)} === ${buildCheckInRHS(\n                  node.right,\n                  file,\n                )}\n              `,\n            );\n          } else {\n            const id = getWeakSetId(\n              classWeakSets,\n              outerClass,\n              outerClass,\n              outerClass.node.id?.name,\n              injectInitialization,\n            );\n\n            path.replaceWith(\n              template.expression.ast`${id}.has(${buildCheckInRHS(\n                node.right,\n                file,\n              )})`,\n            );\n          }\n        } else {\n          // Private fields might not all be initialized: see the 'halfConstructed'\n          // example at https://v8.dev/features/private-brand-checks.\n\n          const id = getWeakSetId<t.ClassPrivateProperty>(\n            fieldsWeakSets,\n            outerClass,\n            privateElement as NodePath<t.ClassPrivateProperty>,\n            privateElement.node.key.id.name,\n            injectToFieldInit,\n          );\n\n          path.replaceWith(\n            template.expression.ast`${id}.has(${buildCheckInRHS(\n              node.right,\n              file,\n            )})`,\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,gCAAA,GAAAD,OAAA;AAMA,IAAAE,qBAAA,GAAAF,OAAA;AAA4D,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAM7C,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,GAAY,KAAK;EAC5CD,GAAG,CAACE,aAAa,uCAAoB,CAAC;EACtC,MAAM;IAAEC,KAAK,EAAEC,CAAC;IAAEC;EAAS,CAAC,GAAGL,GAAG;EAClC,MAAM;IAAEM;EAAM,CAAC,GAAGL,GAAG;EAQrB,MAAMM,aAA6C,GAAG,IAAIC,OAAO,CAAC,CAAC;EACnE,MAAMC,cAGL,GAAG,IAAID,OAAO,CAAC,CAAC;EAEjB,SAASE,QAAQA,CAACC,IAAY,EAAEC,WAAkB,EAAEC,KAAY,EAAE;IAChE,OAAOA,KAAK,KAAKD,WAAW,EAAE;MAC5B,IAAIC,KAAK,CAACC,aAAa,CAACH,IAAI,CAAC,EAAEE,KAAK,CAACE,MAAM,CAACJ,IAAI,CAAC;MACjDE,KAAK,GAAGA,KAAK,CAACG,MAAM;IACtB;EACF;EAEA,SAASC,iBAAiBA,CACxBC,SAA6D,EAC7DC,IAAkB,EAClBC,MAAM,GAAG,KAAK,EACd;IACA,IAAIF,SAAS,CAACG,IAAI,CAACC,KAAK,EAAE;MACxB,MAAMA,KAAK,GAAGJ,SAAS,CAACK,GAAG,CAAC,OAAO,CAAC;MACpC,IAAIH,MAAM,EAAE;QACVE,KAAK,CAACE,YAAY,CAACL,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLG,KAAK,CAACG,WAAW,CAACN,IAAI,CAAC;MACzB;IACF,CAAC,MAAM;MACLD,SAAS,CAACQ,GAAG,CAAC,OAAO,EAAEtB,CAAC,CAACuB,eAAe,CAAC,MAAM,EAAER,IAAI,CAAC,CAAC;IACzD;EACF;EAEA,SAASS,oBAAoBA,CAC3BC,SAA4B,EAC5BC,IAAkB,EAClB;IACA,IAAIC,cAAc;IAClB,IAAIC,eAAe;IAEnB,KAAK,MAAMC,EAAE,IAAIJ,SAAS,CAACN,GAAG,CAAC,WAAW,CAAC,EAAE;MAC3C,IACE,CAACU,EAAE,CAACC,eAAe,CAAC,CAAC,IAAID,EAAE,CAACE,sBAAsB,CAAC,CAAC,KACpD,CAACF,EAAE,CAACZ,IAAI,CAACe,MAAM,EACf;QACAL,cAAc,GAAGE,EAAE;QACnB;MACF;MACA,IAAI,CAACD,eAAe,IAAIC,EAAE,CAACI,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QACjEN,eAAe,GAAGC,EAAE;MACtB;IACF;IAEA,IAAIF,cAAc,EAAE;MAClBd,iBAAiB,CAACc,cAAc,EAAED,IAAI,EAAE,IAAI,CAAC;IAC/C,CAAC,MAAM;MACL,IAAAS,qDAAqB,EAACV,SAAS,EAAEG,eAAe,EAAE,CAChD5B,CAAC,CAACoC,mBAAmB,CAACV,IAAI,CAAC,CAC5B,CAAC;IACJ;EACF;EAEA,SAASW,YAAYA,CACnBC,QAAuC,EACvCC,UAA6B,EAC7BC,SAAwB,EACxBjC,IAAI,GAAG,EAAE,EACTkC,MAIS,EACT;IACA,IAAIC,EAAE,GAAGJ,QAAQ,CAACnB,GAAG,CAACqB,SAAS,CAACvB,IAAI,CAAC;IAErC,IAAI,CAACyB,EAAE,EAAE;MACPA,EAAE,GAAGH,UAAU,CAAC9B,KAAK,CAACkC,qBAAqB,CAAE,GAAEpC,IAAI,IAAI,EAAG,aAAY,CAAC;MACvE+B,QAAQ,CAAChB,GAAG,CAACkB,SAAS,CAACvB,IAAI,EAAEyB,EAAE,CAAC;MAEhCD,MAAM,CAACD,SAAS,EAAEvC,QAAQ,CAAC2C,UAAU,CAACC,GAAI,GAAE7C,CAAC,CAAC8C,SAAS,CAACJ,EAAE,CAAE,YAAW,CAAC;MAExE,MAAMK,OAAO,GAAG/C,CAAC,CAACgD,aAAa,CAAChD,CAAC,CAACiD,UAAU,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;MAC5D,IAAAC,6BAAc,EAACH,OAAO,CAAC;MAEvBR,UAAU,CAACnB,YAAY,CAACnB,QAAQ,CAAC4C,GAAI,OAAMH,EAAG,MAAKK,OAAQ,EAAC,CAAC;IAC/D;IAEA,OAAO/C,CAAC,CAAC8C,SAAS,CAACJ,EAAE,CAAC;EACxB;EAEA,OAAO;IACLnC,IAAI,EAAE,sCAAsC;IAC5C4C,QAAQ,EACsBvD,GAAG,CAACwD,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,GAC9CC,SAAS,GAEThE,OAAO,CAAC,iDAAiD,CAAC,CAACK,OAAO;IACxE4D,GAAGA,CAAA,EAAG;MAGJ,IAAAC,8CAAa,EAAC,IAAI,CAACC,IAAI,EAAEC,yCAAQ,CAACC,SAAS,EAAExD,KAAK,CAAC;IACrD,CAAC;IACDyD,OAAO,EAAE;MACPC,gBAAgBA,CAACC,IAAI,EAAEC,KAAK,EAAE;QAC5B,MAAM;UAAE7C;QAAK,CAAC,GAAG4C,IAAI;QACrB,MAAM;UAAEL;QAAK,CAAC,GAAGM,KAAK;QACtB,IAAI7C,IAAI,CAAC8C,QAAQ,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC/D,CAAC,CAACgE,aAAa,CAAC/C,IAAI,CAACgD,IAAI,CAAC,EAAE;QAEjC,MAAM;UAAE1D;QAAK,CAAC,GAAGU,IAAI,CAACgD,IAAI,CAACvB,EAAE;QAE7B,IAAIwB,cAEH;QACD,MAAM3B,UAAU,GAAGsB,IAAI,CAACM,UAAU,CAACN,IAAI,IAAI;UACzC,IAAI,CAACA,IAAI,CAACO,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK;UAEjCF,cAAc,GAAGL,IAAI,CAAC1C,GAAG,CAAC,WAAW,CAAC,CAACkD,IAAI,CACzC,CAAC;YAAEpD;UAAK,CAAC,KAEPjB,CAAC,CAACsE,SAAS,CAACrD,IAAI,CAAC,IAAIA,IAAI,CAACsD,GAAG,CAAC7B,EAAE,CAACnC,IAAI,KAAKA,IAC9C,CAA4D;UAE5D,OAAO,CAAC,CAAC2D,cAAc;QACzB,CAAC,CAAsB;QAEvB,IAAI3B,UAAU,CAACiC,UAAU,CAAC/D,KAAK,CAACoD,IAAI,CAACY,SAAS,CAAC,CAAC,EAAE;UAChDlC,UAAU,CAACmC,WAAW,CACpBzE,QAAQ,CAAC4C,GAAI,UAASN,UAAU,CAACtB,IAAK,KACxC,CAAC;UAED;QACF;QAEA,IAAIiD,cAAc,CAACjD,IAAI,CAAC0D,IAAI,KAAK,oBAAoB,EAAE;UACrD,IAAIT,cAAc,CAACjD,IAAI,CAACe,MAAM,EAAE;YAC9B,IAAIO,UAAU,CAACtB,IAAI,CAACyB,EAAE,EAAE;cACtBpC,QAAQ,CAACiC,UAAU,CAACtB,IAAI,CAACyB,EAAE,CAACnC,IAAI,EAAEgC,UAAU,CAAC9B,KAAK,EAAEoD,IAAI,CAACpD,KAAK,CAAC;YACjE,CAAC,MAAM;cACL8B,UAAU,CAACjB,GAAG,CAAC,IAAI,EAAEuC,IAAI,CAACpD,KAAK,CAACkC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACjE;YACAkB,IAAI,CAACa,WAAW,CACdzE,QAAQ,CAAC2C,UAAU,CAACC,GAAI;AACtC,kBAAkB7C,CAAC,CAAC8C,SAAS,CAACP,UAAU,CAACtB,IAAI,CAACyB,EAAE,CAAE,QAAO,IAAAkC,gDAAe,EACtD3D,IAAI,CAAC4D,KAAK,EACVrB,IACF,CAAE;AAClB,eACY,CAAC;UACH,CAAC,MAAM;YAAA,IAAAsB,mBAAA;YACL,MAAMpC,EAAE,GAAGL,YAAY,CACrBlC,aAAa,EACboC,UAAU,EACVA,UAAU,GAAAuC,mBAAA,GACVvC,UAAU,CAACtB,IAAI,CAACyB,EAAE,qBAAlBoC,mBAAA,CAAoBvE,IAAI,EACxBiB,oBACF,CAAC;YAEDqC,IAAI,CAACa,WAAW,CACdzE,QAAQ,CAAC2C,UAAU,CAACC,GAAI,GAAEH,EAAG,QAAO,IAAAkC,gDAAe,EACjD3D,IAAI,CAAC4D,KAAK,EACVrB,IACF,CAAE,GACJ,CAAC;UACH;QACF,CAAC,MAAM;UAIL,MAAMd,EAAE,GAAGL,YAAY,CACrBhC,cAAc,EACdkC,UAAU,EACV2B,cAAc,EACdA,cAAc,CAACjD,IAAI,CAACsD,GAAG,CAAC7B,EAAE,CAACnC,IAAI,EAC/BM,iBACF,CAAC;UAEDgD,IAAI,CAACa,WAAW,CACdzE,QAAQ,CAAC2C,UAAU,CAACC,GAAI,GAAEH,EAAG,QAAO,IAAAkC,gDAAe,EACjD3D,IAAI,CAAC4D,KAAK,EACVrB,IACF,CAAE,GACJ,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}