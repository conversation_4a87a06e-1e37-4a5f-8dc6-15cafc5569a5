{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\NotificationList.vue", "mtime": 1749567732258}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["NotificationList.vue"], "names": [], "mappings": ";AA6LA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NotificationList.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\n  <div class=\"notification-container\">\n    <div class=\"page-header\">\n      <h2>系统通知</h2>\n      <div class=\"header-actions\">\n        <el-button @click=\"markAllAsRead\" :disabled=\"unreadCount === 0\">\n          <i class=\"el-icon-check\"></i> 全部标记为已读\n        </el-button>\n        <el-button type=\"primary\" @click=\"showAddDialog = true\">\n          <i class=\"el-icon-plus\"></i> 发布通知\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <el-row :gutter=\"20\" class=\"stats-row\">\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ totalCount }}</div>\n            <div class=\"stat-label\">总通知数</div>\n          </div>\n          <i class=\"el-icon-bell stat-icon\"></i>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"stat-card\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number unread\">{{ unreadCount }}</div>\n            <div class=\"stat-label\">未读通知</div>\n          </div>\n          <i class=\"el-icon-message stat-icon\"></i>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 筛选条件 -->\n    <el-card class=\"filter-card\" shadow=\"never\">\n      <el-form :inline=\"true\" :model=\"filterForm\" class=\"filter-form\">\n        <el-form-item label=\"状态\">\n          <el-select v-model=\"filterForm.is_read\" placeholder=\"请选择状态\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"未读\" value=\"0\"></el-option>\n            <el-option label=\"已读\" value=\"1\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"类型\">\n          <el-select v-model=\"filterForm.type\" placeholder=\"请选择类型\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\">\n          <el-select v-model=\"filterForm.level\" placeholder=\"请选择级别\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"loadNotifications\">查询</el-button>\n          <el-button @click=\"resetFilter\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 通知列表 -->\n    <el-card class=\"list-card\">\n      <div class=\"notification-list\" v-loading=\"loading\">\n        <div \n          v-for=\"notification in notificationList\" \n          :key=\"notification.id\"\n          class=\"notification-item\"\n          :class=\"{ 'unread': !notification.read }\"\n        >\n          <div class=\"notification-header\">\n            <div class=\"notification-meta\">\n              <el-tag \n                :type=\"getLevelType(notification.level)\" \n                size=\"small\"\n                class=\"level-tag\"\n              >\n                {{ getLevelText(notification.level) }}\n              </el-tag>\n              <el-tag \n                :type=\"getTypeColor(notification.type)\" \n                size=\"small\"\n                class=\"type-tag\"\n              >\n                {{ getTypeText(notification.type) }}\n              </el-tag>\n              <span class=\"notification-time\">{{ notification.time }}</span>\n            </div>\n            <div class=\"notification-actions\">\n              <el-button \n                v-if=\"!notification.read\" \n                type=\"text\" \n                size=\"small\" \n                @click=\"markAsRead(notification)\"\n              >\n                标记已读\n              </el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"deleteNotification(notification)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n          <div class=\"notification-content\">\n            <h4 class=\"notification-title\">{{ notification.title }}</h4>\n            <p class=\"notification-desc\">{{ notification.content }}</p>\n          </div>\n        </div>\n\n        <div v-if=\"notificationList.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-bell\"></i>\n          <p>暂无通知</p>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"pagination.page\"\n          :page-sizes=\"[10, 20, 50]\"\n          :page-size=\"pagination.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"pagination.total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 发布通知对话框 -->\n    <el-dialog \n      title=\"发布通知\" \n      :visible.sync=\"showAddDialog\"\n      width=\"600px\"\n    >\n      <el-form :model=\"newNotification\" :rules=\"notificationRules\" ref=\"notificationForm\" label-width=\"100px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"newNotification.title\" placeholder=\"请输入通知标题\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"内容\" prop=\"content\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"newNotification.content\" \n            placeholder=\"请输入通知内容\"\n            :rows=\"4\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"type\">\n          <el-select v-model=\"newNotification.type\" placeholder=\"请选择类型\">\n            <el-option label=\"系统通知\" value=\"system\"></el-option>\n            <el-option label=\"更新通知\" value=\"update\"></el-option>\n            <el-option label=\"备份通知\" value=\"backup\"></el-option>\n            <el-option label=\"警告通知\" value=\"warning\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"级别\" prop=\"level\">\n          <el-select v-model=\"newNotification.level\" placeholder=\"请选择级别\">\n            <el-option label=\"信息\" value=\"info\"></el-option>\n            <el-option label=\"警告\" value=\"warning\"></el-option>\n            <el-option label=\"错误\" value=\"error\"></el-option>\n            <el-option label=\"成功\" value=\"success\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"目标用户\">\n          <el-select v-model=\"newNotification.target_type\" placeholder=\"请选择目标用户\">\n            <el-option label=\"所有用户\" value=\"all\"></el-option>\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\n            <el-option label=\"普通用户\" value=\"user\"></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showAddDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"publishNotification\">发布</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\n\nexport default {\n  name: 'NotificationList',\n  mixins: [{ methods: { getRequest, postRequest, deleteRequest } }],\n  data() {\n    return {\n      loading: false,\n      showAddDialog: false,\n      notificationList: [],\n      totalCount: 0,\n      unreadCount: 0,\n      filterForm: {\n        is_read: '',\n        type: '',\n        level: ''\n      },\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      },\n      newNotification: {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      },\n      notificationRules: {\n        title: [\n          { required: true, message: '请输入标题', trigger: 'blur' }\n        ],\n        content: [\n          { required: true, message: '请输入内容', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择类型', trigger: 'change' }\n        ],\n        level: [\n          { required: true, message: '请选择级别', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadNotifications()\n    this.loadStats()\n  },\n  methods: {\n    async loadNotifications() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          size: this.pagination.size,\n          ...this.filterForm\n        }\n        const response = await this.getRequest('/notification/list', params)\n        if (response.code === 200) {\n          this.notificationList = response.data.list || []\n          this.pagination.total = response.data.total || 0\n        }\n      } catch (error) {\n        console.error('加载通知失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadStats() {\n      try {\n        const response = await this.getRequest('/notification/stats')\n        if (response.code === 200) {\n          this.totalCount = response.data.total || 0\n          this.unreadCount = response.data.unread || 0\n        }\n      } catch (error) {\n        console.error('加载统计失败:', error)\n      }\n    },\n\n    async markAsRead(notification) {\n      try {\n        const response = await this.postRequest('/dashboard/markNotificationRead', {\n          id: notification.id\n        })\n        if (response.code === 200) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n          this.$message.success('标记成功')\n        }\n      } catch (error) {\n        console.error('标记失败:', error)\n        this.$message.error('操作失败')\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await this.$confirm('确定要将所有未读通知标记为已读吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.postRequest('/notification/markAllRead')\n        if (response.code === 200) {\n          this.notificationList.forEach(item => item.read = true)\n          this.unreadCount = 0\n          this.$message.success('操作成功')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('操作失败:', error)\n          this.$message.error('操作失败')\n        }\n      }\n    },\n\n    async deleteNotification(notification) {\n      try {\n        await this.$confirm('确定要删除这条通知吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await this.deleteRequest('/notification/delete', { id: notification.id })\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async publishNotification() {\n      try {\n        await this.$refs.notificationForm.validate()\n        \n        const response = await this.postRequest('/notification/create', this.newNotification)\n        if (response.code === 200) {\n          this.$message.success('发布成功')\n          this.showAddDialog = false\n          this.resetForm()\n          this.loadNotifications()\n          this.loadStats()\n        }\n      } catch (error) {\n        console.error('发布失败:', error)\n        this.$message.error('发布失败')\n      }\n    },\n\n    resetForm() {\n      this.newNotification = {\n        title: '',\n        content: '',\n        type: 'system',\n        level: 'info',\n        target_type: 'all'\n      }\n      this.$refs.notificationForm && this.$refs.notificationForm.resetFields()\n    },\n\n    resetFilter() {\n      this.filterForm = {\n        is_read: '',\n        type: '',\n        level: ''\n      }\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleSizeChange(size) {\n      this.pagination.size = size\n      this.pagination.page = 1\n      this.loadNotifications()\n    },\n\n    handleCurrentChange(page) {\n      this.pagination.page = page\n      this.loadNotifications()\n    },\n\n    getLevelType(level) {\n      const map = {\n        info: 'info',\n        warning: 'warning',\n        error: 'danger',\n        success: 'success'\n      }\n      return map[level] || 'info'\n    },\n\n    getLevelText(level) {\n      const map = {\n        info: '信息',\n        warning: '警告',\n        error: '错误',\n        success: '成功'\n      }\n      return map[level] || '信息'\n    },\n\n    getTypeColor(type) {\n      const map = {\n        system: '',\n        update: 'success',\n        backup: 'info',\n        warning: 'warning'\n      }\n      return map[type] || ''\n    },\n\n    getTypeText(type) {\n      const map = {\n        system: '系统通知',\n        update: '更新通知',\n        backup: '备份通知',\n        warning: '警告通知'\n      }\n      return map[type] || '系统通知'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notification-container {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.stats-row {\n  margin-bottom: 20px;\n}\n\n.stat-card {\n  padding: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.stat-number.unread {\n  color: #f56c6c;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n.stat-icon {\n  font-size: 40px;\n  color: #c0c4cc;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form {\n  margin-bottom: 0;\n}\n\n.list-card {\n  margin-bottom: 20px;\n}\n\n.notification-list {\n  min-height: 400px;\n}\n\n.notification-item {\n  border: 1px solid #ebeef5;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 12px;\n  transition: all 0.3s;\n}\n\n.notification-item:hover {\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.notification-item.unread {\n  border-left: 4px solid #409eff;\n  background-color: #f0f9ff;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.level-tag, .type-tag {\n  margin-right: 8px;\n}\n\n.notification-time {\n  color: #909399;\n  font-size: 12px;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.notification-title {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.notification-desc {\n  margin: 0;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 0;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"]}]}