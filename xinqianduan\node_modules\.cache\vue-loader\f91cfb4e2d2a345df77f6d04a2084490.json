{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue?vue&type=style&index=0&id=24b5ffb2&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue", "mtime": 1748540171930}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\r\n  <div class=\"employee-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            员工管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统员工信息和职位分配</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增员工\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">员工搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入员工姓名、手机号或账号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">职位筛选</label>\r\n              <el-select\r\n                v-model=\"search.zhiwei_id\"\r\n                placeholder=\"请选择职位\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"zhiwei in zhiweis\"\r\n                  :key=\"zhiwei.id\"\r\n                  :label=\"zhiwei.title\"\r\n                  :value=\"zhiwei.id\"\r\n                ></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"正常\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n            <el-button icon=\"el-icon-download\" @click=\"exportData\">\r\n              导出\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总员工数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon admin\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ adminCount }}</div>\r\n              <div class=\"stat-label\">管理员</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <i class=\"el-icon-circle-check\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeCount }}</div>\r\n              <div class=\"stat-label\">在职员工</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon new\">\r\n              <i class=\"el-icon-plus\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ newCount }}</div>\r\n              <div class=\"stat-label\">本月新增</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 员工列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            员工列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"employee-table\"\r\n            stripe\r\n          >\r\n            <el-table-column label=\"头像\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"avatar-cell\">\r\n                  <el-avatar\r\n                    :src=\"scope.row.pic_path\"\r\n                    :size=\"50\"\r\n                    @click.native=\"showImage(scope.row.pic_path)\"\r\n                    class=\"employee-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"title\" label=\"员工姓名\" min-width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"employee-name-cell\">\r\n                  <div class=\"employee-name clickable\" @click=\"showEmployeeDetail(scope.row)\">{{ scope.row.title }}</div>\r\n                  <div class=\"employee-account\">{{ scope.row.account }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"职位\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  :type=\"getPositionTagType(scope.row.zhiwei_title)\"\r\n                  size=\"small\"\r\n                >\r\n                  {{ scope.row.zhiwei_title || '未分配' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"phone\" label=\"手机号码\" width=\"130\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"phone-cell\">\r\n                  <i class=\"el-icon-phone\"></i>\r\n                  {{ scope.row.phone }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"入职时间\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"showEmployeeEdit(scope.row)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"mini\"\r\n                    @click=\"chongzhi(scope.row.id)\"\r\n                    icon=\"el-icon-key\"\r\n                    plain\r\n                  >\r\n                    重置密码\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"employee in list\" :key=\"employee.id\" class=\"employee-card-col\">\r\n              <div class=\"employee-card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-avatar\">\r\n                    <el-avatar\r\n                      :src=\"employee.pic_path\"\r\n                      :size=\"60\"\r\n                      @click.native=\"showImage(employee.pic_path)\"\r\n                    >\r\n                      <i class=\"el-icon-user-solid\"></i>\r\n                    </el-avatar>\r\n                  </div>\r\n                  <div class=\"card-info\">\r\n                    <div class=\"card-name clickable\" @click=\"showEmployeeDetail(employee)\">{{ employee.title }}</div>\r\n                    <div class=\"card-position\">\r\n                      <el-tag\r\n                        :type=\"getPositionTagType(employee.zhiwei_title)\"\r\n                        size=\"mini\"\r\n                      >\r\n                        {{ employee.zhiwei_title || '未分配' }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-status\">\r\n                    <el-switch\r\n                      v-model=\"employee.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      @change=\"changeStatus(employee)\"\r\n                      size=\"small\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-detail\">\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-phone\"></i>\r\n                      <span>{{ employee.phone }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-user\"></i>\r\n                      <span>{{ employee.account }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      <span>{{ employee.create_time }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"showEmployeeEdit(employee)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    @click=\"chongzhi(employee.id)\"\r\n                    icon=\"el-icon-key\"\r\n                    plain\r\n                  >\r\n                    重置密码\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(employee), employee.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 右侧滑出详情面板 -->\r\n    <div class=\"employee-detail-panel\" :class=\"{ 'panel-open': detailPanelVisible }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDetailPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"panel-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            <span v-if=\"!currentEmployee.id\">新增员工</span>\r\n            <span v-else-if=\"isViewMode\">员工详情</span>\r\n            <span v-else>编辑员工</span>\r\n          </div>\r\n          <div class=\"panel-actions\">\r\n            <el-button\r\n              v-if=\"isViewMode && currentEmployee.id\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"switchToEditMode\"\r\n              icon=\"el-icon-edit\"\r\n            >\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isViewMode\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"saveEmployeeData\"\r\n              :loading=\"saving\"\r\n              icon=\"el-icon-check\"\r\n            >\r\n              保存\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isViewMode && currentEmployee.id\"\r\n              size=\"small\"\r\n              @click=\"cancelEdit\"\r\n              icon=\"el-icon-refresh-left\"\r\n            >\r\n              取消编辑\r\n            </el-button>\r\n            <el-button\r\n              size=\"small\"\r\n              @click=\"closeDetailPanel\"\r\n              icon=\"el-icon-close\"\r\n            >\r\n              关闭\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 面板内容 -->\r\n        <div class=\"panel-body\">\r\n          <el-form\r\n            :model=\"currentEmployee\"\r\n            :rules=\"detailRules\"\r\n            ref=\"detailForm\"\r\n            label-width=\"100px\"\r\n            class=\"employee-form\"\r\n          >\r\n            <!-- 基本信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-user\"></i>\r\n                基本信息\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"员工姓名\" prop=\"title\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.title\"\r\n                      placeholder=\"请输入员工姓名\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.phone\"\r\n                      placeholder=\"请输入手机号码\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"登录账号\" prop=\"account\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.account\"\r\n                      placeholder=\"请输入登录账号\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    >\r\n                      <template slot=\"append\" v-if=\"!currentEmployee.id && !isViewMode\">默认密码888888</template>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"员工状态\" prop=\"status\">\r\n                    <el-switch\r\n                      v-model=\"currentEmployee.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      :disabled=\"isViewMode\"\r\n                      active-text=\"正常\"\r\n                      inactive-text=\"禁用\"\r\n                    >\r\n                    </el-switch>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 职位信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-postcard\"></i>\r\n                职位信息\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"所属职位\" prop=\"zhiwei_id\">\r\n                    <el-select\r\n                      v-if=\"!isViewMode\"\r\n                      v-model=\"currentEmployee.zhiwei_id\"\r\n                      placeholder=\"请选择职位\"\r\n                      filterable\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"zhiwei in zhiweis\"\r\n                        :key=\"zhiwei.id\"\r\n                        :label=\"zhiwei.title\"\r\n                        :value=\"zhiwei.id\"\r\n                      ></el-option>\r\n                    </el-select>\r\n                    <el-input\r\n                      v-else\r\n                      :value=\"currentEmployee.zhiwei_title || '未分配'\"\r\n                      readonly\r\n                      style=\"width: 100%\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"入职时间\">\r\n                    <el-date-picker\r\n                      v-model=\"currentEmployee.join_date\"\r\n                      type=\"date\"\r\n                      placeholder=\"选择入职时间\"\r\n                      :readonly=\"isViewMode\"\r\n                      :disabled=\"isViewMode\"\r\n                      style=\"width: 100%\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 头像信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                头像信息\r\n              </div>\r\n\r\n              <div class=\"avatar-upload-section\">\r\n                <div class=\"current-avatar\">\r\n                  <el-avatar\r\n                    :src=\"currentEmployee.pic_path\"\r\n                    :size=\"100\"\r\n                    @click.native=\"showImage(currentEmployee.pic_path)\"\r\n                    class=\"preview-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"upload-controls\">\r\n                  <el-form-item label=\"头像\" prop=\"pic_path\" v-if=\"!isViewMode\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.pic_path\"\r\n                      placeholder=\"头像URL\"\r\n                      readonly\r\n                      class=\"avatar-input\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <div class=\"upload-buttons\" v-if=\"!isViewMode\">\r\n                    <el-upload\r\n                      action=\"/admin/Upload/uploadImage\"\r\n                      :show-file-list=\"false\"\r\n                      :on-success=\"handleAvatarSuccess\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      class=\"avatar-uploader\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">\r\n                        上传头像\r\n                      </el-button>\r\n                    </el-upload>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"success\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"showImage(currentEmployee.pic_path)\"\r\n                    >\r\n                      查看\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"removeAvatar\"\r\n                    >\r\n                      删除\r\n                    </el-button>\r\n                  </div>\r\n                  <div class=\"view-buttons\" v-else>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"success\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"showImage(currentEmployee.pic_path)\"\r\n                    >\r\n                      查看头像\r\n                    </el-button>\r\n                    <span v-else class=\"no-avatar-text\">暂无头像</span>\r\n                  </div>\r\n                  <div class=\"upload-tip\" v-if=\"!isViewMode\">建议尺寸：330px × 300px</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作记录 -->\r\n            <div class=\"form-section\" v-if=\"currentEmployee.id\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-time\"></i>\r\n                操作记录\r\n              </div>\r\n\r\n              <div class=\"operation-record\">\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">创建时间：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.create_time }}</span>\r\n                </div>\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">最后更新：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.update_time || '暂无' }}</span>\r\n                </div>\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">员工ID：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.id }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"quick-actions\" v-if=\"!isViewMode\">\r\n                <el-button\r\n                  type=\"warning\"\r\n                  size=\"small\"\r\n                  @click=\"resetPassword\"\r\n                  icon=\"el-icon-key\"\r\n                >\r\n                  重置密码\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"small\"\r\n                  @click=\"deleteEmployee\"\r\n                  icon=\"el-icon-delete\"\r\n                >\r\n                  删除员工\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 原有对话框保留用于新增 -->\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"职位类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhiwei_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.zhiwei_id\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n          >\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in zhiweis\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '名称'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '手机'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '账号'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"account\"\r\n        >\r\n          <el-input v-model=\"ruleForm.account\" autocomplete=\"off\">\r\n            <template slot=\"append\">默认密码888888</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"头像\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n          <div class=\"el-upload__tip\">330rpx*300rpx</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      viewMode: 'table', // table | card\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 0,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        zhiwei_id: \"\",\r\n        status: \"\"\r\n      },\r\n      loading: true,\r\n      url: \"/Yuangong/\",\r\n      title: \"员工\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        pic_path: \"\",\r\n        account: \"\",\r\n        phone: \"\",\r\n        zhiwei_id: \"\",\r\n        status: 1\r\n      },\r\n      field: \"\",\r\n      zhiweis: [],\r\n      // 右侧面板相关\r\n      detailPanelVisible: false,\r\n      saving: false,\r\n      isViewMode: true, // 默认为查看模式\r\n      originalEmployee: {}, // 保存原始数据，用于取消编辑时恢复\r\n      currentEmployee: {\r\n        id: null,\r\n        title: \"\",\r\n        account: \"\",\r\n        phone: \"\",\r\n        pic_path: \"\",\r\n        zhiwei_id: \"\",\r\n        zhiwei_title: \"\",\r\n        status: 1,\r\n        join_date: \"\",\r\n        create_time: \"\",\r\n        update_time: \"\"\r\n      },\r\n      detailRules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写员工姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写登录账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写手机号码\",\r\n            trigger: \"blur\",\r\n          },\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写员工姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传头像\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 管理员数量\r\n    adminCount() {\r\n      return this.list.filter(item =>\r\n        item.zhiwei_title && (\r\n          item.zhiwei_title.includes('管理员') ||\r\n          item.zhiwei_title.includes('经理') ||\r\n          item.zhiwei_title.includes('主管')\r\n        )\r\n      ).length;\r\n    },\r\n    // 在职员工数量\r\n    activeCount() {\r\n      return this.list.filter(item => item.status === 1).length;\r\n    },\r\n    // 本月新增员工数量\r\n    newCount() {\r\n      const currentMonth = new Date().getMonth() + 1;\r\n      return this.list.filter(item => {\r\n        if (!item.create_time) return false;\r\n        const itemMonth = new Date(item.create_time).getMonth() + 1;\r\n        return itemMonth === currentMonth;\r\n      }).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取职位标签类型\r\n    getPositionTagType(position) {\r\n      if (!position) return 'info';\r\n      if (position.includes('管理员') || position.includes('经理')) return 'danger';\r\n      if (position.includes('主管') || position.includes('专员')) return 'warning';\r\n      if (position.includes('助理') || position.includes('客服')) return 'success';\r\n      return 'primary';\r\n    },\r\n\r\n    // 状态切换\r\n    changeStatus(row) {\r\n      this.$message.success(`员工\"${row.title}\"状态已${row.status ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        zhiwei_id: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      this.$message.success('导出功能开发中...');\r\n    },\r\n\r\n    // 显示员工详情面板（查看模式）\r\n    showEmployeeDetail(employee) {\r\n      this.currentEmployee = {\r\n        ...employee,\r\n        join_date: employee.join_date || employee.create_time?.split(' ')[0] || ''\r\n      };\r\n      this.originalEmployee = { ...this.currentEmployee }; // 保存原始数据\r\n      this.isViewMode = true; // 设置为查看模式\r\n      this.detailPanelVisible = true;\r\n\r\n      // 确保职位数据已加载\r\n      if (this.zhiweis.length === 0) {\r\n        this.getZhiwei();\r\n      }\r\n    },\r\n\r\n    // 显示员工编辑面板（编辑模式）\r\n    showEmployeeEdit(employee) {\r\n      this.currentEmployee = {\r\n        ...employee,\r\n        join_date: employee.join_date || employee.create_time?.split(' ')[0] || ''\r\n      };\r\n      this.originalEmployee = { ...this.currentEmployee }; // 保存原始数据\r\n      this.isViewMode = false; // 设置为编辑模式\r\n      this.detailPanelVisible = true;\r\n\r\n      // 确保职位数据已加载\r\n      if (this.zhiweis.length === 0) {\r\n        this.getZhiwei();\r\n      }\r\n    },\r\n\r\n    // 切换到编辑模式\r\n    switchToEditMode() {\r\n      this.isViewMode = false;\r\n    },\r\n\r\n    // 取消编辑，恢复原始数据\r\n    cancelEdit() {\r\n      this.currentEmployee = { ...this.originalEmployee };\r\n      this.isViewMode = true;\r\n    },\r\n\r\n    // 关闭详情面板\r\n    closeDetailPanel() {\r\n      this.detailPanelVisible = false;\r\n      this.saving = false;\r\n      // 重置表单\r\n      this.$nextTick(() => {\r\n        if (this.$refs.detailForm) {\r\n          this.$refs.detailForm.resetFields();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 保存员工数据\r\n    saveEmployeeData() {\r\n      this.$refs.detailForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n\r\n          // 模拟保存\r\n          setTimeout(() => {\r\n            this.saving = false;\r\n            this.$message.success('保存成功！');\r\n\r\n            // 更新列表中的数据\r\n            if (this.currentEmployee.id) {\r\n              const index = this.list.findIndex(item => item.id === this.currentEmployee.id);\r\n              if (index !== -1) {\r\n                // 更新职位标题\r\n                const zhiwei = this.zhiweis.find(z => z.id === this.currentEmployee.zhiwei_id);\r\n                this.currentEmployee.zhiwei_title = zhiwei ? zhiwei.title : '';\r\n\r\n                this.$set(this.list, index, { ...this.currentEmployee });\r\n              }\r\n            } else {\r\n              // 新增员工\r\n              const newEmployee = {\r\n                ...this.currentEmployee,\r\n                id: Date.now(), // 临时ID\r\n                create_time: new Date().toLocaleString()\r\n              };\r\n              const zhiwei = this.zhiweis.find(z => z.id === newEmployee.zhiwei_id);\r\n              newEmployee.zhiwei_title = zhiwei ? zhiwei.title : '';\r\n\r\n              this.list.unshift(newEmployee);\r\n              this.total++;\r\n            }\r\n\r\n            this.closeDetailPanel();\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 头像上传成功\r\n    handleAvatarSuccess(res) {\r\n      this.currentEmployee.pic_path = res.data.url;\r\n      this.$message.success('头像上传成功！');\r\n    },\r\n\r\n    // 删除头像\r\n    removeAvatar() {\r\n      this.$confirm('确定要删除头像吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.currentEmployee.pic_path = '';\r\n        this.$message.success('头像已删除！');\r\n      });\r\n    },\r\n\r\n    // 重置密码\r\n    resetPassword() {\r\n      this.$confirm('确定要重置该员工的密码为888888吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.$message.success('密码重置成功！');\r\n      });\r\n    },\r\n\r\n    // 删除员工\r\n    deleteEmployee() {\r\n      this.$confirm('确定要删除该员工吗？删除后不可恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        const index = this.list.findIndex(item => item.id === this.currentEmployee.id);\r\n        if (index !== -1) {\r\n          this.list.splice(index, 1);\r\n          this.total--;\r\n          this.$message.success('员工删除成功！');\r\n          this.closeDetailPanel();\r\n        }\r\n      });\r\n    },\r\n\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    chongzhi(id) {\r\n      this.$confirm(\"重置密码888888?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.postRequest(\"/yuangong/chongzhi\", { id: id }).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"重置成功!\",\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: \"重置失败!\",\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消重置!\",\r\n          });\r\n        });\r\n    },\r\n    getZhiwei() {\r\n      this.postRequest(\"/zhiwei/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.zhiweis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      if (id != 0) {\r\n        // 编辑现有员工，使用右侧面板编辑模式\r\n        const employee = this.list.find(item => item.id === id);\r\n        if (employee) {\r\n          this.showEmployeeEdit(employee);\r\n        }\r\n      } else {\r\n        // 新增员工，使用右侧面板编辑模式\r\n        this.currentEmployee = {\r\n          id: null,\r\n          title: \"\",\r\n          account: \"\",\r\n          phone: \"\",\r\n          pic_path: \"\",\r\n          zhiwei_id: \"\",\r\n          zhiwei_title: \"\",\r\n          status: 1,\r\n          join_date: \"\",\r\n          create_time: \"\",\r\n          update_time: \"\"\r\n        };\r\n        this.originalEmployee = { ...this.currentEmployee };\r\n        this.isViewMode = false; // 新增时直接进入编辑模式\r\n        this.detailPanelVisible = true;\r\n\r\n        // 确保职位数据已加载\r\n        if (this.zhiweis.length === 0) {\r\n          this.getZhiwei();\r\n        }\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"张三\",\r\n            account: \"zhangsan\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 1,\r\n            zhiwei_title: \"系统管理员\",\r\n            status: 1,\r\n            create_time: \"2024-01-01 09:00:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"李四\",\r\n            account: \"lisi\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 2,\r\n            zhiwei_title: \"业务经理\",\r\n            status: 1,\r\n            create_time: \"2024-01-02 10:30:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"王五\",\r\n            account: \"wangwu\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 3,\r\n            zhiwei_title: \"法务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-03 14:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"赵六\",\r\n            account: \"zhaoliu\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 4,\r\n            zhiwei_title: \"财务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-04 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"孙七\",\r\n            account: \"sunqi\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 5,\r\n            zhiwei_title: \"客服专员\",\r\n            status: 0,\r\n            create_time: \"2024-01-05 11:20:00\"\r\n          },\r\n          {\r\n            id: 6,\r\n            title: \"周八\",\r\n            account: \"zhouba\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 2,\r\n            zhiwei_title: \"业务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-06 08:30:00\"\r\n          }\r\n        ];\r\n        _this.total = 6;\r\n\r\n        // 同时获取职位数据用于筛选\r\n        _this.zhiweis = [\r\n          { id: 1, title: \"系统管理员\" },\r\n          { id: 2, title: \"业务经理\" },\r\n          { id: 3, title: \"法务专员\" },\r\n          { id: 4, title: \"财务专员\" },\r\n          { id: 5, title: \"客服专员\" }\r\n        ];\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.employee-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section .page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n}\r\n\r\n.search-form {\r\n  padding: 20px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.admin {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.active {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-icon.new {\r\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格样式 */\r\n.employee-table {\r\n  margin: 0;\r\n}\r\n\r\n.avatar-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.employee-avatar {\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.employee-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.employee-name-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.employee-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.employee-name.clickable {\r\n  cursor: pointer;\r\n  color: #409eff;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.employee-name.clickable:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.employee-account {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  background: #f0f2f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n  width: fit-content;\r\n}\r\n\r\n.phone-cell, .time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #606266;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  padding: 20px 0;\r\n}\r\n\r\n.employee-card-col {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.employee-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.employee-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.card-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-info {\r\n  flex: 1;\r\n}\r\n\r\n.card-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-position {\r\n  display: flex;\r\n}\r\n\r\n.card-status {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n}\r\n\r\n.card-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n}\r\n\r\n.detail-item i {\r\n  width: 16px;\r\n  color: #909399;\r\n}\r\n\r\n.card-actions {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .employee-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .employee-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .card-actions {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 右侧滑出面板 */\r\n.employee-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.employee-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 600px;\r\n  height: 100%;\r\n  background: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n.panel-header {\r\n  padding: 20px 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.panel-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-title i {\r\n  font-size: 20px;\r\n}\r\n\r\n.panel-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.employee-form {\r\n  padding: 0;\r\n}\r\n\r\n.form-section {\r\n  padding: 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.form-section:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #f0f2f5;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-col {\r\n  flex: 1;\r\n}\r\n\r\n.avatar-upload-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.current-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.preview-avatar {\r\n  cursor: pointer;\r\n  border: 2px solid #ebeef5;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.preview-avatar:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-controls {\r\n  flex: 1;\r\n}\r\n\r\n.avatar-input {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.5;\r\n}\r\n\r\n.operation-record {\r\n  background: #fafbfc;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.record-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.record-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.record-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.record-value {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.view-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-top: 12px;\r\n}\r\n\r\n.no-avatar-text {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 查看模式样式 */\r\n.employee-form .el-input.is-disabled .el-input__inner,\r\n.employee-form .el-input__inner[readonly] {\r\n  background-color: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n}\r\n\r\n.employee-form .el-switch.is-disabled {\r\n  opacity: 0.8;\r\n}\r\n\r\n.employee-form .el-date-editor.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n}\r\n\r\n/* 响应式 - 面板 */\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n  }\r\n\r\n  .form-row {\r\n    flex-direction: column;\r\n    gap: 0;\r\n  }\r\n\r\n  .avatar-upload-section {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n  }\r\n\r\n  .upload-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* 原有样式保留 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}