{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\Search.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\Search.vue", "mtime": 1748604992845}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["formatFileSize", "name", "data", "searchForm", "keyword", "fileType", "category", "date<PERSON><PERSON><PERSON>", "viewMode", "currentPage", "pageSize", "allFiles", "id", "fileName", "size", "uploadTime", "computed", "filteredFiles", "files", "toLowerCase", "filter", "file", "includes", "length", "startDate", "endDate", "fileDate", "split", "methods", "getFileIcon", "iconMap", "handleSearch", "$message", "success", "handleReset", "info", "handlePreview", "handleDownload", "handleDelete", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "index", "findIndex", "f", "splice", "catch", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/pages/archive/Search.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-search-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"关键词\">\r\n          <el-input\r\n            v-model=\"searchForm.keyword\"\r\n            placeholder=\"请输入文件名或内容关键词\"\r\n            style=\"width: 200px\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\">\r\n          <el-select v-model=\"searchForm.fileType\" placeholder=\"请选择文件类型\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"PDF文档\" value=\"pdf\" />\r\n            <el-option label=\"Word文档\" value=\"doc\" />\r\n            <el-option label=\"Excel表格\" value=\"xls\" />\r\n            <el-option label=\"图片\" value=\"image\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"分类\">\r\n          <el-select v-model=\"searchForm.category\" placeholder=\"请选择分类\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"案件文书\" value=\"案件文书\" />\r\n            <el-option label=\"合同文件\" value=\"合同文件\" />\r\n            <el-option label=\"咨询记录\" value=\"咨询记录\" />\r\n            <el-option label=\"法律意见书\" value=\"法律意见书\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"时间范围\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">\r\n            <i class=\"el-icon-search\"></i> 搜索\r\n          </el-button>\r\n          <el-button @click=\"handleReset\">\r\n            <i class=\"el-icon-refresh\"></i> 重置\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 搜索结果 -->\r\n    <div class=\"search-results\">\r\n      <div class=\"result-header\">\r\n        <span class=\"result-count\">共找到 {{ filteredFiles.length }} 个文件</span>\r\n        <div class=\"view-mode\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"list\">列表视图</el-radio-button>\r\n            <el-radio-button label=\"grid\">网格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 列表视图 -->\r\n      <div v-if=\"viewMode === 'list'\" class=\"list-view\">\r\n        <el-table :data=\"filteredFiles\" style=\"width: 100%\">\r\n          <el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"getFileIcon(scope.row.fileType)\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.fileName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"category\" label=\"分类\" width=\"120\" />\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatFileSize(scope.row.size) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"160\" />\r\n          <el-table-column label=\"操作\" width=\"240\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"primary\"\r\n                  @click=\"handlePreview(scope.row)\">\r\n                  预览\r\n                </el-button>\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"success\" \r\n                  @click=\"handleDownload(scope.row)\">\r\n                  下载\r\n                </el-button>\r\n                <el-button \r\n                  size=\"mini\" \r\n                  type=\"danger\" \r\n                  @click=\"handleDelete(scope.row)\">\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"grid-view\">\r\n        <div class=\"file-grid\">\r\n          <div\r\n            v-for=\"file in filteredFiles\"\r\n            :key=\"file.id\"\r\n            class=\"file-card\"\r\n            @click=\"handlePreview(file)\"\r\n          >\r\n            <div class=\"file-thumbnail\">\r\n              <i :class=\"getFileIcon(file.fileType)\" class=\"file-icon-large\"></i>\r\n            </div>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\" :title=\"file.fileName\">{{ file.fileName }}</div>\r\n              <div class=\"file-meta\">\r\n                <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                <span class=\"file-date\">{{ file.uploadTime.split(' ')[0] }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"filteredFiles.length\"\r\n        />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { formatFileSize } from '@/utils/fileUtils'\r\n\r\nexport default {\r\n  name: 'ArchiveSearch',\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      },\r\n      viewMode: 'list',\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      // 模拟文件数据\r\n      allFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: \"合同模板.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 1024000,\r\n          uploadTime: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: \"案件资料.docx\",\r\n          fileType: \"docx\",\r\n          category: \"案件文书\",\r\n          size: 512000,\r\n          uploadTime: \"2024-01-14 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: \"咨询记录.txt\",\r\n          fileType: \"txt\",\r\n          category: \"咨询记录\",\r\n          size: 8192,\r\n          uploadTime: \"2024-01-13 16:45:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: \"法律意见书.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"法律意见书\",\r\n          size: 2048000,\r\n          uploadTime: \"2024-01-12 09:15:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: \"证据清单.xlsx\",\r\n          fileType: \"xlsx\",\r\n          category: \"案件文书\",\r\n          size: 256000,\r\n          uploadTime: \"2024-01-11 15:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: \"委托协议.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 768000,\r\n          uploadTime: \"2024-01-10 11:20:00\"\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredFiles() {\r\n      let files = this.allFiles\r\n\r\n      // 关键词搜索\r\n      if (this.searchForm.keyword) {\r\n        const keyword = this.searchForm.keyword.toLowerCase()\r\n        files = files.filter(file => \r\n          file.fileName.toLowerCase().includes(keyword) ||\r\n          file.category.toLowerCase().includes(keyword)\r\n        )\r\n      }\r\n\r\n      // 文件类型筛选\r\n      if (this.searchForm.fileType) {\r\n        files = files.filter(file => {\r\n          if (this.searchForm.fileType === 'doc') {\r\n            return ['doc', 'docx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'xls') {\r\n            return ['xls', 'xlsx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'image') {\r\n            return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(file.fileType)\r\n          } else {\r\n            return file.fileType === this.searchForm.fileType\r\n          }\r\n        })\r\n      }\r\n\r\n      // 分类筛选\r\n      if (this.searchForm.category) {\r\n        files = files.filter(file => file.category === this.searchForm.category)\r\n      }\r\n\r\n      // 时间范围筛选\r\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\r\n        const [startDate, endDate] = this.searchForm.dateRange\r\n        files = files.filter(file => {\r\n          const fileDate = file.uploadTime.split(' ')[0]\r\n          return fileDate >= startDate && fileDate <= endDate\r\n        })\r\n      }\r\n\r\n      return files\r\n    }\r\n  },\r\n  methods: {\r\n    formatFileSize,\r\n    \r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'txt': 'el-icon-document-copy',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'gif': 'el-icon-picture'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n\r\n    handleSearch() {\r\n      this.currentPage = 1\r\n      this.$message.success('搜索完成')\r\n    },\r\n\r\n    handleReset() {\r\n      this.searchForm = {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      }\r\n      this.currentPage = 1\r\n      this.$message.info('搜索条件已重置')\r\n    },\r\n\r\n    handlePreview(file) {\r\n      this.$message.info(`预览文件: ${file.fileName}`)\r\n      // 这里可以实现文件预览功能\r\n    },\r\n\r\n    handleDownload(file) {\r\n      this.$message.success(`开始下载: ${file.fileName}`)\r\n      // 这里可以实现文件下载功能\r\n    },\r\n\r\n    handleDelete(file) {\r\n      this.$confirm(`确定要删除文件\"${file.fileName}\"吗？`, '删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 从数组中删除文件\r\n        const index = this.allFiles.findIndex(f => f.id === file.id)\r\n        if (index > -1) {\r\n          this.allFiles.splice(index, 1)\r\n          this.$message.success(`文件\"${file.fileName}\"已删除`)\r\n        }\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除')\r\n      })\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.pageSize = val\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.archive-search-container {\r\n  padding: 20px;\r\n}\r\n\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-results {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  \r\n  .el-table {\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    \r\n    .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background-color: #fafafa;\r\n          color: #606266;\r\n          font-weight: 600;\r\n          border-bottom: 1px solid #ebeef5;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f5f7fa;\r\n          }\r\n          \r\n          td {\r\n            border-bottom: 1px solid #f0f0f0;\r\n            padding: 12px 0;\r\n            vertical-align: middle;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.result-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n  \r\n  .el-button {\r\n    margin: 0;\r\n    min-width: 60px;\r\n    height: 28px;\r\n    font-size: 12px;\r\n    padding: 5px 12px;\r\n    border-radius: 4px;\r\n    \r\n    &.el-button--mini {\r\n      padding: 5px 12px;\r\n    }\r\n  }\r\n  \r\n  .el-button + .el-button {\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n/* 网格视图样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.file-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-card:hover {\r\n  border-color: #409EFF;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.file-thumbnail {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-icon-large {\r\n  font-size: 48px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-card .file-name {\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.file-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .result-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .file-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 15px;\r\n  }\r\n}\r\n</style> "], "mappings": "AAuJA,SAAAA,cAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,QAAA;MACA;MACAC,QAAA,GACA;QACAC,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA,GACA;QACAH,EAAA;QACAC,QAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,IAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACAC,cAAA;MACA,IAAAC,KAAA,QAAAP,QAAA;;MAEA;MACA,SAAAR,UAAA,CAAAC,OAAA;QACA,MAAAA,OAAA,QAAAD,UAAA,CAAAC,OAAA,CAAAe,WAAA;QACAD,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAR,QAAA,CAAAM,WAAA,GAAAG,QAAA,CAAAlB,OAAA,KACAiB,IAAA,CAAAf,QAAA,CAAAa,WAAA,GAAAG,QAAA,CAAAlB,OAAA,CACA;MACA;;MAEA;MACA,SAAAD,UAAA,CAAAE,QAAA;QACAa,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA;UACA,SAAAlB,UAAA,CAAAE,QAAA;YACA,uBAAAiB,QAAA,CAAAD,IAAA,CAAAhB,QAAA;UACA,gBAAAF,UAAA,CAAAE,QAAA;YACA,uBAAAiB,QAAA,CAAAD,IAAA,CAAAhB,QAAA;UACA,gBAAAF,UAAA,CAAAE,QAAA;YACA,4CAAAiB,QAAA,CAAAD,IAAA,CAAAhB,QAAA;UACA;YACA,OAAAgB,IAAA,CAAAhB,QAAA,UAAAF,UAAA,CAAAE,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAF,UAAA,CAAAG,QAAA;QACAY,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAf,QAAA,UAAAH,UAAA,CAAAG,QAAA;MACA;;MAEA;MACA,SAAAH,UAAA,CAAAI,SAAA,SAAAJ,UAAA,CAAAI,SAAA,CAAAgB,MAAA;QACA,OAAAC,SAAA,EAAAC,OAAA,SAAAtB,UAAA,CAAAI,SAAA;QACAW,KAAA,GAAAA,KAAA,CAAAE,MAAA,CAAAC,IAAA;UACA,MAAAK,QAAA,GAAAL,IAAA,CAAAN,UAAA,CAAAY,KAAA;UACA,OAAAD,QAAA,IAAAF,SAAA,IAAAE,QAAA,IAAAD,OAAA;QACA;MACA;MAEA,OAAAP,KAAA;IACA;EACA;EACAU,OAAA;IACA5B,cAAA;IAEA6B,YAAAxB,QAAA;MACA,MAAAyB,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAzB,QAAA;IACA;IAEA0B,aAAA;MACA,KAAAtB,WAAA;MACA,KAAAuB,QAAA,CAAAC,OAAA;IACA;IAEAC,YAAA;MACA,KAAA/B,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA,KAAAE,WAAA;MACA,KAAAuB,QAAA,CAAAG,IAAA;IACA;IAEAC,cAAAf,IAAA;MACA,KAAAW,QAAA,CAAAG,IAAA,UAAAd,IAAA,CAAAR,QAAA;MACA;IACA;IAEAwB,eAAAhB,IAAA;MACA,KAAAW,QAAA,CAAAC,OAAA,UAAAZ,IAAA,CAAAR,QAAA;MACA;IACA;IAEAyB,aAAAjB,IAAA;MACA,KAAAkB,QAAA,YAAAlB,IAAA,CAAAR,QAAA;QACA2B,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACA,MAAAC,KAAA,QAAAjC,QAAA,CAAAkC,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAlC,EAAA,KAAAS,IAAA,CAAAT,EAAA;QACA,IAAAgC,KAAA;UACA,KAAAjC,QAAA,CAAAoC,MAAA,CAAAH,KAAA;UACA,KAAAZ,QAAA,CAAAC,OAAA,OAAAZ,IAAA,CAAAR,QAAA;QACA;MACA,GAAAmC,KAAA;QACA,KAAAhB,QAAA,CAAAG,IAAA;MACA;IACA;IAEAc,iBAAAC,GAAA;MACA,KAAAxC,QAAA,GAAAwC,GAAA;IACA;IAEAC,oBAAAD,GAAA;MACA,KAAAzC,WAAA,GAAAyC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}