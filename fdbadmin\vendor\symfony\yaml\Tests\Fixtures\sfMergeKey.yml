--- %YAML:1.0
test: Simple In Place Substitution
brief: >
    If you want to reuse an entire alias, only overwriting what is different
    you can use a << in place substitution. This is not part of the official
    YAML spec, but a widely implemented extension. See the following URL for
    details: http://yaml.org/type/merge.html
yaml: |
    foo: &foo
        a: <PERSON>
        b: <PERSON>
        c: Brian
        e: notnull
    bar:
        a: before
        d: other
        e: ~
        <<: *foo
        b: new
        x: Oren
        c:
            foo: bar
            foo: ignore
            bar: foo
    bar_inline: {a: before, d: other, <<: *foo, b: new, x: Oren, c: { foo: bar, foo: ignore, bar: foo}}
    duplicate:
        foo: bar
        foo: ignore
    foo2: &foo2
        a: Ballmer
    ding: &dong [ fi, fei, fo, fam]
    check:
        <<:
            - *foo
            - *dong
        isit: tested
    head:
        <<: [ *foo , *dong , *foo2 ]
    taz: &taz
        a: Steve
        w:
            p: 1234
    nested:
        <<: *taz
        d: <PERSON>
        w: &nestedref
            p: 12345
        z:
            <<: *nestedref
    head_inline: &head_inline { <<: [ *foo , *dong , *foo2 ] }
    recursive_inline: { <<: *head_inline, c: { <<: *foo2 } }
php: |
    array(
        'foo' => array('a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull'),
        'bar' => array('a' => 'before', 'd' => 'other', 'e' => null, 'b' => 'new', 'c' => array('foo' => 'bar', 'bar' => 'foo'), 'x' => 'Oren'),
        'bar_inline' => array('a' => 'before', 'd' => 'other', 'b' => 'new', 'c' => array('foo' => 'bar', 'bar' => 'foo'), 'e' => 'notnull', 'x' => 'Oren'),
        'duplicate' => array('foo' => 'bar'),
        'foo2' => array('a' => 'Ballmer'),
        'ding' => array('fi', 'fei', 'fo', 'fam'),
        'check' => array('a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam', 'isit' => 'tested'),
        'head' => array('a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'),
        'taz' => array('a' => 'Steve', 'w' => array('p' => 1234)),
        'nested' => array('a' => 'Steve', 'w' => array('p' => 12345), 'd' => 'Doug', 'z' => array('p' => 12345)),
        'head_inline' => array('a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'),
        'recursive_inline' => array('a' => 'Steve', 'b' => 'Clark', 'c' => array('a' => 'Ballmer'), 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'),
    )
