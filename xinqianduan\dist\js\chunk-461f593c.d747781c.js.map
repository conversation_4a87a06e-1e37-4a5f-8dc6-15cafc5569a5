{"version": 3, "sources": ["webpack:///./src/components/UserDetail.vue?89c4", "webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./node_modules/core-js/modules/web.url-search-params.has.js", "webpack:///./node_modules/core-js/modules/web.url-search-params.size.js", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./src/views/pages/yonghu/user.vue?7b86", "webpack:///./node_modules/core-js/modules/web.url-search-params.delete.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./src/views/pages/yonghu/user.vue", "webpack:///src/views/pages/yonghu/user.vue", "webpack:///./src/views/pages/yonghu/user.vue?667a", "webpack:///./src/views/pages/yonghu/user.vue?bd92", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f", "webpack:///./node_modules/core-js/internals/array-reduce.js", "webpack:///./node_modules/core-js/internals/validate-arguments-length.js", "webpack:///./node_modules/core-js/internals/define-built-in-accessor.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "CHROME_BUG", "FORCED", "target", "proto", "forced", "reduce", "callbackfn", "length", "arguments", "this", "undefined", "defineBuiltIn", "uncurryThis", "toString", "validateArgumentsLength", "$URLSearchParams", "URLSearchParams", "URLSearchParamsPrototype", "prototype", "getAll", "$has", "has", "params", "name", "$value", "values", "value", "index", "enumerable", "unsafe", "DESCRIPTORS", "defineBuiltInAccessor", "for<PERSON>ach", "get", "count", "configurable", "global", "classof", "module", "exports", "process", "append", "$delete", "push", "entries", "v", "k", "key", "entry", "dindex", "found", "<PERSON><PERSON><PERSON><PERSON>", "fails", "METHOD_NAME", "argument", "method", "call", "render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "$router", "currentRoute", "staticStyle", "attrs", "on", "refulsh", "search", "model", "nickname", "callback", "$$v", "$set", "expression", "phone", "company", "yuangong_id", "linkman", "linkphone", "date<PERSON><PERSON><PERSON>", "$event", "searchData", "resetSearch", "exportSelectedData", "selectedUsers", "openUpload", "addUser", "downloadTemplate", "directives", "rawName", "loading", "ref", "list", "background", "color", "handleSortChange", "handleSelectionChange", "scopedSlots", "_u", "fn", "scope", "row", "headimg", "showImage", "viewData", "id", "getDebtCountType", "debts", "getDebtCount", "class", "getTotalDebtAmount", "formatAmount", "create_time", "last_login_time", "editData", "slot", "nativeOn", "order", "is_del", "delData", "$index", "_e", "total", "page", "size", "handleSizeChange", "handleCurrentChange", "drawerViewVisible", "handleDrawerClose", "activeTab", "handleTabSelect", "isEditMode", "toggleEditMode", "saveUserData", "cancelEdit", "currentUserInfo", "lian_name", "tiaojie_name", "fawu_name", "htsczy_name", "ls_name", "ywy_name", "end_time", "editForm", "rules", "password", "tiaojie_id", "_l", "tia<PERSON><PERSON><PERSON>", "item", "title", "fawu_id", "fawus", "lian_id", "lians", "htsczy_id", "htsczy", "ls_id", "ls", "ywy_id", "ywy", "start_time", "year", "addDebt", "status", "editDebt", "deleteDebt", "addAttachment", "attachments", "idCard", "img", "url", "uploadTime", "downloadFile", "deleteAttachment", "uploadIdCard", "license", "uploadLicense", "others", "file", "getFileIcon", "type", "formatFileSize", "uploadOthers", "drawerEditVisible", "ruleForm", "form<PERSON>abe<PERSON><PERSON>", "saveData", "dialogVisible", "show_image", "dialogFormOrder", "info", "orderForm", "rules2", "changeTaocan", "taocan_id", "taocans", "total_price", "pay_price", "desc", "saveData2", "uploadVisible", "closeUploadDialog", "uploadAction", "uploadData", "uploadSuccess", "checkFile", "submitOrderLoading2", "submitUpload", "closeDialog", "dialogViewUserDetail", "currentId", "dialogAddUser", "debtDialogTitle", "debtDialogVisible", "closeDebtDialog", "debtForm", "debtRules", "tel", "money", "saveDebt", "handleUploadSuccess", "handleUploadError", "beforeUpload", "fileList", "review", "staticRenderFns", "components", "UserDetails", "data", "$store", "getters", "GET_TOKEN", "allSize", "prop", "dialogFormVisible", "originalUserInfo", "isEditingDebt", "editingDebtIndex", "required", "message", "trigger", "pattern", "is_num", "client_id", "pay_path", "pay_type", "qishu", "taocan_year", "taocan_content", "taocan_type", "fenqi", "date", "price", "mounted", "addTestData", "methods", "getOriginalTestData", "filterTestData", "originalData", "filteredData", "toLowerCase", "filter", "user", "includes", "startDate", "Date", "endDate", "createDate", "split", "hasSearchCondition", "$message", "success", "$nextTick", "getTaocans", "_this", "$refs", "validate", "valid", "postRequest", "then", "resp", "code", "msg", "e", "getRequest", "getYuangongs", "zhiwei_id", "console", "log", "find", "JSON", "parse", "stringify", "error", "findIndex", "splice", "getInfo", "debt", "parseFloat", "debtData", "userIndex", "clearValidate", "$confirm", "confirmButtonText", "cancelButtonText", "createFileInput", "files", "handleFileUpload", "accept", "input", "document", "createElement", "multiple", "style", "display", "onchange", "Array", "from", "body", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "click", "typeName", "startsWith", "fileUrl", "URL", "createObjectURL", "setTimeout", "fileData", "toLocaleString", "warning", "revokeObjectURL", "link", "href", "download", "fileType", "bytes", "sizes", "i", "Math", "floor", "pow", "toFixed", "amount", "minimumFractionDigits", "maximumFractionDigits", "catch", "go", "getData", "val", "handleSuccess", "res", "pic_path", "isTypeTrue", "test", "delImage", "fileName", "column", "location", "keyword", "upload", "clearFiles", "response", "slice", "submit", "addVisible", "form", "mobile", "school_id", "grade_id", "class_id", "sex", "is_poor", "is_display", "number", "remark", "is_remark_option", "remark_option", "mobile_checked", "resetFields", "isExcel", "isLt10M", "err", "templateUrl", "selection", "exportUrl", "userIds", "map", "join", "batchDeleteUsers", "dangerouslyUseHTMLString", "userTable", "clearSelection", "component", "viewDebtDetail", "props", "String", "Number", "watch", "immediate", "handler", "newId", "testUserData", "imageUrl", "aCallable", "toObject", "IndexedObject", "lengthOfArrayLike", "$TypeError", "TypeError", "REDUCE_EMPTY", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "right", "passed", "makeBuiltIn", "defineProperty", "descriptor", "getter", "set", "setter", "f"], "mappings": "kHAAA,W,oCCCA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAIlBC,GAAcD,GAAWD,EAAiB,IAAMA,EAAiB,GACjEG,EAASD,IAAeH,EAAoB,UAIhDH,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,OAAQH,GAAU,CAClDI,OAAQ,SAAgBC,GACtB,IAAIC,EAASC,UAAUD,OACvB,OAAOZ,EAAQc,KAAMH,EAAYC,EAAQA,EAAS,EAAIC,UAAU,QAAKE,O,oCChBzE,IAAIC,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmBC,gBACnBC,EAA2BF,EAAiBG,UAC5CC,EAASP,EAAYK,EAAyBE,QAC9CC,EAAOR,EAAYK,EAAyBI,KAC5CC,EAAS,IAAIP,EAAiB,QAI9BO,EAAOD,IAAI,IAAK,IAAOC,EAAOD,IAAI,SAAKX,IACzCC,EAAcM,EAA0B,OAAO,SAAaM,GAC1D,IAAIhB,EAASC,UAAUD,OACnBiB,EAASjB,EAAS,OAAIG,EAAYF,UAAU,GAChD,GAAID,QAAqBG,IAAXc,EAAsB,OAAOJ,EAAKX,KAAMc,GACtD,IAAIE,EAASN,EAAOV,KAAMc,GAC1BT,EAAwBP,EAAQ,GAChC,IAAImB,EAAQb,EAASW,GACjBG,EAAQ,EACZ,MAAOA,EAAQF,EAAOlB,OACpB,GAAIkB,EAAOE,OAAaD,EAAO,OAAO,EACtC,OAAO,IACR,CAAEE,YAAY,EAAMC,QAAQ,K,kCCzBjC,IAAIC,EAAc,EAAQ,QACtBlB,EAAc,EAAQ,QACtBmB,EAAwB,EAAQ,QAEhCd,EAA2BD,gBAAgBE,UAC3Cc,EAAUpB,EAAYK,EAAyBe,SAI/CF,KAAiB,SAAUb,IAC7Bc,EAAsBd,EAA0B,OAAQ,CACtDgB,IAAK,WACH,IAAIC,EAAQ,EAEZ,OADAF,EAAQvB,MAAM,WAAcyB,OACrBA,GAETC,cAAc,EACdP,YAAY,K,oCCjBhB,IAAIQ,EAAS,EAAQ,QACjBC,EAAU,EAAQ,QAEtBC,EAAOC,QAAsC,YAA5BF,EAAQD,EAAOI,U,oFCJhC,W,oCCCA,IAAI7B,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmBC,gBACnBC,EAA2BF,EAAiBG,UAC5CuB,EAAS7B,EAAYK,EAAyBwB,QAC9CC,EAAU9B,EAAYK,EAAyB,WAC/Ce,EAAUpB,EAAYK,EAAyBe,SAC/CW,EAAO/B,EAAY,GAAG+B,MACtBrB,EAAS,IAAIP,EAAiB,eAElCO,EAAO,UAAU,IAAK,GAGtBA,EAAO,UAAU,SAAKZ,GAElBY,EAAS,KAAO,OAClBX,EAAcM,EAA0B,UAAU,SAAUM,GAC1D,IAAIhB,EAASC,UAAUD,OACnBiB,EAASjB,EAAS,OAAIG,EAAYF,UAAU,GAChD,GAAID,QAAqBG,IAAXc,EAAsB,OAAOkB,EAAQjC,KAAMc,GACzD,IAAIqB,EAAU,GACdZ,EAAQvB,MAAM,SAAUoC,EAAGC,GACzBH,EAAKC,EAAS,CAAEG,IAAKD,EAAGpB,MAAOmB,OAEjC/B,EAAwBP,EAAQ,GAChC,IAMIyC,EANAD,EAAMlC,EAASU,GACfG,EAAQb,EAASW,GACjBG,EAAQ,EACRsB,EAAS,EACTC,GAAQ,EACRC,EAAgBP,EAAQrC,OAE5B,MAAOoB,EAAQwB,EACbH,EAAQJ,EAAQjB,KACZuB,GAASF,EAAMD,MAAQA,GACzBG,GAAQ,EACRR,EAAQjC,KAAMuC,EAAMD,MACfE,IAET,MAAOA,EAASE,EACdH,EAAQJ,EAAQK,KACVD,EAAMD,MAAQA,GAAOC,EAAMtB,QAAUA,GAAQe,EAAOhC,KAAMuC,EAAMD,IAAKC,EAAMtB,SAElF,CAAEE,YAAY,EAAMC,QAAQ,K,kCC9CjC,IAAIuB,EAAQ,EAAQ,QAEpBd,EAAOC,QAAU,SAAUc,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,OAAO,GAAM,Q,yCCP7D,IAAIG,EAAS,WAAkB,IAAIC,EAAIjD,KAAKkD,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGtD,KAAKuD,QAAQC,aAAa1C,MAAM,KAAKoC,EAAG,YAAY,CAACO,YAAY,CAAC,MAAQ,SAASC,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmBC,GAAG,CAAC,MAAQV,EAAIW,UAAU,CAACX,EAAII,GAAG,UAAU,GAAGH,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcM,MAAM,CAAC,MAAQT,EAAIY,OAAO,cAAc,SAAS,CAACX,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,UAAU,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOE,SAAUC,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,WAAYI,IAAME,WAAW,sBAAsB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,UAAU,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOO,MAAOJ,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,QAASI,IAAME,WAAW,mBAAmB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,UAAU,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOQ,QAASL,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,UAAWI,IAAME,WAAW,qBAAqB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,YAAY,CAACO,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,YAAc,QAAQ,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOS,YAAaN,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,cAAeI,IAAME,WAAW,uBAAuB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,QAAQ,MAAQ,WAAWR,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,MAAQ,UAAUR,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,MAAQ,WAAW,IAAI,IAAI,IAAI,GAAGR,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,SAAS,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOU,QAASP,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,UAAWI,IAAME,WAAW,qBAAqB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,UAAU,UAAY,GAAG,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOW,UAAWR,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,YAAaI,IAAME,WAAW,uBAAuB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,iBAAiB,CAACO,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,KAAO,YAAY,kBAAkB,IAAI,oBAAoB,OAAO,kBAAkB,OAAO,OAAS,aAAa,eAAe,aAAa,KAAO,SAASI,MAAM,CAAC7C,MAAOgC,EAAIY,OAAOY,UAAWT,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIY,OAAQ,YAAaI,IAAME,WAAW,uBAAuB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,eAAe,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,iBAAiB,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAI0B,gBAAgB,CAAC1B,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,kBAAkB,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAI2B,iBAAiB,CAAC3B,EAAII,GAAG,WAAW,MAAM,IAAI,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,oBAAoBC,GAAG,CAAC,MAAQV,EAAI4B,qBAAqB,CAAC5B,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAI6B,cAAchF,OAAS,EAAI,WAAWmD,EAAI6B,cAAchF,UAAY,UAAU,OAAOoD,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,mBAAmBC,GAAG,CAAC,MAAQV,EAAI8B,aAAa,CAAC9B,EAAII,GAAG,YAAYH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQV,EAAI+B,UAAU,CAAC/B,EAAII,GAAG,YAAYH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,oBAAoBC,GAAG,CAAC,MAAQV,EAAIgC,mBAAmB,CAAChC,EAAII,GAAG,eAAe,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAACgC,WAAW,CAAC,CAACpE,KAAK,UAAUqE,QAAQ,YAAYlE,MAAOgC,EAAImC,QAASjB,WAAW,YAAYkB,IAAI,YAAY5B,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,KAAOT,EAAIqC,KAAK,QAAS,EAAK,oBAAoB,CAACC,WAAW,UAAUC,MAAM,YAAY7B,GAAG,CAAC,cAAcV,EAAIwC,iBAAiB,mBAAmBxC,EAAIyC,wBAAwB,CAACxC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,YAAY,MAAQ,KAAK,MAAQ,YAAYR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,KAAK,MAAQ,KAAK,MAAQ,YAAYR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,GAAG,MAAQ,KAAK,MAAQ,KAAK,MAAQ,UAAUiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAAE0C,EAAMC,IAAIC,SAAiC,KAAtBF,EAAMC,IAAIC,QAAgB9C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,IAAMoC,EAAMC,IAAIC,SAASrC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUH,EAAMC,IAAIC,eAAe9C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,kCAAkCF,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,WAAW,MAAQ,OAAO,SAAW,GAAG,YAAY,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsBO,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIiD,SAASJ,EAAMC,IAAII,OAAO,CAAClD,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIhC,UAAY,UAAUb,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAI3B,mBAAmBlB,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,UAAU,MAAQ,OAAO,SAAW,GAAG,YAAY,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,UAAU,MAAQ,MAAM,SAAW,GAAG,YAAY,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,YAAY,MAAQ,OAAO,SAAW,GAAG,YAAY,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,YAAY,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIzB,aAAe,kBAAkBpB,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,YAAY,MAAM,MAAQ,UAAUiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAOT,EAAImD,iBAAiBN,EAAMC,IAAIM,SAAS,CAACpD,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIqD,aAAaR,EAAMC,IAAIM,QAAQ,eAAenD,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,YAAY,MAAM,MAAQ,UAAUiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,OAAO,CAACE,YAAY,cAAcmD,MAAM,CAAE,WAAYtD,EAAIuD,mBAAmBV,EAAMC,IAAIM,OAAS,IAAK,CAACpD,EAAII,GAAG,KAAKJ,EAAIK,GAAGL,EAAIwD,aAAaxD,EAAIuD,mBAAmBV,EAAMC,IAAIM,SAAS,cAAcnD,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,WAAW,MAAQ,OAAO,YAAY,MAAM,SAAW,MAAMR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,SAAW,GAAG,YAAY,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,OAAO,CAACD,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIW,uBAAuBxD,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,OAAO,YAAY,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,OAAO,CAACD,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIY,iBAAmB,iBAAiBzD,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIiD,SAASJ,EAAMC,IAAII,OAAO,CAAClD,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAI2D,SAASd,EAAMC,IAAII,OAAO,CAAClD,EAAII,GAAG,UAAUH,EAAG,cAAc,CAACQ,MAAM,CAAC,QAAU,UAAU,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,CAACT,EAAII,GAAG,OAAOH,EAAG,IAAI,CAACE,YAAY,wCAAwCF,EAAG,mBAAmB,CAACQ,MAAM,CAAC,KAAO,YAAYmD,KAAK,YAAY,CAAC3D,EAAG,mBAAmB,CAAC4D,SAAS,CAAC,MAAQ,SAASpC,GAAQ,OAAOzB,EAAI8D,MAAMjB,EAAMC,QAAQ,CAAC9C,EAAII,GAAG,UAAWJ,EAAI+D,OAAQ9D,EAAG,mBAAmB,CAACO,YAAY,CAAC,MAAQ,WAAWqD,SAAS,CAAC,MAAQ,SAASpC,GAAQ,OAAOzB,EAAIgE,QAAQnB,EAAMoB,OAAQpB,EAAMC,IAAII,OAAO,CAAClD,EAAII,GAAG,UAAUJ,EAAIkE,MAAM,IAAI,IAAI,WAAW,IAAI,GAAGjE,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,OAAO,CAACD,EAAII,GAAG,KAAKJ,EAAIK,GAAGL,EAAImE,OAAO,QAAQlE,EAAG,OAAO,CAACD,EAAII,GAAG,KAAKJ,EAAIK,GAAGL,EAAIoE,MAAM,UAAUnE,EAAG,gBAAgB,CAACQ,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,GAAI,KAAK,YAAYT,EAAIqE,KAAK,eAAerE,EAAIoE,KAAK,OAAS,mCAAmC,MAAQpE,EAAImE,MAAM,WAAa,IAAIzD,GAAG,CAAC,cAAcV,EAAIsE,iBAAiB,iBAAiBtE,EAAIuE,wBAAwB,KAAKtE,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIwE,kBAAkB,UAAY,MAAM,KAAO,MAAM,eAAexE,EAAIyE,mBAAmB/D,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIwE,kBAAkB/C,KAAU,CAACxB,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcM,MAAM,CAAC,iBAAiBT,EAAI0E,WAAWhE,GAAG,CAAC,OAASV,EAAI2E,kBAAkB,CAAC1E,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,aAAa,CAACR,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACD,EAAII,GAAG,YAAYH,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,WAAW,CAACR,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACD,EAAII,GAAG,YAAYH,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,UAAU,CAACR,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACD,EAAII,GAAG,aAAaH,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,gBAAgB,CAACR,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,OAAO,CAACD,EAAII,GAAG,aAAa,IAAI,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAoB,aAAlBH,EAAI0E,UAA0BzE,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAOT,EAAI4E,WAAa,eAAiB,gBAAgBlE,GAAG,CAAC,MAAQV,EAAI6E,iBAAiB,CAAC7E,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAI4E,WAAa,OAAS,QAAQ,OAAQ5E,EAAI4E,WAAY3E,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,iBAAiBC,GAAG,CAAC,MAAQV,EAAI8E,eAAe,CAAC9E,EAAII,GAAG,UAAUJ,EAAIkE,KAAMlE,EAAI4E,WAAY3E,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,iBAAiBC,GAAG,CAAC,MAAQV,EAAI+E,aAAa,CAAC/E,EAAII,GAAG,UAAUJ,EAAIkE,MAAM,GAAGlE,EAAIkE,KAAwB,aAAlBlE,EAAI0E,UAA0BzE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,YAAcJ,EAAI4E,WAAqvE5E,EAAIkE,KAA7uEjE,EAAG,kBAAkB,CAACQ,MAAM,CAAC,OAAS,EAAE,OAAS,KAAK,CAACR,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB5D,SAAW,OAAO,OAAOnB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB7D,OAAS,OAAO,OAAOlB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBlE,UAAY,OAAO,OAAOb,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB1D,SAAW,OAAO,OAAOrB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBzD,WAAa,OAAO,OAAOtB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB3D,aAAe,YAAY,GAAGpB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBC,WAAa,OAAO,OAAOhF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBE,cAAgB,OAAO,OAAOjF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBG,WAAa,OAAO,OAAOlF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,WAAW,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBI,aAAe,OAAO,OAAOnF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBK,SAAW,OAAO,OAAOpF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBM,UAAY,OAAO,OAAOrF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAEH,EAAIgF,gBAAgBjC,SAA2C,KAAhC/C,EAAIgF,gBAAgBjC,QAAgB9C,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,IAAMT,EAAIgF,gBAAgBjC,SAASrC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIgF,gBAAgBjC,aAAa9C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,OAAO,CAACD,EAAII,GAAG,eAAeH,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,IAAI,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBvB,aAAe,MAAM,OAAOxD,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,IAAI,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBtB,iBAAmB,QAAQ,OAAOzD,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,IAAI,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBO,UAAY,OAAO,QAAQ,GAAavF,EAAI4E,WAAY3E,EAAG,UAAU,CAACmC,IAAI,WAAW3B,MAAM,CAAC,MAAQT,EAAIwF,SAAS,MAAQxF,EAAIyF,MAAM,cAAc,UAAU,CAACxF,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,YAAY,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,WAAWI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASpE,QAASL,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,UAAWxE,IAAME,WAAW,uBAAuB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,UAAU,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,SAAS,SAAW,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASrE,MAAOJ,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,QAASxE,IAAME,WAAW,qBAAqB,IAAI,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,WAAWI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAAS1E,SAAUC,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,WAAYxE,IAAME,WAAW,wBAAwB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,YAAY,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,UAAUI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASlE,QAASP,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,UAAWxE,IAAME,WAAW,uBAAuB,IAAI,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,cAAc,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,WAAWI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASjE,UAAWR,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,YAAaxE,IAAME,WAAW,yBAAyB,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,gBAAgB,KAAO,YAAYI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASE,SAAU3E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,WAAYxE,IAAME,WAAW,wBAAwB,IAAI,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,eAAe,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,SAAS,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASG,WAAY5E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,aAAcxE,IAAME,WAAW,wBAAwB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAI6F,UAAU,SAASC,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,YAAY,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,UAAU,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASQ,QAASjF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,UAAWxE,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIiG,OAAO,SAASH,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,YAAY,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,UAAU,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASU,QAASnF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,UAAWxE,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAImG,OAAO,SAASL,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,KAAO,cAAc,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,YAAY,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASY,UAAWrF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,YAAaxE,IAAME,WAAW,uBAAuB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIqG,QAAQ,SAASP,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,UAAU,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,QAAQ,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASc,MAAOvF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,QAASxE,IAAME,WAAW,mBAAmB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIuG,IAAI,SAAST,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,WAAW,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,SAAS,WAAa,GAAG,UAAY,IAAII,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASgB,OAAQzF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,SAAUxE,IAAME,WAAW,oBAAoB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIyG,KAAK,SAASX,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,IAAI,IAAI,GAAGjD,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,CAACR,EAAG,iBAAiB,CAACQ,MAAM,CAAC,KAAO,OAAO,OAAS,aAAa,eAAe,aAAa,YAAc,UAAUI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASkB,WAAY3F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,aAAcxE,IAAME,WAAW,0BAA0B,IAAI,GAAGjB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,IAAM,EAAE,IAAM,GAAG,YAAc,WAAWI,MAAM,CAAC7C,MAAOgC,EAAIwF,SAASmB,KAAM5F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIwF,SAAU,OAAQxE,IAAME,WAAW,oBAAoB,IAAI,IAAI,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAACR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAEH,EAAIwF,SAASzC,SAAoC,KAAzB/C,EAAIwF,SAASzC,QAAgB9C,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,IAAMT,EAAIwF,SAASzC,SAASrC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIwF,SAASzC,aAAa9C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,OAAO,CAACD,EAAII,GAAG,gBAAgB,GAAGJ,EAAIkE,MAAM,KAAKlE,EAAIkE,KAAwB,WAAlBlE,EAAI0E,UAAwBzE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBH,EAAII,GAAG,YAAYH,EAAG,kBAAkB,CAACQ,MAAM,CAAC,OAAS,EAAE,OAAS,KAAK,CAACR,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB0B,YAAc,OAAO,OAAOzG,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgB2B,MAAQ,GAAG,QAAQ1G,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBvB,aAAe,MAAM,OAAOxD,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBtB,iBAAmB,QAAQ,OAAOzD,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIgF,gBAAgBO,UAAY,OAAO,QAAQ,IAAI,KAAKvF,EAAIkE,KAAwB,UAAlBlE,EAAI0E,UAAuBzE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBH,EAAII,GAAG,WAAWH,EAAG,YAAY,CAACO,YAAY,CAAC,MAAQ,SAASC,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQC,GAAG,CAAC,MAAQV,EAAI4G,UAAU,CAAC3G,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,cAAc,GAAIJ,EAAIgF,gBAAgB5B,OAASpD,EAAIgF,gBAAgB5B,MAAMvG,OAAS,EAAGoD,EAAG,WAAW,CAACO,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,KAAOT,EAAIgF,gBAAgB5B,OAAS,GAAG,KAAO,UAAU,CAACnD,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,UAAU,MAAQ,SAASR,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAA4B,QAArBoC,EAAMC,IAAI+D,OAAmB,UAAiC,QAArBhE,EAAMC,IAAI+D,OAAmB,UAAY,OAAO,KAAO,UAAU,CAAC7G,EAAII,GAAG,IAAIJ,EAAIK,GAAGwC,EAAMC,IAAI+D,QAAQ,WAAW,MAAK,EAAM,cAAc5G,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAI8G,SAASjE,EAAMC,IAAKD,EAAMoB,WAAW,CAACjE,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAI+G,WAAWlE,EAAMoB,WAAW,CAACjE,EAAII,GAAG,YAAY,MAAK,EAAM,eAAe,GAAGH,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACD,EAAII,GAAG,aAAaH,EAAG,MAAMA,EAAG,YAAY,CAACO,YAAY,CAAC,aAAa,QAAQC,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQV,EAAI4G,UAAU,CAAC3G,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,iBAAiB,IAAI,KAAKJ,EAAIkE,KAAwB,gBAAlBlE,EAAI0E,UAA6BzE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BH,EAAII,GAAG,UAAUH,EAAG,YAAY,CAACO,YAAY,CAAC,MAAQ,SAASC,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQC,GAAG,CAAC,MAAQV,EAAIgH,gBAAgB,CAAC/G,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,aAAa,GAAGH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACH,EAAII,GAAG,WAAWH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAAEH,EAAIgF,gBAAgBiC,aAAejH,EAAIgF,gBAAgBiC,YAAYC,QAAUlH,EAAIgF,gBAAgBiC,YAAYC,OAAOrK,OAAS,EAAGoD,EAAG,MAAM,CAACE,YAAY,cAAcH,EAAI4F,GAAI5F,EAAIgF,gBAAgBiC,YAAYC,QAAQ,SAASC,EAAIlJ,GAAO,OAAOgC,EAAG,MAAM,CAACZ,IAAIpB,EAAMkC,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmBM,MAAM,CAAC,IAAM0G,EAAIC,KAAK1G,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUmE,EAAIC,SAASnH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAG8G,EAAItJ,SAASoC,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAG8G,EAAIE,iBAAiBpH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIsH,aAAaH,MAAQ,CAACnH,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIuH,iBAAiB,SAAUtJ,MAAU,CAAC+B,EAAII,GAAG,SAAS,UAAS,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,OAAO,CAACD,EAAII,GAAG,eAAeH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQV,EAAIwH,eAAe,CAACvH,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,cAAc,KAAKH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAAEH,EAAIgF,gBAAgBiC,aAAejH,EAAIgF,gBAAgBiC,YAAYQ,SAAWzH,EAAIgF,gBAAgBiC,YAAYQ,QAAQ5K,OAAS,EAAGoD,EAAG,MAAM,CAACE,YAAY,cAAcH,EAAI4F,GAAI5F,EAAIgF,gBAAgBiC,YAAYQ,SAAS,SAASN,EAAIlJ,GAAO,OAAOgC,EAAG,MAAM,CAACZ,IAAIpB,EAAMkC,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmBM,MAAM,CAAC,IAAM0G,EAAIC,KAAK1G,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUmE,EAAIC,SAASnH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAG8G,EAAItJ,SAASoC,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAG8G,EAAIE,iBAAiBpH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIsH,aAAaH,MAAQ,CAACnH,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIuH,iBAAiB,UAAWtJ,MAAU,CAAC+B,EAAII,GAAG,SAAS,UAAS,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACD,EAAII,GAAG,cAAcH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQV,EAAI0H,gBAAgB,CAACzH,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,eAAe,KAAKH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAAEH,EAAIgF,gBAAgBiC,aAAejH,EAAIgF,gBAAgBiC,YAAYU,QAAU3H,EAAIgF,gBAAgBiC,YAAYU,OAAO9K,OAAS,EAAGoD,EAAG,MAAM,CAACE,YAAY,aAAaH,EAAI4F,GAAI5F,EAAIgF,gBAAgBiC,YAAYU,QAAQ,SAASC,EAAK3J,GAAO,OAAOgC,EAAG,MAAM,CAACZ,IAAIpB,EAAMkC,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBmD,MAAMtD,EAAI6H,YAAYD,EAAKE,UAAU7H,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGuH,EAAK/J,SAASoC,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAI+H,eAAeH,EAAKvD,UAAUpE,EAAG,OAAO,CAACE,YAAY,eAAe,CAACH,EAAII,GAAGJ,EAAIK,GAAGuH,EAAKP,mBAAmBpH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIsH,aAAaM,MAAS,CAAC5H,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIuH,iBAAiB,SAAUtJ,MAAU,CAAC+B,EAAII,GAAG,SAAS,QAAO,GAAGH,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,OAAO,CAACD,EAAII,GAAG,cAAcH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,SAASC,GAAG,CAAC,MAAQV,EAAIgI,eAAe,CAAC/H,EAAG,IAAI,CAACE,YAAY,mBAAmBH,EAAII,GAAG,eAAe,WAAWJ,EAAIkE,WAAWjE,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIiI,kBAAkB,UAAY,MAAM,KAAO,MAAM,eAAejI,EAAIyE,mBAAmB/D,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIiI,kBAAkBxG,KAAU,CAACxB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,YAAYH,EAAG,kBAAkB,CAACQ,MAAM,CAAC,OAAS,EAAE,OAAS,KAAK,CAACR,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIkI,SAAS9G,SAAS,OAAOnB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIkI,SAAS/G,OAAO,OAAOlB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIkI,SAASpH,UAAU,OAAOb,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIkI,SAAS5G,SAAS,OAAOrB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAEH,EAAIkI,SAASnF,SAAoC,KAAzB/C,EAAIkI,SAASnF,QAAgB9C,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,IAAMT,EAAIkI,SAASnF,SAASrC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIkI,SAASnF,aAAa9C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,OAAO,CAACD,EAAII,GAAG,eAAeH,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,IAAI,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIkI,SAAS7G,aAAe,YAAY,IAAI,IAAI,GAAGpB,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,YAAYH,EAAG,UAAU,CAACmC,IAAI,WAAW3B,MAAM,CAAC,MAAQT,EAAIkI,SAAS,MAAQlI,EAAIyF,MAAM,cAAc,UAAU,CAACxF,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS9G,QAASL,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,uBAAuB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS5G,QAASP,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,uBAAuB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS3G,UAAWR,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,YAAalH,IAAME,WAAW,yBAAyB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASxC,SAAU3E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,WAAYlH,IAAME,WAAW,wBAAwB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,aAAa,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASvC,WAAY5E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,aAAclH,IAAME,WAAW,wBAAwB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAI6F,UAAU,SAASC,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASlC,QAASjF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIiG,OAAO,SAASH,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAShC,QAASnF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAImG,OAAO,SAASL,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,KAAO,YAAY,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS9B,UAAWrF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,YAAalH,IAAME,WAAW,uBAAuB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIqG,QAAQ,SAASP,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,QAAQ,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS5B,MAAOvF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,QAASlH,IAAME,WAAW,mBAAmB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIuG,IAAI,SAAST,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,SAAS,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS1B,OAAQzF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,SAAUlH,IAAME,WAAW,oBAAoB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIyG,KAAK,SAASX,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,cAAcT,EAAImI,eAAe,MAAQ,SAAS,CAAyB,IAAvBnI,EAAIkI,SAAST,SAAsC,MAAtBzH,EAAIkI,SAAST,QAAexH,EAAG,MAAM,CAACO,YAAY,CAAC,MAAQ,QAAQ,OAAS,SAASC,MAAM,CAAC,IAAMT,EAAIkI,SAAST,SAAS/G,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIkI,SAAST,aAAazH,EAAIkE,OAAOjE,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,eAAe,KAAO,QAAQ,CAAClI,EAAG,iBAAiB,CAACQ,MAAM,CAAC,KAAO,OAAO,OAAS,aAAa,eAAe,aAAa,YAAc,QAAQI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASxB,WAAY3F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,aAAclH,IAAME,WAAW,0BAA0B,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,kBAAkB,CAACQ,MAAM,CAAC,IAAM,EAAE,IAAM,GAAG,MAAQ,SAASI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASvB,KAAM5F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,OAAQlH,IAAME,WAAW,oBAAoB,IAAI,GAAGjB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASe,GAAQzB,EAAIiI,mBAAoB,KAAS,CAACjI,EAAII,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIoI,cAAc,CAACpI,EAAII,GAAG,UAAU,IAAI,OAAOH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIqI,cAAc,MAAQ,OAAO3H,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIqI,cAAc5G,KAAU,CAACxB,EAAG,WAAW,CAACQ,MAAM,CAAC,IAAMT,EAAIsI,eAAe,GAAGrI,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIuI,gBAAgB,wBAAuB,GAAO7H,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIuI,gBAAgB9G,KAAU,CAACxB,EAAG,SAAS,CAACA,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKpH,SAAS,OAAOnB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKrH,OAAO,OAAOlB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAK1H,UAAU,OAAOb,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKlH,SAAS,OAAOrB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAAqB,IAAnBT,EAAIwI,KAAKzF,SAAkC,MAAlB/C,EAAIwI,KAAKzF,QAAe9C,EAAG,MAAM,CAACO,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQC,MAAM,CAAC,IAAMT,EAAIwI,KAAKzF,SAASrC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIkI,SAASnF,aAAa/C,EAAIkE,OAAOjE,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKnH,aAAa,OAAOpB,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKtD,cAAc,OAAOjF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKrD,WAAW,OAAOlF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKvD,WAAW,OAAOhF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,WAAW,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKpD,aAAa,OAAOnF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKnD,SAAS,OAAOpF,EAAG,uBAAuB,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACT,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKlD,UAAU,QAAQ,IAAI,GAAGrF,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,UAAUR,EAAG,UAAU,CAACmC,IAAI,YAAY3B,MAAM,CAAC,MAAQT,EAAIyI,UAAU,MAAQzI,EAAI0I,OAAO,cAAc,OAAO,KAAO,SAAS,CAACzI,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,cAAc,CAACR,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,OAAOC,GAAG,CAAC,OAASV,EAAI2I,cAAc9H,MAAM,CAAC7C,MAAOgC,EAAIyI,UAAUG,UAAW7H,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIyI,UAAW,YAAazH,IAAME,WAAW,wBAAwB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAI6I,SAAS,SAAS/C,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,QAAQ,CAACR,EAAG,WAAW,CAACE,YAAY,YAAYM,MAAM,CAAC,KAAO,SAAS,YAAc,SAASI,MAAM,CAAC7C,MAAOgC,EAAIyI,UAAUK,YAAa/H,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIyI,UAAW,cAAezH,IAAME,WAAW,4BAA4B,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,cAAc,CAACR,EAAG,WAAW,CAACE,YAAY,YAAYM,MAAM,CAAC,YAAc,SAASI,MAAM,CAAC7C,MAAOgC,EAAIyI,UAAUM,UAAWhI,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIyI,UAAW,YAAazH,IAAME,WAAW,0BAA0B,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,CAACR,EAAG,WAAW,CAACE,YAAY,YAAYM,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,SAASI,MAAM,CAAC7C,MAAOgC,EAAIyI,UAAUO,KAAMjI,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIyI,UAAW,OAAQzH,IAAME,WAAW,qBAAqB,IAAI,GAAGjB,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASe,GAAQzB,EAAIuI,iBAAkB,KAAS,CAACvI,EAAII,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIiJ,eAAe,CAACjJ,EAAII,GAAG,UAAU,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIkJ,cAAc,MAAQ,OAAOxI,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIkJ,cAAczH,GAAQ,MAAQzB,EAAImJ,oBAAoB,CAAClJ,EAAG,UAAU,CAACmC,IAAI,aAAa3B,MAAM,CAAC,iBAAiB,QAAQ,cAAc,UAAU,CAACR,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,UAAU,CAACR,EAAG,YAAY,CAACmC,IAAI,SAAS3B,MAAM,CAAC,eAAc,EAAM,OAAST,EAAIoJ,aAAa,KAAOpJ,EAAIqJ,WAAW,aAAarJ,EAAIsJ,cAAc,gBAAgBtJ,EAAIuJ,UAAU,OAAS,aAAa,MAAQ,IAAI,SAAW,UAAU,CAACtJ,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,WAAWmD,KAAK,WAAW,CAAC5D,EAAII,GAAG,WAAW,IAAI,GAAGH,EAAG,MAAM,CAACO,YAAY,CAAC,aAAa,UAAU,CAACP,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,QAAUT,EAAIwJ,qBAAqB9I,GAAG,CAAC,MAAQV,EAAIyJ,eAAe,CAACzJ,EAAII,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,SAASC,GAAG,CAAC,MAAQV,EAAI0J,cAAc,CAAC1J,EAAII,GAAG,SAAS,IAAI,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAI2J,qBAAqB,wBAAuB,EAAM,MAAQ,OAAOjJ,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAI2J,qBAAqBlI,KAAU,CAACxB,EAAG,eAAe,CAACQ,MAAM,CAAC,GAAKT,EAAI4J,cAAc,GAAG3J,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAI6J,cAAc,wBAAuB,EAAM,MAAQ,OAAOnJ,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAI6J,cAAcpI,KAAU,CAACxB,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,UAAUR,EAAG,UAAU,CAACmC,IAAI,WAAW3B,MAAM,CAAC,MAAQT,EAAIkI,SAAS,MAAQlI,EAAIyF,QAAQ,CAACxF,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS/G,MAAOJ,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,QAASlH,IAAME,WAAW,qBAAqB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS9G,QAASL,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,uBAAuB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS5G,QAASP,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,uBAAuB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS3G,UAAWR,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,YAAalH,IAAME,WAAW,yBAAyB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,WAAW,CAACQ,MAAM,CAAC,aAAe,OAAOI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASxC,SAAU3E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,WAAYlH,IAAME,WAAW,wBAAwB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,aAAa,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASvC,WAAY5E,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,aAAclH,IAAME,WAAW,wBAAwB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAI6F,UAAU,SAASC,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASlC,QAASjF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIiG,OAAO,SAASH,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAShC,QAASnF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,UAAWlH,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAImG,OAAO,SAASL,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,SAAS,KAAO,YAAY,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS9B,UAAWrF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,YAAalH,IAAME,WAAW,uBAAuB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIqG,QAAQ,SAASP,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,QAAQ,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS5B,MAAOvF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,QAASlH,IAAME,WAAW,mBAAmB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIuG,IAAI,SAAST,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,MAAM,KAAO,SAAS,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,YAAY,CAACQ,MAAM,CAAC,YAAc,MAAM,WAAa,IAAII,MAAM,CAAC7C,MAAOgC,EAAIkI,SAAS1B,OAAQzF,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,SAAUlH,IAAME,WAAW,oBAAoB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAII,GAAG,SAASJ,EAAI4F,GAAI5F,EAAIyG,KAAK,SAASX,EAAK7H,GAAO,OAAOgC,EAAG,YAAY,CAACZ,IAAIpB,EAAMwC,MAAM,CAAC,MAAQqF,EAAKC,MAAM,MAAQD,EAAK5C,UAAS,IAAI,GAAGjD,EAAG,eAAe,CAACQ,MAAM,CAAC,cAAcT,EAAImI,eAAe,MAAQ,SAAS,CAAyB,IAAvBnI,EAAIkI,SAAST,SAAsC,MAAtBzH,EAAIkI,SAAST,QAAexH,EAAG,MAAM,CAACO,YAAY,CAAC,MAAQ,QAAQ,OAAS,SAASC,MAAM,CAAC,IAAMT,EAAIkI,SAAST,SAAS/G,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIkI,SAAST,aAAazH,EAAIkE,OAAOjE,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,eAAe,KAAO,QAAQ,CAAClI,EAAG,iBAAiB,CAACQ,MAAM,CAAC,KAAO,OAAO,OAAS,aAAa,eAAe,aAAa,YAAc,QAAQI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASxB,WAAY3F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,aAAclH,IAAME,WAAW,0BAA0B,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,cAAcT,EAAImI,iBAAiB,CAAClI,EAAG,kBAAkB,CAACQ,MAAM,CAAC,IAAM,EAAE,IAAM,GAAG,MAAQ,SAASI,MAAM,CAAC7C,MAAOgC,EAAIkI,SAASvB,KAAM5F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIkI,SAAU,OAAQlH,IAAME,WAAW,oBAAoB,IAAI,GAAGjB,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQ,SAASe,GAAQzB,EAAI6J,eAAgB,KAAS,CAAC7J,EAAII,GAAG,SAASH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIoI,cAAc,CAACpI,EAAII,GAAG,UAAU,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,QAAUT,EAAIqI,cAAc,MAAQ,MAAM,OAAS,IAAI3H,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIqI,cAAc5G,KAAU,CAACxB,EAAG,MAAM,CAACO,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQC,MAAM,CAAC,IAAMT,EAAIsI,gBAAgBrI,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQT,EAAI8J,gBAAgB,QAAU9J,EAAI+J,kBAAkB,MAAQ,SAASrJ,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAI+J,kBAAkBtI,GAAQ,MAAQzB,EAAIgK,kBAAkB,CAAC/J,EAAG,UAAU,CAACmC,IAAI,WAAW3B,MAAM,CAAC,MAAQT,EAAIiK,SAAS,MAAQjK,EAAIkK,UAAU,cAAc,UAAU,CAACjK,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,QAAQ,KAAO,SAAS,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,YAAYI,MAAM,CAAC7C,MAAOgC,EAAIiK,SAASpM,KAAMkD,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIiK,SAAU,OAAQjJ,IAAME,WAAW,oBAAoB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,QAAQ,KAAO,QAAQ,CAACR,EAAG,WAAW,CAACQ,MAAM,CAAC,YAAc,YAAYI,MAAM,CAAC7C,MAAOgC,EAAIiK,SAASE,IAAKpJ,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIiK,SAAU,MAAOjJ,IAAME,WAAW,mBAAmB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACR,EAAG,kBAAkB,CAACO,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,IAAM,EAAE,UAAY,EAAE,YAAc,WAAWI,MAAM,CAAC7C,MAAOgC,EAAIiK,SAASG,MAAOrJ,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIiK,SAAU,QAASjJ,IAAME,WAAW,qBAAqB,GAAGjB,EAAG,eAAe,CAACQ,MAAM,CAAC,MAAQ,KAAK,KAAO,WAAW,CAACR,EAAG,YAAY,CAACO,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,YAAc,SAASI,MAAM,CAAC7C,MAAOgC,EAAIiK,SAASpD,OAAQ9F,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIiK,SAAU,SAAUjJ,IAAME,WAAW,oBAAoB,CAACjB,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,MAAM,MAAQ,SAASR,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,MAAM,MAAQ,SAASR,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,MAAM,MAAQ,UAAU,IAAI,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQV,EAAIgK,kBAAkB,CAAChK,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQV,EAAIqK,WAAW,CAACrK,EAAII,GAAG,SAAS,IAAI,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIkJ,cAAc,MAAQ,SAASxI,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIkJ,cAAczH,GAAQ,MAAQzB,EAAImJ,oBAAoB,CAAClJ,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACQ,MAAM,CAAC,MAAQ,OAAO,KAAO,OAAO,UAAW,EAAM,YAAY,KAAK,CAACR,EAAG,MAAM,CAACQ,MAAM,CAAC,KAAO,eAAemD,KAAK,eAAe,CAAC3D,EAAG,IAAI,CAACD,EAAII,GAAG,8BAA8BH,EAAG,IAAI,CAACD,EAAII,GAAG,4BAA4BH,EAAG,IAAI,CAACD,EAAII,GAAG,wBAAwBH,EAAG,IAAI,CAACD,EAAII,GAAG,4BAA4B,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,oBAAoBC,GAAG,CAAC,MAAQV,EAAIgC,mBAAmB,CAAChC,EAAII,GAAG,eAAe,GAAGH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACmC,IAAI,SAAS3B,MAAM,CAAC,OAAST,EAAIoJ,aAAa,KAAOpJ,EAAIqJ,WAAW,aAAarJ,EAAIsK,oBAAoB,WAAWtK,EAAIuK,kBAAkB,gBAAgBvK,EAAIwK,aAAa,eAAc,EAAM,MAAQ,EAAE,YAAYxK,EAAIyK,SAAS,OAAS,aAAa,KAAO,KAAK,CAACxK,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACH,EAAII,GAAG,aAAaH,EAAG,KAAK,CAACD,EAAII,GAAG,YAAYH,EAAG,MAAM,CAACE,YAAY,iBAAiBM,MAAM,CAAC,KAAO,OAAOmD,KAAK,OAAO,CAAC5D,EAAII,GAAG,gCAAgC,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,cAAc,CAACY,MAAM,CAAC7C,MAAOgC,EAAIqJ,WAAWqB,OAAQ3J,SAAS,SAAUC,GAAMhB,EAAIiB,KAAKjB,EAAIqJ,WAAY,SAAUrI,IAAME,WAAW,sBAAsB,CAAClB,EAAII,GAAG,cAAc,KAAKH,EAAG,MAAM,CAACE,YAAY,gBAAgBM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,YAAY,CAACS,GAAG,CAAC,MAAQV,EAAImJ,oBAAoB,CAACnJ,EAAII,GAAG,QAAQH,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,UAAU,QAAUT,EAAIwJ,qBAAqB9I,GAAG,CAAC,MAAQV,EAAIyJ,eAAe,CAACzJ,EAAII,GAAG,IAAIJ,EAAIK,GAAGL,EAAIwJ,oBAAsB,SAAW,QAAQ,QAAQ,MAAM,IAEti8CmB,EAAkB,G,gEC60CtB,GACA9M,KAAA,OACA+M,WAAA,CAAAC,oBACAC,OACA,OACA1B,aAAA,iCAAA2B,OAAAC,QAAAC,UACA/B,eAAA,EACAM,qBAAA,EACAH,WAAA,CACAqB,QAAA,GAEAD,SAAA,GACAS,QAAA,OACA7I,KAAA,GACA8B,MAAA,EACAC,KAAA,EACAC,KAAA,GACAuF,UAAA,EACA5E,gBAAA,GACApE,OAAA,CACAE,SAAA,GACAK,MAAA,GACAG,QAAA,GACAC,UAAA,GACAH,QAAA,GACAC,YAAA,GACAG,UAAA,GACA2J,KAAA,GACArH,MAAA,IAEAC,QAAA,EACA5B,SAAA,EACAiF,IAAA,SACArB,MAAA,KACAyC,KAAA,GACA3G,cAAA,GACAuJ,mBAAA,EACAzB,sBAAA,EACAE,eAAA,EACArF,mBAAA,EACAyD,mBAAA,EACArD,YAAA,EACAY,SAAA,GACA6F,iBAAA,GACA3G,UAAA,WACA4D,WAAA,GACAD,eAAA,EAEA0B,mBAAA,EACAD,gBAAA,QACAwB,eAAA,EACAC,kBAAA,EACAtB,SAAA,CACApM,KAAA,GACAsM,IAAA,GACAC,MAAA,GACAvD,OAAA,OAEAqD,UAAA,CACArM,KAAA,CACA,CAAA2N,UAAA,EAAAC,QAAA,WAAAC,QAAA,SAEAvB,IAAA,CACA,CAAAqB,UAAA,EAAAC,QAAA,WAAAC,QAAA,QACA,CAAAC,QAAA,gBAAAF,QAAA,aAAAC,QAAA,SAEAtB,MAAA,CACA,CAAAoB,UAAA,EAAAC,QAAA,UAAAC,QAAA,SAEA7E,OAAA,CACA,CAAA2E,UAAA,EAAAC,QAAA,QAAAC,QAAA,YAGAxD,SAAA,CACAnC,MAAA,GACA6F,OAAA,GAGAnG,MAAA,CACAM,MAAA,CACA,CACAyF,UAAA,EACAC,QAAA,QACAC,QAAA,UAIAvD,eAAA,QACAI,iBAAA,EACAM,QAAA,GACAhD,SAAA,GACAI,MAAA,GACAE,MAAA,GACAE,OAAA,GACAE,GAAA,GACAE,IAAA,GACAgC,UAAA,CACAoD,UAAA,GACAjD,UAAA,GACAjD,WAAA,GACAK,QAAA,GACAE,QAAA,GACAE,UAAA,GACAE,MAAA,GACAE,OAAA,GACAsC,YAAA,GACAC,UAAA,EACA+C,SAAA,GACA9C,KAAA,GACA+C,SAAA,EACAC,MAAA,EACAC,YAAA,GACAC,eAAA,GACAC,YAAA,EACAC,MAAA,CACA,CACAC,KAAA,GACAC,MAAA,GACAR,SAAA,IAEA,CACAO,KAAA,GACAC,MAAA,GACAR,SAAA,MAIApD,OAAA,CACAE,UAAA,CACA,CACA4C,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAI,SAAA,CACA,CACAN,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAO,YAAA,CACA,CACAT,UAAA,EACAC,QAAA,QACAC,QAAA,SAGA3C,UAAA,CACA,CACAyC,UAAA,EACAC,QAAA,UACAC,QAAA,SAGA1C,KAAA,CACA,CACAwC,UAAA,EACAC,QAAA,QACAC,QAAA,YAMAa,UAIA,KAAAC,eAEAC,QAAA,CAEAC,sBACA,OACA,CACAxJ,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,WACAE,QAAA,KACAC,UAAA,cACAF,YAAA,QACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,sBACAX,QAAA,sEACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EAEA1B,UAAA,MACAC,aAAA,MACAC,UAAA,MACAC,YAAA,OACAC,QAAA,MACAC,SAAA,QACAlC,MAAA,CACA,CACAvF,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,OAEA,CACAhJ,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,QAGAI,YAAA,CACAC,OAAA,CACA,CACAE,IAAA,wEACAvJ,KAAA,aAEA,CACAuJ,IAAA,sEACAvJ,KAAA,cAGA4J,QAAA,CACA,CACAL,IAAA,sEACAvJ,KAAA,aAGA8J,OAAA,CACA,CACA9J,KAAA,WACAuJ,IAAA,sBACAU,KAAA,UAKA,CACA5E,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,SACAE,QAAA,KACAC,UAAA,cACAF,YAAA,OACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,sBACAX,QAAA,GACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EAEA1B,UAAA,MACAC,aAAA,QACAC,UAAA,OACAC,YAAA,QACAC,QAAA,OACAC,SAAA,QACAlC,MAAA,CACA,CACAvF,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,SAIA,CACA3D,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,SACAE,QAAA,KACAC,UAAA,cACAF,YAAA,QACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,GACAX,QAAA,sEACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,CACA,CACAvF,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,SAIA,CACA3D,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,SACAE,QAAA,KACAC,UAAA,cACAF,YAAA,OACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,sBACAX,QAAA,GACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,IAEA,CACAF,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,WACAE,QAAA,KACAC,UAAA,cACAF,YAAA,QACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,sBACAX,QAAA,sEACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,CACA,CACAvF,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,OAEA,CACAhJ,KAAA,MACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,SAIA,CACA3D,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,SACAE,QAAA,KACAC,UAAA,cACAF,YAAA,QACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,sBACAX,QAAA,GACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,IAEA,CACAF,GAAA,EACA/B,MAAA,cACAL,SAAA,KACAM,QAAA,WACAE,QAAA,KACAC,UAAA,cACAF,YAAA,OACAkE,SAAA,aACA9B,YAAA,sBACAC,gBAAA,GACAX,QAAA,wEACA0E,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,MAIAoJ,cAEA,KAAAnK,KAAA,KAAAqK,sBACA,KAAAvI,MAAA,KAAA9B,KAAAxF,OACA,KAAAsF,SAAA,GAGAwK,iBACA,KAAAxK,SAAA,EAGA,MAAAyK,EAAA,KAAAF,sBACA,IAAAG,EAAA,IAAAD,GAGA,QAAAhM,OAAAE,SAAA,CACA,MAAAA,EAAA,KAAAF,OAAAE,SAAAgM,cACAD,IAAAE,OAAAC,GACAA,EAAAlM,UAAAkM,EAAAlM,SAAAgM,cAAAG,SAAAnM,IAUA,GANA,KAAAF,OAAAO,QACA0L,IAAAE,OAAAC,GACAA,EAAA7L,OAAA6L,EAAA7L,MAAA8L,SAAA,KAAArM,OAAAO,SAIA,KAAAP,OAAAU,QAAA,CACA,MAAAA,EAAA,KAAAV,OAAAU,QAAAwL,cACAD,IAAAE,OAAAC,GACAA,EAAA1L,SAAA0L,EAAA1L,QAAAwL,cAAAG,SAAA3L,IAUA,GANA,KAAAV,OAAAW,YACAsL,IAAAE,OAAAC,GACAA,EAAAzL,WAAAyL,EAAAzL,UAAA0L,SAAA,KAAArM,OAAAW,aAIA,KAAAX,OAAAQ,QAAA,CACA,MAAAA,EAAA,KAAAR,OAAAQ,QAAA0L,cACAD,IAAAE,OAAAC,GACAA,EAAA5L,SAAA4L,EAAA5L,QAAA0L,cAAAG,SAAA7L,IAWA,GAPA,KAAAR,OAAAS,cACAwL,IAAAE,OAAAC,GACAA,EAAA3L,cAAA,KAAAT,OAAAS,cAKA,KAAAT,OAAAY,WAAA,SAAAZ,OAAAY,UAAA3E,OAAA,CACA,MAAAqQ,EAAA,IAAAC,KAAA,KAAAvM,OAAAY,UAAA,IACA4L,EAAA,IAAAD,KAAA,KAAAvM,OAAAY,UAAA,IACAqL,IAAAE,OAAAC,IACA,GAAAA,EAAAvJ,YAAA,CACA,MAAA4J,EAAA,IAAAF,KAAAH,EAAAvJ,YAAA6J,MAAA,SACA,OAAAD,GAAAH,GAAAG,GAAAD,EAEA,WAKA,KAAA/K,KAAAwK,EACA,KAAA1I,MAAA0I,EAAAhQ,OACA,KAAAsF,SAAA,EAGA,MAAAoL,EAAA,KAAA3M,OAAAE,UAAA,KAAAF,OAAAO,OAAA,KAAAP,OAAAU,SACA,KAAAV,OAAAW,WAAA,KAAAX,OAAAQ,SAAA,KAAAR,OAAAS,aACA,KAAAT,OAAAY,WAAA,SAAAZ,OAAAY,UAAA3E,OAEA0Q,GACA,KAAAC,SAAAC,QAAA,WAAAZ,EAAAhQ,iBAGAiH,MAAAhB,GACA,KAAAyF,iBAAA,EACA,KAAAC,KAAA1F,EACA,KAAA2F,UAAA,CACAoD,UAAA/I,EAAAI,GACA0F,UAAA,GACAE,YAAA,GACAC,UAAA,EACA+C,SAAA,GACA9C,KAAA,GACA+C,SAAA,GAEA,KAAA2B,UAAA,KACA,KAAAC,gBAGA1E,YACA,IAAA2E,EAAA,KAEA,KAAAC,MAAA,aAAAC,SAAAC,IACA,IAAAA,EAYA,SAXA,KAAAC,YAAA,qBAAAvF,WAAAwF,KAAAC,IACA,KAAAA,EAAAC,OACAP,EAAAJ,SAAA,CACA1F,KAAA,UACA2D,QAAAyC,EAAAE,MAGAR,EAAArF,iBAAA,QAQAI,aAAA0F,GACA,KAAA5F,UAAAyD,eAAA,GACA,KAAAzD,UAAA0D,YAAA,EACA,KAAAmC,WAAA,mBAAAD,GAAAJ,KAAAC,IACA,KAAAA,EAAAC,OACA,KAAA1F,UAAAK,YAAAoF,EAAApD,KAAAwB,MACA,KAAA7D,UAAAM,UAAAmF,EAAApD,KAAAwB,UAIAqB,aACA,KAAAK,YAAA,sBAAAC,KAAAC,IACA,KAAAA,EAAAC,OACA,KAAAtF,QAAAqF,EAAApD,SAIAyD,eACA,IAAAX,EAAA,KACA,KAAAI,YAAA,wBAAAC,KAAAC,IACA,KAAAA,EAAAC,OACAP,EAAA/H,SAAAqI,EAAApD,KAAAiC,OAAAjH,GAAA,GAAAA,EAAA0I,WACAZ,EAAA3H,MAAAiI,EAAApD,KAAAiC,OAAAjH,GAAA,GAAAA,EAAA0I,WACAZ,EAAAzH,MAAA+H,EAAApD,KAAAiC,OAAAjH,GAAA,IAAAA,EAAA0I,WACAZ,EAAAnH,IAAAyH,EAAApD,KAAAiC,OAAAjH,GAAA,GAAAA,EAAA0I,WACAZ,EAAArH,GAAA2H,EAAApD,KAAAiC,OAAAjH,GAAA,GAAAA,EAAA0I,WACAZ,EAAAvH,OAAA6H,EAAApD,KAAAiC,OAAAjH,GAAA,GAAAA,EAAA0I,eAIAvL,SAAAC,GACAuL,QAAAC,IAAA,2BAAAxL,GACA,IAAA0K,EAAA,KACA,GAAA1K,IACA,KAAA0G,UAAA1G,EAEA,KAAA8B,gBAAA,KAAA3C,KAAAsM,KAAA3B,KAAA9J,SAAA,GACAuL,QAAAC,IAAA,wBAAA1J,iBAEA,KAAAJ,YAAA,EACA,KAAAY,SAAA,GACA,KAAA6F,iBAAA,GAEA,KAAA3G,UAAA,YAEAkJ,EAAApJ,mBAAA,EACAiK,QAAAC,IAAA,4BAAAd,EAAApJ,oBAEAG,gBAAAtF,GACA,KAAAqF,UAAArF,EAEA,aAAAA,IACA,KAAAuF,YAAA,IAGAC,iBACA,KAAAD,WAWA,KAAAA,YAAA,GATA,KAAAA,YAAA,EAEA,KAAAyG,iBAAAuD,KAAAC,MAAAD,KAAAE,UAAA,KAAA9J,kBAEA,KAAAQ,SAAAoJ,KAAAC,MAAAD,KAAAE,UAAA,KAAA9J,kBAEA,KAAAuJ,iBAMAxJ,aAEA,KAAAC,gBAAA4J,KAAAC,MAAAD,KAAAE,UAAA,KAAAzD,mBACA,KAAAzG,YAAA,EACA,KAAAY,SAAA,GACA,KAAAgI,SAAAhF,KAAA,UAEA1D,eAEA,KAAA+I,MAAArI,SAAAsI,SAAAC,IACA,IAAAA,EAiBA,OADA,KAAAP,SAAAuB,MAAA,gBACA,EAjBA,CAEA,KAAA/J,gBAAA4J,KAAAC,MAAAD,KAAAE,UAAA,KAAAtJ,WAGA,MAAAvH,EAAA,KAAAoE,KAAA2M,UAAAhC,KAAA9J,KAAA,KAAA8B,gBAAA9B,KACA,IAAAjF,GACA,KAAAoE,KAAA4M,OAAAhR,EAAA,OAAA+G,iBAIA,KAAAJ,YAAA,EACA,KAAAY,SAAA,GAEA,KAAAgI,SAAAC,QAAA,aAOA9J,SAAAT,GACA,IAAA0K,EAAA,KACA,GAAA1K,EACA,KAAAgM,QAAAhM,GAEA,KAAAgF,SAAA,CACAnC,MAAA,GACAiD,KAAA,IAGA4E,EAAA3F,mBAAA,EACA2F,EAAAW,gBAEA9J,oBACA,KAAAD,mBAAA,EACA,KAAAyD,mBAAA,EAEA,KAAArD,YAAA,EACA,KAAAY,SAAA,GACA,KAAA6F,iBAAA,GAEA,KAAA3G,UAAA,YAGAkC,UACA,KAAAmD,mBAAA,EACA,KAAAE,SAAA,CACApM,KAAA,GACAsM,IAAA,GACAC,MAAA,GACAvD,OAAA,OAEA,KAAAiD,gBAAA,QACA,KAAAwB,eAAA,GAEAxE,SAAAqI,EAAAlR,GACA,KAAA8L,mBAAA,EACA,KAAAE,SAAA,CACApM,KAAAsR,EAAAtR,KACAsM,IAAAgF,EAAAhF,IACAC,MAAAgF,WAAAD,EAAA/E,OACAvD,OAAAsI,EAAAtI,QAEA,KAAAiD,gBAAA,QACA,KAAAwB,eAAA,EACA,KAAAC,iBAAAtN,GAEAoM,WACA,KAAAwD,MAAA5D,SAAA6D,SAAAC,IACA,IAAAA,EA8BA,OADA,KAAAP,SAAAuB,MAAA,gBACA,EA9BA,CACA,MAAAM,EAAA,CACAxR,KAAA,KAAAoM,SAAApM,KACAsM,IAAA,KAAAF,SAAAE,IACAC,MAAA,KAAAH,SAAAG,MAAAjN,WACA0J,OAAA,KAAAoD,SAAApD,QAGA,KAAAyE,eAEA,KAAAtG,gBAAA5B,MAAA,KAAAmI,kBAAA8D,EACA,KAAA7B,SAAAC,QAAA,gBAGA,KAAAzI,gBAAA5B,QACA,KAAA4B,gBAAA5B,MAAA,IAEA,KAAA4B,gBAAA5B,MAAAnE,KAAAoQ,GACA,KAAA7B,SAAAC,QAAA,aAIA,MAAA6B,EAAA,KAAAjN,KAAA2M,UAAAhC,KAAA9J,KAAA,KAAA8B,gBAAA9B,KACA,IAAAoM,IACA,KAAAjN,KAAAiN,GAAAlM,MAAA,SAAA4B,gBAAA5B,QAGA,KAAA4G,sBAOAA,kBACA,KAAAD,mBAAA,EACA,KAAAE,SAAA,CACApM,KAAA,GACAsM,IAAA,GACAC,MAAA,GACAvD,OAAA,OAEA,KAAAyE,eAAA,EACA,KAAAC,kBAAA,EACA,KAAAzB,gBAAA,QAEA,KAAA4D,UAAA,KACA,KAAAG,MAAA5D,UACA,KAAA4D,MAAA5D,SAAAsF,mBAIAxI,WAAA9I,GACA,KAAAuR,SAAA,qBACAC,kBAAA,KACAC,iBAAA,KACA5H,KAAA,YACAmG,KAAA,KACA,KAAAjJ,gBAAA5B,MAAA6L,OAAAhR,EAAA,GACA,KAAAuP,SAAAC,QAAA,YAIAzG,gBACA,KAAAwG,SAAAhF,KAAA,mBAEAhB,eACA,KAAAmI,gBAAA,UAAAC,IACA,KAAAC,iBAAAD,EAAA,qBAGAlI,gBACA,KAAAiI,gBAAA,UAAAC,IACA,KAAAC,iBAAAD,EAAA,qBAGA5H,eACA,KAAA2H,gBAAA,IAAAC,IACA,KAAAC,iBAAAD,EAAA,oBAIAD,gBAAAG,EAAA/O,GACA,MAAAgP,EAAAC,SAAAC,cAAA,SACAF,EAAAjI,KAAA,OACAiI,EAAAD,SACAC,EAAAG,UAAA,EACAH,EAAAI,MAAAC,QAAA,OAEAL,EAAAM,SAAAhC,IACA,MAAAuB,EAAAU,MAAAC,KAAAlC,EAAA7R,OAAAoT,OACAA,EAAA/S,OAAA,GACAkE,EAAA6O,GAEAI,SAAAQ,KAAAC,YAAAV,IAGAC,SAAAQ,KAAAE,YAAAX,GACAA,EAAAY,SAGAd,iBAAAD,EAAA9H,EAAA8I,GACA,GAAAhB,GAAA,IAAAA,EAAA/S,OAAA,CAMA,QAAA+K,KAAAgI,EAAA,CACA,cAAA9H,IAAAF,EAAAE,KAAA+I,WAAA,UAEA,YADA,KAAArD,SAAAuB,MAAA6B,EAAA,YAGA,GAAAhJ,EAAAvD,KAAA,SAEA,YADA,KAAAmJ,SAAAuB,MAAA,MAAAnH,EAAA/J,mBAMA,KAAAmH,gBAAAiC,cACA,KAAAjC,gBAAAiC,YAAA,IAEA,KAAAjC,gBAAAiC,YAAAa,KACA,KAAA9C,gBAAAiC,YAAAa,GAAA,IAIA,KAAA0F,SAAAhF,KAAA,QAAAoH,EAAA/S,iBAEA+S,EAAAtR,QAAA,CAAAsJ,EAAA3J,KAEA,MAAA6S,EAAAC,IAAAC,gBAAApJ,GAGAqJ,WAAA,KACA,MAAAC,EAAA,CACArT,KAAA+J,EAAA/J,KACAuJ,IAAA0J,EACAzM,KAAAuD,EAAAvD,KACAyD,KAAAF,EAAAE,KACAT,YAAA,IAAA8F,MAAAgE,kBAGA,KAAAnM,gBAAAiC,YAAAa,GAAA7I,KAAAiS,GAEAjT,IAAA2R,EAAA/S,OAAA,GACA,KAAA2Q,SAAAC,QAAA,GAAAmD,aAAAhB,EAAA/S,eAEA,KAAAoB,EAAA,WA9CA,KAAAuP,SAAA4D,QAAA,cAiDA7J,iBAAAO,EAAA7J,GACA,KAAAuR,SAAA,oBACAC,kBAAA,KACAC,iBAAA,KACA5H,KAAA,YACAmG,KAAA,KAIA,GAHA,KAAAjJ,gBAAAiC,cACA,KAAAjC,gBAAAiC,YAAA,IAEA,KAAAjC,gBAAAiC,YAAAa,GAAA,CAEA,MAAAF,EAAA,KAAA5C,gBAAAiC,YAAAa,GAAA7J,GACA2J,KAAAR,KAAAQ,EAAAR,IAAAyJ,WAAA,UACAE,IAAAM,gBAAAzJ,EAAAR,KAGA,KAAApC,gBAAAiC,YAAAa,GAAAmH,OAAAhR,EAAA,GACA,KAAAuP,SAAAC,QAAA,aAIAnG,aAAAM,GACA,GAAAA,KAAAR,IAKA,IAEA,MAAAkK,EAAAtB,SAAAC,cAAA,KACAqB,EAAAC,KAAA3J,EAAAR,IACAkK,EAAAE,SAAA5J,EAAA/J,MAAA,KACAyT,EAAAnB,MAAAC,QAAA,OAEAJ,SAAAQ,KAAAE,YAAAY,GACAA,EAAAX,QACAX,SAAAQ,KAAAC,YAAAa,GAEA,KAAA9D,SAAAC,QAAA,SAAA7F,EAAA/J,MACA,MAAAkR,GACA,KAAAvB,SAAAuB,MAAA,YACAN,QAAAM,MAAA,QAAAA,QAlBA,KAAAvB,SAAAuB,MAAA,WAsBAlH,YAAA4J,GACA,OAAAA,EAEAA,EAAAZ,WAAA,4BACAY,EAAAxE,SAAA,QACAwE,EAAAxE,SAAA,SAAAwE,EAAAxE,SAAA,OADA,mBAEAwE,EAAAxE,SAAA,UAAAwE,EAAAxE,SAAA,0BACAwE,EAAAxE,SAAA,QAAAwE,EAAAxE,SAAA,+BACAwE,EAAAxE,SAAA,gCACAwE,EAAAxE,SAAA,2BAEA,mBAVA,oBAYAlF,eAAA2J,GACA,OAAAA,EAAA,YAEA,MAAAtS,EAAA,KACAuS,EAAA,qBACAC,EAAAC,KAAAC,MAAAD,KAAAnD,IAAAgD,GAAAG,KAAAnD,IAAAtP,IAEA,OAAAgQ,YAAAsC,EAAAG,KAAAE,IAAA3S,EAAAwS,IAAAI,QAAA,QAAAL,EAAAC,IAGAvO,aAAAD,GACA,OAAAA,KAAAvG,OAAAuG,EAAAvG,OAAA,GAEAsG,iBAAAC,GACA,MAAA5E,EAAA,KAAA6E,aAAAD,GACA,WAAA5E,EAAA,OACAA,GAAA,YACAA,GAAA,YACA,UAEA+E,mBAAAH,GACA,OAAAA,KAAAvG,OACAuG,EAAAzG,OAAA,CAAAwH,EAAAgL,IACAhL,GAAAiL,WAAAD,EAAA/E,QAAA,GACA,GAHA,GAKA5G,aAAAyO,GACA,WAAAA,EAAA,IACAA,EAAAd,eAAA,SACAe,sBAAA,EACAC,sBAAA,KAGAjD,QAAAhM,GACA,IAAA0K,EAAA,KACAA,EAAAU,WAAAV,EAAAxG,IAAA,WAAAlE,GAAA+K,KAAAC,IACAA,IACAA,EAAApD,KAAAnF,WAAA,GAAAuI,EAAApD,KAAAnF,WAAA,GAAAuI,EAAApD,KAAAnF,WACAuI,EAAApD,KAAA9E,QAAA,GAAAkI,EAAApD,KAAA9E,QAAA,GAAAkI,EAAApD,KAAA9E,QACAkI,EAAApD,KAAA5E,QAAA,GAAAgI,EAAApD,KAAA5E,QAAA,GAAAgI,EAAApD,KAAA5E,QACAgI,EAAApD,KAAAtE,OAAA,GAAA0H,EAAApD,KAAAtE,OAAA,GAAA0H,EAAApD,KAAAtE,OACA0H,EAAApD,KAAA1E,UAAA,GAAA8H,EAAApD,KAAA1E,UAAA,GAAA8H,EAAApD,KAAA1E,UACA8H,EAAApD,KAAAxE,MAAA,GAAA4H,EAAApD,KAAAxE,MAAA,GAAA4H,EAAApD,KAAAxE,MACAsH,EAAA1F,SAAAgG,EAAApD,SAIA9G,QAAA/F,EAAAiF,GACA,KAAAsM,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACA5H,KAAA,YAEAmG,KAAA,KACA,KAAAD,YAAA,KAAA5G,IAAA,aAAAlE,GAAA+K,KAAAC,IACA,KAAAA,EAAAC,OACA,KAAAX,SAAA,CACA1F,KAAA,UACA2D,QAAA,UAEA,KAAApJ,KAAA4M,OAAAhR,EAAA,QAIAmU,MAAA,KACA,KAAA5E,SAAA,CACA1F,KAAA,QACA2D,QAAA,aAIA9K,UACA,KAAAL,QAAA+R,GAAA,IAEA3Q,aACA,KAAA0C,KAAA,EACA,KAAAC,KAAA,GAGA,KAAAsI,kBAEAhL,cACA,KAAAf,OAAA,CACAE,SAAA,GACAK,MAAA,GACAG,QAAA,GACAC,UAAA,GACAH,QAAA,GACAC,YAAA,GACAG,UAAA,GACA2J,KAAA,GACArH,MAAA,IAEA,KAAAM,KAAA,EACA,KAAAC,KAAA,GAGA,KAAAmI,eAGA8F,UACA,IAAA1E,EAAA,KAEAA,EAAAzL,SAAA,EACAyL,EACAI,YACAJ,EAAAxG,IAAA,cAAAwG,EAAAxJ,KAAA,SAAAwJ,EAAAvJ,KACAuJ,EAAAhN,QAEAqN,KAAAC,IACA,KAAAA,EAAAC,OACAP,EAAAvL,KAAA6L,EAAApD,KACA8C,EAAAzJ,MAAA+J,EAAA1P,MAEA,SAAA0P,EAAAE,MACAR,EAAA7J,QAAA,IAGA6J,EAAAzL,SAAA,KAGAiG,WACA,IAAAwF,EAAA,KACAa,QAAAC,IAAA,KAAAxG,UACA,KAAA2F,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAqBA,SApBA,KAAAC,YAAAJ,EAAAxG,IAAA,YAAAc,UAAA+F,KAAAC,IACA,KAAAA,EAAAC,MACAP,EAAAJ,SAAA,CACA1F,KAAA,UACA2D,QAAAyC,EAAAE,MAIA,KAAA5B,cACAoB,EAAAxC,mBAAA,EACAwC,EAAA/D,eAAA,EACA+D,EAAA3F,mBAAA,GAEA2F,EAAAJ,SAAA,CACA1F,KAAA,QACA2D,QAAAyC,EAAAE,WASA9J,iBAAAiO,GACA,KAAAlO,KAAAkO,EAGA,KAAA/F,eAEAjI,oBAAAgO,GACA,KAAAnO,KAAAmO,EAGA,KAAA/F,eAEAgG,cAAAC,GACA,KAAAvK,SAAAwK,SAAAD,EAAA3H,KAAA1D,KAGApE,UAAA4E,GACA,KAAAU,WAAAV,EACA,KAAAS,eAAA,GAEAmC,aAAA5C,GACA,MAAA+K,EAAA,0BAAAC,KAAAhL,EAAAE,MACA6K,GACA,KAAAnF,SAAAuB,MAAA,cAIA8D,SAAAjL,EAAAkL,GACA,IAAAlF,EAAA,KACAA,EAAAU,WAAA,6BAAA1G,GAAAqG,KAAAC,IACA,KAAAA,EAAAC,MACAP,EAAA1F,SAAA4K,GAAA,GAEAlF,EAAAJ,SAAAC,QAAA,UAEAG,EAAAJ,SAAAuB,MAAAb,EAAAE,QAIA5L,kBAAA,OAAAuQ,EAAA,KAAA5H,EAAA,MAAArH,IACA,KAAAlD,OAAAuK,OACA,KAAAvK,OAAAkD,QAGA,KAAA0I,eAIA3N,QAAA,WACA,IAAA+O,EAAA,KACAoF,SAAAzB,KAAA,6BAAA3D,EAAA7C,OAAAC,QAAAC,UAAA,YAAA2C,EAAAhN,OAAAqS,SAWA9J,oBACA,KAAAD,eAAA,EACA,KAAA2E,MAAAqF,OAAAC,aACA,KAAA9J,WAAAqB,QAAA,GAEApB,cAAA8J,GACA,MAAAA,EAAAjF,MACA,KAAAX,SAAA,CACA1F,KAAA,UACA2D,QAAA2H,EAAAhF,MAEA,KAAAlF,eAAA,EAGA,KAAAsD,cACAiC,QAAAC,IAAA0E,IAEA,KAAA5F,SAAA,CACA1F,KAAA,UACA2D,QAAA2H,EAAAhF,MAIA,KAAA5E,qBAAA,EACA,KAAAqE,MAAAqF,OAAAC,cAEA5J,UAAA3B,GACA,IAAA6J,EAAA,eACA3J,EAAAF,EAAA/J,KAAAyP,MAAA,KAAA+F,OAAA,MAAAvG,cACA,QAAA2E,EAAAxE,SAAAnF,KACA,KAAA0F,SAAA,CACA1F,KAAA,UACA2D,QAAA,2BAEA,IAIAhC,eACA,KAAAD,qBAAA,EACA,KAAAqE,MAAAqF,OAAAI,UAEA5J,cACA,KAAA6J,YAAA,EACA,KAAArK,eAAA,EACA,KAAAsK,KAAA,CACAtQ,GAAA,GACApC,SAAA,GACA2S,OAAA,GACAC,UAAA,EACAC,SAAA,GACAC,SAAA,GACAC,IAAA,GACAC,QAAA,GACAC,WAAA,GACAC,OAAA,GACAC,OAAA,GACAC,iBAAA,EACAC,cAAA,GACAC,gBAAA,GAEA,KAAAvG,MAAA2F,KAAAa,eAEAvS,aACA,KAAAoH,eAAA,EACA,KAAAuB,SAAA,GACA,KAAApB,WAAAqB,QAAA,GAGAvB,oBACA,KAAAD,eAAA,EACA,KAAAuB,SAAA,GACA,KAAApB,WAAAqB,QAAA,EACA,KAAAmD,MAAAqF,QACA,KAAArF,MAAAqF,OAAAC,cAIA3I,aAAA5C,GACA,MAAA0M,EAAA,6BAAA1M,EAAAE,MACA,sEAAAF,EAAAE,KACAyM,EAAA3M,EAAAvD,KAAA,aAEA,OAAAiQ,IAIAC,IACA,KAAA/G,SAAAuB,MAAA,qBACA,IALA,KAAAvB,SAAAuB,MAAA,mBACA,IASAtF,eACA,SAAAgB,SAAA5N,QAKA,KAAA2M,qBAAA,EACA,KAAAqE,MAAAqF,OAAAI,UALA,KAAA9F,SAAA4D,QAAA,eAQA9G,oBAAA8I,EAAAxL,EAAA6C,GACA,KAAAjB,qBAAA,EACA,MAAA4J,EAAAjF,MACA,KAAAX,SAAAC,QAAA,YAAA2F,EAAA5U,OAAA,WACA,KAAA2K,oBAEA,KAAAqD,eAEA,KAAAgB,SAAAuB,MAAAqE,EAAAhF,KAAA,SAIA7D,kBAAAiK,EAAA5M,EAAA6C,GACA,KAAAjB,qBAAA,EACA,KAAAgE,SAAAuB,MAAA,cACAN,QAAAM,MAAA,gBAAAyF,IAEAzS,UACA,KAAA8H,eAAA,EACA,KAAA3B,SAAA,GACA,KAAAqG,gBAGAvM,mBACA,MAAAyS,EAAA,4BACAnD,EAAAtB,SAAAC,cAAA,KACAqB,EAAAC,KAAAkD,EACAnD,EAAAE,SAAA,aACAxB,SAAAQ,KAAAE,YAAAY,GACAA,EAAAX,QACAX,SAAAQ,KAAAC,YAAAa,GACA,KAAA9D,SAAAC,QAAA,aAGAhL,sBAAAiS,GACA,KAAA7S,cAAA6S,GAGA9S,qBACA,IAAA+S,EACAlJ,EAEA,QAAA5J,cAAAhF,OAAA,GAEA,MAAA+X,EAAA,KAAA/S,cAAAgT,IAAA7H,KAAA9J,IAAA4R,KAAA,KACAH,EAAA,kCAAA5J,OAAAC,QAAAC,iBAAA2J,IACAnJ,EAAA,aAAA5J,cAAAhF,qBAGA8X,EAAA,kCAAA5J,OAAAC,QAAAC,qBAAA,KAAArK,OAAAqS,SAAA,KACAxH,EAAA,aAIAuH,SAAAzB,KAAAoD,EACA,KAAAnH,SAAAC,QAAAhC,IAGAsJ,mBACA,SAAAlT,cAAAhF,OAKA,KAAA2S,SAAA,iBAAA3N,cAAAhF,uBAAA,UACA4S,kBAAA,OACAC,iBAAA,KACA5H,KAAA,UACAkN,0BAAA,IACA/G,KAAA,KAEA,MAAA2G,EAAA,KAAA/S,cAAAgT,IAAA7H,KAAA9J,IAYA0R,EAAAtW,QAAA4E,IACA,MAAAjF,EAAA,KAAAoE,KAAA2M,UAAAhC,KAAA9J,SACA,IAAAjF,GACA,KAAAoE,KAAA4M,OAAAhR,EAAA,KAIA,KAAAkG,MAAA,KAAA9B,KAAAxF,OACA,KAAAgF,cAAA,GACA,KAAA2L,SAAAC,QAAA,QAAAmH,EAAA/X,cAGA,KAAAgR,MAAAoH,UAAAC,mBACA9C,MAAA,KACA,KAAA5E,SAAAhF,KAAA,aArCA,KAAAgF,SAAA4D,QAAA,iBC3hF2W,I,wBCQvW+D,EAAY,eACd,EACApV,EACA4K,GACA,EACA,KACA,WACA,MAIa,aAAAwK,E,2CCnBf,IAAIpV,EAAS,WAAkB,IAAIC,EAAIjD,KAAKkD,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,cAAcH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKpH,SAAW,cAAcnB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKrH,OAAS,cAAclB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAK1H,UAAY,eAAe,GAAGb,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKlH,SAAW,cAAcrB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKjH,WAAa,cAActB,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKnH,aAAe,eAAe,GAAGpB,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAK9B,YAAc,cAAczG,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAK7B,KAAO3G,EAAIwI,KAAK7B,KAAO,IAAM,cAAc1G,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEH,EAAIwI,KAAKzF,SAAgC,KAArB/C,EAAIwI,KAAKzF,QAAgB9C,EAAG,YAAY,CAACO,YAAY,CAAC,OAAS,WAAWC,MAAM,CAAC,IAAMT,EAAIwI,KAAKzF,QAAQ,KAAO,IAAIc,SAAS,CAAC,MAAQ,SAASpC,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIwI,KAAKzF,aAAa9C,EAAG,OAAO,CAACE,YAAY,WAAW,CAACH,EAAII,GAAG,UAAU,QAAQ,GAAGH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,KAAK,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEH,EAAIwI,KAAKf,SAAgC,KAArBzH,EAAIwI,KAAKf,QAAgBxH,EAAG,WAAW,CAACO,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,OAAS,WAAWC,MAAM,CAAC,IAAMT,EAAIwI,KAAKf,QAAQ,IAAM,SAAS/G,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIgD,UAAUhD,EAAIwI,KAAKf,YAAY,CAACxH,EAAG,MAAM,CAACE,YAAY,aAAaM,MAAM,CAAC,KAAO,SAASmD,KAAK,SAAS,CAAC3D,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,OAAO,CAACE,YAAY,WAAW,CAACH,EAAII,GAAG,UAAU,QAAQ,IAAI,GAAGH,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,YAAYH,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKtD,cAAgB,cAAcjF,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKrD,WAAa,cAAclF,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKvD,WAAa,eAAe,GAAGhF,EAAG,SAAS,CAACQ,MAAM,CAAC,OAAS,KAAK,CAACR,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,UAAUH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKpD,aAAe,cAAcnF,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,QAAQH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKnD,SAAW,cAAcpF,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,IAAI,CAACR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAG,SAASH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACH,EAAII,GAAGJ,EAAIK,GAAGL,EAAIwI,KAAKlD,UAAY,eAAe,IAAI,GAAGrF,EAAG,UAAU,CAACE,YAAY,YAAYM,MAAM,CAAC,OAAS,UAAU,CAACR,EAAG,MAAM,CAACE,YAAY,cAAcM,MAAM,CAAC,KAAO,UAAUmD,KAAK,UAAU,CAAC3D,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACH,EAAII,GAAG,aAAaH,EAAG,WAAW,CAACgC,WAAW,CAAC,CAACpE,KAAK,UAAUqE,QAAQ,YAAYlE,MAAOgC,EAAImC,QAASjB,WAAW,YAAYV,YAAY,CAAC,MAAQ,QAAQC,MAAM,CAAC,KAAOT,EAAIwI,KAAKpF,MAAM,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACd,WAAW,UAAUC,MAAM,aAAa,CAACtC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACT,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIjF,gBAAgBoC,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAGJ,EAAIK,GAAGwC,EAAMC,IAAIqH,eAAelK,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACH,EAAII,GAAG,IAAIJ,EAAIK,GAAGwC,EAAMC,IAAIsH,iBAAiBnK,EAAG,kBAAkB,CAACQ,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,SAAS,CAACQ,MAAM,CAAC,KAA4B,QAArBoC,EAAMC,IAAI+D,OAAmB,UAAY,UAAU,KAAO,UAAU,CAAC7G,EAAII,GAAG,IAAIJ,EAAIK,GAAGwC,EAAMC,IAAI+D,QAAQ,cAAc5G,EAAG,kBAAkB,CAACQ,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOiC,YAAY1C,EAAI2C,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC5C,EAAG,YAAY,CAACQ,MAAM,CAAC,KAAO,OAAO,KAAO,SAASC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOzB,EAAIoV,eAAevS,EAAMC,QAAQ,CAAC7C,EAAG,IAAI,CAACE,YAAY,iBAAiBH,EAAII,GAAG,kBAAkB,GAAKJ,EAAIwI,KAAKpF,OAAmC,IAA1BpD,EAAIwI,KAAKpF,MAAMvG,OAA0HmD,EAAIkE,KAAhHjE,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACD,EAAII,GAAG,gBAAyB,GAAGH,EAAG,YAAY,CAACQ,MAAM,CAAC,MAAQ,OAAO,QAAUT,EAAIqI,cAAc,MAAQ,OAAO3H,GAAG,CAAC,iBAAiB,SAASe,GAAQzB,EAAIqI,cAAc5G,KAAU,CAACxB,EAAG,WAAW,CAACQ,MAAM,CAAC,IAAMT,EAAIsI,eAAe,IAAI,IAEjuNqC,EAAkB,GCqNtB,GACA9M,KAAA,cACAwX,MAAA,CACAnS,GAAA,CACA4E,KAAA,CAAAwN,OAAAC,QACA/J,UAAA,IAGAV,OACA,OACAtC,KAAA,GACArG,SAAA,EACAkG,eAAA,EACAC,WAAA,KAGAkN,MAAA,CACAtS,GAAA,CACAuS,WAAA,EACAC,QAAAC,GACAA,GAAA,GAAAA,IACAlH,QAAAC,IAAA,sBAAAiH,GACA,KAAAzG,QAAAyG,OAKAlJ,QAAA,CACAyC,QAAAhM,GACA,IAAA0K,EAAA,KACAa,QAAAC,IAAA,eAAAxL,GACA0K,EAAAzL,SAAA,EAGA8O,WAAA,KACA,MAAA2E,EAAA,CACA1S,KACA9B,QAAA,WACAD,MAAA,cACAL,SAAA,KACAQ,QAAA,KACAyB,QAAA,GACA1B,YAAA,QACAE,UAAA,cACA2D,aAAA,OACAC,UAAA,MACAF,UAAA,OACAG,YAAA,OACAC,QAAA,MACAC,SAAA,OACAmC,QAAA,GACAf,WAAA,aACAC,KAAA,EACAvD,MAAA,CACA,CACAvF,KAAA,OACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,OAEA,CACAhJ,KAAA,OACAsM,IAAA,cACAC,MAAA,QACAvD,OAAA,SAKA+G,EAAApF,KAAAoN,EACAhI,EAAAzL,SAAA,EACAsM,QAAAC,IAAA,YAAAkH,IACA,MAmBA5S,UAAA6S,GACA,KAAAvN,WAAAuN,EACA,KAAAxN,eAAA,GAGA+M,eAAAjG,GACAV,QAAAC,IAAA,WAAAS,GAEA,KAAA3B,SAAAhF,KAAA,iBC1TmV,I,wBCQ/U2M,EAAY,eACd,EACApV,EACA4K,GACA,EACA,KACA,WACA,MAIa,OAAAwK,E,2CClBf,IAAIW,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAoB,EAAQ,QAE5BC,EAAaC,UAEbC,EAAe,8CAGfC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAM3Z,EAAY4Z,EAAiBC,GAClD,IAAIC,EAAIX,EAASQ,GACbI,EAAOX,EAAcU,GACrB7Z,EAASoZ,EAAkBS,GAE/B,GADAZ,EAAUlZ,GACK,IAAXC,GAAgB2Z,EAAkB,EAAG,MAAM,IAAIN,EAAWE,GAC9D,IAAInY,EAAQqY,EAAWzZ,EAAS,EAAI,EAChC+U,EAAI0E,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAIvY,KAAS0Y,EAAM,CACjBF,EAAOE,EAAK1Y,GACZA,GAAS2T,EACT,MAGF,GADA3T,GAAS2T,EACL0E,EAAWrY,EAAQ,EAAIpB,GAAUoB,EACnC,MAAM,IAAIiY,EAAWE,GAGzB,KAAME,EAAWrY,GAAS,EAAIpB,EAASoB,EAAOA,GAAS2T,EAAO3T,KAAS0Y,IACrEF,EAAO7Z,EAAW6Z,EAAME,EAAK1Y,GAAQA,EAAOyY,IAE9C,OAAOD,IAIX7X,EAAOC,QAAU,CAGf3C,KAAMma,GAAa,GAGnBO,MAAOP,GAAa,K,kCC3CtB,IAAIH,EAAaC,UAEjBvX,EAAOC,QAAU,SAAUgY,EAAQrL,GACjC,GAAIqL,EAASrL,EAAU,MAAM,IAAI0K,EAAW,wBAC5C,OAAOW,I,kCCJT,IAAIC,EAAc,EAAQ,QACtBC,EAAiB,EAAQ,QAE7BnY,EAAOC,QAAU,SAAUrC,EAAQqB,EAAMmZ,GAGvC,OAFIA,EAAWzY,KAAKuY,EAAYE,EAAWzY,IAAKV,EAAM,CAAEoZ,QAAQ,IAC5DD,EAAWE,KAAKJ,EAAYE,EAAWE,IAAKrZ,EAAM,CAAEsZ,QAAQ,IACzDJ,EAAeK,EAAE5a,EAAQqB,EAAMmZ", "file": "js/chunk-461f593c.d747781c.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"", "'use strict';\r\nvar $ = require('../internals/export');\r\nvar $reduce = require('../internals/array-reduce').left;\r\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\r\nvar CHROME_VERSION = require('../internals/engine-v8-version');\r\nvar IS_NODE = require('../internals/engine-is-node');\r\n\r\n// Chrome 80-82 has a critical bug\r\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\r\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\r\nvar FORCED = CHROME_BUG || !arrayMethodIsStrict('reduce');\r\n\r\n// `Array.prototype.reduce` method\r\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n$({ target: 'Array', proto: true, forced: FORCED }, {\r\n  reduce: function reduce(callbackfn /* , initialValue */) {\r\n    var length = arguments.length;\r\n    return $reduce(this, callbackfn, length, length > 1 ? arguments[1] : undefined);\r\n  }\r\n});\r\n", "'use strict';\r\nvar defineBuiltIn = require('../internals/define-built-in');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar toString = require('../internals/to-string');\r\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\r\n\r\nvar $URLSearchParams = URLSearchParams;\r\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\r\nvar getAll = uncurryThis(URLSearchParamsPrototype.getAll);\r\nvar $has = uncurryThis(URLSearchParamsPrototype.has);\r\nvar params = new $URLSearchParams('a=1');\r\n\r\n// `undefined` case is a Chromium 117 bug\r\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\r\nif (params.has('a', 2) || !params.has('a', undefined)) {\r\n  defineBuiltIn(URLSearchParamsPrototype, 'has', function has(name /* , value */) {\r\n    var length = arguments.length;\r\n    var $value = length < 2 ? undefined : arguments[1];\r\n    if (length && $value === undefined) return $has(this, name);\r\n    var values = getAll(this, name); // also validates `this`\r\n    validateArgumentsLength(length, 1);\r\n    var value = toString($value);\r\n    var index = 0;\r\n    while (index < values.length) {\r\n      if (values[index++] === value) return true;\r\n    } return false;\r\n  }, { enumerable: true, unsafe: true });\r\n}\r\n", "'use strict';\r\nvar DESCRIPTORS = require('../internals/descriptors');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\r\n\r\nvar URLSearchParamsPrototype = URLSearchParams.prototype;\r\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\r\n\r\n// `URLSearchParams.prototype.size` getter\r\n// https://github.com/whatwg/url/pull/734\r\nif (DESCRIPTORS && !('size' in URLSearchParamsPrototype)) {\r\n  defineBuiltInAccessor(URLSearchParamsPrototype, 'size', {\r\n    get: function size() {\r\n      var count = 0;\r\n      forEach(this, function () { count++; });\r\n      return count;\r\n    },\r\n    configurable: true,\r\n    enumerable: true\r\n  });\r\n}\r\n", "'use strict';\r\nvar global = require('../internals/global');\r\nvar classof = require('../internals/classof-raw');\r\n\r\nmodule.exports = classof(global.process) === 'process';\r\n", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./user.vue?vue&type=style&index=0&id=4acd38ca&prod&scoped=true&lang=css\"", "'use strict';\r\nvar defineBuiltIn = require('../internals/define-built-in');\r\nvar uncurryThis = require('../internals/function-uncurry-this');\r\nvar toString = require('../internals/to-string');\r\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\r\n\r\nvar $URLSearchParams = URLSearchParams;\r\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\r\nvar append = uncurryThis(URLSearchParamsPrototype.append);\r\nvar $delete = uncurryThis(URLSearchParamsPrototype['delete']);\r\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\r\nvar push = uncurryThis([].push);\r\nvar params = new $URLSearchParams('a=1&a=2&b=3');\r\n\r\nparams['delete']('a', 1);\r\n// `undefined` case is a Chromium 117 bug\r\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\r\nparams['delete']('b', undefined);\r\n\r\nif (params + '' !== 'a=2') {\r\n  defineBuiltIn(URLSearchParamsPrototype, 'delete', function (name /* , value */) {\r\n    var length = arguments.length;\r\n    var $value = length < 2 ? undefined : arguments[1];\r\n    if (length && $value === undefined) return $delete(this, name);\r\n    var entries = [];\r\n    forEach(this, function (v, k) { // also validates `this`\r\n      push(entries, { key: k, value: v });\r\n    });\r\n    validateArgumentsLength(length, 1);\r\n    var key = toString(name);\r\n    var value = toString($value);\r\n    var index = 0;\r\n    var dindex = 0;\r\n    var found = false;\r\n    var entriesLength = entries.length;\r\n    var entry;\r\n    while (index < entriesLength) {\r\n      entry = entries[index++];\r\n      if (found || entry.key === key) {\r\n        found = true;\r\n        $delete(this, entry.key);\r\n      } else dindex++;\r\n    }\r\n    while (dindex < entriesLength) {\r\n      entry = entries[dindex++];\r\n      if (!(entry.key === key && entry.value === value)) append(this, entry.key, entry.value);\r\n    }\r\n  }, { enumerable: true, unsafe: true });\r\n}\r\n", "'use strict';\r\nvar fails = require('../internals/fails');\r\n\r\nmodule.exports = function (METHOD_NAME, argument) {\r\n  var method = [][METHOD_NAME];\r\n  return !!method && fails(function () {\r\n    // eslint-disable-next-line no-useless-call -- required for testing\r\n    method.call(null, argument || function () { return 1; }, 1);\r\n  });\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"page-wrapper\"},[_c('div',{staticClass:\"page-container\"},[_c('div',{staticClass:\"page-title\"},[_vm._v(\" \"+_vm._s(this.$router.currentRoute.name)+\" \"),_c('el-button',{staticStyle:{\"float\":\"right\"},attrs:{\"type\":\"text\",\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\"刷新 \")])],1),_c('div',{staticClass:\"search-container\"},[_c('el-form',{staticClass:\"search-form\",attrs:{\"model\":_vm.search,\"label-width\":\"80px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"用户名称\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入用户名称\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.nickname),callback:function ($$v) {_vm.$set(_vm.search, \"nickname\", $$v)},expression:\"search.nickname\"}})],1)],1),_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"手机号码\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入手机号码\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.phone),callback:function ($$v) {_vm.$set(_vm.search, \"phone\", $$v)},expression:\"search.phone\"}})],1)],1),_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"公司名称\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入公司名称\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.company),callback:function ($$v) {_vm.$set(_vm.search, \"company\", $$v)},expression:\"search.company\"}})],1)],1),_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"用户来源\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择来源\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.yuangong_id),callback:function ($$v) {_vm.$set(_vm.search, \"yuangong_id\", $$v)},expression:\"search.yuangong_id\"}},[_c('el-option',{attrs:{\"label\":\"小程序注册\",\"value\":\"小程序注册\"}}),_c('el-option',{attrs:{\"label\":\"后台创建\",\"value\":\"后台创建\"}}),_c('el-option',{attrs:{\"label\":\"直接注册\",\"value\":\"直接注册\"}})],1)],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"联系人\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入联系人\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.linkman),callback:function ($$v) {_vm.$set(_vm.search, \"linkman\", $$v)},expression:\"search.linkman\"}})],1)],1),_c('el-col',{attrs:{\"span\":6}},[_c('el-form-item',{attrs:{\"label\":\"联系号码\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入联系号码\",\"clearable\":\"\",\"size\":\"small\"},model:{value:(_vm.search.linkphone),callback:function ($$v) {_vm.$set(_vm.search, \"linkphone\", $$v)},expression:\"search.linkphone\"}})],1)],1),_c('el-col',{attrs:{\"span\":8}},[_c('el-form-item',{attrs:{\"label\":\"注册时间\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"size\":\"small\"},model:{value:(_vm.search.dateRange),callback:function ($$v) {_vm.$set(_vm.search, \"dateRange\", $$v)},expression:\"search.dateRange\"}})],1)],1),_c('el-col',{attrs:{\"span\":4}},[_c('el-form-item',[_c('div',{staticClass:\"search-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.searchData()}}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.resetSearch()}}},[_vm._v(\" 重置 \")])],1)])],1)],1)],1),_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportSelectedData}},[_vm._v(\" \"+_vm._s(_vm.selectedUsers.length > 0 ? `导出选中数据 (${_vm.selectedUsers.length})` : '导出全部数据')+\" \")]),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\",\"icon\":\"el-icon-upload2\"},on:{\"click\":_vm.openUpload}},[_vm._v(\" 导入用户 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":_vm.addUser}},[_vm._v(\" 添加用户 \")]),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"success\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.downloadTemplate}},[_vm._v(\" 下载导入模板 \")])],1)],1),_c('div',{staticClass:\"data-table\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"userTable\",staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.list,\"border\":true,\"header-cell-style\":{background:'#fafafa',color:'#606266'}},on:{\"sort-change\":_vm.handleSortChange,\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"type\":\"index\",\"label\":\"序号\",\"width\":\"60\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"头像\",\"width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"avatar-container\"},[(scope.row.headimg && scope.row.headimg !== '')?_c('div',{staticClass:\"avatar-wrapper\"},[_c('img',{staticClass:\"user-avatar\",attrs:{\"src\":scope.row.headimg},on:{\"click\":function($event){return _vm.showImage(scope.row.headimg)}}})]):_c('div',{staticClass:\"no-avatar\"},[_c('i',{staticClass:\"el-icon-user-solid\"})])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"nickname\",\"label\":\"用户名称\",\"sortable\":\"\",\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"user-name clickable\",on:{\"click\":function($event){return _vm.viewData(scope.row.id)}}},[_vm._v(_vm._s(scope.row.nickname || '未设置'))]),_c('div',{staticClass:\"user-phone\"},[_vm._v(_vm._s(scope.row.phone))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"company\",\"label\":\"公司名称\",\"sortable\":\"\",\"min-width\":\"150\"}}),_c('el-table-column',{attrs:{\"prop\":\"linkman\",\"label\":\"联系人\",\"sortable\":\"\",\"min-width\":\"100\"}}),_c('el-table-column',{attrs:{\"prop\":\"linkphone\",\"label\":\"联系号码\",\"sortable\":\"\",\"min-width\":\"120\"}}),_c('el-table-column',{attrs:{\"prop\":\"yuangong_id\",\"label\":\"用户来源\",\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(_vm._s(scope.row.yuangong_id || '直接注册'))])]}}])}),_c('el-table-column',{attrs:{\"label\":\"债务人数量\",\"min-width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"size\":\"small\",\"type\":_vm.getDebtCountType(scope.row.debts)}},[_vm._v(\" \"+_vm._s(_vm.getDebtCount(scope.row.debts))+\"人 \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"债务总金额\",\"min-width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"debt-amount\",class:{ 'has-debt': _vm.getTotalDebtAmount(scope.row.debts) > 0 }},[_vm._v(\" ¥\"+_vm._s(_vm.formatAmount(_vm.getTotalDebtAmount(scope.row.debts)))+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"end_time\",\"label\":\"到期时间\",\"min-width\":\"120\",\"sortable\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"注册时间\",\"sortable\":\"\",\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(scope.row.create_time))])]}}])}),_c('el-table-column',{attrs:{\"label\":\"最后登录\",\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(scope.row.last_login_time || '未登录'))])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons-table\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.viewData(scope.row.id)}}},[_vm._v(\" 查看 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" 编辑 \")]),_c('el-dropdown',{attrs:{\"trigger\":\"click\"}},[_c('el-button',{attrs:{\"size\":\"mini\"}},[_vm._v(\" 更多\"),_c('i',{staticClass:\"el-icon-arrow-down el-icon--right\"})]),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\"},slot:\"dropdown\"},[_c('el-dropdown-item',{nativeOn:{\"click\":function($event){return _vm.order(scope.row)}}},[_vm._v(\"制作订单\")]),(_vm.is_del)?_c('el-dropdown-item',{staticStyle:{\"color\":\"#f56c6c\"},nativeOn:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\"移除用户\")]):_vm._e()],1)],1)],1)]}}])})],1)],1),_c('div',{staticClass:\"pagination-container\"},[_c('div',{staticClass:\"pagination-info\"},[_c('span',[_vm._v(\"共 \"+_vm._s(_vm.total)+\" 条\")]),_c('span',[_vm._v(\"第 \"+_vm._s(_vm.page)+\" 页\")])]),_c('el-pagination',{attrs:{\"page-sizes\":[10, 20, 50, 100],\"page-size\":_vm.size,\"current-page\":_vm.page,\"layout\":\"sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)]),_c('el-drawer',{attrs:{\"title\":\"用户详情\",\"visible\":_vm.drawerViewVisible,\"direction\":\"rtl\",\"size\":\"60%\",\"before-close\":_vm.handleDrawerClose},on:{\"update:visible\":function($event){_vm.drawerViewVisible=$event}}},[_c('div',{staticClass:\"drawer-content-wrapper\"},[_c('div',{staticClass:\"drawer-sidebar\"},[_c('el-menu',{staticClass:\"drawer-menu\",attrs:{\"default-active\":_vm.activeTab},on:{\"select\":_vm.handleTabSelect}},[_c('el-menu-item',{attrs:{\"index\":\"customer\"}},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',[_vm._v(\"客户信息\")])]),_c('el-menu-item',{attrs:{\"index\":\"member\"}},[_c('i',{staticClass:\"el-icon-medal\"}),_c('span',[_vm._v(\"会员信息\")])]),_c('el-menu-item',{attrs:{\"index\":\"debts\"}},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(\"债务人信息\")])]),_c('el-menu-item',{attrs:{\"index\":\"attachments\"}},[_c('i',{staticClass:\"el-icon-folder-opened\"}),_c('span',[_vm._v(\"附件信息\")])])],1)],1),_c('div',{staticClass:\"drawer-content\"},[(_vm.activeTab === 'customer')?_c('div',{staticClass:\"edit-mode-toggle\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":_vm.isEditMode ? 'el-icon-view' : 'el-icon-edit'},on:{\"click\":_vm.toggleEditMode}},[_vm._v(\" \"+_vm._s(_vm.isEditMode ? '查看模式' : '编辑模式')+\" \")]),(_vm.isEditMode)?_c('el-button',{attrs:{\"type\":\"success\",\"icon\":\"el-icon-check\"},on:{\"click\":_vm.saveUserData}},[_vm._v(\" 保存 \")]):_vm._e(),(_vm.isEditMode)?_c('el-button',{attrs:{\"type\":\"info\",\"icon\":\"el-icon-close\"},on:{\"click\":_vm.cancelEdit}},[_vm._v(\" 取消 \")]):_vm._e()],1):_vm._e(),(_vm.activeTab === 'customer')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 客户信息 \")]),(!_vm.isEditMode)?_c('el-descriptions',{attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.currentUserInfo.company || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"手机号\"}},[_vm._v(_vm._s(_vm.currentUserInfo.phone || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"用户名称\"}},[_vm._v(_vm._s(_vm.currentUserInfo.nickname || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[_vm._v(_vm._s(_vm.currentUserInfo.linkman || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"联系电话\"}},[_vm._v(_vm._s(_vm.currentUserInfo.linkphone || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"用户来源\"}},[_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(_vm._s(_vm.currentUserInfo.yuangong_id || '直接注册'))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(_vm._s(_vm.currentUserInfo.lian_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(_vm._s(_vm.currentUserInfo.tiaojie_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(_vm._s(_vm.currentUserInfo.fawu_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"合同上传专员\"}},[_vm._v(_vm._s(_vm.currentUserInfo.htsczy_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"律师\"}},[_vm._v(_vm._s(_vm.currentUserInfo.ls_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"业务员\"}},[_vm._v(_vm._s(_vm.currentUserInfo.ywy_name || '未分配')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"头像\",\"span\":2}},[_c('div',{staticClass:\"avatar-display\"},[(_vm.currentUserInfo.headimg && _vm.currentUserInfo.headimg !== '')?_c('img',{staticClass:\"detail-avatar\",attrs:{\"src\":_vm.currentUserInfo.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.currentUserInfo.headimg)}}}):_c('div',{staticClass:\"no-avatar-large\"},[_c('i',{staticClass:\"el-icon-user-solid\"}),_c('span',[_vm._v(\"无头像\")])])])]),_c('el-descriptions-item',{attrs:{\"label\":\"注册时间\",\"span\":2}},[_vm._v(_vm._s(_vm.currentUserInfo.create_time || '未知')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"最后登录\",\"span\":2}},[_vm._v(_vm._s(_vm.currentUserInfo.last_login_time || '从未登录')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"会员到期\",\"span\":2}},[_vm._v(_vm._s(_vm.currentUserInfo.end_time || '未设置')+\" \")])],1):_vm._e(),(_vm.isEditMode)?_c('el-form',{ref:\"editForm\",attrs:{\"model\":_vm.editForm,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"公司名称\",\"prop\":\"company\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入公司名称\"},model:{value:(_vm.editForm.company),callback:function ($$v) {_vm.$set(_vm.editForm, \"company\", $$v)},expression:\"editForm.company\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"手机号\",\"prop\":\"phone\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入手机号\",\"disabled\":\"\"},model:{value:(_vm.editForm.phone),callback:function ($$v) {_vm.$set(_vm.editForm, \"phone\", $$v)},expression:\"editForm.phone\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"用户名称\",\"prop\":\"nickname\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入用户名称\"},model:{value:(_vm.editForm.nickname),callback:function ($$v) {_vm.$set(_vm.editForm, \"nickname\", $$v)},expression:\"editForm.nickname\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"联系人\",\"prop\":\"linkman\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入联系人\"},model:{value:(_vm.editForm.linkman),callback:function ($$v) {_vm.$set(_vm.editForm, \"linkman\", $$v)},expression:\"editForm.linkman\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"联系电话\",\"prop\":\"linkphone\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入联系电话\"},model:{value:(_vm.editForm.linkphone),callback:function ($$v) {_vm.$set(_vm.editForm, \"linkphone\", $$v)},expression:\"editForm.linkphone\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"登录密码\",\"prop\":\"password\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入新密码（留空不修改）\",\"type\":\"password\"},model:{value:(_vm.editForm.password),callback:function ($$v) {_vm.$set(_vm.editForm, \"password\", $$v)},expression:\"editForm.password\"}})],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"调解员\",\"prop\":\"tiaojie_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择调解员\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.tiaojie_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"tiaojie_id\", $$v)},expression:\"editForm.tiaojie_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.tiaojies),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"法务专员\",\"prop\":\"fawu_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择法务专员\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.fawu_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"fawu_id\", $$v)},expression:\"editForm.fawu_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.fawus),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"立案专员\",\"prop\":\"lian_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择立案专员\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.lian_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"lian_id\", $$v)},expression:\"editForm.lian_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.lians),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"合同上传专用\",\"prop\":\"htsczy_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择合同上传专用\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.htsczy_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"htsczy_id\", $$v)},expression:\"editForm.htsczy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.htsczy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"律师\",\"prop\":\"ls_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择律师\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.ls_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"ls_id\", $$v)},expression:\"editForm.ls_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ls),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"业务员\",\"prop\":\"ywy_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择业务员\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.editForm.ywy_id),callback:function ($$v) {_vm.$set(_vm.editForm, \"ywy_id\", $$v)},expression:\"editForm.ywy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ywy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1)],1)],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"开始时间\",\"prop\":\"start_time\"}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"placeholder\":\"选择开始时间\"},model:{value:(_vm.editForm.start_time),callback:function ($$v) {_vm.$set(_vm.editForm, \"start_time\", $$v)},expression:\"editForm.start_time\"}})],1)],1),_c('el-col',{attrs:{\"span\":12}},[_c('el-form-item',{attrs:{\"label\":\"会员年限\",\"prop\":\"year\"}},[_c('el-input-number',{attrs:{\"min\":0,\"max\":99,\"placeholder\":\"请输入会员年限\"},model:{value:(_vm.editForm.year),callback:function ($$v) {_vm.$set(_vm.editForm, \"year\", $$v)},expression:\"editForm.year\"}})],1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"头像\"}},[_c('div',{staticClass:\"avatar-display\"},[(_vm.editForm.headimg && _vm.editForm.headimg !== '')?_c('img',{staticClass:\"detail-avatar\",attrs:{\"src\":_vm.editForm.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.editForm.headimg)}}}):_c('div',{staticClass:\"no-avatar-large\"},[_c('i',{staticClass:\"el-icon-user-solid\"}),_c('span',[_vm._v(\"无头像\")])])])])],1):_vm._e()],1)]):_vm._e(),(_vm.activeTab === 'member')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-medal\"}),_vm._v(\" 会员信息 \")]),_c('el-descriptions',{attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"开始时间\"}},[_vm._v(_vm._s(_vm.currentUserInfo.start_time || '未设置')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"会员年限\"}},[_vm._v(_vm._s(_vm.currentUserInfo.year || 0)+\"年 \")]),_c('el-descriptions-item',{attrs:{\"label\":\"注册时间\"}},[_vm._v(_vm._s(_vm.currentUserInfo.create_time || '未知')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"最后登录\"}},[_vm._v(_vm._s(_vm.currentUserInfo.last_login_time || '从未登录')+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"会员到期\"}},[_vm._v(_vm._s(_vm.currentUserInfo.end_time || '未设置')+\" \")])],1)],1)]):_vm._e(),(_vm.activeTab === 'debts')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 债务人信息 \"),_c('el-button',{staticStyle:{\"float\":\"right\"},attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":_vm.addDebt}},[_c('i',{staticClass:\"el-icon-plus\"}),_vm._v(\" 添加债务人 \")])],1),(_vm.currentUserInfo.debts && _vm.currentUserInfo.debts.length > 0)?_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.currentUserInfo.debts || [],\"size\":\"small\"}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"120\"}}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"130\"}}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额（元）\",\"width\":\"120\"}}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}],null,false,3932961750)}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.editDebt(scope.row, scope.$index)}}},[_vm._v(\"编辑\")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.deleteDebt(scope.$index)}}},[_vm._v(\"删除\")])]}}],null,false,1147729191)})],1):_c('div',{staticClass:\"no-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(\"暂无债务人信息\")]),_c('br'),_c('el-button',{staticStyle:{\"margin-top\":\"10px\"},attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.addDebt}},[_c('i',{staticClass:\"el-icon-plus\"}),_vm._v(\" 添加第一个债务人 \")])],1)],1)]):_vm._e(),(_vm.activeTab === 'attachments')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-folder-opened\"}),_vm._v(\" 附件信息 \"),_c('el-button',{staticStyle:{\"float\":\"right\"},attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":_vm.addAttachment}},[_c('i',{staticClass:\"el-icon-plus\"}),_vm._v(\" 上传附件 \")])],1),_c('div',{staticClass:\"attachment-grid\"},[_c('div',{staticClass:\"attachment-item\"},[_c('div',{staticClass:\"attachment-title\"},[_vm._v(\"身份证照片\")]),_c('div',{staticClass:\"attachment-content\"},[(_vm.currentUserInfo.attachments && _vm.currentUserInfo.attachments.idCard && _vm.currentUserInfo.attachments.idCard.length > 0)?_c('div',{staticClass:\"image-list\"},_vm._l((_vm.currentUserInfo.attachments.idCard),function(img,index){return _c('div',{key:index,staticClass:\"image-item\"},[_c('img',{staticClass:\"attachment-image\",attrs:{\"src\":img.url},on:{\"click\":function($event){return _vm.showImage(img.url)}}}),_c('div',{staticClass:\"image-overlay\"},[_c('div',{staticClass:\"image-info\"},[_c('span',{staticClass:\"file-name\"},[_vm._v(_vm._s(img.name))]),_c('span',{staticClass:\"upload-time\"},[_vm._v(_vm._s(img.uploadTime))])]),_c('div',{staticClass:\"image-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadFile(img)}}},[_vm._v(\"下载\")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteAttachment('idCard', index)}}},[_vm._v(\"删除\")])],1)])])}),0):_c('div',{staticClass:\"no-attachment\"},[_c('i',{staticClass:\"el-icon-picture\"}),_c('span',[_vm._v(\"暂无身份证照片\")])]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.uploadIdCard}},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 上传身份证 \")])],1)]),_c('div',{staticClass:\"attachment-item\"},[_c('div',{staticClass:\"attachment-title\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"attachment-content\"},[(_vm.currentUserInfo.attachments && _vm.currentUserInfo.attachments.license && _vm.currentUserInfo.attachments.license.length > 0)?_c('div',{staticClass:\"image-list\"},_vm._l((_vm.currentUserInfo.attachments.license),function(img,index){return _c('div',{key:index,staticClass:\"image-item\"},[_c('img',{staticClass:\"attachment-image\",attrs:{\"src\":img.url},on:{\"click\":function($event){return _vm.showImage(img.url)}}}),_c('div',{staticClass:\"image-overlay\"},[_c('div',{staticClass:\"image-info\"},[_c('span',{staticClass:\"file-name\"},[_vm._v(_vm._s(img.name))]),_c('span',{staticClass:\"upload-time\"},[_vm._v(_vm._s(img.uploadTime))])]),_c('div',{staticClass:\"image-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadFile(img)}}},[_vm._v(\"下载\")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteAttachment('license', index)}}},[_vm._v(\"删除\")])],1)])])}),0):_c('div',{staticClass:\"no-attachment\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(\"暂无营业执照\")])]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.uploadLicense}},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 上传营业执照 \")])],1)]),_c('div',{staticClass:\"attachment-item\"},[_c('div',{staticClass:\"attachment-title\"},[_vm._v(\"其他附件\")]),_c('div',{staticClass:\"attachment-content\"},[(_vm.currentUserInfo.attachments && _vm.currentUserInfo.attachments.others && _vm.currentUserInfo.attachments.others.length > 0)?_c('div',{staticClass:\"file-list\"},_vm._l((_vm.currentUserInfo.attachments.others),function(file,index){return _c('div',{key:index,staticClass:\"file-item\"},[_c('div',{staticClass:\"file-icon\"},[_c('i',{staticClass:\"file-type-icon\",class:_vm.getFileIcon(file.type)})]),_c('div',{staticClass:\"file-info\"},[_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(file.name))]),_c('div',{staticClass:\"file-meta\"},[_c('span',{staticClass:\"file-size\"},[_vm._v(_vm._s(_vm.formatFileSize(file.size)))]),_c('span',{staticClass:\"upload-time\"},[_vm._v(_vm._s(file.uploadTime))])])]),_c('div',{staticClass:\"file-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadFile(file)}}},[_vm._v(\"下载\")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteAttachment('others', index)}}},[_vm._v(\"删除\")])],1)])}),0):_c('div',{staticClass:\"no-attachment\"},[_c('i',{staticClass:\"el-icon-folder\"}),_c('span',[_vm._v(\"暂无其他附件\")])]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.uploadOthers}},[_c('i',{staticClass:\"el-icon-upload\"}),_vm._v(\" 上传其他附件 \")])],1)])])])]):_vm._e()])])]),_c('el-drawer',{attrs:{\"title\":\"编辑用户\",\"visible\":_vm.drawerEditVisible,\"direction\":\"rtl\",\"size\":\"50%\",\"before-close\":_vm.handleDrawerClose},on:{\"update:visible\":function($event){_vm.drawerEditVisible=$event}}},[_c('div',{staticClass:\"drawer-content\"},[_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 客户信息 \")]),_c('el-descriptions',{attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.ruleForm.company)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"手机号\"}},[_vm._v(_vm._s(_vm.ruleForm.phone)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"名称\"}},[_vm._v(_vm._s(_vm.ruleForm.nickname)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[_vm._v(_vm._s(_vm.ruleForm.linkman)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"头像\",\"span\":2}},[_c('div',{staticClass:\"avatar-display\"},[(_vm.ruleForm.headimg && _vm.ruleForm.headimg !== '')?_c('img',{staticClass:\"detail-avatar\",attrs:{\"src\":_vm.ruleForm.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.headimg)}}}):_c('div',{staticClass:\"no-avatar-large\"},[_c('i',{staticClass:\"el-icon-user-solid\"}),_c('span',[_vm._v(\"无头像\")])])])]),_c('el-descriptions-item',{attrs:{\"label\":\"用户来源\",\"span\":2}},[_c('el-tag',{attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(_vm._s(_vm.ruleForm.yuangong_id || '直接注册'))])],1)],1)],1),_c('div',{staticClass:\"card\"},[_c('div',{staticClass:\"card-header\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\" 信息编辑 \")]),_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-width\":\"120px\"}},[_c('el-form-item',{attrs:{\"label\":\"公司名称\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.company),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"company\", $$v)},expression:\"ruleForm.company\"}})],1),_c('el-form-item',{attrs:{\"label\":\"联系人\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.linkman),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"linkman\", $$v)},expression:\"ruleForm.linkman\"}})],1),_c('el-form-item',{attrs:{\"label\":\"联系方式\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.linkphone),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"linkphone\", $$v)},expression:\"ruleForm.linkphone\"}})],1),_c('el-form-item',{attrs:{\"label\":\"登录密码\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.password),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"password\", $$v)},expression:\"ruleForm.password\"}})],1),_c('el-form-item',{attrs:{\"label\":\"调解员\",\"prop\":\"tiaojie_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.tiaojie_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"tiaojie_id\", $$v)},expression:\"ruleForm.tiaojie_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.tiaojies),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"法务专员\",\"prop\":\"fawu_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.fawu_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"fawu_id\", $$v)},expression:\"ruleForm.fawu_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.fawus),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"立案专员\",\"prop\":\"lian_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.lian_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"lian_id\", $$v)},expression:\"ruleForm.lian_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.lians),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"合同上传专用\",\"prop\":\"htsczy_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.htsczy_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"htsczy_id\", $$v)},expression:\"ruleForm.htsczy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.htsczy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"律师\",\"prop\":\"ls_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.ls_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"ls_id\", $$v)},expression:\"ruleForm.ls_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ls),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"业务员\",\"prop\":\"ywy_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.ywy_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"ywy_id\", $$v)},expression:\"ruleForm.ywy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ywy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label-width\":_vm.formLabelWidth,\"label\":\"营业执照\"}},[(_vm.ruleForm.license !='' && _vm.ruleForm.license!=null)?_c('img',{staticStyle:{\"width\":\"400px\",\"height\":\"400px\"},attrs:{\"src\":_vm.ruleForm.license},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.license)}}}):_vm._e()]),_c('el-form-item',{attrs:{\"label\":\"开始时间\",\"label-width\":_vm.formLabelWidth,\"prop\":\"day\"}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"placeholder\":\"选择日期\"},model:{value:(_vm.ruleForm.start_time),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"start_time\", $$v)},expression:\"ruleForm.start_time\"}})],1),_c('el-form-item',{attrs:{\"label\":\"会员年限\",\"label-width\":_vm.formLabelWidth}},[_c('el-input-number',{attrs:{\"min\":0,\"max\":99,\"label\":\"请输入年份\"},model:{value:(_vm.ruleForm.year),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"year\", $$v)},expression:\"ruleForm.year\"}})],1)],1),_c('div',{staticClass:\"drawer-footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.drawerEditVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"保 存\")])],1)],1)])]),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{attrs:{\"title\":\"制作订单\",\"visible\":_vm.dialogFormOrder,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogFormOrder=$event}}},[_c('el-row',[_c('el-descriptions',{attrs:{\"title\":\"客户信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.info.company)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"手机号\"}},[_vm._v(_vm._s(_vm.info.phone)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"名称\"}},[_vm._v(_vm._s(_vm.info.nickname)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[_vm._v(_vm._s(_vm.info.linkman)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"头像\"}},[(_vm.info.headimg !='' && _vm.info.headimg!=null)?_c('img',{staticStyle:{\"width\":\"50px\",\"height\":\"50px\"},attrs:{\"src\":_vm.info.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.headimg)}}}):_vm._e()]),_c('el-descriptions-item',{attrs:{\"label\":\"用户来源\"}},[_vm._v(_vm._s(_vm.info.yuangong_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(_vm._s(_vm.info.tiaojie_name)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(_vm._s(_vm.info.fawu_name)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(_vm._s(_vm.info.lian_name)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"合同上传专用\"}},[_vm._v(_vm._s(_vm.info.htsczy_name)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"律师\"}},[_vm._v(_vm._s(_vm.info.ls_name)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"业务员\"}},[_vm._v(_vm._s(_vm.info.ywy_name)+\" \")])],1)],1),_c('el-descriptions',{attrs:{\"title\":\"下单内容\"}}),_c('el-form',{ref:\"orderForm\",attrs:{\"model\":_vm.orderForm,\"rules\":_vm.rules2,\"label-width\":\"80px\",\"mode\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"套餐\",\"prop\":\"taocan_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\"},on:{\"change\":_vm.changeTaocan},model:{value:(_vm.orderForm.taocan_id),callback:function ($$v) {_vm.$set(_vm.orderForm, \"taocan_id\", $$v)},expression:\"orderForm.taocan_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.taocans),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"总金额\"}},[_c('el-input',{staticClass:\"el_input2\",attrs:{\"type\":\"number\",\"placeholder\":\"请输入内容\"},model:{value:(_vm.orderForm.total_price),callback:function ($$v) {_vm.$set(_vm.orderForm, \"total_price\", $$v)},expression:\"orderForm.total_price\"}})],1),_c('el-form-item',{attrs:{\"label\":\"实际支付\",\"prop\":\"pay_price\"}},[_c('el-input',{staticClass:\"el_input2\",attrs:{\"placeholder\":\"请输入内容\"},model:{value:(_vm.orderForm.pay_price),callback:function ($$v) {_vm.$set(_vm.orderForm, \"pay_price\", $$v)},expression:\"orderForm.pay_price\"}})],1),_c('el-form-item',{attrs:{\"label\":\"客户描述\"}},[_c('el-input',{staticClass:\"el_input2\",attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入内容\"},model:{value:(_vm.orderForm.desc),callback:function ($$v) {_vm.$set(_vm.orderForm, \"desc\", $$v)},expression:\"orderForm.desc\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormOrder = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData2()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"导入用户\",\"visible\":_vm.uploadVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.uploadVisible=$event},\"close\":_vm.closeUploadDialog}},[_c('el-form',{ref:\"uploadForm\",attrs:{\"label-position\":\"right\",\"label-width\":\"110px\"}},[_c('el-form-item',{attrs:{\"label\":\"选择文件:\"}},[_c('el-upload',{ref:\"upload\",attrs:{\"auto-upload\":false,\"action\":_vm.uploadAction,\"data\":_vm.uploadData,\"on-success\":_vm.uploadSuccess,\"before-upload\":_vm.checkFile,\"accept\":\".xls,.xlsx\",\"limit\":\"1\",\"multiple\":\"false\"}},[_c('el-button',{attrs:{\"slot\":\"trigger\",\"size\":\"small\",\"type\":\"primary\"},slot:\"trigger\"},[_vm._v(\"选择文件\")])],1)],1),_c('div',{staticStyle:{\"text-align\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"loading\":_vm.submitOrderLoading2},on:{\"click\":_vm.submitUpload}},[_vm._v(\"提交 \")]),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"取消\")])],1)],1)],1),_c('el-dialog',{attrs:{\"title\":\"用户详情\",\"visible\":_vm.dialogViewUserDetail,\"close-on-click-modal\":false,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.dialogViewUserDetail=$event}}},[_c('user-details',{attrs:{\"id\":_vm.currentId}})],1),_c('el-dialog',{attrs:{\"title\":\"新增用户\",\"visible\":_vm.dialogAddUser,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogAddUser=$event}}},[_c('el-descriptions',{attrs:{\"title\":\"信息添加\"}}),_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"手机账号\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.phone),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"phone\", $$v)},expression:\"ruleForm.phone\"}})],1),_c('el-form-item',{attrs:{\"label\":\"公司名称\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.company),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"company\", $$v)},expression:\"ruleForm.company\"}})],1),_c('el-form-item',{attrs:{\"label\":\"联系人\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.linkman),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"linkman\", $$v)},expression:\"ruleForm.linkman\"}})],1),_c('el-form-item',{attrs:{\"label\":\"联系方式\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.linkphone),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"linkphone\", $$v)},expression:\"ruleForm.linkphone\"}})],1),_c('el-form-item',{attrs:{\"label\":\"登录密码\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.password),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"password\", $$v)},expression:\"ruleForm.password\"}})],1),_c('el-form-item',{attrs:{\"label\":\"调解员\",\"prop\":\"tiaojie_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.tiaojie_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"tiaojie_id\", $$v)},expression:\"ruleForm.tiaojie_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.tiaojies),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"法务专员\",\"prop\":\"fawu_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.fawu_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"fawu_id\", $$v)},expression:\"ruleForm.fawu_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.fawus),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"立案专员\",\"prop\":\"lian_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.lian_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"lian_id\", $$v)},expression:\"ruleForm.lian_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.lians),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"合同上传专用\",\"prop\":\"htsczy_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.htsczy_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"htsczy_id\", $$v)},expression:\"ruleForm.htsczy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.htsczy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"律师\",\"prop\":\"ls_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.ls_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"ls_id\", $$v)},expression:\"ruleForm.ls_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ls),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":\"业务员\",\"prop\":\"ywy_id\",\"label-width\":_vm.formLabelWidth}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.ywy_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"ywy_id\", $$v)},expression:\"ruleForm.ywy_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.ywy),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label-width\":_vm.formLabelWidth,\"label\":\"营业执照\"}},[(_vm.ruleForm.license !='' && _vm.ruleForm.license!=null)?_c('img',{staticStyle:{\"width\":\"400px\",\"height\":\"400px\"},attrs:{\"src\":_vm.ruleForm.license},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.license)}}}):_vm._e()]),_c('el-form-item',{attrs:{\"label\":\"开始时间\",\"label-width\":_vm.formLabelWidth,\"prop\":\"day\"}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"placeholder\":\"选择日期\"},model:{value:(_vm.ruleForm.start_time),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"start_time\", $$v)},expression:\"ruleForm.start_time\"}})],1),_c('el-form-item',{attrs:{\"label\":\"会员年限\",\"label-width\":_vm.formLabelWidth}},[_c('el-input-number',{attrs:{\"min\":0,\"max\":99,\"label\":\"请输入年份\"},model:{value:(_vm.ruleForm.year),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"year\", $$v)},expression:\"ruleForm.year\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogAddUser = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible,\"width\":\"50%\",\"center\":\"\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\",\"height\":\"auto\"},attrs:{\"src\":_vm.show_image}})]),_c('el-dialog',{attrs:{\"title\":_vm.debtDialogTitle,\"visible\":_vm.debtDialogVisible,\"width\":\"500px\"},on:{\"update:visible\":function($event){_vm.debtDialogVisible=$event},\"close\":_vm.closeDebtDialog}},[_c('el-form',{ref:\"debtForm\",attrs:{\"model\":_vm.debtForm,\"rules\":_vm.debtRules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"债务人姓名\",\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入债务人姓名\"},model:{value:(_vm.debtForm.name),callback:function ($$v) {_vm.$set(_vm.debtForm, \"name\", $$v)},expression:\"debtForm.name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"债务人电话\",\"prop\":\"tel\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入债务人电话\"},model:{value:(_vm.debtForm.tel),callback:function ($$v) {_vm.$set(_vm.debtForm, \"tel\", $$v)},expression:\"debtForm.tel\"}})],1),_c('el-form-item',{attrs:{\"label\":\"债务金额\",\"prop\":\"money\"}},[_c('el-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":0,\"precision\":2,\"placeholder\":\"请输入债务金额\"},model:{value:(_vm.debtForm.money),callback:function ($$v) {_vm.$set(_vm.debtForm, \"money\", $$v)},expression:\"debtForm.money\"}})],1),_c('el-form-item',{attrs:{\"label\":\"状态\",\"prop\":\"status\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择状态\"},model:{value:(_vm.debtForm.status),callback:function ($$v) {_vm.$set(_vm.debtForm, \"status\", $$v)},expression:\"debtForm.status\"}},[_c('el-option',{attrs:{\"label\":\"待处理\",\"value\":\"待处理\"}}),_c('el-option',{attrs:{\"label\":\"处理中\",\"value\":\"处理中\"}}),_c('el-option',{attrs:{\"label\":\"已完成\",\"value\":\"已完成\"}})],1)],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":_vm.closeDebtDialog}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveDebt}},[_vm._v(\"确定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"导入用户\",\"visible\":_vm.uploadVisible,\"width\":\"600px\"},on:{\"update:visible\":function($event){_vm.uploadVisible=$event},\"close\":_vm.closeUploadDialog}},[_c('div',{staticClass:\"upload-container\"},[_c('div',{staticClass:\"upload-tips\"},[_c('el-alert',{attrs:{\"title\":\"导入说明\",\"type\":\"info\",\"closable\":false,\"show-icon\":\"\"}},[_c('div',{attrs:{\"slot\":\"description\"},slot:\"description\"},[_c('p',[_vm._v(\"1. 请先下载导入模板，按照模板格式填写用户信息\")]),_c('p',[_vm._v(\"2. 支持的文件格式：.xls, .xlsx\")]),_c('p',[_vm._v(\"3. 单次最多导入1000条用户数据\")]),_c('p',[_vm._v(\"4. 手机号码为必填项，且不能重复\")])])])],1),_c('div',{staticClass:\"upload-actions\"},[_c('el-button',{attrs:{\"type\":\"success\",\"icon\":\"el-icon-download\"},on:{\"click\":_vm.downloadTemplate}},[_vm._v(\" 下载导入模板 \")])],1),_c('div',{staticClass:\"upload-area\"},[_c('el-upload',{ref:\"upload\",attrs:{\"action\":_vm.uploadAction,\"data\":_vm.uploadData,\"on-success\":_vm.handleUploadSuccess,\"on-error\":_vm.handleUploadError,\"before-upload\":_vm.beforeUpload,\"auto-upload\":false,\"limit\":1,\"file-list\":_vm.fileList,\"accept\":\".xls,.xlsx\",\"drag\":\"\"}},[_c('i',{staticClass:\"el-icon-upload\"}),_c('div',{staticClass:\"el-upload__text\"},[_vm._v(\"将文件拖到此处，或\"),_c('em',[_vm._v(\"点击上传\")])]),_c('div',{staticClass:\"el-upload__tip\",attrs:{\"slot\":\"tip\"},slot:\"tip\"},[_vm._v(\"只能上传xls/xlsx文件，且不超过10MB\")])])],1),_c('div',{staticClass:\"upload-options\"},[_c('el-checkbox',{model:{value:(_vm.uploadData.review),callback:function ($$v) {_vm.$set(_vm.uploadData, \"review\", $$v)},expression:\"uploadData.review\"}},[_vm._v(\"导入前预览数据\")])],1)]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":_vm.closeUploadDialog}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.submitOrderLoading2},on:{\"click\":_vm.submitUpload}},[_vm._v(\" \"+_vm._s(_vm.submitOrderLoading2 ? '导入中...' : '开始导入')+\" \")])],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div class=\"page-wrapper\">\r\n        <div class=\"page-container\">\r\n            <!-- 页面标题 -->\r\n            <div class=\"page-title\">\r\n                {{ this.$router.currentRoute.name }}\r\n                <el-button\r\n                        style=\"float: right\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                        icon=\"el-icon-refresh\"\r\n                >刷新\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 搜索筛选区域 -->\r\n            <div class=\"search-container\">\r\n                <el-form :model=\"search\" class=\"search-form\" label-width=\"80px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户名称\">\r\n                                <el-input\r\n                                    v-model=\"search.nickname\"\r\n                                    placeholder=\"请输入用户名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"手机号码\">\r\n                                <el-input\r\n                                    v-model=\"search.phone\"\r\n                                    placeholder=\"请输入手机号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"公司名称\">\r\n                                <el-input\r\n                                    v-model=\"search.company\"\r\n                                    placeholder=\"请输入公司名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户来源\">\r\n                                <el-select\r\n                                    v-model=\"search.yuangong_id\"\r\n                                    placeholder=\"请选择来源\"\r\n                                    clearable\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                    <el-option label=\"小程序注册\" value=\"小程序注册\"></el-option>\r\n                                    <el-option label=\"后台创建\" value=\"后台创建\"></el-option>\r\n                                    <el-option label=\"直接注册\" value=\"直接注册\"></el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系人\">\r\n                                <el-input\r\n                                    v-model=\"search.linkman\"\r\n                                    placeholder=\"请输入联系人\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系号码\">\r\n                                <el-input\r\n                                    v-model=\"search.linkphone\"\r\n                                    placeholder=\"请输入联系号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                            <el-form-item label=\"注册时间\">\r\n                                <el-date-picker\r\n                                    v-model=\"search.dateRange\"\r\n                                    type=\"daterange\"\r\n                                    range-separator=\"至\"\r\n                                    start-placeholder=\"开始日期\"\r\n                                    end-placeholder=\"结束日期\"\r\n                                    format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                            <el-form-item>\r\n                                <div class=\"search-buttons\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        icon=\"el-icon-search\"\r\n                                        @click=\"searchData()\"\r\n                                        size=\"small\">\r\n                                        搜索\r\n                                    </el-button>\r\n                                    <el-button\r\n                                        icon=\"el-icon-refresh\"\r\n                                        @click=\"resetSearch()\"\r\n                                        size=\"small\">\r\n                                        重置\r\n                                    </el-button>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n\r\n                <!-- 操作按钮区域 -->\r\n                <div class=\"action-buttons\">\r\n                    <el-button\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        icon=\"el-icon-download\"\r\n                        @click=\"exportSelectedData\">\r\n                        {{ selectedUsers.length > 0 ? `导出选中数据 (${selectedUsers.length})` : '导出全部数据' }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\" @click=\"openUpload\">\r\n                        导入用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addUser\">\r\n                        添加用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 数据表格 -->\r\n            <div class=\"data-table\">\r\n                <el-table\r\n                        :data=\"list\"\r\n                        style=\"width: 100%\"\r\n                        v-loading=\"loading\"\r\n                        @sort-change=\"handleSortChange\"\r\n                        @selection-change=\"handleSelectionChange\"\r\n                        :border=\"true\"\r\n                        :header-cell-style=\"{background:'#fafafa',color:'#606266'}\"\r\n                        ref=\"userTable\"\r\n                >\r\n                    <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n                    <el-table-column prop=\"\" label=\"头像\" width=\"80\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"avatar-container\">\r\n                                <div v-if=\"scope.row.headimg && scope.row.headimg !== ''\" class=\"avatar-wrapper\">\r\n                                    <img\r\n                                        class=\"user-avatar\"\r\n                                        :src=\"scope.row.headimg\"\r\n                                        @click=\"showImage(scope.row.headimg)\"\r\n                                    />\r\n                                </div>\r\n                                <div v-else class=\"no-avatar\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nickname\" label=\"用户名称\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"user-info\">\r\n                                <div class=\"user-name clickable\" @click=\"viewData(scope.row.id)\">{{ scope.row.nickname || '未设置' }}</div>\r\n                                <div class=\"user-phone\">{{ scope.row.phone }}</div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"company\" label=\"公司名称\" sortable min-width=\"150\"></el-table-column>\r\n                    <el-table-column prop=\"linkman\" label=\"联系人\" sortable min-width=\"100\"></el-table-column>\r\n                    <el-table-column prop=\"linkphone\" label=\"联系号码\" sortable min-width=\"120\"></el-table-column>\r\n                    <el-table-column prop=\"yuangong_id\" label=\"用户来源\" min-width=\"100\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ scope.row.yuangong_id || '直接注册' }}</el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务人数量\" min-width=\"100\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" :type=\"getDebtCountType(scope.row.debts)\">\r\n                                {{ getDebtCount(scope.row.debts) }}人\r\n                            </el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务总金额\" min-width=\"120\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"debt-amount\" :class=\"{ 'has-debt': getTotalDebtAmount(scope.row.debts) > 0 }\">\r\n                                ¥{{ formatAmount(getTotalDebtAmount(scope.row.debts)) }}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"end_time\" label=\"到期时间\" min-width=\"120\" sortable></el-table-column>\r\n                    <el-table-column prop=\"create_time\" label=\"注册时间\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.create_time }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最后登录\" min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.last_login_time || '未登录' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"action-buttons-table\">\r\n                                <el-button type=\"primary\" size=\"mini\" @click=\"viewData(scope.row.id)\">\r\n                                    查看\r\n                                </el-button>\r\n                                <el-button type=\"success\" size=\"mini\" @click=\"editData(scope.row.id)\">\r\n                                    编辑\r\n                                </el-button>\r\n                                <el-dropdown trigger=\"click\">\r\n                                    <el-button size=\"mini\">\r\n                                        更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu slot=\"dropdown\">\r\n                                        <el-dropdown-item @click.native=\"order(scope.row)\">制作订单</el-dropdown-item>\r\n                                        <el-dropdown-item v-if=\"is_del\" @click.native=\"delData(scope.$index, scope.row.id)\" style=\"color: #f56c6c;\">移除用户</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n                <div class=\"pagination-info\">\r\n                    <span>共 {{ total }} 条</span>\r\n                    <span>第 {{ page }} 页</span>\r\n                </div>\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"size\"\r\n                        :current-page=\"page\"\r\n                        layout=\"sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                        background\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 用户详情抽屉 -->\r\n        <el-drawer\r\n            title=\"用户详情\"\r\n            :visible.sync=\"drawerViewVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleTabSelect\">\r\n                        <el-menu-item index=\"customer\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>客户信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"member\">\r\n                            <i class=\"el-icon-medal\"></i>\r\n                            <span>会员信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"debts\">\r\n                            <i class=\"el-icon-document\"></i>\r\n                            <span>债务人信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"attachments\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>附件信息</span>\r\n                        </el-menu-item>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 编辑模式切换按钮 -->\r\n                    <div class=\"edit-mode-toggle\" v-if=\"activeTab === 'customer'\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            :icon=\"isEditMode ? 'el-icon-view' : 'el-icon-edit'\"\r\n                            @click=\"toggleEditMode\">\r\n                            {{ isEditMode ? '查看模式' : '编辑模式' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"success\"\r\n                            icon=\"el-icon-check\"\r\n                            @click=\"saveUserData\">\r\n                            保存\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"info\"\r\n                            icon=\"el-icon-close\"\r\n                            @click=\"cancelEdit\">\r\n                            取消\r\n                        </el-button>\r\n                    </div>\r\n\r\n                    <!-- 客户信息标签页 -->\r\n                    <div v-if=\"activeTab === 'customer'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                客户信息\r\n                            </div>\r\n                    <!-- 查看模式 -->\r\n                    <el-descriptions v-if=\"!isEditMode\" :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            currentUserInfo.company || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            currentUserInfo.phone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户名称\">{{\r\n                            currentUserInfo.nickname || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            currentUserInfo.linkman || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系电话\">{{\r\n                            currentUserInfo.linkphone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ currentUserInfo.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"立案专员\">{{\r\n                            currentUserInfo.lian_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"调解员\">{{\r\n                            currentUserInfo.tiaojie_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"法务专员\">{{\r\n                            currentUserInfo.fawu_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"合同上传专员\">{{\r\n                            currentUserInfo.htsczy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"律师\">{{\r\n                            currentUserInfo.ls_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"业务员\">{{\r\n                            currentUserInfo.ywy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"currentUserInfo.headimg && currentUserInfo.headimg !== ''\"\r\n                                     :src=\"currentUserInfo.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(currentUserInfo.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"注册时间\" :span=\"2\">{{\r\n                            currentUserInfo.create_time || '未知'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"最后登录\" :span=\"2\">{{\r\n                            currentUserInfo.last_login_time || '从未登录'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"会员到期\" :span=\"2\">{{\r\n                            currentUserInfo.end_time || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n\r\n                    <!-- 编辑模式 -->\r\n                    <el-form v-if=\"isEditMode\" :model=\"editForm\" :rules=\"rules\" ref=\"editForm\" label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"公司名称\" prop=\"company\">\r\n                                    <el-input v-model=\"editForm.company\" placeholder=\"请输入公司名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"手机号\" prop=\"phone\">\r\n                                    <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号\" disabled></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"用户名称\" prop=\"nickname\">\r\n                                    <el-input v-model=\"editForm.nickname\" placeholder=\"请输入用户名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                                    <el-input v-model=\"editForm.linkman\" placeholder=\"请输入联系人\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"linkphone\">\r\n                                    <el-input v-model=\"editForm.linkphone\" placeholder=\"请输入联系电话\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"登录密码\" prop=\"password\">\r\n                                    <el-input v-model=\"editForm.password\" placeholder=\"请输入新密码（留空不修改）\" type=\"password\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"调解员\" prop=\"tiaojie_id\">\r\n                                    <el-select v-model=\"editForm.tiaojie_id\" placeholder=\"请选择调解员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in tiaojies\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"法务专员\" prop=\"fawu_id\">\r\n                                    <el-select v-model=\"editForm.fawu_id\" placeholder=\"请选择法务专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in fawus\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"立案专员\" prop=\"lian_id\">\r\n                                    <el-select v-model=\"editForm.lian_id\" placeholder=\"请选择立案专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in lians\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\">\r\n                                    <el-select v-model=\"editForm.htsczy_id\" placeholder=\"请选择合同上传专用\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in htsczy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"律师\" prop=\"ls_id\">\r\n                                    <el-select v-model=\"editForm.ls_id\" placeholder=\"请选择律师\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ls\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"业务员\" prop=\"ywy_id\">\r\n                                    <el-select v-model=\"editForm.ywy_id\" placeholder=\"请选择业务员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ywy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"开始时间\" prop=\"start_time\">\r\n                                    <el-date-picker\r\n                                        v-model=\"editForm.start_time\"\r\n                                        type=\"date\"\r\n                                        format=\"yyyy-MM-dd\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        placeholder=\"选择开始时间\">\r\n                                    </el-date-picker>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"会员年限\" prop=\"year\">\r\n                                    <el-input-number\r\n                                        v-model=\"editForm.year\"\r\n                                        :min=\"0\"\r\n                                        :max=\"99\"\r\n                                        placeholder=\"请输入会员年限\">\r\n                                    </el-input-number>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-form-item label=\"头像\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"editForm.headimg && editForm.headimg !== ''\"\r\n                                     :src=\"editForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(editForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 会员信息标签页 -->\r\n                    <div v-if=\"activeTab === 'member'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-medal\"></i>\r\n                                会员信息\r\n                            </div>\r\n                            <el-descriptions :column=\"2\" border>\r\n                                <el-descriptions-item label=\"开始时间\">{{\r\n                                    currentUserInfo.start_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员年限\">{{\r\n                                    currentUserInfo.year || 0\r\n                                    }}年\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"注册时间\">{{\r\n                                    currentUserInfo.create_time || '未知'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"最后登录\">{{\r\n                                    currentUserInfo.last_login_time || '从未登录'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员到期\">{{\r\n                                    currentUserInfo.end_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 债务人信息标签页 -->\r\n                    <div v-if=\"activeTab === 'debts'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                债务人信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addDebt\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加债务人\r\n                                </el-button>\r\n                            </div>\r\n                            <el-table\r\n                                :data=\"currentUserInfo.debts || []\"\r\n                                style=\"width: 100%\"\r\n                                size=\"small\"\r\n                                v-if=\"currentUserInfo.debts && currentUserInfo.debts.length > 0\">\r\n                                <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"130\"></el-table-column>\r\n                                <el-table-column prop=\"money\" label=\"债务金额（元）\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-tag :type=\"scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info'\" size=\"small\">\r\n                                            {{ scope.row.status }}\r\n                                        </el-tag>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column label=\"操作\" width=\"150\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" size=\"mini\" @click=\"editDebt(scope.row, scope.$index)\">编辑</el-button>\r\n                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteDebt(scope.$index)\">删除</el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                            <div v-else class=\"no-data\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>暂无债务人信息</span>\r\n                                <br>\r\n                                <el-button type=\"primary\" size=\"small\" @click=\"addDebt\" style=\"margin-top: 10px;\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加第一个债务人\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 附件信息标签页 -->\r\n                    <div v-if=\"activeTab === 'attachments'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder-opened\"></i>\r\n                                附件信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addAttachment\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传附件\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"attachment-grid\">\r\n                                <!-- 身份证照片 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">身份证照片</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.idCard && currentUserInfo.attachments.idCard.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.idCard\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('idCard', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-picture\"></i>\r\n                                            <span>暂无身份证照片</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadIdCard\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传身份证\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 营业执照 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">营业执照</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.license && currentUserInfo.attachments.license.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.license\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('license', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-document\"></i>\r\n                                            <span>暂无营业执照</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadLicense\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传营业执照\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 其他附件 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">其他附件</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.others && currentUserInfo.attachments.others.length > 0\" class=\"file-list\">\r\n                                            <div v-for=\"(file, index) in currentUserInfo.attachments.others\" :key=\"index\" class=\"file-item\">\r\n                                                <div class=\"file-icon\">\r\n                                                    <i :class=\"getFileIcon(file.type)\" class=\"file-type-icon\"></i>\r\n                                                </div>\r\n                                                <div class=\"file-info\">\r\n                                                    <div class=\"file-name\">{{ file.name }}</div>\r\n                                                    <div class=\"file-meta\">\r\n                                                        <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                                                        <span class=\"upload-time\">{{ file.uploadTime }}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div class=\"file-actions\">\r\n                                                    <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(file)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                    <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('others', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-folder\"></i>\r\n                                            <span>暂无其他附件</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadOthers\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传其他附件\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n\r\n        <!-- 编辑用户抽屉 -->\r\n        <el-drawer\r\n            title=\"编辑用户\"\r\n            :visible.sync=\"drawerEditVisible\"\r\n            direction=\"rtl\"\r\n            size=\"50%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content\">\r\n                <!-- 客户信息展示 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                        客户信息\r\n                    </div>\r\n                    <el-descriptions :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            ruleForm.company\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            ruleForm.phone\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"名称\">{{\r\n                            ruleForm.nickname\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            ruleForm.linkman\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"ruleForm.headimg && ruleForm.headimg !== ''\"\r\n                                     :src=\"ruleForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(ruleForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\" :span=\"2\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ ruleForm.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 信息编辑表单 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-edit\"></i>\r\n                        信息编辑\r\n                    </div>\r\n                    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n                    </el-form>\r\n\r\n                    <!-- 保存按钮 -->\r\n                    <div class=\"drawer-footer\">\r\n                        <el-button @click=\"drawerEditVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveData()\">保 存</el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog :visible.sync=\"dialogVisible\" width=\"50%\" center>\r\n            <img :src=\"show_image\" style=\"width: 100%; height: auto;\" />\r\n        </el-dialog>\r\n\r\n        <!-- 债务人表单对话框 -->\r\n        <el-dialog :title=\"debtDialogTitle\" :visible.sync=\"debtDialogVisible\" width=\"500px\" @close=\"closeDebtDialog\">\r\n            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"100px\">\r\n                <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                    <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                    <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务金额\" prop=\"money\">\r\n                    <el-input-number\r\n                        v-model=\"debtForm.money\"\r\n                        :min=\"0\"\r\n                        :precision=\"2\"\r\n                        placeholder=\"请输入债务金额\"\r\n                        style=\"width: 100%\">\r\n                    </el-input-number>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                        <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                        <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                        <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeDebtDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveDebt\">确定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 导入用户对话框 -->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"600px\" @close=\"closeUploadDialog\">\r\n            <div class=\"upload-container\">\r\n                <div class=\"upload-tips\">\r\n                    <el-alert\r\n                        title=\"导入说明\"\r\n                        type=\"info\"\r\n                        :closable=\"false\"\r\n                        show-icon>\r\n                        <div slot=\"description\">\r\n                            <p>1. 请先下载导入模板，按照模板格式填写用户信息</p>\r\n                            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n                            <p>3. 单次最多导入1000条用户数据</p>\r\n                            <p>4. 手机号码为必填项，且不能重复</p>\r\n                        </div>\r\n                    </el-alert>\r\n                </div>\r\n\r\n                <div class=\"upload-actions\">\r\n                    <el-button type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n\r\n                <div class=\"upload-area\">\r\n                    <el-upload\r\n                        ref=\"upload\"\r\n                        :action=\"uploadAction\"\r\n                        :data=\"uploadData\"\r\n                        :on-success=\"handleUploadSuccess\"\r\n                        :on-error=\"handleUploadError\"\r\n                        :before-upload=\"beforeUpload\"\r\n                        :auto-upload=\"false\"\r\n                        :limit=\"1\"\r\n                        :file-list=\"fileList\"\r\n                        accept=\".xls,.xlsx\"\r\n                        drag>\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n                        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xls/xlsx文件，且不超过10MB</div>\r\n                    </el-upload>\r\n                </div>\r\n\r\n                <div class=\"upload-options\">\r\n                    <el-checkbox v-model=\"uploadData.review\">导入前预览数据</el-checkbox>\r\n                </div>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeUploadDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">\r\n                    {{ submitOrderLoading2 ? '导入中...' : '开始导入' }}\r\n                </el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                fileList: [], // 上传文件列表\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                currentUserInfo: {},\r\n                search: {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                selectedUsers: [], // 选中的用户列表\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                drawerViewVisible: false,\r\n                drawerEditVisible: false,\r\n                isEditMode: false,\r\n                editForm: {},\r\n                originalUserInfo: {},\r\n                activeTab: 'customer',\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                // 债务人表单相关\r\n                debtDialogVisible: false,\r\n                debtDialogTitle: '添加债务人',\r\n                isEditingDebt: false,\r\n                editingDebtIndex: -1,\r\n                debtForm: {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                },\r\n                debtRules: {\r\n                    name: [\r\n                        { required: true, message: '请输入债务人姓名', trigger: 'blur' }\r\n                    ],\r\n                    tel: [\r\n                        { required: true, message: '请输入债务人电话', trigger: 'blur' },\r\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n                    ],\r\n                    money: [\r\n                        { required: true, message: '请输入债务金额', trigger: 'blur' }\r\n                    ],\r\n                    status: [\r\n                        { required: true, message: '请选择状态', trigger: 'change' }\r\n                    ]\r\n                },\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n            };\r\n        },\r\n        mounted() {\r\n            // 使用测试数据，注释掉API调用\r\n            // this.getData();\r\n            // 添加测试数据\r\n            this.addTestData();\r\n        },\r\n        methods: {\r\n            // 获取原始测试数据\r\n            getOriginalTestData() {\r\n                return [\r\n                    {\r\n                        id: 1,\r\n                        phone: '13800138001',\r\n                        nickname: '张三',\r\n                        company: '北京科技有限公司',\r\n                        linkman: '张三',\r\n                        linkphone: '13800138001',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-12-31',\r\n                        create_time: '2023-01-15 10:30:00',\r\n                        last_login_time: '2024-01-20 15:45:00',\r\n                        headimg: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n                        license: '',\r\n                        start_time: '2023-01-15',\r\n                        year: 2,\r\n                        // 专员信息\r\n                        lian_name: '陈专员',\r\n                        tiaojie_name: '朱调解',\r\n                        fawu_name: '严法务',\r\n                        htsczy_name: '合同专员',\r\n                        ls_name: '王律师',\r\n                        ywy_name: '业务员张三',\r\n                        debts: [\r\n                            {\r\n                                name: '王某某',\r\n                                tel: '13912345678',\r\n                                money: '50000',\r\n                                status: '处理中'\r\n                            },\r\n                            {\r\n                                name: '李某某',\r\n                                tel: '13987654321',\r\n                                money: '30000',\r\n                                status: '已完成'\r\n                            }\r\n                        ],\r\n                        attachments: {\r\n                            idCard: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '身份证正面.jpg'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '身份证反面.jpg'\r\n                                }\r\n                            ],\r\n                            license: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                                    name: '营业执照.jpg'\r\n                                }\r\n                            ],\r\n                            others: [\r\n                                {\r\n                                    name: '合同文件.pdf',\r\n                                    url: '/files/contract.pdf',\r\n                                    type: 'pdf'\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        phone: '13900139002',\r\n                        nickname: '李四',\r\n                        company: '上海贸易公司',\r\n                        linkman: '李四',\r\n                        linkphone: '13900139002',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-06-30',\r\n                        create_time: '2023-02-20 14:20:00',\r\n                        last_login_time: '2024-01-18 09:15:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-02-20',\r\n                        year: 1,\r\n                        // 专员信息\r\n                        lian_name: '李专员',\r\n                        tiaojie_name: '调解员王五',\r\n                        fawu_name: '法务李四',\r\n                        htsczy_name: '合同专员B',\r\n                        ls_name: '律师张三',\r\n                        ywy_name: '业务员李四',\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13811112222',\r\n                                money: '80000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        phone: '13700137003',\r\n                        nickname: '王五',\r\n                        company: '深圳创新科技',\r\n                        linkman: '王五',\r\n                        linkphone: '13700137003',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2025-03-15',\r\n                        create_time: '2023-03-10 16:40:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                        license: '',\r\n                        start_time: '2023-03-10',\r\n                        year: 2,\r\n                        debts: [\r\n                            {\r\n                                name: '陈某某',\r\n                                tel: '13765432109',\r\n                                money: '80000',\r\n                                status: '待处理'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 4,\r\n                        phone: '13600136004',\r\n                        nickname: '赵六',\r\n                        company: '广州物流集团',\r\n                        linkman: '赵六',\r\n                        linkphone: '13600136004',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-09-20',\r\n                        create_time: '2023-04-05 11:30:00',\r\n                        last_login_time: '2024-01-19 14:22:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-04-05',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 5,\r\n                        phone: '13500135005',\r\n                        nickname: '孙七',\r\n                        company: '杭州电商有限公司',\r\n                        linkman: '孙七',\r\n                        linkphone: '13500135005',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-11-10',\r\n                        create_time: '2023-05-12 09:15:00',\r\n                        last_login_time: '2024-01-21 16:30:00',\r\n                        headimg: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                        license: '',\r\n                        start_time: '2023-05-12',\r\n                        year: 1,\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13654321098',\r\n                                money: '25000',\r\n                                status: '已完成'\r\n                            },\r\n                            {\r\n                                name: '钱某某',\r\n                                tel: '13543210987',\r\n                                money: '15000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 6,\r\n                        phone: '13400134006',\r\n                        nickname: '周八',\r\n                        company: '成都软件开发',\r\n                        linkman: '周八',\r\n                        linkphone: '13400134006',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-08-15',\r\n                        create_time: '2023-06-18 13:25:00',\r\n                        last_login_time: '2024-01-22 10:12:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-06-18',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 7,\r\n                        phone: '13300133007',\r\n                        nickname: '吴九',\r\n                        company: '武汉贸易有限公司',\r\n                        linkman: '吴九',\r\n                        linkphone: '13300133007',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-10-30',\r\n                        create_time: '2023-07-22 15:45:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                        license: '',\r\n                        start_time: '2023-07-22',\r\n                        year: 1,\r\n                        debts: []\r\n                    }\r\n                ];\r\n            },\r\n            addTestData() {\r\n                // 添加测试数据\r\n                this.list = this.getOriginalTestData();\r\n                this.total = this.list.length;\r\n                this.loading = false;\r\n            },\r\n            // 过滤测试数据（模拟搜索功能）\r\n            filterTestData() {\r\n                this.loading = true;\r\n\r\n                // 获取原始测试数据\r\n                const originalData = this.getOriginalTestData();\r\n                let filteredData = [...originalData];\r\n\r\n                // 根据搜索条件过滤数据\r\n                if (this.search.nickname) {\r\n                    const nickname = this.search.nickname.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.nickname && user.nickname.toLowerCase().includes(nickname)\r\n                    );\r\n                }\r\n\r\n                if (this.search.phone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.phone && user.phone.includes(this.search.phone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkman) {\r\n                    const linkman = this.search.linkman.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkman && user.linkman.toLowerCase().includes(linkman)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkphone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkphone && user.linkphone.includes(this.search.linkphone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.company) {\r\n                    const company = this.search.company.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.company && user.company.toLowerCase().includes(company)\r\n                    );\r\n                }\r\n\r\n                if (this.search.yuangong_id) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.yuangong_id === this.search.yuangong_id\r\n                    );\r\n                }\r\n\r\n                // 注册时间范围过滤\r\n                if (this.search.dateRange && this.search.dateRange.length === 2) {\r\n                    const startDate = new Date(this.search.dateRange[0]);\r\n                    const endDate = new Date(this.search.dateRange[1]);\r\n                    filteredData = filteredData.filter(user => {\r\n                        if (user.create_time) {\r\n                            const createDate = new Date(user.create_time.split(' ')[0]);\r\n                            return createDate >= startDate && createDate <= endDate;\r\n                        }\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                // 更新列表和总数\r\n                this.list = filteredData;\r\n                this.total = filteredData.length;\r\n                this.loading = false;\r\n\r\n                // 显示搜索结果提示\r\n                const hasSearchCondition = this.search.nickname || this.search.phone || this.search.linkman ||\r\n                                         this.search.linkphone || this.search.company || this.search.yuangong_id ||\r\n                                         (this.search.dateRange && this.search.dateRange.length === 2);\r\n\r\n                if (hasSearchCondition) {\r\n                    this.$message.success(`搜索完成，找到 ${filteredData.length} 条匹配记录`);\r\n                }\r\n            },\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                console.log('viewData called with id:', id);\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                    // 从测试数据中找到对应的用户信息\r\n                    this.currentUserInfo = this.list.find(user => user.id === id) || {};\r\n                    console.log('Found user info:', this.currentUserInfo);\r\n                    // 重置编辑模式\r\n                    this.isEditMode = false;\r\n                    this.editForm = {};\r\n                    this.originalUserInfo = {};\r\n                    // 重置到客户信息标签页\r\n                    this.activeTab = 'customer';\r\n                }\r\n                _this.drawerViewVisible = true;\r\n                console.log('Drawer should be visible:', _this.drawerViewVisible);\r\n            },\r\n            handleTabSelect(key) {\r\n                this.activeTab = key;\r\n                // 如果切换到其他标签页，退出编辑模式\r\n                if (key !== 'customer') {\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            toggleEditMode() {\r\n                if (!this.isEditMode) {\r\n                    // 进入编辑模式\r\n                    this.isEditMode = true;\r\n                    // 保存原始数据用于取消时恢复\r\n                    this.originalUserInfo = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 复制当前用户信息到编辑表单\r\n                    this.editForm = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 获取员工数据用于下拉选择\r\n                    this.getYuangongs();\r\n                } else {\r\n                    // 退出编辑模式\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            cancelEdit() {\r\n                // 恢复原始数据\r\n                this.currentUserInfo = JSON.parse(JSON.stringify(this.originalUserInfo));\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.$message.info('已取消编辑');\r\n            },\r\n            saveUserData() {\r\n                // 验证表单\r\n                this.$refs.editForm.validate((valid) => {\r\n                    if (valid) {\r\n                        // 更新当前用户信息\r\n                        this.currentUserInfo = JSON.parse(JSON.stringify(this.editForm));\r\n\r\n                        // 更新列表中的数据\r\n                        const index = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1, this.currentUserInfo);\r\n                        }\r\n\r\n                        // 退出编辑模式\r\n                        this.isEditMode = false;\r\n                        this.editForm = {};\r\n\r\n                        this.$message.success('保存成功！');\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n                _this.drawerEditVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            handleDrawerClose() {\r\n                this.drawerViewVisible = false;\r\n                this.drawerEditVisible = false;\r\n                // 重置编辑模式\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.originalUserInfo = {};\r\n                // 重置标签页\r\n                this.activeTab = 'customer';\r\n            },\r\n            // 债务人管理方法\r\n            addDebt() {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.isEditingDebt = false;\r\n            },\r\n            editDebt(debt, index) {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: debt.name,\r\n                    tel: debt.tel,\r\n                    money: parseFloat(debt.money),\r\n                    status: debt.status\r\n                };\r\n                this.debtDialogTitle = '编辑债务人';\r\n                this.isEditingDebt = true;\r\n                this.editingDebtIndex = index;\r\n            },\r\n            saveDebt() {\r\n                this.$refs.debtForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const debtData = {\r\n                            name: this.debtForm.name,\r\n                            tel: this.debtForm.tel,\r\n                            money: this.debtForm.money.toString(),\r\n                            status: this.debtForm.status\r\n                        };\r\n\r\n                        if (this.isEditingDebt) {\r\n                            // 编辑模式\r\n                            this.currentUserInfo.debts[this.editingDebtIndex] = debtData;\r\n                            this.$message.success('债务人信息修改成功！');\r\n                        } else {\r\n                            // 添加模式\r\n                            if (!this.currentUserInfo.debts) {\r\n                                this.currentUserInfo.debts = [];\r\n                            }\r\n                            this.currentUserInfo.debts.push(debtData);\r\n                            this.$message.success('债务人添加成功！');\r\n                        }\r\n\r\n                        // 更新主列表中的数据\r\n                        const userIndex = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (userIndex !== -1) {\r\n                            this.list[userIndex].debts = [...this.currentUserInfo.debts];\r\n                        }\r\n\r\n                        this.closeDebtDialog();\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            closeDebtDialog() {\r\n                this.debtDialogVisible = false;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.isEditingDebt = false;\r\n                this.editingDebtIndex = -1;\r\n                this.debtDialogTitle = '添加债务人';\r\n                // 清除表单验证\r\n                this.$nextTick(() => {\r\n                    if (this.$refs.debtForm) {\r\n                        this.$refs.debtForm.clearValidate();\r\n                    }\r\n                });\r\n            },\r\n            deleteDebt(index) {\r\n                this.$confirm('确定要删除这个债务人吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.currentUserInfo.debts.splice(index, 1);\r\n                    this.$message.success('删除成功！');\r\n                });\r\n            },\r\n            // 附件管理方法\r\n            addAttachment() {\r\n                this.$message.info('请选择具体的附件类型进行上传');\r\n            },\r\n            uploadIdCard() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'idCard', '身份证照片');\r\n                });\r\n            },\r\n            uploadLicense() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'license', '营业执照');\r\n                });\r\n            },\r\n            uploadOthers() {\r\n                this.createFileInput('*', (files) => {\r\n                    this.handleFileUpload(files, 'others', '其他附件');\r\n                });\r\n            },\r\n            // 创建文件选择器\r\n            createFileInput(accept, callback) {\r\n                const input = document.createElement('input');\r\n                input.type = 'file';\r\n                input.accept = accept;\r\n                input.multiple = true;\r\n                input.style.display = 'none';\r\n\r\n                input.onchange = (e) => {\r\n                    const files = Array.from(e.target.files);\r\n                    if (files.length > 0) {\r\n                        callback(files);\r\n                    }\r\n                    document.body.removeChild(input);\r\n                };\r\n\r\n                document.body.appendChild(input);\r\n                input.click();\r\n            },\r\n            // 处理文件上传\r\n            handleFileUpload(files, type, typeName) {\r\n                if (!files || files.length === 0) {\r\n                    this.$message.warning('请选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                // 验证文件\r\n                for (let file of files) {\r\n                    if (type !== 'others' && !file.type.startsWith('image/')) {\r\n                        this.$message.error(`${typeName}只能上传图片文件`);\r\n                        return;\r\n                    }\r\n                    if (file.size > 10 * 1024 * 1024) { // 10MB限制\r\n                        this.$message.error(`文件 ${file.name} 大小超过10MB限制`);\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 初始化附件数据结构\r\n                if (!this.currentUserInfo.attachments) {\r\n                    this.currentUserInfo.attachments = {};\r\n                }\r\n                if (!this.currentUserInfo.attachments[type]) {\r\n                    this.currentUserInfo.attachments[type] = [];\r\n                }\r\n\r\n                // 模拟上传过程\r\n                this.$message.info(`正在上传 ${files.length} 个文件...`);\r\n\r\n                files.forEach((file, index) => {\r\n                    // 创建文件预览URL\r\n                    const fileUrl = URL.createObjectURL(file);\r\n\r\n                    // 模拟上传延迟\r\n                    setTimeout(() => {\r\n                        const fileData = {\r\n                            name: file.name,\r\n                            url: fileUrl,\r\n                            size: file.size,\r\n                            type: file.type,\r\n                            uploadTime: new Date().toLocaleString()\r\n                        };\r\n\r\n                        this.currentUserInfo.attachments[type].push(fileData);\r\n\r\n                        if (index === files.length - 1) {\r\n                            this.$message.success(`${typeName}上传完成！共上传 ${files.length} 个文件`);\r\n                        }\r\n                    }, (index + 1) * 500); // 模拟上传时间\r\n                });\r\n            },\r\n            deleteAttachment(type, index) {\r\n                this.$confirm('确定要删除这个附件吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    if (this.currentUserInfo.attachments[type]) {\r\n                        // 释放预览URL\r\n                        const file = this.currentUserInfo.attachments[type][index];\r\n                        if (file && file.url && file.url.startsWith('blob:')) {\r\n                            URL.revokeObjectURL(file.url);\r\n                        }\r\n\r\n                        this.currentUserInfo.attachments[type].splice(index, 1);\r\n                        this.$message.success('删除成功！');\r\n                    }\r\n                });\r\n            },\r\n            downloadFile(file) {\r\n                if (!file || !file.url) {\r\n                    this.$message.error('文件链接无效');\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    // 创建下载链接\r\n                    const link = document.createElement('a');\r\n                    link.href = file.url;\r\n                    link.download = file.name || '附件';\r\n                    link.style.display = 'none';\r\n\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n\r\n                    this.$message.success(`开始下载: ${file.name}`);\r\n                } catch (error) {\r\n                    this.$message.error('下载失败，请重试');\r\n                    console.error('下载错误:', error);\r\n                }\r\n            },\r\n            // 文件相关辅助方法\r\n            getFileIcon(fileType) {\r\n                if (!fileType) return 'el-icon-document';\r\n\r\n                if (fileType.startsWith('image/')) return 'el-icon-picture';\r\n                if (fileType.includes('pdf')) return 'el-icon-document';\r\n                if (fileType.includes('word') || fileType.includes('doc')) return 'el-icon-document';\r\n                if (fileType.includes('excel') || fileType.includes('sheet')) return 'el-icon-s-grid';\r\n                if (fileType.includes('zip') || fileType.includes('rar')) return 'el-icon-folder-opened';\r\n                if (fileType.includes('video')) return 'el-icon-video-camera';\r\n                if (fileType.includes('audio')) return 'el-icon-headset';\r\n\r\n                return 'el-icon-document';\r\n            },\r\n            formatFileSize(bytes) {\r\n                if (bytes === 0) return '0 B';\r\n\r\n                const k = 1024;\r\n                const sizes = ['B', 'KB', 'MB', 'GB'];\r\n                const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n            },\r\n            // 债务人相关计算方法\r\n            getDebtCount(debts) {\r\n                return debts && debts.length ? debts.length : 0;\r\n            },\r\n            getDebtCountType(debts) {\r\n                const count = this.getDebtCount(debts);\r\n                if (count === 0) return 'info';\r\n                if (count <= 2) return 'success';\r\n                if (count <= 5) return 'warning';\r\n                return 'danger';\r\n            },\r\n            getTotalDebtAmount(debts) {\r\n                if (!debts || !debts.length) return 0;\r\n                return debts.reduce((total, debt) => {\r\n                    return total + (parseFloat(debt.money) || 0);\r\n                }, 0);\r\n            },\r\n            formatAmount(amount) {\r\n                if (amount === 0) return '0';\r\n                return amount.toLocaleString('zh-CN', {\r\n                    minimumFractionDigits: 0,\r\n                    maximumFractionDigits: 2\r\n                });\r\n            },\r\n            getInfo(id) {\r\n                let _this = this;\r\n                _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n                    if (resp) {\r\n                        resp.data.tiaojie_id = resp.data.tiaojie_id == 0 ? '' : resp.data.tiaojie_id;\r\n                        resp.data.fawu_id = resp.data.fawu_id == 0 ? '' : resp.data.fawu_id;\r\n                        resp.data.lian_id = resp.data.lian_id == 0 ? '' : resp.data.lian_id;\r\n                        resp.data.ywy_id = resp.data.ywy_id == 0 ? '' : resp.data.ywy_id;\r\n                        resp.data.htsczy_id = resp.data.htsczy_id == 0 ? '' : resp.data.htsczy_id;\r\n                        resp.data.ls_id = resp.data.ls_id == 0 ? '' : resp.data.ls_id;\r\n                        _this.ruleForm = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            delData(index, id) {\r\n                this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\",\r\n                })\r\n                    .then(() => {\r\n                        this.postRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                this.$message({\r\n                                    type: \"success\",\r\n                                    message: \"删除成功!\",\r\n                                });\r\n                                this.list.splice(index, 1);\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {\r\n                        this.$message({\r\n                            type: \"error\",\r\n                            message: \"取消删除!\",\r\n                        });\r\n                    });\r\n            },\r\n            refulsh() {\r\n                this.$router.go(0);\r\n            },\r\n            searchData() {\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.filterTestData();\r\n            },\r\n            resetSearch() {\r\n                this.search = {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                };\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n\r\n            getData() {\r\n                let _this = this;\r\n\r\n                _this.loading = true;\r\n                _this\r\n                    .postRequest(\r\n                        _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n                        _this.search\r\n                    )\r\n                    .then((resp) => {\r\n                        if (resp.code == 200) {\r\n                            _this.list = resp.data;\r\n                            _this.total = resp.count;\r\n\r\n                            if (resp.msg == '超级管理员') {\r\n                                _this.is_del = true;\r\n                            }\r\n                        }\r\n                        _this.loading = false;\r\n                    });\r\n            },\r\n            saveData() {\r\n                let _this = this;\r\n                console.log(this.ruleForm);\r\n                this.$refs[\"ruleForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // 使用测试数据，注释掉API调用\r\n                                // this.getData();\r\n                                this.addTestData();\r\n                                _this.dialogFormVisible = false;\r\n                                _this.dialogAddUser = false;\r\n                                _this.drawerEditVisible = false;\r\n                            } else {\r\n                                _this.$message({\r\n                                    type: \"error\",\r\n                                    message: resp.msg,\r\n                                });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleSizeChange(val) {\r\n                this.size = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleSuccess(res) {\r\n                this.ruleForm.pic_path = res.data.url;\r\n            },\r\n\r\n            showImage(file) {\r\n                this.show_image = file;\r\n                this.dialogVisible = true;\r\n            },\r\n            beforeUpload(file) {\r\n                const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n                if (!isTypeTrue) {\r\n                    this.$message.error(\"上传图片格式不对!\");\r\n                    return;\r\n                }\r\n            },\r\n            delImage(file, fileName) {\r\n                let _this = this;\r\n                _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.ruleForm[fileName] = \"\";\r\n\r\n                        _this.$message.success(\"删除成功!\");\r\n                    } else {\r\n                        _this.$message.error(resp.msg);\r\n                    }\r\n                });\r\n            },\r\n            handleSortChange({column, prop, order}) {\r\n                this.search.prop = prop;\r\n                this.search.order = order;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n                // 根据 column, prop, order 来更新你的数据排序\r\n                // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n            },\r\n            exports: function () { //导出表格\r\n                let _this = this;\r\n                location.href = \"/admin/user/export2?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n                // _this.postRequest(\r\n                //                 _this.url + \"export\",\r\n                //                 _this.search\r\n                //         )\r\n                //         .then((resp) => {\r\n                //           if (resp.code == 200) {\r\n                //\r\n                //           }\r\n                //         });\r\n            },\r\n            closeUploadDialog() { //关闭窗口\r\n                this.uploadVisible = false;\r\n                this.$refs.upload.clearFiles();\r\n                this.uploadData.review = false;\r\n            },\r\n            uploadSuccess(response) { //导入完成回调\r\n                if (response.code === 200) {\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: response.msg\r\n                    });\r\n                    this.uploadVisible = false;\r\n                    // 使用测试数据，注释掉API调用\r\n                    // this.getData();\r\n                    this.addTestData();\r\n                    console.log(response);\r\n                } else {\r\n                    this.$message({\r\n                        type: 'warning',\r\n                        message: response.msg\r\n                    });\r\n                }\r\n\r\n                this.submitOrderLoading2 = false;\r\n                this.$refs.upload.clearFiles();\r\n            },\r\n            checkFile(file) { //导入前校验文件后缀\r\n                let fileType = ['xls', 'xlsx'];\r\n                let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n                if (!fileType.includes(type)) {\r\n                    this.$message({\r\n                        type: \"warning\",\r\n                        message: \"文件格式错误仅支持 xls xlxs 文件\"\r\n                    });\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            submitUpload() { //导入提交\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            closeDialog() { //关闭窗口\r\n                this.addVisible = false;\r\n                this.uploadVisible = false;\r\n                this.form = {\r\n                    id: '',\r\n                    nickname: \"\",\r\n                    mobile: \"\",\r\n                    school_id: 0,\r\n                    grade_id: '',\r\n                    class_id: '',\r\n                    sex: '',\r\n                    is_poor: '',\r\n                    is_display: '',\r\n                    number: '',\r\n                    remark: '',\r\n                    is_remark_option: 0,\r\n                    remark_option: [],\r\n                    mobile_checked: false,\r\n                };\r\n                this.$refs.form.resetFields();\r\n            },\r\n            openUpload() { //打开导入弹窗\r\n                this.uploadVisible = true;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n            },\r\n            // 关闭导入对话框\r\n            closeUploadDialog() {\r\n                this.uploadVisible = false;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n                if (this.$refs.upload) {\r\n                    this.$refs.upload.clearFiles();\r\n                }\r\n            },\r\n            // 上传前验证\r\n            beforeUpload(file) {\r\n                const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                               file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n                const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n                if (!isExcel) {\r\n                    this.$message.error('只能上传 Excel 文件!');\r\n                    return false;\r\n                }\r\n                if (!isLt10M) {\r\n                    this.$message.error('上传文件大小不能超过 10MB!');\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            // 提交上传\r\n            submitUpload() {\r\n                if (this.fileList.length === 0) {\r\n                    this.$message.warning('请先选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            // 上传成功回调\r\n            handleUploadSuccess(response, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                if (response.code === 200) {\r\n                    this.$message.success(`导入成功！共导入 ${response.count || 0} 条用户数据`);\r\n                    this.closeUploadDialog();\r\n                    // 重新加载数据\r\n                    this.addTestData(); // 使用测试数据，实际应该调用 this.getData();\r\n                } else {\r\n                    this.$message.error(response.msg || '导入失败');\r\n                }\r\n            },\r\n            // 上传失败回调\r\n            handleUploadError(err, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                this.$message.error('文件上传失败，请重试');\r\n                console.error('Upload error:', err);\r\n            },\r\n            addUser() {\r\n                this.dialogAddUser = true;\r\n                this.ruleForm = {};\r\n                this.getYuangongs();\r\n            },\r\n            // 下载导入模板\r\n            downloadTemplate() {\r\n                const templateUrl = '/import_templete/user.xls';\r\n                const link = document.createElement('a');\r\n                link.href = templateUrl;\r\n                link.download = '用户导入模板.xls';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.$message.success('模板下载中...');\r\n            },\r\n            // 处理表格选择变化\r\n            handleSelectionChange(selection) {\r\n                this.selectedUsers = selection;\r\n            },\r\n            // 导出用户数据（选中或全部）\r\n            exportSelectedData() {\r\n                let exportUrl;\r\n                let message;\r\n\r\n                if (this.selectedUsers.length > 0) {\r\n                    // 导出选中的用户数据\r\n                    const userIds = this.selectedUsers.map(user => user.id).join(',');\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&ids=${userIds}`;\r\n                    message = `正在导出 ${this.selectedUsers.length} 个用户的数据`;\r\n                } else {\r\n                    // 导出全部用户数据\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&keyword=${this.search.keyword || ''}`;\r\n                    message = '正在导出全部用户数据';\r\n                }\r\n\r\n                // 执行导出\r\n                location.href = exportUrl;\r\n                this.$message.success(message);\r\n            },\r\n            // 批量删除用户\r\n            batchDeleteUsers() {\r\n                if (this.selectedUsers.length === 0) {\r\n                    this.$message.warning('请先选择要删除的用户');\r\n                    return;\r\n                }\r\n\r\n                this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`, '批量删除确认', {\r\n                    confirmButtonText: '确定删除',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning',\r\n                    dangerouslyUseHTMLString: true\r\n                }).then(() => {\r\n                    // 获取选中用户的ID列表\r\n                    const userIds = this.selectedUsers.map(user => user.id);\r\n\r\n                    // 这里应该调用批量删除API\r\n                    // this.postRequest(this.url + \"batchDelete\", { ids: userIds }).then((resp) => {\r\n                    //     if (resp.code == 200) {\r\n                    //         this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n                    //         this.getData(); // 重新加载数据\r\n                    //         this.selectedUsers = []; // 清空选择\r\n                    //     }\r\n                    // });\r\n\r\n                    // 临时使用本地删除模拟\r\n                    userIds.forEach(id => {\r\n                        const index = this.list.findIndex(user => user.id === id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1);\r\n                        }\r\n                    });\r\n\r\n                    this.total = this.list.length;\r\n                    this.selectedUsers = []; // 清空选择\r\n                    this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n\r\n                    // 清空表格选择\r\n                    this.$refs.userTable.clearSelection();\r\n                }).catch(() => {\r\n                    this.$message.info('已取消删除操作');\r\n                });\r\n            }\r\n\r\n        },\r\n    };\r\n</script>\r\n<style scoped>\r\n/* 页面特定样式 */\r\n.page-wrapper {\r\n    background-color: #f5f5f5;\r\n    min-height: calc(100vh - 110px);\r\n    padding: 16px;\r\n}\r\n\r\n.page-container {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    padding: 24px;\r\n}\r\n\r\n.page-title {\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-container {\r\n    background: #fff;\r\n    padding: 24px;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #e8e8e8;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-form {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.search-form .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.search-form .el-input__inner,\r\n.search-form .el-select .el-input__inner {\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.search-form .el-input__inner:focus,\r\n.search-form .el-select .el-input__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.search-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n}\r\n\r\n.search-buttons .el-button {\r\n    min-width: 80px;\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-wrap: wrap;\r\n    margin-top: 20px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-buttons .el-button {\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.data-table {\r\n    margin-top: 20px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table .el-table {\r\n    border-radius: 8px;\r\n}\r\n\r\n.data-table .el-table th {\r\n    background-color: #fafafa !important;\r\n    color: #606266 !important;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table .el-table td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 头像样式 */\r\n.avatar-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    border: 2px solid #e8e8e8;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.user-avatar {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.no-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-name.clickable {\r\n    cursor: pointer;\r\n    color: #1890ff;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.user-name.clickable:hover {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n.user-phone {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons-table {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.action-buttons-table .el-button {\r\n    padding: 5px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 16px 0;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    color: #8c8c8c;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 编辑模式切换按钮样式 */\r\n.edit-mode-toggle {\r\n    display: flex;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.edit-mode-toggle .el-button {\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.custom-dialog .el-dialog__body {\r\n    padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.card {\r\n    background: #fff;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.card-header {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.el-input, .el-select {\r\n    width: 100%;\r\n}\r\n\r\n/* 搜索表单特殊样式 */\r\n.search-form .el-input,\r\n.search-form .el-select,\r\n.search-form .el-date-picker {\r\n    width: 100%;\r\n}\r\n\r\n.search-form .el-date-picker {\r\n    width: 100% !important;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.data-table .el-table tbody tr:hover {\r\n    background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n    .search-form .el-col {\r\n        margin-bottom: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .search-form .el-col {\r\n        width: 100% !important;\r\n        flex: 0 0 100% !important;\r\n        max-width: 100% !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n    }\r\n\r\n    .action-buttons {\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 16px;\r\n        margin: 8px;\r\n    }\r\n\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: center;\r\n    }\r\n\r\n    .pagination-info {\r\n        order: 2;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.drawer-content-wrapper {\r\n    display: flex;\r\n    height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n    width: 200px;\r\n    border-right: 1px solid #e6e6e6;\r\n    background-color: #fafafa;\r\n}\r\n\r\n.drawer-menu {\r\n    border-right: none;\r\n    background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.drawer-content {\r\n    flex: 1;\r\n    padding: 20px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n    height: 100%;\r\n}\r\n\r\n.drawer-content .card {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.drawer-content .card-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.drawer-content .card-header i {\r\n    color: #1890ff;\r\n    font-size: 18px;\r\n}\r\n\r\n.drawer-footer {\r\n    margin-top: 24px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n    text-align: right;\r\n}\r\n\r\n.drawer-footer .el-button {\r\n    margin-left: 12px;\r\n}\r\n\r\n/* 头像显示样式 */\r\n.avatar-display {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.detail-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    border: 2px solid #e8e8e8;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.detail-avatar:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.no-avatar-large {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 12px;\r\n    border: 2px solid #e8e8e8;\r\n}\r\n\r\n.no-avatar-large i {\r\n    font-size: 24px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n/* 抽屉内表单样式 */\r\n.drawer-content .el-form-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.drawer-content .el-descriptions-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n}\r\n\r\n/* 无数据显示样式 */\r\n.no-data {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #ccc;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-data i {\r\n    font-size: 48px;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.attachment-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n}\r\n\r\n.attachment-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n}\r\n\r\n.attachment-title {\r\n    background: #f5f5f5;\r\n    padding: 12px 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.attachment-content {\r\n    padding: 16px;\r\n}\r\n\r\n.image-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: box-shadow 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n.attachment-image {\r\n    width: 100%;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.attachment-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\r\n    color: white;\r\n    padding: 12px;\r\n    transform: translateY(100%);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover .image-overlay {\r\n    transform: translateY(0);\r\n}\r\n\r\n.image-info {\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-info .file-name {\r\n    display: block;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.image-info .upload-time {\r\n    font-size: 11px;\r\n    opacity: 0.8;\r\n}\r\n\r\n.image-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n}\r\n\r\n.image-actions .el-button {\r\n    flex: 1;\r\n    font-size: 11px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.file-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    background: #fafafa;\r\n    transition: background-color 0.2s;\r\n}\r\n\r\n.file-item:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n.file-icon {\r\n    margin-right: 12px;\r\n}\r\n\r\n.file-type-icon {\r\n    font-size: 24px;\r\n    color: #1890ff;\r\n}\r\n\r\n.file-info {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.file-info .file-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.file-meta {\r\n    display: flex;\r\n    gap: 12px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.file-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    margin-left: 12px;\r\n}\r\n\r\n.no-attachment {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #999;\r\n}\r\n\r\n.no-attachment i {\r\n    font-size: 48px;\r\n    color: #d9d9d9;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n}\r\n\r\n.no-attachment span {\r\n    display: block;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 导入用户对话框样式 */\r\n.upload-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.upload-tips {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-tips .el-alert__description p {\r\n    margin: 5px 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-actions {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-area .el-upload-dragger {\r\n    width: 100%;\r\n    height: 180px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: border-color 0.2s;\r\n}\r\n\r\n.upload-area .el-upload-dragger:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-area .el-upload-dragger .el-icon-upload {\r\n    font-size: 67px;\r\n    color: #c0c4cc;\r\n    margin: 40px 0 16px;\r\n    line-height: 50px;\r\n}\r\n\r\n.upload-area .el-upload__text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area .el-upload__text em {\r\n    color: #409eff;\r\n    font-style: normal;\r\n}\r\n\r\n.upload-area .el-upload__tip {\r\n    font-size: 12px;\r\n    color: #606266;\r\n    margin-top: 7px;\r\n}\r\n\r\n.upload-options {\r\n    text-align: center;\r\n}\r\n\r\n.upload-options .el-checkbox {\r\n    color: #606266;\r\n}\r\n\r\n/* 债务金额样式 */\r\n.debt-amount {\r\n    font-weight: 500;\r\n    color: #8c8c8c;\r\n}\r\n\r\n.debt-amount.has-debt {\r\n    color: #f56c6c;\r\n    font-weight: 600;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n    .page-wrapper {\r\n        padding: 8px;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .page-title {\r\n        font-size: 16px;\r\n        margin-bottom: 15px;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 10px;\r\n    }\r\n\r\n    .page-title .el-button {\r\n        align-self: flex-end;\r\n        margin-top: -30px;\r\n    }\r\n\r\n    .search-container {\r\n        padding: 12px;\r\n    }\r\n\r\n    .search-form .el-row {\r\n        margin: 0;\r\n    }\r\n\r\n    .search-form .el-col {\r\n        padding: 0 5px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-form .el-form-item {\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-form .el-form-item__label {\r\n        font-size: 12px;\r\n        line-height: 32px;\r\n        width: 70px !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        display: flex;\r\n        gap: 8px;\r\n        justify-content: center;\r\n    }\r\n\r\n    .action-buttons {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n        margin-top: 15px;\r\n    }\r\n\r\n    .action-buttons .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n\r\n    /* 表格移动端适配 */\r\n    .data-table {\r\n        overflow-x: auto;\r\n    }\r\n\r\n    .data-table .el-table {\r\n        min-width: 800px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .data-table .el-table th,\r\n    .data-table .el-table td {\r\n        padding: 6px 4px;\r\n    }\r\n\r\n    .data-table .el-table .cell {\r\n        padding: 0 4px;\r\n        line-height: 1.2;\r\n    }\r\n\r\n    .user-avatar {\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n\r\n    .no-avatar {\r\n        width: 30px;\r\n        height: 30px;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .user-info .user-name {\r\n        font-size: 12px;\r\n        margin-bottom: 2px;\r\n    }\r\n\r\n    .user-info .user-phone {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    /* 分页移动端适配 */\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 10px;\r\n        align-items: center;\r\n        padding: 15px 0;\r\n    }\r\n\r\n    .pagination-info {\r\n        display: flex;\r\n        gap: 15px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .el-pagination {\r\n        justify-content: center;\r\n    }\r\n\r\n    /* 抽屉移动端适配 */\r\n    .el-drawer {\r\n        width: 100% !important;\r\n    }\r\n\r\n    .drawer-content-wrapper {\r\n        flex-direction: column;\r\n        height: 100%;\r\n    }\r\n\r\n    .drawer-sidebar {\r\n        width: 100%;\r\n        height: auto;\r\n        border-right: none;\r\n        border-bottom: 1px solid #e8e8e8;\r\n    }\r\n\r\n    .drawer-menu {\r\n        display: flex;\r\n        flex-direction: row;\r\n        overflow-x: auto;\r\n        border: none;\r\n    }\r\n\r\n    .drawer-menu .el-menu-item {\r\n        min-width: 100px;\r\n        flex-shrink: 0;\r\n        text-align: center;\r\n        border-bottom: none;\r\n        border-right: 1px solid #e8e8e8;\r\n    }\r\n\r\n    .drawer-menu .el-menu-item:last-child {\r\n        border-right: none;\r\n    }\r\n\r\n    .drawer-content {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n    }\r\n\r\n    .edit-mode-toggle {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .edit-mode-toggle .el-button {\r\n        width: 100%;\r\n    }\r\n\r\n    .card {\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .card-header {\r\n        font-size: 14px;\r\n        padding: 10px 15px;\r\n    }\r\n\r\n    .el-descriptions {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .el-descriptions-item__label {\r\n        font-size: 12px !important;\r\n        width: 80px !important;\r\n    }\r\n\r\n    .el-descriptions-item__content {\r\n        font-size: 12px !important;\r\n    }\r\n\r\n    .detail-avatar {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .no-avatar-large {\r\n        width: 60px;\r\n        height: 60px;\r\n        font-size: 20px;\r\n    }\r\n\r\n    /* 表单移动端适配 */\r\n    .el-form .el-row {\r\n        margin: 0;\r\n    }\r\n\r\n    .el-form .el-col {\r\n        padding: 0 5px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n        font-size: 12px !important;\r\n        line-height: 32px !important;\r\n        width: 80px !important;\r\n    }\r\n\r\n    .el-input__inner,\r\n    .el-select .el-input__inner,\r\n    .el-textarea__inner {\r\n        font-size: 12px;\r\n        height: 32px;\r\n        line-height: 32px;\r\n    }\r\n\r\n    .el-textarea__inner {\r\n        height: auto;\r\n        min-height: 60px;\r\n    }\r\n\r\n    /* 债务人列表移动端适配 */\r\n    .debt-list {\r\n        gap: 10px;\r\n    }\r\n\r\n    .debt-item {\r\n        padding: 10px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .debt-header {\r\n        font-size: 13px;\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .debt-info {\r\n        font-size: 11px;\r\n        gap: 8px;\r\n    }\r\n\r\n    /* 附件列表移动端适配 */\r\n    .attachment-grid {\r\n        grid-template-columns: 1fr;\r\n        gap: 10px;\r\n    }\r\n\r\n    .attachment-item {\r\n        padding: 10px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .attachment-name {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .attachment-info {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .attachment-actions .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    /* 上传对话框移动端适配 */\r\n    .upload-dialog .el-dialog {\r\n        width: 95% !important;\r\n        margin: 0 auto !important;\r\n    }\r\n\r\n    .upload-area .el-upload-dragger {\r\n        height: 120px;\r\n    }\r\n\r\n    .upload-area .el-upload-dragger .el-icon-upload {\r\n        font-size: 40px;\r\n        margin: 20px 0 10px;\r\n    }\r\n\r\n    .upload-area .el-upload__text {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .upload-area .el-upload__tip {\r\n        font-size: 11px;\r\n    }\r\n}\r\n\r\n/* 超小屏幕适配 */\r\n@media (max-width: 480px) {\r\n    .page-wrapper {\r\n        padding: 5px;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 8px;\r\n    }\r\n\r\n    .page-title {\r\n        font-size: 14px;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .search-container {\r\n        padding: 8px;\r\n    }\r\n\r\n    .search-form .el-form-item__label {\r\n        width: 60px !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .search-buttons .el-button {\r\n        padding: 4px 8px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .data-table .el-table {\r\n        min-width: 600px;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .data-table .el-table th,\r\n    .data-table .el-table td {\r\n        padding: 4px 2px;\r\n    }\r\n\r\n    .user-avatar {\r\n        width: 25px;\r\n        height: 25px;\r\n    }\r\n\r\n    .no-avatar {\r\n        width: 25px;\r\n        height: 25px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .user-info .user-name {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .user-info .user-phone {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        padding: 2px 6px;\r\n        font-size: 10px;\r\n    }\r\n\r\n    .drawer-content {\r\n        padding: 10px;\r\n    }\r\n\r\n    .card-header {\r\n        font-size: 12px;\r\n        padding: 8px 10px;\r\n    }\r\n\r\n    .el-descriptions {\r\n        font-size: 11px;\r\n    }\r\n\r\n    .el-descriptions-item__label {\r\n        font-size: 11px !important;\r\n        width: 70px !important;\r\n    }\r\n\r\n    .el-descriptions-item__content {\r\n        font-size: 11px !important;\r\n    }\r\n\r\n    .detail-avatar {\r\n        width: 50px;\r\n        height: 50px;\r\n    }\r\n\r\n    .no-avatar-large {\r\n        width: 50px;\r\n        height: 50px;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n        width: 70px !important;\r\n        font-size: 11px !important;\r\n    }\r\n\r\n    .el-input__inner,\r\n    .el-select .el-input__inner,\r\n    .el-textarea__inner {\r\n        font-size: 11px;\r\n        height: 28px;\r\n        line-height: 28px;\r\n    }\r\n\r\n    .el-textarea__inner {\r\n        height: auto;\r\n        min-height: 50px;\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./user.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./user.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./user.vue?vue&type=template&id=4acd38ca&scoped=true\"\nimport script from \"./user.vue?vue&type=script&lang=js\"\nexport * from \"./user.vue?vue&type=script&lang=js\"\nimport style0 from \"./user.vue?vue&type=style&index=0&id=4acd38ca&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4acd38ca\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-detail-container\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"客户基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"公司名称\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.company || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"手机号\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.phone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"客户姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkman || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkphone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"用户来源\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.yuangong_id || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"开始时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.start_time || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"会员年限\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.year ? _vm.info.year + '年' : '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"头像\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.headimg && _vm.info.headimg !== '')?_c('el-avatar',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.headimg,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.license && _vm.info.license !== '')?_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.license,\"fit\":\"cover\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"服务团队\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"调解员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.tiaojie_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"法务专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.fawu_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"立案专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.lian_name || '未分配'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"合同专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.htsczy_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"律师\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ls_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"业务员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ywy_name || '未分配'))])])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人信息\")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debts,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.name))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"phone-number\"},[_vm._v(_vm._s(scope.row.tel))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDebtDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 详情 \")])]}}])})],1),(!_vm.info.debts || _vm.info.debts.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无债务人信息\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=4468717a&scoped=true\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4468717a\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\r\nvar aCallable = require('../internals/a-callable');\r\nvar toObject = require('../internals/to-object');\r\nvar IndexedObject = require('../internals/indexed-object');\r\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\r\n\r\nvar $TypeError = TypeError;\r\n\r\nvar REDUCE_EMPTY = 'Reduce of empty array with no initial value';\r\n\r\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\r\nvar createMethod = function (IS_RIGHT) {\r\n  return function (that, callbackfn, argumentsLength, memo) {\r\n    var O = toObject(that);\r\n    var self = IndexedObject(O);\r\n    var length = lengthOfArrayLike(O);\r\n    aCallable(callbackfn);\r\n    if (length === 0 && argumentsLength < 2) throw new $TypeError(REDUCE_EMPTY);\r\n    var index = IS_RIGHT ? length - 1 : 0;\r\n    var i = IS_RIGHT ? -1 : 1;\r\n    if (argumentsLength < 2) while (true) {\r\n      if (index in self) {\r\n        memo = self[index];\r\n        index += i;\r\n        break;\r\n      }\r\n      index += i;\r\n      if (IS_RIGHT ? index < 0 : length <= index) {\r\n        throw new $TypeError(REDUCE_EMPTY);\r\n      }\r\n    }\r\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\r\n      memo = callbackfn(memo, self[index], index, O);\r\n    }\r\n    return memo;\r\n  };\r\n};\r\n\r\nmodule.exports = {\r\n  // `Array.prototype.reduce` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\r\n  left: createMethod(false),\r\n  // `Array.prototype.reduceRight` method\r\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\r\n  right: createMethod(true)\r\n};\r\n", "'use strict';\r\nvar $TypeError = TypeError;\r\n\r\nmodule.exports = function (passed, required) {\r\n  if (passed < required) throw new $TypeError('Not enough arguments');\r\n  return passed;\r\n};\r\n", "'use strict';\r\nvar makeBuiltIn = require('../internals/make-built-in');\r\nvar defineProperty = require('../internals/object-define-property');\r\n\r\nmodule.exports = function (target, name, descriptor) {\r\n  if (descriptor.get) makeBuiltIn(descriptor.get, name, { getter: true });\r\n  if (descriptor.set) makeBuiltIn(descriptor.set, name, { setter: true });\r\n  return defineProperty.f(target, name, descriptor);\r\n};\r\n"], "sourceRoot": ""}