{"map": "{\"version\":3,\"sources\":[\"js/chunk-0b5221ca.f8376412.js\"],\"names\":[\"window\",\"push\",\"3c54\",\"module\",\"exports\",\"__webpack_require__\",\"4f41\",\"__webpack_exports__\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"_m\",\"attrs\",\"src\",\"userInfo\",\"avatar\",\"defaultAvatar\",\"alt\",\"name\",\"action\",\"show-file-list\",\"on-success\",\"handleAvatarSuccess\",\"before-upload\",\"beforeAvatarUpload\",\"_v\",\"_s\",\"role\",\"department\",\"ref\",\"model\",\"rules\",\"label-width\",\"label\",\"prop\",\"placeholder\",\"disabled\",\"editMode\",\"value\",\"callback\",\"$$v\",\"$set\",\"expression\",\"employee_id\",\"phone\",\"email\",\"staticStyle\",\"width\",\"position\",\"type\",\"rows\",\"bio\",\"icon\",\"loading\",\"saving\",\"on\",\"click\",\"saveProfile\",\"cancelEdit\",\"enableEdit\",\"changePassword\",\"lastLoginTime\",\"lastLoginIP\",\"staticRenderFns\",\"profilevue_type_script_lang_js\",\"[object Object]\",\"originalUserInfo\",\"required\",\"message\",\"trigger\",\"pattern\",\"loadUserInfo\",\"methods\",\"$refs\",\"userForm\",\"validate\",\"valid\",\"setTimeout\",\"$message\",\"success\",\"$router\",\"res\",\"data\",\"url\",\"file\",\"isImage\",\"test\",\"isLt2M\",\"size\",\"error\",\"pages_profilevue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\",\"6489\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,KAM3BC,OACA,SAAUH,EAAQI,EAAqBF,GAE7C,aAEAA,EAAoBG,EAAED,GAGtB,IAAIE,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,mBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,oBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACbE,MAAO,CACLC,IAAOP,EAAIQ,SAASC,QAAUT,EAAIU,cAClCC,IAAOX,EAAIQ,SAASI,QAEpBV,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,kBACbE,MAAO,CACLO,OAAU,4BACVC,kBAAkB,EAClBC,aAAcf,EAAIgB,oBAClBC,gBAAiBjB,EAAIkB,qBAEtB,CAAChB,EAAG,IAAK,CACVE,YAAa,2CACR,KAAMF,EAAG,MAAO,CACrBE,YAAa,mBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,aACZ,CAACJ,EAAImB,GAAGnB,EAAIoB,GAAGpB,EAAIQ,SAASI,MAAQ,UAAWV,EAAG,IAAK,CACxDE,YAAa,aACZ,CAACJ,EAAImB,GAAGnB,EAAIoB,GAAGpB,EAAIQ,SAASa,MAAQ,YAAanB,EAAG,IAAK,CAC1DE,YAAa,mBACZ,CAACJ,EAAImB,GAAGnB,EAAIoB,GAAGpB,EAAIQ,SAASc,YAAc,gBAAiBpB,EAAG,MAAO,CACtEE,YAAa,gBACZ,CAACF,EAAG,UAAW,CAChBqB,IAAK,WACLnB,YAAa,eACbE,MAAO,CACLkB,MAASxB,EAAIQ,SACbiB,MAASzB,EAAIyB,MACbC,cAAe,UAEhB,CAACxB,EAAG,MAAO,CACZE,YAAa,YACZ,CAACF,EAAG,eAAgB,CACrBE,YAAa,YACbE,MAAO,CACLqB,MAAS,KACTC,KAAQ,SAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLuB,YAAe,QACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAASI,KACpBqB,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,OAAQ0B,IAEjCE,WAAY,oBAEX,GAAIlC,EAAG,eAAgB,CAC1BE,YAAa,YACbE,MAAO,CACLqB,MAAS,KACTC,KAAQ,gBAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLuB,YAAe,QACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAAS6B,YACpBJ,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,cAAe0B,IAExCE,WAAY,2BAEX,IAAK,GAAIlC,EAAG,MAAO,CACtBE,YAAa,YACZ,CAACF,EAAG,eAAgB,CACrBE,YAAa,YACbE,MAAO,CACLqB,MAAS,MACTC,KAAQ,UAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLuB,YAAe,SACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAAS8B,MACpBL,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,QAAS0B,IAElCE,WAAY,qBAEX,GAAIlC,EAAG,eAAgB,CAC1BE,YAAa,YACbE,MAAO,CACLqB,MAAS,KACTC,KAAQ,UAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLuB,YAAe,QACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAAS+B,MACpBN,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,QAAS0B,IAElCE,WAAY,qBAEX,IAAK,GAAIlC,EAAG,MAAO,CACtBE,YAAa,YACZ,CAACF,EAAG,eAAgB,CACrBE,YAAa,YACbE,MAAO,CACLqB,MAAS,KACTC,KAAQ,eAET,CAAC1B,EAAG,YAAa,CAClBsC,YAAa,CACXC,MAAS,QAEXnC,MAAO,CACLuB,YAAe,QACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAASc,WACpBW,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,aAAc0B,IAEvCE,WAAY,wBAEb,CAAClC,EAAG,YAAa,CAClBI,MAAO,CACLqB,MAAS,QACTK,MAAS,WAET9B,EAAG,YAAa,CAClBI,MAAO,CACLqB,MAAS,QACTK,MAAS,WAET9B,EAAG,YAAa,CAClBI,MAAO,CACLqB,MAAS,MACTK,MAAS,SAET9B,EAAG,YAAa,CAClBI,MAAO,CACLqB,MAAS,MACTK,MAAS,SAET9B,EAAG,YAAa,CAClBI,MAAO,CACLqB,MAAS,MACTK,MAAS,UAER,IAAK,GAAI9B,EAAG,eAAgB,CAC/BE,YAAa,YACbE,MAAO,CACLqB,MAAS,KACTC,KAAQ,aAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLuB,YAAe,QACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAASkC,SACpBT,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,WAAY0B,IAErCE,WAAY,wBAEX,IAAK,GAAIlC,EAAG,eAAgB,CAC/BI,MAAO,CACLqB,MAAS,OACTC,KAAQ,QAET,CAAC1B,EAAG,WAAY,CACjBI,MAAO,CACLqC,KAAQ,WACRC,KAAQ,EACRf,YAAe,UACfC,UAAa9B,EAAI+B,UAEnBP,MAAO,CACLQ,MAAOhC,EAAIQ,SAASqC,IACpBZ,SAAU,SAAUC,GAClBlC,EAAImC,KAAKnC,EAAIQ,SAAU,MAAO0B,IAEhCE,WAAY,mBAEX,GAAIlC,EAAG,MAAO,CACjBE,YAAa,kBACZ,CAAEJ,EAAI+B,SAQgB,CAAC7B,EAAG,YAAa,CACxCI,MAAO,CACLqC,KAAQ,UACRG,KAAQ,gBACRC,QAAW/C,EAAIgD,QAEjBC,GAAI,CACFC,MAASlD,EAAImD,cAEd,CAACnD,EAAImB,GAAG,YAAajB,EAAG,YAAa,CACtCI,MAAO,CACLwC,KAAQ,iBAEVG,GAAI,CACFC,MAASlD,EAAIoD,aAEd,CAACpD,EAAImB,GAAG,WAxBSjB,EAAG,YAAa,CAClCI,MAAO,CACLqC,KAAQ,UACRG,KAAQ,gBAEVG,GAAI,CACFC,MAASlD,EAAIqD,aAEd,CAACrD,EAAImB,GAAG,aAgBa,IAAK,IAAK,KAAMjB,EAAG,MAAO,CAChDE,YAAa,iBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,YAAa,CAC7BE,YAAa,kBACbE,MAAO,CACLqC,KAAQ,QAEVM,GAAI,CACFC,MAASlD,EAAIsD,iBAEd,CAACtD,EAAImB,GAAG,aAAc,GAAIjB,EAAG,MAAO,CACrCE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAImB,GAAG,UAAWjB,EAAG,MAAO,CAC9BE,YAAa,iBACZ,CAACJ,EAAImB,GAAGnB,EAAIoB,GAAGpB,EAAIuD,sBAAuBrD,EAAG,MAAO,CACrDE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAImB,GAAG,UAAWjB,EAAG,MAAO,CAC9BE,YAAa,iBACZ,CAACJ,EAAImB,GAAGnB,EAAIoB,GAAGpB,EAAIwD,+BAEpBC,EAAkB,CAAC,WACrB,IAAIzD,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAImB,GAAG,YAAajB,EAAG,MAAO,CAChCE,YAAa,iBACZ,CAACJ,EAAImB,GAAG,wBACV,WACD,IAAInB,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,eACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAImB,GAAG,eACV,WACD,IAAInB,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,iBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAImB,GAAG,UAAWjB,EAAG,MAAO,CAC9BE,YAAa,iBACZ,CAACJ,EAAImB,GAAG,uBAUoBuC,GAJb/D,EAAoB,QAI0B,CAChEiB,KAAM,UACN+C,OACE,MAAO,CACL5B,UAAU,EACViB,QAAQ,EACRtC,cAAe,uSACfF,SAAU,CACRI,KAAM,MACNyB,YAAa,WACbC,MAAO,cACPC,MAAO,oBACPjB,WAAY,QACZoB,SAAU,QACVrB,KAAM,QACNwB,IAAK,wBACLpC,OAAQ,IAEVmD,iBAAkB,GAClBL,cAAe,sBACfC,YAAa,gBACb/B,MAAO,CACLb,KAAM,CAAC,CACLiD,UAAU,EACVC,QAAS,QACTC,QAAS,SAEXzB,MAAO,CAAC,CACNuB,UAAU,EACVC,QAAS,SACTC,QAAS,QACR,CACDC,QAAS,gBACTF,QAAS,YACTC,QAAS,SAEXxB,MAAO,CAAC,CACNsB,UAAU,EACVC,QAAS,QACTC,QAAS,QACR,CACDpB,KAAM,QACNmB,QAAS,aACTC,QAAS,YAKjBJ,UACE1D,KAAKgE,gBAEPC,QAAS,CACPP,eAGE1D,KAAK2D,iBAAmB,IACnB3D,KAAKO,WAGZmD,aACE1D,KAAK8B,UAAW,EAChB9B,KAAK2D,iBAAmB,IACnB3D,KAAKO,WAGZmD,aACE1D,KAAK8B,UAAW,EAChB9B,KAAKO,SAAW,IACXP,KAAK2D,mBAGZD,cACE1D,KAAKkE,MAAMC,SAASC,SAASC,IACvBA,IACFrE,KAAK+C,QAAS,EAEduB,WAAW,KACTtE,KAAK+C,QAAS,EACd/C,KAAK8B,UAAW,EAChB9B,KAAKuE,SAASC,QAAQ,cACrB,SAITd,iBACE1D,KAAKyE,QAAQnF,KAAK,eAEpBoE,oBAAoBgB,GACdA,GAAOA,EAAIC,MAAQD,EAAIC,KAAKC,MAC9B5E,KAAKO,SAASC,OAASkE,EAAIC,KAAKC,IAChC5E,KAAKuE,SAASC,QAAQ,aAG1Bd,mBAAmBmB,GACjB,MAAMC,EAAU,0BAA0BC,KAAKF,EAAKnC,MAC9CsC,EAASH,EAAKI,KAAO,KAAO,KAAO,EACzC,OAAKH,IAIAE,IACHhF,KAAKuE,SAASW,MAAM,sBACb,IALPlF,KAAKuE,SAASW,MAAM,0BACb,OAWmBC,EAAuC,EAKrEC,GAHsE1F,EAAoB,QAGpEA,EAAoB,SAW1C2F,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACArF,EACA0D,GACA,EACA,KACA,WACA,MAIyC5D,EAAoB,WAAcyF,EAAiB,SAIxFE,KACA,SAAU/F,EAAQI,EAAqBF,GAE7C,aAC8cA,EAAoB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-0b5221ca\"],{\"3c54\":function(e,s,t){},\"4f41\":function(e,s,t){\"use strict\";t.r(s);var a=function(){var e=this,s=e._self._c;return s(\"div\",{staticClass:\"page-wrapper\"},[s(\"div\",{staticClass:\"page-container\"},[e._m(0),s(\"div\",{staticClass:\"profile-section\"},[s(\"div\",{staticClass:\"profile-card\"},[s(\"div\",{staticClass:\"avatar-section\"},[s(\"div\",{staticClass:\"avatar-container\"},[s(\"img\",{staticClass:\"avatar-image\",attrs:{src:e.userInfo.avatar||e.defaultAvatar,alt:e.userInfo.name}}),s(\"div\",{staticClass:\"avatar-overlay\"},[s(\"el-upload\",{staticClass:\"avatar-uploader\",attrs:{action:\"/admin/Upload/uploadImage\",\"show-file-list\":!1,\"on-success\":e.handleAvatarSuccess,\"before-upload\":e.beforeAvatarUpload}},[s(\"i\",{staticClass:\"el-icon-camera avatar-uploader-icon\"})])],1)]),s(\"div\",{staticClass:\"user-basic-info\"},[s(\"h3\",{staticClass:\"user-name\"},[e._v(e._s(e.userInfo.name||\"管理员\"))]),s(\"p\",{staticClass:\"user-role\"},[e._v(e._s(e.userInfo.role||\"系统管理员\"))]),s(\"p\",{staticClass:\"user-department\"},[e._v(e._s(e.userInfo.department||\"法律服务部\"))])])]),s(\"div\",{staticClass:\"form-section\"},[s(\"el-form\",{ref:\"userForm\",staticClass:\"profile-form\",attrs:{model:e.userInfo,rules:e.rules,\"label-width\":\"120px\"}},[s(\"div\",{staticClass:\"form-row\"},[s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"姓名\",prop:\"name\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入姓名\",disabled:!e.editMode},model:{value:e.userInfo.name,callback:function(s){e.$set(e.userInfo,\"name\",s)},expression:\"userInfo.name\"}})],1),s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"工号\",prop:\"employee_id\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入工号\",disabled:!e.editMode},model:{value:e.userInfo.employee_id,callback:function(s){e.$set(e.userInfo,\"employee_id\",s)},expression:\"userInfo.employee_id\"}})],1)],1),s(\"div\",{staticClass:\"form-row\"},[s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"手机号\",prop:\"phone\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入手机号\",disabled:!e.editMode},model:{value:e.userInfo.phone,callback:function(s){e.$set(e.userInfo,\"phone\",s)},expression:\"userInfo.phone\"}})],1),s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"邮箱\",prop:\"email\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入邮箱\",disabled:!e.editMode},model:{value:e.userInfo.email,callback:function(s){e.$set(e.userInfo,\"email\",s)},expression:\"userInfo.email\"}})],1)],1),s(\"div\",{staticClass:\"form-row\"},[s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"部门\",prop:\"department\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择部门\",disabled:!e.editMode},model:{value:e.userInfo.department,callback:function(s){e.$set(e.userInfo,\"department\",s)},expression:\"userInfo.department\"}},[s(\"el-option\",{attrs:{label:\"法律服务部\",value:\"法律服务部\"}}),s(\"el-option\",{attrs:{label:\"客户服务部\",value:\"客户服务部\"}}),s(\"el-option\",{attrs:{label:\"财务部\",value:\"财务部\"}}),s(\"el-option\",{attrs:{label:\"人事部\",value:\"人事部\"}}),s(\"el-option\",{attrs:{label:\"技术部\",value:\"技术部\"}})],1)],1),s(\"el-form-item\",{staticClass:\"form-item\",attrs:{label:\"职位\",prop:\"position\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入职位\",disabled:!e.editMode},model:{value:e.userInfo.position,callback:function(s){e.$set(e.userInfo,\"position\",s)},expression:\"userInfo.position\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"个人简介\",prop:\"bio\"}},[s(\"el-input\",{attrs:{type:\"textarea\",rows:4,placeholder:\"请输入个人简介\",disabled:!e.editMode},model:{value:e.userInfo.bio,callback:function(s){e.$set(e.userInfo,\"bio\",s)},expression:\"userInfo.bio\"}})],1),s(\"div\",{staticClass:\"action-buttons\"},[e.editMode?[s(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-check\",loading:e.saving},on:{click:e.saveProfile}},[e._v(\" 保存修改 \")]),s(\"el-button\",{attrs:{icon:\"el-icon-close\"},on:{click:e.cancelEdit}},[e._v(\" 取消 \")])]:s(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-edit\"},on:{click:e.enableEdit}},[e._v(\" 编辑资料 \")])],2)],1)],1)]),s(\"div\",{staticClass:\"security-card\"},[e._m(1),s(\"div\",{staticClass:\"security-items\"},[s(\"div\",{staticClass:\"security-item\"},[e._m(2),s(\"el-button\",{staticClass:\"security-action\",attrs:{type:\"text\"},on:{click:e.changePassword}},[e._v(\" 修改密码 \")])],1),s(\"div\",{staticClass:\"security-item\"},[s(\"div\",{staticClass:\"security-info\"},[s(\"div\",{staticClass:\"security-title\"},[e._v(\"最后登录\")]),s(\"div\",{staticClass:\"security-desc\"},[e._v(e._s(e.lastLoginTime))])])]),s(\"div\",{staticClass:\"security-item\"},[s(\"div\",{staticClass:\"security-info\"},[s(\"div\",{staticClass:\"security-title\"},[e._v(\"登录IP\")]),s(\"div\",{staticClass:\"security-desc\"},[e._v(e._s(e.lastLoginIP))])])])])])])])])},i=[function(){var e=this,s=e._self._c;return s(\"div\",{staticClass:\"page-header\"},[s(\"div\",{staticClass:\"header-left\"},[s(\"h2\",{staticClass:\"page-title\"},[s(\"i\",{staticClass:\"el-icon-user\"}),e._v(\" 个人信息 \")]),s(\"div\",{staticClass:\"page-subtitle\"},[e._v(\"管理您的个人资料和账户设置\")])])])},function(){var e=this,s=e._self._c;return s(\"div\",{staticClass:\"card-header\"},[s(\"h3\",{staticClass:\"card-title\"},[s(\"i\",{staticClass:\"el-icon-lock\"}),e._v(\" 安全设置 \")])])},function(){var e=this,s=e._self._c;return s(\"div\",{staticClass:\"security-info\"},[s(\"div\",{staticClass:\"security-title\"},[e._v(\"登录密码\")]),s(\"div\",{staticClass:\"security-desc\"},[e._v(\"定期更换密码，保护账户安全\")])])}],l=(t(\"14d9\"),{name:\"Profile\",data(){return{editMode:!1,saving:!1,defaultAvatar:\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzk5OSI+5aS05YOP</dGV4dD48L3N2Zz4=\",userInfo:{name:\"管理员\",employee_id:\"ADMIN001\",phone:\"13800138000\",email:\"<EMAIL>\",department:\"法律服务部\",position:\"系统管理员\",role:\"系统管理员\",bio:\"负责系统管理和维护工作，确保系统稳定运行。\",avatar:\"\"},originalUserInfo:{},lastLoginTime:\"2024-01-15 09:30:25\",lastLoginIP:\"*************\",rules:{name:[{required:!0,message:\"请输入姓名\",trigger:\"blur\"}],phone:[{required:!0,message:\"请输入手机号\",trigger:\"blur\"},{pattern:/^1[3-9]\\d{9}$/,message:\"请输入正确的手机号\",trigger:\"blur\"}],email:[{required:!0,message:\"请输入邮箱\",trigger:\"blur\"},{type:\"email\",message:\"请输入正确的邮箱格式\",trigger:\"blur\"}]}}},mounted(){this.loadUserInfo()},methods:{loadUserInfo(){this.originalUserInfo={...this.userInfo}},enableEdit(){this.editMode=!0,this.originalUserInfo={...this.userInfo}},cancelEdit(){this.editMode=!1,this.userInfo={...this.originalUserInfo}},saveProfile(){this.$refs.userForm.validate(e=>{e&&(this.saving=!0,setTimeout(()=>{this.saving=!1,this.editMode=!1,this.$message.success(\"个人信息保存成功！\")},1e3))})},changePassword(){this.$router.push(\"/changePwd\")},handleAvatarSuccess(e){e&&e.data&&e.data.url&&(this.userInfo.avatar=e.data.url,this.$message.success(\"头像上传成功！\"))},beforeAvatarUpload(e){const s=/^image\\/(jpeg|png|jpg)$/.test(e.type),t=e.size/1024/1024<2;return s?!!t||(this.$message.error(\"上传头像图片大小不能超过 2MB!\"),!1):(this.$message.error(\"上传头像图片只能是 JPG/PNG 格式!\"),!1)}}}),r=l,o=(t(\"6489\"),t(\"2877\")),n=Object(o[\"a\"])(r,a,i,!1,null,\"1b7c37e7\",null);s[\"default\"]=n.exports},6489:function(e,s,t){\"use strict\";t(\"3c54\")}}]);", "extractedComments": []}