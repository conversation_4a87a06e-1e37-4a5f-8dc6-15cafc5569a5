import Vue from "vue";
import VueRouter from "vue-router";
import Home from "../views/Home.vue";
import Login from "../views/Login.vue";
import axios from "axios";
import store from "../store";
import { Message } from "element-ui";
Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "",
    component: Home,
    hidden: true,
    children: [
      {
        path: "/",
        name: "首页",
        component: () => import("../views/pages/Dashboard.vue"),
      }
    ]
  },
  {
    path: "/login",
    name: "Login",
    component: Login,
    hidden: true,
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: "/jichu",
    name: "基础管理",
    component: Home,
    children: [
      {
        path: "/config",
        name: "基础设置",
        component: () => import("../views/pages/data/configs.vue"),
      },
      {
        path: "/banner",
        name: "轮播图",
        component: () => import("../views/pages/data/banner.vue"),
      },
      {
        path: "/nav",
        name: "首页导航",
        component: () => import("../views/pages/data/nav.vue"),
      },
      {
        path: "/gonggao",
        name: "公告",
        component: () => import("../views/pages/data/gonggao.vue"),
      },
      // {
      //   path: "/vip",
      //   name: "会员",
      //   component: () => import("../views/pages/data/vip.vue"),
      // },
    ],
  },
  {
    path: "/xiadan",
    name: "订单管理",
    component: Home,
    children: [
      {
        path: "/type",
        name: "服务类型",
        component: () => import("../views/pages/taocan/type.vue"),
      },
      {
        path: "/taocan",
        name: "套餐类型",
        component: () => import("../views/pages/taocan/taocan.vue"),
      },
      // {
      //   path: "/yonghu",
      //   name: "用户列表",
      //   component: () => import("../views/pages/taocan/user.vue"),
      // },
      {
        path: "/dingdan",
        name: "签约用户列表",
        component: () => import("../views/pages/taocan/dingdan.vue"),
      },
      {
        path: "/qun",
        name: "签约客户群",
        component: () => import("../views/pages/yonghu/qun.vue"),
      },
    ],
  },
  {
    path: "/yonghu",
    name: "用户管理",
    component: Home,
    children: [
      {
        path: "/user",
        name: "用户",
        component: () => import("../views/pages/yonghu/user.vue"),
      },
      {
        path: "/debts",
        name: "债务人列表",
        component: () => import("../views/pages/debt/debts.vue"),
      }
    ],
  },
  {
    path: "/zhifu",
    name: "支付列表",
    component: Home,
    children: [
      {
        path: "/order",
        name: "支付列表",
        component: () => import("../views/pages/yonghu/order.vue"),
      }
    ],
  },
  {
    path: "/liaotian",
    name: "聊天列表",
    component: Home,
    children: [
      {
        path: "/chat",
        name: "聊天列表",
        component: () => import("../views/pages/yonghu/chat.vue"),
      }
    ],
  },
  {
    path: "/wenshuguanli",
    name: "文书管理",
    component: Home,
    children: [
      {
        path: "/dingzhi",
        name: "合同定制",
        component: () => import("../views/pages/wenshu/dingzhi.vue"),
      },
      {
        path: "/shenhe",
        name: "合同审核",
        component: () => import("../views/pages/wenshu/shenhe.vue"),
      },
      {
        path: "/cate",
        name: "合同类型",
        component: () => import("../views/pages/wenshu/cate.vue"),
      },
      {
        path: "/hetong",
        name: "合同列表",
        component: () => import("../views/pages/wenshu/index.vue"),
      },
      {
        path: "/lawyer",
        name: "发律师函",
        component: () => import("../views/pages/yonghu/lawyer.vue"),
      },
      {
        path: "/kecheng",
        name: "课程列表",
        component: () => import("../views/pages/shipin/kecheng.vue"),
      }
    ],
  },
  {
    path: "/yuangong",
    name: "员工管理",
    component: Home,
    children: [
      {
        path: "/zhiwei",
        name: "职位",
        component: () => import("../views/pages/yuangong/zhiwei.vue"),
      },
      {
        path: "/yuangong",
        name: "员工",
        component: () => import("../views/pages/yuangong/index.vue"),
      },
      {
        path: "/lvshi",
        name: "律师",
        component: () => import("../views/pages/lvshi/lvshi.vue"),
      },
      {
        path: "/quanxian",
        name: "权限管理",
        component: () => import("../views/pages/yuangong/quanxian.vue"),
      }
    ],
  },
  {
    path: "/fuwu",
    name: "服务管理",
    component: Home,
    children: [
      {
        path: "/fuwu",
        name: "服务列表",
        component: () => import("../views/pages/fuwu/index.vue"),
      },
    ],
  },
  {
    path: "/xinwen",
    name: "案例管理",
    component: Home,
    children: [
      {
        path: "/anli",
        name: "案例列表",
        component: () => import("../views/pages/xinwen/xinwen.vue"),
      },
    ],
  },
  {
    path: "/lvshiguanli",
    name: "专业管理",
    component: Home,
    children: [
      {
        path: "/zhuanye",
        name: "专业列表",
        component: () => import("../views/pages/lvshi/zhuanye.vue"),
      }
    ],
  },
  // 个人信息和设置页面
  {
    path: "/profile",
    name: "个人信息",
    component: () => import("../views/pages/profile/index.vue"),
    hidden: true,
  },
  {
    path: "/changePwd",
    name: "修改密码",
    component: () => import("../views/pages/changePwd.vue"),
    hidden: true,
  },
  {
    path: "/archive",
    name: "归档管理",
    component: Home,
    children: [
      {
        path: "/archive/file",
        name: "文件归档",
        component: () => import("../views/pages/archive/File.vue"),
      },
      {
        path: "/archive/search",
        name: "档案检索",
        component: () => import("../views/pages/archive/Search.vue"),
      }
    ]
  },
  {
    path: "/todo",
    name: "待办事项",
    component: Home,
    children: [
      {
        path: "/todo/list",
        name: "待办事项管理",
        component: () => import("../views/pages/TodoList.vue"),
      }
    ]
  },
  {
    path: "/notifications",
    name: "系统通知",
    component: () => import("../views/pages/NotificationList.vue"),
  },
];

const router = new VueRouter({
  routes,
});
router.beforeEach((to, from, next) => {
  // 纯前端模式 - 简化路由守卫
  if (to.path != "/login") {
    // 检查是否有token，如果没有则跳转到登录页
    const token = store.getters.GET_TOKEN;
    if (!token) {
      next({
        path: "/login",
      });
    } else {
      next();
    }
  } else {
    next();
  }
});
export default router;
