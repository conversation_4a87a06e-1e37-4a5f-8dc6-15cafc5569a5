{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748540171931}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["quanxian.vue"], "names": [], "mappings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file": "quanxian.vue", "sourceRoot": "src/views/pages/yuangong", "sourcesContent": ["<template>\r\n  <div class=\"permission-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-key\"></i>\r\n            权限管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统功能权限和访问控制</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增权限\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入权限名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n            \r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限类型</label>\r\n              <el-select\r\n                v-model=\"search.type\"\r\n                placeholder=\"请选择权限类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 权限树形结构 -->\r\n    <div class=\"tree-section\">\r\n      <el-card shadow=\"never\" class=\"tree-card\">\r\n        <div class=\"tree-header\">\r\n          <div class=\"tree-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            权限树形结构\r\n          </div>\r\n          <div class=\"tree-tools\">\r\n            <el-button-group>\r\n              <el-button \r\n                :type=\"viewMode === 'tree' ? 'primary' : ''\" \r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'tree'\"\r\n                size=\"small\"\r\n              >\r\n                树形视图\r\n              </el-button>\r\n              <el-button \r\n                :type=\"viewMode === 'table' ? 'primary' : ''\" \r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 树形视图 -->\r\n        <div v-if=\"viewMode === 'tree'\" class=\"tree-view\">\r\n          <el-tree\r\n            :data=\"treeData\"\r\n            :props=\"treeProps\"\r\n            :default-expand-all=\"true\"\r\n            node-key=\"id\"\r\n            class=\"permission-tree\"\r\n          >\r\n            <span class=\"tree-node\" slot-scope=\"{ node, data }\">\r\n              <div class=\"node-content\">\r\n                <div class=\"node-info\">\r\n                  <i :class=\"getNodeIcon(data.type)\"></i>\r\n                  <span class=\"node-label\">{{ data.label }}</span>\r\n                  <el-tag \r\n                    :type=\"data.status === 1 ? 'success' : 'danger'\" \r\n                    size=\"mini\"\r\n                    class=\"node-status\"\r\n                  >\r\n                    {{ data.status === 1 ? '启用' : '禁用' }}\r\n                  </el-tag>\r\n                </div>\r\n                <div class=\"node-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(data.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click=\"addChild(data)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(data.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </span>\r\n          </el-tree>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"tableData\"\r\n            v-loading=\"loading\"\r\n            class=\"permission-table\"\r\n            stripe\r\n            row-key=\"id\"\r\n            :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n            :default-expand-all=\"false\"\r\n          >\r\n            <el-table-column prop=\"label\" label=\"权限名称\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-name-cell\" :style=\"{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }\">\r\n                  <i :class=\"getNodeIcon(scope.row.type)\" style=\"margin-right: 8px;\"></i>\r\n                  <span class=\"permission-name\">{{ scope.row.label }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"code\" label=\"权限代码\" width=\"180\" align=\"center\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"type\" label=\"权限类型\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  :type=\"getTypeColor(scope.row.type)\"\r\n                  size=\"small\"\r\n                >\r\n                  {{ getTypeLabel(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"240\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"addChild(scope.row)\"\r\n                    icon=\"el-icon-plus\"\r\n                    plain\r\n                    v-if=\"scope.row.type === 'menu'\"\r\n                  >\r\n                    添加子权限\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click.stop=\"delData(scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 编辑权限对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"60%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限名称\" prop=\"label\">\r\n              <el-input v-model=\"ruleForm.label\" placeholder=\"请输入权限名称\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限代码\" prop=\"code\">\r\n              <el-input v-model=\"ruleForm.code\" placeholder=\"请输入权限代码\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"权限类型\" prop=\"type\">\r\n              <el-select v-model=\"ruleForm.type\" placeholder=\"请选择权限类型\" style=\"width: 100%\">\r\n                <el-option label=\"菜单权限\" value=\"menu\"></el-option>\r\n                <el-option label=\"操作权限\" value=\"action\"></el-option>\r\n                <el-option label=\"数据权限\" value=\"data\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"父级权限\">\r\n              <el-cascader\r\n                v-model=\"ruleForm.parent_id\"\r\n                :options=\"parentOptions\"\r\n                :props=\"cascaderProps\"\r\n                placeholder=\"请选择父级权限\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              ></el-cascader>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" :max=\"999\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-switch\r\n                v-model=\"ruleForm.status\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"启用\"\r\n                inactive-text=\"禁用\"\r\n              >\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"权限描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.description\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入权限描述\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路由路径\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.path\" placeholder=\"请输入路由路径\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"图标\" v-if=\"ruleForm.type === 'menu'\">\r\n          <el-input v-model=\"ruleForm.icon\" placeholder=\"请输入图标类名\">\r\n            <template slot=\"prepend\">\r\n              <i :class=\"ruleForm.icon || 'el-icon-menu'\"></i>\r\n            </template>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PermissionManagement\",\r\n  data() {\r\n    return {\r\n      viewMode: 'tree', // tree | table\r\n      loading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      },\r\n      treeData: [],\r\n      originalData: [], // 保存原始数据\r\n      dialogFormVisible: false,\r\n      dialogTitle: \"新增权限\",\r\n      parentOptions: [],\r\n      ruleForm: {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"menu\",\r\n        parent_id: null,\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      },\r\n      rules: {\r\n        label: [\r\n          { required: true, message: \"请输入权限名称\", trigger: \"blur\" }\r\n        ],\r\n        code: [\r\n          { required: true, message: \"请输入权限代码\", trigger: \"blur\" }\r\n        ],\r\n        type: [\r\n          { required: true, message: \"请选择权限类型\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      cascaderProps: {\r\n        value: 'id',\r\n        label: 'label',\r\n        children: 'children',\r\n        checkStrictly: true\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 表格数据 - 将树形数据扁平化但保持层级关系\r\n    tableData() {\r\n      return this.flattenTreeForTable(this.treeData);\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限数据\r\n    getData() {\r\n      this.loading = true;\r\n      \r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        this.loading = false;\r\n        \r\n        const mockData = [\r\n          {\r\n            id: 1,\r\n            label: \"系统管理\",\r\n            code: \"system\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 1,\r\n            status: 1,\r\n            description: \"系统管理模块\",\r\n            path: \"/system\",\r\n            icon: \"el-icon-setting\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 11,\r\n                label: \"用户管理\",\r\n                code: \"system:user\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"用户管理功能\",\r\n                path: \"/user\",\r\n                icon: \"el-icon-user\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 111,\r\n                    label: \"查看用户\",\r\n                    code: \"system:user:view\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看用户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 112,\r\n                    label: \"新增用户\",\r\n                    code: \"system:user:add\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 113,\r\n                    label: \"编辑用户\",\r\n                    code: \"system:user:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑用户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 114,\r\n                    label: \"删除用户\",\r\n                    code: \"system:user:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 11,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除用户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 12,\r\n                label: \"职位管理\",\r\n                code: \"system:position\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"职位管理功能\",\r\n                path: \"/zhiwei\",\r\n                icon: \"el-icon-postcard\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 121,\r\n                    label: \"查看职位\",\r\n                    code: \"system:position:view\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看职位列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 122,\r\n                    label: \"新增职位\",\r\n                    code: \"system:position:add\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 123,\r\n                    label: \"编辑职位\",\r\n                    code: \"system:position:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑职位信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 124,\r\n                    label: \"删除职位\",\r\n                    code: \"system:position:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 12,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除职位\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 13,\r\n                label: \"权限管理\",\r\n                code: \"system:permission\",\r\n                type: \"menu\",\r\n                parent_id: 1,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"权限管理功能\",\r\n                path: \"/quanxian\",\r\n                icon: \"el-icon-key\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 131,\r\n                    label: \"查看权限\",\r\n                    code: \"system:permission:view\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看权限列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 132,\r\n                    label: \"新增权限\",\r\n                    code: \"system:permission:add\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 133,\r\n                    label: \"编辑权限\",\r\n                    code: \"system:permission:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑权限信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 134,\r\n                    label: \"删除权限\",\r\n                    code: \"system:permission:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 13,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除权限\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            label: \"业务管理\",\r\n            code: \"business\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 2,\r\n            status: 1,\r\n            description: \"业务管理模块\",\r\n            path: \"/business\",\r\n            icon: \"el-icon-suitcase\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 21,\r\n                label: \"订单管理\",\r\n                code: \"business:order\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"订单管理功能\",\r\n                path: \"/dingdan\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 211,\r\n                    label: \"查看订单\",\r\n                    code: \"business:order:view\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看订单列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 212,\r\n                    label: \"新增订单\",\r\n                    code: \"business:order:add\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 213,\r\n                    label: \"编辑订单\",\r\n                    code: \"business:order:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑订单信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 214,\r\n                    label: \"删除订单\",\r\n                    code: \"business:order:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除订单\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 215,\r\n                    label: \"导出订单\",\r\n                    code: \"business:order:export\",\r\n                    type: \"action\",\r\n                    parent_id: 21,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"导出订单数据\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 22,\r\n                label: \"客户管理\",\r\n                code: \"business:customer\",\r\n                type: \"menu\",\r\n                parent_id: 2,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"客户管理功能\",\r\n                path: \"/customer\",\r\n                icon: \"el-icon-user-solid\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 221,\r\n                    label: \"查看客户\",\r\n                    code: \"business:customer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看客户列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 222,\r\n                    label: \"新增客户\",\r\n                    code: \"business:customer:add\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 223,\r\n                    label: \"编辑客户\",\r\n                    code: \"business:customer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑客户信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 224,\r\n                    label: \"删除客户\",\r\n                    code: \"business:customer:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 22,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除客户\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            label: \"文书管理\",\r\n            code: \"document\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 3,\r\n            status: 1,\r\n            description: \"文书管理模块\",\r\n            path: \"/document\",\r\n            icon: \"el-icon-document-copy\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 31,\r\n                label: \"合同管理\",\r\n                code: \"document:contract\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"合同管理功能\",\r\n                path: \"/hetong\",\r\n                icon: \"el-icon-document\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 311,\r\n                    label: \"查看合同\",\r\n                    code: \"document:contract:view\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看合同列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 312,\r\n                    label: \"新增合同\",\r\n                    code: \"document:contract:add\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 313,\r\n                    label: \"编辑合同\",\r\n                    code: \"document:contract:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑合同信息\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 314,\r\n                    label: \"删除合同\",\r\n                    code: \"document:contract:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 315,\r\n                    label: \"审核合同\",\r\n                    code: \"document:contract:audit\",\r\n                    type: \"action\",\r\n                    parent_id: 31,\r\n                    sort: 5,\r\n                    status: 1,\r\n                    description: \"审核合同\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 32,\r\n                label: \"律师函管理\",\r\n                code: \"document:lawyer\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 2,\r\n                status: 1,\r\n                description: \"律师函管理功能\",\r\n                path: \"/lawyer\",\r\n                icon: \"el-icon-message\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 321,\r\n                    label: \"查看律师函\",\r\n                    code: \"document:lawyer:view\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看律师函列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 322,\r\n                    label: \"发送律师函\",\r\n                    code: \"document:lawyer:send\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"发送律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 323,\r\n                    label: \"编辑律师函\",\r\n                    code: \"document:lawyer:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 32,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑律师函\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              },\r\n              {\r\n                id: 33,\r\n                label: \"课程管理\",\r\n                code: \"document:course\",\r\n                type: \"menu\",\r\n                parent_id: 3,\r\n                sort: 3,\r\n                status: 1,\r\n                description: \"课程管理功能\",\r\n                path: \"/kecheng\",\r\n                icon: \"el-icon-video-play\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 331,\r\n                    label: \"查看课程\",\r\n                    code: \"document:course:view\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看课程列表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 332,\r\n                    label: \"新增课程\",\r\n                    code: \"document:course:add\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"新增课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 333,\r\n                    label: \"编辑课程\",\r\n                    code: \"document:course:edit\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"编辑课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 334,\r\n                    label: \"删除课程\",\r\n                    code: \"document:course:delete\",\r\n                    type: \"action\",\r\n                    parent_id: 33,\r\n                    sort: 4,\r\n                    status: 1,\r\n                    description: \"删除课程\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            label: \"财务管理\",\r\n            code: \"finance\",\r\n            type: \"menu\",\r\n            parent_id: null,\r\n            sort: 4,\r\n            status: 1,\r\n            description: \"财务管理模块\",\r\n            path: \"/finance\",\r\n            icon: \"el-icon-coin\",\r\n            create_time: \"2024-01-01 10:00:00\",\r\n            children: [\r\n              {\r\n                id: 41,\r\n                label: \"支付管理\",\r\n                code: \"finance:payment\",\r\n                type: \"menu\",\r\n                parent_id: 4,\r\n                sort: 1,\r\n                status: 1,\r\n                description: \"支付管理功能\",\r\n                path: \"/order\",\r\n                icon: \"el-icon-money\",\r\n                create_time: \"2024-01-01 10:00:00\",\r\n                children: [\r\n                  {\r\n                    id: 411,\r\n                    label: \"查看支付记录\",\r\n                    code: \"finance:payment:view\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 1,\r\n                    status: 1,\r\n                    description: \"查看支付记录\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 412,\r\n                    label: \"处理退款\",\r\n                    code: \"finance:payment:refund\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 2,\r\n                    status: 1,\r\n                    description: \"处理退款申请\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  },\r\n                  {\r\n                    id: 413,\r\n                    label: \"导出财务报表\",\r\n                    code: \"finance:payment:export\",\r\n                    type: \"action\",\r\n                    parent_id: 41,\r\n                    sort: 3,\r\n                    status: 1,\r\n                    description: \"导出财务报表\",\r\n                    create_time: \"2024-01-01 10:00:00\"\r\n                  }\r\n                ]\r\n              }\r\n            ]\r\n          }\r\n        ];\r\n        \r\n        this.treeData = mockData;\r\n        this.originalData = JSON.parse(JSON.stringify(mockData)); // 深拷贝保存原始数据\r\n        this.updateParentOptions();\r\n      }, 500);\r\n    },\r\n\r\n    // 将树形数据扁平化用于表格显示（保持层级结构）\r\n    flattenTreeForTable(tree, level = 0, result = []) {\r\n      tree.forEach(node => {\r\n        const flatNode = {\r\n          ...node,\r\n          level: level,\r\n          hasChildren: node.children && node.children.length > 0\r\n        };\r\n        // 移除children属性避免表格渲染问题\r\n        delete flatNode.children;\r\n        result.push(flatNode);\r\n\r\n        // 递归处理子节点\r\n        if (node.children && node.children.length > 0) {\r\n          this.flattenTreeForTable(node.children, level + 1, result);\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n\r\n    // 更新父级权限选项\r\n    updateParentOptions() {\r\n      this.parentOptions = this.buildCascaderOptions(this.treeData);\r\n    },\r\n\r\n    // 构建级联选择器选项\r\n    buildCascaderOptions(tree) {\r\n      return tree.map(node => ({\r\n        id: node.id,\r\n        label: node.label,\r\n        children: node.children ? this.buildCascaderOptions(node.children) : []\r\n      }));\r\n    },\r\n\r\n    // 搜索数据\r\n    searchData() {\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        type: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 刷新页面\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n\r\n    // 编辑权限\r\n    editData(id) {\r\n      if (id === 0) {\r\n        this.dialogTitle = \"新增权限\";\r\n        this.ruleForm = {\r\n          id: null,\r\n          label: \"\",\r\n          code: \"\",\r\n          type: \"menu\",\r\n          parent_id: null,\r\n          sort: 0,\r\n          status: 1,\r\n          description: \"\",\r\n          path: \"\",\r\n          icon: \"\"\r\n        };\r\n      } else {\r\n        this.dialogTitle = \"编辑权限\";\r\n        const permission = this.findPermissionById(id);\r\n        if (permission) {\r\n          this.ruleForm = { ...permission };\r\n        }\r\n      }\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 添加子权限\r\n    addChild(parentData) {\r\n      this.dialogTitle = \"新增子权限\";\r\n      this.ruleForm = {\r\n        id: null,\r\n        label: \"\",\r\n        code: \"\",\r\n        type: \"action\",\r\n        parent_id: [parentData.id],\r\n        sort: 0,\r\n        status: 1,\r\n        description: \"\",\r\n        path: \"\",\r\n        icon: \"\"\r\n      };\r\n      this.dialogFormVisible = true;\r\n    },\r\n\r\n    // 根据ID查找权限（在原始数据中查找）\r\n    findPermissionById(id, tree = this.originalData) {\r\n      for (let node of tree) {\r\n        if (node.id === id) {\r\n          return node;\r\n        }\r\n        if (node.children) {\r\n          const found = this.findPermissionById(id, node.children);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 删除权限\r\n    delData(id) {\r\n      this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        // 模拟删除\r\n        this.$message.success(\"删除成功！\");\r\n        this.getData();\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n\r\n    // 保存权限\r\n    saveData() {\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 模拟保存\r\n          this.$message.success(this.ruleForm.id ? \"更新成功！\" : \"新增成功！\");\r\n          this.dialogFormVisible = false;\r\n          this.getData();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 改变状态\r\n    changeStatus(row) {\r\n      this.$message.success(`权限状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 获取节点图标\r\n    getNodeIcon(type) {\r\n      const iconMap = {\r\n        menu: 'el-icon-menu',\r\n        action: 'el-icon-setting',\r\n        data: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || 'el-icon-menu';\r\n    },\r\n\r\n    // 获取类型颜色\r\n    getTypeColor(type) {\r\n      const colorMap = {\r\n        menu: 'primary',\r\n        action: 'success',\r\n        data: 'warning'\r\n      };\r\n      return colorMap[type] || 'primary';\r\n    },\r\n\r\n    // 获取类型标签\r\n    getTypeLabel(type) {\r\n      const labelMap = {\r\n        menu: '菜单权限',\r\n        action: '操作权限',\r\n        data: '数据权限'\r\n      };\r\n      return labelMap[type] || '菜单权限';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 权限管理容器 */\r\n.permission-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 树形结构区域 */\r\n.tree-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.tree-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tree-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tree-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.tree-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 树形视图样式 */\r\n.tree-view {\r\n  padding: 24px;\r\n}\r\n\r\n.permission-tree {\r\n  background: transparent;\r\n}\r\n\r\n.tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.node-content {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n}\r\n\r\n.node-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.node-label {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.node-status {\r\n  margin-left: 8px;\r\n}\r\n\r\n.node-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.permission-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.permission-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.permission-name {\r\n  font-weight: 500;\r\n  color: #262626;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 4px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-buttons .el-button {\r\n  margin: 2px;\r\n}\r\n\r\n/* 表格行层级样式 */\r\n.permission-table .el-table__row[data-level=\"0\"] {\r\n  background-color: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"1\"] {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.permission-table .el-table__row[data-level=\"2\"] {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 对话框样式 */\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .permission-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .tree-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .node-content {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n\r\n  .node-actions {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}