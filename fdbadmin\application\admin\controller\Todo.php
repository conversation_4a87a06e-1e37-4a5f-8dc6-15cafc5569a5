<?php
namespace app\admin\controller;

use think\Request;
use untils\JsonService;
use models\Todos;

/**
 * 待办事项控制器
 */
class Todo extends Base
{
    /**
     * 获取待办事项列表
     * @param Request $request
     * @return \think\response\Json
     */
    public function getList(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 20);
            $status = $request->get('status', '');
            $priority = $request->get('priority', '');
            $type = $request->get('type', '');

            $where = [];
            
            // 只显示分配给当前用户的待办事项
            $where[] = ['assigned_to', '=', $this->userid];
            
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            
            if ($priority !== '') {
                $where[] = ['priority', '=', $priority];
            }
            
            if ($type !== '') {
                $where[] = ['type', '=', $type];
            }

            $total = Todos::where($where)->count();
            
            $list = Todos::where($where)
                ->order('priority desc, due_date asc, create_time desc')
                ->page($page, $size)
                ->select()
                ->toArray();

            // 格式化数据
            foreach ($list as &$item) {
                $item['completed'] = $item['status'] == 1;
                $item['due_date'] = $item['due_date'] ? date('Y-m-d H:i', $item['due_date']) : null;
                $item['type_text'] = Todos::getTypeText($item['type']);
                $item['priority_text'] = Todos::getPriorityText($item['priority']);
                $item['create_time_text'] = date('Y-m-d H:i', $item['create_time']);
            }

            $data = [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'size' => $size
            ];

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 创建待办事项
     * @param Request $request
     * @return \think\response\Json
     */
    public function create(Request $request)
    {
        try {
            $data = [
                'title' => $request->post('title'),
                'description' => $request->post('description', ''),
                'type' => $request->post('type', 'general'),
                'priority' => $request->post('priority', 'medium'),
                'due_date' => $request->post('due_date') ? strtotime($request->post('due_date')) : null,
                'assigned_to' => $this->userid,
                'created_by' => $this->userid
            ];

            // 验证必填字段
            if (empty($data['title'])) {
                return JsonService::fail('标题不能为空');
            }

            $result = Todos::createTodo($data);

            if ($result) {
                return JsonService::successful('创建成功');
            } else {
                return JsonService::fail('创建失败');
            }

        } catch (\Exception $e) {
            return JsonService::fail('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新待办事项
     * @param Request $request
     * @return \think\response\Json
     */
    public function update(Request $request)
    {
        try {
            $id = $request->post('id');
            $data = [
                'title' => $request->post('title'),
                'description' => $request->post('description', ''),
                'type' => $request->post('type', 'general'),
                'priority' => $request->post('priority', 'medium'),
                'due_date' => $request->post('due_date') ? strtotime($request->post('due_date')) : null,
            ];

            // 验证必填字段
            if (empty($data['title'])) {
                return JsonService::fail('标题不能为空');
            }

            $where = [
                ['id', '=', $id],
                ['assigned_to', '=', $this->userid]
            ];

            $result = Todos::where($where)->update($data);

            if ($result) {
                return JsonService::successful('更新成功');
            } else {
                return JsonService::fail('更新失败或无权限');
            }

        } catch (\Exception $e) {
            return JsonService::fail('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除待办事项
     * @param Request $request
     * @return \think\response\Json
     */
    public function delete(Request $request)
    {
        try {
            $id = $request->param('id');

            $where = [
                ['id', '=', $id],
                ['assigned_to', '=', $this->userid]
            ];

            $result = Todos::where($where)->delete();

            if ($result) {
                return JsonService::successful('删除成功');
            } else {
                return JsonService::fail('删除失败或无权限');
            }

        } catch (\Exception $e) {
            return JsonService::fail('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取待办事项详情
     * @param Request $request
     * @return \think\response\Json
     */
    public function detail(Request $request)
    {
        try {
            $id = $request->param('id');

            $where = [
                ['id', '=', $id],
                ['assigned_to', '=', $this->userid]
            ];

            $todo = Todos::where($where)->find();

            if (!$todo) {
                return JsonService::fail('待办事项不存在或无权限');
            }

            $data = $todo->toArray();
            $data['completed'] = $data['status'] == 1;
            $data['due_date'] = $data['due_date'] ? date('Y-m-d H:i:s', $data['due_date']) : null;
            $data['type_text'] = Todos::getTypeText($data['type']);
            $data['priority_text'] = Todos::getPriorityText($data['priority']);
            $data['create_time_text'] = date('Y-m-d H:i:s', $data['create_time']);

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取统计信息
     * @return \think\response\Json
     */
    public function getStats()
    {
        try {
            $where = [
                ['assigned_to', '=', $this->userid]
            ];

            $total = Todos::where($where)->count();
            $pending = Todos::where($where)->where('status', 0)->count();
            $completed = Todos::where($where)->where('status', 1)->count();
            
            // 即将到期的任务（3天内）
            $dueSoon = Todos::where($where)
                ->where('status', 0)
                ->where('due_date', '>', 0)
                ->where('due_date', '<=', time() + (3 * 86400))
                ->count();

            // 已过期的任务
            $overdue = Todos::where($where)
                ->where('status', 0)
                ->where('due_date', '>', 0)
                ->where('due_date', '<', time())
                ->count();

            $data = [
                'total' => $total,
                'pending' => $pending,
                'completed' => $completed,
                'due_soon' => $dueSoon,
                'overdue' => $overdue
            ];

            return JsonService::successful('获取成功', $data);

        } catch (\Exception $e) {
            return JsonService::fail('获取失败：' . $e->getMessage());
        }
    }
}
