{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue?vue&type=style&index=0&id=2d7a90a9&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\cate.vue", "mtime": 1748540171920}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["cate.vue"], "names": [], "mappings": ";AAqqBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "cate.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-type-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 操作区域 -->\r\n    <div class=\"action-section\">\r\n      <div class=\"search-area\">\r\n        <el-input\r\n          placeholder=\"请输入合同类型名称\"\r\n          v-model=\"search.keyword\"\r\n          class=\"search-input\"\r\n          clearable\r\n        >\r\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n            type=\"primary\"\r\n          >\r\n            搜索\r\n          </el-button>\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"button-area\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"editData(0)\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-btn\"\r\n        >\r\n          新增合同类型\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同类型数据\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"合同类型名称\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-name\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"template_status\" label=\"合同模板\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"template-status\">\r\n              <el-tag\r\n                :type=\"scope.row.template_file ? 'success' : 'warning'\"\r\n                size=\"mini\"\r\n                :icon=\"scope.row.template_file ? 'el-icon-document' : 'el-icon-warning'\"\r\n              >\r\n                {{ scope.row.template_file ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"editData(scope.row.id)\"\r\n                icon=\"el-icon-edit\"\r\n                class=\"action-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.template_file\"\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"downloadTemplate(scope.row)\"\r\n                icon=\"el-icon-download\"\r\n                class=\"action-btn\"\r\n              >\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"合同模板\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"template_file\"\r\n          :rules=\"templateRules\"\r\n        >\r\n          <div class=\"template-upload-area\">\r\n            <!-- 文件上传区域 -->\r\n            <div v-if=\"!ruleForm.template_file\" class=\"upload-section\">\r\n              <el-upload\r\n                ref=\"templateUpload\"\r\n                :action=\"uploadAction\"\r\n                :before-upload=\"beforeTemplateUpload\"\r\n                :on-success=\"handleTemplateSuccess\"\r\n                :on-error=\"handleTemplateError\"\r\n                :show-file-list=\"false\"\r\n                accept=\".doc,.docx,.pdf\"\r\n                :auto-upload=\"true\"\r\n                class=\"template-uploader\"\r\n              >\r\n                <el-button type=\"primary\" icon=\"el-icon-upload\">\r\n                  <span>上传合同模板</span>\r\n                </el-button>\r\n              </el-upload>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>支持 .doc、.docx、.pdf 格式，文件大小不超过 10MB</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 已上传文件显示区域 -->\r\n            <div v-else class=\"uploaded-file\">\r\n              <div class=\"file-info\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ ruleForm.template_name || '合同模板文件' }}</span>\r\n                <span class=\"file-size\">{{ formatFileSize(ruleForm.template_size) }}</span>\r\n              </div>\r\n              <div class=\"file-actions\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"previewTemplate\"\r\n                  icon=\"el-icon-view\"\r\n                  size=\"mini\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"downloadCurrentTemplate\"\r\n                  icon=\"el-icon-download\"\r\n                  size=\"mini\"\r\n                >\r\n                  下载\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"replaceTemplate\"\r\n                  icon=\"el-icon-refresh\"\r\n                  size=\"mini\"\r\n                >\r\n                  替换\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"removeTemplate\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  class=\"danger-text\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 隐藏的替换上传组件 -->\r\n            <el-upload\r\n              v-show=\"false\"\r\n              ref=\"replaceUpload\"\r\n              :action=\"uploadAction\"\r\n              :before-upload=\"beforeTemplateUpload\"\r\n              :on-success=\"handleTemplateSuccess\"\r\n              :on-error=\"handleTemplateError\"\r\n              :show-file-list=\"false\"\r\n              accept=\".doc,.docx,.pdf\"\r\n              :auto-upload=\"true\"\r\n            >\r\n            </el-upload>\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshucate/\",\r\n      title: \"文书类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n        template_file: \"\",\r\n        template_name: \"\",\r\n        template_size: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n\r\n      // 合同模板验证规则\r\n      templateRules: [\r\n        {\r\n          required: true,\r\n          message: \"请上传合同模板\",\r\n          trigger: \"change\",\r\n        },\r\n      ],\r\n\r\n      formLabelWidth: \"120px\",\r\n      uploadAction: \"/admin/Upload/uploadFile\", // 文件上传接口\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          template_file: \"\",\r\n          template_name: \"\",\r\n          template_size: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 模拟搜索测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n\r\n        if (_this.search.keyword) {\r\n          _this.list = allData.filter(item =>\r\n            item.title.includes(_this.search.keyword)\r\n          );\r\n        } else {\r\n          _this.list = allData;\r\n        }\r\n\r\n        _this.total = _this.list.length;\r\n        _this.loading = false;\r\n      }, 300);\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"劳动合同\",\r\n            create_time: \"2024-03-20\",\r\n            template_file: \"/uploads/templates/labor_contract_template.docx\",\r\n            template_name: \"劳动合同模板.docx\",\r\n            template_size: 245760\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"租赁合同\",\r\n            create_time: \"2024-03-19\",\r\n            template_file: \"/uploads/templates/lease_contract_template.pdf\",\r\n            template_name: \"租赁合同模板.pdf\",\r\n            template_size: 512000\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"买卖合同\",\r\n            create_time: \"2024-03-18\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"服务合同\",\r\n            create_time: \"2024-03-17\",\r\n            template_file: \"/uploads/templates/service_contract_template.doc\",\r\n            template_name: \"服务合同模板.doc\",\r\n            template_size: 327680\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"借款合同\",\r\n            create_time: \"2024-03-16\",\r\n            template_file: \"\",\r\n            template_name: \"\",\r\n            template_size: 0\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 合同模板相关方法\r\n    beforeTemplateUpload(file) {\r\n      const isValidType = /\\.(doc|docx|pdf)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('合同模板只能是 .doc、.docx、.pdf 格式!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('合同模板文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n\r\n      this.$message.info('正在上传合同模板...');\r\n      return true;\r\n    },\r\n\r\n    handleTemplateSuccess(response, file) {\r\n      if (response.code === 200) {\r\n        this.ruleForm.template_file = response.data.url;\r\n        this.ruleForm.template_name = file.name;\r\n        this.ruleForm.template_size = file.size;\r\n        this.$message.success('合同模板上传成功!');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      } else {\r\n        this.$message.error(response.msg || '合同模板上传失败!');\r\n      }\r\n    },\r\n\r\n    handleTemplateError(err, file) {\r\n      this.$message.error('合同模板上传失败，请重试!');\r\n      console.error('Template upload error:', err);\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    },\r\n\r\n    // 预览模板\r\n    previewTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        // 这里可以实现文件预览功能\r\n        this.$message.info('预览功能开发中...');\r\n        // window.open(this.ruleForm.template_file, '_blank');\r\n      }\r\n    },\r\n\r\n    // 下载当前模板\r\n    downloadCurrentTemplate() {\r\n      if (this.ruleForm.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = this.ruleForm.template_file;\r\n        link.download = this.ruleForm.template_name || '合同模板';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success('开始下载合同模板');\r\n      }\r\n    },\r\n\r\n    // 替换模板\r\n    replaceTemplate() {\r\n      this.$refs.replaceUpload.$el.querySelector('input').click();\r\n    },\r\n\r\n    // 删除模板\r\n    removeTemplate() {\r\n      this.$confirm('确定要删除当前合同模板吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.ruleForm.template_file = '';\r\n        this.ruleForm.template_name = '';\r\n        this.ruleForm.template_size = 0;\r\n        this.$message.success('合同模板已删除');\r\n\r\n        // 触发表单验证\r\n        this.$refs.ruleForm.validateField('template_file');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 下载表格中的模板\r\n    downloadTemplate(row) {\r\n      if (row.template_file) {\r\n        const link = document.createElement('a');\r\n        link.href = row.template_file;\r\n        link.download = row.template_name || `${row.title}模板`;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        this.$message.success(`开始下载 ${row.title} 模板`);\r\n      } else {\r\n        this.$message.warning('该合同类型暂无模板文件');\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-type-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 操作区域 */\r\n.action-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.search-area {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.button-area {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn {\r\n  font-weight: 500;\r\n  padding: 10px 20px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.type-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.type-name i {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-name span {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-type-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .action-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-area {\r\n    max-width: none;\r\n  }\r\n\r\n  .button-area {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 小屏幕下操作按钮调整 */\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n}\r\n\r\n/* 操作按钮横向布局 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n}\r\n\r\n.el-button--success:hover {\r\n  background: #85ce61;\r\n  border-color: #85ce61;\r\n}\r\n\r\n.el-button--danger:hover {\r\n  background: #f78989;\r\n  border-color: #f78989;\r\n}\r\n\r\n/* 合同模板状态样式 */\r\n.template-status {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n/* 合同模板上传区域样式 */\r\n.template-upload-area {\r\n  width: 100%;\r\n}\r\n\r\n.upload-section {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-section:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.template-uploader {\r\n  display: block;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.upload-tip i {\r\n  color: #409eff;\r\n}\r\n\r\n/* 已上传文件显示样式 */\r\n.uploaded-file {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.file-info i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.file-size {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-actions .el-button--text {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  margin: 0;\r\n}\r\n\r\n.danger-text {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n.danger-text:hover {\r\n  color: #f78989 !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .file-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .file-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"]}]}