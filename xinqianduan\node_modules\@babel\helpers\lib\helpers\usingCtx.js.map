{"version": 3, "names": ["_usingCtx", "_disposeSuppressedError", "SuppressedError", "error", "suppressed", "err", "Error", "name", "empty", "stack", "using", "isAwait", "value", "Object", "TypeError", "dispose", "Symbol", "asyncDispose", "for", "push", "v", "d", "a", "e", "u", "bind", "next", "resource", "pop", "disposalResult", "call", "Promise", "resolve", "then"], "sources": ["../../src/helpers/usingCtx.ts"], "sourcesContent": ["/* @minVersion 7.23.9 */\n\ntype Stack =\n  | {\n      v: Disposable | AsyncDisposable;\n      d: null | undefined | DisposeLike;\n      a: boolean;\n    }\n  | {\n      d: null | undefined;\n      a: true;\n    };\n\ntype DisposeLike = () => void | PromiseLike<void>;\n\ninterface UsingCtxReturn {\n  e: object;\n  u: (value: Disposable | null | undefined) => Disposable | null | undefined;\n  a: (\n    value: AsyncDisposable | Disposable | null | undefined,\n  ) => AsyncDisposable | Disposable | null | undefined;\n  d: DisposeLike;\n}\n\nexport default function _usingCtx(): UsingCtxReturn {\n  var _disposeSuppressedError =\n      typeof SuppressedError === \"function\"\n        ? SuppressedError\n        : (function (error: Error, suppressed: Error) {\n            var err = new Error() as SuppressedError;\n            err.name = \"SuppressedError\";\n            err.error = error;\n            err.suppressed = suppressed;\n            return err;\n          } as SuppressedErrorConstructor),\n    empty = {},\n    stack: Stack[] = [];\n  function using(\n    isAwait: true,\n    value: AsyncDisposable | Disposable | null | undefined,\n  ): AsyncDisposable | Disposable | null | undefined;\n  function using(\n    isAwait: false,\n    value: Disposable | null | undefined,\n  ): Disposable | null | undefined;\n  function using(\n    isAwait: boolean,\n    value: AsyncDisposable | Disposable | null | undefined,\n  ): AsyncDisposable | Disposable | null | undefined {\n    if (value != null) {\n      if (Object(value) !== value) {\n        throw new TypeError(\n          \"using declarations can only be used with objects, functions, null, or undefined.\",\n        );\n      }\n      // core-js-pure uses Symbol.for for polyfilling well-known symbols\n      if (isAwait) {\n        // value can either be an AsyncDisposable or a Disposable\n        // Try AsyncDisposable first\n        var dispose: DisposeLike | null | undefined = (\n          value as AsyncDisposable\n        )[Symbol.asyncDispose || Symbol.for(\"Symbol.asyncDispose\")];\n      }\n      if (dispose == null) {\n        dispose = (value as Disposable)[\n          Symbol.dispose || Symbol.for(\"Symbol.dispose\")\n        ];\n      }\n      if (typeof dispose !== \"function\") {\n        throw new TypeError(`Property [Symbol.dispose] is not a function.`);\n      }\n      stack.push({ v: value, d: dispose, a: isAwait });\n    } else if (isAwait) {\n      // provide the nullish `value` as `d` for minification gain\n      stack.push({ d: value, a: isAwait });\n    }\n    return value;\n  }\n  return {\n    // error\n    e: empty,\n    // using\n    u: using.bind(null, false),\n    // await using\n    // full generic signature to avoid type widening\n    a: using.bind<\n      null,\n      [true],\n      [AsyncDisposable | Disposable | null | undefined],\n      AsyncDisposable | Disposable | null | undefined\n    >(null, true),\n    // dispose\n    d: function () {\n      var error = this.e;\n\n      function next(): Promise<void> | void {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        while ((resource = stack.pop())) {\n          try {\n            var resource,\n              disposalResult = resource.d && resource.d.call(resource.v);\n            if (resource.a) {\n              return Promise.resolve(disposalResult).then(next, err);\n            }\n          } catch (e) {\n            return err(e as Error);\n          }\n        }\n        if (error !== empty) throw error;\n      }\n\n      function err(e: Error): Promise<void> | void {\n        error = error !== empty ? new _disposeSuppressedError(e, error) : e;\n\n        return next();\n      }\n\n      return next();\n    },\n  } satisfies UsingCtxReturn;\n}\n"], "mappings": ";;;;;;AAwBe,SAASA,SAASA,CAAA,EAAmB;EAClD,IAAIC,uBAAuB,GACvB,OAAOC,eAAe,KAAK,UAAU,GACjCA,eAAe,GACd,UAAUC,KAAY,EAAEC,UAAiB,EAAE;MAC1C,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAoB;MACxCD,GAAG,CAACE,IAAI,GAAG,iBAAiB;MAC5BF,GAAG,CAACF,KAAK,GAAGA,KAAK;MACjBE,GAAG,CAACD,UAAU,GAAGA,UAAU;MAC3B,OAAOC,GAAG;IACZ,CAAgC;IACtCG,KAAK,GAAG,CAAC,CAAC;IACVC,KAAc,GAAG,EAAE;EASrB,SAASC,KAAKA,CACZC,OAAgB,EAChBC,KAAsD,EACL;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIC,MAAM,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;QAC3B,MAAM,IAAIE,SAAS,CACjB,kFACF,CAAC;MACH;MAEA,IAAIH,OAAO,EAAE;QAGX,IAAII,OAAuC,GACzCH,KAAK,CACLI,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,GAAG,CAAC,qBAAqB,CAAC,CAAC;MAC7D;MACA,IAAIH,OAAO,IAAI,IAAI,EAAE;QACnBA,OAAO,GAAIH,KAAK,CACdI,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAC,CAC/C;MACH;MACA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;QACjC,MAAM,IAAID,SAAS,CAAE,8CAA6C,CAAC;MACrE;MACAL,KAAK,CAACU,IAAI,CAAC;QAAEC,CAAC,EAAER,KAAK;QAAES,CAAC,EAAEN,OAAO;QAAEO,CAAC,EAAEX;MAAQ,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIA,OAAO,EAAE;MAElBF,KAAK,CAACU,IAAI,CAAC;QAAEE,CAAC,EAAET,KAAK;QAAEU,CAAC,EAAEX;MAAQ,CAAC,CAAC;IACtC;IACA,OAAOC,KAAK;EACd;EACA,OAAO;IAELW,CAAC,EAAEf,KAAK;IAERgB,CAAC,EAAEd,KAAK,CAACe,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAG1BH,CAAC,EAAEZ,KAAK,CAACe,IAAI,CAKX,IAAI,EAAE,IAAI,CAAC;IAEbJ,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIlB,KAAK,GAAG,IAAI,CAACoB,CAAC;MAElB,SAASG,IAAIA,CAAA,EAAyB;QAEpC,OAAQC,QAAQ,GAAGlB,KAAK,CAACmB,GAAG,CAAC,CAAC,EAAG;UAC/B,IAAI;YACF,IAAID,QAAQ;cACVE,cAAc,GAAGF,QAAQ,CAACN,CAAC,IAAIM,QAAQ,CAACN,CAAC,CAACS,IAAI,CAACH,QAAQ,CAACP,CAAC,CAAC;YAC5D,IAAIO,QAAQ,CAACL,CAAC,EAAE;cACd,OAAOS,OAAO,CAACC,OAAO,CAACH,cAAc,CAAC,CAACI,IAAI,CAACP,IAAI,EAAErB,GAAG,CAAC;YACxD;UACF,CAAC,CAAC,OAAOkB,CAAC,EAAE;YACV,OAAOlB,GAAG,CAACkB,CAAU,CAAC;UACxB;QACF;QACA,IAAIpB,KAAK,KAAKK,KAAK,EAAE,MAAML,KAAK;MAClC;MAEA,SAASE,GAAGA,CAACkB,CAAQ,EAAwB;QAC3CpB,KAAK,GAAGA,KAAK,KAAKK,KAAK,GAAG,IAAIP,uBAAuB,CAACsB,CAAC,EAAEpB,KAAK,CAAC,GAAGoB,CAAC;QAEnE,OAAOG,IAAI,CAAC,CAAC;MACf;MAEA,OAAOA,IAAI,CAAC,CAAC;IACf;EACF,CAAC;AACH", "ignoreList": []}