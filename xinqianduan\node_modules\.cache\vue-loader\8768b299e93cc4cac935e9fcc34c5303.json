{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue?vue&type=style&index=0&id=40f2940c&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue", "mtime": 1748377686265}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucGFnZS10b3Agew0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KfQ0KDQoudGFibGUgew0KICB3aWR0aDogMTAwJTsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg=="}, {"version": 3, "sources": ["taocan.vue"], "names": [], "mappings": ";AA6TA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "taocan.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <el-row style=\"width: 600px\">\r\n        <el-input\r\n          placeholder=\"请输入内容\"\r\n          v-model=\"search.keyword\"\r\n          :size=\"allSize\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n        <el-button type=\"success\" @click=\"getData()\" :size=\"allSize\"\r\n          >刷新</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"tableData\"\r\n        class=\"table\"\r\n        v-loading=\"loading\"\r\n        :size=\"allSize\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"描述\"> </el-table-column>\r\n        <el-table-column prop=\"price\" label=\"价格\"> </el-table-column>\r\n        <el-table-column prop=\"year\" label=\"年份\"> </el-table-column>\r\n        <el-table-column prop=\"sort\" label=\"排序\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"创建日期\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      title=\"详情内容\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"套餐名称\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"套餐价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"price\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"年份\" :label-width=\"formLabelWidth\" prop=\"year\">\r\n          <el-input\r\n            v-model=\"ruleForm.year\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"套餐内容\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"good\"\r\n        >\r\n          <el-checkbox-group v-model=\"ruleForm.good\">\r\n            <el-row\r\n              v-for=\"(item, index) in types\"\r\n              style=\"display: flex\"\r\n              :key=\"index\"\r\n            >\r\n              <el-col :span=\"16\">\r\n                <el-checkbox :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                  size=\"mini\"\r\n                  label=\"描述文字\"\r\n                  v-if=\"item.is_num == 1\"\r\n                ></el-input-number>\r\n              </el-col>\r\n            </el-row>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"套餐描述\" :label-width=\"formLabelWidth\">\r\n          <el-input v-model=\"ruleForm.desc\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.sort\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年份\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"80px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      let good = this.ruleForm.good;\r\n      let types = [];\r\n      this.types.forEach((element) => {\r\n        for (let index = 0; index < good.length; index++) {\r\n          const id = good[index];\r\n          if (element.id == id) {\r\n            types.push(element);\r\n          }\r\n        }\r\n      });\r\n      this.ruleForm.num = types;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              _this.dialogFormVisible = false;\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.table {\r\n  width: 100%;\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}