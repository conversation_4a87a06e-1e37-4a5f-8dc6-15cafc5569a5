<?php
namespace app\admin\controller;
use think\Request;
use untils\JsonService;
use models\{Tiaojies,Tiaojiegengjings,Zhuanyes};

class Tiaojie
{
    protected $model;
    public function __construct(Tiaojies $model){
        //parent::__construct();
        $this->model=$model;
        
    }
    public function index(Request $request,$page=1,$size=20){   
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        if(!empty($search['cate_id'])){
            $where[]=['cate_id',"=",$search['cate_id']];
        }
        $res = $this->model::withAttr('status',function($v,$d){
           switch ($v) {
                case '2':
                    return '审核通过';
                    break;
                 case '3':
                    return '审核失败'."<br/>失败原因:".$d['fail_msg'];
                    break;
                default:
                    return '待处理';
                    break;
            }
        })->withAttr('is_success',function ($v,$d){
            switch ($v) {
                case '2':
                    return '调解成功';
                    break;
                 case '3':
                    return '调解失败'."<br/>失败原因:".$d['fail_msg'];
                    break;
                default:
                    if($d['status']==1){
                        return '待处理';
                    }else{
                        return '正在调解';
                    }
                    
                    break;
            }
        })
        ->withAttr('cate_id',function($v,$d){
            return Zhuanyes::where('id',$v)->value('title');
        })
        ->where($where)
        ->where(['is_delete'=>0])
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }
    

    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }
  public function getJilus(Request $request,Tiaojiegengjings $model,$page=1,$size=20){   
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        
		if(!empty($search['times'])){
		    $times = $search['times'];
			$where[]=['create_time','between',[strtotime($times[0]),strtotime($times[1])]];
		}
        $res = $model::withAttr('create_time',function($v,$d){
            return date('Y-m-d H:i:s',$v);
        })
        ->where($where)
        ->where(['tiaojie_id'=>$search['jilu_id']])
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $model->where($where)->where(['tiaojie_id'=>$search['jilu_id']])->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }
    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);
        if(empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功',$res);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->saveData(['id'=>$id,'is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
}
