{"version": 3, "sources": ["webpack:///./src/views/pages/yonghu/lawyer.vue", "webpack:///src/views/pages/yonghu/lawyer.vue", "webpack:///./src/views/pages/yonghu/lawyer.vue?13c0", "webpack:///./src/views/pages/yonghu/lawyer.vue?a490", "webpack:///./src/views/pages/yonghu/lawyer.vue?ea18"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "slot", "_v", "_s", "$router", "currentRoute", "name", "staticStyle", "on", "refulsh", "allSize", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "is_deal", "_l", "options1", "item", "key", "id", "title", "$event", "getData", "clearData", "directives", "rawName", "loading", "list", "scopedSlots", "_u", "fn", "scope", "editData", "row", "nativeOn", "preventDefault", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "type_title", "desc", "file_path", "changeFile", "handleSuccess", "delImage", "_e", "content", "saveData", "dialogVisible", "show_image", "staticRenderFns", "components", "EditorBar", "data", "page", "is_pay", "url", "info", "is_num", "required", "message", "trigger", "options", "mounted", "methods", "filed", "console", "log", "getInfo", "_this", "getRequest", "then", "resp", "code", "$message", "type", "msg", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "index", "splice", "go", "searchData", "postRequest", "count", "$refs", "validate", "valid", "val", "res", "success", "error", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "fileName", "component"], "mappings": "gJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,OAAS,WAAW,CAACF,EAAG,MAAM,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGP,KAAKQ,QAAQC,aAAaC,SAAST,EAAG,YAAY,CAACU,YAAY,CAAC,MAAQ,QAAQ,QAAU,SAASR,MAAM,CAAC,KAAO,QAAQS,GAAG,CAAC,MAAQb,EAAIc,UAAU,CAACd,EAAIO,GAAG,SAAS,GAAGL,EAAG,SAAS,CAACA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,gBAAgB,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOC,QAASC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,MAAM,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOM,QAASJ,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,mBAAmBvB,EAAIyB,GAAIzB,EAAI0B,UAAU,SAASC,GAAM,OAAOzB,EAAG,YAAY,CAAC0B,IAAID,EAAKE,GAAGzB,MAAM,CAAC,MAAQuB,EAAKG,MAAM,MAAQH,EAAKE,SAAQ,IAAI,GAAG3B,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,KAAOJ,EAAIe,SAASF,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAIgC,aAAa,CAAChC,EAAIO,GAAG,SAAS,GAAGL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,KAAOJ,EAAIe,SAASF,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAIiC,eAAe,CAACjC,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,WAAW,CAACgC,WAAW,CAAC,CAACvB,KAAK,UAAUwB,QAAQ,YAAYlB,MAAOjB,EAAIoC,QAASb,WAAW,YAAYX,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQR,MAAM,CAAC,KAAOJ,EAAIqC,KAAK,KAAO,SAAS,CAACnC,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,WAAW,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,MAAM,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,cAAc,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAMkC,YAAYtC,EAAIuC,GAAG,CAAC,CAACX,IAAI,UAAUY,GAAG,SAASC,GAAO,MAAO,CAACvC,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAI0C,SAASD,EAAME,IAAId,OAAO,CAAC7B,EAAIO,GAAG,UAAUL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASwC,SAAS,CAAC,MAAQ,SAASb,GAAgC,OAAxBA,EAAOc,iBAAwB7C,EAAI8C,QAAQL,EAAMM,OAAQN,EAAME,IAAId,OAAO,CAAC7B,EAAIO,GAAG,kBAAkB,GAAGL,EAAG,MAAM,CAACG,YAAY,YAAY,CAACH,EAAG,gBAAgB,CAACE,MAAM,CAAC,aAAa,CAAC,GAAI,IAAK,IAAK,IAAK,KAAK,YAAYJ,EAAIgD,KAAK,OAAS,0CAA0C,MAAQhD,EAAIiD,OAAOpC,GAAG,CAAC,cAAcb,EAAIkD,iBAAiB,iBAAiBlD,EAAImD,wBAAwB,IAAI,GAAGjD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAI8B,MAAQ,KAAK,QAAU9B,EAAIoD,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOvC,GAAG,CAAC,iBAAiB,SAASkB,GAAQ/B,EAAIoD,kBAAkBrB,KAAU,CAAC7B,EAAG,UAAU,CAACmD,IAAI,WAAWjD,MAAM,CAAC,MAAQJ,EAAIsD,SAAS,MAAQtD,EAAIuD,QAAQ,CAACrD,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAIwD,iBAAiB,CAACtD,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,SAAW,IAAIY,MAAM,CAACC,MAAOjB,EAAIsD,SAASG,WAAYrC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,aAAcjC,IAAME,WAAW,0BAA0B,GAAGrB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAIwD,iBAAiB,CAACtD,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,SAAW,IAAIY,MAAM,CAACC,MAAOjB,EAAIsD,SAASxB,MAAOV,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,QAASjC,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAIwD,iBAAiB,CAACtD,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,SAAW,GAAG,KAAO,WAAW,KAAO,GAAGY,MAAM,CAACC,MAAOjB,EAAIsD,SAASI,KAAMtC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,OAAQjC,IAAME,WAAW,oBAAoB,GAAGrB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAIwD,iBAAiB,CAACtD,EAAG,MAAM,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAIsD,SAAS9B,QAASJ,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,UAAWjC,IAAME,WAAW,qBAAqB,CAACvB,EAAIO,GAAG,SAASL,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAIsD,SAAS9B,QAASJ,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,UAAWjC,IAAME,WAAW,qBAAqB,CAACvB,EAAIO,GAAG,UAAU,KAA8B,GAAxBP,EAAIsD,SAAS9B,QAActB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,QAAQ,cAAcJ,EAAIwD,eAAe,KAAO,cAAc,CAACtD,EAAG,WAAW,CAACG,YAAY,WAAWD,MAAM,CAAC,UAAW,GAAMY,MAAM,CAACC,MAAOjB,EAAIsD,SAASK,UAAWvC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,YAAajC,IAAME,WAAW,wBAAwBrB,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAI4D,WAAW,gBAAgB,CAAC1D,EAAG,YAAY,CAACE,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAaJ,EAAI6D,gBAAgB,CAAC7D,EAAIO,GAAG,WAAW,GAAIP,EAAIsD,SAASK,UAAWzD,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAUS,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAI8D,SAAS9D,EAAIsD,SAASK,UAAW,gBAAgB,CAAC3D,EAAIO,GAAG,QAAQP,EAAI+D,MAAM,IAAI,GAAG/D,EAAI+D,KAA8B,GAAxB/D,EAAIsD,SAAS9B,QAActB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAIwD,iBAAiB,CAACtD,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAGY,MAAM,CAACC,MAAOjB,EAAIsD,SAASU,QAAS5C,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIsD,SAAU,UAAWjC,IAAME,WAAW,uBAAuB,GAAGvB,EAAI+D,MAAM,GAAG7D,EAAG,MAAM,CAACG,YAAY,gBAAgBD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASkB,GAAQ/B,EAAIoD,mBAAoB,KAAS,CAACpD,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWS,GAAG,CAAC,MAAQ,SAASkB,GAAQ,OAAO/B,EAAIiE,cAAc,CAACjE,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAIkE,cAAc,MAAQ,OAAOrD,GAAG,CAAC,iBAAiB,SAASkB,GAAQ/B,EAAIkE,cAAcnC,KAAU,CAAC7B,EAAG,WAAW,CAACE,MAAM,CAAC,IAAMJ,EAAImE,eAAe,IAAI,IAEzuLC,EAAkB,G,YCgLP,GACfzD,KAAA,OACA0D,WAAA,CAAAC,kBACAC,OACA,OACAxD,QAAA,OACAsB,KAAA,GACAY,MAAA,EACAuB,KAAA,EACAxB,KAAA,GACA9B,OAAA,CACAC,QAAA,GACAsD,QAAA,EACAjD,SAAA,GAEAY,SAAA,EACAsC,IAAA,WACA5C,MAAA,MACA6C,KAAA,GACAvB,mBAAA,EACAe,WAAA,GACAD,eAAA,EACAZ,SAAA,CACAxB,MAAA,GACA8C,OAAA,GAGArB,MAAA,CACAzB,MAAA,CACA,CACA+C,UAAA,EACAC,QAAA,QACAC,QAAA,SAGApB,UAAA,CACA,CACAkB,UAAA,EACAC,QAAA,QACAC,QAAA,UAIAvB,eAAA,QACAwB,QAAA,CACA,CACAnD,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAGAJ,SAAA,CACA,CACAG,IAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,OAEA,CACAD,GAAA,EACAC,MAAA,UAKAmD,UACA,KAAAjD,WAEAkD,QAAA,CACAtB,WAAAuB,GACA,KAAAA,QACAC,QAAAC,IAAA,KAAAF,QAEAlD,YACA,KAAAf,OAAA,CACAC,QAAA,GACAsD,OAAA,IAEA,KAAAzC,WAEAU,SAAAb,GAEA,GAAAA,EACA,KAAAyD,QAAAzD,GAEA,KAAAyB,SAAA,CACAxB,MAAA,GACA4B,KAAA,KAIA4B,QAAAzD,GACA,IAAA0D,EAAA,KACAA,EAAAC,WAAAD,EAAAb,IAAA,WAAA7C,GAAA4D,KAAAC,IACA,KAAAA,EAAAC,MACAJ,EAAAjC,SAAAoC,EAAAnB,KACAgB,EAAAnC,mBAAA,GAEAmC,EAAAK,SAAA,CACAC,KAAA,QACAf,QAAAY,EAAAI,SAKAC,QAAAlE,GACA,KAAAmE,SAAA,gBACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YAEAJ,KAAA,KACA,KAAAU,cAAA,KAAAzB,IAAA,cAAA7C,GAAA4D,KAAAC,IACA,KAAAA,EAAAC,KACA,KAAAC,SAAA,CACAC,KAAA,UACAf,QAAAY,EAAAI,MAGA,KAAAF,SAAA,CACAC,KAAA,QACAf,QAAAY,EAAAI,UAKAM,MAAA,KACA,KAAAR,SAAA,CACAC,KAAA,QACAf,QAAA,aAIAhC,QAAAuD,EAAAxE,GACA,KAAAmE,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAL,KAAA,YAEAJ,KAAA,KACA,KAAAU,cAAA,KAAAzB,IAAA,aAAA7C,GAAA4D,KAAAC,IACA,KAAAA,EAAAC,OACA,KAAAC,SAAA,CACAC,KAAA,UACAf,QAAA,UAEA,KAAAzC,KAAAiE,OAAAD,EAAA,QAIAD,MAAA,KACA,KAAAR,SAAA,CACAC,KAAA,QACAf,QAAA,aAIAhE,UACA,KAAAL,QAAA8F,GAAA,IAEAC,aACA,KAAAhC,KAAA,EACA,KAAAxB,KAAA,GACA,KAAAhB,WAGAA,UACA,IAAAuD,EAAA,KAEAA,EAAAnD,SAAA,EACAmD,EACAkB,YACAlB,EAAAb,IAAA,cAAAa,EAAAf,KAAA,SAAAe,EAAAvC,KACAuC,EAAArE,QAEAuE,KAAAC,IACA,KAAAA,EAAAC,OACAJ,EAAAlD,KAAAqD,EAAAnB,KACAgB,EAAAtC,MAAAyC,EAAAgB,OAEAnB,EAAAnD,SAAA,KAGA6B,WACA,IAAAsB,EAAA,KACA,KAAAoB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAJ,YAAAlB,EAAAb,IAAA,YAAApB,UAAAmC,KAAAC,IACA,KAAAA,EAAAC,MACAJ,EAAAK,SAAA,CACAC,KAAA,UACAf,QAAAY,EAAAI,MAEA,KAAA9D,UACAuD,EAAAnC,mBAAA,GAEAmC,EAAAK,SAAA,CACAC,KAAA,QACAf,QAAAY,EAAAI,WASA5C,iBAAA4D,GACA,KAAA9D,KAAA8D,EAEA,KAAA9E,WAEAmB,oBAAA2D,GACA,KAAAtC,KAAAsC,EACA,KAAA9E,WAEA6B,cAAAkD,GACA,KAAAA,EAAApB,MACA,KAAAC,SAAAoB,QAAA,QACA,KAAA1D,SAAA,KAAA6B,OAAA4B,EAAAxC,KAAAG,KAEA,KAAAkB,SAAAqB,MAAAF,EAAAjB,MAIAoB,UAAAC,GACA,KAAAhD,WAAAgD,EACA,KAAAjD,eAAA,GAEAkD,aAAAD,GACA,MAAAE,EAAA,0BAAAC,KAAAH,EAAAtB,MACAwB,GACA,KAAAzB,SAAAqB,MAAA,cAIAnD,SAAAqD,EAAAI,GACA,IAAAhC,EAAA,KACAA,EAAAC,WAAA,6BAAA2B,GAAA1B,KAAAC,IACA,KAAAA,EAAAC,MACAJ,EAAAjC,SAAAiE,GAAA,GAEAhC,EAAAK,SAAAoB,QAAA,UAEAzB,EAAAK,SAAAqB,MAAAvB,EAAAI,UCvb6W,I,wBCQzW0B,EAAY,eACd,EACAzH,EACAqE,GACA,EACA,KACA,WACA,MAIa,aAAAoD,E,2CCnBf", "file": "js/chunk-6899bb10.7bfcdead.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-card',{attrs:{\"shadow\":\"always\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(_vm._s(this.$router.currentRoute.name))]),_c('el-button',{staticStyle:{\"float\":\"right\",\"padding\":\"3px 0\"},attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_vm._v(\"刷新\")])],1),_c('el-row',[_c('el-col',{attrs:{\"span\":4}},[_c('el-input',{attrs:{\"placeholder\":\"请输入订单号/购买人/套餐\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-col',{attrs:{\"span\":3}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"size\":_vm.allSize},model:{value:(_vm.search.is_deal),callback:function ($$v) {_vm.$set(_vm.search, \"is_deal\", $$v)},expression:\"search.is_deal\"}},_vm._l((_vm.options1),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.title,\"value\":item.id}})}),1)],1),_c('el-col',{attrs:{\"span\":1}},[_c('el-button',{attrs:{\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\"搜索\")])],1),_c('el-col',{attrs:{\"span\":1}},[_c('el-button',{attrs:{\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.clearData()}}},[_vm._v(\"重置\")])],1)],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\",\"margin-top\":\"10px\"},attrs:{\"data\":_vm.list,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"order_sn\",\"label\":\"工单号\"}}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"工单类型\"}}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"工单标题\"}}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"工单内容\"}}),_c('el-table-column',{attrs:{\"prop\":\"is_deal\",\"label\":\"处理状态\"}}),_c('el-table-column',{attrs:{\"prop\":\"uid\",\"label\":\"用户手机\"}}),_c('el-table-column',{attrs:{\"prop\":\"dt_name\",\"label\":\"债务人\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"发起时间\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"完成制作\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 取消 \")])]}}])})],1),_c('div',{staticClass:\"page-top\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 100, 200, 300, 400],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"工单类型\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.type_title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"type_title\", $$v)},expression:\"ruleForm.type_title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单标题\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单描述\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"制作状态\",\"label-width\":_vm.formLabelWidth}},[_c('div',[_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"已完成\")]),_c('el-radio',{attrs:{\"label\":1},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"处理中\")])],1)]),(_vm.ruleForm.is_deal == 2)?_c('el-form-item',{attrs:{\"label\":\"请上传文件\",\"label-width\":_vm.formLabelWidth,\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',{on:{\"click\":function($event){return _vm.changeFile('file_path')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1):_vm._e(),(_vm.ruleForm.is_deal == 2)?_c('el-form-item',{attrs:{\"label\":\"内容回复\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\"> </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\"> </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\"> </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\"> </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\"> </el-table-column>\r\n        <el-table-column prop=\"uid\" label=\"用户手机\"> </el-table-column>\r\n        <el-table-column prop=\"dt_name\" label=\"债务人\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              取消\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./lawyer.vue?vue&type=template&id=05016c62&scoped=true\"\nimport script from \"./lawyer.vue?vue&type=script&lang=js\"\nexport * from \"./lawyer.vue?vue&type=script&lang=js\"\nimport style0 from \"./lawyer.vue?vue&type=style&index=0&id=05016c62&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"05016c62\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./lawyer.vue?vue&type=style&index=0&id=05016c62&prod&scoped=true&lang=css\""], "sourceRoot": ""}