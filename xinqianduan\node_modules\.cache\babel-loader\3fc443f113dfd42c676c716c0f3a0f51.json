{"remainingRequest": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\data\\configs.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\data\\configs.vue", "mtime": 1748325004944}, {"path": "H:\\fdbfront\\babel.config.js", "mtime": 1732626900032}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditorBar", "name", "components", "data", "ruleForm", "site_name", "company_name", "site_tel", "email", "site_address", "site_icp", "site_icp_url", "site_logo", "lvshi", "my_title", "my_desc", "about_path", "yinsi", "index_about_content", "index_team_content", "activeName", "url", "fullscreenLoading", "show_image", "dialogVisible", "filedName", "isClear", "id", "title", "mounted", "console", "log", "methods", "getList", "changeFiled", "fileName", "change", "getAllData", "handleSuccess", "res", "$message", "success", "beforeUpload", "file", "isTypeTrue", "test", "type", "error", "info", "delImage", "showImage", "handleClick", "saveData", "_this", "setTimeout", "message"], "sources": ["src/views/pages/data/configs.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-title\">\r\n        基础设置\r\n      </div>\r\n\r\n      <!-- 标签页导航 -->\r\n      <div class=\"tab-container\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <div class=\"form-container\">\r\n              <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\">\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"网站名称\">\r\n                      <el-input v-model=\"ruleForm.site_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\">\r\n                      <el-input v-model=\"ruleForm.company_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系方式\">\r\n                      <el-input v-model=\"ruleForm.site_tel\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"邮箱\">\r\n                      <el-input v-model=\"ruleForm.email\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"ruleForm.site_address\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案号\">\r\n                      <el-input v-model=\"ruleForm.site_icp\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案链接\">\r\n                      <el-input v-model=\"ruleForm.site_icp_url\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-form-item label=\"网站Logo\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.site_logo\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传Logo图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('site_logo')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"showImage(ruleForm.site_logo)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"推广律师\">\r\n                  <el-select\r\n                    v-model=\"ruleForm.lvshi\"\r\n                    placeholder=\"请选择推广律师\"\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                      v-for=\"(item, index) in lvshi\"\r\n                      :key=\"index\"\r\n                      :label=\"item.title\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广标题\">\r\n                      <el-input v-model=\"ruleForm.my_title\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广语\">\r\n                      <el-input v-model=\"ruleForm.my_desc\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"推广图片\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.about_path\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传推广图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('about_path')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"showImage(ruleForm.about_path)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"隐私条款内容\">\r\n                <el-input\r\n                  v-model=\"ruleForm.yinsi\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"12\"\r\n                  placeholder=\"请输入隐私条款内容\"\r\n                ></el-input>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"关于我们内容\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_about_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"change\"\r\n                ></editor-bar>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <div class=\"form-container\">\r\n              <el-form-item label=\"团队介绍内容\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_team_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"change\"\r\n                ></editor-bar>\r\n              </el-form-item>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n\r\n      <!-- 提交按钮 -->\r\n      <div class=\"submit-container\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"medium\"\r\n          @click=\"saveData\"\r\n          :loading=\"fullscreenLoading\"\r\n          >保存设置\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: \"这是隐私条款的演示内容...\",\r\n        index_about_content: \"<p>这是关于我们的演示内容...</p>\",\r\n        index_team_content: \"<p>这是团队介绍的演示内容...</p>\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style>\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.el_input {\r\n  width: 600px;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 120px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n}\r\n\r\n.avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: block;\r\n}\r\n</style>\r\n"], "mappings": "AA6NA;AACA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,QAAA;QACAC,SAAA;QACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,QAAA;QACAC,YAAA;QACAC,SAAA;QACAC,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,UAAA;QACAC,KAAA;QACAC,mBAAA;QACAC,kBAAA;MACA;MACAC,UAAA;MACAC,GAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,SAAA;MACAC,OAAA;MACAb,KAAA,GACA;QAAAc,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACAC,QAAA;MACA;MACAH,OAAA,CAAAC,GAAA;IACA;IACAG,YAAAC,QAAA;MACA,KAAAV,SAAA,GAAAU,QAAA;IACA;IACAC,OAAA;IACAC,WAAA;MACA;MACAP,OAAA,CAAAC,GAAA;IACA;IACAO,cAAAC,GAAA;MACA;MACA,KAAAnC,QAAA,MAAAqB,SAAA;MACA,KAAAe,QAAA,CAAAC,OAAA;IACA;IAEAC,aAAAC,IAAA;MACA,MAAAC,UAAA,6BAAAC,IAAA,CAAAF,IAAA,CAAAG,IAAA;MACA,KAAAF,UAAA;QACA,KAAAJ,QAAA,CAAAO,KAAA;QACA;MACA;MACA;MACA,KAAAP,QAAA,CAAAQ,IAAA;MACA;IACA;IACAC,SAAAN,IAAA,EAAAR,QAAA;MACA;MACA,KAAA/B,QAAA,CAAA+B,QAAA;MACA,KAAAK,QAAA,CAAAC,OAAA;IACA;IACAS,UAAAP,IAAA;MACA,KAAApB,UAAA,GAAAoB,IAAA;MACA,KAAAnB,aAAA;IACA;IACA2B,YAAA;IACAC,SAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAA/B,iBAAA;;MAEA;MACAgC,UAAA;QACAD,KAAA,CAAAb,QAAA;UACAM,IAAA;UACAS,OAAA;QACA;QACAF,KAAA,CAAA/B,iBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}