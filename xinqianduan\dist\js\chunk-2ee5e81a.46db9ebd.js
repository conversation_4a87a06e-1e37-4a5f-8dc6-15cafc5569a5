(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ee5e81a"],{"0696":function(e,t,i){"use strict";i("7c01")},"41ad":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"contract-list-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-right"},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增合同 ")]),t("el-button",{staticClass:"refresh-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新 ")])],1)])]),t("div",{staticClass:"search-section"},[t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("div",{staticClass:"search-content"},[t("div",{staticClass:"search-left"},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"搜索合同标题、类型...",clearable:""},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{staticClass:"search-right"},[t("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.searchData()}}},[e._v(" 搜索 ")]),t("el-button",{staticClass:"clear-btn",attrs:{icon:"el-icon-refresh-left"},on:{click:function(t){return e.clearSearch()}}},[e._v(" 重置 ")])],1)])])],1),t("div",{staticClass:"table-section"},[t("el-card",{staticClass:"table-card",attrs:{shadow:"never"}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"contract-table",attrs:{data:e.list,stripe:"",border:""}},[t("el-table-column",{attrs:{prop:"title",label:"文书标题","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"title-cell"},[t("i",{staticClass:"el-icon-document-copy"}),t("span",{staticClass:"title-text"},[e._v(e._s(i.row.title))])])]}}])}),t("el-table-column",{attrs:{prop:"cate_id",label:"文书类型",width:"150"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(" "+e._s(e.getCategoryName(i.row.cate_id))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"price",label:"价格",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",{staticClass:"price-text"},[t("i",{staticClass:"el-icon-money"}),e._v(" ¥"+e._s(i.row.price||"0.00")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"file_path",label:"文件状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-tag",{attrs:{type:i.row.file_path?"success":"warning",size:"small"}},[e._v(" "+e._s(i.row.file_path?"已上传":"未上传")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"录入时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"time-cell"},[t("i",{staticClass:"el-icon-time"}),t("span",[e._v(e._s(i.row.create_time))])])]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"action-buttons"},[t("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"mini",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.editData(i.row.id)}}},[e._v(" 编辑 ")]),i.row.file_path?t("el-button",{staticClass:"action-btn",attrs:{type:"success",size:"mini",icon:"el-icon-view",plain:""},on:{click:function(t){return e.previewContract(i.row)}}},[e._v(" 预览 ")]):e._e(),t("el-button",{staticClass:"action-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-delete",plain:""},on:{click:function(t){return e.delData(i.$index,i.row.id)}}},[e._v(" 删除 ")])],1)]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1),t("el-dialog",{staticClass:"form-dialog",attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t},close:e.handleDialogClose}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"文书类型","label-width":e.formLabelWidth,prop:"cate_id"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.ruleForm.cate_id,callback:function(t){e.$set(e.ruleForm,"cate_id",t)},expression:"ruleForm.cate_id"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._l(e.cates,(function(e,i){return t("el-option",{key:i,attrs:{label:e.title,value:e.id}})}))],2)],1),t("el-form-item",{attrs:{label:e.title+"标题","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"文件上传","label-width":e.formLabelWidth,prop:"file_path"}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.file_path,callback:function(t){e.$set(e.ruleForm,"file_path",t)},expression:"ruleForm.file_path"}}),t("el-button-group",[t("el-button",{on:{click:function(t){return e.changefield("file_path")}}},[t("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":e.handleSuccess}},[e._v(" 上传 ")])],1),e.ruleForm.file_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.file_path,"file_path")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"价格","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"number"},model:{value:e.ruleForm.price,callback:function(t){e.$set(e.ruleForm,"price",t)},expression:"ruleForm.price"}})],1),t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("editor-bar",{attrs:{isClear:e.isClear},on:{change:e.change},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,"content",t)},expression:"ruleForm.content"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.cancelDialog}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{staticClass:"preview-dialog",attrs:{title:"文书预览",visible:e.dialogPreview,"close-on-click-modal":!1,width:"80%"},on:{"update:visible":function(t){e.dialogPreview=t}}},[t("div",{staticClass:"preview-content"},[t("div",{staticClass:"preview-header"},[t("h3",[e._v(e._s(e.previewData.title))]),t("div",{staticClass:"preview-meta"},[t("span",{staticClass:"meta-item"},[t("i",{staticClass:"el-icon-folder"}),e._v(" 类型："+e._s(e.getCategoryName(e.previewData.cate_id))+" ")]),t("span",{staticClass:"meta-item"},[t("i",{staticClass:"el-icon-money"}),e._v(" 价格：¥"+e._s(e.previewData.price||"0.00")+" ")])])]),t("div",{staticClass:"preview-body"},[e.previewData.file_path?t("div",{staticClass:"file-preview"},[t("div",{staticClass:"file-info"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v(e._s(e.previewData.file_path.split("/").pop()))]),t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(t){return e.downloadFile(e.previewData.file_path)}}},[e._v(" 下载 ")])],1)]):e._e(),e.previewData.content?t("div",{staticClass:"content-preview"},[t("h4",[e._v("文书内容：")]),t("div",{staticClass:"content-html",domProps:{innerHTML:e._s(e.previewData.content)}})]):e._e()])])]),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"header-left"},[t("h2",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-document"}),e._v(" 合同列表管理 ")]),t("p",{staticClass:"page-subtitle"},[e._v("管理系统中的所有合同模板和文书")])])}],s=i("0c98"),o={name:"list",components:{EditorBar:s["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/wenshu/",field:"",title:"文书",info:{},dialogFormVisible:!1,dialogPreview:!1,previewData:{},show_image:"",dialogVisible:!1,isClear:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],cate_id:[{required:!0,message:"请选择文书类型",trigger:"blur"}],file_path:[{required:!0,message:"请上传文件",trigger:"blur"}]},formLabelWidth:"120px",cates:[],expireTimeOption:{disabledDate(e){return e.getTime()<Date.now()-864e5}}}},mounted(){console.log("页面挂载完成，开始加载数据..."),this.getData(),this.getLvshi(),document.addEventListener("keydown",this.handleKeyDown),this.$nextTick(()=>{console.log("页面渲染完成"),console.log("当前list数据:",this.list),console.log("当前loading状态:",this.loading)})},beforeDestroy(){document.removeEventListener("keydown",this.handleKeyDown)},watch:{dialogFormVisible(e,t){console.log("对话框可见性变化:",e,t),!e&&t&&this.handleDialogClose()}},methods:{change(){},changefield(e){this.field=e},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""},t.getLvshi(),t.dialogFormVisible=!0},getLvshi(){const e=[{id:1,title:"民事诉讼",desc:"民事纠纷相关文书"},{id:2,title:"商事诉讼",desc:"商业纠纷相关文书"},{id:3,title:"侵权诉讼",desc:"侵权纠纷相关文书"},{id:4,title:"婚姻家庭",desc:"婚姻家庭纠纷文书"},{id:5,title:"知识产权",desc:"知识产权纠纷文书"},{id:6,title:"劳动争议",desc:"劳动关系纠纷文书"},{id:7,title:"行政诉讼",desc:"行政纠纷相关文书"},{id:8,title:"刑事辩护",desc:"刑事案件相关文书"}];setTimeout(()=>{this.cates=e,console.log("加载测试分类数据:",this.cates)},50)},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},clearSearch(){this.search.keyword="",this.searchData()},getCategoryName(e){if(!Array.isArray(this.cates))return console.warn("cates is not an array:",this.cates),"未分类";const t=this.cates.find(t=>t.id===e);return t?t.title:"未分类"},previewContract(e){console.log("预览文书:",e),this.previewData=e,this.dialogPreview=!0},downloadFile(e){const t=document.createElement("a");t.href=e,t.download=e.split("/").pop(),document.body.appendChild(t),t.click(),document.body.removeChild(t),this.$message.success("开始下载文件")},handleDialogClose(){console.log("对话框关闭事件触发"),this.$refs.ruleForm&&this.$refs.ruleForm.resetFields(),this.ruleForm={title:"",is_num:0,cate_id:"",file_path:"",price:"",content:""},this.isClear=!0},cancelDialog(){console.log("取消按钮点击"),this.$refs.ruleForm&&this.$refs.ruleForm.resetFields(),this.ruleForm={title:"",is_num:0,cate_id:"",file_path:"",price:"",content:""},this.isClear=!0,this.dialogFormVisible=!1},handleKeyDown(e){27===e.keyCode&&this.dialogFormVisible&&this.cancelDialog()},getData(){let e=this;e.loading=!0;const t=[{id:1,title:"民事起诉状模板",cate_id:1,file_path:"/uploads/documents/civil_complaint_template.docx",price:"500.00",content:"<p>这是一份标准的民事起诉状模板，适用于一般民事纠纷案件。包含完整的格式要求和必要条款。</p><p>主要内容包括：</p><ul><li>当事人基本信息</li><li>诉讼请求</li><li>事实与理由</li><li>证据清单</li></ul>",create_time:"2024-01-15 10:30:00",update_time:"2024-01-15 10:30:00"},{id:2,title:"劳动合同纠纷起诉书",cate_id:2,file_path:"/uploads/documents/labor_dispute_complaint.pdf",price:"800.00",content:"<p>专门针对劳动合同纠纷的起诉书模板，涵盖工资拖欠、违法解除等常见情形。</p><p>适用范围：</p><ul><li>工资拖欠纠纷</li><li>违法解除劳动合同</li><li>加班费争议</li><li>经济补偿金纠纷</li></ul>",create_time:"2024-01-16 14:20:00",update_time:"2024-01-16 14:20:00"},{id:3,title:"房屋买卖合同纠纷诉状",cate_id:1,file_path:"/uploads/documents/property_sale_dispute.docx",price:"1200.00",content:"<p>房屋买卖合同纠纷专用诉讼文书，包含房产交易中的各种争议处理。</p><p>涵盖问题：</p><ul><li>房屋质量问题</li><li>逾期交房</li><li>产权过户纠纷</li><li>定金违约</li></ul>",create_time:"2024-01-17 09:15:00",update_time:"2024-01-17 09:15:00"},{id:4,title:"交通事故赔偿起诉书",cate_id:3,file_path:"/uploads/documents/traffic_accident_claim.pdf",price:"600.00",content:"<p>交通事故人身损害赔偿起诉书模板，适用于各类交通事故赔偿案件。</p><p>赔偿项目：</p><ul><li>医疗费</li><li>误工费</li><li>护理费</li><li>精神损害抚慰金</li></ul>",create_time:"2024-01-18 16:45:00",update_time:"2024-01-18 16:45:00"},{id:5,title:"借款合同纠纷起诉状",cate_id:2,file_path:"/uploads/documents/loan_dispute_complaint.docx",price:"400.00",content:"<p>民间借贷纠纷起诉状模板，适用于个人借款、企业借贷等各类借款纠纷。</p><p>主要条款：</p><ul><li>借款本金确认</li><li>利息计算标准</li><li>违约责任</li><li>担保责任</li></ul>",create_time:"2024-01-19 11:30:00",update_time:"2024-01-19 11:30:00"},{id:6,title:"离婚纠纷起诉书",cate_id:4,file_path:"/uploads/documents/divorce_complaint.pdf",price:"900.00",content:"<p>离婚纠纷起诉书模板，包含财产分割、子女抚养等完整内容。</p><p>主要内容：</p><ul><li>夫妻感情破裂事实</li><li>财产分割方案</li><li>子女抚养安排</li><li>债务承担</li></ul>",create_time:"2024-01-20 13:20:00",update_time:"2024-01-20 13:20:00"},{id:7,title:"知识产权侵权起诉状",cate_id:5,file_path:"/uploads/documents/ip_infringement_complaint.docx",price:"1500.00",content:"<p>知识产权侵权起诉状模板，适用于商标、专利、著作权等侵权案件。</p><p>保护范围：</p><ul><li>商标权侵权</li><li>专利权侵权</li><li>著作权侵权</li><li>商业秘密侵权</li></ul>",create_time:"2024-01-21 15:10:00",update_time:"2024-01-21 15:10:00"},{id:8,title:"公司股权纠纷起诉书",cate_id:2,file_path:"/uploads/documents/equity_dispute_complaint.pdf",price:"2000.00",content:"<p>公司股权纠纷起诉书模板，处理股东权益、公司治理等复杂商事纠纷。</p><p>争议类型：</p><ul><li>股权转让纠纷</li><li>股东知情权</li><li>利润分配争议</li><li>公司决议效力</li></ul>",create_time:"2024-01-22 10:00:00",update_time:"2024-01-22 10:00:00"}];setTimeout(()=>{try{console.log("开始加载测试数据..."),console.log("原始测试数据:",t);let i=t;if(e.search.keyword&&e.search.keyword.trim()){const l=e.search.keyword.trim().toLowerCase();i=t.filter(e=>e.title.toLowerCase().includes(l)||e.content.toLowerCase().includes(l)),console.log("搜索关键词:",l),console.log("搜索结果:",i)}const l=(e.page-1)*e.size,a=l+e.size,s=i.slice(l,a);e.list=s,e.total=i.length,e.loading=!1,console.log("当前页:",e.page),console.log("每页大小:",e.size),console.log("分页后的数据:",s),console.log("设置到list的数据:",e.list),console.log("总数:",e.total),console.log("加载状态:",e.loading)}catch(i){console.error("加载测试数据出错:",i),e.list=[],e.total=0,e.loading=!1}},100)},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){200==e.code?(this.$message.success("上传成功"),this.ruleForm[this.field]=e.data.url):this.$message.error(e.msg)},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){if("pic_path"==this.filed){const e=/^image\/(jpeg|png|jpg)$/.test(type);if(!e)return void this.$message.error("上传图片格式不对!")}},delImage(e,t){let i=this;i.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(i.ruleForm[t]="",i.$message.success("删除成功!")):i.$message.error(e.msg)})}}},r=o,c=(i("0696"),i("2877")),n=Object(c["a"])(r,l,a,!1,null,"7482c4f7",null);t["default"]=n.exports},"7c01":function(e,t,i){}}]);
//# sourceMappingURL=chunk-2ee5e81a.46db9ebd.js.map