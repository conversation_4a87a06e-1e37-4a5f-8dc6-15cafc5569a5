(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f4caf1e"],{"4a94":function(e,t,a){},c798:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"position-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增职位 ")]),t("el-button",{staticClass:"refresh-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新 ")])],1)])]),t("div",{staticClass:"search-section"},[t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("div",{staticClass:"search-form"},[t("div",{staticClass:"search-row"},[t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("职位搜索")]),t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入职位名称或描述",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("权限级别")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择权限级别",clearable:""},model:{value:e.search.permission_level,callback:function(t){e.$set(e.search,"permission_level",t)},expression:"search.permission_level"}},[t("el-option",{attrs:{label:"超级管理员",value:"super"}}),t("el-option",{attrs:{label:"管理员",value:"admin"}}),t("el-option",{attrs:{label:"普通用户",value:"user"}})],1)],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("状态")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},[t("el-option",{attrs:{label:"启用",value:1}}),t("el-option",{attrs:{label:"禁用",value:0}})],1)],1)]),t("div",{staticClass:"search-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchData}},[e._v(" 搜索 ")]),t("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:e.clearSearch}},[e._v(" 重置 ")])],1)])])],1),t("div",{staticClass:"stats-section"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon total"},[t("i",{staticClass:"el-icon-postcard"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.total))]),t("div",{staticClass:"stat-label"},[e._v("总职位数")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon admin"},[t("i",{staticClass:"el-icon-user-solid"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.adminCount))]),t("div",{staticClass:"stat-label"},[e._v("管理职位")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon user"},[t("i",{staticClass:"el-icon-user"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.userCount))]),t("div",{staticClass:"stat-label"},[e._v("普通职位")])])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon active"},[t("i",{staticClass:"el-icon-circle-check"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.activeCount))]),t("div",{staticClass:"stat-label"},[e._v("启用职位")])])])])],1)],1),t("div",{staticClass:"table-section"},[t("el-card",{staticClass:"table-card",attrs:{shadow:"never"}},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[t("i",{staticClass:"el-icon-menu"}),e._v(" 职位列表 ")]),t("div",{staticClass:"table-tools"},[t("el-button-group",[t("el-button",{attrs:{type:"table"===e.viewMode?"primary":"",icon:"el-icon-menu",size:"small"},on:{click:function(t){e.viewMode="table"}}},[e._v(" 列表视图 ")]),t("el-button",{attrs:{type:"card"===e.viewMode?"primary":"",icon:"el-icon-s-grid",size:"small"},on:{click:function(t){e.viewMode="card"}}},[e._v(" 卡片视图 ")])],1)],1)]),"table"===e.viewMode?t("div",{staticClass:"table-view"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"position-table",attrs:{data:e.list,stripe:""}},[t("el-table-column",{attrs:{prop:"title",label:"职位名称","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"position-title-cell"},[t("div",{staticClass:"position-title"},[e._v(e._s(a.row.title))]),a.row.level?t("div",{staticClass:"position-level"},[e._v(e._s(a.row.level))]):e._e()])]}}],null,!1,414999880)}),t("el-table-column",{attrs:{prop:"desc",label:"职位描述","min-width":"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"权限配置",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"permission-tags"},e._l(e.getPermissionLabels(a.row.quanxian),(function(a,s){return t("el-tag",{key:s,staticStyle:{margin:"2px"},attrs:{size:"mini",type:e.getPermissionTagType(a)}},[e._v(" "+e._s(a)+" ")])})),1)]}}],null,!1,110400083)}),t("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(t){return e.changeStatus(a.row)}},model:{value:a.row.status,callback:function(t){e.$set(a.row,"status",t)},expression:"scope.row.status"}})]}}],null,!1,2880962836)}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"time-cell"},[t("i",{staticClass:"el-icon-time"}),e._v(" "+e._s(a.row.create_time)+" ")])]}}],null,!1,3001843918)}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"action-buttons"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.editData(a.row.id)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",plain:""},on:{click:function(t){return e.delData(a.$index,a.row.id)}}},[e._v(" 删除 ")])],1)]}}],null,!1,2809669383)})],1)],1):e._e(),"card"===e.viewMode?t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-view"},[t("el-row",{attrs:{gutter:20}},e._l(e.list,(function(a){return t("el-col",{key:a.id,staticClass:"position-card-col",attrs:{span:8}},[t("div",{staticClass:"position-card"},[t("div",{staticClass:"card-header"},[t("div",{staticClass:"card-title"},[t("i",{staticClass:"el-icon-postcard"}),e._v(" "+e._s(a.title)+" ")]),t("div",{staticClass:"card-status"},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"small"},on:{change:function(t){return e.changeStatus(a)}},model:{value:a.status,callback:function(t){e.$set(a,"status",t)},expression:"position.status"}})],1)]),t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-desc"},[e._v(e._s(a.desc))]),t("div",{staticClass:"card-permissions"},[t("div",{staticClass:"permission-title"},[e._v("权限配置：")]),t("div",{staticClass:"permission-tags"},e._l(e.getPermissionLabels(a.quanxian),(function(a,s){return t("el-tag",{key:s,staticStyle:{margin:"2px"},attrs:{size:"mini",type:e.getPermissionTagType(a)}},[e._v(" "+e._s(a)+" ")])})),1)]),t("div",{staticClass:"card-footer"},[t("div",{staticClass:"card-time"},[t("i",{staticClass:"el-icon-time"}),e._v(" "+e._s(a.create_time)+" ")])])]),t("div",{staticClass:"card-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.editData(a.id)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete",plain:""},on:{click:function(t){e.delData(e.list.indexOf(a),a.id)}}},[e._v(" 删除 ")])],1)])])})),1)],1):e._e(),t("div",{staticClass:"pagination-container"},[t("el-pagination",{staticClass:"pagination",attrs:{"page-sizes":[12,24,48,96],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)])],1),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:e.title+"标题","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"权限","label-width":e.formLabelWidth}},[t("el-cascader",{attrs:{options:e.options,props:e.props,clearable:""},model:{value:e.ruleForm.quanxian,callback:function(t){e.$set(e.ruleForm,"quanxian",t)},expression:"ruleForm.quanxian"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},l=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"title-section"},[t("h2",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-postcard"}),e._v(" 职位管理 ")]),t("p",{staticClass:"page-subtitle"},[e._v("管理系统职位信息和权限配置")])])}],i={name:"list",components:{},data(){return{viewMode:"table",props:{multiple:!0},options:{},allSize:"mini",list:[],total:5,page:1,size:12,search:{keyword:"",permission_level:"",status:""},loading:!0,url:"/zhiwei/",title:"职位",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",desc:"",quanxian:[],status:1},rules:{title:[{required:!0,message:"请填写职位名称",trigger:"blur"}],desc:[{required:!0,message:"请填写职位描述",trigger:"blur"}]},formLabelWidth:"120px"}},computed:{adminCount(){return this.list.filter(e=>e.quanxian&&(e.quanxian.includes(1)||e.quanxian.includes(13))).length},userCount(){return this.list.filter(e=>e.quanxian&&!e.quanxian.includes(1)&&!e.quanxian.includes(13)).length},activeCount(){return this.list.filter(e=>1===e.status).length}},mounted(){this.getData()},methods:{getPermissionLabels(e){if(!e||!Array.isArray(e))return[];const t={1:"系统管理",2:"业务管理",3:"文书管理",4:"财务管理",11:"用户管理",12:"角色管理",13:"权限管理",21:"订单管理",22:"客户管理",31:"合同管理",32:"律师函管理",33:"课程管理",41:"支付管理"};return e.map(e=>t[e]||"权限"+e).filter(Boolean)},getPermissionTagType(e){return e.includes("系统")||e.includes("权限")?"danger":e.includes("业务")||e.includes("订单")?"primary":e.includes("文书")||e.includes("合同")?"success":e.includes("财务")||e.includes("支付")?"warning":"info"},changeStatus(e){this.$message.success(`职位"${e.title}"状态已${e.status?"启用":"禁用"}`)},clearSearch(){this.search={keyword:"",permission_level:"",status:""},this.searchData()},change(){},editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:""},t.dialogFormVisible=!0,t.getQuanxians()},getQuanxians(){this.getPermissionsFromManagement()},getPermissionsFromManagement(){const e=[{value:1,label:"系统管理",children:[{value:11,label:"用户管理",children:[{value:111,label:"查看用户"},{value:112,label:"新增用户"},{value:113,label:"编辑用户"},{value:114,label:"删除用户"}]},{value:12,label:"角色管理",children:[{value:121,label:"查看角色"},{value:122,label:"新增角色"},{value:123,label:"编辑角色"},{value:124,label:"删除角色"}]},{value:13,label:"权限管理",children:[{value:131,label:"查看权限"},{value:132,label:"新增权限"},{value:133,label:"编辑权限"},{value:134,label:"删除权限"}]}]},{value:2,label:"业务管理",children:[{value:21,label:"订单管理",children:[{value:211,label:"查看订单"},{value:212,label:"新增订单"},{value:213,label:"编辑订单"},{value:214,label:"删除订单"},{value:215,label:"导出订单"}]},{value:22,label:"客户管理",children:[{value:221,label:"查看客户"},{value:222,label:"新增客户"},{value:223,label:"编辑客户"},{value:224,label:"删除客户"}]}]},{value:3,label:"文书管理",children:[{value:31,label:"合同管理",children:[{value:311,label:"查看合同"},{value:312,label:"新增合同"},{value:313,label:"编辑合同"},{value:314,label:"删除合同"},{value:315,label:"审核合同"}]},{value:32,label:"律师函管理",children:[{value:321,label:"查看律师函"},{value:322,label:"发送律师函"},{value:323,label:"编辑律师函"}]},{value:33,label:"课程管理",children:[{value:331,label:"查看课程"},{value:332,label:"新增课程"},{value:333,label:"编辑课程"},{value:334,label:"删除课程"}]}]},{value:4,label:"财务管理",children:[{value:41,label:"支付管理",children:[{value:411,label:"查看支付记录"},{value:412,label:"处理退款"},{value:413,label:"导出财务报表"}]}]}];this.options=e},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code?(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1)):this.$message({type:"error",message:t.msg})})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,setTimeout(()=>{e.loading=!1,e.list=[{id:1,title:"系统管理员",desc:"拥有系统所有权限，负责系统维护和用户管理",quanxian:[1,2,3,4],status:1,level:"超级管理员",create_time:"2024-01-01 10:00:00"},{id:2,title:"业务经理",desc:"负责业务流程管理，客户关系维护",quanxian:[2,3],status:1,level:"管理员",create_time:"2024-01-02 14:30:00"},{id:3,title:"法务专员",desc:"负责合同审核、律师函处理等法务工作",quanxian:[3],status:1,level:"普通用户",create_time:"2024-01-03 09:15:00"},{id:4,title:"财务专员",desc:"负责财务管理、支付处理、报表统计",quanxian:[4],status:1,level:"普通用户",create_time:"2024-01-04 11:20:00"},{id:5,title:"客服专员",desc:"负责客户咨询、问题处理、售后服务",quanxian:[22],status:0,level:"普通用户",create_time:"2024-01-05 16:45:00"}],e.total=5},800)},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let a=this;a.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(a.ruleForm[t]="",a.$message.success("删除成功!")):a.$message.error(e.msg)})}}},n=i,r=(a("fd24"),a("2877")),c=Object(r["a"])(n,s,l,!1,null,"feecce72",null);t["default"]=c.exports},fd24:function(e,t,a){"use strict";a("4a94")}}]);
//# sourceMappingURL=chunk-5f4caf1e.756f9b60.js.map