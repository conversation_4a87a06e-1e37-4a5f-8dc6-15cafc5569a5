{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\dingdan.vue", "mtime": 1748617691748}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dingdan.vue"], "names": [], "mappings": ";AA4xBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dingdan.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n\t<div class=\"order-management-container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<div class=\"page-header\">\r\n\t\t\t<div class=\"header-left\">\r\n\t\t\t\t<h2 class=\"page-title\">\r\n\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t{{ this.$router.currentRoute.name }}\r\n\t\t\t\t</h2>\r\n\t\t\t\t<div class=\"page-subtitle\">管理客户签约订单和合同信息</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t<el-button\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\ticon=\"el-icon-refresh\"\r\n\t\t\t\t\t\t@click=\"refulsh\"\r\n\t\t\t\t\t\tclass=\"refresh-btn\"\r\n\t\t\t\t>\r\n\t\t\t\t\t刷新数据\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 重要通知和提醒区域 -->\r\n\t\t<div class=\"notifications-section\" v-if=\"hasImportantNotifications\">\r\n\t\t\t<el-card shadow=\"hover\" class=\"notification-card\">\r\n\t\t\t\t<div class=\"notification-header\">\r\n\t\t\t\t\t<i class=\"el-icon-warning-outline notification-icon\"></i>\r\n\t\t\t\t\t<span class=\"notification-title\">重要提醒</span>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tsize=\"mini\" \r\n\t\t\t\t\t\t@click=\"dismissNotifications\"\r\n\t\t\t\t\t\tclass=\"dismiss-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"notification-content\">\r\n\t\t\t\t\t<div class=\"notification-list\">\r\n\t\t\t\t\t\t<!-- 待审核订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"pendingOrders > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item urgent\"\r\n\t\t\t\t\t\t\t@click=\"filterByStatus('1')\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ pendingOrders }}</strong> 个订单待审核，请及时处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"warning\">立即处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 即将到期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiringOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item warning\"\r\n\t\t\t\t\t\t\t@click=\"showExpiringOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiringOrders.length }}</strong> 个订单即将到期，请提醒客户续费\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\">查看详情</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 已过期订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"expiredOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item error\"\r\n\t\t\t\t\t\t\t@click=\"showExpiredOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ expiredOrders.length }}</strong> 个订单已过期，需要联系客户处理\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\">紧急处理</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 高价值订单提醒 -->\r\n\t\t\t\t\t\t<div \r\n\t\t\t\t\t\t\tv-if=\"highValueOrders.length > 0\" \r\n\t\t\t\t\t\t\tclass=\"notification-item info\"\r\n\t\t\t\t\t\t\t@click=\"showHighValueOrders\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div class=\"notification-dot\"></div>\r\n\t\t\t\t\t\t\t<div class=\"notification-text\">\r\n\t\t\t\t\t\t\t\t<strong>{{ highValueOrders.length }}</strong> 个高价值订单需要重点关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"notification-action\">\r\n\t\t\t\t\t\t\t\t<el-button size=\"mini\" type=\"success\">查看订单</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</el-card>\r\n\t\t</div>\r\n\r\n\t\t<!-- 统计卡片区域 -->\r\n\t\t<div class=\"stats-section\">\r\n\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon total-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-s-order\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ total }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总订单数</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +8%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('1')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon pending-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">待审核</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change warning\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i> 需关注\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"filterByStatus('2')\">\r\n\t\t\t\t\t\t<div class=\"stat-icon approved-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-circle-check\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">已通过</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +12%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n\t\t\t\t\t<div class=\"stat-card\" @click=\"showRevenueChart\">\r\n\t\t\t\t\t\t<div class=\"stat-icon revenue-icon\">\r\n\t\t\t\t\t\t\t<i class=\"el-icon-money\"></i>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"stat-content\">\r\n\t\t\t\t\t\t\t<div class=\"stat-number\">¥{{ totalRevenue }}</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-label\">总收入</div>\r\n\t\t\t\t\t\t\t<div class=\"stat-change positive\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-arrow-up\"></i> +15%\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t</div>\r\n\r\n\t\t<!-- 搜索和筛选区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"search-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-search\"></i>\r\n\t\t\t\t\t\t搜索与筛选\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"card-subtitle\">快速查找和管理订单信息</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-actions\">\r\n\t\t\t\t\t<el-button-group class=\"action-group\">\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t\t导出\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t<el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n\t\t\t\t\t\t\t刷新\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</el-button-group>\r\n\t\t\t\t\t<el-button type=\"primary\" @click=\"batchAudit\" icon=\"el-icon-check\" class=\"primary-action\">\r\n\t\t\t\t\t\t批量审核\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"search-section\">\r\n\t\t\t\t<el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n\t\t\t\t\t<div class=\"search-row\">\r\n\t\t\t\t\t\t<el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tprefix-icon=\"el-icon-search\"\r\n\t\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t\t\********************=\"getData()\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"业务员\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\t\tv-model=\"search.salesman\"\r\n\t\t\t\t\t\t\t\tclearable\r\n\t\t\t\t\t\t\t\tclass=\"search-select\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item label=\"审核状态\" class=\"search-item\">\r\n\t\t\t\t\t\t\t<el-select v-model=\"search.status\" placeholder=\"选择状态\" clearable class=\"search-select\">\r\n\t\t\t\t\t\t\t\t<el-option label=\"全部状态\" value=\"\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未审核\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"已通过\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t<el-option label=\"未通过\" value=\"3\" />\r\n\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<el-form-item class=\"search-actions-item\">\r\n\t\t\t\t\t\t\t<div class=\"search-actions\">\r\n\t\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\" class=\"search-btn\">\r\n\t\t\t\t\t\t\t\t\t搜索\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n\t\t\t\t\t\t\t\t\t重置\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t<el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n\t\t\t\t\t\t\t\t\t<i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n\t\t\t\t\t\t\t\t\t{{ showAdvanced ? '收起' : '高级筛选' }}\r\n\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 高级筛选区域 -->\r\n\t\t\t\t\t<transition name=\"slide-fade\">\r\n\t\t\t\t\t\t<div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n\t\t\t\t\t\t\t<el-divider content-position=\"left\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-setting\"></i>\r\n\t\t\t\t\t\t\t\t高级筛选选项\r\n\t\t\t\t\t\t\t</el-divider>\r\n\t\t\t\t\t\t\t<div class=\"advanced-content\">\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付时间\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"date-picker\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"支付方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.payType\" placeholder=\"选择支付方式\" class=\"pay-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部方式\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全款支付\" value=\"1\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"分期付款\" value=\"2\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"金额范围\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"amount-range\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.minAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最小金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"range-separator\">-</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number \r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"search.maxAmount\" \r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"最大金额\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols-position=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"advanced-row\">\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"套餐类型\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.packageType\" placeholder=\"选择套餐\" clearable class=\"package-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"全部套餐\" value=\"\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"基础套餐\" value=\"basic\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"高级套餐\" value=\"advanced\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"专业套餐\" value=\"professional\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n\t\t\t\t\t\t\t\t\t\t<el-select v-model=\"search.sortBy\" placeholder=\"排序字段\" class=\"sort-select\">\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"创建时间\" value=\"create_time\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"支付金额\" value=\"pay_age\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"订单状态\" value=\"status\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<el-option label=\"到期时间\" value=\"end_time\" />\r\n\t\t\t\t\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<el-form-item class=\"advanced-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n\t\t\t\t\t\t\t\t\t\t\t应用筛选\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t\t<el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n\t\t\t\t\t\t\t\t\t\t\t清空选项\r\n\t\t\t\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</transition>\r\n\t\t\t\t</el-form>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 数据表格区域 -->\r\n\t\t<el-card shadow=\"hover\" class=\"table-card\">\r\n\t\t\t<div slot=\"header\" class=\"card-header\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<span class=\"card-title\">\r\n\t\t\t\t\t\t<i class=\"el-icon-tickets\"></i>\r\n\t\t\t\t\t\t订单列表\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t<div class=\"selected-info\" v-if=\"selectedRows.length > 0\">\r\n\t\t\t\t\t\t已选择 {{ selectedRows.length }} 项\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"table-actions\">\r\n\t\t\t\t\t<el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n\t\t\t\t\t\t导出数据\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchApprove\" \r\n\t\t\t\t\t\ticon=\"el-icon-check\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"success\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量通过\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button \r\n\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t@click=\"batchReject\" \r\n\t\t\t\t\t\ticon=\"el-icon-close\"\r\n\t\t\t\t\t\t:disabled=\"selectedRows.length === 0\"\r\n\t\t\t\t\t\ttype=\"danger\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t批量拒绝\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<el-table \r\n\t\t\t\t\t:data=\"list\" \r\n\t\t\t\t\tv-loading=\"loading\" \r\n\t\t\t\t\tclass=\"modern-table\"\r\n\t\t\t\t\t:row-class-name=\"tableRowClassName\"\r\n\t\t\t\t\t@selection-change=\"handleSelectionChange\"\r\n\t\t\t>\r\n\t\t\t\t<el-table-column type=\"selection\" width=\"55\" />\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"客户信息\" min-width=\"200\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"client-info\" @click=\"viewUserData(scope.row.client?.id)\">\r\n\t\t\t\t\t\t\t<div class=\"client-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"client-avatar\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-office-building\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"client-details\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"company-name\">\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.company || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-info\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"contact-name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"contact-phone\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-phone\"></i>\r\n\t\t\t\t\t\t\t\t\t\t{{ scope.row.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"套餐内容\" min-width=\"180\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"package-info\">\r\n\t\t\t\t\t\t\t<div class=\"package-name\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.taocan?.title || '未选择套餐' }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-price\">\r\n\t\t\t\t\t\t\t\t<span class=\"price-label\">价格：</span>\r\n\t\t\t\t\t\t\t\t<span class=\"price-value\">¥{{ scope.row.taocan?.price || 0 }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"package-duration\">\r\n\t\t\t\t\t\t\t\t<el-tag size=\"small\" type=\"info\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.taocan?.year || 0 }}年服务\r\n\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"支付情况\" min-width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"payment-info\">\r\n\t\t\t\t\t\t\t<div class=\"payment-type\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-wallet\"></i>\r\n\t\t\t\t\t\t\t\t{{ scope.row.pay_type == 1 ? \"全款\" : `分期/${scope.row.qishu}期` }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-amount\">\r\n\t\t\t\t\t\t\t\t<span class=\"paid\">已付：¥{{ scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-amount\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<span class=\"remaining\">余款：¥{{ scope.row.total_price - scope.row.pay_age }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"payment-progress\" v-if=\"scope.row.pay_type != 1\">\r\n\t\t\t\t\t\t\t\t<el-progress \r\n\t\t\t\t\t\t\t\t\t:percentage=\"Math.round((scope.row.pay_age / scope.row.total_price) * 100)\" \r\n\t\t\t\t\t\t\t\t\t:stroke-width=\"6\"\r\n\t\t\t\t\t\t\t\t\t:show-text=\"false\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"审核状态\" width=\"120\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"status-info\" @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t:type=\"getStatusType(scope.row.status)\" \r\n\t\t\t\t\t\t\t\t\tclass=\"status-tag\"\r\n\t\t\t\t\t\t\t\t\t:effect=\"scope.row.status == 1 ? 'plain' : 'dark'\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{{ getStatusText(scope.row.status) }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t<div v-if=\"scope.row.status == 3\" class=\"status-reason\">\r\n\t\t\t\t\t\t\t\t\t{{ scope.row.status_msg }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\" width=\"100\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"member-info\">\r\n\t\t\t\t\t\t\t<el-tag type=\"info\" size=\"small\">\r\n\t\t\t\t\t\t\t\t{{ scope.row.member?.title || '未分配' }}\r\n\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column label=\"时间信息\" min-width=\"140\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"time-info\">\r\n\t\t\t\t\t\t\t<div class=\"create-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-time\"></i>\r\n\t\t\t\t\t\t\t\t创建：{{ formatDate(scope.row.create_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"end-time\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-date\"></i>\r\n\t\t\t\t\t\t\t\t到期：{{ formatDate(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"remaining-days\" :class=\"getRemainingDaysClass(scope.row.end_time)\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-warning\"></i>\r\n\t\t\t\t\t\t\t\t{{ getRemainingDays(scope.row.end_time) }}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\" width=\"160\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"editData(scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"view-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-view\"></i>\r\n\t\t\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickApprove(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"approve-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-check\"></i>\r\n\t\t\t\t\t\t\t\t\t通过\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"quickReject(scope.row)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"reject-btn\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"scope.row.status === 1\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-close\"></i>\r\n\t\t\t\t\t\t\t\t\t拒绝\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t\t<el-button \r\n\t\t\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t@click=\"delData(scope.$index, scope.row.id)\"\r\n\t\t\t\t\t\t\t\t\tclass=\"delete-btn\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<i class=\"el-icon-delete\"></i>\r\n\t\t\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t\r\n\t\t\t<!-- 分页 -->\r\n\t\t\t<div class=\"pagination-wrapper\">\r\n\t\t\t\t<el-pagination\r\n\t\t\t\t\t\t@size-change=\"handleSizeChange\"\r\n\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t\t:page-sizes=\"[20, 50, 100, 200]\"\r\n\t\t\t\t\t\t:page-size=\"size\"\r\n\t\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\"\r\n\t\t\t\t\t\t:total=\"total\"\r\n\t\t\t\t\t\tbackground\r\n\t\t\t\t/>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\r\n\t\t<!-- 订单详情对话框 -->\r\n\t\t<el-dialog \r\n\t\t\t\ttitle=\"订单详情\" \r\n\t\t\t\t:visible.sync=\"dialogFormVisible\" \r\n\t\t\t\t:close-on-click-modal=\"false\" \r\n\t\t\t\twidth=\"85%\"\r\n\t\t\t\tclass=\"order-detail-dialog\"\r\n\t\t>\r\n\t\t\t\t<div v-if=\"is_info\" class=\"order-detail-content\">\r\n\t\t\t\t\t\t<!-- 客户信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t客户信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"公司名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"info\">{{ info.client?.company || '未填写' }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系人\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.linkman || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"联系方式\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"viewUserData(info.client?.id)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.phone || '未填写' }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"showImage(info.client?.pic_path)\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"clickable-tag\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"info.client?.pic_path\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t查看执照\r\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag v-else type=\"info\">暂无</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"调解员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.tiaojie_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"法务专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.fawu_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"立案专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.lian_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"合同专员\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.htsczy_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"指定律师\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ info.client?.ls_id || '未分配' }}\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 债务人信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.debts && info.debts.length > 0\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-user-solid\"></i>\r\n\t\t\t\t\t\t\t\t\t\t债务人信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-table :data=\"info.debts\" size=\"medium\" class=\"debt-table\">\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"name\" label=\"债务人姓名\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"tel\" label=\"联系电话\" />\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"money\" label=\"债务金额（元）\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t\t\t<el-table-column prop=\"status\" label=\"状态\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag :type=\"getDebtStatusType(scope.row.status)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.status }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t\t</el-table-column>\r\n\t\t\t\t\t\t\t\t</el-table>\r\n\t\t\t\t\t\t</el-card>\r\n\r\n\t\t\t\t\t\t<!-- 套餐信息 -->\r\n\t\t\t\t\t\t<el-card shadow=\"never\" class=\"detail-card\" v-if=\"info.taocan\">\r\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"detail-header\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"el-icon-box\"></i>\r\n\t\t\t\t\t\t\t\t\t\t套餐信息\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<el-descriptions :column=\"3\" border>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"primary\">{{ info.taocan.title }}</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-highlight\">¥{{ info.taocan.price }}</span>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t\t\t<el-descriptions-item label=\"服务年限\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag type=\"success\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t\t\t\t</el-descriptions>\r\n\t\t\t\t\t\t</el-card>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t\t\t<el-button @click=\"dialogFormVisible = false\">关闭</el-button>\r\n\t\t\t\t\t\t<el-button type=\"primary\" @click=\"downloadOrder\">下载订单</el-button>\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 图片查看对话框 -->\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n\t\t\t\t<div class=\"image-viewer\">\r\n\t\t\t\t\t\t<el-image :src=\"show_image\" fit=\"contain\" />\r\n\t\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 收入统计图表对话框 -->\r\n\t\t<el-dialog title=\"收入统计分析\" :visible.sync=\"showRevenueDialog\" width=\"80%\" class=\"revenue-dialog\">\r\n\t\t\t<div class=\"revenue-stats\">\r\n\t\t\t\t<el-row :gutter=\"20\">\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>月度收入趋势</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-data-line chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>月度收入趋势图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"mock-chart-data\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 60%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 80%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 45%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 70%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 90%\"></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"chart-bar\" style=\"height: 65%\"></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t\t<el-col :span=\"12\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>支付方式分布</h4>\r\n\t\t\t\t\t\t\t<div class=\"chart-placeholder\">\r\n\t\t\t\t\t\t\t\t<i class=\"el-icon-pie-chart chart-icon\"></i>\r\n\t\t\t\t\t\t\t\t<p>支付方式比例图</p>\r\n\t\t\t\t\t\t\t\t<div class=\"payment-stats\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot full-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t全款支付: {{ fullPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"payment-item\">\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"payment-dot installment-payment\"></span>\r\n\t\t\t\t\t\t\t\t\t\t分期付款: {{ installmentPaymentCount }}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t\t<el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n\t\t\t\t\t<el-col :span=\"24\">\r\n\t\t\t\t\t\t<div class=\"chart-card\">\r\n\t\t\t\t\t\t\t<h4>订单状态统计</h4>\r\n\t\t\t\t\t\t\t<div class=\"status-overview\">\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle pending-circle\">{{ pendingOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>待审核</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle approved-circle\">{{ approvedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已通过</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle rejected-circle\">{{ rejectedOrders }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>已拒绝</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"status-item\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"status-circle total-circle\">{{ total }}</div>\r\n\t\t\t\t\t\t\t\t\t<span>总计</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</el-col>\r\n\t\t\t\t</el-row>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 数据导出对话框 -->\r\n\t\t<el-dialog title=\"数据导出\" :visible.sync=\"showExportDialog\" width=\"600px\" class=\"export-dialog\">\r\n\t\t\t<el-form :model=\"exportForm\" label-width=\"120px\">\r\n\t\t\t\t<el-form-item label=\"导出格式\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.format\">\r\n\t\t\t\t\t\t<el-radio label=\"excel\">Excel (.xlsx)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"csv\">CSV (.csv)</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"pdf\">PDF (.pdf)</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"导出内容\">\r\n\t\t\t\t\t<el-checkbox-group v-model=\"exportForm.fields\">\r\n\t\t\t\t\t\t<el-checkbox label=\"client\">客户信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"package\">套餐信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"payment\">支付情况</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"status\">审核状态</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"time\">时间信息</el-checkbox>\r\n\t\t\t\t\t\t<el-checkbox label=\"member\">业务员信息</el-checkbox>\r\n\t\t\t\t\t</el-checkbox-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"数据范围\">\r\n\t\t\t\t\t<el-radio-group v-model=\"exportForm.range\">\r\n\t\t\t\t\t\t<el-radio label=\"all\">全部数据</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"current\">当前页面</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"selected\">选中项目</el-radio>\r\n\t\t\t\t\t\t<el-radio label=\"filtered\">筛选结果</el-radio>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<el-form-item label=\"时间范围\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tv-model=\"exportForm.dateRange\"\r\n\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\tstart-placeholder=\"开始日期\"\r\n\t\t\t\t\t\tend-placeholder=\"结束日期\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tstyle=\"width: 100%\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"showExportDialog = false\">取消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"executeExport\" :loading=\"exportLoading\">\r\n\t\t\t\t\t<i class=\"el-icon-download\"></i>\r\n\t\t\t\t\t开始导出\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t\tallSize: \"mini\",\r\n\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\ttotal: 1,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tsize: 20,\r\n\t\t\t\t\t\tsearch: {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tloading: true,\r\n\t\t\t\t\t\turl: \"/dingdan/\",\r\n\t\t\t\t\t\ttitle: \"签约用户\",\r\n\t\t\t\t\t\tinfo: {},\r\n\t\t\t\t\t\tcurrentId:0,\r\n\t\t\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\t\t\tshow_image: \"\",\r\n\t\t\t\t\t\tdialogVisible: false,\r\n\t\t\t\t\t\tdialogViewUserDetail:false,\r\n\t\t\t\t\t\tis_info: false,\r\n\t\t\t\t\t\tupload_index: \"\",\r\n\t\t\t\t\t\tdialogStatus: false,\r\n\t\t\t\t\t\tdialogEndTime: false,\r\n\t\t\t\t\t\truleForm: {\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tstatus_msg: \"\",\r\n\t\t\t\t\t\t\t\tend_time: \"\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\trules: {\r\n\t\t\t\t\t\t\t\tstatus_msg: [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\t\t\tshowAdvanced: false,\r\n\t\t\t\t\t\tselectedRows: [],\r\n\t\t\t\t\t\tshowRevenueDialog: false,\r\n\t\t\t\t\t\tshowExportDialog: false,\r\n\t\t\t\t\t\texportForm: {\r\n\t\t\t\t\t\t\t\tformat: \"excel\",\r\n\t\t\t\t\t\t\t\tfields: [\"client\", \"package\", \"payment\", \"status\", \"time\", \"member\"],\r\n\t\t\t\t\t\t\t\trange: \"all\",\r\n\t\t\t\t\t\t\t\tdateRange: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\texportLoading: false,\r\n\t\t\t\t\t\tfullPaymentCount: 0,\r\n\t\t\t\t\t\tinstallmentPaymentCount: 0,\r\n\t\t\t\t\t\tshowNotifications: true\r\n\t\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t\t// 统计数据计算\r\n\t\t\t\tpendingOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tapprovedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 2).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\trejectedOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.status === 3).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\ttotalRevenue() {\r\n\t\t\t\t\t\treturn this.list.reduce((sum, item) => sum + (item.pay_age || 0), 0).toLocaleString();\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tfullPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type === 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tinstallmentPaymentCount() {\r\n\t\t\t\t\t\treturn this.list.filter(item => item.pay_type !== 1).length;\r\n\t\t\t\t},\r\n\t\t\t\t// 即将到期的订单（7天内）\r\n\t\t\t\texpiringOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\tconst sevenDaysLater = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate >= today && endDate <= sevenDaysLater;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 已过期的订单\r\n\t\t\t\texpiredOrders() {\r\n\t\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\t\treturn this.list.filter(item => {\r\n\t\t\t\t\t\t\t\tif (!item.end_time) return false;\r\n\t\t\t\t\t\t\t\tconst endDate = new Date(item.end_time);\r\n\t\t\t\t\t\t\t\treturn endDate < today;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t// 高价值订单（金额超过5000）\r\n\t\t\t\thighValueOrders() {\r\n\t\t\t\t\t\treturn this.list.filter(item => (item.pay_age || 0) > 5000);\r\n\t\t\t\t},\r\n\t\t\t\t// 是否有重要通知\r\n\t\t\t\thasImportantNotifications() {\r\n\t\t\t\t\t\treturn this.showNotifications && (\r\n\t\t\t\t\t\t\t\tthis.pendingOrders > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiringOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.expiredOrders.length > 0 || \r\n\t\t\t\t\t\t\t\tthis.highValueOrders.length > 0\r\n\t\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\teditData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tviewUserData(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tif (id != 0) {\r\n\t\t\t\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t\t},\r\n\t\t\t\tgetInfo(id) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tdelData(index, id) {\r\n\t\t\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\r\n\t\t\t\tupdateEndTIme() {\r\n\t\t\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\trefulsh() {\r\n\t\t\t\t\t\tthis.$router.go(0);\r\n\t\t\t\t},\r\n\t\t\t\tgetData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.loading = true;\r\n\t\t\t\t\t\t_this\r\n\t\t\t\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tsaveData() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\thandleSizeChange(val) {\r\n\t\t\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleCurrentChange(val) {\r\n\t\t\t\t\t\tthis.page = val;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\thandleSuccess(res) {\r\n\t\t\t\t\t\tlet _this = this\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t},\r\n\t\t\t\tbeforeUpload(file) {\r\n\t\t\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdelImage(file, fileName) {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tshowImage(file) {\r\n\t\t\t\t\t\tthis.show_image = file;\r\n\t\t\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t\t},\r\n\t\t\t\tchangePinzhen(index) {\r\n\t\t\t\t\t\tthis.index = index\r\n\t\t\t\t},\r\n\t\t\t\tshowStatus(row) {\r\n\t\t\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tshowEndTime(row) {\r\n\t\t\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\t\t\tthis.ruleForm = row\r\n\t\t\t\t},\r\n\t\t\t\tchangeEndTime() {\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\t\t\tlet _this = this;\r\n\t\t\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusType(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: 'warning',\r\n\t\t\t\t\t\t\t\t2: 'success', \r\n\t\t\t\t\t\t\t\t3: 'danger'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || 'info';\r\n\t\t\t\t},\r\n\t\t\t\tgetStatusText(status) {\r\n\t\t\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t\t\t\t1: '未审核',\r\n\t\t\t\t\t\t\t\t2: '已通过',\r\n\t\t\t\t\t\t\t\t3: '未通过'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\treturn statusMap[status] || '未知';\r\n\t\t\t\t},\r\n\t\t\t\tgetDebtStatusType(status) {\r\n\t\t\t\t\t\t// 债务状态类型映射\r\n\t\t\t\t\t\tif (status === '已解决') return 'success';\r\n\t\t\t\t\t\tif (status === '处理中') return 'warning';\r\n\t\t\t\t\t\treturn 'info';\r\n\t\t\t\t},\r\n\t\t\t\tformatDate(dateStr) {\r\n\t\t\t\t\t\tif (!dateStr) return '未设置';\r\n\t\t\t\t\t\treturn new Date(dateStr).toLocaleDateString('zh-CN');\r\n\t\t\t\t},\r\n\t\t\t\ttableRowClassName({row, rowIndex}) {\r\n\t\t\t\t\t\tif (row.status === 1) return 'warning-row';\r\n\t\t\t\t\t\tif (row.status === 3) return 'danger-row';\r\n\t\t\t\t\t\treturn '';\r\n\t\t\t\t},\r\n\t\t\t\tresetSearch() {\r\n\t\t\t\t\t\tthis.search = {\r\n\t\t\t\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t\t\t\t\tsalesman: \"\",\r\n\t\t\t\t\t\t\t\trefund_time: [],\r\n\t\t\t\t\t\t\t\tstatus: \"\",\r\n\t\t\t\t\t\t\t\tpayType: \"\",\r\n\t\t\t\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\t\t\t\tpackageType: \"\",\r\n\t\t\t\t\t\t\t\tsortBy: \"\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\texportData() {\r\n\t\t\t\t\tthis.showExportDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\tdownloadOrder() {\r\n\t\t\t\t\t\tthis.$message.success('订单下载功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\ttoggleAdvanced() {\r\n\t\t\t\t\t\tthis.showAdvanced = !this.showAdvanced;\r\n\t\t\t\t},\r\n\t\t\t\tapplyAdvancedSearch() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tclearAdvancedSearch() {\r\n\t\t\t\t\t\tthis.resetSearch();\r\n\t\t\t\t},\r\n\t\t\t\trefreshData() {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t},\r\n\t\t\t\tbatchAudit() {\r\n\t\t\t\t\t\tthis.$message.success('批量审核功能开发中...');\r\n\t\t\t\t},\r\n\t\t\t\thandleSelectionChange(selection) {\r\n\t\t\t\t\t\tthis.selectedRows = selection;\r\n\t\t\t\t},\r\n\t\t\t\tfilterByStatus(status) {\r\n\t\t\t\t\tthis.search.status = status;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t\tthis.$message.success(`已筛选${this.getStatusText(status) || '全部'}订单`);\r\n\t\t\t\t},\r\n\t\t\t\tshowRevenueChart() {\r\n\t\t\t\t\tthis.showRevenueDialog = true;\r\n\t\t\t\t},\r\n\t\t\t\texecuteExport() {\r\n\t\t\t\t\tthis.exportLoading = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟导出过程\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst formatText = {\r\n\t\t\t\t\t\t\t'excel': 'Excel',\r\n\t\t\t\t\t\t\t'csv': 'CSV', \r\n\t\t\t\t\t\t\t'pdf': 'PDF'\r\n\t\t\t\t\t\t}[this.exportForm.format];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst rangeText = {\r\n\t\t\t\t\t\t\t'all': '全部数据',\r\n\t\t\t\t\t\t\t'current': '当前页面',\r\n\t\t\t\t\t\t\t'selected': '选中项目',\r\n\t\t\t\t\t\t\t'filtered': '筛选结果'\r\n\t\t\t\t\t\t}[this.exportForm.range];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 这里可以集成真实的导出库，如 xlsx、jsPDF等\r\n\t\t\t\t\t\tconst blob = this.generateExportData();\r\n\t\t\t\t\t\tconst url = URL.createObjectURL(blob);\r\n\t\t\t\t\t\tconst link = document.createElement('a');\r\n\t\t\t\t\t\tlink.href = url;\r\n\t\t\t\t\t\tlink.download = `订单数据_${new Date().toISOString().split('T')[0]}.${this.exportForm.format}`;\r\n\t\t\t\t\t\tlink.click();\r\n\t\t\t\t\t\tURL.revokeObjectURL(url);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.exportLoading = false;\r\n\t\t\t\t\t\tthis.showExportDialog = false;\r\n\t\t\t\t\t\tthis.$message.success(`${formatText}格式的${rangeText}导出成功！`);\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t},\r\n\t\t\t\tgenerateExportData() {\r\n\t\t\t\t\t// 根据选择的范围获取数据\r\n\t\t\t\t\tlet exportList = [];\r\n\t\t\t\t\tswitch(this.exportForm.range) {\r\n\t\t\t\t\t\tcase 'all':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'current':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'selected':\r\n\t\t\t\t\t\t\texportList = this.selectedRows;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'filtered':\r\n\t\t\t\t\t\t\texportList = this.list;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据选择的字段生成数据\r\n\t\t\t\t\tconst data = exportList.map(item => {\r\n\t\t\t\t\t\tconst row = {};\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('client')) {\r\n\t\t\t\t\t\t\trow['公司名称'] = item.client?.company || '';\r\n\t\t\t\t\t\t\trow['联系人'] = item.client?.linkman || '';\r\n\t\t\t\t\t\t\trow['联系电话'] = item.client?.phone || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('package')) {\r\n\t\t\t\t\t\t\trow['套餐名称'] = item.taocan?.title || '';\r\n\t\t\t\t\t\t\trow['套餐价格'] = item.taocan?.price || 0;\r\n\t\t\t\t\t\t\trow['服务年限'] = item.taocan?.year || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('payment')) {\r\n\t\t\t\t\t\t\trow['支付方式'] = item.pay_type == 1 ? '全款' : '分期';\r\n\t\t\t\t\t\t\trow['已付金额'] = item.pay_age || 0;\r\n\t\t\t\t\t\t\trow['总金额'] = item.total_price || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('status')) {\r\n\t\t\t\t\t\t\trow['审核状态'] = this.getStatusText(item.status);\r\n\t\t\t\t\t\t\trow['状态说明'] = item.status_msg || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('time')) {\r\n\t\t\t\t\t\t\trow['创建时间'] = item.create_time || '';\r\n\t\t\t\t\t\t\trow['到期时间'] = item.end_time || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.exportForm.fields.includes('member')) {\r\n\t\t\t\t\t\t\trow['业务员'] = item.member?.title || '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn row;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 生成CSV格式的Blob\r\n\t\t\t\t\tconst csvContent = this.convertToCSV(data);\r\n\t\t\t\t\treturn new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\t\t\t\t},\r\n\t\t\t\tconvertToCSV(data) {\r\n\t\t\t\t\tif (!data || data.length === 0) return '';\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst headers = Object.keys(data[0]);\r\n\t\t\t\t\tconst csvRows = [headers.join(',')];\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor (const row of data) {\r\n\t\t\t\t\t\tconst values = headers.map(header => {\r\n\t\t\t\t\t\t\tconst value = row[header];\r\n\t\t\t\t\t\t\treturn typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tcsvRows.push(values.join(','));\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn csvRows.join('\\n');\r\n\t\t\t\t},\r\n\t\t\t\tbatchApprove() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量通过的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$confirm(`确认批量通过选中的 ${pendingRows.length} 个待审核订单吗？`, '批量审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t\t'status_msg': '批量审核通过'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量审核完成，成功通过 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量审核过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量审核');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tbatchReject() {\r\n\t\t\t\t\tif (this.selectedRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('请先选择要批量拒绝的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst pendingRows = this.selectedRows.filter(row => row.status === 1);\r\n\t\t\t\t\tif (pendingRows.length === 0) {\r\n\t\t\t\t\t\tthis.$message.warning('选中的订单中没有待审核的订单');\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$prompt('请输入批量拒绝理由', `批量拒绝 ${pendingRows.length} 个订单`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\tconst promises = pendingRows.map(row => {\r\n\t\t\t\t\t\t\treturn this.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tPromise.all(promises).then(responses => {\r\n\t\t\t\t\t\t\tconst successCount = responses.filter(resp => resp.code === 200).length;\r\n\t\t\t\t\t\t\tthis.$message.success(`批量拒绝完成，成功拒绝 ${successCount} 个订单`);\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\tthis.selectedRows = []; // 清空选择\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\tthis.$message.error('批量拒绝过程中出现错误');\r\n\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消批量拒绝');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickApprove(row) {\r\n\t\t\t\t\tthis.$confirm(`确认通过 ${row.client?.company || '该客户'} 的订单吗？`, '快速审核', {\r\n\t\t\t\t\t\tconfirmButtonText: '确认通过',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\ttype: 'success'\r\n\t\t\t\t\t}).then(() => {\r\n\t\t\t\t\t\t// 调用API通过订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 2,\r\n\t\t\t\t\t\t\t'status_msg': '快速审核通过'\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('审核通过成功');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tquickReject(row) {\r\n\t\t\t\t\tthis.$prompt('请输入拒绝理由', `拒绝订单 - ${row.client?.company || '客户'}`, {\r\n\t\t\t\t\t\tconfirmButtonText: '确认拒绝',\r\n\t\t\t\t\t\tcancelButtonText: '取消',\r\n\t\t\t\t\t\tinputPlaceholder: '请填写拒绝的具体原因...',\r\n\t\t\t\t\t\tinputValidator: (value) => {\r\n\t\t\t\t\t\t\tif (!value || value.trim() === '') {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由不能为空';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (value.length < 5) {\r\n\t\t\t\t\t\t\t\treturn '拒绝理由至少需要5个字符';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(({ value }) => {\r\n\t\t\t\t\t\t// 调用API拒绝订单\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': row.id,\r\n\t\t\t\t\t\t\t'status': 3,\r\n\t\t\t\t\t\t\t'status_msg': value\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message.success('订单已拒绝');\r\n\t\t\t\t\t\t\t\tthis.getData(); // 刷新数据\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(resp.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.$message.info('已取消操作');\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDays(end_time) {\r\n\t\t\t\t\tif (!end_time) return '未设置';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return `已过期${Math.abs(remainingDays)}天`;\r\n\t\t\t\t\tif (remainingDays === 0) return '今天到期';\r\n\t\t\t\t\tif (remainingDays <= 7) return `${remainingDays}天后到期`;\r\n\t\t\t\t\tif (remainingDays <= 30) return `${remainingDays}天后到期`;\r\n\t\t\t\t\treturn `${remainingDays}天后到期`;\r\n\t\t\t\t},\r\n\t\t\t\tgetRemainingDaysClass(end_time) {\r\n\t\t\t\t\tif (!end_time) return '';\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst endDate = new Date(end_time);\r\n\t\t\t\t\tconst remainingDays = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (remainingDays < 0) return 'expired';\r\n\t\t\t\t\tif (remainingDays <= 3) return 'urgent';\r\n\t\t\t\t\tif (remainingDays <= 7) return 'warning';\r\n\t\t\t\t\treturn 'normal';\r\n\t\t\t\t},\r\n\t\t\t\t// 通知系统相关方法\r\n\t\t\t\tdismissNotifications() {\r\n\t\t\t\t\tthis.showNotifications = false;\r\n\t\t\t\t\tthis.$message.success('已暂时隐藏提醒通知');\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiringOrders() {\r\n\t\t\t\t\t// 显示即将到期的订单\r\n\t\t\t\t\tthis.$message.info(`查看${this.expiringOrders.length}个即将到期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowExpiredOrders() {\r\n\t\t\t\t\t// 显示已过期的订单\r\n\t\t\t\t\tthis.$message.warning(`查看${this.expiredOrders.length}个已过期的订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t},\r\n\t\t\t\tshowHighValueOrders() {\r\n\t\t\t\t\t// 显示高价值订单\r\n\t\t\t\t\tthis.$message.success(`查看${this.highValueOrders.length}个高价值订单`);\r\n\t\t\t\t\t// 这里可以实现具体的筛选逻辑\r\n\t\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n.order-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card:active {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.pending-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.revenue-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n.stat-change.warning {\r\n  color: #f39c12;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.advanced-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.advanced-item {\r\n  margin-bottom: 0 !important;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-item .el-form-item__label {\r\n  color: #495057;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n}\r\n\r\n.date-picker,\r\n.pay-select,\r\n.package-select,\r\n.sort-select {\r\n  width: 100% !important;\r\n}\r\n\r\n.date-picker .el-input__inner,\r\n.pay-select .el-input__inner,\r\n.package-select .el-input__inner,\r\n.sort-select .el-input__inner {\r\n  height: 36px;\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.date-picker .el-input__inner:focus,\r\n.pay-select .el-input__inner:focus,\r\n.package-select .el-input__inner:focus,\r\n.sort-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.amount-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.amount-range .el-input-number {\r\n  flex: 1;\r\n}\r\n\r\n.range-separator {\r\n  color: #7f8c8d;\r\n  font-weight: 500;\r\n  padding: 0 4px;\r\n}\r\n\r\n.advanced-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n  min-width: 200px;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.advanced-actions .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.advanced-actions .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary) {\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.advanced-actions .el-button:not(.el-button--primary):hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 选择信息样式 */\r\n.selected-info {\r\n  font-size: 13px;\r\n  color: #667eea;\r\n  margin-top: 4px;\r\n  font-weight: 500;\r\n  background: rgba(102, 126, 234, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 表格行样式 */\r\n.modern-table .warning-row {\r\n  background-color: #fff7e6 !important;\r\n}\r\n\r\n.modern-table .danger-row {\r\n  background-color: #fff2f0 !important;\r\n}\r\n\r\n/* 客户信息样式 */\r\n.client-info {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.client-info:hover {\r\n  background-color: #f8f9ff;\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n  margin: -8px;\r\n}\r\n\r\n.client-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.client-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.client-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.company-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.contact-info, .contact-phone {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 2px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 套餐信息样式 */\r\n.package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.package-name {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.package-price {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.price-label {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.price-value {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.package-duration {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 支付信息样式 */\r\n.payment-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-type {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.payment-amount, .remaining-amount {\r\n  font-size: 13px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.paid {\r\n  color: #27ae60;\r\n  font-weight: 500;\r\n}\r\n\r\n.remaining {\r\n  color: #e74c3c;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-progress {\r\n  margin-top: 8px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__outer {\r\n  background-color: #f0f0f0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.payment-progress .el-progress-bar__inner {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 业务员信息样式 */\r\n.member-info {\r\n  text-align: center;\r\n}\r\n\r\n.member-info .el-tag {\r\n  font-size: 12px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 状态信息样式 */\r\n.status-info {\r\n  cursor: pointer;\r\n  text-align: center;\r\n}\r\n\r\n.status-tag {\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-reason {\r\n  font-size: 12px;\r\n  color: #e74c3c;\r\n  margin-top: 4px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n}\r\n\r\n.create-time, .end-time {\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.remaining-days {\r\n  margin-top: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  text-align: center;\r\n}\r\n\r\n.remaining-days.normal {\r\n  background-color: #f0f9ff;\r\n  color: #1e40af;\r\n  border: 1px solid #dbeafe;\r\n}\r\n\r\n.remaining-days.warning {\r\n  background-color: #fef3c7;\r\n  color: #b45309;\r\n  border: 1px solid #fde68a;\r\n}\r\n\r\n.remaining-days.urgent {\r\n  background-color: #fee2e2;\r\n  color: #dc2626;\r\n  border: 1px solid #fecaca;\r\n}\r\n\r\n.remaining-days.expired {\r\n  background-color: #f3f4f6;\r\n  color: #6b7280;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.view-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.approve-btn {\r\n  color: #67C23A !important;\r\n}\r\n\r\n.reject-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.view-btn:hover, .approve-btn:hover, .reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n.approve-btn:hover {\r\n  background-color: rgba(103, 194, 58, 0.1) !important;\r\n}\r\n\r\n.reject-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(245, 108, 108, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-detail-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.order-detail-content {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border: none;\r\n}\r\n\r\n.detail-header {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.clickable-tag {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-tag:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-highlight {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.money-amount {\r\n  font-weight: 600;\r\n  color: #e74c3c;\r\n}\r\n\r\n.debt-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .order-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-input-number {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .amount-range {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .client-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .selected-info {\r\n    margin-top: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n  \r\n  .remaining-days {\r\n    font-size: 11px;\r\n    padding: 2px 6px;\r\n  }\r\n  \r\n  .payment-progress {\r\n    margin-top: 6px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .action-buttons .el-button {\r\n    font-size: 12px;\r\n    padding: 4px 8px;\r\n  }\r\n  \r\n  .table-actions .el-button {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 收入统计图表样式 */\r\n.revenue-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.revenue-stats {\r\n  min-height: 400px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #f0f0f0;\r\n  height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-card h4 {\r\n  margin: 0 0 16px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n  color: #6c757d;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 12px;\r\n  color: #667eea;\r\n}\r\n\r\n.chart-placeholder p {\r\n  margin: 0 0 16px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.mock-chart-data {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 8px;\r\n  height: 100px;\r\n  width: 200px;\r\n}\r\n\r\n.chart-bar {\r\n  flex: 1;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 4px 4px 0 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chart-bar:hover {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.payment-stats {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.payment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.payment-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.full-payment {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.installment-payment {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.status-overview {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 8px;\r\n  border: 2px dashed #e9ecef;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.status-circle {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.status-circle:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.pending-circle {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.approved-circle {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.rejected-circle {\r\n  background: linear-gradient(135deg, #ff9a9e, #fecfef);\r\n}\r\n\r\n.total-circle {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.status-item span {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据导出对话框样式 */\r\n.export-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.export-dialog .el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.export-dialog .el-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox-group {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.export-dialog .el-checkbox {\r\n  margin: 0;\r\n}\r\n\r\n.export-dialog .el-form-item__label {\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.export-dialog .el-radio {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.export-dialog .dialog-footer {\r\n  padding: 16px 0 0 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 20px;\r\n  text-align: right;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button {\r\n  margin-left: 12px;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.export-dialog .dialog-footer .el-button--primary:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 响应式图表样式 */\r\n@media (max-width: 768px) {\r\n  .revenue-stats .el-col {\r\n    margin-bottom: 20px;\r\n  }\r\n  \r\n  .chart-card {\r\n    height: auto;\r\n    min-height: 250px;\r\n  }\r\n  \r\n  .status-overview {\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .status-circle {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .export-dialog .el-checkbox-group {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 通知系统样式 */\r\n.notifications-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 12px;\r\n  border: 1px solid #ffd93d;\r\n  background: linear-gradient(135deg, #fff9c4 0%, #ffffff 100%);\r\n  box-shadow: 0 4px 20px rgba(255, 217, 61, 0.3);\r\n}\r\n\r\n.notification-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px 20px 12px 20px;\r\n  border-bottom: 1px solid rgba(255, 217, 61, 0.2);\r\n}\r\n\r\n.notification-icon {\r\n  font-size: 20px;\r\n  color: #f39c12;\r\n}\r\n\r\n.notification-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  flex: 1;\r\n}\r\n\r\n.dismiss-btn {\r\n  color: #7f8c8d !important;\r\n  padding: 4px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.dismiss-btn:hover {\r\n  color: #e74c3c !important;\r\n  background: rgba(231, 76, 60, 0.1) !important;\r\n}\r\n\r\n.notification-content {\r\n  padding: 16px 20px 20px 20px;\r\n}\r\n\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.notification-item:hover {\r\n  transform: translateX(4px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.notification-item.urgent {\r\n  background: linear-gradient(135deg, #fee2e2 0%, #fef7f7 100%);\r\n  border-color: rgba(239, 68, 68, 0.2);\r\n}\r\n\r\n.notification-item.urgent:hover {\r\n  background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%);\r\n}\r\n\r\n.notification-item.warning {\r\n  background: linear-gradient(135deg, #fef3c7 0%, #fefdf8 100%);\r\n  border-color: rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.notification-item.warning:hover {\r\n  background: linear-gradient(135deg, #fde68a 0%, #fef3c7 100%);\r\n}\r\n\r\n.notification-item.error {\r\n  background: linear-gradient(135deg, #ffebee 0%, #fafafa 100%);\r\n  border-color: rgba(244, 67, 54, 0.2);\r\n}\r\n\r\n.notification-item.error:hover {\r\n  background: linear-gradient(135deg, #ffcdd2 0%, #ffebee 100%);\r\n}\r\n\r\n.notification-item.info {\r\n  background: linear-gradient(135deg, #e3f2fd 0%, #fafafa 100%);\r\n  border-color: rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.notification-item.info:hover {\r\n  background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 100%);\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.notification-item.urgent .notification-dot {\r\n  background: #ef4444;\r\n}\r\n\r\n.notification-item.warning .notification-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.notification-item.error .notification-dot {\r\n  background: #f44336;\r\n}\r\n\r\n.notification-item.info .notification-dot {\r\n  background: #2196f3;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    opacity: 0.7;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.notification-text {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n  line-height: 1.5;\r\n}\r\n\r\n.notification-text strong {\r\n  color: #e74c3c;\r\n  font-weight: 600;\r\n}\r\n\r\n.notification-action {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.notification-action .el-button {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.notification-action .el-button:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.export-dialog .el-checkbox-group {\r\n  grid-template-columns: 1fr;\r\n}\r\n\r\n.notifications-section {\r\n  margin: 0 -8px 16px -8px;\r\n}\r\n\r\n.notification-card {\r\n  border-radius: 8px;\r\n  margin: 0 8px;\r\n}\r\n\r\n.notification-header {\r\n  padding: 12px 16px 8px 16px;\r\n}\r\n\r\n.notification-content {\r\n  padding: 12px 16px 16px 16px;\r\n}\r\n\r\n.notification-item {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.notification-item .notification-dot {\r\n  order: -1;\r\n  align-self: flex-start;\r\n}\r\n\r\n.notification-text {\r\n  order: 0;\r\n  width: 100%;\r\n}\r\n\r\n.notification-action {\r\n  order: 1;\r\n  align-self: flex-end;\r\n}\r\n</style>\r\n"]}]}