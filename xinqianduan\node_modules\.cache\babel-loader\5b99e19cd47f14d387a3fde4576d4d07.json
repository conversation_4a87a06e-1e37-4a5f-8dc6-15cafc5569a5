{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\src\\utils\\fileUtils.js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\utils\\fileUtils.js", "mtime": 1748604247127}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["formatFileSize", "size", "toFixed", "getFileType", "fileName", "extension", "split", "pop", "toLowerCase", "typeMap", "getFileCategory", "fileType", "name", "includes", "isPreviewable", "previewableTypes", "generatePreviewUrl", "fileId", "baseUrl", "process", "env", "VUE_APP_BASE_API"], "sources": ["H:/fdbfront/xinqianduan/src/utils/fileUtils.js"], "sourcesContent": ["/**\r\n * 格式化文件大小\r\n * @param {number} size 文件大小（字节）\r\n * @returns {string} 格式化后的文件大小\r\n */\r\nexport function formatFileSize(size) {\r\n  if (size < 1024) {\r\n    return size + ' B'\r\n  } else if (size < 1024 * 1024) {\r\n    return (size / 1024).toFixed(2) + ' KB'\r\n  } else if (size < 1024 * 1024 * 1024) {\r\n    return (size / (1024 * 1024)).toFixed(2) + ' MB'\r\n  } else {\r\n    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'\r\n  }\r\n}\r\n\r\n/**\r\n * 获取文件类型\r\n * @param {string} fileName 文件名\r\n * @returns {string} 文件类型\r\n */\r\nexport function getFileType(fileName) {\r\n  const extension = fileName.split('.').pop().toLowerCase()\r\n  const typeMap = {\r\n    // 图片\r\n    'jpg': 'image',\r\n    'jpeg': 'image',\r\n    'png': 'image',\r\n    'gif': 'image',\r\n    'bmp': 'image',\r\n    // 文档\r\n    'pdf': 'pdf',\r\n    'doc': 'doc',\r\n    'docx': 'doc',\r\n    'xls': 'xls',\r\n    'xlsx': 'xls',\r\n    'ppt': 'ppt',\r\n    'pptx': 'ppt',\r\n    // 文本\r\n    'txt': 'text',\r\n    'md': 'text',\r\n    // 压缩文件\r\n    'zip': 'archive',\r\n    'rar': 'archive',\r\n    '7z': 'archive'\r\n  }\r\n  return typeMap[extension] || 'other'\r\n}\r\n\r\n/**\r\n * 获取文件分类\r\n * @param {string} fileName 文件名\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 文件分类\r\n */\r\nexport function getFileCategory(fileName, fileType) {\r\n  // 根据文件名和类型智能分类\r\n  const name = fileName.toLowerCase()\r\n  \r\n  // 案件文书\r\n  if (name.includes('案件') || name.includes('诉讼') || name.includes('判决')) {\r\n    return '案件文书'\r\n  }\r\n  \r\n  // 合同文件\r\n  if (name.includes('合同') || name.includes('协议') || name.includes('契约')) {\r\n    return '合同文件'\r\n  }\r\n  \r\n  // 咨询记录\r\n  if (name.includes('咨询') || name.includes('记录') || name.includes('纪要')) {\r\n    return '咨询记录'\r\n  }\r\n  \r\n  // 根据文件类型分类\r\n  switch (fileType) {\r\n    case 'image':\r\n      return '图片文件'\r\n    case 'pdf':\r\n      return 'PDF文档'\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'Word文档'\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'Excel文档'\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'PPT文档'\r\n    case 'text':\r\n      return '文本文件'\r\n    case 'archive':\r\n      return '压缩文件'\r\n    default:\r\n      return '其他文件'\r\n  }\r\n}\r\n\r\n/**\r\n * 检查文件是否可预览\r\n * @param {string} fileType 文件类型\r\n * @returns {boolean} 是否可预览\r\n */\r\nexport function isPreviewable(fileType) {\r\n  const previewableTypes = ['image', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']\r\n  return previewableTypes.includes(fileType)\r\n}\r\n\r\n/**\r\n * 生成文件预览URL\r\n * @param {string} fileId 文件ID\r\n * @param {string} fileType 文件类型\r\n * @returns {string} 预览URL\r\n */\r\nexport function generatePreviewUrl(fileId, fileType) {\r\n  const baseUrl = process.env.VUE_APP_BASE_API\r\n  if (fileType === 'image') {\r\n    return `${baseUrl}/archive/preview/image/${fileId}`\r\n  } else if (fileType === 'pdf') {\r\n    return `${baseUrl}/archive/preview/pdf/${fileId}`\r\n  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileType)) {\r\n    return `${baseUrl}/archive/preview/office/${fileId}`\r\n  }\r\n  return ''\r\n} "], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAE;EACnC,IAAIA,IAAI,GAAG,IAAI,EAAE;IACf,OAAOA,IAAI,GAAG,IAAI;EACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;IAC7B,OAAO,CAACA,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EACzC,CAAC,MAAM,IAAID,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;IACpC,OAAO,CAACA,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EAClD,CAAC,MAAM;IACL,OAAO,CAACD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EACzD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,QAAQ,EAAE;EACpC,MAAMC,SAAS,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACzD,MAAMC,OAAO,GAAG;IACd;IACA,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,OAAO;IACf,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd;IACA,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,KAAK;IACb,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,KAAK;IACb,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,KAAK;IACb;IACA,KAAK,EAAE,MAAM;IACb,IAAI,EAAE,MAAM;IACZ;IACA,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,SAAS;IAChB,IAAI,EAAE;EACR,CAAC;EACD,OAAOA,OAAO,CAACJ,SAAS,CAAC,IAAI,OAAO;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,eAAeA,CAACN,QAAQ,EAAEO,QAAQ,EAAE;EAClD;EACA,MAAMC,IAAI,GAAGR,QAAQ,CAACI,WAAW,CAAC,CAAC;;EAEnC;EACA,IAAII,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrE,OAAO,MAAM;EACf;;EAEA;EACA,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrE,OAAO,MAAM;EACf;;EAEA;EACA,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrE,OAAO,MAAM;EACf;;EAEA;EACA,QAAQF,QAAQ;IACd,KAAK,OAAO;MACV,OAAO,MAAM;IACf,KAAK,KAAK;MACR,OAAO,OAAO;IAChB,KAAK,KAAK;IACV,KAAK,MAAM;MACT,OAAO,QAAQ;IACjB,KAAK,KAAK;IACV,KAAK,MAAM;MACT,OAAO,SAAS;IAClB,KAAK,KAAK;IACV,KAAK,MAAM;MACT,OAAO,OAAO;IAChB,KAAK,MAAM;MACT,OAAO,MAAM;IACf,KAAK,SAAS;MACZ,OAAO,MAAM;IACf;MACE,OAAO,MAAM;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,aAAaA,CAACH,QAAQ,EAAE;EACtC,MAAMI,gBAAgB,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACtF,OAAOA,gBAAgB,CAACF,QAAQ,CAACF,QAAQ,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,kBAAkBA,CAACC,MAAM,EAAEN,QAAQ,EAAE;EACnD,MAAMO,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAC5C,IAAIV,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO,GAAGO,OAAO,0BAA0BD,MAAM,EAAE;EACrD,CAAC,MAAM,IAAIN,QAAQ,KAAK,KAAK,EAAE;IAC7B,OAAO,GAAGO,OAAO,wBAAwBD,MAAM,EAAE;EACnD,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAACJ,QAAQ,CAACF,QAAQ,CAAC,EAAE;IAC3E,OAAO,GAAGO,OAAO,2BAA2BD,MAAM,EAAE;EACtD;EACA,OAAO,EAAE;AACX", "ignoreList": []}]}