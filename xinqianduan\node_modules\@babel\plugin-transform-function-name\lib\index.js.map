{"version": 3, "names": ["_helperCompilationTargets", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_helperFunctionName", "_default", "exports", "default", "declare", "api", "assertVersion", "supportUnicodeId", "isRequired", "targets", "name", "visitor", "FunctionExpression", "exit", "path", "key", "parentPath", "isObjectProperty", "replacement", "nameFunction", "replaceWith", "ObjectProperty", "value", "get", "isFunction", "newNode"], "sources": ["../src/index.ts"], "sourcesContent": ["import { isRequired } from \"@babel/helper-compilation-targets\";\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport nameFunction from \"@babel/helper-function-name\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const supportUnicodeId = !isRequired(\n    \"transform-unicode-escapes\",\n    api.targets(),\n  );\n\n  return {\n    name: \"transform-function-name\",\n\n    visitor: {\n      FunctionExpression: {\n        exit(path) {\n          if (path.key !== \"value\" && !path.parentPath.isObjectProperty()) {\n            const replacement = nameFunction(path);\n            if (replacement) path.replaceWith(replacement);\n          }\n        },\n      },\n\n      ObjectProperty(path) {\n        const value = path.get(\"value\");\n        if (value.isFunction()) {\n          const newNode = nameFunction(\n            // @ts-expect-error Fixme: should check ArrowFunctionExpression\n            value,\n            false,\n            supportUnicodeId,\n          );\n          if (newNode) value.replaceWith(newNode);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AAAuD,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAExC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EACtC,MAAMC,gBAAgB,GAAG,CAAC,IAAAC,oCAAU,EAClC,2BAA2B,EAC3BH,GAAG,CAACI,OAAO,CAAC,CACd,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,yBAAyB;IAE/BC,OAAO,EAAE;MACPC,kBAAkB,EAAE;QAClBC,IAAIA,CAACC,IAAI,EAAE;UACT,IAAIA,IAAI,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,IAAI,CAACE,UAAU,CAACC,gBAAgB,CAAC,CAAC,EAAE;YAC/D,MAAMC,WAAW,GAAG,IAAAC,2BAAY,EAACL,IAAI,CAAC;YACtC,IAAII,WAAW,EAAEJ,IAAI,CAACM,WAAW,CAACF,WAAW,CAAC;UAChD;QACF;MACF,CAAC;MAEDG,cAAcA,CAACP,IAAI,EAAE;QACnB,MAAMQ,KAAK,GAAGR,IAAI,CAACS,GAAG,CAAC,OAAO,CAAC;QAC/B,IAAID,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE;UACtB,MAAMC,OAAO,GAAG,IAAAN,2BAAY,EAE1BG,KAAK,EACL,KAAK,EACLf,gBACF,CAAC;UACD,IAAIkB,OAAO,EAAEH,KAAK,CAACF,WAAW,CAACK,OAAO,CAAC;QACzC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}