{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748446032426}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpsZXQgX3RoaXM7DQppbXBvcnQgZW1vamlEYXRhIGZyb20gIi4vZW1vamkuanMiOw0KaW1wb3J0IGF1ZGlvcGxheSBmcm9tICIvc3JjL2NvbXBvbmVudHMvYXVkaW9wbGF5LnZ1ZSI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJjaGF0IiwNCiAgY29tcG9uZW50czogeyBhdWRpb3BsYXkgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdXNlcnNzOiBbXSwNCiAgICAgIGx2c2hpc3M6IFtdLA0KICAgICAgeXVhbmdvbmdzczogW10sDQogICAgICB0YWJsZTogZmFsc2UsDQogICAgICBncmlkRGF0YTogIiIsDQogICAgICBydWxlRm9ybTogIiIsDQogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsDQogICAgICBlbW9qaURhdGE6IGVtb2ppRGF0YSwNCiAgICAgIGN1cnJlbnRUYWI6ICdncm91cCcsDQogICAgICBzZWxlY3RJZDogMSwNCiAgICAgIGFjdGl2ZU5hbWU6ICJmaXJzdCIsDQogICAgICBzZWFyY2g6ICIiLA0KICAgICAgYWN0aXZlOiBmYWxzZSwNCiAgICAgIGltZ1VscjogIiIsDQogICAgICB5b25faWQ6IDAsDQogICAgICBpZDogMCwNCiAgICAgIGlzU2hvd1NlYWNoOiBmYWxzZSwNCiAgICAgIHR5cGU6ICIiLA0KICAgICAgbGlzdHM6IFtdLA0KICAgICAgbGE6IGZhbHNlLA0KICAgICAgTmFtZXM6ICIiLA0KICAgICAgaXNTaG93UG9wdXA6IGZhbHNlLA0KICAgICAgdGV4dENvbnRlbnQ6ICIiLA0KICAgICAgc2VsZWN0SWQ6IDAsDQogICAgICBsdnNoaWlkOiAiNCIsDQogICAgICBwaWNfcGF0aDogIiIsDQogICAgICBmaWxlX3BhdGg6ICIiLA0KICAgICAgbGlzdDogW10sDQogICAgICB0aW1lcjogIiIsDQogICAgICB1c2VyczogW10sDQogICAgICBxdW5zOiBbXSwNCiAgICAgIHF1bGlhb0luZGV4OiAwLA0KICAgICAgcXVsaWFvczogW10sDQogICAgICBpc0Vtamk6IGZhbHNlLA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgeXVhbnNoaXF1bnM6IFtdLA0KICAgICAgeXVhbnNoaXVzZXJzOiBbXSwNCiAgICB9Ow0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZWRpdERhdGEoaWQpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBpZiAoaWQgIT0gMCkgew0KICAgICAgICB0aGlzLmdldEluZm8oaWQpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5ydWxlRm9ybSA9IHsNCiAgICAgICAgICB0aXRsZTogIiIsDQogICAgICAgICAgZGVzYzogIiIsDQogICAgICAgIH07DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTdWNjZXMxcyhyZXMpIHsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsNCiAgICAgICAgdGhpcy5ydWxlRm9ybVsiZmlsZV9wYXRoIl0gPSByZXMuZGF0YS51cmw7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0SW5mbyhpZCkgew0KICAgICAgbGV0IF90aGlzID0gdGhpczsNCiAgICAgIHRoaXMuZ2V0UmVxdWVzdCgiL2dvbmdkYW4vcmVhZD9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5ydWxlRm9ybSA9IHJlc3AuZGF0YTsNCiAgICAgICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHF1YW55dWFuKCkgew0KICAgICAgX3RoaXMubGEgPSAhX3RoaXMubGENCiAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9nZXRRdW5Nb3JlSW5mbyIsIHsgaWQ6X3RoaXMuaWQgfSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLnVzZXJzcyA9IHJlc3AuZGF0YS51c2Vycw0KICAgICAgICAgIF90aGlzLmx2c2hpc3MgPSByZXNwLmRhdGEubHZzaGlzDQogICAgICAgICAgX3RoaXMueXVhbmdvbmdzcyA9IHJlc3AuZGF0YS55dWFuZ29uZ3MNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzYXZlRGF0YSgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICB0aGlzLiRyZWZzWyJydWxlRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KCIvZ29uZ2Rhbi9zYXZlIiwgdGhpcy5ydWxlRm9ybSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZywNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOw0KICAgICAgICAgICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcC5tc2csDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzaG93Z29uZ2RhbigpIHsNCiAgICAgIGxldCB1aWQgPSB0aGlzLnF1bnNbdGhpcy5xdWxpYW9JbmRleF1bInVpZCJdOw0KICAgICAgX3RoaXMudGFibGUgPSB0cnVlOw0KICAgICAgX3RoaXMucG9zdFJlcXVlc3QoIi9jaGF0L2dvbmdkYW5MaXN0IiwgeyB1aWQ6IHVpZCB9KS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgX3RoaXMuZ3JpZERhdGEgPSByZXNwLmRhdGE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2hvd0RhaWJhbihpc19kYWliYW4pIHsNCiAgICAgIHRoaXMuY3VycmVudFRhYiA9IGlzX2RhaWJhbiA9PT0gJzInID8gJ2dyb3VwJyA6ICd0b2RvJzsNCiAgICAgIF90aGlzDQogICAgICAgIC5wb3N0UmVxdWVzdCgiL2NoYXQvZ2V0UXVuIiwgeyBpc19kYWliYW46IGlzX2RhaWJhbiB9KQ0KICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICBfdGhpcy5xdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgICAgX3RoaXMueXVhbnNoaXF1bnMgPSByZXNwLmRhdGE7DQogICAgICAgICAgICBfdGhpcy5zZWxlY3RJZCA9IC0xOw0KICAgICAgICAgICAgX3RoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBjaGFuZ2VLZXl3b3JkKGUpIHsNCiAgICAgIGxldCBxdW5zID0gX3RoaXMueXVhbnNoaXF1bnM7DQogICAgICBsZXQgdXNlcnMgPSBfdGhpcy55dWFuc2hpdXNlcnM7DQogICAgICBsZXQgc2VhcmNoID0gZS50YXJnZXQudmFsdWU7DQogICAgICANCiAgICAgIHRoaXMuaXNTaG93U2VhY2ggPSBzZWFyY2gubGVuZ3RoID4gMDsNCg0KICAgICAgX3RoaXMucXVucyA9IHF1bnMuZmlsdGVyKChkYXRhKSA9PiBkYXRhLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoLnRvTG93ZXJDYXNlKCkpKTsNCiAgICAgIF90aGlzLnVzZXJzID0gdXNlcnMuZmlsdGVyKChkYXRhKSA9PiBkYXRhLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoLnRvTG93ZXJDYXNlKCkpKTsNCiAgICB9LA0KICAgIGRhaWJhbigpIHsNCiAgICAgIGxldCBpZCA9IHRoaXMucXVuc1t0aGlzLnF1bGlhb0luZGV4XVsiaWQiXTsNCiAgICAgIGxldCBpc19kYWliYW4gPSB0aGlzLnF1bnNbdGhpcy5xdWxpYW9JbmRleF1bImlzX2RhaWJhbiJdID09IDEgPyAyIDogMTsNCiAgICAgIF90aGlzDQogICAgICAgIC5wb3N0UmVxdWVzdCgiL2NoYXQvZGFpYmFuIiwgeyBpZDogaWQsIGlzX2RhaWJhbjogaXNfZGFpYmFuIH0pDQogICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIF90aGlzLnF1bnNbdGhpcy5xdWxpYW9JbmRleF1bImlzX2RhaWJhbiJdID0gaXNfZGFpYmFuOw0KICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXNwLm1zZyk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgb3BlbkVtamkoKSB7DQogICAgICB0aGlzLmlzRW1qaSA9ICF0aGlzLmlzRW1qaTsNCiAgICAgIGNvbnNvbGUubG9nKCItLS0tLS0tLS0tLS0tLS0tLS0tLS0td3cydyIpOw0KICAgIH0sDQogICAgY2hhbmdlRmlsZShmaWVsZCkgew0KICAgICAgdGhpcy50eXBlID0gZmllbGQ7DQogICAgfSwNCiAgICBvcGVuRmlsZSh1cmwpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHVybCwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgb3BlbkltZyhpbWcpIHsNCiAgICAgIHRoaXMuaW1nVWxyID0gaW1nOw0KICAgICAgdGhpcy5pc1Nob3dQb3B1cCA9IHRydWU7DQogICAgICBjb25zb2xlLmxvZygiLS0tLS0tLS0tLSIsIGltZyk7DQogICAgfSwNCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgbGV0IHR5cGUgPSBmaWxlLnR5cGU7DQogICAgICBjb25zb2xlLmxvZyh0eXBlLCAidHlwZSIpOw0KICAgICAgaWYgKA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gImRvYyIgfHwNCiAgICAgICAgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJkb2N4IiB8fA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gInhscyIgfHwNCiAgICAgICAgIWZpbGUudHlwZS5zcGxpdCgiLyIpWzFdID09ICJwcHQiIHx8DQogICAgICAgICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAicGRmIiB8fA0KICAgICAgICAhZmlsZS50eXBlLnNwbGl0KCIvIilbMV0gPT0gInhsc3giIHx8DQogICAgICAgICFmaWxlLnR5cGUuc3BsaXQoIi8iKVsxXSA9PSAicHB0eCINCiAgICAgICkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBzaG93Q2xvc2U6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqSdkb2MsZG9jeCwgeGxzLCBwcHQsIHBkZiwgZG9jeCwgeGxzeCwgcHB0eCfmlofku7YiLA0KICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTdWNjZXNzKHJlcykgew0KICAgICAgbGV0IF90aGlzID0gdGhpczsNCiAgICAgIGNvbnNvbGUubG9nKHJlcyk7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIF90aGlzLnNlbmRJbWcocmVzLmRhdGEudXJsKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU3VjY2VzczEocmVzLCBmbGllKSB7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIF90aGlzLnNlbmRGaWxlKHJlcy5kYXRhLnVybCwgZmxpZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBzaG93Q2xvc2U6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqSdkb2MsIHhscywgcHB0LCBwZGYsIHhsc3gsIHBwdHgn5paH5Lu2IiwNCiAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlZFNlc3Npb24oaW5kZXgpIHsNCiAgICAgIHRoaXMuc2VsZWN0SWQgPSBpbmRleDsNCiAgICAgIHRoaXMucXVsaWFvSW5kZXggPSAtMTsNCiAgICAgIF90aGlzLmxhID0gZmFsc2UNCiAgICAgIF90aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIGNoYW5nZVF1bihpbmRleCkgew0KICAgICAgdGhpcy5zZWxlY3RJZCA9IC0xOw0KICAgICAgdGhpcy5xdWxpYW9JbmRleCA9IGluZGV4Ow0KICAgICAgX3RoaXMucXVuc1tpbmRleF0uY291bnQgPSAwOw0KICAgICAgX3RoaXMubGEgPSBmYWxzZQ0KICAgICAgX3RoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgZ2V0RW1vamkoaXRlbSkgew0KICAgICAgdGhpcy50ZXh0Q29udGVudCArPSBpdGVtOw0KICAgIH0sDQogICAgY2hhbmdlKGUpIHsNCiAgICAgIGlmICh0aGlzLnNlYXJjaCkgdGhpcy5pc1Nob3dTZWFjaCA9IHRydWU7DQogICAgICBlbHNlIHRoaXMuaXNTaG93U2VhY2ggPSBmYWxzZTsNCiAgICB9LA0KICAgIGRlbCgpIHsNCiAgICAgIHRoaXMuc2VhcmNoID0gIiI7DQogICAgICB0aGlzLmlzU2hvd1NlYWNoID0gZmFsc2U7DQogICAgfSwNCiAgICBoYW5kbGVTY3JvbGwoZSkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPT0gMCkgew0KICAgICAgICBjb25zb2xlLmxvZygi6L+Z6YeM5aSE55CG5Yqg6L295pu05aSaIik7DQogICAgICB9DQogICAgfSwNCiAgICBzZW5kKCkgew0KICAgICAgX3RoaXMuc2VuZE1lc3NhZ2UoX3RoaXMudGV4dENvbnRlbnQpOw0KICAgICAgX3RoaXMudGV4dENvbnRlbnQgPSAiIjsNCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICBpZiAoX3RoaXMuc2VsZWN0SWQgIT0gLTEpIHsNCiAgICAgICAgbGV0IGlkID0gX3RoaXMudXNlcnNbX3RoaXMuc2VsZWN0SWRdLmlkOw0KICAgICAgICBfdGhpcy50aXRsZSA9IF90aGlzLnVzZXJzW190aGlzLnNlbGVjdElkXS50aXRsZTsNCiAgICAgICAgDQogICAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9jaGF0TGlzdCIsIHsgdWlkOiBpZCB9KS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIGlmIChyZXNwLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBfdGhpcy5saXN0ID0gcmVzcC5kYXRhOw0KDQogICAgICAgICAgICAgIF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID0gX3RoaXMuJHJlZnMubGlzdC5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgaWQgPSBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS5pZDsNCiAgICAgICAgbGV0IGNvdW50ID0NCiAgICAgICAgICBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS51aWQubGVuZ3RoICogMSArDQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ubHZzaGlfaWQubGVuZ3RoICogMSArDQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ueXVhbmdvbmdfaWQubGVuZ3RoICogMTsNCiAgICAgICAgX3RoaXMuaWQgPSBpZDsNCiAgICAgICAgY29uc29sZS5sb2coX3RoaXMuaWQpDQogICAgICAgIA0KICAgICAgICBfdGhpcy50aXRsZSA9IF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLnRpdGxlICsgIigiICsgY291bnQgKyAiKSI7DQogICAgICAgIF90aGlzLnBvc3RSZXF1ZXN0KCIvY2hhdC9xdW5saWFvTGlzdCIsIHsgcXVuX2lkOiBpZCB9KS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIGlmIChyZXNwLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBfdGhpcy5saXN0ID0gcmVzcC5kYXRhOw0KICAgICAgICAgICAgICBfdGhpcy4kcmVmcy5saXN0LnNjcm9sbFRvcCA9IF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgX3RoaXMubGlzdCA9IFtdOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgDQogICAgICAgICAgICBzZXRUaW1lb3V0KA0KICAgICAgICAgICAgICAoKSA9PiAodGhpcy4kcmVmcy5saXN0LnNjcm9sbFRvcCA9IHRoaXMuJHJlZnMubGlzdC5zY3JvbGxIZWlnaHQpLA0KICAgICAgICAgICAgICAwDQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCiAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0TW9yZUxpc3QoKSB7DQogICAgICBpZiAoX3RoaXMuc2VsZWN0SWQgIT0gLTEpIHsNCiAgICAgICAgbGV0IHVpZCA9IF90aGlzLnVzZXJzW190aGlzLnNlbGVjdElkXS5pZDsNCiAgICAgICAgX3RoaXMudGl0bGUgPSBfdGhpcy51c2Vyc1tfdGhpcy5zZWxlY3RJZF0udGl0bGU7DQoNCiAgICAgICAgbGV0IGlkID0gMDsNCiAgICAgICAgaWYgKF90aGlzLmxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGlkID0gX3RoaXMubGlzdFtfdGhpcy5saXN0Lmxlbmd0aCAtIDFdLmlkOw0KICAgICAgICAgIF90aGlzDQogICAgICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L2dldE1vcmVRdW5MaXN0IiwgeyB1aWQ6IHVpZCwgaWQ6IGlkIH0pDQogICAgICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgICBfdGhpcy5nZXRRdW4xKCk7DQogICAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgaWYgKHJlc3AuZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICBfdGhpcy5saXN0LnB1c2gocmVzcC5kYXRhKTsNCiAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoDQogICAgICAgICAgICAgICAgICAgICgpID0+DQogICAgICAgICAgICAgICAgICAgICAgKHRoaXMuJHJlZnMubGlzdC5zY3JvbGxUb3AgPQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5saXN0LnNjcm9sbEhlaWdodCksDQogICAgICAgICAgICAgICAgICAgIDEwMDANCiAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgcXVuX2lkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0uaWQ7DQogICAgICAgIGxldCBjb3VudCA9DQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ubHZzaGlfaWQubGVuZ3RoICogMSArDQogICAgICAgICAgX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0ueXVhbmdvbmdfaWQubGVuZ3RoICogMSArDQogICAgICAgICAgMTsNCg0KICAgICAgICBfdGhpcy50aXRsZSA9IF90aGlzLnF1bnNbX3RoaXMucXVsaWFvSW5kZXhdLnRpdGxlICsgIigiICsgY291bnQgKyAiKSI7DQogICAgICAgIGxldCBpZCA9IDA7DQogICAgICAgIGlmIChfdGhpcy5saXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBpZCA9IF90aGlzLmxpc3RbX3RoaXMubGlzdC5sZW5ndGggLSAxXS5pZDsNCiAgICAgICAgICBfdGhpcw0KICAgICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9nZXRNb3JlUXVuTGlzdCIsIHsgcXVuX2lkOiBxdW5faWQsIGlkOiBpZCB9KQ0KICAgICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgICAgX3RoaXMuZ2V0UXVuMSgpOw0KICAgICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIF90aGlzLmxpc3QucHVzaChyZXNwLmRhdGEpOw0KDQogICAgICAgICAgICAgICAgc2V0VGltZW91dCgNCiAgICAgICAgICAgICAgICAgICgpID0+DQogICAgICAgICAgICAgICAgICAgIChfdGhpcy4kcmVmcy5saXN0LnNjcm9sbFRvcCA9DQogICAgICAgICAgICAgICAgICAgICAgX3RoaXMuJHJlZnMubGlzdC5zY3JvbGxIZWlnaHQpLA0KICAgICAgICAgICAgICAgICAgMTAwMA0KICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIGlkID0gMTsNCiAgICAgICAgICBfdGhpcw0KICAgICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9nZXRNb3JlUXVuTGlzdCIsIHsgcXVuX2lkOiBxdW5faWQsIGlkOiBpZCB9KQ0KICAgICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgICAgX3RoaXMuZ2V0UXVuMSgpOw0KICAgICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIF90aGlzLmxpc3QucHVzaChyZXNwLmRhdGEpOw0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KA0KICAgICAgICAgICAgICAgICAgKCkgPT4NCiAgICAgICAgICAgICAgICAgICAgKF90aGlzLiRyZWZzLmxpc3Quc2Nyb2xsVG9wID0NCiAgICAgICAgICAgICAgICAgICAgICBfdGhpcy4kcmVmcy5saXN0LnNjcm9sbEhlaWdodCksDQogICAgICAgICAgICAgICAgICAxMDAwDQogICAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2VuZE1lc3NhZ2UoY29udGVudCkgew0KICAgICAgaWYgKF90aGlzLnNlbGVjdElkICE9IC0xKSB7DQogICAgICAgIGxldCBpZCA9IF90aGlzLnVzZXJzW190aGlzLnNlbGVjdElkXS5pZDsNCiAgICAgICAgbGV0IG9ydGhlcl9pZCA9IDM7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIHVpZDogaWQsDQogICAgICAgICAgICBkaXJlY3Rpb246ICJsZWZ0IiwNCiAgICAgICAgICAgIHR5cGU6ICJ0ZXh0IiwNCiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsDQogICAgICAgICAgICBvcnRoZXJfaWQ6IG9ydGhlcl9pZCwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlICE9IDIwMCkgew0KICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdWlkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0udWlkOw0KICAgICAgICBsZXQgcXVuX2lkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0uaWQ7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kUXVuTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIGRpcmVjdGlvbjogImxlZnQiLA0KICAgICAgICAgICAgdHlwZTogInRleHQiLA0KICAgICAgICAgICAgY29udGVudDogY29udGVudCwNCiAgICAgICAgICAgIHF1bl9pZDogcXVuX2lkLA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbmRGaWxlKGNvbnRlbnQsIGZpbGVzKSB7DQogICAgICBpZiAoX3RoaXMuc2VsZWN0SWQgIT0gLTEpIHsNCiAgICAgICAgbGV0IG9ydGhlcl9pZCA9IDM7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIGRpcmVjdGlvbjogImxlZnQiLA0KICAgICAgICAgICAgdHlwZTogImZpbGUiLA0KICAgICAgICAgICAgY29udGVudDogY29udGVudCwNCiAgICAgICAgICAgIG9ydGhlcl9pZDogb3J0aGVyX2lkLA0KICAgICAgICAgICAgZmlsZXM6IGZpbGVzLA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBxdW5faWQgPSBfdGhpcy5xdW5zW190aGlzLnF1bGlhb0luZGV4XS5pZDsNCiAgICAgICAgX3RoaXMNCiAgICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L3NlbmRRdW5NZXNzYWdlIiwgew0KICAgICAgICAgICAgZGlyZWN0aW9uOiAibGVmdCIsDQogICAgICAgICAgICB0eXBlOiAiZmlsZSIsDQogICAgICAgICAgICBjb250ZW50OiBjb250ZW50LA0KICAgICAgICAgICAgcXVuX2lkOiBxdW5faWQsDQogICAgICAgICAgICBmaWxlczogZmlsZXMsDQogICAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3AuY29kZSAhPSAyMDApIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcC5tc2cpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2VuZEltZyhjb250ZW50KSB7DQogICAgICBpZiAoX3RoaXMuc2VsZWN0SWQgIT0gLTEpIHsNCiAgICAgICAgbGV0IGlkID0gX3RoaXMudXNlcnNbX3RoaXMuc2VsZWN0SWRdLmlkOw0KICAgICAgICBsZXQgb3J0aGVyX2lkID0gMzsNCiAgICAgICAgX3RoaXMNCiAgICAgICAgICAucG9zdFJlcXVlc3QoIi9jaGF0L3NlbmRNZXNzYWdlIiwgew0KICAgICAgICAgICAgdWlkOiBpZCwNCiAgICAgICAgICAgIGRpcmVjdGlvbjogImxlZnQiLA0KICAgICAgICAgICAgdHlwZTogImltYWdlIiwNCiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQsDQogICAgICAgICAgICBvcnRoZXJfaWQ6IG9ydGhlcl9pZCwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlICE9IDIwMCkgew0KICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwLm1zZyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdWlkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0udWlkOw0KICAgICAgICBsZXQgcXVuX2lkID0gX3RoaXMucXVuc1tfdGhpcy5xdWxpYW9JbmRleF0uaWQ7DQogICAgICAgIF90aGlzDQogICAgICAgICAgLnBvc3RSZXF1ZXN0KCIvY2hhdC9zZW5kUXVuTWVzc2FnZSIsIHsNCiAgICAgICAgICAgIHVpZDogdWlkLA0KICAgICAgICAgICAgZGlyZWN0aW9uOiAibGVmdCIsDQogICAgICAgICAgICB0eXBlOiAiaW1hZ2UiLA0KICAgICAgICAgICAgY29udGVudDogY29udGVudCwNCiAgICAgICAgICAgIHF1bl9pZDogcXVuX2lkLA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYXRBbGxMaXN0KCkgew0KICAgICAgX3RoaXMucG9zdFJlcXVlc3QoIi9jaGF0L2NoYXRBbGxMaXN0IikudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLnVzZXJzID0gcmVzcC5kYXRhOw0KICAgICAgICAgIF90aGlzLnl1YW5zaGl1c2VycyA9IHJlc3AuZGF0YTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRRdW4oKSB7DQogICAgICBfdGhpcy5wb3N0UmVxdWVzdCgiL2NoYXQvZ2V0UXVuIikudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLnF1bnMgPSByZXNwLmRhdGE7DQogICAgICAgICAgX3RoaXMueXVhbnNoaXF1bnMgPSByZXNwLmRhdGE7DQogICAgICAgICAgX3RoaXMuc2VsZWN0SWQgPSAtMTsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIF90aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9LCAxNTAwKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRRdW4xKCkgew0KICAgICAgX3RoaXMucG9zdFJlcXVlc3QoIi9jaGF0L2dldFF1biIpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5xdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgIF90aGlzLnl1YW5zaGlxdW5zID0gcmVzcC5kYXRhOw0KICAgICAgICAgIF90aGlzLnNlbGVjdElkID0gLTE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAga2V5dXBTdWJtaXQoKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KDQogICAgICBkb2N1bWVudC5vbmtleWRvd24gPSAoZSkgPT4gew0KICAgICAgICBsZXQgX2tleSA9IHdpbmRvdy5ldmVudC5rZXlDb2RlOw0KDQogICAgICAgIGlmIChfa2V5ID09PSAxMykgew0KICAgICAgICAgIF90aGlzLnNlbmQoKTsNCiAgICAgICAgfQ0KICAgICAgfTsNCiAgICB9LA0KICAgIGRlbEltYWdlKGZpbGUsIGZpbGVOYW1lKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgX3RoaXMuZ2V0UmVxdWVzdCgiL1VwbG9hZC9kZWxJbWFnZT9maWxlTmFtZT0iICsgZmlsZSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgIF90aGlzLnJ1bGVGb3JtW2ZpbGVOYW1lXSA9ICIiOw0KDQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfISIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICBjb25zb2xlLmxvZygi56a75byA5LmQIik7DQogICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICBfdGhpcyA9IHRoaXM7DQogICAgX3RoaXMuZ2V0UXVuKCk7DQogICAgX3RoaXMuY2hhdEFsbExpc3QoKTsNCiAgICBfdGhpcy55b25faWQgPSB3aW5kb3cuc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgic3BicyIpOw0KICAgIF90aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgX3RoaXMuZ2V0TW9yZUxpc3QoKTsNCiAgICB9LCAxNTAwKTsNCiAgICBfdGhpcy5rZXl1cFN1Ym1pdCgpOw0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings": ";AAyaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button \r\n                type=\"text\" \r\n                icon=\"el-icon-more\" \r\n                @click=\"quanyuan\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 群成员侧边栏 -->\r\n        <div class=\"member-sidebar\" :class=\"{ 'show': la }\">\r\n          <div class=\"member-content\">\r\n            <!-- 用户列表 -->\r\n            <div class=\"member-section\" v-if=\"userss.length\">\r\n              <div class=\"section-title\">用户</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-item\" v-for=\"(item, index) in userss\" :key=\"'user-' + index\">\r\n                  <div v-for=\"(value, key) in item.list\" :key=\"'user-item-' + key\" class=\"member-card\">\r\n                    <img :src=\"value.headimg\" class=\"member-avatar\"/>\r\n                    <div class=\"member-name\">{{ value.nickname }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 员工列表 -->\r\n            <div class=\"member-section\" v-for=\"(item, index) in yuangongss\" :key=\"'staff-' + index\">\r\n              <div class=\"section-title\">{{ item.zhiwei }}</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-card\" v-for=\"(value, key) in item.list\" :key=\"'staff-item-' + key\">\r\n                  <img :src=\"value.pic_path\" class=\"member-avatar\"/>\r\n                  <div class=\"member-name\">{{ value.title }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n      la: false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    quanyuan() {\r\n      _this.la = !_this.la\r\n      _this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.userss = resp.data.users\r\n          _this.lvshiss = resp.data.lvshis\r\n          _this.yuangongss = resp.data.yuangongs\r\n        }\r\n      });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 群成员侧边栏 */\r\n.member-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -300px;\r\n  width: 300px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n</style>\r\n"]}]}