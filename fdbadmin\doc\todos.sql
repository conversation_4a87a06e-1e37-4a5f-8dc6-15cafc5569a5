-- 待办事项表
CREATE TABLE `todos` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '待办事项ID',
  `title` varchar(255) NOT NULL COMMENT '待办事项标题',
  `description` text COMMENT '详细描述',
  `type` varchar(50) DEFAULT 'general' COMMENT '类型：debt-债务处理,order-订单处理,user-用户管理,system-系统任务,general-一般任务',
  `priority` varchar(20) DEFAULT 'medium' COMMENT '优先级：high-高,medium-中,low-低',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0-未完成,1-已完成,2-已取消',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给用户ID',
  `related_id` int(11) DEFAULT NULL COMMENT '关联记录ID（如债务ID、订单ID等）',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型：debt,order,user等',
  `due_date` int(11) DEFAULT NULL COMMENT '截止时间',
  `completed_at` int(11) DEFAULT NULL COMMENT '完成时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_assigned` (`assigned_to`, `status`),
  KEY `idx_type` (`type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_related` (`related_type`, `related_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='待办事项表';

-- 插入示例数据
INSERT INTO `todos` (`title`, `description`, `type`, `priority`, `status`, `assigned_to`, `related_id`, `related_type`, `due_date`, `create_time`) VALUES
('审核张三的债务申请', '需要审核张三提交的债务追讨申请，核实相关材料和证据。', 'debt', 'high', 0, 1, 1, 'debt', UNIX_TIMESTAMP() + 86400, UNIX_TIMESTAMP()),
('回复李四的法律咨询', '李四咨询关于合同纠纷的法律问题，需要专业回复。', 'general', 'medium', 0, 1, NULL, NULL, UNIX_TIMESTAMP() + 172800, UNIX_TIMESTAMP()),
('准备明天的庭审材料', '整理明天庭审需要的所有文件和证据材料。', 'general', 'high', 1, 1, NULL, NULL, UNIX_TIMESTAMP() + 43200, UNIX_TIMESTAMP() - 3600),
('更新客户联系信息', '批量更新客户的最新联系方式和地址信息。', 'user', 'low', 0, 1, NULL, NULL, UNIX_TIMESTAMP() + 259200, UNIX_TIMESTAMP()),
('整理本月财务报表', '汇总本月的收入支出情况，生成财务报表。', 'system', 'medium', 0, 1, NULL, NULL, UNIX_TIMESTAMP() + 604800, UNIX_TIMESTAMP()),
('跟进王五的订单支付', '王五的订单已超过3天未支付，需要电话跟进。', 'order', 'high', 0, 1, 5, 'order', UNIX_TIMESTAMP() + 86400, UNIX_TIMESTAMP());
