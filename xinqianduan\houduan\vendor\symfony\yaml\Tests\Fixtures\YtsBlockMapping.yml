---
test: One Element Mapping
brief: |
    A mapping with one key/value pair
yaml: |
    foo: bar
php: |
    array('foo' => 'bar')
---
test: Multi Element Mapping
brief: |
    More than one key/value pair
yaml: |
    red: baron
    white: walls
    blue: berries
php: |
    array(
     'red' => 'baron',
     'white' => 'walls',
     'blue' => 'berries',
    )
---
test: Values aligned
brief: |
    Often times human editors of documents will align the values even
    though YAML emitters generally don't.
yaml: |
    red:   baron
    white: walls
    blue:  berries
php: |
    array(
     'red' => 'baron',
     'white' => 'walls',
     'blue' => 'berries',
    )
---
test: Colons aligned
brief: |
    Spaces can come before the ': ' key/value separator.
yaml: |
    red   : baron
    white : walls
    blue  : berries
php: |
    array(
     'red' => 'baron',
     'white' => 'walls',
     'blue' => 'berries',
    )
