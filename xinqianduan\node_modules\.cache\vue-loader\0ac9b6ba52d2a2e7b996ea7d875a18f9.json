{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\File.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\File.vue", "mtime": 1748542462348}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["File.vue"], "names": [], "mappings": ";AAkJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "File.vue", "sourceRoot": "src/views/pages/archive", "sourcesContent": ["<template>\r\n  <div class=\"archive-container\">\r\n    <!-- 顶部操作栏 -->\r\n    <div class=\"operation-bar\">\r\n      <el-button-group>\r\n        <el-button type=\"primary\" @click=\"handleUpload\">\r\n          <i class=\"el-icon-upload\"></i> 上传文件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleBatchArchive\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-folder-add\"></i> 批量归档\r\n        </el-button>\r\n        <el-button type=\"warning\" @click=\"handleBatchDownload\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-download\"></i> 批量下载\r\n        </el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchDelete\" :disabled=\"!selectedFiles.length\">\r\n          <i class=\"el-icon-delete\"></i> 批量删除\r\n        </el-button>\r\n      </el-button-group>\r\n    </div>\r\n\r\n    <!-- 文件列表区域 -->\r\n    <div class=\"file-list-container\">\r\n      <el-table\r\n        :data=\"fileList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\">\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"55\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileName\"\r\n          label=\"文件名\"\r\n          min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"file-name-cell\">\r\n              <i :class=\"getFileIcon(scope.row.fileType)\"></i>\r\n              <span>{{ scope.row.fileName }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"fileType\"\r\n          label=\"类型\"\r\n          width=\"100\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"category\"\r\n          label=\"分类\"\r\n          width=\"120\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"size\"\r\n          label=\"大小\"\r\n          width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatFileSize(scope.row.size) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"uploadTime\"\r\n          label=\"上传时间\"\r\n          width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          width=\"240\"\r\n          align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handlePreview(scope.row)\">\r\n                预览\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleDownload(scope.row)\">\r\n                下载\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\">\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 文件上传对话框 -->\r\n    <el-dialog\r\n      title=\"文件上传\"\r\n      :visible.sync=\"uploadDialogVisible\"\r\n      width=\"500px\">\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        multiple\r\n        :action=\"uploadUrl\"\r\n        :before-upload=\"beforeUpload\"\r\n        :on-progress=\"handleProgress\"\r\n        :on-success=\"handleUploadSuccess\"\r\n        :on-error=\"handleUploadError\"\r\n        :file-list=\"uploadFileList\">\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          支持任意格式文件，单个文件不超过500MB\r\n        </div>\r\n      </el-upload>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"80%\"\r\n      :fullscreen=\"true\">\r\n      <div class=\"preview-container\">\r\n        <!-- 图片预览 -->\r\n        <div v-if=\"isImage\" class=\"image-preview\">\r\n          <img :src=\"previewUrl\" alt=\"预览图片\">\r\n        </div>\r\n        <!-- PDF预览 -->\r\n        <div v-else-if=\"isPdf\" class=\"pdf-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- Office文档预览 -->\r\n        <div v-else-if=\"isOffice\" class=\"office-preview\">\r\n          <iframe :src=\"previewUrl\" width=\"100%\" height=\"600px\"></iframe>\r\n        </div>\r\n        <!-- 其他文件类型 -->\r\n        <div v-else class=\"other-preview\">\r\n          <p>该文件类型暂不支持预览，请下载后查看</p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { formatFileSize, getFileType } from '@/utils/fileUtils'\r\nimport { getRequest, postRequest, deleteRequest } from '@/utils/api'\r\n\r\nexport default {\r\n  name: 'ArchiveFile',\r\n  data() {\r\n    return {\r\n      fileList: [],\r\n      selectedFiles: [],\r\n      uploadDialogVisible: false,\r\n      previewDialogVisible: false,\r\n      uploadFileList: [],\r\n      previewUrl: '',\r\n      currentFile: null,\r\n      uploadUrl: '/archive/upload',\r\n      // 文件类型映射\r\n      fileTypeMap: {\r\n        'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp'],\r\n        'pdf': ['pdf'],\r\n        'office': ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],\r\n        'text': ['txt', 'md'],\r\n        'archive': ['zip', 'rar', '7z']\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'userRole'\r\n    ]),\r\n    isImage() {\r\n      return this.currentFile && this.fileTypeMap.image.includes(this.currentFile.fileType)\r\n    },\r\n    isPdf() {\r\n      return this.currentFile && this.fileTypeMap.pdf.includes(this.currentFile.fileType)\r\n    },\r\n    isOffice() {\r\n      return this.currentFile && this.fileTypeMap.office.includes(this.currentFile.fileType)\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchFileList()\r\n  },\r\n  methods: {\r\n    // 获取文件列表\r\n    async fetchFileList() {\r\n      try {\r\n        // 模拟归档文件数据\r\n        const mockFiles = [\r\n          {\r\n            id: 1,\r\n            fileName: \"合同模板.pdf\",\r\n            fileType: \"pdf\",\r\n            category: \"合同文件\",\r\n            size: 1024000,\r\n            uploadTime: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            fileName: \"案件资料.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 512000,\r\n            uploadTime: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            fileName: \"咨询记录.txt\",\r\n            fileType: \"txt\",\r\n            category: \"咨询记录\",\r\n            size: 8192,\r\n            uploadTime: \"2024-01-13 16:45:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            fileName: \"证据材料.jpg\",\r\n            fileType: \"jpg\",\r\n            category: \"案件文书\",\r\n            size: 2048000,\r\n            uploadTime: \"2024-01-12 09:15:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            fileName: \"法律意见书.docx\",\r\n            fileType: \"docx\",\r\n            category: \"案件文书\",\r\n            size: 768000,\r\n            uploadTime: \"2024-01-11 16:30:00\"\r\n          }\r\n        ];\r\n        \r\n        this.fileList = mockFiles;\r\n        this.$message.success('文件列表加载成功');\r\n      } catch (error) {\r\n        this.$message.error('获取文件列表失败');\r\n      }\r\n    },\r\n    // 显示上传对话框\r\n    handleUpload() {\r\n      this.uploadDialogVisible = true\r\n    },\r\n    // 文件上传前检查\r\n    beforeUpload(file) {\r\n      const isLt500M = file.size / 1024 / 1024 < 500\r\n      if (!isLt500M) {\r\n        this.$message.error('文件大小不能超过 500MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    // 处理上传进度\r\n    handleProgress(event, file) {\r\n      console.log('上传进度：', file.percentage)\r\n    },\r\n    // 处理上传成功\r\n    handleUploadSuccess(response, file) {\r\n      this.$message.success('文件上传成功')\r\n      this.uploadDialogVisible = false\r\n      this.fetchFileList()\r\n    },\r\n    // 处理上传失败\r\n    handleUploadError() {\r\n      this.$message.error('文件上传失败')\r\n    },\r\n    // 处理文件预览\r\n    async handlePreview(file) {\r\n      this.currentFile = file\r\n      this.previewDialogVisible = true\r\n      // 根据文件类型生成预览URL\r\n      this.previewUrl = await this.generatePreviewUrl(file)\r\n    },\r\n    // 生成预览URL\r\n    async generatePreviewUrl(file) {\r\n      try {\r\n        const response = await getRequest(`/archive/preview/${file.id}`)\r\n        return response.data?.previewUrl || 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      } catch (error) {\r\n        return 'data:text/plain;base64,6aKE6KeI5Yqf6IO95byA5Y+R5Lit'\r\n      }\r\n    },\r\n    // 处理文件下载\r\n    async handleDownload(file) {\r\n      try {\r\n        // 创建一个虚拟的下载链接\r\n        const link = document.createElement('a')\r\n        link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n        link.download = file.fileName\r\n        link.click()\r\n        this.$message.success('开始下载')\r\n      } catch (error) {\r\n        this.$message.error('文件下载失败')\r\n      }\r\n    },\r\n    // 处理文件删除\r\n    async handleDelete(file) {\r\n      try {\r\n        await this.$confirm('确认删除该文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        await deleteRequest(`/archive/files/${file.id}`)\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 处理批量操作\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection\r\n    },\r\n    // 批量归档\r\n    async handleBatchArchive() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认归档选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await postRequest('/archive/files/batch/archive', { fileIds })\r\n        this.$message.success('归档成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('归档失败')\r\n        }\r\n      }\r\n    },\r\n    // 批量下载\r\n    async handleBatchDownload() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        this.$message.success('开始批量下载')\r\n        // 模拟批量下载\r\n        this.selectedFiles.forEach(file => {\r\n          const link = document.createElement('a')\r\n          link.href = `data:application/octet-stream;base64,${btoa(file.fileName)}`\r\n          link.download = file.fileName\r\n          link.click()\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('下载失败')\r\n      }\r\n    },\r\n    // 批量删除\r\n    async handleBatchDelete() {\r\n      if (!this.selectedFiles.length) return\r\n      try {\r\n        await this.$confirm('确认删除选中的文件吗？', '提示', {\r\n          type: 'warning'\r\n        })\r\n        const fileIds = this.selectedFiles.map(file => file.id)\r\n        await deleteRequest('/archive/files/batch', { fileIds })\r\n        this.$message.success('删除成功')\r\n        this.fetchFileList()\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }\r\n    },\r\n    // 获取文件图标\r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'ppt': 'el-icon-document',\r\n        'pptx': 'el-icon-document',\r\n        'txt': 'el-icon-document',\r\n        'image': 'el-icon-picture',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n    // 格式化文件大小\r\n    formatFileSize(size) {\r\n      return formatFileSize(size)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-container {\r\n  padding: 20px;\r\n\r\n  .operation-bar {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .file-list-container {\r\n    background: #fff;\r\n    padding: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n    \r\n    .el-table {\r\n      border: 1px solid #ebeef5;\r\n      border-radius: 4px;\r\n      \r\n      .el-table__header-wrapper {\r\n        .el-table__header {\r\n          th {\r\n            background-color: #fafafa;\r\n            color: #606266;\r\n            font-weight: 600;\r\n            border-bottom: 1px solid #ebeef5;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .el-table__body-wrapper {\r\n        .el-table__body {\r\n          tr {\r\n            &:hover {\r\n              background-color: #f5f7fa;\r\n            }\r\n            \r\n            td {\r\n              border-bottom: 1px solid #f0f0f0;\r\n              padding: 12px 0;\r\n              vertical-align: middle;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .file-name-cell {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n    \r\n    .el-button {\r\n      margin: 0;\r\n      min-width: 60px;\r\n      height: 28px;\r\n      font-size: 12px;\r\n      padding: 5px 12px;\r\n      border-radius: 4px;\r\n      \r\n      &.el-button--mini {\r\n        padding: 5px 12px;\r\n      }\r\n    }\r\n    \r\n    .el-button + .el-button {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n\r\n  .preview-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 400px;\r\n\r\n    .image-preview {\r\n      img {\r\n        max-width: 100%;\r\n        max-height: 600px;\r\n      }\r\n    }\r\n\r\n    .pdf-preview, .office-preview {\r\n      width: 100%;\r\n      height: 600px;\r\n    }\r\n\r\n    .other-preview {\r\n      text-align: center;\r\n      color: #909399;\r\n    }\r\n  }\r\n}\r\n</style> "]}]}