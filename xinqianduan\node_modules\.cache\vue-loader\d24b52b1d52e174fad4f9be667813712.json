{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\UserDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\components\\UserDetail.vue", "mtime": 1748442914238}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["UserDetail.vue"], "names": [], "mappings": ";AA2FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "UserDetail.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-descriptions title=\"客户信息\">\r\n        <el-descriptions-item label=\"公司名称\">{{\r\n          info.company\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{\r\n          info.phone\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"名称\">{{\r\n          info.nickname\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人\">{{\r\n          info.linkman\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"头像\">\r\n          <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n               :src=\"info.headimg\"\r\n               style=\"width: 50px; height: 50px;\"\r\n               @click=\"showImage(info.headimg)\"\r\n          /></el-descriptions-item>\r\n        <el-descriptions-item label=\"用户来源\">{{\r\n          info.yuangong_id\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系方式\">{{\r\n          info.linkphone\r\n          }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"调解员\">{{\r\n              info.tiaojie_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"法务专员\">{{\r\n              info.fawu_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"立案专员\">{{\r\n              info.lian_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"合同上传专用\">{{\r\n              info.htsczy_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"律师\">{{\r\n              info.ls_name\r\n              }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务员\">{{\r\n              info.ywy_name\r\n              }}\r\n          </el-descriptions-item>\r\n        <el-descriptions-item label=\"营业执照\">\r\n          <img v-if=\"info.license !='' && info.license!=null\"\r\n               :src=\"info.license\"\r\n               style=\"width: 50px; height: 50px;\"\r\n               @click=\"showImage(info.license)\"\r\n          />\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"开始时间\">{{\r\n          info.start_time\r\n          }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"会员年限\">{{\r\n          info.year\r\n          }}年</el-descriptions-item>\r\n      </el-descriptions>\r\n        <el-descriptions title=\"债务人信息\" :colon=\"false\">\r\n            <el-descriptions-item>\r\n                <el-table\r\n                        :data=\"info.debts\"\r\n                        style=\"width: 100%; margin-top: 10px\"\r\n                        v-loading=\"loading\"\r\n                        size=\"mini\"\r\n                >\r\n                    <el-table-column prop=\"name\" label=\"债务人姓名\"> </el-table-column>\r\n                    <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n                    <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n                    <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n                </el-table></el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n    </el-row>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"13800138002\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      }\r\n    }\r\n  }\r\n</script>\r\n"]}]}