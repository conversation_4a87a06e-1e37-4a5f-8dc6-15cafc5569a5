{"version": 3, "sources": ["webpack:///./src/views/pages/taocan/taocan.vue?3ea5", "webpack:///./src/views/pages/taocan/taocan.vue", "webpack:///src/views/pages/taocan/taocan.vue", "webpack:///./src/views/pages/taocan/taocan.vue?ee97", "webpack:///./src/views/pages/taocan/taocan.vue?8cf4"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticStyle", "allSize", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "on", "$event", "getData", "slot", "staticClass", "editData", "_v", "directives", "name", "rawName", "loading", "tableData", "scopedSlots", "_u", "key", "fn", "scope", "row", "id", "nativeOn", "preventDefault", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "title", "price", "year", "good", "_l", "types", "item", "index", "_s", "is_num", "_e", "desc", "sort", "saveData", "staticRenderFns", "components", "data", "page", "num", "required", "message", "trigger", "url", "mounted", "methods", "_this", "getInfo", "getTypes", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "postRequest", "count", "val", "for<PERSON>ach", "element", "length", "push", "$refs", "validate", "valid", "msg", "component"], "mappings": "yIAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,OAAS,WAAW,CAACF,EAAG,SAAS,CAACG,YAAY,CAAC,MAAQ,UAAU,CAACH,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,QAAQ,KAAOJ,EAAIM,SAASC,MAAM,CAACC,MAAOR,EAAIS,OAAOC,QAASC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAIS,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAACZ,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,SAAS,KAAO,kBAAkBW,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOhB,EAAIiB,YAAYC,KAAK,YAAY,IAAI,GAAGhB,EAAG,SAAS,CAACiB,YAAY,YAAY,CAACjB,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAOJ,EAAIM,SAASS,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOhB,EAAIoB,SAAS,MAAM,CAACpB,EAAIqB,GAAG,QAAQnB,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAOJ,EAAIM,SAASS,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOhB,EAAIiB,aAAa,CAACjB,EAAIqB,GAAG,SAAS,GAAGnB,EAAG,WAAW,CAACoB,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYhB,MAAOR,EAAIyB,QAASX,WAAW,YAAYK,YAAY,QAAQf,MAAM,CAAC,KAAOJ,EAAI0B,UAAU,KAAO1B,EAAIM,UAAU,CAACJ,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,cAAc,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAMuB,YAAY3B,EAAI4B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC7B,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASW,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOhB,EAAIoB,SAASW,EAAMC,IAAIC,OAAO,CAACjC,EAAIqB,GAAG,QAAQnB,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAAS8B,SAAS,CAAC,MAAQ,SAASlB,GAAgC,OAAxBA,EAAOmB,iBAAwBnC,EAAIoC,QAAQL,EAAMM,OAAQN,EAAMC,IAAIC,OAAO,CAACjC,EAAIqB,GAAG,kBAAkB,GAAGnB,EAAG,MAAM,CAACiB,YAAY,YAAY,CAACjB,EAAG,gBAAgB,CAACE,MAAM,CAAC,aAAa,CAAC,GAAI,IAAK,IAAK,IAAK,KAAK,YAAYJ,EAAIsC,KAAK,OAAS,0CAA0C,MAAQtC,EAAIuC,OAAOxB,GAAG,CAAC,cAAcf,EAAIwC,iBAAiB,iBAAiBxC,EAAIyC,wBAAwB,IAAI,GAAGvC,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI0C,kBAAkB,wBAAuB,GAAO3B,GAAG,CAAC,iBAAiB,SAASC,GAAQhB,EAAI0C,kBAAkB1B,KAAU,CAACd,EAAG,UAAU,CAACyC,IAAI,WAAWvC,MAAM,CAAC,MAAQJ,EAAI4C,SAAS,MAAQ5C,EAAI6C,QAAQ,CAAC3C,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAI8C,eAAe,KAAO,UAAU,CAAC5C,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,OAAOG,MAAM,CAACC,MAAOR,EAAI4C,SAASG,MAAOpC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,QAAShC,IAAME,WAAW,qBAAqB,GAAGZ,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAI8C,eAAe,KAAO,UAAU,CAAC5C,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,KAAO,UAAUG,MAAM,CAACC,MAAOR,EAAI4C,SAASI,MAAOrC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,QAAShC,IAAME,WAAW,qBAAqB,GAAGZ,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,KAAK,cAAcJ,EAAI8C,eAAe,KAAO,SAAS,CAAC5C,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,KAAO,UAAUG,MAAM,CAACC,MAAOR,EAAI4C,SAASK,KAAMtC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,OAAQhC,IAAME,WAAW,oBAAoB,GAAGZ,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAI8C,eAAe,KAAO,SAAS,CAAC5C,EAAG,oBAAoB,CAACK,MAAM,CAACC,MAAOR,EAAI4C,SAASM,KAAMvC,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,OAAQhC,IAAME,WAAW,kBAAkBd,EAAImD,GAAInD,EAAIoD,OAAO,SAASC,EAAKC,GAAO,OAAOpD,EAAG,SAAS,CAAC2B,IAAIyB,EAAMjD,YAAY,CAAC,QAAU,SAAS,CAACH,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQiD,EAAKpB,KAAK,CAACjC,EAAIqB,GAAG,IAAIrB,EAAIuD,GAAGF,EAAKN,OAAO,QAAQ,GAAG7C,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAAiB,GAAfiD,EAAKG,OAAatD,EAAG,kBAAkB,CAACE,MAAM,CAAC,IAAM,EAAE,IAAM,IAAI,KAAO,OAAO,MAAQ,QAAQG,MAAM,CAACC,MAAO6C,EAAK7C,MAAOG,SAAS,SAAUC,GAAMZ,EAAIa,KAAKwC,EAAM,QAASzC,IAAME,WAAW,gBAAgBd,EAAIyD,MAAM,IAAI,MAAK,IAAI,GAAGvD,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,cAAcJ,EAAI8C,iBAAiB,CAAC5C,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,OAAOG,MAAM,CAACC,MAAOR,EAAI4C,SAASc,KAAM/C,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,OAAQhC,IAAME,WAAW,oBAAoB,GAAGZ,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,KAAK,cAAcJ,EAAI8C,iBAAiB,CAAC5C,EAAG,WAAW,CAACE,MAAM,CAAC,aAAe,MAAM,KAAO,UAAUG,MAAM,CAACC,MAAOR,EAAI4C,SAASe,KAAMhD,SAAS,SAAUC,GAAMZ,EAAIa,KAAKb,EAAI4C,SAAU,OAAQhC,IAAME,WAAW,oBAAoB,IAAI,GAAGZ,EAAG,MAAM,CAACiB,YAAY,gBAAgBf,MAAM,CAAC,KAAO,UAAUc,KAAK,UAAU,CAAChB,EAAG,YAAY,CAACa,GAAG,CAAC,MAAQ,SAASC,GAAQhB,EAAI0C,mBAAoB,KAAS,CAAC1C,EAAIqB,GAAG,SAASnB,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWW,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOhB,EAAI4D,cAAc,CAAC5D,EAAIqB,GAAG,UAAU,IAAI,IAAI,IAErkJwC,EAAkB,GC+IP,G,UAAA,CACftC,KAAA,OACAuC,WAAA,GACAC,OACA,OACAzD,QAAA,OACAoB,UAAA,GACAD,SAAA,EACAc,MAAA,EACAyB,KAAA,EACA1B,KAAA,GACA7B,OAAA,CACAC,QAAA,IAEAkC,SAAA,CACAG,MAAA,GACAC,MAAA,GACAC,KAAA,GACAS,KAAA,GACAC,KAAA,EACAT,KAAA,GACAe,IAAA,IAEAA,IAAA,EACApB,MAAA,CACAE,MAAA,CACA,CACAmB,UAAA,EACAC,QAAA,QACAC,QAAA,SAGApB,MAAA,CACA,CACAkB,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAnB,KAAA,CACA,CACAiB,UAAA,EACAC,QAAA,QACAC,QAAA,UAIA1B,mBAAA,EACAI,eAAA,OACAuB,IAAA,WACAjB,MAAA,KAGAkB,UACA,KAAArD,WAEAsD,QAAA,CACAnD,SAAAa,GACA,IAAAuC,EAAA,KACA,GAAAvC,EACA,KAAAwC,QAAAxC,IAEA,KAAAW,SAAA,CACAG,MAAA,GACAC,MAAA,GACAC,KAAA,GACAS,KAAA,GACAC,KAAA,EACAT,KAAA,GACAe,IAAA,IAEAO,EAAAE,YAEAF,EAAA9B,mBAAA,GAEA+B,QAAAxC,GACA,IAAAuC,EAAA,KACAA,EAAAG,WAAAH,EAAAH,IAAA,WAAApC,GAAA2C,KAAAC,IACAA,IACAL,EAAA5B,SAAAiC,EAAAd,KACAS,EAAApB,MAAAoB,EAAA5B,SAAAqB,QAIA7B,QAAAkB,EAAArB,GACA,KAAA6C,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,KAAAM,cAAA,KAAAb,IAAA,aAAApC,GAAA2C,KAAAC,IACA,KAAAA,EAAAM,OACA,KAAAC,SAAA,CACAH,KAAA,UACAd,QAAA,UAEA,KAAAzC,UAAA2D,OAAA/B,EAAA,QAIAgC,MAAA,KACA,KAAAF,SAAA,CACAH,KAAA,QACAd,QAAA,aAIAO,WACA,KAAAa,YAAA,oBAAAX,KAAAC,IACA,KAAAA,EAAAM,OACA,KAAA/B,MAAAyB,EAAAd,SAIA9C,UACA,IAAAuD,EAAA,KACAA,EAAA/C,SAAA,EACA+C,EACAe,YACAf,EAAAH,IAAA,cAAAG,EAAAR,KAAA,SAAAQ,EAAAlC,KACAkC,EAAA/D,QAEAmE,KAAAC,IACA,KAAAA,EAAAM,OACAX,EAAA9C,UAAAmD,EAAAd,KACAS,EAAAjC,MAAAsC,EAAAW,OAEAhB,EAAA/C,SAAA,KAGAe,iBAAAiD,GACA,KAAAnD,KAAAmD,EACA,KAAAxE,WAEAwB,oBAAAgD,GACA,KAAAzB,KAAAyB,EACA,KAAAxE,WAEA2C,WACA,IAAAY,EAAA,KACAtB,EAAA,KAAAN,SAAAM,KACAE,EAAA,GACA,KAAAA,MAAAsC,QAAAC,IACA,QAAArC,EAAA,EAAAA,EAAAJ,EAAA0C,OAAAtC,IAAA,CACA,MAAArB,EAAAiB,EAAAI,GACAqC,EAAA1D,OACAmB,EAAAyC,KAAAF,MAIA,KAAA/C,SAAAqB,IAAAb,EACA,KAAA0C,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAWA,SAVA,KAAAT,YAAAf,EAAAH,IAAA,YAAAzB,UAAAgC,KAAAC,IACA,KAAAA,EAAAM,OACAX,EAAAY,SAAA,CACAH,KAAA,UACAd,QAAAU,EAAAoB,MAEAzB,EAAA9B,mBAAA,WCjT6W,I,wBCQzWwD,EAAY,eACd,EACAnG,EACA8D,GACA,EACA,KACA,WACA,MAIa,aAAAqC,E", "file": "js/chunk-a540ec18.d48266f9.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=style&index=0&id=0e653135&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-card',{attrs:{\"shadow\":\"always\"}},[_c('el-row',{staticStyle:{\"width\":\"600px\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入内容\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.getData()}},slot:\"append\"})],1)],1),_c('el-row',{staticClass:\"page-top\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\"新增\")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\"刷新\")])],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"table\",attrs:{\"data\":_vm.tableData,\"size\":_vm.allSize}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"标题\"}}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"描述\"}}),_c('el-table-column',{attrs:{\"prop\":\"price\",\"label\":\"价格\"}}),_c('el-table-column',{attrs:{\"prop\":\"year\",\"label\":\"年份\"}}),_c('el-table-column',{attrs:{\"prop\":\"sort\",\"label\":\"排序\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建日期\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"编辑\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 移除 \")])]}}])})],1),_c('div',{staticClass:\"page-top\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 100, 200, 300, 400],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":\"详情内容\",\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"套餐名称\",\"label-width\":_vm.formLabelWidth,\"prop\":\"title\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"套餐价格\",\"label-width\":_vm.formLabelWidth,\"prop\":\"price\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"number\"},model:{value:(_vm.ruleForm.price),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"price\", $$v)},expression:\"ruleForm.price\"}})],1),_c('el-form-item',{attrs:{\"label\":\"年份\",\"label-width\":_vm.formLabelWidth,\"prop\":\"year\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"number\"},model:{value:(_vm.ruleForm.year),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"year\", $$v)},expression:\"ruleForm.year\"}})],1),_c('el-form-item',{attrs:{\"label\":\"套餐内容\",\"label-width\":_vm.formLabelWidth,\"prop\":\"good\"}},[_c('el-checkbox-group',{model:{value:(_vm.ruleForm.good),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"good\", $$v)},expression:\"ruleForm.good\"}},_vm._l((_vm.types),function(item,index){return _c('el-row',{key:index,staticStyle:{\"display\":\"flex\"}},[_c('el-col',{attrs:{\"span\":16}},[_c('el-checkbox',{attrs:{\"label\":item.id}},[_vm._v(\" \"+_vm._s(item.title)+\" \")])],1),_c('el-col',{attrs:{\"span\":8}},[(item.is_num == 1)?_c('el-input-number',{attrs:{\"min\":1,\"max\":999,\"size\":\"mini\",\"label\":\"描述文字\"},model:{value:(item.value),callback:function ($$v) {_vm.$set(item, \"value\", $$v)},expression:\"item.value\"}}):_vm._e()],1)],1)}),1)],1),_c('el-form-item',{attrs:{\"label\":\"套餐描述\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"排序\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"number\"},model:{value:(_vm.ruleForm.sort),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"sort\", $$v)},expression:\"ruleForm.sort\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <el-row style=\"width: 600px\">\r\n        <el-input\r\n          placeholder=\"请输入内容\"\r\n          v-model=\"search.keyword\"\r\n          :size=\"allSize\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n        <el-button type=\"success\" @click=\"getData()\" :size=\"allSize\"\r\n          >刷新</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"tableData\"\r\n        class=\"table\"\r\n        v-loading=\"loading\"\r\n        :size=\"allSize\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"描述\"> </el-table-column>\r\n        <el-table-column prop=\"price\" label=\"价格\"> </el-table-column>\r\n        <el-table-column prop=\"year\" label=\"年份\"> </el-table-column>\r\n        <el-table-column prop=\"sort\" label=\"排序\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"创建日期\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      title=\"详情内容\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"套餐名称\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"套餐价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"price\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"年份\" :label-width=\"formLabelWidth\" prop=\"year\">\r\n          <el-input\r\n            v-model=\"ruleForm.year\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"套餐内容\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"good\"\r\n        >\r\n          <el-checkbox-group v-model=\"ruleForm.good\">\r\n            <el-row\r\n              v-for=\"(item, index) in types\"\r\n              style=\"display: flex\"\r\n              :key=\"index\"\r\n            >\r\n              <el-col :span=\"16\">\r\n                <el-checkbox :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                  size=\"mini\"\r\n                  label=\"描述文字\"\r\n                  v-if=\"item.is_num == 1\"\r\n                ></el-input-number>\r\n              </el-col>\r\n            </el-row>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"套餐描述\" :label-width=\"formLabelWidth\">\r\n          <el-input v-model=\"ruleForm.desc\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.sort\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年份\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"80px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      let good = this.ruleForm.good;\r\n      let types = [];\r\n      this.types.forEach((element) => {\r\n        for (let index = 0; index < good.length; index++) {\r\n          const id = good[index];\r\n          if (element.id == id) {\r\n            types.push(element);\r\n          }\r\n        }\r\n      });\r\n      this.ruleForm.num = types;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              _this.dialogFormVisible = false;\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.table {\r\n  width: 100%;\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taocan.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./taocan.vue?vue&type=template&id=0e653135&scoped=true\"\nimport script from \"./taocan.vue?vue&type=script&lang=js\"\nexport * from \"./taocan.vue?vue&type=script&lang=js\"\nimport style0 from \"./taocan.vue?vue&type=style&index=0&id=0e653135&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0e653135\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}