{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\Home.vue", "mtime": 1748281481455}, {"path": "H:\\fdbfront\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748278548153}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748278552176}, {"path": "H:\\fdbfront\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748278549571}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AAmOA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <el-container class=\"cont\">\r\n    <el-header class=\"top-header\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"header-left\">\r\n        <span class=\"logo\">{{ name }}</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <el-menu\r\n          class=\"top-menu\"\r\n          @select=\"menuClick\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#001529\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n        >\r\n          <el-menu-item index=\"/\">首页</el-menu-item>\r\n          <el-submenu v-for=\"(item, index) in menus\" :key=\"index\" :index=\"item.path\">\r\n            <template slot=\"title\">{{ item.name }}</template>\r\n            <el-menu-item\r\n              v-for=\"(child, indexj) in item.children\"\r\n              :key=\"indexj\"\r\n              :index=\"child.path\"\r\n            >\r\n              {{ child.name }}\r\n            </el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <el-dropdown trigger=\"click\">\r\n          <span class=\"user-info\">管理员</span>\r\n          <el-dropdown-menu>\r\n            <el-dropdown-item\r\n              ><div @click=\"menuClick('/changePwd')\">\r\n                修改密码\r\n              </div></el-dropdown-item\r\n            >\r\n            <el-dropdown-item>\r\n              <div @click=\"logout()\">退出登录</div>\r\n            </el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n\r\n    <el-container class=\"content-container\">\r\n      <el-header class=\"breadcrumb-header\">\r\n        <el-breadcrumb separator=\"/\">\r\n          <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n          <el-breadcrumb-item>{{\r\n            this.$router.currentRoute.name\r\n          }}</el-breadcrumb-item>\r\n        </el-breadcrumb>\r\n      </el-header>\r\n\r\n      <el-main class=\"main-content\">\r\n        <el-row :gutter=\"12\" v-if=\"this.$router.currentRoute.path == '/'\">\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\"> 访问量 {{ visit_count }}</el-card>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-card shadow=\"always\">\r\n              <span @click=\"showQrcode\">查看二维码</span></el-card\r\n            >\r\n          </el-col>\r\n        </el-row>\r\n        <router-view></router-view>\r\n      </el-main>\r\n    </el-container>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"25%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Home\",\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      money_count: 0,\r\n      user_count: 0,\r\n      visit_count: 1234, // 演示数据\r\n      search_count: 0,\r\n      export_count: 0,\r\n      order_count: 0,\r\n      gaode_count: 0,\r\n      tengxun_count: 0,\r\n      baidu_count: 0,\r\n      shunqiwang_count: 0,\r\n      show_image: \"\",\r\n      menus: [],\r\n      url: \"/Yuangong/\",\r\n    };\r\n  },\r\n  computed: {\r\n    name() {\r\n      return this.$store.getters.GET_TITLE;\r\n    },\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 直接提供菜单数据\r\n    this.menus = [\r\n      {\r\n        path: \"/jichu\",\r\n        name: \"基础管理\",\r\n        children: [\r\n          { path: \"/config\", name: \"基础设置\" },\r\n          { path: \"/banner\", name: \"轮播图\" },\r\n          { path: \"/nav\", name: \"首页导航\" },\r\n          { path: \"/gonggao\", name: \"公告\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xiadan\",\r\n        name: \"订单管理\",\r\n        children: [\r\n          { path: \"/type\", name: \"服务类型\" },\r\n          { path: \"/taocan\", name: \"套餐类型\" },\r\n          { path: \"/dingdan\", name: \"签约用户列表\" },\r\n          { path: \"/qun\", name: \"签约客户群\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yonghu\",\r\n        name: \"用户管理\",\r\n        children: [\r\n          { path: \"/user\", name: \"用户列表\" },\r\n          { path: \"/order\", name: \"支付列表\" },\r\n          { path: \"/chat\", name: \"聊天列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/debt\",\r\n        name: \"债权管理\",\r\n        children: [\r\n          { path: \"/debts\", name: \"债务人列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/wenshuguanli\",\r\n        name: \"文书管理\",\r\n        children: [\r\n          { path: \"/dingzhi\", name: \"合同定制\" },\r\n          { path: \"/shenhe\", name: \"合同审核\" },\r\n          { path: \"/cate\", name: \"合同类型\" },\r\n          { path: \"/hetong\", name: \"合同列表\" },\r\n          { path: \"/lawyer\", name: \"发律师函\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/yuangong\",\r\n        name: \"员工管理\",\r\n        children: [\r\n          { path: \"/zhiwei\", name: \"职位\" },\r\n          { path: \"/yuangong\", name: \"员工\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/shipin\",\r\n        name: \"视频管理\",\r\n        children: [\r\n          { path: \"/kecheng\", name: \"课程列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/fuwu\",\r\n        name: \"服务管理\",\r\n        children: [\r\n          { path: \"/fuwu\", name: \"服务列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/xinwen\",\r\n        name: \"案例管理\",\r\n        children: [\r\n          { path: \"/anli\", name: \"案例列表\" }\r\n        ]\r\n      },\r\n      {\r\n        path: \"/lvshiguanli\",\r\n        name: \"律师管理\",\r\n        children: [\r\n          { path: \"/lvshi\", name: \"律师列表\" },\r\n          { path: \"/zhuanye\", name: \"专业列表\" }\r\n        ]\r\n      }\r\n    ];\r\n\r\n    console.log(\"菜单数据已加载:\", this.menus);\r\n  },\r\n  methods: {\r\n    showQrcode() {\r\n      // 纯前端模式 - 显示演示二维码\r\n      this.show_image = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij7mj7Lnpb7kuoznu7TnoIE8L3RleHQ+PC9zdmc+\";\r\n      this.dialogVisible = true;\r\n    },\r\n    menuClick(index) {\r\n      this.$router.push(index);\r\n    },\r\n    getQuanxian() {\r\n      // 纯前端模式 - 不需要后端权限验证\r\n      console.log(\"纯前端模式，跳过权限验证\");\r\n    },\r\n    getCountAll() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式，使用演示数据\");\r\n    },\r\n    logout() {\r\n      this.$store.commit(\"INIT_TOKEN\", \"\");\r\n      this.$store.commit(\"INIT_TITLE\", \"\");\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"退出成功\",\r\n      });\r\n      let _this = this;\r\n      setTimeout(function () {\r\n        _this.$router.push(\"/login\");\r\n      }, 1500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.cont {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  flex-direction: column;\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.top-header {\r\n  height: 60px;\r\n  background-color: #001529;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.header-left {\r\n  flex: 0 0 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n.logo {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-menu {\r\n  border: none !important;\r\n  background-color: transparent !important;\r\n  width: 100%;\r\n  min-width: 0;\r\n}\r\n\r\n/* 强制水平排列 */\r\n.top-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.top-menu >>> .el-menu {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n}\r\n\r\n.top-menu >>> .el-menu-item,\r\n.top-menu >>> .el-submenu {\r\n  border-bottom: none !important;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  white-space: nowrap;\r\n  flex: 0 0 auto;\r\n  display: inline-flex !important;\r\n  align-items: center;\r\n  float: none !important;\r\n}\r\n\r\n/* 确保Element UI的默认样式被覆盖 */\r\n.el-menu--horizontal {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n}\r\n\r\n.el-menu--horizontal .el-menu-item,\r\n.el-menu--horizontal .el-submenu {\r\n  float: none !important;\r\n  display: inline-flex !important;\r\n}\r\n\r\n.top-menu .el-submenu__title {\r\n  height: 60px;\r\n  line-height: 60px;\r\n  padding: 0 15px;\r\n  border-bottom: none !important;\r\n}\r\n\r\n/* 强制子菜单垂直排列 - 最高优先级 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item:hover {\r\n  background-color: #f5f5f5 !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down {\r\n  position: absolute !important;\r\n  top: 100% !important;\r\n  left: 0 !important;\r\n  z-index: 1000 !important;\r\n  min-width: 160px !important;\r\n  background: #fff !important;\r\n  border: 1px solid #e4e7ed !important;\r\n  border-radius: 4px !important;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;\r\n}\r\n\r\n/* 终极解决方案 - 强制所有子菜单垂直排列 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 使用更高优先级的选择器 */\r\n.el-menu.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n}\r\n\r\n/* 针对所有可能的子菜单容器 */\r\n.el-submenu__drop-down .el-menu-item,\r\n.el-submenu .el-submenu__drop-down .el-menu-item,\r\n.el-menu--horizontal .el-submenu__drop-down .el-menu-item {\r\n  display: block !important;\r\n  float: none !important;\r\n  width: 100% !important;\r\n  clear: both !important;\r\n  margin: 0 !important;\r\n  background-color: #fff !important;\r\n  color: #333 !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  padding: 0 20px !important;\r\n  text-align: left !important;\r\n  border-bottom: 1px solid #f0f0f0 !important;\r\n}\r\n\r\n/* 覆盖任何可能的水平布局 */\r\n.el-menu--horizontal .el-submenu .el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.el-submenu__drop-down .el-menu {\r\n  display: block !important;\r\n  flex-direction: column !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.header-right {\r\n  flex: 0 0 150px;\r\n  min-width: 150px;\r\n  text-align: right;\r\n}\r\n\r\n.user-info {\r\n  color: #fff;\r\n  cursor: pointer;\r\n  padding: 0 15px;\r\n}\r\n\r\n.user-info:hover {\r\n  color: #ffd04b;\r\n}\r\n\r\n/* 面包屑导航样式 */\r\n.breadcrumb-header {\r\n  height: 50px;\r\n  background-color: #f5f5f5;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-breadcrumb {\r\n  line-height: 50px;\r\n}\r\n\r\n/* 主内容区域样式 */\r\n.main-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f0f2f5;\r\n  padding: 20px;\r\n  height: calc(100vh - 110px); /* 减去顶部导航和面包屑的高度 */\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .top-header {\r\n    flex-direction: column;\r\n    height: auto;\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-center {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .top-menu {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 移除原有的侧边栏样式 */\r\n.size {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.homeRouterView {\r\n  margin-top: 10px;\r\n}\r\n</style>\r\n"]}]}