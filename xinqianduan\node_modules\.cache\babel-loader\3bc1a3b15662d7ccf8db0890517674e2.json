{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\zhuanye.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\lvshi\\zhuanye.vue", "mtime": 1748481212188}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "is_num", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "computed", "activeSpecialties", "filter", "item", "status", "length", "mounted", "getData", "methods", "editData", "id", "_this", "getInfo", "desc", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "postRequest", "count", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSortChange", "column", "console", "log", "getStatusType", "row", "getStatusText", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success"], "sources": ["src/views/pages/lvshi/zhuanye.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-medal\"></i>\r\n            {{ this.$router.currentRoute.name }}\r\n          </h2>\r\n          <div class=\"page-subtitle\">管理律师专业领域分类</div>\r\n        </div>\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 统计信息卡片 -->\r\n      <div class=\"stats-section\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-medal\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ total }}</div>\r\n            <div class=\"stat-label\">专业领域</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon active\">\r\n            <i class=\"el-icon-check\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ activeSpecialties }}</div>\r\n            <div class=\"stat-label\">活跃专业</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"search-section\">\r\n        <div class=\"search-controls\">\r\n          <el-input\r\n            placeholder=\"请输入专业名称进行搜索\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <el-button\r\n              slot=\"append\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n            ></el-button>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"action-controls\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n          >\r\n            新增专业\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 数据表格 -->\r\n      <div class=\"table-section\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"data-table\"\r\n          stripe\r\n          @sort-change=\"handleSortChange\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"专业名称\" min-width=\"200\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"specialty-title-cell\">\r\n                <div class=\"specialty-icon\">\r\n                  <i class=\"el-icon-medal\"></i>\r\n                </div>\r\n                <div class=\"specialty-info\">\r\n                  <div class=\"specialty-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"specialty-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"专业描述\" min-width=\"300\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"specialty-description\">\r\n                <span v-if=\"scope.row.desc\">{{ scope.row.desc }}</span>\r\n                <span v-else class=\"no-desc\">暂无描述</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"getStatusType(scope.row)\"\r\n                size=\"small\"\r\n                class=\"status-tag\"\r\n              >\r\n                {{ getStatusText(scope.row) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/zhuanye/\",\r\n      title: \"专业\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 活跃专业数量（假设有状态字段）\r\n    activeSpecialties() {\r\n      return this.list.filter(item => item.status !== 0).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSortChange(column) {\r\n      // 处理排序变化\r\n      console.log('排序变化:', column);\r\n    },\r\n    getStatusType(row) {\r\n      // 根据状态返回标签类型\r\n      if (row.status === 0) return 'info';\r\n      return 'success';\r\n    },\r\n    getStatusText(row) {\r\n      // 根据状态返回文本\r\n      if (row.status === 0) return '停用';\r\n      return '正常';\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0 0 8px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #1890ff;\r\n  font-size: 22px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 统计信息卡片 */\r\n.stats-section {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  min-width: 200px;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\r\n}\r\n\r\n.stat-card:nth-child(2) {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  box-shadow: 0 4px 12px rgba(245, 87, 108, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 搜索和操作区域 */\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  gap: 16px;\r\n}\r\n\r\n.search-controls {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.action-controls {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.data-table {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 专业标题单元格 */\r\n.specialty-title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 4px 0;\r\n}\r\n\r\n.specialty-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.specialty-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.specialty-title {\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.specialty-title:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.specialty-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 专业描述 */\r\n.specialty-description {\r\n  color: #595959;\r\n  line-height: 1.5;\r\n}\r\n\r\n.no-desc {\r\n  color: #d9d9d9;\r\n  font-style: italic;\r\n}\r\n\r\n/* 时间单元格 */\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #595959;\r\n}\r\n\r\n.time-cell i {\r\n  color: #8c8c8c;\r\n}\r\n\r\n/* 状态标签 */\r\n.status-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.edit-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.edit-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.delete-btn:hover {\r\n  color: #ff7875;\r\n}\r\n\r\n/* 分页容器 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 16px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .stats-section {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .stat-card {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-controls {\r\n    max-width: none;\r\n  }\r\n\r\n  .action-controls {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n\r\n  .specialty-title-cell {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table ::v-deep .el-table th {\r\n  background-color: #fafafa;\r\n  color: #262626;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table ::v-deep .el-table td {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  color: #262626;\r\n}\r\n\r\n.data-table ::v-deep .el-table tr:hover > td {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.data-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped:hover td {\r\n  background-color: #f0f0f0;\r\n}\r\n</style>\r\n"], "mappings": "AAwMA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,MAAA;MACA;MAEAC,KAAA;QACAP,KAAA,GACA;UACAQ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA;MACA,YAAArB,IAAA,CAAAsB,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,MAAA,QAAAC,MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAC,EAAA;MACA,IAAAC,KAAA;MACA,IAAAD,EAAA;QACA,KAAAE,OAAA,CAAAF,EAAA;MACA;QACA,KAAAjB,QAAA;UACAL,KAAA;UACAyB,IAAA;QACA;MACA;MAEAF,KAAA,CAAArB,iBAAA;IACA;IACAsB,QAAAF,EAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAAG,UAAA,CAAAH,KAAA,CAAAxB,GAAA,gBAAAuB,EAAA,EAAAK,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAL,KAAA,CAAAlB,QAAA,GAAAuB,IAAA,CAAAtC,IAAA;QACA;MACA;IACA;IACAuC,QAAAC,KAAA,EAAAR,EAAA;MACA,KAAAS,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAApC,GAAA,kBAAAuB,EAAA,EAAAK,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACAzB,OAAA;YACA;YACA,KAAAjB,IAAA,CAAA8C,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACAzB,OAAA;QACA;MACA;IACA;IACA+B,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAAjD,IAAA;MACA,KAAAC,IAAA;MACA,KAAAwB,OAAA;IACA;IAEAA,QAAA;MACA,IAAAI,KAAA;MAEAA,KAAA,CAAAzB,OAAA;MACAyB,KAAA,CACAqB,WAAA,CACArB,KAAA,CAAAxB,GAAA,mBAAAwB,KAAA,CAAA7B,IAAA,cAAA6B,KAAA,CAAA5B,IAAA,EACA4B,KAAA,CAAA3B,MACA,EACA+B,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAA/B,IAAA,GAAAoC,IAAA,CAAAtC,IAAA;UACAiC,KAAA,CAAA9B,KAAA,GAAAmC,IAAA,CAAAiB,KAAA;QACA;QACAtB,KAAA,CAAAzB,OAAA;MACA;IACA;IACAgD,SAAA;MACA,IAAAvB,KAAA;MACA,KAAAwB,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAL,WAAA,CAAArB,KAAA,CAAAxB,GAAA,gBAAAM,QAAA,EAAAsB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAb,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAzB,OAAA,EAAAmB,IAAA,CAAAsB;cACA;cACA,KAAA/B,OAAA;cACAI,KAAA,CAAArB,iBAAA;YACA;cACAqB,KAAA,CAAAc,QAAA;gBACAH,IAAA;gBACAzB,OAAA,EAAAmB,IAAA,CAAAsB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAAzD,IAAA,GAAAyD,GAAA;MAEA,KAAAjC,OAAA;IACA;IACAkC,oBAAAD,GAAA;MACA,KAAA1D,IAAA,GAAA0D,GAAA;MACA,KAAAjC,OAAA;IACA;IACAmC,iBAAAC,MAAA;MACA;MACAC,OAAA,CAAAC,GAAA,UAAAF,MAAA;IACA;IACAG,cAAAC,GAAA;MACA;MACA,IAAAA,GAAA,CAAA3C,MAAA;MACA;IACA;IACA4C,cAAAD,GAAA;MACA;MACA,IAAAA,GAAA,CAAA3C,MAAA;MACA;IACA;IACA6C,cAAAC,GAAA;MACA,KAAAzD,QAAA,CAAA0D,QAAA,GAAAD,GAAA,CAAAxE,IAAA,CAAAS,GAAA;IACA;IAEAiE,UAAAC,IAAA;MACA,KAAA9D,UAAA,GAAA8D,IAAA;MACA,KAAA7D,aAAA;IACA;IACA8D,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAA/B,IAAA;MACA,KAAAiC,UAAA;QACA,KAAA9B,QAAA,CAAAgC,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAAhD,KAAA;MACAA,KAAA,CAAAG,UAAA,gCAAAuC,IAAA,EAAAtC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAb,KAAA,CAAAlB,QAAA,CAAAkE,QAAA;UAEAhD,KAAA,CAAAc,QAAA,CAAAmC,OAAA;QACA;UACAjD,KAAA,CAAAc,QAAA,CAAAgC,KAAA,CAAAzC,IAAA,CAAAsB,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}