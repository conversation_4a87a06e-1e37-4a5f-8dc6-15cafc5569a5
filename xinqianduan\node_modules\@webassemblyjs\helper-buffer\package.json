{"name": "@webassemblyjs/helper-buffer", "version": "1.9.0", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.9.0", "jest-diff": "^24.0.0"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8"}