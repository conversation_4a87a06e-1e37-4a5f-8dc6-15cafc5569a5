{"name": "myclabs/php-enum", "type": "library", "description": "PHP Enum implementation", "keywords": ["enum"], "homepage": "http://github.com/myclabs/php-enum", "license": "MIT", "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "autoload-dev": {"psr-4": {"MyCLabs\\Tests\\Enum\\": "tests/"}}, "require": {"php": ">=7.1", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}}