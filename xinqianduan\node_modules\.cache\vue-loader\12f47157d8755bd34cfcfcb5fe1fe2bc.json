{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\Dashboard.vue?vue&type=style&index=0&id=2e5e4e3f&scoped=true&lang=css", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\Dashboard.vue", "mtime": 1748429146923}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748425633939}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748425638985}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Dashboard.vue"], "names": [], "mappings": ";AA0bA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Dashboard.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <!-- 欢迎区域 -->\r\n    <div class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <h1 class=\"welcome-title\">欢迎使用法律服务管理系统</h1>\r\n        <p class=\"welcome-subtitle\">{{ getCurrentTime() }} | 管理员，您好！</p>\r\n      </div>\r\n      <div class=\"welcome-actions\">\r\n        <el-button type=\"primary\" @click=\"handleQuickAction('new-case')\">\r\n          <i class=\"el-icon-plus\"></i> 新建案件\r\n        </el-button>\r\n        <el-button type=\"success\" @click=\"handleQuickAction('new-contract')\">\r\n          <i class=\"el-icon-document-add\"></i> 新建合同\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalUsers }}</div>\r\n              <div class=\"stat-label\">总用户数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon case-icon\">\r\n              <i class=\"el-icon-folder\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalCases }}</div>\r\n              <div class=\"stat-label\">案件总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon contract-icon\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ stats.totalContracts }}</div>\r\n              <div class=\"stat-label\">合同数量</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon revenue-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ stats.totalRevenue }}</div>\r\n              <div class=\"stat-label\">总收入</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +22%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 左侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"16\" :lg=\"16\" :xl=\"16\">\r\n        <!-- 图表区域 -->\r\n        <div class=\"chart-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">业务数据趋势</span>\r\n              <div class=\"chart-controls\">\r\n                <el-radio-group v-model=\"chartPeriod\" size=\"small\">\r\n                  <el-radio-button label=\"week\">本周</el-radio-button>\r\n                  <el-radio-button label=\"month\">本月</el-radio-button>\r\n                  <el-radio-button label=\"year\">本年</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"chart-container\">\r\n              <div class=\"chart-placeholder\">\r\n                <i class=\"el-icon-data-line chart-icon\"></i>\r\n                <p>数据图表区域</p>\r\n                <p class=\"chart-desc\">这里可以集成 ECharts 或其他图表库显示业务数据趋势</p>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 最近活动 -->\r\n        <div class=\"activity-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">最近活动</span>\r\n              <el-button type=\"text\" @click=\"viewAllActivities\">查看全部</el-button>\r\n            </div>\r\n            <div class=\"activity-list\">\r\n              <div\r\n                v-for=\"activity in recentActivities\"\r\n                :key=\"activity.id\"\r\n                class=\"activity-item\"\r\n              >\r\n                <div class=\"activity-avatar\">\r\n                  <i :class=\"activity.icon\" :style=\"{ color: activity.color }\"></i>\r\n                </div>\r\n                <div class=\"activity-content\">\r\n                  <div class=\"activity-title\">{{ activity.title }}</div>\r\n                  <div class=\"activity-desc\">{{ activity.description }}</div>\r\n                  <div class=\"activity-time\">{{ activity.time }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 右侧内容 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n        <!-- 快捷操作 -->\r\n        <div class=\"quick-actions-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">快捷操作</span>\r\n            </div>\r\n            <div class=\"quick-actions\">\r\n              <div\r\n                v-for=\"action in quickActions\"\r\n                :key=\"action.id\"\r\n                class=\"quick-action-item\"\r\n                @click=\"handleQuickAction(action.action)\"\r\n              >\r\n                <div class=\"action-icon\" :style=\"{ backgroundColor: action.color }\">\r\n                  <i :class=\"action.icon\"></i>\r\n                </div>\r\n                <div class=\"action-content\">\r\n                  <div class=\"action-title\">{{ action.title }}</div>\r\n                  <div class=\"action-desc\">{{ action.description }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 待办事项 -->\r\n        <div class=\"todo-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">待办事项</span>\r\n              <el-badge :value=\"todoList.filter(item => !item.completed).length\" class=\"todo-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllTodos\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"todo-list\">\r\n              <div\r\n                v-for=\"todo in todoList.slice(0, 5)\"\r\n                :key=\"todo.id\"\r\n                class=\"todo-item\"\r\n                :class=\"{ completed: todo.completed }\"\r\n              >\r\n                <el-checkbox\r\n                  v-model=\"todo.completed\"\r\n                  @change=\"handleTodoChange(todo)\"\r\n                >\r\n                  {{ todo.title }}\r\n                </el-checkbox>\r\n                <div class=\"todo-priority\" :class=\"todo.priority\">\r\n                  {{ getPriorityText(todo.priority) }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- 系统通知 -->\r\n        <div class=\"notification-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统通知</span>\r\n              <el-badge :value=\"notifications.filter(item => !item.read).length\" class=\"notification-badge\">\r\n                <el-button type=\"text\" @click=\"viewAllNotifications\">查看全部</el-button>\r\n              </el-badge>\r\n            </div>\r\n            <div class=\"notification-list\">\r\n              <div\r\n                v-for=\"notification in notifications.slice(0, 3)\"\r\n                :key=\"notification.id\"\r\n                class=\"notification-item\"\r\n                :class=\"{ unread: !notification.read }\"\r\n                @click=\"markAsRead(notification)\"\r\n              >\r\n                <div class=\"notification-content\">\r\n                  <div class=\"notification-title\">{{ notification.title }}</div>\r\n                  <div class=\"notification-time\">{{ notification.time }}</div>\r\n                </div>\r\n                <div v-if=\"!notification.read\" class=\"notification-dot\"></div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n\r\n        <!-- System Monitor -->\r\n        <div class=\"system-monitor-section\">\r\n          <el-card shadow=\"hover\">\r\n            <div slot=\"header\" class=\"card-header\">\r\n              <span class=\"card-title\">系统监控</span>\r\n            </div>\r\n            <div class=\"system-monitor-content\">\r\n              <system-monitor></system-monitor>\r\n            </div>\r\n          </el-card>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport SystemMonitor from '@/components/SystemMonitor.vue'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: {\r\n    SystemMonitor\r\n  },\r\n  data() {\r\n    return {\r\n      chartPeriod: 'month',\r\n      stats: {\r\n        totalUsers: 1248,\r\n        totalCases: 356,\r\n        totalContracts: 892,\r\n        totalRevenue: '2,456,789'\r\n      },\r\n      recentActivities: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#409EFF',\r\n          title: '新用户注册',\r\n          description: '张三注册了新账户',\r\n          time: '2分钟前'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document',\r\n          color: '#67C23A',\r\n          title: '合同审核完成',\r\n          description: '《服务合同-001》审核通过',\r\n          time: '15分钟前'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-folder-add',\r\n          color: '#E6A23C',\r\n          title: '新案件创建',\r\n          description: '李四创建了新的法律咨询案件',\r\n          time: '1小时前'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-money',\r\n          color: '#F56C6C',\r\n          title: '收款确认',\r\n          description: '收到客户王五的服务费用',\r\n          time: '2小时前'\r\n        }\r\n      ],\r\n      quickActions: [\r\n        {\r\n          id: 1,\r\n          icon: 'el-icon-plus',\r\n          color: '#409EFF',\r\n          title: '新建案件',\r\n          description: '创建新的法律案件',\r\n          action: 'new-case'\r\n        },\r\n        {\r\n          id: 2,\r\n          icon: 'el-icon-document-add',\r\n          color: '#67C23A',\r\n          title: '新建合同',\r\n          description: '创建新的合同文档',\r\n          action: 'new-contract'\r\n        },\r\n        {\r\n          id: 3,\r\n          icon: 'el-icon-user-solid',\r\n          color: '#E6A23C',\r\n          title: '添加客户',\r\n          description: '添加新的客户信息',\r\n          action: 'new-client'\r\n        },\r\n        {\r\n          id: 4,\r\n          icon: 'el-icon-upload',\r\n          color: '#F56C6C',\r\n          title: '文件归档',\r\n          description: '上传并归档文件',\r\n          action: 'upload-file'\r\n        }\r\n      ],\r\n      todoList: [\r\n        {\r\n          id: 1,\r\n          title: '审核张三的合同申请',\r\n          completed: false,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '回复李四的法律咨询',\r\n          completed: false,\r\n          priority: 'medium'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '准备明天的庭审材料',\r\n          completed: true,\r\n          priority: 'high'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '更新客户联系信息',\r\n          completed: false,\r\n          priority: 'low'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '整理本月财务报表',\r\n          completed: false,\r\n          priority: 'medium'\r\n        }\r\n      ],\r\n      notifications: [\r\n        {\r\n          id: 1,\r\n          title: '系统维护通知',\r\n          time: '今天 14:30',\r\n          read: false\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '新版本更新',\r\n          time: '昨天 16:20',\r\n          read: false\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '数据备份完成',\r\n          time: '昨天 09:15',\r\n          read: true\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    getCurrentTime() {\r\n      const now = new Date()\r\n      const options = {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric',\r\n        weekday: 'long'\r\n      }\r\n      return now.toLocaleDateString('zh-CN', options)\r\n    },\r\n\r\n    handleQuickAction(action) {\r\n      switch (action) {\r\n        case 'new-case':\r\n          this.$message.info('跳转到新建案件页面')\r\n          // this.$router.push('/cases/new')\r\n          break\r\n        case 'new-contract':\r\n          this.$message.info('跳转到新建合同页面')\r\n          // this.$router.push('/contracts/new')\r\n          break\r\n        case 'new-client':\r\n          this.$message.info('跳转到添加客户页面')\r\n          // this.$router.push('/clients/new')\r\n          break\r\n        case 'upload-file':\r\n          this.$message.info('跳转到文件归档页面')\r\n          this.$router.push('/archive/file')\r\n          break\r\n        default:\r\n          this.$message.info(`执行操作: ${action}`)\r\n      }\r\n    },\r\n\r\n    viewAllActivities() {\r\n      this.$message.info('查看所有活动')\r\n    },\r\n\r\n    viewAllTodos() {\r\n      this.$message.info('查看所有待办事项')\r\n    },\r\n\r\n    viewAllNotifications() {\r\n      this.$message.info('查看所有通知')\r\n    },\r\n\r\n    handleTodoChange(todo) {\r\n      this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')\r\n    },\r\n\r\n    markAsRead(notification) {\r\n      notification.read = true\r\n      this.$message.success('通知已标记为已读')\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const map = {\r\n        high: '高',\r\n        medium: '中',\r\n        low: '低'\r\n      }\r\n      return map[priority] || '中'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 欢迎区域 */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  padding: 30px;\r\n  margin-bottom: 20px;\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n.case-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n.contract-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n.revenue-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.chart-section,\r\n.activity-section,\r\n.quick-actions-section,\r\n.todo-section,\r\n.notification-section,\r\n.system-monitor-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 图表区域 */\r\n.chart-container {\r\n  height: 300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-placeholder {\r\n  text-align: center;\r\n  color: #95a5a6;\r\n}\r\n\r\n.chart-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.chart-desc {\r\n  margin: 8px 0 0 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 活动列表 */\r\n.activity-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.activity-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.activity-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.activity-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  font-size: 18px;\r\n}\r\n\r\n.activity-content {\r\n  flex: 1;\r\n}\r\n\r\n.activity-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-desc {\r\n  color: #7f8c8d;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.activity-time {\r\n  color: #bdc3c7;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 快捷操作 */\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-action-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  background-color: #f8f9fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quick-action-item:hover {\r\n  background-color: #e9ecef;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.action-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.action-desc {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 待办事项 */\r\n.todo-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.todo-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.todo-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.todo-item.completed {\r\n  opacity: 0.6;\r\n}\r\n\r\n.todo-priority {\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n}\r\n\r\n.todo-priority.high {\r\n  background-color: #fee;\r\n  color: #e74c3c;\r\n}\r\n\r\n.todo-priority.medium {\r\n  background-color: #fff3cd;\r\n  color: #f39c12;\r\n}\r\n\r\n.todo-priority.low {\r\n  background-color: #d4edda;\r\n  color: #27ae60;\r\n}\r\n\r\n/* 通知列表 */\r\n.notification-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.notification-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.notification-item:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.notification-item.unread {\r\n  background-color: #e3f2fd;\r\n}\r\n\r\n.notification-content {\r\n  flex: 1;\r\n}\r\n\r\n.notification-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.notification-time {\r\n  color: #7f8c8d;\r\n  font-size: 12px;\r\n}\r\n\r\n.notification-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #409EFF;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .welcome-section {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 20px;\r\n  }\r\n\r\n  .welcome-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 12px;\r\n  }\r\n}\r\n</style> "]}]}