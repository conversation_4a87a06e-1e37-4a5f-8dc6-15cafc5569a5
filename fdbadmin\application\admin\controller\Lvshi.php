<?php
namespace app\admin\controller;
use think\Request;
use untils\JsonService;
use models\{Lvshis,Zhuanyes,Lvshicates,District};

class Lvshi
{
    protected $model;
    public function __construct(Lvshis $model){
        //parent::__construct();
        $this->model=$model;
        
    }
    public function index(Request $request,$page=1,$size=20){   
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        $res = $this->model::withAttr('cate_id',function($v,$d){
            return Lvshicates::where(['id'=>$v])->value('title');
        })->withAttr('zhuanyes',function($value,$data){
            if(!empty($value)) return implode(',',Zhuanyes::where('id','in',unserialize($value))->column('title'));
        })->withAttr('city_id',function($value,$data){
            if(!empty($value)) return District::where(['id'=>$value])->value('title');
            else return '暂无';
        })
        ->where($where)
        ->order(['id'=>'desc'])
        ->where(['is_delete'=>0])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);
        if(empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功',$res);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->saveData(['id'=>$id,'is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
}
