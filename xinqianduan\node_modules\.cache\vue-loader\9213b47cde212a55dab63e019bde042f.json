{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\shenhe.vue", "mtime": 1748439533995}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["shenhe.vue"], "names": [], "mappings": ";AA4i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file": "shenhe.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-review-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document-checked\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入工单号/用户名/合同标题\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">审核状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同审核数据\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"order-sn\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ scope.row.order_sn }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.type === '合同审核' ? 'warning' : 'info'\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"title\" label=\"合同标题\" min-width=\"150\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"contract-title\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"desc\" label=\"审核要求\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"review-desc\">\r\n              {{ scope.row.desc }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"review_status\" label=\"审核状态\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getStatusType(scope.row.review_status)\"\r\n              size=\"small\"\r\n            >\r\n              {{ getStatusText(scope.row.review_status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"current_reviewer\" label=\"当前审核人\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.current_reviewer\">{{ scope.row.current_reviewer }}</span>\r\n            <span v-else class=\"text-muted\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-user\"></i>\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-phone\"></i>\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"提交时间\" width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- 审核按钮 -->\r\n              <el-button\r\n                v-if=\"canReview(scope.row)\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"startReview(scope.row)\"\r\n                icon=\"el-icon-edit-outline\"\r\n                class=\"action-btn\"\r\n              >\r\n                审核\r\n              </el-button>\r\n\r\n              <!-- 查看审核进度 -->\r\n              <el-button\r\n                type=\"info\"\r\n                size=\"mini\"\r\n                @click=\"viewReviewProgress(scope.row)\"\r\n                icon=\"el-icon-view\"\r\n                class=\"action-btn\"\r\n              >\r\n                进度\r\n              </el-button>\r\n\r\n              <!-- 预览合同 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"previewContract(scope.row)\"\r\n                icon=\"el-icon-document\"\r\n                class=\"action-btn\"\r\n              >\r\n                预览\r\n              </el-button>\r\n\r\n              <!-- 立案按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.review_status === 'approved'\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitToFiling(scope.row)\"\r\n                icon=\"el-icon-folder-add\"\r\n                class=\"action-btn\"\r\n              >\r\n                立案\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同内容\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同图片\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.images[0]\">\r\n            <div style=\"width: 100%;display: table-cell;\">\r\n          <div style=\"float: left;margin-left:2px;\"\r\n            v-for=\"(item2, index2) in ruleForm.images\"\r\n            :key=\"index2\"\r\n            class=\"image-list\"\r\n          >\r\n            <img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />\r\n          </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同文件\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.attach_path[0]\">\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n          <div\r\n            v-for=\"(item3, index3) in ruleForm.attach_path\"\r\n            :key=\"index3\"\r\n          >\r\n            <div v-if=\"item3\">\r\n              <div >文件{{ index3 +1 }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\">下载</a></div><br />\r\n            </div>\r\n          </div>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 合同预览对话框 -->\r\n    <el-dialog\r\n      title=\"合同预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"contract-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ previewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ previewData.nickname }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-time\"></i>\r\n              提交时间：{{ previewData.create_time }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div class=\"section\">\r\n            <h4>审核要求</h4>\r\n            <p>{{ previewData.desc }}</p>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.images && previewData.images.length\">\r\n            <h4>合同图片</h4>\r\n            <div class=\"image-gallery\">\r\n              <div\r\n                v-for=\"(image, index) in previewData.images\"\r\n                :key=\"index\"\r\n                class=\"image-item\"\r\n                @click=\"showImage(image)\"\r\n              >\r\n                <img :src=\"image\" alt=\"合同图片\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.attach_path && previewData.attach_path.length\">\r\n            <h4>合同文件</h4>\r\n            <div class=\"file-list\">\r\n              <div\r\n                v-for=\"(file, index) in previewData.attach_path\"\r\n                :key=\"index\"\r\n                class=\"file-item\"\r\n                v-if=\"file\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>文件{{ index + 1 }}</span>\r\n                <div class=\"file-actions\">\r\n                  <el-button type=\"text\" @click=\"viewFile(file)\">查看</el-button>\r\n                  <el-button type=\"text\" @click=\"downloadFile(file)\">下载</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog\r\n      title=\"合同审核\"\r\n      :visible.sync=\"dialogReview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"review-dialog\"\r\n    >\r\n      <div class=\"review-content\">\r\n        <div class=\"review-header\">\r\n          <h3>{{ reviewData.title }}</h3>\r\n          <div class=\"review-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ reviewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ reviewData.nickname }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"review-form\">\r\n          <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewForm\" label-width=\"100px\">\r\n            <el-form-item label=\"审核结果\" prop=\"result\">\r\n              <el-radio-group v-model=\"reviewForm.result\">\r\n                <el-radio label=\"approved\">通过</el-radio>\r\n                <el-radio label=\"rejected\">不通过</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              v-if=\"reviewForm.result === 'rejected'\"\r\n              label=\"不通过理由\"\r\n              prop=\"reason\"\r\n            >\r\n              <el-select\r\n                v-model=\"reviewForm.reason\"\r\n                placeholder=\"请选择不通过理由\"\r\n                style=\"width: 100%\"\r\n                clearable\r\n              >\r\n                <el-option label=\"合同条款不完整\" value=\"incomplete_terms\"></el-option>\r\n                <el-option label=\"法律条款有误\" value=\"legal_error\"></el-option>\r\n                <el-option label=\"格式不规范\" value=\"format_error\"></el-option>\r\n                <el-option label=\"内容与需求不符\" value=\"content_mismatch\"></el-option>\r\n                <el-option label=\"缺少必要附件\" value=\"missing_attachments\"></el-option>\r\n                <el-option label=\"其他问题\" value=\"other\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              label=\"审核意见\"\r\n              prop=\"comment\"\r\n              :rules=\"reviewForm.result === 'rejected' ? [{ required: true, message: '请填写审核意见', trigger: 'blur' }] : []\"\r\n            >\r\n              <el-input\r\n                type=\"textarea\"\r\n                v-model=\"reviewForm.comment\"\r\n                :rows=\"4\"\r\n                placeholder=\"请填写详细的审核意见...\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogReview = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitReview\" :loading=\"reviewSubmitting\">\r\n          提交审核\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核进度抽屉 -->\r\n    <el-drawer\r\n      title=\"审核进度\"\r\n      :visible.sync=\"dialogProgress\"\r\n      direction=\"rtl\"\r\n      size=\"50%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"progress-drawer\"\r\n    >\r\n      <div class=\"progress-drawer-content\">\r\n        <div class=\"progress-header\">\r\n          <h3>{{ progressData.title }}</h3>\r\n          <div class=\"progress-meta\">\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>工单号：{{ progressData.order_sn }}</span>\r\n            </div>\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-info\"></i>\r\n              <span>当前状态：{{ getStatusText(progressData.review_status) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"progress-timeline\">\r\n          <el-timeline>\r\n            <el-timeline-item\r\n              v-for=\"(step, index) in reviewSteps\"\r\n              :key=\"index\"\r\n              :timestamp=\"step.time\"\r\n              :type=\"step.status === 'completed' ? 'success' : step.status === 'current' ? 'primary' : 'info'\"\r\n              :icon=\"step.status === 'completed' ? 'el-icon-check' : step.status === 'current' ? 'el-icon-loading' : 'el-icon-time'\"\r\n              placement=\"top\"\r\n            >\r\n              <div class=\"timeline-content\">\r\n                <h4>{{ step.title }}</h4>\r\n                <p>{{ step.reviewer }}</p>\r\n                <div v-if=\"step.comment\" class=\"step-comment\">\r\n                  <strong>审核意见：</strong>{{ step.comment }}\r\n                </div>\r\n                <div v-if=\"step.reason\" class=\"step-reason\">\r\n                  <strong>不通过理由：</strong>{{ step.reason }}\r\n                </div>\r\n              </div>\r\n            </el-timeline-item>\r\n          </el-timeline>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/shenhe/\",\r\n      title: \"合同审核\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogPreview: false,\r\n      dialogReview: false,\r\n      dialogProgress: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewData: {},\r\n      reviewData: {},\r\n      progressData: {},\r\n      reviewSteps: [],\r\n      reviewSubmitting: false,\r\n\r\n      // 审核表单\r\n      reviewForm: {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      },\r\n\r\n      // 审核表单验证规则\r\n      reviewRules: {\r\n        result: [\r\n          { required: true, message: '请选择审核结果', trigger: 'change' }\r\n        ]\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        images:{},\r\n        attach_path:{}\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"SH202403001\",\r\n            type: \"合同审核\",\r\n            title: \"劳动合同审核\",\r\n            desc: \"请帮忙审核劳动合同条款，检查是否符合劳动法规定，特别关注薪资、工作时间、福利待遇等条款的合法性\",\r\n            review_status: \"mediator_review\", // 调解员审核中\r\n            current_reviewer: \"调解员\",\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\",\r\n            images: [\r\n              \"/uploads/contracts/labor_contract_page1.jpg\",\r\n              \"/uploads/contracts/labor_contract_page2.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/labor_contract.pdf\",\r\n              \"/uploads/contracts/labor_contract_supplement.docx\"\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"SH202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"房屋租赁合同审核，需要检查租金条款、押金规定、违约责任等是否合理\",\r\n            review_status: \"business_review\", // 业务员审核中\r\n            current_reviewer: \"业务员\",\r\n            nickname: \"李四\",\r\n            phone: \"***********\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/lease_contract_page1.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/lease_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"SH202403003\",\r\n            type: \"合同审核\",\r\n            title: \"买卖合同审核\",\r\n            desc: \"商品买卖合同审核，重点关注货物交付、付款方式、质量保证等条款\",\r\n            review_status: \"approved\", // 全部审核通过\r\n            current_reviewer: \"\",\r\n            nickname: \"王五\",\r\n            phone: \"***********\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\",\r\n            images: [],\r\n            attach_path: [\r\n              \"/uploads/contracts/sale_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"SH202403004\",\r\n            type: \"合同审核\",\r\n            title: \"服务合同审核\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            review_status: \"lawyer_review\", // 律师审核中\r\n            current_reviewer: \"律师\",\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\",\r\n            images: [],\r\n            attach_path: []\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"SH202403005\",\r\n            type: \"合同审核\",\r\n            title: \"借款合同审核\",\r\n            desc: \"个人借款合同审核，需要确认利率、还款方式、担保条款等是否符合法律规定\",\r\n            review_status: \"rejected\", // 审核不通过\r\n            current_reviewer: \"\",\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/loan_contract_page1.jpg\",\r\n              \"/uploads/contracts/loan_contract_page2.jpg\",\r\n              \"/uploads/contracts/loan_contract_page3.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/loan_contract.pdf\"\r\n            ]\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 预览合同\r\n    previewContract(row) {\r\n      console.log('预览合同:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 查看文件\r\n    viewFile(fileUrl) {\r\n      window.open(fileUrl, '_blank');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        'submitted': 'info',\r\n        'mediator_review': 'warning',\r\n        'business_review': 'warning',\r\n        'lawyer_review': 'warning',\r\n        'final_review': 'warning',\r\n        'approved': 'success',\r\n        'rejected': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'submitted': '已提交',\r\n        'mediator_review': '调解员审核中',\r\n        'business_review': '业务员审核中',\r\n        'lawyer_review': '律师审核中',\r\n        'final_review': '最终审核中',\r\n        'approved': '审核通过',\r\n        'rejected': '审核不通过'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 判断是否可以审核\r\n    canReview(row) {\r\n      // 这里可以根据当前用户角色和审核状态判断\r\n      // 简化处理：只要不是已通过或已拒绝的都可以审核\r\n      return row.review_status !== 'approved' && row.review_status !== 'rejected';\r\n    },\r\n\r\n    // 开始审核\r\n    startReview(row) {\r\n      console.log('开始审核:', row);\r\n      this.reviewData = row;\r\n\r\n      // 重置审核表单\r\n      this.reviewForm = {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      };\r\n\r\n      this.dialogReview = true;\r\n    },\r\n\r\n    // 提交审核\r\n    submitReview() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewSubmitting = true;\r\n\r\n          // 模拟提交审核\r\n          setTimeout(() => {\r\n            const result = this.reviewForm.result;\r\n            const currentData = this.reviewData;\r\n\r\n            // 更新列表中的数据\r\n            const index = this.list.findIndex(item => item.id === currentData.id);\r\n            if (index !== -1) {\r\n              if (result === 'approved') {\r\n                // 通过审核，进入下一个审核环节\r\n                this.list[index] = this.getNextReviewStatus(this.list[index]);\r\n              } else {\r\n                // 审核不通过\r\n                this.list[index].review_status = 'rejected';\r\n                this.list[index].current_reviewer = '';\r\n              }\r\n            }\r\n\r\n            this.reviewSubmitting = false;\r\n            this.dialogReview = false;\r\n            this.$message.success('审核提交成功！');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取下一个审核状态\r\n    getNextReviewStatus(currentItem) {\r\n      const statusFlow = {\r\n        'submitted': 'mediator_review',\r\n        'mediator_review': 'business_review',\r\n        'business_review': 'lawyer_review',\r\n        'lawyer_review': 'final_review',\r\n        'final_review': 'approved'\r\n      };\r\n\r\n      const reviewerMap = {\r\n        'mediator_review': '调解员',\r\n        'business_review': '业务员',\r\n        'lawyer_review': '律师',\r\n        'final_review': '最终审核人',\r\n        'approved': ''\r\n      };\r\n\r\n      const nextStatus = statusFlow[currentItem.review_status];\r\n      return {\r\n        ...currentItem,\r\n        review_status: nextStatus,\r\n        current_reviewer: reviewerMap[nextStatus]\r\n      };\r\n    },\r\n\r\n    // 查看审核进度\r\n    viewReviewProgress(row) {\r\n      console.log('查看审核进度:', row);\r\n      this.progressData = row;\r\n\r\n      // 模拟审核步骤数据\r\n      this.reviewSteps = this.generateReviewSteps(row);\r\n      this.dialogProgress = true;\r\n    },\r\n\r\n    // 生成审核步骤\r\n    generateReviewSteps(row) {\r\n      const allSteps = [\r\n        {\r\n          title: '法务提交',\r\n          reviewer: '法务部门',\r\n          status: 'completed',\r\n          time: row.create_time,\r\n          comment: '合同已完成，提交审核'\r\n        },\r\n        {\r\n          title: '调解员审核',\r\n          reviewer: '调解员',\r\n          status: row.review_status === 'submitted' ? 'pending' :\r\n                  row.review_status === 'mediator_review' ? 'current' : 'completed',\r\n          time: row.review_status === 'submitted' ? '' : '2024-03-20 11:00:00',\r\n          comment: row.review_status === 'submitted' ? '' : '合同条款符合调解要求'\r\n        },\r\n        {\r\n          title: '业务员审核',\r\n          reviewer: '业务员',\r\n          status: ['submitted', 'mediator_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'business_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '2024-03-20 14:30:00',\r\n          comment: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '业务流程审核通过'\r\n        },\r\n        {\r\n          title: '律师审核',\r\n          reviewer: '律师',\r\n          status: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'lawyer_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '2024-03-20 16:00:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '法律条款审核通过'\r\n        },\r\n        {\r\n          title: '最终审核',\r\n          reviewer: '最终审核人',\r\n          status: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'final_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '2024-03-20 17:30:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '最终审核通过'\r\n        }\r\n      ];\r\n\r\n      // 如果审核被拒绝，添加拒绝信息\r\n      if (row.review_status === 'rejected') {\r\n        allSteps.push({\r\n          title: '审核不通过',\r\n          reviewer: '审核人员',\r\n          status: 'completed',\r\n          time: '2024-03-20 18:00:00',\r\n          comment: '',\r\n          reason: '合同条款不完整，需要重新修改'\r\n        });\r\n      }\r\n\r\n      return allSteps;\r\n    },\r\n\r\n    // 提交立案\r\n    submitToFiling(row) {\r\n      this.$confirm('确认将此合同提交到立案部门？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交立案\r\n        setTimeout(() => {\r\n          this.$message.success('已成功提交到立案部门！');\r\n\r\n          // 更新状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            this.list[index].review_status = 'filed';\r\n            this.list[index].current_reviewer = '立案部门';\r\n          }\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-review-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.order-sn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.order-sn i {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.contract-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.contract-title i {\r\n  color: #e6a23c;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-desc {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.user-link {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n}\r\n\r\n.user-link i {\r\n  font-size: 12px;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 合同预览对话框样式 */\r\n.contract-preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #409eff;\r\n}\r\n\r\n.preview-body .section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.preview-body .section h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-body .section p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.image-gallery {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.image-item {\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n}\r\n\r\n.file-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-item i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 审核对话框样式 */\r\n.review-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.review-content {\r\n  padding: 24px;\r\n}\r\n\r\n.review-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.review-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.review-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.review-form {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #f093fb;\r\n}\r\n\r\n/* 审核进度抽屉样式 */\r\n.progress-drawer >>> .el-drawer {\r\n  border-radius: 0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.progress-drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n.progress-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.progress-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 16px 0;\r\n}\r\n\r\n.progress-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.progress-meta .meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.progress-meta .meta-item i {\r\n  color: #4facfe;\r\n  font-size: 16px;\r\n}\r\n\r\n.progress-timeline {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  border-left: 4px solid #4facfe;\r\n}\r\n\r\n.timeline-content h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.timeline-content p {\r\n  color: #606266;\r\n  margin: 0 0 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.step-comment,\r\n.step-reason {\r\n  background: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.step-comment {\r\n  border-left: 3px solid #67c23a;\r\n}\r\n\r\n.step-reason {\r\n  border-left: 3px solid #f56c6c;\r\n}\r\n\r\n.step-comment strong,\r\n.step-reason strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-muted {\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-review-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .image-gallery {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}