(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-100ed178"],{"0f24":function(t,r,s){"use strict";s.r(r);var e=function(){var t=this,r=t._self._c;return r("div",{staticClass:"page-wrapper"},[r("div",{staticClass:"page-container"},[r("div",{staticClass:"page-header"},[t._m(0),r("el-button",{staticClass:"back-btn",attrs:{type:"text",icon:"el-icon-back"},on:{click:t.goBack}},[t._v(" 返回 ")])],1),r("div",{staticClass:"form-section"},[r("div",{staticClass:"form-card"},[t._m(1),r("el-form",{ref:"passwordForm",staticClass:"password-form",attrs:{model:t.passwordForm,rules:t.rules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"当前密码",prop:"oldPassword"}},[r("el-input",{attrs:{type:"password",placeholder:"请输入当前密码","show-password":"",autocomplete:"off"},model:{value:t.passwordForm.oldPassword,callback:function(r){t.$set(t.passwordForm,"oldPassword",r)},expression:"passwordForm.oldPassword"}})],1),r("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[r("el-input",{attrs:{type:"password",placeholder:"请输入新密码","show-password":"",autocomplete:"off"},model:{value:t.passwordForm.newPassword,callback:function(r){t.$set(t.passwordForm,"newPassword",r)},expression:"passwordForm.newPassword"}})],1),r("el-form-item",{attrs:{label:"确认新密码",prop:"confirmPassword"}},[r("el-input",{attrs:{type:"password",placeholder:"请再次输入新密码","show-password":"",autocomplete:"off"},model:{value:t.passwordForm.confirmPassword,callback:function(r){t.$set(t.passwordForm,"confirmPassword",r)},expression:"passwordForm.confirmPassword"}})],1),t.passwordForm.newPassword?r("div",{staticClass:"password-strength"},[r("div",{staticClass:"strength-label"},[t._v("密码强度：")]),r("div",{staticClass:"strength-bar"},[r("div",{staticClass:"strength-fill",class:t.passwordStrengthClass,style:{width:t.passwordStrengthWidth}})]),r("div",{staticClass:"strength-text",class:t.passwordStrengthClass},[t._v(" "+t._s(t.passwordStrengthText)+" ")])]):t._e(),r("div",{staticClass:"action-buttons"},[r("el-button",{attrs:{type:"primary",loading:t.loading,size:"medium"},on:{click:t.changePassword}},[t._v(" 确认修改 ")]),r("el-button",{attrs:{size:"medium"},on:{click:t.resetForm}},[t._v(" 重置 ")])],1)],1)],1)])])])},a=[function(){var t=this,r=t._self._c;return r("div",{staticClass:"header-left"},[r("h2",{staticClass:"page-title"},[r("i",{staticClass:"el-icon-lock"}),t._v(" 修改密码 ")]),r("div",{staticClass:"page-subtitle"},[t._v("为了您的账户安全，请定期更换密码")])])},function(){var t=this,r=t._self._c;return r("div",{staticClass:"security-tips"},[r("div",{staticClass:"tips-header"},[r("i",{staticClass:"el-icon-warning"}),r("span",[t._v("密码安全提示")])]),r("ul",{staticClass:"tips-list"},[r("li",[t._v("密码长度至少8位，包含字母、数字")]),r("li",[t._v("不要使用过于简单的密码")]),r("li",[t._v("建议定期更换密码")]),r("li",[t._v("不要在多个平台使用相同密码")])])])}],o=(s("d9e2"),s("14d9"),{name:"ChangePwd",data(){const t=(t,r,s)=>{""===r?s(new Error("请再次输入新密码")):r!==this.passwordForm.newPassword?s(new Error("两次输入密码不一致")):s()};return{loading:!1,passwordForm:{oldPassword:"",newPassword:"",confirmPassword:""},rules:{oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:8,message:"密码长度至少8位",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d).+$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:t,trigger:"blur"}]}}},computed:{passwordStrength(){const t=this.passwordForm.newPassword;if(!t)return 0;let r=0;return t.length>=8&&(r+=1),t.length>=12&&(r+=1),/[a-z]/.test(t)&&(r+=1),/[A-Z]/.test(t)&&(r+=1),/\d/.test(t)&&(r+=1),/[!@#$%^&*(),.?":{}|<>]/.test(t)&&(r+=1),Math.min(r,4)},passwordStrengthWidth(){return this.passwordStrength/4*100+"%"},passwordStrengthClass(){const t=["weak","fair","good","strong"];return t[Math.max(0,this.passwordStrength-1)]||"weak"},passwordStrengthText(){const t=["弱","一般","良好","强"];return t[Math.max(0,this.passwordStrength-1)]||"弱"}},methods:{changePassword(){this.$refs.passwordForm.validate(t=>{t&&(this.loading=!0,setTimeout(()=>{this.loading=!1,this.$message.success("密码修改成功！"),this.resetForm(),setTimeout(()=>{this.$router.push("/profile")},1500)},1e3))})},resetForm(){this.$refs.passwordForm.resetFields(),this.passwordForm={oldPassword:"",newPassword:"",confirmPassword:""}},goBack(){this.$router.go(-1)}}}),n=o,i=(s("2c85"),s("2877")),c=Object(i["a"])(n,e,a,!1,null,"a6c71daa",null);r["default"]=c.exports},"2ba4":function(t,r,s){"use strict";var e=s("40d5"),a=Function.prototype,o=a.apply,n=a.call;t.exports="object"==typeof Reflect&&Reflect.apply||(e?n.bind(o):function(){return n.apply(o,arguments)})},"2c85":function(t,r,s){"use strict";s("c35f")},"6f19":function(t,r,s){"use strict";var e=s("9112"),a=s("0d26"),o=s("b980"),n=Error.captureStackTrace;t.exports=function(t,r,s,i){o&&(n?n(t,r):e(t,"stack",a(s,i)))}},ab36:function(t,r,s){"use strict";var e=s("861d"),a=s("9112");t.exports=function(t,r){e(r)&&"cause"in r&&a(t,"cause",r.cause)}},aeb0:function(t,r,s){"use strict";var e=s("9bf2").f;t.exports=function(t,r,s){s in t||e(t,s,{configurable:!0,get:function(){return r[s]},set:function(t){r[s]=t}})}},b980:function(t,r,s){"use strict";var e=s("d039"),a=s("5c6c");t.exports=!e((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",a(1,7)),7!==t.stack)}))},c35f:function(t,r,s){},d9e2:function(t,r,s){"use strict";var e=s("23e7"),a=s("da84"),o=s("2ba4"),n=s("e5cb"),i="WebAssembly",c=a[i],u=7!==new Error("e",{cause:7}).cause,d=function(t,r){var s={};s[t]=n(t,r,u),e({global:!0,constructor:!0,arity:1,forced:u},s)},l=function(t,r){if(c&&c[t]){var s={};s[t]=n(i+"."+t,r,u),e({target:i,stat:!0,constructor:!0,arity:1,forced:u},s)}};d("Error",(function(t){return function(r){return o(t,this,arguments)}})),d("EvalError",(function(t){return function(r){return o(t,this,arguments)}})),d("RangeError",(function(t){return function(r){return o(t,this,arguments)}})),d("ReferenceError",(function(t){return function(r){return o(t,this,arguments)}})),d("SyntaxError",(function(t){return function(r){return o(t,this,arguments)}})),d("TypeError",(function(t){return function(r){return o(t,this,arguments)}})),d("URIError",(function(t){return function(r){return o(t,this,arguments)}})),l("CompileError",(function(t){return function(r){return o(t,this,arguments)}})),l("LinkError",(function(t){return function(r){return o(t,this,arguments)}})),l("RuntimeError",(function(t){return function(r){return o(t,this,arguments)}}))},e5cb:function(t,r,s){"use strict";var e=s("d066"),a=s("1a2d"),o=s("9112"),n=s("3a9b"),i=s("d2bb"),c=s("e893"),u=s("aeb0"),d=s("7156"),l=s("e391"),p=s("ab36"),f=s("6f19"),w=s("83ab"),h=s("c430");t.exports=function(t,r,s,m){var g="stackTraceLimit",v=m?2:1,b=t.split("."),P=b[b.length-1],C=e.apply(null,b);if(C){var k=C.prototype;if(!h&&a(k,"cause")&&delete k.cause,!s)return C;var y=e("Error"),F=r((function(t,r){var s=l(m?r:t,void 0),e=m?new C(t):new C;return void 0!==s&&o(e,"message",s),f(e,F,e.stack,2),this&&n(k,this)&&d(e,this,F),arguments.length>v&&p(e,arguments[v]),e}));if(F.prototype=k,"Error"!==P?i?i(F,y):c(F,y,{name:!0}):w&&g in C&&(u(F,C,g),u(F,C,"prepareStackTrace")),c(F,C),!h)try{k.name!==P&&o(k,"name",P),k.constructor=F}catch(_){}return F}}}}]);
//# sourceMappingURL=chunk-100ed178.f0e33fac.js.map