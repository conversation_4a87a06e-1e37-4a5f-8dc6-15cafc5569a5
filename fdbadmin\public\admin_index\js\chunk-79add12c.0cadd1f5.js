(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79add12c"],{"0c98":function(e,t,n){"use strict";var o=function(){var e=this,t=e._self._c;return t("div",{ref:"editor"})},i=[],r=n("6fad"),a=n.n(r),l=n("bc3a"),u=n.n(l);const{$:s,BtnMenu:c,DropListMenu:f,PanelMenu:d,DropList:p,Panel:v,Tooltip:h}=a.a;class A extends c{constructor(e){e;const t=a.a.$('<div class="w-e-menu" data-title="上传文件">\n      <div style="width: 18px;height: 18px;display: flex;"><img src="./file.png"/>\n      <input type="file" style="opacity: 0;width: 16px;height: 16px;position: absolute;" onchange="handleFileChange(this)"/></div>\n      </div>');super(t,e)}clickHandler(){}tryChangeActive(){this.active()}}var m={props:{value:{type:String,default:""},meanArray:{type:Array,default:null}},model:{prop:"value",event:"change"},watch:{value:function(e){e!==this.editor.txt.html()&&this.editor.txt.html(this.value)}},data(){return{defaultMeanus:["head","bold","fontSize","fontName","italic","underline","strikeThrough","indent","lineHeight","foreColor","backColor","link","list","justify","quote","emoticon","image","video","table","code","splitLine","undo","redo","alert"],editor:"",fileName:""}},methods:{init(){const e=this;window.handleFileChange=this.handleFileChange,this.editor=new a.a(this.$refs.editor),this.editor.config.uploadImgShowBase64=!1,this.editor.config.uploadImgServer="/admin/Upload/updateWang",this.editor.config.uploadFileName="file",this.setMenus(),this.editor.config.onchange=t=>{e.$emit("change",t)},this.editor.menus.extend("alertMenu",A),this.editor.config.menus=this.editor.config.menus.concat("alertMenu"),this.editor.create()},handleFileChange(e){let t=new FormData;t.append("file",e.files[0]),t.append("name",e.files[0]["name"]),this.fileName=e.files[0]["name"];let n={headers:{"Content-Type":"multipart/form-data"}},o=this;u.a.post("/admin/upload/uploadFile",t,n).then((function(e){let t=e.code;if(console.log(e),"400"==t)return o.$message.error("文件上传失败,原因:"+e.msg),!1;o.editor.txt.append('<a target="_blank" style="color:blue" download href=\''+e.data.url+"'>"+o.fileName+"</a><br>")}))},setMenus(){this.meanArray?this.editor.config.menus=this.meanArray:this.editor.config.menus=this.defaultMeanus},getHtml(){return this.editor.txt.html()},setHtml(e){this.editor.txt.html(e)}},mounted(){let e=this;e.$nextTick((function(){e.init()}))}},g=m,y=n("2877"),w=Object(y["a"])(g,o,i,!1,null,null,null);t["a"]=w.exports},"6fad":function(e,t,n){(function(t,n){e.exports=n()})(window,(function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=141)}([function(e,t){function n(e){return e&&e.__esModule?e:{default:e}}e.exports=n},function(e,t,n){e.exports=n(142)},function(e,t,n){"use strict";n.r(t),n.d(t,"__extends",(function(){return i})),n.d(t,"__assign",(function(){return r})),n.d(t,"__rest",(function(){return a})),n.d(t,"__decorate",(function(){return l})),n.d(t,"__param",(function(){return u})),n.d(t,"__metadata",(function(){return s})),n.d(t,"__awaiter",(function(){return c})),n.d(t,"__generator",(function(){return f})),n.d(t,"__createBinding",(function(){return d})),n.d(t,"__exportStar",(function(){return p})),n.d(t,"__values",(function(){return v})),n.d(t,"__read",(function(){return h})),n.d(t,"__spread",(function(){return A})),n.d(t,"__spreadArrays",(function(){return m})),n.d(t,"__spreadArray",(function(){return g})),n.d(t,"__await",(function(){return y})),n.d(t,"__asyncGenerator",(function(){return w})),n.d(t,"__asyncDelegator",(function(){return x})),n.d(t,"__asyncValues",(function(){return _})),n.d(t,"__makeTemplateObject",(function(){return E})),n.d(t,"__importStar",(function(){return C})),n.d(t,"__importDefault",(function(){return S})),n.d(t,"__classPrivateFieldGet",(function(){return M})),n.d(t,"__classPrivateFieldSet",(function(){return k}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},o(e,t)};function i(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var r=function(){return r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)};function a(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}function l(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var l=e.length-1;l>=0;l--)(i=e[l])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a}function u(e,t){return function(n,o){t(n,o,e)}}function s(e,t){if("object"===typeof Reflect&&"function"===typeof Reflect.metadata)return Reflect.metadata(e,t)}function c(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function a(e){try{u(o.next(e))}catch(t){r(t)}}function l(e){try{u(o["throw"](e))}catch(t){r(t)}}function u(e){e.done?n(e.value):i(e.value).then(a,l)}u((o=o.apply(e,t||[])).next())}))}function f(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:l(0),throw:l(1),return:l(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function l(e){return function(t){return u([e,t])}}function u(r){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(i=a.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(l){r=[6,l],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}}var d=Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]};function p(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||d(t,e,n)}function v(e){var t="function"===typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"===typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function h(e,t){var n="function"===typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,i,r=n.call(e),a=[];try{while((void 0===t||t-- >0)&&!(o=r.next()).done)a.push(o.value)}catch(l){i={error:l}}finally{try{o&&!o.done&&(n=r["return"])&&n.call(r)}finally{if(i)throw i.error}}return a}function A(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(h(arguments[t]));return e}function m(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,l=r.length;a<l;a++,i++)o[i]=r[a];return o}function g(e,t){for(var n=0,o=t.length,i=e.length;n<o;n++,i++)e[i]=t[n];return e}function y(e){return this instanceof y?(this.v=e,this):new y(e)}function w(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,i=n.apply(e,t||[]),r=[];return o={},a("next"),a("throw"),a("return"),o[Symbol.asyncIterator]=function(){return this},o;function a(e){i[e]&&(o[e]=function(t){return new Promise((function(n,o){r.push([e,t,n,o])>1||l(e,t)}))})}function l(e,t){try{u(i[e](t))}catch(n){f(r[0][3],n)}}function u(e){e.value instanceof y?Promise.resolve(e.value.v).then(s,c):f(r[0][2],e)}function s(e){l("next",e)}function c(e){l("throw",e)}function f(e,t){e(t),r.shift(),r.length&&l(r[0][0],r[0][1])}}function x(e){var t,n;return t={},o("next"),o("throw",(function(e){throw e})),o("return"),t[Symbol.iterator]=function(){return this},t;function o(o,i){t[o]=e[o]?function(t){return(n=!n)?{value:y(e[o](t)),done:"return"===o}:i?i(t):t}:i}}function _(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e="function"===typeof v?v(e):e[Symbol.iterator](),t={},o("next"),o("throw"),o("return"),t[Symbol.asyncIterator]=function(){return this},t);function o(n){t[n]=e[n]&&function(t){return new Promise((function(o,r){t=e[n](t),i(o,r,t.done,t.value)}))}}function i(e,t,n,o){Promise.resolve(o).then((function(t){e({value:t,done:n})}),t)}}function E(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var b=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e["default"]=t};function C(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&d(t,e,n);return b(t,e),t}function S(e){return e&&e.__esModule?e:{default:e}}function M(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)}function k(e,t,n){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,n),n}},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(89)),a=o(n(4)),l=o(n(26)),u=o(n(17)),s=o(n(121)),c=o(n(27)),f=o(n(91)),d=o(n(70)),p=o(n(28)),v=o(n(57));(0,i["default"])(t,"__esModule",{value:!0}),t.DomElement=void 0;var h=n(2),A=n(6),m=[];function g(e){var t=document.createElement("div");t.innerHTML=e;var n=t.children;return A.toArray(n)}function y(e){return!!e&&(e instanceof HTMLCollection||e instanceof NodeList)}function w(e){var t=document.querySelectorAll(e);return A.toArray(t)}function x(e){var t=[],n=[];return t=(0,r["default"])(e)?e:e.split(";"),(0,a["default"])(t).call(t,(function(e){var t,o=(0,l["default"])(t=e.split(":")).call(t,(function(e){return(0,u["default"])(e).call(e)}));2===o.length&&n.push(o[0]+":"+o[1])})),n}var _=function(){function e(t){if(this.elems=[],this.length=this.elems.length,this.dataSource=new s["default"],t){if(t instanceof e)return t;var n=[],o=t instanceof Node?t.nodeType:-1;if(this.selector=t,1===o||9===o)n=[t];else if(y(t))n=A.toArray(t);else if(t instanceof Array)n=t;else if("string"===typeof t){var i,r=(0,u["default"])(i=t.replace("/\n/mg","")).call(i);n=0===(0,c["default"])(r).call(r,"<")?g(r):w(r)}var a=n.length;if(!a)return this;for(var l=0;l<a;l++)this.elems.push(n[l]);this.length=a}}return(0,i["default"])(e.prototype,"id",{get:function(){return this.elems[0].id},enumerable:!1,configurable:!0}),e.prototype.forEach=function(e){for(var t=0;t<this.length;t++){var n=this.elems[t],o=e.call(n,n,t);if(!1===o)break}return this},e.prototype.clone=function(e){var t;void 0===e&&(e=!1);var n=[];return(0,a["default"])(t=this.elems).call(t,(function(t){n.push(t.cloneNode(!!e))})),E(n)},e.prototype.get=function(e){void 0===e&&(e=0);var t=this.length;return e>=t&&(e%=t),E(this.elems[e])},e.prototype.first=function(){return this.get(0)},e.prototype.last=function(){var e=this.length;return this.get(e-1)},e.prototype.on=function(e,t,n){var o;return e?("function"===typeof t&&(n=t,t=""),(0,a["default"])(o=this).call(o,(function(o){if(t){var i=function(e){var o=e.target;o.matches(t)&&n.call(o,e)};o.addEventListener(e,i),m.push({elem:o,selector:t,fn:n,agentFn:i})}else o.addEventListener(e,n)}))):this},e.prototype.off=function(e,t,n){var o;return e?("function"===typeof t&&(n=t,t=""),(0,a["default"])(o=this).call(o,(function(o){if(t){for(var i=-1,r=0;r<m.length;r++){var a=m[r];if(a.selector===t&&a.fn===n&&a.elem===o){i=r;break}}if(-1!==i){var l=(0,f["default"])(m).call(m,i,1)[0].agentFn;o.removeEventListener(e,l)}}else o.removeEventListener(e,n)}))):this},e.prototype.attr=function(e,t){var n;return null==t?this.elems[0].getAttribute(e)||"":(0,a["default"])(n=this).call(n,(function(n){n.setAttribute(e,t)}))},e.prototype.removeAttr=function(e){var t;(0,a["default"])(t=this).call(t,(function(t){t.removeAttribute(e)}))},e.prototype.addClass=function(e){var t;return e?(0,a["default"])(t=this).call(t,(function(t){if(t.className){var n=t.className.split(/\s/);n=(0,d["default"])(n).call(n,(function(e){return!!(0,u["default"])(e).call(e)})),(0,c["default"])(n).call(n,e)<0&&n.push(e),t.className=n.join(" ")}else t.className=e})):this},e.prototype.removeClass=function(e){var t;return e?(0,a["default"])(t=this).call(t,(function(t){if(t.className){var n=t.className.split(/\s/);n=(0,d["default"])(n).call(n,(function(t){return t=(0,u["default"])(t).call(t),!(!t||t===e)})),t.className=n.join(" ")}})):this},e.prototype.hasClass=function(e){if(!e)return!1;var t=this.elems[0];if(!t.className)return!1;var n=t.className.split(/\s/);return(0,p["default"])(n).call(n,e)},e.prototype.css=function(e,t){var n,o;return o=""==t?"":e+":"+t+";",(0,a["default"])(n=this).call(n,(function(t){var n,i=(0,u["default"])(n=t.getAttribute("style")||"").call(n);if(i){var r=x(i);r=(0,l["default"])(r).call(r,(function(t){return 0===(0,c["default"])(t).call(t,e)?o:t})),""!=o&&(0,c["default"])(r).call(r,o)<0&&r.push(o),""==o&&(r=x(r)),t.setAttribute("style",r.join("; "))}else t.setAttribute("style",o)}))},e.prototype.getBoundingClientRect=function(){var e=this.elems[0];return e.getBoundingClientRect()},e.prototype.show=function(){return this.css("display","block")},e.prototype.hide=function(){return this.css("display","none")},e.prototype.children=function(){var e=this.elems[0];return e?E(e.children):null},e.prototype.childNodes=function(){var e=this.elems[0];return e?E(e.childNodes):null},e.prototype.replaceChildAll=function(e){var t=this.getNode(),n=this.elems[0];while(n.hasChildNodes())t.firstChild&&n.removeChild(t.firstChild);this.append(e)},e.prototype.append=function(e){var t;return(0,a["default"])(t=this).call(t,(function(t){(0,a["default"])(e).call(e,(function(e){t.appendChild(e)}))}))},e.prototype.remove=function(){var e;return(0,a["default"])(e=this).call(e,(function(e){if(e.remove)e.remove();else{var t=e.parentElement;t&&t.removeChild(e)}}))},e.prototype.isContain=function(e){var t=this.elems[0],n=e.elems[0];return t.contains(n)},e.prototype.getNodeName=function(){var e=this.elems[0];return e.nodeName},e.prototype.getNode=function(e){var t;return void 0===e&&(e=0),t=this.elems[e],t},e.prototype.find=function(e){var t=this.elems[0];return E(t.querySelectorAll(e))},e.prototype.text=function(e){var t;if(e)return(0,a["default"])(t=this).call(t,(function(t){t.innerHTML=e}));var n=this.elems[0];return n.innerHTML.replace(/<[^>]+>/g,(function(){return""}))},e.prototype.html=function(e){var t=this.elems[0];return e?(t.innerHTML=e,this):t.innerHTML},e.prototype.val=function(){var e,t=this.elems[0];return(0,u["default"])(e=t.value).call(e)},e.prototype.focus=function(){var e;return(0,a["default"])(e=this).call(e,(function(e){e.focus()}))},e.prototype.prev=function(){var e=this.elems[0];return E(e.previousElementSibling)},e.prototype.next=function(){var e=this.elems[0];return E(e.nextElementSibling)},e.prototype.getNextSibling=function(){var e=this.elems[0];return E(e.nextSibling)},e.prototype.parent=function(){var e=this.elems[0];return E(e.parentElement)},e.prototype.parentUntil=function(e,t){var n=t||this.elems[0];if("BODY"===n.nodeName)return null;var o=n.parentElement;return null===o?null:o.matches(e)?E(o):this.parentUntil(e,o)},e.prototype.parentUntilEditor=function(e,t,n){var o=n||this.elems[0];if(E(o).equal(t.$textContainerElem)||E(o).equal(t.$toolbarElem))return null;var i=o.parentElement;return null===i?null:i.matches(e)?E(i):this.parentUntilEditor(e,t,i)},e.prototype.equal=function(t){return t instanceof e?this.elems[0]===t.elems[0]:t instanceof HTMLElement&&this.elems[0]===t},e.prototype.insertBefore=function(e){var t,n=E(e),o=n.elems[0];return o?(0,a["default"])(t=this).call(t,(function(e){var t=o.parentNode;null===t||void 0===t||t.insertBefore(e,o)})):this},e.prototype.insertAfter=function(e){var t,n=E(e),o=n.elems[0],i=o&&o.nextSibling;return o?(0,a["default"])(t=this).call(t,(function(e){var t=o.parentNode;i?t.insertBefore(e,i):t.appendChild(e)})):this},e.prototype.data=function(e,t){if(null==t)return this.dataSource.get(e);this.dataSource.set(e,t)},e.prototype.getNodeTop=function(e){if(this.length<1)return this;var t=this.parent();return e.$textElem.equal(this)||e.$textElem.equal(t)?this:(t.prior=this,t.getNodeTop(e))},e.prototype.getOffsetData=function(){var e=this.elems[0];return{top:e.offsetTop,left:e.offsetLeft,width:e.offsetWidth,height:e.offsetHeight,parent:e.offsetParent}},e.prototype.scrollTop=function(e){var t=this.elems[0];t.scrollTo({top:e})},e}();function E(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return new((0,v["default"])(_).apply(_,h.__spreadArrays([void 0],e)))}t.DomElement=_,t["default"]=E},function(e,t,n){e.exports=n(180)},function(e,t,n){"use strict";var o=n(8),i=n(71).f,r=n(101),a=n(9),l=n(40),u=n(19),s=n(16),c=function(e){var t=function(t,n,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,o)}return e.apply(this,arguments)};return t.prototype=e.prototype,t};e.exports=function(e,t){var n,f,d,p,v,h,A,m,g,y=e.target,w=e.global,x=e.stat,_=e.proto,E=w?o:x?o[y]:(o[y]||{}).prototype,b=w?a:a[y]||(a[y]={}),C=b.prototype;for(p in t)n=r(w?p:y+(x?".":"#")+p,e.forced),f=!n&&E&&s(E,p),h=b[p],f&&(e.noTargetGet?(g=i(E,p),A=g&&g.value):A=E[p]),v=f&&A?A:t[p],f&&typeof h===typeof v||(m=e.bind&&f?l(v,o):e.wrap&&f?c(v):_&&"function"==typeof v?l(Function.call,v):v,(e.sham||v&&v.sham||h&&h.sham)&&u(m,"sham",!0),b[p]=m,_&&(d=y+"Prototype",s(a,d)||u(a,d,{}),a[d][p]=v,e.real&&C&&!C[p]&&u(C,p,v)))}},function(e,t,n){"use strict";var o=n(0),i=o(n(92)),r=o(n(1)),a=o(n(256)),l=o(n(45)),u=o(n(46)),s=o(n(89)),c=o(n(26));(0,r["default"])(t,"__esModule",{value:!0}),t.hexToRgb=t.getRandomCode=t.toArray=t.deepClone=t.isFunction=t.debounce=t.throttle=t.arrForEach=t.forEach=t.replaceSpecialSymbol=t.replaceHtmlSymbol=t.getRandom=t.UA=void 0;var f=n(2),d=function(){function e(){this._ua=navigator.userAgent;var e=this._ua.match(/(Edge?)\/(\d+)/);this.isOldEdge=!!(e&&"Edge"==e[1]&&(0,a["default"])(e[2])<19),this.isFirefox=!(!/Firefox\/\d+/.test(this._ua)||/Seamonkey\/\d+/.test(this._ua))}return e.prototype.isIE=function(){return"ActiveXObject"in window},e.prototype.isWebkit=function(){return/webkit/i.test(this._ua)},e}();function p(e){var t;return void 0===e&&(e=""),e+(0,l["default"])(t=Math.random().toString()).call(t,2)}function v(e){return e.replace(/</gm,"&lt;").replace(/>/gm,"&gt;").replace(/"/gm,"&quot;").replace(/(\r\n|\r|\n)/g,"<br/>")}function h(e){return e.replace(/&lt;/gm,"<").replace(/&gt;/gm,">").replace(/&quot;/gm,'"')}function A(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var o=t(n,e[n]);if(!1===o)break}}function m(e,t){var n,o,i,r=e.length||0;for(n=0;n<r;n++)if(o=e[n],i=t.call(e,o,n),!1===i)break}function g(e,t){void 0===t&&(t=200);var n=!1;return function(){for(var o=this,i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];n||(n=!0,(0,u["default"])((function(){n=!1,e.call.apply(e,f.__spreadArrays([o],i))}),t))}}function y(e,t){void 0===t&&(t=200);var n=0;return function(){for(var o=this,i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];n&&window.clearTimeout(n),n=(0,u["default"])((function(){n=0,e.call.apply(e,f.__spreadArrays([o],i))}),t)}}function w(e){return"function"===typeof e}function x(e){if("object"!==(0,i["default"])(e)||"function"==typeof e||null===e)return e;var t;for(var n in(0,s["default"])(e)&&(t=[]),(0,s["default"])(e)||(t={}),e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=x(e[n]));return t}function _(e){return(0,l["default"])(Array.prototype).call(e)}function E(){var e;return(0,l["default"])(e=Math.random().toString(36)).call(e,-5)}function b(e){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);if(null==t)return null;var n=(0,c["default"])(t).call(t,(function(e){return(0,a["default"])(e,16)})),o=n[1],i=n[2],r=n[3];return"rgb("+o+", "+i+", "+r+")"}t.UA=new d,t.getRandom=p,t.replaceHtmlSymbol=v,t.replaceSpecialSymbol=h,t.forEach=A,t.arrForEach=m,t.throttle=g,t.debounce=y,t.isFunction=w,t.deepClone=x,t.toArray=_,t.getRandomCode=E,t.hexToRgb=b},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(){}(0,i["default"])(t,"__esModule",{value:!0}),t.EMPTY_P_REGEX=t.EMPTY_P_LAST_REGEX=t.EMPTY_P=t.urlRegex=t.EMPTY_FN=void 0,t.EMPTY_FN=r,t.urlRegex=/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&amp;:/~+#]*[\w\-@?^=%&amp;/~+#])?/g,t.EMPTY_P='<p data-we-empty-p=""><br></p>',t.EMPTY_P_LAST_REGEX=/<p data-we-empty-p=""><br\/?><\/p>$/gim,t.EMPTY_P_REGEX=/<p data-we-empty-p="">/gim},function(e,t,n){(function(t){var n=function(e){return e&&e.Math==Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t&&t)||Function("return this")()}).call(this,n(145))},function(e,t){e.exports={}},function(e,t,n){var o=n(8),i=n(74),r=n(16),a=n(64),l=n(76),u=n(106),s=i("wks"),c=o.Symbol,f=u?c:c&&c.withoutSetter||a;e.exports=function(e){return r(s,e)||(l&&r(c,e)?s[e]=c[e]:s[e]=f("Symbol."+e)),s[e]}},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,n){var o=n(9),i=n(16),r=n(93),a=n(18).f;e.exports=function(e){var t=o.Symbol||(o.Symbol={});i(t,e)||a(t,e,{value:r.f(e)})}},function(e,t){e.exports=function(e){return"object"===typeof e?null!==e:"function"===typeof e}},function(e,t,n){var o=n(11);e.exports=!o((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(e,t,n){var o=n(9);e.exports=function(e){return o[e+"Prototype"]}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){e.exports=n(192)},function(e,t,n){var o=n(14),i=n(100),r=n(25),a=n(60),l=Object.defineProperty;t.f=o?l:function(e,t,n){if(r(e),t=a(t,!0),r(n),i)try{return l(e,t,n)}catch(o){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var o=n(14),i=n(18),r=n(48);e.exports=o?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){"use strict";var o=function(){var e;return function(){return"undefined"===typeof e&&(e=Boolean(window&&document&&document.all&&!window.atob)),e}}(),i=function(){var e={};return function(t){if("undefined"===typeof e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(o){n=null}e[t]=n}return e[t]}}(),r=[];function a(e){for(var t=-1,n=0;n<r.length;n++)if(r[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},o=[],i=0;i<e.length;i++){var l=e[i],u=t.base?l[0]+t.base:l[0],s=n[u]||0,c="".concat(u," ").concat(s);n[u]=s+1;var f=a(c),d={css:l[1],media:l[2],sourceMap:l[3]};-1!==f?(r[f].references++,r[f].updater(d)):r.push({identifier:c,updater:h(d,t),references:1}),o.push(c)}return o}function u(e){var t=document.createElement("style"),o=e.attributes||{};if("undefined"===typeof o.nonce){var r=n.nc;r&&(o.nonce=r)}if(Object.keys(o).forEach((function(e){t.setAttribute(e,o[e])})),"function"===typeof e.insert)e.insert(t);else{var a=i(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}function s(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}var c=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function f(e,t,n,o){var i=n?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=c(t,i);else{var r=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(r,a[t]):e.appendChild(r)}}function d(e,t,n){var o=n.css,i=n.media,r=n.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),r&&"undefined"!==typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{while(e.firstChild)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var p=null,v=0;function h(e,t){var n,o,i;if(t.singleton){var r=v++;n=p||(p=u(t)),o=f.bind(null,n,r,!1),i=f.bind(null,n,r,!0)}else n=u(t),o=d.bind(null,n,t),i=function(){s(n)};return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else i()}}e.exports=function(e,t){t=t||{},t.singleton||"boolean"===typeof t.singleton||(t.singleton=o()),e=e||[];var n=l(e,t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<n.length;o++){var i=n[o],u=a(i);r[u].references--}for(var s=l(e,t),c=0;c<n.length;c++){var f=n[c],d=a(f);0===r[d].references&&(r[d].updater(),r.splice(d,1))}n=s}}}},function(e,t,n){"use strict";function o(e,t){var n=e[1]||"",o=e[3];if(!o)return n;if(t&&"function"===typeof btoa){var r=i(o),a=o.sources.map((function(e){return"/*# sourceURL=".concat(o.sourceRoot||"").concat(e," */")}));return[n].concat(a).concat([r]).join("\n")}return[n].join("\n")}function i(e){var t=btoa(unescape(encodeURIComponent(JSON.stringify(e)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(t);return"/*# ".concat(n," */")}e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=o(t,e);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,o){"string"===typeof e&&(e=[[null,e,""]]);var i={};if(o)for(var r=0;r<this.length;r++){var a=this[r][0];null!=a&&(i[a]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);o&&i[u[0]]||(n&&(u[2]?u[2]="".concat(n," and ").concat(u[2]):u[2]=n),t.push(u))}},t}},function(e,t,n){var o=n(14),i=n(11),r=n(16),a=Object.defineProperty,l={},u=function(e){throw e};e.exports=function(e,t){if(r(l,e))return l[e];t||(t={});var n=[][e],s=!!r(t,"ACCESSORS")&&t.ACCESSORS,c=r(t,0)?t[0]:u,f=r(t,1)?t[1]:void 0;return l[e]=!!n&&!i((function(){if(s&&!o)return!0;var e={length:-1};s?a(e,1,{enumerable:!0,get:u}):e[1]=1,n.call(e,c,f)}))}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(95)),l=function(e){function t(t,n){return e.call(this,t,n)||this}return r.__extends(t,e),t}(a["default"]);t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(46));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=l.__importDefault(n(95)),c=l.__importDefault(n(134)),f=function(e){function t(t,n,o){var i=e.call(this,t,n)||this;o.title=n.i18next.t("menus.dropListMenu."+o.title);var l,s="zh-CN"===n.config.lang?"":"w-e-drop-list-tl";""!==s&&"list"===o.type&&(0,r["default"])(l=o.list).call(l,(function(e){var t=e.$elem,n=u["default"](t.children());if(n.length>0){var o=null===n||void 0===n?void 0:n.getNodeName();o&&"I"===o&&t.addClass(s)}}));var f=new c["default"](i,o);return i.dropList=f,t.on("click",(function(){var e;null!=n.selection.getRange()&&(t.css("z-index",n.zIndex.get("menu")),(0,r["default"])(e=n.txt.eventHooks.dropListMenuHoverEvents).call(e,(function(e){return e()})),f.show())})).on("mouseleave",(function(){t.css("z-index","auto"),f.hideTimeoutId=(0,a["default"])((function(){f.hide()}))})),i}return l.__extends(t,e),t}(s["default"]);t["default"]=f},function(e,t,n){var o=n(13);e.exports=function(e){if(!o(e))throw TypeError(String(e)+" is not an object");return e}},function(e,t,n){e.exports=n(188)},function(e,t,n){e.exports=n(201)},function(e,t,n){e.exports=n(213)},function(e,t,n){e.exports=n(283)},function(e,t,n){var o=n(72),i=n(49);e.exports=function(e){return o(i(e))}},function(e,t,n){var o=n(49);e.exports=function(e){return Object(o(e))}},function(e,t,n){var o=n(40),i=n(72),r=n(31),a=n(35),l=n(88),u=[].push,s=function(e){var t=1==e,n=2==e,s=3==e,c=4==e,f=6==e,d=5==e||f;return function(p,v,h,A){for(var m,g,y=r(p),w=i(y),x=o(v,h,3),_=a(w.length),E=0,b=A||l,C=t?b(p,_):n?b(p,0):void 0;_>E;E++)if((d||E in w)&&(m=w[E],g=x(m,E,y),e))if(t)C[E]=g;else if(g)switch(e){case 3:return!0;case 5:return m;case 6:return E;case 2:u.call(C,m)}else if(c)return!1;return f?-1:s||c?c:C}};e.exports={forEach:s(0),map:s(1),filter:s(2),some:s(3),every:s(4),find:s(5),findIndex:s(6)}},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(29)),l=o(n(132));(0,i["default"])(t,"__esModule",{value:!0});var u=n(2),s=u.__importDefault(n(3)),c=n(7),f=function(){function e(t,n){this.menu=t,this.conf=n,this.$container=s["default"]('<div class="w-e-panel-container"></div>');var o=t.editor;o.txt.eventHooks.clickEvents.push(e.hideCurAllPanels),o.txt.eventHooks.toolbarClickEvents.push(e.hideCurAllPanels),o.txt.eventHooks.dropListMenuHoverEvents.push(e.hideCurAllPanels)}return e.prototype.create=function(){var t=this,n=this.menu;if(!e.createdMenus.has(n)){var o=this.conf,i=this.$container,l=o.width||300,f=n.editor.$toolbarElem.getBoundingClientRect(),d=n.$elem.getBoundingClientRect(),p=f.height+f.top-d.top,v=(f.width-l)/2+f.left-d.left,h=300;Math.abs(v)>h&&(v=d.left<document.documentElement.clientWidth/2?-d.width/2:-l+d.width/2),i.css("width",l+"px").css("margin-top",p+"px").css("margin-left",v+"px").css("z-index",n.editor.zIndex.get("panel"));var A=s["default"]('<i class="w-e-icon-close w-e-panel-close"></i>');i.append(A),A.on("click",(function(){t.remove()}));var m=s["default"]('<ul class="w-e-panel-tab-title"></ul>'),g=s["default"]('<div class="w-e-panel-tab-content"></div>');i.append(m).append(g);var y=o.height;y&&g.css("height",y+"px").css("overflow-y","auto");var w=o.tabs||[],x=[],_=[];(0,r["default"])(w).call(w,(function(e,t){if(e){var n=e.title||"",o=e.tpl||"",i=s["default"]('<li class="w-e-item">'+n+"</li>");m.append(i);var a=s["default"](o);g.append(a),x.push(i),_.push(a),0===t?(i.data("active",!0),i.addClass("w-e-active")):a.hide(),i.on("click",(function(){i.data("active")||((0,r["default"])(x).call(x,(function(e){e.data("active",!1),e.removeClass("w-e-active")})),(0,r["default"])(_).call(_,(function(e){e.hide()})),i.data("active",!0),i.addClass("w-e-active"),a.show())}))}})),i.on("click",(function(e){e.stopPropagation()})),n.$elem.append(i),o.setLinkValue&&o.setLinkValue(i,"text"),o.setLinkValue&&o.setLinkValue(i,"link"),(0,r["default"])(w).call(w,(function(e,n){if(e){var o=e.events||[];(0,r["default"])(o).call(o,(function(e){var o,i=e.selector,r=e.type,l=e.fn||c.EMPTY_FN,s=_[n],f=null!==(o=e.bindEnter)&&void 0!==o&&o,d=function(e){return u.__awaiter(t,void 0,void 0,(function(){var t;return u.__generator(this,(function(n){switch(n.label){case 0:return e.stopPropagation(),[4,l(e)];case 1:return t=n.sent(),t&&this.remove(),[2]}}))}))};(0,a["default"])(s).call(s,i).on(r,d),f&&"click"===r&&s.on("keyup",(function(e){13==e.keyCode&&d(e)}))}))}}));var E=(0,a["default"])(i).call(i,"input[type=text],textarea");E.length&&E.get(0).focus(),e.hideCurAllPanels(),n.setPanel(this),e.createdMenus.add(n)}},e.prototype.remove=function(){var t=this.menu,n=this.$container;n&&n.remove(),e.createdMenus["delete"](t)},e.hideCurAllPanels=function(){var t;0!==e.createdMenus.size&&(0,r["default"])(t=e.createdMenus).call(t,(function(e){var t=e.panel;t&&t.remove()}))},e.createdMenus=new l["default"],e}();t["default"]=f},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,n){var o=n(62),i=Math.min;e.exports=function(e){return e>0?i(o(e),9007199254740991):0}},function(e,t,n){var o=n(9),i=n(8),r=function(e){return"function"==typeof e?e:void 0};e.exports=function(e,t){return arguments.length<2?r(o[e])||r(i[e]):o[e]&&o[e][t]||i[e]&&i[e][t]}},function(e,t,n){var o=n(81),i=n(18).f,r=n(19),a=n(16),l=n(170),u=n(10),s=u("toStringTag");e.exports=function(e,t,n,u){if(e){var c=n?e:e.prototype;a(c,s)||i(c,s,{configurable:!0,value:t}),u&&!o&&r(c,"toString",l)}}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(95)),l=function(e){function t(t,n){return e.call(this,t,n)||this}return r.__extends(t,e),t.prototype.setPanel=function(e){this.panel=e},t}(a["default"]);t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(57));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=function(){function e(e,t,n){this.editor=e,this.$targetElem=t,this.conf=n,this._show=!1,this._isInsertTextContainer=!1;var o=u["default"]("<div></div>");o.addClass("w-e-tooltip"),this.$container=o}return e.prototype.getPositionData=function(){var e=this.$container,t=0,n=0,o=20,i=document.documentElement.scrollTop,r=this.$targetElem.getBoundingClientRect(),a=this.editor.$textElem.getBoundingClientRect(),l=this.$targetElem.getOffsetData(),s=u["default"](l.parent),c=this.editor.$textElem.elems[0].scrollTop;if(this._isInsertTextContainer=s.equal(this.editor.$textContainerElem),this._isInsertTextContainer){var f=s.getBoundingClientRect().height,d=l.top,p=l.left,v=l.height,h=d-c;h>o+5?(t=h-o-15,e.addClass("w-e-tooltip-up")):h+v+o<f?(t=h+v+10,e.addClass("w-e-tooltip-down")):(t=(h>0?h:0)+o+10,e.addClass("w-e-tooltip-down")),n=p<0?0:p}else r.top<o||r.top-a.top<o?(t=r.bottom+i+5,e.addClass("w-e-tooltip-down")):(t=r.top+i-o-15,e.addClass("w-e-tooltip-up")),n=r.left<0?0:r.left;return{top:t,left:n}},e.prototype.appendMenus=function(){var e=this,t=this.conf,n=this.editor,o=this.$targetElem,i=this.$container;(0,r["default"])(t).call(t,(function(t,r){var a=t.$elem,l=u["default"]("<div></div>");l.addClass("w-e-tooltip-item-wrapper "),l.append(a),i.append(l),a.on("click",(function(i){i.preventDefault();var r=t.onClick(n,o);r&&e.remove()}))}))},e.prototype.create=function(){var e,t,n=this.editor,o=this.$container;this.appendMenus();var i=this.getPositionData(),r=i.top,l=i.left;o.css("top",r+"px"),o.css("left",l+"px"),o.css("z-index",n.zIndex.get("tooltip")),this._isInsertTextContainer?this.editor.$textContainerElem.append(o):u["default"]("body").append(o),this._show=!0,n.beforeDestroy((0,a["default"])(e=this.remove).call(e,this)),n.txt.eventHooks.onBlurEvents.push((0,a["default"])(t=this.remove).call(t,this))},e.prototype.remove=function(){this.$container.remove(),this._show=!1},(0,i["default"])(e.prototype,"isShow",{get:function(){return this._show},enumerable:!1,configurable:!0}),e}();t["default"]=s},function(e,t,n){var o=n(41);e.exports=function(e,t,n){if(o(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,i){return e.call(t,n,o,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e}},function(e,t,n){var o,i,r,a=n(165),l=n(8),u=n(13),s=n(19),c=n(16),f=n(63),d=n(51),p=l.WeakMap,v=function(e){return r(e)?i(e):o(e,{})},h=function(e){return function(t){var n;if(!u(t)||(n=i(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}};if(a){var A=new p,m=A.get,g=A.has,y=A.set;o=function(e,t){return y.call(A,e,t),t},i=function(e){return m.call(A,e)||{}},r=function(e){return g.call(A,e)}}else{var w=f("state");d[w]=!0,o=function(e,t){return s(e,w,t),t},i=function(e){return c(e,w)?e[w]:{}},r=function(e){return c(e,w)}}e.exports={set:o,get:i,has:r,enforce:v,getterFor:h}},function(e,t){e.exports=!0},function(e,t){e.exports={}},function(e,t,n){e.exports=n(261)},function(e,t,n){e.exports=n(265)},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0}),t.createElementFragment=t.createDocumentFragment=t.createElement=t.insertBefore=t.getEndPoint=t.getStartPoint=t.updateRange=t.filterSelectionNodes=void 0;var a=n(2),l=n(137),u=a.__importDefault(n(3));function s(e){var t=[];return(0,r["default"])(e).call(e,(function(e){var n=e.getNodeName();if(n!==l.ListType.OrderedList&&n!==l.ListType.UnorderedList)t.push(e);else if(e.prior)t.push(e.prior);else{var o=e.children();null===o||void 0===o||(0,r["default"])(o).call(o,(function(e){t.push(u["default"](e))}))}})),t}function c(e,t,n){var o=e.selection,i=document.createRange();t.length>1?(i.setStart(t.elems[0],0),i.setEnd(t.elems[t.length-1],t.elems[t.length-1].childNodes.length)):i.selectNodeContents(t.elems[0]),n&&i.collapse(!1),o.saveRange(i),o.restoreSelection()}function f(e){var t;return e.prior?e.prior:u["default"](null===(t=e.children())||void 0===t?void 0:t.elems[0])}function d(e){var t;return e.prior?e.prior:u["default"](null===(t=e.children())||void 0===t?void 0:t.last().elems[0])}function p(e,t,n){void 0===n&&(n=null),e.parent().elems[0].insertBefore(t,n)}function v(e){return document.createElement(e)}function h(){return document.createDocumentFragment()}function A(e,t,n){return void 0===n&&(n="li"),(0,r["default"])(e).call(e,(function(e){var o=v(n);o.innerHTML=e.html(),t.appendChild(o),e.remove()})),t}t.filterSelectionNodes=s,t.updateRange=c,t.getStartPoint=f,t.getEndPoint=d,t.insertBefore=p,t.createElement=v,t.createDocumentFragment=h,t.createElementFragment=A},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on "+e);return e}},function(e,t,n){"use strict";var o=n(164).charAt,i=n(42),r=n(75),a="String Iterator",l=i.set,u=i.getterFor(a);r(String,"String",(function(e){l(this,{type:a,string:String(e),index:0})}),(function(){var e,t=u(this),n=t.string,i=t.index;return i>=n.length?{value:void 0,done:!0}:(e=o(n,i),t.index+=e.length,{value:e,done:!1})}))},function(e,t){e.exports={}},function(e,t,n){var o=n(107),i=n(80);e.exports=Object.keys||function(e){return o(e,i)}},function(e,t,n){var o=n(19);e.exports=function(e,t,n,i){i&&i.enumerable?e[t]=n:o(e,t,n)}},function(e,t,n){n(173);var o=n(174),i=n(8),r=n(65),a=n(19),l=n(44),u=n(10),s=u("toStringTag");for(var c in o){var f=i[c],d=f&&f.prototype;d&&r(d)!==s&&a(d,s,c),l[c]=l.Array}},function(e,t,n){var o=n(34);e.exports=Array.isArray||function(e){return"Array"==o(e)}},function(e,t,n){var o=n(11),i=n(10),r=n(86),a=i("species");e.exports=function(e){return r>=51||!o((function(){var t=[],n=t.constructor={};return n[a]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},function(e,t,n){e.exports=n(222)},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.ListHandle=void 0;var r=n(2),a=r.__importDefault(n(373)),l=function(){function e(e){this.options=e,this.selectionRangeElem=new a["default"]}return e}();t.ListHandle=l},function(e,t,n){"use strict";var o={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,r=i&&!o.call({1:2},1);t.f=r?function(e){var t=i(this,e);return!!t&&t.enumerable}:o},function(e,t,n){var o=n(13);e.exports=function(e,t){if(!o(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!o(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!o(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!o(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){},function(e,t){var n=Math.ceil,o=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?o:n)(e)}},function(e,t,n){var o=n(74),i=n(64),r=o("keys");e.exports=function(e){return r[e]||(r[e]=i(e))}},function(e,t){var n=0,o=Math.random();e.exports=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++n+o).toString(36)}},function(e,t,n){var o=n(81),i=n(34),r=n(10),a=r("toStringTag"),l="Arguments"==i(function(){return arguments}()),u=function(e,t){try{return e[t]}catch(n){}};e.exports=o?i:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=u(t=Object(e),a))?n:l?i(t):"Object"==(o=i(t))&&"function"==typeof t.callee?"Arguments":o}},function(e,t,n){var o=n(25),i=n(112),r=n(35),a=n(40),l=n(113),u=n(114),s=function(e,t){this.stopped=e,this.result=t},c=e.exports=function(e,t,n,c,f){var d,p,v,h,A,m,g,y=a(t,n,c?2:1);if(f)d=e;else{if(p=l(e),"function"!=typeof p)throw TypeError("Target is not iterable");if(i(p)){for(v=0,h=r(e.length);h>v;v++)if(A=c?y(o(g=e[v])[0],g[1]):y(e[v]),A&&A instanceof s)return A;return new s(!1)}d=p.call(e)}m=d.next;while(!(g=m.call(d)).done)if(A=u(d,y,g.value,c),"object"==typeof A&&A&&A instanceof s)return A;return new s(!1)};c.stop=function(e){return new s(!0,e)}},function(e,t,n){"use strict";var o=n(11);e.exports=function(e,t){var n=[][e];return!!n&&o((function(){n.call(null,t||function(){throw 1},1)}))}},function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},function(e,t,n){"use strict";var o=n(60),i=n(18),r=n(48);e.exports=function(e,t,n){var a=o(t);a in e?i.f(e,a,r(0,n)):e[a]=n}},function(e,t,n){e.exports=n(209)},function(e,t,n){var o=n(14),i=n(59),r=n(48),a=n(30),l=n(60),u=n(16),s=n(100),c=Object.getOwnPropertyDescriptor;t.f=o?c:function(e,t){if(e=a(e),t=l(t,!0),s)try{return c(e,t)}catch(n){}if(u(e,t))return r(!i.f.call(e,t),e[t])}},function(e,t,n){var o=n(11),i=n(34),r="".split;e.exports=o((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==i(e)?r.call(e,""):Object(e)}:Object},function(e,t,n){var o=n(8),i=n(13),r=o.document,a=i(r)&&i(r.createElement);e.exports=function(e){return a?r.createElement(e):{}}},function(e,t,n){var o=n(43),i=n(103);(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.4",mode:o?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,t,n){"use strict";var o=n(5),i=n(167),r=n(105),a=n(171),l=n(37),u=n(19),s=n(53),c=n(10),f=n(43),d=n(44),p=n(104),v=p.IteratorPrototype,h=p.BUGGY_SAFARI_ITERATORS,A=c("iterator"),m="keys",g="values",y="entries",w=function(){return this};e.exports=function(e,t,n,c,p,x,_){i(n,t,c);var E,b,C,S=function(e){if(e===p&&T)return T;if(!h&&e in D)return D[e];switch(e){case m:return function(){return new n(this,e)};case g:return function(){return new n(this,e)};case y:return function(){return new n(this,e)}}return function(){return new n(this)}},M=t+" Iterator",k=!1,D=e.prototype,N=D[A]||D["@@iterator"]||p&&D[p],T=!h&&N||S(p),B="Array"==t&&D.entries||N;if(B&&(E=r(B.call(new e)),v!==Object.prototype&&E.next&&(f||r(E)===v||(a?a(E,v):"function"!=typeof E[A]&&u(E,A,w)),l(E,M,!0,!0),f&&(d[M]=w))),p==g&&N&&N.name!==g&&(k=!0,T=function(){return N.call(this)}),f&&!_||D[A]===T||u(D,A,T),d[t]=T,p)if(b={values:S(g),keys:x?T:S(m),entries:S(y)},_)for(C in b)(h||k||!(C in D))&&s(D,C,b[C]);else o({target:t,proto:!0,forced:h||k},b);return b}},function(e,t,n){var o=n(11);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){return!String(Symbol())}))},function(e,t,n){var o,i=n(25),r=n(169),a=n(80),l=n(51),u=n(108),s=n(73),c=n(63),f=">",d="<",p="prototype",v="script",h=c("IE_PROTO"),A=function(){},m=function(e){return d+v+f+e+d+"/"+v+f},g=function(e){e.write(m("")),e.close();var t=e.parentWindow.Object;return e=null,t},y=function(){var e,t=s("iframe"),n="java"+v+":";return t.style.display="none",u.appendChild(t),t.src=String(n),e=t.contentWindow.document,e.open(),e.write(m("document.F=Object")),e.close(),e.F},w=function(){try{o=document.domain&&new ActiveXObject("htmlfile")}catch(t){}w=o?g(o):y();var e=a.length;while(e--)delete w[p][a[e]];return w()};l[h]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(A[p]=i(e),n=new A,A[p]=null,n[h]=e):n=w(),void 0===t?n:r(n,t)}},function(e,t,n){var o=n(30),i=n(35),r=n(79),a=function(e){return function(t,n,a){var l,u=o(t),s=i(u.length),c=r(a,s);if(e&&n!=n){while(s>c)if(l=u[c++],l!=l)return!0}else for(;s>c;c++)if((e||c in u)&&u[c]===n)return e||c||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},function(e,t,n){var o=n(62),i=Math.max,r=Math.min;e.exports=function(e,t){var n=o(e);return n<0?i(n+t,0):r(n,t)}},function(e,t){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(e,t,n){var o=n(10),i=o("toStringTag"),r={};r[i]="z",e.exports="[object z]"===String(r)},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e}},function(e,t,n){var o=n(36);e.exports=o("navigator","userAgent")||""},function(e,t,n){"use strict";var o=n(41),i=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=o(t),this.reject=o(n)};e.exports.f=function(e){return new i(e)}},function(e,t,n){var o,i,r=n(8),a=n(84),l=r.process,u=l&&l.versions,s=u&&u.v8;s?(o=s.split("."),i=o[0]+o[1]):a&&(o=a.match(/Edge\/(\d+)/),(!o||o[1]>=74)&&(o=a.match(/Chrome\/(\d+)/),o&&(i=o[1]))),e.exports=i&&+i},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(6),s=a.__importDefault(n(267)),c=a.__importDefault(n(280)),f=a.__importDefault(n(281)),d=a.__importDefault(n(282)),p=a.__importDefault(n(301)),v=a.__importStar(n(416)),h=a.__importDefault(n(417)),A=a.__importDefault(n(418)),m=a.__importDefault(n(419)),g=a.__importStar(n(420)),y=a.__importDefault(n(423)),w=a.__importDefault(n(424)),x=a.__importDefault(n(425)),_=a.__importDefault(n(427)),E=a.__importDefault(n(437)),b=a.__importDefault(n(440)),C=a.__importStar(n(441)),S=a.__importDefault(n(23)),M=a.__importDefault(n(134)),k=a.__importDefault(n(24)),D=a.__importDefault(n(33)),N=a.__importDefault(n(38)),T=a.__importDefault(n(39)),B=1,I=function(){function e(e,t){this.pluginsFunctionList={},this.beforeDestroyHooks=[],this.id="wangEditor-"+B++,this.toolbarSelector=e,this.textSelector=t,v.selectorValidator(this),this.config=u.deepClone(s["default"]),this.$toolbarElem=l["default"]("<div></div>"),this.$textContainerElem=l["default"]("<div></div>"),this.$textElem=l["default"]("<div></div>"),this.toolbarElemId="",this.textElemId="",this.isFocus=!1,this.isComposing=!1,this.isCompatibleMode=!1,this.selection=new c["default"](this),this.cmd=new f["default"](this),this.txt=new d["default"](this),this.menus=new p["default"](this),this.zIndex=new w["default"],this.change=new x["default"](this),this.history=new _["default"](this),this.onSelectionChange=new b["default"](this);var n=E["default"](this),o=n.disable,i=n.enable;this.disable=o,this.enable=i,this.isEnable=!0}return e.prototype.initSelection=function(e){h["default"](this,e)},e.prototype.create=function(){this.zIndex.init(this),this.isCompatibleMode=this.config.compatibleMode(),this.isCompatibleMode||(this.config.onchangeTimeout=30),m["default"](this),v["default"](this),this.txt.init(),this.menus.init(),g["default"](this),this.initSelection(!0),A["default"](this),this.change.observe(),this.history.observe(),C["default"](this)},e.prototype.beforeDestroy=function(e){return this.beforeDestroyHooks.push(e),this},e.prototype.destroy=function(){var e,t=this;(0,r["default"])(e=this.beforeDestroyHooks).call(e,(function(e){return e.call(t)})),this.$toolbarElem.remove(),this.$textContainerElem.remove()},e.prototype.fullScreen=function(){g.setFullScreen(this)},e.prototype.unFullScreen=function(){g.setUnFullScreen(this)},e.prototype.scrollToHead=function(e){y["default"](this,e)},e.registerMenu=function(t,n){n&&"function"===typeof n&&(e.globalCustomMenuConstructorList[t]=n)},e.prototype.registerPlugin=function(e,t){C.registerPlugin(e,t,this.pluginsFunctionList)},e.registerPlugin=function(t,n){C.registerPlugin(t,n,e.globalPluginsFunctionList)},e.$=l["default"],e.BtnMenu=S["default"],e.DropList=M["default"],e.DropListMenu=k["default"],e.Panel=D["default"],e.PanelMenu=N["default"],e.Tooltip=T["default"],e.globalCustomMenuConstructorList={},e.globalPluginsFunctionList={},e}();t["default"]=I},function(e,t,n){var o=n(13),i=n(55),r=n(10),a=r("species");e.exports=function(e,t){var n;return i(e)&&(n=e.constructor,"function"!=typeof n||n!==Array&&!i(n.prototype)?o(n)&&(n=n[a],null===n&&(n=void 0)):n=void 0),new(void 0===n?Array:n)(0===t?0:t)}},function(e,t,n){e.exports=n(185)},function(e,t,n){var o=n(49),i=n(68),r="["+i+"]",a=RegExp("^"+r+r+"*"),l=RegExp(r+r+"*$"),u=function(e){return function(t){var n=String(o(t));return 1&e&&(n=n.replace(a,"")),2&e&&(n=n.replace(l,"")),n}};e.exports={start:u(1),end:u(2),trim:u(3)}},function(e,t,n){e.exports=n(205)},function(e,t,n){var o=n(227),i=n(230);function r(t){return e.exports=r="function"===typeof i&&"symbol"===typeof o?function(e){return typeof e}:function(e){return e&&"function"===typeof i&&e.constructor===i&&e!==i.prototype?"symbol":typeof e},r(t)}e.exports=r},function(e,t,n){var o=n(10);t.f=o},function(e,t,n){e.exports=n(306)},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(33)),u=function(){function e(e,t){var n=this;this.$elem=e,this.editor=t,this._active=!1,e.on("click",(function(e){var o;l["default"].hideCurAllPanels(),(0,r["default"])(o=t.txt.eventHooks.menuClickEvents).call(o,(function(e){return e()})),e.stopPropagation(),null!=t.selection.getRange()&&n.clickHandler(e)}))}return e.prototype.clickHandler=function(e){},e.prototype.active=function(){this._active=!0,this.$elem.addClass("w-e-active")},e.prototype.unActive=function(){this._active=!1,this.$elem.removeClass("w-e-active")},(0,i["default"])(e.prototype,"isActive",{get:function(){return this._active},enumerable:!1,configurable:!0}),e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(28));function a(e){var n=e.elems[0];while(n&&(0,r["default"])(o=t.EXTRA_TAG).call(o,n.nodeName)){var o;if(n=n.parentElement,"A"===n.nodeName)return n}}function l(e){var t,n=e.selection.getSelectionContainerElem();if(!(null===(t=null===n||void 0===n?void 0:n.elems)||void 0===t?void 0:t.length))return!1;if("A"===n.getNodeName())return!0;var o=a(n);return!(!o||"A"!==o.nodeName)}(0,i["default"])(t,"__esModule",{value:!0}),t.getParentNodeA=t.EXTRA_TAG=void 0,t.EXTRA_TAG=["B","FONT","I","STRIKE"],t.getParentNodeA=a,t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(57)),a=o(n(4)),l=o(n(27));(0,i["default"])(t,"__esModule",{value:!0});var u=n(2),s=n(6),c=u.__importDefault(n(135)),f=u.__importDefault(n(136)),d=function(){function e(e){this.editor=e}return e.prototype.insertImg=function(e,t,n){var o=this.editor,i=o.config,r="validate.",a=function(e,t){return void 0===t&&(t=r),o.i18next.t(t+e)},l=e.replace(/</g,"&lt;").replace(/>/g,"&gt;");l=l.replace("'",'"');var u="";n&&(u=n.replace("'",'"'),u="data-href='"+encodeURIComponent(u)+"' ");var s="";t&&(s=t.replace(/</g,"&lt;").replace(/>/g,"&gt;"),s=s.replace("'",'"'),s="alt='"+s+"' "),o.cmd["do"]("insertHTML","<img src='"+l+"' "+s+u+'style="max-width:100%;" contenteditable="false"/>'),i.linkImgCallback(e,t,n);var c=document.createElement("img");c.onload=function(){c=null},c.onerror=function(){i.customAlert(a("插入图片错误"),"error","wangEditor: "+a("插入图片错误")+"，"+a("图片链接")+' "'+e+'"，'+a("下载链接失败")),c=null},c.onabort=function(){return c=null},c.src=e},e.prototype.uploadImg=function(e){var t=this;if(e.length){var n=this.editor,o=n.config,i="validate.",u=function(e){return n.i18next.t(i+e)},d=o.uploadImgServer,p=o.uploadImgShowBase64,v=o.uploadImgMaxSize,h=v/1024/1024,A=o.uploadImgMaxLength,m=o.uploadFileName,g=o.uploadImgParams,y=o.uploadImgParamsWithUrl,w=o.uploadImgHeaders,x=o.uploadImgHooks,_=o.uploadImgTimeout,E=o.withCredentials,b=o.customUploadImg;if(b||d||p){var C=[],S=[];if(s.arrForEach(e,(function(e){if(e){var t=e.name||e.type.replace("/","."),o=e.size;if(t&&o){var i=n.config.uploadImgAccept.join("|"),r=".("+i+")$",a=new RegExp(r,"i");!1!==a.test(t)?v<o?S.push("【"+t+"】"+u("大于")+" "+h+"M"):C.push(e):S.push("【"+t+"】"+u("不是图片"))}}})),S.length)o.customAlert(u("图片验证未通过")+": \n"+S.join("\n"),"warning");else if(0!==C.length)if(C.length>A)o.customAlert(u("一次最多上传")+A+u("张图片"),"warning");else if(b&&"function"===typeof b){var M;b(C,(0,r["default"])(M=this.insertImg).call(M,this))}else{var k=new FormData;if((0,a["default"])(C).call(C,(function(e,t){var n=m||e.name;C.length>1&&(n+=t+1),k.append(n,e)})),d){var D=d.split("#");d=D[0];var N=D[1]||"";(0,a["default"])(s).call(s,g,(function(e,t){y&&((0,l["default"])(d).call(d,"?")>0?d+="&":d+="?",d=d+e+"="+t),k.append(e,t)})),N&&(d+="#"+N);var T=c["default"](d,{timeout:_,formData:k,headers:w,withCredentials:!!E,beforeSend:function(e){if(x.before)return x.before(e,n,C)},onTimeout:function(e){o.customAlert(u("上传图片超时"),"error"),x.timeout&&x.timeout(e,n)},onProgress:function(e,t){var o=new f["default"](n);t.lengthComputable&&(e=t.loaded/t.total,o.show(e))},onError:function(e){o.customAlert(u("上传图片错误"),"error",u("上传图片错误")+"，"+u("服务器返回状态")+": "+e.status),x.error&&x.error(e,n)},onFail:function(e,t){o.customAlert(u("上传图片失败"),"error",u("上传图片返回结果错误")+"，"+u("返回结果")+": "+t),x.fail&&x.fail(e,n,t)},onSuccess:function(e,i){if(x.customInsert){var l;x.customInsert((0,r["default"])(l=t.insertImg).call(l,t),i,n)}else{if("0"!=i.errno)return o.customAlert(u("上传图片失败"),"error",u("上传图片返回结果错误")+"，"+u("返回结果")+" errno="+i.errno),void(x.fail&&x.fail(e,n,i));var s=i.data;(0,a["default"])(s).call(s,(function(e){"string"===typeof e?t.insertImg(e):t.insertImg(e.url,e.alt,e.href)})),x.success&&x.success(e,n,i)}}});"string"===typeof T&&o.customAlert(T,"error")}else p&&s.arrForEach(e,(function(e){var n=t,o=new FileReader;o.readAsDataURL(e),o.onload=function(){if(this.result){var e=this.result.toString();n.insertImg(e,e)}}}))}else o.customAlert(u("传入的文件不合法"),"warning")}}},e}();t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(410)),a=o(n(4)),l=o(n(45));function u(e){return!!e.length&&"w-e-todo"===e.attr("class")}function s(e){var t=e.selection.getSelectionRangeTopNodes();if(0!==t.length)return(0,r["default"])(t).call(t,(function(e){return u(e)}))}function c(e,t,n){var o;if(e.hasChildNodes()){var i=e.cloneNode(),r=!1;""===t.nodeValue&&(r=!0);var l=[];return(0,a["default"])(o=e.childNodes).call(o,(function(e){if(!f(e,t)&&r&&(i.appendChild(e.cloneNode(!0)),"BR"!==e.nodeName&&l.push(e)),f(e,t)){if(1===e.nodeType){var o=c(e,t,n);o&&""!==o.textContent&&(null===i||void 0===i||i.appendChild(o))}if(3===e.nodeType&&t.isEqualNode(e)){var a=d(e,n);i.textContent=a}r=!0}})),(0,a["default"])(l).call(l,(function(e){var t=e;t.remove()})),i}}function f(e,t){return 3===e.nodeType?e.nodeValue===t.nodeValue:e.contains(t)}function d(e,t,n){void 0===n&&(n=!0);var o=e.nodeValue,i=null===o||void 0===o?void 0:(0,l["default"])(o).call(o,0,t);if(o=null===o||void 0===o?void 0:(0,l["default"])(o).call(o,t),!n){var r=o;o=i,i=r}return e.nodeValue=i,o}(0,i["default"])(t,"__esModule",{value:!0}),t.dealTextNode=t.isAllTodo=t.isTodo=t.getCursorNextNode=void 0,t.isTodo=u,t.isAllTodo=s,t.getCursorNextNode=c,t.dealTextNode=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(430),a=function(){function e(e){this.maxSize=e,this.isRe=!1,this.data=new r.CeilStack(e),this.revokeData=new r.CeilStack(e)}return(0,i["default"])(e.prototype,"size",{get:function(){return[this.data.size,this.revokeData.size]},enumerable:!1,configurable:!0}),e.prototype.resetMaxSize=function(e){this.data.resetMax(e),this.revokeData.resetMax(e)},e.prototype.save=function(e){return this.isRe&&(this.revokeData.clear(),this.isRe=!1),this.data.instack(e),this},e.prototype.revoke=function(e){!this.isRe&&(this.isRe=!0);var t=this.data.outstack();return!!t&&(this.revokeData.instack(t),e(t),!0)},e.prototype.restore=function(e){!this.isRe&&(this.isRe=!0);var t=this.revokeData.outstack();return!!t&&(this.data.instack(t),e(t),!0)},e}();t["default"]=a},function(e,t,n){var o=n(14),i=n(11),r=n(73);e.exports=!o&&!i((function(){return 7!=Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},function(e,t,n){var o=n(11),i=/#|\.prototype\./,r=function(e,t){var n=l[a(e)];return n==s||n!=u&&("function"==typeof t?o(t):!!t)},a=r.normalize=function(e){return String(e).replace(i,".").toLowerCase()},l=r.data={},u=r.NATIVE="N",s=r.POLYFILL="P";e.exports=r},function(e,t,n){var o=n(103),i=Function.toString;"function"!=typeof o.inspectSource&&(o.inspectSource=function(e){return i.call(e)}),e.exports=o.inspectSource},function(e,t,n){var o=n(8),i=n(166),r="__core-js_shared__",a=o[r]||i(r,{});e.exports=a},function(e,t,n){"use strict";var o,i,r,a=n(105),l=n(19),u=n(16),s=n(10),c=n(43),f=s("iterator"),d=!1,p=function(){return this};[].keys&&(r=[].keys(),"next"in r?(i=a(a(r)),i!==Object.prototype&&(o=i)):d=!0),void 0==o&&(o={}),c||u(o,f)||l(o,f,p),e.exports={IteratorPrototype:o,BUGGY_SAFARI_ITERATORS:d}},function(e,t,n){var o=n(16),i=n(31),r=n(63),a=n(168),l=r("IE_PROTO"),u=Object.prototype;e.exports=a?Object.getPrototypeOf:function(e){return e=i(e),o(e,l)?e[l]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?u:null}},function(e,t,n){var o=n(76);e.exports=o&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(e,t,n){var o=n(16),i=n(30),r=n(78).indexOf,a=n(51);e.exports=function(e,t){var n,l=i(e),u=0,s=[];for(n in l)!o(a,n)&&o(l,n)&&s.push(n);while(t.length>u)o(l,n=t[u++])&&(~r(s,n)||s.push(n));return s}},function(e,t,n){var o=n(36);e.exports=o("document","documentElement")},function(e,t,n){var o=n(8);e.exports=o.Promise},function(e,t,n){var o=n(53);e.exports=function(e,t,n){for(var i in t)n&&n.unsafe&&e[i]?e[i]=t[i]:o(e,i,t[i],n);return e}},function(e,t,n){"use strict";var o=n(36),i=n(18),r=n(10),a=n(14),l=r("species");e.exports=function(e){var t=o(e),n=i.f;a&&t&&!t[l]&&n(t,l,{configurable:!0,get:function(){return this}})}},function(e,t,n){var o=n(10),i=n(44),r=o("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||a[r]===e)}},function(e,t,n){var o=n(65),i=n(44),r=n(10),a=r("iterator");e.exports=function(e){if(void 0!=e)return e[a]||e["@@iterator"]||i[o(e)]}},function(e,t,n){var o=n(25);e.exports=function(e,t,n,i){try{return i?t(o(n)[0],n[1]):t(n)}catch(a){var r=e["return"];throw void 0!==r&&o(r.call(e)),a}}},function(e,t,n){var o=n(10),i=o("iterator"),r=!1;try{var a=0,l={next:function(){return{done:!!a++}},return:function(){r=!0}};l[i]=function(){return this},Array.from(l,(function(){throw 2}))}catch(u){}e.exports=function(e,t){if(!t&&!r)return!1;var n=!1;try{var o={};o[i]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(u){}return n}},function(e,t,n){var o=n(25),i=n(41),r=n(10),a=r("species");e.exports=function(e,t){var n,r=o(e).constructor;return void 0===r||void 0==(n=o(r)[a])?t:i(n)}},function(e,t,n){var o,i,r,a=n(8),l=n(11),u=n(34),s=n(40),c=n(108),f=n(73),d=n(118),p=a.location,v=a.setImmediate,h=a.clearImmediate,A=a.process,m=a.MessageChannel,g=a.Dispatch,y=0,w={},x="onreadystatechange",_=function(e){if(w.hasOwnProperty(e)){var t=w[e];delete w[e],t()}},E=function(e){return function(){_(e)}},b=function(e){_(e.data)},C=function(e){a.postMessage(e+"",p.protocol+"//"+p.host)};v&&h||(v=function(e){var t=[],n=1;while(arguments.length>n)t.push(arguments[n++]);return w[++y]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},o(y),y},h=function(e){delete w[e]},"process"==u(A)?o=function(e){A.nextTick(E(e))}:g&&g.now?o=function(e){g.now(E(e))}:m&&!d?(i=new m,r=i.port2,i.port1.onmessage=b,o=s(r.postMessage,r,1)):!a.addEventListener||"function"!=typeof postMessage||a.importScripts||l(C)||"file:"===p.protocol?o=x in f("script")?function(e){c.appendChild(f("script"))[x]=function(){c.removeChild(this),_(e)}}:function(e){setTimeout(E(e),0)}:(o=C,a.addEventListener("message",b,!1))),e.exports={set:v,clear:h}},function(e,t,n){var o=n(84);e.exports=/(iphone|ipod|ipad).*applewebkit/i.test(o)},function(e,t,n){var o=n(25),i=n(13),r=n(85);e.exports=function(e,t){if(o(e),i(t)&&t.constructor===e)return t;var n=r.f(e),a=n.resolve;return a(t),n.promise}},function(e,t){e.exports=function(e){try{return{error:!1,value:e()}}catch(t){return{error:!0,value:t}}}},function(e,t,n){e.exports=n(197)},function(e,t,n){"use strict";var o=n(5),i=n(8),r=n(123),a=n(11),l=n(19),u=n(66),s=n(83),c=n(13),f=n(37),d=n(18).f,p=n(32).forEach,v=n(14),h=n(42),A=h.set,m=h.getterFor;e.exports=function(e,t,n){var h,g=-1!==e.indexOf("Map"),y=-1!==e.indexOf("Weak"),w=g?"set":"add",x=i[e],_=x&&x.prototype,E={};if(v&&"function"==typeof x&&(y||_.forEach&&!a((function(){(new x).entries().next()})))){h=t((function(t,n){A(s(t,h,e),{type:e,collection:new x}),void 0!=n&&u(n,t[w],t,g)}));var b=m(e);p(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in _)||y&&"clear"==e||l(h.prototype,e,(function(n,o){var i=b(this).collection;if(!t&&y&&!c(n))return"get"==e&&void 0;var r=i[e](0===n?0:n,o);return t?this:r}))})),y||d(h.prototype,"size",{configurable:!0,get:function(){return b(this).collection.size}})}else h=n.getConstructor(t,e,g,w),r.REQUIRED=!0;return f(h,e,!1,!0),E[e]=h,o({global:!0,forced:!0},E),y||n.setStrong(h,e,g),h}},function(e,t,n){var o=n(51),i=n(13),r=n(16),a=n(18).f,l=n(64),u=n(200),s=l("meta"),c=0,f=Object.isExtensible||function(){return!0},d=function(e){a(e,s,{value:{objectID:"O"+ ++c,weakData:{}}})},p=function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!r(e,s)){if(!f(e))return"F";if(!t)return"E";d(e)}return e[s].objectID},v=function(e,t){if(!r(e,s)){if(!f(e))return!0;if(!t)return!1;d(e)}return e[s].weakData},h=function(e){return u&&A.REQUIRED&&f(e)&&!r(e,s)&&d(e),e},A=e.exports={REQUIRED:!1,fastKey:p,getWeakData:v,onFreeze:h};o[s]=!0},function(e,t,n){"use strict";var o=n(18).f,i=n(77),r=n(110),a=n(40),l=n(83),u=n(66),s=n(75),c=n(111),f=n(14),d=n(123).fastKey,p=n(42),v=p.set,h=p.getterFor;e.exports={getConstructor:function(e,t,n,s){var c=e((function(e,o){l(e,c,t),v(e,{type:t,index:i(null),first:void 0,last:void 0,size:0}),f||(e.size=0),void 0!=o&&u(o,e[s],e,n)})),p=h(t),A=function(e,t,n){var o,i,r=p(e),a=m(e,t);return a?a.value=n:(r.last=a={index:i=d(t,!0),key:t,value:n,previous:o=r.last,next:void 0,removed:!1},r.first||(r.first=a),o&&(o.next=a),f?r.size++:e.size++,"F"!==i&&(r.index[i]=a)),e},m=function(e,t){var n,o=p(e),i=d(t);if("F"!==i)return o.index[i];for(n=o.first;n;n=n.next)if(n.key==t)return n};return r(c.prototype,{clear:function(){var e=this,t=p(e),n=t.index,o=t.first;while(o)o.removed=!0,o.previous&&(o.previous=o.previous.next=void 0),delete n[o.index],o=o.next;t.first=t.last=void 0,f?t.size=0:e.size=0},delete:function(e){var t=this,n=p(t),o=m(t,e);if(o){var i=o.next,r=o.previous;delete n.index[o.index],o.removed=!0,r&&(r.next=i),i&&(i.previous=r),n.first==o&&(n.first=i),n.last==o&&(n.last=r),f?n.size--:t.size--}return!!o},forEach:function(e){var t,n=p(this),o=a(e,arguments.length>1?arguments[1]:void 0,3);while(t=t?t.next:n.first){o(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!m(this,e)}}),r(c.prototype,n?{get:function(e){var t=m(this,e);return t&&t.value},set:function(e,t){return A(this,0===e?0:e,t)}}:{add:function(e){return A(this,e=0===e?0:e,e)}}),f&&o(c.prototype,"size",{get:function(){return p(this).size}}),c},setStrong:function(e,t,n){var o=t+" Iterator",i=h(t),r=h(o);s(e,t,(function(e,t){v(this,{type:o,target:e,state:i(e),kind:t,last:void 0})}),(function(){var e=r(this),t=e.kind,n=e.last;while(n&&n.removed)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),c(t)}}},function(e,t,n){var o=n(12);o("iterator")},function(e,t,n){var o=n(107),i=n(80),r=i.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return o(e,r)}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){e.exports=n(268)},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={zIndex:1e4}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={focus:!0,height:300,placeholder:"请输入正文",zIndexFullScreen:10002,showFullScreen:!0}},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0}),t.getPasteImgs=t.getPasteHtml=t.getPasteText=void 0;var a=n(2),l=n(6),u=a.__importDefault(n(292));function s(e){var t=e.clipboardData,n="";return n=null==t?window.clipboardData&&window.clipboardData.getData("text"):t.getData("text/plain"),l.replaceHtmlSymbol(n)}function c(e,t,n){void 0===t&&(t=!0),void 0===n&&(n=!1);var o=e.clipboardData,i="";if(o&&(i=o.getData("text/html")),!i){var r=s(e);if(!r)return"";i="<p>"+r+"</p>"}return i=i.replace(/<(\d)/gm,(function(e,t){return"&lt;"+t})),i=i.replace(/<(\/?meta.*?)>/gim,""),i=u["default"](i,t,n),i}function f(e){var t,n=[],o=s(e);if(o)return n;var i=null===(t=e.clipboardData)||void 0===t?void 0:t.items;return i?((0,r["default"])(l).call(l,i,(function(e,t){var o=t.type;/image/i.test(o)&&n.push(t.getAsFile())})),n):n}t.getPasteText=s,t.getPasteHtml=c,t.getPasteImgs=f},function(e,t,n){e.exports=n(294)},function(e,t,n){e.exports=n(310)},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(46));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=n(7),c=function(){function e(e,t){var n=this;this.hideTimeoutId=0,this.menu=e,this.conf=t;var o=u["default"]('<div class="w-e-droplist"></div>'),i=u["default"]("<p>"+t.title+"</p>");i.addClass("w-e-dp-title"),o.append(i);var l=t.list||[],c=t.type||"list",f=t.clickHandler||s.EMPTY_FN,d=u["default"]('<ul class="'+("list"===c?"w-e-list":"w-e-block")+'"></ul>');(0,r["default"])(l).call(l,(function(e){var t=e.$elem,o=e.value,i=u["default"]('<li class="w-e-item"></li>');t&&(i.append(t),d.append(i),i.on("click",(function(e){f(o),e.stopPropagation(),n.hideTimeoutId=(0,a["default"])((function(){n.hide()}))})))})),o.append(d),o.on("mouseleave",(function(){n.hideTimeoutId=(0,a["default"])((function(){n.hide()}))})),this.$container=o,this.rendered=!1,this._show=!1}return e.prototype.show=function(){this.hideTimeoutId&&clearTimeout(this.hideTimeoutId);var e=this.menu,t=e.$elem,n=this.$container;if(!this._show){if(this.rendered)n.show();else{var o=t.getBoundingClientRect().height||0,i=this.conf.width||100;n.css("margin-top",o+"px").css("width",i+"px"),t.append(n),this.rendered=!0}this._show=!0}},e.prototype.hide=function(){var e=this.$container;this._show&&(e.hide(),this._show=!1)},(0,i["default"])(e.prototype,"isShow",{get:function(){return this._show},enumerable:!1,configurable:!0}),e}();t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(92)),r=o(n(1)),a=o(n(4));(0,r["default"])(t,"__esModule",{value:!0});var l=n(6);function u(e,t){var n=new XMLHttpRequest;if(n.open("POST",e),n.timeout=t.timeout||1e4,n.ontimeout=function(){console.error("wangEditor - 请求超时"),t.onTimeout&&t.onTimeout(n)},n.upload&&(n.upload.onprogress=function(e){var n=e.loaded/e.total;t.onProgress&&t.onProgress(n,e)}),t.headers&&(0,a["default"])(l).call(l,t.headers,(function(e,t){n.setRequestHeader(e,t)})),n.withCredentials=!!t.withCredentials,t.beforeSend){var o=t.beforeSend(n);if(o&&"object"===(0,i["default"])(o)&&o.prevent)return o.msg}return n.onreadystatechange=function(){if(4===n.readyState){var e=n.status;if(!(e<200)&&!(e>=300&&e<400)){if(e>=400)return console.error("wangEditor - XHR 报错，状态码 "+e),void(t.onError&&t.onError(n));var o,r=n.responseText;if("object"!==(0,i["default"])(r))try{o=JSON.parse(r)}catch(a){return console.error("wangEditor - 返回结果不是 JSON 格式",r),void(t.onFail&&t.onFail(n,r))}else o=r;t.onSuccess(n,o)}}},n.send(t.formData||null),n}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(342)),a=o(n(46));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=function(){function e(e){this.editor=e,this.$textContainer=e.$textContainerElem,this.$bar=u["default"]('<div class="w-e-progress"></div>'),this.isShow=!1,this.time=0,this.timeoutId=0}return e.prototype.show=function(e){var t=this;if(!this.isShow){this.isShow=!0;var n=this.$bar,o=this.$textContainer;o.append(n),(0,r["default"])()-this.time>100&&e<=1&&(n.css("width",100*e+"%"),this.time=(0,r["default"])());var i=this.timeoutId;i&&clearTimeout(i),this.timeoutId=(0,a["default"])((function(){t.hide()}),500)}},e.prototype.hide=function(){var e=this.$bar;e.remove(),this.isShow=!1,this.time=0,this.timeoutId=0},e}();t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.ListType=void 0;var r,a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(24)),s=n(47),c=a.__importStar(n(371));(function(e){e["OrderedList"]="OL",e["UnorderedList"]="UL"})(r=t.ListType||(t.ListType={}));var f=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="序列">\n                <i class="w-e-icon-list2"></i>\n            </div>'),i={width:130,title:"序列",type:"list",list:[{$elem:l["default"]('\n                        <p>\n                            <i class="w-e-icon-list2 w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.list.无序列表")+"\n                        <p>"),value:r.UnorderedList},{$elem:l["default"]('<p>\n                            <i class="w-e-icon-list-numbered w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.list.有序列表")+"\n                        <p>"),value:r.OrderedList}],clickHandler:function(e){n.command(e)}};return n=e.call(this,o,t,i)||this,n}return a.__extends(t,e),t.prototype.command=function(e){var t=this.editor,n=t.selection.getSelectionContainerElem();void 0!==n&&(this.handleSelectionRangeNodes(e),this.tryChangeActive())},t.prototype.validator=function(e,t,n){return!(!e.length||!t.length||n.equal(e)||n.equal(t))},t.prototype.handleSelectionRangeNodes=function(e){var t=this.editor,n=t.selection,o=e.toLowerCase(),i=n.getSelectionContainerElem(),r=n.getSelectionStartElem().getNodeTop(t),a=n.getSelectionEndElem().getNodeTop(t);if(this.validator(r,a,t.$textElem)){var l=n.getRange(),u=null===l||void 0===l?void 0:l.collapsed;t.$textElem.equal(i)||(i=i.getNodeTop(t));var f,d={editor:t,listType:e,listTarget:o,$selectionElem:i,$startElem:r,$endElem:a};f=this.isOrderElem(i)?c.ClassType.Wrap:this.isOrderElem(r)&&this.isOrderElem(a)?c.ClassType.Join:this.isOrderElem(r)?c.ClassType.StartJoin:this.isOrderElem(a)?c.ClassType.EndJoin:c.ClassType.Other;var p=new c["default"](c.createListHandle(f,d,l));s.updateRange(t,p.getSelectionRangeElem(),!!u)}},t.prototype.isOrderElem=function(e){var t=e.getNodeName();return t===r.OrderedList||t===r.UnorderedList},t.prototype.tryChangeActive=function(){},t}(u["default"]);t["default"]=f},function(e,t,n){e.exports=n(395)},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(e){var t=e.selection.getSelectionContainerElem();return!!(null===t||void 0===t?void 0:t.length)&&!("CODE"!=t.getNodeName()&&"PRE"!=t.getNodeName()&&"CODE"!=t.parent().getNodeName()&&"PRE"!=t.parent().getNodeName()&&!/hljs/.test(t.parent().attr("class")))}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29));(0,i["default"])(t,"__esModule",{value:!0}),t.todo=void 0;var a=n(2),l=a.__importDefault(n(3)),u=function(){function e(e){var t;this.template='<ul class="w-e-todo"><li><span contenteditable="false"><input type="checkbox"></span></li></ul>',this.checked=!1,this.$todo=l["default"](this.template),this.$child=null===(t=null===e||void 0===e?void 0:e.childNodes())||void 0===t?void 0:t.clone(!0)}return e.prototype.init=function(){var e=this.$child,t=this.getInputContainer();e&&e.insertAfter(t)},e.prototype.getInput=function(){var e=this.$todo,t=(0,r["default"])(e).call(e,"input");return t},e.prototype.getInputContainer=function(){var e=this.getInput().parent();return e},e.prototype.getTodo=function(){return this.$todo},e}();function s(e){var t=new u(e);return t.init(),t}t.todo=u,t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2);n(146),n(148),n(152),n(154),n(156),n(158),n(160);var a=r.__importDefault(n(87));r.__exportStar(n(442),t);try{document}catch(l){throw new Error("请在浏览器环境下运行")}t["default"]=a["default"]},function(e,t,n){var o=n(143);e.exports=o},function(e,t,n){n(144);var o=n(9),i=o.Object,r=e.exports=function(e,t,n){return i.defineProperty(e,t,n)};i.defineProperty.sham&&(r.sham=!0)},function(e,t,n){var o=n(5),i=n(14),r=n(18);o({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:r.f})},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(o){"object"===typeof window&&(n=window)}e.exports=n},function(e,t,n){var o=n(20),i=n(147);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,'.w-e-toolbar,\n.w-e-text-container,\n.w-e-menu-panel {\n  padding: 0;\n  margin: 0;\n  box-sizing: border-box;\n  background-color: #fff;\n  /*表情菜单样式*/\n  /*分割线样式*/\n}\n.w-e-toolbar h1,\n.w-e-text-container h1,\n.w-e-menu-panel h1 {\n  font-size: 32px !important;\n}\n.w-e-toolbar h2,\n.w-e-text-container h2,\n.w-e-menu-panel h2 {\n  font-size: 24px !important;\n}\n.w-e-toolbar h3,\n.w-e-text-container h3,\n.w-e-menu-panel h3 {\n  font-size: 18.72px !important;\n}\n.w-e-toolbar h4,\n.w-e-text-container h4,\n.w-e-menu-panel h4 {\n  font-size: 16px !important;\n}\n.w-e-toolbar h5,\n.w-e-text-container h5,\n.w-e-menu-panel h5 {\n  font-size: 13.28px !important;\n}\n.w-e-toolbar p,\n.w-e-text-container p,\n.w-e-menu-panel p {\n  font-size: 16px !important;\n}\n.w-e-toolbar .eleImg,\n.w-e-text-container .eleImg,\n.w-e-menu-panel .eleImg {\n  cursor: pointer;\n  display: inline-block;\n  font-size: 18px;\n  padding: 0 3px;\n}\n.w-e-toolbar *,\n.w-e-text-container *,\n.w-e-menu-panel * {\n  padding: 0;\n  margin: 0;\n  box-sizing: border-box;\n}\n.w-e-toolbar hr,\n.w-e-text-container hr,\n.w-e-menu-panel hr {\n  cursor: pointer;\n  display: block;\n  height: 0px;\n  border: 0;\n  border-top: 3px solid #ccc;\n  margin: 20px 0;\n}\n.w-e-clear-fix:after {\n  content: "";\n  display: table;\n  clear: both;\n}\n.w-e-drop-list-item {\n  position: relative;\n  top: 1px;\n  padding-right: 7px;\n  color: #333 !important;\n}\n.w-e-drop-list-tl {\n  padding-left: 10px;\n  text-align: left;\n}\n',""]),e.exports=t},function(e,t,n){var o=n(20),i=n(149);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21),i=n(150),r=n(151);t=o(!1);var a=i(r);t.push([e.i,"@font-face {\n  font-family: 'w-e-icon';\n  src: url("+a+') format(\'truetype\');\n  font-weight: normal;\n  font-style: normal;\n}\n[class^="w-e-icon-"],\n[class*=" w-e-icon-"] {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: \'w-e-icon\' !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.w-e-icon-close:before {\n  content: "\\f00d";\n}\n.w-e-icon-upload2:before {\n  content: "\\e9c6";\n}\n.w-e-icon-trash-o:before {\n  content: "\\f014";\n}\n.w-e-icon-header:before {\n  content: "\\f1dc";\n}\n.w-e-icon-pencil2:before {\n  content: "\\e906";\n}\n.w-e-icon-paint-brush:before {\n  content: "\\f1fc";\n}\n.w-e-icon-image:before {\n  content: "\\e90d";\n}\n.w-e-icon-play:before {\n  content: "\\e912";\n}\n.w-e-icon-location:before {\n  content: "\\e947";\n}\n.w-e-icon-undo:before {\n  content: "\\e965";\n}\n.w-e-icon-redo:before {\n  content: "\\e966";\n}\n.w-e-icon-quotes-left:before {\n  content: "\\e977";\n}\n.w-e-icon-list-numbered:before {\n  content: "\\e9b9";\n}\n.w-e-icon-list2:before {\n  content: "\\e9bb";\n}\n.w-e-icon-link:before {\n  content: "\\e9cb";\n}\n.w-e-icon-happy:before {\n  content: "\\e9df";\n}\n.w-e-icon-bold:before {\n  content: "\\ea62";\n}\n.w-e-icon-underline:before {\n  content: "\\ea63";\n}\n.w-e-icon-italic:before {\n  content: "\\ea64";\n}\n.w-e-icon-strikethrough:before {\n  content: "\\ea65";\n}\n.w-e-icon-table2:before {\n  content: "\\ea71";\n}\n.w-e-icon-paragraph-left:before {\n  content: "\\ea77";\n}\n.w-e-icon-paragraph-center:before {\n  content: "\\ea78";\n}\n.w-e-icon-paragraph-right:before {\n  content: "\\ea79";\n}\n.w-e-icon-paragraph-justify:before {\n  content: "\\ea7a";\n}\n.w-e-icon-terminal:before {\n  content: "\\f120";\n}\n.w-e-icon-page-break:before {\n  content: "\\ea68";\n}\n.w-e-icon-cancel-circle:before {\n  content: "\\ea0d";\n}\n.w-e-icon-font:before {\n  content: "\\ea5c";\n}\n.w-e-icon-text-heigh:before {\n  content: "\\ea5f";\n}\n.w-e-icon-paint-format:before {\n  content: "\\e90c";\n}\n.w-e-icon-indent-increase:before {\n  content: "\\ea7b";\n}\n.w-e-icon-indent-decrease:before {\n  content: "\\ea7c";\n}\n.w-e-icon-row-height:before {\n  content: "\\e9be";\n}\n.w-e-icon-fullscreen_exit:before {\n  content: "\\e900";\n}\n.w-e-icon-fullscreen:before {\n  content: "\\e901";\n}\n.w-e-icon-split-line:before {\n  content: "\\ea0b";\n}\n.w-e-icon-checkbox-checked:before {\n  content: "\\ea52";\n}\n',""]),e.exports=t},function(e,t,n){"use strict";e.exports=function(e,t){return t||(t={}),e=e&&e.__esModule?e.default:e,"string"!==typeof e?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},function(e,t,n){"use strict";n.r(t),t["default"]="data:font/woff;base64,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"},function(e,t,n){var o=n(20),i=n(153);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,'.w-e-toolbar {\n  display: flex;\n  padding: 0 6px;\n  flex-wrap: wrap;\n  position: relative;\n  /* 单个菜单 */\n}\n.w-e-toolbar .w-e-menu {\n  position: relative;\n  display: flex;\n  width: 40px;\n  height: 40px;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  cursor: pointer;\n}\n.w-e-toolbar .w-e-menu i {\n  color: #999;\n}\n.w-e-toolbar .w-e-menu:hover {\n  background-color: #F6F6F6;\n}\n.w-e-toolbar .w-e-menu:hover i {\n  color: #333;\n}\n.w-e-toolbar .w-e-active i {\n  color: #1e88e5;\n}\n.w-e-toolbar .w-e-active:hover i {\n  color: #1e88e5;\n}\n.w-e-menu-tooltip {\n  position: absolute;\n  display: flex;\n  color: #f1f1f1;\n  background-color: rgba(0, 0, 0, 0.75);\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);\n  border-radius: 4px;\n  padding: 4px 5px 6px;\n  justify-content: center;\n  align-items: center;\n}\n.w-e-menu-tooltip-up::after {\n  content: "";\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border: 5px solid rgba(0, 0, 0, 0);\n  border-top-color: rgba(0, 0, 0, 0.73);\n}\n.w-e-menu-tooltip-down::after {\n  content: "";\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border: 5px solid rgba(0, 0, 0, 0);\n  border-bottom-color: rgba(0, 0, 0, 0.73);\n}\n.w-e-menu-tooltip-item-wrapper {\n  font-size: 14px;\n  margin: 0 5px;\n}\n',""]),e.exports=t},function(e,t,n){var o=n(20),i=n(155);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,'.w-e-text-container {\n  position: relative;\n  height: 100%;\n}\n.w-e-text-container .w-e-progress {\n  position: absolute;\n  background-color: #1e88e5;\n  top: 0;\n  left: 0;\n  height: 1px;\n}\n.w-e-text-container .placeholder {\n  color: #D4D4D4;\n  position: absolute;\n  font-size: 11pt;\n  line-height: 22px;\n  left: 10px;\n  top: 10px;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n  z-index: -1;\n}\n.w-e-text {\n  padding: 0 10px;\n  overflow-y: auto;\n}\n.w-e-text p,\n.w-e-text h1,\n.w-e-text h2,\n.w-e-text h3,\n.w-e-text h4,\n.w-e-text h5,\n.w-e-text table,\n.w-e-text pre {\n  margin: 10px 0;\n  line-height: 1.5;\n}\n.w-e-text ul,\n.w-e-text ol {\n  margin: 10px 0 10px 20px;\n}\n.w-e-text blockquote {\n  display: block;\n  border-left: 8px solid #d0e5f2;\n  padding: 5px 10px;\n  margin: 10px 0;\n  line-height: 1.4;\n  font-size: 100%;\n  background-color: #f1f1f1;\n}\n.w-e-text code {\n  display: inline-block;\n  background-color: #f1f1f1;\n  border-radius: 3px;\n  padding: 3px 5px;\n  margin: 0 3px;\n}\n.w-e-text pre code {\n  display: block;\n}\n.w-e-text table {\n  border-top: 1px solid #ccc;\n  border-left: 1px solid #ccc;\n}\n.w-e-text table td,\n.w-e-text table th {\n  border-bottom: 1px solid #ccc;\n  border-right: 1px solid #ccc;\n  padding: 3px 5px;\n  min-height: 30px;\n  height: 30px;\n}\n.w-e-text table th {\n  border-bottom: 2px solid #ccc;\n  text-align: center;\n  background-color: #f1f1f1;\n}\n.w-e-text:focus {\n  outline: none;\n}\n.w-e-text img {\n  cursor: pointer;\n}\n.w-e-text img:hover {\n  box-shadow: 0 0 5px #333;\n}\n.w-e-text .w-e-todo {\n  margin: 0 0 0 20px;\n}\n.w-e-text .w-e-todo li {\n  list-style: none;\n  font-size: 1em;\n}\n.w-e-text .w-e-todo li span:nth-child(1) {\n  position: relative;\n  left: -18px;\n}\n.w-e-text .w-e-todo li span:nth-child(1) input {\n  position: absolute;\n  margin-right: 3px;\n}\n.w-e-text .w-e-todo li span:nth-child(1) input[type=checkbox] {\n  top: 50%;\n  margin-top: -6px;\n}\n.w-e-tooltip {\n  position: absolute;\n  display: flex;\n  color: #f1f1f1;\n  background-color: rgba(0, 0, 0, 0.75);\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);\n  border-radius: 4px;\n  padding: 4px 5px 6px;\n  justify-content: center;\n  align-items: center;\n}\n.w-e-tooltip-up::after {\n  content: "";\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border: 5px solid rgba(0, 0, 0, 0);\n  border-top-color: rgba(0, 0, 0, 0.73);\n}\n.w-e-tooltip-down::after {\n  content: "";\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border: 5px solid rgba(0, 0, 0, 0);\n  border-bottom-color: rgba(0, 0, 0, 0.73);\n}\n.w-e-tooltip-item-wrapper {\n  cursor: pointer;\n  font-size: 14px;\n  margin: 0 5px;\n}\n.w-e-tooltip-item-wrapper:hover {\n  color: #ccc;\n  text-decoration: underline;\n}\n',""]),e.exports=t},function(e,t,n){var o=n(20),i=n(157);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,'.w-e-menu .w-e-panel-container {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  border: 1px solid #ccc;\n  border-top: 0;\n  box-shadow: 1px 1px 2px #ccc;\n  color: #333;\n  background-color: #fff;\n  text-align: left;\n  /* 为 emotion panel 定制的样式 */\n  /* 上传图片、上传视频的 panel 定制样式 */\n}\n.w-e-menu .w-e-panel-container .w-e-panel-close {\n  position: absolute;\n  right: 0;\n  top: 0;\n  padding: 5px;\n  margin: 2px 5px 0 0;\n  cursor: pointer;\n  color: #999;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-close:hover {\n  color: #333;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-title {\n  list-style: none;\n  display: flex;\n  font-size: 14px;\n  margin: 2px 10px 0 10px;\n  border-bottom: 1px solid #f1f1f1;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-item {\n  padding: 3px 5px;\n  color: #999;\n  cursor: pointer;\n  margin: 0 3px;\n  position: relative;\n  top: 1px;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-active {\n  color: #333;\n  border-bottom: 1px solid #333;\n  cursor: default;\n  font-weight: 700;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content {\n  padding: 10px 15px 10px 15px;\n  font-size: 16px;\n  /* 输入框的样式 */\n  /* 按钮的样式 */\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content input:focus,\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus,\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content button:focus {\n  outline: none;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea {\n  width: 100%;\n  border: 1px solid #ccc;\n  padding: 5px;\n  margin-top: 10px;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus {\n  border-color: #1e88e5;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text] {\n  border: none;\n  border-bottom: 1px solid #ccc;\n  font-size: 14px;\n  height: 20px;\n  color: #333;\n  text-align: left;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].small {\n  width: 30px;\n  text-align: center;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].block {\n  display: block;\n  width: 100%;\n  margin: 10px 0;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {\n  border-bottom: 2px solid #1e88e5;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {\n  font-size: 14px;\n  color: #1e88e5;\n  border: none;\n  padding: 5px 10px;\n  background-color: #fff;\n  cursor: pointer;\n  border-radius: 3px;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {\n  float: left;\n  margin-right: 10px;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {\n  float: right;\n  margin-left: 10px;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {\n  color: #999;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {\n  color: #c24f4a;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {\n  background-color: #f1f1f1;\n}\n.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {\n  content: "";\n  display: table;\n  clear: both;\n}\n.w-e-menu .w-e-panel-container .w-e-emoticon-container .w-e-item {\n  cursor: pointer;\n  font-size: 18px;\n  padding: 0 3px;\n  display: inline-block;\n}\n.w-e-menu .w-e-panel-container .w-e-up-img-container,\n.w-e-menu .w-e-panel-container .w-e-up-video-container {\n  text-align: center;\n}\n.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn,\n.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn {\n  display: inline-block;\n  color: #999;\n  cursor: pointer;\n  font-size: 60px;\n  line-height: 1;\n}\n.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover,\n.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn:hover {\n  color: #333;\n}\n',""]),e.exports=t},function(e,t,n){var o=n(20),i=n(159);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,".w-e-toolbar .w-e-droplist {\n  position: absolute;\n  left: 0;\n  top: 0;\n  background-color: #fff;\n  border: 1px solid #f1f1f1;\n  border-right-color: #ccc;\n  border-bottom-color: #ccc;\n}\n.w-e-toolbar .w-e-droplist .w-e-dp-title {\n  text-align: center;\n  color: #999;\n  line-height: 2;\n  border-bottom: 1px solid #f1f1f1;\n  font-size: 13px;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-list {\n  list-style: none;\n  line-height: 1;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {\n  color: #333;\n  padding: 5px 0;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {\n  background-color: #f1f1f1;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-block {\n  list-style: none;\n  text-align: left;\n  padding: 5px;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {\n  display: inline-block;\n  padding: 3px 5px;\n}\n.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {\n  background-color: #f1f1f1;\n}\n",""]),e.exports=t},function(e,t,n){"use strict";var o=n(0),i=o(n(161));Element.prototype.matches||(Element.prototype.matches=function(e){var t=this.ownerDocument.querySelectorAll(e),n=t.length;for(n;n>=0;n--)if(t.item(n)===this)break;return n>-1}),i["default"]||(window.Promise=i["default"])},function(e,t,n){e.exports=n(162)},function(e,t,n){var o=n(163);e.exports=o},function(e,t,n){n(61),n(50),n(54),n(175),n(178),n(179);var o=n(9);e.exports=o.Promise},function(e,t,n){var o=n(62),i=n(49),r=function(e){return function(t,n){var r,a,l=String(i(t)),u=o(n),s=l.length;return u<0||u>=s?e?"":void 0:(r=l.charCodeAt(u),r<55296||r>56319||u+1===s||(a=l.charCodeAt(u+1))<56320||a>57343?e?l.charAt(u):r:e?l.slice(u,u+2):a-56320+(r-55296<<10)+65536)}};e.exports={codeAt:r(!1),charAt:r(!0)}},function(e,t,n){var o=n(8),i=n(102),r=o.WeakMap;e.exports="function"===typeof r&&/native code/.test(i(r))},function(e,t,n){var o=n(8),i=n(19);e.exports=function(e,t){try{i(o,e,t)}catch(n){o[e]=t}return t}},function(e,t,n){"use strict";var o=n(104).IteratorPrototype,i=n(77),r=n(48),a=n(37),l=n(44),u=function(){return this};e.exports=function(e,t,n){var s=t+" Iterator";return e.prototype=i(o,{next:r(1,n)}),a(e,s,!1,!0),l[s]=u,e}},function(e,t,n){var o=n(11);e.exports=!o((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},function(e,t,n){var o=n(14),i=n(18),r=n(25),a=n(52);e.exports=o?Object.defineProperties:function(e,t){r(e);var n,o=a(t),l=o.length,u=0;while(l>u)i.f(e,n=o[u++],t[n]);return e}},function(e,t,n){"use strict";var o=n(81),i=n(65);e.exports=o?{}.toString:function(){return"[object "+i(this)+"]"}},function(e,t,n){var o=n(25),i=n(172);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,e.call(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return o(n),i(r),t?e.call(n,r):n.__proto__=r,n}}():void 0)},function(e,t,n){var o=n(13);e.exports=function(e){if(!o(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype");return e}},function(e,t,n){"use strict";var o=n(30),i=n(82),r=n(44),a=n(42),l=n(75),u="Array Iterator",s=a.set,c=a.getterFor(u);e.exports=l(Array,"Array",(function(e,t){s(this,{type:u,target:o(e),index:0,kind:t})}),(function(){var e=c(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values"),r.Arguments=r.Array,i("keys"),i("values"),i("entries")},function(e,t){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(e,t,n){"use strict";var o,i,r,a,l=n(5),u=n(43),s=n(8),c=n(36),f=n(109),d=n(53),p=n(110),v=n(37),h=n(111),A=n(13),m=n(41),g=n(83),y=n(34),w=n(102),x=n(66),_=n(115),E=n(116),b=n(117).set,C=n(176),S=n(119),M=n(177),k=n(85),D=n(120),N=n(42),T=n(101),B=n(10),I=n(86),R=B("species"),P="Promise",H=N.get,F=N.set,L=N.getterFor(P),Q=f,O=s.TypeError,U=s.document,j=s.process,Y=c("fetch"),z=k.f,$=z,V="process"==y(j),G=!!(U&&U.createEvent&&s.dispatchEvent),J="unhandledrejection",K="rejectionhandled",W=0,q=1,X=2,Z=1,ee=2,te=T(P,(function(){var e=w(Q)!==String(Q);if(!e){if(66===I)return!0;if(!V&&"function"!=typeof PromiseRejectionEvent)return!0}if(u&&!Q.prototype["finally"])return!0;if(I>=51&&/native code/.test(Q))return!1;var t=Q.resolve(1),n=function(e){e((function(){}),(function(){}))},o=t.constructor={};return o[R]=n,!(t.then((function(){}))instanceof n)})),ne=te||!_((function(e){Q.all(e)["catch"]((function(){}))})),oe=function(e){var t;return!(!A(e)||"function"!=typeof(t=e.then))&&t},ie=function(e,t,n){if(!t.notified){t.notified=!0;var o=t.reactions;C((function(){var i=t.value,r=t.state==q,a=0;while(o.length>a){var l,u,s,c=o[a++],f=r?c.ok:c.fail,d=c.resolve,p=c.reject,v=c.domain;try{f?(r||(t.rejection===ee&&ue(e,t),t.rejection=Z),!0===f?l=i:(v&&v.enter(),l=f(i),v&&(v.exit(),s=!0)),l===c.promise?p(O("Promise-chain cycle")):(u=oe(l))?u.call(l,d,p):d(l)):p(i)}catch(h){v&&!s&&v.exit(),p(h)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&ae(e,t)}))}},re=function(e,t,n){var o,i;G?(o=U.createEvent("Event"),o.promise=t,o.reason=n,o.initEvent(e,!1,!0),s.dispatchEvent(o)):o={promise:t,reason:n},(i=s["on"+e])?i(o):e===J&&M("Unhandled promise rejection",n)},ae=function(e,t){b.call(s,(function(){var n,o=t.value,i=le(t);if(i&&(n=D((function(){V?j.emit("unhandledRejection",o,e):re(J,e,o)})),t.rejection=V||le(t)?ee:Z,n.error))throw n.value}))},le=function(e){return e.rejection!==Z&&!e.parent},ue=function(e,t){b.call(s,(function(){V?j.emit("rejectionHandled",e):re(K,e,t.value)}))},se=function(e,t,n,o){return function(i){e(t,n,i,o)}},ce=function(e,t,n,o){t.done||(t.done=!0,o&&(t=o),t.value=n,t.state=X,ie(e,t,!0))},fe=function(e,t,n,o){if(!t.done){t.done=!0,o&&(t=o);try{if(e===n)throw O("Promise can't be resolved itself");var i=oe(n);i?C((function(){var o={done:!1};try{i.call(n,se(fe,e,o,t),se(ce,e,o,t))}catch(r){ce(e,o,r,t)}})):(t.value=n,t.state=q,ie(e,t,!1))}catch(r){ce(e,{done:!1},r,t)}}};te&&(Q=function(e){g(this,Q,P),m(e),o.call(this);var t=H(this);try{e(se(fe,this,t),se(ce,this,t))}catch(n){ce(this,t,n)}},o=function(e){F(this,{type:P,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:W,value:void 0})},o.prototype=p(Q.prototype,{then:function(e,t){var n=L(this),o=z(E(this,Q));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=V?j.domain:void 0,n.parent=!0,n.reactions.push(o),n.state!=W&&ie(this,n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new o,t=H(e);this.promise=e,this.resolve=se(fe,e,t),this.reject=se(ce,e,t)},k.f=z=function(e){return e===Q||e===r?new i(e):$(e)},u||"function"!=typeof f||(a=f.prototype.then,d(f.prototype,"then",(function(e,t){var n=this;return new Q((function(e,t){a.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof Y&&l({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return S(Q,Y.apply(s,arguments))}}))),l({global:!0,wrap:!0,forced:te},{Promise:Q}),v(Q,P,!1,!0),h(P),r=c(P),l({target:P,stat:!0,forced:te},{reject:function(e){var t=z(this);return t.reject.call(void 0,e),t.promise}}),l({target:P,stat:!0,forced:u||te},{resolve:function(e){return S(u&&this===r?Q:this,e)}}),l({target:P,stat:!0,forced:ne},{all:function(e){var t=this,n=z(t),o=n.resolve,i=n.reject,r=D((function(){var n=m(t.resolve),r=[],a=0,l=1;x(e,(function(e){var u=a++,s=!1;r.push(void 0),l++,n.call(t,e).then((function(e){s||(s=!0,r[u]=e,--l||o(r))}),i)})),--l||o(r)}));return r.error&&i(r.value),n.promise},race:function(e){var t=this,n=z(t),o=n.reject,i=D((function(){var i=m(t.resolve);x(e,(function(e){i.call(t,e).then(n.resolve,o)}))}));return i.error&&o(i.value),n.promise}})},function(e,t,n){var o,i,r,a,l,u,s,c,f=n(8),d=n(71).f,p=n(34),v=n(117).set,h=n(118),A=f.MutationObserver||f.WebKitMutationObserver,m=f.process,g=f.Promise,y="process"==p(m),w=d(f,"queueMicrotask"),x=w&&w.value;x||(o=function(){var e,t;y&&(e=m.domain)&&e.exit();while(i){t=i.fn,i=i.next;try{t()}catch(n){throw i?a():r=void 0,n}}r=void 0,e&&e.enter()},y?a=function(){m.nextTick(o)}:A&&!h?(l=!0,u=document.createTextNode(""),new A(o).observe(u,{characterData:!0}),a=function(){u.data=l=!l}):g&&g.resolve?(s=g.resolve(void 0),c=s.then,a=function(){c.call(s,o)}):a=function(){v.call(f,o)}),e.exports=x||function(e){var t={fn:e,next:void 0};r&&(r.next=t),i||(i=t,a()),r=t}},function(e,t,n){var o=n(8);e.exports=function(e,t){var n=o.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}},function(e,t,n){"use strict";var o=n(5),i=n(41),r=n(85),a=n(120),l=n(66);o({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=r.f(t),o=n.resolve,u=n.reject,s=a((function(){var n=i(t.resolve),r=[],a=0,u=1;l(e,(function(e){var i=a++,l=!1;r.push(void 0),u++,n.call(t,e).then((function(e){l||(l=!0,r[i]={status:"fulfilled",value:e},--u||o(r))}),(function(e){l||(l=!0,r[i]={status:"rejected",reason:e},--u||o(r))}))})),--u||o(r)}));return s.error&&u(s.value),n.promise}})},function(e,t,n){"use strict";var o=n(5),i=n(43),r=n(109),a=n(11),l=n(36),u=n(116),s=n(119),c=n(53),f=!!r&&a((function(){r.prototype["finally"].call({then:function(){}},(function(){}))}));o({target:"Promise",proto:!0,real:!0,forced:f},{finally:function(e){var t=u(this,l("Promise")),n="function"==typeof e;return this.then(n?function(n){return s(t,e()).then((function(){return n}))}:e,n?function(n){return s(t,e()).then((function(){throw n}))}:e)}}),i||"function"!=typeof r||r.prototype["finally"]||c(r.prototype,"finally",l("Promise").prototype["finally"])},function(e,t,n){n(54);var o=n(181),i=n(65),r=Array.prototype,a={DOMTokenList:!0,NodeList:!0};e.exports=function(e){var t=e.forEach;return e===r||e instanceof Array&&t===r.forEach||a.hasOwnProperty(i(e))?o:t}},function(e,t,n){var o=n(182);e.exports=o},function(e,t,n){n(183);var o=n(15);e.exports=o("Array").forEach},function(e,t,n){"use strict";var o=n(5),i=n(184);o({target:"Array",proto:!0,forced:[].forEach!=i},{forEach:i})},function(e,t,n){"use strict";var o=n(32).forEach,i=n(67),r=n(22),a=i("forEach"),l=r("forEach");e.exports=a&&l?[].forEach:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}},function(e,t,n){var o=n(186);e.exports=o},function(e,t,n){n(187);var o=n(9);e.exports=o.Array.isArray},function(e,t,n){var o=n(5),i=n(55);o({target:"Array",stat:!0},{isArray:i})},function(e,t,n){var o=n(189);e.exports=o},function(e,t,n){var o=n(190),i=Array.prototype;e.exports=function(e){var t=e.map;return e===i||e instanceof Array&&t===i.map?o:t}},function(e,t,n){n(191);var o=n(15);e.exports=o("Array").map},function(e,t,n){"use strict";var o=n(5),i=n(32).map,r=n(56),a=n(22),l=r("map"),u=a("map");o({target:"Array",proto:!0,forced:!l||!u},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var o=n(193);e.exports=o},function(e,t,n){var o=n(194),i=String.prototype;e.exports=function(e){var t=e.trim;return"string"===typeof e||e===i||e instanceof String&&t===i.trim?o:t}},function(e,t,n){n(195);var o=n(15);e.exports=o("String").trim},function(e,t,n){"use strict";var o=n(5),i=n(90).trim,r=n(196);o({target:"String",proto:!0,forced:r("trim")},{trim:function(){return i(this)}})},function(e,t,n){var o=n(11),i=n(68),r="​᠎";e.exports=function(e){return o((function(){return!!i[e]()||r[e]()!=r||i[e].name!==e}))}},function(e,t,n){var o=n(198);e.exports=o},function(e,t,n){n(199),n(61),n(50),n(54);var o=n(9);e.exports=o.Map},function(e,t,n){"use strict";var o=n(122),i=n(124);e.exports=o("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},function(e,t,n){var o=n(11);e.exports=!o((function(){return Object.isExtensible(Object.preventExtensions({}))}))},function(e,t,n){var o=n(202);e.exports=o},function(e,t,n){var o=n(203),i=Array.prototype;e.exports=function(e){var t=e.indexOf;return e===i||e instanceof Array&&t===i.indexOf?o:t}},function(e,t,n){n(204);var o=n(15);e.exports=o("Array").indexOf},function(e,t,n){"use strict";var o=n(5),i=n(78).indexOf,r=n(67),a=n(22),l=[].indexOf,u=!!l&&1/[1].indexOf(1,-0)<0,s=r("indexOf"),c=a("indexOf",{ACCESSORS:!0,1:0});o({target:"Array",proto:!0,forced:u||!s||!c},{indexOf:function(e){return u?l.apply(this,arguments)||0:i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var o=n(206);e.exports=o},function(e,t,n){var o=n(207),i=Array.prototype;e.exports=function(e){var t=e.splice;return e===i||e instanceof Array&&t===i.splice?o:t}},function(e,t,n){n(208);var o=n(15);e.exports=o("Array").splice},function(e,t,n){"use strict";var o=n(5),i=n(79),r=n(62),a=n(35),l=n(31),u=n(88),s=n(69),c=n(56),f=n(22),d=c("splice"),p=f("splice",{ACCESSORS:!0,0:0,1:2}),v=Math.max,h=Math.min,A=9007199254740991,m="Maximum allowed length exceeded";o({target:"Array",proto:!0,forced:!d||!p},{splice:function(e,t){var n,o,c,f,d,p,g=l(this),y=a(g.length),w=i(e,y),x=arguments.length;if(0===x?n=o=0:1===x?(n=0,o=y-w):(n=x-2,o=h(v(r(t),0),y-w)),y+n-o>A)throw TypeError(m);for(c=u(g,o),f=0;f<o;f++)d=w+f,d in g&&s(c,f,g[d]);if(c.length=o,n<o){for(f=w;f<y-o;f++)d=f+o,p=f+n,d in g?g[p]=g[d]:delete g[p];for(f=y;f>y-o+n;f--)delete g[f-1]}else if(n>o)for(f=y-o;f>w;f--)d=f+o-1,p=f+n-1,d in g?g[p]=g[d]:delete g[p];for(f=0;f<n;f++)g[f+w]=arguments[f+2];return g.length=y-o+n,c}})},function(e,t,n){var o=n(210);e.exports=o},function(e,t,n){var o=n(211),i=Array.prototype;e.exports=function(e){var t=e.filter;return e===i||e instanceof Array&&t===i.filter?o:t}},function(e,t,n){n(212);var o=n(15);e.exports=o("Array").filter},function(e,t,n){"use strict";var o=n(5),i=n(32).filter,r=n(56),a=n(22),l=r("filter"),u=a("filter");o({target:"Array",proto:!0,forced:!l||!u},{filter:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var o=n(214);e.exports=o},function(e,t,n){var o=n(215),i=n(217),r=Array.prototype,a=String.prototype;e.exports=function(e){var t=e.includes;return e===r||e instanceof Array&&t===r.includes?o:"string"===typeof e||e===a||e instanceof String&&t===a.includes?i:t}},function(e,t,n){n(216);var o=n(15);e.exports=o("Array").includes},function(e,t,n){"use strict";var o=n(5),i=n(78).includes,r=n(82),a=n(22),l=a("indexOf",{ACCESSORS:!0,1:0});o({target:"Array",proto:!0,forced:!l},{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),r("includes")},function(e,t,n){n(218);var o=n(15);e.exports=o("String").includes},function(e,t,n){"use strict";var o=n(5),i=n(219),r=n(49),a=n(221);o({target:"String",proto:!0,forced:!a("includes")},{includes:function(e){return!!~String(r(this)).indexOf(i(e),arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var o=n(220);e.exports=function(e){if(o(e))throw TypeError("The method doesn't accept regular expressions");return e}},function(e,t,n){var o=n(13),i=n(34),r=n(10),a=r("match");e.exports=function(e){var t;return o(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==i(e))}},function(e,t,n){var o=n(10),i=o("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[i]=!1,"/./"[e](t)}catch(o){}}return!1}},function(e,t,n){var o=n(223);e.exports=o},function(e,t,n){var o=n(224),i=Function.prototype;e.exports=function(e){var t=e.bind;return e===i||e instanceof Function&&t===i.bind?o:t}},function(e,t,n){n(225);var o=n(15);e.exports=o("Function").bind},function(e,t,n){var o=n(5),i=n(226);o({target:"Function",proto:!0},{bind:i})},function(e,t,n){"use strict";var o=n(41),i=n(13),r=[].slice,a={},l=function(e,t,n){if(!(t in a)){for(var o=[],i=0;i<t;i++)o[i]="a["+i+"]";a[t]=Function("C,a","return new C("+o.join(",")+")")}return a[t](e,n)};e.exports=Function.bind||function(e){var t=o(this),n=r.call(arguments,1),a=function(){var o=n.concat(r.call(arguments));return this instanceof a?l(t,o.length,o):t.apply(e,o)};return i(t.prototype)&&(a.prototype=t.prototype),a}},function(e,t,n){e.exports=n(228)},function(e,t,n){var o=n(229);e.exports=o},function(e,t,n){n(125),n(50),n(54);var o=n(93);e.exports=o.f("iterator")},function(e,t,n){e.exports=n(231)},function(e,t,n){var o=n(232);n(251),n(252),n(253),n(254),n(255),e.exports=o},function(e,t,n){n(233),n(61),n(234),n(236),n(237),n(238),n(239),n(125),n(240),n(241),n(242),n(243),n(244),n(245),n(246),n(247),n(248),n(249),n(250);var o=n(9);e.exports=o.Symbol},function(e,t,n){"use strict";var o=n(5),i=n(11),r=n(55),a=n(13),l=n(31),u=n(35),s=n(69),c=n(88),f=n(56),d=n(10),p=n(86),v=d("isConcatSpreadable"),h=9007199254740991,A="Maximum allowed index exceeded",m=p>=51||!i((function(){var e=[];return e[v]=!1,e.concat()[0]!==e})),g=f("concat"),y=function(e){if(!a(e))return!1;var t=e[v];return void 0!==t?!!t:r(e)},w=!m||!g;o({target:"Array",proto:!0,forced:w},{concat:function(e){var t,n,o,i,r,a=l(this),f=c(a,0),d=0;for(t=-1,o=arguments.length;t<o;t++)if(r=-1===t?a:arguments[t],y(r)){if(i=u(r.length),d+i>h)throw TypeError(A);for(n=0;n<i;n++,d++)n in r&&s(f,d,r[n])}else{if(d>=h)throw TypeError(A);s(f,d++,r)}return f.length=d,f}})},function(e,t,n){"use strict";var o=n(5),i=n(8),r=n(36),a=n(43),l=n(14),u=n(76),s=n(106),c=n(11),f=n(16),d=n(55),p=n(13),v=n(25),h=n(31),A=n(30),m=n(60),g=n(48),y=n(77),w=n(52),x=n(126),_=n(235),E=n(127),b=n(71),C=n(18),S=n(59),M=n(19),k=n(53),D=n(74),N=n(63),T=n(51),B=n(64),I=n(10),R=n(93),P=n(12),H=n(37),F=n(42),L=n(32).forEach,Q=N("hidden"),O="Symbol",U="prototype",j=I("toPrimitive"),Y=F.set,z=F.getterFor(O),$=Object[U],V=i.Symbol,G=r("JSON","stringify"),J=b.f,K=C.f,W=_.f,q=S.f,X=D("symbols"),Z=D("op-symbols"),ee=D("string-to-symbol-registry"),te=D("symbol-to-string-registry"),ne=D("wks"),oe=i.QObject,ie=!oe||!oe[U]||!oe[U].findChild,re=l&&c((function(){return 7!=y(K({},"a",{get:function(){return K(this,"a",{value:7}).a}})).a}))?function(e,t,n){var o=J($,t);o&&delete $[t],K(e,t,n),o&&e!==$&&K($,t,o)}:K,ae=function(e,t){var n=X[e]=y(V[U]);return Y(n,{type:O,tag:e,description:t}),l||(n.description=t),n},le=s?function(e){return"symbol"==typeof e}:function(e){return Object(e)instanceof V},ue=function(e,t,n){e===$&&ue(Z,t,n),v(e);var o=m(t,!0);return v(n),f(X,o)?(n.enumerable?(f(e,Q)&&e[Q][o]&&(e[Q][o]=!1),n=y(n,{enumerable:g(0,!1)})):(f(e,Q)||K(e,Q,g(1,{})),e[Q][o]=!0),re(e,o,n)):K(e,o,n)},se=function(e,t){v(e);var n=A(t),o=w(n).concat(ve(n));return L(o,(function(t){l&&!fe.call(n,t)||ue(e,t,n[t])})),e},ce=function(e,t){return void 0===t?y(e):se(y(e),t)},fe=function(e){var t=m(e,!0),n=q.call(this,t);return!(this===$&&f(X,t)&&!f(Z,t))&&(!(n||!f(this,t)||!f(X,t)||f(this,Q)&&this[Q][t])||n)},de=function(e,t){var n=A(e),o=m(t,!0);if(n!==$||!f(X,o)||f(Z,o)){var i=J(n,o);return!i||!f(X,o)||f(n,Q)&&n[Q][o]||(i.enumerable=!0),i}},pe=function(e){var t=W(A(e)),n=[];return L(t,(function(e){f(X,e)||f(T,e)||n.push(e)})),n},ve=function(e){var t=e===$,n=W(t?Z:A(e)),o=[];return L(n,(function(e){!f(X,e)||t&&!f($,e)||o.push(X[e])})),o};if(u||(V=function(){if(this instanceof V)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=B(e),n=function(e){this===$&&n.call(Z,e),f(this,Q)&&f(this[Q],t)&&(this[Q][t]=!1),re(this,t,g(1,e))};return l&&ie&&re($,t,{configurable:!0,set:n}),ae(t,e)},k(V[U],"toString",(function(){return z(this).tag})),k(V,"withoutSetter",(function(e){return ae(B(e),e)})),S.f=fe,C.f=ue,b.f=de,x.f=_.f=pe,E.f=ve,R.f=function(e){return ae(I(e),e)},l&&(K(V[U],"description",{configurable:!0,get:function(){return z(this).description}}),a||k($,"propertyIsEnumerable",fe,{unsafe:!0}))),o({global:!0,wrap:!0,forced:!u,sham:!u},{Symbol:V}),L(w(ne),(function(e){P(e)})),o({target:O,stat:!0,forced:!u},{for:function(e){var t=String(e);if(f(ee,t))return ee[t];var n=V(t);return ee[t]=n,te[n]=t,n},keyFor:function(e){if(!le(e))throw TypeError(e+" is not a symbol");if(f(te,e))return te[e]},useSetter:function(){ie=!0},useSimple:function(){ie=!1}}),o({target:"Object",stat:!0,forced:!u,sham:!l},{create:ce,defineProperty:ue,defineProperties:se,getOwnPropertyDescriptor:de}),o({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:pe,getOwnPropertySymbols:ve}),o({target:"Object",stat:!0,forced:c((function(){E.f(1)}))},{getOwnPropertySymbols:function(e){return E.f(h(e))}}),G){var he=!u||c((function(){var e=V();return"[null]"!=G([e])||"{}"!=G({a:e})||"{}"!=G(Object(e))}));o({target:"JSON",stat:!0,forced:he},{stringify:function(e,t,n){var o,i=[e],r=1;while(arguments.length>r)i.push(arguments[r++]);if(o=t,(p(t)||void 0!==e)&&!le(e))return d(t)||(t=function(e,t){if("function"==typeof o&&(t=o.call(this,e,t)),!le(t))return t}),i[1]=t,G.apply(null,i)}})}V[U][j]||M(V[U],j,V[U].valueOf),H(V,O),T[Q]=!0},function(e,t,n){var o=n(30),i=n(126).f,r={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],l=function(e){try{return i(e)}catch(t){return a.slice()}};e.exports.f=function(e){return a&&"[object Window]"==r.call(e)?l(e):i(o(e))}},function(e,t,n){var o=n(12);o("asyncIterator")},function(e,t){},function(e,t,n){var o=n(12);o("hasInstance")},function(e,t,n){var o=n(12);o("isConcatSpreadable")},function(e,t,n){var o=n(12);o("match")},function(e,t,n){var o=n(12);o("matchAll")},function(e,t,n){var o=n(12);o("replace")},function(e,t,n){var o=n(12);o("search")},function(e,t,n){var o=n(12);o("species")},function(e,t,n){var o=n(12);o("split")},function(e,t,n){var o=n(12);o("toPrimitive")},function(e,t,n){var o=n(12);o("toStringTag")},function(e,t,n){var o=n(12);o("unscopables")},function(e,t,n){var o=n(37);o(Math,"Math",!0)},function(e,t,n){var o=n(8),i=n(37);i(o.JSON,"JSON",!0)},function(e,t,n){var o=n(12);o("asyncDispose")},function(e,t,n){var o=n(12);o("dispose")},function(e,t,n){var o=n(12);o("observable")},function(e,t,n){var o=n(12);o("patternMatch")},function(e,t,n){var o=n(12);o("replaceAll")},function(e,t,n){e.exports=n(257)},function(e,t,n){var o=n(258);e.exports=o},function(e,t,n){n(259);var o=n(9);e.exports=o.parseInt},function(e,t,n){var o=n(5),i=n(260);o({global:!0,forced:parseInt!=i},{parseInt:i})},function(e,t,n){var o=n(8),i=n(90).trim,r=n(68),a=o.parseInt,l=/^[+-]?0[Xx]/,u=8!==a(r+"08")||22!==a(r+"0x16");e.exports=u?function(e,t){var n=i(String(e));return a(n,t>>>0||(l.test(n)?16:10))}:a},function(e,t,n){var o=n(262);e.exports=o},function(e,t,n){var o=n(263),i=Array.prototype;e.exports=function(e){var t=e.slice;return e===i||e instanceof Array&&t===i.slice?o:t}},function(e,t,n){n(264);var o=n(15);e.exports=o("Array").slice},function(e,t,n){"use strict";var o=n(5),i=n(13),r=n(55),a=n(79),l=n(35),u=n(30),s=n(69),c=n(10),f=n(56),d=n(22),p=f("slice"),v=d("slice",{ACCESSORS:!0,0:0,1:2}),h=c("species"),A=[].slice,m=Math.max;o({target:"Array",proto:!0,forced:!p||!v},{slice:function(e,t){var n,o,c,f=u(this),d=l(f.length),p=a(e,d),v=a(void 0===t?d:t,d);if(r(f)&&(n=f.constructor,"function"!=typeof n||n!==Array&&!r(n.prototype)?i(n)&&(n=n[h],null===n&&(n=void 0)):n=void 0,n===Array||void 0===n))return A.call(f,p,v);for(o=new(void 0===n?Array:n)(m(v-p,0)),c=0;p<v;p++,c++)p in f&&s(o,c,f[p]);return o.length=c,o}})},function(e,t,n){n(266);var o=n(9);e.exports=o.setTimeout},function(e,t,n){var o=n(5),i=n(8),r=n(84),a=[].slice,l=/MSIE .\./.test(r),u=function(e){return function(t,n){var o=arguments.length>2,i=o?a.call(arguments,2):void 0;return e(o?function(){("function"==typeof t?t:Function(t)).apply(this,i)}:t,n)}};o({global:!0,bind:!0,forced:l},{setTimeout:u(i.setTimeout),setInterval:u(i.setInterval)})},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(128));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(272)),u=a.__importDefault(n(273)),s=a.__importDefault(n(129)),c=a.__importDefault(n(274)),f=a.__importDefault(n(275)),d=a.__importDefault(n(276)),p=a.__importDefault(n(130)),v=a.__importDefault(n(277)),h=a.__importDefault(n(278)),A=a.__importDefault(n(279)),m=(0,r["default"])({},l["default"],u["default"],s["default"],f["default"],c["default"],d["default"],p["default"],v["default"],h["default"],A["default"],{linkCheck:function(e,t){return!0}});t["default"]=m},function(e,t,n){var o=n(269);e.exports=o},function(e,t,n){n(270);var o=n(9);e.exports=o.Object.assign},function(e,t,n){var o=n(5),i=n(271);o({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},function(e,t,n){"use strict";var o=n(14),i=n(11),r=n(52),a=n(127),l=n(59),u=n(31),s=n(72),c=Object.assign,f=Object.defineProperty;e.exports=!c||i((function(){if(o&&1!==c({b:1},c(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach((function(e){t[e]=e})),7!=c({},e)[n]||r(c({},t)).join("")!=i}))?function(e,t){var n=u(e),i=arguments.length,c=1,f=a.f,d=l.f;while(i>c){var p,v=s(arguments[c++]),h=f?r(v).concat(f(v)):r(v),A=h.length,m=0;while(A>m)p=h[m++],o&&!d.call(v,p)||(n[p]=v[p])}return n}:c},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={menus:["head","bold","fontSize","fontName","italic","underline","strikeThrough","indent","lineHeight","foreColor","backColor","link","list","todo","justify","quote","emoticon","image","video","table","code","splitLine","undo","redo"],fontNames:["黑体","仿宋","楷体","标楷体","华文仿宋","华文楷体","宋体","微软雅黑","Arial","Tahoma","Verdana","Times New Roman","Courier New"],fontSizes:{"x-small":{name:"10px",value:"1"},small:{name:"13px",value:"2"},normal:{name:"16px",value:"3"},large:{name:"18px",value:"4"},"x-large":{name:"24px",value:"5"},"xx-large":{name:"32px",value:"6"},"xxx-large":{name:"48px",value:"7"}},colors:["#000000","#ffffff","#eeece0","#1c487f","#4d80bf","#c24f4a","#8baa4a","#7b5ba1","#46acc8","#f9963b"],languageType:["Bash","C","C#","C++","CSS","Java","JavaScript","JSON","TypeScript","Plain text","Html","XML","SQL","Go","Kotlin","Lua","Markdown","PHP","Python","Shell Session","Ruby"],languageTab:"　　　　",emotions:[{title:"表情",type:"emoji",content:"😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😛 😝 😜 🤓 😎 😏 😒 😞 😔 😟 😕 🙁 😣 😖 😫 😩 😢 😭 😤 😠 😡 😳 😱 😨 🤗 🤔 😶 😑 😬 🙄 😯 😴 😷 🤑 😈 🤡 💩 👻 💀 👀 👣".split(/\s/)},{title:"手势",type:"emoji",content:"👐 🙌 👏 🤝 👍 👎 👊 ✊ 🤛 🤜 🤞 ✌️ 🤘 👌 👈 👉 👆 👇 ☝️ ✋ 🤚 🖐 🖖 👋 🤙 💪 🖕 ✍️ 🙏".split(/\s/)}],lineHeights:["1","1.15","1.6","2","2.5","3"],undoLimit:20,indentation:"2em",showMenuTooltips:!0,menuTooltipPosition:"up"}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(7);function a(e,t,n){window.alert(e),n&&console.error("wangEditor: "+n)}t["default"]={onchangeTimeout:200,onchange:null,onfocus:r.EMPTY_FN,onblur:r.EMPTY_FN,onCatalogChange:null,customAlert:a}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={pasteFilterStyle:!0,pasteIgnoreImg:!1,pasteTextHandle:function(e){return e}}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={styleWithCSS:!1}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(7);t["default"]={linkImgCheck:function(e,t,n){return!0},showLinkImg:!0,showLinkImgAlt:!0,showLinkImgHref:!0,linkImgCallback:r.EMPTY_FN,uploadImgAccept:["jpg","jpeg","png","gif","bmp"],uploadImgServer:"",uploadImgShowBase64:!1,uploadImgMaxSize:5242880,uploadImgMaxLength:100,uploadFileName:"",uploadImgParams:{},uploadImgParamsWithUrl:!1,uploadImgHeaders:{},uploadImgHooks:{},uploadImgTimeout:1e4,withCredentials:!1,customUploadImg:null,uploadImgFromMedia:null}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t["default"]={lang:"zh-CN",languages:{"zh-CN":{wangEditor:{"重置":"重置","插入":"插入","默认":"默认","创建":"创建","修改":"修改","如":"如","请输入正文":"请输入正文",menus:{title:{"标题":"标题","加粗":"加粗","字号":"字号","字体":"字体","斜体":"斜体","下划线":"下划线","删除线":"删除线","缩进":"缩进","行高":"行高","文字颜色":"文字颜色","背景色":"背景色","链接":"链接","序列":"序列","对齐":"对齐","引用":"引用","表情":"表情","图片":"图片","视频":"视频","表格":"表格","代码":"代码","分割线":"分割线","恢复":"恢复","撤销":"撤销","全屏":"全屏","取消全屏":"取消全屏","待办事项":"待办事项"},dropListMenu:{"设置标题":"设置标题","背景颜色":"背景颜色","文字颜色":"文字颜色","设置字号":"设置字号","设置字体":"设置字体","设置缩进":"设置缩进","对齐方式":"对齐方式","设置行高":"设置行高","序列":"序列",head:{"正文":"正文"},indent:{"增加缩进":"增加缩进","减少缩进":"减少缩进"},justify:{"靠左":"靠左","居中":"居中","靠右":"靠右","两端":"两端"},list:{"无序列表":"无序列表","有序列表":"有序列表"}},panelMenus:{emoticon:{"默认":"默认","新浪":"新浪",emoji:"emoji","手势":"手势"},image:{"上传图片":"上传图片","网络图片":"网络图片","图片地址":"图片地址","图片文字说明":"图片文字说明","跳转链接":"跳转链接"},link:{"链接":"链接","链接文字":"链接文字","取消链接":"取消链接","查看链接":"查看链接"},video:{"插入视频":"插入视频","上传视频":"上传视频"},table:{"行":"行","列":"列","的":"的","表格":"表格","添加行":"添加行","删除行":"删除行","添加列":"添加列","删除列":"删除列","设置表头":"设置表头","取消表头":"取消表头","插入表格":"插入表格","删除表格":"删除表格"},code:{"删除代码":"删除代码","修改代码":"修改代码","插入代码":"插入代码"}}},validate:{"张图片":"张图片","大于":"大于","图片链接":"图片链接","不是图片":"不是图片","返回结果":"返回结果","上传图片超时":"上传图片超时","上传图片错误":"上传图片错误","上传图片失败":"上传图片失败","插入图片错误":"插入图片错误","一次最多上传":"一次最多上传","下载链接失败":"下载链接失败","图片验证未通过":"图片验证未通过","服务器返回状态":"服务器返回状态","上传图片返回结果错误":"上传图片返回结果错误","请替换为支持的图片类型":"请替换为支持的图片类型","您插入的网络图片无法识别":"您插入的网络图片无法识别","您刚才插入的图片链接未通过编辑器校验":"您刚才插入的图片链接未通过编辑器校验","插入视频错误":"插入视频错误","视频链接":"视频链接","不是视频":"不是视频","视频验证未通过":"视频验证未通过","个视频":"个视频","上传视频超时":"上传视频超时","上传视频错误":"上传视频错误","上传视频失败":"上传视频失败","上传视频返回结果错误":"上传视频返回结果错误"}}},en:{wangEditor:{"重置":"reset","插入":"insert","默认":"default","创建":"create","修改":"edit","如":"like","请输入正文":"please enter the text",menus:{title:{"标题":"head","加粗":"bold","字号":"font size","字体":"font family","斜体":"italic","下划线":"underline","删除线":"strikethrough","缩进":"indent","行高":"line heihgt","文字颜色":"font color","背景色":"background","链接":"link","序列":"numbered list","对齐":"align","引用":"quote","表情":"emoticons","图片":"image","视频":"media","表格":"table","代码":"code","分割线":"split line","恢复":"redo","撤销":"undo","全屏":"fullscreen","取消全屏":"cancel fullscreen","待办事项":"todo"},dropListMenu:{"设置标题":"title","背景颜色":"background","文字颜色":"font color","设置字号":"font size","设置字体":"font family","设置缩进":"indent","对齐方式":"align","设置行高":"line heihgt","序列":"list",head:{"正文":"text"},indent:{"增加缩进":"indent","减少缩进":"outdent"},justify:{"靠左":"left","居中":"center","靠右":"right","两端":"justify"},list:{"无序列表":"unordered","有序列表":"ordered"}},panelMenus:{emoticon:{"表情":"emoji","手势":"gesture"},image:{"上传图片":"upload image","网络图片":"network image","图片地址":"image link","图片文字说明":"image alt","跳转链接":"hyperlink"},link:{"链接":"link","链接文字":"link text","取消链接":"unlink","查看链接":"view links"},video:{"插入视频":"insert video","上传视频":"upload local video"},table:{"行":"rows","列":"columns","的":" ","表格":"table","添加行":"insert row","删除行":"delete row","添加列":"insert column","删除列":"delete column","设置表头":"set header","取消表头":"cancel header","插入表格":"insert table","删除表格":"delete table"},code:{"删除代码":"delete code","修改代码":"edit code","插入代码":"insert code"}}},validate:{"张图片":"images","大于":"greater than","图片链接":"image link","不是图片":"is not image","返回结果":"return results","上传图片超时":"upload image timeout","上传图片错误":"upload image error","上传图片失败":"upload image failed","插入图片错误":"insert image error","一次最多上传":"once most at upload","下载链接失败":"download link failed","图片验证未通过":"image validate failed","服务器返回状态":"server return status","上传图片返回结果错误":"upload image return results error","请替换为支持的图片类型":"please replace with a supported image type","您插入的网络图片无法识别":"the network picture you inserted is not recognized","您刚才插入的图片链接未通过编辑器校验":"the image link you just inserted did not pass the editor verification","插入视频错误":"insert video error","视频链接":"video link","不是视频":"is not video","视频验证未通过":"video validate failed","个视频":"videos","上传视频超时":"upload video timeout","上传视频错误":"upload video error","上传视频失败":"upload video failed","上传视频返回结果错误":"upload video return results error"}}}}}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(6);function a(){return!(!r.UA.isIE()&&!r.UA.isOldEdge)}t["default"]={compatibleMode:a,historyMaxSize:30}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(7);t["default"]={onlineVideoCheck:function(e){return!0},onlineVideoCallback:r.EMPTY_FN,showLinkVideo:!0,uploadVideoAccept:["mp4"],uploadVideoServer:"",uploadVideoMaxSize:1073741824,uploadVideoName:"",uploadVideoParams:{},uploadVideoParamsWithUrl:!1,uploadVideoHeaders:{},uploadVideoHooks:{},uploadVideoTimeout:72e5,withVideoCredentials:!1,customUploadVideo:null,customInsertVideo:null}},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(6),s=n(7),c=function(){function e(e){this._currentRange=null,this.editor=e}return e.prototype.getRange=function(){return this._currentRange},e.prototype.saveRange=function(e){if(e)this._currentRange=e;else{var t=window.getSelection();if(0!==t.rangeCount){var n=t.getRangeAt(0),o=this.getSelectionContainerElem(n);if((null===o||void 0===o?void 0:o.length)&&"false"!==o.attr("contenteditable")&&!o.parentUntil("[contenteditable=false]")){var i=this.editor,a=i.$textElem;if(a.isContain(o)){var l;if(a.elems[0]===o.elems[0])if((0,r["default"])(l=a.html()).call(l)===s.EMPTY_P){var u=a.children(),c=null===u||void 0===u?void 0:u.last();i.selection.createRangeByElem(c,!0,!0),i.selection.restoreSelection()}this._currentRange=n}}}}},e.prototype.collapseRange=function(e){void 0===e&&(e=!1);var t=this._currentRange;t&&t.collapse(e)},e.prototype.getSelectionText=function(){var e=this._currentRange;return e?e.toString():""},e.prototype.getSelectionContainerElem=function(e){var t,n;if(t=e||this._currentRange,t)return n=t.commonAncestorContainer,l["default"](1===n.nodeType?n:n.parentNode)},e.prototype.getSelectionStartElem=function(e){var t,n;if(t=e||this._currentRange,t)return n=t.startContainer,l["default"](1===n.nodeType?n:n.parentNode)},e.prototype.getSelectionEndElem=function(e){var t,n;if(t=e||this._currentRange,t)return n=t.endContainer,l["default"](1===n.nodeType?n:n.parentNode)},e.prototype.isSelectionEmpty=function(){var e=this._currentRange;return!(!e||!e.startContainer||e.startContainer!==e.endContainer||e.startOffset!==e.endOffset)},e.prototype.restoreSelection=function(){var e=window.getSelection(),t=this._currentRange;e&&t&&(e.removeAllRanges(),e.addRange(t))},e.prototype.createEmptyRange=function(){var e,t=this.editor,n=this.getRange();if(n&&this.isSelectionEmpty())try{u.UA.isWebkit()?(t.cmd["do"]("insertHTML","&#8203;"),n.setEnd(n.endContainer,n.endOffset+1),this.saveRange(n)):(e=l["default"]("<strong>&#8203;</strong>"),t.cmd["do"]("insertElem",e),this.createRangeByElem(e,!0))}catch(o){}},e.prototype.createRangeByElems=function(e,t){var n=window.getSelection?window.getSelection():document.getSelection();null===n||void 0===n||n.removeAllRanges();var o=document.createRange();o.setStart(e,0),o.setEnd(t,t.childNodes.length||1),this.saveRange(o),this.restoreSelection()},e.prototype.createRangeByElem=function(e,t,n){if(e.length){var o=e.elems[0],i=document.createRange();n?i.selectNodeContents(o):i.selectNode(o),null!=t&&(i.collapse(t),t||(this.saveRange(i),this.editor.selection.moveCursor(o))),this.saveRange(i)}},e.prototype.getSelectionRangeTopNodes=function(){var e,t,n,o=null===(e=this.getSelectionStartElem())||void 0===e?void 0:e.getNodeTop(this.editor),i=null===(t=this.getSelectionEndElem())||void 0===t?void 0:t.getNodeTop(this.editor);return n=this.recordSelectionNodes(l["default"](o),l["default"](i)),n},e.prototype.moveCursor=function(e,t){var n,o=this.getRange(),i=3===e.nodeType?null===(n=e.nodeValue)||void 0===n?void 0:n.length:e.childNodes.length;(u.UA.isFirefox||u.UA.isIE())&&0!==i&&(3!==e.nodeType&&"BR"!==e.childNodes[i-1].nodeName||(i-=1));var r=null!==t&&void 0!==t?t:i;o&&e&&(o.setStart(e,r),o.setEnd(e,r),this.restoreSelection())},e.prototype.getCursorPos=function(){var e=window.getSelection();return null===e||void 0===e?void 0:e.anchorOffset},e.prototype.clearWindowSelectionRange=function(){var e=window.getSelection();e&&e.removeAllRanges()},e.prototype.recordSelectionNodes=function(e,t){var n=[],o=!0;try{var i=e,r=this.editor.$textElem;while(o){var a=null===i||void 0===i?void 0:i.getNodeTop(this.editor);"BODY"===a.getNodeName()&&(o=!1),a.length>0&&(n.push(l["default"](i)),(null===t||void 0===t?void 0:t.equal(a))||r.equal(a)?o=!1:i=a.getNextSibling())}}catch(u){o=!1}return n},e.prototype.setRangeToElem=function(e){var t=this.getRange();null===t||void 0===t||t.setStart(e,0),null===t||void 0===t||t.setEnd(e,0)},e}();t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=function(){function e(e){this.editor=e}return e.prototype["do"]=function(e,t){var n=this.editor;n.config.styleWithCSS&&document.execCommand("styleWithCSS",!1,"true");var o=n.selection;if(o.getRange()){switch(o.restoreSelection(),e){case"insertHTML":this.insertHTML(t);break;case"insertElem":this.insertElem(t);break;default:this.execCommand(e,t);break}n.menus.changeActive(),o.saveRange(),o.restoreSelection()}},e.prototype.insertHTML=function(e){var t=this.editor,n=t.selection.getRange();if(null!=n)if(this.queryCommandSupported("insertHTML"))this.execCommand("insertHTML",e);else if(n.insertNode){if(n.deleteContents(),a["default"](e).elems.length>0)n.insertNode(a["default"](e).elems[0]);else{var o=document.createElement("p");o.appendChild(document.createTextNode(e)),n.insertNode(o)}t.selection.collapseRange()}},e.prototype.insertElem=function(e){var t=this.editor,n=t.selection.getRange();null!=n&&n.insertNode&&(n.deleteContents(),n.insertNode(e.elems[0]))},e.prototype.execCommand=function(e,t){document.execCommand(e,!1,t)},e.prototype.queryCommandValue=function(e){return document.queryCommandValue(e)},e.prototype.queryCommandState=function(e){return document.queryCommandState(e)},e.prototype.queryCommandSupported=function(e){return document.queryCommandSupported(e)},e}();t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29)),a=o(n(4)),l=o(n(17)),u=o(n(27)),s=o(n(46));(0,i["default"])(t,"__esModule",{value:!0});var c=n(2),f=c.__importDefault(n(3)),d=c.__importDefault(n(287)),p=n(6),v=c.__importDefault(n(299)),h=c.__importDefault(n(300)),A=n(7),m=function(){function e(e){this.editor=e,this.eventHooks={onBlurEvents:[],changeEvents:[],dropEvents:[],clickEvents:[],keydownEvents:[],keyupEvents:[],tabUpEvents:[],tabDownEvents:[],enterUpEvents:[],enterDownEvents:[],deleteUpEvents:[],deleteDownEvents:[],pasteEvents:[],linkClickEvents:[],codeClickEvents:[],textScrollEvents:[],toolbarClickEvents:[],imgClickEvents:[],imgDragBarMouseDownEvents:[],tableClickEvents:[],menuClickEvents:[],dropListMenuHoverEvents:[],splitLineEvents:[],videoClickEvents:[]}}return e.prototype.init=function(){this._saveRange(),this._bindEventHooks(),d["default"](this)},e.prototype.togglePlaceholder=function(){var e,t=this.html(),n=(0,r["default"])(e=this.editor.$textContainerElem).call(e,".placeholder");n.hide(),this.editor.isComposing||t&&" "!==t||n.show()},e.prototype.clear=function(){this.html(A.EMPTY_P)},e.prototype.html=function(e){var t=this.editor,n=t.$textElem;if(null==e){var o=n.html();o=o.replace(/\u200b/gm,""),o=o.replace(/<p><\/p>/gim,""),o=o.replace(A.EMPTY_P_LAST_REGEX,""),o=o.replace(A.EMPTY_P_REGEX,"<p>");var i=o.match(/<(img|br|hr|input)[^>]*>/gi);return null!==i&&(0,a["default"])(i).call(i,(function(e){e.match(/\/>/)||(o=o.replace(e,e.substring(0,e.length-1)+"/>"))})),o}e=(0,l["default"])(e).call(e),""===e&&(e=A.EMPTY_P),0!==(0,u["default"])(e).call(e,"<")&&(e="<p>"+e+"</p>"),n.html(e),t.initSelection()},e.prototype.setJSON=function(e){var t=h["default"](e).children(),n=this.editor,o=n.$textElem;t&&o.replaceChildAll(t)},e.prototype.getJSON=function(){var e=this.editor,t=e.$textElem;return v["default"](t)},e.prototype.text=function(e){var t=this.editor,n=t.$textElem;if(null==e){var o=n.text();return o=o.replace(/\u200b/gm,""),o}n.text("<p>"+e+"</p>"),t.initSelection()},e.prototype.append=function(e){var t=this.editor;0!==(0,u["default"])(e).call(e,"<")&&(e="<p>"+e+"</p>"),this.html(this.html()+e),t.initSelection()},e.prototype._saveRange=function(){var e=this.editor,t=e.$textElem,n=f["default"](document);function o(){e.selection.saveRange(),e.menus.changeActive()}function i(){o(),t.off("click",i)}function r(){o(),n.off("mouseup",r)}function a(){n.on("mouseup",r),t.off("mouseleave",a)}t.on("keyup",o),t.on("click",i),t.on("mousedown",(function(){t.on("mouseleave",a)})),t.on("mouseup",(function(n){t.off("mouseleave",a),(0,s["default"])((function(){var t=e.selection,n=t.getRange();null!==n&&o()}),0)}))},e.prototype._bindEventHooks=function(){var e=this.editor,t=e.$textElem,n=this.eventHooks;function o(e){e.preventDefault()}t.on("click",(function(e){var t=n.clickEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))})),t.on("keyup",(function(e){if(13===e.keyCode){var t=n.enterUpEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("keyup",(function(e){var t=n.keyupEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))})),t.on("keydown",(function(e){var t=n.keydownEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))})),t.on("keyup",(function(e){if(8===e.keyCode||46===e.keyCode){var t=n.deleteUpEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("keydown",(function(e){if(8===e.keyCode||46===e.keyCode){var t=n.deleteDownEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("paste",(function(e){if(!p.UA.isIE()){e.preventDefault();var t=n.pasteEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("keydown",(function(t){(e.isFocus||e.isCompatibleMode)&&(t.ctrlKey||t.metaKey)&&90===t.keyCode&&(t.preventDefault(),t.shiftKey?e.history.restore():e.history.revoke())})),t.on("keyup",(function(e){if(9===e.keyCode){e.preventDefault();var t=n.tabUpEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("keydown",(function(e){if(9===e.keyCode){e.preventDefault();var t=n.tabDownEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("scroll",p.throttle((function(e){var t=n.textScrollEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}),100)),f["default"](document).on("dragleave",o).on("drop",o).on("dragenter",o).on("dragover",o),e.beforeDestroy((function(){f["default"](document).off("dragleave",o).off("drop",o).off("dragenter",o).off("dragover",o)})),t.on("drop",(function(e){e.preventDefault();var t=n.dropEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))})),t.on("click",(function(e){var t=null,o=e.target,i=f["default"](o);if("A"===i.getNodeName())t=i;else{var r=i.parentUntil("a");null!=r&&(t=r)}if(t){var l=n.linkClickEvents;(0,a["default"])(l).call(l,(function(e){return e(t)}))}})),t.on("click",(function(e){var t=null,o=e.target,i=f["default"](o);if("IMG"!==i.getNodeName()||i.elems[0].getAttribute("data-emoji")||(e.stopPropagation(),t=i),t){var r=n.imgClickEvents;(0,a["default"])(r).call(r,(function(e){return e(t)}))}})),t.on("click",(function(e){var t=null,o=e.target,i=f["default"](o);if("PRE"===i.getNodeName())t=i;else{var r=i.parentUntil("pre");null!==r&&(t=r)}if(t){var l=n.codeClickEvents;(0,a["default"])(l).call(l,(function(e){return e(t)}))}})),t.on("click",(function(t){var o=null,i=t.target,r=f["default"](i);if("HR"===r.getNodeName()&&(o=r),o){e.selection.createRangeByElem(o),e.selection.restoreSelection();var l=n.splitLineEvents;(0,a["default"])(l).call(l,(function(e){return e(o)}))}})),e.$toolbarElem.on("click",(function(e){var t=n.toolbarClickEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))})),e.$textContainerElem.on("mousedown",(function(e){var t=e.target,o=f["default"](t);if(o.hasClass("w-e-img-drag-rb")){var i=n.imgDragBarMouseDownEvents;(0,a["default"])(i).call(i,(function(e){return e()}))}})),t.on("click",(function(t){var o=null,i=t.target;if(o=f["default"](i).parentUntilEditor("TABLE",e,i),o){var r=n.tableClickEvents;(0,a["default"])(r).call(r,(function(e){return e(o,t)}))}})),t.on("keydown",(function(e){if(13===e.keyCode){var t=n.enterDownEvents;(0,a["default"])(t).call(t,(function(t){return t(e)}))}})),t.on("click",(function(e){var t=null,o=e.target,i=f["default"](o);if("VIDEO"===i.getNodeName()&&(e.stopPropagation(),t=i),t){var r=n.videoClickEvents;(0,a["default"])(r).call(r,(function(e){return e(t)}))}}))},e}();t["default"]=m},function(e,t,n){var o=n(284);e.exports=o},function(e,t,n){var o=n(285),i=Array.prototype;e.exports=function(e){var t=e.find;return e===i||e instanceof Array&&t===i.find?o:t}},function(e,t,n){n(286);var o=n(15);e.exports=o("Array").find},function(e,t,n){"use strict";var o=n(5),i=n(32).find,r=n(82),a=n(22),l="find",u=!0,s=a(l);l in[]&&Array(1)[l]((function(){u=!1})),o({target:"Array",proto:!0,forced:u||!s},{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),r(l)},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(288)),l=r.__importStar(n(289)),u=r.__importDefault(n(290)),s=r.__importDefault(n(291)),c=r.__importDefault(n(298));function f(e){var t=e.editor,n=e.eventHooks;a["default"](t,n.enterUpEvents,n.enterDownEvents),l["default"](t,n.deleteUpEvents,n.deleteDownEvents),l.cutToKeepP(t,n.keyupEvents),u["default"](t,n.tabDownEvents),s["default"](t,n.pasteEvents),c["default"](t,n.imgClickEvents)}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(27));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(7),u=a.__importDefault(n(3));function s(e,t,n){function o(t){var n,o=u["default"](l.EMPTY_P);o.insertBefore(t),(0,r["default"])(n=t.html()).call(n,"<img")>=0?o.remove():(e.selection.createRangeByElem(o,!0,!0),e.selection.restoreSelection(),t.remove())}function i(){var t=e.$textElem,n=e.selection.getSelectionContainerElem(),i=n.parent();if("<code><br></code>"!==i.html())if("FONT"!==n.getNodeName()||""!==n.text()||"monospace"!==n.attr("face")){if(i.equal(t)){var r=n.getNodeName();"P"===r&&null===n.attr("data-we-empty-p")||n.text()||o(n)}}else o(i);else o(i)}function a(t){var n;e.selection.saveRange(null===(n=getSelection())||void 0===n?void 0:n.getRangeAt(0));var o=e.selection.getSelectionContainerElem();o.id===e.textElemId&&(t.preventDefault(),e.cmd["do"]("insertHTML","<p><br></p>"))}t.push(i),n.push(a)}t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17)),a=o(n(28));(0,i["default"])(t,"__esModule",{value:!0}),t.cutToKeepP=void 0;var l=n(2),u=n(7),s=l.__importDefault(n(3));function c(e,t,n){function o(){var t=e.$textElem,n=e.$textElem.html(),o=e.$textElem.text(),i=(0,r["default"])(n).call(n),l=["<p><br></p>","<br>",'<p data-we-empty-p=""></p>',u.EMPTY_P];if(/^\s*$/.test(o)&&(!i||(0,a["default"])(l).call(l,i))){t.html(u.EMPTY_P);var s=t.getNode();e.selection.createRangeByElems(s.childNodes[0],s.childNodes[0]);var c=e.selection.getSelectionContainerElem();e.selection.restoreSelection(),e.selection.moveCursor(c.getNode(),0)}}function i(t){var n,o=e.$textElem,i=(0,r["default"])(n=o.html().toLowerCase()).call(n);i!==u.EMPTY_P||t.preventDefault()}t.push(o),n.push(i)}function f(e,t){function n(t){var n;if(88===t.keyCode){var o=e.$textElem,i=(0,r["default"])(n=o.html().toLowerCase()).call(n);if(!i||"<br>"===i){var a=s["default"](u.EMPTY_P);o.html(" "),o.append(a),e.selection.createRangeByElem(a,!1,!0),e.selection.restoreSelection(),e.selection.moveCursor(a.getNode(),0)}}}t.push(n)}t.cutToKeepP=f,t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(e,t){function n(){if(e.cmd.queryCommandSupported("insertHTML")){var t=e.selection.getSelectionContainerElem();if(t){var n=t.parent(),o=t.getNodeName(),i=n.getNodeName();"CODE"==o||"CODE"===i||"PRE"===i||/hljs/.test(i)?e.cmd["do"]("insertHTML",e.config.languageTab):e.cmd["do"]("insertHTML","&nbsp;&nbsp;&nbsp;&nbsp;")}}}t.push(n)}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17)),a=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var l=n(131),u=n(6),s=n(7);function c(e){var t,n=(0,r["default"])(t=e.replace(/<div>/gim,"<p>").replace(/<\/div>/gim,"</p>")).call(t),o=document.createElement("div");return o.innerHTML=n,o.innerHTML.replace(/<p><\/p>/gim,"")}function f(e){var t=e.replace(/<br>|<br\/>/gm,"\n").replace(/<[^>]+>/gm,"");return t}function d(e){var t;if(""===e)return!1;var n=document.createElement("div");return n.innerHTML=e,"P"===(null===(t=n.firstChild)||void 0===t?void 0:t.nodeName)}function p(e){if(!(null===e||void 0===e?void 0:e.length))return!1;var t=e.elems[0];return"P"===t.nodeName&&"<br>"===t.innerHTML}function v(e,t){function n(t){var n=e.config,o=n.pasteFilterStyle,i=n.pasteIgnoreImg,r=n.pasteTextHandle,v=l.getPasteHtml(t,o,i),h=l.getPasteText(t);h=h.replace(/\n/gm,"<br>");var A=e.selection.getSelectionContainerElem();if(A){var m=null===A||void 0===A?void 0:A.getNodeName(),g=null===A||void 0===A?void 0:A.getNodeTop(e),y="";if(g.elems[0]&&(y=null===g||void 0===g?void 0:g.getNodeName()),"CODE"===m||"PRE"===y)return r&&u.isFunction(r)&&(h=""+(r(h)||"")),void e.cmd["do"]("insertHTML",f(h));if(s.urlRegex.test(h)&&o){r&&u.isFunction(r)&&(h=""+(r(h)||""));var w=h.replace(s.urlRegex,(function(e){return'<a href="'+e+'" target="_blank">'+e+"</a>"})),x=e.selection.getRange(),_=document.createElement("div"),E=document.createDocumentFragment();if(_.innerHTML=w,null==x)return;while(_.childNodes.length)E.append(_.childNodes[0]);var b=E.querySelectorAll("a");return(0,a["default"])(b).call(b,(function(e){e.innerText=e.href})),x.insertNode&&(x.deleteContents(),x.insertNode(E)),void e.selection.clearWindowSelectionRange()}if(v)try{r&&u.isFunction(r)&&(v=""+(r(v)||""));var C=/[\.\#\@]?\w+[ ]+\{[^}]*\}/.test(v);if(C&&o)e.cmd["do"]("insertHTML",""+c(h));else{var S=c(v);if(d(S)){var M=e.$textElem;if(e.cmd["do"]("insertHTML",S),M.equal(A))return void e.selection.createEmptyRange();p(g)&&g.remove()}else e.cmd["do"]("insertHTML",S)}}catch(k){r&&u.isFunction(r)&&(h=""+(r(h)||"")),e.cmd["do"]("insertHTML",""+c(h))}}}t.push(n)}t["default"]=v},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17)),a=o(n(4)),l=o(n(28));(0,i["default"])(t,"__esModule",{value:!0});var u=n(2),s=n(293),c=u.__importDefault(n(297));function f(e){var t=/<span>.*?<\/span>/gi,n=/<span>(.*?)<\/span>/;return e.replace(t,(function(e){var t=e.match(n);return null==t?"":t[1]}))}function d(e,t){var n;return e=(0,r["default"])(n=e.toLowerCase()).call(n),!!s.IGNORE_TAGS.has(e)||!(!t||"img"!==e)}function p(e,t){var n="";n="<"+e;var o=[];(0,a["default"])(t).call(t,(function(e){o.push(e.name+'="'+e.value+'"')})),o.length>0&&(n=n+" "+o.join(" "));var i=s.EMPTY_TAGS.has(e);return n=n+(i?"/":"")+">",n}function v(e){return"</"+e+">"}function h(e,t,n){void 0===t&&(t=!0),void 0===n&&(n=!1);var o=[],i="";function u(e){e=(0,r["default"])(e).call(e),e&&(s.EMPTY_TAGS.has(e)||(i=e))}function h(){i=""}var A=new c["default"];A.parse(e,{startElement:function(e,i){if(u(e),!d(e,n)){var r=s.NECESSARY_ATTRS.get(e)||[],c=[];(0,a["default"])(i).call(i,(function(e){var n=e.name;"style"!==n?!1!==(0,l["default"])(r).call(r,n)&&c.push(e):t||c.push(e)}));var f=p(e,c);o.push(f)}},characters:function(e){e&&(d(i,n)||o.push(e))},endElement:function(e){if(!d(e,n)){var t=v(e);o.push(t),h()}},comment:function(e){u(e)}});var m=o.join("");return m=f(m),m}t["default"]=h},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(132)),a=o(n(121));(0,i["default"])(t,"__esModule",{value:!0}),t.TOP_LEVEL_TAGS=t.EMPTY_TAGS=t.NECESSARY_ATTRS=t.IGNORE_TAGS=void 0,t.IGNORE_TAGS=new r["default"](["doctype","!doctype","html","head","meta","body","script","style","link","frame","iframe","title","svg","center","o:p"]),t.NECESSARY_ATTRS=new a["default"]([["img",["src","alt"]],["a",["href","target"]],["td",["colspan","rowspan"]],["th",["colspan","rowspan"]]]),t.EMPTY_TAGS=new r["default"](["area","base","basefont","br","col","hr","img","input","isindex","embed"]),t.TOP_LEVEL_TAGS=new r["default"](["h1","h2","h3","h4","h5","p","ul","ol","table","blockquote","pre","hr","form"])},function(e,t,n){var o=n(295);e.exports=o},function(e,t,n){n(296),n(61),n(50),n(54);var o=n(9);e.exports=o.Set},function(e,t,n){"use strict";var o=n(122),i=n(124);e.exports=o("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},function(e,t){function n(){}n.prototype={handler:null,startTagRe:/^<([^>\s\/]+)((\s+[^=>\s]+(\s*=\s*((\"[^"]*\")|(\'[^']*\')|[^>\s]+))?)*)\s*\/?\s*>/m,endTagRe:/^<\/([^>\s]+)[^>]*>/m,attrRe:/([^=\s]+)(\s*=\s*((\"([^"]*)\")|(\'([^']*)\')|[^>\s]+))?/gm,parse:function(e,t){t&&(this.contentHandler=t);var n,o,i,r=!1,a=this;while(e.length>0)"\x3c!--"==e.substring(0,4)?(i=e.indexOf("--\x3e"),-1!=i?(this.contentHandler.comment(e.substring(4,i)),e=e.substring(i+3),r=!1):r=!0):"</"==e.substring(0,2)?this.endTagRe.test(e)?(RegExp.leftContext,n=RegExp.lastMatch,o=RegExp.rightContext,n.replace(this.endTagRe,(function(){return a.parseEndTag.apply(a,arguments)})),e=o,r=!1):r=!0:"<"==e.charAt(0)&&(this.startTagRe.test(e)?(RegExp.leftContext,n=RegExp.lastMatch,o=RegExp.rightContext,n.replace(this.startTagRe,(function(){return a.parseStartTag.apply(a,arguments)})),e=o,r=!1):r=!0),r&&(i=e.indexOf("<"),-1==i?(this.contentHandler.characters(e),e=""):(this.contentHandler.characters(e.substring(0,i)),e=e.substring(i))),r=!0},parseStartTag:function(e,t,n){var o=this.parseAttributes(t,n);this.contentHandler.startElement(t,o)},parseEndTag:function(e,t){this.contentHandler.endElement(t)},parseAttributes:function(e,t){var n=this,o=[];return t.replace(this.attrRe,(function(t,i,r,a,l,u,s,c){o.push(n.parseAttribute(e,t,i,r,a,l,u,s,c))})),o},parseAttribute:function(e,t,n){var o="";arguments[7]?o=arguments[8]:arguments[5]?o=arguments[6]:arguments[3]&&(o=arguments[4]);var i=!o&&!arguments[3];return{name:n,value:i?null:o}}},e.exports=n},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(e,t){function n(t){e.selection.createRangeByElem(t),e.selection.restoreSelection()}t.push(n)}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(6),u=a.__importDefault(n(3));function s(e){var t=[],n=e.childNodes()||[];return(0,r["default"])(n).call(n,(function(e){var n,o=e.nodeType;if(3===o&&(n=e.textContent||"",n=l.replaceHtmlSymbol(n)),1===o){n={},n=n,n.tag=e.nodeName.toLowerCase();for(var i=[],r=e.attributes,a=r.length||0,c=0;c<a;c++){var f=r[c];i.push({name:f.name,value:f.value})}n.attrs=i,n.children=s(u["default"](e))}n&&t.push(n)})),t}t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(92)),r=o(n(1)),a=o(n(4));(0,r["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3));function s(e,t){void 0===t&&(t=document.createElement("div"));var n=t;return(0,a["default"])(e).call(e,(function(e){var t,o;("string"===typeof e&&(t=document.createTextNode(e)),"object"===(0,i["default"])(e))&&(t=document.createElement(e.tag),(0,a["default"])(o=e.attrs).call(o,(function(e){u["default"](t).attr(e.name,e.value)})),e.children&&e.children.length>0&&s(e.children,t.getRootNode()));t&&n.appendChild(t)})),u["default"](n)}t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(89)),a=o(n(70)),l=o(n(28)),u=o(n(302)),s=o(n(4)),c=o(n(94)),f=o(n(133)),d=o(n(46)),p=o(n(57));(0,i["default"])(t,"__esModule",{value:!0});var v=n(2),h=v.__importDefault(n(87)),A=v.__importDefault(n(314)),m=v.__importDefault(n(3)),g=function(){function e(e){this.editor=e,this.menuList=[],this.constructorList=A["default"]}return e.prototype.extend=function(e,t){t&&"function"===typeof t&&(this.constructorList[e]=t)},e.prototype.init=function(){var e,t,n=this,o=this.editor.config,i=o.excludeMenus;!1===(0,r["default"])(i)&&(i=[]),o.menus=(0,a["default"])(e=o.menus).call(e,(function(e){return!1===(0,l["default"])(i).call(i,e)}));var f=(0,u["default"])(h["default"].globalCustomMenuConstructorList);f=(0,a["default"])(f).call(f,(function(e){return(0,l["default"])(i).call(i,e)})),(0,s["default"])(f).call(f,(function(e){delete h["default"].globalCustomMenuConstructorList[e]})),(0,s["default"])(t=o.menus).call(t,(function(e){var t=n.constructorList[e];n._initMenuList(e,t)}));for(var d=0,p=(0,c["default"])(h["default"].globalCustomMenuConstructorList);d<p.length;d++){var v=p[d],A=v[0],m=v[1],g=m;this._initMenuList(A,g)}this._addToToolbar(),o.showMenuTooltips&&this._bindMenuTooltips()},e.prototype._initMenuList=function(e,t){var n;if(null!=t&&"function"===typeof t)if((0,f["default"])(n=this.menuList).call(n,(function(t){return t.key===e})))console.warn("菜单名称重复:"+e);else{var o=new t(this.editor);o.key=e,this.menuList.push(o)}},e.prototype._bindMenuTooltips=function(){var e=this.editor,t=e.$toolbarElem,n=e.config,o=n.menuTooltipPosition,i=m["default"]('<div class="w-e-menu-tooltip w-e-menu-tooltip-'+o+'">\n            <div class="w-e-menu-tooltip-item-wrapper">\n              <div></div>\n            </div>\n          </div>');i.css("visibility","hidden"),t.append(i),i.css("z-index",e.zIndex.get("tooltip"));var r=0;function a(){r&&clearTimeout(r)}function l(){a(),i.css("visibility","hidden")}t.on("mouseover",(function(n){var u,s,c=n.target,f=m["default"](c);if(f.isContain(t))l();else{if(null!=f.parentUntil(".w-e-droplist"))l();else if(f.attr("data-title"))u=f.attr("data-title"),s=f;else{var p=f.parentUntil(".w-e-menu");null!=p&&(u=p.attr("data-title"),s=p)}if(u&&s){a();var v=s.getOffsetData();i.text(e.i18next.t("menus.title."+u));var h=i.getOffsetData(),A=v.left+v.width/2-h.width/2;i.css("left",A+"px"),"up"===o?i.css("top",v.top-h.height-8+"px"):"down"===o&&i.css("top",v.top+v.height+8+"px"),r=(0,d["default"])((function(){i.css("visibility","visible")}),200)}else l()}})).on("mouseleave",(function(){l()}))},e.prototype._addToToolbar=function(){var e,t=this.editor,n=t.$toolbarElem;(0,s["default"])(e=this.menuList).call(e,(function(e){var t=e.$elem;t&&n.append(t)}))},e.prototype.menuFind=function(e){for(var t=this.menuList,n=0,o=t.length;n<o;n++)if(t[n].key===e)return t[n];return t[0]},e.prototype.changeActive=function(){var e;(0,s["default"])(e=this.menuList).call(e,(function(e){var t;(0,d["default"])((0,p["default"])(t=e.tryChangeActive).call(t,e),100)}))},e}();t["default"]=g},function(e,t,n){e.exports=n(303)},function(e,t,n){var o=n(304);e.exports=o},function(e,t,n){n(305);var o=n(9);e.exports=o.Object.keys},function(e,t,n){var o=n(5),i=n(31),r=n(52),a=n(11),l=a((function(){r(1)}));o({target:"Object",stat:!0,forced:l},{keys:function(e){return r(i(e))}})},function(e,t,n){var o=n(307);e.exports=o},function(e,t,n){n(308);var o=n(9);e.exports=o.Object.entries},function(e,t,n){var o=n(5),i=n(309).entries;o({target:"Object",stat:!0},{entries:function(e){return i(e)}})},function(e,t,n){var o=n(14),i=n(52),r=n(30),a=n(59).f,l=function(e){return function(t){var n,l=r(t),u=i(l),s=u.length,c=0,f=[];while(s>c)n=u[c++],o&&!a.call(l,n)||f.push(e?[n,l[n]]:l[n]);return f}};e.exports={entries:l(!0),values:l(!1)}},function(e,t,n){var o=n(311);e.exports=o},function(e,t,n){var o=n(312),i=Array.prototype;e.exports=function(e){var t=e.some;return e===i||e instanceof Array&&t===i.some?o:t}},function(e,t,n){n(313);var o=n(15);e.exports=o("Array").some},function(e,t,n){"use strict";var o=n(5),i=n(32).some,r=n(67),a=n(22),l=r("some"),u=a("some");o({target:"Array",proto:!0,forced:!l||!u},{some:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(315)),l=r.__importDefault(n(316)),u=r.__importDefault(n(321)),s=r.__importDefault(n(326)),c=r.__importDefault(n(327)),f=r.__importDefault(n(328)),d=r.__importDefault(n(329)),p=r.__importDefault(n(331)),v=r.__importDefault(n(333)),h=r.__importDefault(n(334)),A=r.__importDefault(n(337)),m=r.__importDefault(n(338)),g=r.__importDefault(n(339)),y=r.__importDefault(n(350)),w=r.__importDefault(n(365)),x=r.__importDefault(n(369)),_=r.__importDefault(n(137)),E=r.__importDefault(n(378)),b=r.__importDefault(n(380)),C=r.__importDefault(n(381)),S=r.__importDefault(n(382)),M=r.__importDefault(n(401)),k=r.__importDefault(n(406)),D=r.__importDefault(n(409));t["default"]={bold:a["default"],head:l["default"],italic:s["default"],link:u["default"],underline:c["default"],strikeThrough:f["default"],fontName:d["default"],fontSize:p["default"],justify:v["default"],quote:h["default"],backColor:A["default"],foreColor:m["default"],video:g["default"],image:y["default"],indent:w["default"],emoticon:x["default"],list:_["default"],lineHeight:E["default"],undo:b["default"],redo:C["default"],table:S["default"],code:M["default"],splitLine:k["default"],todo:D["default"]}},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(23)),l=r.__importDefault(n(3)),u=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="加粗">\n                <i class="w-e-icon-bold"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd["do"]("bold"),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},t.prototype.tryChangeActive=function(){var e=this.editor;e.cmd.queryCommandState("bold")?this.active():this.unActive()},t}(a["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(27)),a=o(n(29)),l=o(n(4)),u=o(n(317)),s=o(n(28));(0,i["default"])(t,"__esModule",{value:!0});var c=n(2),f=c.__importDefault(n(24)),d=c.__importDefault(n(3)),p=n(6),v=n(7),h=function(e){function t(t){var n=this,o=d["default"]('<div class="w-e-menu" data-title="标题"><i class="w-e-icon-header"></i></div>'),i={width:100,title:"设置标题",type:"list",list:[{$elem:d["default"]("<h1>H1</h1>"),value:"<h1>"},{$elem:d["default"]("<h2>H2</h2>"),value:"<h2>"},{$elem:d["default"]("<h3>H3</h3>"),value:"<h3>"},{$elem:d["default"]("<h4>H4</h4>"),value:"<h4>"},{$elem:d["default"]("<h5>H5</h5>"),value:"<h5>"},{$elem:d["default"]("<p>"+t.i18next.t("menus.dropListMenu.head.正文")+"</p>"),value:"<p>"}],clickHandler:function(e){n.command(e)}};n=e.call(this,o,t,i)||this;var r=t.config.onCatalogChange;return r&&(n.oldCatalogs=[],n.addListenerCatalog(),n.getCatalogs()),n}return c.__extends(t,e),t.prototype.command=function(e){var t=this.editor,n=t.selection.getSelectionContainerElem();if(n&&t.$textElem.equal(n))this.setMultilineHead(e);else{var o;if((0,r["default"])(o=["OL","UL","LI","TABLE","TH","TR","CODE","HR"]).call(o,d["default"](n).getNodeName())>-1)return;t.cmd["do"]("formatBlock",e)}"<p>"!==e&&this.addUidForSelectionElem()},t.prototype.addUidForSelectionElem=function(){var e=this.editor,t=e.selection.getSelectionContainerElem(),n=p.getRandomCode();d["default"](t).attr("id",n)},t.prototype.addListenerCatalog=function(){var e=this,t=this.editor;t.txt.eventHooks.changeEvents.push((function(){e.getCatalogs()}))},t.prototype.getCatalogs=function(){var e=this.editor,t=this.editor.$textElem,n=e.config.onCatalogChange,o=(0,a["default"])(t).call(t,"h1,h2,h3,h4,h5"),i=[];(0,l["default"])(o).call(o,(function(e,t){var n=d["default"](e),o=n.attr("id"),r=n.getNodeName(),a=n.text();o||(o=p.getRandomCode(),n.attr("id",o)),a&&i.push({tag:r,id:o,text:a})})),(0,u["default"])(this.oldCatalogs)!==(0,u["default"])(i)&&(this.oldCatalogs=i,n&&n(i))},t.prototype.setMultilineHead=function(e){var t,n,o=this,i=this.editor,r=i.selection,a=null===(t=r.getSelectionContainerElem())||void 0===t?void 0:t.elems[0],u=["IMG","VIDEO","TABLE","TH","TR","UL","OL","PRE","HR","BLOCKQUOTE"],s=d["default"](r.getSelectionStartElem()),c=d["default"](r.getSelectionEndElem());c.elems[0].outerHTML!==d["default"](v.EMPTY_P).elems[0].outerHTML||c.elems[0].nextSibling||(c=c.prev());var f=[];f.push(s.getNodeTop(i));var p=[],h=null===(n=r.getRange())||void 0===n?void 0:n.commonAncestorContainer.childNodes;null===h||void 0===h||(0,l["default"])(h).call(h,(function(e,t){e===f[0].getNode()&&p.push(t),e===c.getNodeTop(i).getNode()&&p.push(t)}));var A=0;while(f[A].getNode()!==c.getNodeTop(i).getNode()){if(!f[A].elems[0])return;var m=d["default"](f[A].next().getNode());f.push(m),A++}null===f||void 0===f||(0,l["default"])(f).call(f,(function(t,n){if(!o.hasTag(t,u)){var i=d["default"](e),r=t.parent().getNode();i.html(""+t.html()),r.insertBefore(i.getNode(),t.getNode()),t.remove()}})),r.createRangeByElems(a.children[p[0]],a.children[p[1]])},t.prototype.hasTag=function(e,t){var n,o=this;if(!e)return!1;if((0,s["default"])(t).call(t,null===e||void 0===e?void 0:e.getNodeName()))return!0;var i=!1;return null===(n=e.children())||void 0===n||(0,l["default"])(n).call(n,(function(e){i=o.hasTag(d["default"](e),t)})),i},t.prototype.tryChangeActive=function(){var e=this.editor,t=/^h/i,n=e.cmd.queryCommandValue("formatBlock");t.test(n)?this.active():this.unActive()},t}(f["default"]);t["default"]=h},function(e,t,n){e.exports=n(318)},function(e,t,n){var o=n(319);e.exports=o},function(e,t,n){n(320);var o=n(9);o.JSON||(o.JSON={stringify:JSON.stringify}),e.exports=function(e,t,n){return o.JSON.stringify.apply(null,arguments)}},function(e,t,n){var o=n(5),i=n(36),r=n(11),a=i("JSON","stringify"),l=/[\uD800-\uDFFF]/g,u=/^[\uD800-\uDBFF]$/,s=/^[\uDC00-\uDFFF]$/,c=function(e,t,n){var o=n.charAt(t-1),i=n.charAt(t+1);return u.test(e)&&!s.test(i)||s.test(e)&&!u.test(o)?"\\u"+e.charCodeAt(0).toString(16):e},f=r((function(){return'"\\udf06\\ud834"'!==a("\udf06\ud834")||'"\\udead"'!==a("\udead")}));a&&o({target:"JSON",stat:!0,forced:f},{stringify:function(e,t,n){var o=a.apply(null,arguments);return"string"==typeof o?o.replace(l,c):o}})},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(38)),u=a.__importDefault(n(3)),s=a.__importDefault(n(322)),c=a.__importStar(n(96)),f=a.__importDefault(n(33)),d=a.__importDefault(n(324)),p=n(7),v=function(e){function t(t){var n=this,o=u["default"]('<div class="w-e-menu" data-title="链接"><i class="w-e-icon-link"></i></div>');return n=e.call(this,o,t)||this,d["default"](t),n}return a.__extends(t,e),t.prototype.clickHandler=function(){var e,t=this.editor,n=t.selection.getSelectionContainerElem(),o=t.$textElem,i=o.html(),a=(0,r["default"])(i).call(i);if(a===p.EMPTY_P){var l=o.children();t.selection.createRangeByElem(l,!0,!0),n=t.selection.getSelectionContainerElem()}if(!n||!t.$textElem.equal(n))if(this.isActive){var s="",f="";if(e=t.selection.getSelectionContainerElem(),!e)return;if("A"!==e.getNodeName()){var d=c.getParentNodeA(e);e=u["default"](d)}s=e.elems[0].innerText,f=e.attr("href"),this.createPanel(s,f)}else t.selection.isSelectionEmpty()?this.createPanel("",""):this.createPanel(t.selection.getSelectionText(),"")},t.prototype.createPanel=function(e,t){var n=s["default"](this.editor,e,t),o=new f["default"](this,n);o.create()},t.prototype.tryChangeActive=function(){var e=this.editor;c["default"](e)?this.active():this.unActive()},t}(l["default"]);t["default"]=v},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(28)),a=o(n(17)),l=o(n(29));(0,i["default"])(t,"__esModule",{value:!0});var u=n(2),s=n(6),c=u.__importDefault(n(3)),f=u.__importStar(n(96)),d=n(323);function p(e,t,n){var o,i=s.getRandom("input-link"),u=s.getRandom("input-text"),p=s.getRandom("btn-ok"),v=s.getRandom("btn-del"),h=f["default"](e)?"inline-block":"none";function A(){if(f["default"](e)){var t=e.selection.getSelectionContainerElem();t&&(e.selection.createRangeByElem(t),e.selection.restoreSelection(),o=t)}}function m(t,n){var o=t.replace(/</g,"&lt;").replace(/>/g,"&gt;"),i=c["default"]('<a target="_blank">'+o+"</a>"),r=i.elems[0];r.innerText=t,r.href=n,f["default"](e)?(A(),e.cmd["do"]("insertElem",i)):e.cmd["do"]("insertElem",i)}function g(){if(f["default"](e))if(A(),"A"===o.getNodeName()){var t,n=o.elems[0],i=n.parentElement;i&&(0,r["default"])(t=f.EXTRA_TAG).call(t,i.nodeName)?i.innerHTML=n.innerHTML:e.cmd["do"]("insertHTML","<span>"+n.innerHTML+"</span>")}else{var a=f.getParentNodeA(o),l=a.innerHTML;e.cmd["do"]("insertHTML","<span>"+l+"</span>")}}function y(t,n){var o=e.config.linkCheck(t,n);if(void 0===o);else{if(!0===o)return!0;e.config.customAlert(o,"warning")}return!1}var w={width:300,height:0,tabs:[{title:e.i18next.t("menus.panelMenus.link.链接"),tpl:'<div>\n                        <input\n                            id="'+u+'"\n                            type="text"\n                            class="block"\n                            placeholder="'+e.i18next.t("menus.panelMenus.link.链接文字")+'"/>\n                        </td>\n                        <input\n                            id="'+i+'"\n                            type="text"\n                            class="block"\n                            placeholder="'+e.i18next.t("如")+' https://..."/>\n                        </td>\n                        <div class="w-e-button-container">\n                            <button type="button" id="'+p+'" class="right">\n                                '+e.i18next.t("插入")+'\n                            </button>\n                            <button type="button" id="'+v+'" class="gray right" style="display:'+h+'">\n                                '+e.i18next.t("menus.panelMenus.link.取消链接")+"\n                            </button>\n                        </div>\n                    </div>",events:[{selector:"#"+p,type:"click",fn:function(){var t,n,o,l,s,p=e.selection.getSelectionContainerElem(),v=null===p||void 0===p?void 0:p.elems[0];e.selection.restoreSelection();var h=e.selection.getSelectionRangeTopNodes()[0].getNode(),A=window.getSelection(),g=c["default"]("#"+i),w=c["default"]("#"+u),x=(0,a["default"])(t=g.val()).call(t),_=(0,a["default"])(n=w.val()).call(n),E="";A&&!(null===A||void 0===A?void 0:A.isCollapsed)&&(E=null===(l=d.insertHtml(A,h))||void 0===l?void 0:(0,a["default"])(l).call(l));var b=null===E||void 0===E?void 0:E.replace(/<.*?>/g,""),C=null!==(s=null===b||void 0===b?void 0:b.length)&&void 0!==s?s:0;if(C<=_.length){var S=_.substring(0,C),M=_.substring(C);b===S&&(_=b+M)}if(x&&(_||(_=x),y(_,x))){if("A"===(null===v||void 0===v?void 0:v.nodeName))return v.setAttribute("href",x),v.innerText=_,!0;if("A"!==(null===v||void 0===v?void 0:v.nodeName)&&(0,r["default"])(o=f.EXTRA_TAG).call(o,v.nodeName)){var k=f.getParentNodeA(p);if(k)return k.setAttribute("href",x),v.innerText=_,!0}return m(_,x),!0}},bindEnter:!0},{selector:"#"+v,type:"click",fn:function(){return g(),!0}}]}],setLinkValue:function(e,o){var r,a="",s="";"text"===o&&(a="#"+u,s=t),"link"===o&&(a="#"+i,s=n),r=(0,l["default"])(e).call(e,a).elems[0],r.value=s}};return w}t["default"]=p},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));function a(e,t){var n=e,o=e;do{if(n.textContent===t)break;o=n,n.parentNode&&(n=null===n||void 0===n?void 0:n.parentNode)}while("P"!==(null===n||void 0===n?void 0:n.nodeName));return o}function l(e,t){var n=e.nodeName,o="";if(3===e.nodeType||/^(h|H)[1-6]$/.test(n))return t;if(1===e.nodeType){var i=e.getAttribute("style"),r=e.getAttribute("face"),a=e.getAttribute("color");i&&(o=o+' style="'+i+'"'),r&&(o=o+' face="'+r+'"'),a&&(o=o+' color="'+a+'"')}return n=n.toLowerCase(),"<"+n+o+">"+t+"</"+n+">"}function u(e,t,n,o){var i,r=null===(i=t.textContent)||void 0===i?void 0:i.substring(n,o),a=t,u="";do{u=l(a,null!==r&&void 0!==r?r:""),r=u,a=null===a||void 0===a?void 0:a.parentElement}while(a&&a.textContent!==e);return u}function s(e,t){var n,o,i,r,s,d=e.anchorNode,p=e.focusNode,v=e.anchorOffset,h=e.focusOffset,A=null!==(n=t.textContent)&&void 0!==n?n:"",m=c(t),g="",y="",w="",x="",_=d,E=p,b=d;if(null===d||void 0===d?void 0:d.isEqualNode(null!==p&&void 0!==p?p:null)){var C=u(A,d,v,h);return C=f(m,C),C}d&&(y=u(A,d,null!==v&&void 0!==v?v:0)),p&&(x=u(A,p,0,h)),d&&(_=a(d,A)),p&&(E=a(p,A)),b=null!==(o=null===_||void 0===_?void 0:_.nextSibling)&&void 0!==o?o:d;while(!(null===b||void 0===b?void 0:b.isEqualNode(null!==E&&void 0!==E?E:null))){var S=null===b||void 0===b?void 0:b.nodeName;if("#text"===S)w+=null===b||void 0===b?void 0:b.textContent;else{var M=null===(r=null===(i=null===b||void 0===b?void 0:b.firstChild)||void 0===i?void 0:i.parentElement)||void 0===r?void 0:r.innerHTML;b&&(w+=l(b,null!==M&&void 0!==M?M:""))}var k=null!==(s=null===b||void 0===b?void 0:b.nextSibling)&&void 0!==s?s:b;if(k===b)break;b=k}return g=""+y+w+x,g=f(m,g),g}function c(e){var t,n=null!==(t=e.textContent)&&void 0!==t?t:"",o=[];while((null===e||void 0===e?void 0:e.textContent)===n)"P"!==e.nodeName&&"TABLE"!==e.nodeName&&o.push(e),e=e.childNodes[0];return o}function f(e,t){return(0,r["default"])(e).call(e,(function(e){t=l(e,t)})),t}(0,i["default"])(t,"__esModule",{value:!0}),t.insertHtml=t.createPartHtml=t.makeHtmlString=t.getTopNode=void 0,t.getTopNode=a,t.makeHtmlString=l,t.createPartHtml=u,t.insertHtml=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(325));function l(e){a["default"](e)}t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(28));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(39)),s=n(96);function c(e){var t;function n(n){var o=[{$elem:l["default"]("<span>"+e.i18next.t("menus.panelMenus.link.查看链接")+"</span>"),onClick:function(e,t){var n=t.attr("href");return window.open(n,"_target"),!0}},{$elem:l["default"]("<span>"+e.i18next.t("menus.panelMenus.link.取消链接")+"</span>"),onClick:function(e,t){var n,o;e.selection.createRangeByElem(t),e.selection.restoreSelection();var i=t.childNodes();if("IMG"===(null===i||void 0===i?void 0:i.getNodeName())){var a=null===(o=null===(n=e.selection.getSelectionContainerElem())||void 0===n?void 0:n.children())||void 0===o?void 0:o.elems[0].children[0];e.cmd["do"]("insertHTML","<img \n                                src="+(null===a||void 0===a?void 0:a.getAttribute("src"))+" \n                                style="+(null===a||void 0===a?void 0:a.getAttribute("style"))+">")}else{var l,u=t.elems[0],c=u.innerHTML,f=u.parentElement;f&&(0,r["default"])(l=s.EXTRA_TAG).call(l,f.nodeName)?f.innerHTML=c:e.cmd["do"]("insertHTML","<span>"+c+"</span>")}return!0}}];t=new u["default"](e,n,o),t.create()}function o(){t&&(t.remove(),t=null)}return{showLinkTooltip:n,hideLinkTooltip:o}}function f(e){var t=c(e),n=t.showLinkTooltip,o=t.hideLinkTooltip;e.txt.eventHooks.linkClickEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o)}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(23)),l=r.__importDefault(n(3)),u=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="斜体">\n                <i class="w-e-icon-italic"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd["do"]("italic"),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},t.prototype.tryChangeActive=function(){var e=this.editor;e.cmd.queryCommandState("italic")?this.active():this.unActive()},t}(a["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(23)),l=r.__importDefault(n(3)),u=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="下划线">\n                <i class="w-e-icon-underline"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd["do"]("underline"),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},t.prototype.tryChangeActive=function(){var e=this.editor;e.cmd.queryCommandState("underline")?this.active():this.unActive()},t}(a["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(23)),l=r.__importDefault(n(3)),u=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="删除线">\n                <i class="w-e-icon-strikethrough"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd["do"]("strikeThrough"),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},t.prototype.tryChangeActive=function(){var e=this.editor;e.cmd.queryCommandState("strikeThrough")?this.active():this.unActive()},t}(a["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(24)),l=r.__importDefault(n(3)),u=r.__importDefault(n(330)),s=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="字体">\n                <i class="w-e-icon-font"></i>\n            </div>'),i=new u["default"](t.config.fontNames),r={width:100,title:"设置字体",type:"list",list:i.getItemList(),clickHandler:function(e){n.command(e)}};return n=e.call(this,o,t,r)||this,n}return r.__extends(t,e),t.prototype.command=function(e){var t,n=this.editor,o=n.selection.isSelectionEmpty(),i=null===(t=n.selection.getSelectionContainerElem())||void 0===t?void 0:t.elems[0];if(null!=i){var r="p"!==(null===i||void 0===i?void 0:i.nodeName.toLowerCase()),a=(null===i||void 0===i?void 0:i.getAttribute("face"))===e;if(o){if(r&&!a){var l=n.selection.getSelectionRangeTopNodes();n.selection.createRangeByElem(l[0]),n.selection.moveCursor(l[0].elems[0])}n.selection.setRangeToElem(i),n.selection.createEmptyRange()}n.cmd["do"]("fontName",e),o&&(n.selection.collapseRange(),n.selection.restoreSelection())}},t.prototype.tryChangeActive=function(){},t}(a["default"]);t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=function(){function e(e){var t=this;this.itemList=[],(0,r["default"])(e).call(e,(function(e){var n="string"===typeof e?e:e.value,o="string"===typeof e?e:e.name;t.itemList.push({$elem:l["default"]("<p style=\"font-family:'"+n+"'\">"+o+"</p>"),value:o})}))}return e.prototype.getItemList=function(){return this.itemList},e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(24)),l=r.__importDefault(n(3)),u=r.__importDefault(n(332)),s=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="字号">\n                <i class="w-e-icon-text-heigh"></i>\n            </div>'),i=new u["default"](t.config.fontSizes),r={width:160,title:"设置字号",type:"list",list:i.getItemList(),clickHandler:function(e){n.command(e)}};return n=e.call(this,o,t,r)||this,n}return r.__extends(t,e),t.prototype.command=function(e){var t,n=this.editor,o=n.selection.isSelectionEmpty(),i=null===(t=n.selection.getSelectionContainerElem())||void 0===t?void 0:t.elems[0];null!=i&&(n.cmd["do"]("fontSize",e),o&&(n.selection.collapseRange(),n.selection.restoreSelection()))},t.prototype.tryChangeActive=function(){},t}(a["default"]);t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=function(){function e(e){for(var t in this.itemList=[],e){var n=e[t];this.itemList.push({$elem:a["default"]('<p style="font-size:'+t+'">'+n.name+"</p>"),value:n.value})}}return e.prototype.getItemList=function(){return this.itemList},e}();t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(27));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(24)),s=l.__importDefault(n(3)),c=["LI"],f=["BLOCKQUOTE"],d=function(e){function t(t){var n=this,o=s["default"]('<div class="w-e-menu" data-title="对齐"><i class="w-e-icon-paragraph-left"></i></div>'),i={width:100,title:"对齐方式",type:"list",list:[{$elem:s["default"]('<p>\n                            <i class="w-e-icon-paragraph-left w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.justify.靠左")+"\n                        </p>"),value:"left"},{$elem:s["default"]('<p>\n                            <i class="w-e-icon-paragraph-center w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.justify.居中")+"\n                        </p>"),value:"center"},{$elem:s["default"]('<p>\n                            <i class="w-e-icon-paragraph-right w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.justify.靠右")+"\n                        </p>"),value:"right"},{$elem:s["default"]('<p>\n                            <i class="w-e-icon-paragraph-justify w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.justify.两端")+"\n                        </p>"),value:"justify"}],clickHandler:function(e){n.command(e)}};return n=e.call(this,o,t,i)||this,n}return l.__extends(t,e),t.prototype.command=function(e){var t=this.editor,n=t.selection,o=n.getSelectionContainerElem();n.saveRange();var i=t.selection.getSelectionRangeTopNodes();if(null===o||void 0===o?void 0:o.length)if(this.isSpecialNode(o,i[0])||this.isSpecialTopNode(i[0])){var a=this.getSpecialNodeUntilTop(o,i[0]);if(null==a)return;s["default"](a).css("text-align",e)}else(0,r["default"])(i).call(i,(function(t){t.css("text-align",e)}));n.restoreSelection()},t.prototype.getSpecialNodeUntilTop=function(e,t){var n=e.elems[0],o=t.elems[0];while(null!=n){if(-1!==(0,a["default"])(c).call(c,null===n||void 0===n?void 0:n.nodeName))return n;if(n.parentNode===o)return n;n=n.parentNode}return n},t.prototype.isSpecialNode=function(e,t){var n=this.getSpecialNodeUntilTop(e,t);return null!=n&&-1!==(0,a["default"])(c).call(c,n.nodeName)},t.prototype.isSpecialTopNode=function(e){var t;return null!=e&&-1!==(0,a["default"])(f).call(f,null===(t=e.elems[0])||void 0===t?void 0:t.nodeName)},t.prototype.tryChangeActive=function(){},t}(u["default"]);t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(23)),s=a.__importDefault(n(335)),c=a.__importDefault(n(336)),f=n(7),d=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="引用">\n                <i class="w-e-icon-quotes-left"></i>\n            </div>');return n=e.call(this,o,t)||this,s["default"](t),n}return a.__extends(t,e),t.prototype.clickHandler=function(){var e,t,n=this.editor,o=n.selection.isSelectionEmpty(),i=n.selection.getSelectionRangeTopNodes(),a=i[i.length-1],u=this.getTopNodeName();if("BLOCKQUOTE"!==u){var s=c["default"](i);if(n.$textElem.equal(a)){var d=null===(e=n.selection.getSelectionContainerElem())||void 0===e?void 0:e.elems[0];n.selection.createRangeByElems(d.children[0],d.children[0]),i=n.selection.getSelectionRangeTopNodes(),s=c["default"](i),a.append(s)}else s.insertAfter(a);this.delSelectNode(i);var p=null===(t=s.childNodes())||void 0===t?void 0:t.last().getNode();if(null==p)return;return p.textContent?n.selection.moveCursor(p):n.selection.moveCursor(p,0),this.tryChangeActive(),void l["default"](f.EMPTY_P).insertAfter(s)}var v=l["default"](a.childNodes()),h=v.length,A=a;(0,r["default"])(v).call(v,(function(e){var t=l["default"](e);t.insertAfter(A),A=t})),a.remove(),n.selection.moveCursor(v.elems[h-1]),this.tryChangeActive(),o&&(n.selection.collapseRange(),n.selection.restoreSelection())},t.prototype.tryChangeActive=function(){var e,t=this.editor,n=null===(e=t.selection.getSelectionRangeTopNodes()[0])||void 0===e?void 0:e.getNodeName();"BLOCKQUOTE"===n?this.active():this.unActive()},t.prototype.getTopNodeName=function(){var e=this.editor,t=e.selection.getSelectionRangeTopNodes()[0],n=null===t||void 0===t?void 0:t.getNodeName();return n},t.prototype.delSelectNode=function(e){(0,r["default"])(e).call(e,(function(e){e.remove()}))},t}(u["default"]);t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=n(7),l=r.__importDefault(n(3));function u(e){function t(t){var n,o=e.selection.getSelectionContainerElem(),i=e.selection.getSelectionRangeTopNodes()[0];if("BLOCKQUOTE"===(null===i||void 0===i?void 0:i.getNodeName())){if("BLOCKQUOTE"===o.getNodeName()){var r=null===(n=o.childNodes())||void 0===n?void 0:n.getNode();e.selection.moveCursor(r)}if(""===o.text()){t.preventDefault(),o.remove();var u=l["default"](a.EMPTY_P);u.insertAfter(i),e.selection.moveCursor(u.getNode(),0)}""===i.text()&&i.remove()}}e.txt.eventHooks.enterDownEvents.push(t)}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3));function u(e){var t=l["default"]("<blockquote></blockquote>");return(0,r["default"])(e).call(e,(function(e){t.append(e.clone(!0))})),t}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(24)),u=a.__importDefault(n(3)),s=n(6),c=function(e){function t(t){var n,o=this,i=u["default"]('<div class="w-e-menu" data-title="背景色">\n                <i class="w-e-icon-paint-brush"></i>\n            </div>'),a={width:120,title:"背景颜色",type:"inline-block",list:(0,r["default"])(n=t.config.colors).call(n,(function(e){return{$elem:u["default"]('<i style="color:'+e+';" class="w-e-icon-paint-brush"></i>'),value:e}})),clickHandler:function(e){o.command(e)}};return o=e.call(this,i,t,a)||this,o}return a.__extends(t,e),t.prototype.command=function(e){var t,n=this.editor,o=n.selection.isSelectionEmpty(),i=null===(t=n.selection.getSelectionContainerElem())||void 0===t?void 0:t.elems[0];if(null!=i){var r="p"!==(null===i||void 0===i?void 0:i.nodeName.toLowerCase()),a=null===i||void 0===i?void 0:i.style.backgroundColor,l=s.hexToRgb(e)===a;if(o){if(r&&!l){var u=n.selection.getSelectionRangeTopNodes();n.selection.createRangeByElem(u[0]),n.selection.moveCursor(u[0].elems[0])}n.selection.createEmptyRange()}n.cmd["do"]("backColor",e),o&&(n.selection.collapseRange(),n.selection.restoreSelection())}},t.prototype.tryChangeActive=function(){},t}(l["default"]);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(24)),u=a.__importDefault(n(3)),s=function(e){function t(t){var n,o=this,i=u["default"]('<div class="w-e-menu" data-title="文字颜色">\n                <i class="w-e-icon-pencil2"></i>\n            </div>'),a={width:120,title:"文字颜色",type:"inline-block",list:(0,r["default"])(n=t.config.colors).call(n,(function(e){return{$elem:u["default"]('<i style="color:'+e+';" class="w-e-icon-pencil2"></i>'),value:e}})),clickHandler:function(e){o.command(e)}};return o=e.call(this,i,t,a)||this,o}return a.__extends(t,e),t.prototype.command=function(e){var t,n=this.editor,o=n.selection.isSelectionEmpty(),i=null===(t=n.selection.getSelectionContainerElem())||void 0===t?void 0:t.elems[0];if(null!=i){var r=n.selection.getSelectionText();if("A"===i.nodeName&&i.textContent===r){var a=u["default"]("<span>&#8203;</span>").getNode();i.appendChild(a)}n.cmd["do"]("foreColor",e),o&&(n.selection.collapseRange(),n.selection.restoreSelection())}},t.prototype.tryChangeActive=function(){},t}(l["default"]);t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(33)),u=r.__importDefault(n(38)),s=r.__importDefault(n(340)),c=r.__importDefault(n(346)),f=function(e){function t(t){var n=this,o=a["default"]('<div class="w-e-menu" data-title="视频">\n                <i class="w-e-icon-play"></i>\n            </div>');return n=e.call(this,o,t)||this,c["default"](t),n}return r.__extends(t,e),t.prototype.clickHandler=function(){this.createPanel("")},t.prototype.createPanel=function(e){var t=s["default"](this.editor,e),n=new l["default"](this,t);n.create()},t.prototype.tryChangeActive=function(){},t}(u["default"]);t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(6),u=a.__importDefault(n(3)),s=a.__importDefault(n(341)),c=n(7);function f(e,t){var n=e.config,o=new s["default"](e),i=l.getRandom("input-iframe"),a=l.getRandom("btn-ok"),f=l.getRandom("input-upload"),d=l.getRandom("btn-local-ok");function p(t){e.cmd["do"]("insertHTML",t+c.EMPTY_P),e.config.onlineVideoCallback(t)}function v(t){var n=e.config.onlineVideoCheck(t);return!0===n||("string"===typeof n&&e.config.customAlert(n,"error"),!1)}var h=[{title:e.i18next.t("menus.panelMenus.video.上传视频"),tpl:'<div class="w-e-up-video-container">\n                    <div id="'+d+'" class="w-e-up-btn">\n                        <i class="w-e-icon-upload2"></i>\n                    </div>\n                    <div style="display:none;">\n                        <input id="'+f+'" type="file" accept="video/*"/>\n                    </div>\n                 </div>',events:[{selector:"#"+d,type:"click",fn:function(){var e=u["default"]("#"+f),t=e.elems[0];if(!t)return!0;t.click()}},{selector:"#"+f,type:"change",fn:function(){var e=u["default"]("#"+f),t=e.elems[0];if(!t)return!0;var n=t.files;return n.length&&o.uploadVideo(n),!0}}]},{title:e.i18next.t("menus.panelMenus.video.插入视频"),tpl:'<div>\n                    <input \n                        id="'+i+'" \n                        type="text" \n                        class="block" \n                        placeholder="'+e.i18next.t("如")+'：<iframe src=... ></iframe>"/>\n                    </td>\n                    <div class="w-e-button-container">\n                        <button type="button" id="'+a+'" class="right">\n                            '+e.i18next.t("插入")+"\n                        </button>\n                    </div>\n                </div>",events:[{selector:"#"+a,type:"click",fn:function(){var e,t=u["default"]("#"+i),n=(0,r["default"])(e=t.val()).call(e);if(n&&v(n))return p(n),!0},bindEnter:!0}]}],A={width:300,height:0,tabs:[]};return window.FileReader&&(n.uploadVideoServer||n.customUploadVideo)&&A.tabs.push(h[0]),n.showLinkVideo&&A.tabs.push(h[1]),A}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(133)),a=o(n(57)),l=o(n(4)),u=o(n(27));(0,i["default"])(t,"__esModule",{value:!0});var s=n(2),c=n(6),f=s.__importDefault(n(135)),d=s.__importDefault(n(136)),p=n(7),v=n(6),h=function(){function e(e){this.editor=e}return e.prototype.uploadVideo=function(e){var t=this;if(e.length){var n=this.editor,o=n.config,i="validate.",s=function(e){return n.i18next.t(i+e)},p=o.uploadVideoServer,v=o.uploadVideoMaxSize,h=v/1024,A=o.uploadVideoName,m=o.uploadVideoParams,g=o.uploadVideoParamsWithUrl,y=o.uploadVideoHeaders,w=o.uploadVideoHooks,x=o.uploadVideoTimeout,_=o.withVideoCredentials,E=o.customUploadVideo,b=o.uploadVideoAccept,C=[],S=[];if(c.arrForEach(e,(function(e){var t=e.name,n=e.size/1024/1024;t&&n&&(b instanceof Array?(0,r["default"])(b).call(b,(function(e){return e===t.split(".")[t.split(".").length-1]}))?h<n?S.push("【"+t+"】"+s("大于")+" "+h+"M"):C.push(e):S.push("【"+t+"】"+s("不是视频")):S.push("【"+b+"】"+s("uploadVideoAccept 不是Array")))})),S.length)o.customAlert(s("视频验证未通过")+": \n"+S.join("\n"),"warning");else if(0!==C.length)if(E&&"function"===typeof E){var M;E(C,(0,a["default"])(M=this.insertVideo).call(M,this))}else{var k=new FormData;if((0,l["default"])(C).call(C,(function(e,t){var n=A||e.name;C.length>1&&(n+=t+1),k.append(n,e)})),p){var D=p.split("#");p=D[0];var N=D[1]||"";(0,l["default"])(c).call(c,m,(function(e,t){g&&((0,u["default"])(p).call(p,"?")>0?p+="&":p+="?",p=p+e+"="+t),k.append(e,t)})),N&&(p+="#"+N);var T=f["default"](p,{timeout:x,formData:k,headers:y,withCredentials:!!_,beforeSend:function(e){if(w.before)return w.before(e,n,C)},onTimeout:function(e){o.customAlert(s("上传视频超时"),"error"),w.timeout&&w.timeout(e,n)},onProgress:function(e,t){var o=new d["default"](n);t.lengthComputable&&(e=t.loaded/t.total,o.show(e))},onError:function(e){o.customAlert(s("上传视频错误"),"error",s("上传视频错误")+"，"+s("服务器返回状态")+": "+e.status),w.error&&w.error(e,n)},onFail:function(e,t){o.customAlert(s("上传视频失败"),"error",s("上传视频返回结果错误")+"，"+s("返回结果")+": "+t),w.fail&&w.fail(e,n,t)},onSuccess:function(e,i){if(w.customInsert){var r;w.customInsert((0,a["default"])(r=t.insertVideo).call(r,t),i,n)}else{if("0"!=i.errno)return o.customAlert(s("上传视频失败"),"error",s("上传视频返回结果错误")+"，"+s("返回结果")+" errno="+i.errno),void(w.fail&&w.fail(e,n,i));var l=i.data;t.insertVideo(l.url),w.success&&w.success(e,n,i)}}});"string"===typeof T&&o.customAlert(T,"error")}}else o.customAlert(s("传入的文件不合法"),"warning")}},e.prototype.insertVideo=function(e){var t=this.editor,n=t.config,o="validate.",i=function(e,n){return void 0===n&&(n=o),t.i18next.t(n+e)};if(n.customInsertVideo)n.customInsertVideo(e);else{v.UA.isFirefox?t.cmd["do"]("insertHTML",'<p data-we-video-p="true"><video src="'+e+'" controls="controls" style="max-width:100%"></video></p><p>&#8203</p>'):t.cmd["do"]("insertHTML",'<video src="'+e+'" controls="controls" style="max-width:100%"></video>'+p.EMPTY_P);var r=document.createElement("video");r.onload=function(){r=null},r.onerror=function(){n.customAlert(i("插入视频错误"),"error","wangEditor: "+i("插入视频错误")+"，"+i("视频链接")+' "'+e+'"，'+i("下载链接失败")),r=null},r.onabort=function(){return r=null},r.src=e}},e}();t["default"]=h},function(e,t,n){e.exports=n(343)},function(e,t,n){var o=n(344);e.exports=o},function(e,t,n){n(345);var o=n(9);e.exports=o.Date.now},function(e,t,n){var o=n(5);o({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}})},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(347)),l=r.__importDefault(n(349));function u(e){a["default"](e),l["default"](e)}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.createShowHideFn=void 0;var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(39)),u=r.__importDefault(n(348));function s(e){var t,n=function(t,n){return void 0===n&&(n=""),e.i18next.t(n+t)};function o(o){var i=[{$elem:a["default"]("<span class='w-e-icon-trash-o'></span>"),onClick:function(e,t){return t.remove(),!0}},{$elem:a["default"]("<span>100%</span>"),onClick:function(e,t){return t.attr("width","100%"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>50%</span>"),onClick:function(e,t){return t.attr("width","50%"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>30%</span>"),onClick:function(e,t){return t.attr("width","30%"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>"+n("重置")+"</span>"),onClick:function(e,t){return t.removeAttr("width"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>"+n("menus.justify.靠左")+"</span>"),onClick:function(e,t){return u["default"](t,"left"),!0}},{$elem:a["default"]("<span>"+n("menus.justify.居中")+"</span>"),onClick:function(e,t){return u["default"](t,"center"),!0}},{$elem:a["default"]("<span>"+n("menus.justify.靠右")+"</span>"),onClick:function(e,t){return u["default"](t,"right"),!0}}];t=new l["default"](e,o,i),t.create()}function i(){t&&(t.remove(),t=null)}return{showVideoTooltip:o,hideVideoTooltip:i}}function c(e){var t=s(e),n=t.showVideoTooltip,o=t.hideVideoTooltip;e.txt.eventHooks.videoClickEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o),e.txt.eventHooks.changeEvents.push(o)}t.createShowHideFn=s,t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(28));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3));function u(e,t){var n=["P"],o=s(e,n);o&&l["default"](o).css("text-align",t)}function s(e,t){var n,o=e.elems[0];while(null!=o){if((0,r["default"])(t).call(t,null===o||void 0===o?void 0:o.nodeName))return o;if("BODY"===(null===(n=null===o||void 0===o?void 0:o.parentNode)||void 0===n?void 0:n.nodeName))return null;o=o.parentNode}return o}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(6);function a(e){if(r.UA.isFirefox){var t=e.txt,n=e.selection,o=t.eventHooks.keydownEvents;o.push((function(t){var o=n.getSelectionContainerElem();if(o){var i=o.getNodeTop(e),r=i.length&&i.prev().length?i.prev():null;r&&r.attr("data-we-video-p")&&0===n.getCursorPos()&&8===t.keyCode&&r.remove()}}))}}t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(7),u=a.__importDefault(n(3)),s=a.__importDefault(n(33)),c=a.__importDefault(n(38)),f=a.__importDefault(n(351)),d=a.__importDefault(n(364)),p=function(e){function t(t){var n,o=this,i=u["default"]('<div class="w-e-menu" data-title="图片"><i class="w-e-icon-image"></i></div>'),a=d["default"](t);a.onlyUploadConf&&(i=a.onlyUploadConf.$elem,(0,r["default"])(n=a.onlyUploadConf.events).call(n,(function(e){var t=e.type,n=e.fn||l.EMPTY_FN;i.on(t,(function(e){e.stopPropagation(),n(e)}))})));return o=e.call(this,i,t)||this,o.imgPanelConfig=a,f["default"](t),o}return a.__extends(t,e),t.prototype.clickHandler=function(){this.imgPanelConfig.onlyUploadConf||this.createPanel()},t.prototype.createPanel=function(){var e=this.imgPanelConfig,t=new s["default"](this,e);this.setPanel(t),t.create()},t.prototype.tryChangeActive=function(){},t}(c["default"]);t["default"]=p},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(352)),l=r.__importDefault(n(353)),u=r.__importDefault(n(354)),s=r.__importDefault(n(362)),c=r.__importDefault(n(363));function f(e){a["default"](e),l["default"](e),u["default"](e),s["default"](e),c["default"](e)}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=n(131),l=r.__importDefault(n(97));function u(e,t){var n=e.config,o=n.pasteFilterStyle,i=n.pasteIgnoreImg,r=a.getPasteHtml(t,o,i);if(r)return!0;var l=a.getPasteText(t);return!!l}function s(e,t){for(var n,o=(null===(n=t.clipboardData)||void 0===n?void 0:n.types)||[],i=0;i<o.length;i++){var r=o[i];if("Files"===r)return!0}return!1}function c(e,t){if(s(t,e)||!u(t,e)){var n=a.getPasteImgs(e);if(n.length){var o=new l["default"](t);o.uploadImg(n)}}}function f(e){e.txt.eventHooks.pasteEvents.unshift((function(t){c(t,e)}))}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(97));function l(e){function t(t){var n=t.dataTransfer&&t.dataTransfer.files;if(n&&n.length){var o=new a["default"](e);o.uploadImg(n)}}e.txt.eventHooks.dropEvents.push(t)}t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29)),a=o(n(355));(0,i["default"])(t,"__esModule",{value:!0}),t.createShowHideFn=void 0;var l=n(2),u=l.__importDefault(n(3));n(360);var s=n(6);function c(e,t,n,o,i){e.attr("style","width:"+t+"px; height:"+n+"px; left:"+o+"px; top:"+i+"px;")}function f(e,t){var n=u["default"]('<div class="w-e-img-drag-mask">\n            <div class="w-e-img-drag-show-size"></div>\n            <div class="w-e-img-drag-rb"></div>\n         </div>');return n.hide(),t.append(n),n}function d(e,t,n){var o=e.getBoundingClientRect(),i=n.getBoundingClientRect(),l=i.width.toFixed(2),u=i.height.toFixed(2);(0,r["default"])(t).call(t,".w-e-img-drag-show-size").text(l+"px * "+u+"px"),c(t,(0,a["default"])(l),(0,a["default"])(u),i.left-o.left,i.top-o.top),t.show()}function p(e){var t,n=e.$textContainerElem,o=f(e,n);function i(e,n){e.on("click",(function(e){e.stopPropagation()})),e.on("mousedown",".w-e-img-drag-rb",(function(o){if(o.preventDefault(),t){var i=o.clientX,l=o.clientY,s=n.getBoundingClientRect(),f=t.getBoundingClientRect(),d=f.width,p=f.height,v=f.left-s.left,h=f.top-s.top,A=d/p,m=d,g=p,y=u["default"](document);y.on("mousemove",x),y.on("mouseup",_),y.on("mouseleave",w)}function w(){y.off("mousemove",x),y.off("mouseup",_)}function x(t){t.stopPropagation(),t.preventDefault(),m=d+(t.clientX-i),g=p+(t.clientY-l),m/g!=A&&(g=m/A),m=(0,a["default"])(m.toFixed(2)),g=(0,a["default"])(g.toFixed(2)),(0,r["default"])(e).call(e,".w-e-img-drag-show-size").text(m.toFixed(2).replace(".00","")+"px * "+g.toFixed(2).replace(".00","")+"px"),c(e,m,g,v,h)}function _(){t.attr("width",m+""),t.attr("height",g+"");var n=t.getBoundingClientRect();c(e,m,g,n.left-s.left,n.top-s.top),w()}}))}function l(e){if(s.UA.isIE())return!1;e&&(t=e,d(n,o,t))}function p(){(0,r["default"])(n).call(n,".w-e-img-drag-mask").hide()}return i(o,n),u["default"](document).on("click",p),e.beforeDestroy((function(){u["default"](document).off("click",p)})),{showDrag:l,hideDrag:p}}function v(e){var t=p(e),n=t.showDrag,o=t.hideDrag;e.txt.eventHooks.imgClickEvents.push(n),e.txt.eventHooks.textScrollEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.changeEvents.push(o)}t.createShowHideFn=p,t["default"]=v},function(e,t,n){e.exports=n(356)},function(e,t,n){var o=n(357);e.exports=o},function(e,t,n){n(358);var o=n(9);e.exports=o.parseFloat},function(e,t,n){var o=n(5),i=n(359);o({global:!0,forced:parseFloat!=i},{parseFloat:i})},function(e,t,n){var o=n(8),i=n(90).trim,r=n(68),a=o.parseFloat,l=1/a(r+"-0")!==-1/0;e.exports=l?function(e){var t=i(String(e)),n=a(t);return 0===n&&"-"==t.charAt(0)?-0:n}:a},function(e,t,n){var o=n(20),i=n(361);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,".w-e-text-container {\n  overflow: hidden;\n}\n.w-e-img-drag-mask {\n  position: absolute;\n  z-index: 1;\n  border: 1px dashed #ccc;\n  box-sizing: border-box;\n}\n.w-e-img-drag-mask .w-e-img-drag-rb {\n  position: absolute;\n  right: -5px;\n  bottom: -5px;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #ccc;\n  cursor: se-resize;\n}\n.w-e-img-drag-mask .w-e-img-drag-show-size {\n  min-width: 110px;\n  height: 22px;\n  line-height: 22px;\n  font-size: 14px;\n  color: #999;\n  position: absolute;\n  left: 0;\n  top: 0;\n  background-color: #999;\n  color: #fff;\n  border-radius: 2px;\n  padding: 0 5px;\n}\n",""]),e.exports=t},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.createShowHideFn=void 0;var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(39));function u(e){var t,n=function(t,n){return void 0===n&&(n=""),e.i18next.t(n+t)};function o(o){var i=[{$elem:a["default"]("<span class='w-e-icon-trash-o'></span>"),onClick:function(e,t){return e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("delete"),!0}},{$elem:a["default"]("<span>30%</span>"),onClick:function(e,t){return t.attr("width","30%"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>50%</span>"),onClick:function(e,t){return t.attr("width","50%"),t.removeAttr("height"),!0}},{$elem:a["default"]("<span>100%</span>"),onClick:function(e,t){return t.attr("width","100%"),t.removeAttr("height"),!0}}];i.push({$elem:a["default"]("<span>"+n("重置")+"</span>"),onClick:function(e,t){return t.removeAttr("width"),t.removeAttr("height"),!0}}),o.attr("data-href")&&i.push({$elem:a["default"]("<span>"+n("查看链接")+"</span>"),onClick:function(e,t){var n=t.attr("data-href");return n&&(n=decodeURIComponent(n),window.open(n,"_target")),!0}}),t=new l["default"](e,o,i),t.create()}function i(){t&&(t.remove(),t=null)}return{showImgTooltip:o,hideImgTooltip:i}}function s(e){var t=u(e),n=t.showImgTooltip,o=t.hideImgTooltip;e.txt.eventHooks.imgClickEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o),e.txt.eventHooks.imgDragBarMouseDownEvents.push(o),e.txt.eventHooks.changeEvents.push(o)}t.createShowHideFn=u,t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(e){var t=e.txt,n=e.selection,o=t.eventHooks.keydownEvents;o.push((function(e){var t=n.getSelectionContainerElem(),o=n.getRange();if(o&&t&&8===e.keyCode&&n.isSelectionEmpty()){var i=o.startContainer,r=o.startOffset,a=null;if(0===r)while(i!==t.elems[0]&&t.elems[0].contains(i)&&i.parentNode&&!a){if(i.previousSibling){a=i.previousSibling;break}i=i.parentNode}else 3!==i.nodeType&&(a=i.childNodes[r-1]);if(a){var l=a;while(l.childNodes.length)l=l.childNodes[l.childNodes.length-1];l instanceof HTMLElement&&"IMG"===l.tagName&&(l.remove(),e.preventDefault())}}}))}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26)),a=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=n(6),c=l.__importDefault(n(97));function f(e){var t,n=e.config,o=new c["default"](e),i=s.getRandom("up-trigger-id"),l=s.getRandom("up-file-id"),f=s.getRandom("input-link-url"),d=s.getRandom("input-link-url-alt"),p=s.getRandom("input-link-url-href"),v=s.getRandom("btn-link"),h="menus.panelMenus.image.",A=function(t,n){return void 0===n&&(n=h),e.i18next.t(n+t)};function m(e,t,o){var i=n.linkImgCheck(e);return!0===i||("string"===typeof i&&n.customAlert(i,"error"),!1)}var g=1===n.uploadImgMaxLength?"":'multiple="multiple"',y=(0,r["default"])(t=n.uploadImgAccept).call(t,(function(e){return"image/"+e})).join(","),w=function(e,t,n){return'<div class="'+e+'" data-title="'+n+'">\n            <div id="'+i+'" class="w-e-up-btn">\n                <i class="'+t+'"></i>\n            </div>\n            <div style="display:none;">\n                <input id="'+l+'" type="file" '+g+' accept="'+y+'"/>\n            </div>\n        </div>'},x=[{selector:"#"+i,type:"click",fn:function(){var e=n.uploadImgFromMedia;if(e&&"function"===typeof e)return e(),!0;var t=u["default"]("#"+l),o=t.elems[0];if(!o)return!0;o.click()}},{selector:"#"+l,type:"change",fn:function(){var e=u["default"]("#"+l),t=e.elems[0];if(!t)return!0;var n=t.files;return(null===n||void 0===n?void 0:n.length)&&o.uploadImg(n),t&&(t.value=""),!0}}],_=['<input\n            id="'+f+'"\n            type="text"\n            class="block"\n            placeholder="'+A("图片地址")+'"/>'];n.showLinkImgAlt&&_.push('\n        <input\n            id="'+d+'"\n            type="text"\n            class="block"\n            placeholder="'+A("图片文字说明")+'"/>'),n.showLinkImgHref&&_.push('\n        <input\n            id="'+p+'"\n            type="text"\n            class="block"\n            placeholder="'+A("跳转链接")+'"/>');var E=[{title:A("上传图片"),tpl:w("w-e-up-img-container","w-e-icon-upload2",""),events:x},{title:A("网络图片"),tpl:"<div>\n                    "+_.join("")+'\n                    <div class="w-e-button-container">\n                        <button type="button" id="'+v+'" class="right">'+A("插入","")+"</button>\n                    </div>\n                </div>",events:[{selector:"#"+v,type:"click",fn:function(){var e,t=u["default"]("#"+f),i=(0,a["default"])(e=t.val()).call(e);if(i){var r,l,s,c;if(n.showLinkImgAlt)r=(0,a["default"])(l=u["default"]("#"+d).val()).call(l);if(n.showLinkImgHref)s=(0,a["default"])(c=u["default"]("#"+p).val()).call(c);if(m(i,r,s))return o.insertImg(i,r,s),!0}},bindEnter:!0}]}],b={width:300,height:0,tabs:[],onlyUploadConf:{$elem:u["default"](w("w-e-menu","w-e-icon-image","图片")),events:x}};return window.FileReader&&(n.uploadImgShowBase64||n.uploadImgServer||n.customUploadImg||n.uploadImgFromMedia)&&b.tabs.push(E[0]),n.showLinkImg&&(b.tabs.push(E[1]),b.onlyUploadConf=void 0),b}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(24)),s=a.__importDefault(n(366)),c=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="缩进">\n                <i class="w-e-icon-indent-increase"></i>\n            </div>'),i={width:130,title:"设置缩进",type:"list",list:[{$elem:l["default"]('<p>\n                            <i class="w-e-icon-indent-increase w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.indent.增加缩进")+"\n                        <p>"),value:"increase"},{$elem:l["default"]('<p>\n                            <i class="w-e-icon-indent-decrease w-e-drop-list-item"></i>\n                            '+t.i18next.t("menus.dropListMenu.indent.减少缩进")+"\n                        <p>"),value:"decrease"}],clickHandler:function(e){n.command(e)}};return n=e.call(this,o,t,i)||this,n}return a.__extends(t,e),t.prototype.command=function(e){var t=this.editor,n=t.selection.getSelectionContainerElem();if(n&&t.$textElem.equal(n)){var o=t.selection.getSelectionRangeTopNodes();o.length>0&&(0,r["default"])(o).call(o,(function(n){s["default"](l["default"](n),e,t)}))}else n&&n.length>0&&(0,r["default"])(n).call(n,(function(n){s["default"](l["default"](n),e,t)}));t.selection.restoreSelection(),this.tryChangeActive()},t.prototype.tryChangeActive=function(){var e=this.editor,t=e.selection.getSelectionStartElem(),n=l["default"](t).getNodeTop(e);n.length<=0||(""!=n.elems[0].style["paddingLeft"]?this.active():this.unActive())},t}(u["default"]);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(45)),a=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(367)),s=l.__importDefault(n(368)),c=/^(\d+)(\w+)$/,f=/^(\d+)%$/;function d(e){var t=e.config.indentation;if("string"===typeof t){if(c.test(t)){var n,o=(0,r["default"])(n=(0,a["default"])(t).call(t).match(c)).call(n,1,3),i=o[0],l=o[1];return{value:Number(i),unit:l}}if(f.test(t))return{value:Number((0,a["default"])(t).call(t).match(f)[1]),unit:"%"}}else if(void 0!==t.value&&t.unit)return t;return{value:2,unit:"em"}}function p(e,t,n){var o=e.getNodeTop(n),i=/^(P|H[0-9]*)$/;i.test(o.getNodeName())&&("increase"===t?u["default"](o,d(n)):"decrease"===t&&s["default"](o,d(n)))}t["default"]=p},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(45));function a(e,t){var n=e.elems[0];if(""===n.style["paddingLeft"])e.css("padding-left",t.value+t.unit);else{var o=n.style["paddingLeft"],i=(0,r["default"])(o).call(o,0,o.length-t.unit.length),a=Number(i)+t.value;e.css("padding-left",""+a+t.unit)}}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(45));function a(e,t){var n=e.elems[0];if(""!==n.style["paddingLeft"]){var o=n.style["paddingLeft"],i=(0,r["default"])(o).call(o,0,o.length-t.unit.length),a=Number(i)-t.value;a>0?e.css("padding-left",""+a+t.unit):e.css("padding-left","")}}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(38)),u=r.__importDefault(n(33)),s=r.__importDefault(n(370)),c=function(e){function t(t){var n=this,o=a["default"]('<div class="w-e-menu" data-title="表情">\n                <i class="w-e-icon-happy"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.createPanel=function(){var e=s["default"](this.editor),t=new u["default"](this,e);t.create()},t.prototype.clickHandler=function(){this.createPanel()},t.prototype.tryChangeActive=function(){},t}(l["default"]);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26)),a=o(n(70)),l=o(n(17));(0,i["default"])(t,"__esModule",{value:!0});var u=n(2),s=u.__importDefault(n(3));function c(e){var t=e.config.emotions;function n(e){var t,n,o=[];"image"==e.type?(o=(0,r["default"])(t=e.content).call(t,(function(e){return"string"==typeof e?"":'<span  title="'+e.alt+'">\n                    <img class="eleImg" data-emoji="'+e.alt+'" style src="'+e.src+'" alt="['+e.alt+']">\n                </span>'})),o=(0,a["default"])(o).call(o,(function(e){return""!==e}))):o=(0,r["default"])(n=e.content).call(n,(function(e){return'<span class="eleImg" title="'+e+'">'+e+"</span>"}));return o.join("").replace(/&nbsp;/g,"")}var o=(0,r["default"])(t).call(t,(function(t){return{title:e.i18next.t("menus.panelMenus.emoticon."+t.title),tpl:"<div>"+n(t)+"</div>",events:[{selector:".eleImg",type:"click",fn:function(t){var n,o,i=s["default"](t.target),r=i.getNodeName();"IMG"===r?n=(0,l["default"])(o=i.parent().html()).call(o):n="<span>"+i.html()+"</span>";return e.cmd["do"]("insertHTML",n),!0}}]}})),i={width:300,height:230,tabs:o};return i}t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.createListHandle=t.ClassType=void 0;var r,a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(372)),s=a.__importDefault(n(374)),c=a.__importDefault(n(375)),f=a.__importDefault(n(376)),d=a.__importDefault(n(377));(function(e){e["Wrap"]="WrapListHandle",e["Join"]="JoinListHandle",e["StartJoin"]="StartJoinListHandle",e["EndJoin"]="EndJoinListHandle",e["Other"]="OtherListHandle"})(r=t.ClassType||(t.ClassType={}));var p={WrapListHandle:u["default"],JoinListHandle:s["default"],StartJoinListHandle:c["default"],EndJoinListHandle:f["default"],OtherListHandle:d["default"]};function v(e,t,n){if(e===r.Other&&void 0===n)throw new Error("other 类需要传入 range");return e!==r.Other?new p[e](t):new p[e](t,n)}t.createListHandle=v;var h=function(){function e(e){this.handle=e,this.handle.exec()}return e.prototype.getSelectionRangeElem=function(){return l["default"](this.handle.selectionRangeElem.get())},e}();t["default"]=h},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(58),s=n(47),c=function(e){function t(t){return e.call(this,t)||this}return a.__extends(t,e),t.prototype.exec=function(){var e,t=this.options,n=t.listType,o=t.listTarget,i=t.$selectionElem,a=t.$startElem,u=t.$endElem,c=[],f=null===i||void 0===i?void 0:i.getNodeName(),d=a.prior,p=u.prior;if(!a.prior&&!u.prior||!(null===d||void 0===d?void 0:d.prev().length)&&!(null===p||void 0===p?void 0:p.next().length)){var v;(0,r["default"])(v=null===i||void 0===i?void 0:i.children()).call(v,(function(e){c.push(l["default"](e))})),f===n?e=s.createElementFragment(c,s.createDocumentFragment(),"p"):(e=s.createElement(o),(0,r["default"])(c).call(c,(function(t){e.appendChild(t.elems[0])}))),this.selectionRangeElem.set(e),s.insertBefore(i,e,i.elems[0]),i.remove()}else{var h=d;while(h.length)c.push(h),h=(null===p||void 0===p?void 0:p.equal(h))?l["default"](void 0):h.next();var A=d.prev(),m=p.next();if(f===n?e=s.createElementFragment(c,s.createDocumentFragment(),"p"):(e=s.createElement(o),(0,r["default"])(c).call(c,(function(t){e.append(t.elems[0])}))),A.length&&m.length){var g=[];while(m.length)g.push(m),m=m.next();var y=s.createElement(f);(0,r["default"])(g).call(g,(function(e){y.append(e.elems[0])})),l["default"](y).insertAfter(i),this.selectionRangeElem.set(e);var w=i.next();w.length?s.insertBefore(i,e,w.elems[0]):i.parent().elems[0].append(e)}else if(A.length){this.selectionRangeElem.set(e);w=i.next();w.length?s.insertBefore(i,e,w.elems[0]):i.parent().elems[0].append(e)}else this.selectionRangeElem.set(e),s.insertBefore(i,e,i.elems[0])}},t}(u.ListHandle);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=function(){function e(){this._element=null}return e.prototype.set=function(e){if(e instanceof DocumentFragment){var t,n=[];(0,r["default"])(t=e.childNodes).call(t,(function(e){n.push(e)})),e=n}this._element=e},e.prototype.get=function(){return this._element},e.prototype.clear=function(){this._element=null},e}();t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(58),s=n(47),c=function(e){function t(t){return e.call(this,t)||this}return a.__extends(t,e),t.prototype.exec=function(){var e,t,n,o,i,a,u,c,f=this.options,d=f.editor,p=f.listType,v=f.listTarget,h=f.$startElem,A=f.$endElem,m=d.selection.getSelectionRangeTopNodes(),g=null===h||void 0===h?void 0:h.getNodeName(),y=null===A||void 0===A?void 0:A.getNodeName();if(g===y)if(m.length>2)if(m.shift(),m.pop(),c=s.createElementFragment(s.filterSelectionNodes(m),s.createDocumentFragment()),g===p)null===(e=A.children())||void 0===e||(0,r["default"])(e).call(e,(function(e){c.append(e)})),A.remove(),this.selectionRangeElem.set(c),h.elems[0].append(c);else{var w=document.createDocumentFragment(),x=document.createDocumentFragment(),_=s.getStartPoint(h);while(_.length){var E=_.elems[0];_=_.next(),w.append(E)}var b=s.getEndPoint(A),C=[];while(b.length)C.unshift(b.elems[0]),b=b.prev();(0,r["default"])(C).call(C,(function(e){x.append(e)}));var S=s.createElement(v);S.append(w),S.append(c),S.append(x),c=S,this.selectionRangeElem.set(c),l["default"](S).insertAfter(h),!(null===(t=h.children())||void 0===t?void 0:t.length)&&h.remove(),!(null===(n=A.children())||void 0===n?void 0:n.length)&&A.remove()}else{m.length=0;_=s.getStartPoint(h);while(_.length)m.push(_),_=_.next();b=s.getEndPoint(A),C=[];while(b.length)C.unshift(b),b=b.prev();m.push.apply(m,C),g===p?(c=s.createElementFragment(m,s.createDocumentFragment(),"p"),this.selectionRangeElem.set(c),s.insertBefore(h,c,A.elems[0])):(c=s.createElement(v),(0,r["default"])(m).call(m,(function(e){c.append(e.elems[0])})),this.selectionRangeElem.set(c),l["default"](c).insertAfter(h)),!(null===(o=h.children())||void 0===o?void 0:o.length)&&A.remove(),!(null===(i=A.children())||void 0===i?void 0:i.length)&&A.remove()}else{var M=[];b=s.getEndPoint(A);while(b.length)M.unshift(b),b=b.prev();var k=[];_=s.getStartPoint(h);while(_.length)k.push(_),_=_.next();if(c=s.createDocumentFragment(),m.shift(),m.pop(),(0,r["default"])(k).call(k,(function(e){return c.append(e.elems[0])})),c=s.createElementFragment(s.filterSelectionNodes(m),c),(0,r["default"])(M).call(M,(function(e){return c.append(e.elems[0])})),this.selectionRangeElem.set(c),g===p)h.elems[0].append(c),!(null===(a=A.children())||void 0===a?void 0:a.length)&&A.remove();else if(null===(u=A.children())||void 0===u?void 0:u.length){var D=A.children();s.insertBefore(D,c,D.elems[0])}else A.elems[0].append(c)}},t}(u.ListHandle);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(58),s=n(47),c=function(e){function t(t){return e.call(this,t)||this}return a.__extends(t,e),t.prototype.exec=function(){var e,t,n=this.options,o=n.editor,i=n.listType,a=n.listTarget,u=n.$startElem,c=o.selection.getSelectionRangeTopNodes(),f=null===u||void 0===u?void 0:u.getNodeName();c.shift();var d=[],p=s.getStartPoint(u);while(p.length)d.push(p),p=p.next();f===i?(t=s.createDocumentFragment(),(0,r["default"])(d).call(d,(function(e){return t.append(e.elems[0])})),t=s.createElementFragment(s.filterSelectionNodes(c),t),this.selectionRangeElem.set(t),u.elems[0].append(t)):(t=s.createElement(a),(0,r["default"])(d).call(d,(function(e){return t.append(e.elems[0])})),t=s.createElementFragment(s.filterSelectionNodes(c),t),this.selectionRangeElem.set(t),l["default"](t).insertAfter(u),!(null===(e=u.children())||void 0===e?void 0:e.length)&&u.remove())},t}(u.ListHandle);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(58),s=n(47),c=function(e){function t(t){return e.call(this,t)||this}return a.__extends(t,e),t.prototype.exec=function(){var e,t,n,o=this.options,i=o.editor,a=o.listType,u=o.listTarget,c=o.$endElem,f=i.selection.getSelectionRangeTopNodes(),d=null===c||void 0===c?void 0:c.getNodeName();f.pop();var p=[],v=s.getEndPoint(c);while(v.length)p.unshift(v),v=v.prev();if(d===a)if(n=s.createElementFragment(s.filterSelectionNodes(f),s.createDocumentFragment()),(0,r["default"])(p).call(p,(function(e){return n.append(e.elems[0])})),this.selectionRangeElem.set(n),null===(e=c.children())||void 0===e?void 0:e.length){var h=c.children();s.insertBefore(h,n,h.elems[0])}else c.elems[0].append(n);else{var A=s.filterSelectionNodes(f);A.push.apply(A,p),n=s.createElementFragment(A,s.createElement(u)),this.selectionRangeElem.set(n),l["default"](n).insertBefore(c),!(null===(t=c.children())||void 0===t?void 0:t.length)&&c.remove()}},t}(u.ListHandle);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=n(58),l=n(47),u=function(e){function t(t,n){var o=e.call(this,t)||this;return o.range=n,o}return r.__extends(t,e),t.prototype.exec=function(){var e=this.options,t=e.editor,n=e.listTarget,o=t.selection.getSelectionRangeTopNodes(),i=l.createElementFragment(l.filterSelectionNodes(o),l.createElement(n));this.selectionRangeElem.set(i),this.range.insertNode(i)},t}(a.ListHandle);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(27));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(24)),s=l.__importDefault(n(3)),c=l.__importDefault(n(379)),f=function(e){function t(t){var n=this,o=s["default"]('<div class="w-e-menu" data-title="行高">\n                    <i class="w-e-icon-row-height"></i>\n                </div>'),i=new c["default"](t,t.config.lineHeights),r={width:100,title:"设置行高",type:"list",list:i.getItemList(),clickHandler:function(e){t.selection.saveRange(),n.command(e)}};return n=e.call(this,o,t,r)||this,n}return l.__extends(t,e),t.prototype.command=function(e){var t=this.editor;t.selection.restoreSelection();var n=s["default"](t.selection.getSelectionContainerElem());if(n.elems.length)if(n&&t.$textElem.equal(n)){for(var o=!1,i=s["default"](t.selection.getSelectionStartElem()).elems[0],r=s["default"](t.selection.getSelectionEndElem()).elems[0],a=this.getDom(i),l=this.getDom(r),u=n.elems[0].children,c=0;c<u.length;c++){var f=u[c];if("P"===s["default"](f).getNodeName()&&(f===a&&(o=!0),o&&(s["default"](f).css("line-height",e),f===l)))return void(o=!1)}t.selection.createRangeByElems(i,r)}else{var d=n.elems[0],p=this.getDom(d);"P"===s["default"](p).getNodeName()&&(s["default"](p).css("line-height",e),t.selection.createRangeByElems(p,p))}},t.prototype.getDom=function(e){var t=s["default"](e).elems[0];if(!t.parentNode)return t;function n(e,t){var o=s["default"](e.parentNode);return t.$textElem.equal(o)?e:n(o.elems[0],t)}return t=n(t,this.editor),t},t.prototype.styleProcessing=function(e){var t="";return(0,r["default"])(e).call(e,(function(e){""!==e&&-1===(0,a["default"])(e).call(e,"line-height")&&(t=t+e+";")})),t},t.prototype.setRange=function(e,t){var n=this.editor,o=window.getSelection?window.getSelection():document.getSelection();null===o||void 0===o||o.removeAllRanges();var i=document.createRange(),r=e,a=t;i.setStart(r,0),i.setEnd(a,1),null===o||void 0===o||o.addRange(i),n.selection.saveRange(),null===o||void 0===o||o.removeAllRanges(),n.selection.restoreSelection()},t.prototype.tryChangeActive=function(){var e=this.editor,t=e.selection.getSelectionContainerElem();if(!t||!e.$textElem.equal(t)){var n=s["default"](e.selection.getSelectionStartElem());if(0!==n.length){n=this.getDom(n.elems[0]);var o=n.getAttribute("style")?n.getAttribute("style"):"";o&&-1!==(0,a["default"])(o).call(o,"line-height")?this.active():this.unActive()}}},t}(u["default"]);t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=function(){function e(e,t){var n=this;this.itemList=[{$elem:l["default"]("<span>"+e.i18next.t("默认")+"</span>"),value:""}],(0,r["default"])(t).call(t,(function(e){n.itemList.push({$elem:l["default"]("<span>"+e+"</span>"),value:e})}))}return e.prototype.getItemList=function(){return this.itemList},e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(23)),u=function(e){function t(t){var n=this,o=a["default"]('<div class="w-e-menu" data-title="撤销">\n                <i class="w-e-icon-undo"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor;e.history.revoke();var t=e.$textElem.children();if(null===t||void 0===t?void 0:t.length){var n=t.last();e.selection.createRangeByElem(n,!1,!0),e.selection.restoreSelection()}},t.prototype.tryChangeActive=function(){this.editor.isCompatibleMode||(this.editor.history.size[0]?this.active():this.unActive())},t}(l["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(23)),u=function(e){function t(t){var n=this,o=a["default"]('<div class="w-e-menu" data-title="恢复">\n                <i class="w-e-icon-redo"></i>\n            </div>');return n=e.call(this,o,t)||this,n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor;e.history.restore();var t=e.$textElem.children();if(null===t||void 0===t?void 0:t.length){var n=t.last();e.selection.createRangeByElem(n,!1,!0),e.selection.restoreSelection()}},t.prototype.tryChangeActive=function(){this.editor.isCompatibleMode||(this.editor.history.size[1]?this.active():this.unActive())},t}(l["default"]);t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(38)),l=r.__importDefault(n(3)),u=r.__importDefault(n(383)),s=r.__importDefault(n(33)),c=r.__importDefault(n(392)),f=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="表格"><i class="w-e-icon-table2"></i></div>');return n=e.call(this,o,t)||this,c["default"](t),n}return r.__extends(t,e),t.prototype.clickHandler=function(){this.createPanel()},t.prototype.createPanel=function(){var e=u["default"](this.editor),t=new s["default"](this,e);t.create()},t.prototype.tryChangeActive=function(){},t}(a["default"]);t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(384));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(6),u=a.__importDefault(n(3));n(389);var s=a.__importDefault(n(391));function c(e){return e>0&&(0,r["default"])(e)}function f(e){var t=new s["default"](e),n=l.getRandom("w-col-id"),o=l.getRandom("w-row-id"),i=l.getRandom("btn-link"),r="menus.panelMenus.table.",a=function(t){return e.i18next.t(t)},f=[{title:a(r+"插入表格"),tpl:'<div>\n                    <div class="w-e-table">\n                        <span>'+a("创建")+'</span>\n                        <input id="'+o+'"  type="text" class="w-e-table-input" value="5"/></td>\n                        <span>'+a(r+"行")+'</span>\n                        <input id="'+n+'" type="text" class="w-e-table-input" value="5"/></td>\n                        <span>'+(a(r+"列")+a(r+"的")+a(r+"表格"))+'</span>\n                    </div>\n                    <div class="w-e-button-container">\n                        <button type="button" id="'+i+'" class="right">'+a("插入")+"</button>\n                    </div>\n                </div>",events:[{selector:"#"+i,type:"click",fn:function(){var i=Number(u["default"]("#"+n).val()),r=Number(u["default"]("#"+o).val());return c(r)&&c(i)?(t.createAction(r,i),!0):(e.config.customAlert("表格行列请输入正整数","warning"),!1)},bindEnter:!0}]}],d={width:330,height:0,tabs:[]};return d.tabs.push(f[0]),d}t["default"]=f},function(e,t,n){e.exports=n(385)},function(e,t,n){var o=n(386);e.exports=o},function(e,t,n){n(387);var o=n(9);e.exports=o.Number.isInteger},function(e,t,n){var o=n(5),i=n(388);o({target:"Number",stat:!0},{isInteger:i})},function(e,t,n){var o=n(13),i=Math.floor;e.exports=function(e){return!o(e)&&isFinite(e)&&i(e)===e}},function(e,t,n){var o=n(20),i=n(390);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,".w-e-table {\n  display: flex;\n}\n.w-e-table .w-e-table-input {\n  width: 40px;\n  text-align: center!important;\n  margin: 0 5px;\n}\n",""]),e.exports=t},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=n(7),l=r.__importDefault(n(3)),u=function(){function e(e){this.editor=e}return e.prototype.createAction=function(e,t){var n=this.editor,o=l["default"](n.selection.getSelectionContainerElem()),i=l["default"](o.elems[0]).parentUntilEditor("UL",n),r=l["default"](o.elems[0]).parentUntilEditor("OL",n);if(!i&&!r){var a=this.createTableHtml(e,t);n.cmd["do"]("insertHTML",a)}},e.prototype.createTableHtml=function(e,t){for(var n="",o="",i=0;i<e;i++){o="";for(var r=0;r<t;r++)o+=0===i?"<th></th>":"<td></td>";n=n+"<tr>"+o+"</tr>"}var l='<table border="0" width="100%" cellpadding="0" cellspacing="0"><tbody>'+n+"</tbody></table>"+a.EMPTY_P;return l},e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(393)),l=n(400);function u(e){a["default"](e),l.bindEventKeyboardEvent(e),l.bindClickEvent(e)}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(39)),u=r.__importDefault(n(394)),s=r.__importDefault(n(399)),c=n(7);function f(e){var t;function n(n){var o=new s["default"](e),i="menus.panelMenus.table.",r=function(t,n){return void 0===n&&(n=i),e.i18next.t(n+t)},f=[{$elem:a["default"]("<span>"+r("删除表格")+"</span>"),onClick:function(e,t){return e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("insertHTML",c.EMPTY_P),!0}},{$elem:a["default"]("<span>"+r("添加行")+"</span>"),onClick:function(e,t){var n=d(e);if(n)return!0;var i=a["default"](e.selection.getSelectionStartElem()),r=o.getRowNode(i.elems[0]);if(!r)return!0;var l=Number(o.getCurrentRowIndex(t.elems[0],r)),s=o.getTableHtml(t.elems[0]),c=o.getTableHtml(u["default"].ProcessingRow(a["default"](s),l).elems[0]);return c=v(t,c),e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("insertHTML",c),!0}},{$elem:a["default"]("<span>"+r("删除行")+"</span>"),onClick:function(e,t){var n=d(e);if(n)return!0;var i=a["default"](e.selection.getSelectionStartElem()),r=o.getRowNode(i.elems[0]);if(!r)return!0;var l=Number(o.getCurrentRowIndex(t.elems[0],r)),s=o.getTableHtml(t.elems[0]),f=u["default"].DeleteRow(a["default"](s),l).elems[0].children[0].children.length,p="";return e.selection.createRangeByElem(t),e.selection.restoreSelection(),p=0===f?c.EMPTY_P:o.getTableHtml(u["default"].DeleteRow(a["default"](s),l).elems[0]),p=v(t,p),e.cmd["do"]("insertHTML",p),!0}},{$elem:a["default"]("<span>"+r("添加列")+"</span>"),onClick:function(e,t){var n=d(e);if(n)return!0;var i=a["default"](e.selection.getSelectionStartElem()),r=o.getCurrentColIndex(i.elems[0]),l=o.getTableHtml(t.elems[0]),s=o.getTableHtml(u["default"].ProcessingCol(a["default"](l),r).elems[0]);return s=v(t,s),e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("insertHTML",s),!0}},{$elem:a["default"]("<span>"+r("删除列")+"</span>"),onClick:function(e,t){var n=d(e);if(n)return!0;var i=a["default"](e.selection.getSelectionStartElem()),r=o.getCurrentColIndex(i.elems[0]),l=o.getTableHtml(t.elems[0]),s=u["default"].DeleteCol(a["default"](l),r),f=s.elems[0].children[0].children[0].children.length,p="";return e.selection.createRangeByElem(t),e.selection.restoreSelection(),p=0===f?c.EMPTY_P:o.getTableHtml(s.elems[0]),p=v(t,p),e.cmd["do"]("insertHTML",p),!0}},{$elem:a["default"]("<span>"+r("设置表头")+"</span>"),onClick:function(e,t){var n=d(e);if(n)return!0;var i=a["default"](e.selection.getSelectionStartElem()),r=o.getRowNode(i.elems[0]);if(!r)return!0;var l=Number(o.getCurrentRowIndex(t.elems[0],r));0!==l&&(l=0);var s=o.getTableHtml(t.elems[0]),c=o.getTableHtml(u["default"].setTheHeader(a["default"](s),l,"th").elems[0]);return c=v(t,c),e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("insertHTML",c),!0}},{$elem:a["default"]("<span>"+r("取消表头")+"</span>"),onClick:function(e,t){var n=a["default"](e.selection.getSelectionStartElem()),i=o.getRowNode(n.elems[0]);if(!i)return!0;var r=Number(o.getCurrentRowIndex(t.elems[0],i));0!==r&&(r=0);var l=o.getTableHtml(t.elems[0]),s=o.getTableHtml(u["default"].setTheHeader(a["default"](l),r,"td").elems[0]);return s=v(t,s),e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("insertHTML",s),!0}}];t=new l["default"](e,n,f),t.create()}function o(){t&&(t.remove(),t=null)}return{showTableTooltip:n,hideTableTooltip:o}}function d(e){var t=e.selection.getSelectionStartElem(),n=e.selection.getSelectionEndElem();return(null===t||void 0===t?void 0:t.elems[0])!==(null===n||void 0===n?void 0:n.elems[0])}function p(e){var t=f(e),n=t.showTableTooltip,o=t.hideTableTooltip;e.txt.eventHooks.tableClickEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o)}function v(e,t){var n=e.elems[0].nextSibling;return n&&"<br>"!==n.innerHTML||(t+=""+c.EMPTY_P),t}t["default"]=p},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(45)),a=o(n(91)),l=o(n(4)),u=o(n(138));(0,i["default"])(t,"__esModule",{value:!0});var s=n(2),c=s.__importDefault(n(3));function f(e,t){for(var n=m(e),o=(0,r["default"])(Array.prototype).apply(n.children),i=o[0].children.length,l=document.createElement("tr"),u=0;u<i;u++){var s=document.createElement("td");l.appendChild(s)}return(0,a["default"])(o).call(o,t+1,0,l),A(n,o),c["default"](n.parentNode)}function d(e,t){for(var n=m(e),o=(0,r["default"])(Array.prototype).apply(n.children),i=function(e){var n,i=[];(0,l["default"])(n=(0,u["default"])(o[e].children)).call(n,(function(e){i.push(e)}));while(0!==o[e].children.length)o[e].removeChild(o[e].children[0]);var r="TH"!==c["default"](i[0]).getNodeName()?document.createElement("td"):document.createElement("th");(0,a["default"])(i).call(i,t+1,0,r);for(var s=0;s<i.length;s++)o[e].appendChild(i[s])},s=0;s<o.length;s++)i(s);return A(n,o),c["default"](n.parentNode)}function p(e,t){var n=m(e),o=(0,r["default"])(Array.prototype).apply(n.children);return(0,a["default"])(o).call(o,t,1),A(n,o),c["default"](n.parentNode)}function v(e,t){for(var n=m(e),o=(0,r["default"])(Array.prototype).apply(n.children),i=function(e){var n,i=[];(0,l["default"])(n=(0,u["default"])(o[e].children)).call(n,(function(e){i.push(e)}));while(0!==o[e].children.length)o[e].removeChild(o[e].children[0]);(0,a["default"])(i).call(i,t,1);for(var r=0;r<i.length;r++)o[e].appendChild(i[r])},s=0;s<o.length;s++)i(s);return A(n,o),c["default"](n.parentNode)}function h(e,t,n){for(var o=m(e),i=(0,r["default"])(Array.prototype).apply(o.children),s=i[t].children,f=document.createElement("tr"),d=function(e){var t,o=document.createElement(n),i=s[e];(0,l["default"])(t=(0,u["default"])(i.childNodes)).call(t,(function(e){o.appendChild(e)})),f.appendChild(o)},p=0;p<s.length;p++)d(p);return(0,a["default"])(i).call(i,t,1,f),A(o,i),c["default"](o.parentNode)}function A(e,t){while(0!==e.children.length)e.removeChild(e.children[0]);for(var n=0;n<t.length;n++)e.appendChild(t[n])}function m(e){var t=e.elems[0].children[0];return"COLGROUP"===t.nodeName&&(t=e.elems[0].children[e.elems[0].children.length-1]),t}t["default"]={ProcessingRow:f,ProcessingCol:d,DeleteRow:p,DeleteCol:v,setTheHeader:h}},function(e,t,n){var o=n(396);e.exports=o},function(e,t,n){n(50),n(397);var o=n(9);e.exports=o.Array.from},function(e,t,n){var o=n(5),i=n(398),r=n(115),a=!r((function(e){Array.from(e)}));o({target:"Array",stat:!0,forced:a},{from:i})},function(e,t,n){"use strict";var o=n(40),i=n(31),r=n(114),a=n(112),l=n(35),u=n(69),s=n(113);e.exports=function(e){var t,n,c,f,d,p,v=i(e),h="function"==typeof this?this:Array,A=arguments.length,m=A>1?arguments[1]:void 0,g=void 0!==m,y=s(v),w=0;if(g&&(m=o(m,A>2?arguments[2]:void 0,2)),void 0==y||h==Array&&a(y))for(t=l(v.length),n=new h(t);t>w;w++)p=g?m(v[w],w):v[w],u(n,w,p);else for(f=y.call(v),d=f.next,n=new h;!(c=d.call(f)).done;w++)p=g?r(f,m,[c.value,w],!0):c.value,u(n,w,p);return n.length=w,n}},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(138));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(3)),s=function(){function e(e){this.editor=e}return e.prototype.getRowNode=function(e){var t,n=u["default"](e).elems[0];return n.parentNode?(n=null===(t=u["default"](n).parentUntil("TR",n))||void 0===t?void 0:t.elems[0],n):n},e.prototype.getCurrentRowIndex=function(e,t){var n,o=0,i=e.children[0];return"COLGROUP"===i.nodeName&&(i=e.children[e.children.length-1]),(0,r["default"])(n=(0,a["default"])(i.children)).call(n,(function(e,n){e===t&&(o=n)})),o},e.prototype.getCurrentColIndex=function(e){var t,n,o=0,i="TD"===u["default"](e).getNodeName()||"TH"===u["default"](e).getNodeName()?e:null===(n=u["default"](e).parentUntil("TD",e))||void 0===n?void 0:n.elems[0],l=u["default"](i).parent();return(0,r["default"])(t=(0,a["default"])(l.elems[0].children)).call(t,(function(e,t){e===i&&(o=t)})),o},e.prototype.getTableHtml=function(e){var t='<table border="0" width="100%" cellpadding="0" cellspacing="0">'+u["default"](e).html()+"</table>";return t},e}();t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.bindEventKeyboardEvent=t.bindClickEvent=void 0;var r=n(2),a=r.__importDefault(n(3));function l(e){if(!e.length)return!1;var t=e.elems[0];return"P"===t.nodeName&&"<br>"===t.innerHTML}function u(e){function t(t,n){if(n.detail>=3){var o=window.getSelection();if(o){var i=o.focusNode,r=o.anchorNode,l=a["default"](null===r||void 0===r?void 0:r.parentElement);if(!t.isContain(a["default"](i))){var u="TD"===l.elems[0].tagName?l:l.parentUntilEditor("td",e);if(u){var s=e.selection.getRange();null===s||void 0===s||s.setEnd(u.elems[0],u.elems[0].childNodes.length),e.selection.restoreSelection()}}}}}e.txt.eventHooks.tableClickEvents.push(t)}function s(e){var t=e.txt,n=e.selection,o=t.eventHooks.keydownEvents;o.push((function(t){e.selection.saveRange();var o=n.getSelectionContainerElem();if(o){var i=o.getNodeTop(e),r=i.length&&i.prev().length?i.prev():null;if(r&&"TABLE"===r.getNodeName()&&n.isSelectionEmpty()&&0===n.getCursorPos()&&8===t.keyCode){var a=i.next(),u=!!a.length;u&&l(i)&&(i.remove(),e.selection.setRangeToElem(a.elems[0])),t.preventDefault()}}}))}t.bindClickEvent=u,t.bindEventKeyboardEvent=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26));(0,i["default"])(t,"__esModule",{value:!0}),t.formatCodeHtml=void 0;var a=n(2),l=a.__importDefault(n(38)),u=a.__importDefault(n(3)),s=n(6),c=a.__importDefault(n(402)),f=a.__importDefault(n(139)),d=a.__importDefault(n(33)),p=a.__importDefault(n(403));function v(e,t){return t?(t=o(t),t=n(t),t=s.replaceSpecialSymbol(t),t):t;function n(e){var t=e.match(/<pre[\s|\S]+?\/pre>/g);return null===t||(0,r["default"])(t).call(t,(function(t){e=e.replace(t,t.replace(/<\/code><code>/g,"\n").replace(/<br>/g,""))})),e}function o(e){var t,n=e.match(/<span\sclass="hljs[\s|\S]+?\/span>/gm);if(!n||!n.length)return e;for(var i=(0,r["default"])(t=s.deepClone(n)).call(t,(function(e){return e=e.replace(/<span\sclass="hljs[^>]+>/,""),e.replace(/<\/span>/,"")})),a=0;a<n.length;a++)e=e.replace(n[a],i[a]);return o(e)}}t.formatCodeHtml=v;var h=function(e){function t(t){var n=this,o=u["default"]('<div class="w-e-menu" data-title="代码"><i class="w-e-icon-terminal"></i></div>');return n=e.call(this,o,t)||this,p["default"](t),n}return a.__extends(t,e),t.prototype.insertLineCode=function(e){var t=this.editor,n=u["default"]("<code>"+e+"</code>");t.cmd["do"]("insertElem",n),t.selection.createRangeByElem(n,!1),t.selection.restoreSelection()},t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.getSelectionText();this.isActive||(e.selection.isSelectionEmpty()?this.createPanel("",""):this.insertLineCode(t))},t.prototype.createPanel=function(e,t){var n=c["default"](this.editor,e,t),o=new d["default"](this,n);o.create()},t.prototype.tryChangeActive=function(){var e=this.editor;f["default"](e)?this.active():this.unActive()},t}(l["default"]);t["default"]=h},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(26));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=n(6),u=a.__importDefault(n(3)),s=a.__importDefault(n(139)),c=n(7);function f(e,t,n){var o,i=l.getRandom("input-iframe"),a=l.getRandom("select"),f=l.getRandom("btn-ok");function d(t,n){var o,i=s["default"](e);i&&p();var r=null===(o=e.selection.getSelectionStartElem())||void 0===o?void 0:o.elems[0].innerHTML;r&&e.cmd["do"]("insertHTML",c.EMPTY_P);var a=n.replace(/</g,"&lt;").replace(/>/g,"&gt;");e.highlight&&(a=e.highlight.highlightAuto(a).value),e.cmd["do"]("insertHTML",'<pre><code class="'+t+'">'+a+"</code></pre>");var l=e.selection.getSelectionStartElem(),f=null===l||void 0===l?void 0:l.getNodeTop(e);0===(null===f||void 0===f?void 0:f.getNextSibling().elems.length)&&u["default"](c.EMPTY_P).insertAfter(f)}function p(){if(s["default"](e)){var t=e.selection.getSelectionStartElem(),n=null===t||void 0===t?void 0:t.getNodeTop(e);n&&(e.selection.createRangeByElem(n),e.selection.restoreSelection(),n)}}var v=function(t){return e.i18next.t(t)},h={width:500,height:0,tabs:[{title:v("menus.panelMenus.code.插入代码"),tpl:'<div>\n                        <select name="" id="'+a+'">\n                            '+(0,r["default"])(o=e.config.languageType).call(o,(function(e){return"<option "+(n==e?"selected":"")+' value ="'+e+'">'+e+"</option>"}))+'\n                        </select>\n                        <textarea id="'+i+'" type="text" class="wang-code-textarea" placeholder="" style="height: 160px">'+t.replace(/&quot;/g,'"')+'</textarea>\n                        <div class="w-e-button-container">\n                            <button type="button" id="'+f+'" class="right">'+(s["default"](e)?v("修改"):v("插入"))+"</button>\n                        </div>\n                    </div>",events:[{selector:"#"+f,type:"click",fn:function(){var t=document.getElementById(i),n=u["default"]("#"+a),o=n.val(),r=t.value;if(r)return!s["default"](e)&&(d(o,r),!0)}}]}]};return h}t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(404)),l=r.__importDefault(n(405));function u(e){a["default"](e),l["default"](e)}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.createShowHideFn=void 0;var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(39));function u(e){var t;function n(n){var o="menus.panelMenus.code.",i=function(t,n){return void 0===n&&(n=o),e.i18next.t(n+t)},r=[{$elem:a["default"]("<span>"+i("删除代码")+"</span>"),onClick:function(e,t){return t.remove(),!0}}];t=new l["default"](e,n,r),t.create()}function o(){t&&(t.remove(),t=null)}return{showCodeTooltip:n,hideCodeTooltip:o}}function s(e){var t=u(e),n=t.showCodeTooltip,o=t.hideCodeTooltip;e.txt.eventHooks.codeClickEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o)}t.createShowHideFn=u,t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=n(7),l=r.__importDefault(n(3));function u(e){var t=e.$textElem,n=e.selection,o=e.txt,i=o.eventHooks.keydownEvents;i.push((function(e){var o;if(40===e.keyCode){var i=n.getSelectionContainerElem(),r=null===(o=t.children())||void 0===o?void 0:o.last();if("XMP"===(null===i||void 0===i?void 0:i.elems[0].tagName)&&"PRE"===(null===r||void 0===r?void 0:r.elems[0].tagName)){var u=l["default"](a.EMPTY_P);t.append(u)}}})),i.push((function(o){e.selection.saveRange();var i=n.getSelectionContainerElem();if(i){var r=i.getNodeTop(e),u=null===r||void 0===r?void 0:r.prev(),s=null===r||void 0===r?void 0:r.getNextSibling();if(u.length&&"PRE"===(null===u||void 0===u?void 0:u.getNodeName())&&0===s.length&&0===n.getCursorPos()&&8===o.keyCode){var c=l["default"](a.EMPTY_P);t.append(c)}}}))}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(23)),l=r.__importDefault(n(3)),u=r.__importDefault(n(407)),s=n(6),c=n(7),f=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="分割线"><i class="w-e-icon-split-line"></i></div>');return n=e.call(this,o,t)||this,u["default"](t),n}return r.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor,t=e.selection.getRange(),n=e.selection.getSelectionContainerElem();if(null===n||void 0===n?void 0:n.length){var o=l["default"](n.elems[0]),i=o.parentUntil("TABLE",n.elems[0]),r=o.children();"CODE"!==o.getNodeName()&&(i&&"TABLE"===l["default"](i.elems[0]).getNodeName()||r&&0!==r.length&&"IMG"===l["default"](r.elems[0]).getNodeName()&&!(null===t||void 0===t?void 0:t.collapsed)||this.createSplitLine())}},t.prototype.createSplitLine=function(){var e="<hr/>"+c.EMPTY_P;s.UA.isFirefox&&(e="<hr/><p></p>"),this.editor.cmd["do"]("insertHTML",e)},t.prototype.tryChangeActive=function(){},t}(a["default"]);t["default"]=f},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(408));function l(e){a["default"](e)}t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=r.__importDefault(n(39));function u(e){var t;function n(n){var o=[{$elem:a["default"]("<span>"+e.i18next.t("menus.panelMenus.删除")+"</span>"),onClick:function(e,t){return e.selection.createRangeByElem(t),e.selection.restoreSelection(),e.cmd["do"]("delete"),!0}}];t=new l["default"](e,n,o),t.create()}function o(){t&&(t.remove(),t=null)}return{showSplitLineTooltip:n,hideSplitLineTooltip:o}}function s(e){var t=u(e),n=t.showSplitLineTooltip,o=t.hideSplitLineTooltip;e.txt.eventHooks.splitLineEvents.push(n),e.txt.eventHooks.clickEvents.push(o),e.txt.eventHooks.keyupEvents.push(o),e.txt.eventHooks.toolbarClickEvents.push(o),e.txt.eventHooks.menuClickEvents.push(o),e.txt.eventHooks.textScrollEvents.push(o)}t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=a.__importDefault(n(23)),s=n(98),c=a.__importDefault(n(415)),f=a.__importDefault(n(140)),d=function(e){function t(t){var n=this,o=l["default"]('<div class="w-e-menu" data-title="待办事项">\n                    <i class="w-e-icon-checkbox-checked"></i>\n                </div>');return n=e.call(this,o,t)||this,c["default"](t),n}return a.__extends(t,e),t.prototype.clickHandler=function(){var e=this.editor;s.isAllTodo(e)?(this.cancelTodo(),this.tryChangeActive()):this.setTodo()},t.prototype.tryChangeActive=function(){s.isAllTodo(this.editor)?this.active():this.unActive()},t.prototype.setTodo=function(){var e=this.editor,t=e.selection.getSelectionRangeTopNodes();(0,r["default"])(t).call(t,(function(t){var n,o=null===t||void 0===t?void 0:t.getNodeName();if("P"===o){var i=f["default"](t),r=i.getTodo(),a=null===(n=r.children())||void 0===n?void 0:n.getNode();r.insertAfter(t),e.selection.moveCursor(a),t.remove()}})),this.tryChangeActive()},t.prototype.cancelTodo=function(){var e=this.editor,t=e.selection.getSelectionRangeTopNodes();(0,r["default"])(t).call(t,(function(t){var n,o,i,r=null===(o=null===(n=t.childNodes())||void 0===n?void 0:n.childNodes())||void 0===o?void 0:o.clone(!0),a=l["default"]("<p></p>");a.append(r),a.insertAfter(t),null===(i=a.childNodes())||void 0===i||i.get(0).remove(),e.selection.moveCursor(a.getNode()),t.remove()}))},t}(u["default"]);t["default"]=d},function(e,t,n){e.exports=n(411)},function(e,t,n){var o=n(412);e.exports=o},function(e,t,n){var o=n(413),i=Array.prototype;e.exports=function(e){var t=e.every;return e===i||e instanceof Array&&t===i.every?o:t}},function(e,t,n){n(414);var o=n(15);e.exports=o("Array").every},function(e,t,n){"use strict";var o=n(5),i=n(32).every,r=n(67),a=n(22),l=r("every"),u=a("every");o({target:"Array",proto:!0,forced:!l||!u},{every:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3)),u=n(98),s=a.__importDefault(n(140)),c=n(98),f=n(7);function d(e){function t(t){var n,o;if(u.isAllTodo(e)){t.preventDefault();var i=e.selection,a=i.getSelectionRangeTopNodes()[0],d=null===(n=a.childNodes())||void 0===n?void 0:n.get(0),p=null===(o=window.getSelection())||void 0===o?void 0:o.anchorNode,v=i.getRange();if(!(null===v||void 0===v?void 0:v.collapsed)){var h=null===v||void 0===v?void 0:v.commonAncestorContainer.childNodes,A=null===v||void 0===v?void 0:v.startContainer,m=null===v||void 0===v?void 0:v.endContainer,g=null===v||void 0===v?void 0:v.startOffset,y=null===v||void 0===v?void 0:v.endOffset,w=0,x=0,_=[];null===h||void 0===h||(0,r["default"])(h).call(h,(function(e,t){e.contains(A)&&(w=t),e.contains(m)&&(x=t)})),x-w>1&&(null===h||void 0===h||(0,r["default"])(h).call(h,(function(e,t){t<=w||t>=x||_.push(e)})),(0,r["default"])(_).call(_,(function(e){e.remove()}))),c.dealTextNode(A,g),c.dealTextNode(m,y,!1),e.selection.moveCursor(m,0)}if(""===a.text()){var E=l["default"](f.EMPTY_P);return E.insertAfter(a),i.moveCursor(E.getNode()),void a.remove()}var b=i.getCursorPos(),C=u.getCursorNextNode(null===d||void 0===d?void 0:d.getNode(),p,b),S=s["default"](l["default"](C)),M=S.getInputContainer(),k=M.parent().getNode(),D=S.getTodo(),N=M.getNode().nextSibling;if(""===(null===d||void 0===d?void 0:d.text())&&(null===d||void 0===d||d.append(l["default"]("<br>"))),D.insertAfter(a),N&&""!==(null===N||void 0===N?void 0:N.textContent))i.moveCursor(k);else{if("BR"!==(null===N||void 0===N?void 0:N.nodeName)){var T=l["default"]("<br>");T.insertAfter(M)}i.moveCursor(k,1)}}}function n(t){var n,o;if(u.isAllTodo(e)){var i,a=e.selection,s=a.getSelectionRangeTopNodes()[0],c=null===(n=s.childNodes())||void 0===n?void 0:n.getNode(),d=l["default"]("<p></p>"),p=d.getNode(),v=null===(o=window.getSelection())||void 0===o?void 0:o.anchorNode,h=a.getCursorPos(),A=v.previousSibling;if(""===s.text()){t.preventDefault();var m=l["default"](f.EMPTY_P);return m.insertAfter(s),s.remove(),void a.moveCursor(m.getNode(),0)}if("SPAN"===(null===A||void 0===A?void 0:A.nodeName)&&"INPUT"===A.childNodes[0].nodeName&&0===h)t.preventDefault(),null===c||void 0===c||(0,r["default"])(i=c.childNodes).call(i,(function(e,t){0!==t&&p.appendChild(e.cloneNode(!0))})),d.insertAfter(s),s.remove()}}function o(){var t=e.selection,n=t.getSelectionRangeTopNodes()[0];n&&c.isTodo(n)&&""===n.text()&&(l["default"](f.EMPTY_P).insertAfter(n),n.remove())}function i(e){e&&e.target instanceof HTMLInputElement&&"checkbox"===e.target.type&&(e.target.checked?e.target.setAttribute("checked","true"):e.target.removeAttribute("checked"))}e.txt.eventHooks.enterDownEvents.push(t),e.txt.eventHooks.deleteUpEvents.push(o),e.txt.eventHooks.deleteDownEvents.push(n),e.txt.eventHooks.clickEvents.push(i)}t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.selectorValidator=void 0;var r=n(2),a=r.__importDefault(n(3)),l=n(6),u=n(7),s=r.__importDefault(n(130)),c={border:"1px solid #c9d8db",toolbarBgColor:"#FFF",toolbarBottomBorder:"1px solid #EEE"};function f(e){var t,n,o,i=e.toolbarSelector,r=a["default"](i),f=e.textSelector,d=e.config,p=d.height,v=e.i18next,h=a["default"]("<div></div>"),A=a["default"]("<div></div>"),m=null;null==f?(n=r.children(),r.append(h).append(A),h.css("background-color",c.toolbarBgColor).css("border",c.border).css("border-bottom",c.toolbarBottomBorder),A.css("border",c.border).css("border-top","none").css("height",p+"px")):(r.append(h),m=a["default"](f).children(),a["default"](f).append(A),n=A.children()),t=a["default"]("<div></div>"),t.attr("contenteditable","true").css("width","100%").css("height","100%");var g=e.config.placeholder;o=g!==s["default"].placeholder?a["default"]("<div>"+g+"</div>"):a["default"]("<div>"+v.t(g)+"</div>"),o.addClass("placeholder"),n&&n.length?(t.append(n),o.hide()):t.append(a["default"](u.EMPTY_P)),m&&m.length&&(t.append(m),o.hide()),A.append(t),A.append(o),h.addClass("w-e-toolbar").css("z-index",e.zIndex.get("toolbar")),A.addClass("w-e-text-container"),A.css("z-index",e.zIndex.get()),t.addClass("w-e-text");var y=l.getRandom("toolbar-elem");h.attr("id",y);var w=l.getRandom("text-elem");t.attr("id",w);var x=A.getBoundingClientRect().height,_=t.getBoundingClientRect().height;x!==_&&t.css("min-height",x+"px"),e.$toolbarElem=h,e.$textContainerElem=A,e.$textElem=t,e.toolbarElemId=y,e.textElemId=w}function d(e){var t="data-we-id",n=/^wangEditor-\d+$/,o=e.textSelector,i=e.toolbarSelector,r={bar:a["default"]("<div></div>"),text:a["default"]("<div></div>")};if(null==i)throw new Error("错误：初始化编辑器时候未传入任何参数，请查阅文档");if(r.bar=a["default"](i),!r.bar.elems.length)throw new Error("无效的节点选择器："+i);if(n.test(r.bar.attr(t)))throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器");if(o){if(r.text=a["default"](o),!r.text.elems.length)throw new Error("无效的节点选择器："+o);if(n.test(r.text.attr(t)))throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器")}r.bar.attr(t,e.id),r.text.attr(t,e.id),e.beforeDestroy((function(){r.bar.removeAttr(t),r.text.removeAttr(t)}))}t["default"]=f,t.selectorValidator=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(3)),l=n(7);function u(e,t){var n=e.$textElem,o=n.children();if(!o||!o.length)return n.append(a["default"](l.EMPTY_P)),void u(e);var i=o.last();if(t){var r=i.html().toLowerCase(),s=i.getNodeName();if("<br>"!==r&&"<br/>"!==r||"P"!==s)return n.append(a["default"](l.EMPTY_P)),void u(e)}e.selection.createRangeByElem(i,!1,!0),e.config.focus?e.selection.restoreSelection():e.selection.clearWindowSelectionRange()}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3));function u(e){s(e),c(e),f(e)}function s(e){e.txt.eventHooks.changeEvents.push((function(){var t=e.config.onchange;if(t){var n=e.txt.html()||"";e.isFocus=!0,t(n)}e.txt.togglePlaceholder()}))}function c(e){function t(t){var n=t.target,o=l["default"](n),i=e.$textElem,r=e.$toolbarElem,a=i.isContain(o),u=r.isContain(o),s=r.elems[0]==t.target;if(a)e.isFocus||p(e),e.isFocus=!0;else{if(u&&!s||!e.isFocus)return;d(e),e.isFocus=!1}}e.isFocus=!1,document.activeElement===e.$textElem.elems[0]&&e.config.focus&&(p(e),e.isFocus=!0),l["default"](document).on("click",t),e.beforeDestroy((function(){l["default"](document).off("click",t)}))}function f(e){e.$textElem.on("compositionstart",(function(){e.isComposing=!0,e.txt.togglePlaceholder()})).on("compositionend",(function(){e.isComposing=!1,e.txt.togglePlaceholder()}))}function d(e){var t,n=e.config,o=n.onblur,i=e.txt.html()||"";(0,r["default"])(t=e.txt.eventHooks.onBlurEvents).call(t,(function(e){return e()})),o(i)}function p(e){var t=e.config,n=t.onfocus,o=e.txt.html()||"";n(o)}t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));function r(e){var t=e.config,n=t.lang,o=t.languages;if(null==e.i18next)e.i18next={t:function(e){var t=e.split(".");return t[t.length-1]}};else try{e.i18next.init({ns:"wangEditor",lng:n,defaultNS:"wangEditor",resources:o})}catch(i){throw new Error("i18next:"+i)}}(0,i["default"])(t,"__esModule",{value:!0}),t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29));(0,i["default"])(t,"__esModule",{value:!0}),t.setUnFullScreen=t.setFullScreen=void 0;var a=n(2),l=a.__importDefault(n(3));n(421);var u="w-e-icon-fullscreen",s="w-e-icon-fullscreen_exit",c="w-e-full-screen-editor";t.setFullScreen=function(e){var t=l["default"](e.toolbarSelector),n=e.$textContainerElem,o=e.$toolbarElem,i=(0,r["default"])(o).call(o,"i."+u),a=e.config;i.removeClass(u),i.addClass(s),t.addClass(c),t.css("z-index",a.zIndexFullScreen);var f=o.getBoundingClientRect();n.css("height","calc(100% - "+f.height+"px)")},t.setUnFullScreen=function(e){var t=l["default"](e.toolbarSelector),n=e.$textContainerElem,o=e.$toolbarElem,i=(0,r["default"])(o).call(o,"i."+s),a=e.config;i.removeClass(s),i.addClass(u),t.removeClass(c),t.css("z-index","auto"),n.css("height",a.height+"px")};var f=function(e){if(!e.textSelector&&e.config.showFullScreen){var n=e.$toolbarElem,o=l["default"]('<div class="w-e-menu" data-title="全屏">\n            <i class="'+u+'"></i>\n        </div>');o.on("click",(function(n){var i,a=(0,r["default"])(i=l["default"](n.currentTarget)).call(i,"i");a.hasClass(u)?(o.attr("data-title","取消全屏"),t.setFullScreen(e)):(o.attr("data-title","全屏"),t.setUnFullScreen(e))})),n.append(o)}};t["default"]=f},function(e,t,n){var o=n(20),i=n(422);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,".w-e-full-screen-editor {\n  position: fixed;\n  width: 100%!important;\n  height: 100%!important;\n  left: 0;\n  top: 0;\n}\n",""]),e.exports=t},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29));(0,i["default"])(t,"__esModule",{value:!0});var a=function(e,t){var n,o=e.isEnable?e.$textElem:(0,r["default"])(n=e.$textContainerElem).call(n,".w-e-content-mantle"),i=(0,r["default"])(o).call(o,"[id='"+t+"']"),a=i.getOffsetData().top;o.scrollTop(a)};t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(129)),l={menu:2,panel:2,toolbar:1,tooltip:1,textContainer:1},u=function(){function e(){this.tier=l,this.baseZIndex=a["default"].zIndex}return e.prototype.get=function(e){return e&&this.tier[e]?this.baseZIndex+this.tier[e]:this.baseZIndex},e.prototype.init=function(e){this.baseZIndex==a["default"].zIndex&&(this.baseZIndex=e.config.zIndex)},e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(70)),a=o(n(4));(0,i["default"])(t,"__esModule",{value:!0});var l=n(2),u=l.__importDefault(n(426)),s=n(6),c=n(7);function f(e,t){return(0,r["default"])(e).call(e,(function(e){var n=e.type,o=e.target,i=e.attributeName;return"attributes"!=n||"attributes"==n&&("contenteditable"==i||o!=t)}))}var d=function(e){function t(t){var n=e.call(this,(function(e,o){var i;if(e=f(e,o.target),(i=n.data).push.apply(i,e),t.isCompatibleMode)n.asyncSave();else if(!t.isComposing)return n.asyncSave()}))||this;return n.editor=t,n.data=[],n.asyncSave=c.EMPTY_FN,n}return l.__extends(t,e),t.prototype.save=function(){this.data.length&&(this.editor.history.save(this.data),this.data.length=0,this.emit())},t.prototype.emit=function(){var e;(0,a["default"])(e=this.editor.txt.eventHooks.changeEvents).call(e,(function(e){return e()}))},t.prototype.observe=function(){var t=this;e.prototype.observe.call(this,this.editor.$textElem.elems[0]);var n=this.editor.config.onchangeTimeout;this.asyncSave=s.debounce((function(){t.save()}),n),this.editor.isCompatibleMode||this.editor.$textElem.on("compositionend",(function(){t.asyncSave()}))},t}(u["default"]);t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=function(){function e(e,t){var n=this;this.options={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0},this.callback=function(t){e(t,n)},this.observer=new MutationObserver(this.callback),t&&(this.options=t)}return(0,i["default"])(e.prototype,"target",{get:function(){return this.node},enumerable:!1,configurable:!0}),e.prototype.observe=function(e){this.node instanceof Node||(this.node=e,this.connect())},e.prototype.connect=function(){if(this.node)return this.observer.observe(this.node,this.options),this;throw new Error("还未初始化绑定，请您先绑定有效的 Node 节点")},e.prototype.disconnect=function(){var e=this.observer.takeRecords();e.length&&this.callback(e),this.observer.disconnect()},e}();t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(428)),l=r.__importDefault(n(435)),u=r.__importDefault(n(436)),s=function(){function e(e){this.editor=e,this.content=new a["default"](e),this.scroll=new l["default"](e),this.range=new u["default"](e)}return(0,i["default"])(e.prototype,"size",{get:function(){return this.scroll.size},enumerable:!1,configurable:!0}),e.prototype.observe=function(){this.content.observe(),this.scroll.observe(),!this.editor.isCompatibleMode&&this.range.observe()},e.prototype.save=function(e){e.length&&(this.content.save(e),this.scroll.save(),!this.editor.isCompatibleMode&&this.range.save())},e.prototype.revoke=function(){this.editor.change.disconnect();var e=this.content.revoke();e&&(this.scroll.revoke(),this.editor.isCompatibleMode||(this.range.revoke(),this.editor.$textElem.focus())),this.editor.change.connect(),e&&this.editor.change.emit()},e.prototype.restore=function(){this.editor.change.disconnect();var e=this.content.restore();e&&(this.scroll.restore(),this.editor.isCompatibleMode||(this.range.restore(),this.editor.$textElem.focus())),this.editor.change.connect(),e&&this.editor.change.emit()},e}();t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(429)),l=r.__importDefault(n(433)),u=function(){function e(e){this.editor=e}return e.prototype.observe=function(){this.editor.isCompatibleMode?this.cache=new l["default"](this.editor):this.cache=new a["default"](this.editor),this.cache.observe()},e.prototype.save=function(e){this.editor.isCompatibleMode?this.cache.save():this.cache.compile(e)},e.prototype.revoke=function(){var e;return null===(e=this.cache)||void 0===e?void 0:e.revoke()},e.prototype.restore=function(){var e;return null===(e=this.cache)||void 0===e?void 0:e.restore()},e}();t["default"]=u},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(99)),l=r.__importDefault(n(431)),u=n(432),s=function(e){function t(t){var n=e.call(this,t.config.historyMaxSize)||this;return n.editor=t,n}return r.__extends(t,e),t.prototype.observe=function(){this.resetMaxSize(this.editor.config.historyMaxSize)},t.prototype.compile=function(e){return this.save(l["default"](e)),this},t.prototype.revoke=function(){return e.prototype.revoke.call(this,(function(e){u.revoke(e)}))},t.prototype.restore=function(){return e.prototype.restore.call(this,(function(e){u.restore(e)}))},t}(a["default"]);t["default"]=s},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0}),t.CeilStack=void 0;var r=function(){function e(e){void 0===e&&(e=0),this.data=[],this.max=0,this.reset=!1,e=Math.abs(e),e&&(this.max=e)}return e.prototype.resetMax=function(e){e=Math.abs(e),this.reset||isNaN(e)||(this.max=e,this.reset=!0)},(0,i["default"])(e.prototype,"size",{get:function(){return this.data.length},enumerable:!1,configurable:!0}),e.prototype.instack=function(e){return this.data.unshift(e),this.max&&this.size>this.max&&(this.data.length=this.max),this},e.prototype.outstack=function(){return this.data.shift()},e.prototype.clear=function(){return this.data.length=0,this},e}();t.CeilStack=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(27));(0,i["default"])(t,"__esModule",{value:!0}),t.compliePosition=t.complieNodes=t.compileValue=t.compileType=void 0;var l=n(6);function u(e){switch(e){case"childList":return"node";case"attributes":return"attr";default:return"text"}}function s(e){switch(e.type){case"attributes":return e.target.getAttribute(e.attributeName)||"";case"characterData":return e.target.textContent;default:return""}}function c(e){var t={};return e.addedNodes.length&&(t.add=l.toArray(e.addedNodes)),e.removedNodes.length&&(t.remove=l.toArray(e.removedNodes)),t}function f(e){var t;return t=e.previousSibling?{type:"before",target:e.previousSibling}:e.nextSibling?{type:"after",target:e.nextSibling}:{type:"parent",target:e.target},t}t.compileType=u,t.compileValue=s,t.complieNodes=c,t.compliePosition=f;var d=["UL","OL","H1","H2","H3","H4","H5","H6"];function p(e){var t=[],n=!1,o=[];return(0,r["default"])(e).call(e,(function(e,i){var r={type:u(e.type),target:e.target,attr:e.attributeName||"",value:s(e)||"",oldValue:e.oldValue||"",nodes:c(e),position:f(e)};if(t.push(r),l.UA.isFirefox){if(n&&e.addedNodes.length&&1==e.addedNodes[0].nodeType){var p=e.addedNodes[0],h={type:"node",target:p,attr:"",value:"",oldValue:"",nodes:{add:[n]},position:{type:"parent",target:p}};-1!=(0,a["default"])(d).call(d,p.nodeName)?(h.nodes.add=l.toArray(p.childNodes),t.push(h)):3==n.nodeType?(v(p,o)&&(h.nodes.add=l.toArray(p.childNodes)),t.push(h)):-1==(0,a["default"])(d).call(d,e.target.nodeName)&&v(p,o)&&(h.nodes.add=l.toArray(p.childNodes),t.push(h))}"node"==r.type&&1==e.removedNodes.length?(n=e.removedNodes[0],o.push(n)):(n=!1,o.length=0)}})),t}function v(e,t){for(var n=0,o=t.length-1;o>0;o--){if(!e.contains(t[o]))break;n++}return n}t["default"]=p},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(4)),a=o(n(94));function l(e,t){var n=e.position.target;switch(e.position.type){case"before":n.nextSibling?(n=n.nextSibling,(0,r["default"])(t).call(t,(function(t){e.target.insertBefore(t,n)}))):(0,r["default"])(t).call(t,(function(t){e.target.appendChild(t)}));break;case"after":(0,r["default"])(t).call(t,(function(t){e.target.insertBefore(t,n)}));break;default:(0,r["default"])(t).call(t,(function(e){n.appendChild(e)}));break}}function u(e){for(var t=0,n=(0,a["default"])(e.nodes);t<n.length;t++){var o=n[t],i=o[0],u=o[1];switch(i){case"add":(0,r["default"])(u).call(u,(function(t){e.target.removeChild(t)}));break;default:l(e,u);break}}}function s(e){var t=e.target;null==e.oldValue?t.removeAttribute(e.attr):t.setAttribute(e.attr,e.oldValue)}function c(e){e.target.textContent=e.oldValue}(0,i["default"])(t,"__esModule",{value:!0}),t.restore=t.revoke=void 0;var f={node:u,text:c,attr:s};function d(e){for(var t=e.length-1;t>-1;t--){var n=e[t];f[n.type](n)}}function p(e){for(var t=0,n=(0,a["default"])(e.nodes);t<n.length;t++){var o=n[t],i=o[0],u=o[1];switch(i){case"add":l(e,u);break;default:(0,r["default"])(u).call(u,(function(e){e.parentNode.removeChild(e)}));break}}}function v(e){e.target.textContent=e.value}function h(e){e.target.setAttribute(e.attr,e.value)}t.revoke=d;var A={node:p,text:v,attr:h};function m(e){for(var t=0,n=e;t<n.length;t++){var o=n[t];A[o.type](o)}}t.restore=m},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(434),a=function(){function e(e){this.editor=e,this.data=new r.TailChain}return e.prototype.observe=function(){this.data.resetMax(this.editor.config.historyMaxSize),this.data.insertLast(this.editor.$textElem.html())},e.prototype.save=function(){return this.data.insertLast(this.editor.$textElem.html()),this},e.prototype.revoke=function(){var e=this.data.prev();return!!e&&(this.editor.$textElem.html(e),!0)},e.prototype.restore=function(){var e=this.data.next();return!!e&&(this.editor.$textElem.html(e),!0)},e}();t["default"]=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(91));(0,i["default"])(t,"__esModule",{value:!0}),t.TailChain=void 0;var a=function(){function e(){this.data=[],this.max=0,this.point=0,this.isRe=!1}return e.prototype.resetMax=function(e){e=Math.abs(e),e&&(this.max=e)},(0,i["default"])(e.prototype,"size",{get:function(){return this.data.length},enumerable:!1,configurable:!0}),e.prototype.insertLast=function(e){var t;this.isRe&&((0,r["default"])(t=this.data).call(t,this.point+1),this.isRe=!1);this.data.push(e);while(this.max&&this.size>this.max)this.data.shift();return this.point=this.size-1,this},e.prototype.current=function(){return this.data[this.point]},e.prototype.prev=function(){if(!this.isRe&&(this.isRe=!0),this.point--,!(this.point<0))return this.current();this.point=0},e.prototype.next=function(){if(!this.isRe&&(this.isRe=!0),this.point++,!(this.point>=this.size))return this.current();this.point=this.size-1},e}();t.TailChain=a},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(99)),l=function(e){function t(t){var n=e.call(this,t.config.historyMaxSize)||this;return n.editor=t,n.last=0,n.target=t.$textElem.elems[0],n}return r.__extends(t,e),t.prototype.observe=function(){var e=this;this.target=this.editor.$textElem.elems[0],this.editor.$textElem.on("scroll",(function(){e.last=e.target.scrollTop})),this.resetMaxSize(this.editor.config.historyMaxSize)},t.prototype.save=function(){return e.prototype.save.call(this,[this.last,this.target.scrollTop]),this},t.prototype.revoke=function(){var t=this;return e.prototype.revoke.call(this,(function(e){t.target.scrollTop=e[0]}))},t.prototype.restore=function(){var t=this;return e.prototype.restore.call(this,(function(e){t.target.scrollTop=e[1]}))},t}(a["default"]);t["default"]=l},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=n(2),a=r.__importDefault(n(99)),l=r.__importDefault(n(3)),u=n(6);function s(e){return{start:[e.startContainer,e.startOffset],end:[e.endContainer,e.endOffset],root:e.commonAncestorContainer,collapsed:e.collapsed}}var c=function(e){function t(t){var n=e.call(this,t.config.historyMaxSize)||this;return n.editor=t,n.lastRange=s(document.createRange()),n.root=t.$textElem.elems[0],n.updateLastRange=u.debounce((function(){n.lastRange=s(n.rangeHandle)}),t.config.onchangeTimeout),n}return r.__extends(t,e),(0,i["default"])(t.prototype,"rangeHandle",{get:function(){var e=document.getSelection();return e&&e.rangeCount?e.getRangeAt(0):document.createRange()},enumerable:!1,configurable:!0}),t.prototype.observe=function(){var e=this;function t(){var t=e.rangeHandle;(e.root===t.commonAncestorContainer||e.root.contains(t.commonAncestorContainer))&&(e.editor.isComposing||e.updateLastRange())}function n(t){"Backspace"!=t.key&&"Delete"!=t.key||e.updateLastRange()}this.root=this.editor.$textElem.elems[0],this.resetMaxSize(this.editor.config.historyMaxSize),l["default"](document).on("selectionchange",t),this.editor.beforeDestroy((function(){l["default"](document).off("selectionchange",t)})),e.editor.$textElem.on("keydown",n)},t.prototype.save=function(){var t=s(this.rangeHandle);return e.prototype.save.call(this,[this.lastRange,t]),this.lastRange=t,this},t.prototype.set=function(e){try{if(e){var t=this.rangeHandle;return t.setStart.apply(t,e.start),t.setEnd.apply(t,e.end),this.editor.menus.changeActive(),!0}}catch(n){return!1}return!1},t.prototype.revoke=function(){var t=this;return e.prototype.revoke.call(this,(function(e){t.set(e[0])}))},t.prototype.restore=function(){var t=this;return e.prototype.restore.call(this,(function(e){t.set(e[1])}))},t}(a["default"]);t["default"]=c},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(29));(0,i["default"])(t,"__esModule",{value:!0});var a=n(2),l=a.__importDefault(n(3));function u(e){var t,n,o=!1;function i(){if(!o){e.$textElem.hide();var i=e.zIndex.get("textContainer"),r=e.txt.html();t=l["default"]('<div class="w-e-content-mantle" style="z-index:'+i+'">\n                <div class="w-e-content-preview w-e-text">'+r+"</div>\n            </div>"),e.$textContainerElem.append(t);var a=e.zIndex.get("menu");n=l["default"]('<div class="w-e-menue-mantle" style="z-index:'+a+'"></div>'),e.$toolbarElem.append(n),o=!0,e.isEnable=!1}}function a(){o&&(t.remove(),n.remove(),e.$textElem.show(),o=!1,e.isEnable=!0)}return e.txt.eventHooks.changeEvents.push((function(){o&&(0,r["default"])(t).call(t,".w-e-content-preview").html(e.$textElem.html())})),{disable:i,enable:a}}n(438),t["default"]=u},function(e,t,n){var o=n(20),i=n(439);i=i.__esModule?i.default:i,"string"===typeof i&&(i=[[e.i,i,""]]);var r={insert:"head",singleton:!1};o(i,r);e.exports=i.locals||{}},function(e,t,n){var o=n(21);t=o(!1),t.push([e.i,".w-e-content-mantle {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n}\n.w-e-content-mantle .w-e-content-preview {\n  width: 100%;\n  min-height: 100%;\n  padding: 0 10px;\n  line-height: 1.5;\n}\n.w-e-content-mantle .w-e-content-preview img {\n  cursor: default;\n}\n.w-e-content-mantle .w-e-content-preview img:hover {\n  box-shadow: none;\n}\n.w-e-menue-mantle {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  top: 0;\n  left: 0;\n}\n",""]),e.exports=t},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0});var r=function(){function e(e){var t=this;this.editor=e;var n=function(){var n=document.activeElement;n===e.$textElem.elems[0]&&t.emit()};window.document.addEventListener("selectionchange",n),this.editor.beforeDestroy((function(){window.document.removeEventListener("selectionchange",n)}))}return e.prototype.emit=function(){var e,t=this.editor.config.onSelectionChange;if(t){var n=this.editor.selection;n.saveRange(),n.isSelectionEmpty()||t({text:n.getSelectionText(),html:null===(e=n.getSelectionContainerElem())||void 0===e?void 0:e.elems[0].innerHTML,selection:n})}},e}();t["default"]=r},function(e,t,n){"use strict";var o=n(0),i=o(n(1)),r=o(n(128)),a=o(n(94)),l=o(n(4));(0,i["default"])(t,"__esModule",{value:!0}),t.registerPlugin=void 0;var u=n(2),s=u.__importDefault(n(87)),c=n(6);function f(e,t,n){if(!e)throw new TypeError("name is not define");if(!t)throw new TypeError("options is not define");if(!t.intention)throw new TypeError("options.intention is not define");if(t.intention&&"function"!==typeof t.intention)throw new TypeError("options.intention is not function");n[e]&&console.warn("plugin "+e+" 已存在，已覆盖。"),n[e]=t}function d(e){var t=(0,r["default"])({},c.deepClone(s["default"].globalPluginsFunctionList),c.deepClone(e.pluginsFunctionList)),n=(0,a["default"])(t);(0,l["default"])(n).call(n,(function(t){var n=t[0],o=t[1];console.info("plugin "+n+" initializing");var i=o.intention,r=o.config;i(e,r),console.info("plugin "+n+" initialization complete")}))}t.registerPlugin=f,t["default"]=d},function(e,t,n){"use strict";var o=n(0),i=o(n(1));(0,i["default"])(t,"__esModule",{value:!0})}])["default"]}))}}]);
//# sourceMappingURL=chunk-79add12c.0cadd1f5.js.map