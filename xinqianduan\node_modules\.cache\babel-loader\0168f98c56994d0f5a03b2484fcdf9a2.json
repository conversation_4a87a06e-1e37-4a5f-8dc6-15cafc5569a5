{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yuangong\\index.vue", "mtime": 1748336508341}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\babel.config.js", "mtime": 1748336479385}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "allSize", "list", "total", "page", "size", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "is_num", "field", "zhi<PERSON>s", "rules", "required", "message", "trigger", "account", "pic_path", "zhiwei_id", "form<PERSON>abe<PERSON><PERSON>", "mounted", "getData", "methods", "changeField", "chong<PERSON>", "id", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "postRequest", "resp", "code", "$message", "catch", "getZhiwei", "editData", "_this", "getInfo", "phone", "getRequest", "delData", "index", "deleteRequest", "splice", "refulsh", "$router", "go", "searchData", "count", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success"], "sources": ["src/views/pages/yuangong/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"名称\"> </el-table-column>\r\n        <el-table-column prop=\"zhiwei_id\" label=\"职位\"> </el-table-column>\r\n        <el-table-column prop=\"pic_path\" label=\"头像\">\r\n          <template slot-scope=\"scope\">\r\n            <img\r\n              :src=\"scope.row.pic_path\"\r\n              style=\"width: 160px; height: 80px\"\r\n              @click=\"showImage(scope.row.pic_path)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"手机号码\"> </el-table-column>\r\n        <el-table-column prop=\"account\" label=\"账号\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button type=\"text\" size=\"small\" @click=\"chongzhi(scope.row.id)\"\r\n              >重置密码</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"职位类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhiwei_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.zhiwei_id\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n          >\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in zhiweis\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '名称'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '手机'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '账号'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"account\"\r\n        >\r\n          <el-input v-model=\"ruleForm.account\" autocomplete=\"off\">\r\n            <template slot=\"append\">默认密码888888</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"头像\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n          <div class=\"el-upload__tip\">330rpx*300rpx</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/Yuangong/\",\r\n      title: \"员工\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n      field: \"\",\r\n      zhiweis: [],\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传头像\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    chongzhi(id) {\r\n      this.$confirm(\"重置密码888888?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.postRequest(\"/yuangong/chongzhi\", { id: id }).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"重置成功!\",\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: \"重置失败!\",\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消重置!\",\r\n          });\r\n        });\r\n    },\r\n    getZhiwei() {\r\n      this.postRequest(\"/zhiwei/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.zhiweis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          pic_path: \"\",\r\n          account: \"\",\r\n          phone: \"\",\r\n          zhiwei_id: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getZhiwei();\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"], "mappings": "AA+KA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,MAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;QACAT,KAAA,GACA;UACAU,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,OAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,QAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAG,SAAA,GACA;UACAL,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAI,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,YAAAb,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACAc,SAAAC,EAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAC,IAAA;QACA,KAAAC,WAAA;UAAAN,EAAA,EAAAA;QAAA,GAAAK,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAL,IAAA;cACAf,OAAA;YACA;UACA;YACA,KAAAoB,QAAA;cACAL,IAAA;cACAf,OAAA;YACA;UACA;QACA;MACA,GACAqB,KAAA;QACA,KAAAD,QAAA;UACAL,IAAA;UACAf,OAAA;QACA;MACA;IACA;IACAsB,UAAA;MACA,KAAAL,WAAA,wBAAAD,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAtB,OAAA,GAAAqB,IAAA,CAAAvC,IAAA;QACA;MACA;IACA;IACA4C,SAAAZ,EAAA;MACA,IAAAa,KAAA;MACA,IAAAb,EAAA;QACA,KAAAc,OAAA,CAAAd,EAAA;MACA;QACA,KAAAjB,QAAA;UACAL,KAAA;UACAc,QAAA;UACAD,OAAA;UACAwB,KAAA;UACAtB,SAAA;QACA;MACA;MAEAoB,KAAA,CAAAjC,iBAAA;MACAiC,KAAA,CAAAF,SAAA;IACA;IACAG,QAAAd,EAAA;MACA,IAAAa,KAAA;MACAA,KAAA,CAAAG,UAAA,CAAAH,KAAA,CAAApC,GAAA,gBAAAuB,EAAA,EAAAK,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA;UACAM,KAAA,CAAA9B,QAAA,GAAAwB,IAAA,CAAAvC,IAAA;QACA;MACA;IACA;IACAiD,QAAAC,KAAA,EAAAlB,EAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAC,IAAA;QACA,KAAAc,aAAA,MAAA1C,GAAA,kBAAAuB,EAAA,EAAAK,IAAA,CAAAE,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAC,QAAA;cACAL,IAAA;cACAf,OAAA;YACA;YACA,KAAAnB,IAAA,CAAAkD,MAAA,CAAAF,KAAA;UACA;QACA;MACA,GACAR,KAAA;QACA,KAAAD,QAAA;UACAL,IAAA;UACAf,OAAA;QACA;MACA;IACA;IACAgC,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAApD,IAAA;MACA,KAAAC,IAAA;MACA,KAAAuB,OAAA;IACA;IAEAA,QAAA;MACA,IAAAiB,KAAA;MAEAA,KAAA,CAAArC,OAAA;MACAqC,KAAA,CACAP,WAAA,CACAO,KAAA,CAAApC,GAAA,mBAAAoC,KAAA,CAAAzC,IAAA,cAAAyC,KAAA,CAAAxC,IAAA,EACAwC,KAAA,CAAAvC,MACA,EACA+B,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAK,KAAA,CAAA3C,IAAA,GAAAqC,IAAA,CAAAvC,IAAA;UACA6C,KAAA,CAAA1C,KAAA,GAAAoC,IAAA,CAAAkB,KAAA;QACA;QACAZ,KAAA,CAAArC,OAAA;MACA;IACA;IACAkD,SAAA;MACA,IAAAb,KAAA;MACA,KAAAc,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAvB,WAAA,CAAAO,KAAA,CAAApC,GAAA,gBAAAM,QAAA,EAAAsB,IAAA,CAAAE,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAK,KAAA,CAAAJ,QAAA;gBACAL,IAAA;gBACAf,OAAA,EAAAkB,IAAA,CAAAuB;cACA;cACA,KAAAlC,OAAA;cACAiB,KAAA,CAAAjC,iBAAA;YACA;cACAiC,KAAA,CAAAJ,QAAA;gBACAL,IAAA;gBACAf,OAAA,EAAAkB,IAAA,CAAAuB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAA3D,IAAA,GAAA2D,GAAA;MAEA,KAAApC,OAAA;IACA;IACAqC,oBAAAD,GAAA;MACA,KAAA5D,IAAA,GAAA4D,GAAA;MACA,KAAApC,OAAA;IACA;IACAsC,cAAAC,GAAA;MACA,KAAApD,QAAA,CAAAS,QAAA,GAAA2C,GAAA,CAAAnE,IAAA,CAAAS,GAAA;IACA;IAEA2D,UAAAC,IAAA;MACA,KAAAxD,UAAA,GAAAwD,IAAA;MACA,KAAAvD,aAAA;IACA;IACAwD,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAAjC,IAAA;MACA,KAAAmC,UAAA;QACA,KAAA9B,QAAA,CAAAgC,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAA9B,KAAA;MACAA,KAAA,CAAAG,UAAA,gCAAAqB,IAAA,EAAAhC,IAAA,CAAAE,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAK,KAAA,CAAA9B,QAAA,CAAA4D,QAAA;UAEA9B,KAAA,CAAAJ,QAAA,CAAAmC,OAAA;QACA;UACA/B,KAAA,CAAAJ,QAAA,CAAAgC,KAAA,CAAAlC,IAAA,CAAAuB,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}