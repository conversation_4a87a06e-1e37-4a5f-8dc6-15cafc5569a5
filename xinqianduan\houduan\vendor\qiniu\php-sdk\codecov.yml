codecov:
  ci:
    - prow.qiniu.io       # prow 里面运行需添加，其他 CI 不要
  require_ci_to_pass: no  # 改为 no，否则 codecov 会等待其他 GitHub 上所有 CI 通过才会留言。
 
github_checks:              #关闭github checks
  annotations: false

comment:
  layout: "reach, diff, flags, files"
  behavior: new           # 默认是更新旧留言，改为 new，删除旧的，增加新的。
  require_changes: false  # if true: only post the comment if coverage changes
  require_base: no        # [yes :: must have a base report to post]
  require_head: yes       # [yes :: must have a head report to post]
  branches:               # branch names that can post comment
      - "master"
 
coverage:
  status:                                 # 评判 pr 通过的标准
      patch: off
      project:                            # project 统计所有代码x
          default:
            # basic
            target: 85%                  # 总体通过标准
            threshold: 3%                 # 允许单次下降的幅度
            base: auto
            if_not_found: success
            if_ci_failed: error
