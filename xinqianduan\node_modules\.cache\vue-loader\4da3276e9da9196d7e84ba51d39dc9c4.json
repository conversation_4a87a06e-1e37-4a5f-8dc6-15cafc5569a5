{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue?vue&type=style&index=0&id=4b11a6a1&prod&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\taocan\\taocan.vue", "mtime": 1748617950297}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucGFja2FnZS1tYW5hZ2VtZW50LWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSAxMTBweCk7DQp9DQoNCi8qIOmhtemdouWktOmDqOagt+W8jyAqLw0KLnBhZ2UtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBwYWRkaW5nOiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBjb2xvcjogd2hpdGU7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpOw0KfQ0KDQouaGVhZGVyLWxlZnQgaDIucGFnZS10aXRsZSB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTJweDsNCn0NCg0KLnBhZ2Utc3VidGl0bGUgew0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuOTsNCiAgbWFyZ2luOiAwOw0KfQ0KDQoucmVmcmVzaC1idG4gew0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykgIWltcG9ydGFudDsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7DQogIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnJlZnJlc2gtYnRuOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsNCn0NCg0KLyog57uf6K6h5Y2h54mH5qC35byPICovDQouc3RhdHMtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5zdGF0LWNhcmQgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjRweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwOw0KfQ0KDQouc3RhdC1jYXJkOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpOw0KICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQp9DQoNCi5zdGF0LWljb24gew0KICB3aWR0aDogNjBweDsNCiAgaGVpZ2h0OiA2MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXJpZ2h0OiAxNnB4Ow0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnRvdGFsLWljb24gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTsNCn0NCg0KLnByaWNlLWljb24gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDNlOTdiLCAjMzhmOWQ3KTsNCn0NCg0KLnByZW1pdW0taWNvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIsICNmNTU3NmMpOw0KfQ0KDQouZHVyYXRpb24taWNvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0ZmFjZmUsICMwMGYyZmUpOw0KfQ0KDQouc3RhdC1jb250ZW50IHsNCiAgZmxleDogMTsNCn0NCg0KLnN0YXQtbnVtYmVyIHsNCiAgZm9udC1zaXplOiAyOHB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KfQ0KDQouc3RhdC1sYWJlbCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLnN0YXQtY2hhbmdlIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDRweDsNCn0NCg0KLnN0YXQtY2hhbmdlLnBvc2l0aXZlIHsNCiAgY29sb3I6ICMyN2FlNjA7DQp9DQoNCi8qIOWNoeeJh+agt+W8jyAqLw0KLnNlYXJjaC1jYXJkLCAucGFja2FnZS1jYXJkIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLmNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAxNnB4IDA7DQp9DQoNCi5jYXJkLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi8qIOaQnOe0ouWMuuWfn+agt+W8jyAqLw0KLnNlYXJjaC1zZWN0aW9uIHsNCiAgcGFkZGluZzogMTZweCAwOw0KfQ0KDQouc2VhcmNoLWZvcm0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMTZweDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnNlYXJjaC1mb3JtIC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQovKiDop4blm77mjqfliLYgKi8NCi52aWV3LWNvbnRyb2xzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQovKiDlpZfppJDnvZHmoLzop4blm74gKi8NCi5wYWNrYWdlLWdyaWQgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMjBweCwgMWZyKSk7DQogIGdhcDogMjBweDsNCiAgcGFkZGluZzogMTZweCAwOw0KfQ0KDQoucGFja2FnZS1pdGVtIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOGU4ZTg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDYpOw0KfQ0KDQoucGFja2FnZS1pdGVtOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpOw0KICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xMik7DQogIGJvcmRlci1jb2xvcjogIzQwOUVGRjsNCn0NCg0KLnBhY2thZ2UtaGVhZGVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7DQogIGNvbG9yOiB3aGl0ZTsNCiAgcGFkZGluZzogMjBweDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoucGFja2FnZS10aXRsZSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luOiAwOw0KfQ0KDQoucGFja2FnZS1wcmljZSB7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgY29sb3I6ICNmZmQwNGI7DQp9DQoNCi5wYWNrYWdlLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQoucGFja2FnZS1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouaW5mby1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA2cHg7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5wYWNrYWdlLWRlc2Mgew0KICBjb2xvcjogIzY2NjsNCiAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgbWluLWhlaWdodDogNDBweDsNCiAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQogIC13ZWJraXQtbGluZS1jbGFtcDogMjsNCiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLnBhY2thZ2UtZmVhdHVyZXMgew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KfQ0KDQouZmVhdHVyZS10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCn0NCg0KLmZlYXR1cmUtbGlzdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA2cHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5mZWF0dXJlLXRhZyB7DQogIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQogIGNvbG9yOiAjMDM2OWExOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTBmMmZlOw0KfQ0KDQoubW9yZS1zZXJ2aWNlcyB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLnBhY2thZ2UtZm9vdGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAwIDIwcHggMjBweDsNCn0NCg0KLnBhY2thZ2UtbWV0YSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5OTk7DQp9DQoNCi5wYWNrYWdlLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDhweDsNCn0NCg0KLyog6KGo5qC86KeG5Zu+5qC35byPICovDQoubW9kZXJuLXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoubW9kZXJuLXRhYmxlIC5lbC10YWJsZV9faGVhZGVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmJmYzsNCn0NCg0KLm1vZGVybi10YWJsZSAuZWwtdGFibGVfX2hlYWRlciB0aCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZiZmMgIWltcG9ydGFudDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZThlOGU4Ow0KICBwYWRkaW5nOiAxNnB4IDA7DQp9DQoNCi5tb2Rlcm4tdGFibGUgLmVsLXRhYmxlX19ib2R5IHRkIHsNCiAgcGFkZGluZzogMTZweCAwOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y1ZjVmNTsNCn0NCg0KLm1vZGVybi10YWJsZSAuZWwtdGFibGVfX3Jvdzpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmYgIWltcG9ydGFudDsNCn0NCg0KLnRhYmxlLXBhY2thZ2UtaW5mbyB7DQogIHBhZGRpbmc6IDhweCAwOw0KfQ0KDQoudGFibGUtcGFja2FnZS10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiA0cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLnRhYmxlLXBhY2thZ2UtZGVzYyB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi5wcmljZS1kaXNwbGF5IHsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNzAwOw0KICBjb2xvcjogI2U3NGMzYzsNCn0NCg0KLnRpbWUtaW5mbyB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi8qIOaTjeS9nOaMiemSruagt+W8jyAqLw0KLmFjdGlvbi1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgZ2FwOiA0cHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5lZGl0LWJ0biB7DQogIGNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQp9DQoNCi5kZWxldGUtYnRuIHsNCiAgY29sb3I6ICNGNTZDNkMgIWltcG9ydGFudDsNCn0NCg0KLmVkaXQtYnRuOmhvdmVyLCAuZGVsZXRlLWJ0bjpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOWIhumhteagt+W8jyAqLw0KLnBhZ2luYXRpb24td3JhcHBlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIG1hcmdpbi10b3A6IDI0cHg7DQogIHBhZGRpbmctdG9wOiAxNnB4Ow0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLyog57yW6L6R5a+56K+d5qGG5qC35byPICovDQouZWRpdC1kaWFsb2cgLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOacjeWKoemAieaLqeWMuuWfnyAqLw0KLnNlcnZpY2Utc2VsZWN0aW9uIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZThlODsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KfQ0KDQouc2VydmljZS10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5zZXJ2aWNlLWxpc3Qgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDEycHg7DQp9DQoNCi5zZXJ2aWNlLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIHBhZGRpbmc6IDEycHg7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQp9DQoNCi5zZXJ2aWNlLWl0ZW06aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmY7DQp9DQoNCi5zZXJ2aWNlLWNoZWNrYm94IHsNCiAgZmxleDogMTsNCn0NCg0KLnNlcnZpY2UtaW5wdXQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmlucHV0LXN1ZmZpeCB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM3ZjhjOGQ7DQp9DQoNCi5zZXJ2aWNlLXVubGltaXRlZCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5wYWNrYWdlLW1hbmFnZW1lbnQtY29udGFpbmVyIHsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICB9DQogIA0KICAucGFnZS1oZWFkZXIgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGdhcDogMTZweDsNCiAgfQ0KICANCiAgLnN0YXRzLXNlY3Rpb24gLmVsLWNvbCB7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgfQ0KICANCiAgLnNlYXJjaC1mb3JtIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOw0KICB9DQogIA0KICAuc2VhcmNoLWZvcm0gLmVsLWZvcm0taXRlbSB7DQogIHdpZHRoOiAxMDAlOw0KICB9DQogIA0KICAuY2FyZC1oZWFkZXIgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7DQogICAgZ2FwOiAxMnB4Ow0KICB9DQogIA0KICAucGFja2FnZS1ncmlkIHsNCiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsNCiAgICBnYXA6IDE2cHg7DQogIH0NCiAgDQogIC5wYWNrYWdlLWhlYWRlciB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDhweDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIH0NCiAgDQogIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgfQ0KICANCiAgLnNlcnZpY2UtaXRlbSB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsNCiAgICBnYXA6IDhweDsNCiAgfQ0KICANCiAgLnNlcnZpY2UtaW5wdXQgew0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["taocan.vue"], "names": [], "mappings": ";AAkoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "taocan.vue", "sourceRoot": "src/views/pages/taocan", "sourcesContent": ["<template>\r\n  <div class=\"package-management-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-box\"></i>\r\n          套餐类型管理\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务套餐产品和价格配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"getData\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-box\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">套餐总数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +12%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon price-icon\">\r\n              <i class=\"el-icon-money\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">¥{{ averagePrice }}</div>\r\n              <div class=\"stat-label\">平均价格</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon premium-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ premiumPackages }}</div>\r\n              <div class=\"stat-label\">高端套餐</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +8%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon duration-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ averageYear }}年</div>\r\n              <div class=\"stat-label\">平均年限</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 稳定\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新增套餐\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入套餐名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n        >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"getData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"价格范围\">\r\n            <el-input-number \r\n              v-model=\"search.minPrice\" \r\n              placeholder=\"最低价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n            <span style=\"margin: 0 8px\">-</span>\r\n            <el-input-number \r\n              v-model=\"search.maxPrice\" \r\n              placeholder=\"最高价格\"\r\n              :min=\"0\"\r\n              style=\"width: 120px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 套餐展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"package-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          套餐列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"package-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"pkg in filteredPackages\" \r\n          :key=\"pkg.id\"\r\n          class=\"package-item\"\r\n        >\r\n          <div class=\"package-header\">\r\n            <div class=\"package-title\">{{ pkg.title }}</div>\r\n            <div class=\"package-price\">¥{{ pkg.price }}</div>\r\n          </div>\r\n          \r\n          <div class=\"package-content\">\r\n            <div class=\"package-info\">\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ pkg.year }}年服务</span>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-sort\"></i>\r\n                <span>排序: {{ pkg.sort }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"package-desc\">\r\n              {{ pkg.desc || '暂无描述' }}\r\n            </div>\r\n            \r\n            <div class=\"package-features\" v-if=\"pkg.services && pkg.services.length > 0\">\r\n              <div class=\"feature-title\">包含服务:</div>\r\n              <div class=\"feature-list\">\r\n                <el-tag \r\n                  v-for=\"service in pkg.services.slice(0, 3)\" \r\n                  :key=\"service.id\"\r\n                  size=\"mini\"\r\n                  class=\"feature-tag\"\r\n                >\r\n                  {{ service.name }}\r\n                </el-tag>\r\n                <span v-if=\"pkg.services.length > 3\" class=\"more-services\">\r\n                  +{{ pkg.services.length - 3 }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"package-footer\">\r\n            <div class=\"package-meta\">\r\n              <span class=\"create-time\">{{ formatDate(pkg.create_time) }}</span>\r\n            </div>\r\n            <div class=\"package-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(pkg.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, pkg.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n          :data=\"filteredPackages\" \r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"套餐信息\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-package-info\">\r\n                <div class=\"table-package-title\">{{ scope.row.title }}</div>\r\n                <div class=\"table-package-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"价格\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"price-display\">¥{{ scope.row.price }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"year\" label=\"年限\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"info\" size=\"small\">{{ scope.row.year }}年</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" />\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐名称\" prop=\"title\">\r\n              <el-input \r\n                v-model=\"ruleForm.title\" \r\n                placeholder=\"请输入套餐名称\"\r\n                autocomplete=\"off\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"套餐价格\" prop=\"price\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.price\"\r\n                :min=\"0\"\r\n                :max=\"999999\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入价格\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务年限\" prop=\"year\">\r\n              <el-input-number\r\n            v-model=\"ruleForm.year\"\r\n                :min=\"1\"\r\n                :max=\"10\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入年限\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\">\r\n              <el-input-number\r\n                v-model=\"ruleForm.sort\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"数字越小排序越靠前\"\r\n              />\r\n        </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-form-item label=\"套餐内容\" prop=\"good\">\r\n          <div class=\"service-selection\">\r\n            <div class=\"service-title\">选择包含的服务类型:</div>\r\n            <div class=\"service-list\">\r\n              <div \r\n              v-for=\"(item, index) in types\"\r\n              :key=\"index\"\r\n                class=\"service-item\"\r\n            >\r\n                <div class=\"service-checkbox\">\r\n                  <el-checkbox v-model=\"item.checked\" :label=\"item.id\">\r\n                  {{ item.title }}\r\n                </el-checkbox>\r\n                </div>\r\n                <div class=\"service-input\" v-if=\"item.is_num == 1 && item.checked\">\r\n                <el-input-number\r\n                  v-model=\"item.value\"\r\n                  :min=\"1\"\r\n                  :max=\"999\"\r\n                    size=\"small\"\r\n                    placeholder=\"次数\"\r\n                  />\r\n                  <span class=\"input-suffix\">次</span>\r\n                </div>\r\n                <div class=\"service-unlimited\" v-else-if=\"item.checked\">\r\n                  <el-tag size=\"small\" type=\"success\">不限次数</el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"套餐描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入套餐详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"PackageManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      tableData: [],\r\n      loading: true,\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        price: \"\",\r\n        year: \"\",\r\n        desc: \"\",\r\n        sort: 0,\r\n        good: [],\r\n        num: [],\r\n      },\r\n      num: 0,\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写套餐名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        year: [\r\n          {\r\n            required: true,\r\n            message: \"请填写年限\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      dialogFormVisible: false,\r\n      formLabelWidth: \"120px\",\r\n      url: \"/taocan/\",\r\n      types: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    averagePrice() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return '0';\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);\r\n      return Math.round(total / this.tableData.length).toLocaleString();\r\n    },\r\n    premiumPackages() {\r\n      return Array.isArray(this.tableData) ? this.tableData.filter(item => parseFloat(item.price) > 10000).length : 0;\r\n    },\r\n    averageYear() {\r\n      if (!Array.isArray(this.tableData) || this.tableData.length === 0) return 1;\r\n      const total = this.tableData.reduce((sum, item) => sum + (parseInt(item.year) || 1), 0);\r\n      return Math.round(total / this.tableData.length);\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑套餐' : '新增套餐';\r\n    },\r\n    filteredPackages() {\r\n      if (!Array.isArray(this.tableData)) return [];\r\n      let filtered = this.tableData;\r\n\r\n      // 关键词搜索\r\n      if (this.search.keyword) {\r\n        const keyword = this.search.keyword.toLowerCase();\r\n        filtered = filtered.filter(item =>\r\n          (item.title && item.title.toLowerCase().includes(keyword)) ||\r\n          (item.desc && item.desc.toLowerCase().includes(keyword))\r\n        );\r\n      }\r\n\r\n      // 价格范围筛选\r\n      if (this.search.minPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) >= this.search.minPrice);\r\n      }\r\n\r\n      if (this.search.maxPrice !== null) {\r\n        filtered = filtered.filter(item => parseFloat(item.price) <= this.search.maxPrice);\r\n      }\r\n\r\n      return filtered;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          price: \"\",\r\n          year: \"\",\r\n          desc: \"\",\r\n          sort: 0,\r\n          good: [],\r\n          num: [],\r\n        };\r\n        _this.getTypes();\r\n      }\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n          _this.types = _this.ruleForm.num;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.tableData.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    getTypes() {\r\n      this.postRequest(\"/type/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.types = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.tableData = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        minPrice: null,\r\n        maxPrice: null\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    saveData() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n          // 处理服务类型选择\r\n          this.ruleForm.good = this.types\r\n            .filter(type => type.checked)\r\n            .map(type => type.id);\r\n          \r\n          // 模拟保存操作\r\n          setTimeout(() => {\r\n            this.saveLoading = false;\r\n            this.dialogFormVisible = false;\r\n            this.$message.success('保存成功');\r\n            this.getData();\r\n          }, 1000);\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.package-management-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.price-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.premium-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.duration-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .package-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 套餐网格视图 */\r\n.package-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.package-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.package-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.package-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.package-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.package-price {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #ffd04b;\r\n}\r\n\r\n.package-content {\r\n  padding: 20px;\r\n}\r\n\r\n.package-info {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.package-desc {\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  min-height: 40px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.package-features {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.feature-title {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.feature-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  align-items: center;\r\n}\r\n\r\n.feature-tag {\r\n  background: #f0f9ff;\r\n  color: #0369a1;\r\n  border: 1px solid #e0f2fe;\r\n}\r\n\r\n.more-services {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  font-style: italic;\r\n}\r\n\r\n.package-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.package-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.package-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-package-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-package-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-package-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.price-display {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #e74c3c;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 服务选择区域 */\r\n.service-selection {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  background: #fafafa;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px;\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-item:hover {\r\n  border-color: #409EFF;\r\n  background: #f8f9ff;\r\n}\r\n\r\n.service-checkbox {\r\n  flex: 1;\r\n}\r\n\r\n.service-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.input-suffix {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.service-unlimited {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .package-management-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n  width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .package-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .package-header {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-input {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}