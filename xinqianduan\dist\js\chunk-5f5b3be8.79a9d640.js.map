{"version": 3, "sources": ["webpack:///./src/views/pages/yuangong/index.vue?e785", "webpack:///./src/views/pages/yuangong/index.vue", "webpack:///src/views/pages/yuangong/index.vue", "webpack:///./src/views/pages/yuangong/index.vue?e055", "webpack:///./src/views/pages/yuangong/index.vue?aa26"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "$event", "editData", "_v", "refulsh", "nativeOn", "type", "indexOf", "_k", "keyCode", "key", "searchData", "apply", "arguments", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "zhiwei_id", "_l", "zhi<PERSON>s", "zhiwei", "id", "title", "status", "clearSearch", "exportData", "_s", "total", "adminCount", "activeCount", "newCount", "viewMode", "directives", "name", "rawName", "loading", "list", "scopedSlots", "_u", "fn", "scope", "row", "pic_path", "showImage", "showEmployeeDetail", "account", "getPositionTagType", "zhiwei_title", "phone", "changeStatus", "create_time", "showEmployeeEdit", "chong<PERSON>", "delData", "$index", "_e", "employee", "size", "handleSizeChange", "handleCurrentChange", "class", "detailPanelVisible", "closeDetailPanel", "currentEmployee", "isViewMode", "switchToEditMode", "saving", "saveEmployeeData", "cancelEdit", "ref", "detailRules", "staticStyle", "join_date", "handleAvatarSuccess", "beforeUpload", "removeAvatar", "update_time", "resetPassword", "deleteEmployee", "dialogFormVisible", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "item", "index", "handleSuccess", "delImage", "saveData", "dialogVisible", "show_image", "staticRenderFns", "components", "data", "allSize", "page", "url", "info", "field", "originalEmployee", "required", "message", "trigger", "pattern", "computed", "filter", "includes", "length", "currentMonth", "Date", "getMonth", "itemMonth", "mounted", "getData", "methods", "position", "$message", "success", "_employee$create_time", "split", "getZhiwei", "_employee$create_time2", "$nextTick", "$refs", "detailForm", "resetFields", "validate", "valid", "setTimeout", "findIndex", "find", "z", "newEmployee", "now", "toLocaleString", "unshift", "res", "$confirm", "confirmButtonText", "cancelButtonText", "then", "splice", "changeField", "postRequest", "resp", "code", "catch", "getInfo", "_this", "getRequest", "deleteRequest", "$router", "go", "msg", "val", "file", "isTypeTrue", "test", "error", "fileName", "component"], "mappings": "2IAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,UAAUE,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAAS,MAAM,CAACT,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACE,YAAY,cAAc<PERSON>,MAAM,CAAC,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIW,UAAU,CAACX,EAAIU,GAAG,WAAW,OAAOR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,iBAAiB,UAAY,IAAIM,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAIA,EAAOK,KAAKC,QAAQ,QAAQd,EAAIe,GAAGP,EAAOQ,QAAQ,QAAQ,GAAGR,EAAOS,IAAI,SAAgB,KAAYjB,EAAIkB,WAAWC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOtB,EAAIuB,OAAOC,QAASC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAAC1B,EAAG,IAAI,CAACE,YAAY,gCAAgCE,MAAM,CAAC,KAAO,UAAUuB,KAAK,cAAc,GAAG3B,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOO,UAAWL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,YAAaG,IAAME,WAAW,qBAAqB5B,EAAI+B,GAAI/B,EAAIgC,SAAS,SAASC,GAAQ,OAAO/B,EAAG,YAAY,CAACe,IAAIgB,EAAOC,GAAG5B,MAAM,CAAC,MAAQ2B,EAAOE,MAAM,MAAQF,EAAOC,SAAQ,IAAI,GAAGhC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,QAAQR,EAAG,YAAY,CAACE,YAAY,gBAAgBE,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAIuB,OAAOa,OAAQX,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAIuB,OAAQ,SAAUG,IAAME,WAAW,kBAAkB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,KAAKJ,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,IAAI,KAAKJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAIkB,aAAa,CAAClB,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQP,EAAIqC,cAAc,CAACrC,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,oBAAoBC,GAAG,CAAC,MAAQP,EAAIsC,aAAa,CAACtC,EAAIU,GAAG,WAAW,QAAQ,GAAGR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAIwC,UAAUtC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,gBAAgBR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,yBAAyBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAIyC,eAAevC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,eAAeR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACE,YAAY,2BAA2BF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAI0C,gBAAgBxC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,gBAAgBR,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAI2C,aAAazC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,iBAAiB,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACE,YAAY,aAAaE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,UAAjBN,EAAI4C,SAAuB,UAAY,GAAG,KAAO,eAAe,KAAO,SAASrC,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAI4C,SAAW,WAAW,CAAC5C,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAwB,SAAjBN,EAAI4C,SAAsB,UAAY,GAAG,KAAO,iBAAiB,KAAO,SAASrC,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAI4C,SAAW,UAAU,CAAC5C,EAAIU,GAAG,aAAa,IAAI,KAAuB,UAAjBV,EAAI4C,SAAsB1C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,WAAW,CAAC2C,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYzB,MAAOtB,EAAIgD,QAASpB,WAAW,YAAYxB,YAAY,iBAAiBE,MAAM,CAAC,KAAON,EAAIiD,KAAK,OAAS,KAAK,CAAC/C,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,KAAK,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACE,YAAY,kBAAkBE,MAAM,CAAC,IAAM+C,EAAMC,IAAIC,SAAS,KAAO,IAAI3C,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAOR,EAAIwD,UAAUH,EAAMC,IAAIC,aAAa,CAACrD,EAAG,IAAI,CAACE,YAAY,0BAA0B,OAAO,MAAK,EAAM,cAAcF,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,OAAO4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,0BAA0BG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIyD,mBAAmBJ,EAAMC,QAAQ,CAACtD,EAAIU,GAAGV,EAAIuC,GAAGc,EAAMC,IAAInB,UAAUjC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGc,EAAMC,IAAII,kBAAkB,MAAK,EAAM,cAAcxD,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,SAAS,CAACI,MAAM,CAAC,KAAON,EAAI2D,mBAAmBN,EAAMC,IAAIM,cAAc,KAAO,UAAU,CAAC5D,EAAIU,GAAG,IAAIV,EAAIuC,GAAGc,EAAMC,IAAIM,cAAgB,OAAO,WAAW,MAAK,EAAM,cAAc1D,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIU,GAAG,IAAIV,EAAIuC,GAAGc,EAAMC,IAAIO,OAAO,WAAW,MAAK,EAAM,cAAc3D,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,GAAGC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOR,EAAI8D,aAAaT,EAAMC,OAAOjC,MAAM,CAACC,MAAO+B,EAAMC,IAAIlB,OAAQX,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK0B,EAAMC,IAAK,SAAU5B,IAAME,WAAW,0BAA0B,MAAK,EAAM,cAAc1B,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,IAAIV,EAAIuC,GAAGc,EAAMC,IAAIS,aAAa,WAAW,MAAK,EAAM,cAAc7D,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAU4C,YAAYlD,EAAImD,GAAG,CAAC,CAAClC,IAAI,UAAUmC,GAAG,SAASC,GAAO,MAAO,CAACnD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIgE,iBAAiBX,EAAMC,QAAQ,CAACtD,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,cAAc,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIiE,SAASZ,EAAMC,IAAIpB,OAAO,CAAClC,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIkE,QAAQb,EAAMc,OAAQd,EAAMC,IAAIpB,OAAO,CAAClC,EAAIU,GAAG,WAAW,OAAO,MAAK,EAAM,eAAe,IAAI,GAAGV,EAAIoE,KAAuB,SAAjBpE,EAAI4C,SAAqB1C,EAAG,MAAM,CAAC2C,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYzB,MAAOtB,EAAIgD,QAASpB,WAAW,YAAYxB,YAAY,aAAa,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,OAAS,KAAKN,EAAI+B,GAAI/B,EAAIiD,MAAM,SAASoB,GAAU,OAAOnE,EAAG,SAAS,CAACe,IAAIoD,EAASnC,GAAG9B,YAAY,oBAAoBE,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,IAAM+D,EAASd,SAAS,KAAO,IAAI3C,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAOR,EAAIwD,UAAUa,EAASd,aAAa,CAACrD,EAAG,IAAI,CAACE,YAAY,0BAA0B,GAAGF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsBG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIyD,mBAAmBY,MAAa,CAACrE,EAAIU,GAAGV,EAAIuC,GAAG8B,EAASlC,UAAUjC,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACI,MAAM,CAAC,KAAON,EAAI2D,mBAAmBU,EAAST,cAAc,KAAO,SAAS,CAAC5D,EAAIU,GAAG,IAAIV,EAAIuC,GAAG8B,EAAST,cAAgB,OAAO,QAAQ,KAAK1D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,EAAE,KAAO,SAASC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAOR,EAAI8D,aAAaO,KAAYhD,MAAM,CAACC,MAAO+C,EAASjC,OAAQX,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK0C,EAAU,SAAU3C,IAAME,WAAW,sBAAsB,KAAK1B,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIU,GAAGV,EAAIuC,GAAG8B,EAASR,YAAY3D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIU,GAAGV,EAAIuC,GAAG8B,EAASX,cAAcxD,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIU,GAAGV,EAAIuC,GAAG8B,EAASN,sBAAsB7D,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIgE,iBAAiBK,MAAa,CAACrE,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,cAAc,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIiE,SAASI,EAASnC,OAAO,CAAClC,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQ,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAIkE,QAAQlE,EAAIiD,KAAKnC,QAAQuD,GAAWA,EAASnC,OAAO,CAAClC,EAAIU,GAAG,WAAW,UAAS,IAAI,GAAGV,EAAIoE,KAAKlE,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,gBAAgB,CAACE,YAAY,aAAaE,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,GAAI,IAAI,YAAYN,EAAIsE,KAAK,OAAS,0CAA0C,MAAQtE,EAAIwC,OAAOjC,GAAG,CAAC,cAAcP,EAAIuE,iBAAiB,iBAAiBvE,EAAIwE,wBAAwB,MAAM,GAAGtE,EAAG,MAAM,CAACE,YAAY,wBAAwBqE,MAAM,CAAE,aAAczE,EAAI0E,qBAAsB,CAACxE,EAAG,MAAM,CAACE,YAAY,gBAAgBG,GAAG,CAAC,MAAQP,EAAI2E,oBAAoBzE,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAmBJ,EAAI4E,gBAAgB1C,GAAiClC,EAAI6E,WAAY3E,EAAG,OAAO,CAACF,EAAIU,GAAG,UAAUR,EAAG,OAAO,CAACF,EAAIU,GAAG,UAA3FR,EAAG,OAAO,CAACF,EAAIU,GAAG,YAAqFR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAAEJ,EAAI6E,YAAc7E,EAAI4E,gBAAgB1C,GAAIhC,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBC,GAAG,CAAC,MAAQP,EAAI8E,mBAAmB,CAAC9E,EAAIU,GAAG,UAAUV,EAAIoE,KAAOpE,EAAI6E,WAAqK7E,EAAIoE,KAA7JlE,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,QAAUN,EAAI+E,OAAO,KAAO,iBAAiBxE,GAAG,CAAC,MAAQP,EAAIgF,mBAAmB,CAAChF,EAAIU,GAAG,WAAqBV,EAAI6E,YAAc7E,EAAI4E,gBAAgB1C,GAAIhC,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,wBAAwBC,GAAG,CAAC,MAAQP,EAAIiF,aAAa,CAACjF,EAAIU,GAAG,YAAYV,EAAIoE,KAAKlE,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,iBAAiBC,GAAG,CAAC,MAAQP,EAAI2E,mBAAmB,CAAC3E,EAAIU,GAAG,WAAW,KAAKR,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,UAAU,CAACgF,IAAI,aAAa9E,YAAY,gBAAgBE,MAAM,CAAC,MAAQN,EAAI4E,gBAAgB,MAAQ5E,EAAImF,YAAY,cAAc,UAAU,CAACjF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,UAAU,SAAWN,EAAI6E,WAAW,UAAY,IAAIxD,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBzC,MAAOV,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,QAASlD,IAAME,WAAW,4BAA4B,IAAI,GAAG1B,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,UAAU,SAAWN,EAAI6E,WAAW,UAAY,IAAIxD,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBf,MAAOpC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,QAASlD,IAAME,WAAW,4BAA4B,IAAI,KAAK1B,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,YAAY,CAACJ,EAAG,WAAW,CAACI,MAAM,CAAC,YAAc,UAAU,SAAWN,EAAI6E,WAAW,UAAY,IAAIxD,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBlB,QAASjC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,UAAWlD,IAAME,WAAW,4BAA4B,CAAG5B,EAAI4E,gBAAgB1C,IAAOlC,EAAI6E,WAAkE7E,EAAIoE,KAA1DlE,EAAG,WAAW,CAAC2B,KAAK,UAAU,CAAC7B,EAAIU,GAAG,iBAA0B,IAAI,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,WAAW,CAACJ,EAAG,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE,iBAAiB,EAAE,SAAWN,EAAI6E,WAAW,cAAc,KAAK,gBAAgB,MAAMxD,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBxC,OAAQX,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,SAAUlD,IAAME,WAAW,6BAA6B,IAAI,OAAO1B,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,KAAO,cAAc,CAAGN,EAAI6E,WAAuZ3E,EAAG,WAAW,CAACkF,YAAY,CAAC,MAAQ,QAAQ9E,MAAM,CAAC,MAAQN,EAAI4E,gBAAgBhB,cAAgB,MAAM,SAAW,MAA3f1D,EAAG,YAAY,CAACkF,YAAY,CAAC,MAAQ,QAAQ9E,MAAM,CAAC,YAAc,QAAQ,WAAa,GAAG,UAAY,IAAIe,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgB9C,UAAWL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,YAAalD,IAAME,WAAW,8BAA8B5B,EAAI+B,GAAI/B,EAAIgC,SAAS,SAASC,GAAQ,OAAO/B,EAAG,YAAY,CAACe,IAAIgB,EAAOC,GAAG5B,MAAM,CAAC,MAAQ2B,EAAOE,MAAM,MAAQF,EAAOC,SAAQ,IAA0H,IAAI,GAAGhC,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAG,iBAAiB,CAACkF,YAAY,CAAC,MAAQ,QAAQ9E,MAAM,CAAC,KAAO,OAAO,YAAc,SAAS,SAAWN,EAAI6E,WAAW,SAAW7E,EAAI6E,WAAW,OAAS,aAAa,eAAe,cAAcxD,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBS,UAAW5D,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,YAAalD,IAAME,WAAW,gCAAgC,IAAI,OAAO1B,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,iBAAiBE,MAAM,CAAC,IAAMN,EAAI4E,gBAAgBrB,SAAS,KAAO,KAAK3C,SAAS,CAAC,MAAQ,SAASJ,GAAQ,OAAOR,EAAIwD,UAAUxD,EAAI4E,gBAAgBrB,aAAa,CAACrD,EAAG,IAAI,CAACE,YAAY,0BAA0B,GAAGF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAGJ,EAAI6E,WAAgU7E,EAAIoE,KAAxTlE,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,KAAO,aAAa,CAACJ,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,QAAQ,SAAW,IAAIe,MAAM,CAACC,MAAOtB,EAAI4E,gBAAgBrB,SAAU9B,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI4E,gBAAiB,WAAYlD,IAAME,WAAW,+BAA+B,GAAc5B,EAAI6E,WAA+tB3E,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAEJ,EAAI4E,gBAAgBrB,SAAUrD,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIwD,UAAUxD,EAAI4E,gBAAgBrB,aAAa,CAACvD,EAAIU,GAAG,YAAYR,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIU,GAAG,WAAW,GAA1gCR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,kBAAkBE,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAaN,EAAIsF,oBAAoB,gBAAgBtF,EAAIuF,eAAe,CAACrF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,mBAAmB,CAACN,EAAIU,GAAG,aAAa,GAAIV,EAAI4E,gBAAgBrB,SAAUrD,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIwD,UAAUxD,EAAI4E,gBAAgBrB,aAAa,CAACvD,EAAIU,GAAG,UAAUV,EAAIoE,KAAMpE,EAAI4E,gBAAgBrB,SAAUrD,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAIwF,eAAe,CAACxF,EAAIU,GAAG,UAAUV,EAAIoE,MAAM,GAA+TpE,EAAI6E,WAAgF7E,EAAIoE,KAAxElE,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAG,yBAAkC,OAAQV,EAAI4E,gBAAgB1C,GAAIhC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,YAAYR,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,WAAWR,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAI4E,gBAAgBb,kBAAkB7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,WAAWR,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAI4E,gBAAgBa,aAAe,WAAWvF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAG,WAAWR,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIU,GAAGV,EAAIuC,GAAGvC,EAAI4E,gBAAgB1C,WAAalC,EAAI6E,WAA6T7E,EAAIoE,KAArTlE,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,eAAeC,GAAG,CAAC,MAAQP,EAAI0F,gBAAgB,CAAC1F,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQ,KAAO,kBAAkBC,GAAG,CAAC,MAAQP,EAAI2F,iBAAiB,CAAC3F,EAAIU,GAAG,aAAa,KAAcV,EAAIoE,QAAQ,OAAOlE,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,QAAUnC,EAAI4F,kBAAkB,wBAAuB,EAAM,MAAQ,OAAOrF,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAI4F,kBAAkBpF,KAAU,CAACN,EAAG,UAAU,CAACgF,IAAI,WAAW5E,MAAM,CAAC,MAAQN,EAAI6F,SAAS,MAAQ7F,EAAI8F,QAAQ,CAAC5F,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,cAAcN,EAAI+F,eAAe,KAAO,cAAc,CAAC7F,EAAG,YAAY,CAACI,MAAM,CAAC,YAAc,MAAM,WAAa,IAAIe,MAAM,CAACC,MAAOtB,EAAI6F,SAAS/D,UAAWL,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI6F,SAAU,YAAanE,IAAME,WAAW,uBAAuB,CAAC1B,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,CAACN,EAAIU,GAAG,SAASV,EAAI+B,GAAI/B,EAAIgC,SAAS,SAASgE,EAAKC,GAAO,OAAO/F,EAAG,YAAY,CAACe,IAAIgF,EAAM3F,MAAM,CAAC,MAAQ0F,EAAK7D,MAAM,MAAQ6D,EAAK9D,UAAS,IAAI,GAAGhC,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,cAAcnC,EAAI+F,eAAe,KAAO,UAAU,CAAC7F,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,OAAOe,MAAM,CAACC,MAAOtB,EAAI6F,SAAS1D,MAAOV,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI6F,SAAU,QAASnE,IAAME,WAAW,qBAAqB,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,cAAcnC,EAAI+F,eAAe,KAAO,UAAU,CAAC7F,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,OAAOe,MAAM,CAACC,MAAOtB,EAAI6F,SAAShC,MAAOpC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI6F,SAAU,QAASnE,IAAME,WAAW,qBAAqB,GAAG1B,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,cAAcnC,EAAI+F,eAAe,KAAO,YAAY,CAAC7F,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,OAAOe,MAAM,CAACC,MAAOtB,EAAI6F,SAASnC,QAASjC,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI6F,SAAU,UAAWnE,IAAME,WAAW,qBAAqB,CAAC1B,EAAG,WAAW,CAAC2B,KAAK,UAAU,CAAC7B,EAAIU,GAAG,iBAAiB,IAAI,GAAGR,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,cAAcN,EAAI+F,eAAe,KAAO,aAAa,CAAC7F,EAAG,WAAW,CAACE,YAAY,WAAWE,MAAM,CAAC,UAAW,GAAMe,MAAM,CAACC,MAAOtB,EAAI6F,SAAStC,SAAU9B,SAAS,SAAUC,GAAM1B,EAAI2B,KAAK3B,EAAI6F,SAAU,WAAYnE,IAAME,WAAW,uBAAuB1B,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACA,EAAG,YAAY,CAACI,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAaN,EAAIkG,cAAc,gBAAgBlG,EAAIuF,eAAe,CAACvF,EAAIU,GAAG,WAAW,GAAIV,EAAI6F,SAAStC,SAAUrD,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIwD,UAAUxD,EAAI6F,SAAStC,aAAa,CAACvD,EAAIU,GAAG,SAASV,EAAIoE,KAAMpE,EAAI6F,SAAStC,SAAUrD,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAImG,SAASnG,EAAI6F,SAAStC,SAAU,eAAe,CAACvD,EAAIU,GAAG,QAAQV,EAAIoE,MAAM,GAAGlE,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIU,GAAG,oBAAoB,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUuB,KAAK,UAAU,CAAC3B,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASC,GAAQR,EAAI4F,mBAAoB,KAAS,CAAC5F,EAAIU,GAAG,SAASR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIoG,cAAc,CAACpG,EAAIU,GAAG,UAAU,IAAI,GAAGR,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAIqG,cAAc,MAAQ,OAAO9F,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAIqG,cAAc7F,KAAU,CAACN,EAAG,WAAW,CAACI,MAAM,CAAC,IAAMN,EAAIsG,eAAe,IAAI,IAE1/oBC,EAAkB,CAAC,WAAY,IAAIvG,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIU,GAAG,YAAYR,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIU,GAAG,uBC4wBzO,GACfoC,KAAA,OACA0D,WAAA,GACAC,OACA,OACA7D,SAAA,QACA8D,QAAA,OACAzD,KAAA,GACAT,MAAA,EACAmE,KAAA,EACArC,KAAA,GACA/C,OAAA,CACAC,QAAA,GACAM,UAAA,GACAM,OAAA,IAEAY,SAAA,EACA4D,IAAA,aACAzE,MAAA,KACA0E,KAAA,GACAjB,mBAAA,EACAU,WAAA,GACAD,eAAA,EACAR,SAAA,CACA1D,MAAA,GACAoB,SAAA,GACAG,QAAA,GACAG,MAAA,GACA/B,UAAA,GACAM,OAAA,GAEA0E,MAAA,GACA9E,QAAA,GAEA0C,oBAAA,EACAK,QAAA,EACAF,YAAA,EACAkC,iBAAA,GACAnC,gBAAA,CACA1C,GAAA,KACAC,MAAA,GACAuB,QAAA,GACAG,MAAA,GACAN,SAAA,GACAzB,UAAA,GACA8B,aAAA,GACAxB,OAAA,EACAiD,UAAA,GACAtB,YAAA,GACA0B,YAAA,IAEAN,YAAA,CACAhD,MAAA,CACA,CACA6E,UAAA,EACAC,QAAA,UACAC,QAAA,SAGAxD,QAAA,CACA,CACAsD,UAAA,EACAC,QAAA,UACAC,QAAA,SAGArD,MAAA,CACA,CACAmD,UAAA,EACAC,QAAA,UACAC,QAAA,QAEA,CACAC,QAAA,gBACAF,QAAA,aACAC,QAAA,SAGApF,UAAA,CACA,CACAkF,UAAA,EACAC,QAAA,QACAC,QAAA,YAIApB,MAAA,CACA3D,MAAA,CACA,CACA6E,UAAA,EACAC,QAAA,UACAC,QAAA,SAGAxD,QAAA,CACA,CACAsD,UAAA,EACAC,QAAA,QACAC,QAAA,SAGArD,MAAA,CACA,CACAmD,UAAA,EACAC,QAAA,SACAC,QAAA,SAGA3D,SAAA,CACA,CACAyD,UAAA,EACAC,QAAA,QACAC,QAAA,SAGApF,UAAA,CACA,CACAkF,UAAA,EACAC,QAAA,UACAC,QAAA,UAIAnB,eAAA,UAGAqB,SAAA,CAEA3E,aACA,YAAAQ,KAAAoE,OAAArB,GACAA,EAAApC,eACAoC,EAAApC,aAAA0D,SAAA,QACAtB,EAAApC,aAAA0D,SAAA,OACAtB,EAAApC,aAAA0D,SAAA,QAEAC,QAGA7E,cACA,YAAAO,KAAAoE,OAAArB,GAAA,IAAAA,EAAA5D,QAAAmF,QAGA5E,WACA,MAAA6E,GAAA,IAAAC,MAAAC,WAAA,EACA,YAAAzE,KAAAoE,OAAArB,IACA,IAAAA,EAAAjC,YAAA,SACA,MAAA4D,EAAA,IAAAF,KAAAzB,EAAAjC,aAAA2D,WAAA,EACA,OAAAC,IAAAH,IACAD,SAGAK,UACA,KAAAC,WAEAC,QAAA,CAEAnE,mBAAAoE,GACA,OAAAA,EACAA,EAAAT,SAAA,QAAAS,EAAAT,SAAA,eACAS,EAAAT,SAAA,OAAAS,EAAAT,SAAA,gBACAS,EAAAT,SAAA,OAAAS,EAAAT,SAAA,gBACA,UAJA,QAQAxD,aAAAR,GACA,KAAA0E,SAAAC,QAAA,MAAA3E,EAAAnB,YAAAmB,EAAAlB,OAAA,cAIAC,cACA,KAAAd,OAAA,CACAC,QAAA,GACAM,UAAA,GACAM,OAAA,IAEA,KAAAlB,cAIAoB,aACA,KAAA0F,SAAAC,QAAA,eAIAxE,mBAAAY,GAAA,IAAA6D,EACA,KAAAtD,gBAAA,IACAP,EACAgB,UAAAhB,EAAAgB,YAAA,QAAA6C,EAAA7D,EAAAN,mBAAA,IAAAmE,OAAA,EAAAA,EAAAC,MAAA,cAEA,KAAApB,iBAAA,SAAAnC,iBACA,KAAAC,YAAA,EACA,KAAAH,oBAAA,EAGA,SAAA1C,QAAAuF,QACA,KAAAa,aAKApE,iBAAAK,GAAA,IAAAgE,EACA,KAAAzD,gBAAA,IACAP,EACAgB,UAAAhB,EAAAgB,YAAA,QAAAgD,EAAAhE,EAAAN,mBAAA,IAAAsE,OAAA,EAAAA,EAAAF,MAAA,cAEA,KAAApB,iBAAA,SAAAnC,iBACA,KAAAC,YAAA,EACA,KAAAH,oBAAA,EAGA,SAAA1C,QAAAuF,QACA,KAAAa,aAKAtD,mBACA,KAAAD,YAAA,GAIAI,aACA,KAAAL,gBAAA,SAAAmC,kBACA,KAAAlC,YAAA,GAIAF,mBACA,KAAAD,oBAAA,EACA,KAAAK,QAAA,EAEA,KAAAuD,UAAA,KACA,KAAAC,MAAAC,YACA,KAAAD,MAAAC,WAAAC,iBAMAzD,mBACA,KAAAuD,MAAAC,WAAAE,SAAAC,IACAA,IACA,KAAA5D,QAAA,EAGA6D,WAAA,KAKA,GAJA,KAAA7D,QAAA,EACA,KAAAiD,SAAAC,QAAA,SAGA,KAAArD,gBAAA1C,GAAA,CACA,MAAA+D,EAAA,KAAAhD,KAAA4F,UAAA7C,KAAA9D,KAAA,KAAA0C,gBAAA1C,IACA,QAAA+D,EAAA,CAEA,MAAAhE,EAAA,KAAAD,QAAA8G,KAAAC,KAAA7G,KAAA,KAAA0C,gBAAA9C,WACA,KAAA8C,gBAAAhB,aAAA3B,IAAAE,MAAA,GAEA,KAAAR,KAAA,KAAAsB,KAAAgD,EAAA,SAAArB,uBAEA,CAEA,MAAAoE,EAAA,IACA,KAAApE,gBACA1C,GAAAuF,KAAAwB,MACAlF,aAAA,IAAA0D,MAAAyB,kBAEAjH,EAAA,KAAAD,QAAA8G,KAAAC,KAAA7G,KAAA8G,EAAAlH,WACAkH,EAAApF,aAAA3B,IAAAE,MAAA,GAEA,KAAAc,KAAAkG,QAAAH,GACA,KAAAxG,QAGA,KAAAmC,oBACA,SAMAW,oBAAA8D,GACA,KAAAxE,gBAAArB,SAAA6F,EAAA3C,KAAAG,IACA,KAAAoB,SAAAC,QAAA,YAIAzC,eACA,KAAA6D,SAAA,kBACAC,kBAAA,KACAC,iBAAA,KACA1I,KAAA,YACA2I,KAAA,KACA,KAAA5E,gBAAArB,SAAA,GACA,KAAAyE,SAAAC,QAAA,aAKAvC,gBACA,KAAA2D,SAAA,6BACAC,kBAAA,KACAC,iBAAA,KACA1I,KAAA,YACA2I,KAAA,KACA,KAAAxB,SAAAC,QAAA,cAKAtC,iBACA,KAAA0D,SAAA,2BACAC,kBAAA,KACAC,iBAAA,KACA1I,KAAA,YACA2I,KAAA,KACA,MAAAvD,EAAA,KAAAhD,KAAA4F,UAAA7C,KAAA9D,KAAA,KAAA0C,gBAAA1C,KACA,IAAA+D,IACA,KAAAhD,KAAAwG,OAAAxD,EAAA,GACA,KAAAzD,QACA,KAAAwF,SAAAC,QAAA,WACA,KAAAtD,uBAKA+E,YAAA5C,GACA,KAAAA,SAEA7C,SAAA/B,GACA,KAAAmH,SAAA,oBACAC,kBAAA,KACAC,iBAAA,KACA1I,KAAA,YAEA2I,KAAA,KACA,KAAAG,YAAA,sBAAAzH,OAAAsH,KAAAI,IACA,KAAAA,EAAAC,KACA,KAAA7B,SAAA,CACAnH,KAAA,UACAoG,QAAA,UAGA,KAAAe,SAAA,CACAnH,KAAA,QACAoG,QAAA,cAKA6C,MAAA,KACA,KAAA9B,SAAA,CACAnH,KAAA,QACAoG,QAAA,aAIAmB,YACA,KAAAuB,YAAA,sBAAAH,KAAAI,IACA,KAAAA,EAAAC,OACA,KAAA7H,QAAA4H,EAAAnD,SAIAhG,SAAAyB,GACA,MAAAA,EAAA,CAEA,MAAAmC,EAAA,KAAApB,KAAA6F,KAAA9C,KAAA9D,QACAmC,GACA,KAAAL,iBAAAK,QAIA,KAAAO,gBAAA,CACA1C,GAAA,KACAC,MAAA,GACAuB,QAAA,GACAG,MAAA,GACAN,SAAA,GACAzB,UAAA,GACA8B,aAAA,GACAxB,OAAA,EACAiD,UAAA,GACAtB,YAAA,GACA0B,YAAA,IAEA,KAAAsB,iBAAA,SAAAnC,iBACA,KAAAC,YAAA,EACA,KAAAH,oBAAA,EAGA,SAAA1C,QAAAuF,QACA,KAAAa,aAIA2B,QAAA7H,GACA,IAAA8H,EAAA,KACAA,EAAAC,WAAAD,EAAApD,IAAA,WAAA1E,GAAAsH,KAAAI,IACAA,IACAI,EAAAnE,SAAA+D,EAAAnD,SAIAvC,QAAA+B,EAAA/D,GACA,KAAAmH,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACA1I,KAAA,YAEA2I,KAAA,KACA,KAAAU,cAAA,KAAAtD,IAAA,aAAA1E,GAAAsH,KAAAI,IACA,KAAAA,EAAAC,OACA,KAAA7B,SAAA,CACAnH,KAAA,UACAoG,QAAA,UAEA,KAAAhE,KAAAwG,OAAAxD,EAAA,QAIA6D,MAAA,KACA,KAAA9B,SAAA,CACAnH,KAAA,QACAoG,QAAA,aAIAtG,UACA,KAAAwJ,QAAAC,GAAA,IAEAlJ,aACA,KAAAyF,KAAA,EACA,KAAArC,KAAA,GACA,KAAAuD,WAGAA,UACA,IAAAmC,EAAA,KACAA,EAAAhH,SAAA,EAGA4F,WAAA,KACAoB,EAAAhH,SAAA,EACAgH,EAAA/G,KAAA,CACA,CACAf,GAAA,EACAC,MAAA,KACAuB,QAAA,WACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,QACAxB,OAAA,EACA2B,YAAA,uBAEA,CACA7B,GAAA,EACAC,MAAA,KACAuB,QAAA,OACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,OACAxB,OAAA,EACA2B,YAAA,uBAEA,CACA7B,GAAA,EACAC,MAAA,KACAuB,QAAA,SACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,OACAxB,OAAA,EACA2B,YAAA,uBAEA,CACA7B,GAAA,EACAC,MAAA,KACAuB,QAAA,UACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,OACAxB,OAAA,EACA2B,YAAA,uBAEA,CACA7B,GAAA,EACAC,MAAA,KACAuB,QAAA,QACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,OACAxB,OAAA,EACA2B,YAAA,uBAEA,CACA7B,GAAA,EACAC,MAAA,KACAuB,QAAA,SACAG,MAAA,cACAN,SAAA,sEACAzB,UAAA,EACA8B,aAAA,OACAxB,OAAA,EACA2B,YAAA,wBAGAiG,EAAAxH,MAAA,EAGAwH,EAAAhI,QAAA,CACA,CAAAE,GAAA,EAAAC,MAAA,SACA,CAAAD,GAAA,EAAAC,MAAA,QACA,CAAAD,GAAA,EAAAC,MAAA,QACA,CAAAD,GAAA,EAAAC,MAAA,QACA,CAAAD,GAAA,EAAAC,MAAA,UAEA,MAkBAiE,WACA,IAAA4D,EAAA,KACA,KAAAzB,MAAA,YAAAG,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAgB,YAAAK,EAAApD,IAAA,YAAAf,UAAA2D,KAAAI,IACA,KAAAA,EAAAC,MACAG,EAAAhC,SAAA,CACAnH,KAAA,UACAoG,QAAA2C,EAAAS,MAEA,KAAAxC,UACAmC,EAAApE,mBAAA,GAEAoE,EAAAhC,SAAA,CACAnH,KAAA,QACAoG,QAAA2C,EAAAS,WASA9F,iBAAA+F,GACA,KAAAhG,KAAAgG,EAEA,KAAAzC,WAEArD,oBAAA8F,GACA,KAAA3D,KAAA2D,EACA,KAAAzC,WAEA3B,cAAAkD,GACA,KAAAvD,SAAAtC,SAAA6F,EAAA3C,KAAAG,KAGApD,UAAA+G,GACA,KAAAjE,WAAAiE,EACA,KAAAlE,eAAA,GAEAd,aAAAgF,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAA1J,MACA2J,GACA,KAAAxC,SAAA0C,MAAA,cAIAvE,SAAAoE,EAAAI,GACA,IAAAX,EAAA,KACAA,EAAAC,WAAA,6BAAAM,GAAAf,KAAAI,IACA,KAAAA,EAAAC,MACAG,EAAAnE,SAAA8E,GAAA,GAEAX,EAAAhC,SAAAC,QAAA,UAEA+B,EAAAhC,SAAA0C,MAAAd,EAAAS,UCl2C4W,I,wBCQxWO,EAAY,eACd,EACA7K,EACAwG,GACA,EACA,KACA,WACA,MAIa,aAAAqE,E", "file": "js/chunk-5f5b3be8.79a9d640.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=04fb7389&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"employee-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{staticClass:\"add-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增员工 \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新 \")])],1)])]),_c('div',{staticClass:\"search-section\"},[_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-row\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"员工搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入员工姓名、手机号或账号\",\"clearable\":\"\"},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchData.apply(null, arguments)}},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"职位筛选\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择职位\",\"clearable\":\"\"},model:{value:(_vm.search.zhiwei_id),callback:function ($$v) {_vm.$set(_vm.search, \"zhiwei_id\", $$v)},expression:\"search.zhiwei_id\"}},_vm._l((_vm.zhiweis),function(zhiwei){return _c('el-option',{key:zhiwei.id,attrs:{\"label\":zhiwei.title,\"value\":zhiwei.id}})}),1)],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.status),callback:function ($$v) {_vm.$set(_vm.search, \"status\", $$v)},expression:\"search.status\"}},[_c('el-option',{attrs:{\"label\":\"正常\",\"value\":1}}),_c('el-option',{attrs:{\"label\":\"禁用\",\"value\":0}})],1)],1)]),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchData}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.clearSearch}},[_vm._v(\" 重置 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-download\"},on:{\"click\":_vm.exportData}},[_vm._v(\" 导出 \")])],1)])])],1),_c('div',{staticClass:\"stats-section\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon total\"},[_c('i',{staticClass:\"el-icon-user\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.total))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"总员工数\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon admin\"},[_c('i',{staticClass:\"el-icon-user-solid\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.adminCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"管理员\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon active\"},[_c('i',{staticClass:\"el-icon-circle-check\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.activeCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"在职员工\")])])])]),_c('el-col',{attrs:{\"span\":6}},[_c('div',{staticClass:\"stat-card\"},[_c('div',{staticClass:\"stat-icon new\"},[_c('i',{staticClass:\"el-icon-plus\"})]),_c('div',{staticClass:\"stat-content\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.newCount))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(\"本月新增\")])])])])],1)],1),_c('div',{staticClass:\"table-section\"},[_c('el-card',{staticClass:\"table-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"table-header\"},[_c('div',{staticClass:\"table-title\"},[_c('i',{staticClass:\"el-icon-menu\"}),_vm._v(\" 员工列表 \")]),_c('div',{staticClass:\"table-tools\"},[_c('el-button-group',[_c('el-button',{attrs:{\"type\":_vm.viewMode === 'table' ? 'primary' : '',\"icon\":\"el-icon-menu\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'table'}}},[_vm._v(\" 列表视图 \")]),_c('el-button',{attrs:{\"type\":_vm.viewMode === 'card' ? 'primary' : '',\"icon\":\"el-icon-s-grid\",\"size\":\"small\"},on:{\"click\":function($event){_vm.viewMode = 'card'}}},[_vm._v(\" 卡片视图 \")])],1)],1)]),(_vm.viewMode === 'table')?_c('div',{staticClass:\"table-view\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"employee-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"label\":\"头像\",\"width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"avatar-cell\"},[_c('el-avatar',{staticClass:\"employee-avatar\",attrs:{\"src\":scope.row.pic_path,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(scope.row.pic_path)}}},[_c('i',{staticClass:\"el-icon-user-solid\"})])],1)]}}],null,false,**********)}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"员工姓名\",\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"employee-name-cell\"},[_c('div',{staticClass:\"employee-name clickable\",on:{\"click\":function($event){return _vm.showEmployeeDetail(scope.row)}}},[_vm._v(_vm._s(scope.row.title))]),_c('div',{staticClass:\"employee-account\"},[_vm._v(_vm._s(scope.row.account))])])]}}],null,false,**********)}),_c('el-table-column',{attrs:{\"label\":\"职位\",\"width\":\"150\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getPositionTagType(scope.row.zhiwei_title),\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.zhiwei_title || '未分配')+\" \")])]}}],null,false,**********)}),_c('el-table-column',{attrs:{\"prop\":\"phone\",\"label\":\"手机号码\",\"width\":\"130\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"phone-cell\"},[_c('i',{staticClass:\"el-icon-phone\"}),_vm._v(\" \"+_vm._s(scope.row.phone)+\" \")])]}}],null,false,**********)}),_c('el-table-column',{attrs:{\"label\":\"状态\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0},on:{\"change\":function($event){return _vm.changeStatus(scope.row)}},model:{value:(scope.row.status),callback:function ($$v) {_vm.$set(scope.row, \"status\", $$v)},expression:\"scope.row.status\"}})]}}],null,false,2880962836)}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"入职时间\",\"width\":\"160\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-cell\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" \"+_vm._s(scope.row.create_time)+\" \")])]}}],null,false,3001843918)}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"220\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.showEmployeeEdit(scope.row)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"mini\",\"icon\":\"el-icon-key\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.chongzhi(scope.row.id)}}},[_vm._v(\" 重置密码 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 删除 \")])],1)]}}],null,false,1304503458)})],1)],1):_vm._e(),(_vm.viewMode === 'card')?_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"card-view\"},[_c('el-row',{attrs:{\"gutter\":20}},_vm._l((_vm.list),function(employee){return _c('el-col',{key:employee.id,staticClass:\"employee-card-col\",attrs:{\"span\":8}},[_c('div',{staticClass:\"employee-card\"},[_c('div',{staticClass:\"card-header\"},[_c('div',{staticClass:\"card-avatar\"},[_c('el-avatar',{attrs:{\"src\":employee.pic_path,\"size\":60},nativeOn:{\"click\":function($event){return _vm.showImage(employee.pic_path)}}},[_c('i',{staticClass:\"el-icon-user-solid\"})])],1),_c('div',{staticClass:\"card-info\"},[_c('div',{staticClass:\"card-name clickable\",on:{\"click\":function($event){return _vm.showEmployeeDetail(employee)}}},[_vm._v(_vm._s(employee.title))]),_c('div',{staticClass:\"card-position\"},[_c('el-tag',{attrs:{\"type\":_vm.getPositionTagType(employee.zhiwei_title),\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(employee.zhiwei_title || '未分配')+\" \")])],1)]),_c('div',{staticClass:\"card-status\"},[_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0,\"size\":\"small\"},on:{\"change\":function($event){return _vm.changeStatus(employee)}},model:{value:(employee.status),callback:function ($$v) {_vm.$set(employee, \"status\", $$v)},expression:\"employee.status\"}})],1)]),_c('div',{staticClass:\"card-content\"},[_c('div',{staticClass:\"card-detail\"},[_c('div',{staticClass:\"detail-item\"},[_c('i',{staticClass:\"el-icon-phone\"}),_c('span',[_vm._v(_vm._s(employee.phone))])]),_c('div',{staticClass:\"detail-item\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',[_vm._v(_vm._s(employee.account))])]),_c('div',{staticClass:\"detail-item\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(employee.create_time))])])])]),_c('div',{staticClass:\"card-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.showEmployeeEdit(employee)}}},[_vm._v(\" 编辑 \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-key\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.chongzhi(employee.id)}}},[_vm._v(\" 重置密码 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){_vm.delData(_vm.list.indexOf(employee), employee.id)}}},[_vm._v(\" 删除 \")])],1)])])}),1)],1):_vm._e(),_c('div',{staticClass:\"pagination-container\"},[_c('el-pagination',{staticClass:\"pagination\",attrs:{\"page-sizes\":[12, 24, 48, 96],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)])],1),_c('div',{staticClass:\"employee-detail-panel\",class:{ 'panel-open': _vm.detailPanelVisible }},[_c('div',{staticClass:\"panel-overlay\",on:{\"click\":_vm.closeDetailPanel}}),_c('div',{staticClass:\"panel-content\"},[_c('div',{staticClass:\"panel-header\"},[_c('div',{staticClass:\"panel-title\"},[_c('i',{staticClass:\"el-icon-user\"}),(!_vm.currentEmployee.id)?_c('span',[_vm._v(\"新增员工\")]):(_vm.isViewMode)?_c('span',[_vm._v(\"员工详情\")]):_c('span',[_vm._v(\"编辑员工\")])]),_c('div',{staticClass:\"panel-actions\"},[(_vm.isViewMode && _vm.currentEmployee.id)?_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-edit\"},on:{\"click\":_vm.switchToEditMode}},[_vm._v(\" 编辑 \")]):_vm._e(),(!_vm.isViewMode)?_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"loading\":_vm.saving,\"icon\":\"el-icon-check\"},on:{\"click\":_vm.saveEmployeeData}},[_vm._v(\" 保存 \")]):_vm._e(),(!_vm.isViewMode && _vm.currentEmployee.id)?_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-refresh-left\"},on:{\"click\":_vm.cancelEdit}},[_vm._v(\" 取消编辑 \")]):_vm._e(),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-close\"},on:{\"click\":_vm.closeDetailPanel}},[_vm._v(\" 关闭 \")])],1)]),_c('div',{staticClass:\"panel-body\"},[_c('el-form',{ref:\"detailForm\",staticClass:\"employee-form\",attrs:{\"model\":_vm.currentEmployee,\"rules\":_vm.detailRules,\"label-width\":\"100px\"}},[_c('div',{staticClass:\"form-section\"},[_c('div',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 基本信息 \")]),_c('div',{staticClass:\"form-row\"},[_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"员工姓名\",\"prop\":\"title\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入员工姓名\",\"readonly\":_vm.isViewMode,\"clearable\":\"\"},model:{value:(_vm.currentEmployee.title),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"title\", $$v)},expression:\"currentEmployee.title\"}})],1)],1),_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"手机号码\",\"prop\":\"phone\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入手机号码\",\"readonly\":_vm.isViewMode,\"clearable\":\"\"},model:{value:(_vm.currentEmployee.phone),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"phone\", $$v)},expression:\"currentEmployee.phone\"}})],1)],1)]),_c('div',{staticClass:\"form-row\"},[_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"登录账号\",\"prop\":\"account\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入登录账号\",\"readonly\":_vm.isViewMode,\"clearable\":\"\"},model:{value:(_vm.currentEmployee.account),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"account\", $$v)},expression:\"currentEmployee.account\"}},[(!_vm.currentEmployee.id && !_vm.isViewMode)?_c('template',{slot:\"append\"},[_vm._v(\"默认密码888888\")]):_vm._e()],2)],1)],1),_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"员工状态\",\"prop\":\"status\"}},[_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0,\"disabled\":_vm.isViewMode,\"active-text\":\"正常\",\"inactive-text\":\"禁用\"},model:{value:(_vm.currentEmployee.status),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"status\", $$v)},expression:\"currentEmployee.status\"}})],1)],1)])]),_c('div',{staticClass:\"form-section\"},[_c('div',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-postcard\"}),_vm._v(\" 职位信息 \")]),_c('div',{staticClass:\"form-row\"},[_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"所属职位\",\"prop\":\"zhiwei_id\"}},[(!_vm.isViewMode)?_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择职位\",\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.currentEmployee.zhiwei_id),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"zhiwei_id\", $$v)},expression:\"currentEmployee.zhiwei_id\"}},_vm._l((_vm.zhiweis),function(zhiwei){return _c('el-option',{key:zhiwei.id,attrs:{\"label\":zhiwei.title,\"value\":zhiwei.id}})}),1):_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"value\":_vm.currentEmployee.zhiwei_title || '未分配',\"readonly\":\"\"}})],1)],1),_c('div',{staticClass:\"form-col\"},[_c('el-form-item',{attrs:{\"label\":\"入职时间\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"date\",\"placeholder\":\"选择入职时间\",\"readonly\":_vm.isViewMode,\"disabled\":_vm.isViewMode,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.currentEmployee.join_date),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"join_date\", $$v)},expression:\"currentEmployee.join_date\"}})],1)],1)])]),_c('div',{staticClass:\"form-section\"},[_c('div',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-picture\"}),_vm._v(\" 头像信息 \")]),_c('div',{staticClass:\"avatar-upload-section\"},[_c('div',{staticClass:\"current-avatar\"},[_c('el-avatar',{staticClass:\"preview-avatar\",attrs:{\"src\":_vm.currentEmployee.pic_path,\"size\":100},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.currentEmployee.pic_path)}}},[_c('i',{staticClass:\"el-icon-user-solid\"})])],1),_c('div',{staticClass:\"upload-controls\"},[(!_vm.isViewMode)?_c('el-form-item',{attrs:{\"label\":\"头像\",\"prop\":\"pic_path\"}},[_c('el-input',{staticClass:\"avatar-input\",attrs:{\"placeholder\":\"头像URL\",\"readonly\":\"\"},model:{value:(_vm.currentEmployee.pic_path),callback:function ($$v) {_vm.$set(_vm.currentEmployee, \"pic_path\", $$v)},expression:\"currentEmployee.pic_path\"}})],1):_vm._e(),(!_vm.isViewMode)?_c('div',{staticClass:\"upload-buttons\"},[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleAvatarSuccess,\"before-upload\":_vm.beforeUpload}},[_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\",\"icon\":\"el-icon-upload\"}},[_vm._v(\" 上传头像 \")])],1),(_vm.currentEmployee.pic_path)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"success\",\"icon\":\"el-icon-view\"},on:{\"click\":function($event){return _vm.showImage(_vm.currentEmployee.pic_path)}}},[_vm._v(\" 查看 \")]):_vm._e(),(_vm.currentEmployee.pic_path)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"danger\",\"icon\":\"el-icon-delete\"},on:{\"click\":_vm.removeAvatar}},[_vm._v(\" 删除 \")]):_vm._e()],1):_c('div',{staticClass:\"view-buttons\"},[(_vm.currentEmployee.pic_path)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"success\",\"icon\":\"el-icon-view\"},on:{\"click\":function($event){return _vm.showImage(_vm.currentEmployee.pic_path)}}},[_vm._v(\" 查看头像 \")]):_c('span',{staticClass:\"no-avatar-text\"},[_vm._v(\"暂无头像\")])],1),(!_vm.isViewMode)?_c('div',{staticClass:\"upload-tip\"},[_vm._v(\"建议尺寸：330px × 300px\")]):_vm._e()],1)])]),(_vm.currentEmployee.id)?_c('div',{staticClass:\"form-section\"},[_c('div',{staticClass:\"section-title\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" 操作记录 \")]),_c('div',{staticClass:\"operation-record\"},[_c('div',{staticClass:\"record-item\"},[_c('span',{staticClass:\"record-label\"},[_vm._v(\"创建时间：\")]),_c('span',{staticClass:\"record-value\"},[_vm._v(_vm._s(_vm.currentEmployee.create_time))])]),_c('div',{staticClass:\"record-item\"},[_c('span',{staticClass:\"record-label\"},[_vm._v(\"最后更新：\")]),_c('span',{staticClass:\"record-value\"},[_vm._v(_vm._s(_vm.currentEmployee.update_time || '暂无'))])]),_c('div',{staticClass:\"record-item\"},[_c('span',{staticClass:\"record-label\"},[_vm._v(\"员工ID：\")]),_c('span',{staticClass:\"record-value\"},[_vm._v(_vm._s(_vm.currentEmployee.id))])])]),(!_vm.isViewMode)?_c('div',{staticClass:\"quick-actions\"},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-key\"},on:{\"click\":_vm.resetPassword}},[_vm._v(\" 重置密码 \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"icon\":\"el-icon-delete\"},on:{\"click\":_vm.deleteEmployee}},[_vm._v(\" 删除员工 \")])],1):_vm._e()]):_vm._e()])],1)])]),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"职位类型\",\"label-width\":_vm.formLabelWidth,\"prop\":\"zhiwei_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.zhiwei_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"zhiwei_id\", $$v)},expression:\"ruleForm.zhiwei_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.zhiweis),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":_vm.title + '名称',\"label-width\":_vm.formLabelWidth,\"prop\":\"title\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":_vm.title + '手机',\"label-width\":_vm.formLabelWidth,\"prop\":\"phone\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.phone),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"phone\", $$v)},expression:\"ruleForm.phone\"}})],1),_c('el-form-item',{attrs:{\"label\":_vm.title + '账号',\"label-width\":_vm.formLabelWidth,\"prop\":\"account\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.account),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"account\", $$v)},expression:\"ruleForm.account\"}},[_c('template',{slot:\"append\"},[_vm._v(\"默认密码888888\")])],2)],1),_c('el-form-item',{attrs:{\"label\":\"头像\",\"label-width\":_vm.formLabelWidth,\"prop\":\"pic_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.pic_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"pic_path\", $$v)},expression:\"ruleForm.pic_path\"}}),_c('el-button-group',[_c('el-button',[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess,\"before-upload\":_vm.beforeUpload}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.pic_path)?_c('el-button',{attrs:{\"type\":\"success\"},on:{\"click\":function($event){return _vm.showImage(_vm.ruleForm.pic_path)}}},[_vm._v(\"查看 \")]):_vm._e(),(_vm.ruleForm.pic_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.pic_path, 'pic_path')}}},[_vm._v(\"删除\")]):_vm._e()],1),_c('div',{staticClass:\"el-upload__tip\"},[_vm._v(\"330rpx*300rpx\")])],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"title-section\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 员工管理 \")]),_c('p',{staticClass:\"page-subtitle\"},[_vm._v(\"管理系统员工信息和职位分配\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"employee-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            员工管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统员工信息和职位分配</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增员工\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">员工搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入员工姓名、手机号或账号\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">职位筛选</label>\r\n              <el-select\r\n                v-model=\"search.zhiwei_id\"\r\n                placeholder=\"请选择职位\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"zhiwei in zhiweis\"\r\n                  :key=\"zhiwei.id\"\r\n                  :label=\"zhiwei.title\"\r\n                  :value=\"zhiwei.id\"\r\n                ></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"正常\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n            <el-button icon=\"el-icon-download\" @click=\"exportData\">\r\n              导出\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总员工数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon admin\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ adminCount }}</div>\r\n              <div class=\"stat-label\">管理员</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <i class=\"el-icon-circle-check\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeCount }}</div>\r\n              <div class=\"stat-label\">在职员工</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon new\">\r\n              <i class=\"el-icon-plus\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ newCount }}</div>\r\n              <div class=\"stat-label\">本月新增</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 员工列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            员工列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"employee-table\"\r\n            stripe\r\n          >\r\n            <el-table-column label=\"头像\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"avatar-cell\">\r\n                  <el-avatar\r\n                    :src=\"scope.row.pic_path\"\r\n                    :size=\"50\"\r\n                    @click.native=\"showImage(scope.row.pic_path)\"\r\n                    class=\"employee-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"title\" label=\"员工姓名\" min-width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"employee-name-cell\">\r\n                  <div class=\"employee-name clickable\" @click=\"showEmployeeDetail(scope.row)\">{{ scope.row.title }}</div>\r\n                  <div class=\"employee-account\">{{ scope.row.account }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"职位\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  :type=\"getPositionTagType(scope.row.zhiwei_title)\"\r\n                  size=\"small\"\r\n                >\r\n                  {{ scope.row.zhiwei_title || '未分配' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"phone\" label=\"手机号码\" width=\"130\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"phone-cell\">\r\n                  <i class=\"el-icon-phone\"></i>\r\n                  {{ scope.row.phone }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"入职时间\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"showEmployeeEdit(scope.row)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"mini\"\r\n                    @click=\"chongzhi(scope.row.id)\"\r\n                    icon=\"el-icon-key\"\r\n                    plain\r\n                  >\r\n                    重置密码\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"employee in list\" :key=\"employee.id\" class=\"employee-card-col\">\r\n              <div class=\"employee-card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-avatar\">\r\n                    <el-avatar\r\n                      :src=\"employee.pic_path\"\r\n                      :size=\"60\"\r\n                      @click.native=\"showImage(employee.pic_path)\"\r\n                    >\r\n                      <i class=\"el-icon-user-solid\"></i>\r\n                    </el-avatar>\r\n                  </div>\r\n                  <div class=\"card-info\">\r\n                    <div class=\"card-name clickable\" @click=\"showEmployeeDetail(employee)\">{{ employee.title }}</div>\r\n                    <div class=\"card-position\">\r\n                      <el-tag\r\n                        :type=\"getPositionTagType(employee.zhiwei_title)\"\r\n                        size=\"mini\"\r\n                      >\r\n                        {{ employee.zhiwei_title || '未分配' }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-status\">\r\n                    <el-switch\r\n                      v-model=\"employee.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      @change=\"changeStatus(employee)\"\r\n                      size=\"small\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-detail\">\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-phone\"></i>\r\n                      <span>{{ employee.phone }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-user\"></i>\r\n                      <span>{{ employee.account }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      <span>{{ employee.create_time }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"showEmployeeEdit(employee)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    @click=\"chongzhi(employee.id)\"\r\n                    icon=\"el-icon-key\"\r\n                    plain\r\n                  >\r\n                    重置密码\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(employee), employee.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 右侧滑出详情面板 -->\r\n    <div class=\"employee-detail-panel\" :class=\"{ 'panel-open': detailPanelVisible }\">\r\n      <div class=\"panel-overlay\" @click=\"closeDetailPanel\"></div>\r\n      <div class=\"panel-content\">\r\n        <!-- 面板头部 -->\r\n        <div class=\"panel-header\">\r\n          <div class=\"panel-title\">\r\n            <i class=\"el-icon-user\"></i>\r\n            <span v-if=\"!currentEmployee.id\">新增员工</span>\r\n            <span v-else-if=\"isViewMode\">员工详情</span>\r\n            <span v-else>编辑员工</span>\r\n          </div>\r\n          <div class=\"panel-actions\">\r\n            <el-button\r\n              v-if=\"isViewMode && currentEmployee.id\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"switchToEditMode\"\r\n              icon=\"el-icon-edit\"\r\n            >\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isViewMode\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"saveEmployeeData\"\r\n              :loading=\"saving\"\r\n              icon=\"el-icon-check\"\r\n            >\r\n              保存\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isViewMode && currentEmployee.id\"\r\n              size=\"small\"\r\n              @click=\"cancelEdit\"\r\n              icon=\"el-icon-refresh-left\"\r\n            >\r\n              取消编辑\r\n            </el-button>\r\n            <el-button\r\n              size=\"small\"\r\n              @click=\"closeDetailPanel\"\r\n              icon=\"el-icon-close\"\r\n            >\r\n              关闭\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 面板内容 -->\r\n        <div class=\"panel-body\">\r\n          <el-form\r\n            :model=\"currentEmployee\"\r\n            :rules=\"detailRules\"\r\n            ref=\"detailForm\"\r\n            label-width=\"100px\"\r\n            class=\"employee-form\"\r\n          >\r\n            <!-- 基本信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-user\"></i>\r\n                基本信息\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"员工姓名\" prop=\"title\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.title\"\r\n                      placeholder=\"请输入员工姓名\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.phone\"\r\n                      placeholder=\"请输入手机号码\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"登录账号\" prop=\"account\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.account\"\r\n                      placeholder=\"请输入登录账号\"\r\n                      :readonly=\"isViewMode\"\r\n                      clearable\r\n                    >\r\n                      <template slot=\"append\" v-if=\"!currentEmployee.id && !isViewMode\">默认密码888888</template>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"员工状态\" prop=\"status\">\r\n                    <el-switch\r\n                      v-model=\"currentEmployee.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      :disabled=\"isViewMode\"\r\n                      active-text=\"正常\"\r\n                      inactive-text=\"禁用\"\r\n                    >\r\n                    </el-switch>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 职位信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-postcard\"></i>\r\n                职位信息\r\n              </div>\r\n\r\n              <div class=\"form-row\">\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"所属职位\" prop=\"zhiwei_id\">\r\n                    <el-select\r\n                      v-if=\"!isViewMode\"\r\n                      v-model=\"currentEmployee.zhiwei_id\"\r\n                      placeholder=\"请选择职位\"\r\n                      filterable\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"zhiwei in zhiweis\"\r\n                        :key=\"zhiwei.id\"\r\n                        :label=\"zhiwei.title\"\r\n                        :value=\"zhiwei.id\"\r\n                      ></el-option>\r\n                    </el-select>\r\n                    <el-input\r\n                      v-else\r\n                      :value=\"currentEmployee.zhiwei_title || '未分配'\"\r\n                      readonly\r\n                      style=\"width: 100%\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </div>\r\n                <div class=\"form-col\">\r\n                  <el-form-item label=\"入职时间\">\r\n                    <el-date-picker\r\n                      v-model=\"currentEmployee.join_date\"\r\n                      type=\"date\"\r\n                      placeholder=\"选择入职时间\"\r\n                      :readonly=\"isViewMode\"\r\n                      :disabled=\"isViewMode\"\r\n                      style=\"width: 100%\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 头像信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                头像信息\r\n              </div>\r\n\r\n              <div class=\"avatar-upload-section\">\r\n                <div class=\"current-avatar\">\r\n                  <el-avatar\r\n                    :src=\"currentEmployee.pic_path\"\r\n                    :size=\"100\"\r\n                    @click.native=\"showImage(currentEmployee.pic_path)\"\r\n                    class=\"preview-avatar\"\r\n                  >\r\n                    <i class=\"el-icon-user-solid\"></i>\r\n                  </el-avatar>\r\n                </div>\r\n                <div class=\"upload-controls\">\r\n                  <el-form-item label=\"头像\" prop=\"pic_path\" v-if=\"!isViewMode\">\r\n                    <el-input\r\n                      v-model=\"currentEmployee.pic_path\"\r\n                      placeholder=\"头像URL\"\r\n                      readonly\r\n                      class=\"avatar-input\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <div class=\"upload-buttons\" v-if=\"!isViewMode\">\r\n                    <el-upload\r\n                      action=\"/admin/Upload/uploadImage\"\r\n                      :show-file-list=\"false\"\r\n                      :on-success=\"handleAvatarSuccess\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      class=\"avatar-uploader\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">\r\n                        上传头像\r\n                      </el-button>\r\n                    </el-upload>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"success\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"showImage(currentEmployee.pic_path)\"\r\n                    >\r\n                      查看\r\n                    </el-button>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"danger\"\r\n                      icon=\"el-icon-delete\"\r\n                      @click=\"removeAvatar\"\r\n                    >\r\n                      删除\r\n                    </el-button>\r\n                  </div>\r\n                  <div class=\"view-buttons\" v-else>\r\n                    <el-button\r\n                      v-if=\"currentEmployee.pic_path\"\r\n                      size=\"small\"\r\n                      type=\"success\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"showImage(currentEmployee.pic_path)\"\r\n                    >\r\n                      查看头像\r\n                    </el-button>\r\n                    <span v-else class=\"no-avatar-text\">暂无头像</span>\r\n                  </div>\r\n                  <div class=\"upload-tip\" v-if=\"!isViewMode\">建议尺寸：330px × 300px</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作记录 -->\r\n            <div class=\"form-section\" v-if=\"currentEmployee.id\">\r\n              <div class=\"section-title\">\r\n                <i class=\"el-icon-time\"></i>\r\n                操作记录\r\n              </div>\r\n\r\n              <div class=\"operation-record\">\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">创建时间：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.create_time }}</span>\r\n                </div>\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">最后更新：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.update_time || '暂无' }}</span>\r\n                </div>\r\n                <div class=\"record-item\">\r\n                  <span class=\"record-label\">员工ID：</span>\r\n                  <span class=\"record-value\">{{ currentEmployee.id }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"quick-actions\" v-if=\"!isViewMode\">\r\n                <el-button\r\n                  type=\"warning\"\r\n                  size=\"small\"\r\n                  @click=\"resetPassword\"\r\n                  icon=\"el-icon-key\"\r\n                >\r\n                  重置密码\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"small\"\r\n                  @click=\"deleteEmployee\"\r\n                  icon=\"el-icon-delete\"\r\n                >\r\n                  删除员工\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 原有对话框保留用于新增 -->\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"职位类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"zhiwei_id\"\r\n        >\r\n          <el-select\r\n            v-model=\"ruleForm.zhiwei_id\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n          >\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in zhiweis\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '名称'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '手机'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"phone\"\r\n        >\r\n          <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '账号'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"account\"\r\n        >\r\n          <el-input v-model=\"ruleForm.account\" autocomplete=\"off\">\r\n            <template slot=\"append\">默认密码888888</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"头像\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n          <div class=\"el-upload__tip\">330rpx*300rpx</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      viewMode: 'table', // table | card\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 0,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        zhiwei_id: \"\",\r\n        status: \"\"\r\n      },\r\n      loading: true,\r\n      url: \"/Yuangong/\",\r\n      title: \"员工\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        pic_path: \"\",\r\n        account: \"\",\r\n        phone: \"\",\r\n        zhiwei_id: \"\",\r\n        status: 1\r\n      },\r\n      field: \"\",\r\n      zhiweis: [],\r\n      // 右侧面板相关\r\n      detailPanelVisible: false,\r\n      saving: false,\r\n      isViewMode: true, // 默认为查看模式\r\n      originalEmployee: {}, // 保存原始数据，用于取消编辑时恢复\r\n      currentEmployee: {\r\n        id: null,\r\n        title: \"\",\r\n        account: \"\",\r\n        phone: \"\",\r\n        pic_path: \"\",\r\n        zhiwei_id: \"\",\r\n        zhiwei_title: \"\",\r\n        status: 1,\r\n        join_date: \"\",\r\n        create_time: \"\",\r\n        update_time: \"\"\r\n      },\r\n      detailRules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写员工姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写登录账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写手机号码\",\r\n            trigger: \"blur\",\r\n          },\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写员工姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: \"请填写账号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: \"请填写手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传头像\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        zhiwei_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择职位类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 管理员数量\r\n    adminCount() {\r\n      return this.list.filter(item =>\r\n        item.zhiwei_title && (\r\n          item.zhiwei_title.includes('管理员') ||\r\n          item.zhiwei_title.includes('经理') ||\r\n          item.zhiwei_title.includes('主管')\r\n        )\r\n      ).length;\r\n    },\r\n    // 在职员工数量\r\n    activeCount() {\r\n      return this.list.filter(item => item.status === 1).length;\r\n    },\r\n    // 本月新增员工数量\r\n    newCount() {\r\n      const currentMonth = new Date().getMonth() + 1;\r\n      return this.list.filter(item => {\r\n        if (!item.create_time) return false;\r\n        const itemMonth = new Date(item.create_time).getMonth() + 1;\r\n        return itemMonth === currentMonth;\r\n      }).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取职位标签类型\r\n    getPositionTagType(position) {\r\n      if (!position) return 'info';\r\n      if (position.includes('管理员') || position.includes('经理')) return 'danger';\r\n      if (position.includes('主管') || position.includes('专员')) return 'warning';\r\n      if (position.includes('助理') || position.includes('客服')) return 'success';\r\n      return 'primary';\r\n    },\r\n\r\n    // 状态切换\r\n    changeStatus(row) {\r\n      this.$message.success(`员工\"${row.title}\"状态已${row.status ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        zhiwei_id: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 导出数据\r\n    exportData() {\r\n      this.$message.success('导出功能开发中...');\r\n    },\r\n\r\n    // 显示员工详情面板（查看模式）\r\n    showEmployeeDetail(employee) {\r\n      this.currentEmployee = {\r\n        ...employee,\r\n        join_date: employee.join_date || employee.create_time?.split(' ')[0] || ''\r\n      };\r\n      this.originalEmployee = { ...this.currentEmployee }; // 保存原始数据\r\n      this.isViewMode = true; // 设置为查看模式\r\n      this.detailPanelVisible = true;\r\n\r\n      // 确保职位数据已加载\r\n      if (this.zhiweis.length === 0) {\r\n        this.getZhiwei();\r\n      }\r\n    },\r\n\r\n    // 显示员工编辑面板（编辑模式）\r\n    showEmployeeEdit(employee) {\r\n      this.currentEmployee = {\r\n        ...employee,\r\n        join_date: employee.join_date || employee.create_time?.split(' ')[0] || ''\r\n      };\r\n      this.originalEmployee = { ...this.currentEmployee }; // 保存原始数据\r\n      this.isViewMode = false; // 设置为编辑模式\r\n      this.detailPanelVisible = true;\r\n\r\n      // 确保职位数据已加载\r\n      if (this.zhiweis.length === 0) {\r\n        this.getZhiwei();\r\n      }\r\n    },\r\n\r\n    // 切换到编辑模式\r\n    switchToEditMode() {\r\n      this.isViewMode = false;\r\n    },\r\n\r\n    // 取消编辑，恢复原始数据\r\n    cancelEdit() {\r\n      this.currentEmployee = { ...this.originalEmployee };\r\n      this.isViewMode = true;\r\n    },\r\n\r\n    // 关闭详情面板\r\n    closeDetailPanel() {\r\n      this.detailPanelVisible = false;\r\n      this.saving = false;\r\n      // 重置表单\r\n      this.$nextTick(() => {\r\n        if (this.$refs.detailForm) {\r\n          this.$refs.detailForm.resetFields();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 保存员工数据\r\n    saveEmployeeData() {\r\n      this.$refs.detailForm.validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n\r\n          // 模拟保存\r\n          setTimeout(() => {\r\n            this.saving = false;\r\n            this.$message.success('保存成功！');\r\n\r\n            // 更新列表中的数据\r\n            if (this.currentEmployee.id) {\r\n              const index = this.list.findIndex(item => item.id === this.currentEmployee.id);\r\n              if (index !== -1) {\r\n                // 更新职位标题\r\n                const zhiwei = this.zhiweis.find(z => z.id === this.currentEmployee.zhiwei_id);\r\n                this.currentEmployee.zhiwei_title = zhiwei ? zhiwei.title : '';\r\n\r\n                this.$set(this.list, index, { ...this.currentEmployee });\r\n              }\r\n            } else {\r\n              // 新增员工\r\n              const newEmployee = {\r\n                ...this.currentEmployee,\r\n                id: Date.now(), // 临时ID\r\n                create_time: new Date().toLocaleString()\r\n              };\r\n              const zhiwei = this.zhiweis.find(z => z.id === newEmployee.zhiwei_id);\r\n              newEmployee.zhiwei_title = zhiwei ? zhiwei.title : '';\r\n\r\n              this.list.unshift(newEmployee);\r\n              this.total++;\r\n            }\r\n\r\n            this.closeDetailPanel();\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 头像上传成功\r\n    handleAvatarSuccess(res) {\r\n      this.currentEmployee.pic_path = res.data.url;\r\n      this.$message.success('头像上传成功！');\r\n    },\r\n\r\n    // 删除头像\r\n    removeAvatar() {\r\n      this.$confirm('确定要删除头像吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.currentEmployee.pic_path = '';\r\n        this.$message.success('头像已删除！');\r\n      });\r\n    },\r\n\r\n    // 重置密码\r\n    resetPassword() {\r\n      this.$confirm('确定要重置该员工的密码为888888吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        this.$message.success('密码重置成功！');\r\n      });\r\n    },\r\n\r\n    // 删除员工\r\n    deleteEmployee() {\r\n      this.$confirm('确定要删除该员工吗？删除后不可恢复！', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      }).then(() => {\r\n        const index = this.list.findIndex(item => item.id === this.currentEmployee.id);\r\n        if (index !== -1) {\r\n          this.list.splice(index, 1);\r\n          this.total--;\r\n          this.$message.success('员工删除成功！');\r\n          this.closeDetailPanel();\r\n        }\r\n      });\r\n    },\r\n\r\n    changeField(field) {\r\n      this.field = field;\r\n    },\r\n    chongzhi(id) {\r\n      this.$confirm(\"重置密码888888?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.postRequest(\"/yuangong/chongzhi\", { id: id }).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"重置成功!\",\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: \"重置失败!\",\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消重置!\",\r\n          });\r\n        });\r\n    },\r\n    getZhiwei() {\r\n      this.postRequest(\"/zhiwei/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.zhiweis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    editData(id) {\r\n      if (id != 0) {\r\n        // 编辑现有员工，使用右侧面板编辑模式\r\n        const employee = this.list.find(item => item.id === id);\r\n        if (employee) {\r\n          this.showEmployeeEdit(employee);\r\n        }\r\n      } else {\r\n        // 新增员工，使用右侧面板编辑模式\r\n        this.currentEmployee = {\r\n          id: null,\r\n          title: \"\",\r\n          account: \"\",\r\n          phone: \"\",\r\n          pic_path: \"\",\r\n          zhiwei_id: \"\",\r\n          zhiwei_title: \"\",\r\n          status: 1,\r\n          join_date: \"\",\r\n          create_time: \"\",\r\n          update_time: \"\"\r\n        };\r\n        this.originalEmployee = { ...this.currentEmployee };\r\n        this.isViewMode = false; // 新增时直接进入编辑模式\r\n        this.detailPanelVisible = true;\r\n\r\n        // 确保职位数据已加载\r\n        if (this.zhiweis.length === 0) {\r\n          this.getZhiwei();\r\n        }\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"张三\",\r\n            account: \"zhangsan\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 1,\r\n            zhiwei_title: \"系统管理员\",\r\n            status: 1,\r\n            create_time: \"2024-01-01 09:00:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"李四\",\r\n            account: \"lisi\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 2,\r\n            zhiwei_title: \"业务经理\",\r\n            status: 1,\r\n            create_time: \"2024-01-02 10:30:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"王五\",\r\n            account: \"wangwu\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 3,\r\n            zhiwei_title: \"法务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-03 14:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"赵六\",\r\n            account: \"zhaoliu\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 4,\r\n            zhiwei_title: \"财务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-04 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"孙七\",\r\n            account: \"sunqi\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 5,\r\n            zhiwei_title: \"客服专员\",\r\n            status: 0,\r\n            create_time: \"2024-01-05 11:20:00\"\r\n          },\r\n          {\r\n            id: 6,\r\n            title: \"周八\",\r\n            account: \"zhouba\",\r\n            phone: \"***********\",\r\n            pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            zhiwei_id: 2,\r\n            zhiwei_title: \"业务专员\",\r\n            status: 1,\r\n            create_time: \"2024-01-06 08:30:00\"\r\n          }\r\n        ];\r\n        _this.total = 6;\r\n\r\n        // 同时获取职位数据用于筛选\r\n        _this.zhiweis = [\r\n          { id: 1, title: \"系统管理员\" },\r\n          { id: 2, title: \"业务经理\" },\r\n          { id: 3, title: \"法务专员\" },\r\n          { id: 4, title: \"财务专员\" },\r\n          { id: 5, title: \"客服专员\" }\r\n        ];\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.employee-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section .page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n}\r\n\r\n.search-form {\r\n  padding: 20px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.admin {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.active {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-icon.new {\r\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格样式 */\r\n.employee-table {\r\n  margin: 0;\r\n}\r\n\r\n.avatar-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.employee-avatar {\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.employee-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.employee-name-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.employee-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.employee-name.clickable {\r\n  cursor: pointer;\r\n  color: #409eff;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.employee-name.clickable:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.employee-account {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  background: #f0f2f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n  width: fit-content;\r\n}\r\n\r\n.phone-cell, .time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #606266;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  padding: 20px 0;\r\n}\r\n\r\n.employee-card-col {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.employee-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.employee-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.card-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-info {\r\n  flex: 1;\r\n}\r\n\r\n.card-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-position {\r\n  display: flex;\r\n}\r\n\r\n.card-status {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n}\r\n\r\n.card-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n}\r\n\r\n.detail-item i {\r\n  width: 16px;\r\n  color: #909399;\r\n}\r\n\r\n.card-actions {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .employee-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .employee-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .card-actions {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* 右侧滑出面板 */\r\n.employee-detail-panel {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2000;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.employee-detail-panel.panel-open {\r\n  pointer-events: auto;\r\n}\r\n\r\n.panel-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.panel-open .panel-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.panel-content {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 600px;\r\n  height: 100%;\r\n  background: white;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.panel-open .panel-content {\r\n  transform: translateX(0);\r\n}\r\n\r\n.panel-header {\r\n  padding: 20px 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.panel-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-title i {\r\n  font-size: 20px;\r\n}\r\n\r\n.panel-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.panel-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n}\r\n\r\n.employee-form {\r\n  padding: 0;\r\n}\r\n\r\n.form-section {\r\n  padding: 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.form-section:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #f0f2f5;\r\n}\r\n\r\n.section-title i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-col {\r\n  flex: 1;\r\n}\r\n\r\n.avatar-upload-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.current-avatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.preview-avatar {\r\n  cursor: pointer;\r\n  border: 2px solid #ebeef5;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.preview-avatar:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-controls {\r\n  flex: 1;\r\n}\r\n\r\n.avatar-input {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.upload-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.5;\r\n}\r\n\r\n.operation-record {\r\n  background: #fafbfc;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.record-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.record-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.record-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  min-width: 80px;\r\n}\r\n\r\n.record-value {\r\n  color: #303133;\r\n  flex: 1;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.view-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-top: 12px;\r\n}\r\n\r\n.no-avatar-text {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 查看模式样式 */\r\n.employee-form .el-input.is-disabled .el-input__inner,\r\n.employee-form .el-input__inner[readonly] {\r\n  background-color: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n}\r\n\r\n.employee-form .el-switch.is-disabled {\r\n  opacity: 0.8;\r\n}\r\n\r\n.employee-form .el-date-editor.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n}\r\n\r\n/* 响应式 - 面板 */\r\n@media (max-width: 768px) {\r\n  .panel-content {\r\n    width: 100%;\r\n  }\r\n\r\n  .form-row {\r\n    flex-direction: column;\r\n    gap: 0;\r\n  }\r\n\r\n  .avatar-upload-section {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n  }\r\n\r\n  .upload-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* 原有样式保留 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=04fb7389&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=04fb7389&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"04fb7389\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}