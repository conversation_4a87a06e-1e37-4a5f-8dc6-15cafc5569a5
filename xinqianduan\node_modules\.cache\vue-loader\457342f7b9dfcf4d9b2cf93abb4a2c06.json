{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\yonghu\\lawyer.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\yonghu\\lawyer.vue", "mtime": 1732626900099}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lawyer.vue"], "names": [], "mappings": ";AAgLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "lawyer.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入订单号/购买人/套餐\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\"> </el-table-column>\r\n        <el-table-column prop=\"type\" label=\"工单类型\"> </el-table-column>\r\n        <el-table-column prop=\"title\" label=\"工单标题\"> </el-table-column>\r\n        <el-table-column prop=\"desc\" label=\"工单内容\"> </el-table-column>\r\n        <el-table-column prop=\"is_deal\" label=\"处理状态\"> </el-table-column>\r\n        <el-table-column prop=\"uid\" label=\"用户手机\"> </el-table-column>\r\n        <el-table-column prop=\"dt_name\" label=\"债务人\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"发起时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              取消\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/lawyer/\",\r\n      title: \"律师函\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}