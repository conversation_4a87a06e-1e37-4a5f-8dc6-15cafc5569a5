{"version": 3, "sources": ["webpack:///./src/views/pages/wenshu/index.vue?b8f0", "webpack:///./src/views/pages/wenshu/index.vue", "webpack:///src/views/pages/wenshu/index.vue", "webpack:///./src/views/pages/wenshu/index.vue?4de1", "webpack:///./src/views/pages/wenshu/index.vue?2634"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "attrs", "on", "$event", "editData", "_v", "refulsh", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "slot", "searchData", "clearSearch", "directives", "name", "rawName", "loading", "list", "scopedSlots", "_u", "key", "fn", "scope", "_s", "row", "title", "getCategoryName", "cate_id", "price", "file_path", "create_time", "id", "previewContract", "_e", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "handleDialogClose", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "_l", "cates", "item", "index", "changefield", "handleSuccess", "delImage", "isClear", "change", "content", "cancelDialog", "saveData", "dialogPreview", "previewData", "split", "pop", "downloadFile", "domProps", "dialogVisible", "show_image", "staticRenderFns", "components", "EditorBar", "data", "allSize", "page", "url", "field", "info", "is_num", "required", "message", "trigger", "expireTimeOption", "disabledDate", "date", "getTime", "Date", "now", "mounted", "console", "log", "getData", "getLvshi", "document", "addEventListener", "handleKeyDown", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "watch", "newVal", "oldVal", "methods", "_this", "getInfo", "desc", "testCategories", "setTimeout", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "$router", "go", "cateId", "Array", "isArray", "warn", "category", "find", "fileUrl", "link", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "success", "$refs", "resetFields", "event", "keyCode", "testData", "update_time", "filteredData", "trim", "toLowerCase", "filter", "includes", "startIndex", "endIndex", "pageData", "slice", "length", "error", "validate", "valid", "postRequest", "msg", "val", "res", "showImage", "file", "beforeUpload", "filed", "isTypeTrue", "test", "fileName", "component"], "mappings": "kHAAA,W,2CCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACE,YAAY,UAAUE,MAAM,CAAC,KAAO,UAAU,KAAO,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAAS,MAAM,CAACT,EAAIU,GAAG,YAAYR,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,KAAO,mBAAmBC,GAAG,CAAC,MAAQP,EAAIW,UAAU,CAACX,EAAIU,GAAG,WAAW,OAAOR,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,UAAU,CAACE,YAAY,cAAcE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,WAAW,CAACE,YAAY,eAAeE,MAAM,CAAC,YAAc,eAAe,UAAY,IAAIM,MAAM,CAACC,MAAOb,EAAIc,OAAOC,QAASC,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIc,OAAQ,UAAWG,IAAME,WAAW,mBAAmB,CAACjB,EAAG,IAAI,CAACE,YAAY,gCAAgCE,MAAM,CAAC,KAAO,UAAUc,KAAK,cAAc,GAAGlB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIqB,gBAAgB,CAACrB,EAAIU,GAAG,UAAUR,EAAG,YAAY,CAACE,YAAY,YAAYE,MAAM,CAAC,KAAO,wBAAwBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIsB,iBAAiB,CAACtB,EAAIU,GAAG,WAAW,QAAQ,GAAGR,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,UAAU,CAACE,YAAY,aAAaE,MAAM,CAAC,OAAS,UAAU,CAACJ,EAAG,WAAW,CAACqB,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYZ,MAAOb,EAAI0B,QAASP,WAAW,YAAYf,YAAY,iBAAiBE,MAAM,CAAC,KAAON,EAAI2B,KAAK,OAAS,GAAG,OAAS,KAAK,CAACzB,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,OAAOsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIU,GAAGV,EAAIiC,GAAGD,EAAME,IAAIC,mBAAmBjC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,UAAU,MAAQ,OAAO,MAAQ,OAAOsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACN,EAAIU,GAAG,IAAIV,EAAIiC,GAAGjC,EAAIoC,gBAAgBJ,EAAME,IAAIG,UAAU,cAAcnC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,OAAO,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIU,GAAG,KAAKV,EAAIiC,GAAGD,EAAME,IAAII,OAAS,QAAQ,cAAcpC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,YAAY,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO0B,EAAME,IAAIK,UAAY,UAAY,UAAU,KAAO,UAAU,CAACvC,EAAIU,GAAG,IAAIV,EAAIiC,GAAGD,EAAME,IAAIK,UAAY,MAAQ,OAAO,cAAcrC,EAAG,kBAAkB,CAACI,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,OAAOsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIU,GAAGV,EAAIiC,GAAGD,EAAME,IAAIM,yBAAyBtC,EAAG,kBAAkB,CAACI,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUsB,YAAY5B,EAAI6B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIS,SAASuB,EAAME,IAAIO,OAAO,CAACzC,EAAIU,GAAG,UAAWsB,EAAME,IAAIK,UAAWrC,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,eAAe,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI0C,gBAAgBV,EAAME,QAAQ,CAAClC,EAAIU,GAAG,UAAUV,EAAI2C,KAAKzC,EAAG,YAAY,CAACE,YAAY,aAAaE,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,iBAAiB,MAAQ,IAAIC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI4C,QAAQZ,EAAMa,OAAQb,EAAME,IAAIO,OAAO,CAACzC,EAAIU,GAAG,WAAW,WAAW,GAAGR,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACI,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,IAAK,KAAK,YAAYN,EAAI8C,KAAK,OAAS,0CAA0C,MAAQ9C,EAAI+C,MAAM,WAAa,IAAIxC,GAAG,CAAC,cAAcP,EAAIgD,iBAAiB,iBAAiBhD,EAAIiD,wBAAwB,IAAI,IAAI,GAAG/C,EAAG,YAAY,CAACE,YAAY,cAAcE,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,QAAUnC,EAAIkD,kBAAkB,wBAAuB,EAAM,MAAQ,OAAO3C,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAIkD,kBAAkB1C,GAAQ,MAAQR,EAAImD,oBAAoB,CAACjD,EAAG,UAAU,CAACkD,IAAI,WAAW9C,MAAM,CAAC,MAAQN,EAAIqD,SAAS,MAAQrD,EAAIsD,QAAQ,CAACpD,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,cAAcN,EAAIuD,eAAe,KAAO,YAAY,CAACrD,EAAG,YAAY,CAACI,MAAM,CAAC,YAAc,MAAM,WAAa,IAAIM,MAAM,CAACC,MAAOb,EAAIqD,SAAShB,QAASrB,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIqD,SAAU,UAAWpC,IAAME,WAAW,qBAAqB,CAACjB,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,KAAK,CAACN,EAAIU,GAAG,SAASV,EAAIwD,GAAIxD,EAAIyD,OAAO,SAASC,EAAKC,GAAO,OAAOzD,EAAG,YAAY,CAAC4B,IAAI6B,EAAMrD,MAAM,CAAC,MAAQoD,EAAKvB,MAAM,MAAQuB,EAAKjB,UAAS,IAAI,GAAGvC,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQN,EAAImC,MAAQ,KAAK,cAAcnC,EAAIuD,eAAe,KAAO,UAAU,CAACrD,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,OAAOM,MAAM,CAACC,MAAOb,EAAIqD,SAASlB,MAAOnB,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIqD,SAAU,QAASpC,IAAME,WAAW,qBAAqB,GAAGjB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,OAAO,cAAcN,EAAIuD,eAAe,KAAO,cAAc,CAACrD,EAAG,WAAW,CAACE,YAAY,WAAWE,MAAM,CAAC,UAAW,GAAMM,MAAM,CAACC,MAAOb,EAAIqD,SAASd,UAAWvB,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIqD,SAAU,YAAapC,IAAME,WAAW,wBAAwBjB,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI4D,YAAY,gBAAgB,CAAC1D,EAAG,YAAY,CAACI,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAaN,EAAI6D,gBAAgB,CAAC7D,EAAIU,GAAG,WAAW,GAAIV,EAAIqD,SAASd,UAAWrC,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAI8D,SAAS9D,EAAIqD,SAASd,UAAW,gBAAgB,CAACvC,EAAIU,GAAG,QAAQV,EAAI2C,MAAM,IAAI,GAAGzC,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,cAAcN,EAAIuD,iBAAiB,CAACrD,EAAG,WAAW,CAACI,MAAM,CAAC,aAAe,MAAM,KAAO,UAAUM,MAAM,CAACC,MAAOb,EAAIqD,SAASf,MAAOtB,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIqD,SAAU,QAASpC,IAAME,WAAW,qBAAqB,GAAGjB,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,KAAK,cAAcN,EAAIuD,iBAAiB,CAACrD,EAAG,aAAa,CAACI,MAAM,CAAC,QAAUN,EAAI+D,SAASxD,GAAG,CAAC,OAASP,EAAIgE,QAAQpD,MAAM,CAACC,MAAOb,EAAIqD,SAASY,QAASjD,SAAS,SAAUC,GAAMjB,EAAIkB,KAAKlB,EAAIqD,SAAU,UAAWpC,IAAME,WAAW,uBAAuB,IAAI,GAAGjB,EAAG,MAAM,CAACE,YAAY,gBAAgBE,MAAM,CAAC,KAAO,UAAUc,KAAK,UAAU,CAAClB,EAAG,YAAY,CAACK,GAAG,CAAC,MAAQP,EAAIkE,eAAe,CAAClE,EAAIU,GAAG,SAASR,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAImE,cAAc,CAACnE,EAAIU,GAAG,UAAU,IAAI,GAAGR,EAAG,YAAY,CAACE,YAAY,iBAAiBE,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAIoE,cAAc,wBAAuB,EAAM,MAAQ,OAAO7D,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAIoE,cAAc5D,KAAU,CAACN,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAIqE,YAAYlC,UAAUjC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIU,GAAG,OAAOV,EAAIiC,GAAGjC,EAAIoC,gBAAgBpC,EAAIqE,YAAYhC,UAAU,OAAOnC,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIU,GAAG,QAAQV,EAAIiC,GAAGjC,EAAIqE,YAAY/B,OAAS,QAAQ,WAAWpC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAEJ,EAAIqE,YAAY9B,UAAWrC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIU,GAAGV,EAAIiC,GAAGjC,EAAIqE,YAAY9B,UAAU+B,MAAM,KAAKC,UAAUrE,EAAG,YAAY,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOR,EAAIwE,aAAaxE,EAAIqE,YAAY9B,cAAc,CAACvC,EAAIU,GAAG,WAAW,KAAKV,EAAI2C,KAAM3C,EAAIqE,YAAYJ,QAAS/D,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACF,EAAIU,GAAG,WAAWR,EAAG,MAAM,CAACE,YAAY,eAAeqE,SAAS,CAAC,UAAYzE,EAAIiC,GAAGjC,EAAIqE,YAAYJ,cAAcjE,EAAI2C,WAAWzC,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAI0E,cAAc,MAAQ,OAAOnE,GAAG,CAAC,iBAAiB,SAASC,GAAQR,EAAI0E,cAAclE,KAAU,CAACN,EAAG,WAAW,CAACI,MAAM,CAAC,IAAMN,EAAI2E,eAAe,IAAI,IAE1vQC,EAAkB,CAAC,WAAY,IAAI5E,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIU,GAAG,cAAcR,EAAG,IAAI,CAACE,YAAY,iBAAiB,CAACJ,EAAIU,GAAG,yB,YCyT7O,GACfc,KAAA,OACAqD,WAAA,CAAAC,kBACAC,OACA,OACAC,QAAA,OACArD,KAAA,GACAoB,MAAA,EACAkC,KAAA,EACAnC,KAAA,GACAhC,OAAA,CACAC,QAAA,IAEAW,SAAA,EACAwD,IAAA,WACAC,MAAA,GACAhD,MAAA,KACAiD,KAAA,GACAlC,mBAAA,EACAkB,eAAA,EACAC,YAAA,GACAM,WAAA,GACAD,eAAA,EACAX,SAAA,EACAV,SAAA,CACAlB,MAAA,GACAkD,OAAA,GAGA/B,MAAA,CACAnB,MAAA,CACA,CACAmD,UAAA,EACAC,QAAA,QACAC,QAAA,SAGAnD,QAAA,CACA,CACAiD,UAAA,EACAC,QAAA,UACAC,QAAA,SAGAjD,UAAA,CACA,CACA+C,UAAA,EACAC,QAAA,QACAC,QAAA,UAIAjC,eAAA,QACAE,MAAA,GACAgC,iBAAA,CACAC,aAAAC,GAEA,OAAAA,EAAAC,UAAAC,KAAAC,MAAA,UAKAC,UACAC,QAAAC,IAAA,oBACA,KAAAC,UACA,KAAAC,WAEAC,SAAAC,iBAAA,eAAAC,eAGA,KAAAC,UAAA,KACAP,QAAAC,IAAA,UACAD,QAAAC,IAAA,iBAAAtE,MACAqE,QAAAC,IAAA,oBAAAvE,YAIA8E,gBAEAJ,SAAAK,oBAAA,eAAAH,gBAGAI,MAAA,CACAxD,kBAAAyD,EAAAC,GACAZ,QAAAC,IAAA,YAAAU,EAAAC,IACAD,GAAAC,GAEA,KAAAzD,sBAKA0D,QAAA,CACA7C,WACAJ,YAAAuB,GACA,KAAAA,SAEA1E,SAAAgC,GACA,IAAAqE,EAAA,KACA,GAAArE,EACA,KAAAsE,QAAAtE,GAEA,KAAAY,SAAA,CACAlB,MAAA,GACA6E,KAAA,IAGAF,EAAAX,WACAW,EAAA5D,mBAAA,GAGAiD,WAEA,MAAAc,EAAA,CACA,CAAAxE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,YACA,CAAAvE,GAAA,EAAAN,MAAA,OAAA6E,KAAA,aAIAE,WAAA,KACA,KAAAzD,MAAAwD,EACAjB,QAAAC,IAAA,iBAAAxC,QACA,KAmBAsD,QAAAtE,GACA,IAAAqE,EAAA,KACAA,EAAAK,WAAAL,EAAA5B,IAAA,WAAAzC,GAAA2E,KAAAC,IACAA,IACAP,EAAAzD,SAAAgE,EAAAtC,SAIAnC,QAAAe,EAAAlB,GACA,KAAA6E,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,KAAAM,cAAA,KAAAxC,IAAA,aAAAzC,GAAA2E,KAAAC,IACA,KAAAA,EAAAM,OACA,KAAAC,SAAA,CACAH,KAAA,UACAlC,QAAA,UAEA,KAAA5D,KAAAkG,OAAAlE,EAAA,QAIAmE,MAAA,KACA,KAAAF,SAAA,CACAH,KAAA,QACAlC,QAAA,aAIA5E,UACA,KAAAoH,QAAAC,GAAA,IAEA3G,aACA,KAAA4D,KAAA,EACA,KAAAnC,KAAA,GACA,KAAAoD,WAIA5E,cACA,KAAAR,OAAAC,QAAA,GACA,KAAAM,cAIAe,gBAAA6F,GAEA,IAAAC,MAAAC,QAAA,KAAA1E,OAEA,OADAuC,QAAAoC,KAAA,8BAAA3E,OACA,MAEA,MAAA4E,EAAA,KAAA5E,MAAA6E,KAAA5E,KAAAjB,KAAAwF,GACA,OAAAI,IAAAlG,MAAA,OAIAO,gBAAAR,GACA8D,QAAAC,IAAA,QAAA/D,GACA,KAAAmC,YAAAnC,EACA,KAAAkC,eAAA,GAIAI,aAAA+D,GACA,MAAAC,EAAApC,SAAAqC,cAAA,KACAD,EAAAE,KAAAH,EACAC,EAAAG,SAAAJ,EAAAjE,MAAA,KAAAC,MACA6B,SAAAwC,KAAAC,YAAAL,GACAA,EAAAM,QACA1C,SAAAwC,KAAAG,YAAAP,GACA,KAAAZ,SAAAoB,QAAA,WAIA7F,oBACA6C,QAAAC,IAAA,aAEA,KAAAgD,MAAA5F,UACA,KAAA4F,MAAA5F,SAAA6F,cAEA,KAAA7F,SAAA,CACAlB,MAAA,GACAkD,OAAA,EACAhD,QAAA,GACAE,UAAA,GACAD,MAAA,GACA2B,QAAA,IAEA,KAAAF,SAAA,GAIAG,eACA8B,QAAAC,IAAA,UAEA,KAAAgD,MAAA5F,UACA,KAAA4F,MAAA5F,SAAA6F,cAEA,KAAA7F,SAAA,CACAlB,MAAA,GACAkD,OAAA,EACAhD,QAAA,GACAE,UAAA,GACAD,MAAA,GACA2B,QAAA,IAEA,KAAAF,SAAA,EACA,KAAAb,mBAAA,GAIAoD,cAAA6C,GAEA,KAAAA,EAAAC,SAAA,KAAAlG,mBACA,KAAAgB,gBAMAgC,UACA,IAAAY,EAAA,KAEAA,EAAApF,SAAA,EAGA,MAAA2H,EAAA,CACA,CACA5G,GAAA,EACAN,MAAA,UACAE,QAAA,EACAE,UAAA,mDACAD,MAAA,SACA2B,QAAA,mIACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,YACAE,QAAA,EACAE,UAAA,iDACAD,MAAA,SACA2B,QAAA,+HACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,aACAE,QAAA,EACAE,UAAA,gDACAD,MAAA,UACA2B,QAAA,qHACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,YACAE,QAAA,EACAE,UAAA,gDACAD,MAAA,SACA2B,QAAA,iHACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,YACAE,QAAA,EACAE,UAAA,iDACAD,MAAA,SACA2B,QAAA,uHACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,UACAE,QAAA,EACAE,UAAA,2CACAD,MAAA,SACA2B,QAAA,sHACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,YACAE,QAAA,EACAE,UAAA,oDACAD,MAAA,UACA2B,QAAA,sHACAzB,YAAA,sBACA8G,YAAA,uBAEA,CACA7G,GAAA,EACAN,MAAA,YACAE,QAAA,EACAE,UAAA,kDACAD,MAAA,UACA2B,QAAA,yHACAzB,YAAA,sBACA8G,YAAA,wBAKApC,WAAA,KACA,IACAlB,QAAAC,IAAA,eACAD,QAAAC,IAAA,UAAAoD,GAGA,IAAAE,EAAAF,EACA,GAAAvC,EAAAhG,OAAAC,SAAA+F,EAAAhG,OAAAC,QAAAyI,OAAA,CACA,MAAAzI,EAAA+F,EAAAhG,OAAAC,QAAAyI,OAAAC,cACAF,EAAAF,EAAAK,OAAAhG,GACAA,EAAAvB,MAAAsH,cAAAE,SAAA5I,IACA2C,EAAAO,QAAAwF,cAAAE,SAAA5I,IAEAiF,QAAAC,IAAA,SAAAlF,GACAiF,QAAAC,IAAA,QAAAsD,GAIA,MAAAK,GAAA9C,EAAA7B,KAAA,GAAA6B,EAAAhE,KACA+G,EAAAD,EAAA9C,EAAAhE,KACAgH,EAAAP,EAAAQ,MAAAH,EAAAC,GAEA/C,EAAAnF,KAAAmI,EACAhD,EAAA/D,MAAAwG,EAAAS,OACAlD,EAAApF,SAAA,EAEAsE,QAAAC,IAAA,OAAAa,EAAA7B,MACAe,QAAAC,IAAA,QAAAa,EAAAhE,MACAkD,QAAAC,IAAA,UAAA6D,GACA9D,QAAAC,IAAA,cAAAa,EAAAnF,MACAqE,QAAAC,IAAA,MAAAa,EAAA/D,OACAiD,QAAAC,IAAA,QAAAa,EAAApF,SACA,MAAAuI,GACAjE,QAAAiE,MAAA,YAAAA,GACAnD,EAAAnF,KAAA,GACAmF,EAAA/D,MAAA,EACA+D,EAAApF,SAAA,IAEA,MA8BAyC,WACA,IAAA2C,EAAA,KACA,KAAAmC,MAAA,YAAAiB,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAAtD,EAAA5B,IAAA,YAAA7B,UAAA+D,KAAAC,IACA,KAAAA,EAAAM,MACAb,EAAAc,SAAA,CACAH,KAAA,UACAlC,QAAA8B,EAAAgD,MAEA,KAAAnE,UACAY,EAAA5D,mBAAA,GAEA4D,EAAAc,SAAA,CACAH,KAAA,QACAlC,QAAA8B,EAAAgD,WASArH,iBAAAsH,GACA,KAAAxH,KAAAwH,EAEA,KAAApE,WAEAjD,oBAAAqH,GACA,KAAArF,KAAAqF,EACA,KAAApE,WAEArC,cAAA0G,GACA,KAAAA,EAAA5C,MACA,KAAAC,SAAAoB,QAAA,QACA,KAAA3F,SAAA,KAAA8B,OAAAoF,EAAAxF,KAAAG,KAEA,KAAA0C,SAAAqC,MAAAM,EAAAF,MAIAG,UAAAC,GACA,KAAA9F,WAAA8F,EACA,KAAA/F,eAAA,GAEAgG,aAAAD,GACA,oBAAAE,MAAA,CACA,MAAAC,EAAA,0BAAAC,KAAApD,MACA,IAAAmD,EAEA,YADA,KAAAhD,SAAAqC,MAAA,eAKAnG,SAAA2G,EAAAK,GACA,IAAAhE,EAAA,KACAA,EAAAK,WAAA,6BAAAsD,GAAArD,KAAAC,IACA,KAAAA,EAAAM,MACAb,EAAAzD,SAAAyH,GAAA,GAEAhE,EAAAc,SAAAoB,QAAA,UAEAlC,EAAAc,SAAAqC,MAAA5C,EAAAgD,UCtyB4W,I,wBCQxWU,EAAY,eACd,EACAhL,EACA6E,GACA,EACA,KACA,WACA,MAIa,aAAAmG,E", "file": "js/chunk-2ee5e81a.46db9ebd.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=7482c4f7&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"contract-list-container\"},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-right\"},[_c('el-button',{staticClass:\"add-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\"},on:{\"click\":function($event){return _vm.editData(0)}}},[_vm._v(\" 新增合同 \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"icon\":\"el-icon-refresh\"},on:{\"click\":_vm.refulsh}},[_vm._v(\" 刷新 \")])],1)])]),_c('div',{staticClass:\"search-section\"},[_c('el-card',{staticClass:\"search-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"search-content\"},[_c('div',{staticClass:\"search-left\"},[_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"搜索合同标题、类型...\",\"clearable\":\"\"},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-right\"},[_c('el-button',{staticClass:\"search-btn\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.searchData()}}},[_vm._v(\" 搜索 \")]),_c('el-button',{staticClass:\"clear-btn\",attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":function($event){return _vm.clearSearch()}}},[_vm._v(\" 重置 \")])],1)])])],1),_c('div',{staticClass:\"table-section\"},[_c('el-card',{staticClass:\"table-card\",attrs:{\"shadow\":\"never\"}},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"contract-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\",\"border\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"文书标题\",\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"title-cell\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_c('span',{staticClass:\"title-text\"},[_vm._v(_vm._s(scope.row.title))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"cate_id\",\"label\":\"文书类型\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(_vm.getCategoryName(scope.row.cate_id))+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"price\",\"label\":\"价格\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"price-text\"},[_c('i',{staticClass:\"el-icon-money\"}),_vm._v(\" ¥\"+_vm._s(scope.row.price || '0.00')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"file_path\",\"label\":\"文件状态\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.file_path ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.file_path ? '已上传' : '未上传')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"录入时间\",\"width\":\"180\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-cell\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(scope.row.create_time))])])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"180\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\" 编辑 \")]),(scope.row.file_path)?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-view\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.previewContract(scope.row)}}},[_vm._v(\" 预览 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 删除 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1)],1),_c('el-dialog',{staticClass:\"form-dialog\",attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event},\"close\":_vm.handleDialogClose}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"文书类型\",\"label-width\":_vm.formLabelWidth,\"prop\":\"cate_id\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.cate_id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"cate_id\", $$v)},expression:\"ruleForm.cate_id\"}},[_c('el-option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_vm._l((_vm.cates),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.title,\"value\":item.id}})})],2)],1),_c('el-form-item',{attrs:{\"label\":_vm.title + '标题',\"label-width\":_vm.formLabelWidth,\"prop\":\"title\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"文件上传\",\"label-width\":_vm.formLabelWidth,\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',{on:{\"click\":function($event){return _vm.changefield('file_path')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1),_c('el-form-item',{attrs:{\"label\":\"价格\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"number\"},model:{value:(_vm.ruleForm.price),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"price\", $$v)},expression:\"ruleForm.price\"}})],1),_c('el-form-item',{attrs:{\"label\":\"内容\",\"label-width\":_vm.formLabelWidth}},[_c('editor-bar',{attrs:{\"isClear\":_vm.isClear},on:{\"change\":_vm.change},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1)],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":_vm.cancelDialog}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{staticClass:\"preview-dialog\",attrs:{\"title\":\"文书预览\",\"visible\":_vm.dialogPreview,\"close-on-click-modal\":false,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.dialogPreview=$event}}},[_c('div',{staticClass:\"preview-content\"},[_c('div',{staticClass:\"preview-header\"},[_c('h3',[_vm._v(_vm._s(_vm.previewData.title))]),_c('div',{staticClass:\"preview-meta\"},[_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-folder\"}),_vm._v(\" 类型：\"+_vm._s(_vm.getCategoryName(_vm.previewData.cate_id))+\" \")]),_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-money\"}),_vm._v(\" 价格：¥\"+_vm._s(_vm.previewData.price || '0.00')+\" \")])])]),_c('div',{staticClass:\"preview-body\"},[(_vm.previewData.file_path)?_c('div',{staticClass:\"file-preview\"},[_c('div',{staticClass:\"file-info\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(_vm._s(_vm.previewData.file_path.split('/').pop()))]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-download\"},on:{\"click\":function($event){return _vm.downloadFile(_vm.previewData.file_path)}}},[_vm._v(\" 下载 \")])],1)]):_vm._e(),(_vm.previewData.content)?_c('div',{staticClass:\"content-preview\"},[_c('h4',[_vm._v(\"文书内容：\")]),_c('div',{staticClass:\"content-html\",domProps:{\"innerHTML\":_vm._s(_vm.previewData.content)}})]):_vm._e()])])]),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"header-left\"},[_c('h2',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 合同列表管理 \")]),_c('p',{staticClass:\"page-subtitle\"},[_vm._v(\"管理系统中的所有合同模板和文书\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"contract-list-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            合同列表管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统中的所有合同模板和文书</p>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增合同\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索和筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-content\">\r\n          <div class=\"search-left\">\r\n            <el-input\r\n              placeholder=\"搜索合同标题、类型...\"\r\n              v-model=\"search.keyword\"\r\n              class=\"search-input\"\r\n              clearable\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch()\"\r\n              class=\"clear-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"contract-table\"\r\n          stripe\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"文书标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"title-text\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"cate_id\" label=\"文书类型\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ getCategoryName(scope.row.cate_id) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"price\" label=\"价格\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"price-text\">\r\n                <i class=\"el-icon-money\"></i>\r\n                ¥{{ scope.row.price || '0.00' }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"file_path\" label=\"文件状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.file_path ? 'success' : 'warning'\"\r\n                size=\"small\"\r\n              >\r\n                {{ scope.row.file_path ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"录入时间\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-edit\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.file_path\"\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"previewContract(scope.row)\"\r\n                  icon=\"el-icon-view\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-delete\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"form-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"文书类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cate_id\"\r\n        >\r\n          <el-select v-model=\"ruleForm.cate_id\" placeholder=\"请选择\" filterable>\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in cates\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"文件上传\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changefield('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 文书预览对话框 -->\r\n    <el-dialog\r\n      title=\"文书预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              类型：{{ getCategoryName(previewData.cate_id) }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-money\"></i>\r\n              价格：¥{{ previewData.price || '0.00' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div v-if=\"previewData.file_path\" class=\"file-preview\">\r\n            <div class=\"file-info\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ previewData.file_path.split('/').pop() }}</span>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"downloadFile(previewData.file_path)\"\r\n                icon=\"el-icon-download\"\r\n              >\r\n                下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"previewData.content\" class=\"content-preview\">\r\n            <h4>文书内容：</h4>\r\n            <div class=\"content-html\" v-html=\"previewData.content\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshu/\",\r\n      field: \"\",\r\n      title: \"文书\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogPreview: false,\r\n      previewData: {},\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      isClear: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        cate_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择文书类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      cates: [],\r\n      expireTimeOption: {\r\n        disabledDate(date) {\r\n          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean\r\n          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    console.log('页面挂载完成，开始加载数据...');\r\n    this.getData();\r\n    this.getLvshi(); // 获取分类数据\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n\r\n    // 添加调试信息\r\n    this.$nextTick(() => {\r\n      console.log('页面渲染完成');\r\n      console.log('当前list数据:', this.list);\r\n      console.log('当前loading状态:', this.loading);\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  watch: {\r\n    dialogFormVisible(newVal, oldVal) {\r\n      console.log('对话框可见性变化:', newVal, oldVal);\r\n      if (!newVal && oldVal) {\r\n        // 对话框关闭时重置表单\r\n        this.handleDialogClose();\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    change() {},\r\n    changefield(field) {\r\n      this.field = field;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n      _this.getLvshi();\r\n      _this.dialogFormVisible = true;\r\n    },\r\n\r\n    getLvshi() {\r\n      // 添加测试分类数据\r\n      const testCategories = [\r\n        { id: 1, title: \"民事诉讼\", desc: \"民事纠纷相关文书\" },\r\n        { id: 2, title: \"商事诉讼\", desc: \"商业纠纷相关文书\" },\r\n        { id: 3, title: \"侵权诉讼\", desc: \"侵权纠纷相关文书\" },\r\n        { id: 4, title: \"婚姻家庭\", desc: \"婚姻家庭纠纷文书\" },\r\n        { id: 5, title: \"知识产权\", desc: \"知识产权纠纷文书\" },\r\n        { id: 6, title: \"劳动争议\", desc: \"劳动关系纠纷文书\" },\r\n        { id: 7, title: \"行政诉讼\", desc: \"行政纠纷相关文书\" },\r\n        { id: 8, title: \"刑事辩护\", desc: \"刑事案件相关文书\" }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        this.cates = testCategories;\r\n        console.log('加载测试分类数据:', this.cates);\r\n      }, 50);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      this.postRequest(\"/wenshucate/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          // 确保返回的数据是数组\r\n          this.cates = Array.isArray(resp.data) ? resp.data : [];\r\n          console.log('获取到的分类数据:', this.cates);\r\n        } else {\r\n          console.error('获取分类失败:', resp);\r\n          this.cates = [];\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取分类出错:', error);\r\n        this.cates = [];\r\n      });\r\n      */\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search.keyword = '';\r\n      this.searchData();\r\n    },\r\n\r\n    // 获取分类名称\r\n    getCategoryName(cateId) {\r\n      // 确保 cates 是数组\r\n      if (!Array.isArray(this.cates)) {\r\n        console.warn('cates is not an array:', this.cates);\r\n        return '未分类';\r\n      }\r\n      const category = this.cates.find(item => item.id === cateId);\r\n      return category ? category.title : '未分类';\r\n    },\r\n\r\n    // 预览文书\r\n    previewContract(row) {\r\n      console.log('预览文书:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      console.log('对话框关闭事件触发');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      console.log('取消按钮点击');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n      this.dialogFormVisible = false;\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27 && this.dialogFormVisible) {\r\n        this.cancelDialog();\r\n      }\r\n    },\r\n\r\n\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          title: \"民事起诉状模板\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/civil_complaint_template.docx\",\r\n          price: \"500.00\",\r\n          content: \"<p>这是一份标准的民事起诉状模板，适用于一般民事纠纷案件。包含完整的格式要求和必要条款。</p><p>主要内容包括：</p><ul><li>当事人基本信息</li><li>诉讼请求</li><li>事实与理由</li><li>证据清单</li></ul>\",\r\n          create_time: \"2024-01-15 10:30:00\",\r\n          update_time: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"劳动合同纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/labor_dispute_complaint.pdf\",\r\n          price: \"800.00\",\r\n          content: \"<p>专门针对劳动合同纠纷的起诉书模板，涵盖工资拖欠、违法解除等常见情形。</p><p>适用范围：</p><ul><li>工资拖欠纠纷</li><li>违法解除劳动合同</li><li>加班费争议</li><li>经济补偿金纠纷</li></ul>\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          update_time: \"2024-01-16 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"房屋买卖合同纠纷诉状\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/property_sale_dispute.docx\",\r\n          price: \"1200.00\",\r\n          content: \"<p>房屋买卖合同纠纷专用诉讼文书，包含房产交易中的各种争议处理。</p><p>涵盖问题：</p><ul><li>房屋质量问题</li><li>逾期交房</li><li>产权过户纠纷</li><li>定金违约</li></ul>\",\r\n          create_time: \"2024-01-17 09:15:00\",\r\n          update_time: \"2024-01-17 09:15:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"交通事故赔偿起诉书\",\r\n          cate_id: 3,\r\n          file_path: \"/uploads/documents/traffic_accident_claim.pdf\",\r\n          price: \"600.00\",\r\n          content: \"<p>交通事故人身损害赔偿起诉书模板，适用于各类交通事故赔偿案件。</p><p>赔偿项目：</p><ul><li>医疗费</li><li>误工费</li><li>护理费</li><li>精神损害抚慰金</li></ul>\",\r\n          create_time: \"2024-01-18 16:45:00\",\r\n          update_time: \"2024-01-18 16:45:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"借款合同纠纷起诉状\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/loan_dispute_complaint.docx\",\r\n          price: \"400.00\",\r\n          content: \"<p>民间借贷纠纷起诉状模板，适用于个人借款、企业借贷等各类借款纠纷。</p><p>主要条款：</p><ul><li>借款本金确认</li><li>利息计算标准</li><li>违约责任</li><li>担保责任</li></ul>\",\r\n          create_time: \"2024-01-19 11:30:00\",\r\n          update_time: \"2024-01-19 11:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"离婚纠纷起诉书\",\r\n          cate_id: 4,\r\n          file_path: \"/uploads/documents/divorce_complaint.pdf\",\r\n          price: \"900.00\",\r\n          content: \"<p>离婚纠纷起诉书模板，包含财产分割、子女抚养等完整内容。</p><p>主要内容：</p><ul><li>夫妻感情破裂事实</li><li>财产分割方案</li><li>子女抚养安排</li><li>债务承担</li></ul>\",\r\n          create_time: \"2024-01-20 13:20:00\",\r\n          update_time: \"2024-01-20 13:20:00\"\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"知识产权侵权起诉状\",\r\n          cate_id: 5,\r\n          file_path: \"/uploads/documents/ip_infringement_complaint.docx\",\r\n          price: \"1500.00\",\r\n          content: \"<p>知识产权侵权起诉状模板，适用于商标、专利、著作权等侵权案件。</p><p>保护范围：</p><ul><li>商标权侵权</li><li>专利权侵权</li><li>著作权侵权</li><li>商业秘密侵权</li></ul>\",\r\n          create_time: \"2024-01-21 15:10:00\",\r\n          update_time: \"2024-01-21 15:10:00\"\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"公司股权纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/equity_dispute_complaint.pdf\",\r\n          price: \"2000.00\",\r\n          content: \"<p>公司股权纠纷起诉书模板，处理股东权益、公司治理等复杂商事纠纷。</p><p>争议类型：</p><ul><li>股权转让纠纷</li><li>股东知情权</li><li>利润分配争议</li><li>公司决议效力</li></ul>\",\r\n          create_time: \"2024-01-22 10:00:00\",\r\n          update_time: \"2024-01-22 10:00:00\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载测试数据...');\r\n          console.log('原始测试数据:', testData);\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.content.toLowerCase().includes(keyword)\r\n            );\r\n            console.log('搜索关键词:', keyword);\r\n            console.log('搜索结果:', filteredData);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('当前页:', _this.page);\r\n          console.log('每页大小:', _this.size);\r\n          console.log('分页后的数据:', pageData);\r\n          console.log('设置到list的数据:', _this.list);\r\n          console.log('总数:', _this.total);\r\n          console.log('加载状态:', _this.loading);\r\n        } catch (error) {\r\n          console.error('加载测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 100); // 减少延迟到100ms\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            // 确保返回的数据是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n            console.log('获取到的列表数据:', _this.list);\r\n          } else {\r\n            console.error('获取数据失败:', resp);\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.field] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-list-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left .page-title i {\r\n  font-size: 28px;\r\n}\r\n\r\n.header-left .page-subtitle {\r\n  margin: 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.add-btn:hover, .refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e4e7ed;\r\n  padding-left: 40px;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn {\r\n  border: 2px solid #e4e7ed;\r\n  color: #606266;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn:hover {\r\n  border-color: #c0c4cc;\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格区域样式 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.contract-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.contract-table >>> .el-table__row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.title-cell i {\r\n  color: #667eea;\r\n  font-size: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.time-cell i {\r\n  color: #909399;\r\n}\r\n\r\n.price-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #e6a23c;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-text i {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n  border-width: 1px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #667eea;\r\n}\r\n\r\n.preview-body {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #667eea;\r\n}\r\n\r\n.file-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  background: white;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-info i {\r\n  color: #667eea;\r\n  font-size: 18px;\r\n}\r\n\r\n.content-preview h4 {\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.content-html {\r\n  background: white;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-list-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-left {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表单对话框样式 */\r\n.form-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__close {\r\n  color: white !important;\r\n  font-size: 20px !important;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__footer {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=7482c4f7&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7482c4f7&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7482c4f7\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}