<?php
namespace app\admin\controller;
use think\Request;
use untils\JsonService;
use models\Configs;
use app\controller\Base;
class Config 
{
    protected $model;
    public function __construct(Configs $model){
        //parent::__construct();
        $this->model=$model;
        
    }
     public function index()
    {   
        $res = $this->model->getAllData();
        return JsonService::successful('成功',$res);
    }

    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        $res = $this->model->saveData($form);
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功');
    }

}