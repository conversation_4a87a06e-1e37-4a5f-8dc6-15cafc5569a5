{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue?vue&type=template&id=64ff9702&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\quanxian.vue", "mtime": 1748540171931}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}