{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748616302957}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetail", "DebtDetail", "store", "name", "components", "data", "uploadAction", "uploadDebtsAction", "$store", "getters", "GET_TOKEN", "uploadVisible", "uploadDebtsVisible", "submitOrderLoading2", "submitOrderLoading3", "uploadData", "review", "uploadDebtsData", "allSize", "listUser", "list", "id", "uid", "tel", "money", "status", "back_money", "un_money", "ctime", "address", "idcard_no", "case_des", "users", "nickname", "total", "page", "currentId", "currentDebtId", "pageUser", "sizeUser", "searchUser", "keyword", "size", "search", "prop", "order", "loading", "url", "urlUser", "title", "info", "images", "attach_path", "cards", "debttrans", "dialogUserFormVisible", "dialogViewUserDetail", "drawerViewUserDetail", "drawerViewDebtDetail", "dialogZfrqVisible", "dialogRichangVisible", "dialogHuikuanVisible", "dialogDebttransFormVisible", "dialogFormVisible", "viewFormVisible", "dialogViewDebtDetail", "show_image", "dialogVisible", "ruleFormDebttrans", "ruleForm", "del_images", "del_attach_path", "rulesDebttrans", "day", "required", "message", "trigger", "rules", "form<PERSON>abe<PERSON><PERSON>", "options", "activeDebtTab", "activeUserTab", "activeDebtDetailTab", "userDebtsList", "phone", "amount", "debtDocuments", "type", "uploadTime", "mounted", "getData", "methods", "changeFile", "filed", "searchUserData", "getUserData", "ruledata", "_this", "postRequest", "then", "resp", "code", "typeClick", "$set", "editRateMoney", "selUserData", "currentRow", "payTypeClick", "clearData", "editData", "getInfo", "viewUserData", "viewDebtData", "editDebttransData", "getDebttransInfo", "viewData", "get<PERSON>iew", "desc", "getRequest", "$message", "msg", "console", "log", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "delData", "index", "splice", "delDataDebt", "refulsh", "$router", "go", "searchData", "isDevelopment", "process", "env", "NODE_ENV", "window", "location", "hostname", "setTimeout", "count", "saveData", "$refs", "validate", "valid", "saveDebttransData", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "arr", "error", "showImage", "file", "showUserList", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "handleSortChange", "column", "exports", "href", "exportsDebtList", "closeUploadDialog", "upload", "clearFiles", "closeUploadDebtsDialog", "uploadSuccess", "response", "uploadDebtsSuccess", "checkFile", "fileType", "split", "slice", "toLowerCase", "includes", "submitUpload", "submit", "submitUploadDebts", "closeDialog", "addVisible", "form", "mobile", "school_id", "grade_id", "class_id", "sex", "is_poor", "is_display", "number", "remark", "is_remark_option", "remark_option", "mobile_checked", "resetFields", "openUpload", "openUploadDebts", "handleDrawerClose", "handleUserDetailDrawerClose", "handleDebtDetailDrawerClose", "handleUserTabSelect", "handleDebtDetailTabSelect", "handleDebtTabSelect", "getEvidenceTitle", "tab", "getEvidenceTypeText", "hasEvidence", "length", "uploadEvidence"], "sources": ["src/views/pages/debt/debts.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <div @click=\"viewUserData(scope.row.uid)\" class=\"clickable-text\">{{scope.row.users.nickname}}</div>\r\n            </template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\" class=\"clickable-text\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <!-- 债务人编辑抽屉 -->\r\n    <el-drawer\r\n      title=\"债务人管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleDrawerClose\">\r\n      <div class=\"drawer-content-wrapper\">\r\n        <!-- 左侧导航菜单 -->\r\n        <div class=\"drawer-sidebar\">\r\n          <el-menu\r\n            :default-active=\"activeDebtTab\"\r\n            class=\"drawer-menu\"\r\n            @select=\"handleDebtTabSelect\">\r\n            <el-menu-item index=\"details\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>债务人详情</span>\r\n            </el-menu-item>\r\n            <el-submenu index=\"evidence\">\r\n              <template slot=\"title\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据</span>\r\n              </template>\r\n              <el-menu-item index=\"evidence-all\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>全部</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-video\">\r\n                <i class=\"el-icon-video-camera\"></i>\r\n                <span>视频</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-image\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                <span>图片</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-audio\">\r\n                <i class=\"el-icon-microphone\"></i>\r\n                <span>语音</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-document\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span>文档</span>\r\n              </el-menu-item>\r\n            </el-submenu>\r\n          </el-menu>\r\n        </div>\r\n\r\n        <!-- 右侧内容区域 -->\r\n        <div class=\"drawer-content\">\r\n          <!-- 债务人详情标签页 -->\r\n          <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                债务人详情\r\n              </div>\r\n              \r\n              <div v-if=\"ruleForm.is_user == 1\">\r\n                <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n              </div>\r\n              \r\n              <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 20px;\">\r\n                <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" style=\"margin-top: 20px;\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择用户\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n                      <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">选择用户</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"用户信息\" v-if=\"ruleForm.utel\">\r\n                      {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人姓名\">\r\n                      <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人电话\">\r\n                      <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"身份证号码\">\r\n                      <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务金额\">\r\n                      <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"债务人地址\">\r\n                  <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"案由描述\">\r\n                  <el-input v-model=\"ruleForm.case_des\" autocomplete=\"off\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n              </el-form>\r\n\r\n              <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 30px;\">\r\n                <el-descriptions-item>\r\n                  <el-table :data=\"ruleForm.debttrans\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"></el-table-column>\r\n                    <el-table-column prop=\"status_name\" label=\"跟进状态\"></el-table-column>\r\n                    <el-table-column prop=\"type_name\" label=\"跟进类型\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额（元）\"></el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"></el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">移除</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"drawer-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确定</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 证据管理标签页 -->\r\n          <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                {{ getEvidenceTitle() }}\r\n                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                  <i class=\"el-icon-plus\"></i> 上传证据\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 证据列表 -->\r\n              <div class=\"evidence-container\">\r\n                <!-- 身份证照片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>身份证照片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('cards')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传身份证\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.cards && ruleForm.cards.length > 0\">\r\n                      <div v-for=\"(item7, index7) in ruleForm.cards\" :key=\"index7\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <img :src=\"item7\" @click=\"showImage(item7)\" class=\"evidence-image\" />\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item7, 'cards', index7)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据图片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据图片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('images')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传图片\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.images && ruleForm.images.length > 0\">\r\n                      <div v-for=\"(item5, index5) in ruleForm.images\" :key=\"index5\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <el-image style=\"width: 100%; height: 150px;\" :src=\"item5\" :preview-src-list=\"ruleForm.images\" fit=\"cover\"></el-image>\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item5, 'images', index5)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的图片 -->\r\n                    <div v-if=\"ruleForm.del_images && ruleForm.del_images.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的图片</h5>\r\n                      <div class=\"evidence-grid\">\r\n                        <div v-for=\"(item8, index8) in ruleForm.del_images\" :key=\"index8\" class=\"evidence-item\">\r\n                          <div class=\"evidence-preview\">\r\n                            <el-image style=\"width: 100%; height: 150px;\" :src=\"item8\" :preview-src-list=\"ruleForm.del_images\" fit=\"cover\"></el-image>\r\n                          </div>\r\n                          <div class=\"evidence-actions\">\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item8, 'del_images', index8)\">删除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据文件 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-document'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据文件</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('attach_path')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传文件\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"file-list\" v-if=\"ruleForm.attach_path && ruleForm.attach_path.length > 0\">\r\n                      <div v-for=\"(item6, index6) in ruleForm.attach_path\" :key=\"index6\" class=\"file-item\" v-if=\"item6\">\r\n                        <div class=\"file-icon\">\r\n                          <i class=\"el-icon-document file-type-icon\"></i>\r\n                        </div>\r\n                        <div class=\"file-info\">\r\n                          <div class=\"file-name\">文件{{ index6 + 1 }}</div>\r\n                        </div>\r\n                        <div class=\"file-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                          </el-button>\r\n                          <el-button type=\"success\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item6, 'attach_path', index6)\">移除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的文件 -->\r\n                    <div v-if=\"ruleForm.del_attach_path && ruleForm.del_attach_path.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的文件</h5>\r\n                      <div class=\"file-list\">\r\n                        <div v-for=\"(item9, index9) in ruleForm.del_attach_path\" :key=\"index9\" class=\"file-item\" v-if=\"item9\">\r\n                          <div class=\"file-icon\">\r\n                            <i class=\"el-icon-document file-type-icon\"></i>\r\n                          </div>\r\n                          <div class=\"file-info\">\r\n                            <div class=\"file-name\">文件{{ index9 + 1 }}</div>\r\n                          </div>\r\n                          <div class=\"file-actions\">\r\n                            <el-button type=\"primary\" size=\"mini\">\r\n                              <a :href=\"item9\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                            </el-button>\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item9, 'del_attach_path', index9)\">移除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空状态 -->\r\n                <div v-if=\"!hasEvidence()\" class=\"no-evidence\">\r\n                  <i class=\"el-icon-folder-opened\"></i>\r\n                  <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                  <br>\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                    <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n            title=\"用户列表\"\r\n            :visible.sync=\"dialogUserFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\">\r\n\r\n        <el-row style=\"width: 300px\">\r\n            <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"searchUserData()\"\r\n                ></el-button>\r\n            </el-input>\r\n        </el-row>\r\n\r\n        <el-table\r\n                :data=\"listUser\"\r\n                style=\"width: 100%; margin-top: 10px\"\r\n                size=\"mini\"\r\n                @current-change=\"selUserData\"\r\n        >\r\n            <el-table-column label=\"选择\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n            <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n            <el-table-column prop=\"\" label=\"头像\">\r\n                <template slot-scope=\"scope\">\r\n                    <div>\r\n\r\n                        <el-row v-if=\"scope.row.headimg==''\">\r\n                            <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                        </el-row>\r\n                        <el-row v-else>\r\n                            <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                        </el-row>\r\n\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n            <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n            <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n            <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n            <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        </el-table>\r\n\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n      <!-- 债务人详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewDebtDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleDebtDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span>债务人详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeDebtDetailTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleDebtDetailTabSelect\">\r\n              <el-menu-item index=\"details\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>债务详情</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"progress\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>跟进记录</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据材料</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"documents\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>相关文档</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\" style=\"overflow-x: auto; max-width: 100%;\">\r\n                <!-- 债务详情 -->\r\n                <div v-if=\"activeDebtDetailTab === 'details'\">\r\n                  <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n                </div>\r\n\r\n                <!-- 跟进记录 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'progress'\">\r\n                  <h3 class=\"section-title\">跟进记录</h3>\r\n                  <el-timeline>\r\n                    <el-timeline-item timestamp=\"2024-01-15 10:30\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>电话联系</h4>\r\n                        <p>已与债务人取得联系，对方表示将在本月底前还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-10 14:20\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>发送催款函</h4>\r\n                        <p>向债务人发送正式催款函，要求在15日内还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-05 09:15\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>案件受理</h4>\r\n                        <p>案件正式受理，开始债务追讨程序</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                  </el-timeline>\r\n                </div>\r\n\r\n                <!-- 证据材料 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'evidence'\">\r\n                  <h3 class=\"section-title\">证据材料</h3>\r\n                  <div class=\"evidence-grid\">\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-picture\"></i>\r\n                      <span>借条照片</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-chat-line-square\"></i>\r\n                      <span>聊天记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-bank-card\"></i>\r\n                      <span>转账记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 相关文档 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'documents'\">\r\n                  <h3 class=\"section-title\">相关文档</h3>\r\n                  <el-table :data=\"debtDocuments\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"文档名称\"></el-table-column>\r\n                    <el-table-column prop=\"type\" label=\"文档类型\"></el-table-column>\r\n                    <el-table-column prop=\"uploadTime\" label=\"上传时间\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\">下载</el-button>\r\n                        <el-button type=\"text\">预览</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n\r\n\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!-- 用户详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewUserDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleUserDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          <span>用户详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeUserTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleUserTabSelect\">\r\n              <el-menu-item index=\"customer\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>客户信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"member\">\r\n                <i class=\"el-icon-medal\"></i>\r\n                <span>会员信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"debts\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>债务人信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"attachments\">\r\n                <i class=\"el-icon-folder-opened\"></i>\r\n                <span>附件信息</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\">\r\n                <!-- 客户信息 -->\r\n                <div v-if=\"activeUserTab === 'customer'\">\r\n                  <user-detail :id=\"currentId\"></user-detail>\r\n                </div>\r\n\r\n                <!-- 会员信息 -->\r\n                <div v-else-if=\"activeUserTab === 'member'\">\r\n                  <h3 class=\"section-title\">会员信息</h3>\r\n                  <el-descriptions :column=\"2\" border>\r\n                    <el-descriptions-item label=\"会员等级\">普通会员</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员状态\">正常</el-descriptions-item>\r\n                    <el-descriptions-item label=\"注册时间\">2024-01-01</el-descriptions-item>\r\n                    <el-descriptions-item label=\"最后登录\">2024-01-15</el-descriptions-item>\r\n                    <el-descriptions-item label=\"积分余额\">1000</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员权益\">基础服务</el-descriptions-item>\r\n                  </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 债务人信息 -->\r\n                <div v-else-if=\"activeUserTab === 'debts'\">\r\n                  <h3 class=\"section-title\">关联债务人信息</h3>\r\n                  <el-table :data=\"userDebtsList\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"债务人姓名\"></el-table-column>\r\n                    <el-table-column prop=\"phone\" label=\"联系电话\"></el-table-column>\r\n                    <el-table-column prop=\"amount\" label=\"债务金额\"></el-table-column>\r\n                    <el-table-column prop=\"status\" label=\"状态\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" @click=\"viewDebtData(scope.row.id)\">查看详情</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n\r\n                <!-- 附件信息 -->\r\n                <div v-else-if=\"activeUserTab === 'attachments'\">\r\n                  <h3 class=\"section-title\">相关附件</h3>\r\n                  <div class=\"attachment-grid\">\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证正面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证反面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>营业执照</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetail from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetail, DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [\r\n        {\r\n          id: 1,\r\n          uid: 1001,\r\n          name: \"张三\",\r\n          tel: \"13800138001\",\r\n          money: \"50000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"50000\",\r\n          ctime: \"2024-01-15 10:30:00\",\r\n          address: \"北京市朝阳区建国路88号\",\r\n          idcard_no: \"110101199001011234\",\r\n          case_des: \"借款纠纷，借款人未按约定时间还款\",\r\n          users: {\r\n            nickname: \"李四\"\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          uid: 1002,\r\n          name: \"王五\",\r\n          tel: \"13900139002\",\r\n          money: \"120000\",\r\n          status: \"调节中\",\r\n          back_money: \"30000\",\r\n          un_money: \"90000\",\r\n          ctime: \"2024-01-10 14:20:00\",\r\n          address: \"上海市浦东新区陆家嘴金融区\",\r\n          idcard_no: \"310101199205155678\",\r\n          case_des: \"合同纠纷，未按合同约定支付货款\",\r\n          users: {\r\n            nickname: \"赵六\"\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          uid: 1003,\r\n          name: \"陈七\",\r\n          tel: \"13700137003\",\r\n          money: \"80000\",\r\n          status: \"诉讼中\",\r\n          back_money: \"20000\",\r\n          un_money: \"60000\",\r\n          ctime: \"2024-01-05 09:15:00\",\r\n          address: \"广州市天河区珠江新城\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"服务费纠纷，拒绝支付约定的服务费用\",\r\n          users: {\r\n            nickname: \"孙八\"\r\n          }\r\n        },\r\n        {\r\n          id: 4,\r\n          uid: 1004,\r\n          name: \"刘九\",\r\n          tel: \"13600136004\",\r\n          money: \"200000\",\r\n          status: \"已结案\",\r\n          back_money: \"200000\",\r\n          un_money: \"0\",\r\n          ctime: \"2023-12-20 16:45:00\",\r\n          address: \"深圳市南山区科技园\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"投资纠纷，已通过调解达成一致\",\r\n          users: {\r\n            nickname: \"周十\"\r\n          }\r\n        },\r\n        {\r\n          id: 5,\r\n          uid: 1005,\r\n          name: \"吴十一\",\r\n          tel: \"13500135005\",\r\n          money: \"75000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"75000\",\r\n          ctime: \"2024-01-18 11:30:00\",\r\n          address: \"杭州市西湖区文三路\",\r\n          idcard_no: \"330101199406067890\",\r\n          case_des: \"租赁纠纷，拖欠房租及违约金\",\r\n          users: {\r\n            nickname: \"郑十二\"\r\n          }\r\n        },\r\n        {\r\n          id: 6,\r\n          uid: 1006,\r\n          name: \"马十三\",\r\n          tel: \"13400134006\",\r\n          money: \"150000\",\r\n          status: \"调节中\",\r\n          back_money: \"50000\",\r\n          un_money: \"100000\",\r\n          ctime: \"2024-01-12 13:20:00\",\r\n          address: \"成都市锦江区春熙路\",\r\n          idcard_no: \"510101199009091234\",\r\n          case_des: \"买卖合同纠纷，货物质量问题导致损失\",\r\n          users: {\r\n            nickname: \"冯十四\"\r\n          }\r\n        }\r\n      ],\r\n      total: 6,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      drawerViewUserDetail: false,\r\n      drawerViewDebtDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n      activeDebtTab: 'details',\r\n      activeUserTab: 'customer',\r\n      activeDebtDetailTab: 'details',\r\n      userDebtsList: [\r\n        {\r\n          id: 1,\r\n          name: \"债务人A\",\r\n          phone: \"13900139001\",\r\n          amount: \"50000\",\r\n          status: \"处理中\"\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"债务人B\",\r\n          phone: \"13900139002\",\r\n          amount: \"30000\",\r\n          status: \"已完成\"\r\n        }\r\n      ],\r\n      debtDocuments: [\r\n        {\r\n          name: \"借款合同.pdf\",\r\n          type: \"合同文件\",\r\n          uploadTime: \"2024-01-10\"\r\n        },\r\n        {\r\n          name: \"催款函.doc\",\r\n          type: \"法律文书\",\r\n          uploadTime: \"2024-01-12\"\r\n        },\r\n        {\r\n          name: \"还款计划.xlsx\",\r\n          type: \"财务文件\",\r\n          uploadTime: \"2024-01-15\"\r\n        }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n      _this.activeDebtTab = 'details';\r\n      _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.drawerViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.drawerViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      \r\n      // 开发模式：使用示例数据，不发送HTTP请求\r\n      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';\r\n      \r\n      if (isDevelopment) {\r\n        // 模拟异步加载\r\n        setTimeout(() => {\r\n          // 这里的数据已经在data()中定义了，所以直接设置loading为false\r\n          _this.loading = false;\r\n        }, 500);\r\n        return;\r\n      }\r\n      \r\n      // 生产模式：发送HTTP请求获取真实数据\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n    handleDrawerClose() {\r\n      this.dialogFormVisible = false;\r\n    },\r\n    handleUserDetailDrawerClose() {\r\n      this.drawerViewUserDetail = false;\r\n      this.activeUserTab = 'customer';\r\n    },\r\n    handleDebtDetailDrawerClose() {\r\n      this.drawerViewDebtDetail = false;\r\n      this.activeDebtDetailTab = 'details';\r\n    },\r\n    handleUserTabSelect(index) {\r\n      this.activeUserTab = index;\r\n    },\r\n    handleDebtDetailTabSelect(index) {\r\n      this.activeDebtDetailTab = index;\r\n    },\r\n    handleDebtTabSelect(index) {\r\n      this.activeDebtTab = index;\r\n    },\r\n    getEvidenceTitle() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部证据';\r\n        case 'evidence-video':\r\n          return '视频证据';\r\n        case 'evidence-image':\r\n          return '图片证据';\r\n        case 'evidence-audio':\r\n          return '语音证据';\r\n        case 'evidence-document':\r\n          return '文档证据';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    getEvidenceTypeText() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部';\r\n        case 'evidence-video':\r\n          return '视频';\r\n        case 'evidence-image':\r\n          return '图片';\r\n        case 'evidence-audio':\r\n          return '语音';\r\n        case 'evidence-document':\r\n          return '文档';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    hasEvidence() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return this.ruleForm.cards.length > 0 || this.ruleForm.images.length > 0 || this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-video':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-image':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-audio':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-document':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    uploadEvidence() {\r\n      // Implementation of uploadEvidence method\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.drawer-content-wrapper {\r\n  display: flex;\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.drawer-sidebar {\r\n  width: 220px;\r\n  padding: 20px 10px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-right: none;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawer-menu {\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n  border-radius: 8px;\r\n  margin-bottom: 8px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n}\r\n\r\n.drawer-menu .el-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.drawer-menu .el-menu-item.is-active {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item {\r\n  padding-left: 40px;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  padding: 25px;\r\n  overflow-y: auto;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.tab-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  padding: 25px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8ecf0;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* 抽屉标题样式 */\r\n.drawer-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.drawer-title i {\r\n  margin-right: 8px;\r\n  font-size: 20px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 现代抽屉样式 */\r\n.modern-drawer .el-drawer__header {\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e8ecf0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n.modern-drawer .el-drawer__body {\r\n  padding: 0;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn {\r\n  color: #606266;\r\n  font-size: 18px;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn:hover {\r\n  color: #409eff;\r\n}\r\n\r\n.card-header {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.evidence-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.evidence-section h4 {\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.evidence-section h5 {\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.evidence-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: #fff;\r\n}\r\n\r\n.evidence-item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n  width: 100%;\r\n  height: 150px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.evidence-actions {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #409eff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.file-type-icon {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.drawer-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.no-evidence {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n  border: 2px dashed #dcdfe6;\r\n}\r\n\r\n.no-evidence i {\r\n  font-size: 48px;\r\n  margin-bottom: 15px;\r\n  display: block;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .drawer-content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .drawer-sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n  \r\n  .evidence-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 10px;\r\n  }\r\n  \r\n  .file-item {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .file-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 可点击文本样式 */\r\n.clickable-text {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n.clickable-text:hover {\r\n  color: #ffffff;\r\n  background-color: #409eff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 标题样式 */\r\n.section-title {\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n  position: relative;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 30px;\r\n  height: 2px;\r\n  background-color: #67c23a;\r\n}\r\n\r\n/* 附件网格样式 */\r\n.attachment-grid, .evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.attachment-item, .evidence-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e8ecf0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.attachment-item:hover, .evidence-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.attachment-item i, .evidence-item i {\r\n  font-size: 24px;\r\n  color: #409eff;\r\n  margin-right: 10px;\r\n}\r\n\r\n.attachment-item span, .evidence-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n.el-timeline-item__content .el-card {\r\n  margin-bottom: 0;\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-timeline-item__content .el-card h4 {\r\n  color: #409eff;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-timeline-item__content .el-card p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 表格溢出处理 */\r\n.drawer-content .card {\r\n  overflow-x: auto;\r\n  max-width: 100%;\r\n}\r\n\r\n.drawer-content .el-table {\r\n  min-width: 1200px; /* 设置表格最小宽度 */\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n  overflow-x: auto;\r\n}\r\n\r\n/* 抽屉内容区域样式优化 */\r\n.drawer-content-wrapper {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  padding: 0 20px 20px 0;\r\n}\r\n</style>\r\n"], "mappings": "AA6yBA;AACA,OAAAA,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAJ,UAAA;IAAAC;EAAA;EACAI,KAAA;IACA;MACAC,YAAA;MACAC,iBAAA,0CAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,UAAA;QACAC,MAAA;MACA;MACAC,eAAA;QACAD,MAAA;MACA;MACAE,OAAA;MACAC,QAAA;MACAC,IAAA,GACA;QACAC,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,EACA;MACAC,KAAA;MACAC,IAAA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,UAAA;QACAC,OAAA;MACA;MACAC,IAAA;MACAC,MAAA;QACAF,OAAA;QACAhB,MAAA;QACAmB,IAAA;QACAC,KAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,OAAA;MACAC,KAAA;MACAC,IAAA;QACAC,MAAA;QACAC,WAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,qBAAA;MACAC,oBAAA;MACAC,oBAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,oBAAA;MACAC,0BAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,oBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,iBAAA;QACAnB,KAAA;MACA;MACAoB,QAAA;QACAlB,MAAA;QACAmB,UAAA;QACAlB,WAAA;QACAmB,eAAA;QACAlB,KAAA;QACAC,SAAA;MACA;MACAkB,cAAA;QACAC,GAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAnD,MAAA,GACA;UACAiD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MAEAC,KAAA;QACAvD,GAAA,GACA;UACAoD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAzE,IAAA,GACA;UACAuE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACApD,KAAA,GACA;UACAkD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACA7C,QAAA,GACA;UACA2C,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,cAAA;MACAC,OAAA,GACA;QACA1D,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,EACA;MACA+B,aAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,aAAA,GACA;QACA9D,EAAA;QACAlB,IAAA;QACAiF,KAAA;QACAC,MAAA;QACA5D,MAAA;MACA,GACA;QACAJ,EAAA;QACAlB,IAAA;QACAiF,KAAA;QACAC,MAAA;QACA5D,MAAA;MACA,EACA;MACA6D,aAAA,GACA;QACAnF,IAAA;QACAoF,IAAA;QACAC,UAAA;MACA,GACA;QACArF,IAAA;QACAoF,IAAA;QACAC,UAAA;MACA,GACA;QACArF,IAAA;QACAoF,IAAA;QACAC,UAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACAC,eAAA;MACA,KAAAxD,QAAA;MACA,KAAAC,QAAA;MACA,KAAAwD,WAAA,MAAA1B,QAAA;IACA;IAEA0B,YAAAC,QAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAA5B,QAAA,GAAA2B,QAAA;MACAC,KAAA,CACAC,WAAA,CACAD,KAAA,CAAAjD,OAAA,mBAAAiD,KAAA,CAAA3D,QAAA,cAAA2D,KAAA,CAAA1D,QAAA,EACA0D,KAAA,CAAAzD,UACA,EACA2D,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAlC,iBAAA;UACAkC,KAAA,CAAA9E,QAAA,GAAAiF,IAAA,CAAA/F,IAAA;QACA;MACA;IACA;IACAiG,UAAAT,KAAA;MACA,KAAAU,IAAA,MAAAnC,iBAAA;MACA,KAAAmC,IAAA,MAAAnC,iBAAA;MACA,KAAAmC,IAAA,MAAAnC,iBAAA;MACA,KAAAmC,IAAA,MAAAnC,iBAAA;MACA,IAAAyB,KAAA;QACA,KAAAhC,oBAAA;QACA,KAAAF,iBAAA;QACA,SAAAS,iBAAA;UACA,KAAAR,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;QACA,KAAAA,oBAAA;QACA,KAAAC,oBAAA;QACA,SAAAO,iBAAA;UACA,KAAAT,iBAAA;QACA;UACA,KAAAA,iBAAA;QACA;MACA;IACA;IACA6C,cAAA;MACA,SAAApC,iBAAA,qBAAAA,iBAAA;QACA;QACA,KAAAmC,IAAA,MAAAnC,iBAAA,qBAAAA,iBAAA,gBAAAA,iBAAA;MACA;IACA;IACAqC,YAAAC,UAAA;MACA,IAAAA,UAAA;QACA,KAAAH,IAAA,MAAAlC,QAAA,SAAAqC,UAAA,CAAArF,EAAA;QACA,IAAAqF,UAAA,CAAAtB,KAAA;UACA,KAAAmB,IAAA,MAAAlC,QAAA,UAAAqC,UAAA,CAAAtB,KAAA;QACA;QACA,IAAAsB,UAAA,CAAAzE,QAAA;UACA,KAAAsE,IAAA,MAAAlC,QAAA,WAAAqC,UAAA,CAAAzE,QAAA;QACA;QACA,KAAA8B,iBAAA;QACA,KAAAR,qBAAA;MACA;IACA;IACAoD,aAAAd,KAAA;MACA,IAAAA,KAAA,SAAAA,KAAA;QACA,SAAAzB,iBAAA;UACA,KAAAR,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;MACA,IAAAiC,KAAA;QACA,SAAAzB,iBAAA;UACA,KAAAT,iBAAA;QACA;UACA,KAAAA,iBAAA;QACA;MACA;MACA,IAAAkC,KAAA;QACA,KAAAlC,iBAAA;QACA,KAAAC,oBAAA;QACA,SAAAQ,iBAAA;UACA,KAAAP,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;IACA;IACA+C,UAAA;MACA,KAAAjE,MAAA;QACAF,OAAA;QACAhB,MAAA;QACAmB,IAAA;QACAC,KAAA;MACA;MACA,KAAA6C,OAAA;IACA;IACAmB,SAAAxF,EAAA;MACA,IAAA4E,KAAA;MACA,IAAA5E,EAAA;QACA,KAAAyF,OAAA,CAAAzF,EAAA;MACA;QACA,KAAAgD,QAAA;UACAlB,MAAA;UACAmB,UAAA;UACAlB,WAAA;UACAmB,eAAA;UACAlB,KAAA;UACAC,SAAA;QACA;MACA;MACA2C,KAAA,CAAAjB,aAAA;MACAiB,KAAA,CAAAlC,iBAAA;IACA;IACAgD,aAAA1F,EAAA;MACA,IAAA4E,KAAA;MACA,IAAA5E,EAAA;QACA,KAAAe,SAAA,GAAAf,EAAA;MACA;MAEA4E,KAAA,CAAAxC,oBAAA;IACA;IACAuD,aAAA3F,EAAA;MACA,IAAA4E,KAAA;MACA,IAAA5E,EAAA;QACA,KAAAgB,aAAA,GAAAhB,EAAA;MACA;MAEA4E,KAAA,CAAAvC,oBAAA;IACA;IACAuD,kBAAA5F,EAAA;MACA,IAAAA,EAAA;QACA,KAAA6F,gBAAA,CAAA7F,EAAA;MACA;QACA,KAAA+C,iBAAA;UACAjE,IAAA;QACA;MACA;IACA;IACAgH,SAAA9F,EAAA;MACA,IAAAA,EAAA;QACA,KAAA+F,OAAA,CAAA/F,EAAA;MACA;QACA,KAAAgD,QAAA;UACApB,KAAA;UACAoE,IAAA;QACA;MACA;IACA;IACAD,QAAA/F,EAAA;MACA,IAAA4E,KAAA;MACAA,KAAA,CAAAqB,UAAA,CAAArB,KAAA,CAAAlD,GAAA,gBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA/C,IAAA,GAAAkD,IAAA,CAAA/F,IAAA;UACA4F,KAAA,CAAAjC,eAAA;UACAiC,KAAA,CAAA3F,YAAA,8BAAAe,EAAA,oBAAAb,MAAA,CAAAC,OAAA,CAAAC,SAAA;QACA;UACAuF,KAAA,CAAAsB,QAAA;YACAhC,IAAA;YACAZ,OAAA,EAAAyB,IAAA,CAAAoB;UACA;QACA;MACA;IACA;IACAV,QAAAzF,EAAA;MACA,IAAA4E,KAAA;MACAA,KAAA,CAAAqB,UAAA,CAAArB,KAAA,CAAAlD,GAAA,gBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA5B,QAAA,GAAA+B,IAAA,CAAA/F,IAAA;UACAoH,OAAA,CAAAC,GAAA,CAAAtB,IAAA,CAAA/F,IAAA;QACA;UACA4F,KAAA,CAAAsB,QAAA;YACAhC,IAAA;YACAZ,OAAA,EAAAyB,IAAA,CAAAoB;UACA;QACA;MACA;IACA;IACAN,iBAAA7F,EAAA;MACA,IAAA4E,KAAA;MACAA,KAAA,CAAAqB,UAAA,CAAArB,KAAA,CAAAlD,GAAA,yBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA7B,iBAAA,GAAAgC,IAAA,CAAA/F,IAAA;UACA4F,KAAA,CAAAtC,iBAAA;UACAsC,KAAA,CAAArC,oBAAA;UACAqC,KAAA,CAAApC,oBAAA;UACAoC,KAAA,CAAAnC,0BAAA;QACA;UACAmC,KAAA,CAAAsB,QAAA;YACAhC,IAAA;YACAZ,OAAA,EAAAyB,IAAA,CAAAoB;UACA;QACA;MACA;IACA;IACAG,QAAAtG,EAAA;MACA,KAAAuG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAvC,IAAA;MACA,GACAY,IAAA;QACA,KAAA4B,aAAA,MAAAhF,GAAA,mBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAkB,QAAA;cACAhC,IAAA;cACAZ,OAAA,EAAAyB,IAAA,CAAAoB;YACA;UACA;YACA,KAAAD,QAAA;cACAhC,IAAA;cACAZ,OAAA,EAAAyB,IAAA,CAAAoB;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACA,KAAAT,QAAA;UACAhC,IAAA;UACAZ,OAAA;QACA;MACA;IACA;IACAsD,QAAAC,KAAA,EAAA7G,EAAA;MACA,KAAAuG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAvC,IAAA;MACA,GACAY,IAAA;QACA,KAAA4B,aAAA,MAAAhF,GAAA,kBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAkB,QAAA;cACAhC,IAAA;cACAZ,OAAA;YACA;YACA,KAAAe,OAAA;YACA,KAAAxC,IAAA,CAAAI,SAAA,CAAA6E,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAT,QAAA;UACAhC,IAAA;UACAZ,OAAA;QACA;MACA;IACA;IACAyD,YAAAF,KAAA,EAAA7G,EAAA;MACA,KAAAuG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAvC,IAAA;MACA,GACAY,IAAA;QACA,KAAA4B,aAAA,MAAAhF,GAAA,sBAAA1B,EAAA,EAAA8E,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAkB,QAAA;cACAhC,IAAA;cACAZ,OAAA;YACA;YACA,KAAAe,OAAA;YACA,KAAAxC,IAAA,CAAAI,SAAA,CAAA6E,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAT,QAAA;UACAhC,IAAA;UACAZ,OAAA;QACA;MACA;IACA;IACA0D,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAArG,IAAA;MACA,KAAAO,IAAA;MACA,KAAAgD,OAAA;IACA;IAEAA,QAAA;MACA,IAAAO,KAAA;MAEAA,KAAA,CAAAnD,OAAA;;MAEA;MACA,MAAA2F,aAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,sBAAAC,MAAA,CAAAC,QAAA,CAAAC,QAAA;MAEA,IAAAN,aAAA;QACA;QACAO,UAAA;UACA;UACA/C,KAAA,CAAAnD,OAAA;QACA;QACA;MACA;;MAEA;MACAmD,KAAA,CACAC,WAAA,CACAD,KAAA,CAAAlD,GAAA,mBAAAkD,KAAA,CAAA9D,IAAA,cAAA8D,KAAA,CAAAvD,IAAA,EACAuD,KAAA,CAAAtD,MACA,EACAwD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA7E,IAAA,GAAAgF,IAAA,CAAA/F,IAAA;UACA4F,KAAA,CAAA/D,KAAA,GAAAkE,IAAA,CAAA6C,KAAA;QACA;QACAhD,KAAA,CAAAnD,OAAA;MACA;IACA;IACAoG,SAAA;MACA,IAAAjD,KAAA;MACA,KAAAkD,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAnD,WAAA,CAAAD,KAAA,CAAAlD,GAAA,gBAAAsB,QAAA,EAAA8B,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAJ,KAAA,CAAAsB,QAAA;gBACAhC,IAAA;gBACAZ,OAAA,EAAAyB,IAAA,CAAAoB;cACA;cACA,KAAA9B,OAAA;cACAO,KAAA,CAAAlC,iBAAA;YACA;cACAkC,KAAA,CAAAsB,QAAA;gBACAhC,IAAA;gBACAZ,OAAA,EAAAyB,IAAA,CAAAoB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA8B,kBAAA;MACA,IAAArD,KAAA;MACA,KAAAkD,KAAA,sBAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAjF,iBAAA,YAAAlE,KAAA,CAAAO,OAAA,CAAAC,SAAA;UACA,KAAAwF,WAAA,CAAAD,KAAA,CAAAlD,GAAA,yBAAAqB,iBAAA,EAAA+B,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAJ,KAAA,CAAAsB,QAAA;gBACAhC,IAAA;gBACAZ,OAAA,EAAAyB,IAAA,CAAAoB;cACA;cACA,KAAA9B,OAAA;cACAO,KAAA,CAAAtC,iBAAA;cACAsC,KAAA,CAAArC,oBAAA;cACAqC,KAAA,CAAApC,oBAAA;cACAoC,KAAA,CAAAnC,0BAAA;YACA;cACAmC,KAAA,CAAAsB,QAAA;gBACAhC,IAAA;gBACAZ,OAAA,EAAAyB,IAAA,CAAAoB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA+B,iBAAAC,GAAA;MACA,KAAA9G,IAAA,GAAA8G,GAAA;MAEA,KAAA9D,OAAA;IACA;IACA+D,oBAAAD,GAAA;MACA,KAAArH,IAAA,GAAAqH,GAAA;MACA,KAAA9D,OAAA;IACA;IACAgE,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAAtD,IAAA;QACA,KAAAkB,QAAA,CAAAqC,OAAA;QACA,IAAAC,GAAA,QAAAxF,QAAA,MAAAwB,KAAA;QAEA,KAAAxB,QAAA,MAAAwB,KAAA,EAAAsC,MAAA,OAAAwB,GAAA,CAAAtJ,IAAA,CAAA0C,GAAA;QACA;MACA;QACA,KAAAwE,QAAA,CAAAuC,KAAA,CAAAH,GAAA,CAAAnC,GAAA;MACA;IACA;IAEAuC,UAAAC,IAAA;MACA,KAAA9F,UAAA,GAAA8F,IAAA;MACA,KAAA7F,aAAA;IACA;IAEA8F,aAAA;MACA,KAAAnE,cAAA;MACA,KAAAvC,qBAAA;IACA;IACA2G,aAAAF,IAAA;MACA,MAAAG,UAAA,6BAAAC,IAAA,CAAAJ,IAAA,CAAAzE,IAAA;MACA,KAAA4E,UAAA;QACA,KAAA5C,QAAA,CAAAuC,KAAA;QACA;MACA;IACA;IACAO,SAAAL,IAAA,EAAAM,QAAA,EAAApC,KAAA;MACA,IAAAjC,KAAA;MACAA,KAAA,CAAAqB,UAAA,gCAAA0C,IAAA,EAAA7D,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA5B,QAAA,CAAAiG,QAAA,EAAAnC,MAAA,CAAAD,KAAA;UACAjC,KAAA,CAAAsB,QAAA,CAAAqC,OAAA;QACA;UACA3D,KAAA,CAAAsB,QAAA,CAAAuC,KAAA,CAAA1D,IAAA,CAAAoB,GAAA;QACA;MACA;IACA;IACA+C,iBAAA;MAAAC,MAAA;MAAA5H,IAAA;MAAAC;IAAA;MACA,KAAAF,MAAA,CAAAC,IAAA,GAAAA,IAAA;MACA,KAAAD,MAAA,CAAAE,KAAA,GAAAA,KAAA;MACA,KAAA6C,OAAA;MACA;MACA;IACA;IACA+E,OAAA,WAAAA,CAAA;MAAA;MACA,IAAAxE,KAAA;MACA6C,QAAA,CAAA4B,IAAA,+BAAAzE,KAAA,CAAAzF,MAAA,CAAAC,OAAA,CAAAC,SAAA,qBAAAuF,KAAA,CAAA5B,QAAA,CAAAhD,EAAA;IACA;IACAsJ,eAAA,WAAAA,CAAA;MAAA;MACA,IAAA1E,KAAA;MACA6C,QAAA,CAAA4B,IAAA,qCAAAzE,KAAA,CAAAzF,MAAA,CAAAC,OAAA,CAAAC,SAAA,iBAAAuF,KAAA,CAAAtD,MAAA,CAAAF,OAAA;IACA;IACAmI,kBAAA;MAAA;MACA,KAAAjK,aAAA;MACA,KAAAwI,KAAA,CAAA0B,MAAA,CAAAC,UAAA;MACA,KAAA/J,UAAA,CAAAC,MAAA;IACA;IACA+J,uBAAA;MAAA;MACA,KAAAnK,kBAAA;MACA,KAAAuI,KAAA,CAAA0B,MAAA,CAAAC,UAAA;MACA,KAAA7J,eAAA,CAAAD,MAAA;IACA;IACAgK,cAAAC,QAAA;MAAA;MACA,IAAAA,QAAA,CAAA5E,IAAA;QACA,KAAAkB,QAAA;UACAhC,IAAA;UACAZ,OAAA,EAAAsG,QAAA,CAAAzD;QACA;QACA,KAAA7G,aAAA;QACA,KAAA+E,OAAA;QACA+B,OAAA,CAAAC,GAAA,CAAAuD,QAAA;MACA;QACA,KAAA1D,QAAA;UACAhC,IAAA;UACAZ,OAAA,EAAAsG,QAAA,CAAAzD;QACA;MACA;MAEA,KAAA3G,mBAAA;MACA,KAAAsI,KAAA,CAAA0B,MAAA,CAAAC,UAAA;IACA;IACAI,mBAAAD,QAAA;MAAA;MACA,IAAAA,QAAA,CAAA5E,IAAA;QACA,KAAAkB,QAAA;UACAhC,IAAA;UACAZ,OAAA,EAAAsG,QAAA,CAAAzD;QACA;QACA,KAAA5G,kBAAA;QACA,KAAA8E,OAAA;QACA+B,OAAA,CAAAC,GAAA,CAAAuD,QAAA;MACA;QACA,KAAA1D,QAAA;UACAhC,IAAA;UACAZ,OAAA,EAAAsG,QAAA,CAAAzD;QACA;MACA;MAEA,KAAA1G,mBAAA;MACA,KAAAqI,KAAA,CAAA0B,MAAA,CAAAC,UAAA;IACA;IACAK,UAAAnB,IAAA;MAAA;MACA,IAAAoB,QAAA;MACA,IAAA7F,IAAA,GAAAyE,IAAA,CAAA7J,IAAA,CAAAkL,KAAA,MAAAC,KAAA,QAAAC,WAAA;MACA,KAAAH,QAAA,CAAAI,QAAA,CAAAjG,IAAA;QACA,KAAAgC,QAAA;UACAhC,IAAA;UACAZ,OAAA;QACA;QACA;MACA;MACA;IACA;IACA8G,aAAA;MAAA;MACA,KAAA5K,mBAAA;MACA,KAAAsI,KAAA,CAAA0B,MAAA,CAAAa,MAAA;IACA;IACAC,kBAAA;MAAA;MACA,KAAA7K,mBAAA;MACA,KAAAqI,KAAA,CAAA0B,MAAA,CAAAa,MAAA;IACA;IACAE,YAAA;MAAA;MACA,KAAAC,UAAA;MACA,KAAAlL,aAAA;MACA,KAAAmL,IAAA;QACAzK,EAAA;QACAY,QAAA;QACA8J,MAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,GAAA;QACAC,OAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA,KAAAvD,KAAA,CAAA2C,IAAA,CAAAa,WAAA;IACA;IACAC,WAAA;MAAA;MACA,KAAAjM,aAAA;IACA;IACAkM,gBAAA;MAAA;MACA,KAAAjM,kBAAA;IACA;IACAkM,kBAAA;MACA,KAAA/I,iBAAA;IACA;IACAgJ,4BAAA;MACA,KAAAtJ,oBAAA;MACA,KAAAwB,aAAA;IACA;IACA+H,4BAAA;MACA,KAAAtJ,oBAAA;MACA,KAAAwB,mBAAA;IACA;IACA+H,oBAAA/E,KAAA;MACA,KAAAjD,aAAA,GAAAiD,KAAA;IACA;IACAgF,0BAAAhF,KAAA;MACA,KAAAhD,mBAAA,GAAAgD,KAAA;IACA;IACAiF,oBAAAjF,KAAA;MACA,KAAAlD,aAAA,GAAAkD,KAAA;IACA;IACAkF,iBAAA;MACA,MAAAC,GAAA,QAAArI,aAAA;MACA,QAAAqI,GAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAC,oBAAA;MACA,MAAAD,GAAA,QAAArI,aAAA;MACA,QAAAqI,GAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAE,YAAA;MACA,MAAAF,GAAA,QAAArI,aAAA;MACA,QAAAqI,GAAA;QACA;UACA,YAAAhJ,QAAA,CAAAhB,KAAA,CAAAmK,MAAA,aAAAnJ,QAAA,CAAAlB,MAAA,CAAAqK,MAAA,aAAAnJ,QAAA,CAAAjB,WAAA,CAAAoK,MAAA;QACA;UACA,YAAAnJ,QAAA,CAAAlB,MAAA,CAAAqK,MAAA;QACA;UACA,YAAAnJ,QAAA,CAAAlB,MAAA,CAAAqK,MAAA;QACA;UACA,YAAAnJ,QAAA,CAAAjB,WAAA,CAAAoK,MAAA;QACA;UACA,YAAAnJ,QAAA,CAAAjB,WAAA,CAAAoK,MAAA;QACA;UACA;MACA;IACA;IACAC,eAAA;MACA;IAAA;EAEA;AACA", "ignoreList": []}]}