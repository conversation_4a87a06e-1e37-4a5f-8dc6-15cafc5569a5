{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748616302957}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["debts.vue"], "names": [], "mappings": ";AA6yBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debts.vue", "sourceRoot": "src/views/pages/debt", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <div @click=\"viewUserData(scope.row.uid)\" class=\"clickable-text\">{{scope.row.users.nickname}}</div>\r\n            </template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\" class=\"clickable-text\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <!-- 债务人编辑抽屉 -->\r\n    <el-drawer\r\n      title=\"债务人管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleDrawerClose\">\r\n      <div class=\"drawer-content-wrapper\">\r\n        <!-- 左侧导航菜单 -->\r\n        <div class=\"drawer-sidebar\">\r\n          <el-menu\r\n            :default-active=\"activeDebtTab\"\r\n            class=\"drawer-menu\"\r\n            @select=\"handleDebtTabSelect\">\r\n            <el-menu-item index=\"details\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>债务人详情</span>\r\n            </el-menu-item>\r\n            <el-submenu index=\"evidence\">\r\n              <template slot=\"title\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据</span>\r\n              </template>\r\n              <el-menu-item index=\"evidence-all\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>全部</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-video\">\r\n                <i class=\"el-icon-video-camera\"></i>\r\n                <span>视频</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-image\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                <span>图片</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-audio\">\r\n                <i class=\"el-icon-microphone\"></i>\r\n                <span>语音</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-document\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span>文档</span>\r\n              </el-menu-item>\r\n            </el-submenu>\r\n          </el-menu>\r\n        </div>\r\n\r\n        <!-- 右侧内容区域 -->\r\n        <div class=\"drawer-content\">\r\n          <!-- 债务人详情标签页 -->\r\n          <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                债务人详情\r\n              </div>\r\n              \r\n              <div v-if=\"ruleForm.is_user == 1\">\r\n                <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n              </div>\r\n              \r\n              <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 20px;\">\r\n                <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" style=\"margin-top: 20px;\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择用户\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n                      <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">选择用户</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"用户信息\" v-if=\"ruleForm.utel\">\r\n                      {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人姓名\">\r\n                      <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人电话\">\r\n                      <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"身份证号码\">\r\n                      <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务金额\">\r\n                      <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"债务人地址\">\r\n                  <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"案由描述\">\r\n                  <el-input v-model=\"ruleForm.case_des\" autocomplete=\"off\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n              </el-form>\r\n\r\n              <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 30px;\">\r\n                <el-descriptions-item>\r\n                  <el-table :data=\"ruleForm.debttrans\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"></el-table-column>\r\n                    <el-table-column prop=\"status_name\" label=\"跟进状态\"></el-table-column>\r\n                    <el-table-column prop=\"type_name\" label=\"跟进类型\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额（元）\"></el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"></el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">移除</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"drawer-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确定</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 证据管理标签页 -->\r\n          <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                {{ getEvidenceTitle() }}\r\n                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                  <i class=\"el-icon-plus\"></i> 上传证据\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 证据列表 -->\r\n              <div class=\"evidence-container\">\r\n                <!-- 身份证照片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>身份证照片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('cards')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传身份证\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.cards && ruleForm.cards.length > 0\">\r\n                      <div v-for=\"(item7, index7) in ruleForm.cards\" :key=\"index7\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <img :src=\"item7\" @click=\"showImage(item7)\" class=\"evidence-image\" />\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item7, 'cards', index7)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据图片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据图片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('images')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传图片\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.images && ruleForm.images.length > 0\">\r\n                      <div v-for=\"(item5, index5) in ruleForm.images\" :key=\"index5\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <el-image style=\"width: 100%; height: 150px;\" :src=\"item5\" :preview-src-list=\"ruleForm.images\" fit=\"cover\"></el-image>\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item5, 'images', index5)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的图片 -->\r\n                    <div v-if=\"ruleForm.del_images && ruleForm.del_images.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的图片</h5>\r\n                      <div class=\"evidence-grid\">\r\n                        <div v-for=\"(item8, index8) in ruleForm.del_images\" :key=\"index8\" class=\"evidence-item\">\r\n                          <div class=\"evidence-preview\">\r\n                            <el-image style=\"width: 100%; height: 150px;\" :src=\"item8\" :preview-src-list=\"ruleForm.del_images\" fit=\"cover\"></el-image>\r\n                          </div>\r\n                          <div class=\"evidence-actions\">\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item8, 'del_images', index8)\">删除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据文件 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-document'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据文件</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('attach_path')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传文件\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"file-list\" v-if=\"ruleForm.attach_path && ruleForm.attach_path.length > 0\">\r\n                      <div v-for=\"(item6, index6) in ruleForm.attach_path\" :key=\"index6\" class=\"file-item\" v-if=\"item6\">\r\n                        <div class=\"file-icon\">\r\n                          <i class=\"el-icon-document file-type-icon\"></i>\r\n                        </div>\r\n                        <div class=\"file-info\">\r\n                          <div class=\"file-name\">文件{{ index6 + 1 }}</div>\r\n                        </div>\r\n                        <div class=\"file-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                          </el-button>\r\n                          <el-button type=\"success\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item6, 'attach_path', index6)\">移除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的文件 -->\r\n                    <div v-if=\"ruleForm.del_attach_path && ruleForm.del_attach_path.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的文件</h5>\r\n                      <div class=\"file-list\">\r\n                        <div v-for=\"(item9, index9) in ruleForm.del_attach_path\" :key=\"index9\" class=\"file-item\" v-if=\"item9\">\r\n                          <div class=\"file-icon\">\r\n                            <i class=\"el-icon-document file-type-icon\"></i>\r\n                          </div>\r\n                          <div class=\"file-info\">\r\n                            <div class=\"file-name\">文件{{ index9 + 1 }}</div>\r\n                          </div>\r\n                          <div class=\"file-actions\">\r\n                            <el-button type=\"primary\" size=\"mini\">\r\n                              <a :href=\"item9\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                            </el-button>\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item9, 'del_attach_path', index9)\">移除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空状态 -->\r\n                <div v-if=\"!hasEvidence()\" class=\"no-evidence\">\r\n                  <i class=\"el-icon-folder-opened\"></i>\r\n                  <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                  <br>\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                    <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n            title=\"用户列表\"\r\n            :visible.sync=\"dialogUserFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\">\r\n\r\n        <el-row style=\"width: 300px\">\r\n            <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"searchUserData()\"\r\n                ></el-button>\r\n            </el-input>\r\n        </el-row>\r\n\r\n        <el-table\r\n                :data=\"listUser\"\r\n                style=\"width: 100%; margin-top: 10px\"\r\n                size=\"mini\"\r\n                @current-change=\"selUserData\"\r\n        >\r\n            <el-table-column label=\"选择\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n            <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n            <el-table-column prop=\"\" label=\"头像\">\r\n                <template slot-scope=\"scope\">\r\n                    <div>\r\n\r\n                        <el-row v-if=\"scope.row.headimg==''\">\r\n                            <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                        </el-row>\r\n                        <el-row v-else>\r\n                            <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                        </el-row>\r\n\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n            <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n            <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n            <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n            <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        </el-table>\r\n\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n      <!-- 债务人详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewDebtDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleDebtDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span>债务人详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeDebtDetailTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleDebtDetailTabSelect\">\r\n              <el-menu-item index=\"details\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>债务详情</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"progress\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>跟进记录</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据材料</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"documents\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>相关文档</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\" style=\"overflow-x: auto; max-width: 100%;\">\r\n                <!-- 债务详情 -->\r\n                <div v-if=\"activeDebtDetailTab === 'details'\">\r\n                  <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n                </div>\r\n\r\n                <!-- 跟进记录 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'progress'\">\r\n                  <h3 class=\"section-title\">跟进记录</h3>\r\n                  <el-timeline>\r\n                    <el-timeline-item timestamp=\"2024-01-15 10:30\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>电话联系</h4>\r\n                        <p>已与债务人取得联系，对方表示将在本月底前还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-10 14:20\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>发送催款函</h4>\r\n                        <p>向债务人发送正式催款函，要求在15日内还款</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item timestamp=\"2024-01-05 09:15\" placement=\"top\">\r\n                      <el-card>\r\n                        <h4>案件受理</h4>\r\n                        <p>案件正式受理，开始债务追讨程序</p>\r\n                      </el-card>\r\n                    </el-timeline-item>\r\n                  </el-timeline>\r\n                </div>\r\n\r\n                <!-- 证据材料 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'evidence'\">\r\n                  <h3 class=\"section-title\">证据材料</h3>\r\n                  <div class=\"evidence-grid\">\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-picture\"></i>\r\n                      <span>借条照片</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-chat-line-square\"></i>\r\n                      <span>聊天记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                    <div class=\"evidence-item\">\r\n                      <i class=\"el-icon-bank-card\"></i>\r\n                      <span>转账记录</span>\r\n                      <el-button type=\"text\">查看</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 相关文档 -->\r\n                <div v-else-if=\"activeDebtDetailTab === 'documents'\">\r\n                  <h3 class=\"section-title\">相关文档</h3>\r\n                  <el-table :data=\"debtDocuments\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"文档名称\"></el-table-column>\r\n                    <el-table-column prop=\"type\" label=\"文档类型\"></el-table-column>\r\n                    <el-table-column prop=\"uploadTime\" label=\"上传时间\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\">下载</el-button>\r\n                        <el-button type=\"text\">预览</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n\r\n\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!-- 用户详情抽屉 -->\r\n      <el-drawer\r\n        :visible.sync=\"drawerViewUserDetail\"\r\n        direction=\"rtl\"\r\n        size=\"70%\"\r\n        :before-close=\"handleUserDetailDrawerClose\"\r\n        custom-class=\"modern-drawer\">\r\n        <div slot=\"title\" class=\"drawer-title\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          <span>用户详情</span>\r\n        </div>\r\n        <div class=\"drawer-content-wrapper\">\r\n          <!-- 左侧导航菜单 -->\r\n          <div class=\"drawer-sidebar\">\r\n            <el-menu\r\n              :default-active=\"activeUserTab\"\r\n              class=\"drawer-menu\"\r\n              @select=\"handleUserTabSelect\">\r\n              <el-menu-item index=\"customer\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>客户信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"member\">\r\n                <i class=\"el-icon-medal\"></i>\r\n                <span>会员信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"debts\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>债务人信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"attachments\">\r\n                <i class=\"el-icon-folder-opened\"></i>\r\n                <span>附件信息</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n\r\n          <!-- 右侧内容区域 -->\r\n          <div class=\"drawer-content\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"card\">\r\n                <!-- 客户信息 -->\r\n                <div v-if=\"activeUserTab === 'customer'\">\r\n                  <user-detail :id=\"currentId\"></user-detail>\r\n                </div>\r\n\r\n                <!-- 会员信息 -->\r\n                <div v-else-if=\"activeUserTab === 'member'\">\r\n                  <h3 class=\"section-title\">会员信息</h3>\r\n                  <el-descriptions :column=\"2\" border>\r\n                    <el-descriptions-item label=\"会员等级\">普通会员</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员状态\">正常</el-descriptions-item>\r\n                    <el-descriptions-item label=\"注册时间\">2024-01-01</el-descriptions-item>\r\n                    <el-descriptions-item label=\"最后登录\">2024-01-15</el-descriptions-item>\r\n                    <el-descriptions-item label=\"积分余额\">1000</el-descriptions-item>\r\n                    <el-descriptions-item label=\"会员权益\">基础服务</el-descriptions-item>\r\n                  </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 债务人信息 -->\r\n                <div v-else-if=\"activeUserTab === 'debts'\">\r\n                  <h3 class=\"section-title\">关联债务人信息</h3>\r\n                  <el-table :data=\"userDebtsList\" style=\"width: 100%\">\r\n                    <el-table-column prop=\"name\" label=\"债务人姓名\"></el-table-column>\r\n                    <el-table-column prop=\"phone\" label=\"联系电话\"></el-table-column>\r\n                    <el-table-column prop=\"amount\" label=\"债务金额\"></el-table-column>\r\n                    <el-table-column prop=\"status\" label=\"状态\"></el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" @click=\"viewDebtData(scope.row.id)\">查看详情</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n\r\n                <!-- 附件信息 -->\r\n                <div v-else-if=\"activeUserTab === 'attachments'\">\r\n                  <h3 class=\"section-title\">相关附件</h3>\r\n                  <div class=\"attachment-grid\">\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证正面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>身份证反面</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                    <div class=\"attachment-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <span>营业执照</span>\r\n                      <el-button type=\"text\">下载</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetail from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetail, DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [\r\n        {\r\n          id: 1,\r\n          uid: 1001,\r\n          name: \"张三\",\r\n          tel: \"13800138001\",\r\n          money: \"50000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"50000\",\r\n          ctime: \"2024-01-15 10:30:00\",\r\n          address: \"北京市朝阳区建国路88号\",\r\n          idcard_no: \"110101199001011234\",\r\n          case_des: \"借款纠纷，借款人未按约定时间还款\",\r\n          users: {\r\n            nickname: \"李四\"\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          uid: 1002,\r\n          name: \"王五\",\r\n          tel: \"13900139002\",\r\n          money: \"120000\",\r\n          status: \"调节中\",\r\n          back_money: \"30000\",\r\n          un_money: \"90000\",\r\n          ctime: \"2024-01-10 14:20:00\",\r\n          address: \"上海市浦东新区陆家嘴金融区\",\r\n          idcard_no: \"310101199205155678\",\r\n          case_des: \"合同纠纷，未按合同约定支付货款\",\r\n          users: {\r\n            nickname: \"赵六\"\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          uid: 1003,\r\n          name: \"陈七\",\r\n          tel: \"13700137003\",\r\n          money: \"80000\",\r\n          status: \"诉讼中\",\r\n          back_money: \"20000\",\r\n          un_money: \"60000\",\r\n          ctime: \"2024-01-05 09:15:00\",\r\n          address: \"广州市天河区珠江新城\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"服务费纠纷，拒绝支付约定的服务费用\",\r\n          users: {\r\n            nickname: \"孙八\"\r\n          }\r\n        },\r\n        {\r\n          id: 4,\r\n          uid: 1004,\r\n          name: \"刘九\",\r\n          tel: \"13600136004\",\r\n          money: \"200000\",\r\n          status: \"已结案\",\r\n          back_money: \"200000\",\r\n          un_money: \"0\",\r\n          ctime: \"2023-12-20 16:45:00\",\r\n          address: \"深圳市南山区科技园\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"投资纠纷，已通过调解达成一致\",\r\n          users: {\r\n            nickname: \"周十\"\r\n          }\r\n        },\r\n        {\r\n          id: 5,\r\n          uid: 1005,\r\n          name: \"吴十一\",\r\n          tel: \"13500135005\",\r\n          money: \"75000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"75000\",\r\n          ctime: \"2024-01-18 11:30:00\",\r\n          address: \"杭州市西湖区文三路\",\r\n          idcard_no: \"330101199406067890\",\r\n          case_des: \"租赁纠纷，拖欠房租及违约金\",\r\n          users: {\r\n            nickname: \"郑十二\"\r\n          }\r\n        },\r\n        {\r\n          id: 6,\r\n          uid: 1006,\r\n          name: \"马十三\",\r\n          tel: \"13400134006\",\r\n          money: \"150000\",\r\n          status: \"调节中\",\r\n          back_money: \"50000\",\r\n          un_money: \"100000\",\r\n          ctime: \"2024-01-12 13:20:00\",\r\n          address: \"成都市锦江区春熙路\",\r\n          idcard_no: \"510101199009091234\",\r\n          case_des: \"买卖合同纠纷，货物质量问题导致损失\",\r\n          users: {\r\n            nickname: \"冯十四\"\r\n          }\r\n        }\r\n      ],\r\n      total: 6,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      drawerViewUserDetail: false,\r\n      drawerViewDebtDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n      activeDebtTab: 'details',\r\n      activeUserTab: 'customer',\r\n      activeDebtDetailTab: 'details',\r\n      userDebtsList: [\r\n        {\r\n          id: 1,\r\n          name: \"债务人A\",\r\n          phone: \"13900139001\",\r\n          amount: \"50000\",\r\n          status: \"处理中\"\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"债务人B\",\r\n          phone: \"13900139002\",\r\n          amount: \"30000\",\r\n          status: \"已完成\"\r\n        }\r\n      ],\r\n      debtDocuments: [\r\n        {\r\n          name: \"借款合同.pdf\",\r\n          type: \"合同文件\",\r\n          uploadTime: \"2024-01-10\"\r\n        },\r\n        {\r\n          name: \"催款函.doc\",\r\n          type: \"法律文书\",\r\n          uploadTime: \"2024-01-12\"\r\n        },\r\n        {\r\n          name: \"还款计划.xlsx\",\r\n          type: \"财务文件\",\r\n          uploadTime: \"2024-01-15\"\r\n        }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n      _this.activeDebtTab = 'details';\r\n      _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.drawerViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.drawerViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      \r\n      // 开发模式：使用示例数据，不发送HTTP请求\r\n      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';\r\n      \r\n      if (isDevelopment) {\r\n        // 模拟异步加载\r\n        setTimeout(() => {\r\n          // 这里的数据已经在data()中定义了，所以直接设置loading为false\r\n          _this.loading = false;\r\n        }, 500);\r\n        return;\r\n      }\r\n      \r\n      // 生产模式：发送HTTP请求获取真实数据\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n    handleDrawerClose() {\r\n      this.dialogFormVisible = false;\r\n    },\r\n    handleUserDetailDrawerClose() {\r\n      this.drawerViewUserDetail = false;\r\n      this.activeUserTab = 'customer';\r\n    },\r\n    handleDebtDetailDrawerClose() {\r\n      this.drawerViewDebtDetail = false;\r\n      this.activeDebtDetailTab = 'details';\r\n    },\r\n    handleUserTabSelect(index) {\r\n      this.activeUserTab = index;\r\n    },\r\n    handleDebtDetailTabSelect(index) {\r\n      this.activeDebtDetailTab = index;\r\n    },\r\n    handleDebtTabSelect(index) {\r\n      this.activeDebtTab = index;\r\n    },\r\n    getEvidenceTitle() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部证据';\r\n        case 'evidence-video':\r\n          return '视频证据';\r\n        case 'evidence-image':\r\n          return '图片证据';\r\n        case 'evidence-audio':\r\n          return '语音证据';\r\n        case 'evidence-document':\r\n          return '文档证据';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    getEvidenceTypeText() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部';\r\n        case 'evidence-video':\r\n          return '视频';\r\n        case 'evidence-image':\r\n          return '图片';\r\n        case 'evidence-audio':\r\n          return '语音';\r\n        case 'evidence-document':\r\n          return '文档';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    hasEvidence() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return this.ruleForm.cards.length > 0 || this.ruleForm.images.length > 0 || this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-video':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-image':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-audio':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-document':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    uploadEvidence() {\r\n      // Implementation of uploadEvidence method\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.drawer-content-wrapper {\r\n  display: flex;\r\n  height: 100%;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.drawer-sidebar {\r\n  width: 220px;\r\n  padding: 20px 10px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-right: none;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawer-menu {\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n  border-radius: 8px;\r\n  margin-bottom: 8px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n}\r\n\r\n.drawer-menu .el-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.drawer-menu .el-menu-item.is-active {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item {\r\n  padding-left: 40px;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  padding: 25px;\r\n  overflow-y: auto;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.tab-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  padding: 25px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8ecf0;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* 抽屉标题样式 */\r\n.drawer-title {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.drawer-title i {\r\n  margin-right: 8px;\r\n  font-size: 20px;\r\n  color: #409eff;\r\n}\r\n\r\n/* 现代抽屉样式 */\r\n.modern-drawer .el-drawer__header {\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e8ecf0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n.modern-drawer .el-drawer__body {\r\n  padding: 0;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn {\r\n  color: #606266;\r\n  font-size: 18px;\r\n}\r\n\r\n.modern-drawer .el-drawer__close-btn:hover {\r\n  color: #409eff;\r\n}\r\n\r\n.card-header {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.evidence-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.evidence-section h4 {\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.evidence-section h5 {\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.evidence-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: #fff;\r\n}\r\n\r\n.evidence-item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n  width: 100%;\r\n  height: 150px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.evidence-actions {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #409eff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.file-type-icon {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.drawer-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.no-evidence {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n  border: 2px dashed #dcdfe6;\r\n}\r\n\r\n.no-evidence i {\r\n  font-size: 48px;\r\n  margin-bottom: 15px;\r\n  display: block;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .drawer-content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .drawer-sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n  \r\n  .evidence-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 10px;\r\n  }\r\n  \r\n  .file-item {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .file-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n/* 可点击文本样式 */\r\n.clickable-text {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n\r\n.clickable-text:hover {\r\n  color: #ffffff;\r\n  background-color: #409eff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 标题样式 */\r\n.section-title {\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n  position: relative;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: 0;\r\n  width: 30px;\r\n  height: 2px;\r\n  background-color: #67c23a;\r\n}\r\n\r\n/* 附件网格样式 */\r\n.attachment-grid, .evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.attachment-item, .evidence-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e8ecf0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.attachment-item:hover, .evidence-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n}\r\n\r\n.attachment-item i, .evidence-item i {\r\n  font-size: 24px;\r\n  color: #409eff;\r\n  margin-right: 10px;\r\n}\r\n\r\n.attachment-item span, .evidence-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n.el-timeline-item__content .el-card {\r\n  margin-bottom: 0;\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.el-timeline-item__content .el-card h4 {\r\n  color: #409eff;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-timeline-item__content .el-card p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 表格溢出处理 */\r\n.drawer-content .card {\r\n  overflow-x: auto;\r\n  max-width: 100%;\r\n}\r\n\r\n.drawer-content .el-table {\r\n  min-width: 1200px; /* 设置表格最小宽度 */\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n  overflow-x: auto;\r\n}\r\n\r\n/* 抽屉内容区域样式优化 */\r\n.drawer-content-wrapper {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  padding: 0 20px 20px 0;\r\n}\r\n</style>\r\n"]}]}