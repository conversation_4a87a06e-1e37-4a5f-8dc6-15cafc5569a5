{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\Home.vue?vue&type=template&id=c801f9a4&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\Home.vue", "mtime": 1748427648149}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}