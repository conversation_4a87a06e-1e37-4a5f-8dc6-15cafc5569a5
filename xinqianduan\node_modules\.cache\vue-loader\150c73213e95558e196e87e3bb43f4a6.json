{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\fuwu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\fuwu\\index.vue", "mtime": 1748425644031}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/fuwu", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n        <el-table-column prop=\"day\" label=\"有效期(年)\"> </el-table-column>\r\n        <el-table-column prop=\"price\" label=\"价格(元)\"> </el-table-column>\r\n        <el-table-column prop=\"pic_path\" label=\"封面\">\r\n          <template slot-scope=\"scope\">\r\n            <img\r\n              :src=\"scope.row.pic_path\"\r\n              style=\"width: 160px; height: 80px\"\r\n              @click=\"showImage(scope.row.pic_path)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"有效期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n          <el-input v-model=\"ruleForm.day\" autocomplete=\"off\" type=\"number\"\r\n            ><template slot=\"append\">年</template></el-input\r\n          >\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\" prop=\"price\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"市场价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"shop_price\"\r\n        >\r\n          <el-input v-model=\"ruleForm.shop_price\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"封面\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n            ><template slot=\"append\">330rpx*300rpx</template></el-input\r\n          >\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/server/\",\r\n      title: \"服务\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        day: [\r\n          {\r\n            required: true,\r\n            message: \"请填写有效期\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        shop_price: [\r\n          {\r\n            required: true,\r\n            message: \"请填写市场价格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          price: \"\",\r\n          day: 0,\r\n          shop_price: \"\",\r\n          pic_path: \"\",\r\n          content: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}