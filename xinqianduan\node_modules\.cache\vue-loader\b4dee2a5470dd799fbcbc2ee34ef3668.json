{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue?vue&type=template&id=58cee660&scoped=true", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue", "mtime": 1748540171932}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748425643059}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}