{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\components\\wangEnduit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\components\\wangEnduit.vue", "mtime": 1748425644022}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748425642674}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgRSBmcm9tICJ3YW5nZWRpdG9yIjsNCmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7DQppbXBvcnQgQWxlcnRNZW51IGZyb20gJy4vanMvQWxlcnRNZW51Jw0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHZhbHVlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAiIiwNCiAgICB9LA0KICAgIG1lYW5BcnJheTogew0KICAgICAgLy8g6Ieq5a6a5LmJ6I+c5Y2VDQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IG51bGwsDQogICAgfSwNCiAgfSwNCiAgbW9kZWw6IHsNCiAgICBwcm9wOiAidmFsdWUiLA0KICAgIGV2ZW50OiAiY2hhbmdlIiwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2YWx1ZTogZnVuY3Rpb24gKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgIT09IHRoaXMuZWRpdG9yLnR4dC5odG1sKCkpIHsNCiAgICAgICAgdGhpcy5lZGl0b3IudHh0Lmh0bWwodGhpcy52YWx1ZSk7DQogICAgICB9DQogICAgfSwNCiAgICAvL3ZhbHVl5Li657yW6L6R5qGG6L6T5YWl55qE5YaF5a6577yM6L+Z6YeM5oiR55uR5ZCs5LqG5LiA5LiL5YC877yM5b2T54i257uE5Lu26LCD55So5b6X5pe25YCZ77yM5aaC5p6c57uZdmFsdWXotYvlgLzkuobvvIzlrZDnu4Tku7blsIbkvJrmmL7npLrniLbnu4Tku7botYvnu5nnmoTlgLwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6buY6K6k5pyJ6L+Z5LmI5aSa6I+c5Y2V77yMbWVhbkFycmF55pyJ5YC85LulbWVhbkFycmF55Li65YeGDQogICAgICBkZWZhdWx0TWVhbnVzOiBbDQogICAgICAgICJoZWFkIiwNCiAgICAgICAgImJvbGQiLA0KICAgICAgICAiZm9udFNpemUiLA0KICAgICAgICAiZm9udE5hbWUiLA0KICAgICAgICAiaXRhbGljIiwNCiAgICAgICAgInVuZGVybGluZSIsDQogICAgICAgICJzdHJpa2VUaHJvdWdoIiwNCiAgICAgICAgImluZGVudCIsDQogICAgICAgICJsaW5lSGVpZ2h0IiwNCiAgICAgICAgImZvcmVDb2xvciIsDQogICAgICAgICJiYWNrQ29sb3IiLA0KICAgICAgICAibGluayIsDQogICAgICAgICJsaXN0IiwNCiAgICAgICAgImp1c3RpZnkiLA0KICAgICAgICAicXVvdGUiLA0KICAgICAgICAiZW1vdGljb24iLA0KICAgICAgICAiaW1hZ2UiLA0KICAgICAgICAidmlkZW8iLA0KICAgICAgICAidGFibGUiLA0KICAgICAgICAiY29kZSIsDQogICAgICAgICJzcGxpdExpbmUiLA0KICAgICAgICAidW5kbyIsDQogICAgICAgICJyZWRvIiwNCiAgICAgICAgImFsZXJ0IiwNCiAgICAgICAgDQogICAgICBdLA0KICAgICAgZWRpdG9yOiAiIiwNCiAgICAgIGZpbGVOYW1lOiIiDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXQoKSB7DQogICAgICBjb25zdCBfdGhpcyA9IHRoaXM7DQogICAgICB3aW5kb3cuaGFuZGxlRmlsZUNoYW5nZSA9IHRoaXMuaGFuZGxlRmlsZUNoYW5nZTsNCiAgICAgIHRoaXMuZWRpdG9yID0gbmV3IEUodGhpcy4kcmVmcy5lZGl0b3IpOw0KICAgICAgdGhpcy5lZGl0b3IuY29uZmlnLnVwbG9hZEltZ1Nob3dCYXNlNjQgPSBmYWxzZTsgLy8g5L2/55SoIGJhc2U2NCDkv53lrZjlm77niYcNCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy51cGxvYWRJbWdTZXJ2ZXIgPSAiL2FkbWluL1VwbG9hZC91cGRhdGVXYW5nIjsNCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy51cGxvYWRGaWxlTmFtZSA9ICJmaWxlIjsNCiAgICAgIHRoaXMuc2V0TWVudXMoKTsgLy/orr7nva7oj5zljZUNCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5vbmNoYW5nZSA9IChodG1sKSA9PiB7DQogICAgICAgIF90aGlzLiRlbWl0KCJjaGFuZ2UiLCBodG1sKTsgLy8g5bCG5YaF5a655ZCM5q2l5Yiw54i257uE5Lu25LitDQogICAgICB9Ow0KICAgICAgdGhpcy5lZGl0b3IubWVudXMuZXh0ZW5kKCdhbGVydE1lbnUnLCBBbGVydE1lbnUpICAvLyDphY3nva7mianlsZXnmoToj5zljZUNCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cyA9IHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cy5jb25jYXQoJ2FsZXJ0TWVudScpDQogICAgICB0aGlzLmVkaXRvci5jcmVhdGUoKTsgLy/liJvlu7rnvJbovpHlmagNCiAgICAgIA0KICAgIH0sDQogICAgaGFuZGxlRmlsZUNoYW5nZShlKXsNCiAgICAgIC8vbGV0IGFnZW50SW5mbyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImFnZW50SW5mbyIpKTsNCiAgICAgIGxldCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KICAgICAgLy8gZm9yIChsZXQgaSA9IDA7IGkgPCBlLmZpbGVzLmxlbmd0aDsgaSsrKSB7DQogICAgICAvLyAgIC8vIGZvcm1EYXRhLmFwcGVuZChmaWxlLm5hbWUsZmlsZSk7DQogICAgICAvLyAgIGxldCBmaWxlID0gZS5maWxlc1tpXTsNCiAgICAgIC8vICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlc1snK2krJ10nLCBmaWxlLCBmaWxlLm5hbWUpOw0KICAgICAgLy8gfQ0KICAgICAgDQogICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLGUuZmlsZXNbMF0pDQogICAgICBmb3JtRGF0YS5hcHBlbmQoJ25hbWUnLGUuZmlsZXNbMF1bJ25hbWUnXSkNCiAgICAgIHRoaXMuZmlsZU5hbWUgPSBlLmZpbGVzWzBdWyduYW1lJ10NCiAgICAgIA0KICAgICAgbGV0IGNvbmZpZyA9IHsNCiAgICAgICAgICBoZWFkZXJzOiB7DQogICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScNCiAgICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGF4aW9zLnBvc3QoIi9hZG1pbi91cGxvYWQvdXBsb2FkRmlsZSIsZm9ybURhdGEsY29uZmlnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsgIC8v5raI5oGv5p+l6K+iDQogICAgICAgICAgbGV0IHJlc3VsdD0gcmVzLmNvZGU7DQogICAgICAgICAgY29uc29sZS5sb2cocmVzKQ0KICAgICAgICAgIGlmKHJlc3VsdCA9PSAiNDAwIil7DQogICAgICAgICAgICB0aGF0LiRtZXNzYWdlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKUs5Y6f5ZugOicrcmVzLm1zZyk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgIHRoYXQuZWRpdG9yLnR4dC5hcHBlbmQoJzxhIHRhcmdldD0iX2JsYW5rIiBzdHlsZT0iY29sb3I6Ymx1ZSIgZG93bmxvYWQgaHJlZj1cJycrcmVzLmRhdGEudXJsKydcJz4nK3RoYXQuZmlsZU5hbWUrJzwvYT48YnI+Jyk7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8v5bCG6L+U5Zue55qE5pWw5o2uIGFwcGVuZOWcqOWvjOaWh+acrOWQjumdog0KICAgICAgIA0KICAgICAgICAgIA0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldE1lbnVzKCkgew0KICAgICAgLy8g6K6+572u6I+c5Y2VDQogICAgICBpZiAodGhpcy5tZWFuQXJyYXkpIHsNCiAgICAgICAgdGhpcy5lZGl0b3IuY29uZmlnLm1lbnVzID0gdGhpcy5tZWFuQXJyYXk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVkaXRvci5jb25maWcubWVudXMgPSB0aGlzLmRlZmF1bHRNZWFudXM7DQogICAgICAgIA0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0SHRtbCgpIHsNCiAgICAgIC8vIOW+l+WIsOaWh+acrOWGheWuuQ0KICAgICAgcmV0dXJuIHRoaXMuZWRpdG9yLnR4dC5odG1sKCk7DQogICAgfSwNCiAgICBzZXRIdG1sKHR4dCkgew0KICAgICAgLy8g6K6+572u5a+M5paH5pys6YeM6Z2i55qE5YC8DQogICAgICB0aGlzLmVkaXRvci50eHQuaHRtbCh0eHQpOw0KICAgIH0sDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgIHRoYXQuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsNCiAgICAgIHRoYXQuaW5pdCgpOw0KICAgIH0pOw0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["wangEnduit.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "wangEnduit.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n  <div ref=\"editor\"></div>\r\n</template>\r\n\r\n<script>\r\nimport E from \"wangeditor\";\r\nimport axios from \"axios\";\r\nimport AlertMenu from './js/AlertMenu'\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    meanArray: {\r\n      // 自定义菜单\r\n      type: Array,\r\n      default: null,\r\n    },\r\n  },\r\n  model: {\r\n    prop: \"value\",\r\n    event: \"change\",\r\n  },\r\n  watch: {\r\n    value: function (value) {\r\n      if (value !== this.editor.txt.html()) {\r\n        this.editor.txt.html(this.value);\r\n      }\r\n    },\r\n    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值\r\n  },\r\n  data() {\r\n    return {\r\n      // 默认有这么多菜单，meanArray有值以meanArray为准\r\n      defaultMeanus: [\r\n        \"head\",\r\n        \"bold\",\r\n        \"fontSize\",\r\n        \"fontName\",\r\n        \"italic\",\r\n        \"underline\",\r\n        \"strikeThrough\",\r\n        \"indent\",\r\n        \"lineHeight\",\r\n        \"foreColor\",\r\n        \"backColor\",\r\n        \"link\",\r\n        \"list\",\r\n        \"justify\",\r\n        \"quote\",\r\n        \"emoticon\",\r\n        \"image\",\r\n        \"video\",\r\n        \"table\",\r\n        \"code\",\r\n        \"splitLine\",\r\n        \"undo\",\r\n        \"redo\",\r\n        \"alert\",\r\n        \r\n      ],\r\n      editor: \"\",\r\n      fileName:\"\"\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const _this = this;\r\n      window.handleFileChange = this.handleFileChange;\r\n      this.editor = new E(this.$refs.editor);\r\n      this.editor.config.uploadImgShowBase64 = false; // 使用 base64 保存图片\r\n      this.editor.config.uploadImgServer = \"/admin/Upload/updateWang\";\r\n      this.editor.config.uploadFileName = \"file\";\r\n      this.setMenus(); //设置菜单\r\n      this.editor.config.onchange = (html) => {\r\n        _this.$emit(\"change\", html); // 将内容同步到父组件中\r\n      };\r\n      this.editor.menus.extend('alertMenu', AlertMenu)  // 配置扩展的菜单\r\n      this.editor.config.menus = this.editor.config.menus.concat('alertMenu')\r\n      this.editor.create(); //创建编辑器\r\n      \r\n    },\r\n    handleFileChange(e){\r\n      //let agentInfo = JSON.parse(localStorage.getItem(\"agentInfo\"));\r\n      let formData = new FormData();\r\n      // for (let i = 0; i < e.files.length; i++) {\r\n      //   // formData.append(file.name,file);\r\n      //   let file = e.files[i];\r\n      //   formData.append('files['+i+']', file, file.name);\r\n      // }\r\n      \r\n      formData.append('file',e.files[0])\r\n      formData.append('name',e.files[0]['name'])\r\n      this.fileName = e.files[0]['name']\r\n      \r\n      let config = {\r\n          headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n          }\r\n      }\r\n\r\n      let that = this;\r\n      axios.post(\"/admin/upload/uploadFile\",formData,config).then(function (res) {  //消息查询\r\n          let result= res.code;\r\n          console.log(res)\r\n          if(result == \"400\"){\r\n            that.$message.error('文件上传失败,原因:'+res.msg);\r\n            return false;\r\n          }else{\r\n              that.editor.txt.append('<a target=\"_blank\" style=\"color:blue\" download href=\\''+res.data.url+'\\'>'+that.fileName+'</a><br>');\r\n          }\r\n          \r\n          //将返回的数据 append在富文本后面\r\n       \r\n          \r\n      })\r\n    },\r\n    setMenus() {\r\n      // 设置菜单\r\n      if (this.meanArray) {\r\n        this.editor.config.menus = this.meanArray;\r\n      } else {\r\n        this.editor.config.menus = this.defaultMeanus;\r\n        \r\n      }\r\n    },\r\n    getHtml() {\r\n      // 得到文本内容\r\n      return this.editor.txt.html();\r\n    },\r\n    setHtml(txt) {\r\n      // 设置富文本里面的值\r\n      this.editor.txt.html(txt);\r\n    },\r\n  },\r\n  mounted() {\r\n    let that = this;\r\n    that.$nextTick(function () {\r\n      that.init();\r\n    });\r\n  },\r\n};\r\n</script>\r\n"]}]}