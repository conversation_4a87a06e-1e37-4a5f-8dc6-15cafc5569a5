<?php
namespace app\admin\controller;
use think\Request;
use untils\JsonService;
use models\{Zhiweis,Quanxians,Yuangongs};

class Zhiwei
{
    protected $model;
    public function __construct(Zhiweis $model){
        //parent::__construct();
        $this->model=$model;
        
    }
    public function index(Request $request,$page=1,$size=20){   
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        $res = $this->model
        ->where($where)
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $form['quanxian']=serialize($form['quanxian']);
        
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);
        if(empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功',$res);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $count = Yuangongs::where(['zhiwei_id'=>$id,'is_delete'=>0])->count();
        if(!empty($count)) return JsonService::fail('该职位下有员工,无法删除');
        $res = $this->model->delData(['id'=>$id]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
    // 
    public function getQuanxians(Quanxians $model){
        $res = $model::withAttr('children',function($v,$d){
            return Quanxians::field('name as label,id as value')
            ->where(['pid'=>$d['value']])->select();
        })
        ->field('name as label,id as value')
        ->where(['pid'=>0])
        ->append(['children'])
        ->select();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
}
