{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\taocan\\type.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\taocan\\type.vue", "mtime": 1748606740784}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "allSize", "list", "total", "page", "size", "showAdvanced", "search", "keyword", "serviceType", "status", "date<PERSON><PERSON><PERSON>", "sortBy", "sortOrder", "usageLevel", "features", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "saveLoading", "selectedRows", "ruleForm", "desc", "is_num", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "computed", "activeTypes", "filter", "item", "trim", "length", "dialogTitle", "id", "mounted", "getData", "methods", "editData", "_this", "getInfo", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "postRequest", "count", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "getStatusType", "row", "getStatusText", "formatDate", "dateStr", "Date", "toLocaleDateString", "resetSearch", "toggleAdvanced", "refreshData", "handleSelectionChange", "selection", "exportData", "batchDelete", "warning", "applyAdvancedSearch", "clearAdvancedSearch"], "sources": ["src/views/pages/taocan/type.vue"], "sourcesContent": ["<template>\r\n  <div class=\"service-type-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-menu\"></i>\r\n          {{ this.$router.currentRoute.name }}\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理法律服务分类和类型配置</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-menu\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">服务类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +5%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active-icon\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeTypes }}</div>\r\n              <div class=\"stat-label\">活跃类型</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-check\"></i> 正常\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon usage-icon\">\r\n              <i class=\"el-icon-data-analysis\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">85%</div>\r\n              <div class=\"stat-label\">使用率</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +3%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <div class=\"header-left\">\r\n          <span class=\"card-title\">\r\n            <i class=\"el-icon-search\"></i>\r\n            搜索与筛选\r\n          </span>\r\n          <div class=\"card-subtitle\">快速查找和管理服务类型</div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button-group class=\"action-group\">\r\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n              导出\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\r\n              刷新\r\n            </el-button>\r\n          </el-button-group>\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\" class=\"primary-action\">\r\n            新增类型\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <el-form-item label=\"关键词搜索\" class=\"search-item-main\">\r\n              <el-input \r\n                placeholder=\"请输入类型名称或描述关键词...\" \r\n                v-model=\"search.keyword\" \r\n                clearable\r\n                prefix-icon=\"el-icon-search\"\r\n                class=\"search-input\"\r\n                @keyup.enter.native=\"searchData()\"\r\n              />\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"服务类型\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.serviceType\" \r\n                placeholder=\"选择服务类型\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部类型\" value=\"\" />\r\n                <el-option label=\"计次服务\" value=\"1\" />\r\n                <el-option label=\"不限次数\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item label=\"状态筛选\" class=\"search-item\">\r\n              <el-select \r\n                v-model=\"search.status\" \r\n                placeholder=\"选择状态\" \r\n                clearable\r\n                class=\"search-select\"\r\n              >\r\n                <el-option label=\"全部状态\" value=\"\" />\r\n                <el-option label=\"正常\" value=\"1\" />\r\n                <el-option label=\"待完善\" value=\"0\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            \r\n            <el-form-item class=\"search-actions-item\">\r\n              <div class=\"search-actions\">\r\n                <el-button type=\"primary\" @click=\"searchData\" icon=\"el-icon-search\" class=\"search-btn\">\r\n                  搜索\r\n                </el-button>\r\n                <el-button @click=\"resetSearch\" icon=\"el-icon-refresh-left\" class=\"reset-btn\">\r\n                  重置\r\n                </el-button>\r\n                <el-button @click=\"toggleAdvanced\" type=\"text\" class=\"toggle-btn\">\r\n                  <i :class=\"showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n                  {{ showAdvanced ? '收起' : '高级筛选' }}\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 高级筛选区域 -->\r\n          <transition name=\"slide-fade\">\r\n            <div v-show=\"showAdvanced\" class=\"advanced-search\">\r\n              <el-divider content-position=\"left\">\r\n                <i class=\"el-icon-setting\"></i>\r\n                高级筛选选项\r\n              </el-divider>\r\n              <div class=\"advanced-content\">\r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"创建时间范围\" class=\"advanced-item\">\r\n                    <el-date-picker\r\n                      v-model=\"search.dateRange\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      class=\"date-picker\"\r\n                    />\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序方式\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.sortBy\" placeholder=\"选择排序\" class=\"sort-select\">\r\n                      <el-option label=\"创建时间\" value=\"create_time\" />\r\n                      <el-option label=\"名称字母\" value=\"title\" />\r\n                      <el-option label=\"使用频率\" value=\"usage\" />\r\n                      <el-option label=\"更新时间\" value=\"update_time\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"排序顺序\" class=\"advanced-item\">\r\n                    <el-radio-group v-model=\"search.sortOrder\" size=\"small\" class=\"sort-order\">\r\n                      <el-radio-button label=\"desc\">\r\n                        <i class=\"el-icon-sort-down\"></i> 降序\r\n                      </el-radio-button>\r\n                      <el-radio-button label=\"asc\">\r\n                        <i class=\"el-icon-sort-up\"></i> 升序\r\n                      </el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </div>\r\n                \r\n                <div class=\"advanced-row\">\r\n                  <el-form-item label=\"使用频率\" class=\"advanced-item\">\r\n                    <el-select v-model=\"search.usageLevel\" placeholder=\"选择使用频率\" class=\"usage-select\">\r\n                      <el-option label=\"全部频率\" value=\"\" />\r\n                      <el-option label=\"高频使用\" value=\"high\" />\r\n                      <el-option label=\"中频使用\" value=\"medium\" />\r\n                      <el-option label=\"低频使用\" value=\"low\" />\r\n                      <el-option label=\"未使用\" value=\"none\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item label=\"类型特性\" class=\"advanced-item\">\r\n                    <el-checkbox-group v-model=\"search.features\" class=\"feature-checkboxes\">\r\n                      <el-checkbox label=\"popular\">热门类型</el-checkbox>\r\n                      <el-checkbox label=\"new\">新增类型</el-checkbox>\r\n                      <el-checkbox label=\"recommended\">推荐类型</el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  \r\n                  <el-form-item class=\"advanced-actions\">\r\n                    <el-button @click=\"applyAdvancedSearch\" type=\"primary\" size=\"small\" icon=\"el-icon-check\">\r\n                      应用筛选\r\n                    </el-button>\r\n                    <el-button @click=\"clearAdvancedSearch\" size=\"small\" icon=\"el-icon-close\">\r\n                      清空高级选项\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <el-card shadow=\"hover\" class=\"table-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          类型列表\r\n        </span>\r\n        <div class=\"table-actions\">\r\n          <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\r\n            导出数据\r\n          </el-button>\r\n          <el-button size=\"small\" @click=\"batchDelete\" icon=\"el-icon-delete\">\r\n            批量删除\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table \r\n        :data=\"list\" \r\n        v-loading=\"loading\" \r\n        class=\"modern-table\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        \r\n        <el-table-column label=\"类型信息\" min-width=\"300\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"type-info\">\r\n              <div class=\"type-header\">\r\n                <div class=\"type-icon\">\r\n                  <i class=\"el-icon-star-on\"></i>\r\n                </div>\r\n                <div class=\"type-details\">\r\n                  <div class=\"type-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"type-desc\" v-if=\"scope.row.desc\">\r\n                    {{ scope.row.desc }}\r\n                  </div>\r\n                  <div class=\"type-features\">\r\n                    <el-tag \r\n                      size=\"mini\" \r\n                      :type=\"scope.row.is_num == 1 ? 'success' : 'info'\"\r\n                    >\r\n                      {{ scope.row.is_num == 1 ? '计次服务' : '不限次数' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              {{ formatDate(scope.row.create_time) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row)\" effect=\"dark\">\r\n              {{ getStatusText(scope.row) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-button \r\n                type=\"text\" \r\n                size=\"small\" \r\n                @click=\"editData(scope.row.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                <i class=\"el-icon-edit\"></i>\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                size=\"small\" \r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                <i class=\"el-icon-delete\"></i>\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"600px\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-form-item label=\"类型名称\" prop=\"title\">\r\n          <el-input \r\n            v-model=\"ruleForm.title\" \r\n            placeholder=\"请输入服务类型名称\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"计次设置\">\r\n          <el-radio-group v-model=\"ruleForm.is_num\">\r\n            <el-radio :label=\"1\">计次服务</el-radio>\r\n            <el-radio :label=\"0\">不限次数</el-radio>\r\n          </el-radio-group>\r\n          <div class=\"form-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            计次服务将限制使用次数，不限次数则可无限使用\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"类型描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入服务类型的详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <div class=\"image-viewer\">\r\n        <el-image :src=\"show_image\" fit=\"contain\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      showAdvanced: false,\r\n      search: {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      },\r\n      loading: true,\r\n      url: \"/type/\",\r\n      title: \"服务类型\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      saveLoading: false,\r\n      selectedRows: [],\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        is_num: 0,\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写类型名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    activeTypes() {\r\n      return this.list.filter(item => item.title && item.title.trim() !== '').length;\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑服务类型' : '新增服务类型';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_num: 0,\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 新增方法\r\n    getStatusType(row) {\r\n      // 根据数据判断状态类型\r\n      if (row.title && row.title.trim() !== '') {\r\n        return 'success';\r\n      }\r\n      return 'info';\r\n    },\r\n    getStatusText(row) {\r\n      // 根据数据判断状态文本\r\n      if (row.title && row.title.trim() !== '') {\r\n        return '正常';\r\n      }\r\n      return '待完善';\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        serviceType: \"\",\r\n        status: \"\",\r\n        dateRange: [],\r\n        sortBy: \"create_time\",\r\n        sortOrder: \"desc\",\r\n        usageLevel: \"\",\r\n        features: []\r\n      };\r\n      this.showAdvanced = false;\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    toggleAdvanced() {\r\n      this.showAdvanced = !this.showAdvanced;\r\n    },\r\n    refreshData() {\r\n      this.getData();\r\n      this.$message.success('数据已刷新');\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n    exportData() {\r\n      this.$message.success('数据导出功能开发中...');\r\n    },\r\n    batchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请先选择要删除的数据');\r\n        return;\r\n      }\r\n      \r\n      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条数据吗？`, '批量删除确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里实现批量删除逻辑\r\n        this.$message.success('批量删除功能开发中...');\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    applyAdvancedSearch() {\r\n      this.page = 1;\r\n      this.getData();\r\n      this.$message.success('高级筛选已应用');\r\n    },\r\n    clearAdvancedSearch() {\r\n      this.search.dateRange = [];\r\n      this.search.sortBy = \"create_time\";\r\n      this.search.sortOrder = \"desc\";\r\n      this.search.usageLevel = \"\";\r\n      this.search.features = [];\r\n      this.$message.info('高级筛选选项已清空');\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.service-type-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.usage-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .table-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 16px 0;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-group .el-button {\r\n  margin: 0;\r\n  border-radius: 0;\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #495057;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-group .el-button:hover {\r\n  background: #e9ecef;\r\n  color: #495057;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.primary-action {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 20px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.primary-action:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 20px 0 16px 0;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: flex-end;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.search-item-main {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.search-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-actions-item {\r\n  margin-bottom: 0 !important;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.search-input {\r\n  width: 100% !important;\r\n}\r\n\r\n.search-input .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  padding: 12px 16px;\r\n  font-size: 14px;\r\n  height: 40px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-input .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-select {\r\n  width: 160px !important;\r\n}\r\n\r\n.search-select .el-input__inner {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 8px;\r\n  border: 2px solid #e8e8e8;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-select .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  padding: 10px 24px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\r\n}\r\n\r\n.reset-btn {\r\n  background: #f8f9fa !important;\r\n  border: 1px solid #e9ecef !important;\r\n  color: #6c757d !important;\r\n  padding: 10px 16px !important;\r\n  border-radius: 8px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.reset-btn:hover {\r\n  background: #e9ecef !important;\r\n  color: #495057 !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n.toggle-btn {\r\n  color: #667eea !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 12px !important;\r\n  border-radius: 6px !important;\r\n  height: 40px !important;\r\n  transition: all 0.3s ease !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 4px !important;\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: rgba(102, 126, 234, 0.1) !important;\r\n  color: #667eea !important;\r\n}\r\n\r\n/* 高级筛选区域 */\r\n.slide-fade-enter-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.slide-fade-enter {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.slide-fade-leave-to {\r\n  transform: translateY(-10px);\r\n  opacity: 0;\r\n}\r\n\r\n.advanced-search {\r\n  margin-top: 20px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 12px;\r\n  border: 1px solid #e9ecef;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.advanced-search .el-divider {\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.advanced-search .el-divider__text {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  color: #495057;\r\n  font-weight: 600;\r\n  padding: 0 16px;\r\n  font-size: 14px;\r\n}\r\n\r\n.advanced-content {\r\n  gap: 16px;\r\n}\r\n\r\n.advanced-row {\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.advanced-item {\r\n  width: 100%;\r\n  min-width: unset;\r\n}\r\n\r\n.advanced-item .el-form-item__content {\r\n  width: 100%;\r\n}\r\n\r\n.advanced-item .el-select,\r\n.advanced-item .el-date-picker,\r\n.advanced-item .el-radio-group {\r\n  width: 100% !important;\r\n}\r\n\r\n.feature-checkboxes {\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.feature-checkboxes .el-checkbox {\r\n  flex: none;\r\n  min-width: unset;\r\n}\r\n\r\n.advanced-actions {\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  min-width: unset;\r\n  width: 100%;\r\n}\r\n\r\n.advanced-actions .el-button {\r\n  width: 100%;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n/* 类型信息样式 */\r\n.type-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.type-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.type-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.type-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.type-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.type-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 6px;\r\n  line-height: 1.4;\r\n  max-height: 40px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.type-features {\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 时间信息样式 */\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  margin-top: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.form-tip i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 表格操作区域 */\r\n.table-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .el-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .service-type-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    width: 100%;\r\n    display: flex;\r\n  }\r\n  \r\n  .action-group .el-button {\r\n    flex: 1;\r\n  }\r\n  \r\n  .primary-action {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .search-item-main {\r\n    min-width: unset;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-item .el-select,\r\n  .search-item .el-input {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .search-actions {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .search-btn,\r\n  .reset-btn {\r\n    width: 100% !important;\r\n    justify-content: center !important;\r\n  }\r\n  \r\n  .advanced-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .advanced-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-form-item__content {\r\n    width: 100%;\r\n  }\r\n  \r\n  .advanced-item .el-select,\r\n  .advanced-item .el-date-picker,\r\n  .advanced-item .el-radio-group {\r\n    width: 100% !important;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .type-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .table-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n  \r\n  .pagination-wrapper {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .pagination-wrapper .el-pagination {\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-card,\r\n  .table-card {\r\n    margin: 0 -8px 16px -8px;\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  .search-section {\r\n    padding: 16px 0 12px 0;\r\n  }\r\n  \r\n  .advanced-search {\r\n    padding: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  \r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .stat-number {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .type-title {\r\n    font-size: 15px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA0YA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,YAAA;MACAC,MAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;QACAP,KAAA;QACAQ,IAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAV,KAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA;MACA,YAAAhC,IAAA,CAAAiC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAlB,KAAA,IAAAkB,IAAA,CAAAlB,KAAA,CAAAmB,IAAA,WAAAC,MAAA;IACA;IACAC,YAAA;MACA,YAAAd,QAAA,CAAAe,EAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAJ,EAAA;MACA,IAAAK,KAAA;MACA,IAAAL,EAAA;QACA,KAAAM,OAAA,CAAAN,EAAA;MACA;QACA,KAAAf,QAAA;UACAP,KAAA;UACAQ,IAAA;UACAC,MAAA;QACA;MACA;MAEAkB,KAAA,CAAAzB,iBAAA;IACA;IACA0B,QAAAN,EAAA;MACA,IAAAK,KAAA;MACAA,KAAA,CAAAE,UAAA,CAAAF,KAAA,CAAA5B,GAAA,gBAAAuB,EAAA,EAAAQ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAJ,KAAA,CAAApB,QAAA,GAAAwB,IAAA,CAAAjD,IAAA;QACA;MACA;IACA;IACAkD,QAAAC,KAAA,EAAAX,EAAA;MACA,KAAAY,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAAvC,GAAA,kBAAAuB,EAAA,EAAAQ,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAC,QAAA;cACAH,IAAA;cACAzB,OAAA;YACA;YACA,KAAA5B,IAAA,CAAAyD,MAAA,CAAAR,KAAA;UACA;QACA;MACA,GACAS,KAAA;QACA,KAAAF,QAAA;UACAH,IAAA;UACAzB,OAAA;QACA;MACA;IACA;IACA+B,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA5D,IAAA;MACA,KAAAC,IAAA;MACA,KAAAqC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAG,KAAA;MAEAA,KAAA,CAAA7B,OAAA;MACA6B,KAAA,CACAoB,WAAA,CACApB,KAAA,CAAA5B,GAAA,mBAAA4B,KAAA,CAAAzC,IAAA,cAAAyC,KAAA,CAAAxC,IAAA,EACAwC,KAAA,CAAAtC,MACA,EACAyC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAZ,KAAA,CAAA3C,IAAA,GAAA+C,IAAA,CAAAjD,IAAA;UACA6C,KAAA,CAAA1C,KAAA,GAAA8C,IAAA,CAAAiB,KAAA;QACA;QACArB,KAAA,CAAA7B,OAAA;MACA;IACA;IACAmD,SAAA;MACA,IAAAtB,KAAA;MACA,KAAAuB,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAL,WAAA,CAAApB,KAAA,CAAA5B,GAAA,gBAAAQ,QAAA,EAAAuB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAZ,KAAA,CAAAa,QAAA;gBACAH,IAAA;gBACAzB,OAAA,EAAAmB,IAAA,CAAAsB;cACA;cACA,KAAA7B,OAAA;cACAG,KAAA,CAAAzB,iBAAA;YACA;cACAyB,KAAA,CAAAa,QAAA;gBACAH,IAAA;gBACAzB,OAAA,EAAAmB,IAAA,CAAAsB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAApE,IAAA,GAAAoE,GAAA;MAEA,KAAA/B,OAAA;IACA;IACAgC,oBAAAD,GAAA;MACA,KAAArE,IAAA,GAAAqE,GAAA;MACA,KAAA/B,OAAA;IACA;IACAiC,cAAAC,GAAA;MACA,KAAAnD,QAAA,CAAAoD,QAAA,GAAAD,GAAA,CAAA5E,IAAA,CAAAiB,GAAA;IACA;IAEA6D,UAAAC,IAAA;MACA,KAAA1D,UAAA,GAAA0D,IAAA;MACA,KAAAzD,aAAA;IACA;IACA0D,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAAxB,IAAA;MACA,KAAA0B,UAAA;QACA,KAAAvB,QAAA,CAAAyB,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAAxC,KAAA;MACAA,KAAA,CAAAE,UAAA,gCAAAgC,IAAA,EAAA/B,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAZ,KAAA,CAAApB,QAAA,CAAA4D,QAAA;UAEAxC,KAAA,CAAAa,QAAA,CAAA4B,OAAA;QACA;UACAzC,KAAA,CAAAa,QAAA,CAAAyB,KAAA,CAAAlC,IAAA,CAAAsB,GAAA;QACA;MACA;IACA;IACA;IACAgB,cAAAC,GAAA;MACA;MACA,IAAAA,GAAA,CAAAtE,KAAA,IAAAsE,GAAA,CAAAtE,KAAA,CAAAmB,IAAA;QACA;MACA;MACA;IACA;IACAoD,cAAAD,GAAA;MACA;MACA,IAAAA,GAAA,CAAAtE,KAAA,IAAAsE,GAAA,CAAAtE,KAAA,CAAAmB,IAAA;QACA;MACA;MACA;IACA;IACAqD,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,WAAAC,IAAA,CAAAD,OAAA,EAAAE,kBAAA;IACA;IACAC,YAAA;MACA,KAAAvF,MAAA;QACAC,OAAA;QACAC,WAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACA,KAAAT,YAAA;MACA,KAAAF,IAAA;MACA,KAAAsC,OAAA;IACA;IACAqD,eAAA;MACA,KAAAzF,YAAA,SAAAA,YAAA;IACA;IACA0F,YAAA;MACA,KAAAtD,OAAA;MACA,KAAAgB,QAAA,CAAA4B,OAAA;IACA;IACAW,sBAAAC,SAAA;MACA,KAAA1E,YAAA,GAAA0E,SAAA;IACA;IACAC,WAAA;MACA,KAAAzC,QAAA,CAAA4B,OAAA;IACA;IACAc,YAAA;MACA,SAAA5E,YAAA,CAAAc,MAAA;QACA,KAAAoB,QAAA,CAAA2C,OAAA;QACA;MACA;MAEA,KAAAjD,QAAA,kBAAA5B,YAAA,CAAAc,MAAA;QACAe,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAP,IAAA;QACA;QACA,KAAAU,QAAA,CAAA4B,OAAA;MACA,GAAA1B,KAAA;QACA,KAAAF,QAAA,CAAAvC,IAAA;MACA;IACA;IACAmF,oBAAA;MACA,KAAAlG,IAAA;MACA,KAAAsC,OAAA;MACA,KAAAgB,QAAA,CAAA4B,OAAA;IACA;IACAiB,oBAAA;MACA,KAAAhG,MAAA,CAAAI,SAAA;MACA,KAAAJ,MAAA,CAAAK,MAAA;MACA,KAAAL,MAAA,CAAAM,SAAA;MACA,KAAAN,MAAA,CAAAO,UAAA;MACA,KAAAP,MAAA,CAAAQ,QAAA;MACA,KAAA2C,QAAA,CAAAvC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}