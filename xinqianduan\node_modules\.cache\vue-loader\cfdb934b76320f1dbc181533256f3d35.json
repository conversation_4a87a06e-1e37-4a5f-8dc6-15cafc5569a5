{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\profile\\index.vue", "mtime": 1748484320636}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAyMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/profile", "sourcesContent": ["<template>\n  <div class=\"page-wrapper\">\n    <div class=\"page-container\">\n      <!-- 页面标题 -->\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <h2 class=\"page-title\">\n            <i class=\"el-icon-user\"></i>\n            个人信息\n          </h2>\n          <div class=\"page-subtitle\">管理您的个人资料和账户设置</div>\n        </div>\n      </div>\n\n      <!-- 个人信息卡片 -->\n      <div class=\"profile-section\">\n        <div class=\"profile-card\">\n          <!-- 头像区域 -->\n          <div class=\"avatar-section\">\n            <div class=\"avatar-container\">\n              <img \n                :src=\"userInfo.avatar || defaultAvatar\" \n                :alt=\"userInfo.name\"\n                class=\"avatar-image\"\n              />\n              <div class=\"avatar-overlay\">\n                <el-upload\n                  action=\"/admin/Upload/uploadImage\"\n                  :show-file-list=\"false\"\n                  :on-success=\"handleAvatarSuccess\"\n                  :before-upload=\"beforeAvatarUpload\"\n                  class=\"avatar-uploader\"\n                >\n                  <i class=\"el-icon-camera avatar-uploader-icon\"></i>\n                </el-upload>\n              </div>\n            </div>\n            <div class=\"user-basic-info\">\n              <h3 class=\"user-name\">{{ userInfo.name || '管理员' }}</h3>\n              <p class=\"user-role\">{{ userInfo.role || '系统管理员' }}</p>\n              <p class=\"user-department\">{{ userInfo.department || '法律服务部' }}</p>\n            </div>\n          </div>\n\n          <!-- 信息表单 -->\n          <div class=\"form-section\">\n            <el-form \n              :model=\"userInfo\" \n              :rules=\"rules\" \n              ref=\"userForm\" \n              label-width=\"120px\"\n              class=\"profile-form\"\n            >\n              <div class=\"form-row\">\n                <el-form-item label=\"姓名\" prop=\"name\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.name\" \n                    placeholder=\"请输入姓名\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n                \n                <el-form-item label=\"工号\" prop=\"employee_id\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.employee_id\" \n                    placeholder=\"请输入工号\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <div class=\"form-row\">\n                <el-form-item label=\"手机号\" prop=\"phone\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.phone\" \n                    placeholder=\"请输入手机号\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n                \n                <el-form-item label=\"邮箱\" prop=\"email\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.email\" \n                    placeholder=\"请输入邮箱\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <div class=\"form-row\">\n                <el-form-item label=\"部门\" prop=\"department\" class=\"form-item\">\n                  <el-select \n                    v-model=\"userInfo.department\" \n                    placeholder=\"请选择部门\"\n                    :disabled=\"!editMode\"\n                    style=\"width: 100%\"\n                  >\n                    <el-option label=\"法律服务部\" value=\"法律服务部\"></el-option>\n                    <el-option label=\"客户服务部\" value=\"客户服务部\"></el-option>\n                    <el-option label=\"财务部\" value=\"财务部\"></el-option>\n                    <el-option label=\"人事部\" value=\"人事部\"></el-option>\n                    <el-option label=\"技术部\" value=\"技术部\"></el-option>\n                  </el-select>\n                </el-form-item>\n                \n                <el-form-item label=\"职位\" prop=\"position\" class=\"form-item\">\n                  <el-input \n                    v-model=\"userInfo.position\" \n                    placeholder=\"请输入职位\"\n                    :disabled=\"!editMode\"\n                  ></el-input>\n                </el-form-item>\n              </div>\n\n              <el-form-item label=\"个人简介\" prop=\"bio\">\n                <el-input \n                  v-model=\"userInfo.bio\" \n                  type=\"textarea\" \n                  :rows=\"4\"\n                  placeholder=\"请输入个人简介\"\n                  :disabled=\"!editMode\"\n                ></el-input>\n              </el-form-item>\n\n              <!-- 操作按钮 -->\n              <div class=\"action-buttons\">\n                <el-button \n                  v-if=\"!editMode\"\n                  type=\"primary\" \n                  icon=\"el-icon-edit\"\n                  @click=\"enableEdit\"\n                >\n                  编辑资料\n                </el-button>\n                \n                <template v-else>\n                  <el-button \n                    type=\"primary\" \n                    icon=\"el-icon-check\"\n                    @click=\"saveProfile\"\n                    :loading=\"saving\"\n                  >\n                    保存修改\n                  </el-button>\n                  <el-button \n                    icon=\"el-icon-close\"\n                    @click=\"cancelEdit\"\n                  >\n                    取消\n                  </el-button>\n                </template>\n              </div>\n            </el-form>\n          </div>\n        </div>\n\n        <!-- 安全设置卡片 -->\n        <div class=\"security-card\">\n          <div class=\"card-header\">\n            <h3 class=\"card-title\">\n              <i class=\"el-icon-lock\"></i>\n              安全设置\n            </h3>\n          </div>\n          \n          <div class=\"security-items\">\n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">登录密码</div>\n                <div class=\"security-desc\">定期更换密码，保护账户安全</div>\n              </div>\n              <el-button \n                type=\"text\" \n                @click=\"changePassword\"\n                class=\"security-action\"\n              >\n                修改密码\n              </el-button>\n            </div>\n            \n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">最后登录</div>\n                <div class=\"security-desc\">{{ lastLoginTime }}</div>\n              </div>\n            </div>\n            \n            <div class=\"security-item\">\n              <div class=\"security-info\">\n                <div class=\"security-title\">登录IP</div>\n                <div class=\"security-desc\">{{ lastLoginIP }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Profile\",\n  data() {\n    return {\n      editMode: false,\n      saving: false,\n      defaultAvatar: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzk5OSI+5aS05YOP</dGV4dD48L3N2Zz4=\",\n      userInfo: {\n        name: \"管理员\",\n        employee_id: \"ADMIN001\",\n        phone: \"13800138000\",\n        email: \"<EMAIL>\",\n        department: \"法律服务部\",\n        position: \"系统管理员\",\n        role: \"系统管理员\",\n        bio: \"负责系统管理和维护工作，确保系统稳定运行。\",\n        avatar: \"\"\n      },\n      originalUserInfo: {},\n      lastLoginTime: \"2024-01-15 09:30:25\",\n      lastLoginIP: \"*************\",\n      rules: {\n        name: [\n          { required: true, message: \"请输入姓名\", trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, message: \"请输入手机号\", trigger: \"blur\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\n          { type: \"email\", message: \"请输入正确的邮箱格式\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  mounted() {\n    this.loadUserInfo();\n  },\n  methods: {\n    loadUserInfo() {\n      // 这里可以从后端加载用户信息\n      // 目前使用默认数据\n      this.originalUserInfo = { ...this.userInfo };\n    },\n    enableEdit() {\n      this.editMode = true;\n      this.originalUserInfo = { ...this.userInfo };\n    },\n    cancelEdit() {\n      this.editMode = false;\n      this.userInfo = { ...this.originalUserInfo };\n    },\n    saveProfile() {\n      this.$refs.userForm.validate((valid) => {\n        if (valid) {\n          this.saving = true;\n          // 模拟保存过程\n          setTimeout(() => {\n            this.saving = false;\n            this.editMode = false;\n            this.$message.success(\"个人信息保存成功！\");\n          }, 1000);\n        }\n      });\n    },\n    changePassword() {\n      this.$router.push(\"/changePwd\");\n    },\n    handleAvatarSuccess(res) {\n      if (res && res.data && res.data.url) {\n        this.userInfo.avatar = res.data.url;\n        this.$message.success(\"头像上传成功！\");\n      }\n    },\n    beforeAvatarUpload(file) {\n      const isImage = /^image\\/(jpeg|png|jpg)$/.test(file.type);\n      const isLt2M = file.size / 1024 / 1024 < 2;\n\n      if (!isImage) {\n        this.$message.error(\"上传头像图片只能是 JPG/PNG 格式!\");\n        return false;\n      }\n      if (!isLt2M) {\n        this.$message.error(\"上传头像图片大小不能超过 2MB!\");\n        return false;\n      }\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* 页面布局样式 */\n.page-wrapper {\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding: 16px;\n}\n\n.page-container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 页面头部 */\n.page-header {\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #ffffff;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.page-title i {\n  color: #1890ff;\n  font-size: 22px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #8c8c8c;\n  margin: 0;\n}\n\n/* 个人信息区域 */\n.profile-section {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n}\n\n/* 个人信息卡片 */\n.profile-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n/* 头像区域 */\n.avatar-section {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 32px;\n  display: flex;\n  align-items: center;\n  gap: 24px;\n  color: white;\n}\n\n.avatar-container {\n  position: relative;\n  flex-shrink: 0;\n}\n\n.avatar-image {\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n}\n\n.avatar-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s;\n  cursor: pointer;\n}\n\n.avatar-container:hover .avatar-overlay {\n  opacity: 1;\n}\n\n.avatar-uploader {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar-uploader-icon {\n  font-size: 24px;\n  color: white;\n}\n\n.user-basic-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 24px;\n  font-weight: 500;\n  margin: 0 0 8px 0;\n}\n\n.user-role {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0 0 4px 0;\n}\n\n.user-department {\n  font-size: 14px;\n  opacity: 0.8;\n  margin: 0;\n}\n\n/* 表单区域 */\n.form-section {\n  padding: 32px;\n}\n\n.profile-form {\n  max-width: 600px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24px;\n  margin-bottom: 0;\n}\n\n.form-item {\n  margin-bottom: 24px;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  gap: 12px;\n}\n\n/* 安全设置卡片 */\n.security-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  height: fit-content;\n}\n\n.card-header {\n  padding: 24px 24px 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #262626;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.card-title i {\n  color: #1890ff;\n}\n\n.security-items {\n  padding: 16px 24px 24px 24px;\n}\n\n.security-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.security-item:last-child {\n  border-bottom: none;\n}\n\n.security-info {\n  flex: 1;\n}\n\n.security-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n  margin-bottom: 4px;\n}\n\n.security-desc {\n  font-size: 12px;\n  color: #8c8c8c;\n}\n\n.security-action {\n  color: #1890ff;\n  font-size: 14px;\n}\n\n.security-action:hover {\n  color: #40a9ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .profile-section {\n    grid-template-columns: 1fr;\n  }\n\n  .avatar-section {\n    flex-direction: column;\n    text-align: center;\n    gap: 16px;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 0;\n  }\n\n  .form-section {\n    padding: 24px 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n}\n\n/* 表单样式优化 */\n.profile-form ::v-deep .el-form-item__label {\n  color: #262626;\n  font-weight: 500;\n}\n\n.profile-form ::v-deep .el-input__inner {\n  border-radius: 6px;\n}\n\n.profile-form ::v-deep .el-input__inner:focus {\n  border-color: #1890ff;\n}\n\n.profile-form ::v-deep .el-textarea__inner {\n  border-radius: 6px;\n}\n\n.profile-form ::v-deep .el-select {\n  width: 100%;\n}\n\n/* 禁用状态样式 */\n.profile-form ::v-deep .el-input.is-disabled .el-input__inner {\n  background-color: #f5f5f5;\n  border-color: #e4e7ed;\n  color: #606266;\n}\n</style>\n"]}]}