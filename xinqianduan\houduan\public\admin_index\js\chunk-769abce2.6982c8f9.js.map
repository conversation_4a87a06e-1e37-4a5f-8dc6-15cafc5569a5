{"version": 3, "sources": ["webpack:///./src/views/pages/taocan/dingdan.vue?620a", "webpack:///./src/views/pages/taocan/dingdan.vue", "webpack:///src/views/pages/taocan/dingdan.vue", "webpack:///./src/views/pages/taocan/dingdan.vue?61a7", "webpack:///./src/views/pages/taocan/dingdan.vue?3379", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "slot", "_v", "_s", "$router", "currentRoute", "name", "staticStyle", "on", "refulsh", "allSize", "model", "value", "search", "keyword", "callback", "$$v", "$set", "expression", "refund_time", "$event", "getData", "directives", "rawName", "loading", "list", "scopedSlots", "_u", "key", "fn", "scope", "row", "client", "company", "linkman", "phone", "taocan", "title", "price", "year", "pay_type", "qishu", "pay_age", "total_price", "showStatus", "status", "_e", "status_msg", "editData", "id", "nativeOn", "preventDefault", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "is_info", "info", "viewUserData", "showImage", "pic_path", "ruleForm", "debts", "end_time", "updateEndTIme", "_l", "num", "item", "index", "is_num", "fenqi", "date", "pay_path", "changePinzhen", "handleSuccess", "beforeUpload", "desc", "dialogStatus", "ref", "rules", "form<PERSON>abe<PERSON><PERSON>", "changeStatus", "dialogEndTime", "changeEndTime", "dialogVisible", "show_image", "用户详情", "dialogViewUserDetail", "currentId", "staticRenderFns", "components", "UserDetails", "data", "page", "url", "dialogAddOrder", "required", "message", "trigger", "mounted", "methods", "_this", "getInfo", "getRequest", "then", "resp", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "$message", "splice", "msg", "catch", "postRequest", "go", "count", "saveData", "$refs", "validate", "valid", "val", "res", "file", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "showEndTime", "component", "nickname", "headimg", "yuangong_id", "linkphone", "tiaojie_id", "fawu_id", "lian_id", "htsczy_id", "ls_id", "ywy_id", "license", "start_time", "props", "String", "watch", "immediate", "handler", "newId"], "mappings": "yIAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,OAAS,WAAW,CAACF,EAAG,MAAM,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGP,KAAKQ,QAAQC,aAAaC,SAAST,EAAG,YAAY,CAACU,YAAY,CAAC,MAAQ,QAAQ,QAAU,SAASR,MAAM,CAAC,KAAO,QAAQS,GAAG,CAAC,MAAQb,EAAIc,UAAU,CAACd,EAAIO,GAAG,SAAS,GAAGL,EAAG,SAAS,CAACA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,oBAAoB,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOC,QAASC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,WAAW,KAAOJ,EAAIe,SAASC,MAAM,CAACC,MAAOjB,EAAIkB,OAAOC,QAASC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,UAAWG,IAAME,WAAW,qBAAqB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,YAAY,gBAAgB,GAAG,kBAAkB,IAAI,oBAAoB,SAAS,kBAAkB,SAAS,KAAO,OAAO,eAAe,sBAAsB,eAAe,CAAC,WAAY,aAAaY,MAAM,CAACC,MAAOjB,EAAIkB,OAAOM,YAAaJ,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIkB,OAAQ,cAAeG,IAAME,WAAW,yBAAyB,GAAGrB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,KAAOJ,EAAIe,SAASF,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAI0B,aAAa,CAAC1B,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,WAAW,CAACyB,WAAW,CAAC,CAAChB,KAAK,UAAUiB,QAAQ,YAAYX,MAAOjB,EAAI6B,QAASN,WAAW,YAAYX,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQR,MAAM,CAAC,KAAOJ,EAAI8B,KAAK,KAAO,SAAS,CAAC5B,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAGF,EAAMC,IAAIC,OAAOC,aAAa,GAAGpC,EAAG,SAAS,CAACF,EAAIO,GAAG,OAAOP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAIF,EAAMC,IAAIC,OAAOE,YAAYvC,EAAIO,GAAG,UAAUL,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAqB,MAAlB2B,EAAMC,IAAIC,OAAc,GAAIF,EAAMC,IAAIC,OAAOG,WAAW,UAAUtC,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAASN,EAAMC,IAAIK,OAAOC,MAAM,OAAOxC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAAON,EAAMC,IAAIK,OAAOE,MAAM,IAAI,OAAOzC,EAAG,SAAS,CAACF,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAG2B,EAAMC,IAAIK,OAAQN,EAAMC,IAAIK,OAAOG,KAAM,IAAI,QAAQ,UAAU1C,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAyB,GAAtB2B,EAAMC,IAAIS,SAAgB,KAAO,MAAaV,EAAMC,IAAIU,MAAQ,QAAQ5C,EAAG,SAAS,CAACF,EAAIO,GAAG,OAAOP,EAAIQ,GAAG2B,EAAMC,IAAIW,SAAS,OAAO7C,EAAG,SAAS,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAyB,GAAtB2B,EAAMC,IAAIS,SAAc,EAAEV,EAAMC,IAAIY,YAAcb,EAAMC,IAAIW,SAAS,cAAc7C,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIiD,WAAWd,EAAMC,QAAQ,CAAoB,GAAlBD,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,WAA8B,GAAlB4B,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,aAAgC,GAAlB4B,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACU,YAAY,CAAC,OAAS,UAAU,MAAQ,YAAY,CAACZ,EAAIO,GAAG,WAAWP,EAAImD,KAAwB,GAAlBhB,EAAMC,IAAIc,OAAWhD,EAAG,SAAS,CAACA,EAAG,OAAO,CAACF,EAAIO,GAAG,MAAMP,EAAIQ,GAAG2B,EAAMC,IAAIgB,iBAAiBpD,EAAImD,MAAM,UAAUjD,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,eAAe,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,cAAc,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,WAAW,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAM2B,YAAY/B,EAAIgC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIqD,SAASlB,EAAMC,IAAIkB,OAAO,CAACtD,EAAIO,GAAG,QAAQL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASmD,SAAS,CAAC,MAAQ,SAAS9B,GAAgC,OAAxBA,EAAO+B,iBAAwBxD,EAAIyD,QAAQtB,EAAMuB,OAAQvB,EAAMC,IAAIkB,OAAO,CAACtD,EAAIO,GAAG,kBAAkB,GAAGL,EAAG,MAAM,CAACG,YAAY,YAAY,CAACH,EAAG,gBAAgB,CAACE,MAAM,CAAC,aAAa,CAAC,GAAI,IAAK,IAAK,IAAK,KAAK,YAAYJ,EAAI2D,KAAK,OAAS,0CAA0C,MAAQ3D,EAAI4D,OAAO/C,GAAG,CAAC,cAAcb,EAAI6D,iBAAiB,iBAAiB7D,EAAI8D,wBAAwB,IAAI,GAAG5D,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI+D,kBAAkB,wBAAuB,GAAOlD,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI+D,kBAAkBtC,KAAU,CAAEzB,EAAIgE,QAAS9D,EAAG,MAAM,CAACA,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAGrC,EAAIiE,KAAK5B,OAAOC,YAAYpC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIkE,aAAalE,EAAIiE,KAAK5B,OAAOiB,OAAO,CAACtD,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAIrC,EAAIiE,KAAK5B,OAAOE,YAAYvC,EAAImD,MAAM,GAAGjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIkE,aAAalE,EAAIiE,KAAK5B,OAAOiB,OAAO,CAACtD,EAAIO,GAAGP,EAAIQ,GAAoB,MAAjBR,EAAIiE,KAAK5B,OAAc,GAAIrC,EAAIiE,KAAK5B,OAAOG,UAAUxC,EAAImD,MAAM,GAAGjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAmB,MAAjBJ,EAAIiE,KAAK5B,OAAcnC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAK5B,OAAO+B,aAAa,CAACpE,EAAIO,GAAG,SAASL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,UAAU,OAAQ,IAAQ,CAACF,EAAG,uBAAuB,CAACA,EAAG,WAAW,CAACyB,WAAW,CAAC,CAAChB,KAAK,UAAUiB,QAAQ,YAAYX,MAAOjB,EAAI6B,QAASN,WAAW,YAAYX,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQR,MAAM,CAAC,KAAOJ,EAAIqE,SAASC,MAAM,KAAO,SAAS,CAACpE,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAWF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,MAAM,MAAQ,WAAWF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,aAAa,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,YAAY,MAAQ,YAAYF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,QAAQF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,SAAS,MAAQ,UAAU,IAAI,IAAI,GAAGF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOC,UAAUxC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOE,UAAUzC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKxB,OAAOG,MAAM,QAAQ,GAAG1C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,WAAW,OAAS,sBAAsB,eAAe,sBAAsB,YAAc,OAAO,KAAO,QAAQY,MAAM,CAACC,MAAOjB,EAAIiE,KAAKM,SAAUnD,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIiE,KAAM,WAAY5C,IAAME,WAAW,mBAAmBrB,EAAG,SAAS,CAACU,YAAY,CAAC,OAAS,WAAWR,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIwE,mBAAmB,CAACxE,EAAIO,GAAG,SAAS,IAAI,GAAGL,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAASJ,EAAIyE,GAAIzE,EAAIiE,KAAKxB,OAAOiC,KAAK,SAASC,EAAKC,GAAO,OAAO1E,EAAG,uBAAuB,CAAC+B,IAAI2C,EAAMxE,MAAM,CAAC,MAAQuE,EAAKjC,QAAQ,CAAC1C,EAAIO,GAAGP,EAAIQ,GAAkB,GAAfmE,EAAKE,OAAcF,EAAK1D,MAAQ,IAAM,YAAW,GAAGf,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAwB,GAArBR,EAAIiE,KAAKpB,SAAgB,KAAO,MAAa7C,EAAIiE,KAAKnB,MAAQ,QAAQ5C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKlB,SAAS,QAAQ,GAAG7C,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKjB,YAAchD,EAAIiE,KAAKlB,SAAS,QAAQ,IAAI,GAAuB,GAAnB/C,EAAIiE,KAAKpB,SAAa3C,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,QAAQJ,EAAImD,KAAKnD,EAAIyE,GAAIzE,EAAIiE,KAAKa,OAAO,SAASH,EAAKC,GAAO,OAA6B,GAArB5E,EAAIiE,KAAKpB,SAAe3C,EAAG,kBAAkB,CAAC+B,IAAI2C,GAAO,CAAC1E,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAANwE,EAAQ,GAAG,MAAM,CAAC5E,EAAIO,GAAG,IAAIP,EAAIQ,GAAGmE,EAAKhC,OAAO,OAAOzC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAANwE,EAAQ,GAAG,QAAQ,CAAC5E,EAAIO,GAAG,IAAIP,EAAIQ,GAAGmE,EAAKI,MAAM,OAAO7E,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,KAAW,EAANwE,EAAQ,GAAG,QAAQ,CAAED,EAAKK,SAAU9E,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUQ,EAAKK,aAAa,CAAChF,EAAIO,GAAG,QAAQL,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,SAASS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIiF,cAAcL,MAAU,CAAC1E,EAAG,YAAY,CAACE,MAAM,CAAC,OAAS,4BAA4B,OAAS,oBAAoB,kBAAiB,EAAM,aAAaJ,EAAIkF,cAAc,gBAAgBlF,EAAImF,eAAe,CAACnF,EAAIO,GAAG,aAAa,IAAI,IAAI,GAAGP,EAAImD,QAAOjD,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKmB,UAAU,IAAI,GAAGpF,EAAImD,OAAOjD,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAIqF,aAAa,wBAAuB,GAAOxE,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAIqF,aAAa5D,KAAU,CAACvB,EAAG,UAAU,CAACoF,IAAI,WAAWlF,MAAM,CAAC,MAAQJ,EAAIqE,SAAS,MAAQrE,EAAIuF,QAAQ,CAACrF,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,KAAK,cAAcJ,EAAIwF,iBAAiB,CAACtF,EAAG,MAAM,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,EAAE,SAAgC,GAArBJ,EAAIqE,SAASnB,QAAsBlC,MAAM,CAACC,MAAOjB,EAAIqE,SAASnB,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIqE,SAAU,SAAUhD,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,UAAUL,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAIqE,SAASnB,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIqE,SAAU,SAAUhD,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,UAAUL,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,GAAGY,MAAM,CAACC,MAAOjB,EAAIqE,SAASnB,OAAQ9B,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIqE,SAAU,SAAUhD,IAAME,WAAW,oBAAoB,CAACvB,EAAIO,GAAG,aAAa,KAA2B,GAArBP,EAAIqE,SAASnB,OAAWhD,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,QAAQ,cAAcJ,EAAIwF,eAAe,KAAO,eAAe,CAACtF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,SAASY,MAAM,CAACC,MAAOjB,EAAIqE,SAASjB,WAAYhC,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIqE,SAAU,aAAchD,IAAME,WAAW,0BAA0B,GAAGvB,EAAImD,MAAM,GAAGjD,EAAG,MAAM,CAACG,YAAY,gBAAgBD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQzB,EAAIqF,cAAe,KAAS,CAACrF,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAIyF,kBAAkB,CAACzF,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,SAAS,QAAUJ,EAAI0F,cAAc,wBAAuB,GAAO7E,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI0F,cAAcjE,KAAU,CAACvB,EAAG,UAAU,CAACoF,IAAI,WAAWlF,MAAM,CAAC,MAAQJ,EAAIqE,SAAS,MAAQrE,EAAIuF,QAAQ,CAACrF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,QAAQ,YAAc,QAAQY,MAAM,CAACC,MAAOjB,EAAIqE,SAASE,SAAUnD,SAAS,SAAUC,GAAMrB,EAAIsB,KAAKtB,EAAIqE,SAAU,WAAYhD,IAAME,WAAW,wBAAwB,GAAGrB,EAAG,MAAM,CAACG,YAAY,gBAAgBD,MAAM,CAAC,KAAO,UAAUE,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACW,GAAG,CAAC,MAAQ,SAASY,GAAQzB,EAAI0F,eAAgB,KAAS,CAAC1F,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWS,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAI2F,mBAAmB,CAAC3F,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI4F,cAAc,MAAQ,OAAO/E,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI4F,cAAcnE,KAAU,CAACvB,EAAG,WAAW,CAACE,MAAM,CAAC,IAAMJ,EAAI6F,eAAe,GAAG3F,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAI8F,KAAK,QAAU9F,EAAI+F,qBAAqB,wBAAuB,GAAOlF,GAAG,CAAC,iBAAiB,SAASY,GAAQzB,EAAI+F,qBAAqBtE,KAAU,CAACvB,EAAG,eAAe,CAACE,MAAM,CAAC,GAAKJ,EAAIgG,cAAc,IAAI,IAElrXC,EAAkB,G,YC0StB,GACAtF,KAAA,OACAuF,WAAA,CAAAC,oBACAC,OACA,OACArF,QAAA,OACAe,KAAA,GACA8B,MAAA,EACAyC,KAAA,EACA1C,KAAA,GACAqC,UAAA,EACA9E,OAAA,CACAC,QAAA,IAEAU,SAAA,EACAyE,IAAA,YACArC,KAAA,GACAD,SAAA,EACAD,mBAAA,EACA6B,eAAA,EACAG,sBAAA,EACAV,cAAA,EACAK,eAAA,EACAa,gBAAA,EACAlC,SAAA,CACAnB,OAAA,EACAE,WAAA,GACAE,GAAA,IAEAiC,MAAA,CACAnC,WAAA,EACAoD,UAAA,EACAC,QAAA,WACAC,QAAA,SAEAnC,SAAA,EACAiC,UAAA,EACAC,QAAA,QACAC,QAAA,UAGAlB,eAAA,QACAK,WAAA,GACAjB,MAAA,IAGA+B,UACA,KAAAjF,WAEAkF,QAAA,CACAvD,SAAAC,GACA,IAAAuD,EAAA,KACA,GAAAvD,EACA,KAAAwD,QAAAxD,GAEA,KAAAe,SAAA,CACA3B,MAAA,IAGAmE,EAAA9C,mBAAA,GAEAG,aAAAZ,GACA,IAAAuD,EAAA,KACA,GAAAvD,IACA,KAAA0C,UAAA1C,GAGAuD,EAAAd,sBAAA,GAEAe,QAAAxD,GACA,IAAAuD,EAAA,KACAA,EAAAE,WAAAF,EAAAP,IAAA,WAAAhD,GAAA0D,KAAAC,IACAA,IACAJ,EAAA5C,KAAAgD,EAAAb,KACAS,EAAA7C,SAAA,MAIAP,QAAAmB,EAAAtB,GACA,KAAA4D,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,KAAAM,cAAA,KAAAhB,IAAA,aAAAhD,GAAA0D,KAAAC,IACA,KAAAA,EAAAM,MACA,KAAAC,SAAA,CACAH,KAAA,UACAZ,QAAA,UAEA,KAAA3E,KAAA2F,OAAA7C,EAAA,IAEAiC,MAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,UAKAC,MAAA,KACA,KAAAH,SAAA,CACAH,KAAA,QACAZ,QAAA,aAKAjC,gBACA,KAAA0C,SAAA,kBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAL,KAAA,KACA,IAAAZ,EAAA,SAAAnC,KAAAX,GAAA,cAAAW,KAAAM,UACA,KAAAqD,YAAA,KAAAtB,IAAA,gBAAAF,GACAY,KAAAC,IACA,KAAAA,EAAAM,KACA,KAAAC,SAAA,CACAH,KAAA,UACAZ,QAAA,UAGAI,MAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,UAKAC,MAAA,KACA,KAAAH,SAAA,CACAH,KAAA,QACAZ,QAAA,aAIA3F,UACA,KAAAL,QAAAoH,GAAA,IAEAnG,UACA,IAAAmF,EAAA,KACAA,EAAAhF,SAAA,EACAgF,EACAe,YACAf,EAAAP,IAAA,eAAAO,EAAAR,KAAA,SAAAQ,EAAAlD,KACAkD,EAAA3F,QAEA8F,KAAAC,IACA,KAAAA,EAAAM,OACAV,EAAA/E,KAAAmF,EAAAb,KACAS,EAAAjD,MAAAqD,EAAAa,OAEAjB,EAAAhF,SAAA,KAGAkG,WACA,IAAAlB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAgBA,SAfA,KAAAN,YAAAf,EAAAP,IAAA,YAAAjC,UAAA2C,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAAQ,EAAAS,MAEAb,EAAA9C,mBAAA,GAEA8C,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,WASA7D,iBAAAsE,GACA,KAAAxE,KAAAwE,EAEA,KAAAzG,WAEAoC,oBAAAqE,GACA,KAAA9B,KAAA8B,EACA,KAAAzG,WAEAwD,cAAAkD,GACA,IAAAvB,EAAA,KACA,KAAAuB,EAAAb,OACAV,EAAA5C,KAAAa,MAAA+B,EAAAjC,OAAAI,SAAAoD,EAAAhC,KAAAE,IACAO,EAAAe,YAAAf,EAAAP,IAAA,QACA,GAAAO,EAAA5C,KAAAX,GACA,MAAAuD,EAAA5C,KAAAa,QACAkC,KAAAC,IACA,KAAAA,EAAAM,KACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAIAI,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAA,aAQAtB,aAAAkD,GACA,MAAAC,EAAA,0BAAAC,KAAAF,EAAAhB,MACAiB,GACA,KAAAd,SAAAgB,MAAA,cAIAC,SAAAJ,EAAAK,GACA,IAAA7B,EAAA,KACAA,EAAAE,WAAA,6BAAAsB,GAAArB,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAxC,SAAAqE,GAAA,GAEA7B,EAAAW,SAAAmB,QAAA,UAEA9B,EAAAW,SAAAgB,MAAAvB,EAAAS,QAIAvD,UAAAkE,GACA,KAAAxC,WAAAwC,EACA,KAAAzC,eAAA,GAEAX,cAAAL,GACA,KAAAA,SAEA3B,WAAAb,GACA,KAAAiD,cAAA,EACA,KAAAhB,SAAAjC,GAEAwG,YAAAxG,GACA,KAAAsD,eAAA,EACA,KAAArB,SAAAjC,GAEAuD,gBACA,IAAAkB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBArB,EAAAe,YAAAf,EAAAP,IAAA,QACA,GAAAO,EAAAxC,SAAAf,GACA,SAAAuD,EAAAxC,SAAAE,WAEAyC,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAEAI,EAAAxB,cAAA,GAEAwB,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,WASAjC,eAGA,IAAAoB,EAAA,KACA,KAAAmB,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAoBA,SAnBArB,EAAAe,YAAAf,EAAAP,IAAA,gBACA,GAAAO,EAAAxC,SAAAf,GACA,OAAAuD,EAAAxC,SAAAnB,OACA,WAAA2D,EAAAxC,SAAAjB,aACA4D,KAAAC,IACA,KAAAA,EAAAM,MACAV,EAAAW,SAAA,CACAH,KAAA,UACAZ,QAAA,SAEAI,EAAAxB,cAAA,GAEAwB,EAAAW,SAAA,CACAH,KAAA,QACAZ,QAAAQ,EAAAS,aCnlB8W,I,wBCQ1WmB,EAAY,eACd,EACA9I,EACAkG,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,2CCnBf,IAAI9I,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK3B,YAAYpC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKzB,UAAUtC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK6E,aAAa5I,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK1B,YAAYrC,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAAqB,IAAnBJ,EAAIiE,KAAK8E,SAAkC,MAAlB/I,EAAIiE,KAAK8E,QAAe7I,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQR,MAAM,CAAC,IAAMJ,EAAIiE,KAAK8E,SAASlI,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAK8E,aAAa/I,EAAImD,OAAOjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAK+E,gBAAgB9I,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKgF,cAAc/I,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKiF,YAAY,OAAOhJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKkF,SAAS,OAAOjJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKmF,SAAS,OAAOlJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKoF,WAAW,OAAOnJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKqF,OAAO,OAAOpJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKsF,QAAQ,OAAOrJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAAqB,IAAnBJ,EAAIiE,KAAKuF,SAAkC,MAAlBxJ,EAAIiE,KAAKuF,QAAetJ,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQR,MAAM,CAAC,IAAMJ,EAAIiE,KAAKuF,SAAS3I,GAAG,CAAC,MAAQ,SAASY,GAAQ,OAAOzB,EAAImE,UAAUnE,EAAIiE,KAAKuF,aAAaxJ,EAAImD,OAAOjD,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKwF,eAAevJ,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIiE,KAAKrB,MAAM,QAAQ,IAAI,IAE33DqD,EAAkB,GCmEtB,GACAtF,KAAA,cACA+I,MAAA,CACApG,GAAA,CACA+D,KAAAsC,OACAnD,UAAA,IAGAJ,OACA,OACAnC,KAAA,KAGA2F,MAAA,CACAtG,GAAA,CACAuG,WAAA,EACAC,QAAAC,GACA,KAAAjD,QAAAiD,MAIAnD,QAAA,CACAE,QAAAxD,GACA,IAAAuD,EAAA,KACAA,EAAAE,WAAA,iBAAAzD,GAAA0D,KAAAC,IACAA,IACAJ,EAAA5C,KAAAgD,EAAAb,WC/FmV,I,YCO/UyC,EAAY,eACd,EACA9I,EACAkG,GACA,EACA,KACA,KACA,MAIa,OAAA4C,E", "file": "js/chunk-769abce2.6982c8f9.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=style&index=0&id=d5cc8fca&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-card',{attrs:{\"shadow\":\"always\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(_vm._s(this.$router.currentRoute.name))]),_c('el-button',{staticStyle:{\"float\":\"right\",\"padding\":\"3px 0\"},attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_vm._v(\"刷新\")])],1),_c('el-row',[_c('el-col',{attrs:{\"span\":4}},[_c('el-input',{attrs:{\"placeholder\":\"请输入订单号/购买人/套餐/手机号\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-col',{attrs:{\"span\":3}},[_c('el-input',{attrs:{\"placeholder\":\"请输入业务员姓名\",\"size\":_vm.allSize},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}})],1),_c('el-col',{attrs:{\"span\":8}},[_c('el-date-picker',{attrs:{\"type\":\"daterange\",\"unlink-panels\":\"\",\"range-separator\":\"至\",\"start-placeholder\":\"支付开始日期\",\"end-placeholder\":\"支付结束日期\",\"size\":\"mini\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"default-time\":['00:00:00', '23:59:59']},model:{value:(_vm.search.refund_time),callback:function ($$v) {_vm.$set(_vm.search, \"refund_time\", $$v)},expression:\"search.refund_time\"}})],1),_c('el-col',{attrs:{\"span\":1}},[_c('el-button',{attrs:{\"size\":_vm.allSize},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\"搜索\")])],1)],1),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\",\"margin-top\":\"10px\"},attrs:{\"data\":_vm.list,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"客户信息\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"公司名称:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.client==null ?'':scope.row.client.company))])],1),_c('el-row',[_vm._v(\"联系人:\"+_vm._s(scope.row.client==null ?'': scope.row.client.linkman))]),_vm._v(\" 用户内容 \"),_c('el-row',[_vm._v(\"联系方式:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.client==null ?'': scope.row.client.phone))])],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"套餐内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"套餐名称:\"+_vm._s(scope.row.taocan ? scope.row.taocan.title:''))]),_c('el-row',[_vm._v(\"套餐价格:\"+_vm._s(scope.row.taocan?scope.row.taocan.price:\"\")+\"元\")]),_c('el-row',[_vm._v(\"套餐年份:\"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.taocan ?scope.row.taocan.year :'')+\"年\")])],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"支付情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-row',[_vm._v(\"支付类型:\"+_vm._s(scope.row.pay_type == 1 ? \"全款\" : \"分期\" + \"/\" + scope.row.qishu + \"期\"))]),_c('el-row',[_vm._v(\"已付款:\"+_vm._s(scope.row.pay_age)+\"元\")]),_c('el-row',[_vm._v(\"剩余尾款:\"+_vm._s(scope.row.pay_type == 1?0:scope.row.total_price - scope.row.pay_age)+\"元\")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"审核状态\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{on:{\"click\":function($event){return _vm.showStatus(scope.row)}}},[(scope.row.status==1)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#409EFF\"}},[_vm._v(\"未审核\")])]):(scope.row.status==3)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#F56C6C\"}},[_vm._v(\"审核未通过\")])]):(scope.row.status==2)?_c('el-row',[_c('span',{staticStyle:{\"cursor\":\"pointer\",\"color\":\"#67C23A\"}},[_vm._v(\"已通过\")])]):_vm._e(),(scope.row.status==3)?_c('el-row',[_c('span',[_vm._v(\"原因:\"+_vm._s(scope.row.status_msg))])]):_vm._e()],1)]}}])}),_c('el-table-column',{attrs:{\"prop\":\"member.title\",\"label\":\"业务员\"}}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"创建时间\"}}),_c('el-table-column',{attrs:{\"prop\":\"end_time\",\"label\":\"到期时间\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"查看\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 移除 \")])]}}])})],1),_c('div',{staticClass:\"page-top\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 100, 200, 300, 400],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":\"订单信息\",\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[(_vm.is_info)?_c('div',[_c('el-descriptions',{attrs:{\"title\":\"客户信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.info.client==null ?'':_vm.info.client.company))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client.id)}}},[_vm._v(_vm._s(_vm.info.client==null ?'': _vm.info.client.linkman))]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"联系方式\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewUserData(_vm.info.client.id)}}},[_vm._v(_vm._s(_vm.info.client==null ?'': _vm.info.client.phone))]):_vm._e()],1),_c('el-descriptions-item',{attrs:{\"label\":\"营业执照\"}},[(_vm.info.client!=null)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.client.pic_path)}}},[_vm._v(\"查看 \")]):_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(\"暂无\")])],1)],1),_c('el-descriptions',{attrs:{\"title\":\"债务人相关信息\",\"colon\":false}},[_c('el-descriptions-item',[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\",\"margin-top\":\"10px\"},attrs:{\"data\":_vm.ruleForm.debts,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\"}}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\"}}),_c('el-table-column',{attrs:{\"prop\":\"tiaojie_id\",\"label\":\"调解员\"}}),_c('el-table-column',{attrs:{\"prop\":\"fawu_id\",\"label\":\"法务专员\"}}),_c('el-table-column',{attrs:{\"prop\":\"lian_id\",\"label\":\"立案专员\"}}),_c('el-table-column',{attrs:{\"prop\":\"htsczy_id\",\"label\":\"合同上传专用\"}}),_c('el-table-column',{attrs:{\"prop\":\"ls_id\",\"label\":\"律师\"}}),_c('el-table-column',{attrs:{\"prop\":\"ywy_id\",\"label\":\"业务员\"}})],1)],1)],1),_c('el-descriptions',{attrs:{\"title\":\"套餐内容\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"套餐名称\"}},[_vm._v(_vm._s(_vm.info.taocan.title))]),_c('el-descriptions-item',{attrs:{\"label\":\"套餐价格\"}},[_vm._v(_vm._s(_vm.info.taocan.price))]),_c('el-descriptions-item',{attrs:{\"label\":\"套餐年份\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.taocan.year)+\"年\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"到期时间\"}},[_c('el-date-picker',{attrs:{\"type\":\"datetime\",\"format\":\"yyyy-MM-dd HH:mm:ss\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择日期\",\"size\":\"mini\"},model:{value:(_vm.info.end_time),callback:function ($$v) {_vm.$set(_vm.info, \"end_time\", $$v)},expression:\"info.end_time\"}}),_c('el-tag',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.updateEndTIme()}}},[_vm._v(\"修改\")])],1)],1),_c('el-descriptions',{attrs:{\"title\":\"套餐详情\"}},_vm._l((_vm.info.taocan.num),function(item,index){return _c('el-descriptions-item',{key:index,attrs:{\"label\":item.title}},[_vm._v(_vm._s(item.is_num == 1 ? item.value + \"次\" : \"不限\"))])}),1),_c('el-descriptions',{attrs:{\"title\":\"款项信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"付款类型\"}},[_vm._v(_vm._s(_vm.info.pay_type == 1 ? \"全款\" : \"分期\" + \"/\" + _vm.info.qishu + \"期\"))]),_c('el-descriptions-item',{attrs:{\"label\":\"已付款\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.pay_age)+\"元\")])],1),_c('el-descriptions-item',{attrs:{\"label\":\"剩余款\"}},[_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.info.total_price - _vm.info.pay_age)+\"元\")])],1)],1),(_vm.info.pay_type==2)?_c('el-descriptions',{attrs:{\"title\":\"期数\"}}):_vm._e(),_vm._l((_vm.info.fenqi),function(item,index){return (_vm.info.pay_type == 2)?_c('el-descriptions',{key:index},[_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'期'}},[_vm._v(\" \"+_vm._s(item.price)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'还款期'}},[_vm._v(\" \"+_vm._s(item.date)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":'第'+(index*1+1)+'期凭证'}},[(item.pay_path)?_c('el-tag',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.showImage(item.pay_path)}}},[_vm._v(\"查看\")]):_c('el-tag',{attrs:{\"type\":\"warning\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.changePinzhen(index)}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"accept\":\".jpg, .jpeg, .png\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess,\"before-upload\":_vm.beforeUpload}},[_vm._v(\" 上传凭证 \")])],1)],1)],1):_vm._e()}),_c('el-descriptions',{attrs:{\"title\":\"备注信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"具体内容\"}},[_vm._v(_vm._s(_vm.info.desc))])],1)],2):_vm._e()]),_c('el-dialog',{attrs:{\"title\":\"审核内容\",\"visible\":_vm.dialogStatus,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogStatus=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"审核\",\"label-width\":_vm.formLabelWidth}},[_c('div',[_c('el-radio',{attrs:{\"label\":1,\"disabled\":_vm.ruleForm.status==2?true:false},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"未审核 \")]),_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"审核通过\")]),_c('el-radio',{attrs:{\"label\":3},model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_vm._v(\"审核不通过 \")])],1)]),(_vm.ruleForm.status==3)?_c('el-form-item',{attrs:{\"label\":\"不通过原因\",\"label-width\":_vm.formLabelWidth,\"prop\":\"status_msg\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"请输入内容\"},model:{value:(_vm.ruleForm.status_msg),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status_msg\", $$v)},expression:\"ruleForm.status_msg\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogStatus = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.changeStatus()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"设置到期时间\",\"visible\":_vm.dialogEndTime,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogEndTime=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"format\":\"Y-m-d\",\"placeholder\":\"选择日期\"},model:{value:(_vm.ruleForm.end_time),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"end_time\", $$v)},expression:\"ruleForm.end_time\"}})],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogEndTime = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.changeEndTime()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"60%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{attrs:{\"title\":_vm.用户详情,\"visible\":_vm.dialogViewUserDetail,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.dialogViewUserDetail=$event}}},[_c('user-details',{attrs:{\"id\":_vm.currentId}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div>\r\n\t\t<el-card shadow=\"always\">\r\n\t\t\t<div slot=\"header\" class=\"clearfix\">\r\n\t\t\t\t<span>{{ this.$router.currentRoute.name }}</span>\r\n\t\t\t\t<el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refulsh\">刷新</el-button>\r\n\t\t\t</div>\r\n\t\t\t<el-row>\r\n\t\t\t\t<el-col :span=\"4\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\tplaceholder=\"请输入订单号/购买人/套餐/手机号\"\r\n\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t:size=\"allSize\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :span=\"3\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\tplaceholder=\"请输入业务员姓名\"\r\n\t\t\t\t\t\t\tv-model=\"search.keyword\"\r\n\t\t\t\t\t\t\t:size=\"allSize\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-col>\r\n\t\t\t\t<el-col :span=\"8\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\tv-model=\"search.refund_time\"\r\n\t\t\t\t\t\t\ttype=\"daterange\"\r\n\t\t\t\t\t\t\tunlink-panels\r\n\t\t\t\t\t\t\trange-separator=\"至\"\r\n\t\t\t\t\t\t\tstart-placeholder=\"支付开始日期\"\r\n\t\t\t\t\t\t\tend-placeholder=\"支付结束日期\"\r\n\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t:default-time=\"['00:00:00', '23:59:59']\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t</el-date-picker>\r\n\t\t\t\t</el-col>\r\n\r\n\t\t\t\t<el-col :span=\"1\">\r\n\t\t\t\t\t<el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n\t\t\t\t</el-col>\r\n\t\t\t</el-row>\r\n\t\t\t<!-- <el-row class=\"page-top\">\r\n\t\t\t\t<el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">新增</el-button>\r\n\t\t\t</el-row> -->\r\n\t\t\t<el-table :data=\"list\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"客户信息\">\r\n\t\t\t\t\t<template slot-scope=\"scope\" @click=\"viewUserData(info.client.id)\">\r\n\t\t\t\t\t\t<el-row>公司名称:<el-tag size=\"small\">{{ scope.row.client==null\r\n\t\t\t\t\t  ?'':scope.row.client.company }}</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t<el-row>联系人:{{scope.row.client==null\r\n\t\t\t\t\t  ?'': scope.row.client.linkman }}</el-row>\r\n                        用户内容\r\n\t\t\t\t\t\t<el-row>联系方式:<el-tag size=\"small\">{{scope.row.client==null\r\n\t\t\t\t\t  ?'': scope.row.client.phone }}</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"套餐内容\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-row>套餐名称:{{ scope.row.taocan ? scope.row.taocan.title:''}}</el-row>\r\n\t\t\t\t\t\t<el-row>套餐价格:{{ scope.row.taocan?scope.row.taocan.price:\"\" }}元</el-row>\r\n\t\t\t\t\t\t<el-row>套餐年份:<el-tag size=\"small\">{{ scope.row.taocan ?scope.row.taocan.year :''}}年</el-tag>\r\n\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"title\" label=\"支付情况\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-row>支付类型:{{\r\n                scope.row.pay_type == 1\r\n                  ? \"全款\"\r\n                  : \"分期\" + \"/\" + scope.row.qishu + \"期\"\r\n              }}</el-row>\r\n\t\t\t\t\t\t<el-row>已付款:{{ scope.row.pay_age }}元</el-row>\r\n\t\t\t\t\t\t<el-row>剩余尾款:{{\r\n                 scope.row.pay_type == 1?0:scope.row.total_price - scope.row.pay_age\r\n              }}元</el-row>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<!-- <el-table-column prop=\"title\" label=\"支付凭证\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div v-if=\"scope.row.pay_type == 1\">\r\n\t\t\t\t\t\t\t<div class=\"pictrueBox pictrue\">\r\n\t\t\t\t\t\t\t\t<img :src=\"scope.row.pay_path\" @click=\"showImage(scope.row.pay_path)\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div v-else>\r\n\t\t\t\t\t\t\t<div class=\"pictrueBox pictrue\" v-for=\"(item,index) in scope.row.fenqi\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<img :src=\"item.pay_path\" @click=\"showImage(item.pay_path)\" v-if=\"item.pay_path\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column> -->\r\n\t\t\t\t<el-table-column  label=\"审核状态\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<div  @click=\"showStatus(scope.row)\">\r\n\t\t\t\t\t\t\t<el-row v-if=\"scope.row.status==1\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#409EFF\">未审核</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-else-if=\"scope.row.status==3\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#F56C6C\">审核未通过</span>\r\n\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-else-if=\"scope.row.status==2\">\r\n\t\t\t\t\t\t\t\t<span style=\"    cursor: pointer;color:#67C23A\">已通过</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t\t<el-row v-if=\"scope.row.status==3\">\r\n\r\n\t\t\t\t\t\t\t\t<span>原因:{{scope.row.status_msg}}</span>\r\n\t\t\t\t\t\t\t</el-row>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"member.title\" label=\"业务员\"> </el-table-column>\r\n\t\t\t\t<el-table-column prop=\"create_time\" label=\"创建时间\"> </el-table-column>\r\n\t\t\t\t<el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n\t\t\t\t<el-table-column fixed=\"right\" label=\"操作\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\">查看</el-button>\r\n\r\n\t\t\t\t\t\t<el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">\r\n\t\t\t\t\t\t\t移除\r\n\t\t\t\t\t\t</el-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t\t<div class=\"page-top\">\r\n\t\t\t\t<el-pagination @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\r\n\t\t\t\t\t:page-sizes=\"[20, 100, 200, 300, 400]\" :page-size=\"size\"\r\n\t\t\t\t\tlayout=\"total, sizes, prev, pager, next, jumper\" :total=\"total\">\r\n\t\t\t\t</el-pagination>\r\n\t\t\t</div>\r\n\t\t</el-card>\r\n\t\t<el-dialog title=\"订单信息\" :visible.sync=\"dialogFormVisible\" :close-on-click-modal=\"false\">\r\n\t\t\t<div v-if=\"is_info\">\r\n\t\t\t\t<el-descriptions title=\"客户信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"公司名称\">{{info.client==null\r\n\t\t\t\t\t  ?'':info.client.company\r\n\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<el-descriptions-item label=\"联系人\"><el-tag @click=\"viewUserData(info.client.id)\" size=\"small\" v-if=\"info.client!=null\">{{info.client==null\r\n\t\t\t\t\t  ?'':\r\n\t\t\t\t\t  info.client.linkman\r\n\t\t\t\t\t}}</el-tag></el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"联系方式\"><el-tag @click=\"viewUserData(info.client.id)\" size=\"small\" v-if=\"info.client!=null\">{{info.client==null\r\n\t\t\t\t\t  ?'':\r\n\t\t\t\t\t  info.client.phone\r\n\t\t\t\t\t}}</el-tag></el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"营业执照\">\r\n\r\n\t\t\t\t\t\t<el-tag @click=\"showImage(info.client.pic_path)\" size=\"small\" v-if=\"info.client!=null\">查看\r\n\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t\t<el-tag size=\"small\" v-else>暂无</el-tag>\r\n\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\r\n                <el-descriptions title=\"债务人相关信息\" :colon=\"false\">\r\n                    <el-descriptions-item>\r\n                        <el-table\r\n                                :data=\"ruleForm.debts\"\r\n                                style=\"width: 100%; margin-top: 10px\"\r\n                                v-loading=\"loading\"\r\n                                size=\"mini\"\r\n                        >\r\n                            <el-table-column prop=\"name\" label=\"债务人姓名\"> </el-table-column>\r\n                            <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n                            <el-table-column prop=\"tiaojie_id\" label=\"调解员\"> </el-table-column>\r\n                            <el-table-column prop=\"fawu_id\" label=\"法务专员\"> </el-table-column>\r\n                            <el-table-column prop=\"lian_id\" label=\"立案专员\"> </el-table-column>\r\n                            <el-table-column prop=\"htsczy_id\" label=\"合同上传专用\"> </el-table-column>\r\n                            <el-table-column prop=\"ls_id\" label=\"律师\"></el-table-column>\r\n                            <el-table-column prop=\"ywy_id\" label=\"业务员\"> </el-table-column>\r\n                        </el-table></el-descriptions-item>\r\n                </el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"套餐内容\">\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐名称\">{{\r\n            info.taocan.title\r\n          }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐价格\">{{\r\n            info.taocan.price\r\n          }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"套餐年份\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{ info.taocan.year }}年</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"到期时间\">\r\n<!--\t\t\t\t\t\t<el-tag size=\"small\">{{ info.end_time }}</el-tag>-->\r\n\t\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\t\t\tv-model=\"info.end_time\"\r\n\t\t\t\t\t\t\t\ttype=\"datetime\"\r\n\t\t\t\t\t\t\t\tformat=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"选择日期\"\r\n\t\t\t\t\t\t\t\tsize=\"mini\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t</el-date-picker>\r\n\t\t\t\t\t\t<el-tag @click=\"updateEndTIme()\" size=\"small\" style=\"cursor:pointer;\">修改</el-tag>\r\n\r\n\t\t\t\t\t</el-descriptions-item>\r\n\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"套餐详情\">\r\n\t\t\t\t\t<el-descriptions-item :label=\"item.title\" v-for=\"(item, index) in info.taocan.num\" :key=\"index\">{{\r\n              item.is_num == 1 ? item.value + \"次\" : \"不限\"\r\n            }}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"款项信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"付款类型\">{{\r\n                info.pay_type == 1\r\n                  ? \"全款\"\r\n                  : \"分期\" + \"/\" + info.qishu + \"期\"\r\n              }}</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"已付款\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{info.pay_age}}元</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item label=\"剩余款\">\r\n\t\t\t\t\t\t<el-tag size=\"small\">{{info.total_price - info.pay_age}}元</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"期数\" v-if=\"info.pay_type==2\">\r\n\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions v-for=\"(item,index) in info.fenqi\" v-if=\"info.pay_type == 2\" :key=\"index\">\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'期'\">\r\n\t\t\t\t\t\t{{item.price}}\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'还款期'\">\r\n\t\t\t\t\t\t{{item.date}}\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t\t<el-descriptions-item :label=\"'第'+(index*1+1)+'期凭证'\">\r\n\t\t\t\t\t\t<el-tag @click=\"showImage(item.pay_path)\" size=\"small\" v-if=\"item.pay_path\">查看</el-tag>\r\n\r\n\t\t\t\t\t\t<el-tag type=\"warning\" size=\"small\" v-else @click=\"changePinzhen(index)\">\r\n\t\t\t\t\t\t\t<el-upload action=\"/admin/Upload/uploadImage\" accept=\".jpg, .jpeg, .png\"\r\n\t\t\t\t\t\t\t\t:show-file-list=\"false\" :on-success=\"handleSuccess\" :before-upload=\"beforeUpload\">\r\n\t\t\t\t\t\t\t\t上传凭证\r\n\t\t\t\t\t\t\t</el-upload>\r\n\t\t\t\t\t\t</el-tag>\r\n\t\t\t\t\t</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t\t<el-descriptions title=\"备注信息\">\r\n\t\t\t\t\t<el-descriptions-item label=\"具体内容\">{{\r\n\t\t\t\t\t  info.desc\r\n\t\t\t\t\t}}</el-descriptions-item>\r\n\t\t\t\t</el-descriptions>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog title=\"审核内容\" :visible.sync=\"dialogStatus\" :close-on-click-modal=\"false\">\r\n\t\t\t<el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n\t\t\t\t<el-form-item label=\"审核\" :label-width=\"formLabelWidth\">\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"1\" :disabled=\"ruleForm.status==2?true:false\">未审核\r\n\t\t\t\t\t\t</el-radio>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"2\">审核通过</el-radio>\r\n\t\t\t\t\t\t<el-radio v-model=\"ruleForm.status\" :label=\"3\">审核不通过\r\n\t\t\t\t\t\t</el-radio>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item label=\"不通过原因\" :label-width=\"formLabelWidth\" prop=\"status_msg\" v-if=\"ruleForm.status==3\">\r\n\t\t\t\t\t<el-input type=\"textarea\" :rows=\"3\" placeholder=\"请输入内容\" v-model=\"ruleForm.status_msg\">\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"dialogStatus = false\">取 消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"changeStatus()\">确 定</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog title=\"设置到期时间\" :visible.sync=\"dialogEndTime\" :close-on-click-modal=\"false\">\r\n\t\t\t<el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n\r\n\t\t\t\t<el-date-picker v-model=\"ruleForm.end_time\" type=\"date\" format=\"Y-m-d\" placeholder=\"选择日期\">\r\n\t\t\t\t</el-date-picker>\r\n\r\n\t\t\t</el-form>\r\n\t\t\t<div slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"dialogEndTime = false\">取 消</el-button>\r\n\t\t\t\t<el-button type=\"primary\" @click=\"changeEndTime()\">确 定</el-button>\r\n\t\t\t</div>\r\n\t\t</el-dialog>\r\n\r\n\t\t<el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"60%\">\r\n\t\t\t<el-image :src=\"show_image\"></el-image>\r\n\t\t</el-dialog>\r\n\t\t<el-dialog\r\n\t\t\t\t:title=\"用户详情\"\r\n\t\t\t\t:visible.sync=\"dialogViewUserDetail\"\r\n\t\t\t\t:close-on-click-modal=\"false\"\r\n\t\t>\r\n\t\t\t<user-details :id=\"currentId\"></user-details>\r\n\t\t</el-dialog>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\t// @ is an alias to /src\r\n\timport UserDetails from '/src/components/UserDetail.vue';\r\n\r\n\texport default {\r\n\t\tname: \"list\",\r\n\t\tcomponents: { UserDetails },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tallSize: \"mini\",\r\n\t\t\t\tlist: [],\r\n\t\t\t\ttotal: 1,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tsize: 20,\r\n\t\t\t\tcurrentId:0,\r\n\t\t\t\tsearch: {\r\n\t\t\t\t\tkeyword: \"\",\r\n\t\t\t\t},\r\n\t\t\t\tloading: true,\r\n\t\t\t\turl: \"/Dingdan/\",\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tis_info: false,\r\n\t\t\t\tdialogFormVisible: false,\r\n\t\t\t\tdialogVisible: false,\r\n\t\t\t\tdialogViewUserDetail: false,\r\n\t\t\t\tdialogStatus: false,\r\n\t\t\t\tdialogEndTime: false,\r\n\t\t\t\tdialogAddOrder: false,\r\n\t\t\t\truleForm: {\r\n\t\t\t\t\tstatus: 1,\r\n\t\t\t\t\tstatus_msg: '',\r\n\t\t\t\t\tid: '',\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\tstatus_msg: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"请填写不通过原因\",\r\n\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t}, ],\r\n\t\t\t\t\tend_time: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"请填写时间\",\r\n\t\t\t\t\t\ttrigger: \"blur\",\r\n\t\t\t\t\t}, ],\r\n\t\t\t\t},\r\n\t\t\t\tformLabelWidth: \"120px\",\r\n\t\t\t\tshow_image: '',\r\n\t\t\t\tindex: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.getData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\teditData(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (id != 0) {\r\n\t\t\t\t\tthis.getInfo(id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t\t_this.dialogFormVisible = true;\r\n\t\t\t},\r\n\t\t\tviewUserData(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tif (id != 0) {\r\n\t\t\t\t\tthis.currentId = id;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t_this.dialogViewUserDetail = true;\r\n\t\t\t},\r\n\t\t\tgetInfo(id) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n\t\t\t\t\tif (resp) {\r\n\t\t\t\t\t\t_this.info = resp.data;\r\n\t\t\t\t\t\t_this.is_info = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdelData(index, id) {\r\n\t\t\t\tthis.$confirm(\"是否删除该信息?\", \"提示\", {\r\n\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tthis.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: \"删除成功!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\tmessage: \"取消删除!\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tupdateEndTIme() {\r\n\t\t\t\tthis.$confirm(\"确认修改到期时间?\", \"提示\", {\r\n\t\t\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t\t\t\t\tcancelButtonText: \"取消\",\r\n\t\t\t\t\t\ttype: \"warning\",\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\tvar data = {'id':this.info.id,'end_time':this.info.end_time}\r\n\t\t\t\t\t\tthis.postRequest(this.url + \"updateEndTIme\", data)\r\n\t\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: \"修改成功!\",\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\tmessage: \"取消修改!\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\trefulsh() {\r\n\t\t\t\tthis.$router.go(0);\r\n\t\t\t},\r\n\t\t\tgetData() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.loading = true;\r\n\t\t\t\t_this\r\n\t\t\t\t\t.postRequest(\r\n\t\t\t\t\t\t_this.url + \"index1?page=\" + _this.page + \"&size=\" + _this.size,\r\n\t\t\t\t\t\t_this.search\r\n\t\t\t\t\t)\r\n\t\t\t\t\t.then((resp) => {\r\n\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t_this.list = resp.data;\r\n\t\t\t\t\t\t\t_this.total = resp.count;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaveData() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\tthis.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogFormVisible = false;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleSizeChange(val) {\r\n\t\t\t\tthis.size = val;\r\n\r\n\t\t\t\tthis.getData();\r\n\t\t\t},\r\n\t\t\thandleCurrentChange(val) {\r\n\t\t\t\tthis.page = val;\r\n\t\t\t\tthis.getData();\r\n\t\t\t},\r\n\t\t\thandleSuccess(res) {\r\n\t\t\t\tlet _this = this\r\n\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t_this.info.fenqi[_this.index].pay_path = res.data.url;\r\n\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t'id': _this.info.id,\r\n\t\t\t\t\t\t'fenqi': _this.info.fenqi\r\n\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\tmessage: '上传成功',\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\tmessage: '上传失败',\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\tbeforeUpload(file) {\r\n\t\t\t\tconst isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n\t\t\t\tif (!isTypeTrue) {\r\n\t\t\t\t\tthis.$message.error(\"上传图片格式不对!\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdelImage(file, fileName) {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\t_this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t_this.ruleForm[fileName] = \"\";\r\n\r\n\t\t\t\t\t\t_this.$message.success(\"删除成功!\");\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t_this.$message.error(resp.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshowImage(file) {\r\n\t\t\t\tthis.show_image = file;\r\n\t\t\t\tthis.dialogVisible = true;\r\n\t\t\t},\r\n\t\t\tchangePinzhen(index) {\r\n\t\t\t\tthis.index = index\r\n\t\t\t},\r\n\t\t\tshowStatus(row) {\r\n\t\t\t\tthis.dialogStatus = true\r\n\t\t\t\tthis.ruleForm = row\r\n\t\t\t},\r\n\t\t\tshowEndTime(row) {\r\n\t\t\t\tthis.dialogEndTime = true\r\n\t\t\t\tthis.ruleForm = row\r\n\t\t\t},\r\n\t\t\tchangeEndTime() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t_this.postRequest(_this.url + \"save\", {\r\n\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t'end_time': _this.ruleForm.end_time,\r\n\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchangeStatus() {\r\n\r\n\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\tthis.$refs[\"ruleForm\"].validate((valid) => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t_this.postRequest(_this.url + \"changeStatus\", {\r\n\t\t\t\t\t\t\t'id': _this.ruleForm.id,\r\n\t\t\t\t\t\t\t'status': _this.ruleForm.status,\r\n\t\t\t\t\t\t\t'status_msg': _this.ruleForm.status_msg\r\n\t\t\t\t\t\t}).then((resp) => {\r\n\t\t\t\t\t\t\tif (resp.code == 200) {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tmessage: '审核成功',\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t_this.dialogStatus = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t_this.$message({\r\n\t\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t\t\tmessage: resp.msg,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n<style scoped>\r\n\t.page-top {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t.el_input {\r\n\t\twidth: 475px;\r\n\t}\r\n\r\n\t.pictrueBox {\r\n\t\tdisplay: inline-block !important;\r\n\t}\r\n\r\n\t.pictrue {\r\n\t\twidth: 60px;\r\n\t\theight: 60px;\r\n\t\tborder: 1px dotted rgba(0, 0, 0, 0.1);\r\n\t\tmargin-right: 15px;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.pictrue img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dingdan.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./dingdan.vue?vue&type=template&id=d5cc8fca&scoped=true\"\nimport script from \"./dingdan.vue?vue&type=script&lang=js\"\nexport * from \"./dingdan.vue?vue&type=script&lang=js\"\nimport style0 from \"./dingdan.vue?vue&type=style&index=0&id=d5cc8fca&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d5cc8fca\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('el-row',[_c('el-descriptions',{attrs:{\"title\":\"客户信息\"}},[_c('el-descriptions-item',{attrs:{\"label\":\"公司名称\"}},[_vm._v(_vm._s(_vm.info.company))]),_c('el-descriptions-item',{attrs:{\"label\":\"手机号\"}},[_vm._v(_vm._s(_vm.info.phone))]),_c('el-descriptions-item',{attrs:{\"label\":\"名称\"}},[_vm._v(_vm._s(_vm.info.nickname))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系人\"}},[_vm._v(_vm._s(_vm.info.linkman))]),_c('el-descriptions-item',{attrs:{\"label\":\"头像\"}},[(_vm.info.headimg !='' && _vm.info.headimg!=null)?_c('img',{staticStyle:{\"width\":\"50px\",\"height\":\"50px\"},attrs:{\"src\":_vm.info.headimg},on:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_vm._e()]),_c('el-descriptions-item',{attrs:{\"label\":\"用户来源\"}},[_vm._v(_vm._s(_vm.info.yuangong_id))]),_c('el-descriptions-item',{attrs:{\"label\":\"联系方式\"}},[_vm._v(_vm._s(_vm.info.linkphone))]),_c('el-descriptions-item',{attrs:{\"label\":\"调解员\"}},[_vm._v(_vm._s(_vm.info.tiaojie_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"法务专员\"}},[_vm._v(_vm._s(_vm.info.fawu_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"立案专员\"}},[_vm._v(_vm._s(_vm.info.lian_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"合同上传专用\"}},[_vm._v(_vm._s(_vm.info.htsczy_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"律师\"}},[_vm._v(_vm._s(_vm.info.ls_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"业务员\"}},[_vm._v(_vm._s(_vm.info.ywy_id)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"营业执照\"}},[(_vm.info.license !='' && _vm.info.license!=null)?_c('img',{staticStyle:{\"width\":\"50px\",\"height\":\"50px\"},attrs:{\"src\":_vm.info.license},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}}):_vm._e()]),_c('el-descriptions-item',{attrs:{\"label\":\"开始时间\"}},[_vm._v(_vm._s(_vm.info.start_time))]),_c('el-descriptions-item',{attrs:{\"label\":\"会员年限\"}},[_vm._v(_vm._s(_vm.info.year)+\"年\")])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <el-row>\r\n    <el-descriptions title=\"客户信息\">\r\n      <el-descriptions-item label=\"公司名称\">{{\r\n        info.company\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"手机号\">{{\r\n        info.phone\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"名称\">{{\r\n        info.nickname\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系人\">{{\r\n        info.linkman\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"头像\">\r\n        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n             :src=\"info.headimg\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.headimg)\"\r\n        /></el-descriptions-item>\r\n      <el-descriptions-item label=\"用户来源\">{{\r\n        info.yuangong_id\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"联系方式\">{{\r\n        info.linkphone\r\n        }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"调解员\">{{\r\n            info.tiaojie_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"法务专员\">{{\r\n            info.fawu_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"立案专员\">{{\r\n            info.lian_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"合同上传专用\">{{\r\n            info.htsczy_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"律师\">{{\r\n            info.ls_id\r\n            }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"业务员\">{{\r\n            info.ywy_id\r\n            }}\r\n        </el-descriptions-item>\r\n      <el-descriptions-item label=\"营业执照\">\r\n        <img v-if=\"info.license !='' && info.license!=null\"\r\n             :src=\"info.license\"\r\n             style=\"width: 50px; height: 50px;\"\r\n             @click=\"showImage(info.license)\"\r\n        />\r\n      </el-descriptions-item>\r\n      <el-descriptions-item label=\"开始时间\">{{\r\n        info.start_time\r\n        }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"会员年限\">{{\r\n        info.year\r\n        }}年</el-descriptions-item>\r\n    </el-descriptions>\r\n  </el-row>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: String,\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: [] // 用于存储接口返回的数据\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          if (resp) {\r\n            _this.info = resp.data;\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=9e80c8c2\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}