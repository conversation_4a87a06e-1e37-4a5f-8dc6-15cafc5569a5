{"map": "{\"version\":3,\"sources\":[\"js/chunk-7963da7c.c3eacd3c.js\"],\"names\":[\"window\",\"push\",\"13d5\",\"module\",\"exports\",\"__webpack_require__\",\"$\",\"$reduce\",\"left\",\"arrayMethodIsStrict\",\"CHROME_VERSION\",\"IS_NODE\",\"CHROME_BUG\",\"FORCED\",\"target\",\"proto\",\"forced\",\"reduce\",\"callbackfn\",\"length\",\"arguments\",\"this\",\"undefined\",\"51a4\",\"605d\",\"global\",\"classof\",\"process\",\"a640\",\"fails\",\"METHOD_NAME\",\"argument\",\"method\",\"call\",\"d564\",\"__webpack_exports__\",\"d58f\",\"aCallable\",\"toObject\",\"IndexedObject\",\"lengthOfArrayLike\",\"$TypeError\",\"TypeError\",\"REDUCE_EMPTY\",\"createMethod\",\"IS_RIGHT\",\"that\",\"argumentsLength\",\"memo\",\"O\",\"self\",\"index\",\"i\",\"right\",\"d625\",\"r\",\"render\",\"_vm\",\"_c\",\"_self\",\"staticClass\",\"_m\",\"attrs\",\"type\",\"icon\",\"on\",\"click\",\"getData\",\"_v\",\"gutter\",\"xs\",\"sm\",\"md\",\"lg\",\"xl\",\"_s\",\"total\",\"averagePrice\",\"premiumPackages\",\"averageYear\",\"shadow\",\"slot\",\"$event\",\"editData\",\"model\",\"search\",\"inline\",\"label\",\"staticStyle\",\"width\",\"placeholder\",\"clearable\",\"value\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"min\",\"minPrice\",\"margin\",\"maxPrice\",\"resetSearch\",\"size\",\"viewMode\",\"directives\",\"name\",\"rawName\",\"loading\",\"_l\",\"filteredPackages\",\"pkg\",\"key\",\"id\",\"title\",\"price\",\"year\",\"sort\",\"desc\",\"services\",\"slice\",\"service\",\"_e\",\"formatDate\",\"create_time\",\"delData\",\"data\",\"min-width\",\"scopedSlots\",\"_u\",\"fn\",\"scope\",\"row\",\"prop\",\"fixed\",\"$index\",\"page-sizes\",\"page-size\",\"layout\",\"background\",\"size-change\",\"handleSizeChange\",\"current-change\",\"handleCurrentChange\",\"dialogTitle\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"ref\",\"ruleForm\",\"rules\",\"label-width\",\"span\",\"autocomplete\",\"max\",\"precision\",\"types\",\"item\",\"checked\",\"is_num\",\"rows\",\"saveLoading\",\"saveData\",\"staticRenderFns\",\"taocanvue_type_script_lang_js\",\"components\",\"[object Object]\",\"allSize\",\"tableData\",\"page\",\"good\",\"num\",\"required\",\"message\",\"trigger\",\"formLabelWidth\",\"url\",\"computed\",\"Array\",\"isArray\",\"sum\",\"parseFloat\",\"Math\",\"round\",\"toLocaleString\",\"filter\",\"parseInt\",\"filtered\",\"toLowerCase\",\"includes\",\"methods\",\"_this\",\"getInfo\",\"getTypes\",\"getRequest\",\"then\",\"resp\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"deleteRequest\",\"code\",\"$message\",\"splice\",\"catch\",\"postRequest\",\"count\",\"val\",\"dateStr\",\"Date\",\"toLocaleDateString\",\"$refs\",\"validate\",\"valid\",\"map\",\"setTimeout\",\"success\",\"taocan_taocanvue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAASC,GAEjC,aAEA,IAAIC,EAAID,EAAoB,QACxBE,EAAUF,EAAoB,QAAQG,KACtCC,EAAsBJ,EAAoB,QAC1CK,EAAiBL,EAAoB,QACrCM,EAAUN,EAAoB,QAI9BO,GAAcD,GAAWD,EAAiB,IAAMA,EAAiB,GACjEG,EAASD,IAAeH,EAAoB,UAIhDH,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,OAAQH,GAAU,CAClDI,OAAQ,SAAgBC,GACtB,IAAIC,EAASC,UAAUD,OACvB,OAAOZ,EAAQc,KAAMH,EAAYC,EAAQA,EAAS,EAAIC,UAAU,QAAKE,OAOnEC,OACA,SAAUpB,EAAQC,EAASC,KAM3BmB,OACA,SAAUrB,EAAQC,EAASC,GAEjC,aAEA,IAAIoB,EAASpB,EAAoB,QAC7BqB,EAAUrB,EAAoB,QAElCF,EAAOC,QAAsC,YAA5BsB,EAAQD,EAAOE,UAK1BC,KACA,SAAUzB,EAAQC,EAASC,GAEjC,aAEA,IAAIwB,EAAQxB,EAAoB,QAEhCF,EAAOC,QAAU,SAAU0B,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,OAAO,GAAM,QAOvDG,KACA,SAAU/B,EAAQgC,EAAqB9B,GAE7C,aAC+cA,EAAoB,SAO7d+B,KACA,SAAUjC,EAAQC,EAASC,GAEjC,aAEA,IAAIgC,EAAYhC,EAAoB,QAChCiC,EAAWjC,EAAoB,QAC/BkC,EAAgBlC,EAAoB,QACpCmC,EAAoBnC,EAAoB,QAExCoC,EAAaC,UAEbC,EAAe,8CAGfC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAM5B,EAAY6B,EAAiBC,GAClD,IAAIC,EAAIX,EAASQ,GACbI,EAAOX,EAAcU,GACrB9B,EAASqB,EAAkBS,GAE/B,GADAZ,EAAUnB,GACK,IAAXC,GAAgB4B,EAAkB,EAAG,MAAM,IAAIN,EAAWE,GAC9D,IAAIQ,EAAQN,EAAW1B,EAAS,EAAI,EAChCiC,EAAIP,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASC,EACT,MAGF,GADAD,GAASC,EACLP,EAAWM,EAAQ,EAAIhC,GAAUgC,EACnC,MAAM,IAAIV,EAAWE,GAGzB,KAAME,EAAWM,GAAS,EAAIhC,EAASgC,EAAOA,GAASC,EAAOD,KAASD,IACrEF,EAAO9B,EAAW8B,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIX7C,EAAOC,QAAU,CAGfI,KAAMoC,GAAa,GAGnBS,MAAOT,GAAa,KAMhBU,KACA,SAAUnD,EAAQgC,EAAqB9B,GAE7C,aAEAA,EAAoBkD,EAAEpB,GAGtB,IAAIqB,EAAS,WACX,IAAIC,EAAMpC,KACRqC,EAAKD,EAAIE,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,gCACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACH,EAAII,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,cACbE,MAAO,CACLC,KAAQ,OACRC,KAAQ,mBAEVC,GAAI,CACFC,MAAST,EAAIU,UAEd,CAACV,EAAIW,GAAG,aAAc,KAAMV,EAAG,MAAO,CACvCE,YAAa,iBACZ,CAACF,EAAG,SAAU,CACfI,MAAO,CACLO,OAAU,KAEX,CAACX,EAAG,SAAU,CACfI,MAAO,CACLQ,GAAM,GACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,IAEP,CAAChB,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,kBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGlB,EAAImB,UAAWlB,EAAG,MAAO,CACzCE,YAAa,cACZ,CAACH,EAAIW,GAAG,UAAWV,EAAG,MAAO,CAC9BE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXH,EAAIW,GAAG,kBAAmBV,EAAG,SAAU,CACzCI,MAAO,CACLQ,GAAM,GACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,IAEP,CAAChB,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,oBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACH,EAAIW,GAAG,IAAMX,EAAIkB,GAAGlB,EAAIoB,iBAAkBnB,EAAG,MAAO,CACtDE,YAAa,cACZ,CAACH,EAAIW,GAAG,UAAWV,EAAG,MAAO,CAC9BE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXH,EAAIW,GAAG,iBAAkBV,EAAG,SAAU,CACxCI,MAAO,CACLQ,GAAM,GACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,IAEP,CAAChB,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,0BACZ,CAACF,EAAG,IAAK,CACVE,YAAa,sBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGlB,EAAIqB,oBAAqBpB,EAAG,MAAO,CACnDE,YAAa,cACZ,CAACH,EAAIW,GAAG,UAAWV,EAAG,MAAO,CAC9BE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,qBACXH,EAAIW,GAAG,iBAAkBV,EAAG,SAAU,CACxCI,MAAO,CACLQ,GAAM,GACNC,GAAM,EACNC,GAAM,EACNC,GAAM,EACNC,GAAM,IAEP,CAAChB,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,2BACZ,CAACF,EAAG,IAAK,CACVE,YAAa,mBACTF,EAAG,MAAO,CACdE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGlB,EAAIsB,aAAe,OAAQrB,EAAG,MAAO,CACrDE,YAAa,cACZ,CAACH,EAAIW,GAAG,UAAWV,EAAG,MAAO,CAC9BE,YAAa,wBACZ,CAACF,EAAG,IAAK,CACVE,YAAa,kBACXH,EAAIW,GAAG,iBAAkB,IAAK,GAAIV,EAAG,UAAW,CAClDE,YAAa,cACbE,MAAO,CACLkB,OAAU,UAEX,CAACtB,EAAG,MAAO,CACZE,YAAa,cACbE,MAAO,CACLmB,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,mBACXH,EAAIW,GAAG,YAAaV,EAAG,MAAO,CAChCE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRC,KAAQ,gBAEVC,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAI0B,SAAS,MAGvB,CAAC1B,EAAIW,GAAG,aAAc,KAAMV,EAAG,MAAO,CACvCE,YAAa,kBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,cACbE,MAAO,CACLsB,MAAS3B,EAAI4B,OACbC,QAAU,IAEX,CAAC5B,EAAG,eAAgB,CACrBI,MAAO,CACLyB,MAAS,QAEV,CAAC7B,EAAG,WAAY,CACjB8B,YAAa,CACXC,MAAS,SAEX3B,MAAO,CACL4B,YAAe,aACfC,UAAa,IAEfP,MAAO,CACLQ,MAAOnC,EAAI4B,OAAOQ,QAClBC,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI4B,OAAQ,UAAWU,IAElCE,WAAY,mBAEb,CAACvC,EAAG,YAAa,CAClBI,MAAO,CACLmB,KAAQ,SACRjB,KAAQ,kBAEVC,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAIU,YAGfc,KAAM,YACH,IAAK,GAAIvB,EAAG,eAAgB,CAC/BI,MAAO,CACLyB,MAAS,SAEV,CAAC7B,EAAG,kBAAmB,CACxB8B,YAAa,CACXC,MAAS,SAEX3B,MAAO,CACL4B,YAAe,OACfQ,IAAO,GAETd,MAAO,CACLQ,MAAOnC,EAAI4B,OAAOc,SAClBL,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI4B,OAAQ,WAAYU,IAEnCE,WAAY,qBAEZvC,EAAG,OAAQ,CACb8B,YAAa,CACXY,OAAU,UAEX,CAAC3C,EAAIW,GAAG,OAAQV,EAAG,kBAAmB,CACvC8B,YAAa,CACXC,MAAS,SAEX3B,MAAO,CACL4B,YAAe,OACfQ,IAAO,GAETd,MAAO,CACLQ,MAAOnC,EAAI4B,OAAOgB,SAClBP,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI4B,OAAQ,WAAYU,IAEnCE,WAAY,sBAEX,GAAIvC,EAAG,eAAgB,CAACA,EAAG,YAAa,CAC3CI,MAAO,CACLE,KAAQ,mBAEVC,GAAI,CACFC,MAAST,EAAI6C,cAEd,CAAC7C,EAAIW,GAAG,WAAY,IAAK,IAAK,KAAMV,EAAG,UAAW,CACnDE,YAAa,eACbE,MAAO,CACLkB,OAAU,UAEX,CAACtB,EAAG,MAAO,CACZE,YAAa,cACbE,MAAO,CACLmB,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,oBACXH,EAAIW,GAAG,YAAaV,EAAG,MAAO,CAChCE,YAAa,iBACZ,CAACF,EAAG,iBAAkB,CACvBI,MAAO,CACLyC,KAAQ,SAEVnB,MAAO,CACLQ,MAAOnC,EAAI+C,SACXV,SAAU,SAAUC,GAClBtC,EAAI+C,SAAWT,GAEjBE,WAAY,aAEb,CAACvC,EAAG,kBAAmB,CACxBI,MAAO,CACLyB,MAAS,SAEV,CAAC9B,EAAIW,GAAG,UAAWV,EAAG,kBAAmB,CAC1CI,MAAO,CACLyB,MAAS,UAEV,CAAC9B,EAAIW,GAAG,WAAY,IAAK,KAAuB,SAAjBX,EAAI+C,SAAsB9C,EAAG,MAAO,CACpE+C,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTf,MAAOnC,EAAImD,QACXX,WAAY,YAEdrC,YAAa,gBACZH,EAAIoD,GAAGpD,EAAIqD,kBAAkB,SAAUC,GACxC,OAAOrD,EAAG,MAAO,CACfsD,IAAKD,EAAIE,GACTrD,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGoC,EAAIG,UAAWxD,EAAG,MAAO,CACzCE,YAAa,iBACZ,CAACH,EAAIW,GAAG,IAAMX,EAAIkB,GAAGoC,EAAII,YAAazD,EAAG,MAAO,CACjDE,YAAa,mBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CAACD,EAAIW,GAAGX,EAAIkB,GAAGoC,EAAIK,MAAQ,WAAY1D,EAAG,MAAO,CAC9DE,YAAa,aACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXF,EAAG,OAAQ,CAACD,EAAIW,GAAG,OAASX,EAAIkB,GAAGoC,EAAIM,aAAc3D,EAAG,MAAO,CACjEE,YAAa,gBACZ,CAACH,EAAIW,GAAG,IAAMX,EAAIkB,GAAGoC,EAAIO,MAAQ,QAAU,OAAQP,EAAIQ,UAAYR,EAAIQ,SAASpG,OAAS,EAAIuC,EAAG,MAAO,CACxGE,YAAa,oBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACH,EAAIW,GAAG,WAAYV,EAAG,MAAO,CAC/BE,YAAa,gBACZ,CAACH,EAAIoD,GAAGE,EAAIQ,SAASC,MAAM,EAAG,IAAI,SAAUC,GAC7C,OAAO/D,EAAG,SAAU,CAClBsD,IAAKS,EAAQR,GACbrD,YAAa,cACbE,MAAO,CACLyC,KAAQ,SAET,CAAC9C,EAAIW,GAAG,IAAMX,EAAIkB,GAAG8C,EAAQf,MAAQ,UACtCK,EAAIQ,SAASpG,OAAS,EAAIuC,EAAG,OAAQ,CACvCE,YAAa,iBACZ,CAACH,EAAIW,GAAG,KAAOX,EAAIkB,GAAGoC,EAAIQ,SAASpG,OAAS,GAAK,OAASsC,EAAIiE,MAAO,KAAOjE,EAAIiE,OAAQhE,EAAG,MAAO,CACnGE,YAAa,kBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,OAAQ,CACbE,YAAa,eACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGlB,EAAIkE,WAAWZ,EAAIa,mBAAoBlE,EAAG,MAAO,CACjEE,YAAa,mBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,WACbE,MAAO,CACLC,KAAQ,QAEVE,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAI0B,SAAS4B,EAAIE,OAG3B,CAACxD,EAAIW,GAAG,UAAWV,EAAG,YAAa,CACpCE,YAAa,aACbE,MAAO,CACLC,KAAQ,QAEVE,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAIoE,SAAS,EAAGd,EAAIE,OAG9B,CAACxD,EAAIW,GAAG,WAAY,UACrB,GAAKX,EAAIiE,KAAuB,UAAjBjE,EAAI+C,SAAuB9C,EAAG,MAAO,CAACA,EAAG,WAAY,CACtE+C,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTf,MAAOnC,EAAImD,QACXX,WAAY,YAEdrC,YAAa,eACbE,MAAO,CACLgE,KAAQrE,EAAIqD,mBAEb,CAACpD,EAAG,kBAAmB,CACxBI,MAAO,CACLyB,MAAS,OACTwC,YAAa,OAEfC,YAAavE,EAAIwE,GAAG,CAAC,CACnBjB,IAAK,UACLkB,GAAI,SAAUC,GACZ,MAAO,CAACzE,EAAG,MAAO,CAChBE,YAAa,sBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,uBACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGwD,EAAMC,IAAIlB,UAAWxD,EAAG,MAAO,CAC/CE,YAAa,sBACZ,CAACH,EAAIW,GAAGX,EAAIkB,GAAGwD,EAAMC,IAAId,MAAQ,iBAEpC,MAAM,EAAO,cACf5D,EAAG,kBAAmB,CACxBI,MAAO,CACLyB,MAAS,KACTE,MAAS,OAEXuC,YAAavE,EAAIwE,GAAG,CAAC,CACnBjB,IAAK,UACLkB,GAAI,SAAUC,GACZ,MAAO,CAACzE,EAAG,MAAO,CAChBE,YAAa,iBACZ,CAACH,EAAIW,GAAG,IAAMX,EAAIkB,GAAGwD,EAAMC,IAAIjB,cAElC,MAAM,EAAO,cACfzD,EAAG,kBAAmB,CACxBI,MAAO,CACLuE,KAAQ,OACR9C,MAAS,KACTE,MAAS,OAEXuC,YAAavE,EAAIwE,GAAG,CAAC,CACnBjB,IAAK,UACLkB,GAAI,SAAUC,GACZ,MAAO,CAACzE,EAAG,SAAU,CACnBI,MAAO,CACLC,KAAQ,OACRwC,KAAQ,UAET,CAAC9C,EAAIW,GAAGX,EAAIkB,GAAGwD,EAAMC,IAAIhB,MAAQ,WAEpC,MAAM,EAAO,cACf1D,EAAG,kBAAmB,CACxBI,MAAO,CACLuE,KAAQ,OACR9C,MAAS,KACTE,MAAS,QAET/B,EAAG,kBAAmB,CACxBI,MAAO,CACLyB,MAAS,OACTE,MAAS,OAEXuC,YAAavE,EAAIwE,GAAG,CAAC,CACnBjB,IAAK,UACLkB,GAAI,SAAUC,GACZ,MAAO,CAACzE,EAAG,MAAO,CAChBE,YAAa,aACZ,CAACH,EAAIW,GAAG,IAAMX,EAAIkB,GAAGlB,EAAIkE,WAAWQ,EAAMC,IAAIR,cAAgB,WAEjE,MAAM,EAAO,cACflE,EAAG,kBAAmB,CACxBI,MAAO,CACLwE,MAAS,QACT/C,MAAS,KACTE,MAAS,OAEXuC,YAAavE,EAAIwE,GAAG,CAAC,CACnBjB,IAAK,UACLkB,GAAI,SAAUC,GACZ,MAAO,CAACzE,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,WACbE,MAAO,CACLC,KAAQ,OACRwC,KAAQ,SAEVtC,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAI0B,SAASgD,EAAMC,IAAInB,OAGjC,CAACvD,EAAG,IAAK,CACVE,YAAa,iBACXH,EAAIW,GAAG,UAAWV,EAAG,YAAa,CACpCE,YAAa,aACbE,MAAO,CACLC,KAAQ,OACRwC,KAAQ,SAEVtC,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAIoE,QAAQM,EAAMI,OAAQJ,EAAMC,IAAInB,OAG9C,CAACvD,EAAG,IAAK,CACVE,YAAa,mBACXH,EAAIW,GAAG,WAAY,OAEvB,MAAM,EAAO,eACd,IAAK,GAAKX,EAAIiE,KAAMhE,EAAG,MAAO,CACjCE,YAAa,sBACZ,CAACF,EAAG,gBAAiB,CACtBI,MAAO,CACL0E,aAAc,CAAC,GAAI,GAAI,GAAI,KAC3BC,YAAahF,EAAI8C,KACjBmC,OAAU,0CACV9D,MAASnB,EAAImB,MACb+D,WAAc,IAEhB1E,GAAI,CACF2E,cAAenF,EAAIoF,iBACnBC,iBAAkBrF,EAAIsF,wBAErB,KAAMrF,EAAG,YAAa,CACzBE,YAAa,cACbE,MAAO,CACLoD,MAASzD,EAAIuF,YACbC,QAAWxF,EAAIyF,kBACfC,wBAAwB,EACxB1D,MAAS,OAEXxB,GAAI,CACFmF,iBAAkB,SAAUlE,GAC1BzB,EAAIyF,kBAAoBhE,KAG3B,CAACxB,EAAG,UAAW,CAChB2F,IAAK,WACLvF,MAAO,CACLsB,MAAS3B,EAAI6F,SACbC,MAAS9F,EAAI8F,MACbC,cAAe,UAEhB,CAAC9F,EAAG,SAAU,CACfI,MAAO,CACLO,OAAU,KAEX,CAACX,EAAG,SAAU,CACfI,MAAO,CACL2F,KAAQ,KAET,CAAC/F,EAAG,eAAgB,CACrBI,MAAO,CACLyB,MAAS,OACT8C,KAAQ,UAET,CAAC3E,EAAG,WAAY,CACjBI,MAAO,CACL4B,YAAe,UACfgE,aAAgB,OAElBtE,MAAO,CACLQ,MAAOnC,EAAI6F,SAASpC,MACpBpB,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI6F,SAAU,QAASvD,IAElCE,WAAY,qBAEX,IAAK,GAAIvC,EAAG,SAAU,CACzBI,MAAO,CACL2F,KAAQ,KAET,CAAC/F,EAAG,eAAgB,CACrBI,MAAO,CACLyB,MAAS,OACT8C,KAAQ,UAET,CAAC3E,EAAG,kBAAmB,CACxB8B,YAAa,CACXC,MAAS,QAEX3B,MAAO,CACLoC,IAAO,EACPyD,IAAO,OACPC,UAAa,EACblE,YAAe,SAEjBN,MAAO,CACLQ,MAAOnC,EAAI6F,SAASnC,MACpBrB,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI6F,SAAU,QAASvD,IAElCE,WAAY,qBAEX,IAAK,IAAK,GAAIvC,EAAG,SAAU,CAC9BI,MAAO,CACLO,OAAU,KAEX,CAACX,EAAG,SAAU,CACfI,MAAO,CACL2F,KAAQ,KAET,CAAC/F,EAAG,eAAgB,CACrBI,MAAO,CACLyB,MAAS,OACT8C,KAAQ,SAET,CAAC3E,EAAG,kBAAmB,CACxB8B,YAAa,CACXC,MAAS,QAEX3B,MAAO,CACLoC,IAAO,EACPyD,IAAO,GACPjE,YAAe,SAEjBN,MAAO,CACLQ,MAAOnC,EAAI6F,SAASlC,KACpBtB,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI6F,SAAU,OAAQvD,IAEjCE,WAAY,oBAEX,IAAK,GAAIvC,EAAG,SAAU,CACzBI,MAAO,CACL2F,KAAQ,KAET,CAAC/F,EAAG,eAAgB,CACrBI,MAAO,CACLyB,MAAS,OAEV,CAAC7B,EAAG,kBAAmB,CACxB8B,YAAa,CACXC,MAAS,QAEX3B,MAAO,CACLoC,IAAO,EACPyD,IAAO,IACPjE,YAAe,aAEjBN,MAAO,CACLQ,MAAOnC,EAAI6F,SAASjC,KACpBvB,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI6F,SAAU,OAAQvD,IAEjCE,WAAY,oBAEX,IAAK,IAAK,GAAIvC,EAAG,eAAgB,CACpCI,MAAO,CACLyB,MAAS,OACT8C,KAAQ,SAET,CAAC3E,EAAG,MAAO,CACZE,YAAa,qBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,iBACZ,CAACH,EAAIW,GAAG,gBAAiBV,EAAG,MAAO,CACpCE,YAAa,gBACZH,EAAIoD,GAAGpD,EAAIoG,OAAO,SAAUC,EAAM3G,GACnC,OAAOO,EAAG,MAAO,CACfsD,IAAK7D,EACLS,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,oBACZ,CAACF,EAAG,cAAe,CACpBI,MAAO,CACLyB,MAASuE,EAAK7C,IAEhB7B,MAAO,CACLQ,MAAOkE,EAAKC,QACZjE,SAAU,SAAUC,GAClBtC,EAAIuC,KAAK8D,EAAM,UAAW/D,IAE5BE,WAAY,iBAEb,CAACxC,EAAIW,GAAG,IAAMX,EAAIkB,GAAGmF,EAAK5C,OAAS,QAAS,GAAmB,GAAf4C,EAAKE,QAAeF,EAAKC,QAAUrG,EAAG,MAAO,CAC9FE,YAAa,iBACZ,CAACF,EAAG,kBAAmB,CACxBI,MAAO,CACLoC,IAAO,EACPyD,IAAO,IACPpD,KAAQ,QACRb,YAAe,MAEjBN,MAAO,CACLQ,MAAOkE,EAAKlE,MACZE,SAAU,SAAUC,GAClBtC,EAAIuC,KAAK8D,EAAM,QAAS/D,IAE1BE,WAAY,gBAEZvC,EAAG,OAAQ,CACbE,YAAa,gBACZ,CAACH,EAAIW,GAAG,QAAS,GAAK0F,EAAKC,QAAUrG,EAAG,MAAO,CAChDE,YAAa,qBACZ,CAACF,EAAG,SAAU,CACfI,MAAO,CACLyC,KAAQ,QACRxC,KAAQ,YAET,CAACN,EAAIW,GAAG,WAAY,GAAKX,EAAIiE,UAC9B,OAAQhE,EAAG,eAAgB,CAC7BI,MAAO,CACLyB,MAAS,SAEV,CAAC7B,EAAG,WAAY,CACjBI,MAAO,CACLC,KAAQ,WACRkG,KAAQ,EACRvE,YAAe,eACfgE,aAAgB,OAElBtE,MAAO,CACLQ,MAAOnC,EAAI6F,SAAShC,KACpBxB,SAAU,SAAUC,GAClBtC,EAAIuC,KAAKvC,EAAI6F,SAAU,OAAQvD,IAEjCE,WAAY,oBAEX,IAAK,GAAIvC,EAAG,MAAO,CACtBE,YAAa,gBACbE,MAAO,CACLmB,KAAQ,UAEVA,KAAM,UACL,CAACvB,EAAG,YAAa,CAClBO,GAAI,CACFC,MAAS,SAAUgB,GACjBzB,EAAIyF,mBAAoB,KAG3B,CAACzF,EAAIW,GAAG,QAASV,EAAG,YAAa,CAClCI,MAAO,CACLC,KAAQ,UACR6C,QAAWnD,EAAIyG,aAEjBjG,GAAI,CACFC,MAAS,SAAUgB,GACjB,OAAOzB,EAAI0G,cAGd,CAAC1G,EAAIW,GAAG,WAAY,IAAK,IAAK,IAE/BgG,EAAkB,CAAC,WACrB,IAAI3G,EAAMpC,KACRqC,EAAKD,EAAIE,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,eACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,gBACXH,EAAIW,GAAG,cAAeV,EAAG,MAAO,CAClCE,YAAa,iBACZ,CAACH,EAAIW,GAAG,yBAYoBiG,GANXhK,EAAoB,QAMuB,CAC/DqG,KAAM,oBACN4D,WAAY,GACZC,OACE,MAAO,CACLC,QAAS,OACTC,UAAW,GACX7D,SAAS,EACThC,MAAO,EACP8F,KAAM,EACNnE,KAAM,GACNC,SAAU,OACV0D,aAAa,EACb7E,OAAQ,CACNQ,QAAS,GACTM,SAAU,KACVE,SAAU,MAEZiD,SAAU,CACRpC,MAAO,GACPC,MAAO,GACPC,KAAM,GACNE,KAAM,GACND,KAAM,EACNsD,KAAM,GACNC,IAAK,IAEPA,IAAK,EACLrB,MAAO,CACLrC,MAAO,CAAC,CACN2D,UAAU,EACVC,QAAS,UACTC,QAAS,SAEX5D,MAAO,CAAC,CACN0D,UAAU,EACVC,QAAS,QACTC,QAAS,SAEX3D,KAAM,CAAC,CACLyD,UAAU,EACVC,QAAS,QACTC,QAAS,UAGb7B,mBAAmB,EACnB8B,eAAgB,QAChBC,IAAK,WACLpB,MAAO,KAGXqB,SAAU,CAERX,eACE,IAAKY,MAAMC,QAAQ/J,KAAKoJ,YAAwC,IAA1BpJ,KAAKoJ,UAAUtJ,OAAc,MAAO,IAC1E,MAAMyD,EAAQvD,KAAKoJ,UAAUxJ,OAAO,CAACoK,EAAKvB,IAASuB,GAAOC,WAAWxB,EAAK3C,QAAU,GAAI,GACxF,OAAOoE,KAAKC,MAAM5G,EAAQvD,KAAKoJ,UAAUtJ,QAAQsK,kBAEnDlB,kBACE,OAAOY,MAAMC,QAAQ/J,KAAKoJ,WAAapJ,KAAKoJ,UAAUiB,OAAO5B,GAAQwB,WAAWxB,EAAK3C,OAAS,KAAOhG,OAAS,GAEhHoJ,cACE,IAAKY,MAAMC,QAAQ/J,KAAKoJ,YAAwC,IAA1BpJ,KAAKoJ,UAAUtJ,OAAc,OAAO,EAC1E,MAAMyD,EAAQvD,KAAKoJ,UAAUxJ,OAAO,CAACoK,EAAKvB,IAASuB,GAAOM,SAAS7B,EAAK1C,OAAS,GAAI,GACrF,OAAOmE,KAAKC,MAAM5G,EAAQvD,KAAKoJ,UAAUtJ,SAE3CoJ,cACE,OAAOlJ,KAAKiI,SAASrC,GAAK,OAAS,QAErCsD,mBACE,IAAKY,MAAMC,QAAQ/J,KAAKoJ,WAAY,MAAO,GAC3C,IAAImB,EAAWvK,KAAKoJ,UAGpB,GAAIpJ,KAAKgE,OAAOQ,QAAS,CACvB,MAAMA,EAAUxE,KAAKgE,OAAOQ,QAAQgG,cACpCD,EAAWA,EAASF,OAAO5B,GAAQA,EAAK5C,OAAS4C,EAAK5C,MAAM2E,cAAcC,SAASjG,IAAYiE,EAAKxC,MAAQwC,EAAKxC,KAAKuE,cAAcC,SAASjG,IAU/I,OAN6B,OAAzBxE,KAAKgE,OAAOc,WACdyF,EAAWA,EAASF,OAAO5B,GAAQwB,WAAWxB,EAAK3C,QAAU9F,KAAKgE,OAAOc,WAE9C,OAAzB9E,KAAKgE,OAAOgB,WACduF,EAAWA,EAASF,OAAO5B,GAAQwB,WAAWxB,EAAK3C,QAAU9F,KAAKgE,OAAOgB,WAEpEuF,IAGXrB,UACElJ,KAAK8C,WAEP4H,QAAS,CACPxB,SAAStD,GACP,IAAI+E,EAAQ3K,KACF,GAAN4F,EACF5F,KAAK4K,QAAQhF,IAEb5F,KAAKiI,SAAW,CACdpC,MAAO,GACPC,MAAO,GACPC,KAAM,GACNE,KAAM,GACND,KAAM,EACNsD,KAAM,GACNC,IAAK,IAEPoB,EAAME,YAERF,EAAM9C,mBAAoB,GAE5BqB,QAAQtD,GACN,IAAI+E,EAAQ3K,KACZ2K,EAAMG,WAAWH,EAAMf,IAAM,WAAahE,GAAImF,KAAKC,IAC7CA,IACFL,EAAM1C,SAAW+C,EAAKvE,KACtBkE,EAAMnC,MAAQmC,EAAM1C,SAASsB,QAInCL,QAAQpH,EAAO8D,GACb5F,KAAKiL,SAAS,WAAY,KAAM,CAC9BC,kBAAmB,KACnBC,iBAAkB,KAClBzI,KAAM,YACLqI,KAAK,KACN/K,KAAKoL,cAAcpL,KAAK4J,IAAM,aAAehE,GAAImF,KAAKC,IACnC,KAAbA,EAAKK,OACPrL,KAAKsL,SAAS,CACZ5I,KAAM,UACN+G,QAAS,UAEXzJ,KAAKoJ,UAAUmC,OAAOzJ,EAAO,QAGhC0J,MAAM,KACPxL,KAAKsL,SAAS,CACZ5I,KAAM,QACN+G,QAAS,aAIfP,WACElJ,KAAKyL,YAAY,gBAAiB,IAAIV,KAAKC,IACxB,KAAbA,EAAKK,OACPrL,KAAKwI,MAAQwC,EAAKvE,SAIxByC,UACE,IAAIyB,EAAQ3K,KACZ2K,EAAMpF,SAAU,EAChBoF,EAAMc,YAAYd,EAAMf,IAAM,cAAgBe,EAAMtB,KAAO,SAAWsB,EAAMzF,KAAMyF,EAAM3G,QAAQ+G,KAAKC,IAClF,KAAbA,EAAKK,OACPV,EAAMvB,UAAY4B,EAAKvE,KACvBkE,EAAMpH,MAAQyH,EAAKU,OAErBf,EAAMpF,SAAU,KAGpB2D,iBAAiByC,GACf3L,KAAKkF,KAAOyG,EACZ3L,KAAK8C,WAEPoG,oBAAoByC,GAClB3L,KAAKqJ,KAAOsC,EACZ3L,KAAK8C,WAEPoG,WAAW0C,GACT,OAAKA,EACE,IAAIC,KAAKD,GAASE,mBAAmB,SADvB,OAGvB5C,cACElJ,KAAKgE,OAAS,CACZQ,QAAS,GACTM,SAAU,KACVE,SAAU,MAEZhF,KAAKqJ,KAAO,EACZrJ,KAAK8C,WAEPoG,WACElJ,KAAK+L,MAAM9D,SAAS+D,SAASC,IAC3B,IAAIA,EAaF,OAAO,EAZPjM,KAAK6I,aAAc,EAEnB7I,KAAKiI,SAASqB,KAAOtJ,KAAKwI,MAAM6B,OAAO3H,GAAQA,EAAKgG,SAASwD,IAAIxJ,GAAQA,EAAKkD,IAG9EuG,WAAW,KACTnM,KAAK6I,aAAc,EACnB7I,KAAK6H,mBAAoB,EACzB7H,KAAKsL,SAASc,QAAQ,QACtBpM,KAAK8C,WACJ,WASqBuJ,EAAuC,EAKrEC,GAHqEtN,EAAoB,QAGnEA,EAAoB,SAW1CuN,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACAlK,EACA4G,GACA,EACA,KACA,WACA,MAIwCjI,EAAoB,WAAcyL,EAAiB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-7963da7c\"],{\"13d5\":function(t,e,a){\"use strict\";var s=a(\"23e7\"),i=a(\"d58f\").left,l=a(\"a640\"),r=a(\"2d00\"),c=a(\"605d\"),o=!c&&r>79&&r<83,n=o||!l(\"reduce\");s({target:\"Array\",proto:!0,forced:n},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},\"51a4\":function(t,e,a){},\"605d\":function(t,e,a){\"use strict\";var s=a(\"da84\"),i=a(\"c6b6\");t.exports=\"process\"===i(s.process)},a640:function(t,e,a){\"use strict\";var s=a(\"d039\");t.exports=function(t,e){var a=[][t];return!!a&&s((function(){a.call(null,e||function(){return 1},1)}))}},d564:function(t,e,a){\"use strict\";a(\"51a4\")},d58f:function(t,e,a){\"use strict\";var s=a(\"59ed\"),i=a(\"7b0b\"),l=a(\"44ad\"),r=a(\"07fa\"),c=TypeError,o=\"Reduce of empty array with no initial value\",n=function(t){return function(e,a,n,d){var u=i(e),m=l(u),p=r(u);if(s(a),0===p&&n<2)throw new c(o);var v=t?p-1:0,h=t?-1:1;if(n<2)while(1){if(v in m){d=m[v],v+=h;break}if(v+=h,t?v<0:p<=v)throw new c(o)}for(;t?v>=0:p>v;v+=h)v in m&&(d=a(d,m[v],v,u));return d}};t.exports={left:n(!1),right:n(!0)}},d625:function(t,e,a){\"use strict\";a.r(e);var s=function(){var t=this,e=t._self._c;return e(\"div\",{staticClass:\"package-management-container\"},[e(\"div\",{staticClass:\"page-header\"},[t._m(0),e(\"div\",{staticClass:\"header-actions\"},[e(\"el-button\",{staticClass:\"refresh-btn\",attrs:{type:\"text\",icon:\"el-icon-refresh\"},on:{click:t.getData}},[t._v(\" 刷新数据 \")])],1)]),e(\"div\",{staticClass:\"stats-section\"},[e(\"el-row\",{attrs:{gutter:20}},[e(\"el-col\",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e(\"div\",{staticClass:\"stat-card\"},[e(\"div\",{staticClass:\"stat-icon total-icon\"},[e(\"i\",{staticClass:\"el-icon-box\"})]),e(\"div\",{staticClass:\"stat-content\"},[e(\"div\",{staticClass:\"stat-number\"},[t._v(t._s(t.total))]),e(\"div\",{staticClass:\"stat-label\"},[t._v(\"套餐总数\")]),e(\"div\",{staticClass:\"stat-change positive\"},[e(\"i\",{staticClass:\"el-icon-arrow-up\"}),t._v(\" +12% \")])])])]),e(\"el-col\",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e(\"div\",{staticClass:\"stat-card\"},[e(\"div\",{staticClass:\"stat-icon price-icon\"},[e(\"i\",{staticClass:\"el-icon-money\"})]),e(\"div\",{staticClass:\"stat-content\"},[e(\"div\",{staticClass:\"stat-number\"},[t._v(\"¥\"+t._s(t.averagePrice))]),e(\"div\",{staticClass:\"stat-label\"},[t._v(\"平均价格\")]),e(\"div\",{staticClass:\"stat-change positive\"},[e(\"i\",{staticClass:\"el-icon-arrow-up\"}),t._v(\" +5% \")])])])]),e(\"el-col\",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e(\"div\",{staticClass:\"stat-card\"},[e(\"div\",{staticClass:\"stat-icon premium-icon\"},[e(\"i\",{staticClass:\"el-icon-star-on\"})]),e(\"div\",{staticClass:\"stat-content\"},[e(\"div\",{staticClass:\"stat-number\"},[t._v(t._s(t.premiumPackages))]),e(\"div\",{staticClass:\"stat-label\"},[t._v(\"高端套餐\")]),e(\"div\",{staticClass:\"stat-change positive\"},[e(\"i\",{staticClass:\"el-icon-arrow-up\"}),t._v(\" +8% \")])])])]),e(\"el-col\",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[e(\"div\",{staticClass:\"stat-card\"},[e(\"div\",{staticClass:\"stat-icon duration-icon\"},[e(\"i\",{staticClass:\"el-icon-time\"})]),e(\"div\",{staticClass:\"stat-content\"},[e(\"div\",{staticClass:\"stat-number\"},[t._v(t._s(t.averageYear)+\"年\")]),e(\"div\",{staticClass:\"stat-label\"},[t._v(\"平均年限\")]),e(\"div\",{staticClass:\"stat-change positive\"},[e(\"i\",{staticClass:\"el-icon-check\"}),t._v(\" 稳定 \")])])])])],1)],1),e(\"el-card\",{staticClass:\"search-card\",attrs:{shadow:\"hover\"}},[e(\"div\",{staticClass:\"card-header\",attrs:{slot:\"header\"},slot:\"header\"},[e(\"span\",{staticClass:\"card-title\"},[e(\"i\",{staticClass:\"el-icon-search\"}),t._v(\" 搜索管理 \")]),e(\"div\",{staticClass:\"header-actions\"},[e(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-plus\"},on:{click:function(e){return t.editData(0)}}},[t._v(\" 新增套餐 \")])],1)]),e(\"div\",{staticClass:\"search-section\"},[e(\"el-form\",{staticClass:\"search-form\",attrs:{model:t.search,inline:!0}},[e(\"el-form-item\",{attrs:{label:\"关键词\"}},[e(\"el-input\",{staticStyle:{width:\"300px\"},attrs:{placeholder:\"请输入套餐名称或描述\",clearable:\"\"},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,\"keyword\",e)},expression:\"search.keyword\"}},[e(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-search\"},on:{click:function(e){return t.getData()}},slot:\"append\"})],1)],1),e(\"el-form-item\",{attrs:{label:\"价格范围\"}},[e(\"el-input-number\",{staticStyle:{width:\"120px\"},attrs:{placeholder:\"最低价格\",min:0},model:{value:t.search.minPrice,callback:function(e){t.$set(t.search,\"minPrice\",e)},expression:\"search.minPrice\"}}),e(\"span\",{staticStyle:{margin:\"0 8px\"}},[t._v(\"-\")]),e(\"el-input-number\",{staticStyle:{width:\"120px\"},attrs:{placeholder:\"最高价格\",min:0},model:{value:t.search.maxPrice,callback:function(e){t.$set(t.search,\"maxPrice\",e)},expression:\"search.maxPrice\"}})],1),e(\"el-form-item\",[e(\"el-button\",{attrs:{icon:\"el-icon-refresh\"},on:{click:t.resetSearch}},[t._v(\" 重置 \")])],1)],1)],1)]),e(\"el-card\",{staticClass:\"package-card\",attrs:{shadow:\"hover\"}},[e(\"div\",{staticClass:\"card-header\",attrs:{slot:\"header\"},slot:\"header\"},[e(\"span\",{staticClass:\"card-title\"},[e(\"i\",{staticClass:\"el-icon-tickets\"}),t._v(\" 套餐列表 \")]),e(\"div\",{staticClass:\"view-controls\"},[e(\"el-radio-group\",{attrs:{size:\"small\"},model:{value:t.viewMode,callback:function(e){t.viewMode=e},expression:\"viewMode\"}},[e(\"el-radio-button\",{attrs:{label:\"grid\"}},[t._v(\"卡片视图\")]),e(\"el-radio-button\",{attrs:{label:\"table\"}},[t._v(\"表格视图\")])],1)],1)]),\"grid\"===t.viewMode?e(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],staticClass:\"package-grid\"},t._l(t.filteredPackages,(function(a){return e(\"div\",{key:a.id,staticClass:\"package-item\"},[e(\"div\",{staticClass:\"package-header\"},[e(\"div\",{staticClass:\"package-title\"},[t._v(t._s(a.title))]),e(\"div\",{staticClass:\"package-price\"},[t._v(\"¥\"+t._s(a.price))])]),e(\"div\",{staticClass:\"package-content\"},[e(\"div\",{staticClass:\"package-info\"},[e(\"div\",{staticClass:\"info-item\"},[e(\"i\",{staticClass:\"el-icon-time\"}),e(\"span\",[t._v(t._s(a.year)+\"年服务\")])]),e(\"div\",{staticClass:\"info-item\"},[e(\"i\",{staticClass:\"el-icon-sort\"}),e(\"span\",[t._v(\"排序: \"+t._s(a.sort))])])]),e(\"div\",{staticClass:\"package-desc\"},[t._v(\" \"+t._s(a.desc||\"暂无描述\")+\" \")]),a.services&&a.services.length>0?e(\"div\",{staticClass:\"package-features\"},[e(\"div\",{staticClass:\"feature-title\"},[t._v(\"包含服务:\")]),e(\"div\",{staticClass:\"feature-list\"},[t._l(a.services.slice(0,3),(function(a){return e(\"el-tag\",{key:a.id,staticClass:\"feature-tag\",attrs:{size:\"mini\"}},[t._v(\" \"+t._s(a.name)+\" \")])})),a.services.length>3?e(\"span\",{staticClass:\"more-services\"},[t._v(\" +\"+t._s(a.services.length-3)+\" \")]):t._e()],2)]):t._e()]),e(\"div\",{staticClass:\"package-footer\"},[e(\"div\",{staticClass:\"package-meta\"},[e(\"span\",{staticClass:\"create-time\"},[t._v(t._s(t.formatDate(a.create_time)))])]),e(\"div\",{staticClass:\"package-actions\"},[e(\"el-button\",{staticClass:\"edit-btn\",attrs:{type:\"text\"},on:{click:function(e){return t.editData(a.id)}}},[t._v(\" 编辑 \")]),e(\"el-button\",{staticClass:\"delete-btn\",attrs:{type:\"text\"},on:{click:function(e){return t.delData(-1,a.id)}}},[t._v(\" 删除 \")])],1)])])})),0):t._e(),\"table\"===t.viewMode?e(\"div\",[e(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],staticClass:\"modern-table\",attrs:{data:t.filteredPackages}},[e(\"el-table-column\",{attrs:{label:\"套餐信息\",\"min-width\":\"200\"},scopedSlots:t._u([{key:\"default\",fn:function(a){return[e(\"div\",{staticClass:\"table-package-info\"},[e(\"div\",{staticClass:\"table-package-title\"},[t._v(t._s(a.row.title))]),e(\"div\",{staticClass:\"table-package-desc\"},[t._v(t._s(a.row.desc||\"暂无描述\"))])])]}}],null,!1,3323292265)}),e(\"el-table-column\",{attrs:{label:\"价格\",width:\"120\"},scopedSlots:t._u([{key:\"default\",fn:function(a){return[e(\"div\",{staticClass:\"price-display\"},[t._v(\"¥\"+t._s(a.row.price))])]}}],null,!1,2696510062)}),e(\"el-table-column\",{attrs:{prop:\"year\",label:\"年限\",width:\"100\"},scopedSlots:t._u([{key:\"default\",fn:function(a){return[e(\"el-tag\",{attrs:{type:\"info\",size:\"small\"}},[t._v(t._s(a.row.year)+\"年\")])]}}],null,!1,3902229530)}),e(\"el-table-column\",{attrs:{prop:\"sort\",label:\"排序\",width:\"80\"}}),e(\"el-table-column\",{attrs:{label:\"创建时间\",width:\"120\"},scopedSlots:t._u([{key:\"default\",fn:function(a){return[e(\"div\",{staticClass:\"time-info\"},[t._v(\" \"+t._s(t.formatDate(a.row.create_time))+\" \")])]}}],null,!1,2692560985)}),e(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\",width:\"120\"},scopedSlots:t._u([{key:\"default\",fn:function(a){return[e(\"div\",{staticClass:\"action-buttons\"},[e(\"el-button\",{staticClass:\"edit-btn\",attrs:{type:\"text\",size:\"small\"},on:{click:function(e){return t.editData(a.row.id)}}},[e(\"i\",{staticClass:\"el-icon-edit\"}),t._v(\" 编辑 \")]),e(\"el-button\",{staticClass:\"delete-btn\",attrs:{type:\"text\",size:\"small\"},on:{click:function(e){return t.delData(a.$index,a.row.id)}}},[e(\"i\",{staticClass:\"el-icon-delete\"}),t._v(\" 删除 \")])],1)]}}],null,!1,1323445013)})],1)],1):t._e(),e(\"div\",{staticClass:\"pagination-wrapper\"},[e(\"el-pagination\",{attrs:{\"page-sizes\":[12,20,50,100],\"page-size\":t.size,layout:\"total, sizes, prev, pager, next, jumper\",total:t.total,background:\"\"},on:{\"size-change\":t.handleSizeChange,\"current-change\":t.handleCurrentChange}})],1)]),e(\"el-dialog\",{staticClass:\"edit-dialog\",attrs:{title:t.dialogTitle,visible:t.dialogFormVisible,\"close-on-click-modal\":!1,width:\"70%\"},on:{\"update:visible\":function(e){t.dialogFormVisible=e}}},[e(\"el-form\",{ref:\"ruleForm\",attrs:{model:t.ruleForm,rules:t.rules,\"label-width\":\"120px\"}},[e(\"el-row\",{attrs:{gutter:20}},[e(\"el-col\",{attrs:{span:12}},[e(\"el-form-item\",{attrs:{label:\"套餐名称\",prop:\"title\"}},[e(\"el-input\",{attrs:{placeholder:\"请输入套餐名称\",autocomplete:\"off\"},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,\"title\",e)},expression:\"ruleForm.title\"}})],1)],1),e(\"el-col\",{attrs:{span:12}},[e(\"el-form-item\",{attrs:{label:\"套餐价格\",prop:\"price\"}},[e(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:0,max:999999,precision:2,placeholder:\"请输入价格\"},model:{value:t.ruleForm.price,callback:function(e){t.$set(t.ruleForm,\"price\",e)},expression:\"ruleForm.price\"}})],1)],1)],1),e(\"el-row\",{attrs:{gutter:20}},[e(\"el-col\",{attrs:{span:12}},[e(\"el-form-item\",{attrs:{label:\"服务年限\",prop:\"year\"}},[e(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:1,max:10,placeholder:\"请输入年限\"},model:{value:t.ruleForm.year,callback:function(e){t.$set(t.ruleForm,\"year\",e)},expression:\"ruleForm.year\"}})],1)],1),e(\"el-col\",{attrs:{span:12}},[e(\"el-form-item\",{attrs:{label:\"排序\"}},[e(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:0,max:999,placeholder:\"数字越小排序越靠前\"},model:{value:t.ruleForm.sort,callback:function(e){t.$set(t.ruleForm,\"sort\",e)},expression:\"ruleForm.sort\"}})],1)],1)],1),e(\"el-form-item\",{attrs:{label:\"套餐内容\",prop:\"good\"}},[e(\"div\",{staticClass:\"service-selection\"},[e(\"div\",{staticClass:\"service-title\"},[t._v(\"选择包含的服务类型:\")]),e(\"div\",{staticClass:\"service-list\"},t._l(t.types,(function(a,s){return e(\"div\",{key:s,staticClass:\"service-item\"},[e(\"div\",{staticClass:\"service-checkbox\"},[e(\"el-checkbox\",{attrs:{label:a.id},model:{value:a.checked,callback:function(e){t.$set(a,\"checked\",e)},expression:\"item.checked\"}},[t._v(\" \"+t._s(a.title)+\" \")])],1),1==a.is_num&&a.checked?e(\"div\",{staticClass:\"service-input\"},[e(\"el-input-number\",{attrs:{min:1,max:999,size:\"small\",placeholder:\"次数\"},model:{value:a.value,callback:function(e){t.$set(a,\"value\",e)},expression:\"item.value\"}}),e(\"span\",{staticClass:\"input-suffix\"},[t._v(\"次\")])],1):a.checked?e(\"div\",{staticClass:\"service-unlimited\"},[e(\"el-tag\",{attrs:{size:\"small\",type:\"success\"}},[t._v(\"不限次数\")])],1):t._e()])})),0)])]),e(\"el-form-item\",{attrs:{label:\"套餐描述\"}},[e(\"el-input\",{attrs:{type:\"textarea\",rows:3,placeholder:\"请输入套餐详细描述...\",autocomplete:\"off\"},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,\"desc\",e)},expression:\"ruleForm.desc\"}})],1)],1),e(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[e(\"el-button\",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(\"取消\")]),e(\"el-button\",{attrs:{type:\"primary\",loading:t.saveLoading},on:{click:function(e){return t.saveData()}}},[t._v(\" 保存 \")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e(\"div\",{staticClass:\"header-left\"},[e(\"h2\",{staticClass:\"page-title\"},[e(\"i\",{staticClass:\"el-icon-box\"}),t._v(\" 套餐类型管理 \")]),e(\"div\",{staticClass:\"page-subtitle\"},[t._v(\"管理法律服务套餐产品和价格配置\")])])}],l=(a(\"13d5\"),{name:\"PackageManagement\",components:{},data(){return{allSize:\"mini\",tableData:[],loading:!0,total:1,page:1,size:12,viewMode:\"grid\",saveLoading:!1,search:{keyword:\"\",minPrice:null,maxPrice:null},ruleForm:{title:\"\",price:\"\",year:\"\",desc:\"\",sort:0,good:[],num:[]},num:0,rules:{title:[{required:!0,message:\"请填写套餐名称\",trigger:\"blur\"}],price:[{required:!0,message:\"请填写价格\",trigger:\"blur\"}],year:[{required:!0,message:\"请填写年限\",trigger:\"blur\"}]},dialogFormVisible:!1,formLabelWidth:\"120px\",url:\"/taocan/\",types:[]}},computed:{averagePrice(){if(!Array.isArray(this.tableData)||0===this.tableData.length)return\"0\";const t=this.tableData.reduce((t,e)=>t+(parseFloat(e.price)||0),0);return Math.round(t/this.tableData.length).toLocaleString()},premiumPackages(){return Array.isArray(this.tableData)?this.tableData.filter(t=>parseFloat(t.price)>1e4).length:0},averageYear(){if(!Array.isArray(this.tableData)||0===this.tableData.length)return 1;const t=this.tableData.reduce((t,e)=>t+(parseInt(e.year)||1),0);return Math.round(t/this.tableData.length)},dialogTitle(){return this.ruleForm.id?\"编辑套餐\":\"新增套餐\"},filteredPackages(){if(!Array.isArray(this.tableData))return[];let t=this.tableData;if(this.search.keyword){const e=this.search.keyword.toLowerCase();t=t.filter(t=>t.title&&t.title.toLowerCase().includes(e)||t.desc&&t.desc.toLowerCase().includes(e))}return null!==this.search.minPrice&&(t=t.filter(t=>parseFloat(t.price)>=this.search.minPrice)),null!==this.search.maxPrice&&(t=t.filter(t=>parseFloat(t.price)<=this.search.maxPrice)),t}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):(this.ruleForm={title:\"\",price:\"\",year:\"\",desc:\"\",sort:0,good:[],num:[]},e.getTypes()),e.dialogFormVisible=!0},getInfo(t){let e=this;e.getRequest(e.url+\"read?id=\"+t).then(t=>{t&&(e.ruleForm=t.data,e.types=e.ruleForm.num)})},delData(t,e){this.$confirm(\"是否删除该信息?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.deleteRequest(this.url+\"delete?id=\"+e).then(e=>{200==e.code&&(this.$message({type:\"success\",message:\"删除成功!\"}),this.tableData.splice(t,1))})}).catch(()=>{this.$message({type:\"error\",message:\"取消删除!\"})})},getTypes(){this.postRequest(\"/type/getList\",{}).then(t=>{200==t.code&&(this.types=t.data)})},getData(){let t=this;t.loading=!0,t.postRequest(t.url+\"index?page=\"+t.page+\"&size=\"+t.size,t.search).then(e=>{200==e.code&&(t.tableData=e.data,t.total=e.count),t.loading=!1})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},formatDate(t){return t?new Date(t).toLocaleDateString(\"zh-CN\"):\"未设置\"},resetSearch(){this.search={keyword:\"\",minPrice:null,maxPrice:null},this.page=1,this.getData()},saveData(){this.$refs.ruleForm.validate(t=>{if(!t)return!1;this.saveLoading=!0,this.ruleForm.good=this.types.filter(t=>t.checked).map(t=>t.id),setTimeout(()=>{this.saveLoading=!1,this.dialogFormVisible=!1,this.$message.success(\"保存成功\"),this.getData()},1e3)})}}}),r=l,c=(a(\"d564\"),a(\"2877\")),o=Object(c[\"a\"])(r,s,i,!1,null,\"4b11a6a1\",null);e[\"default\"]=o.exports}}]);", "extractedComments": []}