{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\data\\configs.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\data\\configs.vue", "mtime": 1748489112808}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["configs.vue"], "names": [], "mappings": ";AAmh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file": "configs.vue", "sourceRoot": "src/views/pages/data", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-title\">\r\n        基础设置\r\n      </div>\r\n\r\n      <!-- 标签页导航 -->\r\n      <div class=\"tab-container\">\r\n        <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\r\n          <el-tab-pane label=\"基础管理\" name=\"first\">\r\n            <div class=\"form-container\">\r\n              <el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"140px\">\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"网站名称\">\r\n                      <el-input v-model=\"ruleForm.site_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\">\r\n                      <el-input v-model=\"ruleForm.company_name\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系方式\">\r\n                      <el-input v-model=\"ruleForm.site_tel\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"邮箱\">\r\n                      <el-input v-model=\"ruleForm.email\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"ruleForm.site_address\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案号\">\r\n                      <el-input v-model=\"ruleForm.site_icp\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"ICP备案链接\">\r\n                      <el-input v-model=\"ruleForm.site_icp_url\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-form-item label=\"网站Logo\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.site_logo\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传Logo图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('site_logo')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"showImage(ruleForm.site_logo)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.site_logo\"\r\n                        @click=\"delImage(ruleForm.site_logo, 'site_logo')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"推广律师\">\r\n                  <el-select\r\n                    v-model=\"ruleForm.lvshi\"\r\n                    placeholder=\"请选择推广律师\"\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                      v-for=\"(item, index) in lvshi\"\r\n                      :key=\"index\"\r\n                      :label=\"item.title\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-row :gutter=\"24\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广标题\">\r\n                      <el-input v-model=\"ruleForm.my_title\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"推广语\">\r\n                      <el-input v-model=\"ruleForm.my_desc\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-form-item label=\"推广图片\">\r\n                  <div class=\"upload-container\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.about_path\"\r\n                      :disabled=\"true\"\r\n                      placeholder=\"请上传推广图片\"\r\n                    ></el-input>\r\n                    <div class=\"upload-actions\">\r\n                      <el-button @click=\"changeFiled('about_path')\" size=\"small\">\r\n                        <el-upload\r\n                          action=\"/admin/Upload/uploadImage\"\r\n                          :show-file-list=\"false\"\r\n                          :on-success=\"handleSuccess\"\r\n                          :before-upload=\"beforeUpload\"\r\n                        >\r\n                          上传\r\n                        </el-upload>\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"success\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"showImage(ruleForm.about_path)\"\r\n                        >查看\r\n                      </el-button>\r\n                      <el-button\r\n                        type=\"danger\"\r\n                        size=\"small\"\r\n                        v-if=\"ruleForm.about_path\"\r\n                        @click=\"delImage(ruleForm.about_path, 'about_path')\"\r\n                        >删除</el-button\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"隐私条款\" name=\"yinsi\">\r\n            <div class=\"privacy-container\">\r\n              <!-- 隐私条款工具栏 -->\r\n              <div class=\"privacy-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewPrivacy\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetPrivacy\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ privacyWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"privacyEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"privacyEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.yinsi\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onPrivacyChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.yinsi_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入隐私条款内容...\"\r\n                  @input=\"onPrivacyTextChange\"\r\n                  class=\"privacy-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('邮箱地址')\"\r\n                  >\r\n                    邮箱地址\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertText('生效日期')\"\r\n                  >\r\n                    生效日期\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"关于我们\" name=\"about\">\r\n            <div class=\"about-container\">\r\n              <!-- 关于我们工具栏 -->\r\n              <div class=\"about-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useAboutTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewAbout\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetAbout\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ aboutWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"aboutEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"aboutEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_about_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onAboutChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.about_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入关于我们的内容...\"\r\n                  @input=\"onAboutTextChange\"\r\n                  class=\"about-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司名称')\"\r\n                  >\r\n                    公司名称\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('成立时间')\"\r\n                  >\r\n                    成立时间\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertAboutText('公司地址')\"\r\n                  >\r\n                    公司地址\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane label=\"团队介绍\" name=\"team\">\r\n            <div class=\"team-container\">\r\n              <!-- 团队介绍工具栏 -->\r\n              <div class=\"team-toolbar\">\r\n                <div class=\"toolbar-left\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"useTeamTemplate\"\r\n                  >\r\n                    使用模板\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"success\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-view\"\r\n                    @click=\"previewTeam\"\r\n                  >\r\n                    预览\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"warning\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-refresh\"\r\n                    @click=\"resetTeam\"\r\n                  >\r\n                    重置\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"toolbar-right\">\r\n                  <span class=\"word-count\">字数：{{ teamWordCount }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 编辑模式切换 -->\r\n              <div class=\"edit-mode-switch\">\r\n                <el-radio-group v-model=\"teamEditMode\" size=\"small\">\r\n                  <el-radio-button label=\"rich\">富文本编辑</el-radio-button>\r\n                  <el-radio-button label=\"text\">纯文本编辑</el-radio-button>\r\n                </el-radio-group>\r\n              </div>\r\n\r\n              <!-- 富文本编辑器 -->\r\n              <div v-if=\"teamEditMode === 'rich'\" class=\"rich-editor-container\">\r\n                <editor-bar\r\n                  v-model=\"ruleForm.index_team_content\"\r\n                  :isClear=\"isClear\"\r\n                  @change=\"onTeamChange\"\r\n                  :height=\"400\"\r\n                ></editor-bar>\r\n              </div>\r\n\r\n              <!-- 纯文本编辑器 -->\r\n              <div v-else class=\"text-editor-container\">\r\n                <el-input\r\n                  v-model=\"ruleForm.team_text\"\r\n                  type=\"textarea\"\r\n                  :rows=\"20\"\r\n                  placeholder=\"请输入团队介绍内容...\"\r\n                  @input=\"onTeamTextChange\"\r\n                  class=\"team-textarea\"\r\n                ></el-input>\r\n              </div>\r\n\r\n              <!-- 快捷插入 -->\r\n              <div class=\"quick-insert\">\r\n                <div class=\"quick-insert-title\">快捷插入：</div>\r\n                <div class=\"quick-insert-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('团队规模')\"\r\n                  >\r\n                    团队规模\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('专业领域')\"\r\n                  >\r\n                    专业领域\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('服务理念')\"\r\n                  >\r\n                    服务理念\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    @click=\"insertTeamText('联系方式')\"\r\n                  >\r\n                    联系方式\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n\r\n      <!-- 提交按钮 -->\r\n      <div class=\"submit-container\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"medium\"\r\n          @click=\"saveData\"\r\n          :loading=\"fullscreenLoading\"\r\n          >保存设置\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\" style=\"width: 100%\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 隐私条款预览对话框 -->\r\n    <el-dialog\r\n      title=\"隐私条款预览\"\r\n      :visible.sync=\"previewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"privacy-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.yinsi\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"previewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 关于我们预览对话框 -->\r\n    <el-dialog\r\n      title=\"关于我们预览\"\r\n      :visible.sync=\"aboutPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"about-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_about_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"aboutPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"aboutPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 团队介绍预览对话框 -->\r\n    <el-dialog\r\n      title=\"团队介绍预览\"\r\n      :visible.sync=\"teamPreviewDialogVisible\"\r\n      width=\"70%\"\r\n      class=\"team-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\" v-html=\"ruleForm.index_team_content\"></div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"teamPreviewDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"teamPreviewDialogVisible = false\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"edit\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      ruleForm: {\r\n        site_name: \"法律服务管理系统\",\r\n        company_name: \"示例法律服务公司\",\r\n        site_tel: \"************\",\r\n        email: \"<EMAIL>\",\r\n        site_address: \"北京市朝阳区示例大厦\",\r\n        site_icp: \"京ICP备12345678号\",\r\n        site_icp_url: \"https://beian.miit.gov.cn/\",\r\n        site_logo: \"\",\r\n        lvshi: \"\",\r\n        my_title: \"专业法律服务\",\r\n        my_desc: \"为您提供专业、高效的法律服务\",\r\n        about_path: \"\",\r\n        yinsi: `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>2024年1月1日</p>\r\n<p><strong>最后更新：</strong>2024年1月1日</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供法律服务</li>\r\n<li>改善我们的服务质量</li>\r\n<li>与您沟通服务相关事宜</li>\r\n</ul>\r\n\r\n<h3>3. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。</p>\r\n\r\n<h3>4. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        yinsi_text: \"\",\r\n        index_about_content: `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：北京市朝阳区示例大厦<br>\r\n电话：************<br>\r\n邮箱：<EMAIL></p>`,\r\n        about_text: \"\",\r\n        index_team_content: `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由20余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 3名</li>\r\n<li>合伙人律师 5名</li>\r\n<li>资深律师 8名</li>\r\n<li>执业律师 6名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过10年</li>\r\n<li>成功处理案件超过1000起</li>\r\n<li>客户满意度达到98%以上</li>\r\n<li>多名律师获得行业荣誉</li>\r\n</ul>`,\r\n        team_text: \"\"\r\n      },\r\n      activeName: \"first\",\r\n      url: \"/Config/\",\r\n      fullscreenLoading: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewDialogVisible: false,\r\n      aboutPreviewDialogVisible: false,\r\n      teamPreviewDialogVisible: false,\r\n      filedName: \"\",\r\n      isClear: true,\r\n      privacyEditMode: \"rich\", // 隐私条款编辑模式\r\n      aboutEditMode: \"rich\", // 关于我们编辑模式\r\n      teamEditMode: \"rich\", // 团队介绍编辑模式\r\n      privacyTemplate: \"\", // 隐私条款模板\r\n      lvshi: [\r\n        { id: 1, title: \"张律师\" },\r\n        { id: 2, title: \"李律师\" },\r\n        { id: 3, title: \"王律师\" }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 隐私条款字数统计\r\n    privacyWordCount() {\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 去除HTML标签后计算字数\r\n        const text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.yinsi_text.length;\r\n      }\r\n    },\r\n    // 关于我们字数统计\r\n    aboutWordCount() {\r\n      if (this.aboutEditMode === 'rich') {\r\n        const text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.about_text.length;\r\n      }\r\n    },\r\n    // 团队介绍字数统计\r\n    teamWordCount() {\r\n      if (this.teamEditMode === 'rich') {\r\n        const text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n        return text.length;\r\n      } else {\r\n        return this.ruleForm.team_text.length;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 纯前端模式 - 使用演示数据\r\n    console.log(\"纯前端模式：基础设置页面已加载\");\r\n    // 初始化纯文本内容\r\n    this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n    this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：律师列表已加载\");\r\n    },\r\n    changeFiled(fileName) {\r\n      this.filedName = fileName;\r\n    },\r\n    change() {},\r\n    getAllData() {\r\n      // 纯前端模式 - 使用演示数据\r\n      console.log(\"纯前端模式：配置数据已加载\");\r\n    },\r\n    handleSuccess(res) {\r\n      // 纯前端模式 - 模拟上传成功\r\n      this.ruleForm[this.filedName] = \"demo-image-url.jpg\";\r\n      this.$message.success(\"上传成功（演示）\");\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return false;\r\n      }\r\n      // 纯前端模式 - 阻止实际上传\r\n      this.$message.info(\"纯前端演示模式，文件上传已模拟\");\r\n      return false;\r\n    },\r\n    delImage(file, fileName) {\r\n      // 纯前端模式 - 模拟删除\r\n      this.ruleForm[fileName] = \"\";\r\n      this.$message.success(\"删除成功（演示）\");\r\n    },\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClick() {},\r\n    // 隐私条款相关方法\r\n    useTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>隐私政策</h2>\r\n<p><strong>生效日期：</strong>${new Date().toLocaleDateString()}</p>\r\n<p><strong>最后更新：</strong>${new Date().toLocaleDateString()}</p>\r\n\r\n<h3>1. 信息收集</h3>\r\n<p>我们可能收集以下类型的信息：</p>\r\n<ul>\r\n<li>个人身份信息（姓名、电话号码、电子邮件地址等）</li>\r\n<li>法律服务相关信息</li>\r\n<li>使用我们服务时的技术信息</li>\r\n<li>设备信息和使用数据</li>\r\n</ul>\r\n\r\n<h3>2. 信息使用</h3>\r\n<p>我们使用收集的信息用于：</p>\r\n<ul>\r\n<li>提供和改善法律服务</li>\r\n<li>处理您的请求和查询</li>\r\n<li>发送服务相关通知</li>\r\n<li>遵守法律义务</li>\r\n</ul>\r\n\r\n<h3>3. 信息共享</h3>\r\n<p>我们不会向第三方出售、交易或转让您的个人信息，除非：</p>\r\n<ul>\r\n<li>获得您的明确同意</li>\r\n<li>法律要求或政府部门要求</li>\r\n<li>为保护我们的权利和安全</li>\r\n</ul>\r\n\r\n<h3>4. 信息保护</h3>\r\n<p>我们采取适当的安全措施来保护您的个人信息：</p>\r\n<ul>\r\n<li>数据加密传输和存储</li>\r\n<li>访问权限控制</li>\r\n<li>定期安全审计</li>\r\n<li>员工隐私培训</li>\r\n</ul>\r\n\r\n<h3>5. 您的权利</h3>\r\n<p>您有权：</p>\r\n<ul>\r\n<li>访问和更新您的个人信息</li>\r\n<li>删除您的个人信息</li>\r\n<li>限制信息处理</li>\r\n<li>数据可携带性</li>\r\n</ul>\r\n\r\n<h3>6. Cookie使用</h3>\r\n<p>我们使用Cookie来改善用户体验，您可以通过浏览器设置管理Cookie偏好。</p>\r\n\r\n<h3>7. 政策更新</h3>\r\n<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。</p>\r\n\r\n<h3>8. 联系我们</h3>\r\n<p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\r\n<p>公司名称：${this.ruleForm.company_name}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}<br>\r\n地址：${this.ruleForm.site_address}</p>`;\r\n\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = template;\r\n        } else {\r\n          this.ruleForm.yinsi_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewPrivacy() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n\r\n    resetPrivacy() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.privacyEditMode === 'rich') {\r\n          this.ruleForm.yinsi = '';\r\n        } else {\r\n          this.ruleForm.yinsi_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onPrivacyChange(val) {\r\n      this.ruleForm.yinsi = val;\r\n      // 同步更新纯文本版本\r\n      this.ruleForm.yinsi_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onPrivacyTextChange(val) {\r\n      this.ruleForm.yinsi_text = val;\r\n      // 同步更新富文本版本（简单的换行转换）\r\n      this.ruleForm.yinsi = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '邮箱地址':\r\n          insertValue = this.ruleForm.email || '[邮箱地址]';\r\n          break;\r\n        case '生效日期':\r\n          insertValue = new Date().toLocaleDateString();\r\n          break;\r\n      }\r\n\r\n      if (this.privacyEditMode === 'rich') {\r\n        // 富文本模式下插入\r\n        this.ruleForm.yinsi += insertValue;\r\n      } else {\r\n        // 纯文本模式下插入\r\n        this.ruleForm.yinsi_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 关于我们相关方法\r\n    useAboutTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>关于我们</h2>\r\n<p>我们是一家专业的法律服务机构，致力于为客户提供全方位、高质量的法律服务。</p>\r\n\r\n<h3>公司简介</h3>\r\n<p>${this.ruleForm.company_name}成立于2020年，我们拥有一支经验丰富、专业素质过硬的律师团队。我们秉承\"专业、诚信、高效\"的服务理念，为客户提供优质的法律解决方案。</p>\r\n\r\n<h3>服务领域</h3>\r\n<ul>\r\n<li>企业法律顾问</li>\r\n<li>合同纠纷处理</li>\r\n<li>知识产权保护</li>\r\n<li>劳动争议调解</li>\r\n<li>民事诉讼代理</li>\r\n<li>刑事辩护代理</li>\r\n<li>房地产法务</li>\r\n<li>金融法务</li>\r\n</ul>\r\n\r\n<h3>我们的优势</h3>\r\n<ul>\r\n<li><strong>专业团队：</strong>拥有多名资深律师，专业覆盖面广</li>\r\n<li><strong>丰富经验：</strong>处理过大量成功案例，经验丰富</li>\r\n<li><strong>高效服务：</strong>快速响应，及时解决客户问题</li>\r\n<li><strong>合理收费：</strong>收费透明，性价比高</li>\r\n<li><strong>全程跟踪：</strong>专人负责，全程跟踪服务</li>\r\n</ul>\r\n\r\n<h3>服务承诺</h3>\r\n<p>我们承诺为每一位客户提供：</p>\r\n<ul>\r\n<li>专业的法律咨询服务</li>\r\n<li>及时的案件进展反馈</li>\r\n<li>透明的收费标准</li>\r\n<li>保密的客户信息</li>\r\n</ul>\r\n\r\n<h3>联系我们</h3>\r\n<p>地址：${this.ruleForm.site_address}<br>\r\n电话：${this.ruleForm.site_tel}<br>\r\n邮箱：${this.ruleForm.email}</p>`;\r\n\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = template;\r\n        } else {\r\n          this.ruleForm.about_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewAbout() {\r\n      this.aboutPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetAbout() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.aboutEditMode === 'rich') {\r\n          this.ruleForm.index_about_content = '';\r\n        } else {\r\n          this.ruleForm.about_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onAboutChange(val) {\r\n      this.ruleForm.index_about_content = val;\r\n      this.ruleForm.about_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onAboutTextChange(val) {\r\n      this.ruleForm.about_text = val;\r\n      this.ruleForm.index_about_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertAboutText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '公司名称':\r\n          insertValue = this.ruleForm.company_name || '[公司名称]';\r\n          break;\r\n        case '成立时间':\r\n          insertValue = '2020年';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n        case '公司地址':\r\n          insertValue = this.ruleForm.site_address || '[公司地址]';\r\n          break;\r\n      }\r\n\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.index_about_content += insertValue;\r\n      } else {\r\n        this.ruleForm.about_text += insertValue;\r\n      }\r\n    },\r\n\r\n    // 团队介绍相关方法\r\n    useTeamTemplate() {\r\n      this.$confirm('使用模板将覆盖当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const template = `<h2>团队介绍</h2>\r\n<p>我们拥有一支由资深律师组成的专业团队，每位成员都具备深厚的法律功底和丰富的实践经验。</p>\r\n\r\n<h3>团队规模</h3>\r\n<p>我们的团队由30余名专业律师组成，其中包括：</p>\r\n<ul>\r\n<li>高级合伙人律师 5名</li>\r\n<li>合伙人律师 8名</li>\r\n<li>资深律师 12名</li>\r\n<li>执业律师 10名</li>\r\n</ul>\r\n\r\n<h3>专业领域</h3>\r\n<p>团队成员专业领域覆盖：</p>\r\n<ul>\r\n<li><strong>公司法务：</strong>企业设立、股权转让、并购重组、公司治理</li>\r\n<li><strong>合同纠纷：</strong>合同起草、审查、纠纷处理、违约责任</li>\r\n<li><strong>知识产权：</strong>商标、专利、著作权保护、侵权诉讼</li>\r\n<li><strong>劳动法务：</strong>劳动合同、工伤赔偿、劳动仲裁、人事争议</li>\r\n<li><strong>民商事诉讼：</strong>各类民商事案件代理、仲裁程序</li>\r\n<li><strong>刑事辩护：</strong>刑事案件辩护、取保候审、缓刑申请</li>\r\n<li><strong>房地产法务：</strong>房产交易、物业纠纷、拆迁补偿</li>\r\n<li><strong>金融法务：</strong>银行业务、证券投资、保险理赔</li>\r\n</ul>\r\n\r\n<h3>服务理念</h3>\r\n<p>我们始终坚持\"客户至上、专业至上\"的服务理念，以客户需求为导向，提供个性化的法律服务方案。我们相信，只有真正理解客户的需求，才能提供最有价值的法律服务。</p>\r\n\r\n<h3>团队优势</h3>\r\n<ul>\r\n<li>平均执业年限超过12年</li>\r\n<li>成功处理案件超过2000起</li>\r\n<li>客户满意度达到99%以上</li>\r\n<li>多名律师获得省市级荣誉</li>\r\n<li>团队协作，资源共享</li>\r\n</ul>\r\n\r\n<h3>持续发展</h3>\r\n<p>我们注重团队的持续发展和专业提升，定期组织内部培训、学术交流和案例研讨，确保团队始终保持专业领先地位。</p>`;\r\n\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = template;\r\n        } else {\r\n          this.ruleForm.team_text = template.replace(/<[^>]*>/g, '');\r\n        }\r\n        this.$message.success('模板已应用');\r\n      });\r\n    },\r\n\r\n    previewTeam() {\r\n      this.teamPreviewDialogVisible = true;\r\n    },\r\n\r\n    resetTeam() {\r\n      this.$confirm('重置将清空当前内容，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.teamEditMode === 'rich') {\r\n          this.ruleForm.index_team_content = '';\r\n        } else {\r\n          this.ruleForm.team_text = '';\r\n        }\r\n        this.$message.success('内容已重置');\r\n      });\r\n    },\r\n\r\n    onTeamChange(val) {\r\n      this.ruleForm.index_team_content = val;\r\n      this.ruleForm.team_text = val.replace(/<[^>]*>/g, '');\r\n    },\r\n\r\n    onTeamTextChange(val) {\r\n      this.ruleForm.team_text = val;\r\n      this.ruleForm.index_team_content = val.replace(/\\n/g, '<br>');\r\n    },\r\n\r\n    insertTeamText(type) {\r\n      let insertValue = '';\r\n      switch(type) {\r\n        case '团队规模':\r\n          insertValue = '30余名专业律师';\r\n          break;\r\n        case '专业领域':\r\n          insertValue = '公司法务、合同纠纷、知识产权、劳动法务';\r\n          break;\r\n        case '服务理念':\r\n          insertValue = '客户至上、专业至上';\r\n          break;\r\n        case '联系方式':\r\n          insertValue = this.ruleForm.site_tel || '[联系方式]';\r\n          break;\r\n      }\r\n\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.index_team_content += insertValue;\r\n      } else {\r\n        this.ruleForm.team_text += insertValue;\r\n      }\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      _this.fullscreenLoading = true;\r\n\r\n      // 保存前同步所有内容的两种格式\r\n      // 隐私条款\r\n      if (this.privacyEditMode === 'rich') {\r\n        this.ruleForm.yinsi_text = this.ruleForm.yinsi.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.yinsi = this.ruleForm.yinsi_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 关于我们\r\n      if (this.aboutEditMode === 'rich') {\r\n        this.ruleForm.about_text = this.ruleForm.index_about_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_about_content = this.ruleForm.about_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 团队介绍\r\n      if (this.teamEditMode === 'rich') {\r\n        this.ruleForm.team_text = this.ruleForm.index_team_content.replace(/<[^>]*>/g, '');\r\n      } else {\r\n        this.ruleForm.index_team_content = this.ruleForm.team_text.replace(/\\n/g, '<br>');\r\n      }\r\n\r\n      // 纯前端模式 - 模拟保存\r\n      setTimeout(() => {\r\n        _this.$message({\r\n          type: \"success\",\r\n          message: \"保存成功（演示）\",\r\n        });\r\n        _this.fullscreenLoading = false;\r\n      }, 1000);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 页面样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  padding: 24px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.tab-container {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.form-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 上传组件样式 */\r\n.upload-container {\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.upload-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 提交按钮容器 */\r\n.submit-container {\r\n  text-align: center;\r\n  padding: 24px 0;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-input, .el-select, .el-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.el-textarea .el-textarea__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 标签页样式 */\r\n.el-tabs--card > .el-tabs__header .el-tabs__item {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n\r\n.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\r\n  background-color: #fff;\r\n  border-bottom-color: #fff;\r\n}\r\n\r\n/* 上传按钮样式 */\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n/* 隐私条款、关于我们、团队介绍相关样式 */\r\n.privacy-container,\r\n.about-container,\r\n.team-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.privacy-toolbar,\r\n.about-toolbar,\r\n.team-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: #fff;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  border: 1px solid #ddd;\r\n}\r\n\r\n.edit-mode-switch {\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.rich-editor-container {\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.text-editor-container {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.privacy-textarea,\r\n.about-textarea,\r\n.team-textarea {\r\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-textarea ::v-deep .el-textarea__inner,\r\n.about-textarea ::v-deep .el-textarea__inner,\r\n.team-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 6px;\r\n  border: 1px solid #dcdfe6;\r\n  padding: 16px;\r\n  resize: vertical;\r\n}\r\n\r\n.quick-insert {\r\n  background: #f0f9ff;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 6px;\r\n  padding: 12px 16px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.quick-insert-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #0369a1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.quick-insert-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text {\r\n  color: #0369a1;\r\n  background: #e0f2fe;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 4px;\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.quick-insert-buttons .el-button--text:hover {\r\n  background: #0369a1;\r\n  color: #fff;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.privacy-preview-dialog .preview-content,\r\n.about-preview-dialog .preview-content,\r\n.team-preview-dialog .preview-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 6px;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h2,\r\n.about-preview-dialog .preview-content h2,\r\n.team-preview-dialog .preview-content h2 {\r\n  color: #2c3e50;\r\n  border-bottom: 2px solid #3498db;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content h3,\r\n.about-preview-dialog .preview-content h3,\r\n.team-preview-dialog .preview-content h3 {\r\n  color: #34495e;\r\n  margin-top: 25px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content p,\r\n.about-preview-dialog .preview-content p,\r\n.team-preview-dialog .preview-content p {\r\n  margin-bottom: 12px;\r\n  color: #555;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content ul,\r\n.about-preview-dialog .preview-content ul,\r\n.team-preview-dialog .preview-content ul {\r\n  margin-bottom: 15px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content li,\r\n.about-preview-dialog .preview-content li,\r\n.team-preview-dialog .preview-content li {\r\n  margin-bottom: 8px;\r\n  color: #666;\r\n}\r\n\r\n.privacy-preview-dialog .preview-content strong,\r\n.about-preview-dialog .preview-content strong,\r\n.team-preview-dialog .preview-content strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-wrapper {\r\n    padding: 8px;\r\n  }\r\n\r\n  .page-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .upload-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .upload-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-toolbar {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    justify-content: center;\r\n  }\r\n\r\n  .toolbar-right {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-insert-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .privacy-preview-dialog,\r\n  .about-preview-dialog,\r\n  .team-preview-dialog {\r\n    width: 95% !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}