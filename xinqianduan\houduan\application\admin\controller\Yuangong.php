<?php
namespace app\admin\controller;
use think\Request;
use untils\{JsonService,JwtAuth};
use models\{Yuangongs,Zhiweis,Quanxians};

class Yuangong
{
    protected $model;
    public function __construct(Yuangongs $model){
        //parent::__construct();
        $this->model=$model;
        
    }
    public function index(Request $request,$page=1,$size=20){
       
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        $res = $this->model::withAttr('zhiwei_id',function($v,$d){
            return Zhiweis::where('id',$v)->value('title');
        })
        ->where($where)
        ->where(['is_delete'=>0])
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        if(empty($form['id'])) $form['pwd']=md5(md5(888888).'fdb');
        
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);
        if(empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功',$res);
    }
    public function chongzhi(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        $form['password']=md5(md5(888888).'fdb');
        
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }
    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->where(['id'=>$id])->update(['is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function getList(){
        $res = $this->model->where(['is_delete'=>0])->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
    public function getMoreList(){
        $model = new Zhiweis();
        $res = $model::withAttr('options',function($v,$d){
            return Yuangongs::field('title as label,id as value')->where('zhiwei_id',$d['id'])->select();
        })
        ->field('id,title as label')
        ->append(['options'])
        ->select();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
    public function quanxian(Request $request){
        $post = $request->post();
        $token = $post['token'];
        $res = JwtAuth::getInfoByToken($token);
       
        if(empty($res->admin_id))  return JsonService::fail('登录已失效,请重新登录');
        $id = $res->admin_id;
       
        $info = $this->model->find($id);
        if(empty($info))  return JsonService::fail('账号不存在');
        $zhiwei = Zhiweis::where(['id'=>$info['zhiwei_id']])->find();
        if(empty($zhiwei['quanxian'])) return  JsonService::fail('');
        $quanxian = $zhiwei['quanxian'];
        $dealList = [];
        foreach ($quanxian as $k=>$v){
            $dealList[$v[0]][]=$v[1];
        }
        $list = [];
        foreach ($dealList as $k=>$v){
            $res = Quanxians::where(['id'=>$k])->find()->toArray();
            $res['children']=Quanxians::where('id','in',$v)->select()->toArray();
           
            $list[] = $res;
                    
        }
       return JsonService::successful('ok',$list);
       
    }
}
