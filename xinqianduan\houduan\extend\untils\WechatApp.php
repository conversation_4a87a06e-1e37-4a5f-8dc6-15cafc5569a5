<?php
/**
 * package 客户小程序端微信授权控制器
 * author [stms] <[<EMAIL>]>
 * date:2020.03.09
 * 
 */

namespace untils;
class WechatApp{

	private static $wechat_config = [
        'appid'     => 'wxed96286a6f9879e7',
        'appsecret'     =>'9bbe7430182f91cee4772e5b7b8879ec'
    ];

    //自动加载
    public function __construct(){

    }

     /**
     * 根据前台传code  获取 openid 和  session_key //会话密匙
     * @param string $code
     * @return array|mixed
     */
    public static function _setCode($code = ''){
        if($code == '') return [];
        $url = 'https://api.weixin.qq.com/sns/jscode2session?appid='.self::$wechat_config['appid'].'&secret='.self::$wechat_config['appsecret'].'&js_code='.$code.'&grant_type=authorization_code';
        return self::_request($url);
        
    }
    /**
     * [_getAccessToken 获取access同行]
     * @return [type] [description]
     */
    public static function _getAccessToken(){
		$appid = self::$wechat_config['appid'];
		$appsecret=self::$wechat_config['appsecret'];
	    $str = file_get_contents('../access_token.json');
	    if(!empty($str)){
   			$data = json_decode($str,true);
   			if(!empty($data['expires_in'])){
   				if($data['expires_in']*1>time()){
   				  return $data['access_token'];
   				}
   			}
   		}
		$url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=".$appid."&secret=".$appsecret;
		$result = self::_request($url);
	    
		return $result['access_token'];
	}
	
	/**
     * @explain
     * 通过code获取用户openid以及用户的微信号信息
     * @return array|mixed
     * @remark
     * 获取到用户的openid之后可以判断用户是否有数据，可以直接跳过获取access_token,也可以继续获取access_token
     * access_token每日获取次数是有限制的，access_token有时间限制，可以存储到数据库7200s. 7200s后access_token失效
     **/
    public static function _getUserInfo($openid)
    {
        $access_token = self::_getaccessToken();
       
        $url = 'https://api.weixin.qq.com/sns/userinfo?access_token='.$access_token.'&openid='.$openid.'&lang=zh_CN';
        $userinfo = self::_request($url);
        
        //获取用户的基本信息，并将用户的唯一标识保存在session中
        if(!$userinfo){
            return [
                'code' => 0,
                'msg' => '获取用户信息失败！', 
            ];
        }
        
        return json_decode($userinfo,true);
    }	
	/**
	 * [_getphone 获取手机信息]
	 * @param  [type] $sessionKey    [description]
	 * @param  [type] $encryptedData [description]
	 * @param  [type] $iv            [description]
	 * @return [type]                [description]
	 */
	public static function _getphone($sessionKey,$encryptedData,$iv){
		$appid=self::$wechat_config['appid'];
	
		$aesKey=base64_decode($sessionKey);
	
		$aesIV=base64_decode($iv);
	
		$aesCipher=base64_decode($encryptedData);
		$result=openssl_decrypt( $aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);
		$dataObj=json_decode( $result );
		if($dataObj->watermark->appid != $appid) return [];
		return json_decode($result,true);
		
	}
	/**
	 * [_getqrcode 生成小程序二维码]
	 * @param  [type] $uid [description]
	 * @return [type]      [description]
	 */
	public static function _getqrcode($scene){
	    //header('content-type:image/jpg');
		$access_token = self::_getAccessToken();
		$data['scene'] = $scene;
		$data['page'] = "pages/mine/bind";
		$data['width'] = 430;
		$color['r'] = 0;
        $color['g'] = 0;
        $color['b'] = 0;
        $data['auto_color'] = false;
        $data['line_color'] = $color;
        $data['is_hyaline'] = false;
    	$url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" . $access_token;
		$resCode = Self::_curlPost($url,json_encode($data));
	  
	    $newFilePath='/qrcode/'.$scene.'.jpg';
	    file_put_contents(".".$newFilePath,$resCode);
	    
	    return $newFilePath;
	}
		/**
     * curl post
     * @param string $url
     * @param string $postData
     * @param array $options
     * @return mixed
     */
    public static function _curlPost($url = '', $postData = '', $options = array())
    {
        if (is_array($postData)) {
            $postData = http_build_query($postData);
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); //设置cURL允许执行的最长秒数
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }
    /**
     * [_request curl请求连接]
     * @param  [type] $url     [description]
     * @param  [type] $options [description]
     * @return [type]          [description]
     */
    protected static function _request($url,$options=null){
		$ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return json_decode($data,true);
	}
    /**
     * 模板消息 发送
     * @param $touser
     * @param $arr
     */
    public static function send_temp($openid, $arr)
    {

        $access_token = self::_getaccessToken();

        if (!empty($access_token)) {
            $send_url = 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=' .$access_token;
            $send_data = [
                'touser' => $openid,//微信小程序openid
                'mp_template_msg' => [
                    'appid' => 'wx82309797155b7b16',//公众号appid
                    'template_id' => '1d7HeBs5-LFdZkJCpTAxzjaRfc9DwSmTuC6X9YWEPuQ',//模板id
                    'url' => 'http://weixin.qq.com/download',//发送后用户点击跳转的链接
                    'miniprogram' => [ //与公众号绑定的小程序（选传）
                        'appid' => self::$wechat_config['appid'],//小程序id
                        'path' => 'pages/mine/debt',//跳转页面
                    ],
                    'data' => $arr
                ],
            ];
            $send_data_decode = json_encode($send_data, true);
            $resCode = Self::_curlPost($send_url,$send_data_decode);
            return $resCode;
        }
    }

}