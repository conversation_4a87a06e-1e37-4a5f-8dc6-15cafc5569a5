{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_globals", "_t", "t", "_cache", "_visitors", "NOT_LOCAL_BINDING", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isCallExpression", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMemberExpression", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "buildUndefinedNode", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "scope", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "ReferencedIdentifier", "state", "references", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "Object", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "bindings", "CatchClause", "Function", "params", "param", "isFunctionExpression", "has", "ClassExpression", "TSTypeAnnotation", "skip", "uid", "<PERSON><PERSON>", "constructor", "block", "labels", "inited", "globals", "uids", "data", "crawling", "cached", "scopeCache", "set", "Map", "_parent", "_path", "shouldSkip", "<PERSON><PERSON><PERSON>", "parentPath", "isScope", "parentBlock", "hub", "traverse", "opts", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "_generateUid", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "_renameFromMap", "map", "dump", "sep", "repeat", "console", "log", "violations", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "registerConstantViolation", "ids", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "right", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "expressions", "tag", "noGlobals", "quasi", "isStringLiteral", "setData", "val", "getData", "removeData", "init", "crawl", "create", "programParent", "isExplodedVisitor", "visit", "enter", "call", "typeVisitors", "ref", "getPatternParent", "isBlockStatement", "isProgram", "isSwitchStatement", "unique", "isFunction", "pushContainer", "isLoop", "isCatchClause", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "Error", "isFunctionParent", "isBlockParent", "getAllBindings", "getAllBindingsOfKind", "kinds", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "noUids", "includes", "contextVariables", "parentHasBinding", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "exports", "default", "builtin"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport type { TraverseOptions } from \"../index.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globals from \"globals\";\nimport {\n  NOT_LOCAL_BINDING,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isCallExpression,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMemberExpression,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n  buildUndefinedNode,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport { isExplodedVisitor } from \"../visitors.ts\";\n\ntype NodePart = string | number | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\n//\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n  TSTypeAnnotation(path) {\n    path.skip();\n  },\n};\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport { Scope as default };\nclass Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  labels;\n  inited;\n\n  bindings: { [name: string]: Binding };\n  references: { [name: string]: true };\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  uids: { [name: string]: boolean };\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = Object.keys(globals.builtin);\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path?.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  get parentBlock() {\n    return this.path.parent;\n  }\n\n  get hub() {\n    return this.path.hub;\n  }\n\n  traverse<S>(\n    node: t.Node | t.Node[],\n    opts: TraverseOptions<S>,\n    state: S,\n  ): void;\n  traverse(node: t.Node | t.Node[], opts?: TraverseOptions, state?: any): void;\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  traverse<S>(node: any, opts: any, state?: S) {\n    traverse(node, opts, this, state, this.path);\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name)\n      .replace(/^_+/, \"\")\n      .replace(/[0-9]+$/g, \"\");\n\n    let uid;\n    let i = 1;\n    do {\n      uid = this._generateUid(name, i);\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    program.references[uid] = true;\n    program.uids[uid] = true;\n\n    return uid;\n  }\n\n  /**\n   * Generate an `_id1`.\n   */\n\n  _generateUid(name: string, i: number) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  /** @deprecated Not used in our codebase */\n  _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  toArray(\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.hub.addHelper(helperName), args);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return buildUndefinedNode();\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getBindingIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      parent.references[name] = true;\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          local.reassign(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.uids[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    return !!this.getProgramParent().references[name];\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", { noGlobals: true }) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isMemberExpression(node)) {\n      return (\n        !node.computed &&\n        isIdentifier(node.object) &&\n        node.object.name === \"Symbol\" &&\n        isIdentifier(node.property) &&\n        node.property.name !== \"for\" &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true })\n      );\n    } else if (isCallExpression(node)) {\n      return (\n        matchesPattern(node.callee, \"Symbol.for\") &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true }) &&\n        node.arguments.length === 1 &&\n        t.isStringLiteral(node.arguments[0])\n      );\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    this.references = Object.create(null);\n    this.bindings = Object.create(null);\n    this.globals = Object.create(null);\n    this.uids = Object.create(null);\n    this.data = Object.create(null);\n\n    const programParent = this.getProgramParent();\n    if (programParent.crawling) return;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\" && isExplodedVisitor(collectorVisitor)) {\n      for (const visit of collectorVisitor.enter) {\n        visit.call(state, path, state);\n      }\n      const typeVisitors = collectorVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    path.traverse(collectorVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getBindingIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.LVal;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      path.isFunction() &&\n      // @ts-expect-error ArrowFunctionExpression never has a name\n      !path.node.name &&\n      isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      path.ensureBlock();\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n\n  getAllBindingsOfKind(...kinds: string[]): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?: boolean | { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    if (!name) return false;\n    let scope: Scope = this;\n    do {\n      if (scope.hasOwnBinding(name)) {\n        return true;\n      }\n    } while ((scope = scope.parent));\n\n    // TODO: Only accept the object form.\n    let noGlobals;\n    let noUids;\n    if (typeof opts === \"object\") {\n      noGlobals = opts.noGlobals;\n      noUids = opts.noUids;\n    } else if (typeof opts === \"boolean\") {\n      noGlobals = opts;\n    }\n\n    if (!noUids && this.hasUid(name)) return true;\n    if (!noGlobals && Scope.globals.includes(name)) return true;\n    if (!noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    let scope: Scope = this;\n    do {\n      if (scope.uids[name]) {\n        scope.uids[name] = false;\n      }\n    } while ((scope = scope.parent));\n  }\n}\n\ntype _Binding = Binding;\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Scope {\n  export type Binding = _Binding;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AA+CsB,IAAAK,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAN,OAAA;AAEA,IAAAO,SAAA,GAAAP,OAAA;AAAmD;EAlDjDQ,iBAAiB;EACjBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,gBAAgB;EAChBC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,kBAAkB;EAClBC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,mBAAmB;EACnBC;AAAkB,IAAAjD,EAAA;AASpB,SAASkD,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAIjC,mBAAmB,CAAC+B,IAAI,CAAC,IAAIH,mBAAmB,CAACG,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACvC,sBAAsB,CAACoC,IAAI,CAAC,IAC3BlC,wBAAwB,CAACkC,IAAI,CAAC,IAC9B/B,mBAAmB,CAAC+B,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAACnC,wBAAwB,CAACkC,IAAI,CAAC,IAAI/B,mBAAmB,CAAC+B,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACpC,0BAA0B,CAACmC,IAAI,CAAC,IAC/BlC,wBAAwB,CAACkC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI5B,iBAAiB,CAAC2B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACL/B,SAAS,CAAC8B,IAAI,CAAC,IACf,CAAC1B,aAAa,CAAC0B,IAAI,CAAC,IACpB,CAACtB,eAAe,CAACsB,IAAI,CAAC,IACtB,CAACnB,iBAAiB,CAACmB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;MACXT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AASA,MAAMyB,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEP,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDQ,WAAWA,CAACT,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACU,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIV,IAAI,CAAC3D,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAI2D,IAAI,CAAC/B,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAM0C,MAAM,GACVX,IAAI,CAACI,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIN,IAAI,CAACI,KAAK,CAACG,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDa,iBAAiBA,CAACb,IAAI,EAAE;IAEtB,MAAMW,MAAM,GAAGX,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACC,UAAU,CAACnC,IAAI,CAACkB,IAAI,CAAC;EAC7B,CAAC;EAEDkB,aAAaA,CAAClB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMzB,IAAI,GAAGS,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAIX,IAAI,CAAC4B,SAAS,CAAC,CAAC,IAAI5B,IAAI,CAACnD,YAAY,CAAC,CAAC,EAAE;MAC3C4E,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIT,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEjB,IAAI,CAAC;IAC1C;EACF,CAAC;EAED8B,iBAAiB,EAAE;IACjBC,IAAIA,CAACtB,IAAI,EAAE;MACT,MAAM;QAAE5B,IAAI;QAAEgC;MAAM,CAAC,GAAGJ,IAAI;MAE5B,IAAIhE,sBAAsB,CAACoC,IAAI,CAAC,EAAE;MAClC,MAAM6B,MAAM,GAAG7B,IAAI,CAACQ,WAAW;MAC/B,IAAI7C,kBAAkB,CAACkE,MAAM,CAAC,IAAI9D,qBAAqB,CAAC8D,MAAM,CAAC,EAAE;QAC/D,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM+B,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAAChC,EAAE,CAACN,IAAI,CAAC;QACzCqC,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAI5C,qBAAqB,CAAC6C,MAAM,CAAC,EAAE;QACxC,KAAK,MAAMyB,IAAI,IAAIzB,MAAM,CAAC0B,YAAY,EAAE;UACtC,KAAK,MAAMzC,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACrG,qBAAqB,CAACkG,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC;YACtCqC,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAED8B,gBAAgBA,CAAC9B,IAAI,EAAE;IACrBA,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAACZ,IAAI,CAAC;EACvD,CAAC;EAED+B,oBAAoBA,CAAC/B,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACgB,WAAW,CAAClD,IAAI,CAACkB,IAAI,CAAC;EAC9B,CAAC;EAEDiC,gBAAgBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;EACrC,CAAC;EAEDkC,eAAeA,CAAClC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAAC5B,IAAI,CAAC+D,QAAQ,KAAK,QAAQ,EAAE;MACnCnB,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC;EACF,CAAC;EAEDoC,WAAWA,CAACpC,IAAI,EAAE;IAChB,IAAII,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACtB,IAAIA,KAAK,CAACJ,IAAI,KAAKA,IAAI,EAAEI,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE7C,MAAMA,MAAM,GAAGP,KAAK,CAACU,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAACjE,kBAAkB,CAAC,CAAC,IAAIiE,IAAI,CAAC5B,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGQ,IAAI,CAAC5B,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpBc,IAAI,CAACI,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGc,IAAI,CAACI,KAAK,CAACO,MAAM,CAACa,UAAU,CAACtC,IAAI,CAAC;IAChE;EACF,CAAC;EAEDoD,WAAWA,CAACtC,IAAI,EAAE;IAChBA,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;EACzC,CAAC;EAEDuC,QAAQA,CAACvC,IAAI,EAAE;IACb,MAAMwC,MAAuB,GAAGxC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMuC,KAAK,IAAID,MAAM,EAAE;MAC1BxC,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAEiC,KAAK,CAAC;IAC5C;IAKA,IACEzC,IAAI,CAAC0C,oBAAoB,CAAC,CAAC,IAC3B1C,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC9B,IAAI,CAAC/C,iBAAiB,CAAC,EACvC;MACA2E,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED4C,eAAeA,CAAC5C,IAAI,EAAE;IACpB,IACEA,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC9B,IAAI,CAAC/C,iBAAiB,CAAC,EACvC;MACA2E,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EACD6C,gBAAgBA,CAAC7C,IAAI,EAAE;IACrBA,IAAI,CAAC8C,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAED,IAAIC,GAAG,GAAG,CAAC;AAKX,MAAMC,KAAK,CAAC;EAoBVC,WAAWA,CAACjD,IAAsC,EAAE;IAAA,KAnBpD+C,GAAG;IAAA,KAEH/C,IAAI;IAAA,KACJkD,KAAK;IAAA,KAELC,MAAM;IAAA,KACNC,MAAM;IAAA,KAENf,QAAQ;IAAA,KACRpB,UAAU;IAAA,KACVoC,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAEpF;IAAK,CAAC,GAAG4B,IAAI;IACrB,MAAMyD,MAAM,GAAGC,YAAU,CAACxD,GAAG,CAAC9B,IAAI,CAAC;IAGnC,IAAI,CAAAqF,MAAM,oBAANA,MAAM,CAAEzD,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOyD,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACvF,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAAC2E,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG9E,IAAI;IACjB,IAAI,CAAC4B,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACmD,MAAM,GAAG,IAAIS,GAAG,CAAC,CAAC;IACvB,IAAI,CAACR,MAAM,GAAG,KAAK;EACrB;EAcA,IAAIzC,MAAMA,CAAA,EAAG;IAAA,IAAAkD,OAAA;IACX,IAAIlD,MAAM;MACRX,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAAA,IAAA8D,KAAA;MAED,MAAMC,UAAU,GAAG/D,IAAI,CAACV,GAAG,KAAK,KAAK,IAAIU,IAAI,CAACgE,OAAO,KAAK,YAAY;MACtEhE,IAAI,GAAGA,IAAI,CAACiE,UAAU;MACtB,IAAIF,UAAU,IAAI/D,IAAI,CAACxD,QAAQ,CAAC,CAAC,EAAEwD,IAAI,GAAGA,IAAI,CAACiE,UAAU;MACzD,KAAAH,KAAA,GAAI9D,IAAI,aAAJ8D,KAAA,CAAMI,OAAO,CAAC,CAAC,EAAEvD,MAAM,GAAGX,IAAI;IACpC,CAAC,QAAQA,IAAI,IAAI,CAACW,MAAM;IAExB,QAAAkD,OAAA,GAAOlD,MAAM,qBAANkD,OAAA,CAAQzD,KAAK;EACtB;EAEA,IAAI+D,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACnE,IAAI,CAACW,MAAM;EACzB;EAEA,IAAIyD,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACpE,IAAI,CAACoE,GAAG;EACtB;EAmBAC,QAAQA,CAAIjG,IAAS,EAAEkG,IAAS,EAAEtD,KAAS,EAAE;IAC3C,IAAAqD,cAAQ,EAACjG,IAAI,EAAEkG,IAAI,EAAE,IAAI,EAAEtD,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C;EAMAuE,6BAA6BA,CAACrF,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAACgF,qBAAqB,CAACtF,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAOjE,SAAS,CAACiE,EAAE,CAAC;EACtB;EAMAgF,qBAAqBA,CAACtF,IAAa,EAAE;IACnC,OAAOzD,UAAU,CAAC,IAAI,CAACgJ,WAAW,CAACvF,IAAI,CAAC,CAAC;EAC3C;EAMAuF,WAAWA,CAACvF,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG1B,YAAY,CAAC0B,IAAI,CAAC,CACtBwF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE1B,IAAI3B,GAAG;IACP,IAAI4B,CAAC,GAAG,CAAC;IACT,GAAG;MACD5B,GAAG,GAAG,IAAI,CAAC6B,YAAY,CAAC1F,IAAI,EAAEyF,CAAC,CAAC;MAChCA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACE,QAAQ,CAAC9B,GAAG,CAAC,IAClB,IAAI,CAAC+B,UAAU,CAAC/B,GAAG,CAAC,IACpB,IAAI,CAACgC,SAAS,CAAChC,GAAG,CAAC,IACnB,IAAI,CAACiC,YAAY,CAACjC,GAAG,CAAC;IAGxB,MAAMkC,OAAO,GAAG,IAAI,CAAC1E,gBAAgB,CAAC,CAAC;IACvC0E,OAAO,CAAChE,UAAU,CAAC8B,GAAG,CAAC,GAAG,IAAI;IAC9BkC,OAAO,CAAC3B,IAAI,CAACP,GAAG,CAAC,GAAG,IAAI;IAExB,OAAOA,GAAG;EACZ;EAMA6B,YAAYA,CAAC1F,IAAY,EAAEyF,CAAS,EAAE;IACpC,IAAInF,EAAE,GAAGN,IAAI;IACb,IAAIyF,CAAC,GAAG,CAAC,EAAEnF,EAAE,IAAImF,CAAC;IAClB,OAAQ,IAAGnF,EAAG,EAAC;EACjB;EAEA0F,sBAAsBA,CAAC9G,IAAY,EAAE+G,WAAoB,EAAE;IACzD,MAAM9G,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAAC+G,IAAI,CAAC,GAAG,CAAC;IACxB5F,EAAE,GAAGA,EAAE,CAACkF,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIS,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACV,WAAW,CAACjF,EAAE,CAAC6F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAClH,IAAY,EAAE+G,WAAoB,EAAE;IACnE,OAAO1J,UAAU,CAAC,IAAI,CAACyJ,sBAAsB,CAAC9G,IAAI,EAAE+G,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAACnH,IAAY,EAAW;IAC9B,IAAIlB,gBAAgB,CAACkB,IAAI,CAAC,IAAIrB,OAAO,CAACqB,IAAI,CAAC,IAAIN,gBAAgB,CAACM,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAIhC,YAAY,CAACgC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,EAAE;QACX,OAAOA,OAAO,CAACiE,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAAC1G,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAuG,qBAAqBA,CAACrH,IAAY,EAAEsH,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAACnH,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAAC8F,gCAAgC,CAAClH,IAAI,CAAC;MACtD,IAAI,CAACsH,QAAQ,EAAE;QACb,IAAI,CAAC5G,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAOjE,SAAS,CAACiE,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEAmG,0BAA0BA,CACxB9G,KAAc,EACd+G,IAAiB,EACjB1G,IAAY,EACZM,EAAO,EACP;IAEA,IAAIoG,IAAI,KAAK,OAAO,EAAE;IAItB,IAAI/G,KAAK,CAAC+G,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACd/G,KAAK,CAAC+G,IAAI,KAAK,KAAK,IACpB/G,KAAK,CAAC+G,IAAI,KAAK,OAAO,IACtB/G,KAAK,CAAC+G,IAAI,KAAK,QAAQ,IAEtB/G,KAAK,CAAC+G,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACzB,GAAG,CAAC0B,UAAU,CACvBtG,EAAE,EACD,0BAAyBN,IAAK,GAAE,EACjC6G,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAM3E,OAAO,GAAG,IAAI,CAACC,UAAU,CAACyE,OAAO,CAAC;IACxC,IAAI1E,OAAO,EAAE;MACX2E,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC/G,IAAI;MACpD,MAAMiH,OAAO,GAAG,IAAIC,gBAAO,CAAC7E,OAAO,EAAE0E,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAGAC,cAAcA,CACZC,GAAqC,EACrCN,OAAwB,EACxBC,OAAwB,EACxBnH,KAAc,EACd;IACA,IAAIwH,GAAG,CAACN,OAAO,CAAC,EAAE;MAChBM,GAAG,CAACL,OAAO,CAAC,GAAGnH,KAAK;MACpBwH,GAAG,CAACN,OAAO,CAAC,GAAG,IAAI;IACrB;EACF;EAEAO,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAIrG,KAAY,GAAG,IAAI;IACvB,GAAG;MACDuG,OAAO,CAACC,GAAG,CAAC,GAAG,EAAExG,KAAK,CAAC8C,KAAK,CAAC5E,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;QACpCyH,OAAO,CAACC,GAAG,CAAC,IAAI,EAAE1H,IAAI,EAAE;UACtBsG,QAAQ,EAAEjE,OAAO,CAACiE,QAAQ;UAC1BvE,UAAU,EAAEM,OAAO,CAACN,UAAU;UAC9B4F,UAAU,EAAEtF,OAAO,CAACH,kBAAkB,CAAC1C,MAAM;UAC7CkH,IAAI,EAAErE,OAAO,CAACqE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASxF,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9BgG,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAGAK,OAAOA,CACL1I,IAAY,EACZuG,CAAoB,EACpBoC,mBAAoC,EACpC;IACA,IAAI3K,YAAY,CAACgC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,YAAPA,OAAO,CAAEiE,QAAQ,IAAIjE,OAAO,CAACvB,IAAI,CAACgH,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAO5I,IAAI;MACb;IACF;IAEA,IAAI1C,iBAAiB,CAAC0C,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAIhC,YAAY,CAACgC,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO5D,cAAc,CACnBgC,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC7B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAAC2C,IAAI,CACP,CAAC;IACH;IAEA,IAAI6I,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC9I,IAAI,CAAC;IACnB,IAAIuG,CAAC,KAAK,IAAI,EAAE;MAEdsC,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAOtC,CAAC,KAAK,QAAQ,EAAE;MAChCuC,IAAI,CAACpI,IAAI,CAACvB,cAAc,CAACoH,CAAC,CAAC,CAAC;MAG5BsC,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC/C,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,CAAC;MAC5CA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAO3L,cAAc,CAAC,IAAI,CAAC8I,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAC7D;EAEArC,QAAQA,CAAC3F,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACmI,QAAQ,CAACnI,IAAI,CAAC;EAC9B;EAEAmI,QAAQA,CAACnI,IAAY,EAAE;IACrB,OAAO,IAAI,CAACiE,MAAM,CAACjD,GAAG,CAAChB,IAAI,CAAC;EAC9B;EAEAoI,aAAaA,CAACtH,IAAkC,EAAE;IAChD,IAAI,CAACmD,MAAM,CAACQ,GAAG,CAAC3D,IAAI,CAAC5B,IAAI,CAACmJ,KAAK,CAACrI,IAAI,EAAEc,IAAI,CAAC;EAC7C;EAEAY,mBAAmBA,CAACZ,IAAc,EAAE;IAClC,IAAIA,IAAI,CAACwH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACtH,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC7D,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACqE,eAAe,CAAC,SAAS,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAAC5C,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAMuE,YAAY,GAAG3B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAE0F;MAAK,CAAC,GAAG5F,IAAI,CAAC5B,IAAI;MAC1B,KAAK,MAAM6B,MAAM,IAAI0B,YAAY,EAAE;QACjC,IAAI,CAACnB,eAAe,CAClBoF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3D3F,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAID,IAAI,CAACjE,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAIiE,IAAI,CAAC5B,IAAI,CAACqJ,OAAO,EAAE;MACvB,IAAI,CAACjH,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAAC3D,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAMqL,iBAAiB,GACrB1H,IAAI,CAAC5B,IAAI,CAACuJ,UAAU,KAAK,MAAM,IAAI3H,IAAI,CAAC5B,IAAI,CAACuJ,UAAU,KAAK,QAAQ;MACtE,MAAMlJ,UAAU,GAAGuB,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAM0H,SAAS,IAAInJ,UAAU,EAAE;QAClC,MAAMoJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAACxJ,IAAI,CAACuJ,UAAU,KAAK,MAAM,IACnCC,SAAS,CAACxJ,IAAI,CAACuJ,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAACnH,eAAe,CAACqH,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAI5H,IAAI,CAAC/B,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAMgC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAAClE,kBAAkB,CAAC,CAAC,IAC3BkE,MAAM,CAAC9D,qBAAqB,CAAC,CAAC,IAC9B8D,MAAM,CAAC7C,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAACwD,mBAAmB,CAACX,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACO,eAAe,CAAC,SAAS,EAAER,IAAI,CAAC;IACvC;EACF;EAEA9B,kBAAkBA,CAAA,EAAG;IACnB,OAAOA,kBAAkB,CAAC,CAAC;EAC7B;EAEA6J,yBAAyBA,CAAC/H,IAAc,EAAE;IACxC,MAAMgI,GAAG,GAAGhI,IAAI,CAACxE,qBAAqB,CAAC,CAAC;IACxC,KAAK,MAAM0D,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACmG,GAAG,CAAC,EAAE;MAAA,IAAAC,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAACzG,UAAU,CAACtC,IAAI,CAAC,aAArB+I,gBAAA,CAAuBC,QAAQ,CAAClI,IAAI,CAAC;IACvC;EACF;EAEAQ,eAAeA,CACboF,IAAqB,EACrB5F,IAAc,EACdmI,WAAqB,GAAGnI,IAAI,EAC5B;IACA,IAAI,CAAC4F,IAAI,EAAE,MAAM,IAAIwC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAIpI,IAAI,CAAC5C,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAMiL,WAA4B,GAAGrI,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAIoI,WAAW,EAAE;QAChC,IAAI,CAAC7H,eAAe,CAACoF,IAAI,EAAE3F,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMU,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAMyH,GAAG,GAAGhI,IAAI,CAACsI,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMpJ,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACmG,GAAG,CAAC,EAAE;MACnCrH,MAAM,CAACM,UAAU,CAAC/B,IAAI,CAAC,GAAG,IAAI;MAE9B,KAAK,MAAMM,EAAE,IAAIwI,GAAG,CAAC9I,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAAC0J,aAAa,CAACrJ,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACpD,UAAU,KAAK+D,EAAE,EAAE;UAE7B,IAAI,CAACmG,0BAA0B,CAAC9G,KAAK,EAAE+G,IAAI,EAAE1G,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACTA,KAAK,CAACqJ,QAAQ,CAACC,WAAW,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC9F,QAAQ,CAACnD,IAAI,CAAC,GAAG,IAAIsJ,gBAAO,CAAC;YAChC/M,UAAU,EAAE+D,EAAE;YACdY,KAAK,EAAE,IAAI;YACXJ,IAAI,EAAEmI,WAAW;YACjBvC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA6C,SAASA,CAACrK,IAAoC,EAAE;IAC9C,IAAI,CAACiF,OAAO,CAACjF,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEAsK,MAAMA,CAACxJ,IAAY,EAAW;IAC5B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACkD,IAAI,CAACpE,IAAI,CAAC,EAAE,OAAO,IAAI;IACnC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAoE,SAASA,CAAC7F,IAAY,EAAW;IAC/B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACiD,OAAO,CAACnE,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAqE,YAAYA,CAAC9F,IAAY,EAAW;IAClC,OAAO,CAAC,CAAC,IAAI,CAACqB,gBAAgB,CAAC,CAAC,CAACU,UAAU,CAAC/B,IAAI,CAAC;EACnD;EAEAyJ,MAAMA,CAACvK,IAAY,EAAEwK,aAAuB,EAAW;IACrD,IAAIxM,YAAY,CAACgC,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACqC,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAIqH,aAAa,EAAE,OAAOrH,OAAO,CAACiE,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLtI,gBAAgB,CAACkB,IAAI,CAAC,IACtBL,cAAc,CAACK,IAAI,CAAC,IACpBN,gBAAgB,CAACM,IAAI,CAAC,IACtBJ,aAAa,CAACI,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIvC,OAAO,CAACuC,IAAI,CAAC,EAAE;MAAA,IAAAyK,gBAAA;MACxB,IAAIzK,IAAI,CAAC0K,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAACvK,IAAI,CAAC0K,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAAzK,IAAI,CAAC2K,UAAU,qBAAfF,gBAAA,CAAiBnK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAACiK,MAAM,CAACvK,IAAI,CAAC4K,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAI9M,WAAW,CAACsC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAM6K,MAAM,IAAI7K,IAAI,CAAC4K,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIjN,QAAQ,CAACyC,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAACuK,MAAM,CAACvK,IAAI,CAACmB,IAAI,EAAEqJ,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAACvK,IAAI,CAAC8K,KAAK,EAAEN,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIlN,iBAAiB,CAAC0C,IAAI,CAAC,IAAIR,iBAAiB,CAACQ,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAM+K,IAAI,IAAI/K,IAAI,CAACgL,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAEP,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIjM,kBAAkB,CAACyB,IAAI,CAAC,IAAIT,kBAAkB,CAACS,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAMiL,IAAI,IAAIjL,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAACuJ,MAAM,CAACU,IAAI,EAAET,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIpM,QAAQ,CAAC4B,IAAI,CAAC,EAAE;MAAA,IAAAkL,iBAAA;MACzB,IAAIlL,IAAI,CAACmL,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACvK,IAAI,CAACkB,GAAG,EAAEsJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAU,iBAAA,GAAAlL,IAAI,CAAC2K,UAAU,qBAAfO,iBAAA,CAAiB5K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI9B,UAAU,CAACwB,IAAI,CAAC,EAAE;MAAA,IAAAoL,iBAAA;MAE3B,IAAIpL,IAAI,CAACmL,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACvK,IAAI,CAACkB,GAAG,EAAEsJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAY,iBAAA,GAAApL,IAAI,CAAC2K,UAAU,qBAAfS,iBAAA,CAAiB9K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAIb,gBAAgB,CAACO,IAAI,CAAC,IAAIA,IAAI,CAACqL,MAAM,EAAE;QACzC,IAAIrL,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC4J,MAAM,CAACvK,IAAI,CAACW,KAAK,EAAE6J,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzL,iBAAiB,CAACiB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAACuK,MAAM,CAACvK,IAAI,CAACiB,QAAQ,EAAEuJ,aAAa,CAAC;IAClD,CAAC,MAAM,IAAI3L,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAACsL,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACf,MAAM,CAAClJ,UAAU,EAAEmJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI5L,0BAA0B,CAACoB,IAAI,CAAC,EAAE;MAC3C,OACEf,cAAc,CAACe,IAAI,CAACuL,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAAC7E,UAAU,CAAC,QAAQ,EAAE;QAAE8E,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C,IAAI,CAACjB,MAAM,CAACvK,IAAI,CAACyL,KAAK,EAAEjB,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIrM,kBAAkB,CAAC6B,IAAI,CAAC,EAAE;MACnC,OACE,CAACA,IAAI,CAACmL,QAAQ,IACdnN,YAAY,CAACgC,IAAI,CAACY,MAAM,CAAC,IACzBZ,IAAI,CAACY,MAAM,CAACE,IAAI,KAAK,QAAQ,IAC7B9C,YAAY,CAACgC,IAAI,CAACa,QAAQ,CAAC,IAC3Bb,IAAI,CAACa,QAAQ,CAACC,IAAI,KAAK,KAAK,IAC5B,CAAC,IAAI,CAAC4F,UAAU,CAAC,QAAQ,EAAE;QAAE8E,SAAS,EAAE;MAAK,CAAC,CAAC;IAEnD,CAAC,MAAM,IAAIhO,gBAAgB,CAACwC,IAAI,CAAC,EAAE;MACjC,OACEf,cAAc,CAACe,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC,IACzC,CAAC,IAAI,CAAC2F,UAAU,CAAC,QAAQ,EAAE;QAAE8E,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/CxL,IAAI,CAACiI,SAAS,CAAC3H,MAAM,KAAK,CAAC,IAC3BxD,CAAC,CAAC4O,eAAe,CAAC1L,IAAI,CAACiI,SAAS,CAAC,CAAC,CAAC,CAAC;IAExC,CAAC,MAAM;MACL,OAAOxJ,SAAS,CAACuB,IAAI,CAAC;IACxB;EACF;EAMA2L,OAAOA,CAACzK,GAAoB,EAAE0K,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAACzG,IAAI,CAACjE,GAAG,CAAC,GAAG0K,GAAG;EAC9B;EAMAC,OAAOA,CAAC3K,GAAoB,EAAO;IACjC,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMmD,IAAI,GAAGnD,KAAK,CAACmD,IAAI,CAACjE,GAAG,CAAC;MAC5B,IAAIiE,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASnD,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAOAuJ,UAAUA,CAAC5K,GAAW,EAAE;IACtB,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMmD,IAAI,GAAGnD,KAAK,CAACmD,IAAI,CAACjE,GAAG,CAAC;MAC5B,IAAIiE,IAAI,IAAI,IAAI,EAAEnD,KAAK,CAACmD,IAAI,CAACjE,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASc,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEAwJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC/G,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAACgH,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAMpK,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,IAAI,CAACiB,UAAU,GAAGW,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAChI,QAAQ,GAAGT,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI,CAAChH,OAAO,GAAGzB,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC/G,IAAI,GAAG1B,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC9G,IAAI,GAAG3B,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IAE/B,MAAMC,aAAa,GAAG,IAAI,CAAC/J,gBAAgB,CAAC,CAAC;IAC7C,IAAI+J,aAAa,CAAC9G,QAAQ,EAAE;IAE5B,MAAMxC,KAA0B,GAAG;MACjCC,UAAU,EAAE,EAAE;MACdG,kBAAkB,EAAE,EAAE;MACtBY,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACwB,QAAQ,GAAG,IAAI;IAGpB,IAAIxD,IAAI,CAAC1B,IAAI,KAAK,SAAS,IAAI,IAAAiM,2BAAiB,EAACzK,gBAAgB,CAAC,EAAE;MAClE,KAAK,MAAM0K,KAAK,IAAI1K,gBAAgB,CAAC2K,KAAK,EAAE;QAC1CD,KAAK,CAACE,IAAI,CAAC1J,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;MAChC;MACA,MAAM2J,YAAY,GAAG7K,gBAAgB,CAACE,IAAI,CAAC1B,IAAI,CAAC;MAChD,IAAIqM,YAAY,EAAE;QAChB,KAAK,MAAMH,KAAK,IAAIG,YAAY,CAACF,KAAK,EAAE;UACtCD,KAAK,CAACE,IAAI,CAAC1J,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;QAChC;MACF;IACF;IACAhB,IAAI,CAACqE,QAAQ,CAACvE,gBAAgB,EAAEkB,KAAK,CAAC;IACtC,IAAI,CAACwC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMxD,IAAI,IAAIgB,KAAK,CAACgB,WAAW,EAAE;MAEpC,MAAMgG,GAAG,GAAGhI,IAAI,CAACxE,qBAAqB,CAAC,CAAC;MACxC,KAAK,MAAM0D,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACmG,GAAG,CAAC,EAAE;QACnC,IAAIhI,IAAI,CAACI,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC,EAAE;QACjCoL,aAAa,CAAC7B,SAAS,CAACT,GAAG,CAAC9I,IAAI,CAAC,CAAC;MACpC;MAGAc,IAAI,CAACI,KAAK,CAAC2H,yBAAyB,CAAC/H,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAM4K,GAAG,IAAI5J,KAAK,CAACC,UAAU,EAAE;MAClC,MAAMM,OAAO,GAAGqJ,GAAG,CAACxK,KAAK,CAACoB,UAAU,CAACoJ,GAAG,CAACxM,IAAI,CAACc,IAAI,CAAC;MACnD,IAAIqC,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAACmJ,GAAG,CAAC;MACxB,CAAC,MAAM;QACLN,aAAa,CAAC7B,SAAS,CAACmC,GAAG,CAACxM,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAM4B,IAAI,IAAIgB,KAAK,CAACI,kBAAkB,EAAE;MAC3CpB,IAAI,CAACI,KAAK,CAAC2H,yBAAyB,CAAC/H,IAAI,CAAC;IAC5C;EACF;EAEAlB,IAAIA,CAACwF,IAMJ,EAAE;IACD,IAAItE,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;MACpBnB,IAAI,GAAG,IAAI,CAAC6K,gBAAgB,CAAC,CAAC,CAAC7K,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAAC8K,gBAAgB,CAAC,CAAC,IAAI,CAAC9K,IAAI,CAAC+K,SAAS,CAAC,CAAC,EAAE;MACxD/K,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC,CAACd,IAAI;IACnC;IAEA,IAAIA,IAAI,CAACgL,iBAAiB,CAAC,CAAC,EAAE;MAC5BhL,IAAI,GAAG,CAAC,IAAI,CAACM,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEP,IAAI;IACnE;IAEA,MAAM;MAAEmK,IAAI;MAAEc,MAAM;MAAErF,IAAI,GAAG,KAAK;MAAEpG;IAAG,CAAC,GAAG8E,IAAI;IAM/C,IACE,CAAC6F,IAAI,IACL,CAACc,MAAM,KACNrF,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClC5F,IAAI,CAACkL,UAAU,CAAC,CAAC,IAEjB,CAAClL,IAAI,CAAC5B,IAAI,CAACc,IAAI,IACftD,gBAAgB,CAACoE,IAAI,CAACW,MAAM,EAAE;MAAExB,MAAM,EAAEa,IAAI,CAAC5B;IAAK,CAAC,CAAC,IACpD4B,IAAI,CAACW,MAAM,CAAC0F,SAAS,CAAC3H,MAAM,IAAIsB,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,IACvDtC,YAAY,CAACoD,EAAE,CAAC,EAChB;MACAQ,IAAI,CAACmL,aAAa,CAAC,QAAQ,EAAE3L,EAAE,CAAC;MAChCQ,IAAI,CAACI,KAAK,CAACI,eAAe,CACxB,OAAO,EACPR,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAIsB,IAAI,CAACoL,MAAM,CAAC,CAAC,IAAIpL,IAAI,CAACqL,aAAa,CAAC,CAAC,IAAIrL,IAAI,CAACkL,UAAU,CAAC,CAAC,EAAE;MAC9DlL,IAAI,CAACsL,WAAW,CAAC,CAAC;MAClBtL,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAMqL,UAAU,GAAGjH,IAAI,CAACkH,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGlH,IAAI,CAACkH,WAAW;IAElE,MAAMC,OAAO,GAAI,eAAc7F,IAAK,IAAG2F,UAAW,EAAC;IACnD,IAAIG,UAAU,GAAG,CAACT,MAAM,IAAIjL,IAAI,CAACiK,OAAO,CAACwB,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAMzL,MAAM,GAAGxC,mBAAmB,CAACmI,IAAI,EAAE,EAAE,CAAC;MAE5C3F,MAAM,CAACuL,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAI1L,IAAI,CAAgC2L,gBAAgB,CAClE,MAAM,EACN,CAAC1L,MAAM,CACT,CAAC;MACD,IAAI,CAACgL,MAAM,EAAEjL,IAAI,CAAC+J,OAAO,CAAC0B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAGlO,kBAAkB,CAAC8B,EAAE,EAAE2K,IAAI,CAAC;IAC/C,MAAM0B,GAAG,GAAGH,UAAU,CAACtN,IAAI,CAACuD,YAAY,CAAC7C,IAAI,CAAC8M,UAAU,CAAC;IACzD5L,IAAI,CAACI,KAAK,CAACI,eAAe,CAACoF,IAAI,EAAE8F,UAAU,CAACxL,GAAG,CAAC,cAAc,CAAC,CAAC2L,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMAtL,gBAAgBA,CAAA,EAAG;IACjB,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAAC+K,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAO3K,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAImL,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMAxL,iBAAiBA,CAAA,EAAiB;IAChC,IAAIF,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAAC+L,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAO3L,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIV,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACgM,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAO5L,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAImL,KAAK,CACb,8EACF,CAAC;EACH;EAOAjB,gBAAgBA,CAAA,EAAG;IACjB,IAAIzK,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACJ,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOf,KAAK,CAACU,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAASV,KAAK,GAAGA,KAAK,CAACO,MAAM,CAACA,MAAM;IACrC,MAAM,IAAImL,KAAK,CACb,8EACF,CAAC;EACH;EAMAG,cAAcA,CAAA,EAA4B;IACxC,MAAMjE,GAAG,GAAGpG,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIjK,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMd,GAAG,IAAIsC,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC7C,IAAI/C,GAAG,IAAI0I,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAAC1I,GAAG,CAAC,GAAGc,KAAK,CAACiC,QAAQ,CAAC/C,GAAG,CAAC;QAChC;MACF;MACAc,KAAK,GAAGA,KAAK,CAACO,MAAM;IACtB,CAAC,QAAQP,KAAK;IAEd,OAAO4H,GAAG;EACZ;EAMAkE,oBAAoBA,CAAC,GAAGC,KAAe,EAA2B;IAChE,MAAMnE,GAAG,GAAGpG,MAAM,CAACyI,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMzE,IAAI,IAAIuG,KAAK,EAAE;MACxB,IAAI/L,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMlB,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;UAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;UACpC,IAAIqC,OAAO,CAACqE,IAAI,KAAKA,IAAI,EAAEoC,GAAG,CAAC9I,IAAI,CAAC,GAAGqC,OAAO;QAChD;QACAnB,KAAK,GAAGA,KAAK,CAACO,MAAM;MACtB,CAAC,QAAQP,KAAK;IAChB;IAEA,OAAO4H,GAAG;EACZ;EAEAoE,uBAAuBA,CAAClN,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAACiO,oBAAoB,CAACnN,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAoD,UAAUA,CAACtC,IAAY,EAAuB;IAC5C,IAAIkB,KAAY,GAAG,IAAI;IACvB,IAAIkM,YAAY;IAEhB,GAAG;MACD,MAAM/K,OAAO,GAAGnB,KAAK,CAACmI,aAAa,CAACrJ,IAAI,CAAC;MACzC,IAAIqC,OAAO,EAAE;QAAA,IAAAgL,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAcpL,SAAS,CAAC,CAAC,IACzBI,OAAO,CAACqE,IAAI,KAAK,OAAO,IACxBrE,OAAO,CAACqE,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAOrE,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRrC,IAAI,KAAK,WAAW,IACpBkB,KAAK,CAACJ,IAAI,CAACkL,UAAU,CAAC,CAAC,IACvB,CAAC9K,KAAK,CAACJ,IAAI,CAACwM,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAF,YAAY,GAAGlM,KAAK,CAACJ,IAAI;IAC3B,CAAC,QAASI,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEA4H,aAAaA,CAACrJ,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAGAmN,oBAAoBA,CAACnN,IAAY,EAAgB;IAAA,IAAAuN,iBAAA;IAC/C,QAAAA,iBAAA,GAAO,IAAI,CAACjL,UAAU,CAACtC,IAAI,CAAC,qBAArBuN,iBAAA,CAAuBhR,UAAU;EAC1C;EAGAiR,uBAAuBA,CAACxN,IAAY,EAAgB;IAClD,MAAMqC,OAAO,GAAG,IAAI,CAACc,QAAQ,CAACnD,IAAI,CAAC;IACnC,OAAOqC,OAAO,oBAAPA,OAAO,CAAE9F,UAAU;EAC5B;EAEAkR,aAAaA,CAACzN,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACqJ,aAAa,CAACrJ,IAAI,CAAC;EACnC;EAQA4F,UAAUA,CACR5F,IAAY,EACZoF,IAA0D,EAC1D;IACA,IAAI,CAACpF,IAAI,EAAE,OAAO,KAAK;IACvB,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACuM,aAAa,CAACzN,IAAI,CAAC,EAAE;QAC7B,OAAO,IAAI;MACb;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAG9B,IAAIiJ,SAAS;IACb,IAAIgD,MAAM;IACV,IAAI,OAAOtI,IAAI,KAAK,QAAQ,EAAE;MAC5BsF,SAAS,GAAGtF,IAAI,CAACsF,SAAS;MAC1BgD,MAAM,GAAGtI,IAAI,CAACsI,MAAM;IACtB,CAAC,MAAM,IAAI,OAAOtI,IAAI,KAAK,SAAS,EAAE;MACpCsF,SAAS,GAAGtF,IAAI;IAClB;IAEA,IAAI,CAACsI,MAAM,IAAI,IAAI,CAAClE,MAAM,CAACxJ,IAAI,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAAC0K,SAAS,IAAI5G,KAAK,CAACK,OAAO,CAACwJ,QAAQ,CAAC3N,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAI,CAAC0K,SAAS,IAAI5G,KAAK,CAAC8J,gBAAgB,CAACD,QAAQ,CAAC3N,IAAI,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd;EAEA6N,gBAAgBA,CACd7N,IAAY,EACZoF,IAAgD,EAChD;IAAA,IAAA0I,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAACrM,MAAM,qBAAXqM,YAAA,CAAalI,UAAU,CAAC5F,IAAI,EAAEoF,IAAI,CAAC;EAC5C;EAMA2I,aAAaA,CAAC/N,IAAY,EAAEkB,KAAY,EAAE;IACxC,MAAM8M,IAAI,GAAG,IAAI,CAAC1L,UAAU,CAACtC,IAAI,CAAC;IAClC,IAAIgO,IAAI,EAAE;MACRA,IAAI,CAAC9M,KAAK,CAAC+M,gBAAgB,CAACjO,IAAI,CAAC;MACjCgO,IAAI,CAAC9M,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGgO,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAACjO,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAEAkO,aAAaA,CAAClO,IAAY,EAAE;IAAA,IAAAmO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAC7L,UAAU,CAACtC,IAAI,CAAC,aAArBmO,iBAAA,CAAuBjN,KAAK,CAAC+M,gBAAgB,CAACjO,IAAI,CAAC;IAGnD,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACkD,IAAI,CAACpE,IAAI,CAAC,EAAE;QACpBkB,KAAK,CAACkD,IAAI,CAACpE,IAAI,CAAC,GAAG,KAAK;MAC1B;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;AACF;AAAC2M,OAAA,CAAAC,OAAA,GAAAvK,KAAA;AAj+BKA,KAAK,CA2CFK,OAAO,GAAGzB,MAAM,CAACC,IAAI,CAACwB,QAAO,CAACmK,OAAO,CAAC;AA3CzCxK,KAAK,CAiDF8J,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC", "ignoreList": []}