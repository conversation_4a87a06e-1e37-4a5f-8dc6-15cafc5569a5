{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\components\\wangEnduit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\components\\wangEnduit.vue", "mtime": 1748425644022}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEUgZnJvbSAid2FuZ2VkaXRvciI7CmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7CmltcG9ydCBBbGVydE1lbnUgZnJvbSAnLi9qcy9BbGVydE1lbnUnOwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogIiIKICAgIH0sCiAgICBtZWFuQXJyYXk6IHsKICAgICAgLy8g6Ieq5a6a5LmJ6I+c5Y2VCiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9CiAgfSwKICBtb2RlbDogewogICAgcHJvcDogInZhbHVlIiwKICAgIGV2ZW50OiAiY2hhbmdlIgogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlOiBmdW5jdGlvbiAodmFsdWUpIHsKICAgICAgaWYgKHZhbHVlICE9PSB0aGlzLmVkaXRvci50eHQuaHRtbCgpKSB7CiAgICAgICAgdGhpcy5lZGl0b3IudHh0Lmh0bWwodGhpcy52YWx1ZSk7CiAgICAgIH0KICAgIH0KICAgIC8vdmFsdWXkuLrnvJbovpHmoYbovpPlhaXnmoTlhoXlrrnvvIzov5nph4zmiJHnm5HlkKzkuobkuIDkuIvlgLzvvIzlvZPniLbnu4Tku7bosIPnlKjlvpfml7blgJnvvIzlpoLmnpznu5l2YWx1Zei1i+WAvOS6hu+8jOWtkOe7hOS7tuWwhuS8muaYvuekuueItue7hOS7tui1i+e7meeahOWAvAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOm7mOiupOaciei/meS5iOWkmuiPnOWNle+8jG1lYW5BcnJheeacieWAvOS7pW1lYW5BcnJheeS4uuWHhgogICAgICBkZWZhdWx0TWVhbnVzOiBbImhlYWQiLCAiYm9sZCIsICJmb250U2l6ZSIsICJmb250TmFtZSIsICJpdGFsaWMiLCAidW5kZXJsaW5lIiwgInN0cmlrZVRocm91Z2giLCAiaW5kZW50IiwgImxpbmVIZWlnaHQiLCAiZm9yZUNvbG9yIiwgImJhY2tDb2xvciIsICJsaW5rIiwgImxpc3QiLCAianVzdGlmeSIsICJxdW90ZSIsICJlbW90aWNvbiIsICJpbWFnZSIsICJ2aWRlbyIsICJ0YWJsZSIsICJjb2RlIiwgInNwbGl0TGluZSIsICJ1bmRvIiwgInJlZG8iLCAiYWxlcnQiXSwKICAgICAgZWRpdG9yOiAiIiwKICAgICAgZmlsZU5hbWU6ICIiCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgaW5pdCgpIHsKICAgICAgY29uc3QgX3RoaXMgPSB0aGlzOwogICAgICB3aW5kb3cuaGFuZGxlRmlsZUNoYW5nZSA9IHRoaXMuaGFuZGxlRmlsZUNoYW5nZTsKICAgICAgdGhpcy5lZGl0b3IgPSBuZXcgRSh0aGlzLiRyZWZzLmVkaXRvcik7CiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy51cGxvYWRJbWdTaG93QmFzZTY0ID0gZmFsc2U7IC8vIOS9v+eUqCBiYXNlNjQg5L+d5a2Y5Zu+54mHCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy51cGxvYWRJbWdTZXJ2ZXIgPSAiL2FkbWluL1VwbG9hZC91cGRhdGVXYW5nIjsKICAgICAgdGhpcy5lZGl0b3IuY29uZmlnLnVwbG9hZEZpbGVOYW1lID0gImZpbGUiOwogICAgICB0aGlzLnNldE1lbnVzKCk7IC8v6K6+572u6I+c5Y2VCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5vbmNoYW5nZSA9IGh0bWwgPT4gewogICAgICAgIF90aGlzLiRlbWl0KCJjaGFuZ2UiLCBodG1sKTsgLy8g5bCG5YaF5a655ZCM5q2l5Yiw54i257uE5Lu25LitCiAgICAgIH07CiAgICAgIHRoaXMuZWRpdG9yLm1lbnVzLmV4dGVuZCgnYWxlcnRNZW51JywgQWxlcnRNZW51KTsgLy8g6YWN572u5omp5bGV55qE6I+c5Y2VCiAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cyA9IHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cy5jb25jYXQoJ2FsZXJ0TWVudScpOwogICAgICB0aGlzLmVkaXRvci5jcmVhdGUoKTsgLy/liJvlu7rnvJbovpHlmagKICAgIH0sCiAgICBoYW5kbGVGaWxlQ2hhbmdlKGUpIHsKICAgICAgLy9sZXQgYWdlbnRJbmZvID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiYWdlbnRJbmZvIikpOwogICAgICBsZXQgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgLy8gZm9yIChsZXQgaSA9IDA7IGkgPCBlLmZpbGVzLmxlbmd0aDsgaSsrKSB7CiAgICAgIC8vICAgLy8gZm9ybURhdGEuYXBwZW5kKGZpbGUubmFtZSxmaWxlKTsKICAgICAgLy8gICBsZXQgZmlsZSA9IGUuZmlsZXNbaV07CiAgICAgIC8vICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlc1snK2krJ10nLCBmaWxlLCBmaWxlLm5hbWUpOwogICAgICAvLyB9CgogICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBlLmZpbGVzWzBdKTsKICAgICAgZm9ybURhdGEuYXBwZW5kKCduYW1lJywgZS5maWxlc1swXVsnbmFtZSddKTsKICAgICAgdGhpcy5maWxlTmFtZSA9IGUuZmlsZXNbMF1bJ25hbWUnXTsKICAgICAgbGV0IGNvbmZpZyA9IHsKICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnCiAgICAgICAgfQogICAgICB9OwogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGF4aW9zLnBvc3QoIi9hZG1pbi91cGxvYWQvdXBsb2FkRmlsZSIsIGZvcm1EYXRhLCBjb25maWcpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIC8v5raI5oGv5p+l6K+iCiAgICAgICAgbGV0IHJlc3VsdCA9IHJlcy5jb2RlOwogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgaWYgKHJlc3VsdCA9PSAiNDAwIikgewogICAgICAgICAgdGhhdC4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlLOWOn+WboDonICsgcmVzLm1zZyk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoYXQuZWRpdG9yLnR4dC5hcHBlbmQoJzxhIHRhcmdldD0iX2JsYW5rIiBzdHlsZT0iY29sb3I6Ymx1ZSIgZG93bmxvYWQgaHJlZj1cJycgKyByZXMuZGF0YS51cmwgKyAnXCc+JyArIHRoYXQuZmlsZU5hbWUgKyAnPC9hPjxicj4nKTsKICAgICAgICB9CgogICAgICAgIC8v5bCG6L+U5Zue55qE5pWw5o2uIGFwcGVuZOWcqOWvjOaWh+acrOWQjumdogogICAgICB9KTsKICAgIH0sCiAgICBzZXRNZW51cygpIHsKICAgICAgLy8g6K6+572u6I+c5Y2VCiAgICAgIGlmICh0aGlzLm1lYW5BcnJheSkgewogICAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cyA9IHRoaXMubWVhbkFycmF5OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZWRpdG9yLmNvbmZpZy5tZW51cyA9IHRoaXMuZGVmYXVsdE1lYW51czsKICAgICAgfQogICAgfSwKICAgIGdldEh0bWwoKSB7CiAgICAgIC8vIOW+l+WIsOaWh+acrOWGheWuuQogICAgICByZXR1cm4gdGhpcy5lZGl0b3IudHh0Lmh0bWwoKTsKICAgIH0sCiAgICBzZXRIdG1sKHR4dCkgewogICAgICAvLyDorr7nva7lr4zmlofmnKzph4zpnaLnmoTlgLwKICAgICAgdGhpcy5lZGl0b3IudHh0Lmh0bWwodHh0KTsKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICB0aGF0LiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIHRoYXQuaW5pdCgpOwogICAgfSk7CiAgfQp9Ow=="}, {"version": 3, "names": ["E", "axios", "Alert<PERSON>enu", "props", "value", "type", "String", "default", "meanArray", "Array", "model", "prop", "event", "watch", "editor", "txt", "html", "data", "defaultMeanus", "fileName", "methods", "init", "_this", "window", "handleFileChange", "$refs", "config", "uploadImgShowBase64", "uploadImgServer", "uploadFileName", "setMenus", "onchange", "$emit", "menus", "extend", "concat", "create", "e", "formData", "FormData", "append", "files", "headers", "that", "post", "then", "res", "result", "code", "console", "log", "$message", "error", "msg", "url", "getHtml", "setHtml", "mounted", "$nextTick"], "sources": ["src/components/wangEnduit.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"editor\"></div>\r\n</template>\r\n\r\n<script>\r\nimport E from \"wangeditor\";\r\nimport axios from \"axios\";\r\nimport AlertMenu from './js/AlertMenu'\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    meanArray: {\r\n      // 自定义菜单\r\n      type: Array,\r\n      default: null,\r\n    },\r\n  },\r\n  model: {\r\n    prop: \"value\",\r\n    event: \"change\",\r\n  },\r\n  watch: {\r\n    value: function (value) {\r\n      if (value !== this.editor.txt.html()) {\r\n        this.editor.txt.html(this.value);\r\n      }\r\n    },\r\n    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值\r\n  },\r\n  data() {\r\n    return {\r\n      // 默认有这么多菜单，meanArray有值以meanArray为准\r\n      defaultMeanus: [\r\n        \"head\",\r\n        \"bold\",\r\n        \"fontSize\",\r\n        \"fontName\",\r\n        \"italic\",\r\n        \"underline\",\r\n        \"strikeThrough\",\r\n        \"indent\",\r\n        \"lineHeight\",\r\n        \"foreColor\",\r\n        \"backColor\",\r\n        \"link\",\r\n        \"list\",\r\n        \"justify\",\r\n        \"quote\",\r\n        \"emoticon\",\r\n        \"image\",\r\n        \"video\",\r\n        \"table\",\r\n        \"code\",\r\n        \"splitLine\",\r\n        \"undo\",\r\n        \"redo\",\r\n        \"alert\",\r\n        \r\n      ],\r\n      editor: \"\",\r\n      fileName:\"\"\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const _this = this;\r\n      window.handleFileChange = this.handleFileChange;\r\n      this.editor = new E(this.$refs.editor);\r\n      this.editor.config.uploadImgShowBase64 = false; // 使用 base64 保存图片\r\n      this.editor.config.uploadImgServer = \"/admin/Upload/updateWang\";\r\n      this.editor.config.uploadFileName = \"file\";\r\n      this.setMenus(); //设置菜单\r\n      this.editor.config.onchange = (html) => {\r\n        _this.$emit(\"change\", html); // 将内容同步到父组件中\r\n      };\r\n      this.editor.menus.extend('alertMenu', AlertMenu)  // 配置扩展的菜单\r\n      this.editor.config.menus = this.editor.config.menus.concat('alertMenu')\r\n      this.editor.create(); //创建编辑器\r\n      \r\n    },\r\n    handleFileChange(e){\r\n      //let agentInfo = JSON.parse(localStorage.getItem(\"agentInfo\"));\r\n      let formData = new FormData();\r\n      // for (let i = 0; i < e.files.length; i++) {\r\n      //   // formData.append(file.name,file);\r\n      //   let file = e.files[i];\r\n      //   formData.append('files['+i+']', file, file.name);\r\n      // }\r\n      \r\n      formData.append('file',e.files[0])\r\n      formData.append('name',e.files[0]['name'])\r\n      this.fileName = e.files[0]['name']\r\n      \r\n      let config = {\r\n          headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n          }\r\n      }\r\n\r\n      let that = this;\r\n      axios.post(\"/admin/upload/uploadFile\",formData,config).then(function (res) {  //消息查询\r\n          let result= res.code;\r\n          console.log(res)\r\n          if(result == \"400\"){\r\n            that.$message.error('文件上传失败,原因:'+res.msg);\r\n            return false;\r\n          }else{\r\n              that.editor.txt.append('<a target=\"_blank\" style=\"color:blue\" download href=\\''+res.data.url+'\\'>'+that.fileName+'</a><br>');\r\n          }\r\n          \r\n          //将返回的数据 append在富文本后面\r\n       \r\n          \r\n      })\r\n    },\r\n    setMenus() {\r\n      // 设置菜单\r\n      if (this.meanArray) {\r\n        this.editor.config.menus = this.meanArray;\r\n      } else {\r\n        this.editor.config.menus = this.defaultMeanus;\r\n        \r\n      }\r\n    },\r\n    getHtml() {\r\n      // 得到文本内容\r\n      return this.editor.txt.html();\r\n    },\r\n    setHtml(txt) {\r\n      // 设置富文本里面的值\r\n      this.editor.txt.html(txt);\r\n    },\r\n  },\r\n  mounted() {\r\n    let that = this;\r\n    that.$nextTick(function () {\r\n      that.init();\r\n    });\r\n  },\r\n};\r\n</script>\r\n"], "mappings": "AAKA,OAAAA,CAAA;AACA,OAAAC,KAAA;AACA,OAAAC,SAAA;AACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,SAAA;MACA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA;IACA;EACA;EACAG,KAAA;IACAC,IAAA;IACAC,KAAA;EACA;EACAC,KAAA;IACAT,KAAA,WAAAA,MAAA;MACA,IAAAA,KAAA,UAAAU,MAAA,CAAAC,GAAA,CAAAC,IAAA;QACA,KAAAF,MAAA,CAAAC,GAAA,CAAAC,IAAA,MAAAZ,KAAA;MACA;IACA;IACA;EACA;EACAa,KAAA;IACA;MACA;MACAC,aAAA,GACA,QACA,QACA,YACA,YACA,UACA,aACA,iBACA,UACA,cACA,aACA,aACA,QACA,QACA,WACA,SACA,YACA,SACA,SACA,SACA,QACA,aACA,QACA,QACA,QAEA;MACAJ,MAAA;MACAK,QAAA;IACA;EACA;EACAC,OAAA;IACAC,KAAA;MACA,MAAAC,KAAA;MACAC,MAAA,CAAAC,gBAAA,QAAAA,gBAAA;MACA,KAAAV,MAAA,OAAAd,CAAA,MAAAyB,KAAA,CAAAX,MAAA;MACA,KAAAA,MAAA,CAAAY,MAAA,CAAAC,mBAAA;MACA,KAAAb,MAAA,CAAAY,MAAA,CAAAE,eAAA;MACA,KAAAd,MAAA,CAAAY,MAAA,CAAAG,cAAA;MACA,KAAAC,QAAA;MACA,KAAAhB,MAAA,CAAAY,MAAA,CAAAK,QAAA,GAAAf,IAAA;QACAM,KAAA,CAAAU,KAAA,WAAAhB,IAAA;MACA;MACA,KAAAF,MAAA,CAAAmB,KAAA,CAAAC,MAAA,cAAAhC,SAAA;MACA,KAAAY,MAAA,CAAAY,MAAA,CAAAO,KAAA,QAAAnB,MAAA,CAAAY,MAAA,CAAAO,KAAA,CAAAE,MAAA;MACA,KAAArB,MAAA,CAAAsB,MAAA;IAEA;IACAZ,iBAAAa,CAAA;MACA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACA;MACA;MACA;MACA;MACA;;MAEAD,QAAA,CAAAE,MAAA,SAAAH,CAAA,CAAAI,KAAA;MACAH,QAAA,CAAAE,MAAA,SAAAH,CAAA,CAAAI,KAAA;MACA,KAAAtB,QAAA,GAAAkB,CAAA,CAAAI,KAAA;MAEA,IAAAf,MAAA;QACAgB,OAAA;UACA;QACA;MACA;MAEA,IAAAC,IAAA;MACA1C,KAAA,CAAA2C,IAAA,6BAAAN,QAAA,EAAAZ,MAAA,EAAAmB,IAAA,WAAAC,GAAA;QAAA;QACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,IAAA;QACAC,OAAA,CAAAC,GAAA,CAAAJ,GAAA;QACA,IAAAC,MAAA;UACAJ,IAAA,CAAAQ,QAAA,CAAAC,KAAA,gBAAAN,GAAA,CAAAO,GAAA;UACA;QACA;UACAV,IAAA,CAAA7B,MAAA,CAAAC,GAAA,CAAAyB,MAAA,4DAAAM,GAAA,CAAA7B,IAAA,CAAAqC,GAAA,WAAAX,IAAA,CAAAxB,QAAA;QACA;;QAEA;MAGA;IACA;IACAW,SAAA;MACA;MACA,SAAAtB,SAAA;QACA,KAAAM,MAAA,CAAAY,MAAA,CAAAO,KAAA,QAAAzB,SAAA;MACA;QACA,KAAAM,MAAA,CAAAY,MAAA,CAAAO,KAAA,QAAAf,aAAA;MAEA;IACA;IACAqC,QAAA;MACA;MACA,YAAAzC,MAAA,CAAAC,GAAA,CAAAC,IAAA;IACA;IACAwC,QAAAzC,GAAA;MACA;MACA,KAAAD,MAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAD,GAAA;IACA;EACA;EACA0C,QAAA;IACA,IAAAd,IAAA;IACAA,IAAA,CAAAe,SAAA;MACAf,IAAA,CAAAtB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}