(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1c0897a3"],{9402:function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"body-div",on:{click:function(e){t.isEmji=!1}}},[e("div",{staticClass:"content"},[e("div",{staticClass:"msglist"},[e("div",{staticClass:"wrapper"},[e("div",{staticClass:"search-wrapper"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.search,expression:"search"}],staticClass:"searchInput",attrs:{type:"text",placeholder:"搜索"},domProps:{value:t.search},on:{change:t.changeKeyword,input:function(e){e.target.composing||(t.search=e.target.value)}}}),t.isShowSeach?e("div",{staticClass:"searchInput-delete",on:{click:t.del}}):t._e()])]),e("el-tag",{on:{click:function(e){return t.showDaiban("2")}}},[t._v("群聊")]),e("el-tag",{attrs:{type:"success"},on:{click:function(e){return t.showDaiban("1")}}},[t._v("代办")]),e("ul",{staticClass:"msg-left-box"},[t._l(t.quns,(function(s,i){return e("li",{key:"qun"+i,staticClass:"sessionlist",class:{active:i===t.quliaoIndex},on:{click:function(e){return t.changeQun(i)}}},[e("div",{staticClass:"list-left"},[e("img",{staticClass:"avatar",attrs:{width:"38",height:"38",src:s.pic_path}}),s.count>0?e("span",[t._v(t._s(s.count))]):t._e()]),e("div",{staticClass:"list-right"},[e("p",{staticClass:"name"},[t._v(t._s(s.title))]),e("span",{staticClass:"time"},[t._v(t._s(s.create_time))]),e("p",{staticClass:"lastmsg"},[t._v(t._s(s.desc))]),s.count>0?e("p",{staticClass:"number-badge"},[t._v(t._s(s.count))]):t._e()])])})),t._l(t.users,(function(s,i){return e("li",{key:i,staticClass:"sessionlist",class:{active:i===t.selectId},on:{click:function(e){return t.redSession(i)}}},[e("div",{staticClass:"list-left"},[e("img",{staticClass:"avatar",attrs:{width:"42",height:"42",src:s.pic_path}}),e("span",[t._v("99")])]),e("div",{staticClass:"list-right"},[e("p",{staticClass:"name"},[t._v(t._s(s.title))]),e("span",{staticClass:"time"},[t._v(t._s(s.time))]),e("p",{staticClass:"lastmsg"},[t._v(t._s(s.content))])])])}))],2)],1),e("div",{staticClass:"chatbox"},[e("div",{staticClass:"message"},[e("header",{staticClass:"header"},[e("div",{staticClass:"friendname"},[t._v(" "+t._s(t.title)+" ")])])]),e("div",{ref:"list",staticClass:"message-wrapper",on:{scroll:function(e){return t.handleScroll()}}},t._l(t.list,(function(s,i){return e("div",{key:i,staticClass:"msg-box"},[e("div",{staticClass:"msg-time"},[e("span",[t._v(t._s(s.create_time))])]),e("div",{class:["chatMsg-box",{oneself:s.yuangong_id==t.yon_id}]},[e("div",{staticClass:"chat-name"},[t._v(" "+t._s(s.title)+" ")]),e("div",{class:["chatMsg-flex"]},[e("div",{staticClass:"flex-view"},[e("img",{attrs:{src:s.avatar}})]),e("div",{staticClass:"flex-view"},["image"==s.type?e("div",{staticClass:"chatMsg-img"},[e("img",{attrs:{src:s.content},on:{click:function(e){return t.openImg(s.content)}}})]):t._e(),"text"==s.type?e("div",{staticClass:"chatMsg-content"},[t._v(" "+t._s(s.content)+" ")]):t._e(),"voice"==s.type?e("div",{staticClass:"chatMsg-content"},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("audioplay",{attrs:{recordFile:s.content}}),e("div",[t._v(t._s(s.datas))])],1)]):t._e(),"file"==s.type?e("div",{staticClass:"file-box"},[e("div",{staticClass:"file-flex",on:{click:function(e){return t.openFile(s.content)}}},[e("div",{staticClass:"file-name"},[t._v(t._s(s.files.name))]),e("div",{staticClass:"file-size"},[t._v(t._s(s.files.size))])]),t._m(0,!0)]):t._e()])])])])})),0),e("div",{staticStyle:{position:"absolute",top:"14px",right:"20px","font-size":"32px",cursor:"pointer"},on:{click:t.quanyuan}},[t._v("···")]),1==t.la?e("div",{staticStyle:{position:"absolute","overflow-y":"auto",width:"200px",background:"#f2f2f2",height:"690px",right:"-200px",top:"0px"}},[e("div",{},[e("div",{staticClass:"chat-list-box"},[e("div",{staticClass:"chat-list-title"},[t._v(" 用户 ")]),e("div",{staticClass:"chat-list"},t._l(t.userss,(function(s,i){return e("div",{key:i,staticClass:"chat-flex"},t._l(s.list,(function(s,i){return e("div",{key:i},[e("img",{attrs:{src:s.headimg}}),e("div",{staticClass:"sl"},[t._v(t._s(s.nickname))])])})),0)})),0)]),t._l(t.yuangongss,(function(s,i){return e("div",{key:i,staticClass:"chat-list-box"},[e("div",{staticClass:"chat-list-title"},[t._v(" "+t._s(s.zhiwei)+" ")]),e("div",{staticClass:"chat-list"},t._l(s.list,(function(s,i){return e("div",{key:i,staticClass:"chat-flex"},[e("img",{attrs:{src:s.pic_path}}),e("div",{staticClass:"sl"},[t._v(t._s(s.title))])])})),0)])}))],2)]):t._e(),e("div",{staticClass:"input-box"},[e("div",{staticClass:"workbar-box"},[e("div",{staticClass:"upload-emji"},[e("img",{attrs:{src:"img/biaoqing.png",alt:""},on:{click:function(e){return e.stopPropagation(),t.openEmji.apply(null,arguments)}}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isEmji,expression:"isEmji"}],staticClass:"emji-box"},[e("div",{staticClass:"biao-box"},t._l(t.emojiData,(function(s,i){return e("div",{key:i,staticClass:"biao-flex",on:{click:function(e){return t.getEmoji(s)}}},[t._v(" "+t._s(s)+" ")])})),0)])]),e("div",{staticClass:"upload-file"},[e("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":t.handleSuccess}},[e("img",{attrs:{src:"img/insert_img.png",alt:""}})])],1),e("div",{staticClass:"upload-file",on:{click:function(e){return t.changeFile("image")}}},[e("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":t.handleSuccess1,"before-upload":t.beforeUpload}},[e("img",{attrs:{src:"img/wenjian.png",alt:""}})])],1),e("div",{staticClass:"upload-file",on:{click:t.daiban}},[e("img",{attrs:{src:"img/daiban.png",alt:""}})]),e("div",{staticClass:"upload-file",on:{click:t.showgongdan}},[e("img",{attrs:{src:"img/gongdan.png",alt:""}})])]),e("div",{staticClass:"input-text"},[e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.textContent,expression:"textContent"}],attrs:{placeholder:"您想说什么？"},domProps:{value:t.textContent},on:{input:function(e){e.target.composing||(t.textContent=e.target.value)}}})]),e("div",{staticClass:"input-btn"},[e("span",{on:{click:t.send}},[t._v("发送Enter")])])])])]),e("div",{staticClass:"img-popup",class:{"show-popup":t.isShowPopup}},[e("div",[e("div",{staticClass:"img-div"},[e("img",{attrs:{src:t.imgUlr,alt:""}})]),e("div",{staticClass:"close"},[e("span",{on:{click:function(e){t.isShowPopup=!1}}},[t._v("×")])])])]),e("el-drawer",{attrs:{title:"客户工单",visible:t.table,direction:"rtl",size:"40%"},on:{"update:visible":function(e){t.table=e}}},[e("el-table",{attrs:{data:t.gridData}},[e("el-table-column",{attrs:{property:"create_time",label:"下单日期",width:"150"}}),e("el-table-column",{attrs:{property:"title",label:"需求标题"}}),e("el-table-column",{attrs:{property:"desc",label:"需求描述"}}),e("el-table-column",{attrs:{property:"type_title",label:"下单类型"}}),e("el-table-column",{attrs:{property:"is_deal_title",label:"状态"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.editData(s.row.id)}}},[t._v("完成制作")])]}}])})],1)],1),e("el-dialog",{attrs:{title:t.title+"内容",visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm}},[e("el-form-item",{attrs:{label:"工单类型"}},[e("el-input",{attrs:{autocomplete:"off",readonly:""},model:{value:t.ruleForm.type_title,callback:function(e){t.$set(t.ruleForm,"type_title",e)},expression:"ruleForm.type_title"}})],1),e("el-form-item",{attrs:{label:"工单标题"}},[e("el-input",{attrs:{autocomplete:"off",readonly:""},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1),e("el-form-item",{attrs:{label:"工单描述"}},[e("el-input",{attrs:{autocomplete:"off",readonly:"",type:"textarea",rows:4},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1),e("el-form-item",{attrs:{label:"制作状态"}},[e("div",[e("el-radio",{attrs:{label:2},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("已完成")]),e("el-radio",{attrs:{label:1},model:{value:t.ruleForm.is_deal,callback:function(e){t.$set(t.ruleForm,"is_deal",e)},expression:"ruleForm.is_deal"}},[t._v("处理中")])],1)]),2==t.ruleForm.is_deal&&2==t.ruleForm.type?e("el-form-item",{attrs:{label:"请上传文件",prop:"file_path"}},[e("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:t.ruleForm.file_path,callback:function(e){t.$set(t.ruleForm,"file_path",e)},expression:"ruleForm.file_path"}}),e("el-button-group",[e("el-button",[e("el-upload",{attrs:{action:"/admin/Upload/uploadFile","show-file-list":!1,"on-success":t.handleSuccess1}},[t._v(" 上传 ")])],1),t.ruleForm.file_path?e("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delImage(t.ruleForm.file_path,"file_path")}}},[t._v("删除")]):t._e()],1)],1):t._e(),2==t.ruleForm.is_deal&&2!=t.ruleForm.type?e("el-form-item",{attrs:{label:"内容回复"}},[e("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:t.ruleForm.content,callback:function(e){t.$set(t.ruleForm,"content",e)},expression:"ruleForm.content"}})],1):t._e()],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("确 定")])],1)],1)],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"file-flex"},[e("img",{attrs:{src:"img/wenjian.png"}})])}];s("14d9");const l=["😀","😃","😄","😆","😅","🤣","🙂","😇","🤩","🤩","😘","😗","😚","😋","😛","🤪","🙁","😴","😷","🤮","🥵","🥵","🤢","🤢","😦","😰","😥","😱","😈","💀","🤡","👺","👿","😠","😡","😤","🥱","👻","👽","😺","😸","😹","😻","😼","😽","🙀","😿","🙈","🙉","🙊","💋","💌"];var o=l,n=function(){var t=this,e=t._self._c;return e("i",{class:0==t.isPlay?"el-icon-video-play":"el-icon-video-pause",staticStyle:{cursor:"pointer","margin-right":"10px","margin-top":"3px","font-size":"25px"},style:0==t.isPlay?"":"color: red;",attrs:{slot:"reference"},on:{click:t.autoPlay},slot:"reference"})},r=[],c={props:{recordFile:{type:String}},name:"audioplay",data(){return{isPlay:!1,myAuto:new Audio(this.recordFile)}},methods:{autoPlay(){this.isPlay=!this.isPlay,this.isPlay?(this.myAuto.play(),this.palyEnd()):(this.myAuto.pause(),this.palyEnd())},palyEnd(){this.myAuto.addEventListener("ended",()=>{this.isPlay=!1})}}},d=c,u=s("2877"),p=Object(u["a"])(d,n,r,!1,null,"51f04c58",null),h=p.exports;let g;var m={name:"chat",components:{audioplay:h},data(){return{userss:[],lvshiss:[],yuangongss:[],table:!1,gridData:"",ruleForm:"",dialogFormVisible:!1,emojiData:o,selectId:1,activeName:"first",search:"",active:!1,imgUlr:"",yon_id:0,id:0,isShowSeach:!1,type:"",lists:[],la:!1,Names:"",isShowPopup:!1,textContent:"",selectId:0,lvshiid:"4",pic_path:"",file_path:"",list:[],timer:"",users:[],quns:[],quliaoIndex:0,quliaos:[],isEmji:!1,title:"",yuanshiquns:[],yuanshiusers:[]}},methods:{editData(t){0!=t?this.getInfo(t):this.ruleForm={title:"",desc:""}},handleSucces1s(t){200==t.code?(this.$message.success("上传成功"),this.ruleForm["file_path"]=t.data.url):this.$message.error(t.msg)},getInfo(t){let e=this;this.getRequest("/gongdan/read?id="+t).then(t=>{200==t.code?(e.ruleForm=t.data,e.dialogFormVisible=!0):e.$message({type:"error",message:t.msg})})},quanyuan(){g.la=!g.la,g.postRequest("/chat/getQunMoreInfo",{id:g.id}).then(t=>{200==t.code&&(g.userss=t.data.users,g.lvshiss=t.data.lvshis,g.yuangongss=t.data.yuangongs)})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest("/gongdan/save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},showgongdan(){let t=this.quns[this.quliaoIndex]["uid"];g.table=!0,g.postRequest("/chat/gongdanList",{uid:t}).then(t=>{200==t.code&&(g.gridData=t.data)})},showDaiban(t){g.postRequest("/chat/getQun",{is_daiban:t}).then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1,g.getList())})},changeKeyword(t){let e=g.yuanshiquns,s=g.yuanshiusers,i=t.target._value;g.quns=e.filter(t=>-1!=t.title.search(i)),g.users=s.filter(t=>!i||t.title.toLowerCase().includes(i.toLowerCase()))},daiban(){let t=this.quns[this.quliaoIndex]["id"],e=1==this.quns[this.quliaoIndex]["is_daiban"]?2:1;g.postRequest("/chat/daiban",{id:t,is_daiban:e}).then(t=>{200==t.code?(g.quns[this.quliaoIndex]["is_daiban"]=e,g.$message.success(t.msg)):g.$message.error(t.msg)})},openEmji(){this.isEmji=!this.isEmji,console.log("----------------------ww2w")},changeFile(t){this.type=t},openFile(t){window.open(t,"_blank")},openImg(t){this.imgUlr=t,this.isShowPopup=!0,console.log("----------",t)},beforeUpload(t){let e=t.type;if(console.log(e,"type"),"doc"==!t.type.split("/")[1]||"docx"==!t.type.split("/")[1]||"xls"==!t.type.split("/")[1]||"ppt"==!t.type.split("/")[1]||"pdf"==!t.type.split("/")[1]||"xlsx"==!t.type.split("/")[1]||"pptx"==!t.type.split("/")[1])return this.$message({showClose:!0,message:"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件",type:"error"}),!1},handleSuccess(t){let e=this;console.log(t),200==t.code?e.sendImg(t.data.url):e.$message.error(t.msg)},handleSuccess1(t,e){200==t.code?g.sendFile(t.data.url,e):this.$message({showClose:!0,message:"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件",type:"error"})},redSession(t){this.selectId=t,this.quliaoIndex=-1,g.la=!1,g.getList()},changeQun(t){this.selectId=-1,this.quliaoIndex=t,g.quns[t].count=0,g.la=!1,g.getList()},getEmoji(t){this.textContent+=t},change(t){this.search?this.isShowSeach=!0:this.isShowSeach=!1},del(){this.search="",this.isShowSeach=!1},handleScroll(t){0==this.$refs.list.scrollTop&&console.log("这里处理加载更多")},send(){g.sendMessage(g.textContent),g.textContent=""},getList(){if(-1!=g.selectId){let t=g.users[g.selectId].id;g.title=g.users[g.selectId].title,g.postRequest("/chat/chatList",{uid:t}).then(t=>{200==t.code&&t.data.length>0&&(g.list=t.data,g.$refs.list.scrollTop=g.$refs.list.scrollHeight),g.loading=!1})}else{let t=g.quns[g.quliaoIndex].id,e=1*g.quns[g.quliaoIndex].uid.length+1*g.quns[g.quliaoIndex].lvshi_id.length+1*g.quns[g.quliaoIndex].yuangong_id.length;g.id=t,console.log(g.id),g.title=g.quns[g.quliaoIndex].title+"("+e+")",g.postRequest("/chat/qunliaoList",{qun_id:t}).then(t=>{200==t.code&&(t.data.length>0?(g.list=t.data,g.$refs.list.scrollTop=g.$refs.list.scrollHeight):g.list=[],setTimeout(()=>this.$refs.list.scrollTop=this.$refs.list.scrollHeight,0)),g.loading=!1})}},getMoreList(){if(-1!=g.selectId){let t=g.users[g.selectId].id;g.title=g.users[g.selectId].title;let e=0;g.list.length>0&&(e=g.list[g.list.length-1].id,g.postRequest("/chat/getMoreQunList",{uid:t,id:e}).then(t=>{g.getQun1(),200==t.code&&t.data.length>0&&(g.list.push(t.data),setTimeout(()=>this.$refs.list.scrollTop=this.$refs.list.scrollHeight,1e3)),g.loading=!1}))}else{let t=g.quns[g.quliaoIndex].id,e=1*g.quns[g.quliaoIndex].lvshi_id.length+1*g.quns[g.quliaoIndex].yuangong_id.length+1;g.title=g.quns[g.quliaoIndex].title+"("+e+")";let s=0;g.list.length>0?(s=g.list[g.list.length-1].id,g.postRequest("/chat/getMoreQunList",{qun_id:t,id:s}).then(t=>{g.getQun1(),200==t.code&&(g.list.push(t.data),setTimeout(()=>g.$refs.list.scrollTop=g.$refs.list.scrollHeight,1e3)),g.loading=!1})):(s=1,g.postRequest("/chat/getMoreQunList",{qun_id:t,id:s}).then(t=>{g.getQun1(),200==t.code&&(g.list.push(t.data),setTimeout(()=>g.$refs.list.scrollTop=g.$refs.list.scrollHeight,1e3)),g.loading=!1}))}},sendMessage(t){if(-1!=g.selectId){let e=g.users[g.selectId].id,s=3;g.postRequest("/chat/sendMessage",{uid:e,direction:"left",type:"text",content:t,orther_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{g.quns[g.quliaoIndex].uid;let e=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{direction:"left",type:"text",content:t,qun_id:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},sendFile(t,e){if(-1!=g.selectId){let s=3;g.postRequest("/chat/sendMessage",{direction:"left",type:"file",content:t,orther_id:s,files:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{let s=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{direction:"left",type:"file",content:t,qun_id:s,files:e}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},sendImg(t){if(-1!=g.selectId){let e=g.users[g.selectId].id,s=3;g.postRequest("/chat/sendMessage",{uid:e,direction:"left",type:"image",content:t,orther_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}else{let e=g.quns[g.quliaoIndex].uid,s=g.quns[g.quliaoIndex].id;g.postRequest("/chat/sendQunMessage",{uid:e,direction:"left",type:"image",content:t,qun_id:s}).then(t=>{200!=t.code&&g.$message.error(t.msg)})}},chatAllList(){g.postRequest("/chat/chatAllList").then(t=>{200==t.code&&(g.users=t.data,g.yuanshiusers=t.data)})},getQun(){g.postRequest("/chat/getQun").then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1,setTimeout(()=>{g.getList()},1500))})},getQun1(){g.postRequest("/chat/getQun").then(t=>{200==t.code&&(g.quns=t.data,g.yuanshiquns=t.data,g.selectId=-1)})},keyupSubmit(){let t=this;document.onkeydown=e=>{let s=window.event.keyCode;13===s&&t.send()}},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})}},beforeDestroy(){console.log("离开乐"),clearInterval(this.timer)},mounted(){g=this,g.getQun(),g.chatAllList(),g.yon_id=window.sessionStorage.getItem("spbs"),g.timer=setInterval(()=>{g.getMoreList()},1500),g.keyupSubmit()}},f=m,v=(s("f2c4"),Object(u["a"])(f,i,a,!1,null,null,null));e["default"]=v.exports},b952:function(t,e,s){},f2c4:function(t,e,s){"use strict";s("b952")}}]);
//# sourceMappingURL=chunk-1c0897a3.f77938e1.js.map