var _Object$setPrototypeOf = require("core-js-pure/features/object/set-prototype-of.js");
var _bindInstanceProperty = require("core-js-pure/features/instance/bind.js");
function _setPrototypeOf(t, e) {
  var _context;
  return (module.exports = _setPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$setPrototypeOf).call(_context) : function (t, e) {
    return t.__proto__ = e, t;
  }, module.exports.__esModule = true, module.exports["default"] = module.exports), _setPrototypeOf(t, e);
}
module.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;