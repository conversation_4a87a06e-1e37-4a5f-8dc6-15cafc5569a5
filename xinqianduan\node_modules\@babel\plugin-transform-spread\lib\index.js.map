{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperSkipTransparentExpressionWrappers", "_core", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_options$allowArrayLi", "assertVersion", "iterableIsArray", "assumption", "loose", "arrayLikeIsIterable", "allowArrayLike", "getSpreadLiteral", "spread", "scope", "t", "isIdentifier", "argument", "name", "toArray", "hasHole", "elements", "some", "el", "hasSpread", "nodes", "i", "length", "isSpreadElement", "push", "_props", "arrayExpression", "build", "props", "file", "prop", "spreadLiteral", "isArrayExpression", "callExpression", "addHelper", "visitor", "ArrayExpression", "path", "node", "first", "replaceWith", "shift", "memberExpression", "identifier", "CallExpression", "args", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "skipTransparentExprWrappers", "get", "is<PERSON><PERSON><PERSON>", "buildCodeFrameError", "contextLiteral", "buildUndefinedNode", "callee", "isMemberExpression", "temp", "maybeGenerateMemoised", "object", "assignmentExpression", "cloneNode", "thisExpression", "unshift", "NewExpression", "hub"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport { types as t } from \"@babel/core\";\nimport type { File, NodePath, Scope } from \"@babel/core\";\n\ntype ListElement = t.SpreadElement | t.Expression;\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const iterableIsArray = api.assumption(\"iterableIsArray\") ?? options.loose;\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\");\n\n  function getSpreadLiteral(\n    spread: t.SpreadElement,\n    scope: Scope,\n  ): t.Expression {\n    if (\n      iterableIsArray &&\n      !t.isIdentifier(spread.argument, { name: \"arguments\" })\n    ) {\n      return spread.argument;\n    } else {\n      return scope.toArray(spread.argument, true, arrayLikeIsIterable);\n    }\n  }\n\n  function hasHole(spread: t.ArrayExpression): boolean {\n    return spread.elements.some(el => el === null);\n  }\n\n  function hasSpread(nodes: Array<t.Node>): boolean {\n    for (let i = 0; i < nodes.length; i++) {\n      if (t.isSpreadElement(nodes[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  function push(_props: Array<ListElement>, nodes: Array<t.Expression>) {\n    if (!_props.length) return _props;\n    nodes.push(t.arrayExpression(_props));\n    return [];\n  }\n\n  function build(\n    props: Array<ListElement>,\n    scope: Scope,\n    file: File,\n  ): t.Expression[] {\n    const nodes: Array<t.Expression> = [];\n    let _props: Array<ListElement> = [];\n\n    for (const prop of props) {\n      if (t.isSpreadElement(prop)) {\n        _props = push(_props, nodes);\n        let spreadLiteral = getSpreadLiteral(prop, scope);\n\n        if (t.isArrayExpression(spreadLiteral) && hasHole(spreadLiteral)) {\n          spreadLiteral = t.callExpression(\n            file.addHelper(\n              process.env.BABEL_8_BREAKING\n                ? \"arrayLikeToArray\"\n                : \"arrayWithoutHoles\",\n            ),\n            [spreadLiteral],\n          );\n        }\n\n        nodes.push(spreadLiteral);\n      } else {\n        _props.push(prop);\n      }\n    }\n\n    push(_props, nodes);\n\n    return nodes;\n  }\n\n  return {\n    name: \"transform-spread\",\n\n    visitor: {\n      ArrayExpression(path): void {\n        const { node, scope } = path;\n        const elements = node.elements;\n        if (!hasSpread(elements)) return;\n\n        const nodes = build(elements, scope, this.file);\n        let first = nodes[0];\n\n        // If there is only one element in the ArrayExpression and\n        // the element was transformed (Array.prototype.slice.call or toConsumableArray)\n        // we know that the transformed code already takes care of cloning the array.\n        // So we can simply return that element.\n        if (\n          nodes.length === 1 &&\n          first !== (elements[0] as t.SpreadElement).argument\n        ) {\n          path.replaceWith(first);\n          return;\n        }\n\n        // If the first element is a ArrayExpression we can directly call\n        // concat on it.\n        // `[..].concat(..)`\n        // If not then we have to use `[].concat(arr)` and not `arr.concat`\n        // because `arr` could be extended/modified (e.g. Immutable) and we do not know exactly\n        // what concat would produce.\n        if (!t.isArrayExpression(first)) {\n          first = t.arrayExpression([]);\n        } else {\n          nodes.shift();\n        }\n\n        path.replaceWith(\n          t.callExpression(\n            t.memberExpression(first, t.identifier(\"concat\")),\n            nodes,\n          ),\n        );\n      },\n      CallExpression(path): void {\n        const { node, scope } = path;\n\n        const args = node.arguments as Array<ListElement>;\n        if (!hasSpread(args)) return;\n        const calleePath = skipTransparentExprWrappers(\n          path.get(\"callee\") as NodePath<t.Expression>,\n        );\n        if (calleePath.isSuper()) {\n          // NOTE: spread and classes have almost the same compat data, so this is very unlikely to happen in practice.\n          throw path.buildCodeFrameError(\n            \"It's not possible to compile spread arguments in `super()` without compiling classes.\\n\" +\n              \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n          );\n        }\n        let contextLiteral: t.Expression | t.Super = scope.buildUndefinedNode();\n        node.arguments = [];\n\n        let nodes: t.Expression[];\n        if (\n          args.length === 1 &&\n          t.isIdentifier((args[0] as t.SpreadElement).argument, {\n            name: \"arguments\",\n          })\n        ) {\n          nodes = [(args[0] as t.SpreadElement).argument];\n        } else {\n          nodes = build(args, scope, this.file);\n        }\n\n        const first = nodes.shift();\n        if (nodes.length) {\n          node.arguments.push(\n            t.callExpression(\n              t.memberExpression(first, t.identifier(\"concat\")),\n              nodes,\n            ),\n          );\n        } else {\n          node.arguments.push(first);\n        }\n\n        const callee = calleePath.node as t.MemberExpression;\n\n        if (t.isMemberExpression(callee)) {\n          const temp = scope.maybeGenerateMemoised(callee.object);\n          if (temp) {\n            callee.object = t.assignmentExpression(\n              \"=\",\n              temp,\n              // object must not be Super when `temp` is an identifier\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n              callee.object as t.Expression,\n            );\n            contextLiteral = temp;\n          } else {\n            contextLiteral = t.cloneNode(callee.object);\n          }\n        }\n\n        // We use the original callee here, to preserve any types/parentheses\n        node.callee = t.memberExpression(\n          node.callee as t.Expression,\n          t.identifier(\"apply\"),\n        );\n        if (t.isSuper(contextLiteral)) {\n          contextLiteral = t.thisExpression();\n        }\n\n        node.arguments.unshift(t.cloneNode(contextLiteral));\n      },\n\n      NewExpression(path): void {\n        const { node, scope } = path;\n        if (!hasSpread(node.arguments)) return;\n\n        const nodes = build(\n          node.arguments as Array<ListElement>,\n          scope,\n          this.file,\n        );\n\n        const first = nodes.shift();\n\n        let args: t.Expression;\n        if (nodes.length) {\n          args = t.callExpression(\n            t.memberExpression(first, t.identifier(\"concat\")),\n            nodes,\n          );\n        } else {\n          args = first;\n        }\n\n        path.replaceWith(\n          t.callExpression(path.hub.addHelper(\"construct\"), [\n            node.callee as t.Expression,\n            args,\n          ]),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,wCAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAyC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAU1B,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,qBAAA;EAChDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,eAAe,IAAAH,eAAA,GAAGF,GAAG,CAACM,UAAU,CAAC,iBAAiB,CAAC,YAAAJ,eAAA,GAAID,OAAO,CAACM,KAAK;EAC1E,MAAMC,mBAAmB,IAAAL,qBAAA,GACvBF,OAAO,CAACQ,cAAc,YAAAN,qBAAA,GAAIH,GAAG,CAACM,UAAU,CAAC,qBAAqB,CAAC;EAEjE,SAASI,gBAAgBA,CACvBC,MAAuB,EACvBC,KAAY,EACE;IACd,IACEP,eAAe,IACf,CAACQ,WAAC,CAACC,YAAY,CAACH,MAAM,CAACI,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAC,CAAC,EACvD;MACA,OAAOL,MAAM,CAACI,QAAQ;IACxB,CAAC,MAAM;MACL,OAAOH,KAAK,CAACK,OAAO,CAACN,MAAM,CAACI,QAAQ,EAAE,IAAI,EAAEP,mBAAmB,CAAC;IAClE;EACF;EAEA,SAASU,OAAOA,CAACP,MAAyB,EAAW;IACnD,OAAOA,MAAM,CAACQ,QAAQ,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;EAChD;EAEA,SAASC,SAASA,CAACC,KAAoB,EAAW;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIX,WAAC,CAACa,eAAe,CAACH,KAAK,CAACC,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA,SAASG,IAAIA,CAACC,MAA0B,EAAEL,KAA0B,EAAE;IACpE,IAAI,CAACK,MAAM,CAACH,MAAM,EAAE,OAAOG,MAAM;IACjCL,KAAK,CAACI,IAAI,CAACd,WAAC,CAACgB,eAAe,CAACD,MAAM,CAAC,CAAC;IACrC,OAAO,EAAE;EACX;EAEA,SAASE,KAAKA,CACZC,KAAyB,EACzBnB,KAAY,EACZoB,IAAU,EACM;IAChB,MAAMT,KAA0B,GAAG,EAAE;IACrC,IAAIK,MAA0B,GAAG,EAAE;IAEnC,KAAK,MAAMK,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIlB,WAAC,CAACa,eAAe,CAACO,IAAI,CAAC,EAAE;QAC3BL,MAAM,GAAGD,IAAI,CAACC,MAAM,EAAEL,KAAK,CAAC;QAC5B,IAAIW,aAAa,GAAGxB,gBAAgB,CAACuB,IAAI,EAAErB,KAAK,CAAC;QAEjD,IAAIC,WAAC,CAACsB,iBAAiB,CAACD,aAAa,CAAC,IAAIhB,OAAO,CAACgB,aAAa,CAAC,EAAE;UAChEA,aAAa,GAAGrB,WAAC,CAACuB,cAAc,CAC9BJ,IAAI,CAACK,SAAS,CAGR,mBACN,CAAC,EACD,CAACH,aAAa,CAChB,CAAC;QACH;QAEAX,KAAK,CAACI,IAAI,CAACO,aAAa,CAAC;MAC3B,CAAC,MAAM;QACLN,MAAM,CAACD,IAAI,CAACM,IAAI,CAAC;MACnB;IACF;IAEAN,IAAI,CAACC,MAAM,EAAEL,KAAK,CAAC;IAEnB,OAAOA,KAAK;EACd;EAEA,OAAO;IACLP,IAAI,EAAE,kBAAkB;IAExBsB,OAAO,EAAE;MACPC,eAAeA,CAACC,IAAI,EAAQ;QAC1B,MAAM;UAAEC,IAAI;UAAE7B;QAAM,CAAC,GAAG4B,IAAI;QAC5B,MAAMrB,QAAQ,GAAGsB,IAAI,CAACtB,QAAQ;QAC9B,IAAI,CAACG,SAAS,CAACH,QAAQ,CAAC,EAAE;QAE1B,MAAMI,KAAK,GAAGO,KAAK,CAACX,QAAQ,EAAEP,KAAK,EAAE,IAAI,CAACoB,IAAI,CAAC;QAC/C,IAAIU,KAAK,GAAGnB,KAAK,CAAC,CAAC,CAAC;QAMpB,IACEA,KAAK,CAACE,MAAM,KAAK,CAAC,IAClBiB,KAAK,KAAMvB,QAAQ,CAAC,CAAC,CAAC,CAAqBJ,QAAQ,EACnD;UACAyB,IAAI,CAACG,WAAW,CAACD,KAAK,CAAC;UACvB;QACF;QAQA,IAAI,CAAC7B,WAAC,CAACsB,iBAAiB,CAACO,KAAK,CAAC,EAAE;UAC/BA,KAAK,GAAG7B,WAAC,CAACgB,eAAe,CAAC,EAAE,CAAC;QAC/B,CAAC,MAAM;UACLN,KAAK,CAACqB,KAAK,CAAC,CAAC;QACf;QAEAJ,IAAI,CAACG,WAAW,CACd9B,WAAC,CAACuB,cAAc,CACdvB,WAAC,CAACgC,gBAAgB,CAACH,KAAK,EAAE7B,WAAC,CAACiC,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDvB,KACF,CACF,CAAC;MACH,CAAC;MACDwB,cAAcA,CAACP,IAAI,EAAQ;QACzB,MAAM;UAAEC,IAAI;UAAE7B;QAAM,CAAC,GAAG4B,IAAI;QAE5B,MAAMQ,IAAI,GAAGP,IAAI,CAACQ,SAA+B;QACjD,IAAI,CAAC3B,SAAS,CAAC0B,IAAI,CAAC,EAAE;QACtB,MAAME,UAAU,GAAG,IAAAC,oEAA2B,EAC5CX,IAAI,CAACY,GAAG,CAAC,QAAQ,CACnB,CAAC;QACD,IAAIF,UAAU,CAACG,OAAO,CAAC,CAAC,EAAE;UAExB,MAAMb,IAAI,CAACc,mBAAmB,CAC5B,yFAAyF,GACvF,2EACJ,CAAC;QACH;QACA,IAAIC,cAAsC,GAAG3C,KAAK,CAAC4C,kBAAkB,CAAC,CAAC;QACvEf,IAAI,CAACQ,SAAS,GAAG,EAAE;QAEnB,IAAI1B,KAAqB;QACzB,IACEyB,IAAI,CAACvB,MAAM,KAAK,CAAC,IACjBZ,WAAC,CAACC,YAAY,CAAEkC,IAAI,CAAC,CAAC,CAAC,CAAqBjC,QAAQ,EAAE;UACpDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACAO,KAAK,GAAG,CAAEyB,IAAI,CAAC,CAAC,CAAC,CAAqBjC,QAAQ,CAAC;QACjD,CAAC,MAAM;UACLQ,KAAK,GAAGO,KAAK,CAACkB,IAAI,EAAEpC,KAAK,EAAE,IAAI,CAACoB,IAAI,CAAC;QACvC;QAEA,MAAMU,KAAK,GAAGnB,KAAK,CAACqB,KAAK,CAAC,CAAC;QAC3B,IAAIrB,KAAK,CAACE,MAAM,EAAE;UAChBgB,IAAI,CAACQ,SAAS,CAACtB,IAAI,CACjBd,WAAC,CAACuB,cAAc,CACdvB,WAAC,CAACgC,gBAAgB,CAACH,KAAK,EAAE7B,WAAC,CAACiC,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDvB,KACF,CACF,CAAC;QACH,CAAC,MAAM;UACLkB,IAAI,CAACQ,SAAS,CAACtB,IAAI,CAACe,KAAK,CAAC;QAC5B;QAEA,MAAMe,MAAM,GAAGP,UAAU,CAACT,IAA0B;QAEpD,IAAI5B,WAAC,CAAC6C,kBAAkB,CAACD,MAAM,CAAC,EAAE;UAChC,MAAME,IAAI,GAAG/C,KAAK,CAACgD,qBAAqB,CAACH,MAAM,CAACI,MAAM,CAAC;UACvD,IAAIF,IAAI,EAAE;YACRF,MAAM,CAACI,MAAM,GAAGhD,WAAC,CAACiD,oBAAoB,CACpC,GAAG,EACHH,IAAI,EAGJF,MAAM,CAACI,MACT,CAAC;YACDN,cAAc,GAAGI,IAAI;UACvB,CAAC,MAAM;YACLJ,cAAc,GAAG1C,WAAC,CAACkD,SAAS,CAACN,MAAM,CAACI,MAAM,CAAC;UAC7C;QACF;QAGApB,IAAI,CAACgB,MAAM,GAAG5C,WAAC,CAACgC,gBAAgB,CAC9BJ,IAAI,CAACgB,MAAM,EACX5C,WAAC,CAACiC,UAAU,CAAC,OAAO,CACtB,CAAC;QACD,IAAIjC,WAAC,CAACwC,OAAO,CAACE,cAAc,CAAC,EAAE;UAC7BA,cAAc,GAAG1C,WAAC,CAACmD,cAAc,CAAC,CAAC;QACrC;QAEAvB,IAAI,CAACQ,SAAS,CAACgB,OAAO,CAACpD,WAAC,CAACkD,SAAS,CAACR,cAAc,CAAC,CAAC;MACrD,CAAC;MAEDW,aAAaA,CAAC1B,IAAI,EAAQ;QACxB,MAAM;UAAEC,IAAI;UAAE7B;QAAM,CAAC,GAAG4B,IAAI;QAC5B,IAAI,CAAClB,SAAS,CAACmB,IAAI,CAACQ,SAAS,CAAC,EAAE;QAEhC,MAAM1B,KAAK,GAAGO,KAAK,CACjBW,IAAI,CAACQ,SAAS,EACdrC,KAAK,EACL,IAAI,CAACoB,IACP,CAAC;QAED,MAAMU,KAAK,GAAGnB,KAAK,CAACqB,KAAK,CAAC,CAAC;QAE3B,IAAII,IAAkB;QACtB,IAAIzB,KAAK,CAACE,MAAM,EAAE;UAChBuB,IAAI,GAAGnC,WAAC,CAACuB,cAAc,CACrBvB,WAAC,CAACgC,gBAAgB,CAACH,KAAK,EAAE7B,WAAC,CAACiC,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDvB,KACF,CAAC;QACH,CAAC,MAAM;UACLyB,IAAI,GAAGN,KAAK;QACd;QAEAF,IAAI,CAACG,WAAW,CACd9B,WAAC,CAACuB,cAAc,CAACI,IAAI,CAAC2B,GAAG,CAAC9B,SAAS,CAAC,WAAW,CAAC,EAAE,CAChDI,IAAI,CAACgB,MAAM,EACXT,IAAI,CACL,CACH,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}