{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\Search.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\archive\\Search.vue", "mtime": 1748604247133}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Search.vue"], "names": [], "mappings": ";AAwJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Search.vue", "sourceRoot": "src/views/pages/archive", "sourcesContent": ["<template>\r\n  <div class=\"archive-search-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"关键词\">\r\n          <el-input\r\n            v-model=\"searchForm.keyword\"\r\n            placeholder=\"请输入文件名或内容关键词\"\r\n            style=\"width: 200px\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\">\r\n          <el-select v-model=\"searchForm.fileType\" placeholder=\"请选择文件类型\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"PDF文档\" value=\"pdf\" />\r\n            <el-option label=\"Word文档\" value=\"doc\" />\r\n            <el-option label=\"Excel表格\" value=\"xls\" />\r\n            <el-option label=\"图片\" value=\"image\" />\r\n            <el-option label=\"其他\" value=\"other\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"分类\">\r\n          <el-select v-model=\"searchForm.category\" placeholder=\"请选择分类\" clearable>\r\n            <el-option label=\"全部\" value=\"\" />\r\n            <el-option label=\"案件文书\" value=\"案件文书\" />\r\n            <el-option label=\"合同文件\" value=\"合同文件\" />\r\n            <el-option label=\"咨询记录\" value=\"咨询记录\" />\r\n            <el-option label=\"法律意见书\" value=\"法律意见书\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"时间范围\">\r\n          <el-date-picker\r\n            v-model=\"searchForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">\r\n            <i class=\"el-icon-search\"></i> 搜索\r\n          </el-button>\r\n          <el-button @click=\"handleReset\">\r\n            <i class=\"el-icon-refresh\"></i> 重置\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 搜索结果 -->\r\n    <div class=\"search-results\">\r\n      <div class=\"result-header\">\r\n        <span class=\"result-count\">共找到 {{ filteredFiles.length }} 个文件</span>\r\n        <div class=\"view-mode\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"list\">列表视图</el-radio-button>\r\n            <el-radio-button label=\"grid\">网格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 列表视图 -->\r\n      <div v-if=\"viewMode === 'list'\" class=\"list-view\">\r\n        <el-table :data=\"filteredFiles\" style=\"width: 100%\">\r\n          <el-table-column prop=\"fileName\" label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"getFileIcon(scope.row.fileType)\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.fileName }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"category\" label=\"分类\" width=\"120\" />\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatFileSize(scope.row.size) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"160\" />\r\n          <el-table-column label=\"操作\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" @click=\"handlePreview(scope.row)\">预览</el-button>\r\n              <el-button size=\"mini\" type=\"success\" @click=\"handleDownload(scope.row)\">下载</el-button>\r\n              <el-button size=\"mini\" type=\"info\" @click=\"handleViewDetails(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"grid-view\">\r\n        <div class=\"file-grid\">\r\n          <div\r\n            v-for=\"file in filteredFiles\"\r\n            :key=\"file.id\"\r\n            class=\"file-card\"\r\n            @click=\"handlePreview(file)\"\r\n          >\r\n            <div class=\"file-thumbnail\">\r\n              <i :class=\"getFileIcon(file.fileType)\" class=\"file-icon-large\"></i>\r\n            </div>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\" :title=\"file.fileName\">{{ file.fileName }}</div>\r\n              <div class=\"file-meta\">\r\n                <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                <span class=\"file-date\">{{ file.uploadTime.split(' ')[0] }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"filteredFiles.length\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 文件详情对话框 -->\r\n    <el-dialog title=\"文件详情\" :visible.sync=\"detailDialogVisible\" width=\"600px\">\r\n      <div v-if=\"currentFile\" class=\"file-details\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"文件名\">{{ currentFile.fileName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件类型\">{{ currentFile.fileType.toUpperCase() }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件大小\">{{ formatFileSize(currentFile.size) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"分类\">{{ currentFile.category }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"上传时间\">{{ currentFile.uploadTime }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"文件路径\">{{ currentFile.filePath || '/archive/files/' + currentFile.fileName }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownload(currentFile)\">下载文件</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { formatFileSize } from '@/utils/fileUtils'\r\n\r\nexport default {\r\n  name: 'ArchiveSearch',\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      },\r\n      viewMode: 'list',\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      detailDialogVisible: false,\r\n      currentFile: null,\r\n      // 模拟文件数据\r\n      allFiles: [\r\n        {\r\n          id: 1,\r\n          fileName: \"合同模板.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 1024000,\r\n          uploadTime: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          fileName: \"案件资料.docx\",\r\n          fileType: \"docx\",\r\n          category: \"案件文书\",\r\n          size: 512000,\r\n          uploadTime: \"2024-01-14 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          fileName: \"咨询记录.txt\",\r\n          fileType: \"txt\",\r\n          category: \"咨询记录\",\r\n          size: 8192,\r\n          uploadTime: \"2024-01-13 16:45:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          fileName: \"法律意见书.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"法律意见书\",\r\n          size: 2048000,\r\n          uploadTime: \"2024-01-12 09:15:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          fileName: \"证据清单.xlsx\",\r\n          fileType: \"xlsx\",\r\n          category: \"案件文书\",\r\n          size: 256000,\r\n          uploadTime: \"2024-01-11 15:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          fileName: \"委托协议.pdf\",\r\n          fileType: \"pdf\",\r\n          category: \"合同文件\",\r\n          size: 768000,\r\n          uploadTime: \"2024-01-10 11:20:00\"\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredFiles() {\r\n      let files = this.allFiles\r\n\r\n      // 关键词搜索\r\n      if (this.searchForm.keyword) {\r\n        const keyword = this.searchForm.keyword.toLowerCase()\r\n        files = files.filter(file => \r\n          file.fileName.toLowerCase().includes(keyword) ||\r\n          file.category.toLowerCase().includes(keyword)\r\n        )\r\n      }\r\n\r\n      // 文件类型筛选\r\n      if (this.searchForm.fileType) {\r\n        files = files.filter(file => {\r\n          if (this.searchForm.fileType === 'doc') {\r\n            return ['doc', 'docx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'xls') {\r\n            return ['xls', 'xlsx'].includes(file.fileType)\r\n          } else if (this.searchForm.fileType === 'image') {\r\n            return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(file.fileType)\r\n          } else {\r\n            return file.fileType === this.searchForm.fileType\r\n          }\r\n        })\r\n      }\r\n\r\n      // 分类筛选\r\n      if (this.searchForm.category) {\r\n        files = files.filter(file => file.category === this.searchForm.category)\r\n      }\r\n\r\n      // 时间范围筛选\r\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\r\n        const [startDate, endDate] = this.searchForm.dateRange\r\n        files = files.filter(file => {\r\n          const fileDate = file.uploadTime.split(' ')[0]\r\n          return fileDate >= startDate && fileDate <= endDate\r\n        })\r\n      }\r\n\r\n      return files\r\n    }\r\n  },\r\n  methods: {\r\n    formatFileSize,\r\n    \r\n    getFileIcon(fileType) {\r\n      const iconMap = {\r\n        'pdf': 'el-icon-document',\r\n        'doc': 'el-icon-document',\r\n        'docx': 'el-icon-document',\r\n        'xls': 'el-icon-s-grid',\r\n        'xlsx': 'el-icon-s-grid',\r\n        'txt': 'el-icon-document-copy',\r\n        'jpg': 'el-icon-picture',\r\n        'jpeg': 'el-icon-picture',\r\n        'png': 'el-icon-picture',\r\n        'gif': 'el-icon-picture'\r\n      }\r\n      return iconMap[fileType] || 'el-icon-document'\r\n    },\r\n\r\n    handleSearch() {\r\n      this.currentPage = 1\r\n      this.$message.success('搜索完成')\r\n    },\r\n\r\n    handleReset() {\r\n      this.searchForm = {\r\n        keyword: '',\r\n        fileType: '',\r\n        category: '',\r\n        dateRange: []\r\n      }\r\n      this.currentPage = 1\r\n      this.$message.info('搜索条件已重置')\r\n    },\r\n\r\n    handlePreview(file) {\r\n      this.$message.info(`预览文件: ${file.fileName}`)\r\n      // 这里可以实现文件预览功能\r\n    },\r\n\r\n    handleDownload(file) {\r\n      this.$message.success(`开始下载: ${file.fileName}`)\r\n      // 这里可以实现文件下载功能\r\n    },\r\n\r\n    handleViewDetails(file) {\r\n      this.currentFile = file\r\n      this.detailDialogVisible = true\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.pageSize = val\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.archive-search-container {\r\n  padding: 20px;\r\n}\r\n\r\n.search-section {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-results {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.result-count {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 网格视图样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.file-card {\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-card:hover {\r\n  border-color: #409EFF;\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.file-thumbnail {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-icon-large {\r\n  font-size: 48px;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-card .file-name {\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.file-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8e8e8;\r\n}\r\n\r\n.file-details {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .result-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .file-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 15px;\r\n  }\r\n}\r\n</style> "]}]}