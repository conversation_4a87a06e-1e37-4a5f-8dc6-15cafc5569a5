# 宝塔面板部署指南

## 前置准备

### 1. 服务器要求
- **操作系统**: CentOS 7+, Ubuntu 18+, Debian 9+
- **内存**: 最少1GB，推荐2GB+
- **硬盘**: 最少20GB可用空间
- **网络**: 公网IP，开放80、443、8888端口

### 2. 域名准备（可选）
- 已备案的域名
- 域名解析指向服务器IP

## 第一步：安装宝塔面板

### CentOS安装命令
```bash
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec
```

### Ubuntu/Debian安装命令
```bash
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
```

### 安装完成后
1. 记录面板地址、用户名、密码
2. 在浏览器访问面板地址
3. 首次登录需要绑定宝塔账号

## 第二步：安装运行环境

### 1. 安装LNMP环境
在宝塔面板首页点击"一键安装"：
- **Nginx**: 1.20+
- **MySQL**: 5.7+ 或 8.0
- **PHP**: 7.4+ 或 8.0+
- **phpMyAdmin**: 最新版

### 2. 安装Node.js（用于构建前端）
```bash
# 方法一：通过宝塔软件商店安装
软件商店 -> 运行环境 -> Node.js -> 安装

# 方法二：手动安装
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs
```

### 3. 验证安装
```bash
node --version
npm --version
```

## 第三步：准备项目文件

### 1. 本地构建项目
在本地开发环境执行：
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 2. 上传文件到服务器
构建完成后，将 `dist` 文件夹上传到服务器：

**方法一：宝塔文件管理器**
1. 登录宝塔面板
2. 文件 -> 进入网站根目录
3. 上传 `dist.zip` 并解压

**方法二：FTP/SFTP工具**
- 使用FileZilla、WinSCP等工具
- 上传到 `/www/wwwroot/your-domain/`

**方法三：Git部署**
```bash
# 在服务器上克隆项目
cd /www/wwwroot/
git clone https://github.com/your-username/your-project.git
cd your-project

# 安装依赖并构建
npm install
npm run build
```

## 第四步：创建网站

### 1. 添加站点
1. 宝塔面板 -> 网站 -> 添加站点
2. 填写域名（或IP地址）
3. 选择PHP版本
4. 创建数据库（如需要）

### 2. 配置网站目录
1. 点击网站设置
2. 修改网站目录为 `dist` 文件夹路径
3. 或将 `dist` 内容复制到网站根目录

### 3. 设置默认首页
在网站设置中添加：
```
index.html
index.htm
```

## 第五步：配置Nginx

### 1. 基础配置
点击网站设置 -> 配置文件，添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/your-domain/dist;
    index index.html;

    # 支持Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理（如果需要）
    location /api/ {
        proxy_pass http://your-backend-server/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

### 2. HTTPS配置（推荐）
1. 网站设置 -> SSL -> Let's Encrypt
2. 申请免费SSL证书
3. 强制HTTPS重定向

## 第六步：配置后端API

### 1. 创建API站点
如果有后端API，创建单独的站点：
```nginx
server {
    listen 80;
    server_name api.your-domain.com;
    root /www/wwwroot/api;
    
    location / {
        # PHP后端配置
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-74.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 2. 数据库配置
1. 宝塔面板 -> 数据库
2. 创建数据库和用户
3. 导入数据库文件
4. 配置后端数据库连接

## 第七步：性能优化

### 1. 开启Gzip压缩
在Nginx配置中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 配置CDN（可选）
- 使用阿里云CDN、腾讯云CDN等
- 加速静态资源访问

### 3. 监控配置
1. 宝塔面板 -> 监控
2. 设置CPU、内存、磁盘监控
3. 配置告警通知

## 第八步：安全配置

### 1. 防火墙设置
```bash
# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

### 2. 宝塔安全设置
1. 修改默认面板端口
2. 设置面板SSL
3. 开启BasicAuth认证
4. 配置IP白名单

### 3. 网站安全
1. 安装宝塔网站防火墙
2. 配置CC防护
3. 设置SQL注入防护

## 第九步：备份策略

### 1. 自动备份
1. 宝塔面板 -> 计划任务
2. 设置网站文件备份
3. 设置数据库备份
4. 配置备份到云存储

### 2. 手动备份
定期手动备份重要文件和数据库

## 常见问题解决

### 1. 404错误
- 检查网站目录是否正确
- 确认index.html文件存在
- 检查Nginx配置

### 2. 静态资源加载失败
- 检查文件路径
- 确认文件权限
- 检查Nginx配置

### 3. API请求失败
- 检查跨域配置
- 确认API服务正常
- 检查代理配置

### 4. 性能问题
- 开启Gzip压缩
- 配置静态资源缓存
- 使用CDN加速

## 维护建议

1. **定期更新**: 保持系统和软件最新
2. **监控日志**: 定期查看访问日志和错误日志
3. **性能监控**: 关注服务器性能指标
4. **安全检查**: 定期进行安全扫描
5. **备份验证**: 定期验证备份文件完整性

## 联系支持

如遇到问题，可以：
1. 查看宝塔官方文档
2. 访问宝塔社区论坛
3. 联系宝塔技术支持
