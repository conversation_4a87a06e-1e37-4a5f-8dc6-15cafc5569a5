<?php
namespace app\index\validate;

use think\Validate;

class LawCase extends Validate
{
    protected $rule =   [

      'uid'=>'require',
      'title'=>'require',
      'phone'=>'require|checkPhone:请输入正确的手机号码',
      'duifang_title'=>'require',
      'duifang_phone'=>'require|checkPhone:请输入正确的对方手机号码',
      'money'=>'require|number',
      'content'=>'require'
       
    ];
    
    protected $message  =   [
      	'uid.require' => '请先登陆',
      	'title.require'=>'名字不能为空',
      	'money.require'=>'请填写金额',
        'duifang_title.require'=>'对方名字不能为空',
      	'money.number'=>'金额必须为数字',
      	'content'=>'请填写相应内容'
      	
    ];
    
   
    protected  function checkPhone($value,$rule)
	{
		if(!preg_match("/^1[34578]\d{9}$/", $value)) return $rule;
		else return true;
			
	}
}