{"version": 3, "sources": ["webpack:///./src/views/pages/yonghu/chat.vue?086f", "webpack:///./src/views/pages/yonghu/chat.vue", "webpack:///./src/views/pages/yonghu/emoji.js", "webpack:///./src/components/audioplay.vue", "webpack:///src/components/audioplay.vue", "webpack:///./src/components/audioplay.vue?56f3", "webpack:///./src/components/audioplay.vue?721f", "webpack:///src/views/pages/yonghu/chat.vue", "webpack:///./src/views/pages/yonghu/chat.vue?d959", "webpack:///./src/views/pages/yonghu/chat.vue?57f0"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "$event", "isEmji", "directives", "name", "rawName", "value", "search", "expression", "attrs", "domProps", "target", "composing", "changeKeyword", "isShowSeach", "del", "_e", "class", "currentTab", "showDaiban", "_v", "_l", "quns", "item", "index", "key", "quliaoIndex", "selectId", "changeQun", "pic_path", "count", "_s", "title", "create_time", "desc", "users", "redSession", "time", "content", "showMemberPanel", "showUserDetail", "stopPropagation", "toggleMemberPanel", "apply", "arguments", "ref", "handleScroll", "list", "yuangong_id", "yon_id", "avatar", "type", "openImg", "datas", "openFile", "_m", "files", "size", "activeDetailTab", "currentUserDetail", "phone", "idCard", "registerTime", "lastLogin", "status", "debtors", "length", "debtor", "id", "amount", "statusText", "documents", "doc", "getDocIcon", "uploadTime", "downloadDoc", "previewDoc", "orders", "order", "createTime", "getOrderStatusType", "payments", "payment", "description", "openEmji", "emojiData", "<PERSON><PERSON><PERSON><PERSON>", "handleSuccess", "handleSuccess1", "beforeUpload", "daiban", "<PERSON><PERSON><PERSON>", "model", "textContent", "callback", "$$v", "trim", "send", "isShowPopup", "imgUlr", "table", "staticStyle", "gridData", "scopedSlots", "_u", "fn", "scope", "editData", "row", "dialogFormVisible", "ruleForm", "type_title", "$set", "is_deal", "file_path", "delImage", "slot", "saveData", "staticRenderFns", "isPlay", "style", "autoPlay", "props", "recordFile", "String", "data", "myAuto", "Audio", "methods", "play", "palyEnd", "pause", "addEventListener", "component", "_this", "components", "audioplay", "userss", "lvshiss", "yuangongss", "activeName", "active", "lists", "Names", "lv<PERSON><PERSON>", "timer", "quli<PERSON><PERSON>", "yuanshiquns", "yuanshiusers", "memberData", "lawyers", "staff", "getInfo", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "$refs", "validate", "valid", "postRequest", "getData", "uid", "is_daiban", "getList", "e", "filter", "toLowerCase", "includes", "console", "log", "changeFile", "field", "window", "open", "img", "file", "split", "showClose", "sendImg", "flie", "sendFile", "loadTestMessages", "loadUserDetailData", "change", "scrollTop", "sendMessage", "scrollHeight", "loading", "lvshi_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "orther_id", "direction", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "fileName", "loadTestData", "loadPrivateMessages", "qun", "loadGroupMessages", "$nextTick", "groupMessages", "privateMessages", "loadMemberData", "current<PERSON><PERSON>", "replace", "iconMap", "image", "pdf", "word", "excel", "default", "typeMap", "info", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted"], "mappings": "kHAAA,W,kECAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAIO,QAAS,KAAS,CAACL,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACE,YAAY,+BAA+BF,EAAG,QAAQ,CAACM,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOX,EAAIY,OAAQC,WAAW,WAAWT,YAAY,eAAeU,MAAM,CAAC,KAAO,OAAO,YAAc,YAAYC,SAAS,CAAC,MAASf,EAAIY,QAASP,GAAG,CAAC,MAAQ,CAAC,SAASC,GAAWA,EAAOU,OAAOC,YAAiBjB,EAAIY,OAAON,EAAOU,OAAOL,QAAOX,EAAIkB,kBAAmBlB,EAAImB,YAAajB,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,IAAI,CAACE,YAAY,2BAA2BC,GAAG,CAAC,MAAQL,EAAIoB,SAASpB,EAAIqB,MAAM,KAAKnB,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACoB,MAAM,CAAE,aAAiC,UAAnBtB,EAAIuB,YAAyBT,MAAM,CAAC,KAAO,UAAU,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIwB,WAAW,QAAQ,CAACtB,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIyB,GAAG,UAAUvB,EAAG,YAAY,CAACoB,MAAM,CAAE,aAAiC,SAAnBtB,EAAIuB,YAAwBT,MAAM,CAAC,KAAO,UAAU,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIwB,WAAW,QAAQ,CAACtB,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIyB,GAAG,WAAW,GAAGvB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAI0B,GAAI1B,EAAI2B,MAAM,SAASC,EAAKC,GAAO,OAAO3B,EAAG,MAAM,CAAC4B,IAAI,MAAQD,EAAMzB,YAAY,eAAekB,MAAM,CAAE,OAAUO,IAAU7B,EAAI+B,cAAiC,IAAlB/B,EAAIgC,UAAkB3B,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIiC,UAAUJ,MAAU,CAAC3B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,SAASU,MAAM,CAAC,IAAMc,EAAKM,YAAaN,EAAKO,MAAQ,EAAGjC,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKO,UAAUnC,EAAIqB,OAAOnB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKS,UAAUnC,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKU,kBAAkBpC,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKW,gBAAevC,EAAI0B,GAAI1B,EAAIwC,OAAO,SAASZ,EAAKC,GAAO,OAAO3B,EAAG,MAAM,CAAC4B,IAAI,OAASD,EAAMzB,YAAY,eAAekB,MAAM,CAAE,OAAUO,IAAU7B,EAAIgC,WAAiC,IAArBhC,EAAI+B,aAAqB1B,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIyC,WAAWZ,MAAU,CAAC3B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,SAASU,MAAM,CAAC,IAAMc,EAAKM,YAAYhC,EAAG,MAAM,CAACE,YAAY,oBAAoBF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKS,UAAUnC,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKc,WAAWxC,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKe,oBAAmB,KAAKzC,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI4C,iBAAkB,KAAS,CAAC1C,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIqC,YAAYnC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,SAAS,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,kBAAkBkB,MAAM,CAAE,OAAUtB,EAAI6C,gBAAiB/B,MAAM,CAAC,KAAO,OAAO,KAAO,gBAAgBT,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAOwC,kBAAkB9C,EAAI6C,gBAAkB7C,EAAI6C,oBAAoB,GAAG3C,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,QAAQ,UAAY,SAAS,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,gBAAgBT,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwC,kBAAyB9C,EAAI+C,kBAAkBC,MAAM,KAAMC,gBAAgB,IAAI,KAAK/C,EAAG,MAAM,CAACgD,IAAI,OAAO9C,YAAY,eAAeC,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAON,EAAImD,kBAAkBnD,EAAI0B,GAAI1B,EAAIoD,MAAM,SAASxB,EAAKC,GAAO,OAAO3B,EAAG,MAAM,CAAC4B,IAAID,EAAMzB,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKU,kBAAkBpC,EAAG,MAAM,CAACE,YAAY,kBAAkBkB,MAAM,CAAE,cAAeM,EAAKyB,aAAerD,EAAIsD,SAAU,CAACpD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMc,EAAK2B,YAAYrD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKS,UAAUnC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAe,SAAbwB,EAAK4B,KAAiBtD,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMc,EAAKe,SAAStC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIyD,QAAQ7B,EAAKe,eAAe3C,EAAIqB,KAAmB,QAAbO,EAAK4B,KAAgBtD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAG,IAAIzB,EAAIoC,GAAGR,EAAKe,SAAS,OAAO3C,EAAIqB,KAAmB,SAAbO,EAAK4B,KAAiBtD,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACY,MAAM,CAAC,WAAac,EAAKe,WAAWzC,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAK8B,WAAW,KAAK1D,EAAIqB,KAAmB,QAAbO,EAAK4B,KAAgBtD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAI2D,SAAS/B,EAAKe,YAAY,CAAC3C,EAAI4D,GAAG,GAAE,GAAM1D,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKiC,MAAMpD,SAASP,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGR,EAAKiC,MAAMC,eAAe9D,EAAIqB,gBAAe,GAAGnB,EAAG,MAAM,CAACE,YAAY,sBAAsBkB,MAAM,CAAE,KAAQtB,EAAI6C,gBAAiBxC,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAOwC,qBAAsB,CAAC5C,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAG,UAAUvB,EAAG,YAAY,CAACE,YAAY,YAAYU,MAAM,CAAC,KAAO,OAAO,KAAO,iBAAiBT,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI6C,gBAAiB,OAAW,GAAG3C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYkB,MAAM,CAAE,OAAkC,SAAxBtB,EAAI+D,iBAA6B1D,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI+D,gBAAkB,UAAU,CAAC7D,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIyB,GAAG,YAAYvB,EAAG,MAAM,CAACE,YAAY,YAAYkB,MAAM,CAAE,OAAkC,YAAxBtB,EAAI+D,iBAAgC1D,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI+D,gBAAkB,aAAa,CAAC7D,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIyB,GAAG,aAAavB,EAAG,MAAM,CAACE,YAAY,YAAYkB,MAAM,CAAE,OAAkC,cAAxBtB,EAAI+D,iBAAkC1D,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI+D,gBAAkB,eAAe,CAAC7D,EAAG,IAAI,CAACE,YAAY,mBAAmBF,EAAG,OAAO,CAACF,EAAIyB,GAAG,YAAYvB,EAAG,MAAM,CAACE,YAAY,YAAYkB,MAAM,CAAE,OAAkC,WAAxBtB,EAAI+D,iBAA+B1D,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI+D,gBAAkB,YAAY,CAAC7D,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,OAAO,CAACF,EAAIyB,GAAG,YAAYvB,EAAG,MAAM,CAACE,YAAY,YAAYkB,MAAM,CAAE,OAAkC,aAAxBtB,EAAI+D,iBAAiC1D,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAI+D,gBAAkB,cAAc,CAAC7D,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIyB,GAAG,cAAcvB,EAAG,MAAM,CAACE,YAAY,eAAe,CAA0B,SAAxBJ,EAAI+D,gBAA4B7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMd,EAAIgE,kBAAkBT,OAAO,IAAM,YAAYrD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBvD,SAASP,EAAG,IAAI,CAACE,YAAY,aAAa,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBR,aAAatD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIyB,GAAG,WAAWvB,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBC,YAAY/D,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIyB,GAAG,WAAWvB,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBE,aAAahE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIyB,GAAG,WAAWvB,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBG,mBAAmBjE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIyB,GAAG,WAAWvB,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBI,gBAAgBlE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACF,EAAIyB,GAAG,WAAWvB,EAAG,SAAS,CAACY,MAAM,CAAC,KAAwC,OAAjCd,EAAIgE,kBAAkBK,OAAkB,UAAY,WAAW,CAACrE,EAAIyB,GAAG,IAAIzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBK,QAAQ,QAAQ,OAAOrE,EAAIqB,KAA8B,YAAxBrB,EAAI+D,gBAA+B7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAG,aAAavB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBM,QAAQC,QAAQ,SAASrE,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAI0B,GAAI1B,EAAIgE,kBAAkBM,SAAS,SAASE,GAAQ,OAAOtE,EAAG,MAAM,CAAC4B,IAAI0C,EAAOC,GAAGrE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGoC,EAAO/D,SAASP,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAG,SAASzB,EAAIoC,GAAGoC,EAAOE,WAAWxE,EAAG,OAAO,CAACE,YAAY,cAAckB,MAAMkD,EAAOH,QAAQ,CAACrE,EAAIyB,GAAGzB,EAAIoC,GAAGoC,EAAOG,mBAAmBzE,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,OAAO,KAAO,UAAU,CAACd,EAAIyB,GAAG,WAAW,QAAO,KAAKzB,EAAIqB,KAA8B,cAAxBrB,EAAI+D,gBAAiC7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAG,UAAUvB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBY,UAAUL,QAAQ,SAASrE,EAAG,MAAM,CAACE,YAAY,kBAAkBJ,EAAI0B,GAAI1B,EAAIgE,kBAAkBY,WAAW,SAASC,GAAK,OAAO3E,EAAG,MAAM,CAAC4B,IAAI+C,EAAIJ,GAAGrE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,IAAI,CAACoB,MAAMtB,EAAI8E,WAAWD,EAAIrB,UAAUtD,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGyC,EAAIpE,SAASP,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGyC,EAAIf,SAAS5D,EAAG,OAAO,CAACF,EAAIyB,GAAGzB,EAAIoC,GAAGyC,EAAIE,mBAAmB7E,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,OAAO,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIgF,YAAYH,MAAQ,CAAC7E,EAAIyB,GAAG,QAAQvB,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,OAAO,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIiF,WAAWJ,MAAQ,CAAC7E,EAAIyB,GAAG,SAAS,QAAO,KAAKzB,EAAIqB,KAA8B,WAAxBrB,EAAI+D,gBAA8B7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAG,UAAUvB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBkB,OAAOX,QAAQ,SAASrE,EAAG,MAAM,CAACE,YAAY,eAAeJ,EAAI0B,GAAI1B,EAAIgE,kBAAkBkB,QAAQ,SAASC,GAAO,OAAOjF,EAAG,MAAM,CAAC4B,IAAIqD,EAAMV,GAAGrE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAG+C,EAAM9C,UAAUnC,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAG+C,EAAM3B,SAAStD,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAG+C,EAAMC,mBAAmBlF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACY,MAAM,CAAC,KAAOd,EAAIqF,mBAAmBF,EAAMd,UAAU,CAACrE,EAAIyB,GAAGzB,EAAIoC,GAAG+C,EAAMd,YAAY,QAAO,KAAKrE,EAAIqB,KAA8B,aAAxBrB,EAAI+D,gBAAgC7D,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIyB,GAAG,UAAUvB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGpC,EAAIgE,kBAAkBsB,SAASf,QAAQ,SAASrE,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAI0B,GAAI1B,EAAIgE,kBAAkBsB,UAAU,SAASC,GAAS,OAAOrF,EAAG,MAAM,CAAC4B,IAAIyD,EAAQd,GAAGrE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGmD,EAAQC,gBAAgBtF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIyB,GAAGzB,EAAIoC,GAAGmD,EAAQ7C,WAAWxC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACE,YAAY,UAAU,CAACJ,EAAIyB,GAAG,IAAIzB,EAAIoC,GAAGmD,EAAQb,WAAWxE,EAAG,SAAS,CAACY,MAAM,CAAC,KAA0B,QAAnByE,EAAQlB,OAAmB,UAAY,UAAU,KAAO,SAAS,CAACrE,EAAIyB,GAAG,IAAIzB,EAAIoC,GAAGmD,EAAQlB,QAAQ,QAAQ,QAAO,KAAKrE,EAAIqB,WAAWnB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,iBAAiBT,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwC,kBAAyB9C,EAAIyF,SAASzC,MAAM,KAAMC,gBAAgB,GAAG/C,EAAG,MAAM,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOX,EAAIO,OAAQM,WAAW,WAAWT,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcJ,EAAI0B,GAAI1B,EAAI0F,WAAW,SAAS9D,EAAKC,GAAO,OAAO3B,EAAG,MAAM,CAAC4B,IAAID,EAAMzB,YAAY,aAAaC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAI2F,SAAS/D,MAAS,CAAC5B,EAAIyB,GAAG,IAAIzB,EAAIoC,GAAGR,GAAM,UAAS,MAAM,GAAG1B,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,4BAA4B,kBAAiB,EAAM,aAAad,EAAI4F,gBAAgB,CAAC1F,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,sBAAsB,IAAI,IAAI,GAAGZ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAad,EAAI6F,eAAe,gBAAgB7F,EAAI8F,eAAe,CAAC5F,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,qBAAqB,IAAI,IAAI,GAAGZ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmBT,GAAG,CAAC,MAAQL,EAAI+F,WAAW,IAAI,GAAG7F,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,OAAO,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,OAAO,KAAO,mBAAmBT,GAAG,CAAC,MAAQL,EAAIgG,gBAAgB,IAAI,KAAK9F,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,WAAW,CAACE,YAAY,gBAAgBU,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,UAAU,OAAS,QAAQmF,MAAM,CAACtF,MAAOX,EAAIkG,YAAaC,SAAS,SAAUC,GAAMpG,EAAIkG,YAAYE,GAAKvF,WAAW,kBAAkB,GAAGX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,aAAa,CAACY,MAAM,CAAC,QAAU,eAAe,UAAY,MAAM,OAAS,SAAS,CAACZ,EAAG,YAAY,CAACE,YAAY,WAAWU,MAAM,CAAC,KAAO,UAAU,UAAYd,EAAIkG,YAAYG,QAAQhG,GAAG,CAAC,MAAQL,EAAIsG,OAAO,CAACpG,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIyB,GAAG,WAAW,IAAI,SAASvB,EAAG,YAAY,CAACY,MAAM,CAAC,MAAQ,OAAO,QAAUd,EAAIuG,YAAY,MAAQ,MAAM,OAAS,IAAIlG,GAAG,CAAC,iBAAiB,SAASC,GAAQN,EAAIuG,YAAYjG,KAAU,CAACJ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACY,MAAM,CAAC,IAAMd,EAAIwG,OAAO,IAAM,cAActG,EAAG,YAAY,CAACY,MAAM,CAAC,MAAQ,OAAO,QAAUd,EAAIyG,MAAM,UAAY,MAAM,KAAO,OAAOpG,GAAG,CAAC,iBAAiB,SAASC,GAAQN,EAAIyG,MAAMnG,KAAU,CAACJ,EAAG,WAAW,CAACwG,YAAY,CAAC,MAAQ,QAAQ5F,MAAM,CAAC,KAAOd,EAAI2G,WAAW,CAACzG,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,cAAc,MAAQ,OAAO,MAAQ,SAASZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,QAAQ,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,OAAO,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,aAAa,MAAQ,UAAUZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,SAAW,gBAAgB,MAAQ,QAAQZ,EAAG,kBAAkB,CAACY,MAAM,CAAC,MAAQ,QAAQ,MAAQ,MAAM8F,YAAY5G,EAAI6G,GAAG,CAAC,CAAC/E,IAAI,UAAUgF,GAAG,SAASC,GAAO,MAAO,CAAC7G,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,OAAO,KAAO,SAAST,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIgH,SAASD,EAAME,IAAIxC,OAAO,CAACzE,EAAIyB,GAAG,kBAAkB,IAAI,GAAGvB,EAAG,YAAY,CAACY,MAAM,CAAC,MAAQ,OAAO,QAAUd,EAAIkH,kBAAkB,MAAQ,OAAO7G,GAAG,CAAC,iBAAiB,SAASC,GAAQN,EAAIkH,kBAAkB5G,KAAU,CAACJ,EAAG,UAAU,CAACgD,IAAI,WAAWpC,MAAM,CAAC,MAAQd,EAAImH,WAAW,CAACjH,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,IAAImF,MAAM,CAACtF,MAAOX,EAAImH,SAASC,WAAYjB,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,aAAcf,IAAMvF,WAAW,0BAA0B,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,IAAImF,MAAM,CAACtF,MAAOX,EAAImH,SAAS9E,MAAO8D,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,QAASf,IAAMvF,WAAW,qBAAqB,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,SAAW,GAAG,KAAO,WAAW,KAAO,GAAGmF,MAAM,CAACtF,MAAOX,EAAImH,SAAS5E,KAAM4D,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,OAAQf,IAAMvF,WAAW,oBAAoB,GAAGX,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,MAAM,CAACA,EAAG,WAAW,CAACY,MAAM,CAAC,MAAQ,GAAGmF,MAAM,CAACtF,MAAOX,EAAImH,SAASG,QAASnB,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,UAAWf,IAAMvF,WAAW,qBAAqB,CAACb,EAAIyB,GAAG,SAASvB,EAAG,WAAW,CAACY,MAAM,CAAC,MAAQ,GAAGmF,MAAM,CAACtF,MAAOX,EAAImH,SAASG,QAASnB,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,UAAWf,IAAMvF,WAAW,qBAAqB,CAACb,EAAIyB,GAAG,UAAU,KAA8B,GAAxBzB,EAAImH,SAASG,SAAqC,GAArBtH,EAAImH,SAAS3D,KAAWtD,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,QAAQ,KAAO,cAAc,CAACZ,EAAG,WAAW,CAACE,YAAY,WAAWU,MAAM,CAAC,UAAW,GAAMmF,MAAM,CAACtF,MAAOX,EAAImH,SAASI,UAAWpB,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,YAAaf,IAAMvF,WAAW,wBAAwBX,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACA,EAAG,YAAY,CAACY,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAad,EAAI6F,iBAAiB,CAAC7F,EAAIyB,GAAG,WAAW,GAAIzB,EAAImH,SAASI,UAAWrH,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,UAAUT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIwH,SAASxH,EAAImH,SAASI,UAAW,gBAAgB,CAACvH,EAAIyB,GAAG,QAAQzB,EAAIqB,MAAM,IAAI,GAAGrB,EAAIqB,KAA8B,GAAxBrB,EAAImH,SAASG,SAAqC,GAArBtH,EAAImH,SAAS3D,KAAWtD,EAAG,eAAe,CAACY,MAAM,CAAC,MAAQ,SAAS,CAACZ,EAAG,WAAW,CAACY,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAGmF,MAAM,CAACtF,MAAOX,EAAImH,SAASxE,QAASwD,SAAS,SAAUC,GAAMpG,EAAIqH,KAAKrH,EAAImH,SAAU,UAAWf,IAAMvF,WAAW,uBAAuB,GAAGb,EAAIqB,MAAM,GAAGnB,EAAG,MAAM,CAACE,YAAY,gBAAgBU,MAAM,CAAC,KAAO,UAAU2G,KAAK,UAAU,CAACvH,EAAG,YAAY,CAACG,GAAG,CAAC,MAAQ,SAASC,GAAQN,EAAIkH,mBAAoB,KAAS,CAAClH,EAAIyB,GAAG,SAASvB,EAAG,YAAY,CAACY,MAAM,CAAC,KAAO,WAAWT,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAI0H,cAAc,CAAC1H,EAAIyB,GAAG,UAAU,IAAI,IAAI,IAEj6iBkG,EAAkB,CAAC,WAAY,IAAI3H,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,yB,UCF/H,MAAMsF,EAAU,CACf,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MAMcA,QC1DX3F,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,IAAI,CAACoB,MAAoB,GAAdtB,EAAI4H,OAAkB,qBAAuB,sBAAsBlB,YAAY,CAAC,OAAS,UAAU,eAAe,OAAO,aAAa,MAAM,YAAY,QAAQmB,MAAqB,GAAd7H,EAAI4H,OAAkB,GAAK,cAAe9G,MAAM,CAAC,KAAO,aAAaT,GAAG,CAAC,MAAQL,EAAI8H,UAAUL,KAAK,eAExVE,EAAkB,GCcP,GACfI,MAAA,CACAC,WAAA,CACAxE,KAAAyE,SAGAxH,KAAA,YACAyH,OACA,OACAN,QAAA,EACAO,OAAA,IAAAC,MAAA,KAAAJ,cAGAK,QAAA,CACAP,WACA,KAAAF,QAAA,KAAAA,OACA,KAAAA,QACA,KAAAO,OAAAG,OACA,KAAAC,YAEA,KAAAJ,OAAAK,QACA,KAAAD,YAGAA,UACA,KAAAJ,OAAAM,iBAAA,aACA,KAAAb,QAAA,OC1CkV,I,YCO9Uc,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCyjBf,IAAAC,EAGe,OACflI,KAAA,OACAmI,WAAA,CAAAC,aACAX,OACA,OACAY,OAAA,GACAC,QAAA,GACAC,WAAA,GACAvC,OAAA,EACAE,SAAA,GACAQ,SAAA,GACAD,mBAAA,EACAxB,YACAnE,WAAA,QACAS,SAAA,EACAiH,WAAA,QACArI,OAAA,GACAsI,QAAA,EACA1C,OAAA,GACAlD,OAAA,EACAmB,GAAA,EACAtD,aAAA,EACAqC,KAAA,GACA2F,MAAA,GAEAC,MAAA,GACA7C,aAAA,EACAL,YAAA,GACAlE,SAAA,EACAqH,QAAA,IACAnH,SAAA,GACAqF,UAAA,GACAnE,KAAA,GACAkG,MAAA,GACA9G,MAAA,GACAb,KAAA,GACAI,YAAA,EACAwH,QAAA,GACAhJ,QAAA,EACA8B,MAAA,GACAmH,YAAA,GACAC,aAAA,GACA7G,iBAAA,EACA8G,WAAA,CACAlH,MAAA,GACAmH,QAAA,GACAC,MAAA,IAEA/G,gBAAA,EACAkB,gBAAA,OACAC,kBAAA,CACAvD,KAAA,GACA+C,KAAA,GACAD,OAAA,GACAU,MAAA,GACAC,OAAA,GACAC,aAAA,GACAC,UAAA,GACAC,OAAA,GACAC,QAAA,GACAM,UAAA,GACAM,OAAA,GACAI,SAAA,MAIA+C,QAAA,CACArB,SAAAvC,GAEA,GAAAA,EACA,KAAAoF,QAAApF,GAEA,KAAA0C,SAAA,CACA9E,MAAA,GACAE,KAAA,KAIAuH,eAAAC,GACA,KAAAA,EAAAC,MACA,KAAAC,SAAAC,QAAA,QACA,KAAA/C,SAAA,aAAA4C,EAAA7B,KAAAiC,KAEA,KAAAF,SAAAG,MAAAL,EAAAM,MAGAR,QAAApF,GACA,IAAAkE,EAAA,KACA,KAAA2B,WAAA,oBAAA7F,GAAA8F,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAxB,SAAAqD,EAAAtC,KACAS,EAAAzB,mBAAA,GAEAyB,EAAAsB,SAAA,CACAzG,KAAA,QACAiH,QAAAD,EAAAH,SAMA3C,WACA,IAAAiB,EAAA,KACA,KAAA+B,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAA,qBAAA1D,UAAAoD,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAA,CACAzG,KAAA,UACAiH,QAAAD,EAAAH,MAEA,KAAAS,UACAnC,EAAAzB,mBAAA,GAEAyB,EAAAsB,SAAA,CACAzG,KAAA,QACAiH,QAAAD,EAAAH,WASArE,cACA,IAAA+E,EAAA,KAAApJ,KAAA,KAAAI,aAAA,OACA4G,EAAAlC,OAAA,EACAkC,EAAAkC,YAAA,qBAAAE,QAAAR,KAAAC,IACA,KAAAA,EAAAR,OACArB,EAAAhC,SAAA6D,EAAAtC,SAIA1G,WAAAwJ,GACA,KAAAzJ,WAAA,MAAAyJ,EAAA,eACArC,EACAkC,YAAA,gBAAAG,cACAT,KAAAC,IACA,KAAAA,EAAAR,OACArB,EAAAhH,KAAA6I,EAAAtC,KACAS,EAAAa,YAAAgB,EAAAtC,KACAS,EAAA3G,UAAA,EACA2G,EAAAsC,cAIA/J,cAAAgK,GACA,IAAAvJ,EAAAgH,EAAAa,YACAhH,EAAAmG,EAAAc,aACA7I,EAAAsK,EAAAlK,OAAAL,MAEA,KAAAQ,YAAAP,EAAA2D,OAAA,EAEAoE,EAAAhH,OAAAwJ,OAAAjD,KAAA7F,MAAA+I,cAAAC,SAAAzK,EAAAwK,gBACAzC,EAAAnG,QAAA2I,OAAAjD,KAAA7F,MAAA+I,cAAAC,SAAAzK,EAAAwK,iBAEArF,SACA,IAAAtB,EAAA,KAAA9C,KAAA,KAAAI,aAAA,MACAiJ,EAAA,QAAArJ,KAAA,KAAAI,aAAA,iBACA4G,EACAkC,YAAA,gBAAApG,KAAAuG,cACAT,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAhH,KAAA,KAAAI,aAAA,aAAAiJ,EACArC,EAAAsB,SAAAC,QAAAM,EAAAH,MAEA1B,EAAAsB,SAAAG,MAAAI,EAAAH,QAIA5E,WACAkD,EAAApI,QAAAoI,EAAApI,OACA+K,QAAAC,IAAA,+BAEAC,WAAAC,GACA9C,EAAAnF,KAAAiI,GAEA9H,SAAAwG,GACAuB,OAAAC,KAAAxB,EAAA,WAEA1G,QAAAmI,GACAjD,EAAAnC,OAAAoF,EACAjD,EAAApC,aAAA,EACA+E,QAAAC,IAAA,aAAAK,IAEA9F,aAAA+F,GACA,IAAArI,EAAAqI,EAAArI,KAEA,GADA8H,QAAAC,IAAA/H,EAAA,QAEA,QAAAqI,EAAArI,KAAAsI,MAAA,SACA,SAAAD,EAAArI,KAAAsI,MAAA,SACA,QAAAD,EAAArI,KAAAsI,MAAA,SACA,QAAAD,EAAArI,KAAAsI,MAAA,SACA,QAAAD,EAAArI,KAAAsI,MAAA,SACA,SAAAD,EAAArI,KAAAsI,MAAA,SACA,SAAAD,EAAArI,KAAAsI,MAAA,QAOA,OALAnD,EAAAsB,SAAA,CACA8B,WAAA,EACAtB,QAAA,mDACAjH,KAAA,WAEA,GAGAoC,cAAAmE,GACA,IAAApB,EAAA,KACA2C,QAAAC,IAAAxB,GACA,KAAAA,EAAAC,KACArB,EAAAqD,QAAAjC,EAAA7B,KAAAiC,KAEAxB,EAAAsB,SAAAG,MAAAL,EAAAM,MAGAxE,eAAAkE,EAAAkC,GACA,KAAAlC,EAAAC,KACArB,EAAAuD,SAAAnC,EAAA7B,KAAAiC,IAAA8B,GAEAtD,EAAAsB,SAAA,CACA8B,WAAA,EACAtB,QAAA,wCACAjH,KAAA,WAIAf,WAAAZ,GACAyJ,QAAAC,IAAA,QAAA1J,GACA8G,EAAA3G,SAAAH,EACA8G,EAAA5G,aAAA,EACA4G,EAAA/F,iBAAA,EACA+F,EAAAwD,mBACAxD,EAAAyD,sBAEAnK,UAAAJ,GACAyJ,QAAAC,IAAA,QAAA1J,GACA8G,EAAA3G,UAAA,EACA2G,EAAA5G,YAAAF,EACA8G,EAAAhH,KAAAE,GAAAM,MAAA,EACAwG,EAAA/F,iBAAA,EACA+F,EAAAwD,mBACAxD,EAAAyD,sBAEAzG,SAAA/D,GACA+G,EAAAzC,aAAAtE,GAEAyK,OAAAnB,GACAvC,EAAA/H,OAAA+H,EAAAxH,aAAA,EACAwH,EAAAxH,aAAA,GAEAC,MACAuH,EAAA/H,OAAA,GACA+H,EAAAxH,aAAA,GAEAgC,aAAA+H,GACA,GAAAvC,EAAA+B,MAAAtH,KAAAkJ,WACAhB,QAAAC,IAAA,aAGAjF,OACAqC,EAAA4D,YAAA5D,EAAAzC,aACAyC,EAAAzC,YAAA,IAEA+E,UACA,OAAAtC,EAAA3G,SAAA,CACA,IAAAyC,EAAAkE,EAAAnG,MAAAmG,EAAA3G,UAAAyC,GACAkE,EAAAtG,MAAAsG,EAAAnG,MAAAmG,EAAA3G,UAAAK,MAEAsG,EAAAkC,YAAA,kBAAAE,IAAAtG,IAAA8F,KAAAC,IACA,KAAAA,EAAAR,MACAQ,EAAAtC,KAAA3D,OAAA,IACAoE,EAAAvF,KAAAoH,EAAAtC,KAEAS,EAAA+B,MAAAtH,KAAAkJ,UAAA3D,EAAA+B,MAAAtH,KAAAoJ,cAGA7D,EAAA8D,SAAA,QAEA,CACA,IAAAhI,EAAAkE,EAAAhH,KAAAgH,EAAA5G,aAAA0C,GACAtC,EACA,EAAAwG,EAAAhH,KAAAgH,EAAA5G,aAAAgJ,IAAAxG,OACA,EAAAoE,EAAAhH,KAAAgH,EAAA5G,aAAA2K,SAAAnI,OACA,EAAAoE,EAAAhH,KAAAgH,EAAA5G,aAAAsB,YAAAkB,OACAoE,EAAAlE,KACA6G,QAAAC,IAAA5C,EAAAlE,IAEAkE,EAAAtG,MAAAsG,EAAAhH,KAAAgH,EAAA5G,aAAAM,MAAA,IAAAF,EAAA,IACAwG,EAAAkC,YAAA,qBAAA8B,OAAAlI,IAAA8F,KAAAC,IACA,KAAAA,EAAAR,OACAQ,EAAAtC,KAAA3D,OAAA,GACAoE,EAAAvF,KAAAoH,EAAAtC,KACAS,EAAA+B,MAAAtH,KAAAkJ,UAAA3D,EAAA+B,MAAAtH,KAAAoJ,cAEA7D,EAAAvF,KAAA,GAGAwJ,WACA,SAAAlC,MAAAtH,KAAAkJ,UAAA,KAAA5B,MAAAtH,KAAAoJ,aACA,IAGA7D,EAAA8D,SAAA,MAIAI,cACA,OAAAlE,EAAA3G,SAAA,CACA,IAAA+I,EAAApC,EAAAnG,MAAAmG,EAAA3G,UAAAyC,GACAkE,EAAAtG,MAAAsG,EAAAnG,MAAAmG,EAAA3G,UAAAK,MAEA,IAAAoC,EAAA,EACAkE,EAAAvF,KAAAmB,OAAA,IACAE,EAAAkE,EAAAvF,KAAAuF,EAAAvF,KAAAmB,OAAA,GAAAE,GACAkE,EACAkC,YAAA,wBAAAE,MAAAtG,OACA8F,KAAAC,IACA7B,EAAAmE,UACA,KAAAtC,EAAAR,MACAQ,EAAAtC,KAAA3D,OAAA,IACAoE,EAAAvF,KAAA2J,KAAAvC,EAAAtC,MACA0E,WACA,IACA,KAAAlC,MAAAtH,KAAAkJ,UACA,KAAA5B,MAAAtH,KAAAoJ,aACA,MAIA7D,EAAA8D,SAAA,SAGA,CACA,IAAAE,EAAAhE,EAAAhH,KAAAgH,EAAA5G,aAAA0C,GACAtC,EACA,EAAAwG,EAAAhH,KAAAgH,EAAA5G,aAAA2K,SAAAnI,OACA,EAAAoE,EAAAhH,KAAAgH,EAAA5G,aAAAsB,YAAAkB,OACA,EAEAoE,EAAAtG,MAAAsG,EAAAhH,KAAAgH,EAAA5G,aAAAM,MAAA,IAAAF,EAAA,IACA,IAAAsC,EAAA,EACAkE,EAAAvF,KAAAmB,OAAA,GACAE,EAAAkE,EAAAvF,KAAAuF,EAAAvF,KAAAmB,OAAA,GAAAE,GACAkE,EACAkC,YAAA,wBAAA8B,SAAAlI,OACA8F,KAAAC,IACA7B,EAAAmE,UACA,KAAAtC,EAAAR,OACArB,EAAAvF,KAAA2J,KAAAvC,EAAAtC,MAEA0E,WACA,IACAjE,EAAA+B,MAAAtH,KAAAkJ,UACA3D,EAAA+B,MAAAtH,KAAAoJ,aACA,MAGA7D,EAAA8D,SAAA,MAGAhI,EAAA,EACAkE,EACAkC,YAAA,wBAAA8B,SAAAlI,OACA8F,KAAAC,IACA7B,EAAAmE,UACA,KAAAtC,EAAAR,OACArB,EAAAvF,KAAA2J,KAAAvC,EAAAtC,MAEA0E,WACA,IACAjE,EAAA+B,MAAAtH,KAAAkJ,UACA3D,EAAA+B,MAAAtH,KAAAoJ,aACA,MAGA7D,EAAA8D,SAAA,OAKAF,YAAA5J,GACA,OAAAgG,EAAA3G,SAAA,CACA,IAAAyC,EAAAkE,EAAAnG,MAAAmG,EAAA3G,UAAAyC,GACAuI,EAAA,EACArE,EACAkC,YAAA,qBACAE,IAAAtG,EACAwI,UAAA,OACAzJ,KAAA,OACAb,UACAqK,cAEAzC,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,WAGA,CACA1B,EAAAhH,KAAAgH,EAAA5G,aAAAgJ,IAAA,IACA4B,EAAAhE,EAAAhH,KAAAgH,EAAA5G,aAAA0C,GACAkE,EACAkC,YAAA,wBACAoC,UAAA,OACAzJ,KAAA,OACAb,UACAgK,WAEApC,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,SAKA6B,SAAAvJ,EAAAkB,GACA,OAAA8E,EAAA3G,SAAA,CACA,IAAAgL,EAAA,EACArE,EACAkC,YAAA,qBACAoC,UAAA,OACAzJ,KAAA,OACAb,UACAqK,YACAnJ,UAEA0G,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,WAGA,CACA,IAAAsC,EAAAhE,EAAAhH,KAAAgH,EAAA5G,aAAA0C,GACAkE,EACAkC,YAAA,wBACAoC,UAAA,OACAzJ,KAAA,OACAb,UACAgK,SACA9I,UAEA0G,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,SAKA2B,QAAArJ,GACA,OAAAgG,EAAA3G,SAAA,CACA,IAAAyC,EAAAkE,EAAAnG,MAAAmG,EAAA3G,UAAAyC,GACAuI,EAAA,EACArE,EACAkC,YAAA,qBACAE,IAAAtG,EACAwI,UAAA,OACAzJ,KAAA,QACAb,UACAqK,cAEAzC,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,WAGA,CACA,IAAAU,EAAApC,EAAAhH,KAAAgH,EAAA5G,aAAAgJ,IACA4B,EAAAhE,EAAAhH,KAAAgH,EAAA5G,aAAA0C,GACAkE,EACAkC,YAAA,wBACAE,MACAkC,UAAA,OACAzJ,KAAA,QACAb,UACAgK,WAEApC,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAsB,SAAAG,MAAAI,EAAAH,SAKA6C,cACAvE,EAAAkC,YAAA,qBAAAN,KAAAC,IACA,KAAAA,EAAAR,OACArB,EAAAnG,MAAAgI,EAAAtC,KACAS,EAAAc,aAAAe,EAAAtC,SAIAiF,SACAxE,EAAAkC,YAAA,gBAAAN,KAAAC,IACA,KAAAA,EAAAR,OACArB,EAAAhH,KAAA6I,EAAAtC,KACAS,EAAAa,YAAAgB,EAAAtC,KACAS,EAAA3G,UAAA,EACA4K,WAAA,KACAjE,EAAAsC,WACA,UAIA6B,UACAnE,EAAAkC,YAAA,gBAAAN,KAAAC,IACA,KAAAA,EAAAR,OACArB,EAAAhH,KAAA6I,EAAAtC,KACAS,EAAAa,YAAAgB,EAAAtC,KACAS,EAAA3G,UAAA,MAIAoL,cACA,IAAAzE,EAAA,KAEA0E,SAAAC,UAAApC,IACA,IAAAqC,EAAA7B,OAAA8B,MAAAC,QAEA,KAAAF,GACA5E,EAAArC,SAIAkB,SAAAqE,EAAA6B,GACA,IAAA/E,EAAA,KACAA,EAAA2B,WAAA,6BAAAuB,GAAAtB,KAAAC,IACA,KAAAA,EAAAR,MACArB,EAAAxB,SAAAuG,GAAA,GAEA/E,EAAAsB,SAAAC,QAAA,UAEAvB,EAAAsB,SAAAG,MAAAI,EAAAH,QAKAsD,eAEAhF,EAAAhH,KAAA,CACA,CACA8C,GAAA,EACApC,MAAA,QACAE,KAAA,eACAL,SAAA,sEACAI,YAAA,QACAH,MAAA,EACA4I,IAAA,QACA2B,SAAA,MACArJ,YAAA,QACA2H,UAAA,GAEA,CACAvG,GAAA,EACApC,MAAA,QACAE,KAAA,kBACAL,SAAA,wEACAI,YAAA,KACAH,MAAA,EACA4I,IAAA,MACA2B,SAAA,IACArJ,YAAA,MACA2H,UAAA,GAEA,CACAvG,GAAA,EACApC,MAAA,QACAE,KAAA,gBACAL,SAAA,sEACAI,YAAA,QACAH,MAAA,EACA4I,IAAA,QACA2B,SAAA,IACArJ,YAAA,MACA2H,UAAA,IAKArC,EAAAnG,MAAA,CACA,CACAiC,GAAA,EACApC,MAAA,SACAM,QAAA,mBACAT,SAAA,sEACAQ,KAAA,SAEA,CACA+B,GAAA,EACApC,MAAA,SACAM,QAAA,eACAT,SAAA,wEACAQ,KAAA,SAEA,CACA+B,GAAA,EACApC,MAAA,UACAM,QAAA,gBACAT,SAAA,sEACAQ,KAAA,MAEA,CACA+B,GAAA,EACApC,MAAA,SACAM,QAAA,UACAT,SAAA,sEACAQ,KAAA,UAKAiG,EAAAa,YAAA,IAAAb,EAAAhH,MACAgH,EAAAc,aAAA,IAAAd,EAAAnG,OAGAmG,EAAA3G,UAAA,EACA2G,EAAA5G,YAAA,EAGA6K,WAAA,KACAjE,EAAAwD,oBACA,MAGAA,mBAGA,GAFAb,QAAAC,IAAA,oBAAA5C,EAAA3G,SAAA,eAAA2G,EAAA5G,cAEA,IAAA4G,EAAA3G,SACA2G,EAAAtG,MAAAsG,EAAAnG,MAAAmG,EAAA3G,UAAAK,MAEAsG,EAAAiF,0BACA,CACA,MAAAC,EAAAlF,EAAAhH,KAAAgH,EAAA5G,aACAI,EAAA0L,EAAA9C,IAAAe,MAAA,KAAAvH,OAAAsJ,EAAAnB,SAAAZ,MAAA,KAAAvH,OAAAsJ,EAAAxK,YAAAyI,MAAA,KAAAvH,OACAoE,EAAAtG,MAAAwL,EAAAxL,MAAA,IAAAF,EAAA,IAEAwG,EAAAmF,oBAIAnF,EAAAoF,UAAA,KACApF,EAAA+B,MAAAtH,OACAuF,EAAA+B,MAAAtH,KAAAkJ,UAAA3D,EAAA+B,MAAAtH,KAAAoJ,iBAKAsB,oBACA,MAAAE,EAAA,CACA,GACA,CACAvJ,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,wEACAlB,MAAA,MACAmB,KAAA,OACAb,QAAA,yBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,MACAmB,KAAA,OACAb,QAAA,oBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,iBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,QACAb,QAAA,uEAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,wEACAlB,MAAA,MACAmB,KAAA,OACAb,QAAA,4BAGA,GACA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,gBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,qBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,WAGA,GACA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,wEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,cAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,cAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,wEACAlB,MAAA,MACAmB,KAAA,OACAb,QAAA,iBAKAgG,EAAAvF,KAAA4K,EAAArF,EAAA5G,cAAA,GACAuJ,QAAAC,IAAA,YAAA5C,EAAAvF,KAAAmB,SAGAqJ,sBACA,MAAAK,EAAA,CACA,GACA,CACAxJ,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,oBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,2BAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,YAGA,GACA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,wEACAlB,MAAA,MACAmB,KAAA,OACAb,QAAA,gBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,YAGA,GACA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,OACAmB,KAAA,OACAb,QAAA,iBAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,cAGA,GACA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,KACAmB,KAAA,OACAb,QAAA,WAEA,CACA8B,GAAA,EACAnC,YAAA,sBACAe,YAAA,EACAE,OAAA,sEACAlB,MAAA,IACAmB,KAAA,OACAb,QAAA,kBAKAgG,EAAAvF,KAAA6K,EAAAtF,EAAA3G,WAAA,GACAsJ,QAAAC,IAAA,YAAA5C,EAAAvF,KAAAmB,SAMAxB,oBACAuI,QAAAC,IAAA,WACA5C,EAAA/F,iBAAA+F,EAAA/F,gBACA+F,EAAA/F,iBACA+F,EAAAuF,kBAKAA,iBACA5C,QAAAC,IAAA,WACA5C,EAAAe,WAAA,CACAlH,MAAA,CACA,CACAiC,GAAA,EACAhE,KAAA,KACA8C,OAAA,uEAEA,CACAkB,GAAA,EACAhE,KAAA,KACA8C,OAAA,yEAEA,CACAkB,GAAA,EACAhE,KAAA,KACA8C,OAAA,wEAGAoG,QAAA,CACA,CACAlF,GAAA,EACAhE,KAAA,MACA8C,OAAA,yEAEA,CACAkB,GAAA,EACAhE,KAAA,MACA8C,OAAA,wEAGAqG,MAAA,CACA,CACAnF,GAAA,EACAhE,KAAA,OACA8C,OAAA,uEAEA,CACAkB,GAAA,EACAhE,KAAA,MACA8C,OAAA,uEAEA,CACAkB,GAAA,EACAhE,KAAA,MACA8C,OAAA,4EAOA6I,qBAEA,MAAA+B,EAAAxF,EAAAhH,KAAAgH,EAAA5G,aACAoM,IACAxF,EAAA3E,kBAAA,CACAvD,KAAA0N,EAAA9L,MAAA+L,QAAA,cACA5K,KAAA,KACAD,OAAA4K,EAAAjM,SACA+B,MAAA,cACAC,OAAA,qBACAC,aAAA,sBACAC,UAAA,sBACAC,OAAA,KACAC,QAAA,CACA,CACAG,GAAA,EACAhE,KAAA,KACAiE,OAAA,WACAL,OAAA,UACAM,WAAA,MAEA,CACAF,GAAA,EACAhE,KAAA,KACAiE,OAAA,WACAL,OAAA,SACAM,WAAA,MAEA,CACAF,GAAA,EACAhE,KAAA,KACAiE,OAAA,WACAL,OAAA,UACAM,WAAA,QAGAC,UAAA,CACA,CACAH,GAAA,EACAhE,KAAA,YACA+C,KAAA,QACAM,KAAA,QACAiB,WAAA,cAEA,CACAN,GAAA,EACAhE,KAAA,WACA+C,KAAA,MACAM,KAAA,QACAiB,WAAA,cAEA,CACAN,GAAA,EACAhE,KAAA,YACA+C,KAAA,OACAM,KAAA,QACAiB,WAAA,eAGAG,OAAA,CACA,CACAT,GAAA,EACApC,MAAA,SACAmB,KAAA,OACAa,OAAA,MACAe,WAAA,cAEA,CACAX,GAAA,EACApC,MAAA,SACAmB,KAAA,OACAa,OAAA,MACAe,WAAA,cAEA,CACAX,GAAA,EACApC,MAAA,SACAmB,KAAA,OACAa,OAAA,MACAe,WAAA,eAGAE,SAAA,CACA,CACAb,GAAA,EACAe,YAAA,SACAd,OAAA,SACAL,OAAA,MACA3B,KAAA,uBAEA,CACA+B,GAAA,EACAe,YAAA,SACAd,OAAA,SACAL,OAAA,MACA3B,KAAA,uBAEA,CACA+B,GAAA,EACAe,YAAA,UACAd,OAAA,UACAL,OAAA,MACA3B,KAAA,2BAQAoC,WAAAtB,GACA,MAAA6K,EAAA,CACAC,MAAA,kBACAC,IAAA,mBACAC,KAAA,mBACAC,MAAA,iBACAC,QAAA,oBAEA,OAAAL,EAAA7K,IAAA6K,EAAAK,SAIArJ,mBAAAhB,GACA,MAAAsK,EAAA,CACA,gBACA,gBACA,aACA,gBAEA,OAAAA,EAAAtK,IAAA,QAIAW,YAAAH,GACAyG,QAAAC,IAAA,QAAA1G,EAAApE,MACAkI,EAAAsB,SAAAC,QAAA,SAAArF,EAAApE,OAIAwE,WAAAJ,GACAyG,QAAAC,IAAA,QAAA1G,EAAApE,MACAkI,EAAAsB,SAAA2E,KAAA,gBAGAC,gBACAvD,QAAAC,IAAA,OACAuD,cAAA,KAAAxF,QAEAyF,UACApG,EAAA,KAEAA,EAAAgF,eACAhF,EAAAyD,qBACAzD,EAAArF,OAAA,EAKAqF,EAAAyE,gBCpoD2W,ICQvW,G,UAAY,eACd,EACArN,EACA4H,GACA,EACA,KACA,WACA,OAIa,e", "file": "js/chunk-0ffa028e.214153fe.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=style&index=0&id=72349a68&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-container\",on:{\"click\":function($event){_vm.isEmji = false}}},[_c('div',{staticClass:\"chat-content\"},[_c('div',{staticClass:\"contact-sidebar\"},[_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"search-input-wrapper\"},[_c('i',{staticClass:\"el-icon-search search-icon\"}),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.search),expression:\"search\"}],staticClass:\"search-input\",attrs:{\"type\":\"text\",\"placeholder\":\"搜索联系人或群聊\"},domProps:{\"value\":(_vm.search)},on:{\"input\":[function($event){if($event.target.composing)return;_vm.search=$event.target.value},_vm.changeKeyword]}}),(_vm.isShowSeach)?_c('el-tooltip',{attrs:{\"content\":\"清除搜索\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('i',{staticClass:\"el-icon-close clear-icon\",on:{\"click\":_vm.del}})]):_vm._e()],1)]),_c('div',{staticClass:\"tab-section\"},[_c('el-button',{class:{ 'active-tab': _vm.currentTab === 'group' },attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.showDaiban('2')}}},[_c('i',{staticClass:\"el-icon-s-custom\"}),_vm._v(\" 群聊 \")]),_c('el-button',{class:{ 'active-tab': _vm.currentTab === 'todo' },attrs:{\"type\":\"success\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.showDaiban('1')}}},[_c('i',{staticClass:\"el-icon-s-order\"}),_vm._v(\" 代办 \")])],1),_c('div',{staticClass:\"contact-list\"},[_vm._l((_vm.quns),function(item,index){return _c('div',{key:'qun' + index,staticClass:\"contact-item\",class:{ 'active': index === _vm.quliaoIndex && _vm.selectId === -1 },on:{\"click\":function($event){return _vm.changeQun(index)}}},[_c('div',{staticClass:\"avatar-wrapper\"},[_c('img',{staticClass:\"avatar\",attrs:{\"src\":item.pic_path}}),(item.count > 0)?_c('span',{staticClass:\"unread-badge\"},[_vm._v(_vm._s(item.count))]):_vm._e()]),_c('div',{staticClass:\"contact-info\"},[_c('div',{staticClass:\"contact-header\"},[_c('h4',{staticClass:\"contact-name\"},[_vm._v(_vm._s(item.title))]),_c('span',{staticClass:\"contact-time\"},[_vm._v(_vm._s(item.create_time))])]),_c('p',{staticClass:\"last-message\"},[_vm._v(_vm._s(item.desc))])])])}),_vm._l((_vm.users),function(item,index){return _c('div',{key:'user' + index,staticClass:\"contact-item\",class:{ 'active': index === _vm.selectId && _vm.quliaoIndex === -1 },on:{\"click\":function($event){return _vm.redSession(index)}}},[_c('div',{staticClass:\"avatar-wrapper\"},[_c('img',{staticClass:\"avatar\",attrs:{\"src\":item.pic_path}}),_c('div',{staticClass:\"online-status\"})]),_c('div',{staticClass:\"contact-info\"},[_c('div',{staticClass:\"contact-header\"},[_c('h4',{staticClass:\"contact-name\"},[_vm._v(_vm._s(item.title))]),_c('span',{staticClass:\"contact-time\"},[_vm._v(_vm._s(item.time))])]),_c('p',{staticClass:\"last-message\"},[_vm._v(_vm._s(item.content))])])])})],2)]),_c('div',{staticClass:\"chat-main\",on:{\"click\":function($event){_vm.showMemberPanel = false}}},[_c('div',{staticClass:\"chat-header\"},[_c('div',{staticClass:\"chat-title\"},[_c('h3',[_vm._v(_vm._s(_vm.title))])]),_c('div',{staticClass:\"chat-actions\"},[_c('el-tooltip',{attrs:{\"content\":\"用户详情\",\"placement\":\"bottom\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"user-detail-btn\",class:{ 'active': _vm.showUserDetail },attrs:{\"type\":\"text\",\"icon\":\"el-icon-user\"},on:{\"click\":function($event){$event.stopPropagation();_vm.showUserDetail = !_vm.showUserDetail}}})],1),_c('el-tooltip',{attrs:{\"content\":\"查看群成员\",\"placement\":\"bottom\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"more-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-more\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.toggleMemberPanel.apply(null, arguments)}}})],1)],1)]),_c('div',{ref:\"list\",staticClass:\"message-list\",on:{\"scroll\":function($event){return _vm.handleScroll()}}},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"message-item\"},[_c('div',{staticClass:\"time-divider\"},[_c('span',{staticClass:\"time-text\"},[_vm._v(_vm._s(item.create_time))])]),_c('div',{staticClass:\"message-wrapper\",class:{ 'own-message': item.yuangong_id == _vm.yon_id }},[_c('div',{staticClass:\"message-avatar\"},[_c('img',{attrs:{\"src\":item.avatar}})]),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"sender-name\"},[_vm._v(_vm._s(item.title))]),_c('div',{staticClass:\"message-bubble\"},[(item.type == 'image')?_c('div',{staticClass:\"image-message\"},[_c('img',{attrs:{\"src\":item.content},on:{\"click\":function($event){return _vm.openImg(item.content)}}})]):_vm._e(),(item.type == 'text')?_c('div',{staticClass:\"text-message\"},[_vm._v(\" \"+_vm._s(item.content)+\" \")]):_vm._e(),(item.type == 'voice')?_c('div',{staticClass:\"voice-message\"},[_c('div',{staticClass:\"voice-content\"},[_c('audioplay',{attrs:{\"recordFile\":item.content}}),_c('span',{staticClass:\"voice-duration\"},[_vm._v(_vm._s(item.datas))])],1)]):_vm._e(),(item.type == 'file')?_c('div',{staticClass:\"file-message\"},[_c('div',{staticClass:\"file-content\",on:{\"click\":function($event){return _vm.openFile(item.content)}}},[_vm._m(0,true),_c('div',{staticClass:\"file-info\"},[_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(item.files.name))]),_c('div',{staticClass:\"file-size\"},[_vm._v(_vm._s(item.files.size))])])])]):_vm._e()])])])])}),0),_c('div',{staticClass:\"user-detail-sidebar\",class:{ 'show': _vm.showUserDetail },on:{\"click\":function($event){$event.stopPropagation();}}},[_c('div',{staticClass:\"detail-header\"},[_c('h3',[_vm._v(\"用户详情\")]),_c('el-button',{staticClass:\"close-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-close\"},on:{\"click\":function($event){_vm.showUserDetail = false}}})],1),_c('div',{staticClass:\"detail-content\"},[_c('div',{staticClass:\"detail-menu\"},[_c('div',{staticClass:\"menu-item\",class:{ 'active': _vm.activeDetailTab === 'info' },on:{\"click\":function($event){_vm.activeDetailTab = 'info'}}},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',[_vm._v(\"基本信息\")])]),_c('div',{staticClass:\"menu-item\",class:{ 'active': _vm.activeDetailTab === 'debtors' },on:{\"click\":function($event){_vm.activeDetailTab = 'debtors'}}},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',[_vm._v(\"关联债务人\")])]),_c('div',{staticClass:\"menu-item\",class:{ 'active': _vm.activeDetailTab === 'documents' },on:{\"click\":function($event){_vm.activeDetailTab = 'documents'}}},[_c('i',{staticClass:\"el-icon-folder\"}),_c('span',[_vm._v(\"相关文档\")])]),_c('div',{staticClass:\"menu-item\",class:{ 'active': _vm.activeDetailTab === 'orders' },on:{\"click\":function($event){_vm.activeDetailTab = 'orders'}}},[_c('i',{staticClass:\"el-icon-tickets\"}),_c('span',[_vm._v(\"工单记录\")])]),_c('div',{staticClass:\"menu-item\",class:{ 'active': _vm.activeDetailTab === 'payments' },on:{\"click\":function($event){_vm.activeDetailTab = 'payments'}}},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',[_vm._v(\"支付记录\")])])]),_c('div',{staticClass:\"detail-main\"},[(_vm.activeDetailTab === 'info')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"user-profile\"},[_c('div',{staticClass:\"profile-avatar\"},[_c('img',{attrs:{\"src\":_vm.currentUserDetail.avatar,\"alt\":\"用户头像\"}})]),_c('div',{staticClass:\"profile-info\"},[_c('h4',[_vm._v(_vm._s(_vm.currentUserDetail.name))]),_c('p',{staticClass:\"user-type\"},[_vm._v(_vm._s(_vm.currentUserDetail.type))])])]),_c('div',{staticClass:\"info-section\"},[_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"手机号码：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserDetail.phone))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"身份证号：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserDetail.idCard))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"注册时间：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserDetail.registerTime))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"最后登录：\")]),_c('span',[_vm._v(_vm._s(_vm.currentUserDetail.lastLogin))])]),_c('div',{staticClass:\"info-item\"},[_c('label',[_vm._v(\"用户状态：\")]),_c('el-tag',{attrs:{\"type\":_vm.currentUserDetail.status === '正常' ? 'success' : 'danger'}},[_vm._v(\" \"+_vm._s(_vm.currentUserDetail.status)+\" \")])],1)])]):_vm._e(),(_vm.activeDetailTab === 'debtors')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"section-header\"},[_c('h4',[_vm._v(\"关联债务人列表\")]),_c('span',{staticClass:\"count-badge\"},[_vm._v(_vm._s(_vm.currentUserDetail.debtors.length)+\"人\")])]),_c('div',{staticClass:\"debtors-list\"},_vm._l((_vm.currentUserDetail.debtors),function(debtor){return _c('div',{key:debtor.id,staticClass:\"debtor-card\"},[_c('div',{staticClass:\"debtor-info\"},[_c('div',{staticClass:\"debtor-name\"},[_vm._v(_vm._s(debtor.name))]),_c('div',{staticClass:\"debtor-details\"},[_c('span',{staticClass:\"debt-amount\"},[_vm._v(\"欠款金额：¥\"+_vm._s(debtor.amount))]),_c('span',{staticClass:\"debt-status\",class:debtor.status},[_vm._v(_vm._s(debtor.statusText))])])]),_c('div',{staticClass:\"debtor-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"}},[_vm._v(\"查看详情\")])],1)])}),0)]):_vm._e(),(_vm.activeDetailTab === 'documents')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"section-header\"},[_c('h4',[_vm._v(\"相关文档\")]),_c('span',{staticClass:\"count-badge\"},[_vm._v(_vm._s(_vm.currentUserDetail.documents.length)+\"个\")])]),_c('div',{staticClass:\"documents-list\"},_vm._l((_vm.currentUserDetail.documents),function(doc){return _c('div',{key:doc.id,staticClass:\"document-item\"},[_c('div',{staticClass:\"doc-icon\"},[_c('i',{class:_vm.getDocIcon(doc.type)})]),_c('div',{staticClass:\"doc-info\"},[_c('div',{staticClass:\"doc-name\"},[_vm._v(_vm._s(doc.name))]),_c('div',{staticClass:\"doc-meta\"},[_c('span',[_vm._v(_vm._s(doc.size))]),_c('span',[_vm._v(_vm._s(doc.uploadTime))])])]),_c('div',{staticClass:\"doc-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.downloadDoc(doc)}}},[_vm._v(\"下载\")]),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.previewDoc(doc)}}},[_vm._v(\"预览\")])],1)])}),0)]):_vm._e(),(_vm.activeDetailTab === 'orders')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"section-header\"},[_c('h4',[_vm._v(\"工单记录\")]),_c('span',{staticClass:\"count-badge\"},[_vm._v(_vm._s(_vm.currentUserDetail.orders.length)+\"个\")])]),_c('div',{staticClass:\"orders-list\"},_vm._l((_vm.currentUserDetail.orders),function(order){return _c('div',{key:order.id,staticClass:\"order-item\"},[_c('div',{staticClass:\"order-info\"},[_c('div',{staticClass:\"order-title\"},[_vm._v(_vm._s(order.title))]),_c('div',{staticClass:\"order-meta\"},[_c('span',{staticClass:\"order-type\"},[_vm._v(_vm._s(order.type))]),_c('span',{staticClass:\"order-time\"},[_vm._v(_vm._s(order.createTime))])])]),_c('div',{staticClass:\"order-status\"},[_c('el-tag',{attrs:{\"type\":_vm.getOrderStatusType(order.status)}},[_vm._v(_vm._s(order.status))])],1)])}),0)]):_vm._e(),(_vm.activeDetailTab === 'payments')?_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"section-header\"},[_c('h4',[_vm._v(\"支付记录\")]),_c('span',{staticClass:\"count-badge\"},[_vm._v(_vm._s(_vm.currentUserDetail.payments.length)+\"笔\")])]),_c('div',{staticClass:\"payments-list\"},_vm._l((_vm.currentUserDetail.payments),function(payment){return _c('div',{key:payment.id,staticClass:\"payment-item\"},[_c('div',{staticClass:\"payment-info\"},[_c('div',{staticClass:\"payment-desc\"},[_vm._v(_vm._s(payment.description))]),_c('div',{staticClass:\"payment-time\"},[_vm._v(_vm._s(payment.time))])]),_c('div',{staticClass:\"payment-amount\"},[_c('span',{staticClass:\"amount\"},[_vm._v(\"¥\"+_vm._s(payment.amount))]),_c('el-tag',{attrs:{\"type\":payment.status === '已支付' ? 'success' : 'warning',\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(payment.status)+\" \")])],1)])}),0)]):_vm._e()])])]),_c('div',{staticClass:\"input-section\"},[_c('div',{staticClass:\"toolbar\"},[_c('div',{staticClass:\"tool-item emoji-tool\"},[_c('el-tooltip',{attrs:{\"content\":\"发送表情\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"tool-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-sunny\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.openEmji.apply(null, arguments)}}})],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isEmji),expression:\"isEmji\"}],staticClass:\"emoji-panel\"},[_c('div',{staticClass:\"emoji-grid\"},_vm._l((_vm.emojiData),function(item,index){return _c('div',{key:index,staticClass:\"emoji-item\",on:{\"click\":function($event){return _vm.getEmoji(item)}}},[_vm._v(\" \"+_vm._s(item)+\" \")])}),0)])],1),_c('div',{staticClass:\"tool-item\"},[_c('el-tooltip',{attrs:{\"content\":\"发送图片\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadImage\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_c('el-button',{staticClass:\"tool-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-picture\"}})],1)],1)],1),_c('div',{staticClass:\"tool-item\"},[_c('el-tooltip',{attrs:{\"content\":\"发送文件\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess1,\"before-upload\":_vm.beforeUpload}},[_c('el-button',{staticClass:\"tool-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-folder\"}})],1)],1)],1),_c('div',{staticClass:\"tool-item\"},[_c('el-tooltip',{attrs:{\"content\":\"标记代办\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"tool-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-s-order\"},on:{\"click\":_vm.daiban}})],1)],1),_c('div',{staticClass:\"tool-item\"},[_c('el-tooltip',{attrs:{\"content\":\"查看工单\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"tool-btn\",attrs:{\"type\":\"text\",\"icon\":\"el-icon-tickets\"},on:{\"click\":_vm.showgongdan}})],1)],1)]),_c('div',{staticClass:\"input-wrapper\"},[_c('el-input',{staticClass:\"message-input\",attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":\"输入消息...\",\"resize\":\"none\"},model:{value:(_vm.textContent),callback:function ($$v) {_vm.textContent=$$v},expression:\"textContent\"}})],1),_c('div',{staticClass:\"send-section\"},[_c('el-tooltip',{attrs:{\"content\":\"发送消息 (Enter)\",\"placement\":\"top\",\"effect\":\"dark\"}},[_c('el-button',{staticClass:\"send-btn\",attrs:{\"type\":\"primary\",\"disabled\":!_vm.textContent.trim()},on:{\"click\":_vm.send}},[_c('i',{staticClass:\"el-icon-position\"}),_vm._v(\" 发送 \")])],1)],1)])])]),_c('el-dialog',{attrs:{\"title\":\"图片预览\",\"visible\":_vm.isShowPopup,\"width\":\"60%\",\"center\":\"\"},on:{\"update:visible\":function($event){_vm.isShowPopup=$event}}},[_c('div',{staticClass:\"image-preview\"},[_c('img',{attrs:{\"src\":_vm.imgUlr,\"alt\":\"预览图片\"}})])]),_c('el-drawer',{attrs:{\"title\":\"客户工单\",\"visible\":_vm.table,\"direction\":\"rtl\",\"size\":\"40%\"},on:{\"update:visible\":function($event){_vm.table=$event}}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.gridData}},[_c('el-table-column',{attrs:{\"property\":\"create_time\",\"label\":\"下单日期\",\"width\":\"150\"}}),_c('el-table-column',{attrs:{\"property\":\"title\",\"label\":\"需求标题\"}}),_c('el-table-column',{attrs:{\"property\":\"desc\",\"label\":\"需求描述\"}}),_c('el-table-column',{attrs:{\"property\":\"type_title\",\"label\":\"下单类型\"}}),_c('el-table-column',{attrs:{\"property\":\"is_deal_title\",\"label\":\"状态\"}}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editData(scope.row.id)}}},[_vm._v(\"完成制作\")])]}}])})],1)],1),_c('el-dialog',{attrs:{\"title\":\"工单详情\",\"visible\":_vm.dialogFormVisible,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm}},[_c('el-form-item',{attrs:{\"label\":\"工单类型\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.type_title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"type_title\", $$v)},expression:\"ruleForm.type_title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单标题\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"工单描述\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),_c('el-form-item',{attrs:{\"label\":\"制作状态\"}},[_c('div',[_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"已完成\")]),_c('el-radio',{attrs:{\"label\":1},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"处理中\")])],1)]),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type == 2)?_c('el-form-item',{attrs:{\"label\":\"请上传文件\",\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess1}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1):_vm._e(),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type != 2)?_c('el-form-item',{attrs:{\"label\":\"内容回复\"}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1)],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"file-icon\"},[_c('i',{staticClass:\"el-icon-document\"})])\n}]\n\nexport { render, staticRenderFns }", "const emojiData=[\r\n\t'😀',\r\n\t'😃',\r\n\t'😄',\r\n\t'😆',\r\n\t'😅',\r\n\t'🤣',\r\n\t'🙂',\r\n\t'😇',\r\n\t'🤩',\r\n\t'🤩',\r\n\t'😘',\r\n\t'😗',\r\n\t'😚',\r\n\t'😋',\r\n\t'😛',\r\n\t'🤪',\r\n\t'🙁',\r\n\t'😴',\r\n\t'😷',\r\n\t'🤮',\r\n\t'🥵',\r\n\t'🥵',\r\n\t'🤢',\r\n\t'🤢',\r\n\t'😦',\r\n\t'😰',\r\n\t'😥',\r\n\t'😱',\r\n\t'😈',\r\n\t'💀',\r\n\t'🤡',\r\n\t'👺',\r\n\t'👿',\r\n\t'😠',\r\n\t'😡',\r\n\t'😤',\r\n\t'🥱',\r\n\t'👻',\r\n\t'👽',\r\n\t'😺',\r\n\t'😸',\r\n\t'😹',\r\n\t'😻',\r\n\t'😼',\r\n\t'😽',\r\n\t'🙀',\r\n\t'😿',\r\n\t'🙈',\r\n\t'🙉',\r\n\t'🙊',\r\n\t'💋',\r\n\t'💌'\r\n\t\r\n\t\r\n\t\r\n\t\r\n]\r\nexport default emojiData", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('i',{class:_vm.isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause',staticStyle:{\"cursor\":\"pointer\",\"margin-right\":\"10px\",\"margin-top\":\"3px\",\"font-size\":\"25px\"},style:(_vm.isPlay == false ? '' : 'color: red;'),attrs:{\"slot\":\"reference\"},on:{\"click\":_vm.autoPlay},slot:\"reference\"})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <i\r\n    slot=\"reference\"\r\n    :style=\"isPlay == false ? '' : 'color: red;'\"\r\n    :class=\"isPlay == false ? 'el-icon-video-play' : 'el-icon-video-pause'\"\r\n    @click=\"autoPlay\"\r\n    style=\"\r\n      cursor: pointer;\r\n      margin-right: 10px;\r\n      margin-top: 3px;\r\n      font-size: 25px;\r\n    \"\r\n  ></i>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    recordFile: {\r\n      type: String,\r\n    },\r\n  },\r\n  name: \"audioplay\",\r\n  data() {\r\n    return {\r\n      isPlay: false,\r\n      myAuto: new Audio(this.recordFile),\r\n    };\r\n  },\r\n  methods: {\r\n    autoPlay() {\r\n      this.isPlay = !this.isPlay;\r\n      if (this.isPlay) {\r\n        this.myAuto.play();\r\n        this.palyEnd();\r\n      } else {\r\n        this.myAuto.pause();\r\n        this.palyEnd();\r\n      }\r\n    },\r\n    palyEnd() {\r\n      this.myAuto.addEventListener(\"ended\", () => {\r\n        this.isPlay = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped></style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./audioplay.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./audioplay.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./audioplay.vue?vue&type=template&id=51f04c58&scoped=true\"\nimport script from \"./audioplay.vue?vue&type=script&lang=js\"\nexport * from \"./audioplay.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51f04c58\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\" @click=\"showMemberPanel = false\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"用户详情\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-user\"\r\n                @click.stop=\"showUserDetail = !showUserDetail\"\r\n                class=\"user-detail-btn\"\r\n                :class=\"{ 'active': showUserDetail }\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button\r\n                type=\"text\"\r\n                icon=\"el-icon-more\"\r\n                @click.stop=\"toggleMemberPanel\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户详情侧边栏 -->\r\n        <div class=\"user-detail-sidebar\" :class=\"{ 'show': showUserDetail }\" @click.stop>\r\n          <div class=\"detail-header\">\r\n            <h3>用户详情</h3>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"showUserDetail = false\"\r\n              class=\"close-btn\"\r\n            ></el-button>\r\n          </div>\r\n\r\n          <div class=\"detail-content\">\r\n            <!-- 左侧子菜单 -->\r\n            <div class=\"detail-menu\">\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'info' }\"\r\n                   @click=\"activeDetailTab = 'info'\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>基本信息</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'debtors' }\"\r\n                   @click=\"activeDetailTab = 'debtors'\">\r\n                <i class=\"el-icon-s-custom\"></i>\r\n                <span>关联债务人</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'documents' }\"\r\n                   @click=\"activeDetailTab = 'documents'\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>相关文档</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'orders' }\"\r\n                   @click=\"activeDetailTab = 'orders'\">\r\n                <i class=\"el-icon-tickets\"></i>\r\n                <span>工单记录</span>\r\n              </div>\r\n              <div class=\"menu-item\"\r\n                   :class=\"{ 'active': activeDetailTab === 'payments' }\"\r\n                   @click=\"activeDetailTab = 'payments'\">\r\n                <i class=\"el-icon-money\"></i>\r\n                <span>支付记录</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧内容区域 -->\r\n            <div class=\"detail-main\">\r\n              <!-- 基本信息 -->\r\n              <div v-if=\"activeDetailTab === 'info'\" class=\"tab-content\">\r\n                <div class=\"user-profile\">\r\n                  <div class=\"profile-avatar\">\r\n                    <img :src=\"currentUserDetail.avatar\" alt=\"用户头像\" />\r\n                  </div>\r\n                  <div class=\"profile-info\">\r\n                    <h4>{{ currentUserDetail.name }}</h4>\r\n                    <p class=\"user-type\">{{ currentUserDetail.type }}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"info-section\">\r\n                  <div class=\"info-item\">\r\n                    <label>手机号码：</label>\r\n                    <span>{{ currentUserDetail.phone }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>身份证号：</label>\r\n                    <span>{{ currentUserDetail.idCard }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>注册时间：</label>\r\n                    <span>{{ currentUserDetail.registerTime }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>最后登录：</label>\r\n                    <span>{{ currentUserDetail.lastLogin }}</span>\r\n                  </div>\r\n                  <div class=\"info-item\">\r\n                    <label>用户状态：</label>\r\n                    <el-tag :type=\"currentUserDetail.status === '正常' ? 'success' : 'danger'\">\r\n                      {{ currentUserDetail.status }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 关联债务人 -->\r\n              <div v-if=\"activeDetailTab === 'debtors'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>关联债务人列表</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.debtors.length }}人</span>\r\n                </div>\r\n                <div class=\"debtors-list\">\r\n                  <div v-for=\"debtor in currentUserDetail.debtors\" :key=\"debtor.id\" class=\"debtor-card\">\r\n                    <div class=\"debtor-info\">\r\n                      <div class=\"debtor-name\">{{ debtor.name }}</div>\r\n                      <div class=\"debtor-details\">\r\n                        <span class=\"debt-amount\">欠款金额：¥{{ debtor.amount }}</span>\r\n                        <span class=\"debt-status\" :class=\"debtor.status\">{{ debtor.statusText }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"debtor-actions\">\r\n                      <el-button type=\"text\" size=\"small\">查看详情</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 相关文档 -->\r\n              <div v-if=\"activeDetailTab === 'documents'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>相关文档</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.documents.length }}个</span>\r\n                </div>\r\n                <div class=\"documents-list\">\r\n                  <div v-for=\"doc in currentUserDetail.documents\" :key=\"doc.id\" class=\"document-item\">\r\n                    <div class=\"doc-icon\">\r\n                      <i :class=\"getDocIcon(doc.type)\"></i>\r\n                    </div>\r\n                    <div class=\"doc-info\">\r\n                      <div class=\"doc-name\">{{ doc.name }}</div>\r\n                      <div class=\"doc-meta\">\r\n                        <span>{{ doc.size }}</span>\r\n                        <span>{{ doc.uploadTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"doc-actions\">\r\n                      <el-button type=\"text\" size=\"small\" @click=\"downloadDoc(doc)\">下载</el-button>\r\n                      <el-button type=\"text\" size=\"small\" @click=\"previewDoc(doc)\">预览</el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 工单记录 -->\r\n              <div v-if=\"activeDetailTab === 'orders'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>工单记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.orders.length }}个</span>\r\n                </div>\r\n                <div class=\"orders-list\">\r\n                  <div v-for=\"order in currentUserDetail.orders\" :key=\"order.id\" class=\"order-item\">\r\n                    <div class=\"order-info\">\r\n                      <div class=\"order-title\">{{ order.title }}</div>\r\n                      <div class=\"order-meta\">\r\n                        <span class=\"order-type\">{{ order.type }}</span>\r\n                        <span class=\"order-time\">{{ order.createTime }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"order-status\">\r\n                      <el-tag :type=\"getOrderStatusType(order.status)\">{{ order.status }}</el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 支付记录 -->\r\n              <div v-if=\"activeDetailTab === 'payments'\" class=\"tab-content\">\r\n                <div class=\"section-header\">\r\n                  <h4>支付记录</h4>\r\n                  <span class=\"count-badge\">{{ currentUserDetail.payments.length }}笔</span>\r\n                </div>\r\n                <div class=\"payments-list\">\r\n                  <div v-for=\"payment in currentUserDetail.payments\" :key=\"payment.id\" class=\"payment-item\">\r\n                    <div class=\"payment-info\">\r\n                      <div class=\"payment-desc\">{{ payment.description }}</div>\r\n                      <div class=\"payment-time\">{{ payment.time }}</div>\r\n                    </div>\r\n                    <div class=\"payment-amount\">\r\n                      <span class=\"amount\">¥{{ payment.amount }}</span>\r\n                      <el-tag :type=\"payment.status === '已支付' ? 'success' : 'warning'\" size=\"mini\">\r\n                        {{ payment.status }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n      showMemberPanel: false,\r\n      memberData: {\r\n        users: [],\r\n        lawyers: [],\r\n        staff: []\r\n      },\r\n      showUserDetail: true, // 默认打开用户详情面板\r\n      activeDetailTab: 'info', // 默认显示基本信息\r\n      currentUserDetail: {\r\n        name: '',\r\n        type: '',\r\n        avatar: '',\r\n        phone: '',\r\n        idCard: '',\r\n        registerTime: '',\r\n        lastLogin: '',\r\n        status: '',\r\n        debtors: [],\r\n        documents: [],\r\n        orders: [],\r\n        payments: []\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      _this.isEmji = !_this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      _this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      _this.imgUlr = img;\r\n      _this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        _this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      console.log('点击私聊:', index);\r\n      _this.selectId = index;\r\n      _this.quliaoIndex = -1;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    changeQun(index) {\r\n      console.log('点击群聊:', index);\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.showMemberPanel = false;\r\n      _this.loadTestMessages();\r\n      _this.loadUserDetailData(); // 更新用户详情数据\r\n    },\r\n    getEmoji(item) {\r\n      _this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (_this.search) _this.isShowSeach = true;\r\n      else _this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      _this.search = \"\";\r\n      _this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (_this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      // 群聊测试数据\r\n      _this.quns = [\r\n        {\r\n          id: 1,\r\n          title: \"法务团队群\",\r\n          desc: \"最新消息：合同审核已完成\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          create_time: \"09:30\",\r\n          count: 3,\r\n          uid: \"1,2,3\",\r\n          lvshi_id: \"1,2\",\r\n          yuangong_id: \"1,2,3\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"客户服务群\",\r\n          desc: \"张三：请问合同什么时候能完成？\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          create_time: \"昨天\",\r\n          count: 1,\r\n          uid: \"4,5\",\r\n          lvshi_id: \"3\",\r\n          yuangong_id: \"4,5\",\r\n          is_daiban: 2\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"紧急处理群\",\r\n          desc: \"李四：这个案件需要加急处理\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          create_time: \"15:20\",\r\n          count: 5,\r\n          uid: \"1,3,5\",\r\n          lvshi_id: \"1\",\r\n          yuangong_id: \"1,3\",\r\n          is_daiban: 1\r\n        }\r\n      ];\r\n\r\n      // 私聊用户测试数据\r\n      _this.users = [\r\n        {\r\n          id: 1,\r\n          title: \"张三（客户）\",\r\n          content: \"您好，请问我的合同审核进度如何？\",\r\n          pic_path: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n          time: \"10:30\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"李四（律师）\",\r\n          content: \"合同已经审核完毕，请查收\",\r\n          pic_path: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n          time: \"09:15\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"王五（调解员）\",\r\n          content: \"调解会议安排在明天下午2点\",\r\n          pic_path: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n          time: \"昨天\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"赵六（客户）\",\r\n          content: \"谢谢您的帮助！\",\r\n          pic_path: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n          time: \"16:45\"\r\n        }\r\n      ];\r\n\r\n      // 保存原始数据用于搜索\r\n      _this.yuanshiquns = [..._this.quns];\r\n      _this.yuanshiusers = [..._this.users];\r\n\r\n      // 设置默认选中第一个群聊\r\n      _this.selectId = -1;\r\n      _this.quliaoIndex = 0;\r\n\r\n      // 加载第一个群聊的消息\r\n      setTimeout(() => {\r\n        _this.loadTestMessages();\r\n      }, 500);\r\n    },\r\n    // 加载测试消息数据\r\n    loadTestMessages() {\r\n      console.log('加载测试消息, selectId:', _this.selectId, 'quliaoIndex:', _this.quliaoIndex);\r\n      // 设置当前聊天标题\r\n      if (_this.selectId !== -1) {\r\n        _this.title = _this.users[_this.selectId].title;\r\n        // 加载私聊消息\r\n        _this.loadPrivateMessages();\r\n      } else {\r\n        const qun = _this.quns[_this.quliaoIndex];\r\n        const count = qun.uid.split(',').length + qun.lvshi_id.split(',').length + qun.yuangong_id.split(',').length;\r\n        _this.title = qun.title + \"(\" + count + \")\";\r\n        // 加载群聊消息\r\n        _this.loadGroupMessages();\r\n      }\r\n\r\n      // 滚动到底部\r\n      _this.$nextTick(() => {\r\n        if (_this.$refs.list) {\r\n          _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n        }\r\n      });\r\n    },\r\n    // 加载群聊消息\r\n    loadGroupMessages() {\r\n      const groupMessages = {\r\n        0: [ // 法务团队群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:00:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"大家好，今天我们讨论一下最新的合同审核流程\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:05:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张专员\",\r\n            type: \"text\",\r\n            content: \"好的，我这边已经准备好相关材料了\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 09:10:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"请大家查看一下这份合同模板\"\r\n          },\r\n          {\r\n            id: 4,\r\n            create_time: \"2024-01-22 09:12:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"image\",\r\n            content: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          },\r\n          {\r\n            id: 5,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"这个模板看起来不错，我们可以在此基础上进行修改\"\r\n          }\r\n        ],\r\n        1: [ // 客户服务群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:00:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"请问合同什么时候能完成？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，合同预计明天下午可以完成审核\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:10:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢！\"\r\n          }\r\n        ],\r\n        2: [ // 紧急处理群\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 15:00:00\",\r\n            yuangong_id: 5,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李四\",\r\n            type: \"text\",\r\n            content: \"这个案件需要加急处理\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 15:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，我立即安排处理\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 15:10:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"我这边也会配合加急处理\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = groupMessages[_this.quliaoIndex] || [];\r\n      console.log('群聊消息加载完成:', _this.list.length);\r\n    },\r\n    // 加载私聊消息\r\n    loadPrivateMessages() {\r\n      const privateMessages = {\r\n        0: [ // 张三（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 10:30:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"您好，请问我的合同审核进度如何？\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 10:35:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"您好，您的合同正在审核中，预计今天下午可以完成\"\r\n          },\r\n          {\r\n            id: 3,\r\n            create_time: \"2024-01-22 10:40:00\",\r\n            yuangong_id: 4,\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\",\r\n            title: \"张三\",\r\n            type: \"text\",\r\n            content: \"好的，谢谢您！\"\r\n          }\r\n        ],\r\n        1: [ // 李四（律师）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 09:15:00\",\r\n            yuangong_id: 2,\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\",\r\n            title: \"李律师\",\r\n            type: \"text\",\r\n            content: \"合同已经审核完毕，请查收\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 09:20:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"收到，辛苦了！\"\r\n          }\r\n        ],\r\n        2: [ // 王五（调解员）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-21 16:00:00\",\r\n            yuangong_id: 3,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"王调解员\",\r\n            type: \"text\",\r\n            content: \"调解会议安排在明天下午2点\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-21 16:05:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"好的，我会准时参加\"\r\n          }\r\n        ],\r\n        3: [ // 赵六（客户）\r\n          {\r\n            id: 1,\r\n            create_time: \"2024-01-22 16:45:00\",\r\n            yuangong_id: 6,\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\",\r\n            title: \"赵六\",\r\n            type: \"text\",\r\n            content: \"谢谢您的帮助！\"\r\n          },\r\n          {\r\n            id: 2,\r\n            create_time: \"2024-01-22 16:50:00\",\r\n            yuangong_id: 1,\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\",\r\n            title: \"我\",\r\n            type: \"text\",\r\n            content: \"不客气，有问题随时联系我\"\r\n          }\r\n        ]\r\n      };\r\n\r\n      _this.list = privateMessages[_this.selectId] || [];\r\n      console.log('私聊消息加载完成:', _this.list.length);\r\n    },\r\n\r\n\r\n\r\n    // 新的群成员面板切换方法\r\n    toggleMemberPanel() {\r\n      console.log('切换群成员面板');\r\n      _this.showMemberPanel = !_this.showMemberPanel;\r\n      if (_this.showMemberPanel) {\r\n        _this.loadMemberData();\r\n      }\r\n    },\r\n\r\n    // 加载群成员数据\r\n    loadMemberData() {\r\n      console.log('加载群成员数据');\r\n      _this.memberData = {\r\n        users: [\r\n          {\r\n            id: 1,\r\n            name: \"张三\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 2,\r\n            name: \"李四\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 3,\r\n            name: \"王五\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          }\r\n        ],\r\n        lawyers: [\r\n          {\r\n            id: 4,\r\n            name: \"李律师\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          },\r\n          {\r\n            id: 5,\r\n            name: \"陈律师\",\r\n            avatar: \"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png\"\r\n          }\r\n        ],\r\n        staff: [\r\n          {\r\n            id: 6,\r\n            name: \"王调解员\",\r\n            avatar: \"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png\"\r\n          },\r\n          {\r\n            id: 7,\r\n            name: \"张专员\",\r\n            avatar: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\r\n          },\r\n          {\r\n            id: 8,\r\n            name: \"赵专员\",\r\n            avatar: \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\r\n          }\r\n        ]\r\n      };\r\n    },\r\n\r\n    // 加载用户详情数据\r\n    loadUserDetailData() {\r\n      // 根据当前选中的群聊加载对应用户信息\r\n      const currentQun = _this.quns[_this.quliaoIndex];\r\n      if (currentQun) {\r\n        _this.currentUserDetail = {\r\n          name: currentQun.title.replace(/群$/, '') + '用户',\r\n          type: '客户',\r\n          avatar: currentQun.pic_path,\r\n          phone: '138****8888',\r\n          idCard: '320***********1234',\r\n          registerTime: '2024-01-15 10:30:00',\r\n          lastLogin: '2024-01-22 09:15:00',\r\n          status: '正常',\r\n          debtors: [\r\n            {\r\n              id: 1,\r\n              name: '张三',\r\n              amount: '50000.00',\r\n              status: 'overdue',\r\n              statusText: '逾期'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '李四',\r\n              amount: '30000.00',\r\n              status: 'normal',\r\n              statusText: '正常'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '王五',\r\n              amount: '80000.00',\r\n              status: 'settled',\r\n              statusText: '已结清'\r\n            }\r\n          ],\r\n          documents: [\r\n            {\r\n              id: 1,\r\n              name: '身份证正面.jpg',\r\n              type: 'image',\r\n              size: '2.5MB',\r\n              uploadTime: '2024-01-15'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '营业执照.pdf',\r\n              type: 'pdf',\r\n              size: '1.8MB',\r\n              uploadTime: '2024-01-16'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '合同文件.docx',\r\n              type: 'word',\r\n              size: '856KB',\r\n              uploadTime: '2024-01-18'\r\n            }\r\n          ],\r\n          orders: [\r\n            {\r\n              id: 1,\r\n              title: '合同审核申请',\r\n              type: '法律咨询',\r\n              status: '已完成',\r\n              createTime: '2024-01-20'\r\n            },\r\n            {\r\n              id: 2,\r\n              title: '债务追讨服务',\r\n              type: '债务处理',\r\n              status: '处理中',\r\n              createTime: '2024-01-21'\r\n            },\r\n            {\r\n              id: 3,\r\n              title: '法律文书起草',\r\n              type: '文书服务',\r\n              status: '待处理',\r\n              createTime: '2024-01-22'\r\n            }\r\n          ],\r\n          payments: [\r\n            {\r\n              id: 1,\r\n              description: '法律咨询费用',\r\n              amount: '500.00',\r\n              status: '已支付',\r\n              time: '2024-01-20 14:30:00'\r\n            },\r\n            {\r\n              id: 2,\r\n              description: '合同审核费用',\r\n              amount: '800.00',\r\n              status: '已支付',\r\n              time: '2024-01-21 10:15:00'\r\n            },\r\n            {\r\n              id: 3,\r\n              description: '债务处理服务费',\r\n              amount: '1200.00',\r\n              status: '待支付',\r\n              time: '2024-01-22 09:00:00'\r\n            }\r\n          ]\r\n        };\r\n      }\r\n    },\r\n\r\n    // 获取文档图标\r\n    getDocIcon(type) {\r\n      const iconMap = {\r\n        image: 'el-icon-picture',\r\n        pdf: 'el-icon-document',\r\n        word: 'el-icon-document',\r\n        excel: 'el-icon-s-grid',\r\n        default: 'el-icon-document'\r\n      };\r\n      return iconMap[type] || iconMap.default;\r\n    },\r\n\r\n    // 获取工单状态类型\r\n    getOrderStatusType(status) {\r\n      const typeMap = {\r\n        '已完成': 'success',\r\n        '处理中': 'warning',\r\n        '待处理': 'info',\r\n        '已取消': 'danger'\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 下载文档\r\n    downloadDoc(doc) {\r\n      console.log('下载文档:', doc.name);\r\n      _this.$message.success('开始下载: ' + doc.name);\r\n    },\r\n\r\n    // 预览文档\r\n    previewDoc(doc) {\r\n      console.log('预览文档:', doc.name);\r\n      _this.$message.info('预览功能开发中...');\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    // 使用测试数据替代API调用\r\n    _this.loadTestData();\r\n    _this.loadUserDetailData();\r\n    _this.yon_id = 1; // 设置当前用户ID\r\n    // 注释掉定时器，避免不必要的API调用\r\n    // _this.timer = setInterval(() => {\r\n    //   _this.getMoreList();\r\n    // }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 用户详情侧边栏 */\r\n.user-detail-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -600px;\r\n  width: 600px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  display: flex;\r\n  height: calc(100% - 80px);\r\n}\r\n\r\n.detail-menu {\r\n  width: 200px;\r\n  background: #f8f9fa;\r\n  border-right: 1px solid #e9ecef;\r\n  padding: 20px 0;\r\n}\r\n\r\n.menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  color: #6c757d;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    color: #495057;\r\n  }\r\n\r\n  &.active {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n  }\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  span {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.detail-main {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.profile-avatar {\r\n  margin-right: 20px;\r\n\r\n  img {\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 3px solid #ffffff;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.profile-info {\r\n  h4 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .user-type {\r\n    margin: 0;\r\n    font-size: 14px;\r\n    color: #6c757d;\r\n    padding: 4px 12px;\r\n    background: #e9ecef;\r\n    border-radius: 20px;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.info-section {\r\n  .info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    label {\r\n      width: 120px;\r\n      font-weight: 500;\r\n      color: #495057;\r\n      margin: 0;\r\n    }\r\n\r\n    span {\r\n      color: #212529;\r\n    }\r\n  }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .count-badge {\r\n    background: #007bff;\r\n    color: #ffffff;\r\n    padding: 4px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.debtors-list {\r\n  .debtor-card {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n      transform: translateY(-1px);\r\n    }\r\n  }\r\n\r\n  .debtor-info {\r\n    .debtor-name {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .debtor-details {\r\n      display: flex;\r\n      gap: 15px;\r\n\r\n      .debt-amount {\r\n        color: #dc3545;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .debt-status {\r\n        padding: 2px 8px;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n\r\n        &.overdue {\r\n          background: #f8d7da;\r\n          color: #721c24;\r\n        }\r\n\r\n        &.normal {\r\n          background: #d4edda;\r\n          color: #155724;\r\n        }\r\n\r\n        &.settled {\r\n          background: #cce7ff;\r\n          color: #004085;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.documents-list {\r\n  .document-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 8px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .doc-icon {\r\n    margin-right: 12px;\r\n    color: #6c757d;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .doc-info {\r\n    flex: 1;\r\n\r\n    .doc-name {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .doc-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .doc-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n.orders-list {\r\n  .order-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .order-info {\r\n    .order-title {\r\n      font-weight: 600;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .order-meta {\r\n      display: flex;\r\n      gap: 15px;\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n\r\n      .order-type {\r\n        padding: 2px 8px;\r\n        background: #e9ecef;\r\n        border-radius: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.payments-list {\r\n  .payment-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n\r\n  .payment-info {\r\n    .payment-desc {\r\n      font-weight: 500;\r\n      color: #212529;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .payment-time {\r\n      font-size: 12px;\r\n      color: #6c757d;\r\n    }\r\n  }\r\n\r\n  .payment-amount {\r\n    text-align: right;\r\n\r\n    .amount {\r\n      font-weight: 600;\r\n      color: #28a745;\r\n      margin-bottom: 4px;\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n.member-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #212529;\r\n  }\r\n\r\n  .close-btn {\r\n    font-size: 18px;\r\n    color: #6c757d;\r\n    padding: 4px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      color: #495057;\r\n      background: #e9ecef;\r\n    }\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 用户详情按钮样式 */\r\n.user-detail-btn {\r\n  color: #606266;\r\n  font-size: 16px;\r\n  margin-right: 8px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #409eff;\r\n    transform: scale(1.1);\r\n  }\r\n\r\n  &.active {\r\n    color: #409eff;\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n\r\n/* 手机端适配 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    flex-direction: column;\r\n    height: 100vh;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    max-height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    overflow-y: hidden;\r\n  }\r\n\r\n  .sidebar-header {\r\n    padding: 10px 15px;\r\n  }\r\n\r\n  .search-box {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .contact-list {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 10px;\r\n    padding: 0 15px 15px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 120px;\r\n    flex-shrink: 0;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .chat-main {\r\n    flex: 1;\r\n    height: calc(100vh - 200px);\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .chat-header {\r\n    padding: 10px 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .chat-actions {\r\n    gap: 5px;\r\n  }\r\n\r\n  .user-detail-btn,\r\n  .more-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-list {\r\n    flex: 1;\r\n    padding: 10px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .message-item {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 85%;\r\n  }\r\n\r\n  .message-avatar img {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n\r\n  .message-bubble {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .sender-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 15px;\r\n    border-top: 1px solid #e9ecef;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 3px;\r\n    margin-bottom: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .message-input {\r\n    ::v-deep .el-textarea__inner {\r\n      font-size: 14px;\r\n      padding: 8px 12px;\r\n    }\r\n  }\r\n\r\n  .send-btn {\r\n    padding: 8px 16px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 用户详情侧边栏手机端适配 */\r\n  .user-detail-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n    z-index: 1000;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .detail-header {\r\n    padding: 15px;\r\n    border-bottom: 1px solid #e9ecef;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-content {\r\n    flex-direction: column;\r\n    height: calc(100vh - 60px);\r\n  }\r\n\r\n  .detail-menu {\r\n    width: 100%;\r\n    height: auto;\r\n    flex-direction: row;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e9ecef;\r\n    overflow-x: auto;\r\n    padding: 0;\r\n  }\r\n\r\n  .menu-item {\r\n    min-width: 100px;\r\n    flex-shrink: 0;\r\n    padding: 12px 15px;\r\n    border-bottom: none;\r\n    border-right: 1px solid #e9ecef;\r\n\r\n    &:last-child {\r\n      border-right: none;\r\n    }\r\n\r\n    span {\r\n      font-size: 12px;\r\n    }\r\n\r\n    i {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .detail-main {\r\n    flex: 1;\r\n    padding: 15px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .user-profile {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .profile-avatar img {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .profile-info h4 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .info-item {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .section-header {\r\n    margin-bottom: 15px;\r\n\r\n    h4 {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .debtor-card,\r\n  .document-item,\r\n  .order-item,\r\n  .payment-item {\r\n    padding: 12px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 群成员面板手机端适配 */\r\n  .member-sidebar {\r\n    width: 100%;\r\n    height: 100vh;\r\n    top: 0;\r\n    right: -100%;\r\n\r\n    &.show {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .member-header {\r\n    padding: 15px;\r\n\r\n    h3 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .member-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));\r\n    gap: 10px;\r\n    padding: 15px;\r\n  }\r\n\r\n  .member-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n\r\n  .member-name {\r\n    font-size: 11px;\r\n  }\r\n\r\n  /* 表情面板手机端适配 */\r\n  .emoji-panel {\r\n    width: 280px !important;\r\n    height: 180px !important;\r\n    bottom: 45px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(6, 1fr) !important;\r\n    padding: 10px !important;\r\n  }\r\n\r\n  .emoji-item {\r\n    padding: 8px !important;\r\n    font-size: 18px !important;\r\n  }\r\n}\r\n\r\n/* 超小屏幕适配 (小于480px) */\r\n@media (max-width: 480px) {\r\n  .sidebar {\r\n    max-height: 150px;\r\n  }\r\n\r\n  .contact-item {\r\n    min-width: 100px;\r\n  }\r\n\r\n  .chat-main {\r\n    height: calc(100vh - 150px);\r\n  }\r\n\r\n  .message-wrapper {\r\n    max-width: 90%;\r\n  }\r\n\r\n  .message-bubble {\r\n    font-size: 13px;\r\n    padding: 6px 10px;\r\n  }\r\n\r\n  .input-section {\r\n    padding: 10px;\r\n  }\r\n\r\n  .toolbar {\r\n    gap: 2px;\r\n  }\r\n\r\n  .tool-btn {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .detail-menu {\r\n    .menu-item {\r\n      min-width: 80px;\r\n      padding: 10px 12px;\r\n\r\n      span {\r\n        font-size: 11px;\r\n      }\r\n\r\n      i {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .emoji-panel {\r\n    width: 250px !important;\r\n    height: 160px !important;\r\n  }\r\n\r\n  .emoji-grid {\r\n    grid-template-columns: repeat(5, 1fr) !important;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./chat.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./chat.vue?vue&type=template&id=72349a68&scoped=true\"\nimport script from \"./chat.vue?vue&type=script&lang=js\"\nexport * from \"./chat.vue?vue&type=script&lang=js\"\nimport style0 from \"./chat.vue?vue&type=style&index=0&id=72349a68&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72349a68\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}