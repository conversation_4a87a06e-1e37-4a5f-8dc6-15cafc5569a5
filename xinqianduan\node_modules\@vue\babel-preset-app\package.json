{"name": "@vue/babel-preset-app", "version": "4.5.19", "description": "babel-preset-app for vue-cli", "main": "index.js", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/babel-preset-app"}, "keywords": ["vue", "cli"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/babel-preset-app#readme", "dependencies": {"@babel/core": "^7.11.0", "@babel/helper-compilation-targets": "^7.9.6", "@babel/helper-module-imports": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-jsx": "^7.8.3", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@babel/runtime": "^7.11.0", "@vue/babel-plugin-jsx": "^1.0.3", "@vue/babel-preset-jsx": "^1.2.4", "babel-plugin-dynamic-import-node": "^2.3.3", "core-js": "^3.6.5", "core-js-compat": "^3.6.5", "semver": "^6.1.0"}, "peerDependencies": {"@babel/core": "*", "core-js": "^3", "vue": "^2 || ^3.0.0-0"}, "peerDependenciesMeta": {"core-js": {"optional": true}, "vue": {"optional": true}}, "gitHead": "bef7a67566585876d56fa0e41b364675467bba8f"}