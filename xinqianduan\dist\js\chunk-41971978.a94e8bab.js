(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-41971978"],{"0091":function(t,e,s){"use strict";s("67a0")},1305:function(t,e,s){"use strict";s("1c1f")},"13d5":function(t,e,s){"use strict";var a=s("23e7"),i=s("d58f").left,l=s("a640"),n=s("2d00"),r=s("605d"),o=!r&&n>79&&n<83,c=o||!l("reduce");a({target:"Array",proto:!0,forced:c},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"1c1f":function(t,e,s){},"271a":function(t,e,s){"use strict";var a=s("cb2d"),i=s("e330"),l=s("577e"),n=s("d6d6"),r=URLSearchParams,o=r.prototype,c=i(o.getAll),d=i(o.has),u=new r("a=1");!u.has("a",2)&&u.has("a",void 0)||a(o,"has",(function(t){var e=arguments.length,s=e<2?void 0:arguments[1];if(e&&void 0===s)return d(this,t);var a=c(this,t);n(e,1);var i=l(s),r=0;while(r<a.length)if(a[r++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},5494:function(t,e,s){"use strict";var a=s("83ab"),i=s("e330"),l=s("edd0"),n=URLSearchParams.prototype,r=i(n.forEach);a&&!("size"in n)&&l(n,"size",{get:function(){var t=0;return r(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"605d":function(t,e,s){"use strict";var a=s("da84"),i=s("c6b6");t.exports="process"===i(a.process)},"67a0":function(t,e,s){},"88a7":function(t,e,s){"use strict";var a=s("cb2d"),i=s("e330"),l=s("577e"),n=s("d6d6"),r=URLSearchParams,o=r.prototype,c=i(o.append),d=i(o["delete"]),u=i(o.forEach),h=i([].push),v=new r("a=1&a=2&b=3");v["delete"]("a",1),v["delete"]("b",void 0),v+""!=="a=2"&&a(o,"delete",(function(t){var e=arguments.length,s=e<2?void 0:arguments[1];if(e&&void 0===s)return d(this,t);var a=[];u(this,(function(t,e){h(a,{key:e,value:t})})),n(e,1);var i,r=l(t),o=l(s),v=0,m=0,p=!1,g=a.length;while(v<g)i=a[v++],p||i.key===r?(p=!0,d(this,i.key)):m++;while(m<g)i=a[m++],i.key===r&&i.value===o||c(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},a578:function(t,e,s){"use strict";s.r(e);var a=function(){var t,e,s,a,i,l,n,r,o,c=this,d=c._self._c;return d("div",{staticClass:"order-management-container"},[d("div",{staticClass:"page-header"},[d("div",{staticClass:"header-left"},[d("h2",{staticClass:"page-title"},[d("i",{staticClass:"el-icon-s-order"}),c._v(" "+c._s(this.$router.currentRoute.name)+" ")]),d("div",{staticClass:"page-subtitle"},[c._v("管理客户签约订单和合同信息")])]),d("div",{staticClass:"header-actions"},[d("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:c.refulsh}},[c._v(" 刷新数据 ")])],1)]),c.hasImportantNotifications?d("div",{staticClass:"notifications-section"},[d("el-card",{staticClass:"notification-card",attrs:{shadow:"hover"}},[d("div",{staticClass:"notification-header"},[d("i",{staticClass:"el-icon-warning-outline notification-icon"}),d("span",{staticClass:"notification-title"},[c._v("重要提醒")]),d("el-button",{staticClass:"dismiss-btn",attrs:{type:"text",size:"mini"},on:{click:c.dismissNotifications}},[d("i",{staticClass:"el-icon-close"})])],1),d("div",{staticClass:"notification-content"},[d("div",{staticClass:"notification-list"},[c.pendingOrders>0?d("div",{staticClass:"notification-item urgent",on:{click:function(t){return c.filterByStatus("1")}}},[d("div",{staticClass:"notification-dot"}),d("div",{staticClass:"notification-text"},[d("strong",[c._v(c._s(c.pendingOrders))]),c._v(" 个订单待审核，请及时处理 ")]),d("div",{staticClass:"notification-action"},[d("el-button",{attrs:{size:"mini",type:"warning"}},[c._v("立即处理")])],1)]):c._e(),c.expiringOrders.length>0?d("div",{staticClass:"notification-item warning",on:{click:c.showExpiringOrders}},[d("div",{staticClass:"notification-dot"}),d("div",{staticClass:"notification-text"},[d("strong",[c._v(c._s(c.expiringOrders.length))]),c._v(" 个订单即将到期，请提醒客户续费 ")]),d("div",{staticClass:"notification-action"},[d("el-button",{attrs:{size:"mini",type:"primary"}},[c._v("查看详情")])],1)]):c._e(),c.expiredOrders.length>0?d("div",{staticClass:"notification-item error",on:{click:c.showExpiredOrders}},[d("div",{staticClass:"notification-dot"}),d("div",{staticClass:"notification-text"},[d("strong",[c._v(c._s(c.expiredOrders.length))]),c._v(" 个订单已过期，需要联系客户处理 ")]),d("div",{staticClass:"notification-action"},[d("el-button",{attrs:{size:"mini",type:"danger"}},[c._v("紧急处理")])],1)]):c._e(),c.highValueOrders.length>0?d("div",{staticClass:"notification-item info",on:{click:c.showHighValueOrders}},[d("div",{staticClass:"notification-dot"}),d("div",{staticClass:"notification-text"},[d("strong",[c._v(c._s(c.highValueOrders.length))]),c._v(" 个高价值订单需要重点关注 ")]),d("div",{staticClass:"notification-action"},[d("el-button",{attrs:{size:"mini",type:"success"}},[c._v("查看订单")])],1)]):c._e()])])])],1):c._e(),d("div",{staticClass:"stats-section"},[d("el-row",{attrs:{gutter:20}},[d("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[d("div",{staticClass:"stat-card",on:{click:function(t){return c.filterByStatus("")}}},[d("div",{staticClass:"stat-icon total-icon"},[d("i",{staticClass:"el-icon-s-order"})]),d("div",{staticClass:"stat-content"},[d("div",{staticClass:"stat-number"},[c._v(c._s(c.total))]),d("div",{staticClass:"stat-label"},[c._v("总订单数")]),d("div",{staticClass:"stat-change positive"},[d("i",{staticClass:"el-icon-arrow-up"}),c._v(" +8% ")])])])]),d("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[d("div",{staticClass:"stat-card",on:{click:function(t){return c.filterByStatus("1")}}},[d("div",{staticClass:"stat-icon pending-icon"},[d("i",{staticClass:"el-icon-time"})]),d("div",{staticClass:"stat-content"},[d("div",{staticClass:"stat-number"},[c._v(c._s(c.pendingOrders))]),d("div",{staticClass:"stat-label"},[c._v("待审核")]),d("div",{staticClass:"stat-change warning"},[d("i",{staticClass:"el-icon-warning"}),c._v(" 需关注 ")])])])]),d("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[d("div",{staticClass:"stat-card",on:{click:function(t){return c.filterByStatus("2")}}},[d("div",{staticClass:"stat-icon approved-icon"},[d("i",{staticClass:"el-icon-circle-check"})]),d("div",{staticClass:"stat-content"},[d("div",{staticClass:"stat-number"},[c._v(c._s(c.approvedOrders))]),d("div",{staticClass:"stat-label"},[c._v("已通过")]),d("div",{staticClass:"stat-change positive"},[d("i",{staticClass:"el-icon-arrow-up"}),c._v(" +12% ")])])])]),d("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[d("div",{staticClass:"stat-card",on:{click:c.showRevenueChart}},[d("div",{staticClass:"stat-icon revenue-icon"},[d("i",{staticClass:"el-icon-money"})]),d("div",{staticClass:"stat-content"},[d("div",{staticClass:"stat-number"},[c._v("¥"+c._s(c.totalRevenue))]),d("div",{staticClass:"stat-label"},[c._v("总收入")]),d("div",{staticClass:"stat-change positive"},[d("i",{staticClass:"el-icon-arrow-up"}),c._v(" +15% ")])])])])],1)],1),d("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[d("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[d("div",{staticClass:"header-left"},[d("span",{staticClass:"card-title"},[d("i",{staticClass:"el-icon-search"}),c._v(" 搜索与筛选 ")]),d("div",{staticClass:"card-subtitle"},[c._v("快速查找和管理订单信息")])]),d("div",{staticClass:"header-actions"},[d("el-button-group",{staticClass:"action-group"},[d("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:c.exportData}},[c._v(" 导出 ")]),d("el-button",{attrs:{size:"small",icon:"el-icon-refresh"},on:{click:c.refreshData}},[c._v(" 刷新 ")])],1),d("el-button",{staticClass:"primary-action",attrs:{type:"primary",icon:"el-icon-check"},on:{click:c.batchAudit}},[c._v(" 批量审核 ")])],1)]),d("div",{staticClass:"search-section"},[d("el-form",{staticClass:"search-form",attrs:{model:c.search,inline:!0}},[d("div",{staticClass:"search-row"},[d("el-form-item",{staticClass:"search-item-main",attrs:{label:"关键词搜索"}},[d("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入订单号/购买人/套餐/手机号",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&c._k(t.keyCode,"enter",13,t.key,"Enter")?null:c.getData()}},model:{value:c.search.keyword,callback:function(t){c.$set(c.search,"keyword",t)},expression:"search.keyword"}})],1),d("el-form-item",{staticClass:"search-item",attrs:{label:"业务员"}},[d("el-input",{staticClass:"search-select",attrs:{placeholder:"请输入业务员姓名",clearable:""},model:{value:c.search.salesman,callback:function(t){c.$set(c.search,"salesman",t)},expression:"search.salesman"}})],1),d("el-form-item",{staticClass:"search-item",attrs:{label:"审核状态"}},[d("el-select",{staticClass:"search-select",attrs:{placeholder:"选择状态",clearable:""},model:{value:c.search.status,callback:function(t){c.$set(c.search,"status",t)},expression:"search.status"}},[d("el-option",{attrs:{label:"全部状态",value:""}}),d("el-option",{attrs:{label:"未审核",value:"1"}}),d("el-option",{attrs:{label:"已通过",value:"2"}}),d("el-option",{attrs:{label:"未通过",value:"3"}})],1)],1),d("el-form-item",{staticClass:"search-actions-item"},[d("div",{staticClass:"search-actions"},[d("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return c.getData()}}},[c._v(" 搜索 ")]),d("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh-left"},on:{click:c.resetSearch}},[c._v(" 重置 ")]),d("el-button",{staticClass:"toggle-btn",attrs:{type:"text"},on:{click:c.toggleAdvanced}},[d("i",{class:c.showAdvanced?"el-icon-arrow-up":"el-icon-arrow-down"}),c._v(" "+c._s(c.showAdvanced?"收起":"高级筛选")+" ")])],1)])],1),d("transition",{attrs:{name:"slide-fade"}},[d("div",{directives:[{name:"show",rawName:"v-show",value:c.showAdvanced,expression:"showAdvanced"}],staticClass:"advanced-search"},[d("el-divider",{attrs:{"content-position":"left"}},[d("i",{staticClass:"el-icon-setting"}),c._v(" 高级筛选选项 ")]),d("div",{staticClass:"advanced-content"},[d("div",{staticClass:"advanced-row"},[d("el-form-item",{staticClass:"advanced-item",attrs:{label:"支付时间"}},[d("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},model:{value:c.search.refund_time,callback:function(t){c.$set(c.search,"refund_time",t)},expression:"search.refund_time"}})],1),d("el-form-item",{staticClass:"advanced-item",attrs:{label:"支付方式"}},[d("el-select",{staticClass:"pay-select",attrs:{placeholder:"选择支付方式"},model:{value:c.search.payType,callback:function(t){c.$set(c.search,"payType",t)},expression:"search.payType"}},[d("el-option",{attrs:{label:"全部方式",value:""}}),d("el-option",{attrs:{label:"全款支付",value:"1"}}),d("el-option",{attrs:{label:"分期付款",value:"2"}})],1)],1),d("el-form-item",{staticClass:"advanced-item",attrs:{label:"金额范围"}},[d("div",{staticClass:"amount-range"},[d("el-input-number",{attrs:{placeholder:"最小金额",min:0,precision:2,"controls-position":"right",size:"small"},model:{value:c.search.minAmount,callback:function(t){c.$set(c.search,"minAmount",t)},expression:"search.minAmount"}}),d("span",{staticClass:"range-separator"},[c._v("-")]),d("el-input-number",{attrs:{placeholder:"最大金额",min:0,precision:2,"controls-position":"right",size:"small"},model:{value:c.search.maxAmount,callback:function(t){c.$set(c.search,"maxAmount",t)},expression:"search.maxAmount"}})],1)])],1),d("div",{staticClass:"advanced-row"},[d("el-form-item",{staticClass:"advanced-item",attrs:{label:"套餐类型"}},[d("el-select",{staticClass:"package-select",attrs:{placeholder:"选择套餐",clearable:""},model:{value:c.search.packageType,callback:function(t){c.$set(c.search,"packageType",t)},expression:"search.packageType"}},[d("el-option",{attrs:{label:"全部套餐",value:""}}),d("el-option",{attrs:{label:"基础套餐",value:"basic"}}),d("el-option",{attrs:{label:"高级套餐",value:"advanced"}}),d("el-option",{attrs:{label:"专业套餐",value:"professional"}})],1)],1),d("el-form-item",{staticClass:"advanced-item",attrs:{label:"排序方式"}},[d("el-select",{staticClass:"sort-select",attrs:{placeholder:"排序字段"},model:{value:c.search.sortBy,callback:function(t){c.$set(c.search,"sortBy",t)},expression:"search.sortBy"}},[d("el-option",{attrs:{label:"创建时间",value:"create_time"}}),d("el-option",{attrs:{label:"支付金额",value:"pay_age"}}),d("el-option",{attrs:{label:"订单状态",value:"status"}}),d("el-option",{attrs:{label:"到期时间",value:"end_time"}})],1)],1),d("el-form-item",{staticClass:"advanced-actions"},[d("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-check"},on:{click:c.applyAdvancedSearch}},[c._v(" 应用筛选 ")]),d("el-button",{attrs:{size:"small",icon:"el-icon-close"},on:{click:c.clearAdvancedSearch}},[c._v(" 清空选项 ")])],1)],1)])],1)])],1)],1)]),d("el-card",{staticClass:"table-card",attrs:{shadow:"hover"}},[d("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[d("div",{staticClass:"header-left"},[d("span",{staticClass:"card-title"},[d("i",{staticClass:"el-icon-tickets"}),c._v(" 订单列表 ")]),c.selectedRows.length>0?d("div",{staticClass:"selected-info"},[c._v(" 已选择 "+c._s(c.selectedRows.length)+" 项 ")]):c._e()]),d("div",{staticClass:"table-actions"},[d("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:c.exportData}},[c._v(" 导出数据 ")]),d("el-button",{attrs:{size:"small",icon:"el-icon-check",disabled:0===c.selectedRows.length,type:"success"},on:{click:c.batchApprove}},[c._v(" 批量通过 ")]),d("el-button",{attrs:{size:"small",icon:"el-icon-close",disabled:0===c.selectedRows.length,type:"danger"},on:{click:c.batchReject}},[c._v(" 批量拒绝 ")])],1)]),d("el-table",{directives:[{name:"loading",rawName:"v-loading",value:c.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:c.list,"row-class-name":c.tableRowClassName},on:{"selection-change":c.handleSelectionChange}},[d("el-table-column",{attrs:{type:"selection",width:"55"}}),d("el-table-column",{attrs:{label:"客户信息","min-width":"200"},scopedSlots:c._u([{key:"default",fn:function(t){var e,s,a;return[d("div",{staticClass:"client-info",on:{click:function(e){var s;return c.viewUserData(null===(s=t.row.client)||void 0===s?void 0:s.id)}}},[d("div",{staticClass:"client-header"},[d("div",{staticClass:"client-avatar"},[d("i",{staticClass:"el-icon-office-building"})]),d("div",{staticClass:"client-details"},[d("div",{staticClass:"company-name"},[c._v(" "+c._s((null===(e=t.row.client)||void 0===e?void 0:e.company)||"未填写")+" ")]),d("div",{staticClass:"contact-info"},[d("span",{staticClass:"contact-name"},[d("i",{staticClass:"el-icon-user"}),c._v(" "+c._s((null===(s=t.row.client)||void 0===s?void 0:s.linkman)||"未填写")+" ")])]),d("div",{staticClass:"contact-phone"},[d("i",{staticClass:"el-icon-phone"}),c._v(" "+c._s((null===(a=t.row.client)||void 0===a?void 0:a.phone)||"未填写")+" ")])])])])]}}])}),d("el-table-column",{attrs:{label:"套餐内容","min-width":"180"},scopedSlots:c._u([{key:"default",fn:function(t){var e,s,a;return[d("div",{staticClass:"package-info"},[d("div",{staticClass:"package-name"},[d("i",{staticClass:"el-icon-box"}),c._v(" "+c._s((null===(e=t.row.taocan)||void 0===e?void 0:e.title)||"未选择套餐")+" ")]),d("div",{staticClass:"package-price"},[d("span",{staticClass:"price-label"},[c._v("价格：")]),d("span",{staticClass:"price-value"},[c._v("¥"+c._s((null===(s=t.row.taocan)||void 0===s?void 0:s.price)||0))])]),d("div",{staticClass:"package-duration"},[d("el-tag",{attrs:{size:"small",type:"info"}},[c._v(" "+c._s((null===(a=t.row.taocan)||void 0===a?void 0:a.year)||0)+"年服务 ")])],1)])]}}])}),d("el-table-column",{attrs:{label:"支付情况","min-width":"160"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("div",{staticClass:"payment-info"},[d("div",{staticClass:"payment-type"},[d("i",{staticClass:"el-icon-wallet"}),c._v(" "+c._s(1==t.row.pay_type?"全款":`分期/${t.row.qishu}期`)+" ")]),d("div",{staticClass:"payment-amount"},[d("span",{staticClass:"paid"},[c._v("已付：¥"+c._s(t.row.pay_age))])]),1!=t.row.pay_type?d("div",{staticClass:"remaining-amount"},[d("span",{staticClass:"remaining"},[c._v("余款：¥"+c._s(t.row.total_price-t.row.pay_age))])]):c._e(),1!=t.row.pay_type?d("div",{staticClass:"payment-progress"},[d("el-progress",{attrs:{percentage:Math.round(t.row.pay_age/t.row.total_price*100),"stroke-width":6,"show-text":!1}})],1):c._e()])]}}])}),d("el-table-column",{attrs:{label:"审核状态",width:"120"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("div",{staticClass:"status-info",on:{click:function(e){return c.showStatus(t.row)}}},[d("el-tag",{staticClass:"status-tag",attrs:{type:c.getStatusType(t.row.status),effect:1==t.row.status?"plain":"dark"}},[c._v(" "+c._s(c.getStatusText(t.row.status))+" ")]),3==t.row.status?d("div",{staticClass:"status-reason"},[c._v(" "+c._s(t.row.status_msg)+" ")]):c._e()],1)]}}])}),d("el-table-column",{attrs:{prop:"member.title",label:"业务员",width:"100"},scopedSlots:c._u([{key:"default",fn:function(t){var e;return[d("div",{staticClass:"member-info"},[d("el-tag",{attrs:{type:"info",size:"small"}},[c._v(" "+c._s((null===(e=t.row.member)||void 0===e?void 0:e.title)||"未分配")+" ")])],1)]}}])}),d("el-table-column",{attrs:{label:"时间信息","min-width":"140"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("div",{staticClass:"time-info"},[d("div",{staticClass:"create-time"},[d("i",{staticClass:"el-icon-time"}),c._v(" 创建："+c._s(c.formatDate(t.row.create_time))+" ")]),d("div",{staticClass:"end-time"},[d("i",{staticClass:"el-icon-date"}),c._v(" 到期："+c._s(c.formatDate(t.row.end_time))+" ")]),d("div",{staticClass:"remaining-days",class:c.getRemainingDaysClass(t.row.end_time)},[d("i",{staticClass:"el-icon-warning"}),c._v(" "+c._s(c.getRemainingDays(t.row.end_time))+" ")])])]}}])}),d("el-table-column",{attrs:{fixed:"right",label:"操作",width:"160"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("div",{staticClass:"action-buttons"},[d("el-button",{staticClass:"view-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return c.editData(t.row.id)}}},[d("i",{staticClass:"el-icon-view"}),c._v(" 查看 ")]),1===t.row.status?d("el-button",{staticClass:"approve-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return c.quickApprove(t.row)}}},[d("i",{staticClass:"el-icon-check"}),c._v(" 通过 ")]):c._e(),1===t.row.status?d("el-button",{staticClass:"reject-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return c.quickReject(t.row)}}},[d("i",{staticClass:"el-icon-close"}),c._v(" 拒绝 ")]):c._e(),d("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return c.delData(t.$index,t.row.id)}}},[d("i",{staticClass:"el-icon-delete"}),c._v(" 移除 ")])],1)]}}])})],1),d("div",{staticClass:"pagination-wrapper"},[d("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":c.size,layout:"total, sizes, prev, pager, next, jumper",total:c.total,background:""},on:{"size-change":c.handleSizeChange,"current-change":c.handleCurrentChange}})],1)],1),d("el-dialog",{staticClass:"order-detail-dialog",attrs:{title:"订单详情",visible:c.dialogFormVisible,"close-on-click-modal":!1,width:"85%"},on:{"update:visible":function(t){c.dialogFormVisible=t}}},[c.is_info?d("div",{staticClass:"order-detail-content"},[d("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[d("div",{staticClass:"detail-header",attrs:{slot:"header"},slot:"header"},[d("i",{staticClass:"el-icon-user"}),c._v(" 客户信息 ")]),d("el-descriptions",{attrs:{column:3,border:""}},[d("el-descriptions-item",{attrs:{label:"公司名称"}},[d("el-tag",{attrs:{type:"info"}},[c._v(c._s((null===(t=c.info.client)||void 0===t?void 0:t.company)||"未填写"))])],1),d("el-descriptions-item",{attrs:{label:"联系人"}},[c.info.client?d("el-tag",{staticClass:"clickable-tag",on:{click:function(t){var e;return c.viewUserData(null===(e=c.info.client)||void 0===e?void 0:e.id)}}},[c._v(" "+c._s((null===(e=c.info.client)||void 0===e?void 0:e.linkman)||"未填写")+" ")]):c._e()],1),d("el-descriptions-item",{attrs:{label:"联系方式"}},[c.info.client?d("el-tag",{staticClass:"clickable-tag",on:{click:function(t){var e;return c.viewUserData(null===(e=c.info.client)||void 0===e?void 0:e.id)}}},[c._v(" "+c._s((null===(s=c.info.client)||void 0===s?void 0:s.phone)||"未填写")+" ")]):c._e()],1),d("el-descriptions-item",{attrs:{label:"营业执照"}},[null!==(a=c.info.client)&&void 0!==a&&a.pic_path?d("el-tag",{staticClass:"clickable-tag",on:{click:function(t){var e;return c.showImage(null===(e=c.info.client)||void 0===e?void 0:e.pic_path)}}},[c._v(" 查看执照 ")]):d("el-tag",{attrs:{type:"info"}},[c._v("暂无")])],1),d("el-descriptions-item",{attrs:{label:"调解员"}},[c._v(" "+c._s((null===(i=c.info.client)||void 0===i?void 0:i.tiaojie_id)||"未分配")+" ")]),d("el-descriptions-item",{attrs:{label:"法务专员"}},[c._v(" "+c._s((null===(l=c.info.client)||void 0===l?void 0:l.fawu_id)||"未分配")+" ")]),d("el-descriptions-item",{attrs:{label:"立案专员"}},[c._v(" "+c._s((null===(n=c.info.client)||void 0===n?void 0:n.lian_id)||"未分配")+" ")]),d("el-descriptions-item",{attrs:{label:"合同专员"}},[c._v(" "+c._s((null===(r=c.info.client)||void 0===r?void 0:r.htsczy_id)||"未分配")+" ")]),d("el-descriptions-item",{attrs:{label:"指定律师"}},[c._v(" "+c._s((null===(o=c.info.client)||void 0===o?void 0:o.ls_id)||"未分配")+" ")])],1)],1),c.info.debts&&c.info.debts.length>0?d("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[d("div",{staticClass:"detail-header",attrs:{slot:"header"},slot:"header"},[d("i",{staticClass:"el-icon-user-solid"}),c._v(" 债务人信息 ")]),d("el-table",{staticClass:"debt-table",attrs:{data:c.info.debts,size:"medium"}},[d("el-table-column",{attrs:{prop:"name",label:"债务人姓名"}}),d("el-table-column",{attrs:{prop:"tel",label:"联系电话"}}),d("el-table-column",{attrs:{prop:"money",label:"债务金额（元）"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("span",{staticClass:"money-amount"},[c._v("¥"+c._s(t.row.money))])]}}],null,!1,1629117519)}),d("el-table-column",{attrs:{prop:"status",label:"状态"},scopedSlots:c._u([{key:"default",fn:function(t){return[d("el-tag",{attrs:{type:c.getDebtStatusType(t.row.status)}},[c._v(" "+c._s(t.row.status)+" ")])]}}],null,!1,1325240676)})],1)],1):c._e(),c.info.taocan?d("el-card",{staticClass:"detail-card",attrs:{shadow:"never"}},[d("div",{staticClass:"detail-header",attrs:{slot:"header"},slot:"header"},[d("i",{staticClass:"el-icon-box"}),c._v(" 套餐信息 ")]),d("el-descriptions",{attrs:{column:3,border:""}},[d("el-descriptions-item",{attrs:{label:"套餐名称"}},[d("el-tag",{attrs:{type:"primary"}},[c._v(c._s(c.info.taocan.title))])],1),d("el-descriptions-item",{attrs:{label:"套餐价格"}},[d("span",{staticClass:"price-highlight"},[c._v("¥"+c._s(c.info.taocan.price))])]),d("el-descriptions-item",{attrs:{label:"服务年限"}},[d("el-tag",{attrs:{type:"success"}},[c._v(c._s(c.info.taocan.year)+"年")])],1)],1)],1):c._e()],1):c._e(),d("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[d("el-button",{on:{click:function(t){c.dialogFormVisible=!1}}},[c._v("关闭")]),d("el-button",{attrs:{type:"primary"},on:{click:c.downloadOrder}},[c._v("下载订单")])],1)]),d("el-dialog",{attrs:{title:"图片查看",visible:c.dialogVisible,width:"50%"},on:{"update:visible":function(t){c.dialogVisible=t}}},[d("div",{staticClass:"image-viewer"},[d("el-image",{attrs:{src:c.show_image,fit:"contain"}})],1)]),d("el-dialog",{staticClass:"revenue-dialog",attrs:{title:"收入统计分析",visible:c.showRevenueDialog,width:"80%"},on:{"update:visible":function(t){c.showRevenueDialog=t}}},[d("div",{staticClass:"revenue-stats"},[d("el-row",{attrs:{gutter:20}},[d("el-col",{attrs:{span:12}},[d("div",{staticClass:"chart-card"},[d("h4",[c._v("月度收入趋势")]),d("div",{staticClass:"chart-placeholder"},[d("i",{staticClass:"el-icon-data-line chart-icon"}),d("p",[c._v("月度收入趋势图")]),d("div",{staticClass:"mock-chart-data"},[d("div",{staticClass:"chart-bar",staticStyle:{height:"60%"}}),d("div",{staticClass:"chart-bar",staticStyle:{height:"80%"}}),d("div",{staticClass:"chart-bar",staticStyle:{height:"45%"}}),d("div",{staticClass:"chart-bar",staticStyle:{height:"70%"}}),d("div",{staticClass:"chart-bar",staticStyle:{height:"90%"}}),d("div",{staticClass:"chart-bar",staticStyle:{height:"65%"}})])])])]),d("el-col",{attrs:{span:12}},[d("div",{staticClass:"chart-card"},[d("h4",[c._v("支付方式分布")]),d("div",{staticClass:"chart-placeholder"},[d("i",{staticClass:"el-icon-pie-chart chart-icon"}),d("p",[c._v("支付方式比例图")]),d("div",{staticClass:"payment-stats"},[d("div",{staticClass:"payment-item"},[d("span",{staticClass:"payment-dot full-payment"}),c._v(" 全款支付: "+c._s(c.fullPaymentCount)+" ")]),d("div",{staticClass:"payment-item"},[d("span",{staticClass:"payment-dot installment-payment"}),c._v(" 分期付款: "+c._s(c.installmentPaymentCount)+" ")])])])])])],1),d("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[d("el-col",{attrs:{span:24}},[d("div",{staticClass:"chart-card"},[d("h4",[c._v("订单状态统计")]),d("div",{staticClass:"status-overview"},[d("div",{staticClass:"status-item"},[d("div",{staticClass:"status-circle pending-circle"},[c._v(c._s(c.pendingOrders))]),d("span",[c._v("待审核")])]),d("div",{staticClass:"status-item"},[d("div",{staticClass:"status-circle approved-circle"},[c._v(c._s(c.approvedOrders))]),d("span",[c._v("已通过")])]),d("div",{staticClass:"status-item"},[d("div",{staticClass:"status-circle rejected-circle"},[c._v(c._s(c.rejectedOrders))]),d("span",[c._v("已拒绝")])]),d("div",{staticClass:"status-item"},[d("div",{staticClass:"status-circle total-circle"},[c._v(c._s(c.total))]),d("span",[c._v("总计")])])])])])],1)],1)]),d("el-dialog",{staticClass:"export-dialog",attrs:{title:"数据导出",visible:c.showExportDialog,width:"600px"},on:{"update:visible":function(t){c.showExportDialog=t}}},[d("el-form",{attrs:{model:c.exportForm,"label-width":"120px"}},[d("el-form-item",{attrs:{label:"导出格式"}},[d("el-radio-group",{model:{value:c.exportForm.format,callback:function(t){c.$set(c.exportForm,"format",t)},expression:"exportForm.format"}},[d("el-radio",{attrs:{label:"excel"}},[c._v("Excel (.xlsx)")]),d("el-radio",{attrs:{label:"csv"}},[c._v("CSV (.csv)")]),d("el-radio",{attrs:{label:"pdf"}},[c._v("PDF (.pdf)")])],1)],1),d("el-form-item",{attrs:{label:"导出内容"}},[d("el-checkbox-group",{model:{value:c.exportForm.fields,callback:function(t){c.$set(c.exportForm,"fields",t)},expression:"exportForm.fields"}},[d("el-checkbox",{attrs:{label:"client"}},[c._v("客户信息")]),d("el-checkbox",{attrs:{label:"package"}},[c._v("套餐信息")]),d("el-checkbox",{attrs:{label:"payment"}},[c._v("支付情况")]),d("el-checkbox",{attrs:{label:"status"}},[c._v("审核状态")]),d("el-checkbox",{attrs:{label:"time"}},[c._v("时间信息")]),d("el-checkbox",{attrs:{label:"member"}},[c._v("业务员信息")])],1)],1),d("el-form-item",{attrs:{label:"数据范围"}},[d("el-radio-group",{model:{value:c.exportForm.range,callback:function(t){c.$set(c.exportForm,"range",t)},expression:"exportForm.range"}},[d("el-radio",{attrs:{label:"all"}},[c._v("全部数据")]),d("el-radio",{attrs:{label:"current"}},[c._v("当前页面")]),d("el-radio",{attrs:{label:"selected"}},[c._v("选中项目")]),d("el-radio",{attrs:{label:"filtered"}},[c._v("筛选结果")])],1)],1),d("el-form-item",{attrs:{label:"时间范围"}},[d("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:c.exportForm.dateRange,callback:function(t){c.$set(c.exportForm,"dateRange",t)},expression:"exportForm.dateRange"}})],1)],1),d("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[d("el-button",{on:{click:function(t){c.showExportDialog=!1}}},[c._v("取消")]),d("el-button",{attrs:{type:"primary",loading:c.exportLoading},on:{click:c.executeExport}},[d("i",{staticClass:"el-icon-download"}),c._v(" 开始导出 ")])],1)],1)],1)},i=[],l=(s("14d9"),s("13d5"),s("88a7"),s("271a"),s("5494"),s("d522")),n={name:"list",components:{UserDetails:l["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:"",salesman:"",refund_time:[],status:"",payType:"",minAmount:0,maxAmount:0,packageType:"",sortBy:""},loading:!0,url:"/dingdan/",title:"签约用户",info:{},currentId:0,dialogFormVisible:!1,show_image:"",dialogVisible:!1,dialogViewUserDetail:!1,is_info:!1,upload_index:"",dialogStatus:!1,dialogEndTime:!1,ruleForm:{status:"",status_msg:"",end_time:""},rules:{status_msg:[{required:!0,message:"请填写不通过原因",trigger:"blur"}]},formLabelWidth:"120px",showAdvanced:!1,selectedRows:[],showRevenueDialog:!1,showExportDialog:!1,exportForm:{format:"excel",fields:["client","package","payment","status","time","member"],range:"all",dateRange:[]},exportLoading:!1,showNotifications:!0}},computed:{pendingOrders(){return Array.isArray(this.list)?this.list.filter(t=>1===t.status).length:0},approvedOrders(){return Array.isArray(this.list)?this.list.filter(t=>2===t.status).length:0},rejectedOrders(){return Array.isArray(this.list)?this.list.filter(t=>3===t.status).length:0},totalRevenue(){return Array.isArray(this.list)?this.list.reduce((t,e)=>t+(e.pay_age||0),0).toLocaleString():"0"},fullPaymentCount(){return Array.isArray(this.list)?this.list.filter(t=>1===t.pay_type).length:0},installmentPaymentCount(){return Array.isArray(this.list)?this.list.filter(t=>1!==t.pay_type).length:0},expiringOrders(){if(!Array.isArray(this.list))return[];const t=new Date,e=new Date(t.getTime()+6048e5);return this.list.filter(s=>{if(!s.end_time)return!1;const a=new Date(s.end_time);return a>=t&&a<=e})},expiredOrders(){if(!Array.isArray(this.list))return[];const t=new Date;return this.list.filter(e=>{if(!e.end_time)return!1;const s=new Date(e.end_time);return s<t})},highValueOrders(){return Array.isArray(this.list)?this.list.filter(t=>(t.pay_age||0)>5e3):[]},hasImportantNotifications(){return this.showNotifications&&(this.pendingOrders>0||this.expiringOrders.length>0||this.expiredOrders.length>0||this.highValueOrders.length>0)}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):this.ruleForm={title:""},e.dialogFormVisible=!0},viewUserData(t){let e=this;0!=t&&(this.currentId=t),e.dialogViewUserDetail=!0},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{t&&(e.info=t.data,e.is_info=!0)})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code?(this.$message({type:"success",message:"删除成功!"}),this.list.splice(t,1)):_this.$message({type:"error",message:e.msg})})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},updateEndTIme(){this.$confirm("确认修改到期时间?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var t={id:this.info.id,end_time:this.info.end_time};this.postRequest(this.url+"updateEndTIme",t).then(t=>{200==t.code?this.$message({type:"success",message:"修改成功!"}):_this.$message({type:"error",message:t.msg})})}).catch(()=>{this.$message({type:"error",message:"取消修改!"})})},refulsh(){this.$router.go(0)},getData(){let t=this;t.loading=!0,t.postRequest(t.url+"index1?page="+t.page+"&size="+t.size,t.search).then(e=>{200==e.code&&(t.list=e.data,t.total=e.count),t.loading=!1})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest(t.url+"save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},handleSuccess(t){let e=this;200==t.code&&(e.info.fenqi[e.index].pay_path=t.data.url,e.postRequest(e.url+"save",{id:e.info.id,fenqi:e.info.fenqi}).then(t=>{200==t.code?e.$message({type:"success",message:"上传成功"}):e.$message({type:"error",message:"上传失败"})}))},beforeUpload(t){const e=/^image\/(jpeg|png|jpg)$/.test(t.type);e||this.$message.error("上传图片格式不对!")},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})},showImage(t){this.show_image=t,this.dialogVisible=!0},changePinzhen(t){this.index=t},showStatus(t){this.dialogStatus=!0,this.ruleForm=t},showEndTime(t){this.dialogEndTime=!0,this.ruleForm=t},changeEndTime(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;t.postRequest(t.url+"save",{id:t.ruleForm.id,end_time:t.ruleForm.end_time}).then(e=>{200==e.code?(t.$message({type:"success",message:"审核成功"}),t.dialogStatus=!1):t.$message({type:"error",message:e.msg})})})},changeStatus(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;t.postRequest(t.url+"changeStatus",{id:t.ruleForm.id,status:t.ruleForm.status,status_msg:t.ruleForm.status_msg}).then(e=>{200==e.code?(t.$message({type:"success",message:"审核成功"}),t.dialogStatus=!1):t.$message({type:"error",message:e.msg})})})},getStatusType(t){const e={1:"warning",2:"success",3:"danger"};return e[t]||"info"},getStatusText(t){const e={1:"未审核",2:"已通过",3:"未通过"};return e[t]||"未知"},getDebtStatusType(t){return"已解决"===t?"success":"处理中"===t?"warning":"info"},formatDate(t){return t?new Date(t).toLocaleDateString("zh-CN"):"未设置"},tableRowClassName({row:t}){return 1===t.status?"warning-row":3===t.status?"danger-row":""},resetSearch(){this.search={keyword:"",salesman:"",refund_time:[],status:"",payType:"",minAmount:0,maxAmount:0,packageType:"",sortBy:""},this.page=1,this.getData()},exportData(){this.showExportDialog=!0},downloadOrder(){this.$message.success("订单下载功能开发中...")},toggleAdvanced(){this.showAdvanced=!this.showAdvanced},applyAdvancedSearch(){this.getData()},clearAdvancedSearch(){this.resetSearch()},refreshData(){this.getData()},batchAudit(){this.$message.success("批量审核功能开发中...")},handleSelectionChange(t){this.selectedRows=t},filterByStatus(t){this.search.status=t,this.page=1,this.getData(),this.$message.success(`已筛选${this.getStatusText(t)||"全部"}订单`)},showRevenueChart(){this.showRevenueDialog=!0},executeExport(){this.exportLoading=!0,setTimeout(()=>{const t={excel:"Excel",csv:"CSV",pdf:"PDF"}[this.exportForm.format],e={all:"全部数据",current:"当前页面",selected:"选中项目",filtered:"筛选结果"}[this.exportForm.range],s=this.generateExportData(),a=URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=`订单数据_${(new Date).toISOString().split("T")[0]}.${this.exportForm.format}`,i.click(),URL.revokeObjectURL(a),this.exportLoading=!1,this.showExportDialog=!1,this.$message.success(`${t}格式的${e}导出成功！`)},2e3)},generateExportData(){let t=[];switch(this.exportForm.range){case"all":t=this.list;break;case"current":t=this.list;break;case"selected":t=this.selectedRows;break;case"filtered":t=this.list;break}const e=t.map(t=>{const e={};var s,a,i,l,n,r,o;this.exportForm.fields.includes("client")&&(e["公司名称"]=(null===(s=t.client)||void 0===s?void 0:s.company)||"",e["联系人"]=(null===(a=t.client)||void 0===a?void 0:a.linkman)||"",e["联系电话"]=(null===(i=t.client)||void 0===i?void 0:i.phone)||"");this.exportForm.fields.includes("package")&&(e["套餐名称"]=(null===(l=t.taocan)||void 0===l?void 0:l.title)||"",e["套餐价格"]=(null===(n=t.taocan)||void 0===n?void 0:n.price)||0,e["服务年限"]=(null===(r=t.taocan)||void 0===r?void 0:r.year)||0);(this.exportForm.fields.includes("payment")&&(e["支付方式"]=1==t.pay_type?"全款":"分期",e["已付金额"]=t.pay_age||0,e["总金额"]=t.total_price||0),this.exportForm.fields.includes("status")&&(e["审核状态"]=this.getStatusText(t.status),e["状态说明"]=t.status_msg||""),this.exportForm.fields.includes("time")&&(e["创建时间"]=t.create_time||"",e["到期时间"]=t.end_time||""),this.exportForm.fields.includes("member"))&&(e["业务员"]=(null===(o=t.member)||void 0===o?void 0:o.title)||"");return e}),s=this.convertToCSV(e);return new Blob([s],{type:"text/csv;charset=utf-8;"})},convertToCSV(t){if(!t||0===t.length)return"";const e=Object.keys(t[0]),s=[e.join(",")];for(const a of t){const t=e.map(t=>{const e=a[t];return"string"===typeof e&&e.includes(",")?`"${e}"`:e});s.push(t.join(","))}return s.join("\n")},batchApprove(){if(0===this.selectedRows.length)return void this.$message.warning("请先选择要批量通过的订单");const t=this.selectedRows.filter(t=>1===t.status);0!==t.length?this.$confirm(`确认批量通过选中的 ${t.length} 个待审核订单吗？`,"批量审核",{confirmButtonText:"确认通过",cancelButtonText:"取消",type:"success"}).then(()=>{const e=t.map(t=>this.postRequest(this.url+"changeStatus",{id:t.id,status:2,status_msg:"批量审核通过"}));Promise.all(e).then(t=>{const e=t.filter(t=>200===t.code).length;this.$message.success(`批量审核完成，成功通过 ${e} 个订单`),this.getData(),this.selectedRows=[]}).catch(()=>{this.$message.error("批量审核过程中出现错误"),this.getData()})}).catch(()=>{this.$message.info("已取消批量审核")}):this.$message.warning("选中的订单中没有待审核的订单")},batchReject(){if(0===this.selectedRows.length)return void this.$message.warning("请先选择要批量拒绝的订单");const t=this.selectedRows.filter(t=>1===t.status);0!==t.length?this.$prompt("请输入批量拒绝理由",`批量拒绝 ${t.length} 个订单`,{confirmButtonText:"确认拒绝",cancelButtonText:"取消",inputPlaceholder:"请填写拒绝的具体原因...",inputValidator:t=>t&&""!==t.trim()?!(t.length<5)||"拒绝理由至少需要5个字符":"拒绝理由不能为空"}).then(({value:e})=>{const s=t.map(t=>this.postRequest(this.url+"changeStatus",{id:t.id,status:3,status_msg:e}));Promise.all(s).then(t=>{const e=t.filter(t=>200===t.code).length;this.$message.success(`批量拒绝完成，成功拒绝 ${e} 个订单`),this.getData(),this.selectedRows=[]}).catch(()=>{this.$message.error("批量拒绝过程中出现错误"),this.getData()})}).catch(()=>{this.$message.info("已取消批量拒绝")}):this.$message.warning("选中的订单中没有待审核的订单")},quickApprove(t){var e;this.$confirm(`确认通过 ${(null===(e=t.client)||void 0===e?void 0:e.company)||"该客户"} 的订单吗？`,"快速审核",{confirmButtonText:"确认通过",cancelButtonText:"取消",type:"success"}).then(()=>{this.postRequest(this.url+"changeStatus",{id:t.id,status:2,status_msg:"快速审核通过"}).then(t=>{200==t.code?(this.$message.success("审核通过成功"),this.getData()):this.$message.error(t.msg)})}).catch(()=>{this.$message.info("已取消操作")})},quickReject(t){var e;this.$prompt("请输入拒绝理由","拒绝订单 - "+((null===(e=t.client)||void 0===e?void 0:e.company)||"客户"),{confirmButtonText:"确认拒绝",cancelButtonText:"取消",inputPlaceholder:"请填写拒绝的具体原因...",inputValidator:t=>t&&""!==t.trim()?!(t.length<5)||"拒绝理由至少需要5个字符":"拒绝理由不能为空"}).then(({value:e})=>{this.postRequest(this.url+"changeStatus",{id:t.id,status:3,status_msg:e}).then(t=>{200==t.code?(this.$message.success("订单已拒绝"),this.getData()):this.$message.error(t.msg)})}).catch(()=>{this.$message.info("已取消操作")})},getRemainingDays(t){if(!t)return"未设置";const e=new Date,s=new Date(t),a=Math.ceil((s-e)/864e5);return a<0?`已过期${Math.abs(a)}天`:0===a?"今天到期":a+"天后到期"},getRemainingDaysClass(t){if(!t)return"";const e=new Date,s=new Date(t),a=Math.ceil((s-e)/864e5);return a<0?"expired":a<=3?"urgent":a<=7?"warning":"normal"},dismissNotifications(){this.showNotifications=!1,this.$message.success("已暂时隐藏提醒通知")},showExpiringOrders(){this.$message.info(`查看${this.expiringOrders.length}个即将到期的订单`)},showExpiredOrders(){this.$message.warning(`查看${this.expiredOrders.length}个已过期的订单`)},showHighValueOrders(){this.$message.success(`查看${this.highValueOrders.length}个高价值订单`)}}},r=n,o=(s("1305"),s("2877")),c=Object(o["a"])(r,a,i,!1,null,"ffe30e72",null);e["default"]=c.exports},a640:function(t,e,s){"use strict";var a=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&a((function(){s.call(null,e||function(){return 1},1)}))}},d522:function(t,e,s){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"user-detail-container"},[e("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-user"}),e("span",{staticClass:"card-title"},[t._v("客户基本信息")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("公司名称")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.company||"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("手机号")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.phone||"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("客户姓名")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.nickname||"未填写"))])])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("联系人")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.linkman||"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("联系方式")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.linkphone||"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("用户来源")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.yuangong_id||"未填写"))])])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("开始时间")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.start_time||"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("会员年限")]),e("div",{staticClass:"info-value"},[t._v(t._s(t.info.year?t.info.year+"年":"未填写"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("头像")]),e("div",{staticClass:"info-value"},[t.info.headimg&&""!==t.info.headimg?e("el-avatar",{staticStyle:{cursor:"pointer"},attrs:{src:t.info.headimg,size:50},nativeOn:{click:function(e){return t.showImage(t.info.headimg)}}}):e("span",{staticClass:"no-data"},[t._v("未上传")])],1)])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-label"},[t._v("营业执照")]),e("div",{staticClass:"info-value"},[t.info.license&&""!==t.info.license?e("el-image",{staticStyle:{width:"100px",height:"100px",cursor:"pointer"},attrs:{src:t.info.license,fit:"cover"},on:{click:function(e){return t.showImage(t.info.license)}}},[e("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[e("i",{staticClass:"el-icon-picture-outline"})])]):e("span",{staticClass:"no-data"},[t._v("未上传")])],1)])])],1)],1),e("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-s-custom"}),e("span",{staticClass:"card-title"},[t._v("服务团队")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("调解员")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.tiaojie_name||"未分配"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("法务专员")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.fawu_name||"未分配"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("立案专员")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.lian_name||"未分配"))])])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("合同专员")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.htsczy_name||"未分配"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("律师")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.ls_name||"未分配"))])])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"team-item"},[e("div",{staticClass:"team-role"},[t._v("业务员")]),e("div",{staticClass:"team-name"},[t._v(t._s(t.info.ywy_name||"未分配"))])])])],1)],1),e("el-card",{staticClass:"info-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-money"}),e("span",{staticClass:"card-title"},[t._v("债务人信息")])]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.info.debts,size:"medium",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[e("el-table-column",{attrs:{prop:"name",label:"债务人姓名",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:"primary",size:"small"}},[t._v(t._s(s.row.name))])]}}])}),e("el-table-column",{attrs:{prop:"tel",label:"债务人电话",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("span",{staticClass:"phone-number"},[t._v(t._s(s.row.tel))])]}}])}),e("el-table-column",{attrs:{prop:"money",label:"债务金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("span",{staticClass:"money-amount"},[t._v("¥"+t._s(s.row.money))])]}}])}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:"已完成"===s.row.status?"success":"warning",size:"small"}},[t._v(" "+t._s(s.row.status)+" ")])]}}])}),e("el-table-column",{attrs:{label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewDebtDetail(s.row)}}},[e("i",{staticClass:"el-icon-view"}),t._v(" 详情 ")])]}}])})],1),t.info.debts&&0!==t.info.debts.length?t._e():e("div",{staticClass:"empty-data"},[e("i",{staticClass:"el-icon-document"}),e("p",[t._v("暂无债务人信息")])])],1),e("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("el-image",{attrs:{src:t.show_image}})],1)],1)},i=[],l={name:"UserDetails",props:{id:{type:[String,Number],required:!0}},data(){return{info:{},loading:!1,dialogVisible:!1,show_image:""}},watch:{id:{immediate:!0,handler(t){t&&0!=t&&(console.log("UserDetails 接收到 ID:",t),this.getInfo(t))}}},methods:{getInfo(t){let e=this;console.log("正在获取用户信息，ID:",t),e.loading=!0,setTimeout(()=>{const s={id:t,company:"测试公司有限公司",phone:"13800138001",nickname:"张三",linkman:"李四",headimg:"",yuangong_id:"微信小程序",linkphone:"13800138002",tiaojie_name:"王调解员",fawu_name:"赵法务",lian_name:"钱立案员",htsczy_name:"孙合同员",ls_name:"周律师",ywy_name:"吴业务员",license:"",start_time:"2024-01-01",year:1,debts:[{name:"债务人A",tel:"13900139001",money:"50000",status:"处理中"},{name:"债务人B",tel:"13900139002",money:"30000",status:"已完成"}]};e.info=s,e.loading=!1,console.log("用户数据加载完成:",s)},500)},showImage(t){this.show_image=t,this.dialogVisible=!0},viewDebtDetail(t){console.log("查看债务人详情:",t),this.$message.info("债务人详情功能待开发")}}},n=l,r=(s("0091"),s("2877")),o=Object(r["a"])(n,a,i,!1,null,"4468717a",null);e["a"]=o.exports},d58f:function(t,e,s){"use strict";var a=s("59ed"),i=s("7b0b"),l=s("44ad"),n=s("07fa"),r=TypeError,o="Reduce of empty array with no initial value",c=function(t){return function(e,s,c,d){var u=i(e),h=l(u),v=n(u);if(a(s),0===v&&c<2)throw new r(o);var m=t?v-1:0,p=t?-1:1;if(c<2)while(1){if(m in h){d=h[m],m+=p;break}if(m+=p,t?m<0:v<=m)throw new r(o)}for(;t?m>=0:v>m;m+=p)m in h&&(d=s(d,h[m],m,u));return d}};t.exports={left:c(!1),right:c(!0)}},d6d6:function(t,e,s){"use strict";var a=TypeError;t.exports=function(t,e){if(t<e)throw new a("Not enough arguments");return t}},edd0:function(t,e,s){"use strict";var a=s("13d2"),i=s("9bf2");t.exports=function(t,e,s){return s.get&&a(s.get,e,{getter:!0}),s.set&&a(s.set,e,{setter:!0}),i.f(t,e,s)}}}]);
//# sourceMappingURL=chunk-41971978.a94e8bab.js.map