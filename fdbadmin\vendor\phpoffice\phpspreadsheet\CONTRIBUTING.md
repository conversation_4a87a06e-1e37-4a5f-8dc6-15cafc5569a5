# Want to contribute?

If you would like to contribute, here are some notes and guidelines:

 - All new development happens on feature/fix branches, and are then merged to the `master` branch once stable; so the `master` branch is always the most up-to-date, working code
 - Tagged releases are made from the `master` branch
 - If you are going to be submitting a pull request, please fork from `master`, and submit your pull request back as a fix/feature branch referencing the GitHub issue number
 - Code style might be automatically fixed by `composer fix`
 - All code changes must be validated by `composer check`
 - [Helpful article about forking](https://help.github.com/articles/fork-a-repo/ "Forking a GitHub repository")
 - [Helpful article about pull requests](https://help.github.com/articles/using-pull-requests/ "Pull Requests")
