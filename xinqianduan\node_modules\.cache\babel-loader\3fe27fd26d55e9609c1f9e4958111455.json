{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yuangong\\zhiwei.vue", "mtime": 1748464736128}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "viewMode", "props", "multiple", "options", "allSize", "list", "total", "page", "size", "search", "keyword", "permission_level", "status", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "desc", "quanxian", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "computed", "adminCount", "filter", "item", "includes", "length", "userCount", "activeCount", "mounted", "getData", "methods", "getPermissionLabels", "permissions", "Array", "isArray", "permissionMap", "map", "id", "Boolean", "getPermissionTagType", "permission", "changeStatus", "row", "$message", "success", "clearSearch", "searchData", "change", "editData", "_this", "getInfo", "getQuanxians", "getPermissionsFromManagement", "permissionData", "value", "label", "children", "getRequest", "then", "resp", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "code", "splice", "msg", "catch", "refulsh", "$router", "go", "setTimeout", "level", "create_time", "saveData", "$refs", "validate", "valid", "postRequest", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "pic_path", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName"], "sources": ["src/views/pages/yuangong/zhiwei.vue"], "sourcesContent": ["<template>\r\n  <div class=\"position-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-postcard\"></i>\r\n            职位管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统职位信息和权限配置</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增职位\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">职位搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入职位名称或描述\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">权限级别</label>\r\n              <el-select\r\n                v-model=\"search.permission_level\"\r\n                placeholder=\"请选择权限级别\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"超级管理员\" value=\"super\"></el-option>\r\n                <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n                <el-option label=\"普通用户\" value=\"user\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">状态</label>\r\n              <el-select\r\n                v-model=\"search.status\"\r\n                placeholder=\"请选择状态\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"启用\" :value=\"1\"></el-option>\r\n                <el-option label=\"禁用\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-postcard\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总职位数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon admin\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ adminCount }}</div>\r\n              <div class=\"stat-label\">管理职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon user\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ userCount }}</div>\r\n              <div class=\"stat-label\">普通职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active\">\r\n              <i class=\"el-icon-circle-check\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeCount }}</div>\r\n              <div class=\"stat-label\">启用职位</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 职位列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            职位列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"position-table\"\r\n            stripe\r\n          >\r\n            <el-table-column prop=\"title\" label=\"职位名称\" min-width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"position-title-cell\">\r\n                  <div class=\"position-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"position-level\" v-if=\"scope.row.level\">{{ scope.row.level }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"desc\" label=\"职位描述\" min-width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"权限配置\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"permission-tags\">\r\n                  <el-tag\r\n                    v-for=\"(permission, index) in getPermissionLabels(scope.row.quanxian)\"\r\n                    :key=\"index\"\r\n                    size=\"mini\"\r\n                    :type=\"getPermissionTagType(permission)\"\r\n                    style=\"margin: 2px;\"\r\n                  >\r\n                    {{ permission }}\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch\r\n                  v-model=\"scope.row.status\"\r\n                  :active-value=\"1\"\r\n                  :inactive-value=\"0\"\r\n                  @change=\"changeStatus(scope.row)\"\r\n                >\r\n                </el-switch>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"position in list\" :key=\"position.id\" class=\"position-card-col\">\r\n              <div class=\"position-card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-title\">\r\n                    <i class=\"el-icon-postcard\"></i>\r\n                    {{ position.title }}\r\n                  </div>\r\n                  <div class=\"card-status\">\r\n                    <el-switch\r\n                      v-model=\"position.status\"\r\n                      :active-value=\"1\"\r\n                      :inactive-value=\"0\"\r\n                      @change=\"changeStatus(position)\"\r\n                      size=\"small\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-desc\">{{ position.desc }}</div>\r\n\r\n                  <div class=\"card-permissions\">\r\n                    <div class=\"permission-title\">权限配置：</div>\r\n                    <div class=\"permission-tags\">\r\n                      <el-tag\r\n                        v-for=\"(permission, index) in getPermissionLabels(position.quanxian)\"\r\n                        :key=\"index\"\r\n                        size=\"mini\"\r\n                        :type=\"getPermissionTagType(permission)\"\r\n                        style=\"margin: 2px;\"\r\n                      >\r\n                        {{ permission }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ position.create_time }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"card-actions\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    @click=\"editData(position.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"small\"\r\n                    @click=\"delData(list.indexOf(position), position.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"权限\" :label-width=\"formLabelWidth\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.quanxian\"\r\n            :options=\"options\"\r\n            :props=\"props\"\r\n            clearable\r\n          ></el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"list\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      viewMode: 'table', // table | card\r\n      props: { multiple: true },\r\n      options: {},\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 5,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      },\r\n      loading: true,\r\n      url: \"/zhiwei/\",\r\n      title: \"职位\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        desc: \"\",\r\n        quanxian: [],\r\n        status: 1\r\n      },\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        desc: [\r\n          {\r\n            required: true,\r\n            message: \"请填写职位描述\",\r\n            trigger: \"blur\",\r\n          },\r\n        ]\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 管理员职位数量\r\n    adminCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && (item.quanxian.includes(1) || item.quanxian.includes(13))\r\n      ).length;\r\n    },\r\n    // 普通用户职位数量\r\n    userCount() {\r\n      return this.list.filter(item =>\r\n        item.quanxian && !item.quanxian.includes(1) && !item.quanxian.includes(13)\r\n      ).length;\r\n    },\r\n    // 启用职位数量\r\n    activeCount() {\r\n      return this.list.filter(item => item.status === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 获取权限标签\r\n    getPermissionLabels(permissions) {\r\n      if (!permissions || !Array.isArray(permissions)) return [];\r\n\r\n      const permissionMap = {\r\n        1: \"系统管理\",\r\n        2: \"业务管理\",\r\n        3: \"文书管理\",\r\n        4: \"财务管理\",\r\n        11: \"用户管理\",\r\n        12: \"角色管理\",\r\n        13: \"权限管理\",\r\n        21: \"订单管理\",\r\n        22: \"客户管理\",\r\n        31: \"合同管理\",\r\n        32: \"律师函管理\",\r\n        33: \"课程管理\",\r\n        41: \"支付管理\"\r\n      };\r\n\r\n      return permissions.map(id => permissionMap[id] || `权限${id}`).filter(Boolean);\r\n    },\r\n\r\n    // 获取权限标签类型\r\n    getPermissionTagType(permission) {\r\n      if (permission.includes('系统') || permission.includes('权限')) return 'danger';\r\n      if (permission.includes('业务') || permission.includes('订单')) return 'primary';\r\n      if (permission.includes('文书') || permission.includes('合同')) return 'success';\r\n      if (permission.includes('财务') || permission.includes('支付')) return 'warning';\r\n      return 'info';\r\n    },\r\n\r\n    // 状态切换\r\n    changeStatus(row) {\r\n      this.$message.success(`职位\"${row.title}\"状态已${row.status ? '启用' : '禁用'}`);\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        permission_level: \"\",\r\n        status: \"\"\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    change() {},\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getQuanxians();\r\n    },\r\n    getQuanxians() {\r\n      // 从权限管理页面获取权限数据\r\n      this.getPermissionsFromManagement();\r\n    },\r\n\r\n    // 从权限管理获取权限数据\r\n    getPermissionsFromManagement() {\r\n      // 模拟从权限管理页面获取的权限数据\r\n      const permissionData = [\r\n        {\r\n          value: 1,\r\n          label: \"系统管理\",\r\n          children: [\r\n            {\r\n              value: 11,\r\n              label: \"用户管理\",\r\n              children: [\r\n                { value: 111, label: \"查看用户\" },\r\n                { value: 112, label: \"新增用户\" },\r\n                { value: 113, label: \"编辑用户\" },\r\n                { value: 114, label: \"删除用户\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 12,\r\n              label: \"角色管理\",\r\n              children: [\r\n                { value: 121, label: \"查看角色\" },\r\n                { value: 122, label: \"新增角色\" },\r\n                { value: 123, label: \"编辑角色\" },\r\n                { value: 124, label: \"删除角色\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 13,\r\n              label: \"权限管理\",\r\n              children: [\r\n                { value: 131, label: \"查看权限\" },\r\n                { value: 132, label: \"新增权限\" },\r\n                { value: 133, label: \"编辑权限\" },\r\n                { value: 134, label: \"删除权限\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 2,\r\n          label: \"业务管理\",\r\n          children: [\r\n            {\r\n              value: 21,\r\n              label: \"订单管理\",\r\n              children: [\r\n                { value: 211, label: \"查看订单\" },\r\n                { value: 212, label: \"新增订单\" },\r\n                { value: 213, label: \"编辑订单\" },\r\n                { value: 214, label: \"删除订单\" },\r\n                { value: 215, label: \"导出订单\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 22,\r\n              label: \"客户管理\",\r\n              children: [\r\n                { value: 221, label: \"查看客户\" },\r\n                { value: 222, label: \"新增客户\" },\r\n                { value: 223, label: \"编辑客户\" },\r\n                { value: 224, label: \"删除客户\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 3,\r\n          label: \"文书管理\",\r\n          children: [\r\n            {\r\n              value: 31,\r\n              label: \"合同管理\",\r\n              children: [\r\n                { value: 311, label: \"查看合同\" },\r\n                { value: 312, label: \"新增合同\" },\r\n                { value: 313, label: \"编辑合同\" },\r\n                { value: 314, label: \"删除合同\" },\r\n                { value: 315, label: \"审核合同\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 32,\r\n              label: \"律师函管理\",\r\n              children: [\r\n                { value: 321, label: \"查看律师函\" },\r\n                { value: 322, label: \"发送律师函\" },\r\n                { value: 323, label: \"编辑律师函\" }\r\n              ]\r\n            },\r\n            {\r\n              value: 33,\r\n              label: \"课程管理\",\r\n              children: [\r\n                { value: 331, label: \"查看课程\" },\r\n                { value: 332, label: \"新增课程\" },\r\n                { value: 333, label: \"编辑课程\" },\r\n                { value: 334, label: \"删除课程\" }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          value: 4,\r\n          label: \"财务管理\",\r\n          children: [\r\n            {\r\n              value: 41,\r\n              label: \"支付管理\",\r\n              children: [\r\n                { value: 411, label: \"查看支付记录\" },\r\n                { value: 412, label: \"处理退款\" },\r\n                { value: 413, label: \"导出财务报表\" }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      this.options = permissionData;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n        _this.list = [\r\n          {\r\n            id: 1,\r\n            title: \"系统管理员\",\r\n            desc: \"拥有系统所有权限，负责系统维护和用户管理\",\r\n            quanxian: [1, 2, 3, 4], // 拥有所有模块权限\r\n            status: 1,\r\n            level: \"超级管理员\",\r\n            create_time: \"2024-01-01 10:00:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"业务经理\",\r\n            desc: \"负责业务流程管理，客户关系维护\",\r\n            quanxian: [2, 3], // 拥有业务管理和文书管理权限\r\n            status: 1,\r\n            level: \"管理员\",\r\n            create_time: \"2024-01-02 14:30:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"法务专员\",\r\n            desc: \"负责合同审核、律师函处理等法务工作\",\r\n            quanxian: [3], // 只有文书管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-03 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"财务专员\",\r\n            desc: \"负责财务管理、支付处理、报表统计\",\r\n            quanxian: [4], // 只有财务管理权限\r\n            status: 1,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-04 11:20:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"客服专员\",\r\n            desc: \"负责客户咨询、问题处理、售后服务\",\r\n            quanxian: [22], // 只有客户管理权限\r\n            status: 0,\r\n            level: \"普通用户\",\r\n            create_time: \"2024-01-05 16:45:00\"\r\n          }\r\n        ];\r\n        _this.total = 5;\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.position-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 30px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section .page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-title i {\r\n  font-size: 32px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n}\r\n\r\n.search-form {\r\n  padding: 20px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 100%;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.admin {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.user {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.active {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格样式 */\r\n.position-table {\r\n  margin: 0;\r\n}\r\n\r\n.position-title-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.position-title {\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.position-level {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  background: #f0f2f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n  width: fit-content;\r\n}\r\n\r\n.permission-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n  justify-content: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #606266;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 卡片视图 */\r\n.card-view {\r\n  padding: 20px 0;\r\n}\r\n\r\n.position-card-col {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.position-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.position-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-desc {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-permissions {\r\n  margin-bottom: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.permission-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.card-footer {\r\n  margin-top: auto;\r\n}\r\n\r\n.card-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.card-actions {\r\n  padding: 16px 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .position-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .position-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiYA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,KAAA;QAAAC,QAAA;MAAA;MACAC,OAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,MAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,IAAA;QACAC,QAAA;QACAV,MAAA;MACA;MACAW,KAAA;QACAR,KAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAL,IAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,WAAA;MACA,YAAAxB,IAAA,CAAAyB,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAT,QAAA,KAAAS,IAAA,CAAAT,QAAA,CAAAU,QAAA,OAAAD,IAAA,CAAAT,QAAA,CAAAU,QAAA,KACA,EAAAC,MAAA;IACA;IACA;IACAC,UAAA;MACA,YAAA7B,IAAA,CAAAyB,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAT,QAAA,KAAAS,IAAA,CAAAT,QAAA,CAAAU,QAAA,QAAAD,IAAA,CAAAT,QAAA,CAAAU,QAAA,IACA,EAAAC,MAAA;IACA;IACA;IACAE,YAAA;MACA,YAAA9B,IAAA,CAAAyB,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAnB,MAAA,QAAAqB,MAAA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,oBAAAC,WAAA;MACA,KAAAA,WAAA,KAAAC,KAAA,CAAAC,OAAA,CAAAF,WAAA;MAEA,MAAAG,aAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,OAAAH,WAAA,CAAAI,GAAA,CAAAC,EAAA,IAAAF,aAAA,CAAAE,EAAA,UAAAA,EAAA,IAAAf,MAAA,CAAAgB,OAAA;IACA;IAEA;IACAC,qBAAAC,UAAA;MACA,IAAAA,UAAA,CAAAhB,QAAA,UAAAgB,UAAA,CAAAhB,QAAA;MACA,IAAAgB,UAAA,CAAAhB,QAAA,UAAAgB,UAAA,CAAAhB,QAAA;MACA,IAAAgB,UAAA,CAAAhB,QAAA,UAAAgB,UAAA,CAAAhB,QAAA;MACA,IAAAgB,UAAA,CAAAhB,QAAA,UAAAgB,UAAA,CAAAhB,QAAA;MACA;IACA;IAEA;IACAiB,aAAAC,GAAA;MACA,KAAAC,QAAA,CAAAC,OAAA,OAAAF,GAAA,CAAAnC,KAAA,OAAAmC,GAAA,CAAAtC,MAAA;IACA;IAEA;IACAyC,YAAA;MACA,KAAA5C,MAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,MAAA;MACA;MACA,KAAA0C,UAAA;IACA;IAEAC,OAAA;IACAC,SAAAX,EAAA;MACA,IAAAY,KAAA;MACA,IAAAZ,EAAA;QACA,KAAAa,OAAA,CAAAb,EAAA;MACA;QACA,KAAAzB,QAAA;UACAL,KAAA;UACAM,IAAA;QACA;MACA;MAEAoC,KAAA,CAAAxC,iBAAA;MACAwC,KAAA,CAAAE,YAAA;IACA;IACAA,aAAA;MACA;MACA,KAAAC,4BAAA;IACA;IAEA;IACAA,6BAAA;MACA;MACA,MAAAC,cAAA,IACA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UACAF,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA,GACA;UACAD,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA,GACA;UACAD,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UACAF,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA,GACA;UACAD,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UACAF,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA,GACA;UACAD,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA,GACA;UACAD,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UACAF,KAAA;UACAC,KAAA;UACAC,QAAA,GACA;YAAAF,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA;QAEA;MAEA,EACA;MAEA,KAAA5D,OAAA,GAAA0D,cAAA;IACA;IACAH,QAAAb,EAAA;MACA,IAAAY,KAAA;MACAA,KAAA,CAAAQ,UAAA,CAAAR,KAAA,CAAA3C,GAAA,gBAAA+B,EAAA,EAAAqB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAV,KAAA,CAAArC,QAAA,GAAA+C,IAAA,CAAApE,IAAA;QACA;MACA;IACA;IACAqE,QAAAC,KAAA,EAAAxB,EAAA;MACA,KAAAyB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAP,IAAA;QACA,KAAAQ,aAAA,MAAA5D,GAAA,kBAAA+B,EAAA,EAAAqB,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAQ,IAAA;YACA,KAAAxB,QAAA;cACAsB,IAAA;cACAhD,OAAA;YACA;YACA,KAAApB,IAAA,CAAAuE,MAAA,CAAAP,KAAA;UACA;YACA,KAAAlB,QAAA;cACAsB,IAAA;cACAhD,OAAA,EAAA0C,IAAA,CAAAU;YACA;UACA;QACA;MACA,GACAC,KAAA;QACA,KAAA3B,QAAA;UACAsB,IAAA;UACAhD,OAAA;QACA;MACA;IACA;IACAsD,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACA3B,WAAA;MACA,KAAA/C,IAAA;MACA,KAAAC,IAAA;MACA,KAAA6B,OAAA;IACA;IAEAA,QAAA;MACA,IAAAoB,KAAA;MACAA,KAAA,CAAA5C,OAAA;;MAEA;MACAqE,UAAA;QACAzB,KAAA,CAAA5C,OAAA;QACA4C,KAAA,CAAApD,IAAA,IACA;UACAwC,EAAA;UACA9B,KAAA;UACAM,IAAA;UACAC,QAAA;UAAA;UACAV,MAAA;UACAuE,KAAA;UACAC,WAAA;QACA,GACA;UACAvC,EAAA;UACA9B,KAAA;UACAM,IAAA;UACAC,QAAA;UAAA;UACAV,MAAA;UACAuE,KAAA;UACAC,WAAA;QACA,GACA;UACAvC,EAAA;UACA9B,KAAA;UACAM,IAAA;UACAC,QAAA;UAAA;UACAV,MAAA;UACAuE,KAAA;UACAC,WAAA;QACA,GACA;UACAvC,EAAA;UACA9B,KAAA;UACAM,IAAA;UACAC,QAAA;UAAA;UACAV,MAAA;UACAuE,KAAA;UACAC,WAAA;QACA,GACA;UACAvC,EAAA;UACA9B,KAAA;UACAM,IAAA;UACAC,QAAA;UAAA;UACAV,MAAA;UACAuE,KAAA;UACAC,WAAA;QACA,EACA;QACA3B,KAAA,CAAAnD,KAAA;MACA;;MAEA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA+E,SAAA;MACA,IAAA5B,KAAA;MACA,KAAA6B,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAAhC,KAAA,CAAA3C,GAAA,gBAAAM,QAAA,EAAA8C,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAQ,IAAA;cACAlB,KAAA,CAAAN,QAAA;gBACAsB,IAAA;gBACAhD,OAAA,EAAA0C,IAAA,CAAAU;cACA;cACA,KAAAxC,OAAA;cACAoB,KAAA,CAAAxC,iBAAA;YACA;cACAwC,KAAA,CAAAN,QAAA;gBACAsB,IAAA;gBACAhD,OAAA,EAAA0C,IAAA,CAAAU;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAa,iBAAAC,GAAA;MACA,KAAAnF,IAAA,GAAAmF,GAAA;MAEA,KAAAtD,OAAA;IACA;IACAuD,oBAAAD,GAAA;MACA,KAAApF,IAAA,GAAAoF,GAAA;MACA,KAAAtD,OAAA;IACA;IACAwD,cAAAC,GAAA;MACA,KAAA1E,QAAA,CAAA2E,QAAA,GAAAD,GAAA,CAAA/F,IAAA,CAAAe,GAAA;IACA;IAEAkF,UAAAC,IAAA;MACA,KAAA/E,UAAA,GAAA+E,IAAA;MACA,KAAA9E,aAAA;IACA;IACA+E,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAAxB,IAAA;MACA,KAAA0B,UAAA;QACA,KAAAhD,QAAA,CAAAkD,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAA9C,KAAA;MACAA,KAAA,CAAAQ,UAAA,gCAAAgC,IAAA,EAAA/B,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAQ,IAAA;UACAlB,KAAA,CAAArC,QAAA,CAAAmF,QAAA;UAEA9C,KAAA,CAAAN,QAAA,CAAAC,OAAA;QACA;UACAK,KAAA,CAAAN,QAAA,CAAAkD,KAAA,CAAAlC,IAAA,CAAAU,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}