{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue?vue&type=style&index=0&id=56f25456&scoped=true&lang=css", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue", "mtime": 1748463119363}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748377658854}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748377684217}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748377671910}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["kecheng.vue"], "names": [], "mappings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file": "kecheng.vue", "sourceRoot": "src/views/pages/shipin", "sourcesContent": ["<template>\r\n  <div class=\"course-container\">\r\n    <!-- 页面标题区域 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-video-play\"></i>\r\n            课程列表\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理和维护在线课程内容</p>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增课程\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-form\">\r\n          <div class=\"search-row\">\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程搜索</label>\r\n              <el-input\r\n                v-model=\"search.keyword\"\r\n                placeholder=\"请输入课程标题或关键词\"\r\n                class=\"search-input\"\r\n                clearable\r\n                @keyup.enter.native=\"searchData\"\r\n              >\r\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n              </el-input>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">课程类型</label>\r\n              <el-select\r\n                v-model=\"search.is_free\"\r\n                placeholder=\"请选择课程类型\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"免费课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"付费课程\" :value=\"2\"></el-option>\r\n              </el-select>\r\n            </div>\r\n\r\n            <div class=\"search-item\">\r\n              <label class=\"search-label\">热门推荐</label>\r\n              <el-select\r\n                v-model=\"search.is_hot\"\r\n                placeholder=\"请选择是否热门\"\r\n                class=\"search-select\"\r\n                clearable\r\n              >\r\n                <el-option label=\"热门课程\" :value=\"1\"></el-option>\r\n                <el-option label=\"普通课程\" :value=\"0\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"search-actions\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchData\">\r\n              搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh-left\" @click=\"clearSearch\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据统计区域 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total\">\r\n              <i class=\"el-icon-video-play\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">总课程数</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon free\">\r\n              <i class=\"el-icon-present\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ freeCount }}</div>\r\n              <div class=\"stat-label\">免费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon paid\">\r\n              <i class=\"el-icon-coin\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ paidCount }}</div>\r\n              <div class=\"stat-label\">付费课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon hot\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ hotCount }}</div>\r\n              <div class=\"stat-label\">热门课程</div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 课程列表区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <i class=\"el-icon-menu\"></i>\r\n            课程列表\r\n          </div>\r\n          <div class=\"table-tools\">\r\n            <el-button-group>\r\n              <el-button\r\n                :type=\"viewMode === 'table' ? 'primary' : ''\"\r\n                icon=\"el-icon-menu\"\r\n                @click=\"viewMode = 'table'\"\r\n                size=\"small\"\r\n              >\r\n                列表视图\r\n              </el-button>\r\n              <el-button\r\n                :type=\"viewMode === 'card' ? 'primary' : ''\"\r\n                icon=\"el-icon-s-grid\"\r\n                @click=\"viewMode = 'card'\"\r\n                size=\"small\"\r\n              >\r\n                卡片视图\r\n              </el-button>\r\n            </el-button-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格视图 -->\r\n        <div v-if=\"viewMode === 'table'\" class=\"table-view\">\r\n          <el-table\r\n            :data=\"list\"\r\n            v-loading=\"loading\"\r\n            class=\"course-table\"\r\n            stripe\r\n            @sort-change=\"handleSortChange\"\r\n          >\r\n            <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-title-cell\">\r\n                  <div class=\"course-title\">{{ scope.row.title }}</div>\r\n                  <div class=\"course-desc\" v-if=\"scope.row.desc\">{{ scope.row.desc }}</div>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"pic_path\" label=\"封面\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"course-cover\">\r\n                  <img\r\n                    :src=\"scope.row.pic_path\"\r\n                    @click=\"showImage(scope.row.pic_path)\"\r\n                    class=\"cover-image\"\r\n                    :alt=\"scope.row.title\"\r\n                  />\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"price\" label=\"价格\" width=\"100\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"price-cell\">\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <span v-else class=\"price-amount\">¥{{ scope.row.price || 0 }}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"状态\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"status-cell\">\r\n                  <el-tag v-if=\"scope.row.is_hot === 1\" type=\"warning\" size=\"small\">\r\n                    <i class=\"el-icon-star-on\"></i>\r\n                    热门\r\n                  </el-tag>\r\n                  <el-tag v-if=\"scope.row.is_free === 1\" type=\"success\" size=\"small\">\r\n                    免费\r\n                  </el-tag>\r\n                  <el-tag v-else type=\"info\" size=\"small\">\r\n                    付费\r\n                  </el-tag>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column prop=\"create_time\" label=\"创建时间\" width=\"160\" align=\"center\" sortable>\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"time-cell\">\r\n                  <i class=\"el-icon-time\"></i>\r\n                  {{ scope.row.create_time }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column fixed=\"right\" label=\"操作\" width=\"160\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    size=\"mini\"\r\n                    @click=\"editData(scope.row.id)\"\r\n                    icon=\"el-icon-edit\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"danger\"\r\n                    size=\"mini\"\r\n                    @click=\"delData(scope.$index, scope.row.id)\"\r\n                    icon=\"el-icon-delete\"\r\n                    plain\r\n                    class=\"action-btn\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 卡片视图 -->\r\n        <div v-if=\"viewMode === 'card'\" class=\"card-view\" v-loading=\"loading\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\" v-for=\"course in list\" :key=\"course.id\" class=\"course-card-col\">\r\n              <div class=\"course-card\">\r\n                <div class=\"card-cover\" @click=\"showImage(course.pic_path)\">\r\n                  <img :src=\"course.pic_path\" :alt=\"course.title\" />\r\n                  <div class=\"cover-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-content\">\r\n                  <div class=\"card-header\">\r\n                    <h3 class=\"card-title\" :title=\"course.title\">{{ course.title }}</h3>\r\n                    <div class=\"card-badges\">\r\n                      <el-tag v-if=\"course.is_hot === 1\" type=\"warning\" size=\"mini\">\r\n                        <i class=\"el-icon-star-on\"></i>\r\n                        热门\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-desc\" v-if=\"course.desc\">{{ course.desc }}</div>\r\n                  <div class=\"card-footer\">\r\n                    <div class=\"card-price\">\r\n                      <el-tag v-if=\"course.is_free === 1\" type=\"success\" size=\"small\">\r\n                        免费课程\r\n                      </el-tag>\r\n                      <span v-else class=\"price\">¥{{ course.price || 0 }}</span>\r\n                    </div>\r\n                    <div class=\"card-time\">\r\n                      <i class=\"el-icon-time\"></i>\r\n                      {{ course.create_time }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"card-actions\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      size=\"small\"\r\n                      @click=\"editData(course.id)\"\r\n                      icon=\"el-icon-edit\"\r\n                      plain\r\n                    >\r\n                      编辑\r\n                    </el-button>\r\n                    <el-button\r\n                      type=\"danger\"\r\n                      size=\"small\"\r\n                      @click=\"delData(list.indexOf(course), course.id)\"\r\n                      icon=\"el-icon-delete\"\r\n                      plain\r\n                    >\r\n                      删除\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[12, 24, 48, 96]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            class=\"pagination\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否免费\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"2\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"首页热门\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"0\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_free == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"课程视频\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"showImage(ruleForm.file_path)\"\r\n              >查看\r\n            </el-button> -->\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      search: {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/kecheng/\",\r\n      title: \"课程\",\r\n      info: {},\r\n      filed: \"\",\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      viewMode: 'table', // 视图模式：table | card\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传视频\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  computed: {\r\n    // 免费课程数量\r\n    freeCount() {\r\n      return this.list.filter(item => item.is_free === 1).length;\r\n    },\r\n    // 付费课程数量\r\n    paidCount() {\r\n      return this.list.filter(item => item.is_free === 2).length;\r\n    },\r\n    // 热门课程数量\r\n    hotCount() {\r\n      return this.list.filter(item => item.is_hot === 1).length;\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_free: \"\",\r\n        is_hot: \"\",\r\n      };\r\n      this.searchData();\r\n    },\r\n\r\n    // 处理排序变化\r\n    handleSortChange({ column, prop, order }) {\r\n      console.log('排序变化:', { column, prop, order });\r\n      // 这里可以添加排序逻辑\r\n    },\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_free: 2,\r\n          file_path: \"\",\r\n          pic_path: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n      _this.loading = true;\r\n\r\n      // 使用测试数据\r\n      setTimeout(() => {\r\n        _this.loading = false;\r\n\r\n        // 模拟搜索过滤\r\n        let filteredList = [\r\n          {\r\n            id: 1,\r\n            title: \"Vue.js 从入门到精通\",\r\n            desc: \"全面学习Vue.js框架，包括组件开发、路由管理、状态管理等核心概念\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Vue.js\",\r\n            create_time: \"2024-01-15 10:30:00\"\r\n          },\r\n          {\r\n            id: 2,\r\n            title: \"React 实战开发教程\",\r\n            desc: \"深入学习React框架，掌握现代前端开发技能\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/2196F3/white?text=React\",\r\n            create_time: \"2024-01-14 14:20:00\"\r\n          },\r\n          {\r\n            id: 3,\r\n            title: \"JavaScript 基础入门\",\r\n            desc: \"零基础学习JavaScript编程语言，为前端开发打下坚实基础\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF9800/white?text=JavaScript\",\r\n            create_time: \"2024-01-13 09:15:00\"\r\n          },\r\n          {\r\n            id: 4,\r\n            title: \"Node.js 后端开发\",\r\n            desc: \"学习使用Node.js进行后端开发，包括Express框架和数据库操作\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/4CAF50/white?text=Node.js\",\r\n            create_time: \"2024-01-12 16:45:00\"\r\n          },\r\n          {\r\n            id: 5,\r\n            title: \"CSS3 动画与特效\",\r\n            desc: \"掌握CSS3高级特性，创建炫酷的网页动画和视觉效果\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/E91E63/white?text=CSS3\",\r\n            create_time: \"2024-01-11 11:30:00\"\r\n          },\r\n          {\r\n            id: 6,\r\n            title: \"TypeScript 进阶指南\",\r\n            desc: \"深入学习TypeScript，提升JavaScript开发的类型安全性\",\r\n            price: 249,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/3F51B5/white?text=TypeScript\",\r\n            create_time: \"2024-01-10 13:20:00\"\r\n          },\r\n          {\r\n            id: 7,\r\n            title: \"HTML5 移动端开发\",\r\n            desc: \"学习HTML5移动端开发技术，创建响应式移动应用\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/FF5722/white?text=HTML5\",\r\n            create_time: \"2024-01-09 15:10:00\"\r\n          },\r\n          {\r\n            id: 8,\r\n            title: \"微信小程序开发实战\",\r\n            desc: \"从零开始学习微信小程序开发，包括组件使用、API调用等\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/00BCD4/white?text=小程序\",\r\n            create_time: \"2024-01-08 10:00:00\"\r\n          },\r\n          {\r\n            id: 9,\r\n            title: \"前端工程化实践\",\r\n            desc: \"学习现代前端工程化工具和流程，提升开发效率\",\r\n            price: 299,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/9C27B0/white?text=工程化\",\r\n            create_time: \"2024-01-07 14:30:00\"\r\n          },\r\n          {\r\n            id: 10,\r\n            title: \"Web安全基础\",\r\n            desc: \"了解常见的Web安全漏洞和防护措施，保障应用安全\",\r\n            price: 0,\r\n            is_free: 1,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/795548/white?text=安全\",\r\n            create_time: \"2024-01-06 09:45:00\"\r\n          },\r\n          {\r\n            id: 11,\r\n            title: \"数据可视化技术\",\r\n            desc: \"学习使用D3.js、ECharts等工具创建数据可视化图表\",\r\n            price: 399,\r\n            is_free: 2,\r\n            is_hot: 1,\r\n            pic_path: \"https://via.placeholder.com/300x200/607D8B/white?text=可视化\",\r\n            create_time: \"2024-01-05 12:15:00\"\r\n          },\r\n          {\r\n            id: 12,\r\n            title: \"PWA 渐进式Web应用\",\r\n            desc: \"学习PWA技术，创建类似原生应用体验的Web应用\",\r\n            price: 199,\r\n            is_free: 2,\r\n            is_hot: 0,\r\n            pic_path: \"https://via.placeholder.com/300x200/8BC34A/white?text=PWA\",\r\n            create_time: \"2024-01-04 16:20:00\"\r\n          }\r\n        ];\r\n\r\n        // 应用搜索过滤\r\n        if (_this.search.keyword) {\r\n          filteredList = filteredList.filter(item =>\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.desc.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_free !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_free === _this.search.is_free);\r\n        }\r\n\r\n        if (_this.search.is_hot !== \"\") {\r\n          filteredList = filteredList.filter(item => item.is_hot === _this.search.is_hot);\r\n        }\r\n\r\n        // 分页处理\r\n        const startIndex = (_this.page - 1) * _this.size;\r\n        const endIndex = startIndex + _this.size;\r\n        _this.list = filteredList.slice(startIndex, endIndex);\r\n        _this.total = filteredList.length;\r\n\r\n      }, 800);\r\n\r\n      // 原始API调用代码（注释掉）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      } else {\r\n        if (\r\n          !file.type.split(\"/\")[1] == \"mp4\" ||\r\n          !file.type.split(\"/\")[1] == \"qlv\" ||\r\n          !file.type.split(\"/\")[1] == \"qsv\" ||\r\n          !file.type.split(\"/\")[1] == \"oga\" ||\r\n          !file.type.split(\"/\")[1] == \"flv\" ||\r\n          !file.type.split(\"/\")[1] == \"avi\" ||\r\n          !file.type.split(\"/\")[1] == \"wmv\" ||\r\n          !file.type.split(\"/\")[1] == \"rmvb\"\r\n        ) {\r\n          this.$message({\r\n            showClose: true,\r\n            message: \"请选择'.mp4,.qlv,.qsv,.oga,.flv,.avi,.wmv,.rmvb'文件\",\r\n            type: \"error\",\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 课程管理容器 */\r\n.course-container {\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n  padding: 24px;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title-section h2.page-title {\r\n  margin: 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-section .page-subtitle {\r\n  margin: 8px 0 0 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn {\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-form {\r\n  padding: 8px;\r\n}\r\n\r\n.search-row {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 200px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input, .search-select {\r\n  width: 240px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 统计卡片区域 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.total {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.free {\r\n  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\r\n}\r\n\r\n.stat-icon.paid {\r\n  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);\r\n}\r\n\r\n.stat-icon.hot {\r\n  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #262626;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-tools {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.table-view {\r\n  padding: 0 24px 24px;\r\n}\r\n\r\n.course-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.course-title-cell {\r\n  padding: 8px 0;\r\n}\r\n\r\n.course-title {\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.course-desc {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  line-height: 1.4;\r\n  max-width: 300px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.course-cover {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.cover-image {\r\n  width: 80px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.price-cell {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.price-amount {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 16px;\r\n}\r\n\r\n.status-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #8c8c8c;\r\n  font-size: 13px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n/* 卡片视图样式 */\r\n.card-view {\r\n  padding: 0 24px 24px;\r\n  min-height: 400px;\r\n}\r\n\r\n.course-card-col {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.course-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.course-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-cover {\r\n  position: relative;\r\n  height: 200px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.card-cover img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cover-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: all 0.3s ease;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.card-cover:hover .cover-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.card-cover:hover img {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.card-content {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-badges {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.card-desc {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-price .price {\r\n  font-weight: 600;\r\n  color: #f5222d;\r\n  font-size: 18px;\r\n}\r\n\r\n.card-time {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination {\r\n  background: transparent;\r\n}\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .course-card-col {\r\n    span: 12;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .course-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-row {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .search-item {\r\n    min-width: auto;\r\n  }\r\n\r\n  .search-input, .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .course-card-col {\r\n    span: 24;\r\n  }\r\n\r\n  .table-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n"]}]}