(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2a386260"],{"0172":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-wrapper"},[t("div",{staticClass:"page-container"},[t("div",{staticClass:"page-header"},[t("h2",{staticClass:"page-title"},[e._v(e._s(this.$router.currentRoute.name))]),t("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新 ")])],1),t("div",{staticClass:"search-section"},[t("div",{staticClass:"search-controls"},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入案例标题进行搜索",clearable:""},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(t){return e.searchData()}},slot:"append"})],1)],1),t("div",{staticClass:"action-controls"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增案例 ")])],1)]),t("div",{staticClass:"table-section"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"data-table",attrs:{data:e.list,stripe:""},on:{"sort-change":e.handleSortChange}},[t("el-table-column",{attrs:{prop:"title",label:"案例标题","min-width":"200","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"case-title-cell"},[t("div",{staticClass:"case-title"},[e._v(e._s(a.row.title))]),a.row.desc?t("div",{staticClass:"case-desc"},[e._v(e._s(a.row.desc))]):e._e()])]}}])}),t("el-table-column",{attrs:{prop:"pic_path",label:"封面",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.pic_path?t("div",{staticClass:"case-cover"},[t("img",{staticClass:"cover-image",attrs:{src:a.row.pic_path,alt:a.row.title},on:{click:function(t){return e.showImage(a.row.pic_path)}}})]):t("span",{staticClass:"no-cover"},[e._v("暂无封面")])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",sortable:""},scopedSlots:e._u([{key:"default",fn:function(a){return[t("i",{staticClass:"el-icon-time"}),t("span",[e._v(e._s(a.row.create_time))])]}}])}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",{staticClass:"action-buttons"},[t("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.editData(a.row.id)}}},[e._v(" 编辑 ")]),t("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small",icon:"el-icon-delete"},nativeOn:{click:function(t){return t.preventDefault(),e.delData(a.$index,a.row.id)}}},[e._v(" 删除 ")])],1)]}}])})],1)],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":e.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("el-dialog",{attrs:{title:e.title+"内容",visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[t("el-form-item",{attrs:{label:e.title+"标题","label-width":e.formLabelWidth,prop:"title"}},[t("el-input",{attrs:{autocomplete:"off"},model:{value:e.ruleForm.title,callback:function(t){e.$set(e.ruleForm,"title",t)},expression:"ruleForm.title"}})],1),t("el-form-item",{attrs:{label:"封面","label-width":e.formLabelWidth}},[t("el-input",{staticClass:"el_input",attrs:{disabled:!0},model:{value:e.ruleForm.pic_path,callback:function(t){e.$set(e.ruleForm,"pic_path",t)},expression:"ruleForm.pic_path"}},[t("template",{slot:"append"},[e._v("280rpx*200rpx")])],2),t("el-button-group",[t("el-button",[t("el-upload",{attrs:{action:"/admin/Upload/uploadImage","show-file-list":!1,"on-success":e.handleSuccess,"before-upload":e.beforeUpload}},[e._v(" 上传 ")])],1),e.ruleForm.pic_path?t("el-button",{attrs:{type:"success"},on:{click:function(t){return e.showImage(e.ruleForm.pic_path)}}},[e._v("查看 ")]):e._e(),e.ruleForm.pic_path?t("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.delImage(e.ruleForm.pic_path,"pic_path")}}},[e._v("删除")]):e._e()],1)],1),t("el-form-item",{attrs:{label:"描述","label-width":e.formLabelWidth}},[t("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1),t("el-form-item",{attrs:{label:"内容","label-width":e.formLabelWidth}},[t("editor-bar",{attrs:{isClear:e.isClear},on:{change:e.change},model:{value:e.ruleForm.content,callback:function(t){e.$set(e.ruleForm,"content",t)},expression:"ruleForm.content"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1),t("el-dialog",{attrs:{title:"图片查看",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-image",{attrs:{src:e.show_image}})],1)],1)},l=[],i=a("0c98"),o={name:"list",components:{EditorBar:i["a"]},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/anli/",title:"案例",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},isClear:!1,rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}]},formLabelWidth:"120px"}},mounted(){this.getData()},methods:{editData(e){let t=this;0!=e?this.getInfo(e):this.ruleForm={title:"",desc:"",pic_path:"",content:""},t.dialogFormVisible=!0},getInfo(e){let t=this;t.getRequest(t.url+"read?id="+e).then(e=>{e&&(t.ruleForm=e.data)})},delData(e,t){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+t).then(t=>{200==t.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(e,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let e=this;e.loading=!0,e.postRequest(e.url+"index?page="+e.page+"&size="+e.size,e.search).then(t=>{t&&200==t.code?(e.list=Array.isArray(t.data)?t.data:[],e.total=t.count||0):(e.list=[],e.total=0),e.loading=!1}).catch(t=>{console.error("获取数据失败:",t),e.list=[],e.total=0,e.loading=!1})},saveData(){let e=this;this.$refs["ruleForm"].validate(t=>{if(!t)return!1;this.postRequest(e.url+"save",this.ruleForm).then(t=>{200==t.code?(e.$message({type:"success",message:t.msg}),this.getData(),e.dialogFormVisible=!1):e.$message({type:"error",message:t.msg})})})},handleSizeChange(e){this.size=e,this.getData()},handleCurrentChange(e){this.page=e,this.getData()},handleSortChange(e){console.log("排序变化:",e)},change(e){this.ruleForm.content=e},handleSuccess(e){this.ruleForm.pic_path=e.data.url},showImage(e){this.show_image=e,this.dialogVisible=!0},beforeUpload(e){const t=/^image\/(jpeg|png|jpg)$/.test(e.type);t||this.$message.error("上传图片格式不对!")},delImage(e,t){let a=this;a.getRequest("/Upload/delImage?fileName="+e).then(e=>{200==e.code?(a.ruleForm[t]="",a.$message.success("删除成功!")):a.$message.error(e.msg)})}}},r=o,n=(a("f69f"),a("2877")),c=Object(n["a"])(r,s,l,!1,null,"0e015722",null);t["default"]=c.exports},ca02:function(e,t,a){},f69f:function(e,t,a){"use strict";a("ca02")}}]);
//# sourceMappingURL=chunk-2a386260.cf8493dc.js.map