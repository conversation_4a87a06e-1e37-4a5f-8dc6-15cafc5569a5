<?php
/**
 * author zct
 * date 2020-10-22
 * desc 微信公众号开发
 * ps 佛祖保佑
 */
namespace untils;
class Wechat{
    private static $errorMsg;

    private $config = [
      //  'appid'     => 'wxc98ee59d737de2c4',
       // 'appsecret'     => 'dd07ff3059d41289fdaa7c82feaad190'
        //福建法多邦法务公众号
        'appid'     => 'wx82309797155b7b16',
        'appsecret'     => '8348621479a22b3c39dc929fcbe89548'
    ];

    const DEFAULT_ERROR_MSG = '操作失败,请稍候再试!';
    
    /**
     * Service constructor.
     */
    public function __construct()
    {

    }
    /**
     * [_getaccessToken 获取token]
     * @param  [type] $code [code]
     * @return [type]       [返回token]
     */
    public function _getaccessToken(){
        $url ='https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid='.$this->config['appid'].'&secret='.$this->config['appsecret'];
        $data = $this->_httpsRequest($url);
        $data = json_decode($data,true);
        return $data['access_token'];

    }
    public function _getMaterials(){
        $url = 'https://api.weixin.qq.com/cgi-bin/template/get_industry?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url);
        return json_decode($data,true);
    }
    /**
     * @return mixed 获取设置的行业信息
     */
    public function _getIndustry(){
        $url = 'https://api.weixin.qq.com/cgi-bin/template/get_industry?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url);
        return json_decode($data,true);
    }

    /**
     * [_getIndustryList 获取模板列表
     * @return array  返回列表
     */
    public function _getIndustryList(){

        $url =  'https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url);
        return json_decode($data,true);
        20000000
    }

    /**
     * 获取公众号用户id
     * @return array
     */
    public function _getUserList(){
        $url  ='https://api.weixin.qq.com/cgi-bin/user/get?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url);
        return json_decode($data,true);
    }

    public function _getUserList2($user_list){
        $url = ' https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token='.$this->_getaccessToken();
        $data =  $this->_httpsRequest($url,$user_list);
        return json_decode($data,true);
    }
    public function _getUserInfo($openid){
        $url = 'https://api.weixin.qq.com/cgi-bin/user/info?access_token='.$this->_getaccessToken().'&openid='.$openid.'&lang=zh_CN';
        $data =  $this->_httpsRequest($url);
        return json_decode($data,true);
    }
    public function _getINdustryId($template_id_short=''){
        $url = 'https://api.weixin.qq.com/cgi-bin/template/api_add_template?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url,['template_id_short'=>$template_id_short]);
        return json_decode($data,true);
    }
    public function _sendTemplete($data){
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token='.$this->_getaccessToken();
        $data = $this->_httpsRequest($url,$data);
        return json_decode($data,true);
    }
    /**
     * @param $url
     * @param null $data
     * @return mixed
     */
    protected function _httpsRequest($url, $data = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)){
            //处理php版本问题开始
//            if (class_exists('\CURLFile')) {
//                $data['media'] = new \CURLFile(realpath($data['media']));
//            } else {
//                if (defined('CURLOPT_SAFE_UPLOAD')) {
//                    curl_setopt($curl, CURLOPT_SAFE_UPLOAD, FALSE);
//                }
//            }
            $postData = json_encode($data);

            //处理php版本问题结束
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }
    /**
     * 获取错误信息
     * @param string $defaultMsg
     * @return string
     */
    public  function _getErrorInfo($defaultMsg = self::DEFAULT_ERROR_MSG)
    {
        return !empty(self::$errorMsg) ? self::$errorMsg : $defaultMsg;
    }
    /**
     *_request():发出请求
     *@curl:访问的URL
     *@https：安全访问协议
     *@method：请求的方式，默认为get
     *@data：post方式请求时上传的数据
     **/
    private function _post($curl){
        $ch = curl_init();//初始化
        curl_setopt($ch, CURLOPT_URL, $curl);//设置访问的URL
        curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//不做服务器认证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//不做客户端认证
        curl_setopt($ch, CURLOPT_POST, true);//设置请求是POST方式
        $str = curl_exec($ch);//执行访问，返回结果
        curl_close($ch);//关闭curl，释放资源
        return $str;
    }

}