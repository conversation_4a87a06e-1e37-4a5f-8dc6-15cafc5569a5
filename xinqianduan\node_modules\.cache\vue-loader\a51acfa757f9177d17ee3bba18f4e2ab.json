{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=style&index=0&id=ab8b1f14&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748446032426}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748336490492}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748336507382}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748336500812}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1748336505711}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["chat.vue"], "names": [], "mappings": ";AAo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file": "chat.vue", "sourceRoot": "src/views/pages/yonghu", "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button \r\n                type=\"text\" \r\n                icon=\"el-icon-more\" \r\n                @click=\"quanyuan\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 群成员侧边栏 -->\r\n        <div class=\"member-sidebar\" :class=\"{ 'show': la }\">\r\n          <div class=\"member-content\">\r\n            <!-- 用户列表 -->\r\n            <div class=\"member-section\" v-if=\"userss.length\">\r\n              <div class=\"section-title\">用户</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-item\" v-for=\"(item, index) in userss\" :key=\"'user-' + index\">\r\n                  <div v-for=\"(value, key) in item.list\" :key=\"'user-item-' + key\" class=\"member-card\">\r\n                    <img :src=\"value.headimg\" class=\"member-avatar\"/>\r\n                    <div class=\"member-name\">{{ value.nickname }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 员工列表 -->\r\n            <div class=\"member-section\" v-for=\"(item, index) in yuangongss\" :key=\"'staff-' + index\">\r\n              <div class=\"section-title\">{{ item.zhiwei }}</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-card\" v-for=\"(value, key) in item.list\" :key=\"'staff-item-' + key\">\r\n                  <img :src=\"value.pic_path\" class=\"member-avatar\"/>\r\n                  <div class=\"member-name\">{{ value.title }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n      la: false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    quanyuan() {\r\n      _this.la = !_this.la\r\n      _this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.userss = resp.data.users\r\n          _this.lvshiss = resp.data.lvshis\r\n          _this.yuangongss = resp.data.yuangongs\r\n        }\r\n      });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 群成员侧边栏 */\r\n.member-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -300px;\r\n  width: 300px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n</style>\r\n"]}]}