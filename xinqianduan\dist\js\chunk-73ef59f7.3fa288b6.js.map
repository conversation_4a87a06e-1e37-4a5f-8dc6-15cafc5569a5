{"version": 3, "sources": ["webpack:///./src/components/UserDetail.vue?89c4", "webpack:///./src/views/pages/wenshu/shenhe.vue?ac7d", "webpack:///./src/components/UserDetail.vue", "webpack:///src/components/UserDetail.vue", "webpack:///./src/components/UserDetail.vue?fef8", "webpack:///./src/components/UserDetail.vue?a94f", "webpack:///./src/views/pages/wenshu/shenhe.vue", "webpack:///src/views/pages/wenshu/shenhe.vue", "webpack:///./src/views/pages/wenshu/shenhe.vue?1fc8", "webpack:///./src/views/pages/wenshu/shenhe.vue?2b54"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "attrs", "slot", "_v", "_s", "info", "company", "phone", "nickname", "linkman", "linkphone", "yuangong_id", "start_time", "year", "headimg", "staticStyle", "nativeOn", "$event", "showImage", "license", "on", "tiaojie_name", "fawu_name", "lian_name", "htsczy_name", "ls_name", "ywy_name", "directives", "name", "rawName", "value", "loading", "expression", "debts", "background", "color", "scopedSlots", "_u", "key", "fn", "scope", "row", "tel", "money", "status", "viewDebtDetail", "length", "_e", "dialogVisible", "show_image", "staticRenderFns", "props", "id", "type", "String", "Number", "required", "data", "watch", "immediate", "handler", "newId", "console", "log", "getInfo", "methods", "_this", "setTimeout", "testUserData", "imageUrl", "debt", "$message", "component", "$router", "currentRoute", "refulsh", "model", "search", "keyword", "callback", "$$v", "$set", "is_deal", "_l", "options1", "item", "title", "getData", "clearData", "list", "order_sn", "desc", "getStatusType", "review_status", "getStatusText", "current_reviewer", "viewUserData", "uid", "create_time", "canReview", "startReview", "viewReviewProgress", "previewContract", "submitToFiling", "delData", "$index", "size", "total", "handleSizeChange", "handleCurrentChange", "dialogFormVisible", "ref", "ruleForm", "rules", "form<PERSON>abe<PERSON><PERSON>", "images", "item2", "index2", "attach_path", "item3", "index3", "file_path", "changeFile", "handleSuccess", "delImage", "content", "saveData", "dialogPreview", "previewData", "image", "index", "file", "viewFile", "downloadFile", "dialogReview", "reviewData", "reviewForm", "reviewRules", "result", "reason", "message", "trigger", "comment", "reviewSubmitting", "submitReview", "dialogProgress", "progressData", "reviewSteps", "step", "time", "reviewer", "dialogViewUserDetail", "currentId", "components", "UserDetails", "allSize", "page", "is_pay", "url", "is_num", "options", "mounted", "filed", "editData", "getRequest", "then", "resp", "code", "msg", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "splice", "go", "searchData", "allData", "filteredData", "filter", "includes", "$refs", "validate", "valid", "postRequest", "val", "res", "success", "error", "beforeUpload", "isTypeTrue", "test", "fileName", "fileUrl", "window", "open", "link", "document", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "statusMap", "currentData", "findIndex", "getNextReviewStatus", "currentItem", "statusFlow", "reviewerMap", "nextStatus", "generateReviewSteps", "allSteps", "push"], "mappings": "kHAAA,W,6DCAA,W,2DCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUC,KAAK,UAAU,CAACJ,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,cAAcL,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKC,SAAW,cAAcR,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,SAASL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKE,OAAS,cAAcT,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKG,UAAY,eAAe,GAAGV,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,SAASL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKI,SAAW,cAAcX,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKK,WAAa,cAAcZ,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKM,aAAe,eAAe,GAAGb,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKO,YAAc,cAAcd,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKQ,KAAOjB,EAAIS,KAAKQ,KAAO,IAAM,cAAcf,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,QAAQL,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAIS,KAAKS,SAAgC,KAArBlB,EAAIS,KAAKS,QAAgBhB,EAAG,YAAY,CAACiB,YAAY,CAAC,OAAS,WAAWd,MAAM,CAAC,IAAML,EAAIS,KAAKS,QAAQ,KAAO,IAAIE,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAOrB,EAAIsB,UAAUtB,EAAIS,KAAKS,aAAahB,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIO,GAAG,UAAU,QAAQ,GAAGL,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,KAAK,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,cAAc,CAAEJ,EAAIS,KAAKc,SAAgC,KAArBvB,EAAIS,KAAKc,QAAgBrB,EAAG,WAAW,CAACiB,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,OAAS,WAAWd,MAAM,CAAC,IAAML,EAAIS,KAAKc,QAAQ,IAAM,SAASC,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIsB,UAAUtB,EAAIS,KAAKc,YAAY,CAACrB,EAAG,MAAM,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,SAASC,KAAK,SAAS,CAACJ,EAAG,IAAI,CAACE,YAAY,gCAAgCF,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIO,GAAG,UAAU,QAAQ,IAAI,GAAGL,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUC,KAAK,UAAU,CAACJ,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,YAAYL,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,SAASL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKgB,cAAgB,cAAcvB,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKiB,WAAa,cAAcxB,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKkB,WAAa,eAAe,GAAGzB,EAAG,SAAS,CAACG,MAAM,CAAC,OAAS,KAAK,CAACH,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKmB,aAAe,cAAc1B,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,QAAQL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKoB,SAAW,cAAc3B,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,IAAI,CAACH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,SAASL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,KAAKqB,UAAY,eAAe,IAAI,GAAG5B,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,OAAS,UAAU,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,UAAUC,KAAK,UAAU,CAACJ,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,aAAaL,EAAG,WAAW,CAAC6B,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYC,MAAOlC,EAAImC,QAASC,WAAW,YAAYjB,YAAY,CAAC,MAAQ,QAAQd,MAAM,CAAC,KAAOL,EAAIS,KAAK4B,MAAM,KAAO,SAAS,OAAS,GAAG,oBAAoB,CAACC,WAAW,UAAUC,MAAM,aAAa,CAACrC,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,QAAQ,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,UAAU,KAAO,UAAU,CAACL,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAIb,gBAAgB9B,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,MAAM,MAAQ,QAAQ,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAIC,eAAe5C,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACJ,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIE,iBAAiB7C,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,SAAS,MAAQ,KAAK,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,SAAS,CAACG,MAAM,CAAC,KAA4B,QAArBuC,EAAMC,IAAIG,OAAmB,UAAY,UAAU,KAAO,UAAU,CAAChD,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIG,QAAQ,cAAc9C,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,OAAO,KAAO,SAASmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIiD,eAAeL,EAAMC,QAAQ,CAAC3C,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIO,GAAG,kBAAkB,GAAKP,EAAIS,KAAK4B,OAAmC,IAA1BrC,EAAIS,KAAK4B,MAAMa,OAA0HlD,EAAImD,KAAhHjD,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,IAAI,CAACF,EAAIO,GAAG,gBAAyB,GAAGL,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIoD,cAAc,MAAQ,OAAO5B,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAIoD,cAAc/B,KAAU,CAACnB,EAAG,WAAW,CAACG,MAAM,CAAC,IAAML,EAAIqD,eAAe,IAAI,IAEjuNC,EAAkB,GCqNtB,GACAtB,KAAA,cACAuB,MAAA,CACAC,GAAA,CACAC,KAAA,CAAAC,OAAAC,QACAC,UAAA,IAGAC,OACA,OACApD,KAAA,GACA0B,SAAA,EACAiB,eAAA,EACAC,WAAA,KAGAS,MAAA,CACAN,GAAA,CACAO,WAAA,EACAC,QAAAC,GACAA,GAAA,GAAAA,IACAC,QAAAC,IAAA,sBAAAF,GACA,KAAAG,QAAAH,OAKAI,QAAA,CACAD,QAAAZ,GACA,IAAAc,EAAA,KACAJ,QAAAC,IAAA,eAAAX,GACAc,EAAAnC,SAAA,EAGAoC,WAAA,KACA,MAAAC,EAAA,CACAhB,KACA9C,QAAA,WACAC,MAAA,cACAC,SAAA,KACAC,QAAA,KACAK,QAAA,GACAH,YAAA,QACAD,UAAA,cACAW,aAAA,OACAC,UAAA,MACAC,UAAA,OACAC,YAAA,OACAC,QAAA,MACAC,SAAA,OACAP,QAAA,GACAP,WAAA,aACAC,KAAA,EACAoB,MAAA,CACA,CACAL,KAAA,OACAc,IAAA,cACAC,MAAA,QACAC,OAAA,OAEA,CACAhB,KAAA,OACAc,IAAA,cACAC,MAAA,QACAC,OAAA,SAKAsB,EAAA7D,KAAA+D,EACAF,EAAAnC,SAAA,EACA+B,QAAAC,IAAA,YAAAK,IACA,MAmBAlD,UAAAmD,GACA,KAAApB,WAAAoB,EACA,KAAArB,eAAA,GAGAH,eAAAyB,GACAR,QAAAC,IAAA,WAAAO,GAEA,KAAAC,SAAAlE,KAAA,iBC1TmV,I,wBCQ/UmE,EAAY,eACd,EACA7E,EACAuD,GACA,EACA,KACA,WACA,MAIa,OAAAsB,E,kDCnBf,IAAI7E,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,6BAA6BJ,EAAIO,GAAG,IAAIP,EAAIQ,GAAGP,KAAK4E,QAAQC,aAAa9C,MAAM,OAAO9B,EAAG,YAAY,CAACE,YAAY,cAAcC,MAAM,CAAC,KAAO,QAAQmB,GAAG,CAAC,MAAQxB,EAAI+E,UAAU,CAAC7E,EAAG,IAAI,CAACE,YAAY,oBAAoBJ,EAAIO,GAAG,WAAW,GAAGL,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIO,GAAG,WAAWL,EAAG,WAAW,CAACE,YAAY,eAAeC,MAAM,CAAC,YAAc,kBAAkB,UAAY,IAAI2E,MAAM,CAAC9C,MAAOlC,EAAIiF,OAAOC,QAASC,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIiF,OAAQ,UAAWG,IAAMhD,WAAW,mBAAmB,CAAClC,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,KAAK,cAAc,GAAGJ,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,QAAQ,CAACE,YAAY,gBAAgB,CAACJ,EAAIO,GAAG,UAAUL,EAAG,YAAY,CAACE,YAAY,gBAAgBC,MAAM,CAAC,YAAc,QAAQ,UAAY,IAAI2E,MAAM,CAAC9C,MAAOlC,EAAIiF,OAAOK,QAASH,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIiF,OAAQ,UAAWG,IAAMhD,WAAW,mBAAmBpC,EAAIuF,GAAIvF,EAAIwF,UAAU,SAASC,GAAM,OAAOvF,EAAG,YAAY,CAACwC,IAAI+C,EAAKjC,GAAGnD,MAAM,CAAC,MAAQoF,EAAKC,MAAM,MAAQD,EAAKjC,SAAQ,IAAI,GAAGtD,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI2F,aAAa,CAAC3F,EAAIO,GAAG,UAAUL,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,wBAAwBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI4F,eAAe,CAAC5F,EAAIO,GAAG,WAAW,OAAOL,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,WAAW,CAAC6B,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYC,MAAOlC,EAAImC,QAASC,WAAW,YAAYhC,YAAY,aAAaC,MAAM,CAAC,KAAOL,EAAI6F,KAAK,OAAS,GAAG,OAAS,GAAG,aAAa,aAAa,CAAC3F,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,MAAQ,MAAM,wBAAwB,IAAImC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAIiD,sBAAsB5F,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,SAAS,CAACG,MAAM,CAAC,KAA0B,SAAnBuC,EAAMC,IAAIY,KAAkB,UAAY,OAAO,KAAO,SAAS,CAACzD,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIY,MAAM,cAAcvD,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,YAAY,MAAM,wBAAwB,IAAImC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,IAAI,CAACE,YAAY,0BAA0BF,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAI6C,mBAAmBxF,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,OAAO,MAAQ,OAAO,YAAY,MAAM,wBAAwB,IAAImC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIkD,MAAM,cAAc7F,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,gBAAgB,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,SAAS,CAACG,MAAM,CAAC,KAAOL,EAAIgG,cAAcpD,EAAMC,IAAIoD,eAAe,KAAO,UAAU,CAACjG,EAAIO,GAAG,IAAIP,EAAIQ,GAAGR,EAAIkG,cAActD,EAAMC,IAAIoD,gBAAgB,cAAc/F,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,mBAAmB,MAAQ,QAAQ,MAAQ,MAAM,MAAQ,UAAUmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAEA,EAAMC,IAAIsD,iBAAkBjG,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAIsD,qBAAqBjG,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIO,GAAG,cAAcL,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,WAAW,MAAQ,MAAM,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,KAAO,UAAU,WAAY,GAAOmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIoG,aAAaxD,EAAMC,IAAIwD,QAAQ,CAACnG,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIjC,UAAU,cAAcV,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,MAAQ,OAAOmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,UAAU,CAACE,YAAY,YAAYC,MAAM,CAAC,KAAO,UAAU,WAAY,GAAOmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIoG,aAAaxD,EAAMC,IAAIwD,QAAQ,CAACnG,EAAG,IAAI,CAACE,YAAY,kBAAkBJ,EAAIO,GAAG,IAAIP,EAAIQ,GAAGoC,EAAMC,IAAIlC,OAAO,cAAcT,EAAG,kBAAkB,CAACG,MAAM,CAAC,KAAO,cAAc,MAAQ,OAAO,MAAQ,MAAM,MAAQ,UAAUmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIO,GAAGP,EAAIQ,GAAGoC,EAAMC,IAAIyD,yBAAyBpG,EAAG,kBAAkB,CAACG,MAAM,CAAC,MAAQ,QAAQ,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUmC,YAAYxC,EAAIyC,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,SAASC,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAAEJ,EAAIuG,UAAU3D,EAAMC,KAAM3C,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,wBAAwBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIwG,YAAY5D,EAAMC,QAAQ,CAAC7C,EAAIO,GAAG,UAAUP,EAAImD,KAAKjD,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,OAAO,KAAO,OAAO,KAAO,gBAAgBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIyG,mBAAmB7D,EAAMC,QAAQ,CAAC7C,EAAIO,GAAG,UAAUL,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,oBAAoBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI0G,gBAAgB9D,EAAMC,QAAQ,CAAC7C,EAAIO,GAAG,UAAuC,aAA5BqC,EAAMC,IAAIoD,cAA8B/F,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,UAAU,KAAO,OAAO,KAAO,sBAAsBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI2G,eAAe/D,EAAMC,QAAQ,CAAC7C,EAAIO,GAAG,UAAUP,EAAImD,KAAKjD,EAAG,YAAY,CAACE,YAAY,aAAaC,MAAM,CAAC,KAAO,SAAS,KAAO,OAAO,KAAO,kBAAkBmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI4G,QAAQhE,EAAMiE,OAAQjE,EAAMC,IAAIW,OAAO,CAACxD,EAAIO,GAAG,WAAW,WAAW,GAAGL,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,gBAAgB,CAACG,MAAM,CAAC,aAAa,CAAC,GAAI,GAAI,IAAK,KAAK,YAAYL,EAAI8G,KAAK,OAAS,0CAA0C,MAAQ9G,EAAI+G,MAAM,WAAa,IAAIvF,GAAG,CAAC,cAAcxB,EAAIgH,iBAAiB,iBAAiBhH,EAAIiH,wBAAwB,IAAI,GAAG/G,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQL,EAAI0F,MAAQ,KAAK,QAAU1F,EAAIkH,kBAAkB,wBAAuB,EAAM,MAAQ,OAAO1F,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAIkH,kBAAkB7F,KAAU,CAACnB,EAAG,UAAU,CAACiH,IAAI,WAAW9G,MAAM,CAAC,MAAQL,EAAIoH,SAAS,MAAQpH,EAAIqH,QAAQ,CAACnH,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,WAAW,CAACG,MAAM,CAAC,aAAe,MAAM,SAAW,IAAI2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAAS1B,MAAOP,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,QAAShC,IAAMhD,WAAW,qBAAqB,GAAGlC,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,WAAW,CAACG,MAAM,CAAC,aAAe,MAAM,SAAW,GAAG,KAAO,WAAW,KAAO,GAAG2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAASrB,KAAMZ,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,OAAQhC,IAAMhD,WAAW,oBAAoB,GAAIpC,EAAIoH,SAASG,OAAO,GAAIrH,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,MAAM,CAACiB,YAAY,CAAC,MAAQ,OAAO,QAAU,eAAenB,EAAIuF,GAAIvF,EAAIoH,SAASG,QAAQ,SAASC,EAAMC,GAAQ,OAAOvH,EAAG,MAAM,CAACwC,IAAI+E,EAAOrH,YAAY,aAAae,YAAY,CAAC,MAAQ,OAAO,cAAc,QAAQ,CAACjB,EAAG,MAAM,CAACiB,YAAY,CAAC,MAAQ,QAAQ,OAAS,SAASd,MAAM,CAAC,IAAMmH,EAAM,KAAO,aAAahG,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIsB,UAAUkG,YAAe,KAAKxH,EAAImD,KAAMnD,EAAIoH,SAASM,YAAY,GAAIxH,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,MAAM,CAACiB,YAAY,CAAC,MAAQ,OAAO,QAAU,aAAa,cAAc,SAASnB,EAAIuF,GAAIvF,EAAIoH,SAASM,aAAa,SAASC,EAAMC,GAAQ,OAAO1H,EAAG,MAAM,CAACwC,IAAIkF,GAAQ,CAAED,EAAOzH,EAAG,MAAM,CAACA,EAAG,MAAM,CAACF,EAAIO,GAAG,KAAKP,EAAIQ,GAAGoH,EAAQ,IAAI1H,EAAG,IAAI,CAACiB,YAAY,CAAC,cAAc,QAAQd,MAAM,CAAC,KAAOsH,EAAM,OAAS,WAAW,CAAC3H,EAAIO,GAAG,QAAQL,EAAG,IAAI,CAACiB,YAAY,CAAC,cAAc,QAAQd,MAAM,CAAC,KAAOsH,IAAQ,CAAC3H,EAAIO,GAAG,UAAUL,EAAG,QAAQF,EAAImD,UAAS,KAAKnD,EAAImD,KAAKjD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,MAAM,CAACA,EAAG,WAAW,CAACG,MAAM,CAAC,MAAQ,GAAG2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAAS9B,QAASH,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,UAAWhC,IAAMhD,WAAW,qBAAqB,CAACpC,EAAIO,GAAG,SAASL,EAAG,WAAW,CAACG,MAAM,CAAC,MAAQ,GAAG2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAAS9B,QAASH,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,UAAWhC,IAAMhD,WAAW,qBAAqB,CAACpC,EAAIO,GAAG,UAAU,KAA8B,GAAxBP,EAAIoH,SAAS9B,QAAcpF,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,QAAQ,cAAcL,EAAIsH,eAAe,KAAO,cAAc,CAACpH,EAAG,WAAW,CAACE,YAAY,WAAWC,MAAM,CAAC,UAAW,GAAM2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAASS,UAAW1C,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,YAAahC,IAAMhD,WAAW,wBAAwBlC,EAAG,kBAAkB,CAACA,EAAG,YAAY,CAACsB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAI8H,WAAW,gBAAgB,CAAC5H,EAAG,YAAY,CAACG,MAAM,CAAC,OAAS,2BAA2B,kBAAiB,EAAM,aAAaL,EAAI+H,gBAAgB,CAAC/H,EAAIO,GAAG,WAAW,GAAIP,EAAIoH,SAASS,UAAW3H,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAUmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIgI,SAAShI,EAAIoH,SAASS,UAAW,gBAAgB,CAAC7H,EAAIO,GAAG,QAAQP,EAAImD,MAAM,IAAI,GAAGnD,EAAImD,KAA8B,GAAxBnD,EAAIoH,SAAS9B,SAAqC,GAArBtF,EAAIoH,SAAS3D,KAAWvD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,cAAcL,EAAIsH,iBAAiB,CAACpH,EAAG,WAAW,CAACG,MAAM,CAAC,aAAe,MAAM,KAAO,WAAW,KAAO,GAAG2E,MAAM,CAAC9C,MAAOlC,EAAIoH,SAASa,QAAS9C,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAIoH,SAAU,UAAWhC,IAAMhD,WAAW,uBAAuB,GAAGpC,EAAImD,MAAM,GAAGjD,EAAG,MAAM,CAACE,YAAY,gBAAgBC,MAAM,CAAC,KAAO,UAAUC,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACsB,GAAG,CAAC,MAAQ,SAASH,GAAQrB,EAAIkH,mBAAoB,KAAS,CAAClH,EAAIO,GAAG,SAASL,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,WAAWmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIkI,cAAc,CAAClI,EAAIO,GAAG,UAAU,IAAI,GAAGL,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIoD,cAAc,MAAQ,OAAO5B,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAIoD,cAAc/B,KAAU,CAACnB,EAAG,WAAW,CAACG,MAAM,CAAC,IAAML,EAAIqD,eAAe,GAAGnD,EAAG,YAAY,CAACE,YAAY,0BAA0BC,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAImI,cAAc,wBAAuB,EAAM,MAAQ,OAAO3G,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAImI,cAAc9G,KAAU,CAACnB,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIoI,YAAY1C,UAAUxF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIO,GAAG,QAAQP,EAAIQ,GAAGR,EAAIoI,YAAYtC,UAAU,OAAO5F,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIO,GAAG,QAAQP,EAAIQ,GAAGR,EAAIoI,YAAYxH,UAAU,OAAOV,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIO,GAAG,SAASP,EAAIQ,GAAGR,EAAIoI,YAAY9B,aAAa,WAAWpG,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAG,UAAUL,EAAG,IAAI,CAACF,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIoI,YAAYrC,WAAY/F,EAAIoI,YAAYb,QAAUvH,EAAIoI,YAAYb,OAAOrE,OAAQhD,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAIuF,GAAIvF,EAAIoI,YAAYb,QAAQ,SAASc,EAAMC,GAAO,OAAOpI,EAAG,MAAM,CAACwC,IAAI4F,EAAMlI,YAAY,aAAaoB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIsB,UAAU+G,MAAU,CAACnI,EAAG,MAAM,CAACG,MAAM,CAAC,IAAMgI,EAAM,IAAM,eAAc,KAAKrI,EAAImD,KAAMnD,EAAIoI,YAAYV,aAAe1H,EAAIoI,YAAYV,YAAYxE,OAAQhD,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAG,UAAUL,EAAG,MAAM,CAACE,YAAY,aAAaJ,EAAIuF,GAAIvF,EAAIoI,YAAYV,aAAa,SAASa,EAAKD,GAAO,OAAQC,EAAMrI,EAAG,MAAM,CAACwC,IAAI4F,EAAMlI,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIO,GAAG,KAAKP,EAAIQ,GAAG8H,EAAQ,MAAMpI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,QAAQmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIwI,SAASD,MAAS,CAACvI,EAAIO,GAAG,QAAQL,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,QAAQmB,GAAG,CAAC,MAAQ,SAASH,GAAQ,OAAOrB,EAAIyI,aAAaF,MAAS,CAACvI,EAAIO,GAAG,SAAS,KAAKP,EAAImD,QAAO,KAAKnD,EAAImD,WAAWjD,EAAG,YAAY,CAACE,YAAY,gBAAgBC,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAI0I,aAAa,wBAAuB,EAAM,MAAQ,OAAOlH,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAI0I,aAAarH,KAAU,CAACnB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAIQ,GAAGR,EAAI2I,WAAWjD,UAAUxF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBJ,EAAIO,GAAG,QAAQP,EAAIQ,GAAGR,EAAI2I,WAAW7C,UAAU,OAAO5F,EAAG,OAAO,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBJ,EAAIO,GAAG,QAAQP,EAAIQ,GAAGR,EAAI2I,WAAW/H,UAAU,WAAWV,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,UAAU,CAACiH,IAAI,aAAa9G,MAAM,CAAC,MAAQL,EAAI4I,WAAW,MAAQ5I,EAAI6I,YAAY,cAAc,UAAU,CAAC3I,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,WAAW,CAACH,EAAG,iBAAiB,CAAC8E,MAAM,CAAC9C,MAAOlC,EAAI4I,WAAWE,OAAQ3D,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAI4I,WAAY,SAAUxD,IAAMhD,WAAW,sBAAsB,CAAClC,EAAG,WAAW,CAACG,MAAM,CAAC,MAAQ,aAAa,CAACL,EAAIO,GAAG,QAAQL,EAAG,WAAW,CAACG,MAAM,CAAC,MAAQ,aAAa,CAACL,EAAIO,GAAG,UAAU,IAAI,GAA8B,aAA1BP,EAAI4I,WAAWE,OAAuB5I,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,QAAQ,KAAO,WAAW,CAACH,EAAG,YAAY,CAACiB,YAAY,CAAC,MAAQ,QAAQd,MAAM,CAAC,YAAc,WAAW,UAAY,IAAI2E,MAAM,CAAC9C,MAAOlC,EAAI4I,WAAWG,OAAQ5D,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAI4I,WAAY,SAAUxD,IAAMhD,WAAW,sBAAsB,CAAClC,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,UAAU,MAAQ,sBAAsBH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,SAAS,MAAQ,iBAAiBH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,QAAQ,MAAQ,kBAAkBH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,UAAU,MAAQ,sBAAsBH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,SAAS,MAAQ,yBAAyBH,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQ,OAAO,MAAQ,YAAY,IAAI,GAAGL,EAAImD,KAAKjD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAU,MAAkC,aAA1BL,EAAI4I,WAAWE,OAAwB,CAAC,CAAElF,UAAU,EAAMoF,QAAS,UAAWC,QAAS,SAAY,KAAK,CAAC/I,EAAG,WAAW,CAACG,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAc,iBAAiB2E,MAAM,CAAC9C,MAAOlC,EAAI4I,WAAWM,QAAS/D,SAAS,SAAUC,GAAMpF,EAAIqF,KAAKrF,EAAI4I,WAAY,UAAWxD,IAAMhD,WAAW,yBAAyB,IAAI,IAAI,KAAKlC,EAAG,MAAM,CAACE,YAAY,gBAAgBC,MAAM,CAAC,KAAO,UAAUC,KAAK,UAAU,CAACJ,EAAG,YAAY,CAACsB,GAAG,CAAC,MAAQ,SAASH,GAAQrB,EAAI0I,cAAe,KAAS,CAAC1I,EAAIO,GAAG,QAAQL,EAAG,YAAY,CAACG,MAAM,CAAC,KAAO,UAAU,QAAUL,EAAImJ,kBAAkB3H,GAAG,CAAC,MAAQxB,EAAIoJ,eAAe,CAACpJ,EAAIO,GAAG,aAAa,KAAKL,EAAG,YAAY,CAACE,YAAY,kBAAkBC,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAIqJ,eAAe,UAAY,MAAM,KAAO,MAAM,yBAAwB,EAAK,wBAAuB,GAAO7H,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAIqJ,eAAehI,KAAU,CAACnB,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIsJ,aAAa5D,UAAUxF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,qBAAqBF,EAAG,OAAO,CAACF,EAAIO,GAAG,OAAOP,EAAIQ,GAAGR,EAAIsJ,aAAaxD,eAAe5F,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIO,GAAG,QAAQP,EAAIQ,GAAGR,EAAIkG,cAAclG,EAAIsJ,aAAarD,yBAAyB/F,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,cAAcF,EAAIuF,GAAIvF,EAAIuJ,aAAa,SAASC,EAAKlB,GAAO,OAAOpI,EAAG,mBAAmB,CAACwC,IAAI4F,EAAMjI,MAAM,CAAC,UAAYmJ,EAAKC,KAAK,KAAuB,cAAhBD,EAAKxG,OAAyB,UAA4B,YAAhBwG,EAAKxG,OAAuB,UAAY,OAAO,KAAuB,cAAhBwG,EAAKxG,OAAyB,gBAAkC,YAAhBwG,EAAKxG,OAAuB,kBAAoB,eAAe,UAAY,QAAQ,CAAC9C,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,KAAK,CAACF,EAAIO,GAAGP,EAAIQ,GAAGgJ,EAAK9D,UAAUxF,EAAG,IAAI,CAACF,EAAIO,GAAGP,EAAIQ,GAAGgJ,EAAKE,aAAcF,EAAKN,QAAShJ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACF,EAAIO,GAAG,WAAWP,EAAIO,GAAGP,EAAIQ,GAAGgJ,EAAKN,SAAS,OAAOlJ,EAAImD,KAAMqG,EAAKT,OAAQ7I,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACF,EAAIO,GAAG,YAAYP,EAAIO,GAAGP,EAAIQ,GAAGgJ,EAAKT,QAAQ,OAAO/I,EAAImD,YAAW,IAAI,OAAOjD,EAAG,YAAY,CAACE,YAAY,qBAAqBC,MAAM,CAAC,MAAQ,OAAO,QAAUL,EAAI2J,qBAAqB,UAAY,MAAM,KAAO,MAAM,yBAAwB,EAAK,wBAAuB,GAAOnI,GAAG,CAAC,iBAAiB,SAASH,GAAQrB,EAAI2J,qBAAqBtI,KAAU,CAACnB,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,eAAe,CAACG,MAAM,CAAC,GAAKL,EAAI4J,cAAc,MAAM,IAEx4gBtG,EAAkB,G,wBC4iBP,GACftB,KAAA,OACA6H,WAAA,CAAAC,oBACAjG,OACA,OACAkG,QAAA,OACAlE,KAAA,GACAkB,MAAA,EACA6C,UAAA,EACAI,KAAA,EACAlD,KAAA,GACA7B,OAAA,CACAC,QAAA,GACA+E,QAAA,EACA3E,SAAA,GAEAnD,SAAA,EACA+H,IAAA,WACAxE,MAAA,OACAjF,KAAA,GACAyG,mBAAA,EACAyC,sBAAA,EACAxB,eAAA,EACAO,cAAA,EACAW,gBAAA,EACAhG,WAAA,GACAD,eAAA,EACAgF,YAAA,GACAO,WAAA,GACAW,aAAA,GACAC,YAAA,GACAJ,kBAAA,EAGAP,WAAA,CACAE,OAAA,GACAC,OAAA,GACAG,QAAA,IAIAL,YAAA,CACAC,OAAA,CACA,CAAAlF,UAAA,EAAAoF,QAAA,UAAAC,QAAA,YAGA7B,SAAA,CACA1B,MAAA,GACAyE,OAAA,EACA5C,OAAA,GACAG,YAAA,IAGAL,MAAA,CACA3B,MAAA,CACA,CACA9B,UAAA,EACAoF,QAAA,QACAC,QAAA,SAGApB,UAAA,CACA,CACAjE,UAAA,EACAoF,QAAA,QACAC,QAAA,UAIA3B,eAAA,QACA8C,QAAA,CACA,CACA5G,IAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,OAGAF,SAAA,CACA,CACAhC,IAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,OAEA,CACAlC,GAAA,EACAkC,MAAA,UAKA2E,UACA,KAAA1E,WAEAtB,QAAA,CACAyD,WAAAwC,GACA,KAAAA,QACApG,QAAAC,IAAA,KAAAmG,QAEA1E,YACA,KAAAX,OAAA,CACAC,QAAA,GACA+E,OAAA,IAEA,KAAAtE,WAEAS,aAAA5C,GACA,IAAAc,EAAA,KACA,GAAAd,IACA,KAAAoG,UAAApG,GAGAc,EAAAqF,sBAAA,GAEAY,SAAA/G,GAEA,GAAAA,EACA,KAAAY,QAAAZ,GAEA,KAAA4D,SAAA,CACA1B,MAAA,GACAK,KAAA,KAIA3B,QAAAZ,GACA,IAAAc,EAAA,KACAA,EAAAkG,WAAAlG,EAAA4F,IAAA,WAAA1G,GAAAiH,KAAAC,IACA,KAAAA,EAAAC,MACArG,EAAA8C,SAAAsD,EAAA7G,KACAS,EAAA4C,mBAAA,GAEA5C,EAAAK,SAAA,CACAlB,KAAA,QACAuF,QAAA0B,EAAAE,SAKAC,QAAArH,GACA,KAAAsH,SAAA,gBACAC,kBAAA,KACAC,iBAAA,KACAvH,KAAA,YAEAgH,KAAA,KACA,KAAAQ,cAAA,KAAAf,IAAA,cAAA1G,GAAAiH,KAAAC,IACA,KAAAA,EAAAC,KACA,KAAAhG,SAAA,CACAlB,KAAA,UACAuF,QAAA0B,EAAAE,MAGA,KAAAjG,SAAA,CACAlB,KAAA,QACAuF,QAAA0B,EAAAE,UAKAM,MAAA,KACA,KAAAvG,SAAA,CACAlB,KAAA,QACAuF,QAAA,aAIApC,QAAA0B,EAAA9E,GACA,KAAAsH,SAAA,iBACAC,kBAAA,KACAC,iBAAA,KACAvH,KAAA,YAEAgH,KAAA,KACA,KAAAQ,cAAA,KAAAf,IAAA,aAAA1G,GAAAiH,KAAAC,IACA,KAAAA,EAAAC,OACA,KAAAhG,SAAA,CACAlB,KAAA,UACAuF,QAAA,UAEA,KAAAnD,KAAAsF,OAAA7C,EAAA,QAIA4C,MAAA,KACA,KAAAvG,SAAA,CACAlB,KAAA,QACAuF,QAAA,aAIAjE,UACA,KAAAF,QAAAuG,GAAA,IAEAC,aACA,KAAArB,KAAA,EACA,KAAAlD,KAAA,GACA,KAAAnB,WAGAA,UACA,IAAArB,EAAA,KAEAA,EAAAnC,SAAA,EAGAoC,WAAA,KACA,IAAA+G,EAAA,CACA,CACA9H,GAAA,EACAsC,SAAA,cACArC,KAAA,OACAiC,MAAA,SACAK,KAAA,kDACAE,cAAA,kBACAE,iBAAA,MACAvF,SAAA,KACAD,MAAA,cACA0F,IAAA,EACAC,YAAA,sBACAiB,OAAA,CACA,8CACA,+CAEAG,YAAA,CACA,wCACA,sDAGA,CACAlE,GAAA,EACAsC,SAAA,cACArC,KAAA,OACAiC,MAAA,SACAK,KAAA,mCACAE,cAAA,kBACAE,iBAAA,MACAvF,SAAA,KACAD,MAAA,cACA0F,IAAA,EACAC,YAAA,sBACAiB,OAAA,CACA,+CAEAG,YAAA,CACA,0CAGA,CACAlE,GAAA,EACAsC,SAAA,cACArC,KAAA,OACAiC,MAAA,SACAK,KAAA,iCACAE,cAAA,WACAE,iBAAA,GACAvF,SAAA,KACAD,MAAA,cACA0F,IAAA,EACAC,YAAA,sBACAiB,OAAA,GACAG,YAAA,CACA,yCAGA,CACAlE,GAAA,EACAsC,SAAA,cACArC,KAAA,OACAiC,MAAA,SACAK,KAAA,6BACAE,cAAA,gBACAE,iBAAA,KACAvF,SAAA,KACAD,MAAA,cACA0F,IAAA,EACAC,YAAA,sBACAiB,OAAA,GACAG,YAAA,IAEA,CACAlE,GAAA,EACAsC,SAAA,cACArC,KAAA,OACAiC,MAAA,SACAK,KAAA,qCACAE,cAAA,WACAE,iBAAA,GACAvF,SAAA,KACAD,MAAA,cACA0F,IAAA,EACAC,YAAA,sBACAiB,OAAA,CACA,6CACA,6CACA,8CAEAG,YAAA,CACA,0CAMA6D,EAAAD,EACAhH,EAAAW,OAAAC,UACAqG,EAAAD,EAAAE,OAAA/F,GACAA,EAAAK,SAAA2F,SAAAnH,EAAAW,OAAAC,UACAO,EAAAC,MAAA+F,SAAAnH,EAAAW,OAAAC,UACAO,EAAA7E,SAAA6K,SAAAnH,EAAAW,OAAAC,UACAO,EAAA9E,MAAA8K,SAAAnH,EAAAW,OAAAC,YAIA,IAAAZ,EAAAW,OAAAK,SAAA,KAAAhB,EAAAW,OAAAK,UACAiG,IAAAC,OAAA/F,GACAA,EAAAH,SAAAhB,EAAAW,OAAAK,UAIAhB,EAAAuB,KAAA0F,EACAjH,EAAAyC,MAAAwE,EAAArI,OACAoB,EAAAnC,SAAA,GACA,MAkBA+F,WACA,IAAA5D,EAAA,KACA,KAAAoH,MAAA,YAAAC,SAAAC,IACA,IAAAA,EAiBA,SAhBA,KAAAC,YAAAvH,EAAA4F,IAAA,YAAA9C,UAAAqD,KAAAC,IACA,KAAAA,EAAAC,MACArG,EAAAK,SAAA,CACAlB,KAAA,UACAuF,QAAA0B,EAAAE,MAEA,KAAAjF,UACArB,EAAA4C,mBAAA,GAEA5C,EAAAK,SAAA,CACAlB,KAAA,QACAuF,QAAA0B,EAAAE,WASA5D,iBAAA8E,GACA,KAAAhF,KAAAgF,EAEA,KAAAnG,WAEAsB,oBAAA6E,GACA,KAAA9B,KAAA8B,EACA,KAAAnG,WAEAoC,cAAAgE,GACA,KAAAA,EAAApB,MACA,KAAAhG,SAAAqH,QAAA,QACA,KAAA5E,SAAA,KAAAkD,OAAAyB,EAAAlI,KAAAqG,KAEA,KAAAvF,SAAAsH,MAAAF,EAAAnB,MAIAtJ,UAAAiH,GACA,KAAAlF,WAAAkF,EACA,KAAAnF,eAAA,GAEA8I,aAAA3D,GACA,MAAA4D,EAAA,0BAAAC,KAAA7D,EAAA9E,MACA0I,GACA,KAAAxH,SAAAsH,MAAA,cAIAjE,SAAAO,EAAA8D,GACA,IAAA/H,EAAA,KACAA,EAAAkG,WAAA,6BAAAjC,GAAAkC,KAAAC,IACA,KAAAA,EAAAC,MACArG,EAAA8C,SAAAiF,GAAA,GAEA/H,EAAAK,SAAAqH,QAAA,UAEA1H,EAAAK,SAAAsH,MAAAvB,EAAAE,QAMAlE,gBAAA7D,GACAqB,QAAAC,IAAA,QAAAtB,GACA,KAAAuF,YAAAvF,EACA,KAAAsF,eAAA,GAIAK,SAAA8D,GACAC,OAAAC,KAAAF,EAAA,WAIA7D,aAAA6D,GACA,MAAAG,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAAN,EACAG,EAAAI,SAAAP,EAAAQ,MAAA,KAAAC,MACAL,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,QACAR,SAAAM,KAAAG,YAAAV,GACA,KAAA9H,SAAAqH,QAAA,WAIAhG,cAAAhD,GACA,MAAAoK,EAAA,CACA,iBACA,0BACA,0BACA,wBACA,uBACA,mBACA,mBAEA,OAAAA,EAAApK,IAAA,QAIAkD,cAAAlD,GACA,MAAAoK,EAAA,CACA,gBACA,yBACA,yBACA,sBACA,qBACA,gBACA,kBAEA,OAAAA,EAAApK,IAAA,QAIAuD,UAAA1D,GAGA,mBAAAA,EAAAoD,eAAA,aAAApD,EAAAoD,eAIAO,YAAA3D,GACAqB,QAAAC,IAAA,QAAAtB,GACA,KAAA8F,WAAA9F,EAGA,KAAA+F,WAAA,CACAE,OAAA,GACAC,OAAA,GACAG,QAAA,IAGA,KAAAR,cAAA,GAIAU,eACA,KAAAsC,MAAA9C,WAAA+C,SAAAC,IACAA,IACA,KAAAzC,kBAAA,EAGA5E,WAAA,KACA,MAAAuE,EAAA,KAAAF,WAAAE,OACAuE,EAAA,KAAA1E,WAGAL,EAAA,KAAAzC,KAAAyH,UAAA7H,KAAAjC,KAAA6J,EAAA7J,KACA,IAAA8E,IACA,aAAAQ,EAEA,KAAAjD,KAAAyC,GAAA,KAAAiF,oBAAA,KAAA1H,KAAAyC,KAGA,KAAAzC,KAAAyC,GAAArC,cAAA,WACA,KAAAJ,KAAAyC,GAAAnC,iBAAA,KAIA,KAAAgD,kBAAA,EACA,KAAAT,cAAA,EACA,KAAA/D,SAAAqH,QAAA,YACA,SAMAuB,oBAAAC,GACA,MAAAC,EAAA,CACA,4BACA,kCACA,gCACA,6BACA,yBAGAC,EAAA,CACA,sBACA,sBACA,mBACA,qBACA,aAGAC,EAAAF,EAAAD,EAAAvH,eACA,UACAuH,EACAvH,cAAA0H,EACAxH,iBAAAuH,EAAAC,KAKAlH,mBAAA5D,GACAqB,QAAAC,IAAA,UAAAtB,GACA,KAAAyG,aAAAzG,EAGA,KAAA0G,YAAA,KAAAqE,oBAAA/K,GACA,KAAAwG,gBAAA,GAIAuE,oBAAA/K,GACA,MAAAgL,EAAA,CACA,CACAnI,MAAA,OACAgE,SAAA,OACA1G,OAAA,YACAyG,KAAA5G,EAAAyD,YACA4C,QAAA,cAEA,CACAxD,MAAA,QACAgE,SAAA,MACA1G,OAAA,cAAAH,EAAAoD,cAAA,UACA,oBAAApD,EAAAoD,cAAA,sBACAwD,KAAA,cAAA5G,EAAAoD,cAAA,yBACAiD,QAAA,cAAArG,EAAAoD,cAAA,iBAEA,CACAP,MAAA,QACAgE,SAAA,MACA1G,OAAA,gCAAAyI,SAAA5I,EAAAoD,eAAA,UACA,oBAAApD,EAAAoD,cAAA,sBACAwD,KAAA,gCAAAgC,SAAA5I,EAAAoD,eAAA,yBACAiD,QAAA,gCAAAuC,SAAA5I,EAAAoD,eAAA,eAEA,CACAP,MAAA,OACAgE,SAAA,KACA1G,OAAA,kDAAAyI,SAAA5I,EAAAoD,eAAA,UACA,kBAAApD,EAAAoD,cAAA,sBACAwD,KAAA,kDAAAgC,SAAA5I,EAAAoD,eAAA,yBACAiD,QAAA,kDAAAuC,SAAA5I,EAAAoD,eAAA,eAEA,CACAP,MAAA,OACAgE,SAAA,QACA1G,OAAA,kEAAAyI,SAAA5I,EAAAoD,eAAA,UACA,iBAAApD,EAAAoD,cAAA,sBACAwD,KAAA,kEAAAgC,SAAA5I,EAAAoD,eAAA,yBACAiD,QAAA,kEAAAuC,SAAA5I,EAAAoD,eAAA,cAgBA,MAXA,aAAApD,EAAAoD,eACA4H,EAAAC,KAAA,CACApI,MAAA,QACAgE,SAAA,OACA1G,OAAA,YACAyG,KAAA,sBACAP,QAAA,GACAH,OAAA,mBAIA8E,GAIAlH,eAAA9D,GACA,KAAAiI,SAAA,uBACAC,kBAAA,KACAC,iBAAA,KACAvH,KAAA,YACAgH,KAAA,KAEAlG,WAAA,KACA,KAAAI,SAAAqH,QAAA,eAGA,MAAA1D,EAAA,KAAAzC,KAAAyH,UAAA7H,KAAAjC,KAAAX,EAAAW,KACA,IAAA8E,IACA,KAAAzC,KAAAyC,GAAArC,cAAA,QACA,KAAAJ,KAAAyC,GAAAnC,iBAAA,SAEA,OACA+E,MAAA,KACA,KAAAvG,SAAAlE,KAAA,cCjrC6W,I,wBCQzWmE,EAAY,eACd,EACA7E,EACAuD,GACA,EACA,KACA,WACA,MAIa,aAAAsB,E", "file": "js/chunk-73ef59f7.3fa288b6.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shenhe.vue?vue&type=style&index=0&id=6377a706&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"user-detail-container\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-user\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"客户基本信息\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"公司名称\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.company || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"手机号\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.phone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"客户姓名\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.nickname || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系人\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkman || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"联系方式\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.linkphone || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"用户来源\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.yuangong_id || '未填写'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"开始时间\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.start_time || '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"会员年限\")]),_c('div',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.info.year ? _vm.info.year + '年' : '未填写'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"头像\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.headimg && _vm.info.headimg !== '')?_c('el-avatar',{staticStyle:{\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.headimg,\"size\":50},nativeOn:{\"click\":function($event){return _vm.showImage(_vm.info.headimg)}}}):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"info-item\"},[_c('div',{staticClass:\"info-label\"},[_vm._v(\"营业执照\")]),_c('div',{staticClass:\"info-value\"},[(_vm.info.license && _vm.info.license !== '')?_c('el-image',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\",\"cursor\":\"pointer\"},attrs:{\"src\":_vm.info.license,\"fit\":\"cover\"},on:{\"click\":function($event){return _vm.showImage(_vm.info.license)}}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])]):_c('span',{staticClass:\"no-data\"},[_vm._v(\"未上传\")])],1)])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-s-custom\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"服务团队\")])]),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"调解员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.tiaojie_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"法务专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.fawu_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"立案专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.lian_name || '未分配'))])])])],1),_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"合同专员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.htsczy_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"律师\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ls_name || '未分配'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"team-item\"},[_c('div',{staticClass:\"team-role\"},[_vm._v(\"业务员\")]),_c('div',{staticClass:\"team-name\"},[_vm._v(_vm._s(_vm.info.ywy_name || '未分配'))])])])],1)],1),_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"hover\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',{staticClass:\"card-title\"},[_vm._v(\"债务人信息\")])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.info.debts,\"size\":\"medium\",\"stripe\":\"\",\"header-cell-style\":{background:'#f5f7fa',color:'#606266'}}},[_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"债务人姓名\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(_vm._s(scope.row.name))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"tel\",\"label\":\"债务人电话\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"phone-number\"},[_vm._v(_vm._s(scope.row.tel))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"债务金额\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticClass:\"money-amount\"},[_vm._v(\"¥\"+_vm._s(scope.row.money))])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"状态\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.status === '已完成' ? 'success' : 'warning',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.status)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDebtDetail(scope.row)}}},[_c('i',{staticClass:\"el-icon-view\"}),_vm._v(\" 详情 \")])]}}])})],1),(!_vm.info.debts || _vm.info.debts.length === 0)?_c('div',{staticClass:\"empty-data\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('p',[_vm._v(\"暂无债务人信息\")])]):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"user-detail-container\">\r\n    <!-- 客户基本信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-user\"></i>\r\n        <span class=\"card-title\">客户基本信息</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">公司名称</div>\r\n            <div class=\"info-value\">{{ info.company || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">手机号</div>\r\n            <div class=\"info-value\">{{ info.phone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">客户姓名</div>\r\n            <div class=\"info-value\">{{ info.nickname || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系人</div>\r\n            <div class=\"info-value\">{{ info.linkman || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">联系方式</div>\r\n            <div class=\"info-value\">{{ info.linkphone || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">用户来源</div>\r\n            <div class=\"info-value\">{{ info.yuangong_id || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">开始时间</div>\r\n            <div class=\"info-value\">{{ info.start_time || '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">会员年限</div>\r\n            <div class=\"info-value\">{{ info.year ? info.year + '年' : '未填写' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">头像</div>\r\n            <div class=\"info-value\">\r\n              <el-avatar\r\n                v-if=\"info.headimg && info.headimg !== ''\"\r\n                :src=\"info.headimg\"\r\n                :size=\"50\"\r\n                @click.native=\"showImage(info.headimg)\"\r\n                style=\"cursor: pointer;\">\r\n              </el-avatar>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <div class=\"info-item\">\r\n            <div class=\"info-label\">营业执照</div>\r\n            <div class=\"info-value\">\r\n              <el-image\r\n                v-if=\"info.license && info.license !== ''\"\r\n                :src=\"info.license\"\r\n                style=\"width: 100px; height: 100px; cursor: pointer;\"\r\n                fit=\"cover\"\r\n                @click=\"showImage(info.license)\">\r\n                <div slot=\"error\" class=\"image-slot\">\r\n                  <i class=\"el-icon-picture-outline\"></i>\r\n                </div>\r\n              </el-image>\r\n              <span v-else class=\"no-data\">未上传</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 服务团队信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-s-custom\"></i>\r\n        <span class=\"card-title\">服务团队</span>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">调解员</div>\r\n            <div class=\"team-name\">{{ info.tiaojie_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">法务专员</div>\r\n            <div class=\"team-name\">{{ info.fawu_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">立案专员</div>\r\n            <div class=\"team-name\">{{ info.lian_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">合同专员</div>\r\n            <div class=\"team-name\">{{ info.htsczy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">律师</div>\r\n            <div class=\"team-name\">{{ info.ls_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"team-item\">\r\n            <div class=\"team-role\">业务员</div>\r\n            <div class=\"team-name\">{{ info.ywy_name || '未分配' }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 债务人信息卡片 -->\r\n    <el-card class=\"info-card\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <i class=\"el-icon-money\"></i>\r\n        <span class=\"card-title\">债务人信息</span>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"info.debts\"\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n        size=\"medium\"\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n      >\r\n        <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"primary\" size=\"small\">{{ scope.row.name }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"phone-number\">{{ scope.row.tel }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span class=\"money-amount\">¥{{ scope.row.money }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.status === '已完成' ? 'success' : 'warning'\"\r\n              size=\"small\">\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"viewDebtDetail(scope.row)\">\r\n              <i class=\"el-icon-view\"></i> 详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div v-if=\"!info.debts || info.debts.length === 0\" class=\"empty-data\">\r\n        <i class=\"el-icon-document\"></i>\r\n        <p>暂无债务人信息</p>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'UserDetails',\r\n    props: {\r\n      id: {\r\n        type: [String, Number],\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: {}, // 用于存储接口返回的数据\r\n          loading: false,\r\n          dialogVisible: false,\r\n          show_image: \"\"\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              if (newId && newId != 0) {\r\n                  console.log('UserDetails 接收到 ID:', newId);\r\n                  this.getInfo(newId);\r\n              }\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        console.log('正在获取用户信息，ID:', id);\r\n        _this.loading = true;\r\n\r\n        // 使用测试数据，因为API可能不可用\r\n        setTimeout(() => {\r\n          const testUserData = {\r\n            id: id,\r\n            company: \"测试公司有限公司\",\r\n            phone: \"13800138001\",\r\n            nickname: \"张三\",\r\n            linkman: \"李四\",\r\n            headimg: \"\",\r\n            yuangong_id: \"微信小程序\",\r\n            linkphone: \"***********\",\r\n            tiaojie_name: \"王调解员\",\r\n            fawu_name: \"赵法务\",\r\n            lian_name: \"钱立案员\",\r\n            htsczy_name: \"孙合同员\",\r\n            ls_name: \"周律师\",\r\n            ywy_name: \"吴业务员\",\r\n            license: \"\",\r\n            start_time: \"2024-01-01\",\r\n            year: 1,\r\n            debts: [\r\n              {\r\n                name: \"债务人A\",\r\n                tel: \"13900139001\",\r\n                money: \"50000\",\r\n                status: \"处理中\"\r\n              },\r\n              {\r\n                name: \"债务人B\",\r\n                tel: \"13900139002\",\r\n                money: \"30000\",\r\n                status: \"已完成\"\r\n              }\r\n            ]\r\n          };\r\n\r\n          _this.info = testUserData;\r\n          _this.loading = false;\r\n          console.log('用户数据加载完成:', testUserData);\r\n        }, 500);\r\n\r\n        // 原始API调用（注释掉）\r\n        /*\r\n        _this.getRequest(\"/user/read?id=\" + id).then((resp) => {\r\n          console.log('API响应:', resp);\r\n          if (resp && resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            console.error('获取用户信息失败:', resp);\r\n          }\r\n          _this.loading = false;\r\n        }).catch(error => {\r\n          console.error('API请求错误:', error);\r\n          _this.loading = false;\r\n        });\r\n        */\r\n      },\r\n\r\n      showImage(imageUrl) {\r\n        this.show_image = imageUrl;\r\n        this.dialogVisible = true;\r\n      },\r\n\r\n      viewDebtDetail(debt) {\r\n        console.log('查看债务人详情:', debt);\r\n        // 这里可以添加跳转到债务人详情页面的逻辑\r\n        this.$message.info('债务人详情功能待开发');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.info-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.card-title {\r\n  color: #303133;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  border: 1px solid #ebeef5;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.info-label {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  word-break: break-all;\r\n}\r\n\r\n.no-data {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.team-item {\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.team-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.team-role {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  margin-bottom: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.team-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n.money-amount {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-data i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-data p {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.el-table th {\r\n  background-color: #f5f7fa !important;\r\n  color: #606266 !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.el-table td {\r\n  border-bottom: 1px solid #f0f2f5;\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .info-item {\r\n    margin-bottom: 15px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .team-item {\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UserDetail.vue?vue&type=template&id=4468717a&scoped=true\"\nimport script from \"./UserDetail.vue?vue&type=script&lang=js\"\nexport * from \"./UserDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./UserDetail.vue?vue&type=style&index=0&id=4468717a&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4468717a\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"contract-review-container\"},[_c('div',{staticClass:\"page-header\"},[_c('h1',{staticClass:\"page-title\"},[_c('i',{staticClass:\"el-icon-document-checked\"}),_vm._v(\" \"+_vm._s(this.$router.currentRoute.name)+\" \")]),_c('el-button',{staticClass:\"refresh-btn\",attrs:{\"type\":\"text\"},on:{\"click\":_vm.refulsh}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\" 刷新 \")])],1),_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"search-form\"},[_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"关键词搜索\")]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":\"请输入工单号/用户名/合同标题\",\"clearable\":\"\"},model:{value:(_vm.search.keyword),callback:function ($$v) {_vm.$set(_vm.search, \"keyword\", $$v)},expression:\"search.keyword\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"prefix\"},slot:\"prefix\"})])],1),_c('div',{staticClass:\"search-item\"},[_c('label',{staticClass:\"search-label\"},[_vm._v(\"审核状态\")]),_c('el-select',{staticClass:\"search-select\",attrs:{\"placeholder\":\"请选择状态\",\"clearable\":\"\"},model:{value:(_vm.search.is_deal),callback:function ($$v) {_vm.$set(_vm.search, \"is_deal\", $$v)},expression:\"search.is_deal\"}},_vm._l((_vm.options1),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.title,\"value\":item.id}})}),1)],1),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":function($event){return _vm.getData()}}},[_vm._v(\" 搜索 \")]),_c('el-button',{attrs:{\"icon\":\"el-icon-refresh-left\"},on:{\"click\":function($event){return _vm.clearData()}}},[_vm._v(\" 重置 \")])],1)])]),_c('div',{staticClass:\"table-section\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"data-table\",attrs:{\"data\":_vm.list,\"stripe\":\"\",\"border\":\"\",\"empty-text\":\"暂无合同审核数据\"}},[_c('el-table-column',{attrs:{\"prop\":\"order_sn\",\"label\":\"工单号\",\"width\":\"120\",\"show-overflow-tooltip\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"order-sn\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(_vm._s(scope.row.order_sn))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"工单类型\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.type === '合同审核' ? 'warning' : 'info',\"size\":\"mini\"}},[_vm._v(\" \"+_vm._s(scope.row.type)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"title\",\"label\":\"合同标题\",\"min-width\":\"150\",\"show-overflow-tooltip\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"contract-title\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_c('span',[_vm._v(_vm._s(scope.row.title))])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"desc\",\"label\":\"审核要求\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"review-desc\"},[_vm._v(\" \"+_vm._s(scope.row.desc)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"review_status\",\"label\":\"审核状态\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getStatusType(scope.row.review_status),\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(scope.row.review_status))+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"current_reviewer\",\"label\":\"当前审核人\",\"width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.current_reviewer)?_c('span',[_vm._v(_vm._s(scope.row.current_reviewer))]):_c('span',{staticClass:\"text-muted\"},[_vm._v(\"-\")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"nickname\",\"label\":\"用户名\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{staticClass:\"user-link\",attrs:{\"type\":\"primary\",\"underline\":false},on:{\"click\":function($event){return _vm.viewUserData(scope.row.uid)}}},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" \"+_vm._s(scope.row.nickname)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"phone\",\"label\":\"用户手机\",\"width\":\"130\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{staticClass:\"user-link\",attrs:{\"type\":\"primary\",\"underline\":false},on:{\"click\":function($event){return _vm.viewUserData(scope.row.uid)}}},[_c('i',{staticClass:\"el-icon-phone\"}),_vm._v(\" \"+_vm._s(scope.row.phone)+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"create_time\",\"label\":\"提交时间\",\"width\":\"160\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"time-info\"},[_c('i',{staticClass:\"el-icon-time\"}),_c('span',[_vm._v(_vm._s(scope.row.create_time))])])]}}])}),_c('el-table-column',{attrs:{\"fixed\":\"right\",\"label\":\"操作\",\"width\":\"220\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"action-buttons\"},[(_vm.canReview(scope.row))?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"primary\",\"size\":\"mini\",\"icon\":\"el-icon-edit-outline\"},on:{\"click\":function($event){return _vm.startReview(scope.row)}}},[_vm._v(\" 审核 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"info\",\"size\":\"mini\",\"icon\":\"el-icon-view\"},on:{\"click\":function($event){return _vm.viewReviewProgress(scope.row)}}},[_vm._v(\" 进度 \")]),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"icon\":\"el-icon-document\"},on:{\"click\":function($event){return _vm.previewContract(scope.row)}}},[_vm._v(\" 预览 \")]),(scope.row.review_status === 'approved')?_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"warning\",\"size\":\"mini\",\"icon\":\"el-icon-folder-add\"},on:{\"click\":function($event){return _vm.submitToFiling(scope.row)}}},[_vm._v(\" 立案 \")]):_vm._e(),_c('el-button',{staticClass:\"action-btn\",attrs:{\"type\":\"danger\",\"size\":\"mini\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.delData(scope.$index, scope.row.id)}}},[_vm._v(\" 取消 \")])],1)]}}])})],1),_c('div',{staticClass:\"pagination-wrapper\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 50, 100, 200],\"page-size\":_vm.size,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total,\"background\":\"\"},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.title + '内容',\"visible\":_vm.dialogFormVisible,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogFormVisible=$event}}},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"合同标题\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\"},model:{value:(_vm.ruleForm.title),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"title\", $$v)},expression:\"ruleForm.title\"}})],1),_c('el-form-item',{attrs:{\"label\":\"合同内容\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"readonly\":\"\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.desc),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"desc\", $$v)},expression:\"ruleForm.desc\"}})],1),(_vm.ruleForm.images[0])?_c('el-form-item',{attrs:{\"label\":\"合同图片\",\"label-width\":_vm.formLabelWidth}},[_c('div',{staticStyle:{\"width\":\"100%\",\"display\":\"table-cell\"}},_vm._l((_vm.ruleForm.images),function(item2,index2){return _c('div',{key:index2,staticClass:\"image-list\",staticStyle:{\"float\":\"left\",\"margin-left\":\"2px\"}},[_c('img',{staticStyle:{\"width\":\"100px\",\"height\":\"100px\"},attrs:{\"src\":item2,\"mode\":\"aspectFit\"},on:{\"click\":function($event){return _vm.showImage(item2)}}})])}),0)]):_vm._e(),(_vm.ruleForm.attach_path[0])?_c('el-form-item',{attrs:{\"label\":\"合同文件\",\"label-width\":_vm.formLabelWidth}},[_c('div',{staticStyle:{\"width\":\"100%\",\"display\":\"table-cell\",\"line-height\":\"20px\"}},_vm._l((_vm.ruleForm.attach_path),function(item3,index3){return _c('div',{key:index3},[(item3)?_c('div',[_c('div',[_vm._v(\"文件\"+_vm._s(index3 +1)),_c('a',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"href\":item3,\"target\":\"_blank\"}},[_vm._v(\"查看\")]),_c('a',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"href\":item3}},[_vm._v(\"下载\")])]),_c('br')]):_vm._e()])}),0)]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"制作状态\",\"label-width\":_vm.formLabelWidth}},[_c('div',[_c('el-radio',{attrs:{\"label\":2},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"已完成\")]),_c('el-radio',{attrs:{\"label\":1},model:{value:(_vm.ruleForm.is_deal),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"is_deal\", $$v)},expression:\"ruleForm.is_deal\"}},[_vm._v(\"处理中\")])],1)]),(_vm.ruleForm.is_deal == 2)?_c('el-form-item',{attrs:{\"label\":\"请上传文件\",\"label-width\":_vm.formLabelWidth,\"prop\":\"file_path\"}},[_c('el-input',{staticClass:\"el_input\",attrs:{\"disabled\":true},model:{value:(_vm.ruleForm.file_path),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"file_path\", $$v)},expression:\"ruleForm.file_path\"}}),_c('el-button-group',[_c('el-button',{on:{\"click\":function($event){return _vm.changeFile('file_path')}}},[_c('el-upload',{attrs:{\"action\":\"/admin/Upload/uploadFile\",\"show-file-list\":false,\"on-success\":_vm.handleSuccess}},[_vm._v(\" 上传 \")])],1),(_vm.ruleForm.file_path)?_c('el-button',{attrs:{\"type\":\"danger\"},on:{\"click\":function($event){return _vm.delImage(_vm.ruleForm.file_path, 'file_path')}}},[_vm._v(\"删除\")]):_vm._e()],1)],1):_vm._e(),(_vm.ruleForm.is_deal == 2 && _vm.ruleForm.type != 2)?_c('el-form-item',{attrs:{\"label\":\"内容回复\",\"label-width\":_vm.formLabelWidth}},[_c('el-input',{attrs:{\"autocomplete\":\"off\",\"type\":\"textarea\",\"rows\":4},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1):_vm._e()],1),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogFormVisible = false}}},[_vm._v(\"取 消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveData()}}},[_vm._v(\"确 定\")])],1)],1),_c('el-dialog',{attrs:{\"title\":\"图片查看\",\"visible\":_vm.dialogVisible,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-image',{attrs:{\"src\":_vm.show_image}})],1),_c('el-dialog',{staticClass:\"contract-preview-dialog\",attrs:{\"title\":\"合同预览\",\"visible\":_vm.dialogPreview,\"close-on-click-modal\":false,\"width\":\"80%\"},on:{\"update:visible\":function($event){_vm.dialogPreview=$event}}},[_c('div',{staticClass:\"preview-content\"},[_c('div',{staticClass:\"preview-header\"},[_c('h3',[_vm._v(_vm._s(_vm.previewData.title))]),_c('div',{staticClass:\"preview-meta\"},[_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 工单号：\"+_vm._s(_vm.previewData.order_sn)+\" \")]),_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 提交人：\"+_vm._s(_vm.previewData.nickname)+\" \")]),_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-time\"}),_vm._v(\" 提交时间：\"+_vm._s(_vm.previewData.create_time)+\" \")])])]),_c('div',{staticClass:\"preview-body\"},[_c('div',{staticClass:\"section\"},[_c('h4',[_vm._v(\"审核要求\")]),_c('p',[_vm._v(_vm._s(_vm.previewData.desc))])]),(_vm.previewData.images && _vm.previewData.images.length)?_c('div',{staticClass:\"section\"},[_c('h4',[_vm._v(\"合同图片\")]),_c('div',{staticClass:\"image-gallery\"},_vm._l((_vm.previewData.images),function(image,index){return _c('div',{key:index,staticClass:\"image-item\",on:{\"click\":function($event){return _vm.showImage(image)}}},[_c('img',{attrs:{\"src\":image,\"alt\":\"合同图片\"}})])}),0)]):_vm._e(),(_vm.previewData.attach_path && _vm.previewData.attach_path.length)?_c('div',{staticClass:\"section\"},[_c('h4',[_vm._v(\"合同文件\")]),_c('div',{staticClass:\"file-list\"},_vm._l((_vm.previewData.attach_path),function(file,index){return (file)?_c('div',{key:index,staticClass:\"file-item\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(\"文件\"+_vm._s(index + 1))]),_c('div',{staticClass:\"file-actions\"},[_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":function($event){return _vm.viewFile(file)}}},[_vm._v(\"查看\")]),_c('el-button',{attrs:{\"type\":\"text\"},on:{\"click\":function($event){return _vm.downloadFile(file)}}},[_vm._v(\"下载\")])],1)]):_vm._e()}),0)]):_vm._e()])])]),_c('el-dialog',{staticClass:\"review-dialog\",attrs:{\"title\":\"合同审核\",\"visible\":_vm.dialogReview,\"close-on-click-modal\":false,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogReview=$event}}},[_c('div',{staticClass:\"review-content\"},[_c('div',{staticClass:\"review-header\"},[_c('h3',[_vm._v(_vm._s(_vm.reviewData.title))]),_c('div',{staticClass:\"review-meta\"},[_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\" 工单号：\"+_vm._s(_vm.reviewData.order_sn)+\" \")]),_c('span',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-user\"}),_vm._v(\" 提交人：\"+_vm._s(_vm.reviewData.nickname)+\" \")])])]),_c('div',{staticClass:\"review-form\"},[_c('el-form',{ref:\"reviewForm\",attrs:{\"model\":_vm.reviewForm,\"rules\":_vm.reviewRules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"审核结果\",\"prop\":\"result\"}},[_c('el-radio-group',{model:{value:(_vm.reviewForm.result),callback:function ($$v) {_vm.$set(_vm.reviewForm, \"result\", $$v)},expression:\"reviewForm.result\"}},[_c('el-radio',{attrs:{\"label\":\"approved\"}},[_vm._v(\"通过\")]),_c('el-radio',{attrs:{\"label\":\"rejected\"}},[_vm._v(\"不通过\")])],1)],1),(_vm.reviewForm.result === 'rejected')?_c('el-form-item',{attrs:{\"label\":\"不通过理由\",\"prop\":\"reason\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择不通过理由\",\"clearable\":\"\"},model:{value:(_vm.reviewForm.reason),callback:function ($$v) {_vm.$set(_vm.reviewForm, \"reason\", $$v)},expression:\"reviewForm.reason\"}},[_c('el-option',{attrs:{\"label\":\"合同条款不完整\",\"value\":\"incomplete_terms\"}}),_c('el-option',{attrs:{\"label\":\"法律条款有误\",\"value\":\"legal_error\"}}),_c('el-option',{attrs:{\"label\":\"格式不规范\",\"value\":\"format_error\"}}),_c('el-option',{attrs:{\"label\":\"内容与需求不符\",\"value\":\"content_mismatch\"}}),_c('el-option',{attrs:{\"label\":\"缺少必要附件\",\"value\":\"missing_attachments\"}}),_c('el-option',{attrs:{\"label\":\"其他问题\",\"value\":\"other\"}})],1)],1):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"审核意见\",\"prop\":\"comment\",\"rules\":_vm.reviewForm.result === 'rejected' ? [{ required: true, message: '请填写审核意见', trigger: 'blur' }] : []}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":4,\"placeholder\":\"请填写详细的审核意见...\"},model:{value:(_vm.reviewForm.comment),callback:function ($$v) {_vm.$set(_vm.reviewForm, \"comment\", $$v)},expression:\"reviewForm.comment\"}})],1)],1)],1)]),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.dialogReview = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.reviewSubmitting},on:{\"click\":_vm.submitReview}},[_vm._v(\" 提交审核 \")])],1)]),_c('el-drawer',{staticClass:\"progress-drawer\",attrs:{\"title\":\"审核进度\",\"visible\":_vm.dialogProgress,\"direction\":\"rtl\",\"size\":\"50%\",\"close-on-press-escape\":true,\"modal-append-to-body\":false},on:{\"update:visible\":function($event){_vm.dialogProgress=$event}}},[_c('div',{staticClass:\"progress-drawer-content\"},[_c('div',{staticClass:\"progress-header\"},[_c('h3',[_vm._v(_vm._s(_vm.progressData.title))]),_c('div',{staticClass:\"progress-meta\"},[_c('div',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-document\"}),_c('span',[_vm._v(\"工单号：\"+_vm._s(_vm.progressData.order_sn))])]),_c('div',{staticClass:\"meta-item\"},[_c('i',{staticClass:\"el-icon-info\"}),_c('span',[_vm._v(\"当前状态：\"+_vm._s(_vm.getStatusText(_vm.progressData.review_status)))])])])]),_c('div',{staticClass:\"progress-timeline\"},[_c('el-timeline',_vm._l((_vm.reviewSteps),function(step,index){return _c('el-timeline-item',{key:index,attrs:{\"timestamp\":step.time,\"type\":step.status === 'completed' ? 'success' : step.status === 'current' ? 'primary' : 'info',\"icon\":step.status === 'completed' ? 'el-icon-check' : step.status === 'current' ? 'el-icon-loading' : 'el-icon-time',\"placement\":\"top\"}},[_c('div',{staticClass:\"timeline-content\"},[_c('h4',[_vm._v(_vm._s(step.title))]),_c('p',[_vm._v(_vm._s(step.reviewer))]),(step.comment)?_c('div',{staticClass:\"step-comment\"},[_c('strong',[_vm._v(\"审核意见：\")]),_vm._v(_vm._s(step.comment)+\" \")]):_vm._e(),(step.reason)?_c('div',{staticClass:\"step-reason\"},[_c('strong',[_vm._v(\"不通过理由：\")]),_vm._v(_vm._s(step.reason)+\" \")]):_vm._e()])])}),1)],1)])]),_c('el-drawer',{staticClass:\"user-detail-drawer\",attrs:{\"title\":\"用户详情\",\"visible\":_vm.dialogViewUserDetail,\"direction\":\"rtl\",\"size\":\"60%\",\"close-on-press-escape\":true,\"modal-append-to-body\":false},on:{\"update:visible\":function($event){_vm.dialogViewUserDetail=$event}}},[_c('div',{staticClass:\"drawer-content\"},[_c('user-details',{attrs:{\"id\":_vm.currentId}})],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"contract-review-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-header\">\r\n      <h1 class=\"page-title\">\r\n        <i class=\"el-icon-document-checked\"></i>\r\n        {{ this.$router.currentRoute.name }}\r\n      </h1>\r\n      <el-button type=\"text\" @click=\"refulsh\" class=\"refresh-btn\">\r\n        <i class=\"el-icon-refresh\"></i> 刷新\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-form\">\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">关键词搜索</label>\r\n          <el-input\r\n            placeholder=\"请输入工单号/用户名/合同标题\"\r\n            v-model=\"search.keyword\"\r\n            class=\"search-input\"\r\n            clearable\r\n          >\r\n            <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n          </el-input>\r\n        </div>\r\n\r\n        <div class=\"search-item\">\r\n          <label class=\"search-label\">审核状态</label>\r\n          <el-select\r\n            v-model=\"search.is_deal\"\r\n            placeholder=\"请选择状态\"\r\n            class=\"search-select\"\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n\r\n        <div class=\"search-actions\">\r\n          <el-button type=\"primary\" @click=\"getData()\" icon=\"el-icon-search\">\r\n            搜索\r\n          </el-button>\r\n          <el-button @click=\"clearData()\" icon=\"el-icon-refresh-left\">\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        class=\"data-table\"\r\n        stripe\r\n        border\r\n        empty-text=\"暂无合同审核数据\"\r\n      >\r\n        <el-table-column prop=\"order_sn\" label=\"工单号\" width=\"120\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"order-sn\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ scope.row.order_sn }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"type\" label=\"工单类型\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"scope.row.type === '合同审核' ? 'warning' : 'info'\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.type }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"title\" label=\"合同标题\" min-width=\"150\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"contract-title\">\r\n              <i class=\"el-icon-document-copy\"></i>\r\n              <span>{{ scope.row.title }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"desc\" label=\"审核要求\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"review-desc\">\r\n              {{ scope.row.desc }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"review_status\" label=\"审核状态\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getStatusType(scope.row.review_status)\"\r\n              size=\"small\"\r\n            >\r\n              {{ getStatusText(scope.row.review_status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"current_reviewer\" label=\"当前审核人\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.current_reviewer\">{{ scope.row.current_reviewer }}</span>\r\n            <span v-else class=\"text-muted\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"nickname\" label=\"用户名\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-user\"></i>\r\n              {{ scope.row.nickname }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"phone\" label=\"用户手机\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link\r\n              type=\"primary\"\r\n              @click=\"viewUserData(scope.row.uid)\"\r\n              :underline=\"false\"\r\n              class=\"user-link\"\r\n            >\r\n              <i class=\"el-icon-phone\"></i>\r\n              {{ scope.row.phone }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"create_time\" label=\"提交时间\" width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"time-info\">\r\n              <i class=\"el-icon-time\"></i>\r\n              <span>{{ scope.row.create_time }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"220\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <!-- 审核按钮 -->\r\n              <el-button\r\n                v-if=\"canReview(scope.row)\"\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"startReview(scope.row)\"\r\n                icon=\"el-icon-edit-outline\"\r\n                class=\"action-btn\"\r\n              >\r\n                审核\r\n              </el-button>\r\n\r\n              <!-- 查看审核进度 -->\r\n              <el-button\r\n                type=\"info\"\r\n                size=\"mini\"\r\n                @click=\"viewReviewProgress(scope.row)\"\r\n                icon=\"el-icon-view\"\r\n                class=\"action-btn\"\r\n              >\r\n                进度\r\n              </el-button>\r\n\r\n              <!-- 预览合同 -->\r\n              <el-button\r\n                type=\"success\"\r\n                size=\"mini\"\r\n                @click=\"previewContract(scope.row)\"\r\n                icon=\"el-icon-document\"\r\n                class=\"action-btn\"\r\n              >\r\n                预览\r\n              </el-button>\r\n\r\n              <!-- 立案按钮 -->\r\n              <el-button\r\n                v-if=\"scope.row.review_status === 'approved'\"\r\n                type=\"warning\"\r\n                size=\"mini\"\r\n                @click=\"submitToFiling(scope.row)\"\r\n                icon=\"el-icon-folder-add\"\r\n                class=\"action-btn\"\r\n              >\r\n                立案\r\n              </el-button>\r\n\r\n              <!-- 取消按钮 -->\r\n              <el-button\r\n                type=\"danger\"\r\n                size=\"mini\"\r\n                @click=\"delData(scope.$index, scope.row.id)\"\r\n                icon=\"el-icon-delete\"\r\n                class=\"action-btn\"\r\n              >\r\n                取消\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 50, 100, 200]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item label=\"合同标题\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同内容\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同图片\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.images[0]\">\r\n            <div style=\"width: 100%;display: table-cell;\">\r\n          <div style=\"float: left;margin-left:2px;\"\r\n            v-for=\"(item2, index2) in ruleForm.images\"\r\n            :key=\"index2\"\r\n            class=\"image-list\"\r\n          >\r\n            <img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />\r\n          </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"合同文件\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.attach_path[0]\">\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n          <div\r\n            v-for=\"(item3, index3) in ruleForm.attach_path\"\r\n            :key=\"index3\"\r\n          >\r\n            <div v-if=\"item3\">\r\n              <div >文件{{ index3 +1 }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\">下载</a></div><br />\r\n            </div>\r\n          </div>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2\"\r\n          label=\"请上传文件\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\r\n    <!-- 合同预览对话框 -->\r\n    <el-dialog\r\n      title=\"合同预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"contract-preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ previewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ previewData.nickname }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-time\"></i>\r\n              提交时间：{{ previewData.create_time }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div class=\"section\">\r\n            <h4>审核要求</h4>\r\n            <p>{{ previewData.desc }}</p>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.images && previewData.images.length\">\r\n            <h4>合同图片</h4>\r\n            <div class=\"image-gallery\">\r\n              <div\r\n                v-for=\"(image, index) in previewData.images\"\r\n                :key=\"index\"\r\n                class=\"image-item\"\r\n                @click=\"showImage(image)\"\r\n              >\r\n                <img :src=\"image\" alt=\"合同图片\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"section\" v-if=\"previewData.attach_path && previewData.attach_path.length\">\r\n            <h4>合同文件</h4>\r\n            <div class=\"file-list\">\r\n              <div\r\n                v-for=\"(file, index) in previewData.attach_path\"\r\n                :key=\"index\"\r\n                class=\"file-item\"\r\n                v-if=\"file\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>文件{{ index + 1 }}</span>\r\n                <div class=\"file-actions\">\r\n                  <el-button type=\"text\" @click=\"viewFile(file)\">查看</el-button>\r\n                  <el-button type=\"text\" @click=\"downloadFile(file)\">下载</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核对话框 -->\r\n    <el-dialog\r\n      title=\"合同审核\"\r\n      :visible.sync=\"dialogReview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"review-dialog\"\r\n    >\r\n      <div class=\"review-content\">\r\n        <div class=\"review-header\">\r\n          <h3>{{ reviewData.title }}</h3>\r\n          <div class=\"review-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              工单号：{{ reviewData.order_sn }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-user\"></i>\r\n              提交人：{{ reviewData.nickname }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"review-form\">\r\n          <el-form :model=\"reviewForm\" :rules=\"reviewRules\" ref=\"reviewForm\" label-width=\"100px\">\r\n            <el-form-item label=\"审核结果\" prop=\"result\">\r\n              <el-radio-group v-model=\"reviewForm.result\">\r\n                <el-radio label=\"approved\">通过</el-radio>\r\n                <el-radio label=\"rejected\">不通过</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              v-if=\"reviewForm.result === 'rejected'\"\r\n              label=\"不通过理由\"\r\n              prop=\"reason\"\r\n            >\r\n              <el-select\r\n                v-model=\"reviewForm.reason\"\r\n                placeholder=\"请选择不通过理由\"\r\n                style=\"width: 100%\"\r\n                clearable\r\n              >\r\n                <el-option label=\"合同条款不完整\" value=\"incomplete_terms\"></el-option>\r\n                <el-option label=\"法律条款有误\" value=\"legal_error\"></el-option>\r\n                <el-option label=\"格式不规范\" value=\"format_error\"></el-option>\r\n                <el-option label=\"内容与需求不符\" value=\"content_mismatch\"></el-option>\r\n                <el-option label=\"缺少必要附件\" value=\"missing_attachments\"></el-option>\r\n                <el-option label=\"其他问题\" value=\"other\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item\r\n              label=\"审核意见\"\r\n              prop=\"comment\"\r\n              :rules=\"reviewForm.result === 'rejected' ? [{ required: true, message: '请填写审核意见', trigger: 'blur' }] : []\"\r\n            >\r\n              <el-input\r\n                type=\"textarea\"\r\n                v-model=\"reviewForm.comment\"\r\n                :rows=\"4\"\r\n                placeholder=\"请填写详细的审核意见...\"\r\n              ></el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogReview = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitReview\" :loading=\"reviewSubmitting\">\r\n          提交审核\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审核进度抽屉 -->\r\n    <el-drawer\r\n      title=\"审核进度\"\r\n      :visible.sync=\"dialogProgress\"\r\n      direction=\"rtl\"\r\n      size=\"50%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"progress-drawer\"\r\n    >\r\n      <div class=\"progress-drawer-content\">\r\n        <div class=\"progress-header\">\r\n          <h3>{{ progressData.title }}</h3>\r\n          <div class=\"progress-meta\">\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>工单号：{{ progressData.order_sn }}</span>\r\n            </div>\r\n            <div class=\"meta-item\">\r\n              <i class=\"el-icon-info\"></i>\r\n              <span>当前状态：{{ getStatusText(progressData.review_status) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"progress-timeline\">\r\n          <el-timeline>\r\n            <el-timeline-item\r\n              v-for=\"(step, index) in reviewSteps\"\r\n              :key=\"index\"\r\n              :timestamp=\"step.time\"\r\n              :type=\"step.status === 'completed' ? 'success' : step.status === 'current' ? 'primary' : 'info'\"\r\n              :icon=\"step.status === 'completed' ? 'el-icon-check' : step.status === 'current' ? 'el-icon-loading' : 'el-icon-time'\"\r\n              placement=\"top\"\r\n            >\r\n              <div class=\"timeline-content\">\r\n                <h4>{{ step.title }}</h4>\r\n                <p>{{ step.reviewer }}</p>\r\n                <div v-if=\"step.comment\" class=\"step-comment\">\r\n                  <strong>审核意见：</strong>{{ step.comment }}\r\n                </div>\r\n                <div v-if=\"step.reason\" class=\"step-reason\">\r\n                  <strong>不通过理由：</strong>{{ step.reason }}\r\n                </div>\r\n              </div>\r\n            </el-timeline-item>\r\n          </el-timeline>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 用户详情抽屉 -->\r\n    <el-drawer\r\n      title=\"用户详情\"\r\n      :visible.sync=\"dialogViewUserDetail\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :close-on-press-escape=\"true\"\r\n      :modal-append-to-body=\"false\"\r\n      class=\"user-detail-drawer\"\r\n    >\r\n      <div class=\"drawer-content\">\r\n        <user-details :id=\"currentId\"></user-details>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from '/src/components/UserDetail.vue';\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      currentId:0,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        is_pay: -1,\r\n        is_deal: -1,\r\n      },\r\n      loading: true,\r\n      url: \"/shenhe/\",\r\n      title: \"合同审核\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogViewUserDetail: false,\r\n      dialogPreview: false,\r\n      dialogReview: false,\r\n      dialogProgress: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      previewData: {},\r\n      reviewData: {},\r\n      progressData: {},\r\n      reviewSteps: [],\r\n      reviewSubmitting: false,\r\n\r\n      // 审核表单\r\n      reviewForm: {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      },\r\n\r\n      // 审核表单验证规则\r\n      reviewRules: {\r\n        result: [\r\n          { required: true, message: '请选择审核结果', trigger: 'change' }\r\n        ]\r\n      },\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        images:{},\r\n        attach_path:{}\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"未支付\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已支付\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"退款\",\r\n        },\r\n      ],\r\n      options1: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 0,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"处理中\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"已处理\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        is_pay: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    viewUserData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.currentId = id;\r\n      }\r\n\r\n      _this.dialogViewUserDetail = true;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      setTimeout(() => {\r\n        let allData = [\r\n          {\r\n            id: 1,\r\n            order_sn: \"SH202403001\",\r\n            type: \"合同审核\",\r\n            title: \"劳动合同审核\",\r\n            desc: \"请帮忙审核劳动合同条款，检查是否符合劳动法规定，特别关注薪资、工作时间、福利待遇等条款的合法性\",\r\n            review_status: \"mediator_review\", // 调解员审核中\r\n            current_reviewer: \"调解员\",\r\n            nickname: \"张三\",\r\n            phone: \"13800138001\",\r\n            uid: 1,\r\n            create_time: \"2024-03-20 10:30:00\",\r\n            images: [\r\n              \"/uploads/contracts/labor_contract_page1.jpg\",\r\n              \"/uploads/contracts/labor_contract_page2.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/labor_contract.pdf\",\r\n              \"/uploads/contracts/labor_contract_supplement.docx\"\r\n            ]\r\n          },\r\n          {\r\n            id: 2,\r\n            order_sn: \"SH202403002\",\r\n            type: \"合同审核\",\r\n            title: \"租赁合同审核\",\r\n            desc: \"房屋租赁合同审核，需要检查租金条款、押金规定、违约责任等是否合理\",\r\n            review_status: \"business_review\", // 业务员审核中\r\n            current_reviewer: \"业务员\",\r\n            nickname: \"李四\",\r\n            phone: \"***********\",\r\n            uid: 2,\r\n            create_time: \"2024-03-19 14:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/lease_contract_page1.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/lease_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            order_sn: \"SH202403003\",\r\n            type: \"合同审核\",\r\n            title: \"买卖合同审核\",\r\n            desc: \"商品买卖合同审核，重点关注货物交付、付款方式、质量保证等条款\",\r\n            review_status: \"approved\", // 全部审核通过\r\n            current_reviewer: \"\",\r\n            nickname: \"王五\",\r\n            phone: \"***********\",\r\n            uid: 3,\r\n            create_time: \"2024-03-18 09:15:00\",\r\n            images: [],\r\n            attach_path: [\r\n              \"/uploads/contracts/sale_contract.pdf\"\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            order_sn: \"SH202403004\",\r\n            type: \"合同审核\",\r\n            title: \"服务合同审核\",\r\n            desc: \"咨询服务合同相关法律问题，主要涉及服务标准和验收条件\",\r\n            review_status: \"lawyer_review\", // 律师审核中\r\n            current_reviewer: \"律师\",\r\n            nickname: \"赵六\",\r\n            phone: \"13800138004\",\r\n            uid: 4,\r\n            create_time: \"2024-03-17 16:45:00\",\r\n            images: [],\r\n            attach_path: []\r\n          },\r\n          {\r\n            id: 5,\r\n            order_sn: \"SH202403005\",\r\n            type: \"合同审核\",\r\n            title: \"借款合同审核\",\r\n            desc: \"个人借款合同审核，需要确认利率、还款方式、担保条款等是否符合法律规定\",\r\n            review_status: \"rejected\", // 审核不通过\r\n            current_reviewer: \"\",\r\n            nickname: \"孙七\",\r\n            phone: \"13800138005\",\r\n            uid: 5,\r\n            create_time: \"2024-03-16 11:20:00\",\r\n            images: [\r\n              \"/uploads/contracts/loan_contract_page1.jpg\",\r\n              \"/uploads/contracts/loan_contract_page2.jpg\",\r\n              \"/uploads/contracts/loan_contract_page3.jpg\"\r\n            ],\r\n            attach_path: [\r\n              \"/uploads/contracts/loan_contract.pdf\"\r\n            ]\r\n          }\r\n        ];\r\n\r\n        // 根据搜索条件过滤数据\r\n        let filteredData = allData;\r\n        if (_this.search.keyword) {\r\n          filteredData = allData.filter(item =>\r\n            item.order_sn.includes(_this.search.keyword) ||\r\n            item.title.includes(_this.search.keyword) ||\r\n            item.nickname.includes(_this.search.keyword) ||\r\n            item.phone.includes(_this.search.keyword)\r\n          );\r\n        }\r\n\r\n        if (_this.search.is_deal !== -1 && _this.search.is_deal !== '') {\r\n          filteredData = filteredData.filter(item =>\r\n            item.is_deal == _this.search.is_deal\r\n          );\r\n        }\r\n\r\n        _this.list = filteredData;\r\n        _this.total = filteredData.length;\r\n        _this.loading = false;\r\n      }, 500);\r\n\r\n      // 原来的API调用（注释掉，使用测试数据）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 预览合同\r\n    previewContract(row) {\r\n      console.log('预览合同:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 查看文件\r\n    viewFile(fileUrl) {\r\n      window.open(fileUrl, '_blank');\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        'submitted': 'info',\r\n        'mediator_review': 'warning',\r\n        'business_review': 'warning',\r\n        'lawyer_review': 'warning',\r\n        'final_review': 'warning',\r\n        'approved': 'success',\r\n        'rejected': 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'submitted': '已提交',\r\n        'mediator_review': '调解员审核中',\r\n        'business_review': '业务员审核中',\r\n        'lawyer_review': '律师审核中',\r\n        'final_review': '最终审核中',\r\n        'approved': '审核通过',\r\n        'rejected': '审核不通过'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 判断是否可以审核\r\n    canReview(row) {\r\n      // 这里可以根据当前用户角色和审核状态判断\r\n      // 简化处理：只要不是已通过或已拒绝的都可以审核\r\n      return row.review_status !== 'approved' && row.review_status !== 'rejected';\r\n    },\r\n\r\n    // 开始审核\r\n    startReview(row) {\r\n      console.log('开始审核:', row);\r\n      this.reviewData = row;\r\n\r\n      // 重置审核表单\r\n      this.reviewForm = {\r\n        result: '',\r\n        reason: '',\r\n        comment: ''\r\n      };\r\n\r\n      this.dialogReview = true;\r\n    },\r\n\r\n    // 提交审核\r\n    submitReview() {\r\n      this.$refs.reviewForm.validate((valid) => {\r\n        if (valid) {\r\n          this.reviewSubmitting = true;\r\n\r\n          // 模拟提交审核\r\n          setTimeout(() => {\r\n            const result = this.reviewForm.result;\r\n            const currentData = this.reviewData;\r\n\r\n            // 更新列表中的数据\r\n            const index = this.list.findIndex(item => item.id === currentData.id);\r\n            if (index !== -1) {\r\n              if (result === 'approved') {\r\n                // 通过审核，进入下一个审核环节\r\n                this.list[index] = this.getNextReviewStatus(this.list[index]);\r\n              } else {\r\n                // 审核不通过\r\n                this.list[index].review_status = 'rejected';\r\n                this.list[index].current_reviewer = '';\r\n              }\r\n            }\r\n\r\n            this.reviewSubmitting = false;\r\n            this.dialogReview = false;\r\n            this.$message.success('审核提交成功！');\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取下一个审核状态\r\n    getNextReviewStatus(currentItem) {\r\n      const statusFlow = {\r\n        'submitted': 'mediator_review',\r\n        'mediator_review': 'business_review',\r\n        'business_review': 'lawyer_review',\r\n        'lawyer_review': 'final_review',\r\n        'final_review': 'approved'\r\n      };\r\n\r\n      const reviewerMap = {\r\n        'mediator_review': '调解员',\r\n        'business_review': '业务员',\r\n        'lawyer_review': '律师',\r\n        'final_review': '最终审核人',\r\n        'approved': ''\r\n      };\r\n\r\n      const nextStatus = statusFlow[currentItem.review_status];\r\n      return {\r\n        ...currentItem,\r\n        review_status: nextStatus,\r\n        current_reviewer: reviewerMap[nextStatus]\r\n      };\r\n    },\r\n\r\n    // 查看审核进度\r\n    viewReviewProgress(row) {\r\n      console.log('查看审核进度:', row);\r\n      this.progressData = row;\r\n\r\n      // 模拟审核步骤数据\r\n      this.reviewSteps = this.generateReviewSteps(row);\r\n      this.dialogProgress = true;\r\n    },\r\n\r\n    // 生成审核步骤\r\n    generateReviewSteps(row) {\r\n      const allSteps = [\r\n        {\r\n          title: '法务提交',\r\n          reviewer: '法务部门',\r\n          status: 'completed',\r\n          time: row.create_time,\r\n          comment: '合同已完成，提交审核'\r\n        },\r\n        {\r\n          title: '调解员审核',\r\n          reviewer: '调解员',\r\n          status: row.review_status === 'submitted' ? 'pending' :\r\n                  row.review_status === 'mediator_review' ? 'current' : 'completed',\r\n          time: row.review_status === 'submitted' ? '' : '2024-03-20 11:00:00',\r\n          comment: row.review_status === 'submitted' ? '' : '合同条款符合调解要求'\r\n        },\r\n        {\r\n          title: '业务员审核',\r\n          reviewer: '业务员',\r\n          status: ['submitted', 'mediator_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'business_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '2024-03-20 14:30:00',\r\n          comment: ['submitted', 'mediator_review'].includes(row.review_status) ? '' : '业务流程审核通过'\r\n        },\r\n        {\r\n          title: '律师审核',\r\n          reviewer: '律师',\r\n          status: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'lawyer_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '2024-03-20 16:00:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review'].includes(row.review_status) ? '' : '法律条款审核通过'\r\n        },\r\n        {\r\n          title: '最终审核',\r\n          reviewer: '最终审核人',\r\n          status: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? 'pending' :\r\n                  row.review_status === 'final_review' ? 'current' : 'completed',\r\n          time: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '2024-03-20 17:30:00',\r\n          comment: ['submitted', 'mediator_review', 'business_review', 'lawyer_review'].includes(row.review_status) ? '' : '最终审核通过'\r\n        }\r\n      ];\r\n\r\n      // 如果审核被拒绝，添加拒绝信息\r\n      if (row.review_status === 'rejected') {\r\n        allSteps.push({\r\n          title: '审核不通过',\r\n          reviewer: '审核人员',\r\n          status: 'completed',\r\n          time: '2024-03-20 18:00:00',\r\n          comment: '',\r\n          reason: '合同条款不完整，需要重新修改'\r\n        });\r\n      }\r\n\r\n      return allSteps;\r\n    },\r\n\r\n    // 提交立案\r\n    submitToFiling(row) {\r\n      this.$confirm('确认将此合同提交到立案部门？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 模拟提交立案\r\n        setTimeout(() => {\r\n          this.$message.success('已成功提交到立案部门！');\r\n\r\n          // 更新状态\r\n          const index = this.list.findIndex(item => item.id === row.id);\r\n          if (index !== -1) {\r\n            this.list[index].review_status = 'filed';\r\n            this.list[index].current_reviewer = '立案部门';\r\n          }\r\n        }, 500);\r\n      }).catch(() => {\r\n        this.$message.info('已取消提交');\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-review-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 0 8px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #409eff;\r\n  font-size: 26px;\r\n}\r\n\r\n.refresh-btn {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.search-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-input {\r\n  width: 280px;\r\n}\r\n\r\n.search-select {\r\n  width: 200px;\r\n}\r\n\r\n.search-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表格区域 */\r\n.table-section {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.data-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.data-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.data-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.data-table >>> .el-table__body tr:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格内容样式 */\r\n.order-sn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.order-sn i {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n}\r\n\r\n.contract-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.contract-title i {\r\n  color: #e6a23c;\r\n  font-size: 14px;\r\n}\r\n\r\n.review-desc {\r\n  color: #606266;\r\n  line-height: 1.4;\r\n}\r\n\r\n.user-link {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n}\r\n\r\n.user-link i {\r\n  font-size: 12px;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n}\r\n\r\n.time-info i {\r\n  color: #909399;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 12px;\r\n  padding: 5px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n/* 合同预览对话框样式 */\r\n.contract-preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.contract-preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #409eff;\r\n}\r\n\r\n.preview-body .section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.preview-body .section h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-body .section p {\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.image-gallery {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 16px;\r\n}\r\n\r\n.image-item {\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n  border-radius: 8px;\r\n}\r\n\r\n.file-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: white;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-item i {\r\n  color: #409eff;\r\n  font-size: 18px;\r\n}\r\n\r\n.file-item span {\r\n  flex: 1;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 用户详情抽屉样式 */\r\n.user-detail-drawer >>> .el-drawer {\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin-bottom: 0;\r\n  border-radius: 8px 0 0 0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.user-detail-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n/* 审核对话框样式 */\r\n.review-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.review-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.review-content {\r\n  padding: 24px;\r\n}\r\n\r\n.review-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.review-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.review-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.review-form {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #f093fb;\r\n}\r\n\r\n/* 审核进度抽屉样式 */\r\n.progress-drawer >>> .el-drawer {\r\n  border-radius: 0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__header {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__close-btn:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.progress-drawer >>> .el-drawer__body {\r\n  padding: 0;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.progress-drawer-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 24px;\r\n}\r\n\r\n.progress-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.progress-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 16px 0;\r\n}\r\n\r\n.progress-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.progress-meta .meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.progress-meta .meta-item i {\r\n  color: #4facfe;\r\n  font-size: 16px;\r\n}\r\n\r\n.progress-timeline {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  border-left: 4px solid #4facfe;\r\n}\r\n\r\n.timeline-content h4 {\r\n  font-size: 16px;\r\n  color: #2c3e50;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.timeline-content p {\r\n  color: #606266;\r\n  margin: 0 0 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n.step-comment,\r\n.step-reason {\r\n  background: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.step-comment {\r\n  border-left: 3px solid #67c23a;\r\n}\r\n\r\n.step-reason {\r\n  border-left: 3px solid #f56c6c;\r\n}\r\n\r\n.step-comment strong,\r\n.step-reason strong {\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-muted {\r\n  color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-review-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-input,\r\n  .search-select {\r\n    width: 100%;\r\n  }\r\n\r\n  .search-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .action-buttons {\r\n    gap: 4px;\r\n  }\r\n\r\n  .action-btn {\r\n    font-size: 11px;\r\n    padding: 4px 6px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .image-gallery {\r\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  }\r\n}\r\n\r\n/* 原有样式保持兼容 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shenhe.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shenhe.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./shenhe.vue?vue&type=template&id=6377a706&scoped=true\"\nimport script from \"./shenhe.vue?vue&type=script&lang=js\"\nexport * from \"./shenhe.vue?vue&type=script&lang=js\"\nimport style0 from \"./shenhe.vue?vue&type=style&index=0&id=6377a706&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6377a706\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}