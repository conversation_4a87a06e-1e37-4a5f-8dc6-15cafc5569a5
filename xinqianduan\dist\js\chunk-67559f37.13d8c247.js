(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67559f37"],{"02be":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"permission-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.editData(0)}}},[e._v(" 新增权限 ")]),t("el-button",{staticClass:"refresh-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.refulsh}},[e._v(" 刷新 ")])],1)])]),t("div",{staticClass:"search-section"},[t("el-card",{staticClass:"search-card",attrs:{shadow:"never"}},[t("div",{staticClass:"search-form"},[t("div",{staticClass:"search-row"},[t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("权限搜索")]),t("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入权限名称或描述",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("权限类型")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择权限类型",clearable:""},model:{value:e.search.type,callback:function(t){e.$set(e.search,"type",t)},expression:"search.type"}},[t("el-option",{attrs:{label:"菜单权限",value:"menu"}}),t("el-option",{attrs:{label:"操作权限",value:"action"}}),t("el-option",{attrs:{label:"数据权限",value:"data"}})],1)],1),t("div",{staticClass:"search-item"},[t("label",{staticClass:"search-label"},[e._v("状态")]),t("el-select",{staticClass:"search-select",attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},[t("el-option",{attrs:{label:"启用",value:1}}),t("el-option",{attrs:{label:"禁用",value:0}})],1)],1)]),t("div",{staticClass:"search-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchData}},[e._v(" 搜索 ")]),t("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:e.clearSearch}},[e._v(" 重置 ")])],1)])])],1),t("div",{staticClass:"tree-section"},[t("el-card",{staticClass:"tree-card",attrs:{shadow:"never"}},[t("div",{staticClass:"tree-header"},[t("div",{staticClass:"tree-title"},[t("i",{staticClass:"el-icon-menu"}),e._v(" 权限树形结构 ")]),t("div",{staticClass:"tree-tools"},[t("el-button-group",[t("el-button",{attrs:{type:"tree"===e.viewMode?"primary":"",icon:"el-icon-s-grid",size:"small"},on:{click:function(t){e.viewMode="tree"}}},[e._v(" 树形视图 ")]),t("el-button",{attrs:{type:"table"===e.viewMode?"primary":"",icon:"el-icon-menu",size:"small"},on:{click:function(t){e.viewMode="table"}}},[e._v(" 列表视图 ")])],1)],1)]),"tree"===e.viewMode?t("div",{staticClass:"tree-view"},[t("el-tree",{staticClass:"permission-tree",attrs:{data:e.treeData,props:e.treeProps,"default-expand-all":!0,"node-key":"id"},scopedSlots:e._u([{key:"default",fn:function({node:i,data:a}){return t("span",{staticClass:"tree-node"},[t("div",{staticClass:"node-content"},[t("div",{staticClass:"node-info"},[t("i",{class:e.getNodeIcon(a.type)}),t("span",{staticClass:"node-label"},[e._v(e._s(a.label))]),t("el-tag",{staticClass:"node-status",attrs:{type:1===a.status?"success":"danger",size:"mini"}},[e._v(" "+e._s(1===a.status?"启用":"禁用")+" ")])],1),t("div",{staticClass:"node-actions"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit",plain:""},on:{click:function(t){return e.editData(a.id)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus",plain:""},on:{click:function(t){return e.addChild(a)}}},[e._v(" 添加子权限 ")]),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",plain:""},on:{click:function(t){return e.delData(a.id)}}},[e._v(" 删除 ")])],1)])])}}],null,!1,369117358)})],1):e._e(),"table"===e.viewMode?t("div",{staticClass:"table-view"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"permission-table",attrs:{data:e.tableData,stripe:"","row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},"default-expand-all":!1}},[t("el-table-column",{attrs:{prop:"label",label:"权限名称","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"permission-name-cell",style:{paddingLeft:20*(i.row.level||0)+"px"}},[t("i",{class:e.getNodeIcon(i.row.type),staticStyle:{"margin-right":"8px"}}),t("span",{staticClass:"permission-name"},[e._v(e._s(i.row.label))])])]}}],null,!1,296214969)}),t("el-table-column",{attrs:{prop:"code",label:"权限代码",width:"180",align:"center","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"type",label:"权限类型",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-tag",{attrs:{type:e.getTypeColor(i.row.type),size:"small"}},[e._v(" "+e._s(e.getTypeLabel(i.row.type))+" ")])]}}],null,!1,417277375)}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(t){return e.changeStatus(i.row)}},model:{value:i.row.status,callback:function(t){e.$set(i.row,"status",t)},expression:"scope.row.status"}})]}}],null,!1,2880962836)}),t("el-table-column",{attrs:{prop:"sort",label:"排序",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"240",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",{staticClass:"action-buttons"},[t("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit",plain:""},on:{click:function(t){return t.stopPropagation(),e.editData(i.row.id)}}},[e._v(" 编辑 ")]),"menu"===i.row.type?t("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus",plain:""},on:{click:function(t){return t.stopPropagation(),e.addChild(i.row)}}},[e._v(" 添加子权限 ")]):e._e(),t("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",plain:""},on:{click:function(t){return t.stopPropagation(),e.delData(i.row.id)}}},[e._v(" 删除 ")])],1)]}}],null,!1,2647926959)})],1)],1):e._e()])],1),t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogFormVisible,"close-on-click-modal":!1,width:"60%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"权限名称",prop:"label"}},[t("el-input",{attrs:{placeholder:"请输入权限名称"},model:{value:e.ruleForm.label,callback:function(t){e.$set(e.ruleForm,"label",t)},expression:"ruleForm.label"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"权限代码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入权限代码"},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1)],1)],1),t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"权限类型",prop:"type"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择权限类型"},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,"type",t)},expression:"ruleForm.type"}},[t("el-option",{attrs:{label:"菜单权限",value:"menu"}}),t("el-option",{attrs:{label:"操作权限",value:"action"}}),t("el-option",{attrs:{label:"数据权限",value:"data"}})],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"父级权限"}},[t("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.parentOptions,props:e.cascaderProps,placeholder:"请选择父级权限",clearable:""},model:{value:e.ruleForm.parent_id,callback:function(t){e.$set(e.ruleForm,"parent_id",t)},expression:"ruleForm.parent_id"}})],1)],1)],1),t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"排序"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:999},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,"sort",t)},expression:"ruleForm.sort"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"状态"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}})],1)],1)],1),t("el-form-item",{attrs:{label:"权限描述"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入权限描述"},model:{value:e.ruleForm.description,callback:function(t){e.$set(e.ruleForm,"description",t)},expression:"ruleForm.description"}})],1),"menu"===e.ruleForm.type?t("el-form-item",{attrs:{label:"路由路径"}},[t("el-input",{attrs:{placeholder:"请输入路由路径"},model:{value:e.ruleForm.path,callback:function(t){e.$set(e.ruleForm,"path",t)},expression:"ruleForm.path"}})],1):e._e(),"menu"===e.ruleForm.type?t("el-form-item",{attrs:{label:"图标"}},[t("el-input",{attrs:{placeholder:"请输入图标类名"},model:{value:e.ruleForm.icon,callback:function(t){e.$set(e.ruleForm,"icon",t)},expression:"ruleForm.icon"}},[t("template",{slot:"prepend"},[t("i",{class:e.ruleForm.icon||"el-icon-menu"})])],2)],1):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveData()}}},[e._v("确 定")])],1)],1)],1)},s=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"title-section"},[t("h2",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-key"}),e._v(" 权限管理 ")]),t("p",{staticClass:"page-subtitle"},[e._v("管理系统功能权限和访问控制")])])}],r=(i("14d9"),{name:"PermissionManagement",data(){return{viewMode:"tree",loading:!1,search:{keyword:"",type:"",status:""},treeData:[],originalData:[],dialogFormVisible:!1,dialogTitle:"新增权限",parentOptions:[],ruleForm:{id:null,label:"",code:"",type:"menu",parent_id:null,sort:0,status:1,description:"",path:"",icon:""},rules:{label:[{required:!0,message:"请输入权限名称",trigger:"blur"}],code:[{required:!0,message:"请输入权限代码",trigger:"blur"}],type:[{required:!0,message:"请选择权限类型",trigger:"change"}]},treeProps:{children:"children",label:"label"},cascaderProps:{value:"id",label:"label",children:"children",checkStrictly:!0}}},computed:{tableData(){return this.flattenTreeForTable(this.treeData)}},mounted(){this.getData()},methods:{getData(){this.loading=!0,setTimeout(()=>{this.loading=!1;const e=[{id:1,label:"系统管理",code:"system",type:"menu",parent_id:null,sort:1,status:1,description:"系统管理模块",path:"/system",icon:"el-icon-setting",create_time:"2024-01-01 10:00:00",children:[{id:11,label:"用户管理",code:"system:user",type:"menu",parent_id:1,sort:1,status:1,description:"用户管理功能",path:"/user",icon:"el-icon-user",create_time:"2024-01-01 10:00:00",children:[{id:111,label:"查看用户",code:"system:user:view",type:"action",parent_id:11,sort:1,status:1,description:"查看用户列表",create_time:"2024-01-01 10:00:00"},{id:112,label:"新增用户",code:"system:user:add",type:"action",parent_id:11,sort:2,status:1,description:"新增用户",create_time:"2024-01-01 10:00:00"},{id:113,label:"编辑用户",code:"system:user:edit",type:"action",parent_id:11,sort:3,status:1,description:"编辑用户信息",create_time:"2024-01-01 10:00:00"},{id:114,label:"删除用户",code:"system:user:delete",type:"action",parent_id:11,sort:4,status:1,description:"删除用户",create_time:"2024-01-01 10:00:00"}]},{id:12,label:"职位管理",code:"system:position",type:"menu",parent_id:1,sort:2,status:1,description:"职位管理功能",path:"/zhiwei",icon:"el-icon-postcard",create_time:"2024-01-01 10:00:00",children:[{id:121,label:"查看职位",code:"system:position:view",type:"action",parent_id:12,sort:1,status:1,description:"查看职位列表",create_time:"2024-01-01 10:00:00"},{id:122,label:"新增职位",code:"system:position:add",type:"action",parent_id:12,sort:2,status:1,description:"新增职位",create_time:"2024-01-01 10:00:00"},{id:123,label:"编辑职位",code:"system:position:edit",type:"action",parent_id:12,sort:3,status:1,description:"编辑职位信息",create_time:"2024-01-01 10:00:00"},{id:124,label:"删除职位",code:"system:position:delete",type:"action",parent_id:12,sort:4,status:1,description:"删除职位",create_time:"2024-01-01 10:00:00"}]},{id:13,label:"权限管理",code:"system:permission",type:"menu",parent_id:1,sort:3,status:1,description:"权限管理功能",path:"/quanxian",icon:"el-icon-key",create_time:"2024-01-01 10:00:00",children:[{id:131,label:"查看权限",code:"system:permission:view",type:"action",parent_id:13,sort:1,status:1,description:"查看权限列表",create_time:"2024-01-01 10:00:00"},{id:132,label:"新增权限",code:"system:permission:add",type:"action",parent_id:13,sort:2,status:1,description:"新增权限",create_time:"2024-01-01 10:00:00"},{id:133,label:"编辑权限",code:"system:permission:edit",type:"action",parent_id:13,sort:3,status:1,description:"编辑权限信息",create_time:"2024-01-01 10:00:00"},{id:134,label:"删除权限",code:"system:permission:delete",type:"action",parent_id:13,sort:4,status:1,description:"删除权限",create_time:"2024-01-01 10:00:00"}]}]},{id:2,label:"业务管理",code:"business",type:"menu",parent_id:null,sort:2,status:1,description:"业务管理模块",path:"/business",icon:"el-icon-suitcase",create_time:"2024-01-01 10:00:00",children:[{id:21,label:"订单管理",code:"business:order",type:"menu",parent_id:2,sort:1,status:1,description:"订单管理功能",path:"/dingdan",icon:"el-icon-document",create_time:"2024-01-01 10:00:00",children:[{id:211,label:"查看订单",code:"business:order:view",type:"action",parent_id:21,sort:1,status:1,description:"查看订单列表",create_time:"2024-01-01 10:00:00"},{id:212,label:"新增订单",code:"business:order:add",type:"action",parent_id:21,sort:2,status:1,description:"新增订单",create_time:"2024-01-01 10:00:00"},{id:213,label:"编辑订单",code:"business:order:edit",type:"action",parent_id:21,sort:3,status:1,description:"编辑订单信息",create_time:"2024-01-01 10:00:00"},{id:214,label:"删除订单",code:"business:order:delete",type:"action",parent_id:21,sort:4,status:1,description:"删除订单",create_time:"2024-01-01 10:00:00"},{id:215,label:"导出订单",code:"business:order:export",type:"action",parent_id:21,sort:5,status:1,description:"导出订单数据",create_time:"2024-01-01 10:00:00"}]},{id:22,label:"客户管理",code:"business:customer",type:"menu",parent_id:2,sort:2,status:1,description:"客户管理功能",path:"/customer",icon:"el-icon-user-solid",create_time:"2024-01-01 10:00:00",children:[{id:221,label:"查看客户",code:"business:customer:view",type:"action",parent_id:22,sort:1,status:1,description:"查看客户列表",create_time:"2024-01-01 10:00:00"},{id:222,label:"新增客户",code:"business:customer:add",type:"action",parent_id:22,sort:2,status:1,description:"新增客户",create_time:"2024-01-01 10:00:00"},{id:223,label:"编辑客户",code:"business:customer:edit",type:"action",parent_id:22,sort:3,status:1,description:"编辑客户信息",create_time:"2024-01-01 10:00:00"},{id:224,label:"删除客户",code:"business:customer:delete",type:"action",parent_id:22,sort:4,status:1,description:"删除客户",create_time:"2024-01-01 10:00:00"}]}]},{id:3,label:"文书管理",code:"document",type:"menu",parent_id:null,sort:3,status:1,description:"文书管理模块",path:"/document",icon:"el-icon-document-copy",create_time:"2024-01-01 10:00:00",children:[{id:31,label:"合同管理",code:"document:contract",type:"menu",parent_id:3,sort:1,status:1,description:"合同管理功能",path:"/hetong",icon:"el-icon-document",create_time:"2024-01-01 10:00:00",children:[{id:311,label:"查看合同",code:"document:contract:view",type:"action",parent_id:31,sort:1,status:1,description:"查看合同列表",create_time:"2024-01-01 10:00:00"},{id:312,label:"新增合同",code:"document:contract:add",type:"action",parent_id:31,sort:2,status:1,description:"新增合同",create_time:"2024-01-01 10:00:00"},{id:313,label:"编辑合同",code:"document:contract:edit",type:"action",parent_id:31,sort:3,status:1,description:"编辑合同信息",create_time:"2024-01-01 10:00:00"},{id:314,label:"删除合同",code:"document:contract:delete",type:"action",parent_id:31,sort:4,status:1,description:"删除合同",create_time:"2024-01-01 10:00:00"},{id:315,label:"审核合同",code:"document:contract:audit",type:"action",parent_id:31,sort:5,status:1,description:"审核合同",create_time:"2024-01-01 10:00:00"}]},{id:32,label:"律师函管理",code:"document:lawyer",type:"menu",parent_id:3,sort:2,status:1,description:"律师函管理功能",path:"/lawyer",icon:"el-icon-message",create_time:"2024-01-01 10:00:00",children:[{id:321,label:"查看律师函",code:"document:lawyer:view",type:"action",parent_id:32,sort:1,status:1,description:"查看律师函列表",create_time:"2024-01-01 10:00:00"},{id:322,label:"发送律师函",code:"document:lawyer:send",type:"action",parent_id:32,sort:2,status:1,description:"发送律师函",create_time:"2024-01-01 10:00:00"},{id:323,label:"编辑律师函",code:"document:lawyer:edit",type:"action",parent_id:32,sort:3,status:1,description:"编辑律师函",create_time:"2024-01-01 10:00:00"}]},{id:33,label:"课程管理",code:"document:course",type:"menu",parent_id:3,sort:3,status:1,description:"课程管理功能",path:"/kecheng",icon:"el-icon-video-play",create_time:"2024-01-01 10:00:00",children:[{id:331,label:"查看课程",code:"document:course:view",type:"action",parent_id:33,sort:1,status:1,description:"查看课程列表",create_time:"2024-01-01 10:00:00"},{id:332,label:"新增课程",code:"document:course:add",type:"action",parent_id:33,sort:2,status:1,description:"新增课程",create_time:"2024-01-01 10:00:00"},{id:333,label:"编辑课程",code:"document:course:edit",type:"action",parent_id:33,sort:3,status:1,description:"编辑课程",create_time:"2024-01-01 10:00:00"},{id:334,label:"删除课程",code:"document:course:delete",type:"action",parent_id:33,sort:4,status:1,description:"删除课程",create_time:"2024-01-01 10:00:00"}]}]},{id:4,label:"财务管理",code:"finance",type:"menu",parent_id:null,sort:4,status:1,description:"财务管理模块",path:"/finance",icon:"el-icon-coin",create_time:"2024-01-01 10:00:00",children:[{id:41,label:"支付管理",code:"finance:payment",type:"menu",parent_id:4,sort:1,status:1,description:"支付管理功能",path:"/order",icon:"el-icon-money",create_time:"2024-01-01 10:00:00",children:[{id:411,label:"查看支付记录",code:"finance:payment:view",type:"action",parent_id:41,sort:1,status:1,description:"查看支付记录",create_time:"2024-01-01 10:00:00"},{id:412,label:"处理退款",code:"finance:payment:refund",type:"action",parent_id:41,sort:2,status:1,description:"处理退款申请",create_time:"2024-01-01 10:00:00"},{id:413,label:"导出财务报表",code:"finance:payment:export",type:"action",parent_id:41,sort:3,status:1,description:"导出财务报表",create_time:"2024-01-01 10:00:00"}]}]}];this.treeData=e,this.originalData=JSON.parse(JSON.stringify(e)),this.updateParentOptions()},500)},flattenTreeForTable(e,t=0,i=[]){return e.forEach(e=>{const a={...e,level:t,hasChildren:e.children&&e.children.length>0};delete a.children,i.push(a),e.children&&e.children.length>0&&this.flattenTreeForTable(e.children,t+1,i)}),i},updateParentOptions(){this.parentOptions=this.buildCascaderOptions(this.treeData)},buildCascaderOptions(e){return e.map(e=>({id:e.id,label:e.label,children:e.children?this.buildCascaderOptions(e.children):[]}))},searchData(){this.getData()},clearSearch(){this.search={keyword:"",type:"",status:""},this.searchData()},refulsh(){this.$router.go(0)},editData(e){if(0===e)this.dialogTitle="新增权限",this.ruleForm={id:null,label:"",code:"",type:"menu",parent_id:null,sort:0,status:1,description:"",path:"",icon:""};else{this.dialogTitle="编辑权限";const t=this.findPermissionById(e);t&&(this.ruleForm={...t})}this.dialogFormVisible=!0},addChild(e){this.dialogTitle="新增子权限",this.ruleForm={id:null,label:"",code:"",type:"action",parent_id:[e.id],sort:0,status:1,description:"",path:"",icon:""},this.dialogFormVisible=!0},findPermissionById(e,t=this.originalData){for(let i of t){if(i.id===e)return i;if(i.children){const t=this.findPermissionById(e,i.children);if(t)return t}}return null},delData(e){this.$confirm("确定要删除这个权限吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$message.success("删除成功！"),this.getData()}).catch(()=>{this.$message.info("已取消删除")})},saveData(){this.$refs["ruleForm"].validate(e=>{e&&(this.$message.success(this.ruleForm.id?"更新成功！":"新增成功！"),this.dialogFormVisible=!1,this.getData())})},changeStatus(e){this.$message.success("权限状态已"+(1===e.status?"启用":"禁用"))},getNodeIcon(e){const t={menu:"el-icon-menu",action:"el-icon-setting",data:"el-icon-document"};return t[e]||"el-icon-menu"},getTypeColor(e){const t={menu:"primary",action:"success",data:"warning"};return t[e]||"primary"},getTypeLabel(e){const t={menu:"菜单权限",action:"操作权限",data:"数据权限"};return t[e]||"菜单权限"}}}),l=r,o=(i("4978"),i("2877")),n=Object(o["a"])(l,a,s,!1,null,"7aacc5fe",null);t["default"]=n.exports},"2a9c":function(e,t,i){},4978:function(e,t,i){"use strict";i("2a9c")}}]);
//# sourceMappingURL=chunk-67559f37.13d8c247.js.map