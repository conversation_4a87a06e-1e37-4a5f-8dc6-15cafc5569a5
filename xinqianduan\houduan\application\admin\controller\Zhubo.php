<?php
namespace app\admin\controller;
use think\Request;
use untils\{JsonService,LiveA};
use models\{Zhubos,Lvshis};
class Zhubo
{
    protected $model;
    public function __construct(Zhubos $model){
        //parent::__construct();
        $this->model=$model;
        
    }
    public function index(Request $request,$page=1,$size=20){   
        $where=[];
        $search=$request->post();
        if(!empty($search['keyword'])){
            $where[]=['title',"like","%".$search['keyword']."%"];
        }
        $res = $this->model::withAttr('lvshi_id',function($v,$d){
            return Lvshis::where(['id'=>$v])->value('title');
        })->withAttr('start_time',function($v,$d){
            return date('Y-m-d H:i:s',$v);
        })->withAttr('end_time',function($v,$d){
            return date('Y-m-d H:i:s',$v);
        })
        ->where($where)
        ->order(['id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->select();
        $count = $this->model->where($where)->count();
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,$count);
    }


    public function save(Request $request,LiveA $LiveA){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        if(empty($form['id'])){
            $form['start_time'] =strtotime($form['times'][0]);
            $form['end_time'] =strtotime($form['times'][1]);
            if($form['start_time']*1>=$form['end_time']*1){
                return JsonService::fail('结束时间必须大于开始时间');
            }
            $form['uuid']=md5($form['lvshi_id'].$form['end_time']);
            $form['appname']='lvdian';
            $form['streamname']=$form['uuid'];
            $data = $LiveA->createdLive($form['appname'],$form['streamname'],$form['end_time']);
            $form['alive_url']=$data['alive_url'];
            $form['tlive_url']=$data['tlive_url']['hls'];
            
        }
        unset($form['times']);
        
        $this->model::beginTrans();
        try {

              
            $res = $this->model->saveData($form);
            $this->model::commitTrans();
            return JsonService::successful('成功');
        } catch (Exception $e) {
            $this->model::rollbackTrans();
            return JsonService::fail('系统发生错误:'.$e->getMessage());
        }

    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);
        $res['times']=[date('Y-m-d H:i:s',$res['start_time']),date('Y-m-d H:i:s',$res['end_time'])];
        if(empty($res)) return JsonService::fail('获取数据失败');
        else return JsonService::successful('成功',$res);
    }
    public function getHuifang($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        
        if(empty($res)) return JsonService::fail('获取数据失败');
        if(empty($res['huifang']))  return JsonService::fail('获取数据失败');
        
        return JsonService::successful('成功',unserialize($res['huifang']));
    }
    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->delData(['id'=>$id]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }
}
