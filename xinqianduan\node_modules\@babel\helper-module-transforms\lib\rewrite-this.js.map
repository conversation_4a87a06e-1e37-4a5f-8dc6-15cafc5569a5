{"version": 3, "names": ["_helperEnvironmentVisitor", "require", "_core", "numericLiteral", "unaryExpression", "t", "rewriteThisVisitor", "rewriteThis", "programPath", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "path", "replaceWith", "noScope", "node"], "sources": ["../src/rewrite-this.ts"], "sourcesContent": ["import environmentVisitor from \"@babel/helper-environment-visitor\";\nimport { traverse, types as t } from \"@babel/core\";\nconst { numericLiteral, unaryExpression } = t;\n\nimport type { NodePath } from \"@babel/core\";\n\n/**\n * A lazily constructed visitor to walk the tree, rewriting all `this` references in the\n * top-level scope to be `void 0` (undefined).\n *\n */\nlet rewriteThisVisitor: Parameters<typeof traverse>[1];\n\nexport default function rewriteThis(programPath: NodePath) {\n  if (!rewriteThisVisitor) {\n    rewriteThisVisitor = traverse.visitors.merge([\n      environmentVisitor,\n      {\n        ThisExpression(path) {\n          path.replaceWith(unaryExpression(\"void\", numericLiteral(0), true));\n        },\n      },\n    ]);\n    rewriteThisVisitor.noScope = true;\n  }\n  // Rewrite \"this\" to be \"undefined\".\n  traverse(programPath.node, rewriteThisVisitor);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,MAAM;EAAEE,cAAc;EAAEC;AAAgB,CAAC,GAAGC,WAAC;AAS7C,IAAIC,kBAAkD;AAEvC,SAASC,WAAWA,CAACC,WAAqB,EAAE;EACzD,IAAI,CAACF,kBAAkB,EAAE;IACvBA,kBAAkB,GAAGG,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAC3CC,iCAAkB,EAClB;MACEC,cAAcA,CAACC,IAAI,EAAE;QACnBA,IAAI,CAACC,WAAW,CAACX,eAAe,CAAC,MAAM,EAAED,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MACpE;IACF,CAAC,CACF,CAAC;IACFG,kBAAkB,CAACU,OAAO,GAAG,IAAI;EACnC;EAEA,IAAAP,cAAQ,EAACD,WAAW,CAACS,IAAI,EAAEX,kBAAkB,CAAC;AAChD", "ignoreList": []}