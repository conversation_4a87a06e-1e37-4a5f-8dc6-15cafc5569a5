<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------
define('DS', DIRECTORY_SEPARATOR);
defined('APP_PATH') or define('APP_PATH', dirname($_SERVER['SCRIPT_FILENAME']) . DS);
defined('ROOT_PATH') or define('ROOT_PATH', dirname(realpath(APP_PATH)) . DS);
// 应用公共文件
use PhpOffice\PhpSpreadsheet\Spreadsheet;

/**
 *生成唯一订单号
 */
function get_order_num()
{
	return date('Ymd').substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
}

function emoji_to_html($str){
if(!is_string($str))return $str;
    if(!$str || $str=='undefined')return '';

    $text = json_encode($str);
    $text = preg_replace_callback("/(\\\u[ed][0-9a-f]{3})/i",function($str){
        return addslashes($str[0]);
    },$text);
    return json_decode($text);

}
function preg_emoji($str){
    $text = json_encode($str);
    $text = preg_replace_callback('/\\\\\\\\/i', function ($str) {
        return '\\';
    }, $text);
    return json_decode($text);

}

function get_tree_child($data,$pid=0,$fieldId='id',$praent_id='pid',$child='children')
{
    $tree = [];

    foreach($data as $k => $v)
    {
      if($v["$praent_id"] == $pid)
      {        //父亲找到儿子
       $v["$child"] = get_tree_child($data, $v["$fieldId"],$fieldId,$praent_id,$child);
       $tree[] = $v;

      }
    }
    return $tree;
}



function cai_bumen_array(){

}


if(!function_exists('user_text_encode')){
    function user_text_encode($str)
    {
        if (!is_string($str)) return $str;
        if (!$str || $str == 'undefined') return '';

        $text = json_encode($str); //暴露出unicode
        $text = preg_replace_callback("/(\\\u[ed][0-9a-f]{3})/i", function ($str) {
            return addslashes($str[0]);
        }, $text); //将emoji的unicode留下，其他不动，这里的正则比原答案增加了d，因为我发现我很多emoji实际上是\ud开头的，反而暂时没发现有\ue开头。
        return json_decode($text);
    }
}


  function replace_pic_url($content = null, $strUrl = null) {
  if ($strUrl) {

    $content = str_replace("\\", "/", $content);

    //提取图片路径的src的正则表达式 并把结果存入$matches中
      preg_match_all("/<img(.*)src=\"([^\"]+)\"[^>]+>/isU",$content,$matches);
      $img = "";
        if(!empty($matches)) {
        //注意，上面的正则表达式说明src的值是放在数组的第三个中
        $img = $matches[2];
        }else {
           $img = "";
        }

        if (!empty($img)) {
                $patterns= array();
                $replacements = array();
                foreach($img as $imgItem){
                  $final_imgUrl = $strUrl.$imgItem;
                  $replacements[] = $final_imgUrl;
                  $img_new = "/".preg_replace("/\//i","\/",$imgItem)."/";
                  $patterns[] = $img_new;
                }

                //让数组按照key来排序
                ksort($patterns);
                ksort($replacements);

                //替换内容
                $vote_content = preg_replace($patterns, $replacements, $content);

        return $vote_content;
    }else {
      return $content;
    }
  } else {
    return $content;
  }
}

function return_file_type($file_type_str){
    $file_type = '';
    if(empty($file_type_str)){
        return  '/static/other.png';
        exit;
    }
    $file_type_array = explode('.',strtolower($file_type_str));
    if(!empty($file_type_array) && isset($file_type_array) && is_array($file_type_array)){
        $file_type = $file_type_array[1];
    }

    if (in_array($file_type,array('png','jpg','jpeg','gif','svg','bmp'))){
        return '/static/pic.png';
        exit;
    }

    if (in_array($file_type,array('pdf'))){
        return '/static/pdf.png';
        exit;
    }

    if (in_array($file_type,array('doc','docx'))){
        return '/static/doc.png';
        exit;
    }

    if (in_array($file_type,array('xls','xlsx'))){
        return '/static/xls.png';
        exit;
    }

    if (in_array($file_type,array('mp4','avi','mov','wmv','mpg'))){
        return '/static/video.png';
        exit;
    }

    if (in_array($file_type,array('text','txt'))){
        return '/static/text.png';
        exit;
    }

    if (in_array($file_type,array('zip','rar','tar'))){
        return '/static/pack.png';
        exit;
    }

    return  '/static/other.png';
}

function exportData($data,$headerData,$filename){
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->setActiveSheetIndex(0);  //读取文件中的工作表
//    $header = [
//        '注册手机号码',
//        '公司名称',
//        '联系人',
//        '联系号码',
//        '用户来源',
//        '到期时间',
//        '录入时间',
//    ];
    $header = array_keys($headerData);//表头

    $header = array_merge($header);
    foreach ($header as $key => $value) {
        $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        $sheet->getColumnDimension(
            \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($key + 1)
        )->setWidth(strlen($value) * 2);
    }
    $row = 2;
    foreach ($data as $key => $item) {
        $columnIndex = 1;
        foreach ($header as $he){
            $sheet->setCellValueByColumnAndRow($columnIndex, $row, $item[$headerData[$he]]);
            $columnIndex++;
        }
        $row++;
    }

    ob_end_clean();
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
    //删除清空：
    $spreadsheet->disconnectWorksheets();exit;
}
