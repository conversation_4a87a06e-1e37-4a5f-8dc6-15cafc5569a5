var _Symbol = require("core-js-pure/features/symbol/index.js");
var _getIteratorMethod = require("core-js-pure/features/get-iterator-method.js");
var _Array$from = require("core-js-pure/features/array/from.js");
function _iterableToArray(r) {
  if ("undefined" != typeof _Symbol && null != _getIteratorMethod(r) || null != r["@@iterator"]) return _Array$from(r);
}
module.exports = _iterableToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;