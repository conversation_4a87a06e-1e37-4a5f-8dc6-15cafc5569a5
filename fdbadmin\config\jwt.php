<?php
/*
 * JWT 配置
 * TP5 JWT 依赖包
 * composer require firebase/php-jwt
 * 用法：引入 use Jwt\JwtAuth;
 * 1.获取token：JwtAuth::getToken(['key'=>'这里是自定义的参数，一般只存用户id']);
 * 2.解析token：JwtAuth::getInfo();
 * 3.获取单个值：JwtAuth::getVal('cc');
 */
return[
    'salt' => 'AJX5GSA2HLB5',//这里是自定义的一个随机字串，解密时也会用，相当于加密中常用的 盐  salt
    'nbf' => 0, //在什么时候token开始生效  （100表示生成100秒后才生效）
    'exp' => 86400, //token 过期时间 这里表示生成86400秒有效期的token
    'tokenName' => 'authtoken',  //接收token的参数名 类似 表单的name
    'request' => 'header'  //接收token的方式 有header post get 默认header
];