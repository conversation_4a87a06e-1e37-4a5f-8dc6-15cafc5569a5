<?php
namespace app\admin\controller;
use models\Yuangongs;
use think\Request;
use untils\JsonService;
use models\{Orders,Users,Debts};

class Order extends Base
{
    protected $model;
    public function __construct(Orders $model){
        parent::__construct();
        $this->model=$model;

    }
    public function index(Request $request,$page=1,$size=20){
        $where[]=['t1.is_delete','=','0'];
        $search=$request->post();
        $userid = $this->userid;//登录者id
        if(!empty($search['keyword'])){
            $where[]=['t1.title|order_sn',"like","%".$search['keyword']."%"];
        }
        if($search['is_pay']!=-1){
            $where[]=['t1.is_pay','=',$search['is_pay']];
        }
        if($search['is_deal']!=-1){
            $where[]=['t1.is_deal','=',$search['is_deal']];
        }
        $res = $this->model->alias('t1')->leftJoin("users t2","t1.uid = t2.id")
            ->field('t1.*')
            ->withAttr('uid_name',function($v,$d){
            return Users::where(['id'=>$v])->value('t1.nickname');
        })->withAttr('is_pay',function($v,$d){
            switch ($v) {
                case 1:
                    return '未支付';  // code...
                    break;
                case 2:
                    return '已支付';  // code...
                    break;
                case 3:
                    return '已退款';  // code...
                    break;
            }
        })->withAttr('is_deal',function($v,$d){
            switch ($v) {
                case '1':
                    return '处理中';
                    break;
                 case '2':
                    return '已完成';
                    break;
                     case '0':
                    return '待处理';
                    break;


            }
        })->withAttr('phone',function ($v,$d){
            return Users::where(['id'=>$d['uid']])->value('phone');
        })
        ->where($where)
        ->where("{$userid}=1 or t2.tiaojie_id={$userid} or t2.fawu_id={$userid} or t2.lian_id={$userid} or t2.ls_id={$userid} or t2.ywy_id={$userid} or t2.htsczy_id={$userid} or (t2.tiaojie_id=0 and t2.fawu_id=0 and t2.lian_id=0 and t2.htsczy_id=0 and t2.ls_id=0 and t2.ywy_id=0)")
        ->order(['t1.id'=>'desc'])
        ->limit($size)
        ->page($page)
        ->append(['phone'])
        ->select();
        $count = $this->model->alias('t1')->leftJoin("users t2","t1.uid = t2.id")->where($where)->count();
        $money = $this->model->alias('t1')->leftJoin("users t2","t1.uid = t2.id")->where($where)->value('sum(total_price)');
        if(empty($res)) return JsonService::fail('失败');
        else return JsonService::successful('成功',$res,['count'=>$count,'money'=>$money]);
    }


    public function save(Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form)) return JsonService::fail('未接收到参数');
        if(!empty($form['id'])){
            if($form['is_pay']!=2) return JsonService::fail('订单未支付/已退款,不能选择制作完成');
        }
        $res = $this->model->saveData($form);
        $errorMsg= $this->model::getErrorInfo();
        if(!empty($errorMsg)) return JsonService::fail($errorMsg);
        else return JsonService::successful('成功');
    }

    public function read($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
        $res->hidden(['create_time','update_time']);

        if(empty($res)) return JsonService::fail('获取数据失败');
        if($res['is_pay']!=2) return JsonService::fail('订单未支付/已退款,不能选择制作完成');
        else return JsonService::successful('成功',$res);
    }

    public function delete($id=0){
        if(empty($id)) return JsonService::fail('数据不存在');
        $res = $this->model->saveData(['id'=>$id,'is_delete'=>1]);
        if(empty($res)) return JsonService::fail('删除失败');
        else return JsonService::successful('删除成功');
    }
    public function tuikuan($id){
        if(empty($id)) return JsonService::fail('数据不存在');
        $info = $this->model->find($id);
        if($info->is_pay!=2){
            return JsonService::fail('订单未支付/已退款,不能申请退款');
        }
        $res = $this->model->saveData(['id'=>$id,'is_pay'=>3,'refund_time'=>now()]);
        if(empty($res)) return JsonService::fail('退款成功');
        else return JsonService::successful('退款失败');
    }
    public function getList(){
        $res = $this->model->select()->toArray();
        if(empty($res)) return JsonService::fail('fail');
        else return JsonService::successful('ok',$res);
    }

    public function view($id=0){
        if(empty($id))  return JsonService::fail('未接收到参数');
        $res  = $this->model->find($id);
      //  $res->hidden(['create_time','update_time']);
       // $user = Users::where(['id'=>$res['uid']])->value('nickname');
        switch ($res['is_deal']) {
            case '1':
                $res['is_deal_name'] =  '处理中';
                break;
            case '2':
                $res['is_deal_name'] =  '已完成';
                break;
            case '0':
                $res['is_deal_name'] =  '待处理';
                break;
        }
        switch ($res['is_pay']) {
            case '1':
                $res['is_pay_name'] =  '未支付';
                break;
            case '2':
                $res['is_pay_name'] =  '已支付';
                break;
            case '3':
                $res['is_pay_name'] =  '已退款';
                break;
        }
        if(!empty($res)){
            $res['linkman'] = Users::where(['id'=>$res['uid']])->value('nickname');
            $res['linkphone'] = Users::where(['id'=>$res['uid']])->value('phone');
            if(!empty($res['dt_id'])){
                $res['debts_name'] = Debts::where(['id'=>$res['dt_id']])->value('name');
                $res['debts_tel'] = Debts::where(['id'=>$res['uid']])->value('tel');
            }
            $res['free_operator'] = Yuangongs::where(['id'=>$res['free_operator']])->value('title');
        }
        if(empty($res)) return JsonService::fail('获取数据失败');
       // if($res['is_pay']!=2) return JsonService::fail('订单未支付/已退款,不能选择制作完成');
        else return JsonService::successful('成功',$res);
    }
}
