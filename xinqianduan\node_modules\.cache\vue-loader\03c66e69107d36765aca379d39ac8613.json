{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue", "mtime": 1748450855160}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAy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file": "index.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div class=\"contract-list-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-document\"></i>\r\n            合同列表管理\r\n          </h2>\r\n          <p class=\"page-subtitle\">管理系统中的所有合同模板和文书</p>\r\n        </div>\r\n        <div class=\"header-right\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"editData(0)\"\r\n            class=\"add-btn\"\r\n          >\r\n            新增合同\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"refulsh\"\r\n            class=\"refresh-btn\"\r\n          >\r\n            刷新\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索和筛选区域 -->\r\n    <div class=\"search-section\">\r\n      <el-card shadow=\"never\" class=\"search-card\">\r\n        <div class=\"search-content\">\r\n          <div class=\"search-left\">\r\n            <el-input\r\n              placeholder=\"搜索合同标题、类型...\"\r\n              v-model=\"search.keyword\"\r\n              class=\"search-input\"\r\n              clearable\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <div class=\"search-right\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"searchData()\"\r\n              class=\"search-btn\"\r\n            >\r\n              搜索\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"clearSearch()\"\r\n              class=\"clear-btn\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 数据表格区域 -->\r\n    <div class=\"table-section\">\r\n      <el-card shadow=\"never\" class=\"table-card\">\r\n        <el-table\r\n          :data=\"list\"\r\n          v-loading=\"loading\"\r\n          class=\"contract-table\"\r\n          stripe\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"文书标题\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"title-cell\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span class=\"title-text\">{{ scope.row.title }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"cate_id\" label=\"文书类型\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag type=\"primary\" size=\"small\">\r\n                {{ getCategoryName(scope.row.cate_id) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"price\" label=\"价格\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span class=\"price-text\">\r\n                <i class=\"el-icon-money\"></i>\r\n                ¥{{ scope.row.price || '0.00' }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"file_path\" label=\"文件状态\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.file_path ? 'success' : 'warning'\"\r\n                size=\"small\"\r\n              >\r\n                {{ scope.row.file_path ? '已上传' : '未上传' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"create_time\" label=\"录入时间\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-cell\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ scope.row.create_time }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"editData(scope.row.id)\"\r\n                  icon=\"el-icon-edit\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.file_path\"\r\n                  type=\"success\"\r\n                  size=\"mini\"\r\n                  @click=\"previewContract(scope.row)\"\r\n                  icon=\"el-icon-view\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  预览\r\n                </el-button>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  size=\"mini\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  icon=\"el-icon-delete\"\r\n                  plain\r\n                  class=\"action-btn\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-wrapper\">\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-sizes=\"[20, 50, 100, 200]\"\r\n            :page-size=\"size\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            background\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n      class=\"form-dialog\"\r\n      @close=\"handleDialogClose\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"文书类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cate_id\"\r\n        >\r\n          <el-select v-model=\"ruleForm.cate_id\" placeholder=\"请选择\" filterable>\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in cates\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"文件上传\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changefield('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 文书预览对话框 -->\r\n    <el-dialog\r\n      title=\"文书预览\"\r\n      :visible.sync=\"dialogPreview\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\"\r\n      class=\"preview-dialog\"\r\n    >\r\n      <div class=\"preview-content\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewData.title }}</h3>\r\n          <div class=\"preview-meta\">\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-folder\"></i>\r\n              类型：{{ getCategoryName(previewData.cate_id) }}\r\n            </span>\r\n            <span class=\"meta-item\">\r\n              <i class=\"el-icon-money\"></i>\r\n              价格：¥{{ previewData.price || '0.00' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"preview-body\">\r\n          <div v-if=\"previewData.file_path\" class=\"file-preview\">\r\n            <div class=\"file-info\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <span>{{ previewData.file_path.split('/').pop() }}</span>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"downloadFile(previewData.file_path)\"\r\n                icon=\"el-icon-download\"\r\n              >\r\n                下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"previewData.content\" class=\"content-preview\">\r\n            <h4>文书内容：</h4>\r\n            <div class=\"content-html\" v-html=\"previewData.content\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshu/\",\r\n      field: \"\",\r\n      title: \"文书\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      dialogPreview: false,\r\n      previewData: {},\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      isClear: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        cate_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择文书类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      cates: [],\r\n      expireTimeOption: {\r\n        disabledDate(date) {\r\n          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean\r\n          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    console.log('页面挂载完成，开始加载数据...');\r\n    this.getData();\r\n    this.getLvshi(); // 获取分类数据\r\n    // 添加键盘事件监听\r\n    document.addEventListener('keydown', this.handleKeyDown);\r\n\r\n    // 添加调试信息\r\n    this.$nextTick(() => {\r\n      console.log('页面渲染完成');\r\n      console.log('当前list数据:', this.list);\r\n      console.log('当前loading状态:', this.loading);\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除键盘事件监听\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n  },\r\n\r\n  watch: {\r\n    dialogFormVisible(newVal, oldVal) {\r\n      console.log('对话框可见性变化:', newVal, oldVal);\r\n      if (!newVal && oldVal) {\r\n        // 对话框关闭时重置表单\r\n        this.handleDialogClose();\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    change() {},\r\n    changefield(field) {\r\n      this.field = field;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n      _this.getLvshi();\r\n      _this.dialogFormVisible = true;\r\n    },\r\n\r\n    getLvshi() {\r\n      // 添加测试分类数据\r\n      const testCategories = [\r\n        { id: 1, title: \"民事诉讼\", desc: \"民事纠纷相关文书\" },\r\n        { id: 2, title: \"商事诉讼\", desc: \"商业纠纷相关文书\" },\r\n        { id: 3, title: \"侵权诉讼\", desc: \"侵权纠纷相关文书\" },\r\n        { id: 4, title: \"婚姻家庭\", desc: \"婚姻家庭纠纷文书\" },\r\n        { id: 5, title: \"知识产权\", desc: \"知识产权纠纷文书\" },\r\n        { id: 6, title: \"劳动争议\", desc: \"劳动关系纠纷文书\" },\r\n        { id: 7, title: \"行政诉讼\", desc: \"行政纠纷相关文书\" },\r\n        { id: 8, title: \"刑事辩护\", desc: \"刑事案件相关文书\" }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        this.cates = testCategories;\r\n        console.log('加载测试分类数据:', this.cates);\r\n      }, 50);\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      this.postRequest(\"/wenshucate/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          // 确保返回的数据是数组\r\n          this.cates = Array.isArray(resp.data) ? resp.data : [];\r\n          console.log('获取到的分类数据:', this.cates);\r\n        } else {\r\n          console.error('获取分类失败:', resp);\r\n          this.cates = [];\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取分类出错:', error);\r\n        this.cates = [];\r\n      });\r\n      */\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    // 清空搜索\r\n    clearSearch() {\r\n      this.search.keyword = '';\r\n      this.searchData();\r\n    },\r\n\r\n    // 获取分类名称\r\n    getCategoryName(cateId) {\r\n      // 确保 cates 是数组\r\n      if (!Array.isArray(this.cates)) {\r\n        console.warn('cates is not an array:', this.cates);\r\n        return '未分类';\r\n      }\r\n      const category = this.cates.find(item => item.id === cateId);\r\n      return category ? category.title : '未分类';\r\n    },\r\n\r\n    // 预览文书\r\n    previewContract(row) {\r\n      console.log('预览文书:', row);\r\n      this.previewData = row;\r\n      this.dialogPreview = true;\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      const link = document.createElement('a');\r\n      link.href = fileUrl;\r\n      link.download = fileUrl.split('/').pop();\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.$message.success('开始下载文件');\r\n    },\r\n\r\n    // 处理对话框关闭\r\n    handleDialogClose() {\r\n      console.log('对话框关闭事件触发');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n    },\r\n\r\n    // 取消操作\r\n    cancelDialog() {\r\n      console.log('取消按钮点击');\r\n      // 重置表单\r\n      if (this.$refs.ruleForm) {\r\n        this.$refs.ruleForm.resetFields();\r\n      }\r\n      this.ruleForm = {\r\n        title: \"\",\r\n        is_num: 0,\r\n        cate_id: \"\",\r\n        file_path: \"\",\r\n        price: \"\",\r\n        content: \"\"\r\n      };\r\n      this.isClear = true;\r\n      this.dialogFormVisible = false;\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeyDown(event) {\r\n      // ESC键关闭对话框\r\n      if (event.keyCode === 27 && this.dialogFormVisible) {\r\n        this.cancelDialog();\r\n      }\r\n    },\r\n\r\n\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n\r\n      // 添加测试数据\r\n      const testData = [\r\n        {\r\n          id: 1,\r\n          title: \"民事起诉状模板\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/civil_complaint_template.docx\",\r\n          price: \"500.00\",\r\n          content: \"<p>这是一份标准的民事起诉状模板，适用于一般民事纠纷案件。包含完整的格式要求和必要条款。</p><p>主要内容包括：</p><ul><li>当事人基本信息</li><li>诉讼请求</li><li>事实与理由</li><li>证据清单</li></ul>\",\r\n          create_time: \"2024-01-15 10:30:00\",\r\n          update_time: \"2024-01-15 10:30:00\"\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"劳动合同纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/labor_dispute_complaint.pdf\",\r\n          price: \"800.00\",\r\n          content: \"<p>专门针对劳动合同纠纷的起诉书模板，涵盖工资拖欠、违法解除等常见情形。</p><p>适用范围：</p><ul><li>工资拖欠纠纷</li><li>违法解除劳动合同</li><li>加班费争议</li><li>经济补偿金纠纷</li></ul>\",\r\n          create_time: \"2024-01-16 14:20:00\",\r\n          update_time: \"2024-01-16 14:20:00\"\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"房屋买卖合同纠纷诉状\",\r\n          cate_id: 1,\r\n          file_path: \"/uploads/documents/property_sale_dispute.docx\",\r\n          price: \"1200.00\",\r\n          content: \"<p>房屋买卖合同纠纷专用诉讼文书，包含房产交易中的各种争议处理。</p><p>涵盖问题：</p><ul><li>房屋质量问题</li><li>逾期交房</li><li>产权过户纠纷</li><li>定金违约</li></ul>\",\r\n          create_time: \"2024-01-17 09:15:00\",\r\n          update_time: \"2024-01-17 09:15:00\"\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"交通事故赔偿起诉书\",\r\n          cate_id: 3,\r\n          file_path: \"/uploads/documents/traffic_accident_claim.pdf\",\r\n          price: \"600.00\",\r\n          content: \"<p>交通事故人身损害赔偿起诉书模板，适用于各类交通事故赔偿案件。</p><p>赔偿项目：</p><ul><li>医疗费</li><li>误工费</li><li>护理费</li><li>精神损害抚慰金</li></ul>\",\r\n          create_time: \"2024-01-18 16:45:00\",\r\n          update_time: \"2024-01-18 16:45:00\"\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"借款合同纠纷起诉状\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/loan_dispute_complaint.docx\",\r\n          price: \"400.00\",\r\n          content: \"<p>民间借贷纠纷起诉状模板，适用于个人借款、企业借贷等各类借款纠纷。</p><p>主要条款：</p><ul><li>借款本金确认</li><li>利息计算标准</li><li>违约责任</li><li>担保责任</li></ul>\",\r\n          create_time: \"2024-01-19 11:30:00\",\r\n          update_time: \"2024-01-19 11:30:00\"\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"离婚纠纷起诉书\",\r\n          cate_id: 4,\r\n          file_path: \"/uploads/documents/divorce_complaint.pdf\",\r\n          price: \"900.00\",\r\n          content: \"<p>离婚纠纷起诉书模板，包含财产分割、子女抚养等完整内容。</p><p>主要内容：</p><ul><li>夫妻感情破裂事实</li><li>财产分割方案</li><li>子女抚养安排</li><li>债务承担</li></ul>\",\r\n          create_time: \"2024-01-20 13:20:00\",\r\n          update_time: \"2024-01-20 13:20:00\"\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"知识产权侵权起诉状\",\r\n          cate_id: 5,\r\n          file_path: \"/uploads/documents/ip_infringement_complaint.docx\",\r\n          price: \"1500.00\",\r\n          content: \"<p>知识产权侵权起诉状模板，适用于商标、专利、著作权等侵权案件。</p><p>保护范围：</p><ul><li>商标权侵权</li><li>专利权侵权</li><li>著作权侵权</li><li>商业秘密侵权</li></ul>\",\r\n          create_time: \"2024-01-21 15:10:00\",\r\n          update_time: \"2024-01-21 15:10:00\"\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"公司股权纠纷起诉书\",\r\n          cate_id: 2,\r\n          file_path: \"/uploads/documents/equity_dispute_complaint.pdf\",\r\n          price: \"2000.00\",\r\n          content: \"<p>公司股权纠纷起诉书模板，处理股东权益、公司治理等复杂商事纠纷。</p><p>争议类型：</p><ul><li>股权转让纠纷</li><li>股东知情权</li><li>利润分配争议</li><li>公司决议效力</li></ul>\",\r\n          create_time: \"2024-01-22 10:00:00\",\r\n          update_time: \"2024-01-22 10:00:00\"\r\n        }\r\n      ];\r\n\r\n      // 模拟API调用延迟\r\n      setTimeout(() => {\r\n        try {\r\n          console.log('开始加载测试数据...');\r\n          console.log('原始测试数据:', testData);\r\n\r\n          // 模拟搜索功能\r\n          let filteredData = testData;\r\n          if (_this.search.keyword && _this.search.keyword.trim()) {\r\n            const keyword = _this.search.keyword.trim().toLowerCase();\r\n            filteredData = testData.filter(item =>\r\n              item.title.toLowerCase().includes(keyword) ||\r\n              item.content.toLowerCase().includes(keyword)\r\n            );\r\n            console.log('搜索关键词:', keyword);\r\n            console.log('搜索结果:', filteredData);\r\n          }\r\n\r\n          // 模拟分页\r\n          const startIndex = (_this.page - 1) * _this.size;\r\n          const endIndex = startIndex + _this.size;\r\n          const pageData = filteredData.slice(startIndex, endIndex);\r\n\r\n          _this.list = pageData;\r\n          _this.total = filteredData.length;\r\n          _this.loading = false;\r\n\r\n          console.log('当前页:', _this.page);\r\n          console.log('每页大小:', _this.size);\r\n          console.log('分页后的数据:', pageData);\r\n          console.log('设置到list的数据:', _this.list);\r\n          console.log('总数:', _this.total);\r\n          console.log('加载状态:', _this.loading);\r\n        } catch (error) {\r\n          console.error('加载测试数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        }\r\n      }, 100); // 减少延迟到100ms\r\n\r\n      // 保留原有的API调用逻辑（注释掉，以便后续恢复）\r\n      /*\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            // 确保返回的数据是数组\r\n            _this.list = Array.isArray(resp.data) ? resp.data : [];\r\n            _this.total = resp.count || 0;\r\n            console.log('获取到的列表数据:', _this.list);\r\n          } else {\r\n            console.error('获取数据失败:', resp);\r\n            _this.list = [];\r\n            _this.total = 0;\r\n          }\r\n          _this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error('获取数据出错:', error);\r\n          _this.list = [];\r\n          _this.total = 0;\r\n          _this.loading = false;\r\n        });\r\n      */\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.field] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n/* 容器样式 */\r\n.contract-list-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 24px 32px;\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-left .page-title i {\r\n  font-size: 28px;\r\n}\r\n\r\n.header-left .page-subtitle {\r\n  margin: 0;\r\n  opacity: 0.9;\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.add-btn, .refresh-btn {\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.add-btn:hover, .refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  color: white;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.search-left {\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n  border-radius: 8px;\r\n  border: 2px solid #e4e7ed;\r\n  padding-left: 40px;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n}\r\n\r\n.search-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn {\r\n  border: 2px solid #e4e7ed;\r\n  color: #606266;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.clear-btn:hover {\r\n  border-color: #c0c4cc;\r\n  color: #409eff;\r\n}\r\n\r\n/* 表格区域样式 */\r\n.table-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  border: none;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contract-table >>> .el-table__header {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.contract-table >>> .el-table__header th {\r\n  background: #f8f9fa;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.contract-table >>> .el-table__row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 表格单元格样式 */\r\n.title-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.title-cell i {\r\n  color: #667eea;\r\n  font-size: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.time-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.time-cell i {\r\n  color: #909399;\r\n}\r\n\r\n.price-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  color: #e6a23c;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-text i {\r\n  color: #e6a23c;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  border-radius: 6px;\r\n  font-size: 12px;\r\n  padding: 6px 12px;\r\n  border-width: 1px;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close {\r\n  color: white;\r\n  font-size: 20px;\r\n}\r\n\r\n.preview-dialog >>> .el-dialog__close:hover {\r\n  color: #f0f0f0;\r\n}\r\n\r\n.preview-content {\r\n  padding: 24px;\r\n}\r\n\r\n.preview-header {\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  font-size: 20px;\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.preview-meta {\r\n  display: flex;\r\n  gap: 24px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.meta-item i {\r\n  color: #667eea;\r\n}\r\n\r\n.preview-body {\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #667eea;\r\n}\r\n\r\n.file-preview {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  background: white;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-info i {\r\n  color: #667eea;\r\n  font-size: 18px;\r\n}\r\n\r\n.content-preview h4 {\r\n  color: #2c3e50;\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.content-html {\r\n  background: white;\r\n  padding: 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .contract-list-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .search-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-left {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .preview-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 表单对话框样式 */\r\n.form-dialog >>> .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 20px 24px;\r\n  margin: 0;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__close {\r\n  color: white !important;\r\n  font-size: 20px !important;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.form-dialog >>> .el-dialog__footer {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n\r\n\r\n/* 兼容旧样式 */\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}