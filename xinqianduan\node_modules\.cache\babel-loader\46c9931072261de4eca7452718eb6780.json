{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\yonghu\\chat.vue", "mtime": 1748446032426}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\babel.config.js", "mtime": 1748336479385}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748336482899}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_this", "emojiData", "audioplay", "name", "components", "data", "userss", "lvshiss", "yuangongss", "table", "gridData", "ruleForm", "dialogFormVisible", "currentTab", "selectId", "activeName", "search", "active", "imgUlr", "yon_id", "id", "isShowSeach", "type", "lists", "la", "Names", "isShowPopup", "textContent", "lv<PERSON><PERSON>", "pic_path", "file_path", "list", "timer", "users", "quns", "quliaoIndex", "quli<PERSON><PERSON>", "isEmji", "title", "yuanshiquns", "yuanshiusers", "methods", "editData", "getInfo", "desc", "handleSucces1s", "res", "code", "$message", "success", "url", "error", "msg", "getRequest", "then", "resp", "message", "quanyuan", "postRequest", "lvshis", "yuangongs", "saveData", "$refs", "validate", "valid", "getData", "<PERSON><PERSON><PERSON>", "uid", "showDaiban", "is_daiban", "getList", "changeKeyword", "e", "target", "value", "length", "filter", "toLowerCase", "includes", "daiban", "openEmji", "console", "log", "changeFile", "field", "openFile", "window", "open", "openImg", "img", "beforeUpload", "file", "split", "showClose", "handleSuccess", "sendImg", "handleSuccess1", "flie", "sendFile", "redSession", "index", "changeQun", "count", "<PERSON><PERSON><PERSON><PERSON>", "item", "change", "del", "handleScroll", "scrollTop", "send", "sendMessage", "scrollHeight", "loading", "lvshi_id", "yuangong_id", "qun_id", "setTimeout", "getMoreList", "getQun1", "push", "content", "orther_id", "direction", "files", "chatAllList", "getQun", "keyupSubmit", "document", "onkeydown", "_key", "event", "keyCode", "delImage", "fileName", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "mounted", "sessionStorage", "getItem", "setInterval"], "sources": ["src/views/pages/yonghu/chat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chat-container\" @click=\"isEmji = false\">\r\n    <div class=\"chat-content\">\r\n      <!-- 左侧联系人列表 -->\r\n      <div class=\"contact-sidebar\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-section\">\r\n          <div class=\"search-input-wrapper\">\r\n            <i class=\"el-icon-search search-icon\"></i>\r\n            <input\r\n              v-model=\"search\"\r\n              type=\"text\"\r\n              class=\"search-input\"\r\n              placeholder=\"搜索联系人或群聊\"\r\n              @input=\"changeKeyword\"\r\n            />\r\n            <el-tooltip content=\"清除搜索\" placement=\"top\" effect=\"dark\" v-if=\"isShowSeach\">\r\n              <i\r\n                class=\"el-icon-close clear-icon\"\r\n                @click=\"del\"\r\n              ></i>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 标签切换 -->\r\n        <div class=\"tab-section\">\r\n          <el-button \r\n            type=\"primary\" \r\n            size=\"small\" \r\n            :class=\"{ 'active-tab': currentTab === 'group' }\"\r\n            @click=\"showDaiban('2')\"\r\n          >\r\n            <i class=\"el-icon-s-custom\"></i> 群聊\r\n          </el-button>\r\n          <el-button \r\n            type=\"success\" \r\n            size=\"small\"\r\n            :class=\"{ 'active-tab': currentTab === 'todo' }\"\r\n            @click=\"showDaiban('1')\"\r\n          >\r\n            <i class=\"el-icon-s-order\"></i> 代办\r\n          </el-button>\r\n        </div>\r\n\r\n        <!-- 联系人列表 -->\r\n        <div class=\"contact-list\">\r\n          <!-- 群聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in quns\"\r\n            :key=\"'qun' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === quliaoIndex && selectId === -1 }\"\r\n            @click=\"changeQun(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <span v-if=\"item.count > 0\" class=\"unread-badge\">{{ item.count }}</span>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.create_time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.desc }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 私聊列表 -->\r\n          <div\r\n            v-for=\"(item, index) in users\"\r\n            :key=\"'user' + index\"\r\n            class=\"contact-item\"\r\n            :class=\"{ 'active': index === selectId && quliaoIndex === -1 }\"\r\n            @click=\"redSession(index)\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <img class=\"avatar\" :src=\"item.pic_path\" />\r\n              <div class=\"online-status\"></div>\r\n            </div>\r\n            <div class=\"contact-info\">\r\n              <div class=\"contact-header\">\r\n                <h4 class=\"contact-name\">{{ item.title }}</h4>\r\n                <span class=\"contact-time\">{{ item.time }}</span>\r\n              </div>\r\n              <p class=\"last-message\">{{ item.content }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧聊天区域 -->\r\n      <div class=\"chat-main\">\r\n        <!-- 聊天头部 -->\r\n        <div class=\"chat-header\">\r\n          <div class=\"chat-title\">\r\n            <h3>{{ title }}</h3>\r\n          </div>\r\n          <div class=\"chat-actions\">\r\n            <el-tooltip content=\"查看群成员\" placement=\"bottom\" effect=\"dark\">\r\n              <el-button \r\n                type=\"text\" \r\n                icon=\"el-icon-more\" \r\n                @click=\"quanyuan\"\r\n                class=\"more-btn\"\r\n              ></el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 消息列表 -->\r\n        <div ref=\"list\" class=\"message-list\" @scroll=\"handleScroll()\">\r\n          <div class=\"message-item\" v-for=\"(item, index) in list\" :key=\"index\">\r\n            <!-- 时间分隔线 -->\r\n            <div class=\"time-divider\">\r\n              <span class=\"time-text\">{{ item.create_time }}</span>\r\n            </div>\r\n            \r\n            <!-- 消息内容 -->\r\n            <div class=\"message-wrapper\" :class=\"{ 'own-message': item.yuangong_id == yon_id }\">\r\n              <div class=\"message-avatar\">\r\n                <img :src=\"item.avatar\" />\r\n              </div>\r\n              <div class=\"message-content\">\r\n                <div class=\"sender-name\">{{ item.title }}</div>\r\n                <div class=\"message-bubble\">\r\n                  <!-- 图片消息 -->\r\n                  <div class=\"image-message\" v-if=\"item.type == 'image'\">\r\n                    <img :src=\"item.content\" @click=\"openImg(item.content)\" />\r\n                  </div>\r\n                  <!-- 文字消息 -->\r\n                  <div class=\"text-message\" v-if=\"item.type == 'text'\">\r\n                    {{ item.content }}\r\n                  </div>\r\n                  <!-- 语音消息 -->\r\n                  <div class=\"voice-message\" v-if=\"item.type == 'voice'\">\r\n                    <div class=\"voice-content\">\r\n                      <audioplay :recordFile=\"item.content\"></audioplay>\r\n                      <span class=\"voice-duration\">{{ item.datas }}</span>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 文件消息 -->\r\n                  <div class=\"file-message\" v-if=\"item.type == 'file'\">\r\n                    <div class=\"file-content\" @click=\"openFile(item.content)\">\r\n                      <div class=\"file-icon\">\r\n                        <i class=\"el-icon-document\"></i>\r\n                      </div>\r\n                      <div class=\"file-info\">\r\n                        <div class=\"file-name\">{{ item.files.name }}</div>\r\n                        <div class=\"file-size\">{{ item.files.size }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 群成员侧边栏 -->\r\n        <div class=\"member-sidebar\" :class=\"{ 'show': la }\">\r\n          <div class=\"member-content\">\r\n            <!-- 用户列表 -->\r\n            <div class=\"member-section\" v-if=\"userss.length\">\r\n              <div class=\"section-title\">用户</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-item\" v-for=\"(item, index) in userss\" :key=\"'user-' + index\">\r\n                  <div v-for=\"(value, key) in item.list\" :key=\"'user-item-' + key\" class=\"member-card\">\r\n                    <img :src=\"value.headimg\" class=\"member-avatar\"/>\r\n                    <div class=\"member-name\">{{ value.nickname }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 员工列表 -->\r\n            <div class=\"member-section\" v-for=\"(item, index) in yuangongss\" :key=\"'staff-' + index\">\r\n              <div class=\"section-title\">{{ item.zhiwei }}</div>\r\n              <div class=\"member-list\">\r\n                <div class=\"member-card\" v-for=\"(value, key) in item.list\" :key=\"'staff-item-' + key\">\r\n                  <img :src=\"value.pic_path\" class=\"member-avatar\"/>\r\n                  <div class=\"member-name\">{{ value.title }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 输入区域 -->\r\n        <div class=\"input-section\">\r\n          <!-- 工具栏 -->\r\n          <div class=\"toolbar\">\r\n            <!-- 表情按钮 -->\r\n            <div class=\"tool-item emoji-tool\">\r\n              <el-tooltip content=\"发送表情\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-sunny\" \r\n                  @click.stop=\"openEmji\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <!-- 表情面板 -->\r\n              <div class=\"emoji-panel\" v-show=\"isEmji\">\r\n                <div class=\"emoji-grid\">\r\n                  <div\r\n                    class=\"emoji-item\"\r\n                    v-for=\"(item, index) in emojiData\"\r\n                    :key=\"index\"\r\n                    @click=\"getEmoji(item)\"\r\n                  >\r\n                    {{ item }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 图片上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送图片\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadImage\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-picture\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 文件上传 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"发送文件\" placement=\"top\" effect=\"dark\">\r\n                <el-upload\r\n                  action=\"/admin/Upload/uploadFile\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleSuccess1\"\r\n                  :before-upload=\"beforeUpload\"\r\n                >\r\n                  <el-button type=\"text\" icon=\"el-icon-folder\" class=\"tool-btn\"></el-button>\r\n                </el-upload>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 代办 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"标记代办\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-s-order\" \r\n                  @click=\"daiban\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n\r\n            <!-- 工单 -->\r\n            <div class=\"tool-item\">\r\n              <el-tooltip content=\"查看工单\" placement=\"top\" effect=\"dark\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  icon=\"el-icon-tickets\" \r\n                  @click=\"showgongdan\"\r\n                  class=\"tool-btn\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 输入框 -->\r\n          <div class=\"input-wrapper\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"3\"\r\n              placeholder=\"输入消息...\"\r\n              v-model=\"textContent\"\r\n              class=\"message-input\"\r\n              resize=\"none\"\r\n            ></el-input>\r\n          </div>\r\n\r\n          <!-- 发送按钮 -->\r\n          <div class=\"send-section\">\r\n            <el-tooltip content=\"发送消息 (Enter)\" placement=\"top\" effect=\"dark\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"send\"\r\n                :disabled=\"!textContent.trim()\"\r\n                class=\"send-btn\"\r\n              >\r\n                <i class=\"el-icon-position\"></i> 发送\r\n              </el-button>\r\n            </el-tooltip>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 图片预览弹窗 -->\r\n    <el-dialog\r\n      title=\"图片预览\"\r\n      :visible.sync=\"isShowPopup\"\r\n      width=\"60%\"\r\n      center\r\n    >\r\n      <div class=\"image-preview\">\r\n        <img :src=\"imgUlr\" alt=\"预览图片\" />\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 工单抽屉 -->\r\n    <el-drawer\r\n      title=\"客户工单\"\r\n      :visible.sync=\"table\"\r\n      direction=\"rtl\"\r\n      size=\"40%\"\r\n    >\r\n      <el-table :data=\"gridData\" style=\"width: 100%\">\r\n        <el-table-column\r\n          property=\"create_time\"\r\n          label=\"下单日期\"\r\n          width=\"150\"\r\n        ></el-table-column>\r\n        <el-table-column property=\"title\" label=\"需求标题\"></el-table-column>\r\n        <el-table-column property=\"desc\" label=\"需求描述\"></el-table-column>\r\n        <el-table-column\r\n          property=\"type_title\"\r\n          label=\"下单类型\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          property=\"is_deal_title\"\r\n          label=\"状态\"\r\n        ></el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >完成制作</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-drawer>\r\n\r\n    <!-- 工单编辑对话框 -->\r\n    <el-dialog title=\"工单详情\" :visible.sync=\"dialogFormVisible\" width=\"50%\">\r\n      <el-form :model=\"ruleForm\" ref=\"ruleForm\">\r\n        <el-form-item label=\"工单类型\">\r\n          <el-input\r\n            v-model=\"ruleForm.type_title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单标题\">\r\n          <el-input\r\n            v-model=\"ruleForm.title\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"工单描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            readonly\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作状态\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"2\">已完成</el-radio>\r\n            <el-radio v-model=\"ruleForm.is_deal\" :label=\"1\">处理中</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type == 2\"\r\n          label=\"请上传文件\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess1\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"内容回复\"\r\n          v-if=\"ruleForm.is_deal == 2 && ruleForm.type != 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.content\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet _this;\r\nimport emojiData from \"./emoji.js\";\r\nimport audioplay from \"/src/components/audioplay.vue\";\r\nexport default {\r\n  name: \"chat\",\r\n  components: { audioplay },\r\n  data() {\r\n    return {\r\n      userss: [],\r\n      lvshiss: [],\r\n      yuangongss: [],\r\n      table: false,\r\n      gridData: \"\",\r\n      ruleForm: \"\",\r\n      dialogFormVisible: false,\r\n      emojiData: emojiData,\r\n      currentTab: 'group',\r\n      selectId: 1,\r\n      activeName: \"first\",\r\n      search: \"\",\r\n      active: false,\r\n      imgUlr: \"\",\r\n      yon_id: 0,\r\n      id: 0,\r\n      isShowSeach: false,\r\n      type: \"\",\r\n      lists: [],\r\n      la: false,\r\n      Names: \"\",\r\n      isShowPopup: false,\r\n      textContent: \"\",\r\n      selectId: 0,\r\n      lvshiid: \"4\",\r\n      pic_path: \"\",\r\n      file_path: \"\",\r\n      list: [],\r\n      timer: \"\",\r\n      users: [],\r\n      quns: [],\r\n      quliaoIndex: 0,\r\n      quliaos: [],\r\n      isEmji: false,\r\n      title: \"\",\r\n      yuanshiquns: [],\r\n      yuanshiusers: [],\r\n    };\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    handleSucces1s(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[\"file_path\"] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      this.getRequest(\"/gongdan/read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          _this.dialogFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    quanyuan() {\r\n      _this.la = !_this.la\r\n      _this.postRequest(\"/chat/getQunMoreInfo\", { id:_this.id }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.userss = resp.data.users\r\n          _this.lvshiss = resp.data.lvshis\r\n          _this.yuangongss = resp.data.yuangongs\r\n        }\r\n      });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(\"/gongdan/save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    showgongdan() {\r\n      let uid = this.quns[this.quliaoIndex][\"uid\"];\r\n      _this.table = true;\r\n      _this.postRequest(\"/chat/gongdanList\", { uid: uid }).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.gridData = resp.data;\r\n        }\r\n      });\r\n    },\r\n    showDaiban(is_daiban) {\r\n      this.currentTab = is_daiban === '2' ? 'group' : 'todo';\r\n      _this\r\n        .postRequest(\"/chat/getQun\", { is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns = resp.data;\r\n            _this.yuanshiquns = resp.data;\r\n            _this.selectId = -1;\r\n            _this.getList();\r\n          }\r\n        });\r\n    },\r\n    changeKeyword(e) {\r\n      let quns = _this.yuanshiquns;\r\n      let users = _this.yuanshiusers;\r\n      let search = e.target.value;\r\n      \r\n      this.isShowSeach = search.length > 0;\r\n\r\n      _this.quns = quns.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n      _this.users = users.filter((data) => data.title.toLowerCase().includes(search.toLowerCase()));\r\n    },\r\n    daiban() {\r\n      let id = this.quns[this.quliaoIndex][\"id\"];\r\n      let is_daiban = this.quns[this.quliaoIndex][\"is_daiban\"] == 1 ? 2 : 1;\r\n      _this\r\n        .postRequest(\"/chat/daiban\", { id: id, is_daiban: is_daiban })\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.quns[this.quliaoIndex][\"is_daiban\"] = is_daiban;\r\n            _this.$message.success(resp.msg);\r\n          } else {\r\n            _this.$message.error(resp.msg);\r\n          }\r\n        });\r\n    },\r\n    openEmji() {\r\n      this.isEmji = !this.isEmji;\r\n      console.log(\"----------------------ww2w\");\r\n    },\r\n    changeFile(field) {\r\n      this.type = field;\r\n    },\r\n    openFile(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    openImg(img) {\r\n      this.imgUlr = img;\r\n      this.isShowPopup = true;\r\n      console.log(\"----------\", img);\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      console.log(type, \"type\");\r\n      if (\r\n        !file.type.split(\"/\")[1] == \"doc\" ||\r\n        !file.type.split(\"/\")[1] == \"docx\" ||\r\n        !file.type.split(\"/\")[1] == \"xls\" ||\r\n        !file.type.split(\"/\")[1] == \"ppt\" ||\r\n        !file.type.split(\"/\")[1] == \"pdf\" ||\r\n        !file.type.split(\"/\")[1] == \"xlsx\" ||\r\n        !file.type.split(\"/\")[1] == \"pptx\"\r\n      ) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc,docx, xls, ppt, pdf, docx, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n    handleSuccess(res) {\r\n      let _this = this;\r\n      console.log(res);\r\n      if (res.code == 200) {\r\n        _this.sendImg(res.data.url);\r\n      } else {\r\n        _this.$message.error(res.msg);\r\n      }\r\n    },\r\n    handleSuccess1(res, flie) {\r\n      if (res.code == 200) {\r\n        _this.sendFile(res.data.url, flie);\r\n      } else {\r\n        this.$message({\r\n          showClose: true,\r\n          message: \"请选择'doc, xls, ppt, pdf, xlsx, pptx'文件\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n    },\r\n    redSession(index) {\r\n      this.selectId = index;\r\n      this.quliaoIndex = -1;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    changeQun(index) {\r\n      this.selectId = -1;\r\n      this.quliaoIndex = index;\r\n      _this.quns[index].count = 0;\r\n      _this.la = false\r\n      _this.getList();\r\n    },\r\n    getEmoji(item) {\r\n      this.textContent += item;\r\n    },\r\n    change(e) {\r\n      if (this.search) this.isShowSeach = true;\r\n      else this.isShowSeach = false;\r\n    },\r\n    del() {\r\n      this.search = \"\";\r\n      this.isShowSeach = false;\r\n    },\r\n    handleScroll(e) {\r\n      if (this.$refs.list.scrollTop == 0) {\r\n        console.log(\"这里处理加载更多\");\r\n      }\r\n    },\r\n    send() {\r\n      _this.sendMessage(_this.textContent);\r\n      _this.textContent = \"\";\r\n    },\r\n    getList() {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n        \r\n        _this.postRequest(\"/chat/chatList\", { uid: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            }\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      } else {\r\n        let id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].uid.length * 1 +\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1;\r\n        _this.id = id;\r\n        console.log(_this.id)\r\n        \r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        _this.postRequest(\"/chat/qunliaoList\", { qun_id: id }).then((resp) => {\r\n          if (resp.code == 200) {\r\n            if (resp.data.length > 0) {\r\n              _this.list = resp.data;\r\n              _this.$refs.list.scrollTop = _this.$refs.list.scrollHeight;\r\n            } else {\r\n              _this.list = [];\r\n            }\r\n            \r\n            setTimeout(\r\n              () => (this.$refs.list.scrollTop = this.$refs.list.scrollHeight),\r\n              0\r\n            );\r\n          }\r\n          _this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    getMoreList() {\r\n      if (_this.selectId != -1) {\r\n        let uid = _this.users[_this.selectId].id;\r\n        _this.title = _this.users[_this.selectId].title;\r\n\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { uid: uid, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                if (resp.data.length > 0) {\r\n                  _this.list.push(resp.data);\r\n                  setTimeout(\r\n                    () =>\r\n                      (this.$refs.list.scrollTop =\r\n                        this.$refs.list.scrollHeight),\r\n                    1000\r\n                  );\r\n                }\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        let count =\r\n          _this.quns[_this.quliaoIndex].lvshi_id.length * 1 +\r\n          _this.quns[_this.quliaoIndex].yuangong_id.length * 1 +\r\n          1;\r\n\r\n        _this.title = _this.quns[_this.quliaoIndex].title + \"(\" + count + \")\";\r\n        let id = 0;\r\n        if (_this.list.length > 0) {\r\n          id = _this.list[_this.list.length - 1].id;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n\r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }else{\r\n          id = 1;\r\n          _this\r\n            .postRequest(\"/chat/getMoreQunList\", { qun_id: qun_id, id: id })\r\n            .then((resp) => {\r\n              _this.getQun1();\r\n              if (resp.code == 200) {\r\n                _this.list.push(resp.data);\r\n              \r\n                setTimeout(\r\n                  () =>\r\n                    (_this.$refs.list.scrollTop =\r\n                      _this.$refs.list.scrollHeight),\r\n                  1000\r\n                );\r\n              }\r\n              _this.loading = false;\r\n            });\r\n        }\r\n      }\r\n    },\r\n    sendMessage(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"text\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendFile(content, files) {\r\n      if (_this.selectId != -1) {\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            direction: \"left\",\r\n            type: \"file\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n            files: files,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    sendImg(content) {\r\n      if (_this.selectId != -1) {\r\n        let id = _this.users[_this.selectId].id;\r\n        let orther_id = 3;\r\n        _this\r\n          .postRequest(\"/chat/sendMessage\", {\r\n            uid: id,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            orther_id: orther_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      } else {\r\n        let uid = _this.quns[_this.quliaoIndex].uid;\r\n        let qun_id = _this.quns[_this.quliaoIndex].id;\r\n        _this\r\n          .postRequest(\"/chat/sendQunMessage\", {\r\n            uid: uid,\r\n            direction: \"left\",\r\n            type: \"image\",\r\n            content: content,\r\n            qun_id: qun_id,\r\n          })\r\n          .then((resp) => {\r\n            if (resp.code != 200) {\r\n              _this.$message.error(resp.msg);\r\n            }\r\n          });\r\n      }\r\n    },\r\n    chatAllList() {\r\n      _this.postRequest(\"/chat/chatAllList\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.users = resp.data;\r\n          _this.yuanshiusers = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getQun() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n          setTimeout(() => {\r\n            _this.getList();\r\n          }, 1500);\r\n        }\r\n      });\r\n    },\r\n    getQun1() {\r\n      _this.postRequest(\"/chat/getQun\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.quns = resp.data;\r\n          _this.yuanshiquns = resp.data;\r\n          _this.selectId = -1;\r\n        }\r\n      });\r\n    },\r\n    keyupSubmit() {\r\n      let _this = this;\r\n\r\n      document.onkeydown = (e) => {\r\n        let _key = window.event.keyCode;\r\n\r\n        if (_key === 13) {\r\n          _this.send();\r\n        }\r\n      };\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    console.log(\"离开乐\");\r\n    clearInterval(this.timer);\r\n  },\r\n  mounted() {\r\n    _this = this;\r\n    _this.getQun();\r\n    _this.chatAllList();\r\n    _this.yon_id = window.sessionStorage.getItem(\"spbs\");\r\n    _this.timer = setInterval(() => {\r\n      _this.getMoreList();\r\n    }, 1500);\r\n    _this.keyupSubmit();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chat-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 左侧联系人列表 */\r\n.contact-sidebar {\r\n  width: 320px;\r\n  background: #f8f9fa;\r\n  border-right: 2px solid #e9ecef;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-section {\r\n  padding: 20px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 25px;\r\n  padding: 0 15px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:focus-within {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  color: #6c757d;\r\n  margin-right: 10px;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  border: none;\r\n  outline: none;\r\n  padding: 12px 0;\r\n  font-size: 14px;\r\n  background: transparent;\r\n\r\n  &::placeholder {\r\n    color: #adb5bd;\r\n  }\r\n}\r\n\r\n.clear-icon {\r\n  color: #6c757d;\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n.tab-section {\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #f8f9fa;\r\n\r\n  .el-button {\r\n    flex: 1;\r\n    border-radius: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.active-tab {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n.contact-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-left: 3px solid transparent;\r\n  margin: 2px 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: #e9ecef;\r\n    transform: translateX(4px);\r\n  }\r\n\r\n  &.active {\r\n    background: #e3f2fd;\r\n    border-left-color: #2196f3;\r\n    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);\r\n  }\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #ffffff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: #ff4757;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.online-status {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 2px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: #2ed573;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.contact-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.contact-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.contact-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.contact-time {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.last-message {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右侧聊天区域 */\r\n.chat-main {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #ffffff;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 2px solid #e9ecef;\r\n  background: #ffffff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n}\r\n\r\n.chat-title h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #212529;\r\n}\r\n\r\n.more-btn {\r\n  font-size: 18px;\r\n  color: #6c757d;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    color: #495057;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: #fafbfc;\r\n  border-top: 1px solid #f0f0f0;\r\n\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background: #dee2e6;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.message-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.time-divider {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n}\r\n\r\n.time-text {\r\n  background: #ffffff;\r\n  color: #6c757d;\r\n  padding: 6px 16px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e9ecef;\r\n  display: inline-block;\r\n}\r\n\r\n.message-wrapper {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n\r\n  &.own-message {\r\n    flex-direction: row-reverse;\r\n\r\n    .message-content {\r\n      align-items: flex-end;\r\n    }\r\n\r\n    .sender-name {\r\n      text-align: right;\r\n    }\r\n\r\n    .message-bubble {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      &::before {\r\n        right: -8px;\r\n        left: auto;\r\n        border-left: 8px solid #667eea;\r\n        border-right: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.message-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.message-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  max-width: 70%;\r\n}\r\n\r\n.sender-name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.message-bubble {\r\n  position: relative;\r\n  background: #ffffff;\r\n  border-radius: 18px;\r\n  padding: 12px 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  word-wrap: break-word;\r\n  border: 1px solid #f0f0f0;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: -8px;\r\n    top: 12px;\r\n    border: 8px solid transparent;\r\n    border-right: 8px solid #ffffff;\r\n  }\r\n}\r\n\r\n.text-message {\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.image-message img {\r\n  max-width: 200px;\r\n  max-height: 200px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.voice-message {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.voice-duration {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n.file-message {\r\n  min-width: 200px;\r\n}\r\n\r\n.file-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #e3f2fd;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #2196f3;\r\n  font-size: 20px;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #212529;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 群成员侧边栏 */\r\n.member-sidebar {\r\n  position: absolute;\r\n  top: 0;\r\n  right: -300px;\r\n  width: 300px;\r\n  height: 100%;\r\n  background: #ffffff;\r\n  border-left: 1px solid #e9ecef;\r\n  transition: right 0.3s ease;\r\n  z-index: 10;\r\n\r\n  &.show {\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.member-content {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.member-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.member-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n.member-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 8px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.member-name {\r\n  font-size: 12px;\r\n  color: #495057;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 输入区域 */\r\n.input-section {\r\n  background: #ffffff;\r\n  border-top: 2px solid #e9ecef;\r\n  padding: 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n  z-index: 5;\r\n  position: relative;\r\n}\r\n\r\n.toolbar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tool-item {\r\n  position: relative;\r\n}\r\n\r\n.tool-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background: #f8f9fa;\r\n    color: #495057;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n.emoji-tool {\r\n  .emoji-panel {\r\n    position: absolute;\r\n    bottom: 50px;\r\n    left: 0;\r\n    width: 320px;\r\n    height: 200px;\r\n    background: #ffffff;\r\n    border: 1px solid #e9ecef;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .emoji-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(8, 1fr);\r\n    gap: 5px;\r\n    padding: 15px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .emoji-item {\r\n    width: 30px;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    border-radius: 6px;\r\n    transition: background 0.3s ease;\r\n    font-size: 18px;\r\n\r\n    &:hover {\r\n      background: #f8f9fa;\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  margin-bottom: 15px;\r\n\r\n  .message-input {\r\n    border-radius: 12px;\r\n    \r\n    ::v-deep .el-textarea__inner {\r\n      border-radius: 12px;\r\n      border: 1px solid #e9ecef;\r\n      padding: 12px 16px;\r\n      font-size: 14px;\r\n      line-height: 1.4;\r\n      resize: none;\r\n      transition: border-color 0.3s ease;\r\n\r\n      &:focus {\r\n        border-color: #007bff;\r\n        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.send-section {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.send-btn {\r\n  border-radius: 20px;\r\n  padding: 8px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n\r\n  &:not(:disabled):hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 图片预览 */\r\n.image-preview {\r\n  text-align: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 60vh;\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .chat-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .chat-content {\r\n    height: calc(100vh - 20px);\r\n  }\r\n\r\n  .contact-sidebar {\r\n    width: 280px;\r\n  }\r\n\r\n  .member-sidebar {\r\n    width: 250px;\r\n    right: -250px;\r\n  }\r\n}\r\n\r\n/* Tooltip 自定义样式 */\r\n::v-deep .el-tooltip__popper {\r\n  background: rgba(0, 0, 0, 0.85) !important;\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  font-size: 12px !important;\r\n  padding: 8px 12px !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"top\"] .el-popper__arrow {\r\n  border-top-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n::v-deep .el-tooltip__popper[x-placement^=\"bottom\"] .el-popper__arrow {\r\n  border-bottom-color: rgba(0, 0, 0, 0.85) !important;\r\n}\r\n\r\n/* 工具按钮悬停效果增强 */\r\n.tool-btn {\r\n  position: relative;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n    transition: all 0.2s ease;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.message-item {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.member-sidebar.show {\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n</style>\r\n"], "mappings": ";AAyaA,IAAAA,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,iBAAA;MACAX,SAAA,EAAAA,SAAA;MACAY,UAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,EAAA;MACAC,WAAA;MACAC,IAAA;MACAC,KAAA;MACAC,EAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAb,QAAA;MACAc,OAAA;MACAC,QAAA;MACAC,SAAA;MACAC,IAAA;MACAC,KAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAAtB,EAAA;MACA,IAAApB,KAAA;MACA,IAAAoB,EAAA;QACA,KAAAuB,OAAA,CAAAvB,EAAA;MACA;QACA,KAAAT,QAAA;UACA2B,KAAA;UACAM,IAAA;QACA;MACA;IACA;IACAC,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAAtC,QAAA,gBAAAmC,GAAA,CAAAzC,IAAA,CAAA6C,GAAA;MACA;QACA,KAAAF,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACAT,QAAAvB,EAAA;MACA,IAAApB,KAAA;MACA,KAAAqD,UAAA,uBAAAjC,EAAA,EAAAkC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAW,QAAA,GAAA4C,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAY,iBAAA;QACA;UACAZ,KAAA,CAAAgD,QAAA;YACA1B,IAAA;YACAkC,OAAA,EAAAD,IAAA,CAAAH;UACA;QACA;MACA;IACA;IACAK,SAAA;MACAzD,KAAA,CAAAwB,EAAA,IAAAxB,KAAA,CAAAwB,EAAA;MACAxB,KAAA,CAAA0D,WAAA;QAAAtC,EAAA,EAAApB,KAAA,CAAAoB;MAAA,GAAAkC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAM,MAAA,GAAAiD,IAAA,CAAAlD,IAAA,CAAA4B,KAAA;UACAjC,KAAA,CAAAO,OAAA,GAAAgD,IAAA,CAAAlD,IAAA,CAAAsD,MAAA;UACA3D,KAAA,CAAAQ,UAAA,GAAA+C,IAAA,CAAAlD,IAAA,CAAAuD,SAAA;QACA;MACA;IACA;IACAC,SAAA;MACA,IAAA7D,KAAA;MACA,KAAA8D,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAN,WAAA,uBAAA/C,QAAA,EAAA2C,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAAgD,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;cACA,KAAAa,OAAA;cACAjE,KAAA,CAAAY,iBAAA;YACA;cACAZ,KAAA,CAAAgD,QAAA;gBACA1B,IAAA;gBACAkC,OAAA,EAAAD,IAAA,CAAAH;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAc,YAAA;MACA,IAAAC,GAAA,QAAAjC,IAAA,MAAAC,WAAA;MACAnC,KAAA,CAAAS,KAAA;MACAT,KAAA,CAAA0D,WAAA;QAAAS,GAAA,EAAAA;MAAA,GAAAb,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAU,QAAA,GAAA6C,IAAA,CAAAlD,IAAA;QACA;MACA;IACA;IACA+D,WAAAC,SAAA;MACA,KAAAxD,UAAA,GAAAwD,SAAA;MACArE,KAAA,CACA0D,WAAA;QAAAW,SAAA,EAAAA;MAAA,GACAf,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAd,KAAA,CAAAsE,OAAA;QACA;MACA;IACA;IACAC,cAAAC,CAAA;MACA,IAAAtC,IAAA,GAAAlC,KAAA,CAAAuC,WAAA;MACA,IAAAN,KAAA,GAAAjC,KAAA,CAAAwC,YAAA;MACA,IAAAxB,MAAA,GAAAwD,CAAA,CAAAC,MAAA,CAAAC,KAAA;MAEA,KAAArD,WAAA,GAAAL,MAAA,CAAA2D,MAAA;MAEA3E,KAAA,CAAAkC,IAAA,GAAAA,IAAA,CAAA0C,MAAA,CAAAvE,IAAA,IAAAA,IAAA,CAAAiC,KAAA,CAAAuC,WAAA,GAAAC,QAAA,CAAA9D,MAAA,CAAA6D,WAAA;MACA7E,KAAA,CAAAiC,KAAA,GAAAA,KAAA,CAAA2C,MAAA,CAAAvE,IAAA,IAAAA,IAAA,CAAAiC,KAAA,CAAAuC,WAAA,GAAAC,QAAA,CAAA9D,MAAA,CAAA6D,WAAA;IACA;IACAE,OAAA;MACA,IAAA3D,EAAA,QAAAc,IAAA,MAAAC,WAAA;MACA,IAAAkC,SAAA,QAAAnC,IAAA,MAAAC,WAAA;MACAnC,KAAA,CACA0D,WAAA;QAAAtC,EAAA,EAAAA,EAAA;QAAAiD,SAAA,EAAAA;MAAA,GACAf,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,MAAAC,WAAA,iBAAAkC,SAAA;UACArE,KAAA,CAAAgD,QAAA,CAAAC,OAAA,CAAAM,IAAA,CAAAH,GAAA;QACA;UACApD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;IACA4B,SAAA;MACA,KAAA3C,MAAA,SAAAA,MAAA;MACA4C,OAAA,CAAAC,GAAA;IACA;IACAC,WAAAC,KAAA;MACA,KAAA9D,IAAA,GAAA8D,KAAA;IACA;IACAC,SAAAnC,GAAA;MACAoC,MAAA,CAAAC,IAAA,CAAArC,GAAA;IACA;IACAsC,QAAAC,GAAA;MACA,KAAAvE,MAAA,GAAAuE,GAAA;MACA,KAAA/D,WAAA;MACAuD,OAAA,CAAAC,GAAA,eAAAO,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,IAAArE,IAAA,GAAAqE,IAAA,CAAArE,IAAA;MACA2D,OAAA,CAAAC,GAAA,CAAA5D,IAAA;MACA,IACA,CAAAqE,IAAA,CAAArE,IAAA,CAAAsE,KAAA,qBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,sBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,qBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,qBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,qBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,sBACA,CAAAD,IAAA,CAAArE,IAAA,CAAAsE,KAAA,oBACA;QACA,KAAA5C,QAAA;UACA6C,SAAA;UACArC,OAAA;UACAlC,IAAA;QACA;QACA;MACA;IACA;IACAwE,cAAAhD,GAAA;MACA,IAAA9C,KAAA;MACAiF,OAAA,CAAAC,GAAA,CAAApC,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA/C,KAAA,CAAA+F,OAAA,CAAAjD,GAAA,CAAAzC,IAAA,CAAA6C,GAAA;MACA;QACAlD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;MACA;IACA;IACA4C,eAAAlD,GAAA,EAAAmD,IAAA;MACA,IAAAnD,GAAA,CAAAC,IAAA;QACA/C,KAAA,CAAAkG,QAAA,CAAApD,GAAA,CAAAzC,IAAA,CAAA6C,GAAA,EAAA+C,IAAA;MACA;QACA,KAAAjD,QAAA;UACA6C,SAAA;UACArC,OAAA;UACAlC,IAAA;QACA;MACA;IACA;IACA6E,WAAAC,KAAA;MACA,KAAAtF,QAAA,GAAAsF,KAAA;MACA,KAAAjE,WAAA;MACAnC,KAAA,CAAAwB,EAAA;MACAxB,KAAA,CAAAsE,OAAA;IACA;IACA+B,UAAAD,KAAA;MACA,KAAAtF,QAAA;MACA,KAAAqB,WAAA,GAAAiE,KAAA;MACApG,KAAA,CAAAkC,IAAA,CAAAkE,KAAA,EAAAE,KAAA;MACAtG,KAAA,CAAAwB,EAAA;MACAxB,KAAA,CAAAsE,OAAA;IACA;IACAiC,SAAAC,IAAA;MACA,KAAA7E,WAAA,IAAA6E,IAAA;IACA;IACAC,OAAAjC,CAAA;MACA,SAAAxD,MAAA,OAAAK,WAAA,aACA,KAAAA,WAAA;IACA;IACAqF,IAAA;MACA,KAAA1F,MAAA;MACA,KAAAK,WAAA;IACA;IACAsF,aAAAnC,CAAA;MACA,SAAAV,KAAA,CAAA/B,IAAA,CAAA6E,SAAA;QACA3B,OAAA,CAAAC,GAAA;MACA;IACA;IACA2B,KAAA;MACA7G,KAAA,CAAA8G,WAAA,CAAA9G,KAAA,CAAA2B,WAAA;MACA3B,KAAA,CAAA2B,WAAA;IACA;IACA2C,QAAA;MACA,IAAAtE,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAwB,KAAA;QAEAtC,KAAA,CAAA0D,WAAA;UAAAS,GAAA,EAAA/C;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAsE,MAAA;cACA3E,KAAA,CAAA+B,IAAA,GAAAwB,IAAA,CAAAlD,IAAA;cAEAL,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,GAAA5G,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA;YACA;UACA;UACA/G,KAAA,CAAAgH,OAAA;QACA;MACA;QACA,IAAA5F,EAAA,GAAApB,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACA,IAAAkF,KAAA,GACAtG,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAgC,GAAA,CAAAQ,MAAA,OACA3E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAA8E,QAAA,CAAAtC,MAAA,OACA3E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAA+E,WAAA,CAAAvC,MAAA;QACA3E,KAAA,CAAAoB,EAAA,GAAAA,EAAA;QACA6D,OAAA,CAAAC,GAAA,CAAAlF,KAAA,CAAAoB,EAAA;QAEApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAG,KAAA,SAAAgE,KAAA;QACAtG,KAAA,CAAA0D,WAAA;UAAAyD,MAAA,EAAA/F;QAAA,GAAAkC,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAsE,MAAA;cACA3E,KAAA,CAAA+B,IAAA,GAAAwB,IAAA,CAAAlD,IAAA;cACAL,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,GAAA5G,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA;YACA;cACA/G,KAAA,CAAA+B,IAAA;YACA;YAEAqF,UAAA,CACA,WAAAtD,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,QAAA9C,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,CACA;UACA;UACA/G,KAAA,CAAAgH,OAAA;QACA;MACA;IACA;IACAK,YAAA;MACA,IAAArH,KAAA,CAAAc,QAAA;QACA,IAAAqD,GAAA,GAAAnE,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACApB,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAwB,KAAA;QAEA,IAAAlB,EAAA;QACA,IAAApB,KAAA,CAAA+B,IAAA,CAAA4C,MAAA;UACAvD,EAAA,GAAApB,KAAA,CAAA+B,IAAA,CAAA/B,KAAA,CAAA+B,IAAA,CAAA4C,MAAA,MAAAvD,EAAA;UACApB,KAAA,CACA0D,WAAA;YAAAS,GAAA,EAAAA,GAAA;YAAA/C,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAsH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA,IAAAQ,IAAA,CAAAlD,IAAA,CAAAsE,MAAA;gBACA3E,KAAA,CAAA+B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAlD,IAAA;gBACA+G,UAAA,CACA,MACA,KAAAtD,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,GACA,KAAA9C,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;cACA;YACA;YACA/G,KAAA,CAAAgH,OAAA;UACA;QACA;MACA;QACA,IAAAG,MAAA,GAAAnH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACA,IAAAkF,KAAA,GACAtG,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAA8E,QAAA,CAAAtC,MAAA,OACA3E,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAA+E,WAAA,CAAAvC,MAAA,OACA;QAEA3E,KAAA,CAAAsC,KAAA,GAAAtC,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAG,KAAA,SAAAgE,KAAA;QACA,IAAAlF,EAAA;QACA,IAAApB,KAAA,CAAA+B,IAAA,CAAA4C,MAAA;UACAvD,EAAA,GAAApB,KAAA,CAAA+B,IAAA,CAAA/B,KAAA,CAAA+B,IAAA,CAAA4C,MAAA,MAAAvD,EAAA;UACApB,KAAA,CACA0D,WAAA;YAAAyD,MAAA,EAAAA,MAAA;YAAA/F,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAsH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAA+B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAlD,IAAA;cAEA+G,UAAA,CACA,MACApH,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,GACA5G,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;YACA;YACA/G,KAAA,CAAAgH,OAAA;UACA;QACA;UACA5F,EAAA;UACApB,KAAA,CACA0D,WAAA;YAAAyD,MAAA,EAAAA,MAAA;YAAA/F,EAAA,EAAAA;UAAA,GACAkC,IAAA,CAAAC,IAAA;YACAvD,KAAA,CAAAsH,OAAA;YACA,IAAA/D,IAAA,CAAAR,IAAA;cACA/C,KAAA,CAAA+B,IAAA,CAAAwF,IAAA,CAAAhE,IAAA,CAAAlD,IAAA;cAEA+G,UAAA,CACA,MACApH,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAA6E,SAAA,GACA5G,KAAA,CAAA8D,KAAA,CAAA/B,IAAA,CAAAgF,YAAA,EACA,IACA;YACA;YACA/G,KAAA,CAAAgH,OAAA;UACA;QACA;MACA;IACA;IACAF,YAAAU,OAAA;MACA,IAAAxH,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAqG,SAAA;QACAzH,KAAA,CACA0D,WAAA;UACAS,GAAA,EAAA/C,EAAA;UACAsG,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAnE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAe,GAAA,GAAAnE,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAgC,GAAA;QACA,IAAAgD,MAAA,GAAAnH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACA0D,WAAA;UACAgE,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA7D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA8C,SAAAsB,OAAA,EAAAG,KAAA;MACA,IAAA3H,KAAA,CAAAc,QAAA;QACA,IAAA2G,SAAA;QACAzH,KAAA,CACA0D,WAAA;UACAgE,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA,SAAA;UACAE,KAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAA+D,MAAA,GAAAnH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACA0D,WAAA;UACAgE,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA,MAAA;UACAQ,KAAA,EAAAA;QACA,GACArE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACA2C,QAAAyB,OAAA;MACA,IAAAxH,KAAA,CAAAc,QAAA;QACA,IAAAM,EAAA,GAAApB,KAAA,CAAAiC,KAAA,CAAAjC,KAAA,CAAAc,QAAA,EAAAM,EAAA;QACA,IAAAqG,SAAA;QACAzH,KAAA,CACA0D,WAAA;UACAS,GAAA,EAAA/C,EAAA;UACAsG,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAC,SAAA,EAAAA;QACA,GACAnE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;QACA,IAAAe,GAAA,GAAAnE,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAgC,GAAA;QACA,IAAAgD,MAAA,GAAAnH,KAAA,CAAAkC,IAAA,CAAAlC,KAAA,CAAAmC,WAAA,EAAAf,EAAA;QACApB,KAAA,CACA0D,WAAA;UACAS,GAAA,EAAAA,GAAA;UACAuD,SAAA;UACApG,IAAA;UACAkG,OAAA,EAAAA,OAAA;UACAL,MAAA,EAAAA;QACA,GACA7D,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAR,IAAA;YACA/C,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;UACA;QACA;MACA;IACA;IACAwE,YAAA;MACA5H,KAAA,CAAA0D,WAAA,sBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAiC,KAAA,GAAAsB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAwC,YAAA,GAAAe,IAAA,CAAAlD,IAAA;QACA;MACA;IACA;IACAwH,OAAA;MACA7H,KAAA,CAAA0D,WAAA,iBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;UACAsG,UAAA;YACApH,KAAA,CAAAsE,OAAA;UACA;QACA;MACA;IACA;IACAgD,QAAA;MACAtH,KAAA,CAAA0D,WAAA,iBAAAJ,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAkC,IAAA,GAAAqB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAuC,WAAA,GAAAgB,IAAA,CAAAlD,IAAA;UACAL,KAAA,CAAAc,QAAA;QACA;MACA;IACA;IACAgH,YAAA;MACA,IAAA9H,KAAA;MAEA+H,QAAA,CAAAC,SAAA,GAAAxD,CAAA;QACA,IAAAyD,IAAA,GAAA3C,MAAA,CAAA4C,KAAA,CAAAC,OAAA;QAEA,IAAAF,IAAA;UACAjI,KAAA,CAAA6G,IAAA;QACA;MACA;IACA;IACAuB,SAAAzC,IAAA,EAAA0C,QAAA;MACA,IAAArI,KAAA;MACAA,KAAA,CAAAqD,UAAA,gCAAAsC,IAAA,EAAArC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAR,IAAA;UACA/C,KAAA,CAAAW,QAAA,CAAA0H,QAAA;UAEArI,KAAA,CAAAgD,QAAA,CAAAC,OAAA;QACA;UACAjD,KAAA,CAAAgD,QAAA,CAAAG,KAAA,CAAAI,IAAA,CAAAH,GAAA;QACA;MACA;IACA;EACA;EACAkF,cAAA;IACArD,OAAA,CAAAC,GAAA;IACAqD,aAAA,MAAAvG,KAAA;EACA;EACAwG,QAAA;IACAxI,KAAA;IACAA,KAAA,CAAA6H,MAAA;IACA7H,KAAA,CAAA4H,WAAA;IACA5H,KAAA,CAAAmB,MAAA,GAAAmE,MAAA,CAAAmD,cAAA,CAAAC,OAAA;IACA1I,KAAA,CAAAgC,KAAA,GAAA2G,WAAA;MACA3I,KAAA,CAAAqH,WAAA;IACA;IACArH,KAAA,CAAA8H,WAAA;EACA;AACA", "ignoreList": []}]}