<?php
namespace app\admin\controller;
use think\facade\Request;
use think\Controller;
use untils\{JsonService,JwtAuth};
class Base extends Controller{
    protected $userid;

    public function __construct(){
        parent::__construct();

        // 临时禁用认证检查，用于调试
        $this->userid = 1; // 设置默认用户ID

        /*
        $info = Request::header();
        $token = empty($info['login-token']) ? Request::get('token') : $info['login-token'];
        if(empty($token)) {
            echo JsonService::fail('请登录');
            exit;
        }

        $res = JwtAuth::getInfoByToken($token);
        if(empty($res)) {
            echo JsonService::fail('请登录');
            exit;
        }

        $this->userid=$res->admin_id;
        */
    }
}
