<?php
namespace app\admin\controller;
use think\facade\Request;
use think\Controller;
use untils\{JsonService,JwtAuth};
class Base extends Controller{
    protected $userid;

    public function __construct(){

        $info = Request::header();
        $token = empty($info['login-token']) ? Request::get('token') : $info['login-token'];
        if(empty($token)) return JsonService::fail('请登录');

        $res = JwtAuth::getInfoByToken($token);
        if(empty($res)) return JsonService::fail('请登录');

        $this->userid=$res->admin_id;

    }
}
