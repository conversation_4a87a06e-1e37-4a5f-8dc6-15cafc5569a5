<?php
namespace app\admin\controller;

use think\facade\Db;
use think\Request;
use untils\{JsonService};

class Upload{

    public function uploadFile(){
     
      $upload = \EasyUpload\EasyUpload::Instance();
      $res = $upload->fileUpload('file');
     
      if(!empty($res['status'])){
        return JsonService::successful("上传成功",['url'=>$res['success']]);
      }else{
        return JsonService::fail($res['error']);
      }

    }
    public function uploadImage(){
     
      $upload = \EasyUpload\EasyUpload::Instance();
      $res = $upload->imgUpload('file');
      if(!empty($res['status'])){
        return JsonService::successful("上传成功",['url'=>$res['success']]);
      }else{
        return JsonService::fail($res['error']);
      }

    }

    public function delImage($fileName=''){
      if(!file_exists(".".$fileName)){
        return JsonService::fail("删除失败!错误原因:文件不存在");
      }
      $res = unlink(".".$fileName);
      if(!empty($res)) return JsonService::successful("删除成功");
      else return JsonService::fail("删除失败");
    }

    public function download($file_path,$file_name)
    {
      // download是系统封装的一个助手函数
      if(file_exists(".".$file_path))
      {
           
        $download =  new \think\response\Download(".".$file_path);
        return $download->name($file_name);
      }else{
          echo '文件不存在';
      }
       
    }
    public function updateWang(){
      $file = request()->file('file');
    
      $root_path = './uploads';
      $info = $file->move($root_path);
      if(!empty($info)) return json(['errno'=>0,'data'=>[['url'=>'/uploads/'.$info->getSaveName(),'alt'=>'','href'=>'/uploads/'.$info->getSaveName()]]]);
        
    }
}