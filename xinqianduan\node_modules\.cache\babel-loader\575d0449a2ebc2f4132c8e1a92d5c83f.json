{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\debt\\debts.vue", "mtime": 1748508325736}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gQCBpcyBhbiBhbGlhcyB0byAvc3JjCmltcG9ydCBVc2VyRGV0YWlscyBmcm9tICIvc3JjL2NvbXBvbmVudHMvVXNlckRldGFpbC52dWUiOwppbXBvcnQgRGVidERldGFpbCBmcm9tICIvc3JjL2NvbXBvbmVudHMvRGVidERldGFpbC52dWUiOwppbXBvcnQgc3RvcmUgZnJvbSAiLi4vLi4vLi4vc3RvcmUiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImxpc3QiLAogIGNvbXBvbmVudHM6IHsKICAgIFVzZXJEZXRhaWxzLAogICAgRGVidERldGFpbAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVwbG9hZEFjdGlvbjogJycsCiAgICAgIHVwbG9hZERlYnRzQWN0aW9uOiAiL2FkbWluL2RlYnQvaW1wb3J0RGVidHM/dG9rZW49IiArIHRoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RPS0VOLAogICAgICB1cGxvYWRWaXNpYmxlOiBmYWxzZSwKICAgICAgdXBsb2FkRGVidHNWaXNpYmxlOiBmYWxzZSwKICAgICAgc3VibWl0T3JkZXJMb2FkaW5nMjogZmFsc2UsCiAgICAgIHN1Ym1pdE9yZGVyTG9hZGluZzM6IGZhbHNlLAogICAgICB1cGxvYWREYXRhOiB7CiAgICAgICAgcmV2aWV3OiBmYWxzZQogICAgICB9LAogICAgICB1cGxvYWREZWJ0c0RhdGE6IHsKICAgICAgICByZXZpZXc6IGZhbHNlCiAgICAgIH0sCiAgICAgIGFsbFNpemU6ICJtaW5pIiwKICAgICAgbGlzdFVzZXI6IFtdLAogICAgICBsaXN0OiBbewogICAgICAgIGlkOiAxLAogICAgICAgIHVpZDogMTAwMSwKICAgICAgICBuYW1lOiAi5byg5LiJIiwKICAgICAgICB0ZWw6ICIxMzgwMDEzODAwMSIsCiAgICAgICAgbW9uZXk6ICI1MDAwMCIsCiAgICAgICAgc3RhdHVzOiAi5b6F5aSE55CGIiwKICAgICAgICBiYWNrX21vbmV5OiAiMCIsCiAgICAgICAgdW5fbW9uZXk6ICI1MDAwMCIsCiAgICAgICAgY3RpbWU6ICIyMDI0LTAxLTE1IDEwOjMwOjAwIiwKICAgICAgICBhZGRyZXNzOiAi5YyX5Lqs5biC5pyd6Ziz5Yy65bu65Zu96LevODjlj7ciLAogICAgICAgIGlkY2FyZF9ubzogIjExMDEwMTE5OTAwMTAxMTIzNCIsCiAgICAgICAgY2FzZV9kZXM6ICLlgJ/mrL7nuqDnurfvvIzlgJ/mrL7kurrmnKrmjInnuqblrprml7bpl7Tov5jmrL4iLAogICAgICAgIHVzZXJzOiB7CiAgICAgICAgICBuaWNrbmFtZTogIuadjuWbmyIKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICB1aWQ6IDEwMDIsCiAgICAgICAgbmFtZTogIueOi+S6lCIsCiAgICAgICAgdGVsOiAiMTM5MDAxMzkwMDIiLAogICAgICAgIG1vbmV5OiAiMTIwMDAwIiwKICAgICAgICBzdGF0dXM6ICLosIPoioLkuK0iLAogICAgICAgIGJhY2tfbW9uZXk6ICIzMDAwMCIsCiAgICAgICAgdW5fbW9uZXk6ICI5MDAwMCIsCiAgICAgICAgY3RpbWU6ICIyMDI0LTAxLTEwIDE0OjIwOjAwIiwKICAgICAgICBhZGRyZXNzOiAi5LiK5rW35biC5rWm5Lic5paw5Yy66ZmG5a625Zi06YeR6J6N5Yy6IiwKICAgICAgICBpZGNhcmRfbm86ICIzMTAxMDExOTkyMDUxNTU2NzgiLAogICAgICAgIGNhc2VfZGVzOiAi5ZCI5ZCM57qg57q377yM5pyq5oyJ5ZCI5ZCM57qm5a6a5pSv5LuY6LSn5qy+IiwKICAgICAgICB1c2VyczogewogICAgICAgICAgbmlja25hbWU6ICLotbXlha0iCiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgaWQ6IDMsCiAgICAgICAgdWlkOiAxMDAzLAogICAgICAgIG5hbWU6ICLpmYjkuIMiLAogICAgICAgIHRlbDogIjEzNzAwMTM3MDAzIiwKICAgICAgICBtb25leTogIjgwMDAwIiwKICAgICAgICBzdGF0dXM6ICLor4norrzkuK0iLAogICAgICAgIGJhY2tfbW9uZXk6ICIyMDAwMCIsCiAgICAgICAgdW5fbW9uZXk6ICI2MDAwMCIsCiAgICAgICAgY3RpbWU6ICIyMDI0LTAxLTA1IDA5OjE1OjAwIiwKICAgICAgICBhZGRyZXNzOiAi5bm/5bee5biC5aSp5rKz5Yy654+g5rGf5paw5Z+OIiwKICAgICAgICBpZGNhcmRfbm86ICI0NDAxMDExOTkzMDgwODkwMTIiLAogICAgICAgIGNhc2VfZGVzOiAi5pyN5Yqh6LS557qg57q377yM5ouS57ud5pSv5LuY57qm5a6a55qE5pyN5Yqh6LS555SoIiwKICAgICAgICB1c2VyczogewogICAgICAgICAgbmlja25hbWU6ICLlrZnlhasiCiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgaWQ6IDQsCiAgICAgICAgdWlkOiAxMDA0LAogICAgICAgIG5hbWU6ICLliJjkuZ0iLAogICAgICAgIHRlbDogIjEzNjAwMTM2MDA0IiwKICAgICAgICBtb25leTogIjIwMDAwMCIsCiAgICAgICAgc3RhdHVzOiAi5bey57uT5qGIIiwKICAgICAgICBiYWNrX21vbmV5OiAiMjAwMDAwIiwKICAgICAgICB1bl9tb25leTogIjAiLAogICAgICAgIGN0aW1lOiAiMjAyMy0xMi0yMCAxNjo0NTowMCIsCiAgICAgICAgYWRkcmVzczogIua3seWcs+W4guWNl+WxseWMuuenkeaKgOWbrSIsCiAgICAgICAgaWRjYXJkX25vOiAiNDQwMzAxMTk5MTEyMTIzNDU2IiwKICAgICAgICBjYXNlX2RlczogIuaKlei1hOe6oOe6t++8jOW3sumAmui/h+iwg+ino+i+vuaIkOS4gOiHtCIsCiAgICAgICAgdXNlcnM6IHsKICAgICAgICAgIG5pY2tuYW1lOiAi5ZGo5Y2BIgogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGlkOiA1LAogICAgICAgIHVpZDogMTAwNSwKICAgICAgICBuYW1lOiAi5ZC05Y2B5LiAIiwKICAgICAgICB0ZWw6ICIxMzUwMDEzNTAwNSIsCiAgICAgICAgbW9uZXk6ICI3NTAwMCIsCiAgICAgICAgc3RhdHVzOiAi5b6F5aSE55CGIiwKICAgICAgICBiYWNrX21vbmV5OiAiMCIsCiAgICAgICAgdW5fbW9uZXk6ICI3NTAwMCIsCiAgICAgICAgY3RpbWU6ICIyMDI0LTAxLTE4IDExOjMwOjAwIiwKICAgICAgICBhZGRyZXNzOiAi5p2t5bee5biC6KW/5rmW5Yy65paH5LiJ6LevIiwKICAgICAgICBpZGNhcmRfbm86ICIzMzAxMDExOTk0MDYwNjc4OTAiLAogICAgICAgIGNhc2VfZGVzOiAi56ef6LWB57qg57q377yM5ouW5qyg5oi/56ef5Y+K6L+d57qm6YeRIiwKICAgICAgICB1c2VyczogewogICAgICAgICAgbmlja25hbWU6ICLpg5HljYHkuowiCiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgaWQ6IDYsCiAgICAgICAgdWlkOiAxMDA2LAogICAgICAgIG5hbWU6ICLpqazljYHkuIkiLAogICAgICAgIHRlbDogIjEzNDAwMTM0MDA2IiwKICAgICAgICBtb25leTogIjE1MDAwMCIsCiAgICAgICAgc3RhdHVzOiAi6LCD6IqC5LitIiwKICAgICAgICBiYWNrX21vbmV5OiAiNTAwMDAiLAogICAgICAgIHVuX21vbmV5OiAiMTAwMDAwIiwKICAgICAgICBjdGltZTogIjIwMjQtMDEtMTIgMTM6MjA6MDAiLAogICAgICAgIGFkZHJlc3M6ICLmiJDpg73luILplKbmsZ/ljLrmmKXnhpnot68iLAogICAgICAgIGlkY2FyZF9ubzogIjUxMDEwMTE5OTAwOTA5MTIzNCIsCiAgICAgICAgY2FzZV9kZXM6ICLkubDljZblkIjlkIznuqDnurfvvIzotKfnianotKjph4/pl67popjlr7zoh7TmjZ/lpLEiLAogICAgICAgIHVzZXJzOiB7CiAgICAgICAgICBuaWNrbmFtZTogIuWGr+WNgeWbmyIKICAgICAgICB9CiAgICAgIH1dLAogICAgICB0b3RhbDogNiwKICAgICAgcGFnZTogMSwKICAgICAgY3VycmVudElkOiAwLAogICAgICBjdXJyZW50RGVidElkOiAwLAogICAgICBwYWdlVXNlcjogMSwKICAgICAgc2l6ZVVzZXI6IDIwLAogICAgICBzZWFyY2hVc2VyOiB7CiAgICAgICAga2V5d29yZDogIiIKICAgICAgfSwKICAgICAgc2l6ZTogMjAsCiAgICAgIHNlYXJjaDogewogICAgICAgIGtleXdvcmQ6ICIiLAogICAgICAgIHN0YXR1czogLTEsCiAgICAgICAgcHJvcDogIiIsCiAgICAgICAgb3JkZXI6ICIiCiAgICAgIH0sCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHVybDogIi9kZWJ0LyIsCiAgICAgIHVybFVzZXI6ICIvdXNlci8iLAogICAgICB0aXRsZTogIuWAuuWKoSIsCiAgICAgIGluZm86IHsKICAgICAgICBpbWFnZXM6IFtdLAogICAgICAgIGF0dGFjaF9wYXRoOiBbXSwKICAgICAgICBjYXJkczogW10sCiAgICAgICAgZGVidHRyYW5zOiBbXQogICAgICB9LAogICAgICBkaWFsb2dVc2VyRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dWaWV3VXNlckRldGFpbDogZmFsc2UsCiAgICAgIGRpYWxvZ1pmcnFWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nUmljaGFuZ1Zpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dIdWlrdWFuVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ0RlYnR0cmFuc0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICB2aWV3Rm9ybVZpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dWaWV3RGVidERldGFpbDogZmFsc2UsCiAgICAgIHNob3dfaW1hZ2U6ICIiLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZUZvcm1EZWJ0dHJhbnM6IHsKICAgICAgICB0aXRsZTogIiIKICAgICAgfSwKICAgICAgcnVsZUZvcm06IHsKICAgICAgICBpbWFnZXM6IFtdLAogICAgICAgIGRlbF9pbWFnZXM6IFtdLAogICAgICAgIGF0dGFjaF9wYXRoOiBbXSwKICAgICAgICBkZWxfYXR0YWNoX3BhdGg6IFtdLAogICAgICAgIGNhcmRzOiBbXSwKICAgICAgICBkZWJ0dHJhbnM6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzRGVidHRyYW5zOiB7CiAgICAgICAgZGF5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6Lef6L+b5pel5pyfIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHN0YXR1czogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqei3n+i/m+eKtuaAgSIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XQogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHVpZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeeUqOaItyIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBuYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5YC65Yqh5Lq65aeT5ZCNIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIG1vbmV5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5YC65Yqh6YeR6aKdIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGNhc2VfZGVzOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5qGI55SxIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGZvcm1MYWJlbFdpZHRoOiAiMTQwcHgiLAogICAgICBvcHRpb25zOiBbewogICAgICAgIGlkOiAtMSwKICAgICAgICB0aXRsZTogIuivt+mAieaLqSIKICAgICAgfSwgewogICAgICAgIGlkOiAxLAogICAgICAgIHRpdGxlOiAi5b6F5aSE55CGIgogICAgICB9LCB7CiAgICAgICAgaWQ6IDIsCiAgICAgICAgdGl0bGU6ICLosIPoioLkuK0iCiAgICAgIH0sIHsKICAgICAgICBpZDogMywKICAgICAgICB0aXRsZTogIuivieiuvOS4rSIKICAgICAgfSwgewogICAgICAgIGlkOiA0LAogICAgICAgIHRpdGxlOiAi5bey57uT5qGIIgogICAgICB9XSwKICAgICAgYWN0aXZlRGVidFRhYjogJ2RldGFpbHMnCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2hhbmdlRmlsZShmaWxlZCkgewogICAgICB0aGlzLmZpbGVkID0gZmlsZWQ7CiAgICB9LAogICAgc2VhcmNoVXNlckRhdGEoKSB7CiAgICAgIHRoaXMucGFnZVVzZXIgPSAxOwogICAgICB0aGlzLnNpemVVc2VyID0gMjA7CiAgICAgIHRoaXMuZ2V0VXNlckRhdGEodGhpcy5ydWxlRm9ybSk7CiAgICB9LAogICAgZ2V0VXNlckRhdGEocnVsZWRhdGEpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgX3RoaXMucnVsZUZvcm0gPSBydWxlZGF0YTsKICAgICAgX3RoaXMucG9zdFJlcXVlc3QoX3RoaXMudXJsVXNlciArICJpbmRleD9wYWdlPSIgKyBfdGhpcy5wYWdlVXNlciArICImc2l6ZT0iICsgX3RoaXMuc2l6ZVVzZXIsIF90aGlzLnNlYXJjaFVzZXIpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICBfdGhpcy5saXN0VXNlciA9IHJlc3AuZGF0YTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHR5cGVDbGljayhmaWxlZCkgewogICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywgJ3RvdGFsX3ByaWNlJywgJycpOwogICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywgJ2JhY2tfbW9uZXknLCAnJyk7CiAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtRGVidHRyYW5zLCAnY29udGVudCcsICcnKTsKICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm1EZWJ0dHJhbnMsICdyYXRlJywgJycpOwogICAgICBpZiAoZmlsZWQgPT0gMSkgewogICAgICAgIHRoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgaWYgKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3BheV90eXBlJ10gPT0gMSkgewogICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmRpYWxvZ1JpY2hhbmdWaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIHRoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSB0cnVlOwogICAgICAgIGlmICh0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydwYXlfdHlwZSddICE9IDMpIHsKICAgICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSBmYWxzZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5kaWFsb2daZnJxVmlzaWJsZSA9IHRydWU7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgZWRpdFJhdGVNb25leSgpIHsKICAgICAgaWYgKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3JhdGUnXSA+IDAgJiYgdGhpcy5ydWxlRm9ybURlYnR0cmFuc1snYmFja19tb25leSddID4gMCkgewogICAgICAgIC8vdGhpcy5ydWxlRm9ybURlYnR0cmFucy5yYXRlX21vbmV5ID0gdGhpcy5ydWxlRm9ybURlYnR0cmFuc1sncmF0ZSddKnRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ2JhY2tfbW9uZXknXTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywgJ3JhdGVfbW9uZXknLCB0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydyYXRlJ10gKiB0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydiYWNrX21vbmV5J10gLyAxMDApOwogICAgICB9CiAgICB9LAogICAgc2VsVXNlckRhdGEoY3VycmVudFJvdykgewogICAgICBpZiAoY3VycmVudFJvdykgewogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCAndWlkJywgY3VycmVudFJvdy5pZCk7CiAgICAgICAgaWYgKGN1cnJlbnRSb3cucGhvbmUpIHsKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtLCAndXRlbCcsIGN1cnJlbnRSb3cucGhvbmUpOwogICAgICAgIH0KICAgICAgICBpZiAoY3VycmVudFJvdy5uaWNrbmFtZSkgewogICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sICd1bmFtZScsIGN1cnJlbnRSb3cubmlja25hbWUpOwogICAgICAgIH0KICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLmRpYWxvZ1VzZXJGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgcGF5VHlwZUNsaWNrKGZpbGVkKSB7CiAgICAgIGlmIChmaWxlZCA9PSAyIHx8IGZpbGVkID09IDMpIHsKICAgICAgICBpZiAodGhpcy5ydWxlRm9ybURlYnR0cmFuc1sndHlwZSddID09IDEpIHsKICAgICAgICAgIHRoaXMuZGlhbG9nUmljaGFuZ1Zpc2libGUgPSB0cnVlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmRpYWxvZ1JpY2hhbmdWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmIChmaWxlZCA9PSAzKSB7CiAgICAgICAgaWYgKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3R5cGUnXSA9PSAyKSB7CiAgICAgICAgICB0aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5kaWFsb2daZnJxVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAoZmlsZWQgPT0gMSkgewogICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmRpYWxvZ1JpY2hhbmdWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgaWYgKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3R5cGUnXSA9PSAyKSB7CiAgICAgICAgICB0aGlzLmRpYWxvZ0h1aWt1YW5WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5kaWFsb2dIdWlrdWFuVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNsZWFyRGF0YSgpIHsKICAgICAgdGhpcy5zZWFyY2ggPSB7CiAgICAgICAga2V5d29yZDogIiIsCiAgICAgICAgc3RhdHVzOiAiIiwKICAgICAgICBwcm9wOiAiIiwKICAgICAgICBvcmRlcjogIiIKICAgICAgfTsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgZWRpdERhdGEoaWQpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgaWYgKGlkICE9IDApIHsKICAgICAgICB0aGlzLmdldEluZm8oaWQpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucnVsZUZvcm0gPSB7CiAgICAgICAgICBpbWFnZXM6IFtdLAogICAgICAgICAgZGVsX2ltYWdlczogW10sCiAgICAgICAgICBhdHRhY2hfcGF0aDogW10sCiAgICAgICAgICBkZWxfYXR0YWNoX3BhdGg6IFtdLAogICAgICAgICAgY2FyZHM6IFtdLAogICAgICAgICAgZGVidHRyYW5zOiBbXQogICAgICAgIH07CiAgICAgIH0KICAgICAgX3RoaXMuYWN0aXZlRGVidFRhYiA9ICdkZXRhaWxzJzsKICAgICAgX3RoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIHZpZXdVc2VyRGF0YShpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBpZiAoaWQgIT0gMCkgewogICAgICAgIHRoaXMuY3VycmVudElkID0gaWQ7CiAgICAgIH0KICAgICAgX3RoaXMuZGlhbG9nVmlld1VzZXJEZXRhaWwgPSB0cnVlOwogICAgfSwKICAgIHZpZXdEZWJ0RGF0YShpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBpZiAoaWQgIT0gMCkgewogICAgICAgIHRoaXMuY3VycmVudERlYnRJZCA9IGlkOwogICAgICB9CiAgICAgIF90aGlzLmRpYWxvZ1ZpZXdEZWJ0RGV0YWlsID0gdHJ1ZTsKICAgIH0sCiAgICBlZGl0RGVidHRyYW5zRGF0YShpZCkgewogICAgICBpZiAoaWQgIT0gMCkgewogICAgICAgIHRoaXMuZ2V0RGVidHRyYW5zSW5mbyhpZCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5ydWxlRm9ybURlYnR0cmFucyA9IHsKICAgICAgICAgIG5hbWU6ICIiCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIHZpZXdEYXRhKGlkKSB7CiAgICAgIGlmIChpZCAhPSAwKSB7CiAgICAgICAgdGhpcy5nZXRWaWV3KGlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJ1bGVGb3JtID0gewogICAgICAgICAgdGl0bGU6ICIiLAogICAgICAgICAgZGVzYzogIiIKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgZ2V0VmlldyhpZCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KF90aGlzLnVybCArICJ2aWV3P2lkPSIgKyBpZCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMuaW5mbyA9IHJlc3AuZGF0YTsKICAgICAgICAgIF90aGlzLnZpZXdGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICBfdGhpcy51cGxvYWRBY3Rpb24gPSAiL2FkbWluL3VzZXIvaW1wb3J0P2lkPSIgKyBpZCArICImdG9rZW49IiArIHRoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RPS0VOOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldEluZm8oaWQpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgX3RoaXMuZ2V0UmVxdWVzdChfdGhpcy51cmwgKyAicmVhZD9pZD0iICsgaWQpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzLnJ1bGVGb3JtID0gcmVzcC5kYXRhOwogICAgICAgICAgY29uc29sZS5sb2cocmVzcC5kYXRhKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXREZWJ0dHJhbnNJbmZvKGlkKSB7CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIF90aGlzLmdldFJlcXVlc3QoX3RoaXMudXJsICsgImRlYnR0cmFuc1JlYWQ/aWQ9IiArIGlkKS50aGVuKHJlc3AgPT4gewogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBfdGhpcy5ydWxlRm9ybURlYnR0cmFucyA9IHJlc3AuZGF0YTsKICAgICAgICAgIF90aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICBfdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgX3RoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIF90aGlzLmRpYWxvZ0RlYnR0cmFuc0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICB0dWlrdWFuKGlkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpueUs+ivt+mAgOasvj8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5kZWxldGVSZXF1ZXN0KHRoaXMudXJsICsgInR1aWt1YW4/aWQ9IiArIGlkKS50aGVuKHJlc3AgPT4gewogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgIG1lc3NhZ2U6ICLlj5bmtojpgIDmrL4hIgogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBkZWxEYXRhKGluZGV4LCBpZCkgewogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbliKDpmaTor6Xkv6Hmga8/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZGVsZXRlUmVxdWVzdCh0aGlzLnVybCArICJkZWxldGU/aWQ9IiArIGlkKS50aGVuKHJlc3AgPT4gewogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIHRoaXMuaW5mby5kZWJ0dHJhbnMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgIG1lc3NhZ2U6ICLlj5bmtojliKDpmaQhIgogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBkZWxEYXRhRGVidChpbmRleCwgaWQpIHsKICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm5Yig6Zmk6K+l5L+h5oGvPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmRlbGV0ZVJlcXVlc3QodGhpcy51cmwgKyAiZGVsZXRlRGVidD9pZD0iICsgaWQpLnRoZW4ocmVzcCA9PiB7CiAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgdGhpcy5pbmZvLmRlYnR0cmFucy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgbWVzc2FnZTogIuWPlua2iOWIoOmZpCEiCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIHJlZnVsc2goKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsKICAgIH0sCiAgICBzZWFyY2hEYXRhKCkgewogICAgICB0aGlzLnBhZ2UgPSAxOwogICAgICB0aGlzLnNpemUgPSAyMDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgZ2V0RGF0YSgpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgX3RoaXMubG9hZGluZyA9IHRydWU7CgogICAgICAvLyDlvIDlj5HmqKHlvI/vvJrkvb/nlKjnpLrkvovmlbDmja7vvIzkuI3lj5HpgIFIVFRQ6K+35rGCCiAgICAgIGNvbnN0IGlzRGV2ZWxvcG1lbnQgPSBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUgPT09ICdsb2NhbGhvc3QnOwogICAgICBpZiAoaXNEZXZlbG9wbWVudCkgewogICAgICAgIC8vIOaooeaLn+W8guatpeWKoOi9vQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgLy8g6L+Z6YeM55qE5pWw5o2u5bey57uP5ZyoZGF0YSgp5Lit5a6a5LmJ5LqG77yM5omA5Lul55u05o6l6K6+572ubG9hZGluZ+S4umZhbHNlCiAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfSwgNTAwKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOeUn+S6p+aooeW8j++8muWPkemAgUhUVFDor7fmsYLojrflj5bnnJ/lrp7mlbDmja4KICAgICAgX3RoaXMucG9zdFJlcXVlc3QoX3RoaXMudXJsICsgImluZGV4P3BhZ2U9IiArIF90aGlzLnBhZ2UgKyAiJnNpemU9IiArIF90aGlzLnNpemUsIF90aGlzLnNlYXJjaCkudGhlbihyZXNwID0+IHsKICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMubGlzdCA9IHJlc3AuZGF0YTsKICAgICAgICAgIF90aGlzLnRvdGFsID0gcmVzcC5jb3VudDsKICAgICAgICB9CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBzYXZlRGF0YSgpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlIiwgdGhpcy5ydWxlRm9ybSkudGhlbihyZXNwID0+IHsKICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzYXZlRGVidHRyYW5zRGF0YSgpIHsKICAgICAgbGV0IF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm1EZWJ0dHJhbnMiXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWyd0b2tlbiddID0gc3RvcmUuZ2V0dGVycy5HRVRfVE9LRU47CiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlRGVidHRyYW5zIiwgdGhpcy5ydWxlRm9ybURlYnR0cmFucykudGhlbihyZXNwID0+IHsKICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIF90aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMuZGlhbG9nUmljaGFuZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpcy5kaWFsb2dIdWlrdWFuVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0RlYnR0cmFuc0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLnNpemUgPSB2YWw7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgaGFuZGxlU3VjY2VzcyhyZXMpIHsKICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5LiK5Lyg5oiQ5YqfIik7CiAgICAgICAgdmFyIGFyciA9IHRoaXMucnVsZUZvcm1bdGhpcy5maWxlZF07CiAgICAgICAgdGhpcy5ydWxlRm9ybVt0aGlzLmZpbGVkXS5zcGxpY2UoMSwgMCwgcmVzLmRhdGEudXJsKTsKICAgICAgICAvL3RoaXMucnVsZUZvcm1bdGhpcy5maWxlZF0ucHVzaCA9IHJlcy5kYXRhLnVybDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICB9CiAgICB9LAogICAgc2hvd0ltYWdlKGZpbGUpIHsKICAgICAgdGhpcy5zaG93X2ltYWdlID0gZmlsZTsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBzaG93VXNlckxpc3QoKSB7CiAgICAgIHRoaXMuc2VhcmNoVXNlckRhdGEoKTsKICAgICAgdGhpcy5kaWFsb2dVc2VyRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzVHlwZVRydWUgPSAvXmltYWdlXC8oanBlZ3xwbmd8anBnKSQvLnRlc3QoZmlsZS50eXBlKTsKICAgICAgaWYgKCFpc1R5cGVUcnVlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5Zu+54mH5qC85byP5LiN5a+5ISIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgfSwKICAgIGRlbEltYWdlKGZpbGUsIGZpbGVOYW1lLCBpbmRleCkgewogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBfdGhpcy5nZXRSZXF1ZXN0KCIvVXBsb2FkL2RlbEltYWdlP2ZpbGVOYW1lPSIgKyBmaWxlKS50aGVuKHJlc3AgPT4gewogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBfdGhpcy5ydWxlRm9ybVtmaWxlTmFtZV0uc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICAgIF90aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyEiKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcC5tc2cpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlU29ydENoYW5nZSh7CiAgICAgIGNvbHVtbiwKICAgICAgcHJvcCwKICAgICAgb3JkZXIKICAgIH0pIHsKICAgICAgdGhpcy5zZWFyY2gucHJvcCA9IHByb3A7CiAgICAgIHRoaXMuc2VhcmNoLm9yZGVyID0gb3JkZXI7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAvLyDmoLnmja4gY29sdW1uLCBwcm9wLCBvcmRlciDmnaXmm7TmlrDkvaDnmoTmlbDmja7mjpLluo8KICAgICAgLy8g5L6L5aaC77yM5L2g5Y+v5Lul5Y+R6YCB5LiA5Liq6K+35rGC5Yiw5pyN5Yqh5Zmo5p2l6I635Y+W5o6S5bqP5ZCO55qE5pWw5o2uCiAgICB9LAogICAgZXhwb3J0czogZnVuY3Rpb24gKCkgewogICAgICAvL+WvvOWHuuihqOagvAogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBsb2NhdGlvbi5ocmVmID0gIi9hZG1pbi9kZWJ0L3ZpZXc/dG9rZW49IiArIF90aGlzLiRzdG9yZS5nZXR0ZXJzLkdFVF9UT0tFTiArICImZXhwb3J0PTEmaWQ9IiArIF90aGlzLnJ1bGVGb3JtLmlkOwogICAgfSwKICAgIGV4cG9ydHNEZWJ0TGlzdDogZnVuY3Rpb24gKCkgewogICAgICAvL+WvvOWHuuihqOagvAogICAgICBsZXQgX3RoaXMgPSB0aGlzOwogICAgICBsb2NhdGlvbi5ocmVmID0gIi9hZG1pbi9kZWJ0L2V4cG9ydExpc3Q/dG9rZW49IiArIF90aGlzLiRzdG9yZS5nZXR0ZXJzLkdFVF9UT0tFTiArICIma2V5d29yZD0iICsgX3RoaXMuc2VhcmNoLmtleXdvcmQ7CiAgICB9LAogICAgY2xvc2VVcGxvYWREaWFsb2coKSB7CiAgICAgIC8v5YWz6Zet56qX5Y+jCiAgICAgIHRoaXMudXBsb2FkVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMudXBsb2FkRGF0YS5yZXZpZXcgPSBmYWxzZTsKICAgIH0sCiAgICBjbG9zZVVwbG9hZERlYnRzRGlhbG9nKCkgewogICAgICAvL+WFs+mXreeql+WPowogICAgICB0aGlzLnVwbG9hZERlYnRzVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMudXBsb2FkRGVidHNEYXRhLnJldmlldyA9IGZhbHNlOwogICAgfSwKICAgIHVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgLy/lr7zlhaXlrozmiJDlm57osIMKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnCiAgICAgICAgfSk7CiAgICAgICAgdGhpcy51cGxvYWRWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgdGhpcy5zdWJtaXRPcmRlckxvYWRpbmcyID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgIH0sCiAgICB1cGxvYWREZWJ0c1N1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgLy/lr7zlhaXlrozmiJDlm57osIMKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnCiAgICAgICAgfSk7CiAgICAgICAgdGhpcy51cGxvYWREZWJ0c1Zpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tc2cKICAgICAgICB9KTsKICAgICAgfQogICAgICB0aGlzLnN1Ym1pdE9yZGVyTG9hZGluZzMgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgfSwKICAgIGNoZWNrRmlsZShmaWxlKSB7CiAgICAgIC8v5a+85YWl5YmN5qCh6aqM5paH5Lu25ZCO57yACiAgICAgIGxldCBmaWxlVHlwZSA9IFsneGxzJywgJ3hsc3gnXTsKICAgICAgbGV0IHR5cGUgPSBmaWxlLm5hbWUuc3BsaXQoJy4nKS5zbGljZSgtMSlbMF0udG9Mb3dlckNhc2UoKTsKICAgICAgaWYgKCFmaWxlVHlwZS5pbmNsdWRlcyh0eXBlKSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICAgICAgbWVzc2FnZTogIuaWh+S7tuagvOW8j+mUmeivr+S7heaUr+aMgSB4bHMgeGx4cyDmlofku7YiCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKICAgIHN1Ym1pdFVwbG9hZCgpIHsKICAgICAgLy/lr7zlhaXmj5DkuqQKICAgICAgdGhpcy5zdWJtaXRPcmRlckxvYWRpbmcyID0gdHJ1ZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICB9LAogICAgc3VibWl0VXBsb2FkRGVidHMoKSB7CiAgICAgIC8v5a+85YWl5o+Q5LqkCiAgICAgIHRoaXMuc3VibWl0T3JkZXJMb2FkaW5nMyA9IHRydWU7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGNsb3NlRGlhbG9nKCkgewogICAgICAvL+WFs+mXreeql+WPowogICAgICB0aGlzLmFkZFZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy51cGxvYWRWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogJycsCiAgICAgICAgbmlja25hbWU6ICIiLAogICAgICAgIG1vYmlsZTogIiIsCiAgICAgICAgc2Nob29sX2lkOiAwLAogICAgICAgIGdyYWRlX2lkOiAnJywKICAgICAgICBjbGFzc19pZDogJycsCiAgICAgICAgc2V4OiAnJywKICAgICAgICBpc19wb29yOiAnJywKICAgICAgICBpc19kaXNwbGF5OiAnJywKICAgICAgICBudW1iZXI6ICcnLAogICAgICAgIHJlbWFyazogJycsCiAgICAgICAgaXNfcmVtYXJrX29wdGlvbjogMCwKICAgICAgICByZW1hcmtfb3B0aW9uOiBbXSwKICAgICAgICBtb2JpbGVfY2hlY2tlZDogZmFsc2UKICAgICAgfTsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCk7CiAgICB9LAogICAgb3BlblVwbG9hZCgpIHsKICAgICAgLy/miZPlvIDlr7zlhaXlvLnnqpcKICAgICAgdGhpcy51cGxvYWRWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBvcGVuVXBsb2FkRGVidHMoKSB7CiAgICAgIC8v5omT5byA5a+85YWl5by556qXCiAgICAgIHRoaXMudXBsb2FkRGVidHNWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVEcmF3ZXJDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZURlYnRUYWJTZWxlY3QoaW5kZXgpIHsKICAgICAgdGhpcy5hY3RpdmVEZWJ0VGFiID0gaW5kZXg7CiAgICB9LAogICAgZ2V0RXZpZGVuY2VUaXRsZSgpIHsKICAgICAgY29uc3QgdGFiID0gdGhpcy5hY3RpdmVEZWJ0VGFiOwogICAgICBzd2l0Y2ggKHRhYikgewogICAgICAgIGNhc2UgJ2V2aWRlbmNlLWFsbCc6CiAgICAgICAgICByZXR1cm4gJ+WFqOmDqOivgeaNric7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtdmlkZW8nOgogICAgICAgICAgcmV0dXJuICfop4bpopHor4Hmja4nOwogICAgICAgIGNhc2UgJ2V2aWRlbmNlLWltYWdlJzoKICAgICAgICAgIHJldHVybiAn5Zu+54mH6K+B5o2uJzsKICAgICAgICBjYXNlICdldmlkZW5jZS1hdWRpbyc6CiAgICAgICAgICByZXR1cm4gJ+ivremfs+ivgeaNric7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtZG9jdW1lbnQnOgogICAgICAgICAgcmV0dXJuICfmlofmoaPor4Hmja4nOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gJ+WAuuWKoeS6uuivpuaDhSc7CiAgICAgIH0KICAgIH0sCiAgICBnZXRFdmlkZW5jZVR5cGVUZXh0KCkgewogICAgICBjb25zdCB0YWIgPSB0aGlzLmFjdGl2ZURlYnRUYWI7CiAgICAgIHN3aXRjaCAodGFiKSB7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtYWxsJzoKICAgICAgICAgIHJldHVybiAn5YWo6YOoJzsKICAgICAgICBjYXNlICdldmlkZW5jZS12aWRlbyc6CiAgICAgICAgICByZXR1cm4gJ+inhumikSc7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtaW1hZ2UnOgogICAgICAgICAgcmV0dXJuICflm77niYcnOwogICAgICAgIGNhc2UgJ2V2aWRlbmNlLWF1ZGlvJzoKICAgICAgICAgIHJldHVybiAn6K+t6Z+zJzsKICAgICAgICBjYXNlICdldmlkZW5jZS1kb2N1bWVudCc6CiAgICAgICAgICByZXR1cm4gJ+aWh+ahoyc7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAn5YC65Yqh5Lq66K+m5oOFJzsKICAgICAgfQogICAgfSwKICAgIGhhc0V2aWRlbmNlKCkgewogICAgICBjb25zdCB0YWIgPSB0aGlzLmFjdGl2ZURlYnRUYWI7CiAgICAgIHN3aXRjaCAodGFiKSB7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtYWxsJzoKICAgICAgICAgIHJldHVybiB0aGlzLnJ1bGVGb3JtLmNhcmRzLmxlbmd0aCA+IDAgfHwgdGhpcy5ydWxlRm9ybS5pbWFnZXMubGVuZ3RoID4gMCB8fCB0aGlzLnJ1bGVGb3JtLmF0dGFjaF9wYXRoLmxlbmd0aCA+IDA7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtdmlkZW8nOgogICAgICAgICAgcmV0dXJuIHRoaXMucnVsZUZvcm0uaW1hZ2VzLmxlbmd0aCA+IDA7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtaW1hZ2UnOgogICAgICAgICAgcmV0dXJuIHRoaXMucnVsZUZvcm0uaW1hZ2VzLmxlbmd0aCA+IDA7CiAgICAgICAgY2FzZSAnZXZpZGVuY2UtYXVkaW8nOgogICAgICAgICAgcmV0dXJuIHRoaXMucnVsZUZvcm0uYXR0YWNoX3BhdGgubGVuZ3RoID4gMDsKICAgICAgICBjYXNlICdldmlkZW5jZS1kb2N1bWVudCc6CiAgICAgICAgICByZXR1cm4gdGhpcy5ydWxlRm9ybS5hdHRhY2hfcGF0aC5sZW5ndGggPiAwOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICB1cGxvYWRFdmlkZW5jZSgpIHsKICAgICAgLy8gSW1wbGVtZW50YXRpb24gb2YgdXBsb2FkRXZpZGVuY2UgbWV0aG9kCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["UserDetails", "DebtDetail", "store", "name", "components", "data", "uploadAction", "uploadDebtsAction", "$store", "getters", "GET_TOKEN", "uploadVisible", "uploadDebtsVisible", "submitOrderLoading2", "submitOrderLoading3", "uploadData", "review", "uploadDebtsData", "allSize", "listUser", "list", "id", "uid", "tel", "money", "status", "back_money", "un_money", "ctime", "address", "idcard_no", "case_des", "users", "nickname", "total", "page", "currentId", "currentDebtId", "pageUser", "sizeUser", "searchUser", "keyword", "size", "search", "prop", "order", "loading", "url", "urlUser", "title", "info", "images", "attach_path", "cards", "debttrans", "dialogUserFormVisible", "dialogViewUserDetail", "dialogZfrqVisible", "dialogRichangVisible", "dialogHuikuanVisible", "dialogDebttransFormVisible", "dialogFormVisible", "viewFormVisible", "dialogViewDebtDetail", "show_image", "dialogVisible", "ruleFormDebttrans", "ruleForm", "del_images", "del_attach_path", "rulesDebttrans", "day", "required", "message", "trigger", "rules", "form<PERSON>abe<PERSON><PERSON>", "options", "activeDebtTab", "mounted", "getData", "methods", "changeFile", "filed", "searchUserData", "getUserData", "ruledata", "_this", "postRequest", "then", "resp", "code", "typeClick", "$set", "editRateMoney", "selUserData", "currentRow", "phone", "payTypeClick", "clearData", "editData", "getInfo", "viewUserData", "viewDebtData", "editDebttransData", "getDebttransInfo", "viewData", "get<PERSON>iew", "desc", "getRequest", "$message", "type", "msg", "console", "log", "tui<PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "deleteRequest", "catch", "delData", "index", "splice", "delDataDebt", "refulsh", "$router", "go", "searchData", "isDevelopment", "process", "env", "NODE_ENV", "window", "location", "hostname", "setTimeout", "count", "saveData", "$refs", "validate", "valid", "saveDebttransData", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "success", "arr", "error", "showImage", "file", "showUserList", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "handleSortChange", "column", "exports", "href", "exportsDebtList", "closeUploadDialog", "upload", "clearFiles", "closeUploadDebtsDialog", "uploadSuccess", "response", "uploadDebtsSuccess", "checkFile", "fileType", "split", "slice", "toLowerCase", "includes", "submitUpload", "submit", "submitUploadDebts", "closeDialog", "addVisible", "form", "mobile", "school_id", "grade_id", "class_id", "sex", "is_poor", "is_display", "number", "remark", "is_remark_option", "remark_option", "mobile_checked", "resetFields", "openUpload", "openUploadDebts", "handleDrawerClose", "handleDebtTabSelect", "getEvidenceTitle", "tab", "getEvidenceTypeText", "hasEvidence", "length", "uploadEvidence"], "sources": ["src/views/pages/debt/debts.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\"><div @click=\"viewUserData(scope.row.uid)\">{{scope.row.users.nickname}}</div></template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <!-- 债务人编辑抽屉 -->\r\n    <el-drawer\r\n      title=\"债务人管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleDrawerClose\">\r\n      <div class=\"drawer-content-wrapper\">\r\n        <!-- 左侧导航菜单 -->\r\n        <div class=\"drawer-sidebar\">\r\n          <el-menu\r\n            :default-active=\"activeDebtTab\"\r\n            class=\"drawer-menu\"\r\n            @select=\"handleDebtTabSelect\">\r\n            <el-menu-item index=\"details\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>债务人详情</span>\r\n            </el-menu-item>\r\n            <el-submenu index=\"evidence\">\r\n              <template slot=\"title\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                <span>证据</span>\r\n              </template>\r\n              <el-menu-item index=\"evidence-all\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>全部</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-video\">\r\n                <i class=\"el-icon-video-camera\"></i>\r\n                <span>视频</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-image\">\r\n                <i class=\"el-icon-picture\"></i>\r\n                <span>图片</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-audio\">\r\n                <i class=\"el-icon-microphone\"></i>\r\n                <span>语音</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"evidence-document\">\r\n                <i class=\"el-icon-document-copy\"></i>\r\n                <span>文档</span>\r\n              </el-menu-item>\r\n            </el-submenu>\r\n          </el-menu>\r\n        </div>\r\n\r\n        <!-- 右侧内容区域 -->\r\n        <div class=\"drawer-content\">\r\n          <!-- 债务人详情标签页 -->\r\n          <div v-if=\"activeDebtTab === 'details'\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-user\"></i>\r\n                债务人详情\r\n              </div>\r\n              \r\n              <div v-if=\"ruleForm.is_user == 1\">\r\n                <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n              </div>\r\n              \r\n              <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 20px;\">\r\n                <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n                <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\" style=\"margin-top: 20px;\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择用户\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n                      <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\">选择用户</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"用户信息\" v-if=\"ruleForm.utel\">\r\n                      {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人姓名\">\r\n                      <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务人电话\">\r\n                      <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"身份证号码\">\r\n                      <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"债务金额\">\r\n                      <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"债务人地址\">\r\n                  <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                \r\n                <el-form-item label=\"案由描述\">\r\n                  <el-input v-model=\"ruleForm.case_des\" autocomplete=\"off\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n              </el-form>\r\n\r\n              <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\" style=\"margin-top: 30px;\">\r\n                <el-descriptions-item>\r\n                  <el-table :data=\"ruleForm.debttrans\" style=\"width: 100%; margin-top: 10px\" v-loading=\"loading\" size=\"mini\">\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"></el-table-column>\r\n                    <el-table-column prop=\"status_name\" label=\"跟进状态\"></el-table-column>\r\n                    <el-table-column prop=\"type_name\" label=\"跟进类型\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额（元）\"></el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"></el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"delData(scope.$index, scope.row.id)\" type=\"text\" size=\"small\">移除</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"drawer-footer\">\r\n                <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确定</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 证据管理标签页 -->\r\n          <div v-if=\"activeDebtTab.startsWith('evidence')\" class=\"tab-content\">\r\n            <div class=\"card\">\r\n              <div class=\"card-header\">\r\n                <i class=\"el-icon-folder\"></i>\r\n                {{ getEvidenceTitle() }}\r\n                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"uploadEvidence\">\r\n                  <i class=\"el-icon-plus\"></i> 上传证据\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 证据列表 -->\r\n              <div class=\"evidence-container\">\r\n                <!-- 身份证照片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>身份证照片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('cards')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传身份证\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.cards && ruleForm.cards.length > 0\">\r\n                      <div v-for=\"(item7, index7) in ruleForm.cards\" :key=\"index7\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <img :src=\"item7\" @click=\"showImage(item7)\" class=\"evidence-image\" />\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item7, 'cards', index7)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据图片 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-image'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据图片</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('images')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传图片\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"evidence-grid\" v-if=\"ruleForm.images && ruleForm.images.length > 0\">\r\n                      <div v-for=\"(item5, index5) in ruleForm.images\" :key=\"index5\" class=\"evidence-item\">\r\n                        <div class=\"evidence-preview\">\r\n                          <el-image style=\"width: 100%; height: 150px;\" :src=\"item5\" :preview-src-list=\"ruleForm.images\" fit=\"cover\"></el-image>\r\n                        </div>\r\n                        <div class=\"evidence-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item5, 'images', index5)\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的图片 -->\r\n                    <div v-if=\"ruleForm.del_images && ruleForm.del_images.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的图片</h5>\r\n                      <div class=\"evidence-grid\">\r\n                        <div v-for=\"(item8, index8) in ruleForm.del_images\" :key=\"index8\" class=\"evidence-item\">\r\n                          <div class=\"evidence-preview\">\r\n                            <el-image style=\"width: 100%; height: 150px;\" :src=\"item8\" :preview-src-list=\"ruleForm.del_images\" fit=\"cover\"></el-image>\r\n                          </div>\r\n                          <div class=\"evidence-actions\">\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item8, 'del_images', index8)\">删除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 证据文件 -->\r\n                <div v-if=\"activeDebtTab === 'evidence-all' || activeDebtTab === 'evidence-document'\">\r\n                  <div class=\"evidence-section\">\r\n                    <h4>证据文件</h4>\r\n                    <el-button-group style=\"margin-bottom: 10px;\">\r\n                      <el-button @click=\"changeFile('attach_path')\">\r\n                        <el-upload action=\"/admin/Upload/uploadFile\" :show-file-list=\"false\" :on-success=\"handleSuccess\">\r\n                          上传文件\r\n                        </el-upload>\r\n                      </el-button>\r\n                    </el-button-group>\r\n                    <div class=\"file-list\" v-if=\"ruleForm.attach_path && ruleForm.attach_path.length > 0\">\r\n                      <div v-for=\"(item6, index6) in ruleForm.attach_path\" :key=\"index6\" class=\"file-item\" v-if=\"item6\">\r\n                        <div class=\"file-icon\">\r\n                          <i class=\"el-icon-document file-type-icon\"></i>\r\n                        </div>\r\n                        <div class=\"file-info\">\r\n                          <div class=\"file-name\">文件{{ index6 + 1 }}</div>\r\n                        </div>\r\n                        <div class=\"file-actions\">\r\n                          <el-button type=\"primary\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                          </el-button>\r\n                          <el-button type=\"success\" size=\"mini\">\r\n                            <a :href=\"item6\" target=\"_blank\" style=\"color: white; text-decoration: none;\">下载</a>\r\n                          </el-button>\r\n                          <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item6, 'attach_path', index6)\">移除</el-button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <!-- 已删除的文件 -->\r\n                    <div v-if=\"ruleForm.del_attach_path && ruleForm.del_attach_path.length > 0\" style=\"margin-top: 20px;\">\r\n                      <h5>已删除的文件</h5>\r\n                      <div class=\"file-list\">\r\n                        <div v-for=\"(item9, index9) in ruleForm.del_attach_path\" :key=\"index9\" class=\"file-item\" v-if=\"item9\">\r\n                          <div class=\"file-icon\">\r\n                            <i class=\"el-icon-document file-type-icon\"></i>\r\n                          </div>\r\n                          <div class=\"file-info\">\r\n                            <div class=\"file-name\">文件{{ index9 + 1 }}</div>\r\n                          </div>\r\n                          <div class=\"file-actions\">\r\n                            <el-button type=\"primary\" size=\"mini\">\r\n                              <a :href=\"item9\" target=\"_blank\" style=\"color: white; text-decoration: none;\">查看</a>\r\n                            </el-button>\r\n                            <el-button type=\"danger\" size=\"mini\" @click=\"delImage(item9, 'del_attach_path', index9)\">移除</el-button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空状态 -->\r\n                <div v-if=\"!hasEvidence()\" class=\"no-evidence\">\r\n                  <i class=\"el-icon-folder-opened\"></i>\r\n                  <span>暂无{{ getEvidenceTypeText() }}证据</span>\r\n                  <br>\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"uploadEvidence\" style=\"margin-top: 10px;\">\r\n                    <i class=\"el-icon-plus\"></i> 上传第一个证据\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n            title=\"用户列表\"\r\n            :visible.sync=\"dialogUserFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\">\r\n\r\n        <el-row style=\"width: 300px\">\r\n            <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"searchUserData()\"\r\n                ></el-button>\r\n            </el-input>\r\n        </el-row>\r\n\r\n        <el-table\r\n                :data=\"listUser\"\r\n                style=\"width: 100%; margin-top: 10px\"\r\n                size=\"mini\"\r\n                @current-change=\"selUserData\"\r\n        >\r\n            <el-table-column label=\"选择\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n            <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n            <el-table-column prop=\"\" label=\"头像\">\r\n                <template slot-scope=\"scope\">\r\n                    <div>\r\n\r\n                        <el-row v-if=\"scope.row.headimg==''\">\r\n                            <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                        </el-row>\r\n                        <el-row v-else>\r\n                            <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                        </el-row>\r\n\r\n                    </div>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n            <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n            <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n            <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n            <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        </el-table>\r\n\r\n    </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\t\t<el-dialog title=\"债务查看\" :visible.sync=\"dialogViewDebtDetail\" :close-on-click-modal=\"false\" width=\"80%\">\r\n\r\n            <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n\r\n<!--            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>-->\r\n<!--            &lt;!&ndash;<el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"openUpload\">导入跟进记录</el-button>-->\r\n<!--            <a href=\"/import_templete/user.xls\" style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>&ndash;&gt;-->\r\n<!--\t\t\t\t<el-descriptions title=\"债务信息\">-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"用户姓名\">{{info.nickname}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人姓名\">{{info.name}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人电话\">{{info.tel}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人地址\">{{info.address}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务金额\">{{info.money}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"合计回款\">{{info.back_money}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"未回款\">{{info.un_money}}</el-descriptions-item>-->\r\n<!--                    <el-descriptions-item label=\"提交时间\">{{info.ctime}}</el-descriptions-item>-->\r\n<!--                    <el-descriptions-item label=\"最后一次修改时间\">{{info.utime}}</el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"债务人身份信息\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item><div style=\"width: 100%;display: table-cell;\" v-if=\"info.cards[0]\">-->\r\n<!--                    <div style=\"float: left;margin-left:2px;\"-->\r\n<!--                         v-for=\"(item4, index4) in info.cards\"-->\r\n<!--                         :key=\"index4\"-->\r\n<!--                         class=\"image-list\"-->\r\n<!--                    >-->\r\n<!--                      <img :src=\"item4\" style=\"width: 100px; height: 100px\" @click=\"showImage(item4)\" mode=\"aspectFit\" />-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"案由\" :colon=\"false\">-->\r\n<!--\t\t\t\t\t<el-descriptions-item>{{info.case_des}}</el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"证据图片\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item> <div style=\"width: 100%;display: table-cell;\" v-if=\"info.images[0]\">-->\r\n<!--                    <div style=\"float: left;margin-left:2px;\"-->\r\n<!--                         v-for=\"(item2, index2) in info.images\"-->\r\n<!--                         :key=\"index2\"-->\r\n<!--                         class=\"image-list\"-->\r\n<!--                    >-->\r\n<!--                      &lt;!&ndash;<img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />&ndash;&gt;-->\r\n<!--                        <el-image-->\r\n<!--                                style=\"width: 100px; height: 100px\"-->\r\n<!--                                :src=\"item2\"-->\r\n<!--                                :preview-src-list=\"info.images\">-->\r\n<!--                        </el-image>-->\r\n<!--                        <a style=\"\" :href=\"item2\" target=\"_blank\" :download=\"'evidence.'+item2.split('.')[1]\">下载</a>-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"证据文件\" v-if=\"info.attach_path[0]\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item><div style=\"width: 100%;display: table-cell;line-height:20px;\">-->\r\n<!--                    <div-->\r\n<!--                            v-for=\"(item3, index3) in info.attach_path\"-->\r\n<!--                            :key=\"index3\"-->\r\n<!--                    >-->\r\n<!--                      <div v-if=\"item3\">-->\r\n<!--                        <div >文件{{ index3 + 1 + '->' + item3.split(\".\")[1] }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">下载</a></div><br />-->\r\n<!--                      </div>-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--                <el-descriptions title=\"跟进记录\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item>-->\r\n<!--                  <el-table-->\r\n<!--                          :data=\"info.debttrans\"-->\r\n<!--                          style=\"width: 100%; margin-top: 10px\"-->\r\n<!--                          v-loading=\"loading\"-->\r\n<!--                          size=\"mini\"-->\r\n<!--                  >-->\r\n<!--                    <el-table-column prop=\"day\" label=\"跟进日期\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"ctime\" label=\"提交时间\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"au_id\" label=\"操作人员\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"type\" label=\"进度类型\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"total_price\" label=\"费用金额/手续费\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"content\" label=\"费用内容\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"rate\" label=\"手续费比率\"></el-table-column>-->\r\n<!--                    <el-table-column prop=\"back_money\" label=\"回款金额\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_type\" label=\"支付状态\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_time\" label=\"支付时间\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_order_type\" label=\"支付方式\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"desc\" label=\"进度描述\"> </el-table-column>-->\r\n<!--                    <el-table-column fixed=\"right\" label=\"操作\">-->\r\n<!--                      <template slot-scope=\"scope\">-->\r\n<!--                        <el-button-->\r\n<!--                                @click.native.prevent=\"delData(scope.$index, scope.row.id)\"-->\r\n<!--                                type=\"text\"-->\r\n<!--                                size=\"small\"-->\r\n<!--                        >-->\r\n<!--                          移除-->\r\n<!--                        </el-button>-->\r\n<!--                      </template>-->\r\n<!--                    </el-table-column>-->\r\n<!--                  </el-table></el-descriptions-item>-->\r\n<!--                </el-descriptions>-->\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"dialogViewDebtDetail = false\">取 消</el-button>\r\n            </div>\r\n\t\t</el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <el-dialog\r\n              :title=\"用户详情\"\r\n              :visible.sync=\"dialogViewUserDetail\"\r\n              :close-on-click-modal=\"false\"  width=\"80%\"\r\n      >\r\n          <user-details :id=\"currentId\"></user-details>\r\n\r\n      </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails,DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [\r\n        {\r\n          id: 1,\r\n          uid: 1001,\r\n          name: \"张三\",\r\n          tel: \"13800138001\",\r\n          money: \"50000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"50000\",\r\n          ctime: \"2024-01-15 10:30:00\",\r\n          address: \"北京市朝阳区建国路88号\",\r\n          idcard_no: \"110101199001011234\",\r\n          case_des: \"借款纠纷，借款人未按约定时间还款\",\r\n          users: {\r\n            nickname: \"李四\"\r\n          }\r\n        },\r\n        {\r\n          id: 2,\r\n          uid: 1002,\r\n          name: \"王五\",\r\n          tel: \"13900139002\",\r\n          money: \"120000\",\r\n          status: \"调节中\",\r\n          back_money: \"30000\",\r\n          un_money: \"90000\",\r\n          ctime: \"2024-01-10 14:20:00\",\r\n          address: \"上海市浦东新区陆家嘴金融区\",\r\n          idcard_no: \"310101199205155678\",\r\n          case_des: \"合同纠纷，未按合同约定支付货款\",\r\n          users: {\r\n            nickname: \"赵六\"\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          uid: 1003,\r\n          name: \"陈七\",\r\n          tel: \"13700137003\",\r\n          money: \"80000\",\r\n          status: \"诉讼中\",\r\n          back_money: \"20000\",\r\n          un_money: \"60000\",\r\n          ctime: \"2024-01-05 09:15:00\",\r\n          address: \"广州市天河区珠江新城\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"服务费纠纷，拒绝支付约定的服务费用\",\r\n          users: {\r\n            nickname: \"孙八\"\r\n          }\r\n        },\r\n        {\r\n          id: 4,\r\n          uid: 1004,\r\n          name: \"刘九\",\r\n          tel: \"13600136004\",\r\n          money: \"200000\",\r\n          status: \"已结案\",\r\n          back_money: \"200000\",\r\n          un_money: \"0\",\r\n          ctime: \"2023-12-20 16:45:00\",\r\n          address: \"深圳市南山区科技园\",\r\n          idcard_no: \"******************\",\r\n          case_des: \"投资纠纷，已通过调解达成一致\",\r\n          users: {\r\n            nickname: \"周十\"\r\n          }\r\n        },\r\n        {\r\n          id: 5,\r\n          uid: 1005,\r\n          name: \"吴十一\",\r\n          tel: \"13500135005\",\r\n          money: \"75000\",\r\n          status: \"待处理\",\r\n          back_money: \"0\",\r\n          un_money: \"75000\",\r\n          ctime: \"2024-01-18 11:30:00\",\r\n          address: \"杭州市西湖区文三路\",\r\n          idcard_no: \"330101199406067890\",\r\n          case_des: \"租赁纠纷，拖欠房租及违约金\",\r\n          users: {\r\n            nickname: \"郑十二\"\r\n          }\r\n        },\r\n        {\r\n          id: 6,\r\n          uid: 1006,\r\n          name: \"马十三\",\r\n          tel: \"13400134006\",\r\n          money: \"150000\",\r\n          status: \"调节中\",\r\n          back_money: \"50000\",\r\n          un_money: \"100000\",\r\n          ctime: \"2024-01-12 13:20:00\",\r\n          address: \"成都市锦江区春熙路\",\r\n          idcard_no: \"510101199009091234\",\r\n          case_des: \"买卖合同纠纷，货物质量问题导致损失\",\r\n          users: {\r\n            nickname: \"冯十四\"\r\n          }\r\n        }\r\n      ],\r\n      total: 6,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n      activeDebtTab: 'details',\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n      _this.activeDebtTab = 'details';\r\n      _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.dialogViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.dialogViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      \r\n      // 开发模式：使用示例数据，不发送HTTP请求\r\n      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';\r\n      \r\n      if (isDevelopment) {\r\n        // 模拟异步加载\r\n        setTimeout(() => {\r\n          // 这里的数据已经在data()中定义了，所以直接设置loading为false\r\n          _this.loading = false;\r\n        }, 500);\r\n        return;\r\n      }\r\n      \r\n      // 生产模式：发送HTTP请求获取真实数据\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n    handleDrawerClose() {\r\n      this.dialogFormVisible = false;\r\n    },\r\n    handleDebtTabSelect(index) {\r\n      this.activeDebtTab = index;\r\n    },\r\n    getEvidenceTitle() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部证据';\r\n        case 'evidence-video':\r\n          return '视频证据';\r\n        case 'evidence-image':\r\n          return '图片证据';\r\n        case 'evidence-audio':\r\n          return '语音证据';\r\n        case 'evidence-document':\r\n          return '文档证据';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    getEvidenceTypeText() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return '全部';\r\n        case 'evidence-video':\r\n          return '视频';\r\n        case 'evidence-image':\r\n          return '图片';\r\n        case 'evidence-audio':\r\n          return '语音';\r\n        case 'evidence-document':\r\n          return '文档';\r\n        default:\r\n          return '债务人详情';\r\n      }\r\n    },\r\n    hasEvidence() {\r\n      const tab = this.activeDebtTab;\r\n      switch (tab) {\r\n        case 'evidence-all':\r\n          return this.ruleForm.cards.length > 0 || this.ruleForm.images.length > 0 || this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-video':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-image':\r\n          return this.ruleForm.images.length > 0;\r\n        case 'evidence-audio':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        case 'evidence-document':\r\n          return this.ruleForm.attach_path.length > 0;\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    uploadEvidence() {\r\n      // Implementation of uploadEvidence method\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n\r\n.drawer-content-wrapper {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n  width: 200px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n}\r\n\r\n.drawer-menu {\r\n  border: none;\r\n  background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.drawer-menu .el-menu-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.drawer-menu .el-menu-item.is-active {\r\n  background-color: #409eff;\r\n  color: white;\r\n}\r\n\r\n.drawer-menu .el-submenu .el-menu-item {\r\n  padding-left: 40px;\r\n}\r\n\r\n.drawer-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.card-header i {\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n}\r\n\r\n.evidence-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.evidence-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.evidence-section h4 {\r\n  color: #303133;\r\n  margin-bottom: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.evidence-section h5 {\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.evidence-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.evidence-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: #fff;\r\n}\r\n\r\n.evidence-item:hover {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.evidence-preview {\r\n  width: 100%;\r\n  height: 150px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.evidence-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.evidence-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.evidence-actions {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  border-color: #409eff;\r\n}\r\n\r\n.file-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #409eff;\r\n  border-radius: 6px;\r\n}\r\n\r\n.file-type-icon {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.file-info {\r\n  flex: 1;\r\n}\r\n\r\n.file-name {\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.drawer-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.no-evidence {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n  border: 2px dashed #dcdfe6;\r\n}\r\n\r\n.no-evidence i {\r\n  font-size: 48px;\r\n  margin-bottom: 15px;\r\n  display: block;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .drawer-content-wrapper {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .drawer-sidebar {\r\n    width: 100%;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n  \r\n  .evidence-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n    gap: 10px;\r\n  }\r\n  \r\n  .file-item {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .file-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA6rBA;AACA,OAAAA,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,KAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAJ,WAAA;IAAAC;EAAA;EACAI,KAAA;IACA;MACAC,YAAA;MACAC,iBAAA,0CAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,UAAA;QACAC,MAAA;MACA;MACAC,eAAA;QACAD,MAAA;MACA;MACAE,OAAA;MACAC,QAAA;MACAC,IAAA,GACA;QACAC,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,GACA;QACAZ,EAAA;QACAC,GAAA;QACAnB,IAAA;QACAoB,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,KAAA;UACAC,QAAA;QACA;MACA,EACA;MACAC,KAAA;MACAC,IAAA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,UAAA;QACAC,OAAA;MACA;MACAC,IAAA;MACAC,MAAA;QACAF,OAAA;QACAhB,MAAA;QACAmB,IAAA;QACAC,KAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,OAAA;MACAC,KAAA;MACAC,IAAA;QACAC,MAAA;QACAC,WAAA;QACAC,KAAA;QACAC,SAAA;MACA;MACAC,qBAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,oBAAA;MACAC,0BAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,oBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,iBAAA;QACAjB,KAAA;MACA;MACAkB,QAAA;QACAhB,MAAA;QACAiB,UAAA;QACAhB,WAAA;QACAiB,eAAA;QACAhB,KAAA;QACAC,SAAA;MACA;MACAgB,cAAA;QACAC,GAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAjD,MAAA,GACA;UACA+C,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MAEAC,KAAA;QACArD,GAAA,GACA;UACAkD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAvE,IAAA,GACA;UACAqE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAlD,KAAA,GACA;UACAgD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACA3C,QAAA,GACA;UACAyC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAE,cAAA;MACAC,OAAA,GACA;QACAxD,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,GACA;QACA5B,EAAA;QACA4B,KAAA;MACA,EACA;MACA6B,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,WAAAC,KAAA;MACA,KAAAA,KAAA,GAAAA,KAAA;IACA;IACAC,eAAA;MACA,KAAA9C,QAAA;MACA,KAAAC,QAAA;MACA,KAAA8C,WAAA,MAAAlB,QAAA;IACA;IAEAkB,YAAAC,QAAA;MACA,IAAAC,KAAA;MACAA,KAAA,CAAApB,QAAA,GAAAmB,QAAA;MACAC,KAAA,CACAC,WAAA,CACAD,KAAA,CAAAvC,OAAA,mBAAAuC,KAAA,CAAAjD,QAAA,cAAAiD,KAAA,CAAAhD,QAAA,EACAgD,KAAA,CAAA/C,UACA,EACAiD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA1B,iBAAA;UACA0B,KAAA,CAAApE,QAAA,GAAAuE,IAAA,CAAArF,IAAA;QACA;MACA;IACA;IACAuF,UAAAT,KAAA;MACA,KAAAU,IAAA,MAAA3B,iBAAA;MACA,KAAA2B,IAAA,MAAA3B,iBAAA;MACA,KAAA2B,IAAA,MAAA3B,iBAAA;MACA,KAAA2B,IAAA,MAAA3B,iBAAA;MACA,IAAAiB,KAAA;QACA,KAAAxB,oBAAA;QACA,KAAAF,iBAAA;QACA,SAAAS,iBAAA;UACA,KAAAR,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;QACA,KAAAA,oBAAA;QACA,KAAAC,oBAAA;QACA,SAAAO,iBAAA;UACA,KAAAT,iBAAA;QACA;UACA,KAAAA,iBAAA;QACA;MACA;IACA;IACAqC,cAAA;MACA,SAAA5B,iBAAA,qBAAAA,iBAAA;QACA;QACA,KAAA2B,IAAA,MAAA3B,iBAAA,qBAAAA,iBAAA,gBAAAA,iBAAA;MACA;IACA;IACA6B,YAAAC,UAAA;MACA,IAAAA,UAAA;QACA,KAAAH,IAAA,MAAA1B,QAAA,SAAA6B,UAAA,CAAA3E,EAAA;QACA,IAAA2E,UAAA,CAAAC,KAAA;UACA,KAAAJ,IAAA,MAAA1B,QAAA,UAAA6B,UAAA,CAAAC,KAAA;QACA;QACA,IAAAD,UAAA,CAAA/D,QAAA;UACA,KAAA4D,IAAA,MAAA1B,QAAA,WAAA6B,UAAA,CAAA/D,QAAA;QACA;QACA,KAAA4B,iBAAA;QACA,KAAAN,qBAAA;MACA;IACA;IACA2C,aAAAf,KAAA;MACA,IAAAA,KAAA,SAAAA,KAAA;QACA,SAAAjB,iBAAA;UACA,KAAAR,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;MACA,IAAAyB,KAAA;QACA,SAAAjB,iBAAA;UACA,KAAAT,iBAAA;QACA;UACA,KAAAA,iBAAA;QACA;MACA;MACA,IAAA0B,KAAA;QACA,KAAA1B,iBAAA;QACA,KAAAC,oBAAA;QACA,SAAAQ,iBAAA;UACA,KAAAP,oBAAA;QACA;UACA,KAAAA,oBAAA;QACA;MACA;IACA;IACAwC,UAAA;MACA,KAAAxD,MAAA;QACAF,OAAA;QACAhB,MAAA;QACAmB,IAAA;QACAC,KAAA;MACA;MACA,KAAAmC,OAAA;IACA;IACAoB,SAAA/E,EAAA;MACA,IAAAkE,KAAA;MACA,IAAAlE,EAAA;QACA,KAAAgF,OAAA,CAAAhF,EAAA;MACA;QACA,KAAA8C,QAAA;UACAhB,MAAA;UACAiB,UAAA;UACAhB,WAAA;UACAiB,eAAA;UACAhB,KAAA;UACAC,SAAA;QACA;MACA;MACAiC,KAAA,CAAAT,aAAA;MACAS,KAAA,CAAA1B,iBAAA;IACA;IACAyC,aAAAjF,EAAA;MACA,IAAAkE,KAAA;MACA,IAAAlE,EAAA;QACA,KAAAe,SAAA,GAAAf,EAAA;MACA;MAEAkE,KAAA,CAAA/B,oBAAA;IACA;IACA+C,aAAAlF,EAAA;MACA,IAAAkE,KAAA;MACA,IAAAlE,EAAA;QACA,KAAAgB,aAAA,GAAAhB,EAAA;MACA;MAEAkE,KAAA,CAAAxB,oBAAA;IACA;IACAyC,kBAAAnF,EAAA;MACA,IAAAA,EAAA;QACA,KAAAoF,gBAAA,CAAApF,EAAA;MACA;QACA,KAAA6C,iBAAA;UACA/D,IAAA;QACA;MACA;IACA;IACAuG,SAAArF,EAAA;MACA,IAAAA,EAAA;QACA,KAAAsF,OAAA,CAAAtF,EAAA;MACA;QACA,KAAA8C,QAAA;UACAlB,KAAA;UACA2D,IAAA;QACA;MACA;IACA;IACAD,QAAAtF,EAAA;MACA,IAAAkE,KAAA;MACAA,KAAA,CAAAsB,UAAA,CAAAtB,KAAA,CAAAxC,GAAA,gBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAArC,IAAA,GAAAwC,IAAA,CAAArF,IAAA;UACAkF,KAAA,CAAAzB,eAAA;UACAyB,KAAA,CAAAjF,YAAA,8BAAAe,EAAA,oBAAAb,MAAA,CAAAC,OAAA,CAAAC,SAAA;QACA;UACA6E,KAAA,CAAAuB,QAAA;YACAC,IAAA;YACAtC,OAAA,EAAAiB,IAAA,CAAAsB;UACA;QACA;MACA;IACA;IACAX,QAAAhF,EAAA;MACA,IAAAkE,KAAA;MACAA,KAAA,CAAAsB,UAAA,CAAAtB,KAAA,CAAAxC,GAAA,gBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAApB,QAAA,GAAAuB,IAAA,CAAArF,IAAA;UACA4G,OAAA,CAAAC,GAAA,CAAAxB,IAAA,CAAArF,IAAA;QACA;UACAkF,KAAA,CAAAuB,QAAA;YACAC,IAAA;YACAtC,OAAA,EAAAiB,IAAA,CAAAsB;UACA;QACA;MACA;IACA;IACAP,iBAAApF,EAAA;MACA,IAAAkE,KAAA;MACAA,KAAA,CAAAsB,UAAA,CAAAtB,KAAA,CAAAxC,GAAA,yBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAArB,iBAAA,GAAAwB,IAAA,CAAArF,IAAA;UACAkF,KAAA,CAAA9B,iBAAA;UACA8B,KAAA,CAAA7B,oBAAA;UACA6B,KAAA,CAAA5B,oBAAA;UACA4B,KAAA,CAAA3B,0BAAA;QACA;UACA2B,KAAA,CAAAuB,QAAA;YACAC,IAAA;YACAtC,OAAA,EAAAiB,IAAA,CAAAsB;UACA;QACA;MACA;IACA;IACAG,QAAA9F,EAAA;MACA,KAAA+F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GACAtB,IAAA;QACA,KAAA8B,aAAA,MAAAxE,GAAA,mBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAmB,QAAA;cACAC,IAAA;cACAtC,OAAA,EAAAiB,IAAA,CAAAsB;YACA;UACA;YACA,KAAAF,QAAA;cACAC,IAAA;cACAtC,OAAA,EAAAiB,IAAA,CAAAsB;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACA,KAAAV,QAAA;UACAC,IAAA;UACAtC,OAAA;QACA;MACA;IACA;IACAgD,QAAAC,KAAA,EAAArG,EAAA;MACA,KAAA+F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GACAtB,IAAA;QACA,KAAA8B,aAAA,MAAAxE,GAAA,kBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAmB,QAAA;cACAC,IAAA;cACAtC,OAAA;YACA;YACA,KAAAO,OAAA;YACA,KAAA9B,IAAA,CAAAI,SAAA,CAAAqE,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAV,QAAA;UACAC,IAAA;UACAtC,OAAA;QACA;MACA;IACA;IACAmD,YAAAF,KAAA,EAAArG,EAAA;MACA,KAAA+F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GACAtB,IAAA;QACA,KAAA8B,aAAA,MAAAxE,GAAA,sBAAA1B,EAAA,EAAAoE,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAmB,QAAA;cACAC,IAAA;cACAtC,OAAA;YACA;YACA,KAAAO,OAAA;YACA,KAAA9B,IAAA,CAAAI,SAAA,CAAAqE,MAAA,CAAAD,KAAA;UACA;QACA;MACA,GACAF,KAAA;QACA,KAAAV,QAAA;UACAC,IAAA;UACAtC,OAAA;QACA;MACA;IACA;IACAoD,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA7F,IAAA;MACA,KAAAO,IAAA;MACA,KAAAsC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAO,KAAA;MAEAA,KAAA,CAAAzC,OAAA;;MAEA;MACA,MAAAmF,aAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,sBAAAC,MAAA,CAAAC,QAAA,CAAAC,QAAA;MAEA,IAAAN,aAAA;QACA;QACAO,UAAA;UACA;UACAjD,KAAA,CAAAzC,OAAA;QACA;QACA;MACA;;MAEA;MACAyC,KAAA,CACAC,WAAA,CACAD,KAAA,CAAAxC,GAAA,mBAAAwC,KAAA,CAAApD,IAAA,cAAAoD,KAAA,CAAA7C,IAAA,EACA6C,KAAA,CAAA5C,MACA,EACA8C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAnE,IAAA,GAAAsE,IAAA,CAAArF,IAAA;UACAkF,KAAA,CAAArD,KAAA,GAAAwD,IAAA,CAAA+C,KAAA;QACA;QACAlD,KAAA,CAAAzC,OAAA;MACA;IACA;IACA4F,SAAA;MACA,IAAAnD,KAAA;MACA,KAAAoD,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAArD,WAAA,CAAAD,KAAA,CAAAxC,GAAA,gBAAAoB,QAAA,EAAAsB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAJ,KAAA,CAAAuB,QAAA;gBACAC,IAAA;gBACAtC,OAAA,EAAAiB,IAAA,CAAAsB;cACA;cACA,KAAAhC,OAAA;cACAO,KAAA,CAAA1B,iBAAA;YACA;cACA0B,KAAA,CAAAuB,QAAA;gBACAC,IAAA;gBACAtC,OAAA,EAAAiB,IAAA,CAAAsB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA8B,kBAAA;MACA,IAAAvD,KAAA;MACA,KAAAoD,KAAA,sBAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAA3E,iBAAA,YAAAhE,KAAA,CAAAO,OAAA,CAAAC,SAAA;UACA,KAAA8E,WAAA,CAAAD,KAAA,CAAAxC,GAAA,yBAAAmB,iBAAA,EAAAuB,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAJ,KAAA,CAAAuB,QAAA;gBACAC,IAAA;gBACAtC,OAAA,EAAAiB,IAAA,CAAAsB;cACA;cACA,KAAAhC,OAAA;cACAO,KAAA,CAAA9B,iBAAA;cACA8B,KAAA,CAAA7B,oBAAA;cACA6B,KAAA,CAAA5B,oBAAA;cACA4B,KAAA,CAAA3B,0BAAA;YACA;cACA2B,KAAA,CAAAuB,QAAA;gBACAC,IAAA;gBACAtC,OAAA,EAAAiB,IAAA,CAAAsB;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA+B,iBAAAC,GAAA;MACA,KAAAtG,IAAA,GAAAsG,GAAA;MAEA,KAAAhE,OAAA;IACA;IACAiE,oBAAAD,GAAA;MACA,KAAA7G,IAAA,GAAA6G,GAAA;MACA,KAAAhE,OAAA;IACA;IACAkE,cAAAC,GAAA;MACA,IAAAA,GAAA,CAAAxD,IAAA;QACA,KAAAmB,QAAA,CAAAsC,OAAA;QACA,IAAAC,GAAA,QAAAlF,QAAA,MAAAgB,KAAA;QAEA,KAAAhB,QAAA,MAAAgB,KAAA,EAAAwC,MAAA,OAAAwB,GAAA,CAAA9I,IAAA,CAAA0C,GAAA;QACA;MACA;QACA,KAAA+D,QAAA,CAAAwC,KAAA,CAAAH,GAAA,CAAAnC,GAAA;MACA;IACA;IAEAuC,UAAAC,IAAA;MACA,KAAAxF,UAAA,GAAAwF,IAAA;MACA,KAAAvF,aAAA;IACA;IAEAwF,aAAA;MACA,KAAArE,cAAA;MACA,KAAA7B,qBAAA;IACA;IACAmG,aAAAF,IAAA;MACA,MAAAG,UAAA,6BAAAC,IAAA,CAAAJ,IAAA,CAAAzC,IAAA;MACA,KAAA4C,UAAA;QACA,KAAA7C,QAAA,CAAAwC,KAAA;QACA;MACA;IACA;IACAO,SAAAL,IAAA,EAAAM,QAAA,EAAApC,KAAA;MACA,IAAAnC,KAAA;MACAA,KAAA,CAAAsB,UAAA,gCAAA2C,IAAA,EAAA/D,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAJ,KAAA,CAAApB,QAAA,CAAA2F,QAAA,EAAAnC,MAAA,CAAAD,KAAA;UACAnC,KAAA,CAAAuB,QAAA,CAAAsC,OAAA;QACA;UACA7D,KAAA,CAAAuB,QAAA,CAAAwC,KAAA,CAAA5D,IAAA,CAAAsB,GAAA;QACA;MACA;IACA;IACA+C,iBAAA;MAAAC,MAAA;MAAApH,IAAA;MAAAC;IAAA;MACA,KAAAF,MAAA,CAAAC,IAAA,GAAAA,IAAA;MACA,KAAAD,MAAA,CAAAE,KAAA,GAAAA,KAAA;MACA,KAAAmC,OAAA;MACA;MACA;IACA;IACAiF,OAAA,WAAAA,CAAA;MAAA;MACA,IAAA1E,KAAA;MACA+C,QAAA,CAAA4B,IAAA,+BAAA3E,KAAA,CAAA/E,MAAA,CAAAC,OAAA,CAAAC,SAAA,qBAAA6E,KAAA,CAAApB,QAAA,CAAA9C,EAAA;IACA;IACA8I,eAAA,WAAAA,CAAA;MAAA;MACA,IAAA5E,KAAA;MACA+C,QAAA,CAAA4B,IAAA,qCAAA3E,KAAA,CAAA/E,MAAA,CAAAC,OAAA,CAAAC,SAAA,iBAAA6E,KAAA,CAAA5C,MAAA,CAAAF,OAAA;IACA;IACA2H,kBAAA;MAAA;MACA,KAAAzJ,aAAA;MACA,KAAAgI,KAAA,CAAA0B,MAAA,CAAAC,UAAA;MACA,KAAAvJ,UAAA,CAAAC,MAAA;IACA;IACAuJ,uBAAA;MAAA;MACA,KAAA3J,kBAAA;MACA,KAAA+H,KAAA,CAAA0B,MAAA,CAAAC,UAAA;MACA,KAAArJ,eAAA,CAAAD,MAAA;IACA;IACAwJ,cAAAC,QAAA;MAAA;MACA,IAAAA,QAAA,CAAA9E,IAAA;QACA,KAAAmB,QAAA;UACAC,IAAA;UACAtC,OAAA,EAAAgG,QAAA,CAAAzD;QACA;QACA,KAAArG,aAAA;QACA,KAAAqE,OAAA;QACAiC,OAAA,CAAAC,GAAA,CAAAuD,QAAA;MACA;QACA,KAAA3D,QAAA;UACAC,IAAA;UACAtC,OAAA,EAAAgG,QAAA,CAAAzD;QACA;MACA;MAEA,KAAAnG,mBAAA;MACA,KAAA8H,KAAA,CAAA0B,MAAA,CAAAC,UAAA;IACA;IACAI,mBAAAD,QAAA;MAAA;MACA,IAAAA,QAAA,CAAA9E,IAAA;QACA,KAAAmB,QAAA;UACAC,IAAA;UACAtC,OAAA,EAAAgG,QAAA,CAAAzD;QACA;QACA,KAAApG,kBAAA;QACA,KAAAoE,OAAA;QACAiC,OAAA,CAAAC,GAAA,CAAAuD,QAAA;MACA;QACA,KAAA3D,QAAA;UACAC,IAAA;UACAtC,OAAA,EAAAgG,QAAA,CAAAzD;QACA;MACA;MAEA,KAAAlG,mBAAA;MACA,KAAA6H,KAAA,CAAA0B,MAAA,CAAAC,UAAA;IACA;IACAK,UAAAnB,IAAA;MAAA;MACA,IAAAoB,QAAA;MACA,IAAA7D,IAAA,GAAAyC,IAAA,CAAArJ,IAAA,CAAA0K,KAAA,MAAAC,KAAA,QAAAC,WAAA;MACA,KAAAH,QAAA,CAAAI,QAAA,CAAAjE,IAAA;QACA,KAAAD,QAAA;UACAC,IAAA;UACAtC,OAAA;QACA;QACA;MACA;MACA;IACA;IACAwG,aAAA;MAAA;MACA,KAAApK,mBAAA;MACA,KAAA8H,KAAA,CAAA0B,MAAA,CAAAa,MAAA;IACA;IACAC,kBAAA;MAAA;MACA,KAAArK,mBAAA;MACA,KAAA6H,KAAA,CAAA0B,MAAA,CAAAa,MAAA;IACA;IACAE,YAAA;MAAA;MACA,KAAAC,UAAA;MACA,KAAA1K,aAAA;MACA,KAAA2K,IAAA;QACAjK,EAAA;QACAY,QAAA;QACAsJ,MAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,GAAA;QACAC,OAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA,KAAAvD,KAAA,CAAA2C,IAAA,CAAAa,WAAA;IACA;IACAC,WAAA;MAAA;MACA,KAAAzL,aAAA;IACA;IACA0L,gBAAA;MAAA;MACA,KAAAzL,kBAAA;IACA;IACA0L,kBAAA;MACA,KAAAzI,iBAAA;IACA;IACA0I,oBAAA7E,KAAA;MACA,KAAA5C,aAAA,GAAA4C,KAAA;IACA;IACA8E,iBAAA;MACA,MAAAC,GAAA,QAAA3H,aAAA;MACA,QAAA2H,GAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAC,oBAAA;MACA,MAAAD,GAAA,QAAA3H,aAAA;MACA,QAAA2H,GAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAE,YAAA;MACA,MAAAF,GAAA,QAAA3H,aAAA;MACA,QAAA2H,GAAA;QACA;UACA,YAAAtI,QAAA,CAAAd,KAAA,CAAAuJ,MAAA,aAAAzI,QAAA,CAAAhB,MAAA,CAAAyJ,MAAA,aAAAzI,QAAA,CAAAf,WAAA,CAAAwJ,MAAA;QACA;UACA,YAAAzI,QAAA,CAAAhB,MAAA,CAAAyJ,MAAA;QACA;UACA,YAAAzI,QAAA,CAAAhB,MAAA,CAAAyJ,MAAA;QACA;UACA,YAAAzI,QAAA,CAAAf,WAAA,CAAAwJ,MAAA;QACA;UACA,YAAAzI,QAAA,CAAAf,WAAA,CAAAwJ,MAAA;QACA;UACA;MACA;IACA;IACAC,eAAA;MACA;IAAA;EAEA;AACA", "ignoreList": []}]}