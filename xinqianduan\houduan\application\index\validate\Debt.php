<?php
namespace app\index\validate;

use think\Validate;

class Debt extends Validate
{
    protected $rule =   [
        'name'=>'require|max:50',
        'idcard_no'=>'max:18|checkIdcard_no:请输入正确的身份证号码',
    	'tel'=>'checkTel:请输入正确的手机号码',
    	'address'=>'max:100',
    	'money'=>'require|max:10',
    	'case_des'=>'require|max:1000',
    ];
    
    protected $message  =   [
      	'name.require' => '姓名必填',
        'address.require' => '地址必填',
        'money.require' => '金额必填',
        'case_des.require' => '案由必填',
        'name.max'    => '姓名支持50字内',
        'idcard_no.max'    => '身份证号支持18字内',
        'address.max'    => '地址支持100字内',
        'money.max'    => '金额支持10字内',
        'case_des.max'    => '案由支持1000字内',
    ];

    /**
     * 应用场景
     * @var array
     */
    protected $scene = [
        'add' => ['name','idcard_no','phone','address','money','case_des']
    ];

    protected  function checkTel($value,$rule)
	{
	
		if(!preg_match("/^1[3456789]\d{9}$/", $value)) return $rule;
		else return true;
			
	}

    protected  function checkIdcard_no($value,$rule)
    {
        $pattern = '#^([\d]{17}[xX\d])$#';
        if (preg_match($pattern, $value)) {
            return true;
        } else {
            return $rule;
        }

    }

}