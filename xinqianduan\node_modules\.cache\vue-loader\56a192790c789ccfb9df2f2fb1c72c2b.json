{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue?vue&type=style&index=0&id=56f25456&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\src\\views\\pages\\shipin\\kecheng.vue", "mtime": 1748336508332}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748336490492}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748336507382}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748336500812}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748336484514}, {"path": "C:\\Users\\<USER>\\Desktop\\fdbqd\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748336507381}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucGFnZS10b3Agew0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KfQ0KDQouZWxfaW5wdXQgew0KICB3aWR0aDogNDc1cHg7DQp9DQo="}, {"version": 3, "sources": ["kecheng.vue"], "names": [], "mappings": ";AAmbA;AACA;AACA;;AAEA;AACA;AACA", "file": "kecheng.vue", "sourceRoot": "src/views/pages/shipin", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n        <el-table-column prop=\"price\" label=\"价格\"> </el-table-column>\r\n        <el-table-column prop=\"pic_path\" label=\"封面\">\r\n          <template slot-scope=\"scope\">\r\n            <img\r\n              :src=\"scope.row.pic_path\"\r\n              style=\"width: 160px; height: 80px\"\r\n              @click=\"showImage(scope.row.pic_path)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否免费\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_free\" :label=\"2\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"首页热门\" :label-width=\"formLabelWidth\">\r\n          <div>\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"1\">是</el-radio>\r\n\r\n            <el-radio v-model=\"ruleForm.is_hot\" :label=\"0\">否</el-radio>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"价格\"\r\n          :label-width=\"formLabelWidth\"\r\n          v-if=\"ruleForm.is_free == 2\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"封面\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"pic_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.pic_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('pic_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"showImage(ruleForm.pic_path)\"\r\n              >查看\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.pic_path\"\r\n              @click=\"delImage(ruleForm.pic_path, 'pic_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"课程视频\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"showImage(ruleForm.file_path)\"\r\n              >查看\r\n            </el-button> -->\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            autocomplete=\"off\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/kecheng/\",\r\n      title: \"课程\",\r\n      info: {},\r\n      filed: \"\",\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        pic_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传封面\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传视频\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n      console.log(this.filed);\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n          is_free: 2,\r\n          file_path: \"\",\r\n          pic_path: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.filed] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      let type = file.type;\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      } else {\r\n        if (\r\n          !file.type.split(\"/\")[1] == \"mp4\" ||\r\n          !file.type.split(\"/\")[1] == \"qlv\" ||\r\n          !file.type.split(\"/\")[1] == \"qsv\" ||\r\n          !file.type.split(\"/\")[1] == \"oga\" ||\r\n          !file.type.split(\"/\")[1] == \"flv\" ||\r\n          !file.type.split(\"/\")[1] == \"avi\" ||\r\n          !file.type.split(\"/\")[1] == \"wmv\" ||\r\n          !file.type.split(\"/\")[1] == \"rmvb\"\r\n        ) {\r\n          this.$message({\r\n            showClose: true,\r\n            message: \"请选择'.mp4,.qlv,.qsv,.oga,.flv,.avi,.wmv,.rmvb'文件\",\r\n            type: \"error\",\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}