{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\data\\banner.vue?vue&type=template&id=7c6c0c88&scoped=true", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\data\\banner.vue", "mtime": 1732626900077}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748278552278}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}