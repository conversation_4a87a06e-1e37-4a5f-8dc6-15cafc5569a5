{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\wenshu\\index.vue", "mtime": 1748425644038}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/pages/wenshu", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row style=\"width: 600px\">\r\n        <el-input placeholder=\"请输入内容\" v-model=\"search.keyword\" size=\"mini\">\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n          ></el-button>\r\n        </el-input>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n          >新增</el-button\r\n        >\r\n      </el-row>\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n      >\r\n        <el-table-column prop=\"title\" label=\"标题\"> </el-table-column>\r\n\r\n        <el-table-column prop=\"title\" label=\"文书标题\"> </el-table-column>\r\n        <el-table-column prop=\"cate_id\" label=\"文书类型\"> </el-table-column>\r\n        <el-table-column prop=\"price\" label=\"价格\"> </el-table-column>\r\n        <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n            <el-button\r\n              @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n              type=\"text\"\r\n              size=\"small\"\r\n            >\r\n              移除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      :title=\"title + '内容'\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n        <el-form-item\r\n          label=\"文书类型\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cate_id\"\r\n        >\r\n          <el-select v-model=\"ruleForm.cate_id\" placeholder=\"请选择\" filterable>\r\n            <el-option value=\"\">请选择</el-option>\r\n            <el-option\r\n              v-for=\"(item, index) in cates\"\r\n              :key=\"index\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item\r\n          :label=\"title + '标题'\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"title\"\r\n        >\r\n          <el-input v-model=\"ruleForm.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"文件上传\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"file_path\"\r\n        >\r\n          <el-input\r\n            v-model=\"ruleForm.file_path\"\r\n            :disabled=\"true\"\r\n            class=\"el_input\"\r\n          ></el-input>\r\n          <el-button-group>\r\n            <el-button @click=\"changefield('file_path')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n\r\n            <el-button\r\n              type=\"danger\"\r\n              v-if=\"ruleForm.file_path\"\r\n              @click=\"delImage(ruleForm.file_path, 'file_path')\"\r\n              >删除</el-button\r\n            >\r\n          </el-button-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"价格\" :label-width=\"formLabelWidth\">\r\n          <el-input\r\n            v-model=\"ruleForm.price\"\r\n            autocomplete=\"off\"\r\n            type=\"number\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"内容\" :label-width=\"formLabelWidth\">\r\n          <editor-bar\r\n            v-model=\"ruleForm.content\"\r\n            :isClear=\"isClear\"\r\n            @change=\"change\"\r\n          ></editor-bar>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport EditorBar from \"/src/components/wangEnduit.vue\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { EditorBar },\r\n  data() {\r\n    return {\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/wenshu/\",\r\n      field: \"\",\r\n      title: \"文书\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      isClear: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n      },\r\n\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写标题\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        cate_id: [\r\n          {\r\n            required: true,\r\n            message: \"请选择文书类型\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        file_path: [\r\n          {\r\n            required: true,\r\n            message: \"请上传文件\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      cates: [],\r\n      expireTimeOption: {\r\n        disabledDate(date) {\r\n          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean\r\n          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n\r\n  methods: {\r\n    change() {},\r\n    changefield(field) {\r\n      this.field = field;\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n      _this.getLvshi();\r\n      _this.dialogFormVisible = true;\r\n    },\r\n\r\n    getLvshi() {\r\n      this.postRequest(\"/wenshucate/getList\", {}).then((resp) => {\r\n        if (resp.code == 200) {\r\n          this.cates = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        this.ruleForm[this.field] = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      if (this.filed == \"pic_path\") {\r\n        const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(type);\r\n        if (!isTypeTrue) {\r\n          this.$message.error(\"上传图片格式不对!\");\r\n          return;\r\n        }\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}