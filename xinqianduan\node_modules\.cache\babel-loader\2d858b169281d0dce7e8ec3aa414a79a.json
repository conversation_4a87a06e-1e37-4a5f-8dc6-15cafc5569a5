{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\yonghu\\user.vue", "mtime": 1748425644044}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UserDetails", "name", "components", "data", "uploadAction", "$store", "getters", "GET_TOKEN", "uploadVisible", "submitOrderLoading2", "uploadData", "review", "fileList", "allSize", "list", "total", "page", "size", "currentId", "currentUserInfo", "search", "nickname", "phone", "linkman", "linkphone", "company", "yuangong_id", "date<PERSON><PERSON><PERSON>", "prop", "order", "is_del", "loading", "url", "title", "info", "selectedUsers", "dialogFormVisible", "dialogViewUserDetail", "dialogAddUser", "drawerViewVisible", "drawerEditVisible", "isEditMode", "editForm", "originalUserInfo", "activeTab", "show_image", "dialogVisible", "debtDialogVisible", "debtDialogTitle", "isEditingDebt", "editingDebtIndex", "debtForm", "tel", "money", "status", "debtRules", "required", "message", "trigger", "pattern", "ruleForm", "is_num", "rules", "form<PERSON>abe<PERSON><PERSON>", "dialogFormOrder", "taocans", "tia<PERSON><PERSON><PERSON>", "fawus", "lians", "htsczy", "ls", "ywy", "orderForm", "client_id", "taocan_id", "tiaojie_id", "fawu_id", "lian_id", "htsczy_id", "ls_id", "ywy_id", "total_price", "pay_price", "pay_path", "desc", "pay_type", "qishu", "taocan_year", "taocan_content", "taocan_type", "fenqi", "date", "price", "rules2", "mounted", "addTestData", "methods", "getOriginalTestData", "id", "end_time", "create_time", "last_login_time", "headimg", "license", "start_time", "year", "lian_name", "tiaojie_name", "fawu_name", "htsczy_name", "ls_name", "ywy_name", "debts", "attachments", "idCard", "others", "type", "length", "filterTestData", "originalData", "filteredData", "toLowerCase", "filter", "user", "includes", "startDate", "Date", "endDate", "createDate", "split", "hasSearchCondition", "$message", "success", "row", "$nextTick", "getTaocans", "saveData2", "_this", "$refs", "validate", "valid", "postRequest", "then", "resp", "code", "msg", "changeTaocan", "e", "getRequest", "getYuangongs", "item", "zhiwei_id", "viewData", "console", "log", "find", "handleTabSelect", "key", "toggleEditMode", "JSON", "parse", "stringify", "cancelEdit", "saveUserData", "index", "findIndex", "splice", "error", "editData", "getInfo", "handleDrawerClose", "addDebt", "editDebt", "debt", "parseFloat", "saveDebt", "debtData", "toString", "push", "userIndex", "closeDebtDialog", "clearValidate", "deleteDebt", "$confirm", "confirmButtonText", "cancelButtonText", "addAttachment", "uploadIdCard", "createFileInput", "files", "handleFileUpload", "uploadLicense", "uploadOthers", "accept", "callback", "input", "document", "createElement", "multiple", "style", "display", "onchange", "Array", "from", "target", "body", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "click", "typeName", "warning", "file", "startsWith", "for<PERSON>ach", "fileUrl", "URL", "createObjectURL", "setTimeout", "fileData", "uploadTime", "toLocaleString", "deleteAttachment", "revokeObjectURL", "downloadFile", "link", "href", "download", "getFileIcon", "fileType", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "pow", "toFixed", "getDebtCount", "getDebtCountType", "count", "getTotalDebtAmount", "reduce", "formatAmount", "amount", "minimumFractionDigits", "maximumFractionDigits", "delData", "catch", "refulsh", "$router", "go", "searchData", "resetSearch", "getData", "saveData", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "pic_path", "showImage", "beforeUpload", "isTypeTrue", "test", "delImage", "fileName", "handleSortChange", "column", "exports", "location", "keyword", "closeUploadDialog", "upload", "clearFiles", "uploadSuccess", "response", "checkFile", "slice", "submitUpload", "submit", "closeDialog", "addVisible", "form", "mobile", "school_id", "grade_id", "class_id", "sex", "is_poor", "is_display", "number", "remark", "is_remark_option", "remark_option", "mobile_checked", "resetFields", "openUpload", "isExcel", "isLt10M", "handleUploadSuccess", "handleUploadError", "err", "addUser", "downloadTemplate", "templateUrl", "handleSelectionChange", "selection", "exportSelectedData", "exportUrl", "userIds", "map", "join", "batchDeleteUsers", "dangerouslyUseHTMLString", "userTable", "clearSelection"], "sources": ["src/views/pages/yonghu/user.vue"], "sourcesContent": ["<template>\r\n    <div class=\"page-wrapper\">\r\n        <div class=\"page-container\">\r\n            <!-- 页面标题 -->\r\n            <div class=\"page-title\">\r\n                {{ this.$router.currentRoute.name }}\r\n                <el-button\r\n                        style=\"float: right\"\r\n                        type=\"text\"\r\n                        @click=\"refulsh\"\r\n                        icon=\"el-icon-refresh\"\r\n                >刷新\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 搜索筛选区域 -->\r\n            <div class=\"search-container\">\r\n                <el-form :model=\"search\" class=\"search-form\" label-width=\"80px\">\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户名称\">\r\n                                <el-input\r\n                                    v-model=\"search.nickname\"\r\n                                    placeholder=\"请输入用户名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"手机号码\">\r\n                                <el-input\r\n                                    v-model=\"search.phone\"\r\n                                    placeholder=\"请输入手机号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"公司名称\">\r\n                                <el-input\r\n                                    v-model=\"search.company\"\r\n                                    placeholder=\"请输入公司名称\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"用户来源\">\r\n                                <el-select\r\n                                    v-model=\"search.yuangong_id\"\r\n                                    placeholder=\"请选择来源\"\r\n                                    clearable\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                    <el-option label=\"小程序注册\" value=\"小程序注册\"></el-option>\r\n                                    <el-option label=\"后台创建\" value=\"后台创建\"></el-option>\r\n                                    <el-option label=\"直接注册\" value=\"直接注册\"></el-option>\r\n                                </el-select>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                    <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系人\">\r\n                                <el-input\r\n                                    v-model=\"search.linkman\"\r\n                                    placeholder=\"请输入联系人\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"6\">\r\n                            <el-form-item label=\"联系号码\">\r\n                                <el-input\r\n                                    v-model=\"search.linkphone\"\r\n                                    placeholder=\"请输入联系号码\"\r\n                                    clearable\r\n                                    size=\"small\">\r\n                                </el-input>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                            <el-form-item label=\"注册时间\">\r\n                                <el-date-picker\r\n                                    v-model=\"search.dateRange\"\r\n                                    type=\"daterange\"\r\n                                    range-separator=\"至\"\r\n                                    start-placeholder=\"开始日期\"\r\n                                    end-placeholder=\"结束日期\"\r\n                                    format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    size=\"small\"\r\n                                    style=\"width: 100%\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                            <el-form-item>\r\n                                <div class=\"search-buttons\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        icon=\"el-icon-search\"\r\n                                        @click=\"searchData()\"\r\n                                        size=\"small\">\r\n                                        搜索\r\n                                    </el-button>\r\n                                    <el-button\r\n                                        icon=\"el-icon-refresh\"\r\n                                        @click=\"resetSearch()\"\r\n                                        size=\"small\">\r\n                                        重置\r\n                                    </el-button>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </el-col>\r\n                    </el-row>\r\n                </el-form>\r\n\r\n                <!-- 操作按钮区域 -->\r\n                <div class=\"action-buttons\">\r\n                    <el-button\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        icon=\"el-icon-download\"\r\n                        @click=\"exportSelectedData\">\r\n                        {{ selectedUsers.length > 0 ? `导出选中数据 (${selectedUsers.length})` : '导出全部数据' }}\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\" @click=\"openUpload\">\r\n                        导入用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-plus\" @click=\"addUser\">\r\n                        添加用户\r\n                    </el-button>\r\n                    <el-button size=\"small\" type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 数据表格 -->\r\n            <div class=\"data-table\">\r\n                <el-table\r\n                        :data=\"list\"\r\n                        style=\"width: 100%\"\r\n                        v-loading=\"loading\"\r\n                        @sort-change=\"handleSortChange\"\r\n                        @selection-change=\"handleSelectionChange\"\r\n                        :border=\"true\"\r\n                        :header-cell-style=\"{background:'#fafafa',color:'#606266'}\"\r\n                        ref=\"userTable\"\r\n                >\r\n                    <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\r\n                    <el-table-column prop=\"\" label=\"头像\" width=\"80\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"avatar-container\">\r\n                                <div v-if=\"scope.row.headimg && scope.row.headimg !== ''\" class=\"avatar-wrapper\">\r\n                                    <img\r\n                                        class=\"user-avatar\"\r\n                                        :src=\"scope.row.headimg\"\r\n                                        @click=\"showImage(scope.row.headimg)\"\r\n                                    />\r\n                                </div>\r\n                                <div v-else class=\"no-avatar\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                </div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nickname\" label=\"用户名称\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"user-info\">\r\n                                <div class=\"user-name clickable\" @click=\"viewData(scope.row.id)\">{{ scope.row.nickname || '未设置' }}</div>\r\n                                <div class=\"user-phone\">{{ scope.row.phone }}</div>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"company\" label=\"公司名称\" sortable min-width=\"150\"></el-table-column>\r\n                    <el-table-column prop=\"linkman\" label=\"联系人\" sortable min-width=\"100\"></el-table-column>\r\n                    <el-table-column prop=\"linkphone\" label=\"联系号码\" sortable min-width=\"120\"></el-table-column>\r\n                    <el-table-column prop=\"yuangong_id\" label=\"用户来源\" min-width=\"100\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ scope.row.yuangong_id || '直接注册' }}</el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务人数量\" min-width=\"100\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag size=\"small\" :type=\"getDebtCountType(scope.row.debts)\">\r\n                                {{ getDebtCount(scope.row.debts) }}人\r\n                            </el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"债务总金额\" min-width=\"120\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"debt-amount\" :class=\"{ 'has-debt': getTotalDebtAmount(scope.row.debts) > 0 }\">\r\n                                ¥{{ formatAmount(getTotalDebtAmount(scope.row.debts)) }}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"end_time\" label=\"到期时间\" min-width=\"120\" sortable></el-table-column>\r\n                    <el-table-column prop=\"create_time\" label=\"注册时间\" sortable min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.create_time }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最后登录\" min-width=\"120\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span>{{ scope.row.last_login_time || '未登录' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"200\" align=\"center\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"action-buttons-table\">\r\n                                <el-button type=\"primary\" size=\"mini\" @click=\"viewData(scope.row.id)\">\r\n                                    查看\r\n                                </el-button>\r\n                                <el-button type=\"success\" size=\"mini\" @click=\"editData(scope.row.id)\">\r\n                                    编辑\r\n                                </el-button>\r\n                                <el-dropdown trigger=\"click\">\r\n                                    <el-button size=\"mini\">\r\n                                        更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu slot=\"dropdown\">\r\n                                        <el-dropdown-item @click.native=\"order(scope.row)\">制作订单</el-dropdown-item>\r\n                                        <el-dropdown-item v-if=\"is_del\" @click.native=\"delData(scope.$index, scope.row.id)\" style=\"color: #f56c6c;\">移除用户</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n                <div class=\"pagination-info\">\r\n                    <span>共 {{ total }} 条</span>\r\n                    <span>第 {{ page }} 页</span>\r\n                </div>\r\n                <el-pagination\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"size\"\r\n                        :current-page=\"page\"\r\n                        layout=\"sizes, prev, pager, next, jumper\"\r\n                        :total=\"total\"\r\n                        background\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 用户详情抽屉 -->\r\n        <el-drawer\r\n            title=\"用户详情\"\r\n            :visible.sync=\"drawerViewVisible\"\r\n            direction=\"rtl\"\r\n            size=\"60%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content-wrapper\">\r\n                <!-- 左侧导航菜单 -->\r\n                <div class=\"drawer-sidebar\">\r\n                    <el-menu\r\n                        :default-active=\"activeTab\"\r\n                        class=\"drawer-menu\"\r\n                        @select=\"handleTabSelect\">\r\n                        <el-menu-item index=\"customer\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>客户信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"member\">\r\n                            <i class=\"el-icon-medal\"></i>\r\n                            <span>会员信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"debts\">\r\n                            <i class=\"el-icon-document\"></i>\r\n                            <span>债务人信息</span>\r\n                        </el-menu-item>\r\n                        <el-menu-item index=\"attachments\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>附件信息</span>\r\n                        </el-menu-item>\r\n                    </el-menu>\r\n                </div>\r\n\r\n                <!-- 右侧内容区域 -->\r\n                <div class=\"drawer-content\">\r\n                    <!-- 编辑模式切换按钮 -->\r\n                    <div class=\"edit-mode-toggle\" v-if=\"activeTab === 'customer'\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            :icon=\"isEditMode ? 'el-icon-view' : 'el-icon-edit'\"\r\n                            @click=\"toggleEditMode\">\r\n                            {{ isEditMode ? '查看模式' : '编辑模式' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"success\"\r\n                            icon=\"el-icon-check\"\r\n                            @click=\"saveUserData\">\r\n                            保存\r\n                        </el-button>\r\n                        <el-button\r\n                            v-if=\"isEditMode\"\r\n                            type=\"info\"\r\n                            icon=\"el-icon-close\"\r\n                            @click=\"cancelEdit\">\r\n                            取消\r\n                        </el-button>\r\n                    </div>\r\n\r\n                    <!-- 客户信息标签页 -->\r\n                    <div v-if=\"activeTab === 'customer'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-user\"></i>\r\n                                客户信息\r\n                            </div>\r\n                    <!-- 查看模式 -->\r\n                    <el-descriptions v-if=\"!isEditMode\" :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            currentUserInfo.company || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            currentUserInfo.phone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户名称\">{{\r\n                            currentUserInfo.nickname || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            currentUserInfo.linkman || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系电话\">{{\r\n                            currentUserInfo.linkphone || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ currentUserInfo.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"立案专员\">{{\r\n                            currentUserInfo.lian_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"调解员\">{{\r\n                            currentUserInfo.tiaojie_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"法务专员\">{{\r\n                            currentUserInfo.fawu_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"合同上传专员\">{{\r\n                            currentUserInfo.htsczy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"律师\">{{\r\n                            currentUserInfo.ls_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"业务员\">{{\r\n                            currentUserInfo.ywy_name || '未分配'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"currentUserInfo.headimg && currentUserInfo.headimg !== ''\"\r\n                                     :src=\"currentUserInfo.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(currentUserInfo.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"注册时间\" :span=\"2\">{{\r\n                            currentUserInfo.create_time || '未知'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"最后登录\" :span=\"2\">{{\r\n                            currentUserInfo.last_login_time || '从未登录'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"会员到期\" :span=\"2\">{{\r\n                            currentUserInfo.end_time || '未设置'\r\n                            }}\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n\r\n                    <!-- 编辑模式 -->\r\n                    <el-form v-if=\"isEditMode\" :model=\"editForm\" :rules=\"rules\" ref=\"editForm\" label-width=\"120px\">\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"公司名称\" prop=\"company\">\r\n                                    <el-input v-model=\"editForm.company\" placeholder=\"请输入公司名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"手机号\" prop=\"phone\">\r\n                                    <el-input v-model=\"editForm.phone\" placeholder=\"请输入手机号\" disabled></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"用户名称\" prop=\"nickname\">\r\n                                    <el-input v-model=\"editForm.nickname\" placeholder=\"请输入用户名称\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                                    <el-input v-model=\"editForm.linkman\" placeholder=\"请输入联系人\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"linkphone\">\r\n                                    <el-input v-model=\"editForm.linkphone\" placeholder=\"请输入联系电话\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"登录密码\" prop=\"password\">\r\n                                    <el-input v-model=\"editForm.password\" placeholder=\"请输入新密码（留空不修改）\" type=\"password\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"调解员\" prop=\"tiaojie_id\">\r\n                                    <el-select v-model=\"editForm.tiaojie_id\" placeholder=\"请选择调解员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in tiaojies\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"法务专员\" prop=\"fawu_id\">\r\n                                    <el-select v-model=\"editForm.fawu_id\" placeholder=\"请选择法务专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in fawus\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"立案专员\" prop=\"lian_id\">\r\n                                    <el-select v-model=\"editForm.lian_id\" placeholder=\"请选择立案专员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in lians\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\">\r\n                                    <el-select v-model=\"editForm.htsczy_id\" placeholder=\"请选择合同上传专用\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in htsczy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"律师\" prop=\"ls_id\">\r\n                                    <el-select v-model=\"editForm.ls_id\" placeholder=\"请选择律师\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ls\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"业务员\" prop=\"ywy_id\">\r\n                                    <el-select v-model=\"editForm.ywy_id\" placeholder=\"请选择业务员\" filterable clearable>\r\n                                        <el-option value=\"\">请选择</el-option>\r\n                                        <el-option\r\n                                            v-for=\"(item, index) in ywy\"\r\n                                            :key=\"index\"\r\n                                            :label=\"item.title\"\r\n                                            :value=\"item.id\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-row :gutter=\"20\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"开始时间\" prop=\"start_time\">\r\n                                    <el-date-picker\r\n                                        v-model=\"editForm.start_time\"\r\n                                        type=\"date\"\r\n                                        format=\"yyyy-MM-dd\"\r\n                                        value-format=\"yyyy-MM-dd\"\r\n                                        placeholder=\"选择开始时间\">\r\n                                    </el-date-picker>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"会员年限\" prop=\"year\">\r\n                                    <el-input-number\r\n                                        v-model=\"editForm.year\"\r\n                                        :min=\"0\"\r\n                                        :max=\"99\"\r\n                                        placeholder=\"请输入会员年限\">\r\n                                    </el-input-number>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                        <el-form-item label=\"头像\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"editForm.headimg && editForm.headimg !== ''\"\r\n                                     :src=\"editForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(editForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 会员信息标签页 -->\r\n                    <div v-if=\"activeTab === 'member'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-medal\"></i>\r\n                                会员信息\r\n                            </div>\r\n                            <el-descriptions :column=\"2\" border>\r\n                                <el-descriptions-item label=\"开始时间\">{{\r\n                                    currentUserInfo.start_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员年限\">{{\r\n                                    currentUserInfo.year || 0\r\n                                    }}年\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"注册时间\">{{\r\n                                    currentUserInfo.create_time || '未知'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"最后登录\">{{\r\n                                    currentUserInfo.last_login_time || '从未登录'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                                <el-descriptions-item label=\"会员到期\">{{\r\n                                    currentUserInfo.end_time || '未设置'\r\n                                    }}\r\n                                </el-descriptions-item>\r\n                            </el-descriptions>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 债务人信息标签页 -->\r\n                    <div v-if=\"activeTab === 'debts'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                债务人信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addDebt\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加债务人\r\n                                </el-button>\r\n                            </div>\r\n                            <el-table\r\n                                :data=\"currentUserInfo.debts || []\"\r\n                                style=\"width: 100%\"\r\n                                size=\"small\"\r\n                                v-if=\"currentUserInfo.debts && currentUserInfo.debts.length > 0\">\r\n                                <el-table-column prop=\"name\" label=\"债务人姓名\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"tel\" label=\"债务人电话\" width=\"130\"></el-table-column>\r\n                                <el-table-column prop=\"money\" label=\"债务金额（元）\" width=\"120\"></el-table-column>\r\n                                <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-tag :type=\"scope.row.status === '已完成' ? 'success' : scope.row.status === '处理中' ? 'warning' : 'info'\" size=\"small\">\r\n                                            {{ scope.row.status }}\r\n                                        </el-tag>\r\n                                    </template>\r\n                                </el-table-column>\r\n                                <el-table-column label=\"操作\" width=\"150\">\r\n                                    <template slot-scope=\"scope\">\r\n                                        <el-button type=\"primary\" size=\"mini\" @click=\"editDebt(scope.row, scope.$index)\">编辑</el-button>\r\n                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteDebt(scope.$index)\">删除</el-button>\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                            <div v-else class=\"no-data\">\r\n                                <i class=\"el-icon-document\"></i>\r\n                                <span>暂无债务人信息</span>\r\n                                <br>\r\n                                <el-button type=\"primary\" size=\"small\" @click=\"addDebt\" style=\"margin-top: 10px;\">\r\n                                    <i class=\"el-icon-plus\"></i> 添加第一个债务人\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 附件信息标签页 -->\r\n                    <div v-if=\"activeTab === 'attachments'\" class=\"tab-content\">\r\n                        <div class=\"card\">\r\n                            <div class=\"card-header\">\r\n                                <i class=\"el-icon-folder-opened\"></i>\r\n                                附件信息\r\n                                <el-button type=\"primary\" size=\"mini\" style=\"float: right;\" @click=\"addAttachment\">\r\n                                    <i class=\"el-icon-plus\"></i> 上传附件\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"attachment-grid\">\r\n                                <!-- 身份证照片 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">身份证照片</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.idCard && currentUserInfo.attachments.idCard.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.idCard\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('idCard', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-picture\"></i>\r\n                                            <span>暂无身份证照片</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadIdCard\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传身份证\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 营业执照 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">营业执照</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.license && currentUserInfo.attachments.license.length > 0\" class=\"image-list\">\r\n                                            <div v-for=\"(img, index) in currentUserInfo.attachments.license\" :key=\"index\" class=\"image-item\">\r\n                                                <img :src=\"img.url\" @click=\"showImage(img.url)\" class=\"attachment-image\">\r\n                                                <div class=\"image-overlay\">\r\n                                                    <div class=\"image-info\">\r\n                                                        <span class=\"file-name\">{{ img.name }}</span>\r\n                                                        <span class=\"upload-time\">{{ img.uploadTime }}</span>\r\n                                                    </div>\r\n                                                    <div class=\"image-actions\">\r\n                                                        <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(img)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                        <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('license', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-document\"></i>\r\n                                            <span>暂无营业执照</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadLicense\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传营业执照\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <!-- 其他附件 -->\r\n                                <div class=\"attachment-item\">\r\n                                    <div class=\"attachment-title\">其他附件</div>\r\n                                    <div class=\"attachment-content\">\r\n                                        <div v-if=\"currentUserInfo.attachments && currentUserInfo.attachments.others && currentUserInfo.attachments.others.length > 0\" class=\"file-list\">\r\n                                            <div v-for=\"(file, index) in currentUserInfo.attachments.others\" :key=\"index\" class=\"file-item\">\r\n                                                <div class=\"file-icon\">\r\n                                                    <i :class=\"getFileIcon(file.type)\" class=\"file-type-icon\"></i>\r\n                                                </div>\r\n                                                <div class=\"file-info\">\r\n                                                    <div class=\"file-name\">{{ file.name }}</div>\r\n                                                    <div class=\"file-meta\">\r\n                                                        <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                                                        <span class=\"upload-time\">{{ file.uploadTime }}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div class=\"file-actions\">\r\n                                                    <el-button type=\"primary\" size=\"mini\" @click=\"downloadFile(file)\" icon=\"el-icon-download\">下载</el-button>\r\n                                                    <el-button type=\"danger\" size=\"mini\" @click=\"deleteAttachment('others', index)\" icon=\"el-icon-delete\">删除</el-button>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div v-else class=\"no-attachment\">\r\n                                            <i class=\"el-icon-folder\"></i>\r\n                                            <span>暂无其他附件</span>\r\n                                        </div>\r\n                                        <el-button type=\"primary\" size=\"small\" @click=\"uploadOthers\">\r\n                                            <i class=\"el-icon-upload\"></i> 上传其他附件\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n\r\n        <!-- 编辑用户抽屉 -->\r\n        <el-drawer\r\n            title=\"编辑用户\"\r\n            :visible.sync=\"drawerEditVisible\"\r\n            direction=\"rtl\"\r\n            size=\"50%\"\r\n            :before-close=\"handleDrawerClose\">\r\n            <div class=\"drawer-content\">\r\n                <!-- 客户信息展示 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                        客户信息\r\n                    </div>\r\n                    <el-descriptions :column=\"2\" border>\r\n                        <el-descriptions-item label=\"公司名称\">{{\r\n                            ruleForm.company\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"手机号\">{{\r\n                            ruleForm.phone\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"名称\">{{\r\n                            ruleForm.nickname\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"联系人\">{{\r\n                            ruleForm.linkman\r\n                            }}\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"头像\" :span=\"2\">\r\n                            <div class=\"avatar-display\">\r\n                                <img v-if=\"ruleForm.headimg && ruleForm.headimg !== ''\"\r\n                                     :src=\"ruleForm.headimg\"\r\n                                     class=\"detail-avatar\"\r\n                                     @click=\"showImage(ruleForm.headimg)\"\r\n                                />\r\n                                <div v-else class=\"no-avatar-large\">\r\n                                    <i class=\"el-icon-user-solid\"></i>\r\n                                    <span>无头像</span>\r\n                                </div>\r\n                            </div>\r\n                        </el-descriptions-item>\r\n                        <el-descriptions-item label=\"用户来源\" :span=\"2\">\r\n                            <el-tag size=\"small\" type=\"info\">{{ ruleForm.yuangong_id || '直接注册' }}</el-tag>\r\n                        </el-descriptions-item>\r\n                    </el-descriptions>\r\n                </div>\r\n\r\n                <!-- 信息编辑表单 -->\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                        <i class=\"el-icon-edit\"></i>\r\n                        信息编辑\r\n                    </div>\r\n                    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.htsczy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in htsczy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n                    </el-form>\r\n\r\n                    <!-- 保存按钮 -->\r\n                    <div class=\"drawer-footer\">\r\n                        <el-button @click=\"drawerEditVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveData()\">保 存</el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-drawer>\r\n        <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n            <el-image :src=\"show_image\"></el-image>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"制作订单\"\r\n                :visible.sync=\"dialogFormOrder\"\r\n                :close-on-click-modal=\"false\"\r\n        >\r\n            <el-row>\r\n                <el-descriptions title=\"客户信息\">\r\n                    <el-descriptions-item label=\"公司名称\">{{\r\n                        info.company\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"手机号\">{{\r\n                        info.phone\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"名称\">{{\r\n                        info.nickname\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"联系人\">{{\r\n                        info.linkman\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"头像\">\r\n                        <img v-if=\"info.headimg !='' && info.headimg!=null\"\r\n                             :src=\"info.headimg\"\r\n                             style=\"width: 50px; height: 50px;\"\r\n                             @click=\"showImage(ruleForm.headimg)\"\r\n                        /></el-descriptions-item>\r\n                    <el-descriptions-item label=\"用户来源\">{{\r\n                        info.yuangong_id\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"调解员\">{{\r\n                        info.tiaojie_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"法务专员\">{{\r\n                        info.fawu_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"立案专员\">{{\r\n                        info.lian_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"合同上传专用\">{{\r\n                        info.htsczy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"律师\">{{\r\n                        info.ls_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <el-descriptions-item label=\"业务员\">{{\r\n                        info.ywy_name\r\n                        }}\r\n                    </el-descriptions-item>\r\n                    <!-- <el-descriptions-item label=\"联系地址\">{{\r\n                      info.address\r\n                    }}</el-descriptions-item> -->\r\n                </el-descriptions>\r\n            </el-row>\r\n            <el-descriptions title=\"下单内容\"></el-descriptions>\r\n            <el-form\r\n                    :model=\"orderForm\"\r\n                    :rules=\"rules2\"\r\n                    ref=\"orderForm\"\r\n                    label-width=\"80px\"\r\n                    mode=\"left\"\r\n            >\r\n                <el-form-item label=\"套餐\" prop=\"taocan_id\">\r\n                    <el-select\r\n                            v-model=\"orderForm.taocan_id\"\r\n                            placeholder=\"请选择\"\r\n                            @change=\"changeTaocan\"\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n\r\n                        <el-option\r\n                                v-for=\"(item, index) in taocans\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"总金额\">\r\n                    <el-input\r\n                            type=\"number\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.total_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"实际支付\" prop=\"pay_price\">\r\n                    <el-input\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.pay_price\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"客户描述\">\r\n                    <el-input\r\n                            type=\"textarea\"\r\n                            :rows=\"3\"\r\n                            placeholder=\"请输入内容\"\r\n                            v-model=\"orderForm.desc\"\r\n                            class=\"el_input2\"\r\n                    >\r\n                    </el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogFormOrder = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData2()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!--导入-->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n            <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n                <el-form-item label=\"选择文件:\">\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            :auto-upload=\"false\"\r\n                            :action=\"uploadAction\"\r\n                            :data=\"uploadData\"\r\n                            :on-success=\"uploadSuccess\"\r\n                            :before-upload=\"checkFile\"\r\n                            accept=\".xls,.xlsx\"\r\n                            limit=\"1\"\r\n                            multiple=\"false\">\r\n                        <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n                </el-form-item>\r\n\r\n                <div style=\"text-align: right\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交\r\n                    </el-button>\r\n                    <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-dialog>\r\n        <el-dialog\r\n                title=\"用户详情\"\r\n                :visible.sync=\"dialogViewUserDetail\"\r\n                :close-on-click-modal=\"false\"  width=\"80%\"\r\n        >\r\n            <user-details :id=\"currentId\"></user-details>\r\n\r\n        </el-dialog>\r\n\r\n        <!--新增用户-->\r\n        <el-dialog\r\n                title=\"新增用户\"\r\n                :visible.sync=\"dialogAddUser\"\r\n                :close-on-click-modal=\"false\"\r\n                width=\"70%\"\r\n        >\r\n            <el-descriptions title=\"信息添加\"></el-descriptions>\r\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n                <el-form-item label=\"手机账号\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.phone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.company\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkman\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.linkphone\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"登录密码\" :label-width=\"formLabelWidth\">\r\n                    <el-input v-model=\"ruleForm.password\" autocomplete=\"off\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"调解员\" prop=\"tiaojie_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.tiaojie_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in tiaojies\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"法务专员\" prop=\"fawu_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.fawu_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in fawus\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"立案专员\" prop=\"lian_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.lian_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in lians\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item><el-form-item label=\"合同上传专用\" prop=\"htsczy_id\" :label-width=\"formLabelWidth\">\r\n                <el-select\r\n                        v-model=\"ruleForm.htsczy_id\"\r\n                        placeholder=\"请选择\"\r\n                        filterable\r\n                >\r\n                    <el-option value=\"\">请选择</el-option>\r\n                    <el-option\r\n                            v-for=\"(item, index) in htsczy\"\r\n                            :key=\"index\"\r\n                            :label=\"item.title\"\r\n                            :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n                <el-form-item label=\"律师\" prop=\"ls_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ls_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ls\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"业务员\" prop=\"ywy_id\" :label-width=\"formLabelWidth\">\r\n                    <el-select\r\n                            v-model=\"ruleForm.ywy_id\"\r\n                            placeholder=\"请选择\"\r\n                            filterable\r\n                    >\r\n                        <el-option value=\"\">请选择</el-option>\r\n                        <el-option\r\n                                v-for=\"(item, index) in ywy\"\r\n                                :key=\"index\"\r\n                                :label=\"item.title\"\r\n                                :value=\"item.id\"\r\n                        >\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label-width=\"formLabelWidth\" label=\"营业执照\">\r\n\r\n                    <img v-if=\"ruleForm.license !='' && ruleForm.license!=null\"\r\n                         :src=\"ruleForm.license\"\r\n                         style=\"width:400px; height: 400px\"\r\n                         @click=\"showImage(ruleForm.license)\"\r\n                    />\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"开始时间\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n                    <el-date-picker\r\n                            v-model=\"ruleForm.start_time\"\r\n                            type=\"date\"\r\n                            format=\"yyyy-MM-dd\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            placeholder=\"选择日期\"\r\n                    >\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"会员年限\" :label-width=\"formLabelWidth\">\r\n                    <el-input-number\r\n                            v-model=\"ruleForm.year\"\r\n                            :min=\"0\"\r\n                            :max=\"99\"\r\n                            label=\"请输入年份\"\r\n                    ></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"dialogAddUser = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览对话框 -->\r\n        <el-dialog :visible.sync=\"dialogVisible\" width=\"50%\" center>\r\n            <img :src=\"show_image\" style=\"width: 100%; height: auto;\" />\r\n        </el-dialog>\r\n\r\n        <!-- 债务人表单对话框 -->\r\n        <el-dialog :title=\"debtDialogTitle\" :visible.sync=\"debtDialogVisible\" width=\"500px\" @close=\"closeDebtDialog\">\r\n            <el-form :model=\"debtForm\" :rules=\"debtRules\" ref=\"debtForm\" label-width=\"100px\">\r\n                <el-form-item label=\"债务人姓名\" prop=\"name\">\r\n                    <el-input v-model=\"debtForm.name\" placeholder=\"请输入债务人姓名\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务人电话\" prop=\"tel\">\r\n                    <el-input v-model=\"debtForm.tel\" placeholder=\"请输入债务人电话\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"债务金额\" prop=\"money\">\r\n                    <el-input-number\r\n                        v-model=\"debtForm.money\"\r\n                        :min=\"0\"\r\n                        :precision=\"2\"\r\n                        placeholder=\"请输入债务金额\"\r\n                        style=\"width: 100%\">\r\n                    </el-input-number>\r\n                </el-form-item>\r\n                <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-select v-model=\"debtForm.status\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n                        <el-option label=\"待处理\" value=\"待处理\"></el-option>\r\n                        <el-option label=\"处理中\" value=\"处理中\"></el-option>\r\n                        <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeDebtDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"saveDebt\">确定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 导入用户对话框 -->\r\n        <el-dialog title=\"导入用户\" :visible.sync=\"uploadVisible\" width=\"600px\" @close=\"closeUploadDialog\">\r\n            <div class=\"upload-container\">\r\n                <div class=\"upload-tips\">\r\n                    <el-alert\r\n                        title=\"导入说明\"\r\n                        type=\"info\"\r\n                        :closable=\"false\"\r\n                        show-icon>\r\n                        <div slot=\"description\">\r\n                            <p>1. 请先下载导入模板，按照模板格式填写用户信息</p>\r\n                            <p>2. 支持的文件格式：.xls, .xlsx</p>\r\n                            <p>3. 单次最多导入1000条用户数据</p>\r\n                            <p>4. 手机号码为必填项，且不能重复</p>\r\n                        </div>\r\n                    </el-alert>\r\n                </div>\r\n\r\n                <div class=\"upload-actions\">\r\n                    <el-button type=\"success\" icon=\"el-icon-download\" @click=\"downloadTemplate\">\r\n                        下载导入模板\r\n                    </el-button>\r\n                </div>\r\n\r\n                <div class=\"upload-area\">\r\n                    <el-upload\r\n                        ref=\"upload\"\r\n                        :action=\"uploadAction\"\r\n                        :data=\"uploadData\"\r\n                        :on-success=\"handleUploadSuccess\"\r\n                        :on-error=\"handleUploadError\"\r\n                        :before-upload=\"beforeUpload\"\r\n                        :auto-upload=\"false\"\r\n                        :limit=\"1\"\r\n                        :file-list=\"fileList\"\r\n                        accept=\".xls,.xlsx\"\r\n                        drag>\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n                        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xls/xlsx文件，且不超过10MB</div>\r\n                    </el-upload>\r\n                </div>\r\n\r\n                <div class=\"upload-options\">\r\n                    <el-checkbox v-model=\"uploadData.review\">导入前预览数据</el-checkbox>\r\n                </div>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"closeUploadDialog\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">\r\n                    {{ submitOrderLoading2 ? '导入中...' : '开始导入' }}\r\n                </el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\n    // @ is an alias to /src\r\n    import UserDetails from '/src/components/UserDetail.vue';\r\n\r\n    export default {\r\n        name: \"list\",\r\n        components: {UserDetails,},\r\n        data() {\r\n            return {\r\n                uploadAction: \"/admin/user/import?token=\" + this.$store.getters.GET_TOKEN,\r\n                uploadVisible: false,\r\n                submitOrderLoading2: false,\r\n                uploadData: {\r\n                    review: false\r\n                },\r\n                fileList: [], // 上传文件列表\r\n                allSize: \"mini\",\r\n                list: [],\r\n                total: 1,\r\n                page: 1,\r\n                size: 20,\r\n                currentId: 0,\r\n                currentUserInfo: {},\r\n                search: {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                },\r\n                is_del: false,//列表删除按钮是否出现\r\n                loading: true,\r\n                url: \"/user/\",\r\n                title: \"用户\",\r\n                info: {},\r\n                selectedUsers: [], // 选中的用户列表\r\n                dialogFormVisible: false,\r\n                dialogViewUserDetail: false,\r\n                dialogAddUser: false,\r\n                drawerViewVisible: false,\r\n                drawerEditVisible: false,\r\n                isEditMode: false,\r\n                editForm: {},\r\n                originalUserInfo: {},\r\n                activeTab: 'customer',\r\n                show_image: \"\",\r\n                dialogVisible: false,\r\n                // 债务人表单相关\r\n                debtDialogVisible: false,\r\n                debtDialogTitle: '添加债务人',\r\n                isEditingDebt: false,\r\n                editingDebtIndex: -1,\r\n                debtForm: {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                },\r\n                debtRules: {\r\n                    name: [\r\n                        { required: true, message: '请输入债务人姓名', trigger: 'blur' }\r\n                    ],\r\n                    tel: [\r\n                        { required: true, message: '请输入债务人电话', trigger: 'blur' },\r\n                        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n                    ],\r\n                    money: [\r\n                        { required: true, message: '请输入债务金额', trigger: 'blur' }\r\n                    ],\r\n                    status: [\r\n                        { required: true, message: '请选择状态', trigger: 'change' }\r\n                    ]\r\n                },\r\n                ruleForm: {\r\n                    title: \"\",\r\n                    is_num: 0,\r\n                },\r\n\r\n                rules: {\r\n                    title: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写标题\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n                formLabelWidth: \"120px\",\r\n                dialogFormOrder: false,\r\n                taocans: [],\r\n                tiaojies: [],\r\n                fawus: [],\r\n                lians: [],\r\n                htsczy: [],\r\n                ls: [],\r\n                ywy: [],\r\n                orderForm: {\r\n                    client_id: \"\",\r\n                    taocan_id: \"\",\r\n                    tiaojie_id: \"\",\r\n                    fawu_id: \"\",\r\n                    lian_id: \"\",\r\n                    htsczy_id: \"\",\r\n                    ls_id: \"\",\r\n                    ywy_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                    qishu: 2,\r\n                    taocan_year: \"\",\r\n                    taocan_content: [],\r\n                    taocan_type: 1,\r\n                    fenqi: [\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                        {\r\n                            date: \"\",\r\n                            price: \"\",\r\n                            pay_path: \"\",\r\n                        },\r\n                    ],\r\n                },\r\n                rules2: {\r\n                    taocan_id: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请选择套餐\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_path: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请上传凭证\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    taocan_year: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写年份\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    pay_price: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写支付金额\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                    desc: [\r\n                        {\r\n                            required: true,\r\n                            message: \"请填写内容\",\r\n                            trigger: \"blur\",\r\n                        },\r\n                    ],\r\n                },\r\n            };\r\n        },\r\n        mounted() {\r\n            // 使用测试数据，注释掉API调用\r\n            // this.getData();\r\n            // 添加测试数据\r\n            this.addTestData();\r\n        },\r\n        methods: {\r\n            // 获取原始测试数据\r\n            getOriginalTestData() {\r\n                return [\r\n                    {\r\n                        id: 1,\r\n                        phone: '13800138001',\r\n                        nickname: '张三',\r\n                        company: '北京科技有限公司',\r\n                        linkman: '张三',\r\n                        linkphone: '13800138001',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-12-31',\r\n                        create_time: '2023-01-15 10:30:00',\r\n                        last_login_time: '2024-01-20 15:45:00',\r\n                        headimg: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n                        license: '',\r\n                        start_time: '2023-01-15',\r\n                        year: 2,\r\n                        // 专员信息\r\n                        lian_name: '陈专员',\r\n                        tiaojie_name: '朱调解',\r\n                        fawu_name: '严法务',\r\n                        htsczy_name: '合同专员',\r\n                        ls_name: '王律师',\r\n                        ywy_name: '业务员张三',\r\n                        debts: [\r\n                            {\r\n                                name: '王某某',\r\n                                tel: '13912345678',\r\n                                money: '50000',\r\n                                status: '处理中'\r\n                            },\r\n                            {\r\n                                name: '李某某',\r\n                                tel: '13987654321',\r\n                                money: '30000',\r\n                                status: '已完成'\r\n                            }\r\n                        ],\r\n                        attachments: {\r\n                            idCard: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                                    name: '身份证正面.jpg'\r\n                                },\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                                    name: '身份证反面.jpg'\r\n                                }\r\n                            ],\r\n                            license: [\r\n                                {\r\n                                    url: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                                    name: '营业执照.jpg'\r\n                                }\r\n                            ],\r\n                            others: [\r\n                                {\r\n                                    name: '合同文件.pdf',\r\n                                    url: '/files/contract.pdf',\r\n                                    type: 'pdf'\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        id: 2,\r\n                        phone: '13900139002',\r\n                        nickname: '李四',\r\n                        company: '上海贸易公司',\r\n                        linkman: '李四',\r\n                        linkphone: '13900139002',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-06-30',\r\n                        create_time: '2023-02-20 14:20:00',\r\n                        last_login_time: '2024-01-18 09:15:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-02-20',\r\n                        year: 1,\r\n                        // 专员信息\r\n                        lian_name: '李专员',\r\n                        tiaojie_name: '调解员王五',\r\n                        fawu_name: '法务李四',\r\n                        htsczy_name: '合同专员B',\r\n                        ls_name: '律师张三',\r\n                        ywy_name: '业务员李四',\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13811112222',\r\n                                money: '80000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 3,\r\n                        phone: '13700137003',\r\n                        nickname: '王五',\r\n                        company: '深圳创新科技',\r\n                        linkman: '王五',\r\n                        linkphone: '13700137003',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2025-03-15',\r\n                        create_time: '2023-03-10 16:40:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',\r\n                        license: '',\r\n                        start_time: '2023-03-10',\r\n                        year: 2,\r\n                        debts: [\r\n                            {\r\n                                name: '陈某某',\r\n                                tel: '13765432109',\r\n                                money: '80000',\r\n                                status: '待处理'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 4,\r\n                        phone: '13600136004',\r\n                        nickname: '赵六',\r\n                        company: '广州物流集团',\r\n                        linkman: '赵六',\r\n                        linkphone: '13600136004',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-09-20',\r\n                        create_time: '2023-04-05 11:30:00',\r\n                        last_login_time: '2024-01-19 14:22:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-04-05',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 5,\r\n                        phone: '13500135005',\r\n                        nickname: '孙七',\r\n                        company: '杭州电商有限公司',\r\n                        linkman: '孙七',\r\n                        linkphone: '13500135005',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-11-10',\r\n                        create_time: '2023-05-12 09:15:00',\r\n                        last_login_time: '2024-01-21 16:30:00',\r\n                        headimg: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',\r\n                        license: '',\r\n                        start_time: '2023-05-12',\r\n                        year: 1,\r\n                        debts: [\r\n                            {\r\n                                name: '赵某某',\r\n                                tel: '13654321098',\r\n                                money: '25000',\r\n                                status: '已完成'\r\n                            },\r\n                            {\r\n                                name: '钱某某',\r\n                                tel: '13543210987',\r\n                                money: '15000',\r\n                                status: '处理中'\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        id: 6,\r\n                        phone: '13400134006',\r\n                        nickname: '周八',\r\n                        company: '成都软件开发',\r\n                        linkman: '周八',\r\n                        linkphone: '13400134006',\r\n                        yuangong_id: '小程序注册',\r\n                        end_time: '2024-08-15',\r\n                        create_time: '2023-06-18 13:25:00',\r\n                        last_login_time: '2024-01-22 10:12:00',\r\n                        headimg: '',\r\n                        license: '',\r\n                        start_time: '2023-06-18',\r\n                        year: 1,\r\n                        debts: []\r\n                    },\r\n                    {\r\n                        id: 7,\r\n                        phone: '13300133007',\r\n                        nickname: '吴九',\r\n                        company: '武汉贸易有限公司',\r\n                        linkman: '吴九',\r\n                        linkphone: '13300133007',\r\n                        yuangong_id: '后台创建',\r\n                        end_time: '2024-10-30',\r\n                        create_time: '2023-07-22 15:45:00',\r\n                        last_login_time: '',\r\n                        headimg: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',\r\n                        license: '',\r\n                        start_time: '2023-07-22',\r\n                        year: 1,\r\n                        debts: []\r\n                    }\r\n                ];\r\n            },\r\n            addTestData() {\r\n                // 添加测试数据\r\n                this.list = this.getOriginalTestData();\r\n                this.total = this.list.length;\r\n                this.loading = false;\r\n            },\r\n            // 过滤测试数据（模拟搜索功能）\r\n            filterTestData() {\r\n                this.loading = true;\r\n\r\n                // 获取原始测试数据\r\n                const originalData = this.getOriginalTestData();\r\n                let filteredData = [...originalData];\r\n\r\n                // 根据搜索条件过滤数据\r\n                if (this.search.nickname) {\r\n                    const nickname = this.search.nickname.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.nickname && user.nickname.toLowerCase().includes(nickname)\r\n                    );\r\n                }\r\n\r\n                if (this.search.phone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.phone && user.phone.includes(this.search.phone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkman) {\r\n                    const linkman = this.search.linkman.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkman && user.linkman.toLowerCase().includes(linkman)\r\n                    );\r\n                }\r\n\r\n                if (this.search.linkphone) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.linkphone && user.linkphone.includes(this.search.linkphone)\r\n                    );\r\n                }\r\n\r\n                if (this.search.company) {\r\n                    const company = this.search.company.toLowerCase();\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.company && user.company.toLowerCase().includes(company)\r\n                    );\r\n                }\r\n\r\n                if (this.search.yuangong_id) {\r\n                    filteredData = filteredData.filter(user =>\r\n                        user.yuangong_id === this.search.yuangong_id\r\n                    );\r\n                }\r\n\r\n                // 注册时间范围过滤\r\n                if (this.search.dateRange && this.search.dateRange.length === 2) {\r\n                    const startDate = new Date(this.search.dateRange[0]);\r\n                    const endDate = new Date(this.search.dateRange[1]);\r\n                    filteredData = filteredData.filter(user => {\r\n                        if (user.create_time) {\r\n                            const createDate = new Date(user.create_time.split(' ')[0]);\r\n                            return createDate >= startDate && createDate <= endDate;\r\n                        }\r\n                        return false;\r\n                    });\r\n                }\r\n\r\n                // 更新列表和总数\r\n                this.list = filteredData;\r\n                this.total = filteredData.length;\r\n                this.loading = false;\r\n\r\n                // 显示搜索结果提示\r\n                const hasSearchCondition = this.search.nickname || this.search.phone || this.search.linkman ||\r\n                                         this.search.linkphone || this.search.company || this.search.yuangong_id ||\r\n                                         (this.search.dateRange && this.search.dateRange.length === 2);\r\n\r\n                if (hasSearchCondition) {\r\n                    this.$message.success(`搜索完成，找到 ${filteredData.length} 条匹配记录`);\r\n                }\r\n            },\r\n            order(row) {\r\n                this.dialogFormOrder = true;\r\n                this.info = row;\r\n                this.orderForm = {\r\n                    client_id: row.id,\r\n                    taocan_id: \"\",\r\n                    total_price: \"\",\r\n                    pay_price: 0,\r\n                    pay_path: \"\",\r\n                    desc: \"\",\r\n                    pay_type: 1,\r\n                };\r\n                this.$nextTick(() => {\r\n                    this.getTaocans();\r\n                });\r\n            },\r\n            saveData2() {\r\n                let _this = this;\r\n\r\n                this.$refs[\"orderForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(\"/dingdan/save\", this.orderForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // _this.getRemarks();\r\n                                _this.dialogFormOrder = false;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            changeTaocan(e) {\r\n                this.orderForm.taocan_content = [];\r\n                this.orderForm.taocan_type = 1;\r\n                this.getRequest(\"/taocan/read?id=\" + e).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.orderForm.total_price = resp.data.price;\r\n                        this.orderForm.pay_price = resp.data.price;\r\n                    }\r\n                });\r\n            },\r\n            getTaocans() {\r\n                this.postRequest(\"/taocan/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        this.taocans = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            getYuangongs() {\r\n                let _this = this;\r\n                this.postRequest(\"/yuangong/getList\", {}).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.tiaojies = resp.data.filter(item => item.zhiwei_id == 6);\r\n                        _this.fawus = resp.data.filter(item => item.zhiwei_id == 5);\r\n                        _this.lians = resp.data.filter(item => item.zhiwei_id == 12);\r\n                        _this.ywy = resp.data.filter(item => item.zhiwei_id == 3);\r\n                        _this.ls = resp.data.filter(item => item.zhiwei_id == 4);\r\n                        _this.htsczy = resp.data.filter(item => item.zhiwei_id == 9);\r\n                    }\r\n                });\r\n            },\r\n            viewData(id) {\r\n                console.log('viewData called with id:', id);\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.currentId = id;\r\n                    // 从测试数据中找到对应的用户信息\r\n                    this.currentUserInfo = this.list.find(user => user.id === id) || {};\r\n                    console.log('Found user info:', this.currentUserInfo);\r\n                    // 重置编辑模式\r\n                    this.isEditMode = false;\r\n                    this.editForm = {};\r\n                    this.originalUserInfo = {};\r\n                    // 重置到客户信息标签页\r\n                    this.activeTab = 'customer';\r\n                }\r\n                _this.drawerViewVisible = true;\r\n                console.log('Drawer should be visible:', _this.drawerViewVisible);\r\n            },\r\n            handleTabSelect(key) {\r\n                this.activeTab = key;\r\n                // 如果切换到其他标签页，退出编辑模式\r\n                if (key !== 'customer') {\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            toggleEditMode() {\r\n                if (!this.isEditMode) {\r\n                    // 进入编辑模式\r\n                    this.isEditMode = true;\r\n                    // 保存原始数据用于取消时恢复\r\n                    this.originalUserInfo = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 复制当前用户信息到编辑表单\r\n                    this.editForm = JSON.parse(JSON.stringify(this.currentUserInfo));\r\n                    // 获取员工数据用于下拉选择\r\n                    this.getYuangongs();\r\n                } else {\r\n                    // 退出编辑模式\r\n                    this.isEditMode = false;\r\n                }\r\n            },\r\n            cancelEdit() {\r\n                // 恢复原始数据\r\n                this.currentUserInfo = JSON.parse(JSON.stringify(this.originalUserInfo));\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.$message.info('已取消编辑');\r\n            },\r\n            saveUserData() {\r\n                // 验证表单\r\n                this.$refs.editForm.validate((valid) => {\r\n                    if (valid) {\r\n                        // 更新当前用户信息\r\n                        this.currentUserInfo = JSON.parse(JSON.stringify(this.editForm));\r\n\r\n                        // 更新列表中的数据\r\n                        const index = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1, this.currentUserInfo);\r\n                        }\r\n\r\n                        // 退出编辑模式\r\n                        this.isEditMode = false;\r\n                        this.editForm = {};\r\n\r\n                        this.$message.success('保存成功！');\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            editData(id) {\r\n                let _this = this;\r\n                if (id != 0) {\r\n                    this.getInfo(id);\r\n                } else {\r\n                    this.ruleForm = {\r\n                        title: \"\",\r\n                        desc: \"\",\r\n                    };\r\n                }\r\n                _this.drawerEditVisible = true;\r\n                _this.getYuangongs();\r\n            },\r\n            handleDrawerClose() {\r\n                this.drawerViewVisible = false;\r\n                this.drawerEditVisible = false;\r\n                // 重置编辑模式\r\n                this.isEditMode = false;\r\n                this.editForm = {};\r\n                this.originalUserInfo = {};\r\n                // 重置标签页\r\n                this.activeTab = 'customer';\r\n            },\r\n            // 债务人管理方法\r\n            addDebt() {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.debtDialogTitle = '添加债务人';\r\n                this.isEditingDebt = false;\r\n            },\r\n            editDebt(debt, index) {\r\n                this.debtDialogVisible = true;\r\n                this.debtForm = {\r\n                    name: debt.name,\r\n                    tel: debt.tel,\r\n                    money: parseFloat(debt.money),\r\n                    status: debt.status\r\n                };\r\n                this.debtDialogTitle = '编辑债务人';\r\n                this.isEditingDebt = true;\r\n                this.editingDebtIndex = index;\r\n            },\r\n            saveDebt() {\r\n                this.$refs.debtForm.validate((valid) => {\r\n                    if (valid) {\r\n                        const debtData = {\r\n                            name: this.debtForm.name,\r\n                            tel: this.debtForm.tel,\r\n                            money: this.debtForm.money.toString(),\r\n                            status: this.debtForm.status\r\n                        };\r\n\r\n                        if (this.isEditingDebt) {\r\n                            // 编辑模式\r\n                            this.currentUserInfo.debts[this.editingDebtIndex] = debtData;\r\n                            this.$message.success('债务人信息修改成功！');\r\n                        } else {\r\n                            // 添加模式\r\n                            if (!this.currentUserInfo.debts) {\r\n                                this.currentUserInfo.debts = [];\r\n                            }\r\n                            this.currentUserInfo.debts.push(debtData);\r\n                            this.$message.success('债务人添加成功！');\r\n                        }\r\n\r\n                        // 更新主列表中的数据\r\n                        const userIndex = this.list.findIndex(user => user.id === this.currentUserInfo.id);\r\n                        if (userIndex !== -1) {\r\n                            this.list[userIndex].debts = [...this.currentUserInfo.debts];\r\n                        }\r\n\r\n                        this.closeDebtDialog();\r\n                    } else {\r\n                        this.$message.error('请检查表单填写是否正确');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            closeDebtDialog() {\r\n                this.debtDialogVisible = false;\r\n                this.debtForm = {\r\n                    name: '',\r\n                    tel: '',\r\n                    money: '',\r\n                    status: '待处理'\r\n                };\r\n                this.isEditingDebt = false;\r\n                this.editingDebtIndex = -1;\r\n                this.debtDialogTitle = '添加债务人';\r\n                // 清除表单验证\r\n                this.$nextTick(() => {\r\n                    if (this.$refs.debtForm) {\r\n                        this.$refs.debtForm.clearValidate();\r\n                    }\r\n                });\r\n            },\r\n            deleteDebt(index) {\r\n                this.$confirm('确定要删除这个债务人吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.currentUserInfo.debts.splice(index, 1);\r\n                    this.$message.success('删除成功！');\r\n                });\r\n            },\r\n            // 附件管理方法\r\n            addAttachment() {\r\n                this.$message.info('请选择具体的附件类型进行上传');\r\n            },\r\n            uploadIdCard() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'idCard', '身份证照片');\r\n                });\r\n            },\r\n            uploadLicense() {\r\n                this.createFileInput('image/*', (files) => {\r\n                    this.handleFileUpload(files, 'license', '营业执照');\r\n                });\r\n            },\r\n            uploadOthers() {\r\n                this.createFileInput('*', (files) => {\r\n                    this.handleFileUpload(files, 'others', '其他附件');\r\n                });\r\n            },\r\n            // 创建文件选择器\r\n            createFileInput(accept, callback) {\r\n                const input = document.createElement('input');\r\n                input.type = 'file';\r\n                input.accept = accept;\r\n                input.multiple = true;\r\n                input.style.display = 'none';\r\n\r\n                input.onchange = (e) => {\r\n                    const files = Array.from(e.target.files);\r\n                    if (files.length > 0) {\r\n                        callback(files);\r\n                    }\r\n                    document.body.removeChild(input);\r\n                };\r\n\r\n                document.body.appendChild(input);\r\n                input.click();\r\n            },\r\n            // 处理文件上传\r\n            handleFileUpload(files, type, typeName) {\r\n                if (!files || files.length === 0) {\r\n                    this.$message.warning('请选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                // 验证文件\r\n                for (let file of files) {\r\n                    if (type !== 'others' && !file.type.startsWith('image/')) {\r\n                        this.$message.error(`${typeName}只能上传图片文件`);\r\n                        return;\r\n                    }\r\n                    if (file.size > 10 * 1024 * 1024) { // 10MB限制\r\n                        this.$message.error(`文件 ${file.name} 大小超过10MB限制`);\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 初始化附件数据结构\r\n                if (!this.currentUserInfo.attachments) {\r\n                    this.currentUserInfo.attachments = {};\r\n                }\r\n                if (!this.currentUserInfo.attachments[type]) {\r\n                    this.currentUserInfo.attachments[type] = [];\r\n                }\r\n\r\n                // 模拟上传过程\r\n                this.$message.info(`正在上传 ${files.length} 个文件...`);\r\n\r\n                files.forEach((file, index) => {\r\n                    // 创建文件预览URL\r\n                    const fileUrl = URL.createObjectURL(file);\r\n\r\n                    // 模拟上传延迟\r\n                    setTimeout(() => {\r\n                        const fileData = {\r\n                            name: file.name,\r\n                            url: fileUrl,\r\n                            size: file.size,\r\n                            type: file.type,\r\n                            uploadTime: new Date().toLocaleString()\r\n                        };\r\n\r\n                        this.currentUserInfo.attachments[type].push(fileData);\r\n\r\n                        if (index === files.length - 1) {\r\n                            this.$message.success(`${typeName}上传完成！共上传 ${files.length} 个文件`);\r\n                        }\r\n                    }, (index + 1) * 500); // 模拟上传时间\r\n                });\r\n            },\r\n            deleteAttachment(type, index) {\r\n                this.$confirm('确定要删除这个附件吗？', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    if (!this.currentUserInfo.attachments) {\r\n                        this.currentUserInfo.attachments = {};\r\n                    }\r\n                    if (this.currentUserInfo.attachments[type]) {\r\n                        // 释放预览URL\r\n                        const file = this.currentUserInfo.attachments[type][index];\r\n                        if (file && file.url && file.url.startsWith('blob:')) {\r\n                            URL.revokeObjectURL(file.url);\r\n                        }\r\n\r\n                        this.currentUserInfo.attachments[type].splice(index, 1);\r\n                        this.$message.success('删除成功！');\r\n                    }\r\n                });\r\n            },\r\n            downloadFile(file) {\r\n                if (!file || !file.url) {\r\n                    this.$message.error('文件链接无效');\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    // 创建下载链接\r\n                    const link = document.createElement('a');\r\n                    link.href = file.url;\r\n                    link.download = file.name || '附件';\r\n                    link.style.display = 'none';\r\n\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n\r\n                    this.$message.success(`开始下载: ${file.name}`);\r\n                } catch (error) {\r\n                    this.$message.error('下载失败，请重试');\r\n                    console.error('下载错误:', error);\r\n                }\r\n            },\r\n            // 文件相关辅助方法\r\n            getFileIcon(fileType) {\r\n                if (!fileType) return 'el-icon-document';\r\n\r\n                if (fileType.startsWith('image/')) return 'el-icon-picture';\r\n                if (fileType.includes('pdf')) return 'el-icon-document';\r\n                if (fileType.includes('word') || fileType.includes('doc')) return 'el-icon-document';\r\n                if (fileType.includes('excel') || fileType.includes('sheet')) return 'el-icon-s-grid';\r\n                if (fileType.includes('zip') || fileType.includes('rar')) return 'el-icon-folder-opened';\r\n                if (fileType.includes('video')) return 'el-icon-video-camera';\r\n                if (fileType.includes('audio')) return 'el-icon-headset';\r\n\r\n                return 'el-icon-document';\r\n            },\r\n            formatFileSize(bytes) {\r\n                if (bytes === 0) return '0 B';\r\n\r\n                const k = 1024;\r\n                const sizes = ['B', 'KB', 'MB', 'GB'];\r\n                const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n            },\r\n            // 债务人相关计算方法\r\n            getDebtCount(debts) {\r\n                return debts && debts.length ? debts.length : 0;\r\n            },\r\n            getDebtCountType(debts) {\r\n                const count = this.getDebtCount(debts);\r\n                if (count === 0) return 'info';\r\n                if (count <= 2) return 'success';\r\n                if (count <= 5) return 'warning';\r\n                return 'danger';\r\n            },\r\n            getTotalDebtAmount(debts) {\r\n                if (!debts || !debts.length) return 0;\r\n                return debts.reduce((total, debt) => {\r\n                    return total + (parseFloat(debt.money) || 0);\r\n                }, 0);\r\n            },\r\n            formatAmount(amount) {\r\n                if (amount === 0) return '0';\r\n                return amount.toLocaleString('zh-CN', {\r\n                    minimumFractionDigits: 0,\r\n                    maximumFractionDigits: 2\r\n                });\r\n            },\r\n            getInfo(id) {\r\n                let _this = this;\r\n                _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n                    if (resp) {\r\n                        resp.data.tiaojie_id = resp.data.tiaojie_id == 0 ? '' : resp.data.tiaojie_id;\r\n                        resp.data.fawu_id = resp.data.fawu_id == 0 ? '' : resp.data.fawu_id;\r\n                        resp.data.lian_id = resp.data.lian_id == 0 ? '' : resp.data.lian_id;\r\n                        resp.data.ywy_id = resp.data.ywy_id == 0 ? '' : resp.data.ywy_id;\r\n                        resp.data.htsczy_id = resp.data.htsczy_id == 0 ? '' : resp.data.htsczy_id;\r\n                        resp.data.ls_id = resp.data.ls_id == 0 ? '' : resp.data.ls_id;\r\n                        _this.ruleForm = resp.data;\r\n                    }\r\n                });\r\n            },\r\n            delData(index, id) {\r\n                this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\",\r\n                })\r\n                    .then(() => {\r\n                        this.postRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                this.$message({\r\n                                    type: \"success\",\r\n                                    message: \"删除成功!\",\r\n                                });\r\n                                this.list.splice(index, 1);\r\n                            }\r\n                        });\r\n                    })\r\n                    .catch(() => {\r\n                        this.$message({\r\n                            type: \"error\",\r\n                            message: \"取消删除!\",\r\n                        });\r\n                    });\r\n            },\r\n            refulsh() {\r\n                this.$router.go(0);\r\n            },\r\n            searchData() {\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.filterTestData();\r\n            },\r\n            resetSearch() {\r\n                this.search = {\r\n                    nickname: \"\",\r\n                    phone: \"\",\r\n                    linkman: \"\",\r\n                    linkphone: \"\",\r\n                    company: \"\",\r\n                    yuangong_id: \"\",\r\n                    dateRange: [],\r\n                    prop: \"\",\r\n                    order: \"\",\r\n                };\r\n                this.page = 1;\r\n                this.size = 20;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n\r\n            getData() {\r\n                let _this = this;\r\n\r\n                _this.loading = true;\r\n                _this\r\n                    .postRequest(\r\n                        _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n                        _this.search\r\n                    )\r\n                    .then((resp) => {\r\n                        if (resp.code == 200) {\r\n                            _this.list = resp.data;\r\n                            _this.total = resp.count;\r\n\r\n                            if (resp.msg == '超级管理员') {\r\n                                _this.is_del = true;\r\n                            }\r\n                        }\r\n                        _this.loading = false;\r\n                    });\r\n            },\r\n            saveData() {\r\n                let _this = this;\r\n                console.log(this.ruleForm);\r\n                this.$refs[\"ruleForm\"].validate((valid) => {\r\n                    if (valid) {\r\n                        this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n                            if (resp.code == 200) {\r\n                                _this.$message({\r\n                                    type: \"success\",\r\n                                    message: resp.msg,\r\n                                });\r\n                                // 使用测试数据，注释掉API调用\r\n                                // this.getData();\r\n                                this.addTestData();\r\n                                _this.dialogFormVisible = false;\r\n                                _this.dialogAddUser = false;\r\n                                _this.drawerEditVisible = false;\r\n                            } else {\r\n                                _this.$message({\r\n                                    type: \"error\",\r\n                                    message: resp.msg,\r\n                                });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            handleSizeChange(val) {\r\n                this.size = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n            },\r\n            handleSuccess(res) {\r\n                this.ruleForm.pic_path = res.data.url;\r\n            },\r\n\r\n            showImage(file) {\r\n                this.show_image = file;\r\n                this.dialogVisible = true;\r\n            },\r\n            beforeUpload(file) {\r\n                const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n                if (!isTypeTrue) {\r\n                    this.$message.error(\"上传图片格式不对!\");\r\n                    return;\r\n                }\r\n            },\r\n            delImage(file, fileName) {\r\n                let _this = this;\r\n                _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n                    if (resp.code == 200) {\r\n                        _this.ruleForm[fileName] = \"\";\r\n\r\n                        _this.$message.success(\"删除成功!\");\r\n                    } else {\r\n                        _this.$message.error(resp.msg);\r\n                    }\r\n                });\r\n            },\r\n            handleSortChange({column, prop, order}) {\r\n                this.search.prop = prop;\r\n                this.search.order = order;\r\n                // 使用测试数据，注释掉API调用\r\n                // this.getData();\r\n                this.addTestData();\r\n                // 根据 column, prop, order 来更新你的数据排序\r\n                // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n            },\r\n            exports: function () { //导出表格\r\n                let _this = this;\r\n                location.href = \"/admin/user/export2?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n                // _this.postRequest(\r\n                //                 _this.url + \"export\",\r\n                //                 _this.search\r\n                //         )\r\n                //         .then((resp) => {\r\n                //           if (resp.code == 200) {\r\n                //\r\n                //           }\r\n                //         });\r\n            },\r\n            closeUploadDialog() { //关闭窗口\r\n                this.uploadVisible = false;\r\n                this.$refs.upload.clearFiles();\r\n                this.uploadData.review = false;\r\n            },\r\n            uploadSuccess(response) { //导入完成回调\r\n                if (response.code === 200) {\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: response.msg\r\n                    });\r\n                    this.uploadVisible = false;\r\n                    // 使用测试数据，注释掉API调用\r\n                    // this.getData();\r\n                    this.addTestData();\r\n                    console.log(response);\r\n                } else {\r\n                    this.$message({\r\n                        type: 'warning',\r\n                        message: response.msg\r\n                    });\r\n                }\r\n\r\n                this.submitOrderLoading2 = false;\r\n                this.$refs.upload.clearFiles();\r\n            },\r\n            checkFile(file) { //导入前校验文件后缀\r\n                let fileType = ['xls', 'xlsx'];\r\n                let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n                if (!fileType.includes(type)) {\r\n                    this.$message({\r\n                        type: \"warning\",\r\n                        message: \"文件格式错误仅支持 xls xlxs 文件\"\r\n                    });\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            submitUpload() { //导入提交\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            closeDialog() { //关闭窗口\r\n                this.addVisible = false;\r\n                this.uploadVisible = false;\r\n                this.form = {\r\n                    id: '',\r\n                    nickname: \"\",\r\n                    mobile: \"\",\r\n                    school_id: 0,\r\n                    grade_id: '',\r\n                    class_id: '',\r\n                    sex: '',\r\n                    is_poor: '',\r\n                    is_display: '',\r\n                    number: '',\r\n                    remark: '',\r\n                    is_remark_option: 0,\r\n                    remark_option: [],\r\n                    mobile_checked: false,\r\n                };\r\n                this.$refs.form.resetFields();\r\n            },\r\n            openUpload() { //打开导入弹窗\r\n                this.uploadVisible = true;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n            },\r\n            // 关闭导入对话框\r\n            closeUploadDialog() {\r\n                this.uploadVisible = false;\r\n                this.fileList = [];\r\n                this.uploadData.review = false;\r\n                if (this.$refs.upload) {\r\n                    this.$refs.upload.clearFiles();\r\n                }\r\n            },\r\n            // 上传前验证\r\n            beforeUpload(file) {\r\n                const isExcel = file.type === 'application/vnd.ms-excel' ||\r\n                               file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n                const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n                if (!isExcel) {\r\n                    this.$message.error('只能上传 Excel 文件!');\r\n                    return false;\r\n                }\r\n                if (!isLt10M) {\r\n                    this.$message.error('上传文件大小不能超过 10MB!');\r\n                    return false;\r\n                }\r\n                return true;\r\n            },\r\n            // 提交上传\r\n            submitUpload() {\r\n                if (this.fileList.length === 0) {\r\n                    this.$message.warning('请先选择要上传的文件');\r\n                    return;\r\n                }\r\n\r\n                this.submitOrderLoading2 = true;\r\n                this.$refs.upload.submit();\r\n            },\r\n            // 上传成功回调\r\n            handleUploadSuccess(response, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                if (response.code === 200) {\r\n                    this.$message.success(`导入成功！共导入 ${response.count || 0} 条用户数据`);\r\n                    this.closeUploadDialog();\r\n                    // 重新加载数据\r\n                    this.addTestData(); // 使用测试数据，实际应该调用 this.getData();\r\n                } else {\r\n                    this.$message.error(response.msg || '导入失败');\r\n                }\r\n            },\r\n            // 上传失败回调\r\n            handleUploadError(err, file, fileList) {\r\n                this.submitOrderLoading2 = false;\r\n                this.$message.error('文件上传失败，请重试');\r\n                console.error('Upload error:', err);\r\n            },\r\n            addUser() {\r\n                this.dialogAddUser = true;\r\n                this.ruleForm = {};\r\n                this.getYuangongs();\r\n            },\r\n            // 下载导入模板\r\n            downloadTemplate() {\r\n                const templateUrl = '/import_templete/user.xls';\r\n                const link = document.createElement('a');\r\n                link.href = templateUrl;\r\n                link.download = '用户导入模板.xls';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.$message.success('模板下载中...');\r\n            },\r\n            // 处理表格选择变化\r\n            handleSelectionChange(selection) {\r\n                this.selectedUsers = selection;\r\n            },\r\n            // 导出用户数据（选中或全部）\r\n            exportSelectedData() {\r\n                let exportUrl;\r\n                let message;\r\n\r\n                if (this.selectedUsers.length > 0) {\r\n                    // 导出选中的用户数据\r\n                    const userIds = this.selectedUsers.map(user => user.id).join(',');\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&ids=${userIds}`;\r\n                    message = `正在导出 ${this.selectedUsers.length} 个用户的数据`;\r\n                } else {\r\n                    // 导出全部用户数据\r\n                    exportUrl = `/admin/user/export2?token=${this.$store.getters.GET_TOKEN}&keyword=${this.search.keyword || ''}`;\r\n                    message = '正在导出全部用户数据';\r\n                }\r\n\r\n                // 执行导出\r\n                location.href = exportUrl;\r\n                this.$message.success(message);\r\n            },\r\n            // 批量删除用户\r\n            batchDeleteUsers() {\r\n                if (this.selectedUsers.length === 0) {\r\n                    this.$message.warning('请先选择要删除的用户');\r\n                    return;\r\n                }\r\n\r\n                this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`, '批量删除确认', {\r\n                    confirmButtonText: '确定删除',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning',\r\n                    dangerouslyUseHTMLString: true\r\n                }).then(() => {\r\n                    // 获取选中用户的ID列表\r\n                    const userIds = this.selectedUsers.map(user => user.id);\r\n\r\n                    // 这里应该调用批量删除API\r\n                    // this.postRequest(this.url + \"batchDelete\", { ids: userIds }).then((resp) => {\r\n                    //     if (resp.code == 200) {\r\n                    //         this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n                    //         this.getData(); // 重新加载数据\r\n                    //         this.selectedUsers = []; // 清空选择\r\n                    //     }\r\n                    // });\r\n\r\n                    // 临时使用本地删除模拟\r\n                    userIds.forEach(id => {\r\n                        const index = this.list.findIndex(user => user.id === id);\r\n                        if (index !== -1) {\r\n                            this.list.splice(index, 1);\r\n                        }\r\n                    });\r\n\r\n                    this.total = this.list.length;\r\n                    this.selectedUsers = []; // 清空选择\r\n                    this.$message.success(`成功删除 ${userIds.length} 个用户`);\r\n\r\n                    // 清空表格选择\r\n                    this.$refs.userTable.clearSelection();\r\n                }).catch(() => {\r\n                    this.$message.info('已取消删除操作');\r\n                });\r\n            }\r\n\r\n        },\r\n    };\r\n</script>\r\n<style scoped>\r\n/* 页面特定样式 */\r\n.page-wrapper {\r\n    background-color: #f5f5f5;\r\n    min-height: calc(100vh - 110px);\r\n    padding: 16px;\r\n}\r\n\r\n.page-container {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    padding: 24px;\r\n}\r\n\r\n.page-title {\r\n    font-size: 20px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-container {\r\n    background: #fff;\r\n    padding: 24px;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #e8e8e8;\r\n    box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.search-form {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.search-form .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.search-form .el-input__inner,\r\n.search-form .el-select .el-input__inner {\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.search-form .el-input__inner:focus,\r\n.search-form .el-select .el-input__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.search-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n}\r\n\r\n.search-buttons .el-button {\r\n    min-width: 80px;\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-wrap: wrap;\r\n    margin-top: 20px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-buttons .el-button {\r\n    border-radius: 4px;\r\n    font-weight: 500;\r\n}\r\n\r\n.data-table {\r\n    margin-top: 20px;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n}\r\n\r\n/* 表格样式优化 */\r\n.data-table .el-table {\r\n    border-radius: 8px;\r\n}\r\n\r\n.data-table .el-table th {\r\n    background-color: #fafafa !important;\r\n    color: #606266 !important;\r\n    font-weight: 500;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.data-table .el-table td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 头像样式 */\r\n.avatar-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    border: 2px solid #e8e8e8;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.user-avatar {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.no-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-name.clickable {\r\n    cursor: pointer;\r\n    color: #1890ff;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.user-name.clickable:hover {\r\n    color: #40a9ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n.user-phone {\r\n    color: #8c8c8c;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons-table {\r\n    display: flex;\r\n    gap: 8px;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.action-buttons-table .el-button {\r\n    padding: 5px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    padding: 16px 0;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.pagination-info {\r\n    display: flex;\r\n    gap: 16px;\r\n    color: #8c8c8c;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 编辑模式切换按钮样式 */\r\n.edit-mode-toggle {\r\n    display: flex;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n    padding: 16px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.edit-mode-toggle .el-button {\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.custom-dialog .el-dialog__body {\r\n    padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.card {\r\n    background: #fff;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.card-header {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.el-form-item {\r\n    margin-bottom: 18px;\r\n}\r\n\r\n.el-input, .el-select {\r\n    width: 100%;\r\n}\r\n\r\n/* 搜索表单特殊样式 */\r\n.search-form .el-input,\r\n.search-form .el-select,\r\n.search-form .el-date-picker {\r\n    width: 100%;\r\n}\r\n\r\n.search-form .el-date-picker {\r\n    width: 100% !important;\r\n}\r\n\r\n/* 表格行悬停效果 */\r\n.data-table .el-table tbody tr:hover {\r\n    background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n    .search-form .el-col {\r\n        margin-bottom: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .search-form .el-col {\r\n        width: 100% !important;\r\n        flex: 0 0 100% !important;\r\n        max-width: 100% !important;\r\n    }\r\n\r\n    .search-buttons {\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n    }\r\n\r\n    .action-buttons {\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    .page-container {\r\n        padding: 16px;\r\n        margin: 8px;\r\n    }\r\n\r\n    .pagination-container {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n        align-items: center;\r\n    }\r\n\r\n    .pagination-info {\r\n        order: 2;\r\n    }\r\n\r\n    .action-buttons-table {\r\n        flex-direction: column;\r\n        gap: 4px;\r\n    }\r\n\r\n    .action-buttons-table .el-button {\r\n        width: 100%;\r\n        margin: 0;\r\n    }\r\n}\r\n\r\n/* 抽屉样式 */\r\n.drawer-content-wrapper {\r\n    display: flex;\r\n    height: 100%;\r\n}\r\n\r\n.drawer-sidebar {\r\n    width: 200px;\r\n    border-right: 1px solid #e6e6e6;\r\n    background-color: #fafafa;\r\n}\r\n\r\n.drawer-menu {\r\n    border-right: none;\r\n    background-color: transparent;\r\n}\r\n\r\n.drawer-menu .el-menu-item {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    padding-left: 20px !important;\r\n}\r\n\r\n.drawer-menu .el-menu-item i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.drawer-content {\r\n    flex: 1;\r\n    padding: 20px;\r\n    height: 100%;\r\n    overflow-y: auto;\r\n}\r\n\r\n.tab-content {\r\n    height: 100%;\r\n}\r\n\r\n.drawer-content .card {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.drawer-content .card-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 16px;\r\n    padding-bottom: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.drawer-content .card-header i {\r\n    color: #1890ff;\r\n    font-size: 18px;\r\n}\r\n\r\n.drawer-footer {\r\n    margin-top: 24px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #f0f0f0;\r\n    text-align: right;\r\n}\r\n\r\n.drawer-footer .el-button {\r\n    margin-left: 12px;\r\n}\r\n\r\n/* 头像显示样式 */\r\n.avatar-display {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.detail-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    border: 2px solid #e8e8e8;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.detail-avatar:hover {\r\n    border-color: #1890ff;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.no-avatar-large {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #ccc;\r\n    font-size: 12px;\r\n    border: 2px solid #e8e8e8;\r\n}\r\n\r\n.no-avatar-large i {\r\n    font-size: 24px;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n/* 抽屉内表单样式 */\r\n.drawer-content .el-form-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.drawer-content .el-descriptions {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.drawer-content .el-descriptions-item__label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n}\r\n\r\n/* 无数据显示样式 */\r\n.no-data {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #ccc;\r\n    font-size: 14px;\r\n}\r\n\r\n.no-data i {\r\n    font-size: 48px;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n    color: #e8e8e8;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.attachment-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n}\r\n\r\n.attachment-item {\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n}\r\n\r\n.attachment-title {\r\n    background: #f5f5f5;\r\n    padding: 12px 16px;\r\n    font-weight: 500;\r\n    color: #262626;\r\n    border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.attachment-content {\r\n    padding: 16px;\r\n}\r\n\r\n.image-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 16px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.image-item {\r\n    position: relative;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    background: #fff;\r\n    transition: box-shadow 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n.attachment-image {\r\n    width: 100%;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.attachment-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-overlay {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: linear-gradient(transparent, rgba(0,0,0,0.7));\r\n    color: white;\r\n    padding: 12px;\r\n    transform: translateY(100%);\r\n    transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover .image-overlay {\r\n    transform: translateY(0);\r\n}\r\n\r\n.image-info {\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-info .file-name {\r\n    display: block;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.image-info .upload-time {\r\n    font-size: 11px;\r\n    opacity: 0.8;\r\n}\r\n\r\n.image-actions {\r\n    display: flex;\r\n    gap: 4px;\r\n}\r\n\r\n.image-actions .el-button {\r\n    flex: 1;\r\n    font-size: 11px;\r\n    padding: 4px 8px;\r\n}\r\n\r\n.file-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border: 1px solid #e8e8e8;\r\n    border-radius: 6px;\r\n    background: #fafafa;\r\n    transition: background-color 0.2s;\r\n}\r\n\r\n.file-item:hover {\r\n    background: #f0f0f0;\r\n}\r\n\r\n.file-icon {\r\n    margin-right: 12px;\r\n}\r\n\r\n.file-type-icon {\r\n    font-size: 24px;\r\n    color: #1890ff;\r\n}\r\n\r\n.file-info {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.file-info .file-name {\r\n    font-weight: 500;\r\n    color: #262626;\r\n    margin-bottom: 4px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.file-meta {\r\n    display: flex;\r\n    gap: 12px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.file-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    margin-left: 12px;\r\n}\r\n\r\n.no-attachment {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #999;\r\n}\r\n\r\n.no-attachment i {\r\n    font-size: 48px;\r\n    color: #d9d9d9;\r\n    margin-bottom: 12px;\r\n    display: block;\r\n}\r\n\r\n.no-attachment span {\r\n    display: block;\r\n    font-size: 14px;\r\n}\r\n\r\n/* 导入用户对话框样式 */\r\n.upload-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.upload-tips {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-tips .el-alert__description p {\r\n    margin: 5px 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.upload-actions {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.upload-area .el-upload-dragger {\r\n    width: 100%;\r\n    height: 180px;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: border-color 0.2s;\r\n}\r\n\r\n.upload-area .el-upload-dragger:hover {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-area .el-upload-dragger .el-icon-upload {\r\n    font-size: 67px;\r\n    color: #c0c4cc;\r\n    margin: 40px 0 16px;\r\n    line-height: 50px;\r\n}\r\n\r\n.upload-area .el-upload__text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n    text-align: center;\r\n}\r\n\r\n.upload-area .el-upload__text em {\r\n    color: #409eff;\r\n    font-style: normal;\r\n}\r\n\r\n.upload-area .el-upload__tip {\r\n    font-size: 12px;\r\n    color: #606266;\r\n    margin-top: 7px;\r\n}\r\n\r\n.upload-options {\r\n    text-align: center;\r\n}\r\n\r\n.upload-options .el-checkbox {\r\n    color: #606266;\r\n}\r\n\r\n/* 债务金额样式 */\r\n.debt-amount {\r\n    font-weight: 500;\r\n    color: #8c8c8c;\r\n}\r\n\r\n.debt-amount.has-debt {\r\n    color: #f56c6c;\r\n    font-weight: 600;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AA40CA;AACA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,YAAA,qCAAAC,MAAA,CAAAC,OAAA,CAAAC,SAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,UAAA;QACAC,MAAA;MACA;MACAC,QAAA;MAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,SAAA;MACAC,eAAA;MACAC,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,OAAA;QACAC,WAAA;QACAC,SAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,MAAA;MAAA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,aAAA;MAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACA;MACAC,iBAAA;MACAC,eAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,QAAA;QACAlD,IAAA;QACAmD,GAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,SAAA;QACAtD,IAAA,GACA;UAAAuD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,GAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,MAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,QAAA;QACA3B,KAAA;QACA4B,MAAA;MACA;MAEAC,KAAA;QACA7B,KAAA,GACA;UACAuB,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAK,cAAA;MACAC,eAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAC,MAAA;MACAC,EAAA;MACAC,GAAA;MACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,UAAA;QACAC,OAAA;QACAC,OAAA;QACAC,SAAA;QACAC,KAAA;QACAC,MAAA;QACAC,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,KAAA;QACAC,WAAA;QACAC,cAAA;QACAC,WAAA;QACAC,KAAA,GACA;UACAC,IAAA;UACAC,KAAA;UACAT,QAAA;QACA,GACA;UACAQ,IAAA;UACAC,KAAA;UACAT,QAAA;QACA;MAEA;MACAU,MAAA;QACAnB,SAAA,GACA;UACAlB,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAyB,QAAA,GACA;UACA3B,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACA6B,WAAA,GACA;UACA/B,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAwB,SAAA,GACA;UACA1B,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACA0B,IAAA,GACA;UACA5B,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAoC,QAAA;IACA;IACA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA;IACAC,oBAAA;MACA,QACA;QACAC,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACA;QACAC,SAAA;QACAC,YAAA;QACAC,SAAA;QACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA,GACA;UACA/G,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA,GACA;UACArD,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA,EACA;QACA2D,WAAA;UACAC,MAAA,GACA;YACAlF,GAAA;YACA/B,IAAA;UACA,GACA;YACA+B,GAAA;YACA/B,IAAA;UACA,EACA;UACAsG,OAAA,GACA;YACAvE,GAAA;YACA/B,IAAA;UACA,EACA;UACAkH,MAAA,GACA;YACAlH,IAAA;YACA+B,GAAA;YACAoF,IAAA;UACA;QAEA;MACA,GACA;QACAlB,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACA;QACAC,SAAA;QACAC,YAAA;QACAC,SAAA;QACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA,GACA;UACA/G,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA;MAEA,GACA;QACA4C,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACAO,KAAA,GACA;UACA/G,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA;MAEA,GACA;QACA4C,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACAO,KAAA;MACA,GACA;QACAd,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACAO,KAAA,GACA;UACA/G,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA,GACA;UACArD,IAAA;UACAmD,GAAA;UACAC,KAAA;UACAC,MAAA;QACA;MAEA,GACA;QACA4C,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACAO,KAAA;MACA,GACA;QACAd,EAAA;QACA5E,KAAA;QACAD,QAAA;QACAI,OAAA;QACAF,OAAA;QACAC,SAAA;QACAE,WAAA;QACAyE,QAAA;QACAC,WAAA;QACAC,eAAA;QACAC,OAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA;QACAO,KAAA;MACA,EACA;IACA;IACAjB,YAAA;MACA;MACA,KAAAjF,IAAA,QAAAmF,mBAAA;MACA,KAAAlF,KAAA,QAAAD,IAAA,CAAAuG,MAAA;MACA,KAAAtF,OAAA;IACA;IACA;IACAuF,eAAA;MACA,KAAAvF,OAAA;;MAEA;MACA,MAAAwF,YAAA,QAAAtB,mBAAA;MACA,IAAAuB,YAAA,OAAAD,YAAA;;MAEA;MACA,SAAAnG,MAAA,CAAAC,QAAA;QACA,MAAAA,QAAA,QAAAD,MAAA,CAAAC,QAAA,CAAAoG,WAAA;QACAD,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAtG,QAAA,IAAAsG,IAAA,CAAAtG,QAAA,CAAAoG,WAAA,GAAAG,QAAA,CAAAvG,QAAA,CACA;MACA;MAEA,SAAAD,MAAA,CAAAE,KAAA;QACAkG,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAArG,KAAA,IAAAqG,IAAA,CAAArG,KAAA,CAAAsG,QAAA,MAAAxG,MAAA,CAAAE,KAAA,CACA;MACA;MAEA,SAAAF,MAAA,CAAAG,OAAA;QACA,MAAAA,OAAA,QAAAH,MAAA,CAAAG,OAAA,CAAAkG,WAAA;QACAD,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAApG,OAAA,IAAAoG,IAAA,CAAApG,OAAA,CAAAkG,WAAA,GAAAG,QAAA,CAAArG,OAAA,CACA;MACA;MAEA,SAAAH,MAAA,CAAAI,SAAA;QACAgG,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAnG,SAAA,IAAAmG,IAAA,CAAAnG,SAAA,CAAAoG,QAAA,MAAAxG,MAAA,CAAAI,SAAA,CACA;MACA;MAEA,SAAAJ,MAAA,CAAAK,OAAA;QACA,MAAAA,OAAA,QAAAL,MAAA,CAAAK,OAAA,CAAAgG,WAAA;QACAD,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAlG,OAAA,IAAAkG,IAAA,CAAAlG,OAAA,CAAAgG,WAAA,GAAAG,QAAA,CAAAnG,OAAA,CACA;MACA;MAEA,SAAAL,MAAA,CAAAM,WAAA;QACA8F,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAjG,WAAA,UAAAN,MAAA,CAAAM,WACA;MACA;;MAEA;MACA,SAAAN,MAAA,CAAAO,SAAA,SAAAP,MAAA,CAAAO,SAAA,CAAA0F,MAAA;QACA,MAAAQ,SAAA,OAAAC,IAAA,MAAA1G,MAAA,CAAAO,SAAA;QACA,MAAAoG,OAAA,OAAAD,IAAA,MAAA1G,MAAA,CAAAO,SAAA;QACA6F,YAAA,GAAAA,YAAA,CAAAE,MAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAvB,WAAA;YACA,MAAA4B,UAAA,OAAAF,IAAA,CAAAH,IAAA,CAAAvB,WAAA,CAAA6B,KAAA;YACA,OAAAD,UAAA,IAAAH,SAAA,IAAAG,UAAA,IAAAD,OAAA;UACA;UACA;QACA;MACA;;MAEA;MACA,KAAAjH,IAAA,GAAA0G,YAAA;MACA,KAAAzG,KAAA,GAAAyG,YAAA,CAAAH,MAAA;MACA,KAAAtF,OAAA;;MAEA;MACA,MAAAmG,kBAAA,QAAA9G,MAAA,CAAAC,QAAA,SAAAD,MAAA,CAAAE,KAAA,SAAAF,MAAA,CAAAG,OAAA,IACA,KAAAH,MAAA,CAAAI,SAAA,SAAAJ,MAAA,CAAAK,OAAA,SAAAL,MAAA,CAAAM,WAAA,IACA,KAAAN,MAAA,CAAAO,SAAA,SAAAP,MAAA,CAAAO,SAAA,CAAA0F,MAAA;MAEA,IAAAa,kBAAA;QACA,KAAAC,QAAA,CAAAC,OAAA,YAAAZ,YAAA,CAAAH,MAAA;MACA;IACA;IACAxF,MAAAwG,GAAA;MACA,KAAArE,eAAA;MACA,KAAA9B,IAAA,GAAAmG,GAAA;MACA,KAAA7D,SAAA;QACAC,SAAA,EAAA4D,GAAA,CAAAnC,EAAA;QACAxB,SAAA;QACAO,WAAA;QACAC,SAAA;QACAC,QAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACA,KAAAiD,SAAA;QACA,KAAAC,UAAA;MACA;IACA;IACAC,UAAA;MACA,IAAAC,KAAA;MAEA,KAAAC,KAAA,cAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,uBAAArE,SAAA,EAAAsE,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAP,KAAA,CAAAN,QAAA;gBACAf,IAAA;gBACA3D,OAAA,EAAAsF,IAAA,CAAAE;cACA;cACA;cACAR,KAAA,CAAAzE,eAAA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAkF,aAAAC,CAAA;MACA,KAAA3E,SAAA,CAAAgB,cAAA;MACA,KAAAhB,SAAA,CAAAiB,WAAA;MACA,KAAA2D,UAAA,sBAAAD,CAAA,EAAAL,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAxE,SAAA,CAAAS,WAAA,GAAA8D,IAAA,CAAA5I,IAAA,CAAAyF,KAAA;UACA,KAAApB,SAAA,CAAAU,SAAA,GAAA6D,IAAA,CAAA5I,IAAA,CAAAyF,KAAA;QACA;MACA;IACA;IACA2C,WAAA;MACA,KAAAM,WAAA,wBAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAA/E,OAAA,GAAA8E,IAAA,CAAA5I,IAAA;QACA;MACA;IACA;IACAkJ,aAAA;MACA,IAAAZ,KAAA;MACA,KAAAI,WAAA,0BAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAAvE,QAAA,GAAA6E,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACAd,KAAA,CAAAtE,KAAA,GAAA4E,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACAd,KAAA,CAAArE,KAAA,GAAA2E,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACAd,KAAA,CAAAlE,GAAA,GAAAwE,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACAd,KAAA,CAAAnE,EAAA,GAAAyE,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;UACAd,KAAA,CAAApE,MAAA,GAAA0E,IAAA,CAAA5I,IAAA,CAAAuH,MAAA,CAAA4B,IAAA,IAAAA,IAAA,CAAAC,SAAA;QACA;MACA;IACA;IACAC,SAAAtD,EAAA;MACAuD,OAAA,CAAAC,GAAA,6BAAAxD,EAAA;MACA,IAAAuC,KAAA;MACA,IAAAvC,EAAA;QACA,KAAAhF,SAAA,GAAAgF,EAAA;QACA;QACA,KAAA/E,eAAA,QAAAL,IAAA,CAAA6I,IAAA,CAAAhC,IAAA,IAAAA,IAAA,CAAAzB,EAAA,KAAAA,EAAA;QACAuD,OAAA,CAAAC,GAAA,0BAAAvI,eAAA;QACA;QACA,KAAAsB,UAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;QACA;QACA,KAAAC,SAAA;MACA;MACA6F,KAAA,CAAAlG,iBAAA;MACAkH,OAAA,CAAAC,GAAA,8BAAAjB,KAAA,CAAAlG,iBAAA;IACA;IACAqH,gBAAAC,GAAA;MACA,KAAAjH,SAAA,GAAAiH,GAAA;MACA;MACA,IAAAA,GAAA;QACA,KAAApH,UAAA;MACA;IACA;IACAqH,eAAA;MACA,UAAArH,UAAA;QACA;QACA,KAAAA,UAAA;QACA;QACA,KAAAE,gBAAA,GAAAoH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA9I,eAAA;QACA;QACA,KAAAuB,QAAA,GAAAqH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA9I,eAAA;QACA;QACA,KAAAkI,YAAA;MACA;QACA;QACA,KAAA5G,UAAA;MACA;IACA;IACAyH,WAAA;MACA;MACA,KAAA/I,eAAA,GAAA4I,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAtH,gBAAA;MACA,KAAAF,UAAA;MACA,KAAAC,QAAA;MACA,KAAAyF,QAAA,CAAAjG,IAAA;IACA;IACAiI,aAAA;MACA;MACA,KAAAzB,KAAA,CAAAhG,QAAA,CAAAiG,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAzH,eAAA,GAAA4I,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvH,QAAA;;UAEA;UACA,MAAA0H,KAAA,QAAAtJ,IAAA,CAAAuJ,SAAA,CAAA1C,IAAA,IAAAA,IAAA,CAAAzB,EAAA,UAAA/E,eAAA,CAAA+E,EAAA;UACA,IAAAkE,KAAA;YACA,KAAAtJ,IAAA,CAAAwJ,MAAA,CAAAF,KAAA,UAAAjJ,eAAA;UACA;;UAEA;UACA,KAAAsB,UAAA;UACA,KAAAC,QAAA;UAEA,KAAAyF,QAAA,CAAAC,OAAA;QACA;UACA,KAAAD,QAAA,CAAAoC,KAAA;UACA;QACA;MACA;IACA;IACAC,SAAAtE,EAAA;MACA,IAAAuC,KAAA;MACA,IAAAvC,EAAA;QACA,KAAAuE,OAAA,CAAAvE,EAAA;MACA;QACA,KAAAtC,QAAA;UACA3B,KAAA;UACAmD,IAAA;QACA;MACA;MACAqD,KAAA,CAAAjG,iBAAA;MACAiG,KAAA,CAAAY,YAAA;IACA;IACAqB,kBAAA;MACA,KAAAnI,iBAAA;MACA,KAAAC,iBAAA;MACA;MACA,KAAAC,UAAA;MACA,KAAAC,QAAA;MACA,KAAAC,gBAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACA+H,QAAA;MACA,KAAA5H,iBAAA;MACA,KAAAI,QAAA;QACAlD,IAAA;QACAmD,GAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA,KAAAN,eAAA;MACA,KAAAC,aAAA;IACA;IACA2H,SAAAC,IAAA,EAAAT,KAAA;MACA,KAAArH,iBAAA;MACA,KAAAI,QAAA;QACAlD,IAAA,EAAA4K,IAAA,CAAA5K,IAAA;QACAmD,GAAA,EAAAyH,IAAA,CAAAzH,GAAA;QACAC,KAAA,EAAAyH,UAAA,CAAAD,IAAA,CAAAxH,KAAA;QACAC,MAAA,EAAAuH,IAAA,CAAAvH;MACA;MACA,KAAAN,eAAA;MACA,KAAAC,aAAA;MACA,KAAAC,gBAAA,GAAAkH,KAAA;IACA;IACAW,SAAA;MACA,KAAArC,KAAA,CAAAvF,QAAA,CAAAwF,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,MAAAoC,QAAA;YACA/K,IAAA,OAAAkD,QAAA,CAAAlD,IAAA;YACAmD,GAAA,OAAAD,QAAA,CAAAC,GAAA;YACAC,KAAA,OAAAF,QAAA,CAAAE,KAAA,CAAA4H,QAAA;YACA3H,MAAA,OAAAH,QAAA,CAAAG;UACA;UAEA,SAAAL,aAAA;YACA;YACA,KAAA9B,eAAA,CAAA6F,KAAA,MAAA9D,gBAAA,IAAA8H,QAAA;YACA,KAAA7C,QAAA,CAAAC,OAAA;UACA;YACA;YACA,UAAAjH,eAAA,CAAA6F,KAAA;cACA,KAAA7F,eAAA,CAAA6F,KAAA;YACA;YACA,KAAA7F,eAAA,CAAA6F,KAAA,CAAAkE,IAAA,CAAAF,QAAA;YACA,KAAA7C,QAAA,CAAAC,OAAA;UACA;;UAEA;UACA,MAAA+C,SAAA,QAAArK,IAAA,CAAAuJ,SAAA,CAAA1C,IAAA,IAAAA,IAAA,CAAAzB,EAAA,UAAA/E,eAAA,CAAA+E,EAAA;UACA,IAAAiF,SAAA;YACA,KAAArK,IAAA,CAAAqK,SAAA,EAAAnE,KAAA,YAAA7F,eAAA,CAAA6F,KAAA;UACA;UAEA,KAAAoE,eAAA;QACA;UACA,KAAAjD,QAAA,CAAAoC,KAAA;UACA;QACA;MACA;IACA;IACAa,gBAAA;MACA,KAAArI,iBAAA;MACA,KAAAI,QAAA;QACAlD,IAAA;QACAmD,GAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA,KAAAL,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAF,eAAA;MACA;MACA,KAAAsF,SAAA;QACA,SAAAI,KAAA,CAAAvF,QAAA;UACA,KAAAuF,KAAA,CAAAvF,QAAA,CAAAkI,aAAA;QACA;MACA;IACA;IACAC,WAAAlB,KAAA;MACA,KAAAmB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GAAA0B,IAAA;QACA,KAAA3H,eAAA,CAAA6F,KAAA,CAAAsD,MAAA,CAAAF,KAAA;QACA,KAAAjC,QAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAsD,cAAA;MACA,KAAAvD,QAAA,CAAAjG,IAAA;IACA;IACAyJ,aAAA;MACA,KAAAC,eAAA,YAAAC,KAAA;QACA,KAAAC,gBAAA,CAAAD,KAAA;MACA;IACA;IACAE,cAAA;MACA,KAAAH,eAAA,YAAAC,KAAA;QACA,KAAAC,gBAAA,CAAAD,KAAA;MACA;IACA;IACAG,aAAA;MACA,KAAAJ,eAAA,MAAAC,KAAA;QACA,KAAAC,gBAAA,CAAAD,KAAA;MACA;IACA;IACA;IACAD,gBAAAK,MAAA,EAAAC,QAAA;MACA,MAAAC,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,KAAA,CAAA/E,IAAA;MACA+E,KAAA,CAAAF,MAAA,GAAAA,MAAA;MACAE,KAAA,CAAAG,QAAA;MACAH,KAAA,CAAAI,KAAA,CAAAC,OAAA;MAEAL,KAAA,CAAAM,QAAA,GAAAtD,CAAA;QACA,MAAA0C,KAAA,GAAAa,KAAA,CAAAC,IAAA,CAAAxD,CAAA,CAAAyD,MAAA,CAAAf,KAAA;QACA,IAAAA,KAAA,CAAAxE,MAAA;UACA6E,QAAA,CAAAL,KAAA;QACA;QACAO,QAAA,CAAAS,IAAA,CAAAC,WAAA,CAAAX,KAAA;MACA;MAEAC,QAAA,CAAAS,IAAA,CAAAE,WAAA,CAAAZ,KAAA;MACAA,KAAA,CAAAa,KAAA;IACA;IACA;IACAlB,iBAAAD,KAAA,EAAAzE,IAAA,EAAA6F,QAAA;MACA,KAAApB,KAAA,IAAAA,KAAA,CAAAxE,MAAA;QACA,KAAAc,QAAA,CAAA+E,OAAA;QACA;MACA;;MAEA;MACA,SAAAC,IAAA,IAAAtB,KAAA;QACA,IAAAzE,IAAA,kBAAA+F,IAAA,CAAA/F,IAAA,CAAAgG,UAAA;UACA,KAAAjF,QAAA,CAAAoC,KAAA,IAAA0C,QAAA;UACA;QACA;QACA,IAAAE,IAAA,CAAAlM,IAAA;UAAA;UACA,KAAAkH,QAAA,CAAAoC,KAAA,OAAA4C,IAAA,CAAAlN,IAAA;UACA;QACA;MACA;;MAEA;MACA,UAAAkB,eAAA,CAAA8F,WAAA;QACA,KAAA9F,eAAA,CAAA8F,WAAA;MACA;MACA,UAAA9F,eAAA,CAAA8F,WAAA,CAAAG,IAAA;QACA,KAAAjG,eAAA,CAAA8F,WAAA,CAAAG,IAAA;MACA;;MAEA;MACA,KAAAe,QAAA,CAAAjG,IAAA,SAAA2J,KAAA,CAAAxE,MAAA;MAEAwE,KAAA,CAAAwB,OAAA,EAAAF,IAAA,EAAA/C,KAAA;QACA;QACA,MAAAkD,OAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;;QAEA;QACAM,UAAA;UACA,MAAAC,QAAA;YACAzN,IAAA,EAAAkN,IAAA,CAAAlN,IAAA;YACA+B,GAAA,EAAAsL,OAAA;YACArM,IAAA,EAAAkM,IAAA,CAAAlM,IAAA;YACAmG,IAAA,EAAA+F,IAAA,CAAA/F,IAAA;YACAuG,UAAA,MAAA7F,IAAA,GAAA8F,cAAA;UACA;UAEA,KAAAzM,eAAA,CAAA8F,WAAA,CAAAG,IAAA,EAAA8D,IAAA,CAAAwC,QAAA;UAEA,IAAAtD,KAAA,KAAAyB,KAAA,CAAAxE,MAAA;YACA,KAAAc,QAAA,CAAAC,OAAA,IAAA6E,QAAA,YAAApB,KAAA,CAAAxE,MAAA;UACA;QACA,IAAA+C,KAAA;MACA;IACA;IACAyD,iBAAAzG,IAAA,EAAAgD,KAAA;MACA,KAAAmB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GAAA0B,IAAA;QACA,UAAA3H,eAAA,CAAA8F,WAAA;UACA,KAAA9F,eAAA,CAAA8F,WAAA;QACA;QACA,SAAA9F,eAAA,CAAA8F,WAAA,CAAAG,IAAA;UACA;UACA,MAAA+F,IAAA,QAAAhM,eAAA,CAAA8F,WAAA,CAAAG,IAAA,EAAAgD,KAAA;UACA,IAAA+C,IAAA,IAAAA,IAAA,CAAAnL,GAAA,IAAAmL,IAAA,CAAAnL,GAAA,CAAAoL,UAAA;YACAG,GAAA,CAAAO,eAAA,CAAAX,IAAA,CAAAnL,GAAA;UACA;UAEA,KAAAb,eAAA,CAAA8F,WAAA,CAAAG,IAAA,EAAAkD,MAAA,CAAAF,KAAA;UACA,KAAAjC,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACA2F,aAAAZ,IAAA;MACA,KAAAA,IAAA,KAAAA,IAAA,CAAAnL,GAAA;QACA,KAAAmG,QAAA,CAAAoC,KAAA;QACA;MACA;MAEA;QACA;QACA,MAAAyD,IAAA,GAAA5B,QAAA,CAAAC,aAAA;QACA2B,IAAA,CAAAC,IAAA,GAAAd,IAAA,CAAAnL,GAAA;QACAgM,IAAA,CAAAE,QAAA,GAAAf,IAAA,CAAAlN,IAAA;QACA+N,IAAA,CAAAzB,KAAA,CAAAC,OAAA;QAEAJ,QAAA,CAAAS,IAAA,CAAAE,WAAA,CAAAiB,IAAA;QACAA,IAAA,CAAAhB,KAAA;QACAZ,QAAA,CAAAS,IAAA,CAAAC,WAAA,CAAAkB,IAAA;QAEA,KAAA7F,QAAA,CAAAC,OAAA,UAAA+E,IAAA,CAAAlN,IAAA;MACA,SAAAsK,KAAA;QACA,KAAApC,QAAA,CAAAoC,KAAA;QACAd,OAAA,CAAAc,KAAA,UAAAA,KAAA;MACA;IACA;IACA;IACA4D,YAAAC,QAAA;MACA,KAAAA,QAAA;MAEA,IAAAA,QAAA,CAAAhB,UAAA;MACA,IAAAgB,QAAA,CAAAxG,QAAA;MACA,IAAAwG,QAAA,CAAAxG,QAAA,YAAAwG,QAAA,CAAAxG,QAAA;MACA,IAAAwG,QAAA,CAAAxG,QAAA,aAAAwG,QAAA,CAAAxG,QAAA;MACA,IAAAwG,QAAA,CAAAxG,QAAA,WAAAwG,QAAA,CAAAxG,QAAA;MACA,IAAAwG,QAAA,CAAAxG,QAAA;MACA,IAAAwG,QAAA,CAAAxG,QAAA;MAEA;IACA;IACAyG,eAAAC,KAAA;MACA,IAAAA,KAAA;MAEA,MAAAC,CAAA;MACA,MAAAC,KAAA;MACA,MAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAhF,GAAA,CAAA4E,KAAA,IAAAI,IAAA,CAAAhF,GAAA,CAAA6E,CAAA;MAEA,OAAAzD,UAAA,EAAAwD,KAAA,GAAAI,IAAA,CAAAE,GAAA,CAAAL,CAAA,EAAAE,CAAA,GAAAI,OAAA,aAAAL,KAAA,CAAAC,CAAA;IACA;IACA;IACAK,aAAA9H,KAAA;MACA,OAAAA,KAAA,IAAAA,KAAA,CAAAK,MAAA,GAAAL,KAAA,CAAAK,MAAA;IACA;IACA0H,iBAAA/H,KAAA;MACA,MAAAgI,KAAA,QAAAF,YAAA,CAAA9H,KAAA;MACA,IAAAgI,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAC,mBAAAjI,KAAA;MACA,KAAAA,KAAA,KAAAA,KAAA,CAAAK,MAAA;MACA,OAAAL,KAAA,CAAAkI,MAAA,EAAAnO,KAAA,EAAA8J,IAAA;QACA,OAAA9J,KAAA,IAAA+J,UAAA,CAAAD,IAAA,CAAAxH,KAAA;MACA;IACA;IACA8L,aAAAC,MAAA;MACA,IAAAA,MAAA;MACA,OAAAA,MAAA,CAAAxB,cAAA;QACAyB,qBAAA;QACAC,qBAAA;MACA;IACA;IACA7E,QAAAvE,EAAA;MACA,IAAAuC,KAAA;MACAA,KAAA,CAAAW,UAAA,CAAAX,KAAA,CAAAzG,GAAA,gBAAAkE,EAAA,EAAA4C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAA,IAAA,CAAA5I,IAAA,CAAAwE,UAAA,GAAAoE,IAAA,CAAA5I,IAAA,CAAAwE,UAAA,aAAAoE,IAAA,CAAA5I,IAAA,CAAAwE,UAAA;UACAoE,IAAA,CAAA5I,IAAA,CAAAyE,OAAA,GAAAmE,IAAA,CAAA5I,IAAA,CAAAyE,OAAA,aAAAmE,IAAA,CAAA5I,IAAA,CAAAyE,OAAA;UACAmE,IAAA,CAAA5I,IAAA,CAAA0E,OAAA,GAAAkE,IAAA,CAAA5I,IAAA,CAAA0E,OAAA,aAAAkE,IAAA,CAAA5I,IAAA,CAAA0E,OAAA;UACAkE,IAAA,CAAA5I,IAAA,CAAA6E,MAAA,GAAA+D,IAAA,CAAA5I,IAAA,CAAA6E,MAAA,aAAA+D,IAAA,CAAA5I,IAAA,CAAA6E,MAAA;UACA+D,IAAA,CAAA5I,IAAA,CAAA2E,SAAA,GAAAiE,IAAA,CAAA5I,IAAA,CAAA2E,SAAA,aAAAiE,IAAA,CAAA5I,IAAA,CAAA2E,SAAA;UACAiE,IAAA,CAAA5I,IAAA,CAAA4E,KAAA,GAAAgE,IAAA,CAAA5I,IAAA,CAAA4E,KAAA,aAAAgE,IAAA,CAAA5I,IAAA,CAAA4E,KAAA;UACA0D,KAAA,CAAA7E,QAAA,GAAAmF,IAAA,CAAA5I,IAAA;QACA;MACA;IACA;IACAoP,QAAAnF,KAAA,EAAAlE,EAAA;MACA,KAAAqF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GACA0B,IAAA;QACA,KAAAD,WAAA,MAAA7G,GAAA,kBAAAkE,EAAA,EAAA4C,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAb,QAAA;cACAf,IAAA;cACA3D,OAAA;YACA;YACA,KAAA3C,IAAA,CAAAwJ,MAAA,CAAAF,KAAA;UACA;QACA;MACA,GACAoF,KAAA;QACA,KAAArH,QAAA;UACAf,IAAA;UACA3D,OAAA;QACA;MACA;IACA;IACAgM,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA5O,IAAA;MACA,KAAAC,IAAA;MACA;MACA;MACA,KAAAqG,cAAA;IACA;IACAuI,YAAA;MACA,KAAAzO,MAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,OAAA;QACAC,WAAA;QACAC,SAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACA,KAAAb,IAAA;MACA,KAAAC,IAAA;MACA;MACA;MACA,KAAA8E,WAAA;IACA;IAEA+J,QAAA;MACA,IAAArH,KAAA;MAEAA,KAAA,CAAA1G,OAAA;MACA0G,KAAA,CACAI,WAAA,CACAJ,KAAA,CAAAzG,GAAA,mBAAAyG,KAAA,CAAAzH,IAAA,cAAAyH,KAAA,CAAAxH,IAAA,EACAwH,KAAA,CAAArH,MACA,EACA0H,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAA3H,IAAA,GAAAiI,IAAA,CAAA5I,IAAA;UACAsI,KAAA,CAAA1H,KAAA,GAAAgI,IAAA,CAAAiG,KAAA;UAEA,IAAAjG,IAAA,CAAAE,GAAA;YACAR,KAAA,CAAA3G,MAAA;UACA;QACA;QACA2G,KAAA,CAAA1G,OAAA;MACA;IACA;IACAgO,SAAA;MACA,IAAAtH,KAAA;MACAgB,OAAA,CAAAC,GAAA,MAAA9F,QAAA;MACA,KAAA8E,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,WAAA,CAAAJ,KAAA,CAAAzG,GAAA,gBAAA4B,QAAA,EAAAkF,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAP,KAAA,CAAAN,QAAA;gBACAf,IAAA;gBACA3D,OAAA,EAAAsF,IAAA,CAAAE;cACA;cACA;cACA;cACA,KAAAlD,WAAA;cACA0C,KAAA,CAAArG,iBAAA;cACAqG,KAAA,CAAAnG,aAAA;cACAmG,KAAA,CAAAjG,iBAAA;YACA;cACAiG,KAAA,CAAAN,QAAA;gBACAf,IAAA;gBACA3D,OAAA,EAAAsF,IAAA,CAAAE;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA+G,iBAAAC,GAAA;MACA,KAAAhP,IAAA,GAAAgP,GAAA;MACA;MACA;MACA,KAAAlK,WAAA;IACA;IACAmK,oBAAAD,GAAA;MACA,KAAAjP,IAAA,GAAAiP,GAAA;MACA;MACA;MACA,KAAAlK,WAAA;IACA;IACAoK,cAAAC,GAAA;MACA,KAAAxM,QAAA,CAAAyM,QAAA,GAAAD,GAAA,CAAAjQ,IAAA,CAAA6B,GAAA;IACA;IAEAsO,UAAAnD,IAAA;MACA,KAAAtK,UAAA,GAAAsK,IAAA;MACA,KAAArK,aAAA;IACA;IACAyN,aAAApD,IAAA;MACA,MAAAqD,UAAA,6BAAAC,IAAA,CAAAtD,IAAA,CAAA/F,IAAA;MACA,KAAAoJ,UAAA;QACA,KAAArI,QAAA,CAAAoC,KAAA;QACA;MACA;IACA;IACAmG,SAAAvD,IAAA,EAAAwD,QAAA;MACA,IAAAlI,KAAA;MACAA,KAAA,CAAAW,UAAA,gCAAA+D,IAAA,EAAArE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAP,KAAA,CAAA7E,QAAA,CAAA+M,QAAA;UAEAlI,KAAA,CAAAN,QAAA,CAAAC,OAAA;QACA;UACAK,KAAA,CAAAN,QAAA,CAAAoC,KAAA,CAAAxB,IAAA,CAAAE,GAAA;QACA;MACA;IACA;IACA2H,iBAAA;MAAAC,MAAA;MAAAjP,IAAA;MAAAC;IAAA;MACA,KAAAT,MAAA,CAAAQ,IAAA,GAAAA,IAAA;MACA,KAAAR,MAAA,CAAAS,KAAA,GAAAA,KAAA;MACA;MACA;MACA,KAAAkE,WAAA;MACA;MACA;IACA;IACA+K,OAAA,WAAAA,CAAA;MAAA;MACA,IAAArI,KAAA;MACAsI,QAAA,CAAA9C,IAAA,kCAAAxF,KAAA,CAAApI,MAAA,CAAAC,OAAA,CAAAC,SAAA,iBAAAkI,KAAA,CAAArH,MAAA,CAAA4P,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,kBAAA;MAAA;MACA,KAAAzQ,aAAA;MACA,KAAAkI,KAAA,CAAAwI,MAAA,CAAAC,UAAA;MACA,KAAAzQ,UAAA,CAAAC,MAAA;IACA;IACAyQ,cAAAC,QAAA;MAAA;MACA,IAAAA,QAAA,CAAArI,IAAA;QACA,KAAAb,QAAA;UACAf,IAAA;UACA3D,OAAA,EAAA4N,QAAA,CAAApI;QACA;QACA,KAAAzI,aAAA;QACA;QACA;QACA,KAAAuF,WAAA;QACA0D,OAAA,CAAAC,GAAA,CAAA2H,QAAA;MACA;QACA,KAAAlJ,QAAA;UACAf,IAAA;UACA3D,OAAA,EAAA4N,QAAA,CAAApI;QACA;MACA;MAEA,KAAAxI,mBAAA;MACA,KAAAiI,KAAA,CAAAwI,MAAA,CAAAC,UAAA;IACA;IACAG,UAAAnE,IAAA;MAAA;MACA,IAAAiB,QAAA;MACA,IAAAhH,IAAA,GAAA+F,IAAA,CAAAlN,IAAA,CAAAgI,KAAA,MAAAsJ,KAAA,QAAA9J,WAAA;MACA,KAAA2G,QAAA,CAAAxG,QAAA,CAAAR,IAAA;QACA,KAAAe,QAAA;UACAf,IAAA;UACA3D,OAAA;QACA;QACA;MACA;MACA;IACA;IACA+N,aAAA;MAAA;MACA,KAAA/Q,mBAAA;MACA,KAAAiI,KAAA,CAAAwI,MAAA,CAAAO,MAAA;IACA;IACAC,YAAA;MAAA;MACA,KAAAC,UAAA;MACA,KAAAnR,aAAA;MACA,KAAAoR,IAAA;QACA1L,EAAA;QACA7E,QAAA;QACAwQ,MAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,GAAA;QACAC,OAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA,KAAA9J,KAAA,CAAAkJ,IAAA,CAAAa,WAAA;IACA;IACAC,WAAA;MAAA;MACA,KAAAlS,aAAA;MACA,KAAAI,QAAA;MACA,KAAAF,UAAA,CAAAC,MAAA;IACA;IACA;IACAsQ,kBAAA;MACA,KAAAzQ,aAAA;MACA,KAAAI,QAAA;MACA,KAAAF,UAAA,CAAAC,MAAA;MACA,SAAA+H,KAAA,CAAAwI,MAAA;QACA,KAAAxI,KAAA,CAAAwI,MAAA,CAAAC,UAAA;MACA;IACA;IACA;IACAZ,aAAApD,IAAA;MACA,MAAAwF,OAAA,GAAAxF,IAAA,CAAA/F,IAAA,mCACA+F,IAAA,CAAA/F,IAAA;MACA,MAAAwL,OAAA,GAAAzF,IAAA,CAAAlM,IAAA;MAEA,KAAA0R,OAAA;QACA,KAAAxK,QAAA,CAAAoC,KAAA;QACA;MACA;MACA,KAAAqI,OAAA;QACA,KAAAzK,QAAA,CAAAoC,KAAA;QACA;MACA;MACA;IACA;IACA;IACAiH,aAAA;MACA,SAAA5Q,QAAA,CAAAyG,MAAA;QACA,KAAAc,QAAA,CAAA+E,OAAA;QACA;MACA;MAEA,KAAAzM,mBAAA;MACA,KAAAiI,KAAA,CAAAwI,MAAA,CAAAO,MAAA;IACA;IACA;IACAoB,oBAAAxB,QAAA,EAAAlE,IAAA,EAAAvM,QAAA;MACA,KAAAH,mBAAA;MACA,IAAA4Q,QAAA,CAAArI,IAAA;QACA,KAAAb,QAAA,CAAAC,OAAA,aAAAiJ,QAAA,CAAArC,KAAA;QACA,KAAAiC,iBAAA;QACA;QACA,KAAAlL,WAAA;MACA;QACA,KAAAoC,QAAA,CAAAoC,KAAA,CAAA8G,QAAA,CAAApI,GAAA;MACA;IACA;IACA;IACA6J,kBAAAC,GAAA,EAAA5F,IAAA,EAAAvM,QAAA;MACA,KAAAH,mBAAA;MACA,KAAA0H,QAAA,CAAAoC,KAAA;MACAd,OAAA,CAAAc,KAAA,kBAAAwI,GAAA;IACA;IACAC,QAAA;MACA,KAAA1Q,aAAA;MACA,KAAAsB,QAAA;MACA,KAAAyF,YAAA;IACA;IACA;IACA4J,iBAAA;MACA,MAAAC,WAAA;MACA,MAAAlF,IAAA,GAAA5B,QAAA,CAAAC,aAAA;MACA2B,IAAA,CAAAC,IAAA,GAAAiF,WAAA;MACAlF,IAAA,CAAAE,QAAA;MACA9B,QAAA,CAAAS,IAAA,CAAAE,WAAA,CAAAiB,IAAA;MACAA,IAAA,CAAAhB,KAAA;MACAZ,QAAA,CAAAS,IAAA,CAAAC,WAAA,CAAAkB,IAAA;MACA,KAAA7F,QAAA,CAAAC,OAAA;IACA;IACA;IACA+K,sBAAAC,SAAA;MACA,KAAAjR,aAAA,GAAAiR,SAAA;IACA;IACA;IACAC,mBAAA;MACA,IAAAC,SAAA;MACA,IAAA7P,OAAA;MAEA,SAAAtB,aAAA,CAAAkF,MAAA;QACA;QACA,MAAAkM,OAAA,QAAApR,aAAA,CAAAqR,GAAA,CAAA7L,IAAA,IAAAA,IAAA,CAAAzB,EAAA,EAAAuN,IAAA;QACAH,SAAA,qCAAAjT,MAAA,CAAAC,OAAA,CAAAC,SAAA,QAAAgT,OAAA;QACA9P,OAAA,gBAAAtB,aAAA,CAAAkF,MAAA;MACA;QACA;QACAiM,SAAA,qCAAAjT,MAAA,CAAAC,OAAA,CAAAC,SAAA,iBAAAa,MAAA,CAAA4P,OAAA;QACAvN,OAAA;MACA;;MAEA;MACAsN,QAAA,CAAA9C,IAAA,GAAAqF,SAAA;MACA,KAAAnL,QAAA,CAAAC,OAAA,CAAA3E,OAAA;IACA;IACA;IACAiQ,iBAAA;MACA,SAAAvR,aAAA,CAAAkF,MAAA;QACA,KAAAc,QAAA,CAAA+E,OAAA;QACA;MACA;MAEA,KAAA3B,QAAA,kBAAApJ,aAAA,CAAAkF,MAAA;QACAmE,iBAAA;QACAC,gBAAA;QACArE,IAAA;QACAuM,wBAAA;MACA,GAAA7K,IAAA;QACA;QACA,MAAAyK,OAAA,QAAApR,aAAA,CAAAqR,GAAA,CAAA7L,IAAA,IAAAA,IAAA,CAAAzB,EAAA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAqN,OAAA,CAAAlG,OAAA,CAAAnH,EAAA;UACA,MAAAkE,KAAA,QAAAtJ,IAAA,CAAAuJ,SAAA,CAAA1C,IAAA,IAAAA,IAAA,CAAAzB,EAAA,KAAAA,EAAA;UACA,IAAAkE,KAAA;YACA,KAAAtJ,IAAA,CAAAwJ,MAAA,CAAAF,KAAA;UACA;QACA;QAEA,KAAArJ,KAAA,QAAAD,IAAA,CAAAuG,MAAA;QACA,KAAAlF,aAAA;QACA,KAAAgG,QAAA,CAAAC,OAAA,SAAAmL,OAAA,CAAAlM,MAAA;;QAEA;QACA,KAAAqB,KAAA,CAAAkL,SAAA,CAAAC,cAAA;MACA,GAAArE,KAAA;QACA,KAAArH,QAAA,CAAAjG,IAAA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}