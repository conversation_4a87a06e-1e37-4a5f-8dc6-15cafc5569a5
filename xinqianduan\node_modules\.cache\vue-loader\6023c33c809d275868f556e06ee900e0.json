{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\changePwd.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\changePwd.vue", "mtime": 1748540171913}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changePwd.vue"], "names": [], "mappings": ";AAmHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "changePwd.vue", "sourceRoot": "src/views/pages", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n    <div class=\"page-container\">\r\n      <!-- 页面标题 -->\r\n      <div class=\"page-header\">\r\n        <div class=\"header-left\">\r\n          <h2 class=\"page-title\">\r\n            <i class=\"el-icon-lock\"></i>\r\n            修改密码\r\n          </h2>\r\n          <div class=\"page-subtitle\">为了您的账户安全，请定期更换密码</div>\r\n        </div>\r\n        <el-button \r\n          type=\"text\" \r\n          icon=\"el-icon-back\"\r\n          @click=\"goBack\"\r\n          class=\"back-btn\"\r\n        >\r\n          返回\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 修改密码表单 -->\r\n      <div class=\"form-section\">\r\n        <div class=\"form-card\">\r\n          <div class=\"security-tips\">\r\n            <div class=\"tips-header\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>密码安全提示</span>\r\n            </div>\r\n            <ul class=\"tips-list\">\r\n              <li>密码长度至少8位，包含字母、数字</li>\r\n              <li>不要使用过于简单的密码</li>\r\n              <li>建议定期更换密码</li>\r\n              <li>不要在多个平台使用相同密码</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <el-form \r\n            :model=\"passwordForm\" \r\n            :rules=\"rules\" \r\n            ref=\"passwordForm\" \r\n            label-width=\"120px\"\r\n            class=\"password-form\"\r\n          >\r\n            <el-form-item label=\"当前密码\" prop=\"oldPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.oldPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请输入当前密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.newPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请输入新密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"确认新密码\" prop=\"confirmPassword\">\r\n              <el-input \r\n                v-model=\"passwordForm.confirmPassword\" \r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password\r\n                autocomplete=\"off\"\r\n              ></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 密码强度指示器 -->\r\n            <div class=\"password-strength\" v-if=\"passwordForm.newPassword\">\r\n              <div class=\"strength-label\">密码强度：</div>\r\n              <div class=\"strength-bar\">\r\n                <div \r\n                  class=\"strength-fill\" \r\n                  :class=\"passwordStrengthClass\"\r\n                  :style=\"{ width: passwordStrengthWidth }\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"strength-text\" :class=\"passwordStrengthClass\">\r\n                {{ passwordStrengthText }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作按钮 -->\r\n            <div class=\"action-buttons\">\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"changePassword\"\r\n                :loading=\"loading\"\r\n                size=\"medium\"\r\n              >\r\n                确认修改\r\n              </el-button>\r\n              <el-button \r\n                @click=\"resetForm\"\r\n                size=\"medium\"\r\n              >\r\n                重置\r\n              </el-button>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ChangePwd\",\r\n  data() {\r\n    // 确认密码验证\r\n    const validateConfirmPassword = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请再次输入新密码'));\r\n      } else if (value !== this.passwordForm.newPassword) {\r\n        callback(new Error('两次输入密码不一致'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      loading: false,\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      rules: {\r\n        oldPassword: [\r\n          { required: true, message: '请输入当前密码', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n          { min: 8, message: '密码长度至少8位', trigger: 'blur' },\r\n          { \r\n            pattern: /^(?=.*[a-zA-Z])(?=.*\\d).+$/, \r\n            message: '密码必须包含字母和数字', \r\n            trigger: 'blur' \r\n          }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: '请确认新密码', trigger: 'blur' },\r\n          { validator: validateConfirmPassword, trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    // 密码强度计算\r\n    passwordStrength() {\r\n      const password = this.passwordForm.newPassword;\r\n      if (!password) return 0;\r\n      \r\n      let strength = 0;\r\n      \r\n      // 长度检查\r\n      if (password.length >= 8) strength += 1;\r\n      if (password.length >= 12) strength += 1;\r\n      \r\n      // 字符类型检查\r\n      if (/[a-z]/.test(password)) strength += 1;\r\n      if (/[A-Z]/.test(password)) strength += 1;\r\n      if (/\\d/.test(password)) strength += 1;\r\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) strength += 1;\r\n      \r\n      return Math.min(strength, 4);\r\n    },\r\n    passwordStrengthWidth() {\r\n      return (this.passwordStrength / 4) * 100 + '%';\r\n    },\r\n    passwordStrengthClass() {\r\n      const classes = ['weak', 'fair', 'good', 'strong'];\r\n      return classes[Math.max(0, this.passwordStrength - 1)] || 'weak';\r\n    },\r\n    passwordStrengthText() {\r\n      const texts = ['弱', '一般', '良好', '强'];\r\n      return texts[Math.max(0, this.passwordStrength - 1)] || '弱';\r\n    }\r\n  },\r\n  methods: {\r\n    changePassword() {\r\n      this.$refs.passwordForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          \r\n          // 模拟密码修改过程\r\n          setTimeout(() => {\r\n            this.loading = false;\r\n            this.$message.success('密码修改成功！');\r\n            this.resetForm();\r\n            \r\n            // 可以选择跳转回个人信息页面或者首页\r\n            setTimeout(() => {\r\n              this.$router.push('/profile');\r\n            }, 1500);\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    resetForm() {\r\n      this.$refs.passwordForm.resetFields();\r\n      this.passwordForm = {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面布局样式 */\r\n.page-wrapper {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 16px;\r\n}\r\n\r\n.page-container {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  margin: 0 0 8px 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.page-title i {\r\n  color: #1890ff;\r\n  font-size: 22px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  margin: 0;\r\n}\r\n\r\n.back-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.back-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.form-card {\r\n  padding: 32px;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f6ffed;\r\n  border: 1px solid #b7eb8f;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.tips-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: 500;\r\n  color: #52c41a;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.tips-list {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #52c41a;\r\n}\r\n\r\n.tips-list li {\r\n  margin-bottom: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表单样式 */\r\n.password-form {\r\n  max-width: 500px;\r\n}\r\n\r\n/* 密码强度指示器 */\r\n.password-strength {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 24px;\r\n  padding: 0 0 0 120px;\r\n}\r\n\r\n.strength-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  white-space: nowrap;\r\n}\r\n\r\n.strength-bar {\r\n  flex: 1;\r\n  height: 6px;\r\n  background: #f0f0f0;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n}\r\n\r\n.strength-fill {\r\n  height: 100%;\r\n  transition: all 0.3s;\r\n  border-radius: 3px;\r\n}\r\n\r\n.strength-fill.weak {\r\n  background: #ff4d4f;\r\n}\r\n\r\n.strength-fill.fair {\r\n  background: #faad14;\r\n}\r\n\r\n.strength-fill.good {\r\n  background: #1890ff;\r\n}\r\n\r\n.strength-fill.strong {\r\n  background: #52c41a;\r\n}\r\n\r\n.strength-text {\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.strength-text.weak {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.strength-text.fair {\r\n  color: #faad14;\r\n}\r\n\r\n.strength-text.good {\r\n  color: #1890ff;\r\n}\r\n\r\n.strength-text.strong {\r\n  color: #52c41a;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  margin-top: 32px;\r\n  padding-top: 24px;\r\n  border-top: 1px solid #f0f0f0;\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 表单样式优化 */\r\n.password-form ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n}\r\n\r\n.password-form ::v-deep .el-input__inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.password-form ::v-deep .el-input__inner:focus {\r\n  border-color: #1890ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .form-card {\r\n    padding: 24px 16px;\r\n  }\r\n  \r\n  .password-strength {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    padding: 0;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .strength-bar {\r\n    width: 100%;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>\r\n"]}]}