{"map": "{\"version\":3,\"sources\":[\"js/chunk-67559f37.321a254a.js\"],\"names\":[\"window\",\"push\",\"02be\",\"module\",\"__webpack_exports__\",\"__webpack_require__\",\"r\",\"render\",\"_vm\",\"this\",\"_c\",\"_self\",\"staticClass\",\"_m\",\"attrs\",\"type\",\"icon\",\"on\",\"click\",\"$event\",\"editData\",\"_v\",\"refulsh\",\"shadow\",\"placeholder\",\"clearable\",\"nativeOn\",\"keyup\",\"indexOf\",\"_k\",\"keyCode\",\"key\",\"searchData\",\"apply\",\"arguments\",\"model\",\"value\",\"search\",\"keyword\",\"callback\",\"$$v\",\"$set\",\"expression\",\"slot\",\"label\",\"status\",\"clearSearch\",\"viewMode\",\"size\",\"data\",\"treeData\",\"props\",\"treeProps\",\"default-expand-all\",\"node-key\",\"scopedSlots\",\"_u\",\"fn\",\"node\",\"class\",\"getNodeIcon\",\"_s\",\"plain\",\"id\",\"addChild\",\"delData\",\"_e\",\"directives\",\"name\",\"rawName\",\"loading\",\"tableData\",\"stripe\",\"row-key\",\"tree-props\",\"children\",\"hasChildren\",\"prop\",\"min-width\",\"scope\",\"style\",\"paddingLeft\",\"row\",\"level\",\"staticStyle\",\"margin-right\",\"width\",\"align\",\"show-overflow-tooltip\",\"getTypeColor\",\"getTypeLabel\",\"active-value\",\"inactive-value\",\"change\",\"changeStatus\",\"fixed\",\"stopPropagation\",\"title\",\"dialogTitle\",\"visible\",\"dialogFormVisible\",\"close-on-click-modal\",\"update:visible\",\"ref\",\"ruleForm\",\"rules\",\"label-width\",\"gutter\",\"span\",\"code\",\"options\",\"parentOptions\",\"cascaderProps\",\"parent_id\",\"min\",\"max\",\"sort\",\"active-text\",\"inactive-text\",\"rows\",\"description\",\"path\",\"saveData\",\"staticRenderFns\",\"quanxianvue_type_script_lang_js\",\"[object Object]\",\"originalData\",\"required\",\"message\",\"trigger\",\"checkStrictly\",\"computed\",\"flattenTreeForTable\",\"getData\",\"methods\",\"setTimeout\",\"mockData\",\"create_time\",\"JSON\",\"parse\",\"stringify\",\"updateParentOptions\",\"tree\",\"result\",\"forEach\",\"flatNode\",\"length\",\"buildCascaderOptions\",\"map\",\"$router\",\"go\",\"permission\",\"findPermissionById\",\"parentData\",\"found\",\"$confirm\",\"confirmButtonText\",\"cancelButtonText\",\"then\",\"$message\",\"success\",\"catch\",\"info\",\"$refs\",\"validate\",\"valid\",\"iconMap\",\"menu\",\"action\",\"colorMap\",\"labelMap\",\"yuangong_quanxianvue_type_script_lang_js\",\"componentNormalizer\",\"component\",\"Object\",\"2a9c\",\"exports\",\"4978\"],\"mappings\":\"CAACA,OAAO,gBAAkBA,OAAO,iBAAmB,IAAIC,KAAK,CAAC,CAAC,kBAAkB,CAE3EC,OACA,SAAUC,EAAQC,EAAqBC,GAE7C,aAEAA,EAAoBC,EAAEF,GAGtB,IAAIG,EAAS,WACX,IAAIC,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,wBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,kBACZ,CAACJ,EAAIK,GAAG,GAAIH,EAAG,MAAO,CACvBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBE,YAAa,UACbE,MAAO,CACLC,KAAQ,UACRC,KAAQ,gBAEVC,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIY,SAAS,MAGvB,CAACZ,EAAIa,GAAG,YAAaX,EAAG,YAAa,CACtCE,YAAa,cACbE,MAAO,CACLE,KAAQ,mBAEVC,GAAI,CACFC,MAASV,EAAIc,UAEd,CAACd,EAAIa,GAAG,WAAY,OAAQX,EAAG,MAAO,CACvCE,YAAa,kBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,cACbE,MAAO,CACLS,OAAU,UAEX,CAACb,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACF,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,UAAWX,EAAG,WAAY,CACnCE,YAAa,eACbE,MAAO,CACLU,YAAe,aACfC,UAAa,IAEfC,SAAU,CACRC,MAAS,SAAUR,GACjB,OAAKA,EAAOJ,KAAKa,QAAQ,QAAUpB,EAAIqB,GAAGV,EAAOW,QAAS,QAAS,GAAIX,EAAOY,IAAK,SAAiB,KAC7FvB,EAAIwB,WAAWC,MAAM,KAAMC,aAGtCC,MAAO,CACLC,MAAO5B,EAAI6B,OAAOC,QAClBC,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,UAAWG,IAElCE,WAAY,mBAEb,CAAChC,EAAG,IAAK,CACVE,YAAa,gCACbE,MAAO,CACL6B,KAAQ,UAEVA,KAAM,cACD,GAAIjC,EAAG,MAAO,CACnBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCE,YAAa,gBACbE,MAAO,CACLU,YAAe,UACfC,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAI6B,OAAOtB,KAClBwB,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,OAAQG,IAE/BE,WAAY,gBAEb,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,UAET1B,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,YAET1B,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,WAER,IAAK,GAAI1B,EAAG,MAAO,CACtBE,YAAa,eACZ,CAACF,EAAG,QAAS,CACdE,YAAa,gBACZ,CAACJ,EAAIa,GAAG,QAASX,EAAG,YAAa,CAClCE,YAAa,gBACbE,MAAO,CACLU,YAAe,QACfC,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAI6B,OAAOQ,OAClBN,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAI6B,OAAQ,SAAUG,IAEjCE,WAAY,kBAEb,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,KACTR,MAAS,KAET1B,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,KACTR,MAAS,MAER,IAAK,KAAM1B,EAAG,MAAO,CACxBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRC,KAAQ,kBAEVC,GAAI,CACFC,MAASV,EAAIwB,aAEd,CAACxB,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCI,MAAO,CACLE,KAAQ,wBAEVC,GAAI,CACFC,MAASV,EAAIsC,cAEd,CAACtC,EAAIa,GAAG,WAAY,QAAS,GAAIX,EAAG,MAAO,CAC5CE,YAAa,gBACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,YACbE,MAAO,CACLS,OAAU,UAEX,CAACb,EAAG,MAAO,CACZE,YAAa,eACZ,CAACF,EAAG,MAAO,CACZE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,iBACXJ,EAAIa,GAAG,cAAeX,EAAG,MAAO,CAClCE,YAAa,cACZ,CAACF,EAAG,kBAAmB,CAACA,EAAG,YAAa,CACzCI,MAAO,CACLC,KAAyB,SAAjBP,EAAIuC,SAAsB,UAAY,GAC9C/B,KAAQ,iBACRgC,KAAQ,SAEV/B,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAIuC,SAAW,UAGlB,CAACvC,EAAIa,GAAG,YAAaX,EAAG,YAAa,CACtCI,MAAO,CACLC,KAAyB,UAAjBP,EAAIuC,SAAuB,UAAY,GAC/C/B,KAAQ,eACRgC,KAAQ,SAEV/B,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAIuC,SAAW,WAGlB,CAACvC,EAAIa,GAAG,aAAc,IAAK,KAAuB,SAAjBb,EAAIuC,SAAsBrC,EAAG,MAAO,CACtEE,YAAa,aACZ,CAACF,EAAG,UAAW,CAChBE,YAAa,kBACbE,MAAO,CACLmC,KAAQzC,EAAI0C,SACZC,MAAS3C,EAAI4C,UACbC,sBAAsB,EACtBC,WAAY,MAEdC,YAAa/C,EAAIgD,GAAG,CAAC,CACnBzB,IAAK,UACL0B,GAAI,UAAUC,KACZA,EAAIT,KACJA,IAEA,OAAOvC,EAAG,OAAQ,CAChBE,YAAa,aACZ,CAACF,EAAG,MAAO,CACZE,YAAa,gBACZ,CAACF,EAAG,MAAO,CACZE,YAAa,aACZ,CAACF,EAAG,IAAK,CACViD,MAAOnD,EAAIoD,YAAYX,EAAKlC,QAC1BL,EAAG,OAAQ,CACbE,YAAa,cACZ,CAACJ,EAAIa,GAAGb,EAAIqD,GAAGZ,EAAKL,UAAWlC,EAAG,SAAU,CAC7CE,YAAa,cACbE,MAAO,CACLC,KAAwB,IAAhBkC,EAAKJ,OAAe,UAAY,SACxCG,KAAQ,SAET,CAACxC,EAAIa,GAAG,IAAMb,EAAIqD,GAAmB,IAAhBZ,EAAKJ,OAAe,KAAO,MAAQ,QAAS,GAAInC,EAAG,MAAO,CAChFE,YAAa,gBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRiC,KAAQ,OACRhC,KAAQ,eACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIY,SAAS6B,EAAKc,OAG5B,CAACvD,EAAIa,GAAG,UAAWX,EAAG,YAAa,CACpCI,MAAO,CACLC,KAAQ,UACRiC,KAAQ,OACRhC,KAAQ,eACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIwD,SAASf,MAGvB,CAACzC,EAAIa,GAAG,aAAcX,EAAG,YAAa,CACvCI,MAAO,CACLC,KAAQ,SACRiC,KAAQ,OACRhC,KAAQ,iBACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIyD,QAAQhB,EAAKc,OAG3B,CAACvD,EAAIa,GAAG,WAAY,UAEvB,MAAM,EAAO,cACd,GAAKb,EAAI0D,KAAuB,UAAjB1D,EAAIuC,SAAuBrC,EAAG,MAAO,CACvDE,YAAa,cACZ,CAACF,EAAG,WAAY,CACjByD,WAAY,CAAC,CACXC,KAAM,UACNC,QAAS,YACTjC,MAAO5B,EAAI8D,QACX5B,WAAY,YAEd9B,YAAa,mBACbE,MAAO,CACLmC,KAAQzC,EAAI+D,UACZC,OAAU,GACVC,UAAW,KACXC,aAAc,CACZC,SAAU,WACVC,YAAa,eAEfvB,sBAAsB,IAEvB,CAAC3C,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,QACRjC,MAAS,OACTkC,YAAa,OAEfvB,YAAa/C,EAAIgD,GAAG,CAAC,CACnBzB,IAAK,UACL0B,GAAI,SAAUsB,GACZ,MAAO,CAACrE,EAAG,MAAO,CAChBE,YAAa,uBACboE,MAAO,CACLC,YAAsC,IAAxBF,EAAMG,IAAIC,OAAS,GAAU,OAE5C,CAACzE,EAAG,IAAK,CACViD,MAAOnD,EAAIoD,YAAYmB,EAAMG,IAAInE,MACjCqE,YAAa,CACXC,eAAgB,SAEhB3E,EAAG,OAAQ,CACbE,YAAa,mBACZ,CAACJ,EAAIa,GAAGb,EAAIqD,GAAGkB,EAAMG,IAAItC,gBAE5B,MAAM,EAAO,aACflC,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,OACRjC,MAAS,OACT0C,MAAS,MACTC,MAAS,SACTC,wBAAyB,MAEzB9E,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,OACRjC,MAAS,OACT0C,MAAS,MACTC,MAAS,UAEXhC,YAAa/C,EAAIgD,GAAG,CAAC,CACnBzB,IAAK,UACL0B,GAAI,SAAUsB,GACZ,MAAO,CAACrE,EAAG,SAAU,CACnBI,MAAO,CACLC,KAAQP,EAAIiF,aAAaV,EAAMG,IAAInE,MACnCiC,KAAQ,UAET,CAACxC,EAAIa,GAAG,IAAMb,EAAIqD,GAAGrD,EAAIkF,aAAaX,EAAMG,IAAInE,OAAS,WAE5D,MAAM,EAAO,aACfL,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,SACRjC,MAAS,KACT0C,MAAS,MACTC,MAAS,UAEXhC,YAAa/C,EAAIgD,GAAG,CAAC,CACnBzB,IAAK,UACL0B,GAAI,SAAUsB,GACZ,MAAO,CAACrE,EAAG,YAAa,CACtBI,MAAO,CACL6E,eAAgB,EAChBC,iBAAkB,GAEpB3E,GAAI,CACF4E,OAAU,SAAU1E,GAClB,OAAOX,EAAIsF,aAAaf,EAAMG,OAGlC/C,MAAO,CACLC,MAAO2C,EAAMG,IAAIrC,OACjBN,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKsC,EAAMG,IAAK,SAAU1C,IAEhCE,WAAY,0BAIhB,MAAM,EAAO,cACfhC,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,OACRjC,MAAS,KACT0C,MAAS,KACTC,MAAS,YAET7E,EAAG,kBAAmB,CACxBI,MAAO,CACL+D,KAAQ,cACRjC,MAAS,OACT0C,MAAS,MACTC,MAAS,YAET7E,EAAG,kBAAmB,CACxBI,MAAO,CACLiF,MAAS,QACTnD,MAAS,KACT0C,MAAS,MACTC,MAAS,UAEXhC,YAAa/C,EAAIgD,GAAG,CAAC,CACnBzB,IAAK,UACL0B,GAAI,SAAUsB,GACZ,MAAO,CAACrE,EAAG,MAAO,CAChBE,YAAa,kBACZ,CAACF,EAAG,YAAa,CAClBI,MAAO,CACLC,KAAQ,UACRiC,KAAQ,OACRhC,KAAQ,eACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GAEjB,OADAA,EAAO6E,kBACAxF,EAAIY,SAAS2D,EAAMG,IAAInB,OAGjC,CAACvD,EAAIa,GAAG,UAA8B,SAAnB0D,EAAMG,IAAInE,KAAkBL,EAAG,YAAa,CAChEI,MAAO,CACLC,KAAQ,UACRiC,KAAQ,OACRhC,KAAQ,eACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GAEjB,OADAA,EAAO6E,kBACAxF,EAAIwD,SAASe,EAAMG,QAG7B,CAAC1E,EAAIa,GAAG,aAAeb,EAAI0D,KAAMxD,EAAG,YAAa,CAClDI,MAAO,CACLC,KAAQ,SACRiC,KAAQ,OACRhC,KAAQ,iBACR8C,MAAS,IAEX7C,GAAI,CACFC,MAAS,SAAUC,GAEjB,OADAA,EAAO6E,kBACAxF,EAAIyD,QAAQc,EAAMG,IAAInB,OAGhC,CAACvD,EAAIa,GAAG,WAAY,OAEvB,MAAM,EAAO,eACd,IAAK,GAAKb,EAAI0D,QAAS,GAAIxD,EAAG,YAAa,CAC9CI,MAAO,CACLmF,MAASzF,EAAI0F,YACbC,QAAW3F,EAAI4F,kBACfC,wBAAwB,EACxBf,MAAS,OAEXrE,GAAI,CACFqF,iBAAkB,SAAUnF,GAC1BX,EAAI4F,kBAAoBjF,KAG3B,CAACT,EAAG,UAAW,CAChB6F,IAAK,WACLzF,MAAO,CACLqB,MAAS3B,EAAIgG,SACbC,MAASjG,EAAIiG,MACbC,cAAe,UAEhB,CAAChG,EAAG,SAAU,CACfI,MAAO,CACL6F,OAAU,KAEX,CAACjG,EAAG,SAAU,CACfI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,OACTiC,KAAQ,UAET,CAACnE,EAAG,WAAY,CACjBI,MAAO,CACLU,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAAS5D,MACpBL,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,QAAShE,IAElCE,WAAY,qBAEX,IAAK,GAAIhC,EAAG,SAAU,CACzBI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,OACTiC,KAAQ,SAET,CAACnE,EAAG,WAAY,CACjBI,MAAO,CACLU,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAASK,KACpBtE,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,OAAQhE,IAEjCE,WAAY,oBAEX,IAAK,IAAK,GAAIhC,EAAG,SAAU,CAC9BI,MAAO,CACL6F,OAAU,KAEX,CAACjG,EAAG,SAAU,CACfI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,OACTiC,KAAQ,SAET,CAACnE,EAAG,YAAa,CAClB0E,YAAa,CACXE,MAAS,QAEXxE,MAAO,CACLU,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAASzF,KACpBwB,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,OAAQhE,IAEjCE,WAAY,kBAEb,CAAChC,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,UAET1B,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,YAET1B,EAAG,YAAa,CAClBI,MAAO,CACL8B,MAAS,OACTR,MAAS,WAER,IAAK,IAAK,GAAI1B,EAAG,SAAU,CAC9BI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,SAEV,CAAClC,EAAG,cAAe,CACpB0E,YAAa,CACXE,MAAS,QAEXxE,MAAO,CACLgG,QAAWtG,EAAIuG,cACf5D,MAAS3C,EAAIwG,cACbxF,YAAe,UACfC,UAAa,IAEfU,MAAO,CACLC,MAAO5B,EAAIgG,SAASS,UACpB1E,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,YAAahE,IAEtCE,WAAY,yBAEX,IAAK,IAAK,GAAIhC,EAAG,SAAU,CAC9BI,MAAO,CACL6F,OAAU,KAEX,CAACjG,EAAG,SAAU,CACfI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,OAEV,CAAClC,EAAG,kBAAmB,CACxB0E,YAAa,CACXE,MAAS,QAEXxE,MAAO,CACLoG,IAAO,EACPC,IAAO,KAEThF,MAAO,CACLC,MAAO5B,EAAIgG,SAASY,KACpB7E,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,OAAQhE,IAEjCE,WAAY,oBAEX,IAAK,GAAIhC,EAAG,SAAU,CACzBI,MAAO,CACL8F,KAAQ,KAET,CAAClG,EAAG,eAAgB,CACrBI,MAAO,CACL8B,MAAS,OAEV,CAAClC,EAAG,YAAa,CAClBI,MAAO,CACL6E,eAAgB,EAChBC,iBAAkB,EAClByB,cAAe,KACfC,gBAAiB,MAEnBnF,MAAO,CACLC,MAAO5B,EAAIgG,SAAS3D,OACpBN,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,SAAUhE,IAEnCE,WAAY,sBAEX,IAAK,IAAK,GAAIhC,EAAG,eAAgB,CACpCI,MAAO,CACL8B,MAAS,SAEV,CAAClC,EAAG,WAAY,CACjBI,MAAO,CACLC,KAAQ,WACRwG,KAAQ,EACR/F,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAASgB,YACpBjF,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,cAAehE,IAExCE,WAAY,2BAEX,GAA0B,SAAtBlC,EAAIgG,SAASzF,KAAkBL,EAAG,eAAgB,CACzDI,MAAO,CACL8B,MAAS,SAEV,CAAClC,EAAG,WAAY,CACjBI,MAAO,CACLU,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAASiB,KACpBlF,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,OAAQhE,IAEjCE,WAAY,oBAEX,GAAKlC,EAAI0D,KAA4B,SAAtB1D,EAAIgG,SAASzF,KAAkBL,EAAG,eAAgB,CACpEI,MAAO,CACL8B,MAAS,OAEV,CAAClC,EAAG,WAAY,CACjBI,MAAO,CACLU,YAAe,WAEjBW,MAAO,CACLC,MAAO5B,EAAIgG,SAASxF,KACpBuB,SAAU,SAAUC,GAClBhC,EAAIiC,KAAKjC,EAAIgG,SAAU,OAAQhE,IAEjCE,WAAY,kBAEb,CAAChC,EAAG,WAAY,CACjBiC,KAAM,WACL,CAACjC,EAAG,IAAK,CACViD,MAAOnD,EAAIgG,SAASxF,MAAQ,oBACvB,IAAK,GAAKR,EAAI0D,MAAO,GAAIxD,EAAG,MAAO,CACxCE,YAAa,gBACbE,MAAO,CACL6B,KAAQ,UAEVA,KAAM,UACL,CAACjC,EAAG,YAAa,CAClBO,GAAI,CACFC,MAAS,SAAUC,GACjBX,EAAI4F,mBAAoB,KAG3B,CAAC5F,EAAIa,GAAG,SAAUX,EAAG,YAAa,CACnCI,MAAO,CACLC,KAAQ,WAEVE,GAAI,CACFC,MAAS,SAAUC,GACjB,OAAOX,EAAIkH,cAGd,CAAClH,EAAIa,GAAG,UAAW,IAAK,IAAK,IAE9BsG,EAAkB,CAAC,WACrB,IAAInH,EAAMC,KACRC,EAAKF,EAAIG,MAAMD,GACjB,OAAOA,EAAG,MAAO,CACfE,YAAa,iBACZ,CAACF,EAAG,KAAM,CACXE,YAAa,cACZ,CAACF,EAAG,IAAK,CACVE,YAAa,gBACXJ,EAAIa,GAAG,YAAaX,EAAG,IAAK,CAC9BE,YAAa,iBACZ,CAACJ,EAAIa,GAAG,uBAUoBuG,GAJbvH,EAAoB,QAI2B,CACjE+D,KAAM,uBACNyD,OACE,MAAO,CACL9E,SAAU,OAEVuB,SAAS,EACTjC,OAAQ,CACNC,QAAS,GACTvB,KAAM,GACN8B,OAAQ,IAEVK,SAAU,GACV4E,aAAc,GAEd1B,mBAAmB,EACnBF,YAAa,OACba,cAAe,GACfP,SAAU,CACRzC,GAAI,KACJnB,MAAO,GACPiE,KAAM,GACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,GACbC,KAAM,GACNzG,KAAM,IAERyF,MAAO,CACL7D,MAAO,CAAC,CACNmF,UAAU,EACVC,QAAS,UACTC,QAAS,SAEXpB,KAAM,CAAC,CACLkB,UAAU,EACVC,QAAS,UACTC,QAAS,SAEXlH,KAAM,CAAC,CACLgH,UAAU,EACVC,QAAS,UACTC,QAAS,YAGb7E,UAAW,CACTuB,SAAU,WACV/B,MAAO,SAEToE,cAAe,CACb5E,MAAO,KACPQ,MAAO,QACP+B,SAAU,WACVuD,eAAe,KAIrBC,SAAU,CAERN,YACE,OAAOpH,KAAK2H,oBAAoB3H,KAAKyC,YAGzC2E,UACEpH,KAAK4H,WAEPC,QAAS,CAEPT,UACEpH,KAAK6D,SAAU,EAGfiE,WAAW,KACT9H,KAAK6D,SAAU,EACf,MAAMkE,EAAW,CAAC,CAChBzE,GAAI,EACJnB,MAAO,OACPiE,KAAM,SACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,UACNzG,KAAM,kBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,GACJnB,MAAO,OACPiE,KAAM,cACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,QACNzG,KAAM,eACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,mBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,kBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,mBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,qBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,yBAEd,CACD1E,GAAI,GACJnB,MAAO,OACPiE,KAAM,kBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,UACNzG,KAAM,mBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,sBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,yBAEd,CACD1E,GAAI,GACJnB,MAAO,OACPiE,KAAM,oBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,YACNzG,KAAM,cACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,wBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,2BACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,2BAGhB,CACD1E,GAAI,EACJnB,MAAO,OACPiE,KAAM,WACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,YACNzG,KAAM,mBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,GACJnB,MAAO,OACPiE,KAAM,iBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,WACNzG,KAAM,mBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,sBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,qBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,sBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,wBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,wBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,yBAEd,CACD1E,GAAI,GACJnB,MAAO,OACPiE,KAAM,oBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,YACNzG,KAAM,qBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,wBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,2BACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,2BAGhB,CACD1E,GAAI,EACJnB,MAAO,OACPiE,KAAM,WACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,YACNzG,KAAM,wBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,GACJnB,MAAO,OACPiE,KAAM,oBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,UACNzG,KAAM,mBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,wBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,2BACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,0BACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,yBAEd,CACD1E,GAAI,GACJnB,MAAO,QACPiE,KAAM,kBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,UACbC,KAAM,UACNzG,KAAM,kBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,QACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,UACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,QACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,QACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,QACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,QACbiB,YAAa,yBAEd,CACD1E,GAAI,GACJnB,MAAO,OACPiE,KAAM,kBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,WACNzG,KAAM,qBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,OACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,sBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,OACbiB,YAAa,2BAGhB,CACD1E,GAAI,EACJnB,MAAO,OACPiE,KAAM,UACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,WACNzG,KAAM,eACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,GACJnB,MAAO,OACPiE,KAAM,kBACN9F,KAAM,OACNkG,UAAW,EACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbC,KAAM,SACNzG,KAAM,gBACNyH,YAAa,sBACb9D,SAAU,CAAC,CACTZ,GAAI,IACJnB,MAAO,SACPiE,KAAM,uBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,OACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,uBACZ,CACD1E,GAAI,IACJnB,MAAO,SACPiE,KAAM,yBACN9F,KAAM,SACNkG,UAAW,GACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,SACbiB,YAAa,4BAInBhI,KAAKyC,SAAWsF,EAChB/H,KAAKqH,aAAeY,KAAKC,MAAMD,KAAKE,UAAUJ,IAC9C/H,KAAKoI,uBACJ,MAGLhB,oBAAoBiB,EAAM3D,EAAQ,EAAG4D,EAAS,IAgB5C,OAfAD,EAAKE,QAAQtF,IACX,MAAMuF,EAAW,IACZvF,EACHyB,MAAOA,EACPP,YAAalB,EAAKiB,UAAYjB,EAAKiB,SAASuE,OAAS,UAGhDD,EAAStE,SAChBoE,EAAO9I,KAAKgJ,GAGRvF,EAAKiB,UAAYjB,EAAKiB,SAASuE,OAAS,GAC1CzI,KAAK2H,oBAAoB1E,EAAKiB,SAAUQ,EAAQ,EAAG4D,KAGhDA,GAGTlB,sBACEpH,KAAKsG,cAAgBtG,KAAK0I,qBAAqB1I,KAAKyC,WAGtD2E,qBAAqBiB,GACnB,OAAOA,EAAKM,IAAI1F,IAAQ,CACtBK,GAAIL,EAAKK,GACTnB,MAAOc,EAAKd,MACZ+B,SAAUjB,EAAKiB,SAAWlE,KAAK0I,qBAAqBzF,EAAKiB,UAAY,OAIzEkD,aACEpH,KAAK4H,WAGPR,cACEpH,KAAK4B,OAAS,CACZC,QAAS,GACTvB,KAAM,GACN8B,OAAQ,IAEVpC,KAAKuB,cAGP6F,UACEpH,KAAK4I,QAAQC,GAAG,IAGlBzB,SAAS9D,GACP,GAAW,IAAPA,EACFtD,KAAKyF,YAAc,OACnBzF,KAAK+F,SAAW,CACdzC,GAAI,KACJnB,MAAO,GACPiE,KAAM,GACN9F,KAAM,OACNkG,UAAW,KACXG,KAAM,EACNvE,OAAQ,EACR2E,YAAa,GACbC,KAAM,GACNzG,KAAM,QAEH,CACLP,KAAKyF,YAAc,OACnB,MAAMqD,EAAa9I,KAAK+I,mBAAmBzF,GACvCwF,IACF9I,KAAK+F,SAAW,IACX+C,IAIT9I,KAAK2F,mBAAoB,GAG3ByB,SAAS4B,GACPhJ,KAAKyF,YAAc,QACnBzF,KAAK+F,SAAW,CACdzC,GAAI,KACJnB,MAAO,GACPiE,KAAM,GACN9F,KAAM,SACNkG,UAAW,CAACwC,EAAW1F,IACvBqD,KAAM,EACNvE,OAAQ,EACR2E,YAAa,GACbC,KAAM,GACNzG,KAAM,IAERP,KAAK2F,mBAAoB,GAG3ByB,mBAAmB9D,EAAI+E,EAAOrI,KAAKqH,cACjC,IAAK,IAAIpE,KAAQoF,EAAM,CACrB,GAAIpF,EAAKK,KAAOA,EACd,OAAOL,EAET,GAAIA,EAAKiB,SAAU,CACjB,MAAM+E,EAAQjJ,KAAK+I,mBAAmBzF,EAAIL,EAAKiB,UAC/C,GAAI+E,EAAO,OAAOA,GAGtB,OAAO,MAGT7B,QAAQ9D,GACNtD,KAAKkJ,SAAS,sBAAuB,KAAM,CACzCC,kBAAmB,KACnBC,iBAAkB,KAClB9I,KAAM,YACL+I,KAAK,KAENrJ,KAAKsJ,SAASC,QAAQ,SACtBvJ,KAAK4H,YACJ4B,MAAM,KACPxJ,KAAKsJ,SAASG,KAAK,YAIvBrC,WACEpH,KAAK0J,MAAM,YAAYC,SAASC,IAC1BA,IAEF5J,KAAKsJ,SAASC,QAAQvJ,KAAK+F,SAASzC,GAAK,QAAU,SACnDtD,KAAK2F,mBAAoB,EACzB3F,KAAK4H,cAKXR,aAAa3C,GACXzE,KAAKsJ,SAASC,QAAQ,SAAuB,IAAf9E,EAAIrC,OAAe,KAAO,QAG1DgF,YAAY9G,GACV,MAAMuJ,EAAU,CACdC,KAAM,eACNC,OAAQ,kBACRvH,KAAM,oBAER,OAAOqH,EAAQvJ,IAAS,gBAG1B8G,aAAa9G,GACX,MAAM0J,EAAW,CACfF,KAAM,UACNC,OAAQ,UACRvH,KAAM,WAER,OAAOwH,EAAS1J,IAAS,WAG3B8G,aAAa9G,GACX,MAAM2J,EAAW,CACfH,KAAM,OACNC,OAAQ,OACRvH,KAAM,QAER,OAAOyH,EAAS3J,IAAS,WAKG4J,EAA2C,EAKzEC,GAHuEvK,EAAoB,QAGrEA,EAAoB,SAW1CwK,EAAYC,OAAOF,EAAoB,KAA3BE,CACdH,EACApK,EACAoH,GACA,EACA,KACA,WACA,MAI0CvH,EAAoB,WAAcyK,EAAiB,SAIzFE,OACA,SAAU5K,EAAQ6K,EAAS3K,KAM3B4K,KACA,SAAU9K,EAAQC,EAAqBC,GAE7C,aACidA,EAAoB\"}", "code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-67559f37\"],{\"02be\":function(e,t,i){\"use strict\";i.r(t);var a=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"permission-container\"},[t(\"div\",{staticClass:\"page-header\"},[t(\"div\",{staticClass:\"header-content\"},[e._m(0),t(\"div\",{staticClass:\"header-actions\"},[t(\"el-button\",{staticClass:\"add-btn\",attrs:{type:\"primary\",icon:\"el-icon-plus\"},on:{click:function(t){return e.editData(0)}}},[e._v(\" 新增权限 \")]),t(\"el-button\",{staticClass:\"refresh-btn\",attrs:{icon:\"el-icon-refresh\"},on:{click:e.refulsh}},[e._v(\" 刷新 \")])],1)])]),t(\"div\",{staticClass:\"search-section\"},[t(\"el-card\",{staticClass:\"search-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"search-form\"},[t(\"div\",{staticClass:\"search-row\"},[t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"权限搜索\")]),t(\"el-input\",{staticClass:\"search-input\",attrs:{placeholder:\"请输入权限名称或描述\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.searchData.apply(null,arguments)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,\"keyword\",t)},expression:\"search.keyword\"}},[t(\"i\",{staticClass:\"el-input__icon el-icon-search\",attrs:{slot:\"prefix\"},slot:\"prefix\"})])],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"权限类型\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择权限类型\",clearable:\"\"},model:{value:e.search.type,callback:function(t){e.$set(e.search,\"type\",t)},expression:\"search.type\"}},[t(\"el-option\",{attrs:{label:\"菜单权限\",value:\"menu\"}}),t(\"el-option\",{attrs:{label:\"操作权限\",value:\"action\"}}),t(\"el-option\",{attrs:{label:\"数据权限\",value:\"data\"}})],1)],1),t(\"div\",{staticClass:\"search-item\"},[t(\"label\",{staticClass:\"search-label\"},[e._v(\"状态\")]),t(\"el-select\",{staticClass:\"search-select\",attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.search.status,callback:function(t){e.$set(e.search,\"status\",t)},expression:\"search.status\"}},[t(\"el-option\",{attrs:{label:\"启用\",value:1}}),t(\"el-option\",{attrs:{label:\"禁用\",value:0}})],1)],1)]),t(\"div\",{staticClass:\"search-actions\"},[t(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\"},on:{click:e.searchData}},[e._v(\" 搜索 \")]),t(\"el-button\",{attrs:{icon:\"el-icon-refresh-left\"},on:{click:e.clearSearch}},[e._v(\" 重置 \")])],1)])])],1),t(\"div\",{staticClass:\"tree-section\"},[t(\"el-card\",{staticClass:\"tree-card\",attrs:{shadow:\"never\"}},[t(\"div\",{staticClass:\"tree-header\"},[t(\"div\",{staticClass:\"tree-title\"},[t(\"i\",{staticClass:\"el-icon-menu\"}),e._v(\" 权限树形结构 \")]),t(\"div\",{staticClass:\"tree-tools\"},[t(\"el-button-group\",[t(\"el-button\",{attrs:{type:\"tree\"===e.viewMode?\"primary\":\"\",icon:\"el-icon-s-grid\",size:\"small\"},on:{click:function(t){e.viewMode=\"tree\"}}},[e._v(\" 树形视图 \")]),t(\"el-button\",{attrs:{type:\"table\"===e.viewMode?\"primary\":\"\",icon:\"el-icon-menu\",size:\"small\"},on:{click:function(t){e.viewMode=\"table\"}}},[e._v(\" 列表视图 \")])],1)],1)]),\"tree\"===e.viewMode?t(\"div\",{staticClass:\"tree-view\"},[t(\"el-tree\",{staticClass:\"permission-tree\",attrs:{data:e.treeData,props:e.treeProps,\"default-expand-all\":!0,\"node-key\":\"id\"},scopedSlots:e._u([{key:\"default\",fn:function({node:i,data:a}){return t(\"span\",{staticClass:\"tree-node\"},[t(\"div\",{staticClass:\"node-content\"},[t(\"div\",{staticClass:\"node-info\"},[t(\"i\",{class:e.getNodeIcon(a.type)}),t(\"span\",{staticClass:\"node-label\"},[e._v(e._s(a.label))]),t(\"el-tag\",{staticClass:\"node-status\",attrs:{type:1===a.status?\"success\":\"danger\",size:\"mini\"}},[e._v(\" \"+e._s(1===a.status?\"启用\":\"禁用\")+\" \")])],1),t(\"div\",{staticClass:\"node-actions\"},[t(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-edit\",plain:\"\"},on:{click:function(t){return e.editData(a.id)}}},[e._v(\" 编辑 \")]),t(\"el-button\",{attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-plus\",plain:\"\"},on:{click:function(t){return e.addChild(a)}}},[e._v(\" 添加子权限 \")]),t(\"el-button\",{attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-delete\",plain:\"\"},on:{click:function(t){return e.delData(a.id)}}},[e._v(\" 删除 \")])],1)])])}}],null,!1,369117358)})],1):e._e(),\"table\"===e.viewMode?t(\"div\",{staticClass:\"table-view\"},[t(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"permission-table\",attrs:{data:e.tableData,stripe:\"\",\"row-key\":\"id\",\"tree-props\":{children:\"children\",hasChildren:\"hasChildren\"},\"default-expand-all\":!1}},[t(\"el-table-column\",{attrs:{prop:\"label\",label:\"权限名称\",\"min-width\":\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"div\",{staticClass:\"permission-name-cell\",style:{paddingLeft:20*(i.row.level||0)+\"px\"}},[t(\"i\",{class:e.getNodeIcon(i.row.type),staticStyle:{\"margin-right\":\"8px\"}}),t(\"span\",{staticClass:\"permission-name\"},[e._v(e._s(i.row.label))])])]}}],null,!1,296214969)}),t(\"el-table-column\",{attrs:{prop:\"code\",label:\"权限代码\",width:\"180\",align:\"center\",\"show-overflow-tooltip\":\"\"}}),t(\"el-table-column\",{attrs:{prop:\"type\",label:\"权限类型\",width:\"120\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"el-tag\",{attrs:{type:e.getTypeColor(i.row.type),size:\"small\"}},[e._v(\" \"+e._s(e.getTypeLabel(i.row.type))+\" \")])]}}],null,!1,417277375)}),t(\"el-table-column\",{attrs:{prop:\"status\",label:\"状态\",width:\"100\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"el-switch\",{attrs:{\"active-value\":1,\"inactive-value\":0},on:{change:function(t){return e.changeStatus(i.row)}},model:{value:i.row.status,callback:function(t){e.$set(i.row,\"status\",t)},expression:\"scope.row.status\"}})]}}],null,!1,2880962836)}),t(\"el-table-column\",{attrs:{prop:\"sort\",label:\"排序\",width:\"80\",align:\"center\"}}),t(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"创建时间\",width:\"160\",align:\"center\"}}),t(\"el-table-column\",{attrs:{fixed:\"right\",label:\"操作\",width:\"240\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(i){return[t(\"div\",{staticClass:\"action-buttons\"},[t(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-edit\",plain:\"\"},on:{click:function(t){return t.stopPropagation(),e.editData(i.row.id)}}},[e._v(\" 编辑 \")]),\"menu\"===i.row.type?t(\"el-button\",{attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-plus\",plain:\"\"},on:{click:function(t){return t.stopPropagation(),e.addChild(i.row)}}},[e._v(\" 添加子权限 \")]):e._e(),t(\"el-button\",{attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-delete\",plain:\"\"},on:{click:function(t){return t.stopPropagation(),e.delData(i.row.id)}}},[e._v(\" 删除 \")])],1)]}}],null,!1,2647926959)})],1)],1):e._e()])],1),t(\"el-dialog\",{attrs:{title:e.dialogTitle,visible:e.dialogFormVisible,\"close-on-click-modal\":!1,width:\"60%\"},on:{\"update:visible\":function(t){e.dialogFormVisible=t}}},[t(\"el-form\",{ref:\"ruleForm\",attrs:{model:e.ruleForm,rules:e.rules,\"label-width\":\"120px\"}},[t(\"el-row\",{attrs:{gutter:24}},[t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"权限名称\",prop:\"label\"}},[t(\"el-input\",{attrs:{placeholder:\"请输入权限名称\"},model:{value:e.ruleForm.label,callback:function(t){e.$set(e.ruleForm,\"label\",t)},expression:\"ruleForm.label\"}})],1)],1),t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"权限代码\",prop:\"code\"}},[t(\"el-input\",{attrs:{placeholder:\"请输入权限代码\"},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,\"code\",t)},expression:\"ruleForm.code\"}})],1)],1)],1),t(\"el-row\",{attrs:{gutter:24}},[t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"权限类型\",prop:\"type\"}},[t(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择权限类型\"},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,\"type\",t)},expression:\"ruleForm.type\"}},[t(\"el-option\",{attrs:{label:\"菜单权限\",value:\"menu\"}}),t(\"el-option\",{attrs:{label:\"操作权限\",value:\"action\"}}),t(\"el-option\",{attrs:{label:\"数据权限\",value:\"data\"}})],1)],1)],1),t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"父级权限\"}},[t(\"el-cascader\",{staticStyle:{width:\"100%\"},attrs:{options:e.parentOptions,props:e.cascaderProps,placeholder:\"请选择父级权限\",clearable:\"\"},model:{value:e.ruleForm.parent_id,callback:function(t){e.$set(e.ruleForm,\"parent_id\",t)},expression:\"ruleForm.parent_id\"}})],1)],1)],1),t(\"el-row\",{attrs:{gutter:24}},[t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"排序\"}},[t(\"el-input-number\",{staticStyle:{width:\"100%\"},attrs:{min:0,max:999},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,\"sort\",t)},expression:\"ruleForm.sort\"}})],1)],1),t(\"el-col\",{attrs:{span:12}},[t(\"el-form-item\",{attrs:{label:\"状态\"}},[t(\"el-switch\",{attrs:{\"active-value\":1,\"inactive-value\":0,\"active-text\":\"启用\",\"inactive-text\":\"禁用\"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,\"status\",t)},expression:\"ruleForm.status\"}})],1)],1)],1),t(\"el-form-item\",{attrs:{label:\"权限描述\"}},[t(\"el-input\",{attrs:{type:\"textarea\",rows:3,placeholder:\"请输入权限描述\"},model:{value:e.ruleForm.description,callback:function(t){e.$set(e.ruleForm,\"description\",t)},expression:\"ruleForm.description\"}})],1),\"menu\"===e.ruleForm.type?t(\"el-form-item\",{attrs:{label:\"路由路径\"}},[t(\"el-input\",{attrs:{placeholder:\"请输入路由路径\"},model:{value:e.ruleForm.path,callback:function(t){e.$set(e.ruleForm,\"path\",t)},expression:\"ruleForm.path\"}})],1):e._e(),\"menu\"===e.ruleForm.type?t(\"el-form-item\",{attrs:{label:\"图标\"}},[t(\"el-input\",{attrs:{placeholder:\"请输入图标类名\"},model:{value:e.ruleForm.icon,callback:function(t){e.$set(e.ruleForm,\"icon\",t)},expression:\"ruleForm.icon\"}},[t(\"template\",{slot:\"prepend\"},[t(\"i\",{class:e.ruleForm.icon||\"el-icon-menu\"})])],2)],1):e._e()],1),t(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[t(\"el-button\",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(\"取 消\")]),t(\"el-button\",{attrs:{type:\"primary\"},on:{click:function(t){return e.saveData()}}},[e._v(\"确 定\")])],1)],1)],1)},s=[function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"title-section\"},[t(\"h2\",{staticClass:\"page-title\"},[t(\"i\",{staticClass:\"el-icon-key\"}),e._v(\" 权限管理 \")]),t(\"p\",{staticClass:\"page-subtitle\"},[e._v(\"管理系统功能权限和访问控制\")])])}],r=(i(\"14d9\"),{name:\"PermissionManagement\",data(){return{viewMode:\"tree\",loading:!1,search:{keyword:\"\",type:\"\",status:\"\"},treeData:[],originalData:[],dialogFormVisible:!1,dialogTitle:\"新增权限\",parentOptions:[],ruleForm:{id:null,label:\"\",code:\"\",type:\"menu\",parent_id:null,sort:0,status:1,description:\"\",path:\"\",icon:\"\"},rules:{label:[{required:!0,message:\"请输入权限名称\",trigger:\"blur\"}],code:[{required:!0,message:\"请输入权限代码\",trigger:\"blur\"}],type:[{required:!0,message:\"请选择权限类型\",trigger:\"change\"}]},treeProps:{children:\"children\",label:\"label\"},cascaderProps:{value:\"id\",label:\"label\",children:\"children\",checkStrictly:!0}}},computed:{tableData(){return this.flattenTreeForTable(this.treeData)}},mounted(){this.getData()},methods:{getData(){this.loading=!0,setTimeout(()=>{this.loading=!1;const e=[{id:1,label:\"系统管理\",code:\"system\",type:\"menu\",parent_id:null,sort:1,status:1,description:\"系统管理模块\",path:\"/system\",icon:\"el-icon-setting\",create_time:\"2024-01-01 10:00:00\",children:[{id:11,label:\"用户管理\",code:\"system:user\",type:\"menu\",parent_id:1,sort:1,status:1,description:\"用户管理功能\",path:\"/user\",icon:\"el-icon-user\",create_time:\"2024-01-01 10:00:00\",children:[{id:111,label:\"查看用户\",code:\"system:user:view\",type:\"action\",parent_id:11,sort:1,status:1,description:\"查看用户列表\",create_time:\"2024-01-01 10:00:00\"},{id:112,label:\"新增用户\",code:\"system:user:add\",type:\"action\",parent_id:11,sort:2,status:1,description:\"新增用户\",create_time:\"2024-01-01 10:00:00\"},{id:113,label:\"编辑用户\",code:\"system:user:edit\",type:\"action\",parent_id:11,sort:3,status:1,description:\"编辑用户信息\",create_time:\"2024-01-01 10:00:00\"},{id:114,label:\"删除用户\",code:\"system:user:delete\",type:\"action\",parent_id:11,sort:4,status:1,description:\"删除用户\",create_time:\"2024-01-01 10:00:00\"}]},{id:12,label:\"职位管理\",code:\"system:position\",type:\"menu\",parent_id:1,sort:2,status:1,description:\"职位管理功能\",path:\"/zhiwei\",icon:\"el-icon-postcard\",create_time:\"2024-01-01 10:00:00\",children:[{id:121,label:\"查看职位\",code:\"system:position:view\",type:\"action\",parent_id:12,sort:1,status:1,description:\"查看职位列表\",create_time:\"2024-01-01 10:00:00\"},{id:122,label:\"新增职位\",code:\"system:position:add\",type:\"action\",parent_id:12,sort:2,status:1,description:\"新增职位\",create_time:\"2024-01-01 10:00:00\"},{id:123,label:\"编辑职位\",code:\"system:position:edit\",type:\"action\",parent_id:12,sort:3,status:1,description:\"编辑职位信息\",create_time:\"2024-01-01 10:00:00\"},{id:124,label:\"删除职位\",code:\"system:position:delete\",type:\"action\",parent_id:12,sort:4,status:1,description:\"删除职位\",create_time:\"2024-01-01 10:00:00\"}]},{id:13,label:\"权限管理\",code:\"system:permission\",type:\"menu\",parent_id:1,sort:3,status:1,description:\"权限管理功能\",path:\"/quanxian\",icon:\"el-icon-key\",create_time:\"2024-01-01 10:00:00\",children:[{id:131,label:\"查看权限\",code:\"system:permission:view\",type:\"action\",parent_id:13,sort:1,status:1,description:\"查看权限列表\",create_time:\"2024-01-01 10:00:00\"},{id:132,label:\"新增权限\",code:\"system:permission:add\",type:\"action\",parent_id:13,sort:2,status:1,description:\"新增权限\",create_time:\"2024-01-01 10:00:00\"},{id:133,label:\"编辑权限\",code:\"system:permission:edit\",type:\"action\",parent_id:13,sort:3,status:1,description:\"编辑权限信息\",create_time:\"2024-01-01 10:00:00\"},{id:134,label:\"删除权限\",code:\"system:permission:delete\",type:\"action\",parent_id:13,sort:4,status:1,description:\"删除权限\",create_time:\"2024-01-01 10:00:00\"}]}]},{id:2,label:\"业务管理\",code:\"business\",type:\"menu\",parent_id:null,sort:2,status:1,description:\"业务管理模块\",path:\"/business\",icon:\"el-icon-suitcase\",create_time:\"2024-01-01 10:00:00\",children:[{id:21,label:\"订单管理\",code:\"business:order\",type:\"menu\",parent_id:2,sort:1,status:1,description:\"订单管理功能\",path:\"/dingdan\",icon:\"el-icon-document\",create_time:\"2024-01-01 10:00:00\",children:[{id:211,label:\"查看订单\",code:\"business:order:view\",type:\"action\",parent_id:21,sort:1,status:1,description:\"查看订单列表\",create_time:\"2024-01-01 10:00:00\"},{id:212,label:\"新增订单\",code:\"business:order:add\",type:\"action\",parent_id:21,sort:2,status:1,description:\"新增订单\",create_time:\"2024-01-01 10:00:00\"},{id:213,label:\"编辑订单\",code:\"business:order:edit\",type:\"action\",parent_id:21,sort:3,status:1,description:\"编辑订单信息\",create_time:\"2024-01-01 10:00:00\"},{id:214,label:\"删除订单\",code:\"business:order:delete\",type:\"action\",parent_id:21,sort:4,status:1,description:\"删除订单\",create_time:\"2024-01-01 10:00:00\"},{id:215,label:\"导出订单\",code:\"business:order:export\",type:\"action\",parent_id:21,sort:5,status:1,description:\"导出订单数据\",create_time:\"2024-01-01 10:00:00\"}]},{id:22,label:\"客户管理\",code:\"business:customer\",type:\"menu\",parent_id:2,sort:2,status:1,description:\"客户管理功能\",path:\"/customer\",icon:\"el-icon-user-solid\",create_time:\"2024-01-01 10:00:00\",children:[{id:221,label:\"查看客户\",code:\"business:customer:view\",type:\"action\",parent_id:22,sort:1,status:1,description:\"查看客户列表\",create_time:\"2024-01-01 10:00:00\"},{id:222,label:\"新增客户\",code:\"business:customer:add\",type:\"action\",parent_id:22,sort:2,status:1,description:\"新增客户\",create_time:\"2024-01-01 10:00:00\"},{id:223,label:\"编辑客户\",code:\"business:customer:edit\",type:\"action\",parent_id:22,sort:3,status:1,description:\"编辑客户信息\",create_time:\"2024-01-01 10:00:00\"},{id:224,label:\"删除客户\",code:\"business:customer:delete\",type:\"action\",parent_id:22,sort:4,status:1,description:\"删除客户\",create_time:\"2024-01-01 10:00:00\"}]}]},{id:3,label:\"文书管理\",code:\"document\",type:\"menu\",parent_id:null,sort:3,status:1,description:\"文书管理模块\",path:\"/document\",icon:\"el-icon-document-copy\",create_time:\"2024-01-01 10:00:00\",children:[{id:31,label:\"合同管理\",code:\"document:contract\",type:\"menu\",parent_id:3,sort:1,status:1,description:\"合同管理功能\",path:\"/hetong\",icon:\"el-icon-document\",create_time:\"2024-01-01 10:00:00\",children:[{id:311,label:\"查看合同\",code:\"document:contract:view\",type:\"action\",parent_id:31,sort:1,status:1,description:\"查看合同列表\",create_time:\"2024-01-01 10:00:00\"},{id:312,label:\"新增合同\",code:\"document:contract:add\",type:\"action\",parent_id:31,sort:2,status:1,description:\"新增合同\",create_time:\"2024-01-01 10:00:00\"},{id:313,label:\"编辑合同\",code:\"document:contract:edit\",type:\"action\",parent_id:31,sort:3,status:1,description:\"编辑合同信息\",create_time:\"2024-01-01 10:00:00\"},{id:314,label:\"删除合同\",code:\"document:contract:delete\",type:\"action\",parent_id:31,sort:4,status:1,description:\"删除合同\",create_time:\"2024-01-01 10:00:00\"},{id:315,label:\"审核合同\",code:\"document:contract:audit\",type:\"action\",parent_id:31,sort:5,status:1,description:\"审核合同\",create_time:\"2024-01-01 10:00:00\"}]},{id:32,label:\"律师函管理\",code:\"document:lawyer\",type:\"menu\",parent_id:3,sort:2,status:1,description:\"律师函管理功能\",path:\"/lawyer\",icon:\"el-icon-message\",create_time:\"2024-01-01 10:00:00\",children:[{id:321,label:\"查看律师函\",code:\"document:lawyer:view\",type:\"action\",parent_id:32,sort:1,status:1,description:\"查看律师函列表\",create_time:\"2024-01-01 10:00:00\"},{id:322,label:\"发送律师函\",code:\"document:lawyer:send\",type:\"action\",parent_id:32,sort:2,status:1,description:\"发送律师函\",create_time:\"2024-01-01 10:00:00\"},{id:323,label:\"编辑律师函\",code:\"document:lawyer:edit\",type:\"action\",parent_id:32,sort:3,status:1,description:\"编辑律师函\",create_time:\"2024-01-01 10:00:00\"}]},{id:33,label:\"课程管理\",code:\"document:course\",type:\"menu\",parent_id:3,sort:3,status:1,description:\"课程管理功能\",path:\"/kecheng\",icon:\"el-icon-video-play\",create_time:\"2024-01-01 10:00:00\",children:[{id:331,label:\"查看课程\",code:\"document:course:view\",type:\"action\",parent_id:33,sort:1,status:1,description:\"查看课程列表\",create_time:\"2024-01-01 10:00:00\"},{id:332,label:\"新增课程\",code:\"document:course:add\",type:\"action\",parent_id:33,sort:2,status:1,description:\"新增课程\",create_time:\"2024-01-01 10:00:00\"},{id:333,label:\"编辑课程\",code:\"document:course:edit\",type:\"action\",parent_id:33,sort:3,status:1,description:\"编辑课程\",create_time:\"2024-01-01 10:00:00\"},{id:334,label:\"删除课程\",code:\"document:course:delete\",type:\"action\",parent_id:33,sort:4,status:1,description:\"删除课程\",create_time:\"2024-01-01 10:00:00\"}]}]},{id:4,label:\"财务管理\",code:\"finance\",type:\"menu\",parent_id:null,sort:4,status:1,description:\"财务管理模块\",path:\"/finance\",icon:\"el-icon-coin\",create_time:\"2024-01-01 10:00:00\",children:[{id:41,label:\"支付管理\",code:\"finance:payment\",type:\"menu\",parent_id:4,sort:1,status:1,description:\"支付管理功能\",path:\"/order\",icon:\"el-icon-money\",create_time:\"2024-01-01 10:00:00\",children:[{id:411,label:\"查看支付记录\",code:\"finance:payment:view\",type:\"action\",parent_id:41,sort:1,status:1,description:\"查看支付记录\",create_time:\"2024-01-01 10:00:00\"},{id:412,label:\"处理退款\",code:\"finance:payment:refund\",type:\"action\",parent_id:41,sort:2,status:1,description:\"处理退款申请\",create_time:\"2024-01-01 10:00:00\"},{id:413,label:\"导出财务报表\",code:\"finance:payment:export\",type:\"action\",parent_id:41,sort:3,status:1,description:\"导出财务报表\",create_time:\"2024-01-01 10:00:00\"}]}]}];this.treeData=e,this.originalData=JSON.parse(JSON.stringify(e)),this.updateParentOptions()},500)},flattenTreeForTable(e,t=0,i=[]){return e.forEach(e=>{const a={...e,level:t,hasChildren:e.children&&e.children.length>0};delete a.children,i.push(a),e.children&&e.children.length>0&&this.flattenTreeForTable(e.children,t+1,i)}),i},updateParentOptions(){this.parentOptions=this.buildCascaderOptions(this.treeData)},buildCascaderOptions(e){return e.map(e=>({id:e.id,label:e.label,children:e.children?this.buildCascaderOptions(e.children):[]}))},searchData(){this.getData()},clearSearch(){this.search={keyword:\"\",type:\"\",status:\"\"},this.searchData()},refulsh(){this.$router.go(0)},editData(e){if(0===e)this.dialogTitle=\"新增权限\",this.ruleForm={id:null,label:\"\",code:\"\",type:\"menu\",parent_id:null,sort:0,status:1,description:\"\",path:\"\",icon:\"\"};else{this.dialogTitle=\"编辑权限\";const t=this.findPermissionById(e);t&&(this.ruleForm={...t})}this.dialogFormVisible=!0},addChild(e){this.dialogTitle=\"新增子权限\",this.ruleForm={id:null,label:\"\",code:\"\",type:\"action\",parent_id:[e.id],sort:0,status:1,description:\"\",path:\"\",icon:\"\"},this.dialogFormVisible=!0},findPermissionById(e,t=this.originalData){for(let i of t){if(i.id===e)return i;if(i.children){const t=this.findPermissionById(e,i.children);if(t)return t}}return null},delData(e){this.$confirm(\"确定要删除这个权限吗？删除后不可恢复！\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then(()=>{this.$message.success(\"删除成功！\"),this.getData()}).catch(()=>{this.$message.info(\"已取消删除\")})},saveData(){this.$refs[\"ruleForm\"].validate(e=>{e&&(this.$message.success(this.ruleForm.id?\"更新成功！\":\"新增成功！\"),this.dialogFormVisible=!1,this.getData())})},changeStatus(e){this.$message.success(\"权限状态已\"+(1===e.status?\"启用\":\"禁用\"))},getNodeIcon(e){const t={menu:\"el-icon-menu\",action:\"el-icon-setting\",data:\"el-icon-document\"};return t[e]||\"el-icon-menu\"},getTypeColor(e){const t={menu:\"primary\",action:\"success\",data:\"warning\"};return t[e]||\"primary\"},getTypeLabel(e){const t={menu:\"菜单权限\",action:\"操作权限\",data:\"数据权限\"};return t[e]||\"菜单权限\"}}}),l=r,o=(i(\"4978\"),i(\"2877\")),n=Object(o[\"a\"])(l,a,s,!1,null,\"7aacc5fe\",null);t[\"default\"]=n.exports},\"2a9c\":function(e,t,i){},4978:function(e,t,i){\"use strict\";i(\"2a9c\")}}]);", "extractedComments": []}