{"name": "markbaker/complex", "type": "library", "description": "PHP Class for working with complex numbers", "keywords": ["complex", "mathematics"], "homepage": "https://github.com/MarkBaker/PHPComplex", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.6.0|^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.0|^6.0|^7.0", "phpdocumentor/phpdocumentor": "2.*", "phpmd/phpmd": "2.*", "sebastian/phpcpd": "2.*", "phploc/phploc": "^4.0|^5.0|^6.0|^7.0", "squizlabs/php_codesniffer": "^3.4.0", "phpcompatibility/php-compatibility": "^9.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.5.0"}, "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "scripts": {"style": ["phpcs --report-width=200 --report=summary,full -n"], "mess": ["phpmd classes/src/ xml codesize,unusedcode,design,naming -n"], "lines": ["phploc classes/src/ -n"], "cpd": ["phpcpd classes/src/ -n"], "versions": ["phpcs --report-width=200 --report=summary,full classes/src/ --standard=PHPCompatibility --runtime-set testVersion 5.6- -n"]}, "minimum-stability": "dev"}