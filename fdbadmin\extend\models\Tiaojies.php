<?php
namespace models;
use think\Model;
class Tiaojies extends Base{
	public function setSligerImagesAttr($value){
		if(empty($value)) return '';
		else return serialize($value);
	}

	public function getSligerImagesAttr($value){
		if(empty($value)) return [];
		else return unserialize($value);
	}
	
	public function setDuifangAttr($value){
		if(empty($value)) return '';
		else return serialize($value);
	}
    
	public function getDuifangAttr($value){
		if(empty($value)) return [];
		else return unserialize($value);
	}
}