<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎使用法律服务管理系统</h1>
        <p class="welcome-subtitle">{{ getCurrentTime() }} | 管理员，您好！</p>
      </div>
      <div class="welcome-actions">
        <el-button type="primary" @click="handleQuickAction('new-case')">
          <i class="el-icon-plus"></i> 新建案件
        </el-button>
        <el-button type="success" @click="handleQuickAction('new-contract')">
          <i class="el-icon-document-add"></i> 新建合同
        </el-button>
        <el-button type="info" @click="refreshData" :loading="loading">
          <i class="el-icon-refresh"></i> 刷新数据
        </el-button>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section" v-loading="loading">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">总用户数</div>
              <div class="stat-change positive">
                <i class="el-icon-arrow-up"></i> +12%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-icon case-icon">
              <i class="el-icon-folder"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalCases }}</div>
              <div class="stat-label">案件总数</div>
              <div class="stat-change positive">
                <i class="el-icon-arrow-up"></i> +8%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-icon contract-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalContracts }}</div>
              <div class="stat-label">合同数量</div>
              <div class="stat-change positive">
                <i class="el-icon-arrow-up"></i> +15%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <div class="stat-card">
            <div class="stat-icon revenue-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">¥{{ stats.totalRevenue }}</div>
              <div class="stat-label">总收入</div>
              <div class="stat-change positive">
                <i class="el-icon-arrow-up"></i> +22%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧内容 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <!-- 图表区域 -->
        <div class="chart-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">业务数据趋势</span>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" size="small">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="year">本年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="el-icon-data-line chart-icon"></i>
                <p>数据图表区域</p>
                <p class="chart-desc">这里可以集成 ECharts 或其他图表库显示业务数据趋势</p>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 最近活动 -->
        <div class="activity-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">最近活动</span>
              <el-button type="text" @click="viewAllActivities">查看全部</el-button>
            </div>
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-avatar">
                  <i :class="activity.icon" :style="{ color: activity.color }"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-desc">{{ activity.description }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <!-- 快捷操作 -->
        <div class="quick-actions-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">快捷操作</span>
            </div>
            <div class="quick-actions">
              <div
                v-for="action in quickActions"
                :key="action.id"
                class="quick-action-item"
                @click="handleQuickAction(action.action)"
              >
                <div class="action-icon" :style="{ backgroundColor: action.color }">
                  <i :class="action.icon"></i>
                </div>
                <div class="action-content">
                  <div class="action-title">{{ action.title }}</div>
                  <div class="action-desc">{{ action.description }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 待办事项 -->
        <div class="todo-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">待办事项</span>
              <el-badge :value="todoList.filter(item => !item.completed).length" class="todo-badge">
                <el-button type="text" @click="viewAllTodos">查看全部</el-button>
              </el-badge>
            </div>
            <div class="todo-list">
              <div
                v-for="todo in todoList.slice(0, 5)"
                :key="todo.id"
                class="todo-item"
                :class="{ completed: todo.completed }"
              >
                <el-checkbox
                  v-model="todo.completed"
                  @change="handleTodoChange(todo)"
                >
                  {{ todo.title }}
                </el-checkbox>
                <div class="todo-priority" :class="todo.priority">
                  {{ getPriorityText(todo.priority) }}
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 系统通知 -->
        <div class="notification-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">系统通知</span>
              <el-badge :value="notifications.filter(item => !item.read).length" class="notification-badge">
                <el-button type="text" @click="viewAllNotifications">查看全部</el-button>
              </el-badge>
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notifications.slice(0, 3)"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
                @click="markAsRead(notification)"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-time">{{ notification.time }}</div>
                </div>
                <div v-if="!notification.read" class="notification-dot"></div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- System Monitor -->
        <div class="system-monitor-section">
          <el-card shadow="hover">
            <div slot="header" class="card-header">
              <span class="card-title">系统监控</span>
            </div>
            <div class="system-monitor-content">
              <system-monitor></system-monitor>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SystemMonitor from '@/components/SystemMonitor.vue'

export default {
  name: 'Dashboard',
  components: {
    SystemMonitor
  },
  data() {
    return {
      chartPeriod: 'month',
      loading: false,
      stats: {
        totalUsers: 0,
        totalCases: 0,
        totalContracts: 0,
        totalRevenue: '0'
      },
      recentActivities: [],
      quickActions: [
        {
          id: 1,
          icon: 'el-icon-plus',
          color: '#409EFF',
          title: '新建案件',
          description: '创建新的法律案件',
          action: 'new-case'
        },
        {
          id: 2,
          icon: 'el-icon-document-add',
          color: '#67C23A',
          title: '新建合同',
          description: '创建新的合同文档',
          action: 'new-contract'
        },
        {
          id: 3,
          icon: 'el-icon-user-solid',
          color: '#E6A23C',
          title: '添加客户',
          description: '添加新的客户信息',
          action: 'new-client'
        },
        {
          id: 4,
          icon: 'el-icon-upload',
          color: '#F56C6C',
          title: '文件归档',
          description: '上传并归档文件',
          action: 'upload-file'
        }
      ],
      todoList: [
        {
          id: 1,
          title: '审核张三的合同申请',
          completed: false,
          priority: 'high'
        },
        {
          id: 2,
          title: '回复李四的法律咨询',
          completed: false,
          priority: 'medium'
        },
        {
          id: 3,
          title: '准备明天的庭审材料',
          completed: true,
          priority: 'high'
        },
        {
          id: 4,
          title: '更新客户联系信息',
          completed: false,
          priority: 'low'
        },
        {
          id: 5,
          title: '整理本月财务报表',
          completed: false,
          priority: 'medium'
        }
      ],
      notifications: [
        {
          id: 1,
          title: '系统维护通知',
          time: '今天 14:30',
          read: false
        },
        {
          id: 2,
          title: '新版本更新',
          time: '昨天 16:20',
          read: false
        },
        {
          id: 3,
          title: '数据备份完成',
          time: '昨天 09:15',
          read: true
        }
      ]
    }
  },
  mounted() {
    this.loadDashboardData()
  },
  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadStats(),
          this.loadActivities(),
          this.loadTodos(),
          this.loadQuickActions()
        ])
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载数据失败，请刷新页面重试')
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const response = await this.getRequest('/dashboard/getStats')
        if (response.code === 200) {
          const data = response.data
          this.stats = {
            totalUsers: data.totalUsers || 0,
            totalCases: data.totalDebts || 0, // 债务数作为案件数
            totalContracts: data.totalOrders || 0, // 订单数作为合同数
            totalRevenue: data.totalRevenue || '0'
          }
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载最近活动
    async loadActivities() {
      try {
        const response = await this.getRequest('/dashboard/getActivities')
        if (response.code === 200) {
          this.recentActivities = response.data || []
        }
      } catch (error) {
        console.error('加载活动数据失败:', error)
      }
    },

    // 加载待办事项
    async loadTodos() {
      try {
        const response = await this.getRequest('/dashboard/getTodos')
        if (response.code === 200) {
          this.todoList = response.data || []
        }
      } catch (error) {
        console.error('加载待办事项失败:', error)
      }
    },

    // 加载快捷操作
    async loadQuickActions() {
      try {
        const response = await this.getRequest('/dashboard/getQuickActions')
        if (response.code === 200) {
          const actions = response.data || []
          // 转换为前端需要的格式
          this.quickActions = actions.map(action => ({
            id: action.title,
            icon: action.icon,
            color: action.color,
            title: action.title,
            description: `当前数量: ${action.count}`,
            action: action.url
          }))
        }
      } catch (error) {
        console.error('加载快捷操作失败:', error)
      }
    },

    getCurrentTime() {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      }
      return now.toLocaleDateString('zh-CN', options)
    },

    handleQuickAction(action) {
      // 如果action是URL路径，直接跳转
      if (action.startsWith('/')) {
        this.$router.push(action)
        return
      }

      // 兼容原有的action处理
      switch (action) {
        case 'new-case':
          this.$router.push('/debts')
          break
        case 'new-contract':
          this.$router.push('/dingdan')
          break
        case 'new-client':
          this.$router.push('/user')
          break
        case 'upload-file':
          this.$router.push('/archive/file')
          break
        default:
          this.$message.info(`执行操作: ${action}`)
      }
    },

    viewAllActivities() {
      this.$message.info('查看所有活动')
    },

    viewAllTodos() {
      this.$message.info('查看所有待办事项')
    },

    viewAllNotifications() {
      this.$message.info('查看所有通知')
    },

    async handleTodoChange(todo) {
      try {
        const response = await this.postRequest('/dashboard/updateTodo', {
          id: todo.id,
          completed: todo.completed
        })
        if (response.code === 200) {
          this.$message.success(todo.completed ? '任务已完成' : '任务已重新激活')
        }
      } catch (error) {
        console.error('更新待办事项失败:', error)
        // 回滚状态
        todo.completed = !todo.completed
        this.$message.error('更新失败，请重试')
      }
    },

    markAsRead(notification) {
      notification.read = true
      this.$message.success('通知已标记为已读')
    },

    getPriorityText(priority) {
      const map = {
        high: '高',
        medium: '中',
        low: '低'
      }
      return map[priority] || '中'
    },

    // 刷新数据
    refreshData() {
      this.loadDashboardData()
      this.$message.success('数据已刷新')
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.case-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.contract-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.revenue-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #27ae60;
}

/* 主要内容区域 */
.main-content {
  margin-top: 20px;
}

.chart-section,
.activity-section,
.quick-actions-section,
.todo-section,
.notification-section,
.system-monitor-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 图表区域 */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #95a5a6;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.chart-desc {
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* 活动列表 */
.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.activity-desc {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 4px;
}

.activity-time {
  color: #bdc3c7;
  font-size: 12px;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-item:hover {
  background-color: #e9ecef;
  transform: translateX(4px);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  font-size: 18px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.action-desc {
  color: #7f8c8d;
  font-size: 12px;
}

/* 待办事项 */
.todo-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-priority {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.todo-priority.high {
  background-color: #fee;
  color: #e74c3c;
}

.todo-priority.medium {
  background-color: #fff3cd;
  color: #f39c12;
}

.todo-priority.low {
  background-color: #d4edda;
  color: #27ae60;
}

/* 通知列表 */
.notification-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e3f2fd;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.notification-time {
  color: #7f8c8d;
  font-size: 12px;
}

.notification-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-actions {
    justify-content: center;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style> 