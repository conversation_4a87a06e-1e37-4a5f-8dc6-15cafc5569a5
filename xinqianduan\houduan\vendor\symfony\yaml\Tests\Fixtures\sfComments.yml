--- %YAML:1.0
test: Comments at the end of a line
brief: >
    Comments at the end of a line
yaml: |
    ex1: "foo # bar"
    ex2: "foo # bar" # comment
    ex3: 'foo # bar' # comment
    ex4: foo # comment
    ex5: foo	#	comment with tab before  
    ex6: foo#foo # comment here
    ex7: foo 	# ignore me # and me
php: |
    array('ex1' => 'foo # bar', 'ex2' => 'foo # bar', 'ex3' => 'foo # bar', 'ex4' => 'foo', 'ex5' => 'foo', 'ex6' => 'foo#foo', 'ex7' => 'foo')
---
test: Comments in the middle
brief: >
  Comments in the middle
yaml: |
    foo:
    # some comment
    # some comment
      bar: foo
    # some comment
    # some comment
php: |
    array('foo' => array('bar' => 'foo'))
---
test: Comments on a hash line
brief: >
  Comments on a hash line
yaml: |
    foo:   # a comment
      foo: bar # a comment
php: |
    array('foo' => array('foo' => 'bar'))
---
test: 'Value starting with a #'
brief: >
  'Value starting with a #'
yaml: |
    foo:   '#bar'
php: |
    array('foo' => '#bar')
---
test: Document starting with a comment and a separator
brief: >
  Commenting before document start is allowed
yaml: |
    # document comment
    ---
    foo: bar # a comment
php: |
    array('foo' => 'bar')
---
test: Comment containing a colon on a hash line
brief: >
    Comment containing a colon on a scalar line
yaml: 'foo # comment: this is also part of the comment'
php: |
    'foo'
---
test: 'Hash key containing a #'
brief: >
    'Hash key containing a #'
yaml: 'foo#bar: baz'
php: |
    array('foo#bar' => 'baz')
---
test: 'Hash key ending with a space and a #'
brief: >
    'Hash key ending with a space and a #'
yaml: |
    'foo #': baz
php: |
    array('foo #' => 'baz')
