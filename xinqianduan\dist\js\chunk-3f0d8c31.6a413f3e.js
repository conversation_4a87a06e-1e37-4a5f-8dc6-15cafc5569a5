(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f0d8c31"],{"7e30":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page-wrapper"},[e("div",{staticClass:"page-container"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-left"},[e("h2",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-medal"}),t._v(" "+t._s(this.$router.currentRoute.name)+" ")]),e("div",{staticClass:"page-subtitle"},[t._v("管理律师专业领域分类")])]),e("el-button",{staticClass:"refresh-btn",attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.refulsh}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-section"},[e("div",{staticClass:"stat-card"},[t._m(0),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.total))]),e("div",{staticClass:"stat-label"},[t._v("专业领域")])])]),e("div",{staticClass:"stat-card"},[t._m(1),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.activeSpecialties))]),e("div",{staticClass:"stat-label"},[t._v("活跃专业")])])])]),e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-controls"},[e("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入专业名称进行搜索",clearable:""},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.searchData()}},slot:"append"})],1)],1),e("div",{staticClass:"action-controls"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.editData(0)}}},[t._v(" 新增专业 ")])],1)]),e("div",{staticClass:"table-section"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"data-table",attrs:{data:t.list,stripe:""},on:{"sort-change":t.handleSortChange}},[e("el-table-column",{attrs:{prop:"title",label:"专业名称","min-width":"200","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"specialty-title-cell"},[e("div",{staticClass:"specialty-icon"},[e("i",{staticClass:"el-icon-medal"})]),e("div",{staticClass:"specialty-info"},[e("div",{staticClass:"specialty-title"},[t._v(t._s(s.row.title))]),s.row.desc?e("div",{staticClass:"specialty-desc"},[t._v(t._s(s.row.desc))]):t._e()])])]}}])}),e("el-table-column",{attrs:{label:"专业描述","min-width":"300","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"specialty-description"},[s.row.desc?e("span",[t._v(t._s(s.row.desc))]):e("span",{staticClass:"no-desc"},[t._v("暂无描述")])])]}}])}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",sortable:""},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-cell"},[e("i",{staticClass:"el-icon-time"}),e("span",[t._v(t._s(s.row.create_time))])])]}}])}),e("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{staticClass:"status-tag",attrs:{type:t.getStatusType(s.row),size:"small"}},[t._v(" "+t._s(t.getStatusText(s.row))+" ")])]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"action-buttons"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.editData(s.row.id)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"delete-btn",attrs:{type:"text",size:"small",icon:"el-icon-delete"},nativeOn:{click:function(e){return e.preventDefault(),t.delData(s.$index,s.row.id)}}},[t._v(" 删除 ")])],1)]}}])})],1)],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"page-sizes":[20,50,100,200],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{attrs:{title:t.title+"内容",visible:t.dialogFormVisible,"close-on-click-modal":!1,width:"70%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[e("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules}},[e("el-form-item",{attrs:{label:t.title+"标题","label-width":t.formLabelWidth,prop:"title"}},[e("el-input",{attrs:{autocomplete:"off"},model:{value:t.ruleForm.title,callback:function(e){t.$set(t.ruleForm,"title",e)},expression:"ruleForm.title"}})],1),e("el-form-item",{attrs:{label:"描述","label-width":t.formLabelWidth}},[e("el-input",{attrs:{autocomplete:"off",type:"textarea",rows:4},model:{value:t.ruleForm.desc,callback:function(e){t.$set(t.ruleForm,"desc",e)},expression:"ruleForm.desc"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("确 定")])],1)],1),e("el-dialog",{attrs:{title:"图片查看",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("el-image",{attrs:{src:t.show_image}})],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-medal"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-icon active"},[e("i",{staticClass:"el-icon-check"})])}],l={name:"list",components:{},data(){return{allSize:"mini",list:[],total:1,page:1,size:20,search:{keyword:""},loading:!0,url:"/zhuanye/",title:"专业",info:{},dialogFormVisible:!1,show_image:"",dialogVisible:!1,ruleForm:{title:"",is_num:0},rules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}]},formLabelWidth:"120px"}},computed:{activeSpecialties(){return this.list.filter(t=>0!==t.status).length}},mounted(){this.getData()},methods:{editData(t){let e=this;0!=t?this.getInfo(t):this.ruleForm={title:"",desc:""},e.dialogFormVisible=!0},getInfo(t){let e=this;e.getRequest(e.url+"read?id="+t).then(t=>{t&&(e.ruleForm=t.data)})},delData(t,e){this.$confirm("是否删除该信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.deleteRequest(this.url+"delete?id="+e).then(e=>{200==e.code&&(this.$message({type:"success",message:"删除成功!"}),this.list.splice(t,1))})}).catch(()=>{this.$message({type:"error",message:"取消删除!"})})},refulsh(){this.$router.go(0)},searchData(){this.page=1,this.size=20,this.getData()},getData(){let t=this;t.loading=!0,t.postRequest(t.url+"index?page="+t.page+"&size="+t.size,t.search).then(e=>{e&&200==e.code?(t.list=Array.isArray(e.data)?e.data:[],t.total=e.count||0):(t.list=[],t.total=0),t.loading=!1}).catch(e=>{console.error("获取数据失败:",e),t.list=[],t.total=0,t.loading=!1})},saveData(){let t=this;this.$refs["ruleForm"].validate(e=>{if(!e)return!1;this.postRequest(t.url+"save",this.ruleForm).then(e=>{200==e.code?(t.$message({type:"success",message:e.msg}),this.getData(),t.dialogFormVisible=!1):t.$message({type:"error",message:e.msg})})})},handleSizeChange(t){this.size=t,this.getData()},handleCurrentChange(t){this.page=t,this.getData()},handleSortChange(t){console.log("排序变化:",t)},getStatusType(t){return 0===t.status?"info":"success"},getStatusText(t){return 0===t.status?"停用":"正常"},handleSuccess(t){this.ruleForm.pic_path=t.data.url},showImage(t){this.show_image=t,this.dialogVisible=!0},beforeUpload(t){const e=/^image\/(jpeg|png|jpg)$/.test(t.type);e||this.$message.error("上传图片格式不对!")},delImage(t,e){let s=this;s.getRequest("/Upload/delImage?fileName="+t).then(t=>{200==t.code?(s.ruleForm[e]="",s.$message.success("删除成功!")):s.$message.error(t.msg)})}}},o=l,r=(s("ac31"),s("2877")),n=Object(r["a"])(o,a,i,!1,null,"4eaa3520",null);e["default"]=n.exports},a862:function(t,e,s){},ac31:function(t,e,s){"use strict";s("a862")}}]);
//# sourceMappingURL=chunk-3f0d8c31.6a413f3e.js.map