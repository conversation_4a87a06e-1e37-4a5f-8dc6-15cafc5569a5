{"remainingRequest": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\src\\views\\pages\\debt\\debts.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\src\\views\\pages\\debt\\debts.vue", "mtime": 1732626900082}, {"path": "H:\\fdbfront\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748278551031}, {"path": "H:\\fdbfront\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748278547552}, {"path": "H:\\fdbfront\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748278550818}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyBAIGlzIGFuIGFsaWFzIHRvIC9zcmMNCmltcG9ydCBVc2VyRGV0YWlscyBmcm9tICIvc3JjL2NvbXBvbmVudHMvVXNlckRldGFpbC52dWUiOw0KaW1wb3J0IERlYnREZXRhaWwgZnJvbSAiL3NyYy9jb21wb25lbnRzL0RlYnREZXRhaWwudnVlIjsNCmltcG9ydCBzdG9yZSBmcm9tICIuLi8uLi8uLi9zdG9yZSI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJsaXN0IiwNCiAgY29tcG9uZW50czogeyBVc2VyRGV0YWlscyxEZWJ0RGV0YWlsIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgICAgdXBsb2FkQWN0aW9uOicnLA0KICAgICAgICB1cGxvYWREZWJ0c0FjdGlvbjogIi9hZG1pbi9kZWJ0L2ltcG9ydERlYnRzP3Rva2VuPSIgKyB0aGlzLiRzdG9yZS5nZXR0ZXJzLkdFVF9UT0tFTiwNCiAgICAgICAgdXBsb2FkVmlzaWJsZTpmYWxzZSwNCiAgICAgICAgdXBsb2FkRGVidHNWaXNpYmxlOmZhbHNlLA0KICAgICAgICBzdWJtaXRPcmRlckxvYWRpbmcyOiBmYWxzZSwNCiAgICAgICAgc3VibWl0T3JkZXJMb2FkaW5nMzogZmFsc2UsDQogICAgICAgIHVwbG9hZERhdGE6IHsNCiAgICAgICAgICAgIHJldmlldzpmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICB1cGxvYWREZWJ0c0RhdGE6IHsNCiAgICAgICAgICAgIHJldmlldzpmYWxzZQ0KICAgICAgICB9LA0KICAgICAgYWxsU2l6ZTogIm1pbmkiLA0KICAgICAgbGlzdFVzZXI6IFtdLA0KICAgICAgbGlzdDogW10sDQogICAgICB0b3RhbDogMSwNCiAgICAgIHBhZ2U6IDEsDQogICAgICBjdXJyZW50SWQ6MCwNCiAgICAgIGN1cnJlbnREZWJ0SWQ6MCwNCiAgICAgIHBhZ2VVc2VyOiAxLA0KICAgICAgc2l6ZVVzZXI6IDIwLA0KICAgICAgc2VhcmNoVXNlcjogew0KICAgICAgICBrZXl3b3JkOiAiIiwNCiAgICAgIH0sDQogICAgICBzaXplOiAyMCwNCiAgICAgIHNlYXJjaDogew0KICAgICAgICBrZXl3b3JkOiAiIiwNCiAgICAgICAgc3RhdHVzOiAtMSwNCiAgICAgICAgICBwcm9wOiAiIiwNCiAgICAgICAgICBvcmRlcjogIiIsDQogICAgICB9LA0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIHVybDogIi9kZWJ0LyIsDQogICAgICB1cmxVc2VyOiAiL3VzZXIvIiwNCiAgICAgIHRpdGxlOiAi5YC65YqhIiwNCiAgICAgIGluZm86IHsNCiAgICAgICAgaW1hZ2VzOltdLA0KICAgICAgICBhdHRhY2hfcGF0aDpbXSwNCiAgICAgICAgY2FyZHM6W10sDQogICAgICAgIGRlYnR0cmFuczpbXQ0KICAgICAgfSwNCiAgICAgIGRpYWxvZ1VzZXJGb3JtVmlzaWJsZTpmYWxzZSwNCiAgICAgIGRpYWxvZ1ZpZXdVc2VyRGV0YWlsOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1pmcnFWaXNpYmxlOmZhbHNlLA0KICAgICAgZGlhbG9nUmljaGFuZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nSHVpa3VhblZpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nRGVidHRyYW5zRm9ybVZpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLA0KICAgICAgdmlld0Zvcm1WaXNpYmxlOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1ZpZXdEZWJ0RGV0YWlsOiBmYWxzZSwNCiAgICAgIHNob3dfaW1hZ2U6ICIiLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBydWxlRm9ybURlYnR0cmFuczogew0KICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgfSwNCiAgICAgIHJ1bGVGb3JtOiB7DQogICAgICAgIGltYWdlczpbXSwNCiAgICAgICAgZGVsX2ltYWdlczpbXSwNCiAgICAgICAgYXR0YWNoX3BhdGg6W10sDQogICAgICAgIGRlbF9hdHRhY2hfcGF0aDpbXSwNCiAgICAgICAgY2FyZHM6W10sDQogICAgICAgIGRlYnR0cmFuczpbXQ0KICAgICAgfSwNCiAgICAgICAgcnVsZXNEZWJ0dHJhbnM6ew0KICAgICAgICAgICAgZGF5OiBbDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqei3n+i/m+aXpeacnyIsDQogICAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIHN0YXR1czogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6not5/ov5vnirbmgIEiLA0KICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIF0sDQogICAgICAgIH0sDQoNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHVpZDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeeUqOaItyIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbmFtZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+Whq+WGmeWAuuWKoeS6uuWnk+WQjSIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgICBtb25leTogWw0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7floavlhpnlgLrliqHph5Hpop0iLA0KICAgICAgICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgY2FzZV9kZXM6IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+35aGr5YaZ5qGI55SxIiwNCiAgICAgICAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIGZvcm1MYWJlbFdpZHRoOiAiMTQwcHgiLA0KICAgICAgb3B0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IC0xLA0KICAgICAgICAgIHRpdGxlOiAi6K+36YCJ5oupIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIHRpdGxlOiAi5b6F5aSE55CGIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIHRpdGxlOiAi6LCD6IqC5LitIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgIHRpdGxlOiAi6K+J6K685LitIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA0LA0KICAgICAgICAgIHRpdGxlOiAi5bey57uT5qGIIiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldERhdGEoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNoYW5nZUZpbGUoZmlsZWQpIHsNCiAgICAgIHRoaXMuZmlsZWQgPSBmaWxlZDsNCiAgICB9LA0KICAgICAgc2VhcmNoVXNlckRhdGEoKSB7DQogICAgICAgICAgdGhpcy5wYWdlVXNlciA9IDE7DQogICAgICAgICAgdGhpcy5zaXplVXNlciA9IDIwOw0KICAgICAgICAgIHRoaXMuZ2V0VXNlckRhdGEodGhpcy5ydWxlRm9ybSk7DQogICAgICB9LA0KDQogICAgICBnZXRVc2VyRGF0YShydWxlZGF0YSkgew0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgX3RoaXMucnVsZUZvcm0gPSBydWxlZGF0YTsNCiAgICAgICAgICBfdGhpcw0KICAgICAgICAgICAgICAucG9zdFJlcXVlc3QoDQogICAgICAgICAgICAgICAgICBfdGhpcy51cmxVc2VyICsgImluZGV4P3BhZ2U9IiArIF90aGlzLnBhZ2VVc2VyICsgIiZzaXplPSIgKyBfdGhpcy5zaXplVXNlciwNCiAgICAgICAgICAgICAgICAgIF90aGlzLnNlYXJjaFVzZXINCiAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICAgICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmxpc3RVc2VyID0gcmVzcC5kYXRhOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgIH0sDQogICAgdHlwZUNsaWNrKGZpbGVkKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLnJ1bGVGb3JtRGVidHRyYW5zLCd0b3RhbF9wcmljZScsJycpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywnYmFja19tb25leScsJycpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywnY29udGVudCcsJycpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybURlYnR0cmFucywncmF0ZScsJycpOw0KICAgICAgICBpZihmaWxlZCA9PSAxKXsNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgIGlmKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3BheV90eXBlJ10gPT0gMSl7DQogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IHRydWU7DQogICAgICAgICAgICB9DQogICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5kaWFsb2dIdWlrdWFuVmlzaWJsZSA9IHRydWU7DQogICAgICAgICAgICBpZih0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydwYXlfdHlwZSddICE9IDMpew0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgfSwNCiAgICBlZGl0UmF0ZU1vbmV5KCl7DQogICAgICAgIGlmKHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3JhdGUnXSA+IDAgICYmIHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ2JhY2tfbW9uZXknXSA+IDApew0KICAgICAgICAgICAgLy90aGlzLnJ1bGVGb3JtRGVidHRyYW5zLnJhdGVfbW9uZXkgPSB0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydyYXRlJ10qdGhpcy5ydWxlRm9ybURlYnR0cmFuc1snYmFja19tb25leSddOw0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm1EZWJ0dHJhbnMsJ3JhdGVfbW9uZXknLHRoaXMucnVsZUZvcm1EZWJ0dHJhbnNbJ3JhdGUnXSp0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWydiYWNrX21vbmV5J10vMTAwKTsNCiAgICAgICAgfQ0KICAgIH0sDQogICAgICBzZWxVc2VyRGF0YShjdXJyZW50Um93KSB7DQogICAgICAgIGlmKGN1cnJlbnRSb3cpew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMucnVsZUZvcm0sJ3VpZCcsY3VycmVudFJvdy5pZCk7DQogICAgICAgICAgICBpZihjdXJyZW50Um93LnBob25lKXsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwndXRlbCcsY3VycmVudFJvdy5waG9uZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZihjdXJyZW50Um93Lm5pY2tuYW1lKXsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5ydWxlRm9ybSwndW5hbWUnLGN1cnJlbnRSb3cubmlja25hbWUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgICAgICAgICB0aGlzLmRpYWxvZ1VzZXJGb3JtVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9LA0KICAgIHBheVR5cGVDbGljayhmaWxlZCkgew0KICAgICAgICBpZihmaWxlZCA9PSAyIHx8IGZpbGVkID09IDMpew0KICAgICAgICAgICAgaWYodGhpcy5ydWxlRm9ybURlYnR0cmFuc1sndHlwZSddID09IDEpew0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nUmljaGFuZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmKGZpbGVkID09IDMpew0KICAgICAgICAgICAgaWYodGhpcy5ydWxlRm9ybURlYnR0cmFuc1sndHlwZSddID09IDIpew0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nWmZycVZpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2daZnJxVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmKGZpbGVkID09IDEpew0KICAgICAgICAgICAgdGhpcy5kaWFsb2daZnJxVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgaWYodGhpcy5ydWxlRm9ybURlYnR0cmFuc1sndHlwZSddID09IDIpew0KICAgICAgICAgICAgICAgIHRoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSB0cnVlOw0KICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dIdWlrdWFuVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgfSwNCiAgICBjbGVhckRhdGEoKSB7DQogICAgICB0aGlzLnNlYXJjaCA9IHsNCiAgICAgICAga2V5d29yZDogIiIsDQogICAgICAgIHN0YXR1czogIiIsDQogICAgICAgIHByb3A6ICIiLA0KICAgICAgICBvcmRlcjogIiIsDQogICAgICB9Ow0KICAgICAgdGhpcy5nZXREYXRhKCk7DQogICAgfSwNCiAgICBlZGl0RGF0YShpZCkgew0KICAgICAgbGV0IF90aGlzID0gdGhpczsNCiAgICAgIGlmIChpZCAhPSAwKSB7DQogICAgICAgIHRoaXMuZ2V0SW5mbyhpZCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnJ1bGVGb3JtID0gew0KICAgICAgICAgICAgaW1hZ2VzOltdLA0KICAgICAgICAgICAgZGVsX2ltYWdlczpbXSwNCiAgICAgICAgICAgIGF0dGFjaF9wYXRoOltdLA0KICAgICAgICAgICAgZGVsX2F0dGFjaF9wYXRoOltdLA0KICAgICAgICAgICAgY2FyZHM6W10sDQogICAgICAgICAgICBkZWJ0dHJhbnM6W10NCiAgICAgICAgfTsNCiAgICAgIH0NCiAgICAgICAgX3RoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgICB2aWV3VXNlckRhdGEoaWQpIHsNCiAgICAgICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgICAgIGlmIChpZCAhPSAwKSB7DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudElkID0gaWQ7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgX3RoaXMuZGlhbG9nVmlld1VzZXJEZXRhaWwgPSB0cnVlOw0KICAgICAgfSwNCiAgICAgIHZpZXdEZWJ0RGF0YShpZCkgew0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgaWYgKGlkICE9IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50RGVidElkID0gaWQ7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgX3RoaXMuZGlhbG9nVmlld0RlYnREZXRhaWwgPSB0cnVlOw0KICAgICAgfSwNCiAgICBlZGl0RGVidHRyYW5zRGF0YShpZCkgew0KICAgICAgaWYgKGlkICE9IDApIHsNCiAgICAgICAgdGhpcy5nZXREZWJ0dHJhbnNJbmZvKGlkKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnVsZUZvcm1EZWJ0dHJhbnMgPSB7DQogICAgICAgICAgbmFtZTogIiIsDQogICAgICAgIH07DQogICAgICB9DQogICAgfSwNCiAgICB2aWV3RGF0YShpZCkgew0KICAgICAgaWYgKGlkICE9IDApIHsNCiAgICAgICAgdGhpcy5nZXRWaWV3KGlkKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnVsZUZvcm0gPSB7DQogICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgIGRlc2M6ICIiLA0KICAgICAgICB9Ow0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0VmlldyhpZCkgew0KICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgX3RoaXMuZ2V0UmVxdWVzdChfdGhpcy51cmwgKyAidmlldz9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgIF90aGlzLmluZm8gPSByZXNwLmRhdGE7DQogICAgICAgICAgIF90aGlzLnZpZXdGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgICAgICAgIF90aGlzLnVwbG9hZEFjdGlvbiA9ICIvYWRtaW4vdXNlci9pbXBvcnQ/aWQ9IitpZCsiJnRva2VuPSIrdGhpcy4kc3RvcmUuZ2V0dGVycy5HRVRfVE9LRU47DQogICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZywNCiAgICAgICAgICAgfSk7DQogICAgICAgICB9DQogICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRJbmZvKGlkKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgX3RoaXMuZ2V0UmVxdWVzdChfdGhpcy51cmwgKyAicmVhZD9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5ydWxlRm9ybSA9IHJlc3AuZGF0YTsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwLmRhdGEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZywNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXREZWJ0dHJhbnNJbmZvKGlkKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgX3RoaXMuZ2V0UmVxdWVzdChfdGhpcy51cmwgKyAiZGVidHRyYW5zUmVhZD9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBfdGhpcy5ydWxlRm9ybURlYnR0cmFucyA9IHJlc3AuZGF0YTsNCiAgICAgICAgICAgIF90aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICBfdGhpcy5kaWFsb2dSaWNoYW5nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgX3RoaXMuZGlhbG9nSHVpa3VhblZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICBfdGhpcy5kaWFsb2dEZWJ0dHJhbnNGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHR1aWt1YW4oaWQpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpueUs+ivt+mAgOasvj8iLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5kZWxldGVSZXF1ZXN0KHRoaXMudXJsICsgInR1aWt1YW4/aWQ9IiArIGlkKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcC5tc2csDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZywNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuWPlua2iOmAgOasviEiLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIGRlbERhdGEoaW5kZXgsIGlkKSB7DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbliKDpmaTor6Xkv6Hmga8/IiwgIuaPkOekuiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZGVsZXRlUmVxdWVzdCh0aGlzLnVybCArICJkZWxldGU/aWQ9IiArIGlkKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7DQogICAgICAgICAgICAgIHRoaXMuaW5mby5kZWJ0dHJhbnMuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5Y+W5raI5Yig6ZmkISIsDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgZGVsRGF0YURlYnQoaW5kZXgsIGlkKSB7DQogICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm5Yig6Zmk6K+l5L+h5oGvPyIsICLmj5DnpLoiLCB7DQogICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICB9KQ0KICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICB0aGlzLmRlbGV0ZVJlcXVlc3QodGhpcy51cmwgKyAiZGVsZXRlRGVidD9pZD0iICsgaWQpLnRoZW4oKHJlc3ApID0+IHsNCiAgICAgICAgICAgICBpZiAocmVzcC5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiLA0KICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsNCiAgICAgICAgICAgICAgIHRoaXMuaW5mby5kZWJ0dHJhbnMuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgICAgICB9DQogICAgICAgICAgIH0pOw0KICAgICAgICAgfSkNCiAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgbWVzc2FnZTogIuWPlua2iOWIoOmZpCEiLA0KICAgICAgICAgICB9KTsNCiAgICAgICAgIH0pOw0KICAgIH0sDQogICAgcmVmdWxzaCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygwKTsNCiAgICB9LA0KICAgIHNlYXJjaERhdGEoKSB7DQogICAgICB0aGlzLnBhZ2UgPSAxOw0KICAgICAgdGhpcy5zaXplID0gMjA7DQogICAgICB0aGlzLmdldERhdGEoKTsNCiAgICB9LA0KDQogICAgZ2V0RGF0YSgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQoNCiAgICAgIF90aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgX3RoaXMNCiAgICAgICAgLnBvc3RSZXF1ZXN0KA0KICAgICAgICAgIF90aGlzLnVybCArICJpbmRleD9wYWdlPSIgKyBfdGhpcy5wYWdlICsgIiZzaXplPSIgKyBfdGhpcy5zaXplLA0KICAgICAgICAgIF90aGlzLnNlYXJjaA0KICAgICAgICApDQogICAgICAgIC50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIF90aGlzLmxpc3QgPSByZXNwLmRhdGE7DQogICAgICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3AuY291bnQ7DQogICAgICAgICAgfQ0KICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBzYXZlRGF0YSgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICB0aGlzLiRyZWZzWyJydWxlRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlIiwgdGhpcy5ydWxlRm9ybSkudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXNwLm1zZywNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOw0KICAgICAgICAgICAgICBfdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcC5tc2csDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAgIHNhdmVEZWJ0dHJhbnNEYXRhKCkgew0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm1EZWJ0dHJhbnMiXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtRGVidHRyYW5zWyd0b2tlbiddID0gc3RvcmUuZ2V0dGVycy5HRVRfVE9LRU47DQogICAgICAgICAgICAgICAgICB0aGlzLnBvc3RSZXF1ZXN0KF90aGlzLnVybCArICJzYXZlRGVidHRyYW5zIiwgdGhpcy5ydWxlRm9ybURlYnR0cmFucykudGhlbigocmVzcCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmRpYWxvZ1pmcnFWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmRpYWxvZ1JpY2hhbmdWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0h1aWt1YW5WaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLmRpYWxvZ0RlYnR0cmFuc0Zvcm1WaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3AubXNnLA0KICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5zaXplID0gdmFsOw0KDQogICAgICB0aGlzLmdldERhdGEoKTsNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLnBhZ2UgPSB2YWw7DQogICAgICB0aGlzLmdldERhdGEoKTsNCiAgICB9LA0KICAgIGhhbmRsZVN1Y2Nlc3MocmVzKSB7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5LiK5Lyg5oiQ5YqfIik7DQogICAgICAgIHZhciBhcnIgPSB0aGlzLnJ1bGVGb3JtW3RoaXMuZmlsZWRdOw0KDQogICAgICAgICAgdGhpcy5ydWxlRm9ybVt0aGlzLmZpbGVkXS5zcGxpY2UoMSwgMCxyZXMuZGF0YS51cmwpOw0KICAgICAgICAgIC8vdGhpcy5ydWxlRm9ybVt0aGlzLmZpbGVkXS5wdXNoID0gcmVzLmRhdGEudXJsOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgc2hvd0ltYWdlKGZpbGUpIHsNCiAgICAgIHRoaXMuc2hvd19pbWFnZSA9IGZpbGU7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAgIHNob3dVc2VyTGlzdCgpIHsNCiAgICAgICAgICB0aGlzLnNlYXJjaFVzZXJEYXRhKCk7DQogICAgICAgICAgdGhpcy5kaWFsb2dVc2VyRm9ybVZpc2libGUgPSB0cnVlOw0KICAgICAgfSwNCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNUeXBlVHJ1ZSA9IC9eaW1hZ2VcLyhqcGVnfHBuZ3xqcGcpJC8udGVzdChmaWxlLnR5cGUpOw0KICAgICAgaWYgKCFpc1R5cGVUcnVlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS4iuS8oOWbvueJh+agvOW8j+S4jeWvuSEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgIH0sDQogICAgZGVsSW1hZ2UoZmlsZSwgZmlsZU5hbWUsaW5kZXgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBfdGhpcy5nZXRSZXF1ZXN0KCIvVXBsb2FkL2RlbEltYWdlP2ZpbGVOYW1lPSIgKyBmaWxlKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgIGlmIChyZXNwLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgX3RoaXMucnVsZUZvcm1bZmlsZU5hbWVdLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgICAgX3RoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfISIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKHJlc3AubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAgIGhhbmRsZVNvcnRDaGFuZ2UoeyBjb2x1bW4sIHByb3AsIG9yZGVyIH0pIHsNCiAgICAgICAgICB0aGlzLnNlYXJjaC5wcm9wID0gcHJvcDsNCiAgICAgICAgICB0aGlzLnNlYXJjaC5vcmRlciA9IG9yZGVyOw0KICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOw0KICAgICAgICAgIC8vIOagueaNriBjb2x1bW4sIHByb3AsIG9yZGVyIOadpeabtOaWsOS9oOeahOaVsOaNruaOkuW6jw0KICAgICAgICAgIC8vIOS+i+Wmgu+8jOS9oOWPr+S7peWPkemAgeS4gOS4quivt+axguWIsOacjeWKoeWZqOadpeiOt+WPluaOkuW6j+WQjueahOaVsOaNrg0KICAgICAgfSwNCiAgICAgIGV4cG9ydHM6ZnVuY3Rpb24gKCkgeyAvL+WvvOWHuuihqOagvA0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgbG9jYXRpb24uaHJlZiA9ICIvYWRtaW4vZGVidC92aWV3P3Rva2VuPSIrX3RoaXMuJHN0b3JlLmdldHRlcnMuR0VUX1RPS0VOKyImZXhwb3J0PTEmaWQ9IitfdGhpcy5ydWxlRm9ybS5pZDsNCiAgICAgIH0sDQogICAgICBleHBvcnRzRGVidExpc3Q6ZnVuY3Rpb24gKCkgeyAvL+WvvOWHuuihqOagvA0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgbG9jYXRpb24uaHJlZiA9ICIvYWRtaW4vZGVidC9leHBvcnRMaXN0P3Rva2VuPSIgKyBfdGhpcy4kc3RvcmUuZ2V0dGVycy5HRVRfVE9LRU4gKyAiJmtleXdvcmQ9IiArIF90aGlzLnNlYXJjaC5rZXl3b3JkOw0KICAgICAgfSwNCiAgICAgIGNsb3NlVXBsb2FkRGlhbG9nKCkgeyAvL+WFs+mXreeql+WPow0KICAgICAgICAgIHRoaXMudXBsb2FkVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgICAgICB0aGlzLnVwbG9hZERhdGEucmV2aWV3ID0gZmFsc2U7DQogICAgICB9LA0KICAgICAgY2xvc2VVcGxvYWREZWJ0c0RpYWxvZygpIHsgLy/lhbPpl63nqpflj6MNCiAgICAgICAgICB0aGlzLnVwbG9hZERlYnRzVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgICAgICB0aGlzLnVwbG9hZERlYnRzRGF0YS5yZXZpZXcgPSBmYWxzZTsNCiAgICAgIH0sDQogICAgICB1cGxvYWRTdWNjZXNzKHJlc3BvbnNlKSB7IC8v5a+85YWl5a6M5oiQ5Zue6LCDDQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIHR5cGU6J3N1Y2Nlc3MnLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLnVwbG9hZFZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlKTsNCiAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICB0eXBlOid3YXJuaW5nJywNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLm1zZw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLnN1Ym1pdE9yZGVyTG9hZGluZzIgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7DQogICAgICB9LA0KICAgICAgdXBsb2FkRGVidHNTdWNjZXNzKHJlc3BvbnNlKSB7IC8v5a+85YWl5a6M5oiQ5Zue6LCDDQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIHR5cGU6J3N1Y2Nlc3MnLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLnVwbG9hZERlYnRzVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIHR5cGU6J3dhcm5pbmcnLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHRoaXMuc3VibWl0T3JkZXJMb2FkaW5nMyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgIH0sDQogICAgICBjaGVja0ZpbGUoZmlsZSkgeyAvL+WvvOWFpeWJjeagoemqjOaWh+S7tuWQjue8gA0KICAgICAgICAgIGxldCBmaWxlVHlwZSA9IFsneGxzJywgJ3hsc3gnXTsNCiAgICAgICAgICBsZXQgdHlwZSA9IGZpbGUubmFtZS5zcGxpdCgnLicpLnNsaWNlKC0xKVswXS50b0xvd2VyQ2FzZSgpOw0KICAgICAgICAgIGlmICghZmlsZVR5cGUuaW5jbHVkZXModHlwZSkpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICB0eXBlOiJ3YXJuaW5nIiwNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IuaWh+S7tuagvOW8j+mUmeivr+S7heaUr+aMgSB4bHMgeGx4cyDmlofku7YiDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfSwNCiAgICAgIHN1Ym1pdFVwbG9hZCgpIHsgLy/lr7zlhaXmj5DkuqQNCiAgICAgICAgICB0aGlzLnN1Ym1pdE9yZGVyTG9hZGluZzIgPSB0cnVlOw0KICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOw0KICAgICAgfSwNCiAgICAgIHN1Ym1pdFVwbG9hZERlYnRzKCkgeyAvL+WvvOWFpeaPkOS6pA0KICAgICAgICAgIHRoaXMuc3VibWl0T3JkZXJMb2FkaW5nMyA9IHRydWU7DQogICAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7DQogICAgICB9LA0KICAgICAgY2xvc2VEaWFsb2coKSB7IC8v5YWz6Zet56qX5Y+jDQogICAgICAgICAgdGhpcy5hZGRWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy51cGxvYWRWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgICAgICBpZDonJywNCiAgICAgICAgICAgICAgbmlja25hbWU6IiIsDQogICAgICAgICAgICAgIG1vYmlsZToiIiwNCiAgICAgICAgICAgICAgc2Nob29sX2lkOjAsDQogICAgICAgICAgICAgIGdyYWRlX2lkOicnLA0KICAgICAgICAgICAgICBjbGFzc19pZDonJywNCiAgICAgICAgICAgICAgc2V4OicnLA0KICAgICAgICAgICAgICBpc19wb29yOicnLA0KICAgICAgICAgICAgICBpc19kaXNwbGF5OicnLA0KICAgICAgICAgICAgICBudW1iZXI6JycsDQogICAgICAgICAgICAgIHJlbWFyazonJywNCiAgICAgICAgICAgICAgaXNfcmVtYXJrX29wdGlvbjowLA0KICAgICAgICAgICAgICByZW1hcmtfb3B0aW9uOltdLA0KICAgICAgICAgICAgICBtb2JpbGVfY2hlY2tlZDpmYWxzZSwNCiAgICAgICAgICB9Ow0KICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOw0KICAgICAgfSwNCiAgICAgIG9wZW5VcGxvYWQoKSB7IC8v5omT5byA5a+85YWl5by556qXDQogICAgICAgICAgdGhpcy51cGxvYWRWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIH0sDQogICAgICBvcGVuVXBsb2FkRGVidHMoKSB7IC8v5omT5byA5a+85YWl5by556qXDQogICAgICAgICAgdGhpcy51cGxvYWREZWJ0c1Zpc2libGUgPSB0cnVlOw0KICAgICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["debts.vue"], "names": [], "mappings": ";AA4nBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debts.vue", "sourceRoot": "src/views/pages/debt", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card shadow=\"always\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>{{ this.$router.currentRoute.name }}</span>\r\n        <el-button\r\n          style=\"float: right; padding: 3px 0\"\r\n          type=\"text\"\r\n          @click=\"refulsh\"\r\n          >刷新</el-button\r\n        >\r\n      </div>\r\n      <el-row>\r\n        <el-col :span=\"4\">\r\n          <el-input\r\n            placeholder=\"请输入用户姓名，债务人的名字，手机号\"\r\n            v-model=\"search.keyword\"\r\n            :size=\"allSize\"\r\n          >\r\n          </el-input>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-select\r\n            v-model=\"search.status\"\r\n            placeholder=\"请选择\"\r\n            :size=\"allSize\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"getData()\" :size=\"allSize\">搜索</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1\">\r\n          <el-button @click=\"clearData()\" :size=\"allSize\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row class=\"page-top\">\r\n        <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n        >新增</el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exportsDebtList\">\r\n              导出列表\r\n          </el-button>\r\n          <el-button style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" icon=\"el-icon-bottom\"\r\n                     @click=\"openUploadDebts\">导入债务人\r\n          </el-button>\r\n          <a href=\"/import_templete/debt_person.xls\"\r\n             style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>\r\n      </el-row>\r\n\r\n      <el-table\r\n        :data=\"list\"\r\n        style=\"width: 100%; margin-top: 10px\"\r\n        v-loading=\"loading\"\r\n        size=\"mini\"\r\n        @sort-change=\"handleSortChange\"\r\n      >\r\n        <el-table-column prop=\"nickname\" label=\"用户姓名\">\r\n            <template slot-scope=\"scope\"><div @click=\"viewUserData(scope.row.uid)\">{{scope.row.users.nickname}}</div></template>\r\n        </el-table-column>\r\n          <el-table-column prop=\"name\" label=\"债务人姓名\">\r\n              <template slot-scope=\"scope\">\r\n                  <div @click=\"viewDebtData(scope.row.id)\">{{scope.row.name}}</div>\r\n              </template>\r\n          </el-table-column>\r\n        <el-table-column prop=\"tel\" label=\"债务人电话\"> </el-table-column>\r\n        <el-table-column prop=\"money\" label=\"债务金额（元）\"> </el-table-column>\r\n        <el-table-column prop=\"status\" label=\"状态\"> </el-table-column>\r\n        <el-table-column prop=\"back_money\" label=\"合计回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"un_money\" label=\"未回款（元）\"> </el-table-column>\r\n        <el-table-column prop=\"ctime\" label=\"提交时间\" sortable> </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\">\r\n          <template slot-scope=\"scope\">\r\n<!--            <el-button type=\"text\" size=\"small\" @click=\"viewDebtData(scope.row.id)\"-->\r\n<!--              >查看</el-button-->\r\n<!--            >-->\r\n            <el-button type=\"text\" size=\"small\" @click=\"editData(scope.row.id)\"\r\n              >编辑</el-button\r\n            >\r\n              <el-button type=\"text\" size=\"small\" @click=\"editDebttransData(scope.row.id)\"\r\n              >跟进</el-button\r\n              >\r\n              <el-button type=\"text\" size=\"small\" @click=\"delDataDebt(scope.$indexs,scope.row.id)\"\r\n              >删除</el-button\r\n              >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"page-top\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[20, 100, 200, 300, 400]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n    <el-dialog\r\n      title=\"债务管理\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"80%\">\r\n        <div v-if=\"ruleForm.is_user == 1\">\r\n            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>\r\n            <!--<el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"openUpload\">导入跟进记录</el-button>-->\r\n            <!--<a href=\"/import_templete/user.xls\" style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>-->\r\n        </div>\r\n        <el-descriptions title=\"债务信息\" v-if=\"ruleForm.is_user == 1\">\r\n            <el-descriptions-item label=\"用户姓名\">{{ruleForm.nickname}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"债务人姓名\">{{ruleForm.name}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"债务人电话\">{{ruleForm.tel}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"债务人地址\">{{ruleForm.address}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"债务金额\">{{ruleForm.money}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"合计回款\">{{ruleForm.back_money}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"未回款\">{{ruleForm.un_money}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"提交时间\">{{ruleForm.ctime}}</el-descriptions-item>\r\n            <el-descriptions-item label=\"最后一次修改时间\">{{ruleForm.utime}}</el-descriptions-item>\r\n        </el-descriptions>\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\r\n          <el-form-item label=\"选择用户\" :label-width=\"formLabelWidth\" @click.native=\"showUserList()\" v-if=\"ruleForm.is_user != 1\">\r\n              <el-button type=\"primary\" @click=\"editData(0)\" :size=\"allSize\"\r\n              >选择用户</el-button\r\n              >\r\n          </el-form-item>\r\n          <el-form-item label=\"用户信息\" :label-width=\"formLabelWidth\" v-if=\"ruleForm.utel\">\r\n              {{ruleForm.uname}}<div style=\"margin-left:10px;\">{{ruleForm.utel}}</div>\r\n          </el-form-item>\r\n          <el-form-item label=\"债务人姓名\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.name\" autocomplete=\"off\"></el-input>\r\n          </el-form-item>\r\n        <el-form-item\r\n          label=\"债务人身份证\"\r\n          :label-width=\"formLabelWidth\"\r\n          prop=\"cards\"\r\n        >\r\n          <el-button-group>\r\n            <el-button @click=\"changeFile('cards')\">\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadFile\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n              >\r\n                上传\r\n              </el-upload>\r\n            </el-button>\r\n          </el-button-group>\r\n        </el-form-item>\r\n          <div style=\"width: 100%;display: table-cell;\" v-if=\"ruleForm.cards[0]\">\r\n              <div style=\"float: left;margin-left:2px;\"\r\n                   v-for=\"(item7, index7) in ruleForm.cards\"\r\n                   :key=\"index7\"\r\n                   class=\"image-list\"\r\n              >\r\n                  <img :src=\"item7\" style=\"width: 100px; height: 100px\" @click=\"showImage(item7)\" mode=\"aspectFit\" />\r\n                  <el-button\r\n                          type=\"danger\"\r\n                          v-if=\"item7\"\r\n                          @click=\"delImage(item7, 'cards',index7)\"\r\n                  >删除</el-button\r\n                  >\r\n              </div>\r\n          </div>\r\n          <el-form-item label=\"债务人身份证号码\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.idcard_no\" autocomplete=\"off\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"债务人电话\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.tel\" autocomplete=\"off\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"债务人地址\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.address\" autocomplete=\"off\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"债务金额\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.money\" autocomplete=\"off\"></el-input>元\r\n          </el-form-item>\r\n          <el-form-item label=\"案由\" :label-width=\"formLabelWidth\">\r\n              <el-input v-model=\"ruleForm.case_des\"\r\n                      autocomplete=\"off\"\r\n                      type=\"textarea\"\r\n                      :rows=\"4\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item\r\n                  label=\"上传证据图片\"\r\n                  :label-width=\"formLabelWidth\"\r\n                  prop=\"images\">\r\n              <el-button-group>\r\n                  <el-button @click=\"changeFile('images')\">\r\n                      <el-upload\r\n                              action=\"/admin/Upload/uploadFile\"\r\n                              :show-file-list=\"false\"\r\n                              :on-success=\"handleSuccess\">\r\n                          上传\r\n                      </el-upload>\r\n                  </el-button>\r\n              </el-button-group>\r\n          </el-form-item>\r\n          <div style=\"width: 100%;display: table-cell;\" v-if=\"ruleForm.images[0]\">\r\n              <div style=\"float: left;margin-left:2px;\"\r\n                   v-for=\"(item5, index5) in ruleForm.images\"\r\n                   :key=\"index5\"\r\n                   class=\"image-list\">\r\n                  <!--<img :src=\"item5\" style=\"width: 100px; height: 100px\" @click=\"showImage(item5)\" mode=\"aspectFit\" />-->\r\n                  <el-image\r\n                          style=\"width: 100px; height: 100px\"\r\n                          :src=\"item5\"\r\n                          :preview-src-list=\"ruleForm.images\">\r\n                  </el-image>\r\n                  <!--:src=\"'http://localhost:8000'+item5\"-->\r\n                  <a style=\"\" :href=\"item5\" target=\"_blank\" :download=\"'evidence.'+item5.split('.')[1]\">下载</a>\r\n                  <el-button\r\n                          type=\"danger\"\r\n                          v-if=\"item5\"\r\n                          @click=\"delImage(item5, 'images',index5)\">删除</el-button>\r\n              </div>\r\n          </div>\r\n          <br/>\r\n          <div v-if=\"ruleForm.del_images[0]\">以下为用户删除的图片</div>\r\n          <div style=\"width: 100%;display: table-cell;\" v-if=\"ruleForm.del_images[0]\">\r\n              <div style=\"float: left;margin-left:2px;\"\r\n                   v-for=\"(item8, index8) in ruleForm.del_images\"\r\n                   :key=\"index8\"\r\n                   class=\"image-list\">\r\n                  <!--<img :src=\"item8\" style=\"width: 100px; height: 100px\" @click=\"showImage(item8)\" mode=\"aspectFit\" />-->\r\n                  <el-image\r\n                          style=\"width: 100px; height: 100px\"\r\n                          :src=\"item8\"\r\n                          :preview-src-list=\"ruleForm.del_images\">\r\n                  </el-image>\r\n                  <el-button\r\n                          type=\"danger\"\r\n                          v-if=\"item8\"\r\n                          @click=\"delImage(item8, 'del_images',index8)\">删除</el-button>\r\n              </div>\r\n          </div>\r\n          <el-form-item\r\n                  label=\"上传证据文件\"\r\n                  :label-width=\"formLabelWidth\"\r\n                  prop=\"attach_path\">\r\n              <el-button-group>\r\n                  <el-button @click=\"changeFile('attach_path')\">\r\n                      <el-upload\r\n                              action=\"/admin/Upload/uploadFile\"\r\n                              :show-file-list=\"false\"\r\n                              :on-success=\"handleSuccess\">\r\n                          上传\r\n                      </el-upload>\r\n                  </el-button>\r\n              </el-button-group>\r\n          </el-form-item>\r\n          <div style=\"width: 100%;display: table-cell;\" v-if=\"ruleForm.attach_path[0]\">\r\n              <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n                  <div\r\n                          v-for=\"(item6, index6) in ruleForm.attach_path\"\r\n                          :key=\"index6\">\r\n                      <div v-if=\"item6\">\r\n                          <div >文件{{ index6 +1 }}<a style=\"margin-left: 10px;\" :href=\"item6\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item6\" target=\"_blank\">下载</a>\r\n                              <el-button\r\n                                      type=\"danger\"\r\n                                      v-if=\"item6\"\r\n                                      @click=\"delImage(item6, 'attach_path',index6)\"\r\n                              >移除</el-button></div><br />\r\n                      </div>\r\n                  </div>\r\n              </div>\r\n          </div>\r\n          <br/>\r\n          <div v-if=\"ruleForm.del_attach_path[0]\">以下为用户删除的文件</div>\r\n          <div style=\"width: 100%;display: table-cell;\" v-if=\"ruleForm.del_attach_path[0]\">\r\n              <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n                  <div\r\n                          v-for=\"(item9, index9) in ruleForm.del_attach_path\"\r\n                          :key=\"index9\"\r\n                  >\r\n                      <div v-if=\"item9\">\r\n                          <div >文件{{ index9 +1 }}<a style=\"margin-left: 10px;\" :href=\"item9\" target=\"_blank\">查看</a>\r\n                              <el-button\r\n                                      type=\"danger\"\r\n                                      v-if=\"item9\"\r\n                                      @click=\"delImage(item9, 'del_attach_path',index9)\"\r\n                              >移除</el-button\r\n                              ></div><br />\r\n                      </div>\r\n                  </div>\r\n              </div>\r\n          </div>\r\n      </el-form>\r\n\r\n        <el-descriptions title=\"跟进记录\" :colon=\"false\" v-if=\"ruleForm.is_user == 1\">\r\n            <el-descriptions-item>\r\n                <el-table\r\n                        :data=\"ruleForm.debttrans\"\r\n                        style=\"width: 100%; margin-top: 10px\"\r\n                        v-loading=\"loading\"\r\n                        size=\"mini\"\r\n                >\r\n                    <el-table-column prop=\"day\" label=\"跟进日期\"> </el-table-column>\r\n                    <el-table-column prop=\"ctime\" label=\"提交时间\"> </el-table-column>\r\n                    <el-table-column prop=\"au_id\" label=\"操作人员\"> </el-table-column>\r\n                    <el-table-column prop=\"type\" label=\"进度类型\"> </el-table-column>\r\n                    <el-table-column prop=\"total_price\" label=\"费用金额/手续费\"> </el-table-column>\r\n                    <el-table-column prop=\"content\" label=\"费用内容\"> </el-table-column>\r\n                    <el-table-column prop=\"rate\" label=\"手续费比率\"></el-table-column>\r\n                    <el-table-column prop=\"back_money\" label=\"回款金额\"> </el-table-column>\r\n                    <el-table-column prop=\"pay_type\" label=\"支付状态\"> </el-table-column>\r\n                    <el-table-column prop=\"pay_time\" label=\"支付时间\"> </el-table-column>\r\n                    <el-table-column prop=\"pay_order_type\" label=\"支付方式\"> </el-table-column>\r\n                    <el-table-column prop=\"desc\" label=\"进度描述\"> </el-table-column>\r\n                    <el-table-column fixed=\"right\" label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button\r\n                                    @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                                    type=\"text\"\r\n                                    size=\"small\"\r\n                            >\r\n                                移除\r\n                            </el-button>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table></el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n      <el-dialog\r\n              title=\"用户列表\"\r\n              :visible.sync=\"dialogUserFormVisible\"\r\n              :close-on-click-modal=\"false\"\r\n              width=\"70%\">\r\n\r\n          <el-row style=\"width: 300px\">\r\n              <el-input placeholder=\"请输入内容\" v-model=\"searchUser.keyword\" size=\"mini\">\r\n                  <el-button\r\n                          slot=\"append\"\r\n                          icon=\"el-icon-search\"\r\n                          @click=\"searchUserData()\"\r\n                  ></el-button>\r\n              </el-input>\r\n          </el-row>\r\n\r\n          <el-table\r\n                  :data=\"listUser\"\r\n                  style=\"width: 100%; margin-top: 10px\"\r\n                  size=\"mini\"\r\n                  @current-change=\"selUserData\"\r\n          >\r\n              <el-table-column label=\"选择\">\r\n                  <template slot-scope=\"scope\">\r\n                      <el-radio v-model=\"ruleForm.user_id\" :label=\"scope.$index\">&nbsp; </el-radio>\r\n                  </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"phone\" label=\"注册手机号码\"> </el-table-column>\r\n              <el-table-column prop=\"nickname\" label=\"名称\"> </el-table-column>\r\n              <el-table-column prop=\"\" label=\"头像\">\r\n                  <template slot-scope=\"scope\">\r\n                      <div>\r\n\r\n                          <el-row v-if=\"scope.row.headimg==''\">\r\n                              <!--img     style=\"width: 50px; height: 50px\" src=\"../../../../dist/img/insert_img.png\"/-->\r\n                          </el-row>\r\n                          <el-row v-else>\r\n                              <img     style=\"width: 50px; height: 50px\" :src=\"scope.row.headimg\"/>\r\n                          </el-row>\r\n\r\n                      </div>\r\n                  </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"linkman\" label=\"联系人\"> </el-table-column>\r\n              <el-table-column prop=\"linkphone\" label=\"联系号码\"> </el-table-column>\r\n              <el-table-column prop=\"yuangong_id\" label=\"用户来源\"> </el-table-column>\r\n              <el-table-column prop=\"end_time\" label=\"到期时间\"> </el-table-column>\r\n              <el-table-column prop=\"create_time\" label=\"录入时间\"> </el-table-column>\r\n          </el-table>\r\n\r\n      </el-dialog>\r\n    <el-dialog\r\n            title=\"跟进\"\r\n            :visible.sync=\"dialogDebttransFormVisible\"\r\n            :close-on-click-modal=\"false\"\r\n            width=\"70%\"\r\n    >\r\n      <el-form :model=\"ruleFormDebttrans\" :rules=\"rulesDebttrans\" ref=\"ruleFormDebttrans\">\r\n        <el-form-item label=\"跟进日期\" :label-width=\"formLabelWidth\" prop=\"day\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进状态\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"1\" @click.native=\"debtStatusClick('2')\">待处理</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"2\" @click.native=\"debtStatusClick('2')\">调节中</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"3\" @click.native=\"debtStatusClick('1')\">转诉讼</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"4\" @click.native=\"debtStatusClick('2')\">已结案</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.status\" :label=\"5\" @click.native=\"debtStatusClick('2')\">已取消</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跟进类型\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"1\" @click.native=\"typeClick('1')\">日常</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.type\" :label=\"2\" @click.native=\"typeClick('2')\">回款</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"支付费用\" :label-width=\"formLabelWidth\">\r\n            <div>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"1\" @click.native=\"payTypeClick('1')\">无需支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"2\" @click.native=\"payTypeClick('2')\">待支付</el-radio>\r\n                <el-radio v-model=\"ruleFormDebttrans.pay_type\" :label=\"3\" @click.native=\"payTypeClick('3')\">已支付</el-radio>\r\n            </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用金额\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.total_price\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"费用内容\" :label-width=\"formLabelWidth\" v-show=\"dialogRichangVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogHuikuanVisible\">\r\n            <el-date-picker\r\n                    v-model=\"ruleFormDebttrans.back_day\"\r\n                    type=\"date\"\r\n                    format=\"yyyy-MM-dd\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                    placeholder=\"选择日期\"\r\n            >\r\n            </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"回款金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.back_money\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>元\r\n        </el-form-item>\r\n        <el-form-item label=\"手续费金额\" :label-width=\"formLabelWidth\" v-show=\"dialogHuikuanVisible\">\r\n          <el-input v-model=\"ruleFormDebttrans.rate\" autocomplete=\"off\" @input=\"editRateMoney()\"></el-input>%\r\n          <el-input v-model=\"ruleFormDebttrans.rate_money\" autocomplete=\"off\"></el-input>元\r\n        </el-form-item>\r\n          <el-form-item label=\"支付日期\" :label-width=\"formLabelWidth\" prop=\"day\" v-show=\"dialogZfrqVisible\">\r\n              <el-date-picker\r\n                      v-model=\"ruleFormDebttrans.pay_time\"\r\n                      type=\"date\"\r\n                      format=\"yyyy-MM-dd\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                      placeholder=\"选择日期\"\r\n              >\r\n              </el-date-picker>\r\n          </el-form-item>\r\n        <el-form-item\r\n                label=\"进度描述\"\r\n                :label-width=\"formLabelWidth\"\r\n        >\r\n          <el-input\r\n                  v-model=\"ruleFormDebttrans.desc\"\r\n                  autocomplete=\"off\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogDebttransFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveDebttransData()\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-image :src=\"show_image\"></el-image>\r\n    </el-dialog>\r\n\t\t<el-dialog title=\"债务查看\" :visible.sync=\"dialogViewDebtDetail\" :close-on-click-modal=\"false\" width=\"80%\">\r\n\r\n            <debt-detail :id=\"currentDebtId\"></debt-detail>\r\n\r\n<!--            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"exports\">导出跟进记录</el-button>-->\r\n<!--            &lt;!&ndash;<el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" @click=\"openUpload\">导入跟进记录</el-button>-->\r\n<!--            <a href=\"/import_templete/user.xls\" style=\"text-decoration:none;color: #4397fd;font-weight: 800;margin-left:10px;\">下载导入模板</a>&ndash;&gt;-->\r\n<!--\t\t\t\t<el-descriptions title=\"债务信息\">-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"用户姓名\">{{info.nickname}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人姓名\">{{info.name}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人电话\">{{info.tel}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务人地址\">{{info.address}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"债务金额\">{{info.money}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"合计回款\">{{info.back_money}}</el-descriptions-item>-->\r\n<!--\t\t\t\t\t<el-descriptions-item label=\"未回款\">{{info.un_money}}</el-descriptions-item>-->\r\n<!--                    <el-descriptions-item label=\"提交时间\">{{info.ctime}}</el-descriptions-item>-->\r\n<!--                    <el-descriptions-item label=\"最后一次修改时间\">{{info.utime}}</el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"债务人身份信息\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item><div style=\"width: 100%;display: table-cell;\" v-if=\"info.cards[0]\">-->\r\n<!--                    <div style=\"float: left;margin-left:2px;\"-->\r\n<!--                         v-for=\"(item4, index4) in info.cards\"-->\r\n<!--                         :key=\"index4\"-->\r\n<!--                         class=\"image-list\"-->\r\n<!--                    >-->\r\n<!--                      <img :src=\"item4\" style=\"width: 100px; height: 100px\" @click=\"showImage(item4)\" mode=\"aspectFit\" />-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"案由\" :colon=\"false\">-->\r\n<!--\t\t\t\t\t<el-descriptions-item>{{info.case_des}}</el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"证据图片\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item> <div style=\"width: 100%;display: table-cell;\" v-if=\"info.images[0]\">-->\r\n<!--                    <div style=\"float: left;margin-left:2px;\"-->\r\n<!--                         v-for=\"(item2, index2) in info.images\"-->\r\n<!--                         :key=\"index2\"-->\r\n<!--                         class=\"image-list\"-->\r\n<!--                    >-->\r\n<!--                      &lt;!&ndash;<img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />&ndash;&gt;-->\r\n<!--                        <el-image-->\r\n<!--                                style=\"width: 100px; height: 100px\"-->\r\n<!--                                :src=\"item2\"-->\r\n<!--                                :preview-src-list=\"info.images\">-->\r\n<!--                        </el-image>-->\r\n<!--                        <a style=\"\" :href=\"item2\" target=\"_blank\" :download=\"'evidence.'+item2.split('.')[1]\">下载</a>-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--\t\t\t\t<el-descriptions title=\"证据文件\" v-if=\"info.attach_path[0]\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item><div style=\"width: 100%;display: table-cell;line-height:20px;\">-->\r\n<!--                    <div-->\r\n<!--                            v-for=\"(item3, index3) in info.attach_path\"-->\r\n<!--                            :key=\"index3\"-->\r\n<!--                    >-->\r\n<!--                      <div v-if=\"item3\">-->\r\n<!--                        <div >文件{{ index3 + 1 + '->' + item3.split(\".\")[1] }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">下载</a></div><br />-->\r\n<!--                      </div>-->\r\n<!--                    </div>-->\r\n<!--                  </div></el-descriptions-item>-->\r\n<!--\t\t\t\t</el-descriptions>-->\r\n<!--                <el-descriptions title=\"跟进记录\" :colon=\"false\">-->\r\n<!--                  <el-descriptions-item>-->\r\n<!--                  <el-table-->\r\n<!--                          :data=\"info.debttrans\"-->\r\n<!--                          style=\"width: 100%; margin-top: 10px\"-->\r\n<!--                          v-loading=\"loading\"-->\r\n<!--                          size=\"mini\"-->\r\n<!--                  >-->\r\n<!--                    <el-table-column prop=\"day\" label=\"跟进日期\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"ctime\" label=\"提交时间\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"au_id\" label=\"操作人员\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"type\" label=\"进度类型\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"total_price\" label=\"费用金额/手续费\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"content\" label=\"费用内容\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"rate\" label=\"手续费比率\"></el-table-column>-->\r\n<!--                    <el-table-column prop=\"back_money\" label=\"回款金额\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_type\" label=\"支付状态\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_time\" label=\"支付时间\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"pay_order_type\" label=\"支付方式\"> </el-table-column>-->\r\n<!--                    <el-table-column prop=\"desc\" label=\"进度描述\"> </el-table-column>-->\r\n<!--                    <el-table-column fixed=\"right\" label=\"操作\">-->\r\n<!--                      <template slot-scope=\"scope\">-->\r\n<!--                        <el-button-->\r\n<!--                                @click.native.prevent=\"delData(scope.$index, scope.row.id)\"-->\r\n<!--                                type=\"text\"-->\r\n<!--                                size=\"small\"-->\r\n<!--                        >-->\r\n<!--                          移除-->\r\n<!--                        </el-button>-->\r\n<!--                      </template>-->\r\n<!--                    </el-table-column>-->\r\n<!--                  </el-table></el-descriptions-item>-->\r\n<!--                </el-descriptions>-->\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"dialogViewDebtDetail = false\">取 消</el-button>\r\n            </div>\r\n\t\t</el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入跟进记录\" :visible.sync=\"uploadVisible\" width=\"30%\" @close=\"closeUploadDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadAction\"\r\n                          :data=\"uploadData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUpload\" :loading=\"submitOrderLoading2\">提交</el-button>\r\n                  <el-button @click=\"closeDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <!--导入-->\r\n      <el-dialog title=\"导入债权人\" :visible.sync=\"uploadDebtsVisible\" width=\"30%\" @close=\"closeUploadDebtsDialog\">\r\n          <el-form ref=\"uploadForm\" label-position=\"right\" label-width=\"110px\">\r\n              <el-form-item label=\"选择文件:\">\r\n                  <el-upload\r\n                          ref=\"upload\"\r\n                          :auto-upload=\"false\"\r\n                          :action=\"uploadDebtsAction\"\r\n                          :data=\"uploadDebtsData\"\r\n                          :on-success=\"uploadSuccess\"\r\n                          :before-upload=\"checkFile\"\r\n                          accept=\".xls,.xlsx\"\r\n                          limit=\"1\"\r\n                          multiple=\"false\">\r\n                      <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\r\n                  </el-upload>\r\n              </el-form-item>\r\n\r\n              <div style=\"text-align: right\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"submitUploadDebts\" :loading=\"submitOrderLoading3\">提交</el-button>\r\n                  <el-button @click=\"closeUploadDebtsDialog\" size=\"small\">取消</el-button>\r\n              </div>\r\n          </el-form>\r\n      </el-dialog>\r\n      <el-dialog\r\n              :title=\"用户详情\"\r\n              :visible.sync=\"dialogViewUserDetail\"\r\n              :close-on-click-modal=\"false\"  width=\"80%\"\r\n      >\r\n          <user-details :id=\"currentId\"></user-details>\r\n\r\n      </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\nimport UserDetails from \"/src/components/UserDetail.vue\";\r\nimport DebtDetail from \"/src/components/DebtDetail.vue\";\r\nimport store from \"../../../store\";\r\nexport default {\r\n  name: \"list\",\r\n  components: { UserDetails,DebtDetail },\r\n  data() {\r\n    return {\r\n        uploadAction:'',\r\n        uploadDebtsAction: \"/admin/debt/importDebts?token=\" + this.$store.getters.GET_TOKEN,\r\n        uploadVisible:false,\r\n        uploadDebtsVisible:false,\r\n        submitOrderLoading2: false,\r\n        submitOrderLoading3: false,\r\n        uploadData: {\r\n            review:false\r\n        },\r\n        uploadDebtsData: {\r\n            review:false\r\n        },\r\n      allSize: \"mini\",\r\n      listUser: [],\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      currentId:0,\r\n      currentDebtId:0,\r\n      pageUser: 1,\r\n      sizeUser: 20,\r\n      searchUser: {\r\n        keyword: \"\",\r\n      },\r\n      size: 20,\r\n      search: {\r\n        keyword: \"\",\r\n        status: -1,\r\n          prop: \"\",\r\n          order: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/debt/\",\r\n      urlUser: \"/user/\",\r\n      title: \"债务\",\r\n      info: {\r\n        images:[],\r\n        attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n      dialogUserFormVisible:false,\r\n      dialogViewUserDetail: false,\r\n      dialogZfrqVisible:false,\r\n      dialogRichangVisible: false,\r\n      dialogHuikuanVisible: false,\r\n      dialogDebttransFormVisible: false,\r\n      dialogFormVisible: false,\r\n      viewFormVisible: false,\r\n      dialogViewDebtDetail: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleFormDebttrans: {\r\n         title: \"\",\r\n      },\r\n      ruleForm: {\r\n        images:[],\r\n        del_images:[],\r\n        attach_path:[],\r\n        del_attach_path:[],\r\n        cards:[],\r\n        debttrans:[]\r\n      },\r\n        rulesDebttrans:{\r\n            day: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进日期\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n            status: [\r\n                {\r\n                    required: true,\r\n                    message: \"请选择跟进状态\",\r\n                    trigger: \"blur\",\r\n                },\r\n            ],\r\n        },\r\n\r\n      rules: {\r\n        uid: [\r\n          {\r\n            required: true,\r\n            message: \"请选择用户\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: \"请填写债务人姓名\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n          money: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写债务金额\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n          case_des: [\r\n              {\r\n                  required: true,\r\n                  message: \"请填写案由\",\r\n                  trigger: \"blur\",\r\n              },\r\n          ],\r\n      },\r\n      formLabelWidth: \"140px\",\r\n      options: [\r\n        {\r\n          id: -1,\r\n          title: \"请选择\",\r\n        },\r\n        {\r\n          id: 1,\r\n          title: \"待处理\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"调节中\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"诉讼中\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"已结案\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    changeFile(filed) {\r\n      this.filed = filed;\r\n    },\r\n      searchUserData() {\r\n          this.pageUser = 1;\r\n          this.sizeUser = 20;\r\n          this.getUserData(this.ruleForm);\r\n      },\r\n\r\n      getUserData(ruledata) {\r\n          let _this = this;\r\n          _this.ruleForm = ruledata;\r\n          _this\r\n              .postRequest(\r\n                  _this.urlUser + \"index?page=\" + _this.pageUser + \"&size=\" + _this.sizeUser,\r\n                  _this.searchUser\r\n              )\r\n              .then((resp) => {\r\n                  if (resp.code == 200) {\r\n                      _this.dialogFormVisible = false;\r\n                      _this.listUser = resp.data;\r\n                  }\r\n              });\r\n      },\r\n    typeClick(filed) {\r\n        this.$set(this.ruleFormDebttrans,'total_price','');\r\n        this.$set(this.ruleFormDebttrans,'back_money','');\r\n        this.$set(this.ruleFormDebttrans,'content','');\r\n        this.$set(this.ruleFormDebttrans,'rate','');\r\n        if(filed == 1){\r\n            this.dialogHuikuanVisible = false;\r\n            this.dialogZfrqVisible = false;\r\n            if(this.ruleFormDebttrans['pay_type'] == 1){\r\n                this.dialogRichangVisible = false;\r\n            }else{\r\n                this.dialogRichangVisible = true;\r\n            }\r\n        }else{\r\n            this.dialogRichangVisible = false;\r\n            this.dialogHuikuanVisible = true;\r\n            if(this.ruleFormDebttrans['pay_type'] != 3){\r\n                this.dialogZfrqVisible = false;\r\n            }else{\r\n                this.dialogZfrqVisible = true;\r\n            }\r\n        }\r\n    },\r\n    editRateMoney(){\r\n        if(this.ruleFormDebttrans['rate'] > 0  && this.ruleFormDebttrans['back_money'] > 0){\r\n            //this.ruleFormDebttrans.rate_money = this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money'];\r\n            this.$set(this.ruleFormDebttrans,'rate_money',this.ruleFormDebttrans['rate']*this.ruleFormDebttrans['back_money']/100);\r\n        }\r\n    },\r\n      selUserData(currentRow) {\r\n        if(currentRow){\r\n            this.$set(this.ruleForm,'uid',currentRow.id);\r\n            if(currentRow.phone){\r\n                this.$set(this.ruleForm,'utel',currentRow.phone);\r\n            }\r\n            if(currentRow.nickname){\r\n                this.$set(this.ruleForm,'uname',currentRow.nickname);\r\n            }\r\n            this.dialogFormVisible = true;\r\n            this.dialogUserFormVisible = false;\r\n        }\r\n      },\r\n    payTypeClick(filed) {\r\n        if(filed == 2 || filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 1){\r\n                this.dialogRichangVisible = true;\r\n            }else{\r\n                this.dialogRichangVisible = false;\r\n            }\r\n        }\r\n        if(filed == 3){\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogZfrqVisible = true;\r\n            }else{\r\n                this.dialogZfrqVisible = false;\r\n            }\r\n        }\r\n        if(filed == 1){\r\n            this.dialogZfrqVisible = false;\r\n            this.dialogRichangVisible = false;\r\n            if(this.ruleFormDebttrans['type'] == 2){\r\n                this.dialogHuikuanVisible = true;\r\n            }else{\r\n                this.dialogHuikuanVisible = false;\r\n            }\r\n        }\r\n    },\r\n    clearData() {\r\n      this.search = {\r\n        keyword: \"\",\r\n        status: \"\",\r\n        prop: \"\",\r\n        order: \"\",\r\n      };\r\n      this.getData();\r\n    },\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n            images:[],\r\n            del_images:[],\r\n            attach_path:[],\r\n            del_attach_path:[],\r\n            cards:[],\r\n            debttrans:[]\r\n        };\r\n      }\r\n        _this.dialogFormVisible = true;\r\n    },\r\n      viewUserData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentId = id;\r\n          }\r\n\r\n          _this.dialogViewUserDetail = true;\r\n      },\r\n      viewDebtData(id) {\r\n          let _this = this;\r\n          if (id != 0) {\r\n              this.currentDebtId = id;\r\n          }\r\n\r\n          _this.dialogViewDebtDetail = true;\r\n      },\r\n    editDebttransData(id) {\r\n      if (id != 0) {\r\n        this.getDebttransInfo(id);\r\n      } else {\r\n        this.ruleFormDebttrans = {\r\n          name: \"\",\r\n        };\r\n      }\r\n    },\r\n    viewData(id) {\r\n      if (id != 0) {\r\n        this.getView(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n        };\r\n      }\r\n    },\r\n    getView(id) {\r\n       let _this = this;\r\n       _this.getRequest(_this.url + \"view?id=\" + id).then((resp) => {\r\n         if (resp.code == 200) {\r\n           _this.info = resp.data;\r\n           _this.viewFormVisible = true;\r\n           _this.uploadAction = \"/admin/user/import?id=\"+id+\"&token=\"+this.$store.getters.GET_TOKEN;\r\n         } else {\r\n           _this.$message({\r\n             type: \"error\",\r\n             message: resp.msg,\r\n           });\r\n         }\r\n       });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm = resp.data;\r\n          console.log(resp.data);\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDebttransInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"debttransRead?id=\" + id).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleFormDebttrans = resp.data;\r\n            _this.dialogZfrqVisible = false;\r\n            _this.dialogRichangVisible = false;\r\n            _this.dialogHuikuanVisible = false;\r\n          _this.dialogDebttransFormVisible = true;\r\n        } else {\r\n          _this.$message({\r\n            type: \"error\",\r\n            message: resp.msg,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    tuikuan(id) {\r\n      this.$confirm(\"是否申请退款?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"tuikuan?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n            } else {\r\n              this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消退款!\",\r\n          });\r\n        });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.getData();\r\n              this.info.debttrans.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    delDataDebt(index, id) {\r\n       this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n         confirmButtonText: \"确定\",\r\n         cancelButtonText: \"取消\",\r\n         type: \"warning\",\r\n       })\r\n         .then(() => {\r\n           this.deleteRequest(this.url + \"deleteDebt?id=\" + id).then((resp) => {\r\n             if (resp.code == 200) {\r\n               this.$message({\r\n                 type: \"success\",\r\n                 message: \"删除成功!\",\r\n               });\r\n               this.getData();\r\n               this.info.debttrans.splice(index, 1);\r\n             }\r\n           });\r\n         })\r\n         .catch(() => {\r\n           this.$message({\r\n             type: \"error\",\r\n             message: \"取消删除!\",\r\n           });\r\n         });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n      saveDebttransData() {\r\n          let _this = this;\r\n          this.$refs[\"ruleFormDebttrans\"].validate((valid) => {\r\n              if (valid) {\r\n                  this.ruleFormDebttrans['token'] = store.getters.GET_TOKEN;\r\n                  this.postRequest(_this.url + \"saveDebttrans\", this.ruleFormDebttrans).then((resp) => {\r\n                      if (resp.code == 200) {\r\n                          _this.$message({\r\n                              type: \"success\",\r\n                              message: resp.msg,\r\n                          });\r\n                          this.getData();\r\n                          _this.dialogZfrqVisible = false;\r\n                          _this.dialogRichangVisible = false;\r\n                          _this.dialogHuikuanVisible = false;\r\n                          _this.dialogDebttransFormVisible = false;\r\n                      } else {\r\n                          _this.$message({\r\n                              type: \"error\",\r\n                              message: resp.msg,\r\n                          });\r\n                      }\r\n                  });\r\n              } else {\r\n                  return false;\r\n              }\r\n          });\r\n      },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      if (res.code == 200) {\r\n        this.$message.success(\"上传成功\");\r\n        var arr = this.ruleForm[this.filed];\r\n\r\n          this.ruleForm[this.filed].splice(1, 0,res.data.url);\r\n          //this.ruleForm[this.filed].push = res.data.url;\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n\r\n      showUserList() {\r\n          this.searchUserData();\r\n          this.dialogUserFormVisible = true;\r\n      },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName,index) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName].splice(index, 1);\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n      handleSortChange({ column, prop, order }) {\r\n          this.search.prop = prop;\r\n          this.search.order = order;\r\n          this.getData();\r\n          // 根据 column, prop, order 来更新你的数据排序\r\n          // 例如，你可以发送一个请求到服务器来获取排序后的数据\r\n      },\r\n      exports:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n      },\r\n      exportsDebtList:function () { //导出表格\r\n          let _this = this;\r\n          location.href = \"/admin/debt/exportList?token=\" + _this.$store.getters.GET_TOKEN + \"&keyword=\" + _this.search.keyword;\r\n      },\r\n      closeUploadDialog() { //关闭窗口\r\n          this.uploadVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadData.review = false;\r\n      },\r\n      closeUploadDebtsDialog() { //关闭窗口\r\n          this.uploadDebtsVisible = false;\r\n          this.$refs.upload.clearFiles();\r\n          this.uploadDebtsData.review = false;\r\n      },\r\n      uploadSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading2 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      uploadDebtsSuccess(response) { //导入完成回调\r\n          if (response.code === 200) {\r\n              this.$message({\r\n                  type:'success',\r\n                  message: response.msg\r\n              });\r\n              this.uploadDebtsVisible = false;\r\n              this.getData();\r\n              console.log(response);\r\n          }else{\r\n              this.$message({\r\n                  type:'warning',\r\n                  message: response.msg\r\n              });\r\n          }\r\n\r\n          this.submitOrderLoading3 = false;\r\n          this.$refs.upload.clearFiles();\r\n      },\r\n      checkFile(file) { //导入前校验文件后缀\r\n          let fileType = ['xls', 'xlsx'];\r\n          let type = file.name.split('.').slice(-1)[0].toLowerCase();\r\n          if (!fileType.includes(type)) {\r\n              this.$message({\r\n                  type:\"warning\",\r\n                  message:\"文件格式错误仅支持 xls xlxs 文件\"\r\n              });\r\n              return false;\r\n          }\r\n          return true;\r\n      },\r\n      submitUpload() { //导入提交\r\n          this.submitOrderLoading2 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      submitUploadDebts() { //导入提交\r\n          this.submitOrderLoading3 = true;\r\n          this.$refs.upload.submit();\r\n      },\r\n      closeDialog() { //关闭窗口\r\n          this.addVisible = false;\r\n          this.uploadVisible = false;\r\n          this.form = {\r\n              id:'',\r\n              nickname:\"\",\r\n              mobile:\"\",\r\n              school_id:0,\r\n              grade_id:'',\r\n              class_id:'',\r\n              sex:'',\r\n              is_poor:'',\r\n              is_display:'',\r\n              number:'',\r\n              remark:'',\r\n              is_remark_option:0,\r\n              remark_option:[],\r\n              mobile_checked:false,\r\n          };\r\n          this.$refs.form.resetFields();\r\n      },\r\n      openUpload() { //打开导入弹窗\r\n          this.uploadVisible = true;\r\n      },\r\n      openUploadDebts() { //打开导入弹窗\r\n          this.uploadDebtsVisible = true;\r\n      },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.page-top {\r\n  margin-top: 15px;\r\n}\r\n\r\n.el_input {\r\n  width: 475px;\r\n}\r\n</style>\r\n"]}]}