{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\views\\pages\\archive\\test.vue", "mtime": 1748428274747}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXJjaGl2ZVRlc3QnLAogIG1ldGhvZHM6IHsKICAgIGdvVG9GaWxlQXJjaGl2ZSgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9hcmNoaXZlL2ZpbGUnKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "methods", "goToFileArchive", "$router", "push"], "sources": ["src/views/pages/archive/test.vue"], "sourcesContent": ["<template>\r\n  <div class=\"test-page\">\r\n    <h1>归档管理测试页面</h1>\r\n    <p>如果你能看到这个页面，说明归档管理路由配置正确。</p>\r\n    <el-button type=\"primary\" @click=\"goToFileArchive\">前往文件归档</el-button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ArchiveTest',\r\n  methods: {\r\n    goToFileArchive() {\r\n      this.$router.push('/archive/file')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.test-page {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n</style> "], "mappings": ";AASA;EACAA,IAAA;EACAC,OAAA;IACAC,gBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}