<?php
namespace models;
use think\Model;
use think\Db;
class Base extends Model{

	protected $autoWriteTimestamp = true;
	
	private static $errorMsg;
	
	private static $transaction = 0;
	
	private static $DbInstance = [];
	
	const DEFAULT_ERROR_MSG = '操作失败,请稍候再试!';
	/**
	 * [saveData description]
	 * @param  [type] $form [description]
	 * @return [type]       [description]
	 */
	public function saveData($data){
		
		if(empty($data['id'])){
			try {
				$res = $this
				->save($data);
				$res = $this->id;
			} catch (Exception $e) {
				
				self::setErrorInfo($e->getMessage());
				$res = false;
			}			
		}else
		{
		try {
				$res = $this
				->update($data,['id' => $data['id']]);
			} catch (Exception $e) {

				self::setErrorInfo($e->getMessage());
				$res = false;
			}	
		}

		return $res;
	}
	
	/**
	 * [delData 删除数据库]
	 * @param  [type] $where [条件]
	 * @return [type]        [返回结果]
	 */
	public function delData($where){
		try {
			$res=$this
			->where($where)
			->delete();
		} catch (Exception $e) {

			$res =false;
		}
		return $res;
	}

	/**
	 * 设置错误信息
	 * @param string $errorMsg
	 * @return bool
	 */
	protected static function setErrorInfo($errorMsg = self::DEFAULT_ERROR_MSG, $rollback = false)
	{
	    if ($rollback) self::rollbackTrans();
	    self::$errorMsg = $errorMsg;
	   	
	}
	
	
	/**
	 * 获取错误信息
	 * @param string $defaultMsg
	 * @return string
	 */
	public static function getErrorInfo($defaultMsg = self::DEFAULT_ERROR_MSG)
	{
	    return !empty(self::$errorMsg) ? self::$errorMsg : '';
	}
	
	/**
	 * 开启事务
	 */
	public static function beginTrans()
	{
	    Db::startTrans();
	}
	
	/**
	 * 提交事务
	 */
	public static function commitTrans()
	{
	    Db::commit();
	}
	
	/**
	 * 关闭事务
	 */
	public static function rollbackTrans()
	{
	    Db::rollback();
	}
	
	/**
	 * 根据结果提交滚回事务
	 * @param $res
	 */
	public static function checkTrans($res)
	{
	    if ($res) {
	        self::commitTrans();
	    } else {
	        self::rollbackTrans();
	    }
	}
}