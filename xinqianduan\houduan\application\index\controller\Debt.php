<?php
namespace app\index\controller;
use think\Request;
use untils\{JsonService,LiveA};
use models\{Configs,Users,Debts,Debttrans,Orders};
use think\Controller;
use think\facade\View;

class Debt extends Controller{

	private $configs = [];
	//private $url  = 'https://fdb.zhongfabang.cn';
	private $url  = 'https://web.faduobang.com';
	public function __construct(Configs $model){
		$configs = $model->getAllData();
        $this->configs =$configs;
	}

	public function index(){
		//跳转至后台
		header("location:https://web.faduobang.com/admin_index/#/login");
	}


    public function debtList(Debts $model, Request $request){
        $post = $request->post();
        $res = $model::withAttr('status_name',function($v,$d){
            switch ($d['status']) {
                case 1:
                    return '待处理';
                    break;
                case 2:
                    return '调节中';
                    break;
                case 3:
                    return '诉讼中';
                    break;
                case 4:
                    return '已结案';
                    break;
            }
        })->withAttr('pay_type',function($v,$d){
            return Debttrans::where([['dt_id', '=', $d['id']],['pay_type','=',2]])->value('pay_type');
        })->where($post)
            ->order(['ctime'=>'desc'])
            ->append(['pay_type','status_name'])
            ->select();
        if($res){
            foreach ($res as &$v){
                $v["un_money"] = sprintf("%.2f",$v["money"] - $v["back_money"]);
            }
        }
        if(!empty($res)) return JsonService::successful("1",$res);
        else return JsonService::fail("暂无数据");
    }

    public function debtTransList(Debttrans $model, Request $request){
        $post = $request->post();
        $w = [];
        $w[] = ['t1.uid','=',$post["uid"]];
        $w[] = ['t1.dt_id','=',$post["dt_id"]];
        $res = $model->where($w)
            ->field("*, t1.id as id , t2.id as order_id, t1.type as trans_type, t1.total_price as total_price")
            ->alias('t1')
            ->leftJoin("orders t2","t1.order_sn = t2.order_sn")
		    ->order(['t1.day'=>'desc'])
            ->select();

        if($res){
            $ret_arr = [];
            $debt_info = Debts::where(['id'=>$post['dt_id']])->find();
            $ret_arr["list"] = $res;
            $total_type_1 = 0;
            $total_type_2 = 0;
            foreach ($res as &$v){
                switch ($v["pay_type"]) {
                    case 1:
                        $v["pay_type_name"] = "无需支付";
                        break;
                    case 2:
                        $v["pay_type_name"] = "待支付";
                        break;
                    case 3:
                        $v["pay_type_name"] = "已支付";
                        break;
                }
                switch ($v["pay_order_type"]) {
                    case 1:
                        $v["pay_order_type_name"] = "线下支付";
                        break;
                    case 2:
                        $v["pay_order_type_name"] = "微信支付";
                        break;
                }
                if($v["trans_type"] == 1){
                    $total_type_1++;
                }
                if($v["trans_type"] == 2){
                    $total_type_2++;
                }

                if($v['order_sn'] && $v['pay_type']==2){
                    $ordersModel = new Orders();
                    $orderInfo = $ordersModel::where(['order_sn'=>$v['order_sn']])->find();
                    if($orderInfo){
                        $v["order_id"] = $orderInfo['id'];
                    }
                }
            }


            $ret_arr["total_type_1"] = $total_type_1;
            $ret_arr["total_type_2"] = $total_type_2;
            $ret_arr["money"] = $debt_info["money"];
            $ret_arr["back_money"] = $debt_info["back_money"];
            $ret_arr["un_money"] = sprintf("%.2f",$ret_arr["money"] - $ret_arr["back_money"]);
            if(!empty($res)) return JsonService::successful("1",$ret_arr);
        }
        if(!empty($res)) return JsonService::successful("1",$res);
        else return JsonService::fail("暂无数据");
    }

    public function debtView(Debts $model, Request $request){
        $post = $request->post();
        $res = $model::withAttr('status',function($v,$d){
            switch ($v) {
                case 1:
                    return '待处理';
                    break;
                case 2:
                    return '调节中';
                    break;
                case 3:
                    return '诉讼中';
                    break;
                case 4:
                    return '已结案';
                    break;
            }
        })->where($post)
            ->order(['ctime'=>'desc'])
            ->find();
        if($res){
            $res["cards"] = !empty($res["cards"])?explode(",",$res["cards"]):[];
            $res["images"] = !empty($res["images"])?explode(",",$res["images"]):[];
            $res["files"] = !empty($res["attach_path"])?explode(",",$res["attach_path"]):[];
            $res["lvshi_path"] = !empty($res["lvshi_path"])?explode(",",$res["lvshi_path"]):[];
            $res["wenshu_path"] = !empty($res["wenshu_path"])?explode(",",$res["wenshu_path"]):[];

            $file_types = array();
            $res["attach_path_type"] = array();
            if($res["files"]){
                foreach($res["files"] as $item){
                    $file_types[] =  return_file_type($item);
                }
                $res["attach_path_type"] = $file_types;
            }

            $file_types2 = array();
            $res["lvshi_path_type"] = array();
            if($res["lvshi_path"]){
                foreach($res["lvshi_path"] as $item){
                    $file_types2[] =  return_file_type($item);
                }
                $res["lvshi_path_type"] = $file_types;
            }

            $file_types3 = array();
            $res["wenshu_path_type"] = array();
            if($res["wenshu_path"]){
                foreach($res["wenshu_path"] as $item){
                    $file_types3[] =  return_file_type($item);
                }
                $res["wenshu_path_type"] = $file_types;
            }

        }
        if(!empty($res)) return JsonService::successful("1",$res);
        else return JsonService::fail("暂无数据");
    }


    public function save(Debts $model, Request $request){
        if(!$request->isPost()) return JsonService::fail('非法请求2');
        $form =$request->post();
        if(empty($form['uid'])) return JsonService::fail('请先登录');
        $info = Users::withAttr('end_time',function($v,$d){
            if(!empty($d['year'])) return $d['start_time']*1+$d['year']*1*365*60*60*24;
        })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['id'=>$form['uid']])
            ->find();
        if(empty($info['end_time'])) return JsonService::fail('该功能需要会员才可发布');
        if(time()>$info['end_time']*1) return JsonService::fail('会员时间已到期,请联系客服升级');
        $validate = new \app\index\validate\Debt;
        if (!$validate->check($form)){
            return JsonService::fail( $validate->getError());
        }
        if(!empty($form['cards'])){
            $card_arr = array();
            foreach ($form['cards'] as $card) {
                $card_arr[] = $card;
            }
            $form['cards'] = implode(",",$card_arr);
        }else{
            $form['cards'] = '';
        }
        if(!empty($form['images'])){
            $img_arr = array();
            foreach ($form['images'] as $img) {
                $tmp_arr = parse_url($img);
                $img_arr[] = $tmp_arr["path"];
            }
            $form['images'] = implode(",",$img_arr);
        }else{
            $form['images'] = '';
        }
        if(!empty($form['files'])){
            $file_arr = array();
            foreach ($form['files'] as $tmp_file) {
                $file_arr[] = $tmp_file;
            }
            $form['attach_path'] = implode(",",$file_arr);
        }else{
            $form['attach_path'] = '';
        }

        //律师函
        if(!empty($form['lvshi_path'])){
            $file_arr = array();
            foreach ($form['lvshi_path'] as $tmp_file) {
                $file_arr[] = $tmp_file;
            }
            $form['lvshi_path'] = implode(",",$file_arr);
        }else{
            $form['lvshi_path'] = '';
        }

        //法律文书
        if(!empty($form['wenshu_path'])){
            $file_arr = array();
            foreach ($form['wenshu_path'] as $tmp_file) {
                $file_arr[] = $tmp_file;
            }
            $form['wenshu_path'] = implode(",",$file_arr);
        }else{
            $form['wenshu_path'] = '';
        }

        $now = time();
        if(isset($form["id"]) && !empty($form["id"])){
            $form["utime"] = $now;

            //编辑的时候搜出原始数据图片
            $debtInfo = $model->where('id',$form['id'])->find();
            if($debtInfo['images']){
                $oldImages = explode(',',$debtInfo['images']);
                $newImages = explode(',',$form['images']);
                //和旧的图片对比  找出被删除的记录下来
                $del_images = empty($debtInfo['del_images'])?[]:explode(',',$debtInfo['del_images']);//这个是已经被删了的
                foreach ($oldImages as $imgs){
                    if(!in_array($imgs,$newImages)){
                        $del_images[] = $imgs;
                    }
                }
                $form['del_images'] = implode(",",$del_images);
            }

            if($debtInfo['attach_path']){
                $oldAttach_path = explode(',',$debtInfo['attach_path']);
                $newAttach_path = explode(',',$form['attach_path']);
                //和旧的图片对比  找出被删除的记录下来
                $del_attach_path = empty($debtInfo['del_attach_path'])?[]:explode(',',$debtInfo['del_attach_path']);//这个是已经被删了的
                foreach ($oldAttach_path as $att){
                    if(!in_array($att,$newAttach_path)){
                        $del_attach_path[] = $att;
                    }
                }
                $form['del_attach_path'] = implode(",",$del_attach_path);
            }

            if($debtInfo['lvshi_path']){
                $oldAttach_path = explode(',',$debtInfo['lvshi_path']);
                $newAttach_path = explode(',',$form['lvshi_path']);
                //和旧的图片对比  找出被删除的记录下来
                $del_attach_path = empty($debtInfo['del_lvshi_path'])?[]:explode(',',$debtInfo['del_lvshi_path']);//这个是已经被删了的
                foreach ($oldAttach_path as $att){
                    if(!in_array($att,$newAttach_path)){
                        $del_attach_path[] = $att;
                    }
                }
                $form['del_lvshi_path'] = implode(",",$del_attach_path);
            }

            if($debtInfo['wenshu_path']){
                $oldAttach_path = explode(',',$debtInfo['wenshu_path']);
                $newAttach_path = explode(',',$form['wenshu_path']);
                //和旧的图片对比  找出被删除的记录下来
                $del_attach_path = empty($debtInfo['del_wenshu_path'])?[]:explode(',',$debtInfo['del_wenshu_path']);//这个是已经被删了的
                foreach ($oldAttach_path as $att){
                    if(!in_array($att,$newAttach_path)){
                        $del_attach_path[] = $att;
                    }
                }
                $form['del_wenshu_path'] = implode(",",$del_attach_path);
            }



        }else{
            $form["ctime"] = $now;
        }
        unset($form["status"]);
        $res = $model->saveData($form);
        if(empty($res)) return JsonService::fail('操作失败');
        else return JsonService::successful('操作成功',$res);
    }



     //$form['order_sn']='GD'.get_order_num();




}
