language: php
dist: bionic

php:
  - 7.1
  - 7.2
  - 7.3
  - 7.4

cache:
  directories:
    - vendor
    - $HOME/.composer/cache

before_script:
  # Deactivate xdebug
  - phpenv config-rm xdebug.ini
  - composer install --ignore-platform-reqs

script:
  - ./vendor/bin/phpunit

jobs:
  include:

    - stage: Code style
      php: 7.2
      script:
        - ./vendor/bin/php-cs-fixer fix --diff --verbose --dry-run
        - ./vendor/bin/phpcs --report-width=200 samples/ src/ tests/ --ignore=samples/Header.php --standard=PSR2 -n

    - stage: Coverage
      php: 7.2
      script:
        - pecl install pcov
        - composer require pcov/clobber --dev
        - ./vendor/bin/pcov clobber
        - ./vendor/bin/phpunit --coverage-clover coverage-clover.xml
      after_script:
        - wget https://scrutinizer-ci.com/ocular.phar
        - php ocular.phar code-coverage:upload --format=php-clover tests/coverage-clover.xml

    - stage: API documentations
      if: tag is present
      php: 7.4
      before_script:
      - curl -O https://github.com/phpDocumentor/phpDocumentor/releases/download/v3.0.0-rc/phpDocumentor.phar
      script:
      - php phpDocumentor.phar --directory src/ --target docs/api
      deploy:
        provider: pages
        skip-cleanup: true
        local-dir: docs/api
        github-token: $GITHUB_TOKEN
        on:
          all_branches: true
          condition: $TRAVIS_BRANCH =~ ^master$
