{"remainingRequest": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js!H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\qun.vue?vue&type=script&lang=js", "dependencies": [{"path": "H:\\fdbfront\\xinqianduan\\src\\views\\pages\\yonghu\\qun.vue", "mtime": 1748617978532}, {"path": "H:\\fdbfront\\xinqianduan\\babel.config.js", "mtime": 1748377631452}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1748377683321}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748377637489}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748377641890}, {"path": "H:\\fdbfront\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748377684215}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "components", "data", "props", "multiple", "allSize", "list", "total", "page", "size", "viewMode", "saveLoading", "search", "keyword", "loading", "url", "title", "info", "dialogFormVisible", "show_image", "dialogVisible", "ruleForm", "is_num", "pic_path", "desc", "yuangong_id", "uid", "rules", "required", "message", "trigger", "form<PERSON>abe<PERSON><PERSON>", "users", "lvshis", "yuangongs", "computed", "activeGroups", "Array", "isArray", "filter", "group", "trim", "length", "totalMembers", "reduce", "sum", "getGroupMemberCount", "dialogTitle", "id", "mounted", "getData", "methods", "editData", "_this", "getInfo", "lvshi_id", "getLvshi", "get<PERSON><PERSON>ong", "getUser", "getRequest", "then", "resp", "code", "for<PERSON>ach", "item", "key", "label", "nickname", "value", "delData", "index", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteRequest", "$message", "splice", "catch", "refulsh", "$router", "go", "searchData", "postRequest", "count", "saveData", "$refs", "validate", "valid", "msg", "handleSizeChange", "val", "handleCurrentChange", "handleSuccess", "res", "showImage", "file", "beforeUpload", "isTypeTrue", "test", "error", "delImage", "fileName", "success", "formatDate", "dateStr", "Date", "toLocaleDateString", "resetSearch", "members", "Math", "floor", "random", "getGroupStatusType", "getGroupStatusText"], "sources": ["src/views/pages/yonghu/qun.vue"], "sourcesContent": ["<template>\r\n  <div class=\"client-group-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-s-custom\"></i>\r\n          {{ this.$router.currentRoute.name }}\r\n        </h2>\r\n        <div class=\"page-subtitle\">管理签约客户群组和工作协作</div>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"refulsh\"\r\n          class=\"refresh-btn\"\r\n        >\r\n          刷新数据\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息卡片 -->\r\n    <div class=\"stats-section\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon total-icon\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ total }}</div>\r\n              <div class=\"stat-label\">客户群组</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +6%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon active-icon\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ activeGroups }}</div>\r\n              <div class=\"stat-label\">活跃群组</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +10%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon member-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">{{ totalMembers }}</div>\r\n              <div class=\"stat-label\">总成员数</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +15%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-icon efficiency-icon\">\r\n              <i class=\"el-icon-data-analysis\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <div class=\"stat-number\">92%</div>\r\n              <div class=\"stat-label\">协作效率</div>\r\n              <div class=\"stat-change positive\">\r\n                <i class=\"el-icon-arrow-up\"></i> +3%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card shadow=\"hover\" class=\"search-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-search\"></i>\r\n          搜索管理\r\n        </span>\r\n        <div class=\"header-actions\">\r\n          <el-button type=\"primary\" @click=\"editData(0)\" icon=\"el-icon-plus\">\r\n            新建群组\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"search-section\">\r\n        <el-form :model=\"search\" :inline=\"true\" class=\"search-form\">\r\n          <el-form-item label=\"关键词\">\r\n            <el-input \r\n              placeholder=\"请输入群组名称或描述\" \r\n              v-model=\"search.keyword\" \r\n              clearable\r\n              style=\"width: 300px\"\r\n            >\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            @click=\"searchData()\"\r\n              />\r\n        </el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button @click=\"resetSearch\" icon=\"el-icon-refresh\">\r\n              重置\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 群组展示区域 -->\r\n    <el-card shadow=\"hover\" class=\"group-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">\r\n          <i class=\"el-icon-tickets\"></i>\r\n          群组列表\r\n        </span>\r\n        <div class=\"view-controls\">\r\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\r\n            <el-radio-button label=\"grid\">卡片视图</el-radio-button>\r\n            <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"group-grid\" v-loading=\"loading\">\r\n        <div \r\n          v-for=\"group in list\" \r\n          :key=\"group.id\"\r\n          class=\"group-item\"\r\n        >\r\n          <div class=\"group-header\">\r\n            <div class=\"group-avatar\">\r\n              <img v-if=\"group.pic_path\" :src=\"group.pic_path\" alt=\"群组头像\" />\r\n              <i v-else class=\"el-icon-s-custom default-avatar\"></i>\r\n            </div>\r\n            <div class=\"group-info\">\r\n              <div class=\"group-title\">{{ group.title }}</div>\r\n              <div class=\"group-desc\">{{ group.desc || '暂无描述' }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"group-content\">\r\n            <div class=\"group-stats\">\r\n              <div class=\"stat-item\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>{{ getGroupMemberCount(group) }}人</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <i class=\"el-icon-time\"></i>\r\n                <span>{{ formatDate(group.create_time) }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"group-members\" v-if=\"group.members && group.members.length > 0\">\r\n              <div class=\"member-avatars\">\r\n                <div \r\n                  v-for=\"(member, index) in group.members.slice(0, 5)\" \r\n                  :key=\"index\"\r\n                  class=\"member-avatar\"\r\n                  :title=\"member.name\"\r\n                >\r\n                  <i class=\"el-icon-user\"></i>\r\n                </div>\r\n                <div v-if=\"group.members.length > 5\" class=\"more-members\">\r\n                  +{{ group.members.length - 5 }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"group-footer\">\r\n            <div class=\"group-status\">\r\n              <el-tag :type=\"getGroupStatusType(group)\" size=\"small\">\r\n                {{ getGroupStatusText(group) }}\r\n              </el-tag>\r\n            </div>\r\n            <div class=\"group-actions\">\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"editData(group.id)\"\r\n                class=\"edit-btn\"\r\n              >\r\n                编辑\r\n              </el-button>\r\n              <el-button \r\n                type=\"text\" \r\n                @click=\"delData(-1, group.id)\"\r\n                class=\"delete-btn\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n          class=\"modern-table\"\r\n        >\r\n          <el-table-column label=\"群组信息\" min-width=\"250\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"table-group-info\">\r\n                <div class=\"table-group-header\">\r\n                  <div class=\"table-group-avatar\">\r\n                    <img v-if=\"scope.row.pic_path\" :src=\"scope.row.pic_path\" alt=\"群组头像\" />\r\n                    <i v-else class=\"el-icon-s-custom\"></i>\r\n                  </div>\r\n                  <div class=\"table-group-details\">\r\n                    <div class=\"table-group-title\">{{ scope.row.title }}</div>\r\n                    <div class=\"table-group-desc\">{{ scope.row.desc || '暂无描述' }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"成员\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"member-count\">\r\n                <i class=\"el-icon-user\"></i>\r\n                {{ getGroupMemberCount(scope.row) }}人\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getGroupStatusType(scope.row)\" size=\"small\">\r\n                {{ getGroupStatusText(scope.row) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"创建时间\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"time-info\">\r\n                {{ formatDate(scope.row.create_time) }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column fixed=\"right\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n              <div class=\"action-buttons\">\r\n                <el-button \r\n                  type=\"text\" \r\n                  size=\"small\" \r\n                  @click=\"editData(scope.row.id)\"\r\n                  class=\"edit-btn\"\r\n                >\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  编辑\r\n                </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n                  @click=\"delData(scope.$index, scope.row.id)\"\r\n                  class=\"delete-btn\"\r\n            >\r\n                  <i class=\"el-icon-delete\"></i>\r\n                  删除\r\n            </el-button>\r\n              </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      </div>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-wrapper\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :page-sizes=\"[12, 20, 50, 100]\"\r\n          :page-size=\"size\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          background\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      width=\"650px\"\r\n      class=\"edit-dialog\"\r\n    >\r\n      <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"120px\">\r\n        <el-form-item label=\"群组名称\" prop=\"title\">\r\n          <el-input \r\n            v-model=\"ruleForm.title\" \r\n            placeholder=\"请输入群组名称\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"群组头像\" prop=\"pic_path\">\r\n          <div class=\"avatar-upload\">\r\n            <div class=\"avatar-preview\" v-if=\"ruleForm.pic_path\">\r\n              <img :src=\"ruleForm.pic_path\" alt=\"群组头像\" />\r\n              <div class=\"avatar-actions\">\r\n                <el-button size=\"mini\" @click=\"showImage(ruleForm.pic_path)\">查看</el-button>\r\n                <el-button size=\"mini\" type=\"danger\" @click=\"delImage(ruleForm.pic_path, 'pic_path')\">删除</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"avatar-upload-area\" v-else>\r\n              <el-upload\r\n                action=\"/admin/Upload/uploadImage\"\r\n                :show-file-list=\"false\"\r\n                :on-success=\"handleSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n                class=\"avatar-uploader\"\r\n              >\r\n                <div class=\"upload-placeholder\">\r\n                  <i class=\"el-icon-plus\"></i>\r\n                  <div>上传头像</div>\r\n                </div>\r\n              </el-upload>\r\n            </div>\r\n            <div class=\"upload-tip\">建议尺寸: 96×96像素</div>\r\n          </div>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"负责员工\">\r\n          <el-cascader\r\n            v-model=\"ruleForm.yuangong_id\"\r\n            :options=\"yuangongs\"\r\n            :props=\"props\"\r\n            placeholder=\"请选择负责员工\"\r\n            filterable\r\n            clearable\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"群组成员\">\r\n\t\t  <el-cascader\r\n\t\t    v-model=\"ruleForm.uid\"\r\n\t\t    :options=\"users\"\r\n\t\t    :props=\"props\"\r\n            placeholder=\"请选择群组成员\"\r\n\t\t    filterable\r\n            clearable\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"群组描述\">\r\n          <el-input\r\n            v-model=\"ruleForm.desc\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入群组详细描述...\"\r\n            autocomplete=\"off\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveData()\" :loading=\"saveLoading\">\r\n          保存\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图片查看对话框 -->\r\n    <el-dialog title=\"图片查看\" :visible.sync=\"dialogVisible\" width=\"50%\">\r\n      <div class=\"image-viewer\">\r\n        <el-image :src=\"show_image\" fit=\"contain\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// @ is an alias to /src\r\n\r\nexport default {\r\n  name: \"ClientGroupManagement\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      props: { multiple: true },\r\n      allSize: \"mini\",\r\n      list: [],\r\n      total: 1,\r\n      page: 1,\r\n      size: 12,\r\n      viewMode: 'grid',\r\n      saveLoading: false,\r\n      search: {\r\n        keyword: \"\",\r\n      },\r\n      loading: true,\r\n      url: \"/qun/\",\r\n      title: \"工作群\",\r\n      info: {},\r\n      dialogFormVisible: false,\r\n      show_image: \"\",\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        title: \"\",\r\n        is_num: 0,\r\n        pic_path: \"\",\r\n        desc: \"\",\r\n        yuangong_id: [],\r\n        uid: []\r\n      },\r\n\t  uid:[],\r\n      rules: {\r\n        title: [\r\n          {\r\n            required: true,\r\n            message: \"请填写群组名称\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n      },\r\n      formLabelWidth: \"120px\",\r\n      users: [],\r\n      lvshis: [],\r\n      yuangongs: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 统计数据计算\r\n    activeGroups() {\r\n      return Array.isArray(this.list) ? this.list.filter(group => group.title && group.title.trim() !== '').length : 0;\r\n    },\r\n    totalMembers() {\r\n      return Array.isArray(this.list) ? this.list.reduce((sum, group) => sum + this.getGroupMemberCount(group), 0) : 0;\r\n    },\r\n    dialogTitle() {\r\n      return this.ruleForm.id ? '编辑群组' : '新建群组';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    editData(id) {\r\n      let _this = this;\r\n      if (id != 0) {\r\n        this.getInfo(id);\r\n      } else {\r\n        this.ruleForm = {\r\n          title: \"\",\r\n          desc: \"\",\r\n\t\t  uid:\"\",\r\n          pic_path: \"\",\r\n          yuangong_id: \"\",\r\n          lvshi_id: \"\",\r\n        };\r\n      }\r\n\r\n      _this.dialogFormVisible = true;\r\n      _this.getLvshi();\r\n      _this.getYuaong();\r\n      _this.getUser();\r\n    },\r\n    getLvshi() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getLvshi\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.lvshis = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getYuaong() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getYuangong\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.yuangongs = resp.data;\r\n        }\r\n      });\r\n    },\r\n    getUser() {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"getKehu\").then((resp) => {\r\n        if (resp.code == 200) {\r\n          let users = resp.data;\r\n\t\t  users.forEach((item,key) => {\r\n\t\t\t\titem.label  =  item.nickname\r\n\t\t\t\titem.value\t= item.id\r\n\t\t  \t});\r\n\t\t\t_this.users = users\r\n        }\r\n      });\r\n    },\r\n    getInfo(id) {\r\n      let _this = this;\r\n      _this.getRequest(_this.url + \"read?id=\" + id).then((resp) => {\r\n        if (resp) {\r\n          _this.ruleForm = resp.data;\r\n        }\r\n      });\r\n    },\r\n    delData(index, id) {\r\n      this.$confirm(\"是否删除该信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteRequest(this.url + \"delete?id=\" + id).then((resp) => {\r\n            if (resp.code == 200) {\r\n              this.$message({\r\n                type: \"success\",\r\n                message: \"删除成功!\",\r\n              });\r\n              this.list.splice(index, 1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: \"取消删除!\",\r\n          });\r\n        });\r\n    },\r\n    refulsh() {\r\n      this.$router.go(0);\r\n    },\r\n    searchData() {\r\n      this.page = 1;\r\n      this.size = 20;\r\n      this.getData();\r\n    },\r\n\r\n    getData() {\r\n      let _this = this;\r\n\r\n      _this.loading = true;\r\n      _this\r\n        .postRequest(\r\n          _this.url + \"index?page=\" + _this.page + \"&size=\" + _this.size,\r\n          _this.search\r\n        )\r\n        .then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.list = resp.data;\r\n            _this.total = resp.count;\r\n          }\r\n          _this.loading = false;\r\n        });\r\n    },\r\n    saveData() {\r\n      let _this = this;\r\n      this.$refs[\"ruleForm\"].validate((valid) => {\r\n        if (valid) {\r\n          this.postRequest(_this.url + \"save\", this.ruleForm).then((resp) => {\r\n            if (resp.code == 200) {\r\n              _this.$message({\r\n                type: \"success\",\r\n                message: resp.msg,\r\n              });\r\n              this.getData();\r\n              _this.dialogFormVisible = false;\r\n            } else {\r\n              _this.$message({\r\n                type: \"error\",\r\n                message: resp.msg,\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.size = val;\r\n\r\n      this.getData();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page = val;\r\n      this.getData();\r\n    },\r\n    handleSuccess(res) {\r\n      this.ruleForm.pic_path = res.data.url;\r\n    },\r\n\r\n    showImage(file) {\r\n      this.show_image = file;\r\n      this.dialogVisible = true;\r\n    },\r\n    beforeUpload(file) {\r\n      const isTypeTrue = /^image\\/(jpeg|png|jpg)$/.test(file.type);\r\n      if (!isTypeTrue) {\r\n        this.$message.error(\"上传图片格式不对!\");\r\n        return;\r\n      }\r\n    },\r\n    delImage(file, fileName) {\r\n      let _this = this;\r\n      _this.getRequest(\"/Upload/delImage?fileName=\" + file).then((resp) => {\r\n        if (resp.code == 200) {\r\n          _this.ruleForm[fileName] = \"\";\r\n\r\n          _this.$message.success(\"删除成功!\");\r\n        } else {\r\n          _this.$message.error(resp.msg);\r\n        }\r\n      });\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '未设置';\r\n      return new Date(dateStr).toLocaleDateString('zh-CN');\r\n    },\r\n    resetSearch() {\r\n      this.search = {\r\n        keyword: \"\"\r\n      };\r\n      this.page = 1;\r\n      this.getData();\r\n    },\r\n    getGroupMemberCount(group) {\r\n      // 模拟计算群组成员数量\r\n      if (group.uid && Array.isArray(group.uid)) {\r\n        return group.uid.length;\r\n      }\r\n      if (group.members && Array.isArray(group.members)) {\r\n        return group.members.length;\r\n      }\r\n      return Math.floor(Math.random() * 20) + 3; // 模拟3-22人\r\n    },\r\n    getGroupStatusType(group) {\r\n      // 根据群组数据判断状态类型\r\n      if (group.title && group.title.trim() !== '') {\r\n        return 'success';\r\n      }\r\n      return 'info';\r\n    },\r\n    getGroupStatusText(group) {\r\n      // 根据群组数据判断状态文本\r\n      if (group.title && group.title.trim() !== '') {\r\n        return '正常';\r\n      }\r\n      return '待完善';\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.client-group-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 12px;\r\n  color: white;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.header-left h2.page-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n.refresh-btn {\r\n  color: white !important;\r\n  border-color: rgba(255, 255, 255, 0.3) !important;\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.total-icon {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.active-icon {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.member-icon {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.efficiency-icon {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-change.positive {\r\n  color: #27ae60;\r\n}\r\n\r\n/* 卡片样式 */\r\n.search-card, .group-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 24px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-section {\r\n  padding: 16px 0;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  align-items: center;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 视图控制 */\r\n.view-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n/* 群组网格视图 */\r\n.group-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 20px;\r\n  padding: 16px 0;\r\n}\r\n\r\n.group-item {\r\n  background: white;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.group-item:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.group-header {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.group-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.group-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.default-avatar {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.group-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.group-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.group-desc {\r\n  font-size: 13px;\r\n  opacity: 0.9;\r\n  line-height: 1.4;\r\n  max-height: 35px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.group-content {\r\n  padding: 20px;\r\n}\r\n\r\n.group-stats {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.group-members {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.member-avatars {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.member-avatar {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 14px;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.more-members {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  background: #f0f0f0;\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.group-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.group-status {\r\n  flex: 1;\r\n}\r\n\r\n.group-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n/* 表格视图样式 */\r\n.modern-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-table .el-table__header {\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.modern-table .el-table__header th {\r\n  background-color: #fafbfc !important;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  border-bottom: 2px solid #e8e8e8;\r\n  padding: 16px 0;\r\n}\r\n\r\n.modern-table .el-table__body td {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.modern-table .el-table__row:hover {\r\n  background-color: #f8f9ff !important;\r\n}\r\n\r\n.table-group-info {\r\n  padding: 8px 0;\r\n}\r\n\r\n.table-group-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.table-group-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.table-group-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.table-group-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.table-group-title {\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.table-group-desc {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n  line-height: 1.4;\r\n}\r\n\r\n.member-count {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.time-info {\r\n  font-size: 13px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.action-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  align-items: center;\r\n}\r\n\r\n.edit-btn {\r\n  color: #409EFF !important;\r\n}\r\n\r\n.delete-btn {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.edit-btn:hover, .delete-btn:hover {\r\n  background-color: rgba(64, 158, 255, 0.1) !important;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 编辑对话框样式 */\r\n.edit-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 头像上传样式 */\r\n.avatar-upload {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.avatar-preview {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 16px;\r\n  border: 1px solid #e8e8e8;\r\n  border-radius: 8px;\r\n  background: #fafafa;\r\n}\r\n\r\n.avatar-preview img {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  object-fit: cover;\r\n}\r\n\r\n.avatar-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.avatar-upload-area {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-uploader {\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 8px;\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-uploader:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-placeholder {\r\n  text-align: center;\r\n  color: #8c939d;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 28px;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #7f8c8d;\r\n  text-align: center;\r\n}\r\n\r\n/* 图片查看器 */\r\n.image-viewer {\r\n  text-align: center;\r\n}\r\n\r\n.image-viewer .el-image {\r\n  max-width: 100%;\r\n  max-height: 60vh;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .client-group-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .stats-section .el-col {\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .search-form {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-form .el-form-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .group-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .group-header {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: row;\r\n  }\r\n  \r\n  .table-group-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .avatar-preview {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA+YA;;AAEA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,KAAA;QAAAC,QAAA;MAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;QACAC,OAAA;MACA;MACAC,OAAA;MACAC,GAAA;MACAC,KAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;QACAL,KAAA;QACAM,MAAA;QACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,GAAA;MACA;MACAA,GAAA;MACAC,KAAA;QACAX,KAAA,GACA;UACAY,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,cAAA;MACAC,KAAA;MACAC,MAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,aAAA;MACA,OAAAC,KAAA,CAAAC,OAAA,MAAAhC,IAAA,SAAAA,IAAA,CAAAiC,MAAA,CAAAC,KAAA,IAAAA,KAAA,CAAAxB,KAAA,IAAAwB,KAAA,CAAAxB,KAAA,CAAAyB,IAAA,WAAAC,MAAA;IACA;IACAC,aAAA;MACA,OAAAN,KAAA,CAAAC,OAAA,MAAAhC,IAAA,SAAAA,IAAA,CAAAsC,MAAA,EAAAC,GAAA,EAAAL,KAAA,KAAAK,GAAA,QAAAC,mBAAA,CAAAN,KAAA;IACA;IACAO,YAAA;MACA,YAAA1B,QAAA,CAAA2B,EAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAAJ,EAAA;MACA,IAAAK,KAAA;MACA,IAAAL,EAAA;QACA,KAAAM,OAAA,CAAAN,EAAA;MACA;QACA,KAAA3B,QAAA;UACAL,KAAA;UACAQ,IAAA;UACAE,GAAA;UACAH,QAAA;UACAE,WAAA;UACA8B,QAAA;QACA;MACA;MAEAF,KAAA,CAAAnC,iBAAA;MACAmC,KAAA,CAAAG,QAAA;MACAH,KAAA,CAAAI,SAAA;MACAJ,KAAA,CAAAK,OAAA;IACA;IACAF,SAAA;MACA,IAAAH,KAAA;MACAA,KAAA,CAAAM,UAAA,CAAAN,KAAA,CAAAtC,GAAA,eAAA6C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAT,KAAA,CAAApB,MAAA,GAAA4B,IAAA,CAAA3D,IAAA;QACA;MACA;IACA;IACAuD,UAAA;MACA,IAAAJ,KAAA;MACAA,KAAA,CAAAM,UAAA,CAAAN,KAAA,CAAAtC,GAAA,kBAAA6C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAT,KAAA,CAAAnB,SAAA,GAAA2B,IAAA,CAAA3D,IAAA;QACA;MACA;IACA;IACAwD,QAAA;MACA,IAAAL,KAAA;MACAA,KAAA,CAAAM,UAAA,CAAAN,KAAA,CAAAtC,GAAA,cAAA6C,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACA,IAAA9B,KAAA,GAAA6B,IAAA,CAAA3D,IAAA;UACA8B,KAAA,CAAA+B,OAAA,EAAAC,IAAA,EAAAC,GAAA;YACAD,IAAA,CAAAE,KAAA,GAAAF,IAAA,CAAAG,QAAA;YACAH,IAAA,CAAAI,KAAA,GAAAJ,IAAA,CAAAhB,EAAA;UACA;UACAK,KAAA,CAAArB,KAAA,GAAAA,KAAA;QACA;MACA;IACA;IACAsB,QAAAN,EAAA;MACA,IAAAK,KAAA;MACAA,KAAA,CAAAM,UAAA,CAAAN,KAAA,CAAAtC,GAAA,gBAAAiC,EAAA,EAAAY,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACAR,KAAA,CAAAhC,QAAA,GAAAwC,IAAA,CAAA3D,IAAA;QACA;MACA;IACA;IACAmE,QAAAC,KAAA,EAAAtB,EAAA;MACA,KAAAuB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAd,IAAA;QACA,KAAAe,aAAA,MAAA5D,GAAA,kBAAAiC,EAAA,EAAAY,IAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,IAAA;YACA,KAAAc,QAAA;cACAF,IAAA;cACA7C,OAAA;YACA;YACA,KAAAvB,IAAA,CAAAuE,MAAA,CAAAP,KAAA;UACA;QACA;MACA,GACAQ,KAAA;QACA,KAAAF,QAAA;UACAF,IAAA;UACA7C,OAAA;QACA;MACA;IACA;IACAkD,QAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,WAAA;MACA,KAAA1E,IAAA;MACA,KAAAC,IAAA;MACA,KAAAyC,OAAA;IACA;IAEAA,QAAA;MACA,IAAAG,KAAA;MAEAA,KAAA,CAAAvC,OAAA;MACAuC,KAAA,CACA8B,WAAA,CACA9B,KAAA,CAAAtC,GAAA,mBAAAsC,KAAA,CAAA7C,IAAA,cAAA6C,KAAA,CAAA5C,IAAA,EACA4C,KAAA,CAAAzC,MACA,EACAgD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAT,KAAA,CAAA/C,IAAA,GAAAuD,IAAA,CAAA3D,IAAA;UACAmD,KAAA,CAAA9C,KAAA,GAAAsD,IAAA,CAAAuB,KAAA;QACA;QACA/B,KAAA,CAAAvC,OAAA;MACA;IACA;IACAuE,SAAA;MACA,IAAAhC,KAAA;MACA,KAAAiC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAL,WAAA,CAAA9B,KAAA,CAAAtC,GAAA,gBAAAM,QAAA,EAAAuC,IAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,IAAA;cACAT,KAAA,CAAAuB,QAAA;gBACAF,IAAA;gBACA7C,OAAA,EAAAgC,IAAA,CAAA4B;cACA;cACA,KAAAvC,OAAA;cACAG,KAAA,CAAAnC,iBAAA;YACA;cACAmC,KAAA,CAAAuB,QAAA;gBACAF,IAAA;gBACA7C,OAAA,EAAAgC,IAAA,CAAA4B;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,GAAA;MACA,KAAAlF,IAAA,GAAAkF,GAAA;MAEA,KAAAzC,OAAA;IACA;IACA0C,oBAAAD,GAAA;MACA,KAAAnF,IAAA,GAAAmF,GAAA;MACA,KAAAzC,OAAA;IACA;IACA2C,cAAAC,GAAA;MACA,KAAAzE,QAAA,CAAAE,QAAA,GAAAuE,GAAA,CAAA5F,IAAA,CAAAa,GAAA;IACA;IAEAgF,UAAAC,IAAA;MACA,KAAA7E,UAAA,GAAA6E,IAAA;MACA,KAAA5E,aAAA;IACA;IACA6E,aAAAD,IAAA;MACA,MAAAE,UAAA,6BAAAC,IAAA,CAAAH,IAAA,CAAAtB,IAAA;MACA,KAAAwB,UAAA;QACA,KAAAtB,QAAA,CAAAwB,KAAA;QACA;MACA;IACA;IACAC,SAAAL,IAAA,EAAAM,QAAA;MACA,IAAAjD,KAAA;MACAA,KAAA,CAAAM,UAAA,gCAAAqC,IAAA,EAAApC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,IAAA;UACAT,KAAA,CAAAhC,QAAA,CAAAiF,QAAA;UAEAjD,KAAA,CAAAuB,QAAA,CAAA2B,OAAA;QACA;UACAlD,KAAA,CAAAuB,QAAA,CAAAwB,KAAA,CAAAvC,IAAA,CAAA4B,GAAA;QACA;MACA;IACA;IACAe,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,WAAAC,IAAA,CAAAD,OAAA,EAAAE,kBAAA;IACA;IACAC,YAAA;MACA,KAAAhG,MAAA;QACAC,OAAA;MACA;MACA,KAAAL,IAAA;MACA,KAAA0C,OAAA;IACA;IACAJ,oBAAAN,KAAA;MACA;MACA,IAAAA,KAAA,CAAAd,GAAA,IAAAW,KAAA,CAAAC,OAAA,CAAAE,KAAA,CAAAd,GAAA;QACA,OAAAc,KAAA,CAAAd,GAAA,CAAAgB,MAAA;MACA;MACA,IAAAF,KAAA,CAAAqE,OAAA,IAAAxE,KAAA,CAAAC,OAAA,CAAAE,KAAA,CAAAqE,OAAA;QACA,OAAArE,KAAA,CAAAqE,OAAA,CAAAnE,MAAA;MACA;MACA,OAAAoE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;IACA;IACAC,mBAAAzE,KAAA;MACA;MACA,IAAAA,KAAA,CAAAxB,KAAA,IAAAwB,KAAA,CAAAxB,KAAA,CAAAyB,IAAA;QACA;MACA;MACA;IACA;IACAyE,mBAAA1E,KAAA;MACA;MACA,IAAAA,KAAA,CAAAxB,KAAA,IAAAwB,KAAA,CAAAxB,KAAA,CAAAyB,IAAA;QACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}