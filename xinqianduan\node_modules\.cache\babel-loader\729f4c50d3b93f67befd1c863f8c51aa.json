{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js!D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\components\\SystemMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\components\\SystemMonitor.vue", "mtime": 1748540172904}, {"path": "D:\\Gitee\\xinqianduan\\babel.config.js", "mtime": 1748425626782}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "systemUptime", "onlineUsers", "systemMetrics", "cpu", "memory", "disk", "services", "status", "version", "uptime", "mounted", "startMonitoring", "<PERSON><PERSON><PERSON><PERSON>", "monitorTimer", "clearInterval", "methods", "setInterval", "updateMetrics", "Math", "floor", "random", "refreshData", "$message", "success", "getProgressColor", "percentage"], "sources": ["src/components/SystemMonitor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"system-monitor\">\r\n    <el-card shadow=\"hover\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span class=\"card-title\">系统状态监控</span>\r\n        <el-button type=\"text\" @click=\"refreshData\">\r\n          <i class=\"el-icon-refresh\"></i> 刷新\r\n        </el-button>\r\n      </div>\r\n      \r\n      <div class=\"monitor-content\">\r\n        <!-- 系统状态指示器 -->\r\n        <div class=\"status-indicators\">\r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon online\">\r\n              <i class=\"el-icon-success\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">系统状态</div>\r\n              <div class=\"status-value\">正常运行</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-time\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">运行时间</div>\r\n              <div class=\"status-value\">{{ systemUptime }}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"status-item\">\r\n            <div class=\"status-icon\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"status-info\">\r\n              <div class=\"status-label\">在线用户</div>\r\n              <div class=\"status-value\">{{ onlineUsers }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性能指标 -->\r\n        <div class=\"performance-metrics\">\r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">CPU使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.cpu }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.cpu\" \r\n              :color=\"getProgressColor(systemMetrics.cpu)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">内存使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.memory }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.memory\" \r\n              :color=\"getProgressColor(systemMetrics.memory)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n          \r\n          <div class=\"metric-item\">\r\n            <div class=\"metric-header\">\r\n              <span class=\"metric-label\">磁盘使用率</span>\r\n              <span class=\"metric-value\">{{ systemMetrics.disk }}%</span>\r\n            </div>\r\n            <el-progress \r\n              :percentage=\"systemMetrics.disk\" \r\n              :color=\"getProgressColor(systemMetrics.disk)\"\r\n              :show-text=\"false\"\r\n            ></el-progress>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 服务状态 -->\r\n        <div class=\"service-status\">\r\n          <div class=\"service-title\">服务状态</div>\r\n          <div class=\"service-list\">\r\n            <div \r\n              v-for=\"service in services\" \r\n              :key=\"service.name\"\r\n              class=\"service-item\"\r\n            >\r\n              <div class=\"service-info\">\r\n                <span class=\"service-name\">{{ service.name }}</span>\r\n                <span \r\n                  class=\"service-status-badge\" \r\n                  :class=\"service.status\"\r\n                >\r\n                  {{ service.status === 'online' ? '正常' : '异常' }}\r\n                </span>\r\n              </div>\r\n              <div class=\"service-details\">\r\n                <span class=\"service-version\">{{ service.version }}</span>\r\n                <span class=\"service-uptime\">运行 {{ service.uptime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SystemMonitor',\r\n  data() {\r\n    return {\r\n      systemUptime: '7天 12小时 35分钟',\r\n      onlineUsers: 23,\r\n      systemMetrics: {\r\n        cpu: 45,\r\n        memory: 62,\r\n        disk: 38\r\n      },\r\n      services: [\r\n        {\r\n          name: 'Web服务器',\r\n          status: 'online',\r\n          version: 'v2.1.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '数据库',\r\n          status: 'online',\r\n          version: 'MySQL 8.0',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '文件服务',\r\n          status: 'online',\r\n          version: 'v1.5.2',\r\n          uptime: '7天'\r\n        },\r\n        {\r\n          name: '邮件服务',\r\n          status: 'online',\r\n          version: 'v3.2.1',\r\n          uptime: '6天'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.startMonitoring()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.monitorTimer) {\r\n      clearInterval(this.monitorTimer)\r\n    }\r\n  },\r\n  methods: {\r\n    startMonitoring() {\r\n      // 模拟实时数据更新\r\n      this.monitorTimer = setInterval(() => {\r\n        this.updateMetrics()\r\n      }, 5000)\r\n    },\r\n    \r\n    updateMetrics() {\r\n      // 模拟性能指标变化\r\n      this.systemMetrics.cpu = Math.floor(Math.random() * 30) + 30\r\n      this.systemMetrics.memory = Math.floor(Math.random() * 20) + 50\r\n      this.systemMetrics.disk = Math.floor(Math.random() * 15) + 30\r\n      \r\n      // 模拟在线用户数变化\r\n      this.onlineUsers = Math.floor(Math.random() * 10) + 20\r\n    },\r\n    \r\n    refreshData() {\r\n      this.updateMetrics()\r\n      this.$message.success('数据已刷新')\r\n    },\r\n    \r\n    getProgressColor(percentage) {\r\n      if (percentage < 50) {\r\n        return '#67C23A'\r\n      } else if (percentage < 80) {\r\n        return '#E6A23C'\r\n      } else {\r\n        return '#F56C6C'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.system-monitor {\r\n  height: 100%;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.monitor-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicators {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n.status-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #f0f0f0;\r\n  color: #666;\r\n  font-size: 18px;\r\n}\r\n\r\n.status-icon.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-info {\r\n  flex: 1;\r\n}\r\n\r\n.status-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.status-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 性能指标 */\r\n.performance-metrics {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.metric-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.metric-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n/* 服务状态 */\r\n.service-status {\r\n  border-top: 1px solid #f0f0f0;\r\n  padding-top: 20px;\r\n}\r\n\r\n.service-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.service-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.service-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 6px;\r\n}\r\n\r\n.service-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.service-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #2c3e50;\r\n}\r\n\r\n.service-status-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.service-status-badge.online {\r\n  background-color: #f0f9ff;\r\n  color: #67C23A;\r\n}\r\n\r\n.service-status-badge.offline {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.service-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.service-version,\r\n.service-uptime {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .status-indicators {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .status-item {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .service-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .service-details {\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style> "], "mappings": "AAkHA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,WAAA;MACAC,aAAA;QACAC,GAAA;QACAC,MAAA;QACAC,IAAA;MACA;MACAC,QAAA,GACA;QACAR,IAAA;QACAS,MAAA;QACAC,OAAA;QACAC,MAAA;MACA,GACA;QACAX,IAAA;QACAS,MAAA;QACAC,OAAA;QACAC,MAAA;MACA,GACA;QACAX,IAAA;QACAS,MAAA;QACAC,OAAA;QACAC,MAAA;MACA,GACA;QACAX,IAAA;QACAS,MAAA;QACAC,OAAA;QACAC,MAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;EACA;EACAC,cAAA;IACA,SAAAC,YAAA;MACAC,aAAA,MAAAD,YAAA;IACA;EACA;EACAE,OAAA;IACAJ,gBAAA;MACA;MACA,KAAAE,YAAA,GAAAG,WAAA;QACA,KAAAC,aAAA;MACA;IACA;IAEAA,cAAA;MACA;MACA,KAAAf,aAAA,CAAAC,GAAA,GAAAe,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA,KAAAlB,aAAA,CAAAE,MAAA,GAAAc,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA,KAAAlB,aAAA,CAAAG,IAAA,GAAAa,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;;MAEA;MACA,KAAAnB,WAAA,GAAAiB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;IACA;IAEAC,YAAA;MACA,KAAAJ,aAAA;MACA,KAAAK,QAAA,CAAAC,OAAA;IACA;IAEAC,iBAAAC,UAAA;MACA,IAAAA,UAAA;QACA;MACA,WAAAA,UAAA;QACA;MACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}