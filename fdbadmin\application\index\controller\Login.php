<?php
namespace app\index\controller;
use think\Request;
use think\Validate;
use untils\{JsonService,WechatApp};
use app\controller\Base;
use models\{Users};
use think\facade\Cache;

class Login {
    public function sendMsg(Request $request){
        $rand=rand(1000,9999);
        Cache::set('code',$rand);

        $form=$request->post();
        if(empty($form['phone']))   return JsonService::fail('请输入手机号码');

        $res = $this->sendMsgs($rand,$form['phone']);
        if($res){
            return JsonService::successful('发送成功');
        }else{
            return JsonService::fail('发送失败');
        }

    }
    public function get(){
        echo Session::get('code');
    }
    public function login(Users $model,Request $request){
        $code = Cache::get('code');
        $post = $request->post();
        if(empty($post['phone'])) return JsonService::fail('请输入手机号码');
        if(empty($post['code'])) return JsonService::fail('请输入验证码');

        //if($code!=$post['code']) return JsonService::fail('验证码错误');
        $info = $model->where(['phone'=>$post['phone']])->find();
        if(!empty($info)) {
            return JsonService::successful('成功',$info);
        }
        $post['password']=md5($post['code']);

        $res = $model->save($post);
        $post['email']='';
        $post['is_vip']=0;
        $post['id']=$res;
        if(empty($info)) return JsonService::successful('成功',$post);

    }

    public function loginOther(Users $model,Request $request){
        $post = $request->post();
        if(empty($post['phone'])) return JsonService::fail('请输入手机号码');
        if(empty($post['password'])) return JsonService::fail('请输入密码');

        $validate = new \app\index\validate\User;
        if (!$validate->check($post))  return JsonService::fail( $validate->getError());

        $info = $model->where(['phone'=>$post['phone']])->find();
        if(empty($info)) {
            return JsonService::fail('账号不存在');
        }

        if($post['password'] != $info['password']){
            return JsonService::fail('密码错误');
        }

        return JsonService::successful('登录成功',$info);
    }



    public function checkUserInfo(Users $model,Request $request){
        $id = $request->post('id');
        if(empty($id)) return JsonService::fail('请登录');
        $info = $model->find($id);
        if($info) return JsonService::successful('',$info);
        else return JsonService::fail('失败');
    }
    public function changeEmail(Users $model,Request $request){
        $id = $request->post('id');
        if(empty($id)) return JsonService::fail('请登录');
        $email = $request->post('email');
        if(!filter_var($email,FILTER_VALIDATE_EMAIL)) return JsonService::fail('请输入正确邮箱格式');
        $info = $model->find($id);
        if(empty($info)) return JsonService::fail('用户不存在');
        $info->email=$email;
        $res = $info->save();
        if(!empty($res)) return JsonService::successful('绑定成功',$info);

    }


    public function useredit(Users $model,Request $request){
        $post = $request->post();
    	if(empty($post['id'])) return JsonService::fail('请先登录');
    	if(empty($post['nickname'])) return JsonService::fail('请输入用户名称');
        if(empty($post['avatarurl'])) return JsonService::fail('请上传头像');
    	$info = $model->where(['id'=>$post['id']])->find();
    	if(empty($info)) return JsonService::fail('未查询到任何信息');

        	 try {

        		$info->nickname = $post['nickname'];
        		$info->headimg = $post['avatarurl'];
        		$info->save();
        		return JsonService::successful('获取成功',$info);
    		} catch (\Exception $e) {

    			return JsonService::fail("修改失败:".$e->getMessage());
    		}


    }

    public function edit(Users $model,Request $request){
        $post = $request->post();

    	if(empty($post['id'])) return JsonService::fail('请先登录');
      	//if($code!=$post['code']) return JsonService::fail('验证码错误');
    	if(empty($post['linkname'])) return JsonService::fail('请输入联系人');
    	if(empty($post['phone'])) return JsonService::fail('请输入手机号码');
    	if(empty($post['company'])) return JsonService::fail('请输入公司名称');
    	if(empty($post['license'])) return JsonService::fail('请上传营业执照');

    	if(!empty($post['password'])){
            $validate = new \app\index\validate\User;
            if (!$validate->check($post))  return JsonService::fail( $validate->getError());
        }

    	$info = $model->where(['id'=>$post['id']])->find();

    	if($info['linkphone'] != $post['phone']){
            $code = Cache::get('code');
            if(empty($post['code'])) return JsonService::fail('请输入验证码');
    	}
    	if(empty($info)) return JsonService::fail('未查询到任何信息');

        	 try {

        		$info->company = $post['company'];
        		$info->linkman = $post['linkname'];
        		$info->linkphone = $post['phone'];
        		$info->license = $post['license'];
                 if(!empty($post['password'])){
                     $info->password = $post['password'];
                 }
        		$info->save();
        		return JsonService::successful('修改成功',$post);
    		} catch (\Exception $e) {

    			return JsonService::fail("修改失败:".$e->getMessage());
    		}


    }
    public function sendMsgs($code,$phone){
        //您的验证码是：{$var} 请不要把验证码泄露给其他人。

        $post_data = array();
        $post_data['account'] = "MXT801426";
        $post_data['pswd'] = "Mxt801426";
        $post_data['mobile'] =$phone;
        $post_data['msg']= "【法多邦】您的验证码是：".$code." 请不要把验证码泄露给其他人";
        $post_data['needstatus'] ="false";

        $url='https://www.weiwebs.cn/msg/HttpBatchSendSM';
        $o="";
        foreach ($post_data as $k=>$v)
        {
           $o.= "$k=".urlencode($v)."&";
        }
        $post_data=substr($o,0,-1);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        $result = curl_exec($ch);
        return true;
    }
    //微信授权登录
    public function weixinp(Users $model,Request $request){
            $post = $request->post();

            try {
            $data =  WechatApp::_getphone($post['key'],$post['encrypted'],$post['iv']);
            if(empty($data)) return JsonService::fail("授权失败,请重新授权");
            $info = $model::withAttr('end_time',function($v,$d){
                if(!empty($d['year'])) return date('Y-m-d',$d['start_time']*1+$d['year']*1*365*60*60*24);
            })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['openid'=>$post['openid']])
            ->find();
            if(!empty($info)){
                $info->phone = $data['phoneNumber'];
                $info->openid=$post['openid'];
                $res = $info->save();
            }else{
                $insert = [
                    'nickname'=>"",
                    'headimg'=>"",
                    'phone'=>$data['phoneNumber'],
                    'openid'=>$post['openid'],
                    'end_time'=>'',
                    'vip_id'=>0
                ];
                $res = $model->save($insert);
                $info =  $info = $model::withAttr('end_time',function($v,$d){
                if(!empty($d['year'])) return date('Y-m-d',$d['start_time']*1+$d['year']*1*365*60*60*24);
            })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['id'=>$model->id])
            ->find();
            }


            if($res) return JsonService::successful('授权成功',$info);
            else return JsonService::fail('授权失败');

        } catch (\Exception $e) {
           return JsonService::fail("系统异常".$e->getMessage());
        }

    }

      //微信授权登录
  public function weixinLogin(Users $model,Request $request){
            $post = $request->post();

           if(empty($post['code'])) return JsonService::fail('未获取code');
            $code = $post['code'];
            try {
           $res =   WechatApp::_setCode($code);
            $data =  WechatApp::_getphone($post['session_key'],$post['encrypted'],$post['iv']);
            if(empty($data)) return JsonService::fail("授权失败,请重新授权");
            $info = $model::withAttr('end_time',function($v,$d){
                if(!empty($d['year'])) return date('Y-m-d',$d['start_time']*1+$d['year']*1*365*60*60*24);
            })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['openid'=>$post['openid']])
            ->find();
            if(!empty($info)){
                $info->phone = $data['phoneNumber'];
                $info->openid=$post['openid'];
                $res = $info->save();
            }else{
                $insert = [
                    'nickname'=>"",
                    'headimg'=>"",
                    'phone'=>$data['phoneNumber'],
                    'openid'=>$post['openid'],
                    'end_time'=>'',
                    'vip_id'=>0
                ];
                $res = $model->save($insert);
                $info = $model::withAttr('end_time',function($v,$d){
                if(!empty($d['year'])) return date('Y-m-d',$d['start_time']*1+$d['year']*1*365*60*60*24);
            })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['id'=>$model->id])
            ->find();
            }


            if($res) return JsonService::successful('成功登录',$info);
            else return JsonService::fail('授权失败');

        } catch (\Exception $e) {
           return JsonService::fail("系统异常".$e->getMessage());
        }

    }

       public function codeKey(Users $model,Request $request){
             $post = $request->post();
          if(empty($post['code'])) return JsonService::fail('未获取code');
            $code = $post['code'];
              try {
                $res =   WechatApp::_setCode($code);

                   $info = $model::withAttr('end_time',function($v,$d){
                if(!empty($d['year'])) return date('Y-m-d',$d['start_time']*1+$d['year']*1*365*60*60*24);
            })
            ->field('id,nickname,headimg,phone,openid,end_time,vip_id,year,start_time')
            ->where(['openid'=>$res['openid']])
            ->find();
                if(!empty($info)){
                     if($info) return JsonService::successful('成功登录',$info);
                       else return JsonService::fail('登录失败');
                }else{
                 return JsonService::successful('绑定手机号',$res);
                }


        } catch (\Exception $e) {
           return JsonService::fail("系统异常".$e->getMessage());
        }
    }
    public function bindUserInfo(Users $model,Request $request){
        $post = $request->post();
        $info = $model->where(['openid'=>$post['openid']])->find();
        $info->headimg = $post['headimg'];
        $info->nickname = $post['nickname'];
        $res = $info->save();
        if($res) return JsonService::successful('一键获取成功');
        else return JsonService::fail('一键获取失败');
    }
}
