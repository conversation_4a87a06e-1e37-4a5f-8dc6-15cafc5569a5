{"remainingRequest": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Gitee\\xinqianduan\\src\\components\\DebtDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Gitee\\xinqianduan\\src\\components\\DebtDetail.vue", "mtime": 1748425644019}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748425629386}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748425630361}, {"path": "D:\\Gitee\\xinqianduan\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748425643057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIGV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAnRGVidERldGFpbCcsDQogICAgcHJvcHM6IHsNCiAgICAgIGlkOiB7DQogICAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICAgIH0NCiAgICB9LA0KICAgIGRhdGEoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICAgIGluZm86IFtdIC8vIOeUqOS6juWtmOWCqOaOpeWPo+i/lOWbnueahOaVsOaNrg0KICAgICAgfTsNCiAgICB9LA0KICAgIHdhdGNoOiB7DQogICAgICBpZDogew0KICAgICAgICAgIGltbWVkaWF0ZTogdHJ1ZSwgLy8g57uE5Lu25Yib5bu65pe256uL5Y2z6Kem5Y+RDQogICAgICAgICAgaGFuZGxlcihuZXdJZCkgew0KICAgICAgICAgICAgICB0aGlzLmdldEluZm8obmV3SWQpOw0KICAgICAgICAgIH0NCiAgICAgIH0NCiAgICAgfSwNCiAgICBtZXRob2RzOiB7DQogICAgICBnZXRJbmZvKGlkKSB7DQogICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgIF90aGlzLmdldFJlcXVlc3QoIi9kZWJ0L3ZpZXc/aWQ9IiArIGlkKS50aGVuKChyZXNwKSA9PiB7DQogICAgICAgICAgaWYgKHJlc3AuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIF90aGlzLmluZm8gPSByZXNwLmRhdGE7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzcC5tc2csDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSwNCiAgICAgICAgZG93bmxvYWRGaWxlcyhpbWdzKSB7DQogICAgICAgICAgICBpbWdzLmZvckVhY2goKGZpbGUpID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICAgICAgICAgIGxpbmsuaHJlZiA9IGZpbGUucGF0aDsNCiAgICAgICAgICAgICAgICBsaW5rLmRvd25sb2FkID0gZmlsZS5uYW1lOw0KICAgICAgICAgICAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBleHBvcnRzOmZ1bmN0aW9uICgpIHsgLy/lr7zlh7rooajmoLwNCiAgICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAgICAgICBsb2NhdGlvbi5ocmVmID0gIi9hZG1pbi9kZWJ0L3ZpZXc/dG9rZW49IitfdGhpcy4kc3RvcmUuZ2V0dGVycy5HRVRfVE9LRU4rIiZleHBvcnQ9MSZpZD0iK190aGlzLnJ1bGVGb3JtLmlkOw0KICAgICAgICB9DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["DebtDetail.vue"], "names": [], "mappings": ";AAkGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DebtDetail.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\r\n    <el-row>\r\n    <el-button size=\"small\" type=\"primary\" icon=\"el-icon-top\" style=\"margin-bottom: 10px;\" @click=\"exports\">导出跟进记录</el-button>\r\n\r\n    <el-descriptions title=\"债务信息\">\r\n        <el-descriptions-item label=\"用户姓名\">{{info.nickname}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人姓名\">{{info.name}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人电话\">{{info.tel}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务人地址\">{{info.address}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"债务金额\">{{info.money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"合计回款\">{{info.back_money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"未回款\">{{info.un_money}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"提交时间\">{{info.ctime}}</el-descriptions-item>\r\n        <el-descriptions-item label=\"最后一次修改时间\">{{info.utime}}</el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"债务人身份信息\" :colon=\"false\">\r\n        <el-descriptions-item><div style=\"width: 100%;display: table-cell;\" v-if=\"info.cards[0]\">\r\n            <div style=\"float: left;margin-left:2px;\"\r\n                 v-for=\"(item4, index4) in info.cards\"\r\n                 :key=\"index4\"\r\n                 class=\"image-list\"\r\n            >\r\n                <img :src=\"item4\" style=\"width: 100px; height: 100px\" @click=\"showImage(item4)\" mode=\"aspectFit\" />\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"案由\" :colon=\"false\">\r\n        <el-descriptions-item>{{info.case_des}}</el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"证据图片\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <el-button v-if=\"info.images[0]\" style=\"margin-top: 5px;\" size=\"small\" type=\"primary\" @click=\"downloadFiles(info.images_download)\">全部下载</el-button>\r\n            <div style=\"width: 100%;display: table-cell;\" v-if=\"info.images[0]\">\r\n            <div style=\"float: left;margin-left:2px;\"\r\n                 v-for=\"(item2, index2) in info.images\"\r\n                 :key=\"index2\"\r\n                 class=\"image-list\"\r\n            >\r\n                <!--<img :src=\"item2\" style=\"width: 100px; height: 100px\" @click=\"showImage(item2)\" mode=\"aspectFit\" />-->\r\n                <el-image\r\n                        style=\"width: 100px; height: 100px\"\r\n                        :src=\"item2\"\r\n                        :preview-src-list=\"info.images\">\r\n                </el-image>\r\n                <a style=\"\" :href=\"item2\" target=\"_blank\" :download=\"'evidence.'+item2.split('.')[1]\">下载</a>\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"证据文件\" v-if=\"info.attach_path[0]\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <div style=\"width: 100%;display: table-cell;line-height:20px;\">\r\n            <div\r\n                    v-for=\"(item3, index3) in info.attach_path\"\r\n                    :key=\"index3\"\r\n            >\r\n                <div v-if=\"item3\">\r\n                    <div >文件{{ index3 + 1 + '->' + item3.split(\".\")[1] }}<a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">查看</a><a style=\"margin-left: 10px;\" :href=\"item3\" target=\"_blank\">下载</a></div><br />\r\n                </div>\r\n            </div>\r\n        </div></el-descriptions-item>\r\n    </el-descriptions>\r\n    <el-descriptions title=\"跟进记录\" :colon=\"false\">\r\n        <el-descriptions-item>\r\n            <el-table\r\n                    :data=\"info.debttrans\"\r\n                    style=\"width: 100%; margin-top: 10px\"\r\n                    v-loading=\"loading\"\r\n                    size=\"mini\"\r\n            >\r\n                <el-table-column prop=\"day\" label=\"跟进日期\"> </el-table-column>\r\n                <el-table-column prop=\"ctime\" label=\"提交时间\"> </el-table-column>\r\n                <el-table-column prop=\"au_id\" label=\"操作人员\"> </el-table-column>\r\n                <el-table-column prop=\"type\" label=\"进度类型\"> </el-table-column>\r\n                <el-table-column prop=\"total_price\" label=\"费用金额/手续费\"> </el-table-column>\r\n                <el-table-column prop=\"content\" label=\"费用内容\"> </el-table-column>\r\n                <el-table-column prop=\"rate\" label=\"手续费比率\"></el-table-column>\r\n                <el-table-column prop=\"back_money\" label=\"回款金额\"> </el-table-column>\r\n                <el-table-column prop=\"pay_type\" label=\"支付状态\"> </el-table-column>\r\n                <el-table-column prop=\"pay_time\" label=\"支付时间\"> </el-table-column>\r\n                <el-table-column prop=\"pay_order_type\" label=\"支付方式\"> </el-table-column>\r\n                <el-table-column prop=\"desc\" label=\"进度描述\"> </el-table-column>\r\n                <el-table-column fixed=\"right\" label=\"操作\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button\r\n                                @click.native.prevent=\"delData(scope.$index, scope.row.id)\"\r\n                                type=\"text\"\r\n                                size=\"small\"\r\n                        >\r\n                            移除\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table></el-descriptions-item>\r\n    </el-descriptions>\r\n    </el-row>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'DebtDetail',\r\n    props: {\r\n      id: {\r\n        type: String,\r\n        required: true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n          info: [] // 用于存储接口返回的数据\r\n      };\r\n    },\r\n    watch: {\r\n      id: {\r\n          immediate: true, // 组件创建时立即触发\r\n          handler(newId) {\r\n              this.getInfo(newId);\r\n          }\r\n      }\r\n     },\r\n    methods: {\r\n      getInfo(id) {\r\n        let _this = this;\r\n        _this.getRequest(\"/debt/view?id=\" + id).then((resp) => {\r\n          if (resp.code == 200) {\r\n            _this.info = resp.data;\r\n          } else {\r\n            _this.$message({\r\n              type: \"error\",\r\n              message: resp.msg,\r\n            });\r\n          }\r\n        });\r\n      },\r\n        downloadFiles(imgs) {\r\n            imgs.forEach((file) => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = file.path;\r\n                link.download = file.name;\r\n                link.click();\r\n            });\r\n        },\r\n        exports:function () { //导出表格\r\n            let _this = this;\r\n            location.href = \"/admin/debt/view?token=\"+_this.$store.getters.GET_TOKEN+\"&export=1&id=\"+_this.ruleForm.id;\r\n        }\r\n    }\r\n  }\r\n</script>\r\n"]}]}